一、Windows安装 archivebox

1、 docker compose run --rm archivebox init --setup
2、 docker compose up -d

3、 # 查找容器名称
docker compose ps

# 复制文件到容器内
docker cp .\data\CTI_urls.txt archivebox-dev-archivebox-1:/tmp/

# 在容器内执行 head 命令查看文件前10行
docker compose exec archivebox head -10 /tmp/CTI_urls.txt

# 在容器内执行命令
docker compose run archivebox archivebox add /tmp/CTI_urls.txt

remove

docker compose run archivebox add --parser=url_list --depth=0 --overwrite --timeout=120 /tmp/CTI_urls.txt


测试链接

https://www.trellix.com/blogs/research/oneclik-a-clickonce-based-apt-campaign-targeting-energy-oil-and-gas-infrastructure/   



--parser=url_list: 指定解析器类型为 URL 列表
--tag="CTI": 添加标签，方便以后检索
--depth=1: 设置抓取深度为 1 (不仅抓取页面本身，还抓取页面中的链接)
--overwrite: 如果 URL 已存在，覆盖它
--timeout=120: 增加超时时间为 120 秒



二、Linux 下安装 ArchiveBox

# 首先安装python3-venv（如果还没有安装）
sudo apt install python3-venv python3-full

# 创建虚拟环境
python3 -m venv ~/archivebox_venv

# 激活虚拟环境
source ~/archivebox_venv/bin/activate

# 现在可以使用pip安装包
pip install --upgrade archivebox yt-dlp playwright -i https://pypi.tuna.tsinghua.edu.cn/simple



# 安装完成后可以初始化ArchiveBox
mkdir -p ~/docker_archivebox/data
cd ~/docker_archivebox/data
archivebox init --setup


# 后台启动 Web 服务
nohup archivebox server 0.0.0.0:8011 > archivebox.log 2>&1 &


[1] 3406636   用id去终止

# 检查服务状态
ps aux | grep archivebox

# 查看日志
tail archivebox.log



# 重要：初始化 Git 仓库（ArchiveBox 需要）
git init
git config user.email "<EMAIL>"
git config user.name "ArchiveBox User"
git add .
git commit -m "Initial ArchiveBox repository"

# 如果遇到 main.py 冲突错误，删除冲突文件
# rm -f ~/archivebox_venv/lib/python3.12/site-packages/main.py

# 禁用可选的 Mercury 解析器（如果安装失败）
# archivebox config --set SAVE_MERCURY=False

