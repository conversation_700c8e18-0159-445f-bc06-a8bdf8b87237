import requests
from bs4 import BeautifulSoup
import csv
import time
import os
import re

'''here set your interested version and number of items'''
version = 17  # set version
start_point = 0  # set start point of index

# 为每种类型设置不同的数量限制
groups_max = 170
software_max = 877
campaigns_max = 50

ALL_FLAG = True  # 如果为True，则会忽略上述设置，爬取所有数据

# URLs for different categories
base_url = f"https://attack.mitre.org/versions/v{version}/"
groups_url = f"{base_url}groups/"
software_url = f"{base_url}software/"
campaigns_url = f"{base_url}campaigns/"

# Result files
groups_txt_file = f'mitre_groups_v{version}.txt'
software_txt_file = f'mitre_software_v{version}.txt'
campaigns_txt_file = f'mitre_campaigns_v{version}.txt'

# CSV result files
groups_csv_file = f'result_groups_v{version}_{start_point+1}to{start_point+groups_max}.csv'
software_csv_file = f'result_software_v{version}_{start_point+1}to{start_point+software_max}.csv'
campaigns_csv_file = f'result_campaigns_v{version}_{start_point+1}to{start_point+campaigns_max}.csv'

# 全局变量
html_content = None
soup_content = None
tbody_content = None
tr_content = None

start_time = 0
end_time = 0


def fetch_html(url):
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            print(f"{url} fetch html failed (attempt {retry_count+1}/{max_retries}): {e}")
            retry_count += 1
            time.sleep(2)  # 添加延迟，避免过快请求
    
    return None


def get_soup(html):
    if html is None:
        return None
    soup = BeautifulSoup(html, "html.parser")
    return soup


def find_tbody(soup):
    if soup is None:
        return None
    tbody = soup.find("tbody")
    return tbody


def find_tr(tbody):
    if tbody is None:
        return []
    tr = tbody.find_all("tr")
    return tr


def create_txt_file(filename):
    # 创建txt文件并写入标题行
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("Name\tAssociated Items\tDescription\n")


def append_to_txt(description, filename):
    # 将一个条目的信息添加到txt文件
    with open(filename, 'a', encoding='utf-8') as f:
        # 清理描述中的引用标记 [1], [2], [3] 等
        description = re.sub(r'\[\d+\]', '', description)
        description = description.replace('\t', ' ').replace('\n', ' ').strip()
        f.write(f"{description}\n")



def get_target_info(tr, target_type):
    """处理特定类型(groups/software/campaigns)的目标信息"""
    global start_point
    
    # 根据目标类型确定对应的最大数量
    if target_type == "groups":
        target_url_base = groups_url
        result_file = groups_csv_file
        txt_file = groups_txt_file
        search_pattern = '/groups/'
        num_interval = groups_max
    elif target_type == "software":
        target_url_base = software_url
        result_file = software_csv_file
        txt_file = software_txt_file
        search_pattern = '/software/'
        num_interval = software_max
    elif target_type == "campaigns":
        target_url_base = campaigns_url
        result_file = campaigns_csv_file
        txt_file = campaigns_txt_file
        search_pattern = '/campaigns/'
        num_interval = campaigns_max
    else:
        print(f"Unknown target type: {target_type}")
        return

    def create_csv(filename):
        # 写入CSV头部
        with open(filename, 'w', newline='', encoding='utf-8') as rst_csv:
            writer = csv.writer(rst_csv)
            header = ['Index', 'ID', 'Name',
                      'URL', 'References', 'Time', 'PDF']
            writer.writerow(header)

    def write_result(data_list, filename):
        # 追加数据到CSV
        with open(filename, 'a', newline='', encoding='utf-8') as rst_csv:
            writer = csv.writer(rst_csv)
            data_list[3] = f'=HYPERLINK("{data_list[3]}", "{data_list[3]}")'
            writer.writerow(data_list)

    def go_target_web(index):  # 获取目标页面信息
        try:
            target_url = target_url_base + id
            target_html = fetch_html(target_url)
            
            if target_html is None:
                print(f"{target_url} - failed to fetch HTML")
                return index
            
            target_soup = get_soup(target_html)
            
            # 提取相关信息
            associated_items = ""
            description = ""
            
            # 查找描述信息
            desc_div = target_soup.find('div', class_='description-body')
            if desc_div:
                description = desc_div.text.strip()
            
            # 查找关联项目
            related_items_list = []
            
            # 根据类型不同查找关联项目
            relation_patterns = {
                "groups": ['/groups/', 'Associated Groups'],
                "software": ['/software/', 'Associated Software'],
                "campaigns": ['/campaigns/', 'Associated Campaigns']
            }
            
            # 先尝试查找特定标题的卡片
            for related_type, (pattern, title_text) in relation_patterns.items():
                related_header = target_soup.find('h2', string=lambda text: text and title_text in text if text else False)
                if related_header:
                    related_section = related_header.parent
                    if related_section:
                        item_links = related_section.find_all('a', href=re.compile(pattern))
                        for item in item_links:
                            item_name = item.text.strip()
                            if item_name and item_name not in related_items_list:
                                related_items_list.append(item_name)
            
            # 查找所有可能包含关系的卡片
            relation_divs = target_soup.find_all('div', class_='card')
            for div in relation_divs:
                div_title = div.find('h2')
                if div_title:
                    title_text = div_title.text.lower() if div_title.text else ""
                    if any(keyword in title_text for keyword in ['groups', 'software', 'campaigns', 'relationship']):
                        for related_type, (pattern, _) in relation_patterns.items():
                            item_links = div.find_all('a', href=re.compile(pattern))
                            for item in item_links:
                                item_name = item.text.strip()
                                if item_name and item_name not in related_items_list:
                                    related_items_list.append(item_name)
            
            # 合并关联项目
            if related_items_list:
                associated_items = ", ".join(related_items_list)
            
            print(f"Found {len(related_items_list)} associated items for {name}")
            
            # 将信息添加到txt文件
            append_to_txt(description, txt_file)
            
            # CSV处理代码
            target_a = target_soup.find_all("a", class_="external text")
            for a in target_a:
                src_url = a['href']
                target_text = a.text.strip()
                src_pdf = ''
                if '.pdf' in src_url.lower():
                    src_pdf = 'O'
                try:
                    retrieved_part = target_text.split("Retrieved")[1] if "Retrieved" in target_text else ""
                except:
                    retrieved_part = ""
                    
                list_data = [index, id, name, src_url, target_text,
                        retrieved_part, src_pdf]
                write_result(list_data, result_file)
                index += 1
                
            print(f'{target_url} - fetch success')
            
            return index
        except Exception as e:
            print(f"Error processing {id}: {str(e)}")
            return index

    if ALL_FLAG:
        start_point = 0
        num_interval = len(tr)
        print(f"ALL_FLAG is True, will process all {len(tr)} {target_type}")
    else:
        print(f"Will process up to {num_interval} {target_type}")
    
    # 创建txt结果文件
    create_txt_file(txt_file)
    
    # 检查要求是否合理
    if num_interval + start_point <= len(tr):
        total_index = 0
        create_csv(result_file)
        for row_index, r in enumerate(tr):
            if start_point <= row_index < start_point + num_interval:
                td = r.find_all("td")
                id = ''
                name = ''
                for j, d in enumerate(td):
                    str_val = d.text.strip()
                    if j == 0:
                        id = str_val
                    elif j == 1:
                        name = str_val
                    else:
                        break
                total_index = go_target_web(total_index)
                # 添加延迟以避免请求过快
                time.sleep(1)
            elif row_index >= start_point + num_interval:
                print('---------------------------------')
                print(f"Processed {row_index - start_point} {target_type}, wrote {total_index} rows of data to {txt_file}")
                break
        else:  # 当for循环正常结束(非break)时执行
            print('---------------------------------')
            print(f"Processed all {num_interval} {target_type}, wrote {total_index} rows of data to {txt_file}")
    else:
        if len(tr) > 0:
            print(f'Warning: Requested {num_interval} {target_type}, but only {len(tr)} are available')
            num_interval = len(tr)  # 调整为可用数量
            
            total_index = 0
            create_csv(result_file)
            for row_index, r in enumerate(tr):
                if start_point <= row_index < len(tr):
                    td = r.find_all("td")
                    id = ''
                    name = ''
                    for j, d in enumerate(td):
                        str_val = d.text.strip()
                        if j == 0:
                            id = str_val
                        elif j == 1:
                            name = str_val
                        else:
                            break
                    total_index = go_target_web(total_index)
                    # 添加延迟以避免请求过快
                    time.sleep(1)
            
            print('---------------------------------')
            print(f"Processed all available {len(tr)} {target_type}, wrote {total_index} rows of data to {txt_file}")
        else:
            print(f'Error: No {target_type} found in the table')


def process_category(category):
    """处理特定类别(groups/software/campaigns)的数据"""
    print(f'Processing {category}...')
    
    if category == "groups":
        url = groups_url
        max_items = groups_max
    elif category == "software":
        url = software_url
        max_items = software_max
    elif category == "campaigns":
        url = campaigns_url
        max_items = campaigns_max
    else:
        print(f"Unknown category: {category}")
        return
    
    print(f"Expected items: {max_items if not ALL_FLAG else 'ALL'}")
    
    html_content = fetch_html(url)
    if html_content:
        soup_content = get_soup(html_content)
        tbody_content = find_tbody(soup_content)
        tr_content = find_tr(tbody_content)
        print(f"Found {len(tr_content)} {category} in the table")
        get_target_info(tr_content, category)
    else:
        print(f"Failed to fetch {category} page!")


def main():
    global start_time, end_time
    start_time = time.time()
    
    print(f'Start crawler, target: MITRE ATT&CK version {version}')
    print(f'ALL_FLAG = {ALL_FLAG} (if True, will process all available items)')
    print('-------------------------------------------')
    
    # 依次处理三种类型
    process_category("groups")
    process_category("software") 
    process_category("campaigns")
    
    end_time = time.time()
    print('-------------------------------------------')
    print(f'All Done! Total time: {end_time-start_time:.2f} seconds')
    print(f'Groups data saved to: {os.path.abspath(groups_txt_file)}')
    print(f'Software data saved to: {os.path.abspath(software_txt_file)}')
    print(f'Campaigns data saved to: {os.path.abspath(campaigns_txt_file)}')


if __name__ == "__main__":
    main()