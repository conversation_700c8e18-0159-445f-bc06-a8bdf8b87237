## Page 4

Abstract
Access control builds on authorisation and authentication. This KA1 will present the general
foundations of access control and some signiﬁcant instantiations that have emerged as IT
kept spreading into new application areas. It will survey modes of user authentication and the
way they are currently deployed, authentication protocols for the web, noting how new use
cases have led to a shift from authentication to authorisation protocols, and the formalisation
of authentication properties as used in today’s protocol analysis tools. On accountability,
the focus is on the management and protection of audit logs. The surveillance of logs to
detect attacks or inappropriate behaviour is described in the Security Operations & Incident
Management CyBOK Knowledge Area [1] while the examination of evidence following a breach
of policy or attack is covered in the Forensics CyBOK Knowledge Area [2]. Throughout the KA,
we will ﬂag technical terms that appear in more than one meaning in the academic and the
trade literature.
1
INTRODUCTION
“All science is either physics or stamp collecting.” [<PERSON>]
In some cases, IT systems may guarantee – by design – that no undesirable behaviour is
possible. In other cases, IT systems exhibit such a degree of ﬂexibility – also by design –
that additional measures need to be taken to limit undesirable behaviour in accordance with
the given circumstances. As noted by <PERSON><PERSON>, this can be done by code in the system that
excludes behaviour, which will violate certain rules, or it can be done by codes of conduct that
the users of the system are expected to adhere to [3]. In the latter case, disciplinary or legal
processes deal with those that had broken the rules. This is the context for authentication,
authorisation, and accountability.
Readers acquainted with the mores of academic writing may now expect deﬁnitions of core
terms, maybe some reﬁnement of terminology, and then an overview of the latest approaches
in achieving authentication, authorisation, and accountability. As will be shown, this approach
fails at the ﬁrst hurdle. These three terms are overloaded to an extent that provides ample
space for confusion and dispute. For example, authorisation stands both for the setting of
rules and for checking compliance with those very rules. Readers should thus be cautious
when studying the literature on this Knowledge Area.
Changes in the way IT is being used create their own challenges for taxonomies. How closely
should terms be tied to the environment in which they ﬁrst emerged? There is a habit in the
trade and research literature of linking terms exclusively to a notional ‘traditional’ instantiation
of some generic concept, and inventing new fashionable terms for new environments, even
though the underlying concepts have not changed.
1The title of this KA, ‘Authentication, Authorisation & Accountability’ is abbreviated as AAA in CyBOK related
documents and past versions of this KA. Please note that the acronym ‘AAA’ is also frequently used in other
contexts to refer to ‘Authentication, Authorisation, and Accounting’, for example in connection with network
access protocols such as Diameter.
KA Authentication, Authorisation & Accountability | July 2021
Page 3
## Page 5

2
CONTENT
This KA ﬁrst addresses authorisation in the context of access control and presents the main
ﬂavours of access control in use today. The section on access control in distributed systems
explains concepts used when implementing access control across different sites. The KA then
moves to authentication, touching on user authentication and on authentication in distributed
systems, and concludes with a discussion of logging services that support accountability.
3
AUTHORISATION
[4, 5, 6, 7, 8]
In their seminal paper [5], Lampson et al. postulate access control = authentication + autho-
risation. We will follow this lead and present authorisation in the context of access control,
starting with an introduction to the concepts fundamental for this domain, followed by an
overview of different policy types. Libicki’s dictum, “connotation, not denotation, is the prob-
lem” [9] also applies here, so we will pay particular attention to the attributes used when
setting access rules, and to the nature of the entities governed by those rules. Code-based
access control, mobile security, and Digital Rights Management will introduce new paradigms
to access control, without changing its substance. We will then present design options for
policy enforcement and discuss delegation and some important theoretical foundations of
access control.
3.1
Access Control
Access control is “the process of granting or denying speciﬁc requests ...” [10]. This process
needs the following inputs
• Who issued the request?
• What is requested?
• Which rules are applicable when deciding on the request?
“Who” in the ﬁrst question is dangerous. The word suggests that requests always come from a
person. This is inaccurate for two reasons. First, the source of a request could be a particular
machine, a machine in a particular conﬁguration, or a particular program, e.g. a particular
Android app. Secondly, at a technical level, requests in a machine are issued by a process, not
by a person. The question thus becomes, “for whom or what is the process speaking for when
making the request?” “What is requested” is frequently given as a combination of an action to
be performed and the object on which the action is to be performed. The rules are logical
expressions that evaluate to a decision. In the elementary case, the decision is permit or deny.
When policies get more elaborate, there may be reasons for adding an indeterminate decision.
A decision may also prescribe further actions to be performed, sometimes called obligations.
KA Authentication, Authorisation & Accountability | July 2021
Page 4
## Page 6

3.1.1
Core Concepts
The term ’security policy’ is used both for the general rules within an organisation that stipulate
how sensitive resources should be protected, and for the rules enforced by IT systems on the
resources they manage. Sterne had coined the terms organisational policies and automated
policies to distinguish these two levels of discourse [4].
When setting security policies, principal stands for the active entity in an access request. When
policies directly refer to users, as was the case in the early stages of IT security, user identities
serve as principals. Access control based on user identities is known as Identity-Based Access
Control (IBAC). In security policies that refer to concepts such as roles or to the program that
issues a request, the principal is a role or a program. Principal may then generally stand for
any security attribute associated with the issuer of a request. With this generalisation, any
ﬂavour of access control is by deﬁnition attribute-based access control (see Section 3.1.4).
Subject stands for the active entity making a request when a system executes some program.
A subject speaks for a principal when the runtime environment associates the subject with the
principal in an unforgeable manner. The original example for creating a subject that speaks
for a principal is user log-in, spawning a process running under the user identity of the person
that had been authenticated. The research literature does not always maintain this distinction
between principals and subjects and one may ﬁnd security policies referring to subjects. When
policies refer to attributes of a user but not to the user’s identity, user identities become a
layer of indirection between principals and subjects [11].
A subject is created, e.g., at log-in, and can be terminated, e.g. at log-out. Similarly, user
identities are created through some administrative action and can be terminated, e.g., by
deleting a user account. In practice, subjects have considerably shorter lifetimes than user
identities. Processes that control industrial plants are a rare example of subjects that could
live forever, but could be killed by system crashes.
Object is the passive entity in an access request. Access operations deﬁne how an object may
be accessed by a subject. Access operations can be as elementary as read, write, execute
in Linux, they can be programs such as setuid programs in Linux, and they can be entire
workﬂows as in some ﬂavours of UCON (Section 3.1.8).
Access rights express how a principal may access an object. In situations where there is a
direct match between access operations and access rights, the conceptual distinction between
access operations and access rights may not be maintained. Permission is frequently used
as a synonym for access right. Privilege may also be used as a synonym for access right, e.g.,
Oracle9i Database Concepts Release 2 (9.2) states:
“A privilege is permission to access a named object in a prescribed manner ...”
Other systems, such as Windows, make a distinction between access rights and privileges,
using privilege speciﬁcally for the right to access system resources and to perform system-
related tasks. Operating systems and databases often have a range of system privileges that
are required for system administration.
The reference monitor (more details in Section 3.2.2) is the component that decides on access
requests according to the given policy.
KA Authentication, Authorisation & Accountability | July 2021
Page 5
## Page 7

3.1.2
Security Policies
Automated security policies are a collection of rules. The rules specify the access rights a
principal has on an object. Conceptually, a policy could then be expressed as an Access Control
Matrix with rows indexed by principals and columns indexed by objects [12]. Access Control
Lists (ACLs) stored with the objects correspond to the columns of this matrix; capabilities
stored with principals correspond to the rows of this matrix (also see the Operating Systems
& Virtualisation CyBOK Knowledge Area [13]).
Discretionary Access Control (DAC) and Mandatory Access Control (MAC) are two core policies
formulated in the 1970s in the context of the US defence sector. Discretionary access control
policies assign the right to access protected resources to individual user identities, at the
discretion of the resource owner. In the literature, DAC may generically refer to policies set by
resource owners but also to policies referring directly to user identities, i.e., to IBAC.
Mandatory access control policies label subjects and objects with security levels. The set
of security levels is partially ordered, with a least upper bound and a greatest lower bound
operator. The security levels thus form a lattice. In the literature, MAC may generically refer
to policies mandated by the system as, e.g., in Security-Enhanced Linux (SELinux) [14, 15]
and in Security-Enhanced (SE) Android [16], or to policies based on security levels as in past
products such as Trusted Xenix or Trusted Oracle. Policies of the latter type are also known
as multi-level security policies and as lattice-based policies.
3.1.3
Role-based Access Control
In Role-Based Access Control (RBAC), roles are an intermediate layer between users and the
permissions to execute certain operations. Operations can be well-formed transactions with
built-in integrity checks that mediate the access to objects. Users are assigned roles and are
authorised to execute the operations linked to their active role. Separation of Duties (SoD)
refers to policies that stop single users from becoming too powerful. Examples for SoD are
rules stating that more than one user must be involved to complete some transaction, rules
stating that a user permitted to perform one set of transactions is not permitted to perform
some other set of transactions, the separation between front ofﬁce and back ofﬁce in ﬁnancial
trading ﬁrms is an example, or rules stating that policy administrators may not assign permis-
sions to themselves. Static SoD rules are considered during user-role assignment, dynamic
SoD must be enforced when a role is activated. The NIST RBAC model [7] distinguishes
between:
• Flat RBAC: users are assigned to roles and roles to permissions to operations; users
get permissions to execute procedures via role membership; user-role reviews are
supported.
• Hierarchical RBAC: adds support for role hierarchies.
• Constrained RBAC: adds separation of duties.
• Symmetric RBAC: adds support for permission-role reviews, which may be difﬁcult to
achieve in large distributed systems.
Many commercial systems support some ﬂavour of role-based access control, without nec-
essarily adhering to the formal speciﬁcations of RBAC published in the research literature.
RBAC is an elegant and intuitive concept, but may become quite messy in deployment as
suggested by comments in an empirical study on the use of RBAC [17]. Practitioners note that
KA Authentication, Authorisation & Accountability | July 2021
Page 6
## Page 8

RBAC works as long as every user has only one role, or that “the enormous effort required for
designing the role structure and populating role data” constitutes an inhibitor for RBAC.
3.1.4
Attribute-based Access Control
Attribute-Based Access Control (ABAC) is deﬁned in [18] as a “logical access control methodol-
ogy where authorisation to perform a set of operations is determined by evaluating attributes
associated with the subject, object, requested operations, and, in some cases, environment
conditions against policy, rules, or relationships that describe the allowable operations for a
given set of attributes”. This is a generic deﬁnition of access control that no longer reserves a
special place to the user or to the user’s role, reﬂecting how the use of IT systems has changed
over time.
Access control may be performed in an application or in the infrastructure supporting the
application. Access control in an infrastructure uses generic attributes and operations. The
Linux access control system may serve as an example. Access control in an application uses
application-speciﬁc attributes and operations. In this distinction, ABAC can be viewed as a
synonym for application-level access control.
3.1.5
Code-based Access Control
Code-Based Access Control (CBAC) assigns access rights to executables. Policies may refer
to code origin, to code identity (e.g., the hash of an executable), or to other properties of the
executable, rather than to the identity of the user who had launched the executable. Origin
can subsume the domain the code was obtained from, the identity of the code signer, a
speciﬁc name space (.NET had experimented with strong names, i.e. bare public keys serving
as names for name spaces), and more. CBAC can be found in the Java security model [19]
and in Microsoft’s .NET architecture [20].
The reference monitor in CBAC typically performs a stack walk to check that all callers have
been granted the required access rights. The stack walk addresses the confused deputy
problem [21], where an unprivileged attacker manipulates a system via calls to privileged code
(the confused deputy). Controlled invocation is implemented through assert statements; a
stack walk for an access right will stop at a caller that asserts this right.
3.1.6
Mobile Security
Smartphones typically have a single owner, hold private user data, offer communication
functions ranging from cell phone to NFC, can observe their surroundings via camera and
microphone, and can determine their location, e.g., via GPS. On smartphones, apps are the
principals for access control. The objects of access control are the sensitive data stored on a
phone and the sensitive device functions on a phone.
Access control on a smartphone addresses the privacy requirements of the owner and the
integrity requirements of the platform. Android, for example, divides permission groups into
normal, dangerous, and signature permissions. Normal permissions do not raise privacy or
platform integrity concerns; apps do not need approval when asserting such permissions.
Dangerous permissions can impact privacy and need user approval. Up to Android 6.0, users
had to decide whether to authorise a requested permission when installing an app. User studies
showed that permissions were authorised too freely due to a general lack of understanding
and risk awareness, see e.g. [22]. Since Android 6.0, users are asked to authorise a permission
KA Authentication, Authorisation & Accountability | July 2021
Page 7
## Page 9

when it is ﬁrst needed. Signature permissions have an impact on platform integrity and can
only be used by apps authorised by the platform provider; app and permission have to be
signed by the same private key. For further details see the Web & Mobile Security CyBOK
Knowledge Area [23].
3.1.7
Digital Rights Management
Digital Rights Management (DRM) has its origin in the entertainment sector. Uncontrolled
copying of digital content such as games, movies or music would seriously impair the busi-
ness models of content producers and distributors. These parties hence have an interest in
controlling how their content can be accessed and used on their customers’ devices. Policies
can regulate the number of times content can be accessed, how long content can be sampled
for free, the number of devices it can be accessed from, or the pricing of content access.
DRM turns the familiar access control paradigm on its head. DRM imposes the security
policy of an external party on the system owner rather than protecting the system owner
from external parties. Superdistribution captures the scenario where data are distributed in
protected containers and can be freely redistributed. Labels specifying the terms of use are
attached to the containers. The data can only be used on machines equipped with a so-called
Superdistribution Label Reader that can unpack the container and track (and report) the usage
of data, and enforce the terms of use [24]. The search for such a tamper resistant enforcement
mechanism was one of the driving forces of Trusted Computing.
The level of tamper resistance required depends on the anticipated threats. Trusted Platform
Modules are a hardware solution giving a high degree of assurance. Enclaves in Intel SGX are
a solution in system software. Document readers that do not permit copying implement this
concept within an application. Sticky policies pursue a related idea [25]; policies stick to an
object and are evaluated whenever the object is accessed.
Attestation provides trustworthy information about a platform’s conﬁguration. Direct anony-
mous attestation implements this service in a way that protects user privacy [26]. Remote
attestation can be used with security policies that are predicated on the software running on
a remote machine. For example, a content owner could check the software conﬁguration at a
destination before releasing content. In the FIDO Universal Authentication Framework (FIDO
UAF) just the model of the authenticator device is attested. All devices of a given model hold
the same private attestation key [27].
For a brief period, it was fashionable to use Digital Rights Management as the generic term
subsuming ‘traditional’ access control as a special case.
3.1.8
Usage Control
Usage Control (UCON) was proposed as a framework encompassing authorisations based
on the attributes of subject and object, obligations, and conditions [6]. In [6], obligations are
additional actions a user has to perform to be granted access, e.g., clicking on a link to agree
to the terms of use. In today’s use of the term, obligations may also be actions the system has
to perform, e.g., logging an access request. Such actions may have to be performed before,
during or after an access happens. Conditions are aspects independent of subject and object,
e.g., time of day when a policy permits access only during ofﬁce hours or the location of the
machine access is requested from. Examples for the latter are policies permitting certain
requests only when issued from the system console, giving access only from machines in
KA Authentication, Authorisation & Accountability | July 2021
Page 8
## Page 10

the local network, or policies that consider the country attributed to the IP address a request
comes from. Many concepts from UCON have been adopted in the XACML 3.0 standard [28].
Usage control may also include provisions for what happens after an object is accessed, e.g.,
that a document can be read but its content cannot be copied or adjustment of attributes
after an access has been performed, e.g., decrementing the counter of free articles a visitor
may access. In another interpretation, ‘traditional’ access control deals with the elementary
access operations found at an infrastructure level while usage control addresses entire work
ﬂows at the application level. In telecom services, usage control may put limits on trafﬁc
volume.
3.2
Enforcing Access Control
To enforce a security policy, this policy ﬁrst has to be set. For a given request, a decision has
to be made about whether the request complies with the policy, which may need additional
information from other sources. Finally, the decision has to be conveyed to the component
that manages the resource requested. In the terminology of XACML, this involves
• Policy Administration Points where policies are set,
• Policy Decision Points where decisions are made,
• Policy Information Points that can be queried for further inputs to the decision algorithm,
• Policy Enforcement Points that execute the decision.
3.2.1
Delegation and Revocation
Delegation and granting of access rights both refer to situations where a principal, or a subject,
gets an access right from someone else. The research literature does not have ﬁrm deﬁnitions
for those terms, and the trade literature even less so. Granting tends to be used in a generic
sense; granted access rights often refer to the current access rights of a subject that delivers a
request to a reference monitor. Delegation is sometimes, but not always, used more narrowly
for granting short-lived access rights during the execution of a process. For example, XACML
distinguishes between policy administration and dynamic delegation that “permits some users
to create policies of limited duration to delegate certain capabilities to others” [29].
A second possible distinction lets delegation refer only to the granting of access rights held
by the delegator, while granting access also includes situations where a managing principal
assigns access rights to others but is not permitted to exercise those rights itself.
Rights may not always be granted in perpetuity. The grantor may set an expiry date on the
delegation, a right may be valid only for the current session, or there may be a revocation
mechanism such as the Online Certiﬁcate Status Protocol (OCSP) for X.509 certiﬁcates (see
Section 4.1). OCSP is supported by all major browsers. Revocation lists are suitable when
online checks are not feasible and when it is known in advance where a granted right may be
consumed.
KA Authentication, Authorisation & Accountability | July 2021
Page 9
## Page 14

4.2
Origin-based Policies
In web applications, clients and servers communicate via the HTTP protocol. The client
browser sends HTTP requests; the server returns result pages. The browser represents the
page internally in the document object in the Document Object Model (DOM). Security policies
specify which resources a script in a web page is allowed to access, or which servers an
XMLHttpRequest may refer to. Web applications are thus the principals in access control.
By convention, principal names are the domain names of the server hosting an application;
the policy decision point (cf. Section 3.2) at the client side is located in the browser.
The prototype policy for web applications is the Same Origin Policy (SOP), stating that a script
may only connect back to the origin it came from or that an HTTP cookie is only included
in requests to the domain that had placed the cookie. Two pages have the same origin if
they share protocol, host name and port number. Certain actions may be exempt from the
same origin policy. For example, a web page may contain links to images from other domains,
reﬂecting a view that images are innocuous data without malign side effects. There exist
variations of the SOP, e.g., policies for cookies that also consider the directory path. There is
also the option to set the HttpOnly ﬂag in a Set-Cookie HTTP response header so that
the cookie cannot be accessed by client side scripts.
Sender Policy Framework (SPF) [45] implements origin-based access control in the email
system as a measure against spooﬁng the sending domain of an email. Domain owners
publish SPF policies in their DNS zone. An SMTP server can then use the domain part of
the MAIL FROM identity to look up the policy and consult this policy to check whether the IP
address of the SMTP client is authorised to send mail from that domain.
4.2.1
Cross-site Scripting
Cross-site scripting attacks on web applications can be treated as cases of failed authen-
tication in access control. The browser lets all scripts that arrive in a web page speak for
the origin of that page. A browser would then run a script injected by the attacker in the
context of an origin other than the attacker’s. Content Security Policy (CSP) reﬁnes SOP-based
access control. The web server conveys a policy to the browser that characterises the scripts
authorised to speak for that server [42]. Typically, this is done by specifying a directory path
on the web server where authorised scripts (and other web elements) will be placed.
The use of CSP in practice has been examined in [46], observing that the unsafe-inline
directive disabling CSP for all pages from a given domain was widely used. This is a familiar
policy management issue. A new security mechanism is deployed but quickly disabled
because it interferes too much with established practices. Moreover, CSP had an inherent
vulnerability related to callbacks. Callbacks are names of scripts passed as arguments to
other (authorised) scripts, but arguments are not covered by CSP. In strict CSP policies, the
server declares a nonce in the CSP policy it sends to the client as the script source. The server
also includes this nonce as an attribute in all scripts fetched by the client. The client’s browser
only accepts scripts that contain this nonce as an attribute. Nonces must only be used once
and must be unpredictable.
KA Authentication, Authorisation & Accountability | July 2021
Page 13
## Page 16

4.4
Cryptography and Access Control
Access control mechanisms in an operating system implement a logical defence. Access
requests passed via the reference monitor will be policed. This includes requests for direct
memory access. However, data are stored in the clear and a party with physical access to the
storage medium can retrieve the data and thus bypass logical access control. When solutions
for the protection of unclassiﬁed but sensitive data were evaluated in the U.S. in the 1970s, it
was decided that encrypting the data was the best way forward. Access control would then
be applied to the keys needed to unlock the data.
4.4.1
Attribute-Based Encryption
Cloud computing has raised the interest in access control on encrypted data over the past
decade. Storing data in encrypted form protects their conﬁdentiality but creates a key manage-
ment challenge. Attribute-Based Encryption (ABE) addresses this challenge by constructing
encryption schemes that enforce attribute-based decryption policies. Policies are logical
predicates over attributes, represented as access structures. The Key Generator is a Trusted
Third Party that generates private keys and has to check a user’s policy / attributes before
issuing a private key. The Key Generator is thus in a position to recreate private keys.
Key-Policy Attribute-Based Encryption (KP-ABE) works with policies that deﬁne a user’s access
rights [43]. From the corresponding access structure, the Key Generator creates a private
decryption key. Documents are encrypted under a set of attributes. In Ciphertext-Policy
Attribute-Based Encryption (CP-ABE) [48], the policy refers to the document and the access
structure is used for encryption. The user’s private key created by the Key Generator depends
on the user’s attribute set. In both variants, decryption is possible if and only if the given
attribute set satisﬁes the given access structure.
A study of the feasibility of ABE in realistic dynamic settings had concluded that the overheads
incurred by those schemes were still prohibitive [49]. Efﬁcient encryption and decryption do
not necessarily imply an efﬁcient access control system.
4.4.2
Key-centric Access Control
In distributed systems, access requests may be digitally signed. Access rights could then
be granted directly to the public veriﬁcation key without the need to bind the public key to
some other principal. SPKI/SDSI uses authorisation certiﬁcates for implementing key centric
access control, where (names of) public keys are bound to access rights [50]. The right to
further delegate an access right is controlled by a delegation ﬂag.
Cryptographic keys are rarely suitable principals for access control, however. They would
need to have an obvious meaning in the application domain that provides the context for a
given security policy. In most cases, cryptographic keys would be subjects speaking for some
principal. Constrained delegation reﬁnes the basic delegation mechanism of SPKI/SDSI so
that separation of duties policies can be enforced [51].
KA Authentication, Authorisation & Accountability | July 2021
Page 15
## Page 17

5
AUTHENTICATION
[52, 53, 54, 55, 56, 57]
Authentication in a narrow sense veriﬁes the identity of a user logging in – locally or remotely
– and binds the corresponding user identity to a subject. User authentication based on
passwords is a common method. Some applications have adopted biometric authentication
as an alternative. Authentication in distributed systems often entails key establishment.
Some security taxonomies thus reduce authentication to a ‘heartbeat’ property to separate
authentication from key establishment. The design of authentication protocols is a mature
area in security research with good tool support for formal analysis. Standard protocols such
as Kerberos, SAML, or OAuth are deployed widely today.
We will give a brief overview of identity management before moving to password-based and
biometric user authentication. We then cover authentication protocols from the Needham-
Schroeder protocol via Kerberos and SAML to OAuth 2.0, observing that OAuth 2.0 is more of
an authorisation protocol than an authentication protocol. We conclude with an overview of
formalisations of authentication properties that serve as the basis for a formal analysis of
authentication protocols.
5.1
Identity Management
Following NIST, “identity management systems are responsible for the creation, use, and termi-
nation of electronic identities”. This includes operational aspects when creating and deleting
electronic identities. On creation, one question is how strongly electronic identities must
be linked to persons. In some sensitive areas, strong links have to be established and doc-
umented. For example, money laundering rules may demand a thorough veriﬁcation of an
account holder’s identity. In other areas, electronic identities need not to be tied to a person.
Privacy by design implies that such applications should use electronic identities that cannot
be linked to persons. Identity management may also link access rights to an electronic identity,
either directly or via some layer of indirection such as a role. Electronic identities should be
terminated when they are no longer required, e.g. when a person leaves an organisation. Care
has to be taken that this is done on all systems where this identity had been registered.
Electronic identities exist at different layers. There are identities for internal system purposes,
such as user identities in an operating system. These identities must be locally unique and
could be created by system administrators (Linux). This can lead to problems when an identity
is taken out of use and re-assigned later. The new user may get unintended access to resources
the predecessor had access to. When organisations merge, collisions between identities may
arise that identity management then must address. Alternatively, identities could be long
random strings (Windows). The probability for one of the problems just mentioned to arise is
then negligible, but when a user account is re-created, a new random identity is assigned so
access rights have to be reassigned from scratch.
Electronic identities such as user names and email addresses could be random strings, but
it is often preferable to assign understandable identities. There is, for example, merit in
communicating with meaningful email addresses. Email addresses can be taken out of use
and re-assigned later, but a user may then receive emails intended for its previous owner.
Web applications often use email addresses as electronic identities. This is convenient for
contacting the user, and it is convenient for users as they do not have to remember a new
KA Authentication, Authorisation & Accountability | July 2021
Page 16
## Page 18

identity. There are alternatives, such as FIDO UAF (Section 5.2.3), where electronic identities
are randomly created public keys and a back channel for resetting passwords is not required
as no passwords are used.
Identity management can also be viewed from a person’s perspective. A person using different
identities with different organisations may want to manage how identities are revealed to
other parties.
5.2
User Authentication
Access requests are issued by subjects. Subjects can be associated with security attributes
when they are created or during their lifetime. Authentication can then be viewed as the service
that validates the security attributes of a subject when it is created. When subjects are created
due to some user action, and when their security attributes depend on the corresponding user
identity, user authentication has to give a reasonable degree of assurance that the user identity
linked to the subject belongs to the user who had triggered the creation of the subject. The
degree of assurance (strength of authentication) should be commensurate with the severity
of the risk one wants to mitigate. The term risk-based authentication thus states the obvious.
User authentication can also support accountability, as further elaborated in Section 6. Au-
thentication ceremony refers to the steps a user has to go through to be authenticated.
There are access control systems where the security attributes of a subject persist throughout
the lifetime of that subject. Many operating systems adopt this approach. Policy changes do
not affect active processes, but the lifetime of subjects is limited, which limits the period when
the new policy is not applied consistently. Alternatively, the attributes of a subject are checked
each time it issues a request. For example, a user already logged in to a banking application
is authenticated again when requesting a funds transfer. When the focus moves from the
subject to individual requests, authentication can be viewed as the service that checks the
validity of the security attributes submitted with the request to the decision algorithm.
5.2.1
Passwords
When passwords are employed for user authentication, protective measures at the system
side include the storing of hashed (Unix, Linux) or encrypted (Windows) passwords, the salting
of passwords, and shadow password ﬁles that move sensitive data out of world-readable
password ﬁles. Protective measures at the user side include guidance on the proper choice
and handling of passwords, and security awareness programs that try to instil behaviour
that assures the link between a person and a principal. Recommendations in this area are
changing. The Digital Identity Guidelines published by NIST build on assessments of the
observed effectiveness of previous password rules and reﬂect the fact that users today have
to manage passwords for multiple accounts [57]. The new recommendations advise
• against automatic password expiry; passwords should only be changed when there is a
reason;
• against rules for complex passwords; password length matters more than complexity;
• against password hints or knowledge-based authentication; in an era of social networks
too much information about a person can be found in public sources;
• to enable “show password while typing” and to allow paste-in password ﬁelds.
KA Authentication, Authorisation & Accountability | July 2021
Page 17
## Page 19

Password-based protocols for remote authentication are RADIUS, DIAMETER (both covered
in the Network Security CyBOK Knowledge Area [58]), HTTP Digest Authentication, and to
some extent Kerberos (Section 5.3.2). Password guidance is further discussed in the Human
Factors CyBOK Knowledge Area [59].
5.2.2
Biometrics for Authentication
Numerous well-rehearsed arguments explain why passwords work poorly in practice. Biomet-
rics are an alternative that avoids the cognitive load attached to password-based authentica-
tion. Fingerprint and face recognition are the two main methods deployed for biometric user
authentication, known as veriﬁcation in that domain.
Biometric features must be sufﬁciently unique to distinguish between users, but ﬁngerprints
or faces cannot be considered as secrets. Fingerprints are left in many places, for example.
Biometric features are thus better treated as public information when conducting a security
analysis and the process of capturing the features during authentication has to offer an
adequate level of liveness detection, be it through supervision of that process or through device
features. Employing biometrics for user authentication makes the following assumptions:
• The biometric features uniquely identify a person; face, ﬁngerprints, and iris patterns
may serve as examples.
• The features are stable; the effects of aging on ﬁngerprint recognition are surveyed, e.g.,
in [60].
• The features can be conveniently captured in operational settings.
• The features cannot be spoofed during user authentication.
User authentication, known as veriﬁcation in biometrics, starts from a template captured
by a device. From the template, a feature vector is extracted. For example, the template
may be the image of a ﬁngerprint, the features are the positions of so-called minutiae (ridge
endings, bifurcations, whorls, etc.). Users initially register a reference feature vector. During
authentication, a new template is captured, features are extracted and compared with the
reference values. A user is authenticated if the number of matching features exceeds a given
threshold. This process may fail for various reasons:
• Failure to capture: this may happen at registration when it is not possible to extract a
sufﬁcient number of features, or during authentication.
• False rejects: the genuine user is rejected because the number of matches between
reference features and extracted features is insufﬁcient.
• False accepts: a wrong user is accepted as the matching threshold is exceeded.
• Spooﬁng: to deceive the device capturing the template, some object carrying the user’s
features is presented. Liveness detection tries to ensure that templates are captured
from the very person that is being authenticated [61].
Biometric authentication based on face recognition or ﬁngerprints is used increasingly at
automated border control gates [62]. It has also become a feature on mobile devices, see e.g.
[63]. A survey of the current state-of-the-art approaches to biometric authentication is given
in [64].
KA Authentication, Authorisation & Accountability | July 2021
Page 18
## Page 20

5.2.3
Authentication Tokens
Authentication by password relies on “something you know”. Biometric authentication builds
on “who you are”. In a further alternative, users are issued with a device (a.k.a. token or security
key, not to be confused with a cryptographic key) that computes a one-time password (OTP)
synchronised with the authenticator, or a response to a challenge set by the authenticator.
Possession of the device is then necessary for successful authentication, which is thus based
on “something you have”.
A token could be a small hand-held device with an LED display for showing an OTP that the
user enters in a log-in form; RSA SecureID and YubiKey are examples for this type of token.
A token could come with a numeric keypad in addition to the LED display and with a ‘sign’
button. The holder could then receive a challenge, e.g., an 8-digit number, enter it at the
keypad, press ‘sign’ to ask the token to compute and display the response, and then enter
the response in a log-in form. Some e-banking services use this type of token for account
holder authentication. With PhotoTAN devices, the challenge is sent as a QR code to the
user’s computer and scanned from the screen by the PhotoTAN device. When authentication
is based on a secret shared between token and server, different tokens must be used for
different servers.
The FIDO authenticator is a token that can create public key / private key pairs; public keys
serve as identiﬁers, private keys are used for generating digital signatures [27]. In FIDO
UAF, users register a public key with a server. The same token can be used for different
servers, but with different keys. User authentication is based on a challenge-response pattern
(Section 5.4.1), where the user’s authenticator digitally signs the response to the server’s
challenge. The response is veriﬁed using the public key registered with the server.
In some applications, possession of the token is sufﬁcient for user authentication. In other
applications, authentication is a two-stage process. First, the token authenticates the user,
e.g., based on a PIN or a ﬁngerprint. In a second stage, the server authenticates the token. It
will depend on the threat model whether ‘weak’ authentication in the ﬁrst stage and ‘strong’
authentication in the second stage can provide adequate security.
Apps on smartphones can provide the same functionality as authentication tokens, but smart-
phones are not dedicated security devices. User authentication may then be compromised
via attacks on the smartphone. This may become even easier when smartphones come with
a secondary authentication mechanism for use when a device is partially locked, with a less
onerous but also less secure authentication ceremony. This creates a conﬂict between the
interests of smartphone manufacturers who value ease-of-use of a communications device,
and the interests of the providers of sensitive applications searching for a security token.
5.2.4
Behavioural Authentication
Behavioural authentication analyses “what you do”, lending itself naturally to continuous au-
thentication. Keystroke dynamics [65, 66] can be captured without dedicated equipment.
Characteristic features of hand writing are writing speed and pen pressure [67]. Here, special
pens or writing pads need to be deployed. Voice recognition needs a microphone. Smart-
phones come with various sensors such as touch screens and microphones that are being
utilised for behavioural authentication today. The requirements on behavioural authentication
are the same as those listed in Section 5.2.2:
• The behavioural features uniquely identify a person.
KA Authentication, Authorisation & Accountability | July 2021
Page 19
## Page 21

• The features are stable and unaffected by temporary impairments.
• The features can be conveniently captured in operational settings.
• The features cannot be spoofed during user authentication.
Advocates of continuous authentication promise minimum friction, maximum security. Be-
havioural authentication does not inconvenience the user with authentication ceremonies, but
variations in user behaviour may cause false rejects. For example, how will a severe cold affect
voice recognition? There needs to be a smooth fall-back when behavioural authentication fails.
Security depends on the strength of liveness detection. For example, will voice recognition
detect synthesised speech or a very proﬁcient human voice imitator? Without a precise threat
model, behavioural authentication can only offer uncertain security guarantees. There is a
growing research literature on different modes of behavioural authentication. Criteria for
assessing the actual contributions of this research include sample size and composition,
whether longitudinal studies have been performed, the existence of an explicit threat model
and resistance to targeted impersonation attempts.
5.2.5
Two-factor Authentication 2FA
Multi-factor authentication combines several user authentication methods for increased
security. The European Payment Services Directive 2 (PSD2, Directive (EU) 2015/2366), written
for the regulation of ﬁnancial service providers, prescribes two-factor authentication (2FA) for
online payments (with a few exceptions). PSD2 thus is a case study on rolling out large scale
2FA solutions.
The two factors could be a password and an authentication token for computing Transaction
Authentication Numbers (TANs) uniquely tied to the content of a transaction. The token could
be a separate device; if the device is tied to one payment service only, customers would have
to carry multiple devices with them. For devices that can be used with several services, some
level of prior standardisation is required. The FIDO alliance has been promoting its standards
for PSD2 compliant two-factor authentication.
The token could be a smartphone registered with the service; customers could then install
apps for several services on the same device. This approach has been favoured by many
banks. However, when passwords (or PINs) and TANs are handled by the same device, the
two mechanisms are no longer independent, reducing the security gains claimed for 2FA.
In contrast to the European Trust Services and Electronic identiﬁcation regulation (eID Direc-
tive - Regulation (EU) No 910/2014) that speciﬁes requirements on secure signature creation
devices, PSD2 does not impose security requirements on the devices used for user authenti-
cation but wants “to allow for the use of all common types of devices (such as computers,
tablets and mobile phones) for carrying out different payment services”. PSD2 and the eID Di-
rective thus strike different balances between ease-of-use and security, a trade-off notoriously
difﬁcult to get right.
KA Authentication, Authorisation & Accountability | July 2021
Page 20
## Page 24

federated single sign-on protocol where the relying party decides how to use assertions when
making decisions according to its own security policy.
In the practical deployment of SAML, parsing XML documents – the price to be paid for
employing a meta-protocol – can create non-trivial overheads and can introduce security
vulnerabilities. Furthermore, the advent of smartphones has made it easier to access the
internet from mobile user devices, removing one of the reasons for introducing a meta-protocol
between web services and the underlying IT systems.
5.3.4
OAuth 2 – OpenID Connect
Newer protocols such as OAuth 2.0 [55] and OpenID Connect [69] run directly over HTTP
and provide authentication and authorisation. The parties involved include a user who owns
resources, the resource owner, a resource server that stores the user’s resources, a so-called
client application that wants to be granted access to those resources, and an Authorisation
Server (AS) that can authenticate users and client applications.
Clients have to be registered with the AS. They will receive a public client ID and a client
secret shared with the AS. This secret is used for establishing secure sessions between the
client and the AS. The client also registers redirect URIs with the AS. The AS will redirect a
user agent only to those registered redirect URIs. Proper deﬁnition of the redirect URIs is
primarily a matter for the client, and can also be enforced by the AS. Weak settings are open
to exploitation by attackers.
In an OAuth protocol run (a high level overview is given in Figure 5), the user agent (browser)
has opened a window for the client application. In the client window, an authorisation request
can be triggered 1 ; the request also contains a redirect URI. The user agent then typically
conveys the authorisation request and the user’s authorisation to the AS 2 . A secure session
between the user agent and the AS is required, and may already exist if the user has logged
in previously at the AS. If authorisation is granted, an authorisation grant is returned to the
user agent 3 , which will pass it on to the redirect URI given by the client 4 . The client then
posts the authorisation grant and a redirect URI to the AS 5 . It is assumed that the AS can
authenticate this message as coming from the client. If the request is valid, the AS returns an
access token to the redirect URI provided, where the token can be used to retrieve the resource
from the resource server 6 .
Authorisation requests and authorisation grantsindexauthorisation grant are linked via a re-
quest ID, called state in OAuth. Omitting the request ID or using a ﬁxed value had introduced
vulnerabilities in applications using OAuth, see e.g. [70, 71].
4. authorisation grant
2. auth request + approval
user
user
agent
6. access token
-
3. authorisation grant
-
author.
server
5. authorisation grant

1. authorisation request
client
(app)
. ..... .... ..................................
..... ... ............
......................
Figure 5: Message ﬂow in OAuth 2.0.
There is a fundamental switch in focus compared to SSO protocols such as Kerberos and SAML
KA Authentication, Authorisation & Accountability | July 2021
Page 23
## Page 25

despite a considerable degree of similarity in the message ﬂows. In an OAuth 2.0 protocol
run the user is no longer the party requesting access to a resource owned by someone else,
but the party granting access to resources owned by the user. OAuth 2.0 has thus become an
authorisation protocol. Several assumptions about pre-existing trust relationships between
parties have to be met for OAuth to be secure. Conversely, one cannot take for granted that
the OAuth security properties still hold when the protocol is deployed in a new setting.
OpenID Connect puts user authentication back into the OAuth 2.0 message ﬂow. The client
application now doubles as a relying party, and the authorisation server becomes an authenti-
cation & authorisation server that issues digitally signed id tokens (authentication assertions
in SAML diction). An id token contains the name of the issuer, the name of the authenticated
user (called subject), the intended relying party (called audience), the nonce that had been
sent with the authentication request, an indicator of authentication strength, and other ﬁelds.
5.4
Facets of Authentication
We have sketched how user authentication in distributed systems ﬁrst integrated session
and key establishment with the process of verifying a user’s identity, and later established
authorisation practices to access a user’s resources. In communication security, peer entity
authentication refers to the process of verifying the identity of the peer in a connection and
data origin authentication to the process of verifying the origin of individual data items.
User authentication, whether relating to a local system or to a remote system, entails three
aspects:
• creating a new subject, e.g. a new process or a new session with a fresh session key,
• linking an internal entity, e.g. a user ID, to the subject,
• linking an external entity, e.g. a person, to an internal identity.
To differentiate between these aspects, the term key establishment was introduced in com-
munication security towards the end of the 1980s for the ﬁrst aspect. Entity authentication
stood for what was left. Quoting ISO/IEC 9798, “entity authentication mechanisms allow the
veriﬁcation, of an entity’s claimed identity, by another entity. The authenticity of the entity can
be ascertained only for the instance of the authentication exchange”. This property is related to
dead peer detection and to the heartbeat extension in RFC 6250 [72]. Note that this deﬁnition
does not distinguish between internal and external entities.
5.4.1
Patterns for Entity Authentication
Entity authentication according to the deﬁnition in ISO/IEC 9798 can be implemented with
challenge response-mechanisms. When prover and veriﬁer share a secret, the veriﬁer sends
an unpredictable challenge to the prover who constructs its response as a function of the
challenge and the shared secret. For example, HTTP digest authentication uses the hash of
the challenge, a password, and further data that binds authentication to a particular HTTP
request.
When public key cryptography is used, the veriﬁer needs the prover’s public key. With a digital
signature scheme, the veriﬁer could send the challenge in the clear and the prover could
respond with the signed challenge. With a public key encryption scheme, the veriﬁer could
encrypt the challenge under the prover’s public key; a response constructed from the decrypted
KA Authentication, Authorisation & Accountability | July 2021
Page 24
## Page 26

challenge would authenticate the prover. The latter mechanism is used with Trusted Platform
Modules (TPMs) where successful decryption of data encrypted under the public endorsement
key of a TPM authenticates the TPM. In both cases, the veriﬁer needs an authentic copy of the
prover’s public veriﬁcation key. When users are identiﬁed by arbitrary public keys, no Public
Key Infrastructure is required and the public key could be set directly in a registration phase.
5.4.2
Correspondence Properties
The Public-Key Needham-Schroeder protocol uses public key encryption with its challenge-
response mechanism [52]. In this protocol, a malicious prover could decrypt a challenge
and reuse it in a protocol run with a third party pretending to be the original veriﬁer; the third
party would then respond to the veriﬁer although the veriﬁer is not engaged in a protocol
run with the third party [56]. This scenario would amount to an attack if the mismatch in the
assumptions about a protocol run is security relevant. The attack would be detected if the
identities of prover and veriﬁer are included in all messages. Note that in this ‘attack’ the
veriﬁer still correctly concludes that the prover is alive.
Matches in the assumptions about aspects of a protocol run held by the peers on completion of
a run can be captured by correspondence properties, as proposed in [73] and further elaborated
in [74]:
• Aliveness: whenever the veriﬁer (initiator) concludes a protocol run, the prover had also
been engaged in a protocol run.
• Weak agreement: whenever the veriﬁer (initiator) concludes a protocol run apparently
with a given prover, the prover had also been engaged in a protocol run, apparently with
that veriﬁer.
• Non-injective agreement: whenever the veriﬁer (initiator) concludes a protocol run appar-
ently with a given prover, the prover had also been engaged in a protocol run, apparently
with that veriﬁer, and responder and receiver agree on a speciﬁed set of data items
pertaining to a protocol run.
• Agreement: whenever the veriﬁer (initiator) concludes a protocol run apparently with a
given prover, the prover had also been engaged in a protocol run, apparently with that
veriﬁer, and responder and receiver agree on a speciﬁed set of data items pertaining to
a protocol run, and each protocol run of the veriﬁer corresponds to a unique protocol
run of the prover.
In the vulnerable Redirect/POST Binding in Google Applications there is no agreement on
the service provider an authentication assertion is intended for ([68], Section 5.3.3). Flawed
implementations of OAuth that use a ﬁxed value for the state variable do not even guarantee
aliveness ([70], Section 5.3.4).
Correspondence properties are intensional properties well suited for protocol analysis using
model checking. This line of research had reversed the earlier decision to separate pure entity
authentication from agreeing on session keys and again added agreement on certain data
items to authentication. TAMARIN [75] and ProVerif [76] are examples for tools that support
the automated analysis of authentication protocols.
KA Authentication, Authorisation & Accountability | July 2021
Page 25
## Page 28

6.1
Technical Aspects
Accountability supports processes that are launched after events have occurred. Such a
process may be a regular audit that checks whether an organisation complies with existing
regulations. It might represent a technical audit that scans logs in search for signs of a
cyber attack. It may also be an investigation triggered by an incident that tries to identify the
vulnerabilities exploited, or an investigation that tries to identify the parties responsible. In all
cases, the quality of the evidence is decisive.
The aforementioned processes make use of logs of events. Such logs may be kept by the
operating system, by networking devices, or by applications (Section 6.2 will give an example).
The nature of the events depends on the activity that is being monitored.
6.1.1
Audit Policies
Accountability is only as strong as the quality of evidence collected during operations. System
administrators may set audit policies that deﬁne which events will be logged. Examples for
such events are successful and failed authentication attempts, and decisions on sensitive
access requests. Operating systems and audit tools provide menus to guide administrators
through this task. Access control policies that specify as obligations that certain requests
must be logged also inﬂuence which evidence is collected.
6.1.2
Preserving the Evidence
Accountability is only as strong as the protection of the evidence collected during operations.
Attackers could try to hide their traces by deleting incriminating log entries once they have
acquired sufﬁcient privileges. They could then modify audit policies so that future actions are
not recorded, but should not be able to tamper with the evidence already collected.
Tamper resistance could rely on physical measures like printing the log on an endless paper
reel or writing the log to WORM (Write-Once, Read-Many) memory like an optical disk. Tamper
resistance could be supported by cryptography. Storing the log as a hash chain [80, 81] makes
it evident when entries have been removed, but does not guarantee that entries cannot be
lost.
Audit policies have to address situations where logging is disrupted, e.g., because the log
ﬁle has run out of space. Is it then acceptable to overwrite old entries or should the system
be stopped until proper auditing is again enabled? This conﬂict between availability and
accountability has to be resolved.
KA Authentication, Authorisation & Accountability | July 2021
Page 27
## Page 29

6.1.3
Analysing the Evidence
Audit logs can create large volumes of data and many entries are not security relevant so that
automated processing is required. Known attack patterns can be detected by their signatures.
Machine learning techniques can help to detect anomalies. Lessons learned when applying
this approach to network intrusion detection are discussed in [82]. Visualisation techniques
try to draw the administrators’ attention to the most relevant events.
6.1.4
Assessing the Evidence
Accountability is only as strong as the method of user authentication when legal or disciplinary
actions are to be supported. This relates to technical aspects of the authentication mechanism
and also to user resilience to phishing and social engineering attacks. Telling users not to
fall for obvious phishing attacks is easy, but a well-designed spear phishing attack will not be
obvious.
Accountability is only as strong as the organisational security policies on connecting devices,
e.g. USB tokens, to internal systems, and policies on access to external web sites. Account-
ability is only as strong as the defences against software vulnerabilities that can be exploited
to run code under a user identity without the user being aware of that fact, e.g. so-called
drive-by-downloads.
6.2
Privacy and Accountability
Privacy rules can have an impact on the events that may be logged. Employment law may, for
example, limit how closely a company monitors its employees, which might make it difﬁcult
to achieve accountability when rules have been broken.
Sometimes, there are technical resolutions to such conﬂicts between legal goals. Take the
example of a company that is not permitted to log which external websites employees connect
to: when an external site is attacked from within the company network, it is desirable that the
perpetrator can be held accountable. To achieve both goals, the company gateway would log
for outgoing requests only the internal IP address and the port number used with the global
IP address. There is thus no record of visited websites. If an attack is reported, the website
affected can provide the port number the attack came from, establishing the link between the
internal IP address and the visited site.
Conversely, logging may have unintended privacy impacts. Take Certiﬁcate Transparency
[RFC 6962] as an example. Certiﬁcate Transparency is a logging service for the issuers of
TLS certiﬁcates. Participating Certiﬁcate Authorities record the issuance of certiﬁcates with
this service. Domain owners can scan the log for certiﬁcates for their domain that they had
not asked for, i.e., detect authentication failures at issuers. This service was introduced in
reaction to attacks where such misissued certiﬁcates had been used to impersonate the
domain affected, and makes issuers accountable to domain owners.
Private subdomains are subdomains created for internal use only. When a certiﬁcate for a
private subdomain is requested, the certiﬁcate will be recorded in the Certiﬁcate Transparency
log disclosing the existence of the private subdomain to the public [83].
KA Authentication, Authorisation & Accountability | July 2021
Page 28
## Page 30

6.3
Distributed Logs
Logs may be kept to hold the users of a system accountable. Logs may be kept to hold the
owner of a system accountable. In the latter case, auditors may require that the logging device
is sealed, i.e., rely on a physical root of trust. Alternatively, logs could be kept in a distributed
system run by independent nodes where there are sufﬁcient barriers to forming alliances that
can take over the system.
The nodes maintaining the log need to synchronise their versions of the log. The overheads
for synchronisation, or consensus, depend on the failure model for the nodes and for the
communication network, and on the rules for joining the distributed system. Systems may be
open for anyone, or be governed by a membership service. The recent interest in blockchains
extends to this type of logging solutions.
6.4
Related Concepts
The deﬁnition at the start of Section 6 refers to non-repudiation and intrusion detection. Non-
repudiation has a speciﬁc meaning in communication security, viz. providing unforgeable
evidence that a speciﬁc action occurred. This goal is not necessarily achieved by logging
mechanisms; they may protect the entries recorded, but may record entries that have already
been manipulated.
Intrusion detection (see the Security Operations & Incident Management CyBOK Knowledge
Area [1]) is an area of its own with overlapping goals. Intrusion detection does not have the
requirement for actions of an entity to be traced uniquely to that entity. The focus will be more
on detecting attacks than detecting the attacker.
The deﬁnition given subsumes both the accountability of legal persons and technical inves-
tigations into security breaches. The standards of evidence may be higher in the ﬁrst case.
Tracing actions uniquely to an entity leads to cyber attribution, the process of tracking and
identifying the perpetrators of a cyber attack. Circumstantial evidence such as similarity
in malware may be used in this process, and mis-attribution due to false ﬂag operations is
an issue. Calling for DRM to protect the intellectual property of content owners, because
digital content can be copied so easily, but assuming that malware cannot be copied would
be incongruous.
APPLYING THE KNOWLEDGE
IT security mechanisms should not be deployed for their own sake but for a reason. The
reason has to come from an application in need of protection. An organisational policy
would capture the protection requirements and then be implemented by an automated policy
(Section 3.1.1). Sometimes, this process can start from a clearly deﬁned organisational policy.
The policies governing access to classiﬁed paper documents are an example. There, the
translation into automated policies did not have to bridge a wide conceptual gap, although
there were unanticipated twists, e.g., the no write-up policy of the BLP model. These speciﬁc
circumstances may have raised the unwarranted expectation that this approach would work
in general. The fact that these policies were applied in highly hierarchical organisations and
were fairly stable are further points worth noting.
KA Authentication, Authorisation & Accountability | July 2021
Page 29
## Page 31

Sinclair et al. paint a picture of a very different world [84]. Their observations can be sum-
marized under the headings of translation (from organisational to automated policies) and
automation. Any translation has to start from a source document, in our case an organisa-
tional policy. The translator will face problems when the source is ambiguous or inconsistent.
This situation is more likely to arise in organisations with a matrixed structure, where several
entities are setting policies, than in strictly hierarchical organisations. Moreover, the wider the
language gap between the source document and the destination document, the more difﬁcult
translation becomes, and the more difﬁcult it is to ascertain that the translation meets the
spirit of the source. The latter step is a prerequisite for policy certiﬁcation, i.e., management
approval of a given automated policy.
Organisational policies may intentionally leave decisions at the discretion of caseworkers,
e.g., for handling situations where none of the existing rules is directly applicable or where
competing rules apply. It is a purpose of automation to remove discretion. Removing discretion
adds rules that do not have a counterpart in the organisational policy. Creating an automated
policy is then more than translation, it becomes an exercise in creative writing in the spirit of
the organisational policy. To do this job well, the writer needs a good understanding of the
applications and their workﬂows, on top of proﬁciency in the target language (the domain
of IT experts). Automated policies based on na¨ıve assumptions easily become denial-of-
service attacks on the user. As a related point, there is a tension between the competing
goals of keeping a policy simple – which may be feasible in an organisational policy that
leaves room for discretion – and of requiring the (automated) policy to cater for a variety of
different contexts. This explains why in many cases the number of rules created to cater for
exceptions to the general rules ends up being overwhelming. Points that span organisational
and automated policies are the handling of dynamic policy changes and the analysis of the
side-effects of policy rules in highly complex systems.
The literature on security operations has to say more about the points raised in this section
than the research literature on IT security, which has a habit of abstracting problems to a point
where much of the awkward issues encountered in real life have disappeared [84], and then
confusing its simpliﬁed models with reality.
Similar disconnects between application experts and infrastructure experts exist within the IT
domain. Dynamically conﬁgurable applications are running foul of well-intended policies such
as SOP (Section 4.2) and CSP (Section 4.2.1). Organisations may then opt for open policies
that provide no protection but allow the dynamic applications to run, or applications writers
may explore workarounds accepted by the automated policy but still defeating its spirit.
CONCLUSIONS
Access control has kept adapting to the changing applications of IT systems. Access control
was originally conceived for the protection of sensitive data in multi-user and multi-level
secure systems. Access control without user identities was literally unthinkable. Applications
have since changed and some require new modes of access control. One could then reserve
‘access control’ for the original setting and invent new terms for each new ﬂavour of access
control. DRM may serve as an example. This KA has not taken this route but applied ‘access
control’, ‘authentication’, and ‘authorisation’ more generally while staying true to the generic
meanings of these terms. User identities have lost their prominence along this way. Code
(apps) and web domains have taken their place.
KA Authentication, Authorisation & Accountability | July 2021
Page 30
## Page 33

[6] J. Park and R. Sandhu, “The UCON ABC usage control model,” ACM Transactions on
Information and System Security (TISSEC), vol. 7, no. 1, pp. 128–174, 2004.
[7] R. S. Sandhu, D. Ferraiolo, and R. Kuhn, “The NIST model for role based access control:
Toward a uniﬁed standard,” in Proceedings of the 5th ACM Workshop on Role Based
Access Control, July 2000, pp. 47–63.
[8] F. B. Schneider, “Enforceable security policies,” ACM Transactions on Information Systems
Security, vol. 3, no. 1, pp. 30–50, Feb. 2000.
[9] M. C. Libicki, “Cyberspace is not a warﬁghting domain,” ISJLP, vol. 8, p. 321, 2012.
[10] R. Kissel, Revision 2: Glossary of key information security terms.
Diane Publishing, 2013.
[11] J. Crampton and J. Sellwood, “Path conditions and principal matching: a new approach
to access control,” in Proceedings of the 19th ACM symposium on Access control models
and technologies.
ACM, 2014, pp. 187–198.
[12] B. Lampson, “Protection,” Operating Systems Review, pp. 8, 1, 18–24, 1974.
[13] H. Bos, The Cyber Security Body of Knowledge.
University of Bristol, 2021, ch. Operating
Systems & Virtualisation, version 1.0.1. [Online]. Available: https://www.cybok.org/
[14] P. Loscocco and S. Smalley, “Integrating ﬂexible support for security policies into the
linux operating system.” in USENIX Annual Technical Conference, FREENIX Track, 2001,
pp. 29–42.
[15] F. Mayer, D. Caplan, and K. MacMillan, SELinux by example: using security enhanced Linux.
Pearson Education, 2006.
[16] S. Smalley and R. Craig, “Security enhanced (SE) Android: Bringing ﬂexible MAC to
Android,” in NDSS, vol. 310, 2013, pp. 20–38.
[17] N. Condori-Fern´andez and, V. N. L. Franqueira, and R. Wieringa, “Report on the survey of
role-based access control (RBAC) in practice,” Centre for Telematics and Information
Technology, University of Twente, Tech. Rep. TR-CTIT-12-06, 2012.
[18] V. C. Hu, D. Ferraiolo, R. Kuhn, A. R. Friedman, A. J. Lang, M. M. Cogdell, A. Schnitzer,
K. Sandlin, R. Miller, K. Scarfone et al., “Guide to attribute based access control (ABAC)
deﬁnition and considerations (draft),” NIST special publication, vol. 800, no. 162, 2013.
[19] L. Gong, M. Dageforde, and G. W. Ellison, Inside Java 2 Platform Security, 2nd ed. Reading,
MA: Addison-Wesley, 2003.
[20] B. A. La Macchia, S. Lange, M. Lyons, R. Martin, and K. T. Price, .NET Framework Security.
Boston, MA: Addison-Wesley Professional, 2002.
[21] N. Hardy, “The confused deputy,” Operating Systems Reviews, vol. 22, no. 4, pp. 36–38,
1988.
[22] A. P. Felt, E. Ha, S. Egelman, A. Haney, E. Chin, and D. Wagner, “Android permissions:
User attention, comprehension, and behavior,” in Proceedings of the Eighth Symposium
on Usable Privacy and Security, ser. SOUPS ’12.
New York, NY, USA: ACM, 2012, pp.
3:1–3:14. [Online]. Available: http://doi.acm.org/10.1145/2335356.2335360
[23] S. Fahl, The Cyber Security Body of Knowledge.
University of Bristol, 2021, ch. Web &
Mobile Security, version 1.0.1. [Online]. Available: https://www.cybok.org/
[24] R. Moan and I. Kawahara, “Superdistribution: An electronic infrastructure for the economy
of the future,” Transactions of Information Processing Society of Japan, vol. 38, no. 7, pp.
1465–1472, 1997.
[25] M. C. Mont, S. Pearson, and P. Bramhall, “Towards accountable management of identity
and privacy: Sticky policies and enforceable tracing services,” in 14th International Work-
shop on Database and Expert Systems Applications, 2003. Proceedings.
IEEE, 2003, pp.
377–382.
[26] E. Brickell, J. Camenisch, and L. Chen, “Direct anonymous attestation,” in Proceedings
of the 11th ACM conference on Computer and communications security.
ACM, 2004, pp.
KA Authentication, Authorisation & Accountability | July 2021
Page 32
## Page 34

132–145.
[27] M. Salah, R. Philpott, S. Srinivas, J. Kemp, and J. Hodges, “FIDO UAF architectural overview,”
FIDO Alliance, Tech. Rep., February 2018, draft 20.
[28] E. Rissanen, “eXtensible access control markup language (XACML),” Oasis, Tech. Rep.
xacml-3.0-core-spec-os-en, 2013, version 3.0.
[29] E. Rissanen, H. Lockhart, and T. Moses, “Xacml v3. 0 administration and delegation proﬁle
version 1.0,” Committee Draft, vol. 1, 2009.
[30] “DoD Trusted Computer System Evaluation Criteria,” U.S. Department of Defense, 1985,
DOD 5200.28-STD.
[31] ´U. Erlingsson and F. B. Schneider, “IRM enforcement of Java stack inspection,” in Pro-
ceedings of the 2000 IEEE Symposium on Security and Privacy, 2000, pp. 246–255.
[32] D. E. Bell and L. J. LaPadula, “Secure computer systems: Mathematical foundations,” The
MITRE Corporation, Bedford MA, Tech. Rep. ESD-TR-73-278, Nov. 1973.
[33] T. F. Lunt, D. E. Denning, R. R. Schell, M. Heckman, and W. R. Shockley, “The seaview
security model,” IEEE Transactions on software engineering, vol. 16, no. 6, pp. 593–607,
1990.
[34] K. J. Biba, “Integrity consideration for secure computer systems,” The MITRE Corporation,
Bedford, MA, Tech. Rep. ESD-TR-76-372, MTR-3153, April 1977.
[35] D. D. Clark and D. R. Wilson, “A comparison of commercial and military computer
security policies,” in Proceedings of the 1987 IEEE Symposium on Security and Privacy,
Oakland, California, USA, April 27-29, 1987, 1987, pp. 184–195. [Online]. Available:
https://doi.org/10.1109/SP.1987.10001
[36] D. F. C. Brewer and M. J. Nash, “The Chinese Wall security policy,” in Proceedings of the
1989 IEEE Symposium on Security and Privacy, 1989, pp. 206–214.
[37] M. A. Harrison, W. L. Ruzzo, and J. D. Ullman, “Protection in operating systems,” Commu-
nications of the ACM, vol. 19, no. 8, pp. 461–471, August 1976.
[38] N. Li, B. N. Grosof, and J. Feigenbaum, “Delegation logic: A logic-based approach to dis-
tributed authorization,” ACM Transactions on Information and System Security (TISSEC),
vol. 6, no. 1, pp. 128–171, 2003.
[39] M. Abadi, M. Burrows, B. W. Lampson, and G. D. Plotkin, “A calculus for access control in
distributed systems,” ACM Transactions Programming Language Systems, vol. 15, no. 4,
pp. 706–734, 1993. [Online]. Available: https://doi.org/10.1145/155183.155225
[40] M. Blaze, J. Feigenbaum, and J. Lacy, “Decentralized trust management,” in Proceedings
of the 1996 IEEE Symposium on Security and Privacy, 1996, pp. 164–173.
[41] J. DeTreville, “Binder, a logic-based security language,” in Proceedings 2002 IEEE Sympo-
sium on Security and Privacy.
IEEE, 2002, pp. 105–113.
[42] S. Stamm, B. Sterne, and G. Markham, “Reining in the web with content security policy,”
in Proceedings of the 19th international conference on World wide web.
ACM, 2010, pp.
921–930.
[43] V. Goyal, O. Pandey, A. Sahai, and B. Waters, “Attribute-based encryption for ﬁne-grained
access control of encrypted data,” in Proceedings of the 13th ACM conference on Com-
puter and communications security.
ACM, 2006, pp. 89–98.
[44] ITU-T, “X509 (2014) ISO/IEC 9594-8:2014/Cor 2:2016, Directory: Public-key and attribute
certiﬁcate framework [TECHNICAL CORRIGENDUM 2],” 2016.
[45] M. Wong and W. Schlitt, “Sender policy framework (SPF) for authorizing use of domains
in e-mail, version 1,” RFC 4408, Tech. Rep., 2006.
[46] L. Weichselbaum, M. Spagnuolo, S. Lekies, and A. Janc, “CSP is dead, long live CSP! on
the insecurity of whitelists and the future of content security policy,” in Proceedings of the
2016 ACM SIGSAC Conference on Computer and Communications Security.
ACM, 2016,
KA Authentication, Authorisation & Accountability | July 2021
Page 33
## Page 35

pp. 1376–1387.
[47] A.
Van
Kesteren,
“Cross-origin
resource
sharing,”
W3C
Working
Draft,
http://www.w3.org/TR/2014/REC-cors-********/, 2014.
[48] J. Bethencourt, A. Sahai, and B. Waters, “Ciphertext-policy attribute-based encryption,” in
2007 IEEE symposium on security and privacy (SP’07).
IEEE, 2007, pp. 321–334.
[49] W. C. Garrison, A. Shull, S. Myers, and A. J. Lee, “On the practicality of cryptographically
enforcing dynamic access control policies in the cloud,” in 2016 IEEE Symposium on
Security and Privacy (SP).
IEEE, 2016, pp. 819–838.
[50] C. M. Ellison, B. Frantz, B. W. Lampson, R. Rivest, B. Thomas, and T. Yl¨onen,
“SPKI certiﬁcate theory,” RFC, vol. 2693, pp. 1–43, 1999. [Online]. Available: https:
//doi.org/10.17487/RFC2693
[51] O. Bandmann, M. Dam, and B. S. Firozabadi, “Constrained delegation,” in Proceedings
2002 IEEE Symposium on Security and Privacy.
IEEE, 2002, pp. 131–140.
[52] R. M. Needham and M. D. Schroeder, “Using encryption for authentication in large net-
works of computers,” Communications of the ACM, vol. 21, no. 12, pp. 993–999, 1978.
[53] J. G. Steiner, B. C. Neuman, and J. I. Schiller, “Kerberos: An authentication service for
open network systems.” in Winter 1988 Usenix Conference.
Citeseer, 1988, pp. 191–202.
[54] H. Lockhart and B. Campbell, “Security assertion markup language (SAML) v2. 0 technical
overview,” OASIS Committee Draft, vol. 2, pp. 94–106, 2008.
[55] D. Hardt, “The OAuth 2.0 authorization framework,” Internet Requests for Comments,
RFC Editor, RFC 6749, October 2012. [Online]. Available: http://www.rfc-editor.org/rfc/
rfc6749.txt
[56] G. Lowe, “Breaking and ﬁxing the needham-schroeder public-key protocol using fdr,”
in International Workshop on Tools and Algorithms for the Construction and Analysis of
Systems.
Springer, 1996, pp. 147–166.
[57] P. A. Grassi, M. E. Garcia, and J. L. Fenton, “Digital identity guidelines,” NIST special
publication, vol. 800, pp. 63–3, 2017.
[58] C. Rossow and S. Jha, The Cyber Security Body of Knowledge.
University of Bristol,
2021, ch. Network Security, version 2.0. [Online]. Available: https://www.cybok.org/
[59] M. A. Sasse and A. Rashid, The Cyber Security Body of Knowledge.
University of Bristol,
2021, ch. Human Factors, version 1.0.1. [Online]. Available: https://www.cybok.org/
[60] S. K. Modi, S. J. Elliott, J. Whetsone, and H. Kim, “Impact of age groups on ﬁngerprint
recognition performance,” in 2007 IEEE Workshop on Automatic Identiﬁcation Advanced
Technologies, June 2007, pp. 19–23.
[61] L. Ghiani, D. Yambay, V. Mura, S. Tocco, G. L. Marcialis, F. Roli, and S. Schuckcrs, “Livdet
2013 ﬁngerprint liveness detection competition 2013,” in 2013 International Conference
on Biometrics (ICB).
IEEE, 2013, pp. 1–6.
[62] R. D. Labati, A. Genovese, E. Mu˜noz, V. Piuri, F. Scotti, and G. Sforza, “Biometric recognition
in automated border control: a survey,” ACM Computing Surveys (CSUR), vol. 49, no. 2,
p. 24, 2016.
[63] L. M. Mayron, “Biometric authentication on mobile devices.” IEEE Security & Privacy,
vol. 13, no. 3, pp. 70–73, 2015.
[64] M. S. Obaidat, I. Traore, and I. Woungang, Biometric-Based Physical and Cybersecurity
Systems.
Springer, 2019.
[65] R. Joyce and G. Gupta, “Identity authentication based on keystroke latencies,” Communi-
cations of the ACM, vol. 33, no. 2, pp. 168–176, 1990.
[66] F. Monrose and A. D. Rubin, “Keystroke dynamics as a biometric for authentication,”
Future Generation Computer Systems, vol. 16, no. 4, pp. 351–359, 2000.
[67] M. Ammar, Y. Yoshida, and T. Fukumura, “A new effective approach for on-line veriﬁca-
KA Authentication, Authorisation & Accountability | July 2021
Page 34
