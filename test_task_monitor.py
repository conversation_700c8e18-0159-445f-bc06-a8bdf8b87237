#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务监控逻辑
"""

import sys
import os
import time
import threading

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'Page_view/backend'))

from task_manager import TaskManager, WeeklyTaskScheduler

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Qjq123456',
    'database': 'cti_database',
    'charset': 'utf8mb4'
}

def test_task_monitor():
    """测试任务监控功能"""
    print("=== 测试任务监控功能 ===")
    
    try:
        # 创建任务管理器
        task_manager = TaskManager(DB_CONFIG)
        print("✓ 任务管理器创建成功")
        
        # 创建调度器
        scheduler = WeeklyTaskScheduler(task_manager)
        print("✓ 调度器创建成功")
        
        # 测试手动执行任务
        print("\n--- 测试手动执行任务 ---")
        success = scheduler.run_now()
        
        if success:
            print("✓ 任务启动成功")
            print("监控线程已启动，将检查脚本执行状态...")
            
            # 等待一段时间观察监控效果
            print("等待60秒观察监控效果...")
            time.sleep(60)
            
        else:
            print("✗ 任务启动失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_process_check():
    """测试进程检查功能"""
    print("\n=== 测试进程检查功能 ===")
    
    try:
        # 创建任务管理器
        task_manager = TaskManager(DB_CONFIG)
        scheduler = WeeklyTaskScheduler(task_manager)
        
        # 测试进程检查
        is_running = scheduler._is_weekly_crawl_running()
        print(f"当前pa_week_ar_test.py进程状态: {'运行中' if is_running else '未运行'}")
        
    except Exception as e:
        print(f"✗ 进程检查失败: {e}")

if __name__ == "__main__":
    print("任务监控测试脚本")
    print("=" * 50)
    
    # 测试进程检查
    test_process_check()
    
    # 询问是否要测试完整的任务监控
    response = input("\n是否要测试完整的任务监控功能？(y/n): ")
    if response.lower() == 'y':
        test_task_monitor()
    else:
        print("跳过完整测试")
