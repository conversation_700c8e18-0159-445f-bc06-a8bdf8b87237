{"chunk_id": "line-1", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-2", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Abstract", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-3", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access control builds on authorisation and authentication. This KA1 will present the general", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-4", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "foundations of access control and some signiﬁcant instantiations that have emerged as IT", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-5", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "kept spreading into new application areas. It will survey modes of user authentication and the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-6", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "way they are currently deployed, authentication protocols for the web, noting how new use", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-7", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "cases have led to a shift from authentication to authorisation protocols, and the formalisation", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-8", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of authentication properties as used in today’s protocol analysis tools. On accountability,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-9", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the focus is on the management and protection of audit logs. The surveillance of logs to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-10", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "detect attacks or inappropriate behaviour is described in the Security Operations & Incident", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-11", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Management CyBOK Knowledge Area [1] while the examination of evidence following a breach", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-12", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of policy or attack is covered in the Forensics CyBOK Knowledge Area [2]. Throughout the KA,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-13", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "we will ﬂag technical terms that appear in more than one meaning in the academic and the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-14", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "trade literature.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-16", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "INTRODUCTION", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-17", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "“All science is either physics or stamp collecting.” [Ernest Rutherford]", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-18", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In some cases, IT systems may guarantee – by design – that no undesirable behaviour is", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-19", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "possible. In other cases, IT systems exhibit such a degree of ﬂexibility – also by design –", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-20", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that additional measures need to be taken to limit undesirable behaviour in accordance with", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-21", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the given circumstances. As noted by Lessig, this can be done by code in the system that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-22", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "excludes behaviour, which will violate certain rules, or it can be done by codes of conduct that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-23", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the users of the system are expected to adhere to [3]. In the latter case, disciplinary or legal", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-24", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "processes deal with those that had broken the rules. This is the context for authentication,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-25", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authorisation, and accountability.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-26", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Readers acquainted with the mores of academic writing may now expect deﬁnitions of core", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-27", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "terms, maybe some reﬁnement of terminology, and then an overview of the latest approaches", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-28", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in achieving authentication, authorisation, and accountability. As will be shown, this approach", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-29", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "fails at the ﬁrst hurdle. These three terms are overloaded to an extent that provides ample", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-30", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "space for confusion and dispute. For example, authorisation stands both for the setting of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-31", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "rules and for checking compliance with those very rules. Readers should thus be cautious", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-32", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "when studying the literature on this Knowledge Area.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-33", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Changes in the way IT is being used create their own challenges for taxonomies. How closely", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-34", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "should terms be tied to the environment in which they ﬁrst emerged? There is a habit in the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-35", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "trade and research literature of linking terms exclusively to a notional ‘traditional’ instantiation", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-36", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of some generic concept, and inventing new fashionable terms for new environments, even", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-37", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "though the underlying concepts have not changed.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-38", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "1The title of this KA, ‘Authentication, Authorisation & Accountability’ is abbreviated as AAA in CyBOK related", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-39", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "documents and past versions of this KA. Please note that the acronym ‘AAA’ is also frequently used in other", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-40", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "contexts to refer to ‘Authentication, Authorisation, and Accounting’, for example in connection with network", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-41", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access protocols such as Diameter.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-42", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-43", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 3", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-44", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 5", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-46", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "CONTENT", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-47", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "This KA ﬁrst addresses authorisation in the context of access control and presents the main", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-48", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ﬂavours of access control in use today. The section on access control in distributed systems", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-49", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "explains concepts used when implementing access control across different sites. The KA then", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-50", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "moves to authentication, touching on user authentication and on authentication in distributed", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-51", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "systems, and concludes with a discussion of logging services that support accountability.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-53", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "AUTHORISATION", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-54", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[4, 5, 6, 7, 8]", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-55", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In their seminal paper [5], Lampson et al. postulate access control = authentication + autho-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-56", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "risation. We will follow this lead and present authorisation in the context of access control,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-57", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "starting with an introduction to the concepts fundamental for this domain, followed by an", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-58", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "overview of different policy types. Libicki’s dictum, “connotation, not denotation, is the prob-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-59", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "lem” [9] also applies here, so we will pay particular attention to the attributes used when", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-60", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "setting access rules, and to the nature of the entities governed by those rules. Code-based", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-61", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access control, mobile security, and Digital Rights Management will introduce new paradigms", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-62", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to access control, without changing its substance. We will then present design options for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-63", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "policy enforcement and discuss delegation and some important theoretical foundations of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-64", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access control.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-65", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-66", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-67", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access control is “the process of granting or denying speciﬁc requests ...” [10]. This process", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-68", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "needs the following inputs", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-69", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Who issued the request?", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-70", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• What is requested?", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-71", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Which rules are applicable when deciding on the request?", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-72", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "“Who” in the ﬁrst question is dangerous. The word suggests that requests always come from a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-73", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "person. This is inaccurate for two reasons. First, the source of a request could be a particular", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-74", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "machine, a machine in a particular conﬁguration, or a particular program, e.g. a particular", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-75", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Android app. Secondly, at a technical level, requests in a machine are issued by a process, not", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-76", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "by a person. The question thus becomes, “for whom or what is the process speaking for when", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-77", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "making the request?” “What is requested” is frequently given as a combination of an action to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-78", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be performed and the object on which the action is to be performed. The rules are logical", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-79", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "expressions that evaluate to a decision. In the elementary case, the decision is permit or deny.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-80", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "When policies get more elaborate, there may be reasons for adding an indeterminate decision.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-81", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "A decision may also prescribe further actions to be performed, sometimes called obligations.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-82", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-83", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-84", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 6", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-85", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-86", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Core Concepts", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-87", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The term ’security policy’ is used both for the general rules within an organisation that stipulate", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-88", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "how sensitive resources should be protected, and for the rules enforced by IT systems on the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-89", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "resources they manage. Sterne had coined the terms organisational policies and automated", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-90", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "policies to distinguish these two levels of discourse [4].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-91", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "When setting security policies, principal stands for the active entity in an access request. When", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-92", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "policies directly refer to users, as was the case in the early stages of IT security, user identities", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-93", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "serve as principals. Access control based on user identities is known as Identity-Based Access", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-94", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Control (IBAC). In security policies that refer to concepts such as roles or to the program that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-95", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "issues a request, the principal is a role or a program. Principal may then generally stand for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-96", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "any security attribute associated with the issuer of a request. With this generalisation, any", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-97", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ﬂavour of access control is by deﬁnition attribute-based access control (see Section 3.1.4).", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-98", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Subject stands for the active entity making a request when a system executes some program.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-99", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "A subject speaks for a principal when the runtime environment associates the subject with the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-100", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "principal in an unforgeable manner. The original example for creating a subject that speaks", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-101", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for a principal is user log-in, spawning a process running under the user identity of the person", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-102", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that had been authenticated. The research literature does not always maintain this distinction", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-103", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "between principals and subjects and one may ﬁnd security policies referring to subjects. When", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-104", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "policies refer to attributes of a user but not to the user’s identity, user identities become a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-105", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "layer of indirection between principals and subjects [11].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-106", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "A subject is created, e.g., at log-in, and can be terminated, e.g. at log-out. Similarly, user", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-107", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "identities are created through some administrative action and can be terminated, e.g., by", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-108", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "deleting a user account. In practice, subjects have considerably shorter lifetimes than user", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-109", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "identities. Processes that control industrial plants are a rare example of subjects that could", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-110", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "live forever, but could be killed by system crashes.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-111", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Object is the passive entity in an access request. Access operations deﬁne how an object may", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-112", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be accessed by a subject. Access operations can be as elementary as read, write, execute", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-113", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in Linux, they can be programs such as setuid programs in Linux, and they can be entire", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-114", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "workﬂows as in some ﬂavours of UCON (Section 3.1.8).", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-115", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access rights express how a principal may access an object. In situations where there is a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-116", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "direct match between access operations and access rights, the conceptual distinction between", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-117", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access operations and access rights may not be maintained. Permission is frequently used", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-118", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "as a synonym for access right. Privilege may also be used as a synonym for access right, e.g.,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-119", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Oracle9i Database Concepts Release 2 (9.2) states:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-120", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "“A privilege is permission to access a named object in a prescribed manner ...”", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-121", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Other systems, such as Windows, make a distinction between access rights and privileges,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-122", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "using privilege speciﬁcally for the right to access system resources and to perform system-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-123", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "related tasks. Operating systems and databases often have a range of system privileges that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-124", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "are required for system administration.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-125", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The reference monitor (more details in Section 3.2.2) is the component that decides on access", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-126", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "requests according to the given policy.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-127", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-128", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 5", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-129", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 7", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-130", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-131", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Security Policies", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-132", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Automated security policies are a collection of rules. The rules specify the access rights a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-133", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "principal has on an object. Conceptually, a policy could then be expressed as an Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-134", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Matrix with rows indexed by principals and columns indexed by objects [12]. Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-135", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Lists (ACLs) stored with the objects correspond to the columns of this matrix; capabilities", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-136", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "stored with principals correspond to the rows of this matrix (also see the Operating Systems", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-137", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "& Virtualisation CyBOK Knowledge Area [13]).", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-138", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Discretionary Access Control (DAC) and Mandatory Access Control (MAC) are two core policies", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-139", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "formulated in the 1970s in the context of the US defence sector. Discretionary access control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-140", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "policies assign the right to access protected resources to individual user identities, at the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-141", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "discretion of the resource owner. In the literature, DAC may generically refer to policies set by", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-142", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "resource owners but also to policies referring directly to user identities, i.e., to IBAC.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-143", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Mandatory access control policies label subjects and objects with security levels. The set", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-144", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of security levels is partially ordered, with a least upper bound and a greatest lower bound", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-145", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "operator. The security levels thus form a lattice. In the literature, MAC may generically refer", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-146", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to policies mandated by the system as, e.g., in Security-Enhanced Linux (SELinux) [14, 15]", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-147", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and in Security-Enhanced (SE) Android [16], or to policies based on security levels as in past", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-148", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "products such as Trusted Xenix or Trusted Oracle. Policies of the latter type are also known", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-149", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "as multi-level security policies and as lattice-based policies.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-150", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1.3", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-151", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Role-based Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-152", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In Role-Based Access Control (RBAC), roles are an intermediate layer between users and the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-153", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "permissions to execute certain operations. Operations can be well-formed transactions with", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-154", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "built-in integrity checks that mediate the access to objects. Users are assigned roles and are", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-155", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authorised to execute the operations linked to their active role. Separation of Duties (SoD)", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-156", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "refers to policies that stop single users from becoming too powerful. Examples for SoD are", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-157", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "rules stating that more than one user must be involved to complete some transaction, rules", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-158", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "stating that a user permitted to perform one set of transactions is not permitted to perform", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-159", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "some other set of transactions, the separation between front ofﬁce and back ofﬁce in ﬁnancial", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-160", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "trading ﬁrms is an example, or rules stating that policy administrators may not assign permis-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-161", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "sions to themselves. Static SoD rules are considered during user-role assignment, dynamic", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-162", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "SoD must be enforced when a role is activated. The NIST RBAC model [7] distinguishes", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-163", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "between:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-164", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Flat RBAC: users are assigned to roles and roles to permissions to operations; users", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-165", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "get permissions to execute procedures via role membership; user-role reviews are", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-166", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "supported.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-167", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Hierarchical RBAC: adds support for role hierarchies.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-168", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Constrained RBAC: adds separation of duties.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-169", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Symmetric RBAC: adds support for permission-role reviews, which may be difﬁcult to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-170", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "achieve in large distributed systems.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-171", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Many commercial systems support some ﬂavour of role-based access control, without nec-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-172", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "essarily adhering to the formal speciﬁcations of RBAC published in the research literature.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-173", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "RBAC is an elegant and intuitive concept, but may become quite messy in deployment as", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-174", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "suggested by comments in an empirical study on the use of RBAC [17]. Practitioners note that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-175", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-176", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 6", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-177", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 8", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-178", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "RBAC works as long as every user has only one role, or that “the enormous effort required for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-179", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "designing the role structure and populating role data” constitutes an inhibitor for RBAC.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-180", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1.4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-181", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Attribute-based Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-182", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Attribute-Based Access Control (ABAC) is deﬁned in [18] as a “logical access control methodol-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-183", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ogy where authorisation to perform a set of operations is determined by evaluating attributes", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-184", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "associated with the subject, object, requested operations, and, in some cases, environment", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-185", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "conditions against policy, rules, or relationships that describe the allowable operations for a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-186", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "given set of attributes”. This is a generic deﬁnition of access control that no longer reserves a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-187", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "special place to the user or to the user’s role, reﬂecting how the use of IT systems has changed", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-188", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "over time.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-189", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access control may be performed in an application or in the infrastructure supporting the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-190", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "application. Access control in an infrastructure uses generic attributes and operations. The", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-191", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Linux access control system may serve as an example. Access control in an application uses", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-192", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "application-speciﬁc attributes and operations. In this distinction, ABAC can be viewed as a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-193", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "synonym for application-level access control.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-194", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1.5", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-195", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Code-based Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-196", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Code-Based Access Control (CBAC) assigns access rights to executables. Policies may refer", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-197", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to code origin, to code identity (e.g., the hash of an executable), or to other properties of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-198", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "executable, rather than to the identity of the user who had launched the executable. Origin", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-199", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "can subsume the domain the code was obtained from, the identity of the code signer, a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-200", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "speciﬁc name space (.NET had experimented with strong names, i.e. bare public keys serving", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-201", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "as names for name spaces), and more. CBAC can be found in the Java security model [19]", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-202", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and in Microsoft’s .NET architecture [20].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-203", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The reference monitor in CBAC typically performs a stack walk to check that all callers have", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-204", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "been granted the required access rights. The stack walk addresses the confused deputy", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-205", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "problem [21], where an unprivileged attacker manipulates a system via calls to privileged code", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-206", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "(the confused deputy). Controlled invocation is implemented through assert statements; a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-207", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "stack walk for an access right will stop at a caller that asserts this right.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-208", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1.6", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-209", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Mobile Security", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-210", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Smartphones typically have a single owner, hold private user data, offer communication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-211", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "functions ranging from cell phone to NFC, can observe their surroundings via camera and", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-212", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "microphone, and can determine their location, e.g., via GPS. On smartphones, apps are the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-213", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "principals for access control. The objects of access control are the sensitive data stored on a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-214", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "phone and the sensitive device functions on a phone.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-215", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access control on a smartphone addresses the privacy requirements of the owner and the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-216", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "integrity requirements of the platform. Android, for example, divides permission groups into", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-217", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "normal, dangerous, and signature permissions. Normal permissions do not raise privacy or", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-218", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "platform integrity concerns; apps do not need approval when asserting such permissions.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-219", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Dangerous permissions can impact privacy and need user approval. Up to Android 6.0, users", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-220", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "had to decide whether to authorise a requested permission when installing an app. User studies", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-221", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "showed that permissions were authorised too freely due to a general lack of understanding", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-222", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and risk awareness, see e.g. [22]. Since Android 6.0, users are asked to authorise a permission", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-223", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-224", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 7", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-225", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 9", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-226", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "when it is ﬁrst needed. Signature permissions have an impact on platform integrity and can", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-227", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "only be used by apps authorised by the platform provider; app and permission have to be", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-228", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "signed by the same private key. For further details see the Web & Mobile Security CyBOK", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-229", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Knowledge Area [23].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-230", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1.7", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-231", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Digital Rights Management", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-232", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Digital Rights Management (DRM) has its origin in the entertainment sector. Uncontrolled", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-233", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "copying of digital content such as games, movies or music would seriously impair the busi-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-234", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ness models of content producers and distributors. These parties hence have an interest in", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-235", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "controlling how their content can be accessed and used on their customers’ devices. Policies", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-236", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "can regulate the number of times content can be accessed, how long content can be sampled", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-237", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for free, the number of devices it can be accessed from, or the pricing of content access.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-238", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "DRM turns the familiar access control paradigm on its head. DRM imposes the security", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-239", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "policy of an external party on the system owner rather than protecting the system owner", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-240", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "from external parties. Superdistribution captures the scenario where data are distributed in", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-241", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "protected containers and can be freely redistributed. Labels specifying the terms of use are", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-242", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "attached to the containers. The data can only be used on machines equipped with a so-called", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-243", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Superdistribution Label Reader that can unpack the container and track (and report) the usage", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-244", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of data, and enforce the terms of use [24]. The search for such a tamper resistant enforcement", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-245", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "mechanism was one of the driving forces of Trusted Computing.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-246", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The level of tamper resistance required depends on the anticipated threats. Trusted Platform", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-247", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Modules are a hardware solution giving a high degree of assurance. Enclaves in Intel SGX are", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-248", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "a solution in system software. Document readers that do not permit copying implement this", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-249", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "concept within an application. Sticky policies pursue a related idea [25]; policies stick to an", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-250", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "object and are evaluated whenever the object is accessed.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-251", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Attestation provides trustworthy information about a platform’s conﬁguration. Direct anony-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-252", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "mous attestation implements this service in a way that protects user privacy [26]. Remote", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-253", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "attestation can be used with security policies that are predicated on the software running on", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-254", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "a remote machine. For example, a content owner could check the software conﬁguration at a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-255", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "destination before releasing content. In the FIDO Universal Authentication Framework (FIDO", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-256", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "UAF) just the model of the authenticator device is attested. All devices of a given model hold", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-257", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the same private attestation key [27].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-258", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "For a brief period, it was fashionable to use Digital Rights Management as the generic term", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-259", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "subsuming ‘traditional’ access control as a special case.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-260", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.1.8", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-261", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Usage Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-262", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Usage Control (UCON) was proposed as a framework encompassing authorisations based", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-263", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "on the attributes of subject and object, obligations, and conditions [6]. In [6], obligations are", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-264", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "additional actions a user has to perform to be granted access, e.g., clicking on a link to agree", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-265", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to the terms of use. In today’s use of the term, obligations may also be actions the system has", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-266", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to perform, e.g., logging an access request. Such actions may have to be performed before,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-267", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "during or after an access happens. Conditions are aspects independent of subject and object,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-268", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "e.g., time of day when a policy permits access only during ofﬁce hours or the location of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-269", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "machine access is requested from. Examples for the latter are policies permitting certain", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-270", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "requests only when issued from the system console, giving access only from machines in", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-271", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-272", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 8", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-273", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 10", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-274", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the local network, or policies that consider the country attributed to the IP address a request", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-275", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "comes from. Many concepts from UCON have been adopted in the XACML 3.0 standard [28].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-276", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Usage control may also include provisions for what happens after an object is accessed, e.g.,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-277", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that a document can be read but its content cannot be copied or adjustment of attributes", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-278", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "after an access has been performed, e.g., decrementing the counter of free articles a visitor", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-279", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "may access. In another interpretation, ‘traditional’ access control deals with the elementary", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-280", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access operations found at an infrastructure level while usage control addresses entire work", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-281", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ﬂows at the application level. In telecom services, usage control may put limits on trafﬁc", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-282", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "volume.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-283", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-284", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Enforcing Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-285", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "To enforce a security policy, this policy ﬁrst has to be set. For a given request, a decision has", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-286", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to be made about whether the request complies with the policy, which may need additional", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-287", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "information from other sources. Finally, the decision has to be conveyed to the component", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-288", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that manages the resource requested. In the terminology of XACML, this involves", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-289", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Policy Administration Points where policies are set,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-290", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Policy Decision Points where decisions are made,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-291", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Policy Information Points that can be queried for further inputs to the decision algorithm,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-292", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Policy Enforcement Points that execute the decision.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-293", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3.2.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-294", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Delegation and Revocation", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-295", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Delegation and granting of access rights both refer to situations where a principal, or a subject,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-296", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "gets an access right from someone else. The research literature does not have ﬁrm deﬁnitions", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-297", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for those terms, and the trade literature even less so. Granting tends to be used in a generic", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-298", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "sense; granted access rights often refer to the current access rights of a subject that delivers a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-299", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "request to a reference monitor. Delegation is sometimes, but not always, used more narrowly", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-300", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for granting short-lived access rights during the execution of a process. For example, XACML", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-301", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "distinguishes between policy administration and dynamic delegation that “permits some users", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-302", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to create policies of limited duration to delegate certain capabilities to others” [29].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-303", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "A second possible distinction lets delegation refer only to the granting of access rights held", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-304", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "by the delegator, while granting access also includes situations where a managing principal", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-305", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "assigns access rights to others but is not permitted to exercise those rights itself.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-306", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Rights may not always be granted in perpetuity. The grantor may set an expiry date on the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-307", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "delegation, a right may be valid only for the current session, or there may be a revocation", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-308", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "mechanism such as the Online Certiﬁcate Status Protocol (OCSP) for X.509 certiﬁcates (see", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-309", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Section 4.1). OCSP is supported by all major browsers. Revocation lists are suitable when", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-310", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "online checks are not feasible and when it is known in advance where a granted right may be", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-311", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "consumed.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-312", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-313", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 9", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-314", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 14", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-315", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "4.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-316", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Origin-based Policies", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-317", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In web applications, clients and servers communicate via the HTTP protocol. The client", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-318", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "browser sends HTTP requests; the server returns result pages. The browser represents the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-319", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "page internally in the document object in the Document Object Model (DOM). Security policies", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-320", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "specify which resources a script in a web page is allowed to access, or which servers an", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-321", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "XMLHttpRequest may refer to. Web applications are thus the principals in access control.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-322", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "By convention, principal names are the domain names of the server hosting an application;", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-323", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the policy decision point (cf. Section 3.2) at the client side is located in the browser.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-324", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The prototype policy for web applications is the Same Origin Policy (SOP), stating that a script", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-325", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "may only connect back to the origin it came from or that an HTTP cookie is only included", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-326", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in requests to the domain that had placed the cookie. Two pages have the same origin if", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-327", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "they share protocol, host name and port number. Certain actions may be exempt from the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-328", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "same origin policy. For example, a web page may contain links to images from other domains,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-329", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "reﬂecting a view that images are innocuous data without malign side effects. There exist", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-330", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "variations of the SOP, e.g., policies for cookies that also consider the directory path. There is", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-331", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "also the option to set the HttpOnly ﬂag in a Set-Cookie HTTP response header so that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-332", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the cookie cannot be accessed by client side scripts.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-333", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Sender Policy Framework (SPF) [45] implements origin-based access control in the email", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-334", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "system as a measure against spooﬁng the sending domain of an email. Domain owners", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-335", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "publish SPF policies in their DNS zone. An SMTP server can then use the domain part of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-336", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the MAIL FROM identity to look up the policy and consult this policy to check whether the IP", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-337", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "address of the SMTP client is authorised to send mail from that domain.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-338", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "4.2.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-339", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Cross-site Scripting", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-340", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Cross-site scripting attacks on web applications can be treated as cases of failed authen-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-341", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "tication in access control. The browser lets all scripts that arrive in a web page speak for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-342", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the origin of that page. A browser would then run a script injected by the attacker in the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-343", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "context of an origin other than the attacker’s. Content Security Policy (CSP) reﬁnes SOP-based", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-344", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access control. The web server conveys a policy to the browser that characterises the scripts", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-345", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authorised to speak for that server [42]. Typically, this is done by specifying a directory path", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-346", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "on the web server where authorised scripts (and other web elements) will be placed.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-347", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The use of CSP in practice has been examined in [46], observing that the unsafe-inline", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-348", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "directive disabling CSP for all pages from a given domain was widely used. This is a familiar", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-349", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "policy management issue. A new security mechanism is deployed but quickly disabled", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-350", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "because it interferes too much with established practices. Moreover, CSP had an inherent", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-351", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "vulnerability related to callbacks. Callbacks are names of scripts passed as arguments to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-352", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "other (authorised) scripts, but arguments are not covered by CSP. In strict CSP policies, the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-353", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "server declares a nonce in the CSP policy it sends to the client as the script source. The server", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-354", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "also includes this nonce as an attribute in all scripts fetched by the client. The client’s browser", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-355", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "only accepts scripts that contain this nonce as an attribute. Nonces must only be used once", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-356", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and must be unpredictable.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-357", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-358", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 13", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-359", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 16", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-360", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "4.4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-361", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Cryptography and Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-362", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access control mechanisms in an operating system implement a logical defence. Access", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-363", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "requests passed via the reference monitor will be policed. This includes requests for direct", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-364", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "memory access. However, data are stored in the clear and a party with physical access to the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-365", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "storage medium can retrieve the data and thus bypass logical access control. When solutions", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-366", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for the protection of unclassiﬁed but sensitive data were evaluated in the U.S. in the 1970s, it", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-367", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "was decided that encrypting the data was the best way forward. Access control would then", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-368", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be applied to the keys needed to unlock the data.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-369", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "4.4.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-370", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Attribute-Based Encryption", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-371", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Cloud computing has raised the interest in access control on encrypted data over the past", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-372", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "decade. Storing data in encrypted form protects their conﬁdentiality but creates a key manage-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-373", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ment challenge. Attribute-Based Encryption (ABE) addresses this challenge by constructing", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-374", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "encryption schemes that enforce attribute-based decryption policies. Policies are logical", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-375", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "predicates over attributes, represented as access structures. The Key Generator is a Trusted", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-376", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Third Party that generates private keys and has to check a user’s policy / attributes before", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-377", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "issuing a private key. The Key Generator is thus in a position to recreate private keys.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-378", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Key-Policy Attribute-Based Encryption (KP-ABE) works with policies that deﬁne a user’s access", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-379", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "rights [43]. From the corresponding access structure, the Key Generator creates a private", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-380", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "decryption key. Documents are encrypted under a set of attributes. In Ciphertext-Policy", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-381", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Attribute-Based Encryption (CP-ABE) [48], the policy refers to the document and the access", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-382", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "structure is used for encryption. The user’s private key created by the Key Generator depends", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-383", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "on the user’s attribute set. In both variants, decryption is possible if and only if the given", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-384", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "attribute set satisﬁes the given access structure.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-385", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "A study of the feasibility of ABE in realistic dynamic settings had concluded that the overheads", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-386", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "incurred by those schemes were still prohibitive [49]. Efﬁcient encryption and decryption do", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-387", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "not necessarily imply an efﬁcient access control system.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-388", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "4.4.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-389", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Key-centric Access Control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-390", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In distributed systems, access requests may be digitally signed. Access rights could then", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-391", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be granted directly to the public veriﬁcation key without the need to bind the public key to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-392", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "some other principal. SPKI/SDSI uses authorisation certiﬁcates for implementing key centric", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-393", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access control, where (names of) public keys are bound to access rights [50]. The right to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-394", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "further delegate an access right is controlled by a delegation ﬂag.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-395", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Cryptographic keys are rarely suitable principals for access control, however. They would", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-396", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "need to have an obvious meaning in the application domain that provides the context for a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-397", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "given security policy. In most cases, cryptographic keys would be subjects speaking for some", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-398", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "principal. Constrained delegation reﬁnes the basic delegation mechanism of SPKI/SDSI so", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-399", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that separation of duties policies can be enforced [51].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-400", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-401", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 15", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-402", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 17", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-404", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "AUTHENTICATION", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-405", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[52, 53, 54, 55, 56, 57]", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-406", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Authentication in a narrow sense veriﬁes the identity of a user logging in – locally or remotely", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-407", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "– and binds the corresponding user identity to a subject. User authentication based on", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-408", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "passwords is a common method. Some applications have adopted biometric authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-409", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "as an alternative. Authentication in distributed systems often entails key establishment.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-410", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Some security taxonomies thus reduce authentication to a ‘heartbeat’ property to separate", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-411", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authentication from key establishment. The design of authentication protocols is a mature", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-412", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "area in security research with good tool support for formal analysis. Standard protocols such", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-413", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "as Kerberos, SAML, or OAuth are deployed widely today.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-414", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "We will give a brief overview of identity management before moving to password-based and", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-415", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "biometric user authentication. We then cover authentication protocols from the Needham-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-416", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Schroeder protocol via Kerberos and SAML to OAuth 2.0, observing that OAuth 2.0 is more of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-417", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "an authorisation protocol than an authentication protocol. We conclude with an overview of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-418", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "formalisations of authentication properties that serve as the basis for a formal analysis of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-419", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authentication protocols.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-420", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-421", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Identity Management", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-422", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Following NIST, “identity management systems are responsible for the creation, use, and termi-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-423", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "nation of electronic identities”. This includes operational aspects when creating and deleting", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-424", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "electronic identities. On creation, one question is how strongly electronic identities must", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-425", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be linked to persons. In some sensitive areas, strong links have to be established and doc-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-426", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "umented. For example, money laundering rules may demand a thorough veriﬁcation of an", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-427", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "account holder’s identity. In other areas, electronic identities need not to be tied to a person.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-428", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Privacy by design implies that such applications should use electronic identities that cannot", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-429", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be linked to persons. Identity management may also link access rights to an electronic identity,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-430", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "either directly or via some layer of indirection such as a role. Electronic identities should be", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-430", "line_number": 430, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-431", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "terminated when they are no longer required, e.g. when a person leaves an organisation. Care", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-432", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "has to be taken that this is done on all systems where this identity had been registered.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-433", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Electronic identities exist at different layers. There are identities for internal system purposes,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-434", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "such as user identities in an operating system. These identities must be locally unique and", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-435", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "could be created by system administrators (Linux). This can lead to problems when an identity", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-436", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "is taken out of use and re-assigned later. The new user may get unintended access to resources", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-437", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the predecessor had access to. When organisations merge, collisions between identities may", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-438", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "arise that identity management then must address. Alternatively, identities could be long", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-439", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "random strings (Windows). The probability for one of the problems just mentioned to arise is", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-440", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "then negligible, but when a user account is re-created, a new random identity is assigned so", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-441", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access rights have to be reassigned from scratch.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-442", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Electronic identities such as user names and email addresses could be random strings, but", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-443", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "it is often preferable to assign understandable identities. There is, for example, merit in", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-444", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "communicating with meaningful email addresses. Email addresses can be taken out of use", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-445", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and re-assigned later, but a user may then receive emails intended for its previous owner.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-446", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Web applications often use email addresses as electronic identities. This is convenient for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-447", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "contacting the user, and it is convenient for users as they do not have to remember a new", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-448", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-449", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 16", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-450", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 18", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-451", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "identity. There are alternatives, such as FIDO UAF (Section 5.2.3), where electronic identities", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-452", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "are randomly created public keys and a back channel for resetting passwords is not required", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-453", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "as no passwords are used.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-454", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Identity management can also be viewed from a person’s perspective. A person using different", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-455", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "identities with different organisations may want to manage how identities are revealed to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-456", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "other parties.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-457", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-458", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "User Authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-459", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access requests are issued by subjects. Subjects can be associated with security attributes", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-460", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "when they are created or during their lifetime. Authentication can then be viewed as the service", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-461", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that validates the security attributes of a subject when it is created. When subjects are created", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-462", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "due to some user action, and when their security attributes depend on the corresponding user", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-463", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "identity, user authentication has to give a reasonable degree of assurance that the user identity", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-464", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "linked to the subject belongs to the user who had triggered the creation of the subject. The", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-465", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "degree of assurance (strength of authentication) should be commensurate with the severity", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-466", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of the risk one wants to mitigate. The term risk-based authentication thus states the obvious.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-467", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "User authentication can also support accountability, as further elaborated in Section 6. Au-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-468", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "thentication ceremony refers to the steps a user has to go through to be authenticated.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-469", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "There are access control systems where the security attributes of a subject persist throughout", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-470", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the lifetime of that subject. Many operating systems adopt this approach. Policy changes do", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-471", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "not affect active processes, but the lifetime of subjects is limited, which limits the period when", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-472", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the new policy is not applied consistently. Alternatively, the attributes of a subject are checked", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-473", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "each time it issues a request. For example, a user already logged in to a banking application", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-474", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "is authenticated again when requesting a funds transfer. When the focus moves from the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-475", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "subject to individual requests, authentication can be viewed as the service that checks the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-476", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "validity of the security attributes submitted with the request to the decision algorithm.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-477", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.2.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-478", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Passwords", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-479", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "When passwords are employed for user authentication, protective measures at the system", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-480", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "side include the storing of hashed (Unix, Linux) or encrypted (Windows) passwords, the salting", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-481", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of passwords, and shadow password ﬁles that move sensitive data out of world-readable", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-482", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "password ﬁles. Protective measures at the user side include guidance on the proper choice", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-483", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and handling of passwords, and security awareness programs that try to instil behaviour", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-484", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that assures the link between a person and a principal. Recommendations in this area are", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-485", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "changing. The Digital Identity Guidelines published by NIST build on assessments of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-486", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "observed effectiveness of previous password rules and reﬂect the fact that users today have", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-487", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to manage passwords for multiple accounts [57]. The new recommendations advise", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-488", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• against automatic password expiry; passwords should only be changed when there is a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-489", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "reason;", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-490", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• against rules for complex passwords; password length matters more than complexity;", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-491", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• against password hints or knowledge-based authentication; in an era of social networks", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-492", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "too much information about a person can be found in public sources;", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-493", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• to enable “show password while typing” and to allow paste-in password ﬁelds.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-494", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-495", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 17", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-496", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 19", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-497", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Password-based protocols for remote authentication are RADIUS, DIAMETER (both covered", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-498", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in the Network Security CyBOK Knowledge Area [58]), HTTP Digest Authentication, and to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-499", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "some extent Kerberos (Section 5.3.2). Password guidance is further discussed in the Human", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-500", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Factors CyBOK Knowledge Area [59].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-501", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.2.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-502", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Biometrics for Authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-503", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Numerous well-rehearsed arguments explain why passwords work poorly in practice. Biomet-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-504", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "rics are an alternative that avoids the cognitive load attached to password-based authentica-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-505", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "tion. Fingerprint and face recognition are the two main methods deployed for biometric user", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-506", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authentication, known as veriﬁcation in that domain.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-507", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Biometric features must be sufﬁciently unique to distinguish between users, but ﬁngerprints", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-508", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "or faces cannot be considered as secrets. Fingerprints are left in many places, for example.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-509", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Biometric features are thus better treated as public information when conducting a security", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-510", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "analysis and the process of capturing the features during authentication has to offer an", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-511", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "adequate level of liveness detection, be it through supervision of that process or through device", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-512", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "features. Employing biometrics for user authentication makes the following assumptions:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-513", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• The biometric features uniquely identify a person; face, ﬁngerprints, and iris patterns", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-514", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "may serve as examples.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-515", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• The features are stable; the effects of aging on ﬁngerprint recognition are surveyed, e.g.,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-516", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in [60].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-517", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• The features can be conveniently captured in operational settings.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-518", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• The features cannot be spoofed during user authentication.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-519", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "User authentication, known as veriﬁcation in biometrics, starts from a template captured", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-520", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "by a device. From the template, a feature vector is extracted. For example, the template", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-521", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "may be the image of a ﬁngerprint, the features are the positions of so-called minutiae (ridge", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-522", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "endings, bifurcations, whorls, etc.). Users initially register a reference feature vector. During", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-523", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authentication, a new template is captured, features are extracted and compared with the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-524", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "reference values. A user is authenticated if the number of matching features exceeds a given", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-525", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "threshold. This process may fail for various reasons:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-526", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Failure to capture: this may happen at registration when it is not possible to extract a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-527", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "sufﬁcient number of features, or during authentication.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-528", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• False rejects: the genuine user is rejected because the number of matches between", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-529", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "reference features and extracted features is insufﬁcient.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-530", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• False accepts: a wrong user is accepted as the matching threshold is exceeded.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-531", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Spooﬁng: to deceive the device capturing the template, some object carrying the user’s", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-532", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "features is presented. Liveness detection tries to ensure that templates are captured", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-533", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "from the very person that is being authenticated [61].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-534", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Biometric authentication based on face recognition or ﬁngerprints is used increasingly at", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-535", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "automated border control gates [62]. It has also become a feature on mobile devices, see e.g.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-536", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[63]. A survey of the current state-of-the-art approaches to biometric authentication is given", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-537", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in [64].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-538", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-539", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 18", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-540", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 20", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-541", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.2.3", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-542", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Authentication Tokens", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-543", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Authentication by password relies on “something you know”. Biometric authentication builds", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-544", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "on “who you are”. In a further alternative, users are issued with a device (a.k.a. token or security", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-545", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "key, not to be confused with a cryptographic key) that computes a one-time password (OTP)", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-546", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "synchronised with the authenticator, or a response to a challenge set by the authenticator.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-547", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Possession of the device is then necessary for successful authentication, which is thus based", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-548", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "on “something you have”.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-549", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "A token could be a small hand-held device with an LED display for showing an OTP that the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-550", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "user enters in a log-in form; RSA SecureID and YubiKey are examples for this type of token.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-551", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "A token could come with a numeric keypad in addition to the LED display and with a ‘sign’", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-552", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "button. The holder could then receive a challenge, e.g., an 8-digit number, enter it at the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-553", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "keypad, press ‘sign’ to ask the token to compute and display the response, and then enter", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-554", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the response in a log-in form. Some e-banking services use this type of token for account", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-555", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "holder authentication. With PhotoTAN devices, the challenge is sent as a QR code to the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-556", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "user’s computer and scanned from the screen by the PhotoTAN device. When authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-557", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "is based on a secret shared between token and server, different tokens must be used for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-558", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "different servers.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-559", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The FIDO authenticator is a token that can create public key / private key pairs; public keys", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-560", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "serve as identiﬁers, private keys are used for generating digital signatures [27]. In FIDO", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-561", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "UAF, users register a public key with a server. The same token can be used for different", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-562", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "servers, but with different keys. User authentication is based on a challenge-response pattern", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-563", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "(Section 5.4.1), where the user’s authenticator digitally signs the response to the server’s", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-564", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "challenge. The response is veriﬁed using the public key registered with the server.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-565", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In some applications, possession of the token is sufﬁcient for user authentication. In other", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-566", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "applications, authentication is a two-stage process. First, the token authenticates the user,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-567", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "e.g., based on a PIN or a ﬁngerprint. In a second stage, the server authenticates the token. It", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-568", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "will depend on the threat model whether ‘weak’ authentication in the ﬁrst stage and ‘strong’", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-569", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authentication in the second stage can provide adequate security.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-570", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Apps on smartphones can provide the same functionality as authentication tokens, but smart-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-571", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "phones are not dedicated security devices. User authentication may then be compromised", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-572", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "via attacks on the smartphone. This may become even easier when smartphones come with", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-573", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "a secondary authentication mechanism for use when a device is partially locked, with a less", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-574", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "onerous but also less secure authentication ceremony. This creates a conﬂict between the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-575", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "interests of smartphone manufacturers who value ease-of-use of a communications device,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-576", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and the interests of the providers of sensitive applications searching for a security token.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-577", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.2.4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-578", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Behavioural Authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-579", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Behavioural authentication analyses “what you do”, lending itself naturally to continuous au-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-580", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "thentication. Keystroke dynamics [65, 66] can be captured without dedicated equipment.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-581", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Characteristic features of hand writing are writing speed and pen pressure [67]. Here, special", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-582", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "pens or writing pads need to be deployed. Voice recognition needs a microphone. Smart-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-583", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "phones come with various sensors such as touch screens and microphones that are being", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-584", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "utilised for behavioural authentication today. The requirements on behavioural authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-585", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "are the same as those listed in Section 5.2.2:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-586", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• The behavioural features uniquely identify a person.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-587", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-588", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 19", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-589", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 21", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-590", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• The features are stable and unaffected by temporary impairments.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-591", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• The features can be conveniently captured in operational settings.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-592", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• The features cannot be spoofed during user authentication.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-593", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Advocates of continuous authentication promise minimum friction, maximum security. Be-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-594", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "havioural authentication does not inconvenience the user with authentication ceremonies, but", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-595", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "variations in user behaviour may cause false rejects. For example, how will a severe cold affect", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-596", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "voice recognition? There needs to be a smooth fall-back when behavioural authentication fails.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-597", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Security depends on the strength of liveness detection. For example, will voice recognition", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-598", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "detect synthesised speech or a very proﬁcient human voice imitator? Without a precise threat", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-599", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "model, behavioural authentication can only offer uncertain security guarantees. There is a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-600", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "growing research literature on different modes of behavioural authentication. Criteria for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-601", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "assessing the actual contributions of this research include sample size and composition,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-602", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "whether longitudinal studies have been performed, the existence of an explicit threat model", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-603", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and resistance to targeted impersonation attempts.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-604", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.2.5", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-605", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Two-factor Authentication 2FA", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-606", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Multi-factor authentication combines several user authentication methods for increased", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-607", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "security. The European Payment Services Directive 2 (PSD2, Directive (EU) 2015/2366), written", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-608", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for the regulation of ﬁnancial service providers, prescribes two-factor authentication (2FA) for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-609", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "online payments (with a few exceptions). PSD2 thus is a case study on rolling out large scale", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-610", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "2FA solutions.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-611", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The two factors could be a password and an authentication token for computing Transaction", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-612", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Authentication Numbers (TANs) uniquely tied to the content of a transaction. The token could", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-613", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be a separate device; if the device is tied to one payment service only, customers would have", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-614", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to carry multiple devices with them. For devices that can be used with several services, some", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-615", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "level of prior standardisation is required. The FIDO alliance has been promoting its standards", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-616", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for PSD2 compliant two-factor authentication.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-617", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The token could be a smartphone registered with the service; customers could then install", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-618", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "apps for several services on the same device. This approach has been favoured by many", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-619", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "banks. However, when passwords (or PINs) and TANs are handled by the same device, the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-620", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "two mechanisms are no longer independent, reducing the security gains claimed for 2FA.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-621", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In contrast to the European Trust Services and Electronic identiﬁcation regulation (eID Direc-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-622", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "tive - Regulation (EU) No 910/2014) that speciﬁes requirements on secure signature creation", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-623", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "devices, PSD2 does not impose security requirements on the devices used for user authenti-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-624", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "cation but wants “to allow for the use of all common types of devices (such as computers,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-625", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "tablets and mobile phones) for carrying out different payment services”. PSD2 and the eID Di-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-626", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "rective thus strike different balances between ease-of-use and security, a trade-off notoriously", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-627", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "difﬁcult to get right.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-628", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-629", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 20", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-630", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 24", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-631", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "federated single sign-on protocol where the relying party decides how to use assertions when", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-632", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "making decisions according to its own security policy.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-633", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In the practical deployment of SAML, parsing XML documents – the price to be paid for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-634", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "employing a meta-protocol – can create non-trivial overheads and can introduce security", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-635", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "vulnerabilities. Furthermore, the advent of smartphones has made it easier to access the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-636", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "internet from mobile user devices, removing one of the reasons for introducing a meta-protocol", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-637", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "between web services and the underlying IT systems.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-638", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.3.4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-639", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "OAuth 2 – OpenID Connect", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-640", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Newer protocols such as OAuth 2.0 [55] and OpenID Connect [69] run directly over HTTP", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-641", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and provide authentication and authorisation. The parties involved include a user who owns", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-642", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "resources, the resource owner, a resource server that stores the user’s resources, a so-called", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-643", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "client application that wants to be granted access to those resources, and an Authorisation", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-644", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Server (AS) that can authenticate users and client applications.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-645", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Clients have to be registered with the AS. They will receive a public client ID and a client", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-646", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "secret shared with the AS. This secret is used for establishing secure sessions between the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-647", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "client and the AS. The client also registers redirect URIs with the AS. The AS will redirect a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-648", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "user agent only to those registered redirect URIs. Proper deﬁnition of the redirect URIs is", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-649", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "primarily a matter for the client, and can also be enforced by the AS. Weak settings are open", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-650", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to exploitation by attackers.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-651", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In an OAuth protocol run (a high level overview is given in Figure 5), the user agent (browser)", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-652", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "has opened a window for the client application. In the client window, an authorisation request", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-653", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "can be triggered 1 ; the request also contains a redirect URI. The user agent then typically", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-654", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "conveys the authorisation request and the user’s authorisation to the AS 2 . A secure session", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-655", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "between the user agent and the AS is required, and may already exist if the user has logged", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-656", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in previously at the AS. If authorisation is granted, an authorisation grant is returned to the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-657", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "user agent 3 , which will pass it on to the redirect URI given by the client 4 . The client then", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-658", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "posts the authorisation grant and a redirect URI to the AS 5 . It is assumed that the AS can", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-659", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authenticate this message as coming from the client. If the request is valid, the AS returns an", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-660", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access token to the redirect URI provided, where the token can be used to retrieve the resource", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-661", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "from the resource server 6 .", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-662", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Authorisation requests and authorisation grantsindexauthorisation grant are linked via a re-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-663", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "quest ID, called state in OAuth. Omitting the request ID or using a ﬁxed value had introduced", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-664", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "vulnerabilities in applications using OAuth, see e.g. [70, 71].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-665", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "4. authorisation grant", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-666", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "2. auth request + approval", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-667", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "user", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-668", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "user", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-669", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "agent", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-670", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6. access token", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-672", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3. authorisation grant", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-674", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "author.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-675", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "server", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-676", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5. authorisation grant", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-678", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "\u001b1. authorisation request", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-679", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "client", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-680", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "(app)", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-681", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": ". ..... .... ..................................", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-681", "line_number": 681, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-682", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "..... ... ............", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-683", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "......................", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-684", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Figure 5: Message ﬂow in OAuth 2.0.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-685", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "There is a fundamental switch in focus compared to SSO protocols such as Kerberos and SAML", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-686", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-687", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 23", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-688", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 25", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-689", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "despite a considerable degree of similarity in the message ﬂows. In an OAuth 2.0 protocol", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-690", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "run the user is no longer the party requesting access to a resource owned by someone else,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-691", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "but the party granting access to resources owned by the user. OAuth 2.0 has thus become an", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-692", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authorisation protocol. Several assumptions about pre-existing trust relationships between", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-692", "line_number": 692, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-693", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "parties have to be met for OAuth to be secure. Conversely, one cannot take for granted that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-694", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the OAuth security properties still hold when the protocol is deployed in a new setting.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-695", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "OpenID Connect puts user authentication back into the OAuth 2.0 message ﬂow. The client", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-696", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "application now doubles as a relying party, and the authorisation server becomes an authenti-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-697", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "cation & authorisation server that issues digitally signed id tokens (authentication assertions", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-698", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in SAML diction). An id token contains the name of the issuer, the name of the authenticated", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-699", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "user (called subject), the intended relying party (called audience), the nonce that had been", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-700", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "sent with the authentication request, an indicator of authentication strength, and other ﬁelds.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-701", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-702", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Facets of Authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-703", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "We have sketched how user authentication in distributed systems ﬁrst integrated session", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-704", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and key establishment with the process of verifying a user’s identity, and later established", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-705", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authorisation practices to access a user’s resources. In communication security, peer entity", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-706", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authentication refers to the process of verifying the identity of the peer in a connection and", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-707", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "data origin authentication to the process of verifying the origin of individual data items.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-708", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "User authentication, whether relating to a local system or to a remote system, entails three", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-708", "line_number": 708, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-709", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "aspects:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-709", "line_number": 709, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-710", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• creating a new subject, e.g. a new process or a new session with a fresh session key,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-710", "line_number": 710, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-711", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• linking an internal entity, e.g. a user ID, to the subject,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-711", "line_number": 711, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-712", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• linking an external entity, e.g. a person, to an internal identity.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-712", "line_number": 712, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-713", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "To differentiate between these aspects, the term key establishment was introduced in com-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-713", "line_number": 713, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-714", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "munication security towards the end of the 1980s for the ﬁrst aspect. Entity authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-715", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "stood for what was left. Quoting ISO/IEC 9798, “entity authentication mechanisms allow the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-716", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "veriﬁcation, of an entity’s claimed identity, by another entity. The authenticity of the entity can", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-717", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be ascertained only for the instance of the authentication exchange”. This property is related to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-717", "line_number": 717, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-718", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "dead peer detection and to the heartbeat extension in RFC 6250 [72]. Note that this deﬁnition", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-718", "line_number": 718, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-719", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "does not distinguish between internal and external entities.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-719", "line_number": 719, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-720", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.4.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-720", "line_number": 720, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-721", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Patterns for Entity Authentication", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-721", "line_number": 721, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-722", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Entity authentication according to the deﬁnition in ISO/IEC 9798 can be implemented with", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-722", "line_number": 722, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-723", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "challenge response-mechanisms. When prover and veriﬁer share a secret, the veriﬁer sends", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-723", "line_number": 723, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-724", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "an unpredictable challenge to the prover who constructs its response as a function of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-725", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "challenge and the shared secret. For example, HTTP digest authentication uses the hash of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-726", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the challenge, a password, and further data that binds authentication to a particular HTTP", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-726", "line_number": 726, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-727", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "request.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-727", "line_number": 727, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-728", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "When public key cryptography is used, the veriﬁer needs the prover’s public key. With a digital", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-729", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "signature scheme, the veriﬁer could send the challenge in the clear and the prover could", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-729", "line_number": 729, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-730", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "respond with the signed challenge. With a public key encryption scheme, the veriﬁer could", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-731", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "encrypt the challenge under the prover’s public key; a response constructed from the decrypted", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-732", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-732", "line_number": 732, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-733", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 24", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-733", "line_number": 733, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-734", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 26", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-734", "line_number": 734, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-735", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "challenge would authenticate the prover. The latter mechanism is used with Trusted Platform", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-735", "line_number": 735, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-736", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Modules (TPMs) where successful decryption of data encrypted under the public endorsement", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-736", "line_number": 736, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-737", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "key of a TPM authenticates the TPM. In both cases, the veriﬁer needs an authentic copy of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-738", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "prover’s public veriﬁcation key. When users are identiﬁed by arbitrary public keys, no Public", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-738", "line_number": 738, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-739", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Key Infrastructure is required and the public key could be set directly in a registration phase.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-739", "line_number": 739, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-740", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "5.4.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-740", "line_number": 740, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-741", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Correspondence Properties", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-741", "line_number": 741, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-742", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The Public-Key Needham-Schroeder protocol uses public key encryption with its challenge-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-743", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "response mechanism [52]. In this protocol, a malicious prover could decrypt a challenge", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-744", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and reuse it in a protocol run with a third party pretending to be the original veriﬁer; the third", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-745", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "party would then respond to the veriﬁer although the veriﬁer is not engaged in a protocol", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-746", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "run with the third party [56]. This scenario would amount to an attack if the mismatch in the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-747", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "assumptions about a protocol run is security relevant. The attack would be detected if the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-748", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "identities of prover and veriﬁer are included in all messages. Note that in this ‘attack’ the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-749", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "veriﬁer still correctly concludes that the prover is alive.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-750", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Matches in the assumptions about aspects of a protocol run held by the peers on completion of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-750", "line_number": 750, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-751", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "a run can be captured by correspondence properties, as proposed in [73] and further elaborated", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-752", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in [74]:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-753", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Aliveness: whenever the veriﬁer (initiator) concludes a protocol run, the prover had also", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-754", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "been engaged in a protocol run.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-755", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Weak agreement: whenever the veriﬁer (initiator) concludes a protocol run apparently", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-756", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "with a given prover, the prover had also been engaged in a protocol run, apparently with", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-756", "line_number": 756, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-757", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that veriﬁer.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-757", "line_number": 757, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-758", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Non-injective agreement: whenever the veriﬁer (initiator) concludes a protocol run appar-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-759", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ently with a given prover, the prover had also been engaged in a protocol run, apparently", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-760", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "with that veriﬁer, and responder and receiver agree on a speciﬁed set of data items", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-760", "line_number": 760, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-761", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "pertaining to a protocol run.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-761", "line_number": 761, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-762", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "• Agreement: whenever the veriﬁer (initiator) concludes a protocol run apparently with a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-762", "line_number": 762, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-763", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "given prover, the prover had also been engaged in a protocol run, apparently with that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-763", "line_number": 763, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-764", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "veriﬁer, and responder and receiver agree on a speciﬁed set of data items pertaining to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-765", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "a protocol run, and each protocol run of the veriﬁer corresponds to a unique protocol", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-766", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "run of the prover.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-767", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "In the vulnerable Redirect/POST Binding in Google Applications there is no agreement on", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-768", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the service provider an authentication assertion is intended for ([68], Section 5.3.3). Flawed", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-769", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "implementations of OAuth that use a ﬁxed value for the state variable do not even guarantee", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-770", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "aliveness ([70], Section 5.3.4).", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-771", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Correspondence properties are intensional properties well suited for protocol analysis using", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-772", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "model checking. This line of research had reversed the earlier decision to separate pure entity", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-773", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "authentication from agreeing on session keys and again added agreement on certain data", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-774", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "items to authentication. TAMARIN [75] and ProVerif [76] are examples for tools that support", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-775", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the automated analysis of authentication protocols.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-776", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-777", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 25", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-778", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 28", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-779", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-780", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Technical Aspects", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-781", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Accountability supports processes that are launched after events have occurred. Such a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-782", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "process may be a regular audit that checks whether an organisation complies with existing", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-783", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "regulations. It might represent a technical audit that scans logs in search for signs of a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-784", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "cyber attack. It may also be an investigation triggered by an incident that tries to identify the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-785", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "vulnerabilities exploited, or an investigation that tries to identify the parties responsible. In all", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-786", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "cases, the quality of the evidence is decisive.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-787", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The aforementioned processes make use of logs of events. Such logs may be kept by the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-788", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "operating system, by networking devices, or by applications (Section 6.2 will give an example).", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-789", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The nature of the events depends on the activity that is being monitored.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-790", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6.1.1", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-791", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Audit Policies", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-792", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Accountability is only as strong as the quality of evidence collected during operations. System", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-793", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "administrators may set audit policies that deﬁne which events will be logged. Examples for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-794", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "such events are successful and failed authentication attempts, and decisions on sensitive", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-795", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access requests. Operating systems and audit tools provide menus to guide administrators", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-796", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "through this task. Access control policies that specify as obligations that certain requests", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-796", "line_number": 796, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-797", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "must be logged also inﬂuence which evidence is collected.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-798", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6.1.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-799", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Preserving the Evidence", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-800", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Accountability is only as strong as the protection of the evidence collected during operations.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-801", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Attackers could try to hide their traces by deleting incriminating log entries once they have", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-802", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "acquired sufﬁcient privileges. They could then modify audit policies so that future actions are", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-803", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "not recorded, but should not be able to tamper with the evidence already collected.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-804", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Tamper resistance could rely on physical measures like printing the log on an endless paper", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-805", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "reel or writing the log to WORM (Write-Once, Read-Many) memory like an optical disk. Tamper", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-806", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "resistance could be supported by cryptography. Storing the log as a hash chain [80, 81] makes", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-807", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "it evident when entries have been removed, but does not guarantee that entries cannot be", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-808", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "lost.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-809", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Audit policies have to address situations where logging is disrupted, e.g., because the log", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-810", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ﬁle has run out of space. Is it then acceptable to overwrite old entries or should the system", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-811", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be stopped until proper auditing is again enabled? This conﬂict between availability and", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-812", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "accountability has to be resolved.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-813", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-813", "line_number": 813, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-814", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 27", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-814", "line_number": 814, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-815", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 29", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-816", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6.1.3", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-817", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Analysing the Evidence", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-817", "line_number": 817, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-818", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Audit logs can create large volumes of data and many entries are not security relevant so that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-818", "line_number": 818, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-819", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "automated processing is required. Known attack patterns can be detected by their signatures.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-820", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Machine learning techniques can help to detect anomalies. Lessons learned when applying", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-821", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "this approach to network intrusion detection are discussed in [82]. Visualisation techniques", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-822", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "try to draw the administrators’ attention to the most relevant events.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-823", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6.1.4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-824", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Assessing the Evidence", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-825", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Accountability is only as strong as the method of user authentication when legal or disciplinary", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-826", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "actions are to be supported. This relates to technical aspects of the authentication mechanism", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-827", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and also to user resilience to phishing and social engineering attacks. Telling users not to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-828", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "fall for obvious phishing attacks is easy, but a well-designed spear phishing attack will not be", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-829", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "obvious.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-830", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Accountability is only as strong as the organisational security policies on connecting devices,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-831", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "e.g. USB tokens, to internal systems, and policies on access to external web sites. Account-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-832", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ability is only as strong as the defences against software vulnerabilities that can be exploited", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-833", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to run code under a user identity without the user being aware of that fact, e.g. so-called", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-834", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "drive-by-downloads.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-835", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6.2", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-836", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Privacy and Accountability", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-837", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Privacy rules can have an impact on the events that may be logged. Employment law may, for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-838", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "example, limit how closely a company monitors its employees, which might make it difﬁcult", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-839", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to achieve accountability when rules have been broken.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-840", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Sometimes, there are technical resolutions to such conﬂicts between legal goals. Take the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-841", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "example of a company that is not permitted to log which external websites employees connect", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-842", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to: when an external site is attacked from within the company network, it is desirable that the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-843", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "perpetrator can be held accountable. To achieve both goals, the company gateway would log", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-844", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for outgoing requests only the internal IP address and the port number used with the global", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-845", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "IP address. There is thus no record of visited websites. If an attack is reported, the website", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-845", "line_number": 845, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-846", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "affected can provide the port number the attack came from, establishing the link between the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-846", "line_number": 846, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-847", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "internal IP address and the visited site.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-847", "line_number": 847, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-848", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Conversely, logging may have unintended privacy impacts. Take Certiﬁcate Transparency", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-849", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[RFC 6962] as an example. Certiﬁcate Transparency is a logging service for the issuers of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-850", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "TLS certiﬁcates. Participating Certiﬁcate Authorities record the issuance of certiﬁcates with", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-851", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "this service. Domain owners can scan the log for certiﬁcates for their domain that they had", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-852", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "not asked for, i.e., detect authentication failures at issuers. This service was introduced in", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-853", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "reaction to attacks where such misissued certiﬁcates had been used to impersonate the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-854", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "domain affected, and makes issuers accountable to domain owners.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-855", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Private subdomains are subdomains created for internal use only. When a certiﬁcate for a", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-856", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "private subdomain is requested, the certiﬁcate will be recorded in the Certiﬁcate Transparency", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-857", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "log disclosing the existence of the private subdomain to the public [83].", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-858", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-859", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 28", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-860", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 30", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-861", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6.3", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-862", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Distributed Logs", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-863", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Logs may be kept to hold the users of a system accountable. Logs may be kept to hold the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-864", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "owner of a system accountable. In the latter case, auditors may require that the logging device", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-865", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "is sealed, i.e., rely on a physical root of trust. Alternatively, logs could be kept in a distributed", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-866", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "system run by independent nodes where there are sufﬁcient barriers to forming alliances that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-867", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "can take over the system.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-868", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The nodes maintaining the log need to synchronise their versions of the log. The overheads", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-869", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "for synchronisation, or consensus, depend on the failure model for the nodes and for the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-870", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "communication network, and on the rules for joining the distributed system. Systems may be", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-871", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "open for anyone, or be governed by a membership service. The recent interest in blockchains", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-872", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "extends to this type of logging solutions.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-873", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "6.4", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-874", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Related Concepts", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-875", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The deﬁnition at the start of Section 6 refers to non-repudiation and intrusion detection. Non-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-876", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "repudiation has a speciﬁc meaning in communication security, viz. providing unforgeable", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-877", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "evidence that a speciﬁc action occurred. This goal is not necessarily achieved by logging", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-877", "line_number": 877, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-878", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "mechanisms; they may protect the entries recorded, but may record entries that have already", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-878", "line_number": 878, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-879", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "been manipulated.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-879", "line_number": 879, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-880", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Intrusion detection (see the Security Operations & Incident Management CyBOK Knowledge", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-880", "line_number": 880, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-881", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Area [1]) is an area of its own with overlapping goals. Intrusion detection does not have the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-881", "line_number": 881, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-882", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "requirement for actions of an entity to be traced uniquely to that entity. The focus will be more", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-882", "line_number": 882, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-883", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "on detecting attacks than detecting the attacker.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-883", "line_number": 883, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-884", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The deﬁnition given subsumes both the accountability of legal persons and technical inves-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-884", "line_number": 884, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-885", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "tigations into security breaches. The standards of evidence may be higher in the ﬁrst case.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-885", "line_number": 885, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-886", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Tracing actions uniquely to an entity leads to cyber attribution, the process of tracking and", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-886", "line_number": 886, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-887", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "identifying the perpetrators of a cyber attack. Circumstantial evidence such as similarity", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-887", "line_number": 887, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-888", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in malware may be used in this process, and mis-attribution due to false ﬂag operations is", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-888", "line_number": 888, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-889", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "an issue. Calling for DRM to protect the intellectual property of content owners, because", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-889", "line_number": 889, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-890", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "digital content can be copied so easily, but assuming that malware cannot be copied would", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-890", "line_number": 890, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-891", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "be incongruous.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-891", "line_number": 891, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-892", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "APPLYING THE KNOWLEDGE", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-892", "line_number": 892, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-893", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "IT security mechanisms should not be deployed for their own sake but for a reason. The", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-893", "line_number": 893, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-894", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "reason has to come from an application in need of protection. An organisational policy", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-894", "line_number": 894, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-895", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "would capture the protection requirements and then be implemented by an automated policy", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-895", "line_number": 895, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-896", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "(Section 3.1.1). Sometimes, this process can start from a clearly deﬁned organisational policy.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-896", "line_number": 896, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-897", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The policies governing access to classiﬁed paper documents are an example. There, the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-897", "line_number": 897, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-898", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "translation into automated policies did not have to bridge a wide conceptual gap, although", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-898", "line_number": 898, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-899", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "there were unanticipated twists, e.g., the no write-up policy of the BLP model. These speciﬁc", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-899", "line_number": 899, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-900", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "circumstances may have raised the unwarranted expectation that this approach would work", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-900", "line_number": 900, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-901", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in general. The fact that these policies were applied in highly hierarchical organisations and", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-901", "line_number": 901, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-902", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "were fairly stable are further points worth noting.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-902", "line_number": 902, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-903", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-903", "line_number": 903, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-904", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 29", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-904", "line_number": 904, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-905", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 31", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-905", "line_number": 905, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-906", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Sinclair et al. paint a picture of a very different world [84]. Their observations can be sum-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-906", "line_number": 906, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-907", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "marized under the headings of translation (from organisational to automated policies) and", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-907", "line_number": 907, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-908", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "automation. Any translation has to start from a source document, in our case an organisa-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-908", "line_number": 908, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-909", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "tional policy. The translator will face problems when the source is ambiguous or inconsistent.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-909", "line_number": 909, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-910", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "This situation is more likely to arise in organisations with a matrixed structure, where several", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-910", "line_number": 910, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-911", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "entities are setting policies, than in strictly hierarchical organisations. Moreover, the wider the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-911", "line_number": 911, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-912", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "language gap between the source document and the destination document, the more difﬁcult", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-912", "line_number": 912, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-913", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "translation becomes, and the more difﬁcult it is to ascertain that the translation meets the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-913", "line_number": 913, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-914", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "spirit of the source. The latter step is a prerequisite for policy certiﬁcation, i.e., management", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-914", "line_number": 914, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-915", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "approval of a given automated policy.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-915", "line_number": 915, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-916", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Organisational policies may intentionally leave decisions at the discretion of caseworkers,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-916", "line_number": 916, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-917", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "e.g., for handling situations where none of the existing rules is directly applicable or where", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-917", "line_number": 917, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-918", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "competing rules apply. It is a purpose of automation to remove discretion. Removing discretion", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-918", "line_number": 918, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-919", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "adds rules that do not have a counterpart in the organisational policy. Creating an automated", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-919", "line_number": 919, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-920", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "policy is then more than translation, it becomes an exercise in creative writing in the spirit of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-920", "line_number": 920, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-921", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the organisational policy. To do this job well, the writer needs a good understanding of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-921", "line_number": 921, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-922", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "applications and their workﬂows, on top of proﬁciency in the target language (the domain", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-922", "line_number": 922, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-923", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of IT experts). Automated policies based on na¨ıve assumptions easily become denial-of-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-923", "line_number": 923, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-924", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "service attacks on the user. As a related point, there is a tension between the competing", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-924", "line_number": 924, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-925", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "goals of keeping a policy simple – which may be feasible in an organisational policy that", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-925", "line_number": 925, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-926", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "leaves room for discretion – and of requiring the (automated) policy to cater for a variety of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-926", "line_number": 926, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-927", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "different contexts. This explains why in many cases the number of rules created to cater for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-927", "line_number": 927, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-928", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "exceptions to the general rules ends up being overwhelming. Points that span organisational", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-928", "line_number": 928, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-929", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and automated policies are the handling of dynamic policy changes and the analysis of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-929", "line_number": 929, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-930", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "side-effects of policy rules in highly complex systems.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-930", "line_number": 930, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-931", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "The literature on security operations has to say more about the points raised in this section", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-931", "line_number": 931, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-932", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "than the research literature on IT security, which has a habit of abstracting problems to a point", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-932", "line_number": 932, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-933", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "where much of the awkward issues encountered in real life have disappeared [84], and then", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-933", "line_number": 933, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-934", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "confusing its simpliﬁed models with reality.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-934", "line_number": 934, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-935", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Similar disconnects between application experts and infrastructure experts exist within the IT", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-935", "line_number": 935, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-936", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "domain. Dynamically conﬁgurable applications are running foul of well-intended policies such", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-936", "line_number": 936, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-937", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "as SOP (Section 4.2) and CSP (Section 4.2.1). Organisations may then opt for open policies", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-937", "line_number": 937, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-938", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "that provide no protection but allow the dynamic applications to run, or applications writers", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-938", "line_number": 938, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-939", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "may explore workarounds accepted by the automated policy but still defeating its spirit.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-939", "line_number": 939, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-940", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "CONCLUSIONS", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-940", "line_number": 940, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-941", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access control has kept adapting to the changing applications of IT systems. Access control", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-941", "line_number": 941, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-942", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "was originally conceived for the protection of sensitive data in multi-user and multi-level", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-942", "line_number": 942, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-943", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "secure systems. Access control without user identities was literally unthinkable. Applications", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-943", "line_number": 943, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-944", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "have since changed and some require new modes of access control. One could then reserve", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-944", "line_number": 944, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-945", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "‘access control’ for the original setting and invent new terms for each new ﬂavour of access", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-945", "line_number": 945, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-946", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "control. DRM may serve as an example. This KA has not taken this route but applied ‘access", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-946", "line_number": 946, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-947", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "control’, ‘authentication’, and ‘authorisation’ more generally while staying true to the generic", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-947", "line_number": 947, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-948", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "meanings of these terms. User identities have lost their prominence along this way. Code", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-948", "line_number": 948, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-949", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "(apps) and web domains have taken their place.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-949", "line_number": 949, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-950", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-950", "line_number": 950, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-951", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 30", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-951", "line_number": 951, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-952", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 33", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-952", "line_number": 952, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-953", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[6] J. Park and R. Sandhu, “The UCON ABC usage control model,” ACM Transactions on", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-953", "line_number": 953, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-954", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Information and System Security (TISSEC), vol. 7, no. 1, pp. 128–174, 2004.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-954", "line_number": 954, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-955", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[7] R. S. Sandhu, D. Ferraiolo, and R. Kuhn, “The NIST model for role based access control:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-955", "line_number": 955, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-956", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Toward a uniﬁed standard,” in Proceedings of the 5th ACM Workshop on Role Based", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-956", "line_number": 956, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-957", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Access Control, July 2000, pp. 47–63.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-957", "line_number": 957, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-958", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[8] F. B. Schneider, “Enforceable security policies,” ACM Transactions on Information Systems", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-958", "line_number": 958, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-959", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Security, vol. 3, no. 1, pp. 30–50, Feb. 2000.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-959", "line_number": 959, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-960", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[9] M. C. Libicki, “Cyberspace is not a warﬁghting domain,” ISJLP, vol. 8, p. 321, 2012.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-960", "line_number": 960, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-961", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[10] R. Kissel, Revision 2: Glossary of key information security terms.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-961", "line_number": 961, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-962", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Diane Publishing, 2013.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-962", "line_number": 962, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-963", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[11] J. Crampton and J. Sellwood, “Path conditions and principal matching: a new approach", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-963", "line_number": 963, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-964", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "to access control,” in Proceedings of the 19th ACM symposium on Access control models", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-964", "line_number": 964, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-965", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and technologies.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-965", "line_number": 965, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-966", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ACM, 2014, pp. 187–198.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-966", "line_number": 966, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-967", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[12] B. Lampson, “Protection,” Operating Systems Review, pp. 8, 1, 18–24, 1974.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-967", "line_number": 967, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-968", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[13] H. Bos, The Cyber Security Body of Knowledge.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-968", "line_number": 968, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-969", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "University of Bristol, 2021, ch. Operating", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-969", "line_number": 969, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-970", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Systems & Virtualisation, version 1.0.1. [Online]. Available: https://www.cybok.org/", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-970", "line_number": 970, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-971", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[14] P. Loscocco and S. Smalley, “Integrating ﬂexible support for security policies into the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-971", "line_number": 971, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-972", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "linux operating system.” in USENIX Annual Technical Conference, FREENIX Track, 2001,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-972", "line_number": 972, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-973", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "pp. 29–42.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-973", "line_number": 973, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-974", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[15] F. Mayer, D. Caplan, and K. MacMillan, SELinux by example: using security enhanced Linux.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-974", "line_number": 974, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-975", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Pearson Education, 2006.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-975", "line_number": 975, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-976", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[16] S. Smalley and R. Craig, “Security enhanced (SE) Android: Bringing ﬂexible MAC to", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-976", "line_number": 976, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-977", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Android,” in NDSS, vol. 310, 2013, pp. 20–38.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-977", "line_number": 977, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-978", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[17] N. Condori-Fern´andez and, V. N. L. Franqueira, and R. Wieringa, “Report on the survey of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-978", "line_number": 978, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-979", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "role-based access control (RBAC) in practice,” Centre for Telematics and Information", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-979", "line_number": 979, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-980", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Technology, University of Twente, Tech. Rep. TR-CTIT-12-06, 2012.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-980", "line_number": 980, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-981", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[18] V. C. Hu, D. Ferraiolo, R. Kuhn, A. R. Friedman, A. J. Lang, M. M. Cogdell, A. Schnitzer,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-981", "line_number": 981, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-982", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "K. Sandlin, R. Miller, K. Scarfone et al., “Guide to attribute based access control (ABAC)", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-982", "line_number": 982, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-983", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "deﬁnition and considerations (draft),” NIST special publication, vol. 800, no. 162, 2013.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-983", "line_number": 983, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-984", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[19] L. Gong, M. Dageforde, and G. W. Ellison, Inside Java 2 Platform Security, 2nd ed. Reading,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-984", "line_number": 984, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-985", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "MA: Addison-Wesley, 2003.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-985", "line_number": 985, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-986", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[20] B. A. La Macchia, S. Lange, M. Lyons, R. Martin, and K. T. Price, .NET Framework Security.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-986", "line_number": 986, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-987", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Boston, MA: Addison-Wesley Professional, 2002.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-987", "line_number": 987, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-988", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[21] N. Hardy, “The confused deputy,” Operating Systems Reviews, vol. 22, no. 4, pp. 36–38,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-988", "line_number": 988, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-989", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "1988.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-989", "line_number": 989, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-990", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[22] A. P. Felt, E. Ha, S. Egelman, A. Haney, E. Chin, and D. Wagner, “Android permissions:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-990", "line_number": 990, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-991", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "User attention, comprehension, and behavior,” in Proceedings of the Eighth Symposium", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-991", "line_number": 991, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-992", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "on Usable Privacy and Security, ser. SOUPS ’12.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-992", "line_number": 992, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-993", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "New York, NY, USA: ACM, 2012, pp.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-993", "line_number": 993, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-994", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "3:1–3:14. [Online]. Available: http://doi.acm.org/10.1145/2335356.2335360", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-994", "line_number": 994, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-995", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[23] S. Fahl, The Cyber Security Body of Knowledge.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-995", "line_number": 995, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-996", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "University of Bristol, 2021, ch. Web &", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-996", "line_number": 996, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-997", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Mobile Security, version 1.0.1. [Online]. Available: https://www.cybok.org/", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-997", "line_number": 997, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-998", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[24] R. Moan and I. Kawahara, “Superdistribution: An electronic infrastructure for the economy", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-998", "line_number": 998, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-999", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of the future,” Transactions of Information Processing Society of Japan, vol. 38, no. 7, pp.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-999", "line_number": 999, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1000", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "1465–1472, 1997.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1000", "line_number": 1000, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1001", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[25] M. C. Mont, S. Pearson, and P. Bramhall, “Towards accountable management of identity", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1001", "line_number": 1001, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1002", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "and privacy: Sticky policies and enforceable tracing services,” in 14th International Work-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1002", "line_number": 1002, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1003", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "shop on Database and Expert Systems Applications, 2003. Proceedings.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1003", "line_number": 1003, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1004", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "IEEE, 2003, pp.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1004", "line_number": 1004, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1005", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "377–382.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1005", "line_number": 1005, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1006", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[26] E. Brickell, J. Camenisch, and L. Chen, “Direct anonymous attestation,” in Proceedings", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1006", "line_number": 1006, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1007", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of the 11th ACM conference on Computer and communications security.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1007", "line_number": 1007, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1008", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ACM, 2004, pp.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1008", "line_number": 1008, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1009", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1009", "line_number": 1009, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1010", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 32", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1010", "line_number": 1010, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1011", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 34", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1011", "line_number": 1011, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1012", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "132–145.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1012", "line_number": 1012, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1013", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[27] M. Salah, R. Philpott, S. Srinivas, J. Kemp, and J. Hodges, “FIDO UAF architectural overview,”", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1013", "line_number": 1013, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1014", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "FIDO Alliance, Tech. Rep., February 2018, draft 20.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1014", "line_number": 1014, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1015", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[28] E. Rissanen, “eXtensible access control markup language (XACML),” Oasis, Tech. Rep.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1015", "line_number": 1015, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1016", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "xacml-3.0-core-spec-os-en, 2013, version 3.0.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1016", "line_number": 1016, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1017", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[29] E. Rissanen, H. Lockhart, and T. Moses, “Xacml v3. 0 administration and delegation proﬁle", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1017", "line_number": 1017, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1018", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "version 1.0,” Committee Draft, vol. 1, 2009.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1018", "line_number": 1018, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1019", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[30] “DoD Trusted Computer System Evaluation Criteria,” U.S. Department of Defense, 1985,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1019", "line_number": 1019, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1020", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "DOD 5200.28-STD.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1020", "line_number": 1020, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1021", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[31] ´U. Erlingsson and F. B. Schneider, “IRM enforcement of Java stack inspection,” in Pro-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1021", "line_number": 1021, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1022", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ceedings of the 2000 IEEE Symposium on Security and Privacy, 2000, pp. 246–255.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1022", "line_number": 1022, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1023", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[32] D. E. Bell and L. J. LaPadula, “Secure computer systems: Mathematical foundations,” The", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1023", "line_number": 1023, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1024", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "MITRE Corporation, Bedford MA, Tech. Rep. ESD-TR-73-278, Nov. 1973.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1024", "line_number": 1024, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1025", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[33] T. F. Lunt, D. E. Denning, R. R. Schell, M. Heckman, and W. R. Shockley, “The seaview", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1025", "line_number": 1025, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1026", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "security model,” IEEE Transactions on software engineering, vol. 16, no. 6, pp. 593–607,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1026", "line_number": 1026, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1027", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "1990.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1027", "line_number": 1027, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1028", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[34] K. J. Biba, “Integrity consideration for secure computer systems,” The MITRE Corporation,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1028", "line_number": 1028, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1029", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Bedford, MA, Tech. Rep. ESD-TR-76-372, MTR-3153, April 1977.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1029", "line_number": 1029, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1030", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[35] D. D. Clark and D. R. Wilson, “A comparison of commercial and military computer", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1030", "line_number": 1030, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1031", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "security policies,” in Proceedings of the 1987 IEEE Symposium on Security and Privacy,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1031", "line_number": 1031, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1032", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Oakland, California, USA, April 27-29, 1987, 1987, pp. 184–195. [Online]. Available:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1032", "line_number": 1032, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1033", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "https://doi.org/10.1109/SP.1987.10001", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1033", "line_number": 1033, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1034", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[36] D. F. C. Brewer and M. J. Nash, “The Chinese Wall security policy,” in Proceedings of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1034", "line_number": 1034, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1035", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "1989 IEEE Symposium on Security and Privacy, 1989, pp. 206–214.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1035", "line_number": 1035, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1036", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[37] M. A. Harrison, W. L. Ruzzo, and J. D. Ullman, “Protection in operating systems,” Commu-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1036", "line_number": 1036, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1037", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "nications of the ACM, vol. 19, no. 8, pp. 461–471, August 1976.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1037", "line_number": 1037, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1038", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[38] N. Li, B. N. Grosof, and J. Feigenbaum, “Delegation logic: A logic-based approach to dis-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1038", "line_number": 1038, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1039", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "tributed authorization,” ACM Transactions on Information and System Security (TISSEC),", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1039", "line_number": 1039, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1040", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "vol. 6, no. 1, pp. 128–171, 2003.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1040", "line_number": 1040, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1041", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[39] M. Abadi, M. Burrows, B. W. Lampson, and G. D. Plotkin, “A calculus for access control in", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1041", "line_number": 1041, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1042", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "distributed systems,” ACM Transactions Programming Language Systems, vol. 15, no. 4,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1042", "line_number": 1042, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1043", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "pp. 706–734, 1993. [Online]. Available: https://doi.org/10.1145/155183.155225", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1043", "line_number": 1043, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1044", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[40] M. Blaze, J. Feigenbaum, and J. Lacy, “Decentralized trust management,” in Proceedings", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1044", "line_number": 1044, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1045", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "of the 1996 IEEE Symposium on Security and Privacy, 1996, pp. 164–173.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1045", "line_number": 1045, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1046", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[41] J. DeTreville, “Binder, a logic-based security language,” in Proceedings 2002 IEEE Sympo-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1046", "line_number": 1046, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1047", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "sium on Security and Privacy.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1047", "line_number": 1047, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1048", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "IEEE, 2002, pp. 105–113.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1048", "line_number": 1048, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1049", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[42] S. Stamm, B. Sterne, and G. Markham, “Reining in the web with content security policy,”", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1049", "line_number": 1049, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1050", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in Proceedings of the 19th international conference on World wide web.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1050", "line_number": 1050, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1051", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ACM, 2010, pp.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1051", "line_number": 1051, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1052", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "921–930.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1052", "line_number": 1052, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1053", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[43] V. Goyal, O. Pandey, A. Sahai, and B. Waters, “Attribute-based encryption for ﬁne-grained", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1053", "line_number": 1053, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1054", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "access control of encrypted data,” in Proceedings of the 13th ACM conference on Com-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1054", "line_number": 1054, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1055", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "puter and communications security.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1055", "line_number": 1055, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1056", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ACM, 2006, pp. 89–98.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1056", "line_number": 1056, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1057", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[44] ITU-T, “X509 (2014) ISO/IEC 9594-8:2014/Cor 2:2016, Directory: Public-key and attribute", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1057", "line_number": 1057, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1058", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "certiﬁcate framework [TECHNICAL CORRIGENDUM 2],” 2016.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1058", "line_number": 1058, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1059", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[45] M. Wong and W. Schlitt, “Sender policy framework (SPF) for authorizing use of domains", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1059", "line_number": 1059, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1060", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in e-mail, version 1,” RFC 4408, Tech. Rep., 2006.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1060", "line_number": 1060, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1061", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[46] L. Weichselbaum, M. Spagnuolo, S. Lekies, and A. Janc, “CSP is dead, long live CSP! on", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1061", "line_number": 1061, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1062", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "the insecurity of whitelists and the future of content security policy,” in Proceedings of the", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1062", "line_number": 1062, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1063", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "2016 ACM SIGSAC Conference on Computer and Communications Security.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1063", "line_number": 1063, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1064", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "ACM, 2016,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1064", "line_number": 1064, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1065", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1065", "line_number": 1065, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1066", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 33", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1066", "line_number": 1066, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1067", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "## Page 35", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1067", "line_number": 1067, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1068", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "pp. 1376–1387.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1068", "line_number": 1068, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1069", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[47] A.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1069", "line_number": 1069, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1070", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Van", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1070", "line_number": 1070, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1071", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Kesteren,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1071", "line_number": 1071, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1072", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "“Cross-origin", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1072", "line_number": 1072, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1073", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "resource", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1073", "line_number": 1073, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1074", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "sharing,”", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1074", "line_number": 1074, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1075", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "W3C", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1075", "line_number": 1075, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1076", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Working", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1076", "line_number": 1076, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1077", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Draft,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1077", "line_number": 1077, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1078", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "http://www.w3.org/TR/2014/REC-cors-********/, 2014.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1078", "line_number": 1078, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1079", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[48] J. Bethencourt, A. Sahai, and B. Waters, “Ciphertext-policy attribute-based encryption,” in", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1079", "line_number": 1079, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1080", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "2007 IEEE symposium on security and privacy (SP’07).", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1080", "line_number": 1080, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1081", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "IEEE, 2007, pp. 321–334.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1081", "line_number": 1081, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1082", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[49] W. C. Garrison, A. Shull, S. Myers, and A. J. Lee, “On the practicality of cryptographically", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1082", "line_number": 1082, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1083", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "enforcing dynamic access control policies in the cloud,” in 2016 IEEE Symposium on", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1083", "line_number": 1083, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1084", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Security and Privacy (SP).", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1084", "line_number": 1084, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1085", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "IEEE, 2016, pp. 819–838.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1085", "line_number": 1085, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1086", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[50] C. M. Ellison, B. Frantz, B. W. Lampson, R. Rivest, B. Thomas, and T. Yl¨onen,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1086", "line_number": 1086, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1087", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "“SPKI certiﬁcate theory,” RFC, vol. 2693, pp. 1–43, 1999. [Online]. Available: https:", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1087", "line_number": 1087, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1088", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "//doi.org/10.17487/RFC2693", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1088", "line_number": 1088, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1089", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[51] O. Bandmann, M. Dam, and B. S. Firozabadi, “Constrained delegation,” in Proceedings", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1089", "line_number": 1089, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1090", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "2002 IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1090", "line_number": 1090, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1091", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "IEEE, 2002, pp. 131–140.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1091", "line_number": 1091, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1092", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[52] R. M. Needham and M. D. Schroeder, “Using encryption for authentication in large net-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1092", "line_number": 1092, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1093", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "works of computers,” Communications of the ACM, vol. 21, no. 12, pp. 993–999, 1978.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1093", "line_number": 1093, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1094", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[53] J. G. Steiner, B. C. Neuman, and J. I. Schiller, “Kerberos: An authentication service for", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1094", "line_number": 1094, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1095", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "open network systems.” in Winter 1988 Usenix Conference.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1095", "line_number": 1095, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1096", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Citeseer, 1988, pp. 191–202.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1096", "line_number": 1096, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1097", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[54] H. Lockhart and B. Campbell, “Security assertion markup language (SAML) v2. 0 technical", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1097", "line_number": 1097, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1098", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "overview,” OASIS Committee Draft, vol. 2, pp. 94–106, 2008.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1098", "line_number": 1098, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1099", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[55] D. Hardt, “The OAuth 2.0 authorization framework,” Internet Requests for Comments,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1099", "line_number": 1099, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1100", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "RFC Editor, RFC 6749, October 2012. [Online]. Available: http://www.rfc-editor.org/rfc/", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1100", "line_number": 1100, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1101", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "rfc6749.txt", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1101", "line_number": 1101, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1102", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[56] G. Lowe, “Breaking and ﬁxing the needham-schroeder public-key protocol using fdr,”", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1102", "line_number": 1102, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1103", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in International Workshop on Tools and Algorithms for the Construction and Analysis of", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1103", "line_number": 1103, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1104", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Systems.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1104", "line_number": 1104, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1105", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Springer, 1996, pp. 147–166.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1105", "line_number": 1105, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1106", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[57] P. A. Grassi, M. E. Garcia, and J. L. Fenton, “Digital identity guidelines,” NIST special", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1106", "line_number": 1106, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1107", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "publication, vol. 800, pp. 63–3, 2017.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1107", "line_number": 1107, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1108", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[58] C. Rossow and S. Jha, The Cyber Security Body of Knowledge.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1108", "line_number": 1108, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1109", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "University of Bristol,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1109", "line_number": 1109, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1110", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "2021, ch. Network Security, version 2.0. [Online]. Available: https://www.cybok.org/", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1110", "line_number": 1110, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1111", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[59] M. A. Sasse and A. Rashid, The Cyber Security Body of Knowledge.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1111", "line_number": 1111, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1112", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "University of Bristol,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1112", "line_number": 1112, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1113", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "2021, ch. Human Factors, version 1.0.1. [Online]. Available: https://www.cybok.org/", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1113", "line_number": 1113, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1114", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[60] S. K. Modi, S. J. Elliott, J. Whetsone, and H. Kim, “Impact of age groups on ﬁngerprint", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1114", "line_number": 1114, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1115", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "recognition performance,” in 2007 IEEE Workshop on Automatic Identiﬁcation Advanced", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1115", "line_number": 1115, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1116", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Technologies, June 2007, pp. 19–23.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1116", "line_number": 1116, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1117", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[61] L. Ghiani, D. Yambay, V. Mura, S. Tocco, G. L. Marcialis, F. Roli, and S. Schuckcrs, “Livdet", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1117", "line_number": 1117, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1118", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "2013 ﬁngerprint liveness detection competition 2013,” in 2013 International Conference", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1118", "line_number": 1118, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1119", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "on Biometrics (ICB).", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1119", "line_number": 1119, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1120", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "IEEE, 2013, pp. 1–6.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1120", "line_number": 1120, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1121", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[62] R. D. Labati, A. Genovese, E. Mu˜noz, V. Piuri, F. Scotti, and G. Sforza, “Biometric recognition", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1121", "line_number": 1121, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1122", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "in automated border control: a survey,” ACM Computing Surveys (CSUR), vol. 49, no. 2,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1122", "line_number": 1122, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1123", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "p. 24, 2016.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1123", "line_number": 1123, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1124", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[63] L. M. Mayron, “Biometric authentication on mobile devices.” IEEE Security & Privacy,", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1124", "line_number": 1124, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1125", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "vol. 13, no. 3, pp. 70–73, 2015.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1125", "line_number": 1125, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1126", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[64] M. S. Obaidat, I. Traore, and I. Woungang, Biometric-Based Physical and Cybersecurity", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1126", "line_number": 1126, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1127", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Systems.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1127", "line_number": 1127, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1128", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Springer, 2019.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1128", "line_number": 1128, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1129", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[65] R. Joyce and G. Gupta, “Identity authentication based on keystroke latencies,” Communi-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1129", "line_number": 1129, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1130", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "cations of the ACM, vol. 33, no. 2, pp. 168–176, 1990.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1130", "line_number": 1130, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1131", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[66] F. Monrose and A. D. Rubin, “Keystroke dynamics as a biometric for authentication,”", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1131", "line_number": 1131, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1132", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Future Generation Computer Systems, vol. 16, no. 4, pp. 351–359, 2000.", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1132", "line_number": 1132, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1133", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "[67] M. Ammar, Y. Yoshida, and T. Fukumura, “A new effective approach for on-line veriﬁca-", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1133", "line_number": 1133, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1134", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "KA Authentication, Authorisation & Accountability | July 2021", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1134", "line_number": 1134, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
{"chunk_id": "line-1135", "filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "content": "Page 34", "metadata": {"filename": "Authentication_Authorisation_Accountability_v1.0.2_processed.txt", "chunk_id": "line-1135", "line_number": 1135, "source": "知识库\\output\\Authentication_Authorisation_Accountability_v1.0.2_processed.txt"}}
