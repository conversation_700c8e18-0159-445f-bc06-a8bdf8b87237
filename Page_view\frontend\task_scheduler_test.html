<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时任务测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-active {
            background-color: #28a745;
        }
        .status-inactive {
            background-color: #dc3545;
        }
        .card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">每周威胁情报爬取定时任务管理</h1>
        
        <!-- 任务状态卡片 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">任务状态</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>当前状态:</strong> 
                            <span class="status-indicator" id="status-indicator"></span>
                            <span id="status-text">检查中...</span>
                        </p>
                        <p><strong>上次执行:</strong> <span id="last-run-time">-</span></p>
                        <p><strong>下次执行:</strong> <span id="next-run-time">-</span></p>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" id="toggle-btn" onclick="toggleTask()">
                                <span id="toggle-text">检查中...</span>
                            </button>
                            <button class="btn btn-success" onclick="runTaskNow()">
                                立即执行一次
                            </button>
                            <button class="btn btn-secondary" onclick="refreshStatus()">
                                刷新状态
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 日志输出 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">操作日志</h5>
            </div>
            <div class="card-body">
                <div id="log-output" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                    <p class="text-muted">等待操作...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let isActive = false;
        
        // 页面加载时获取状态
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
        });
        
        // 刷新任务状态
        function refreshStatus() {
            addLog('正在获取任务状态...');
            
            fetch('/api/task_status')
                .then(response => response.json())
                .then(data => {
                    updateStatusDisplay(data);
                    addLog('任务状态已更新');
                })
                .catch(error => {
                    addLog('获取任务状态失败: ' + error.message, 'error');
                });
        }
        
        // 更新状态显示
        function updateStatusDisplay(data) {
            isActive = data.is_active;
            
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const toggleBtn = document.getElementById('toggle-btn');
            const toggleText = document.getElementById('toggle-text');
            const lastRunTime = document.getElementById('last-run-time');
            const nextRunTime = document.getElementById('next-run-time');
            
            if (isActive) {
                statusIndicator.className = 'status-indicator status-active';
                statusText.textContent = '已启用';
                toggleBtn.className = 'btn btn-danger';
                toggleText.textContent = '停用定时任务';
            } else {
                statusIndicator.className = 'status-indicator status-inactive';
                statusText.textContent = '已停用';
                toggleBtn.className = 'btn btn-success';
                toggleText.textContent = '启用定时任务';
            }
            
            lastRunTime.textContent = data.last_run ? 
                new Date(data.last_run).toLocaleString('zh-CN') : '尚未执行';
            nextRunTime.textContent = data.next_run ? 
                new Date(data.next_run).toLocaleString('zh-CN') : '未计划';
        }
        
        // 切换任务状态
        function toggleTask() {
            const action = isActive ? '停用' : '启用';
            addLog(`正在${action}定时任务...`);
            
            fetch('/api/toggle_task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog(`定时任务已${action}`, 'success');
                    refreshStatus();
                } else {
                    addLog(`${action}定时任务失败: ` + data.message, 'error');
                }
            })
            .catch(error => {
                addLog(`${action}定时任务失败: ` + error.message, 'error');
            });
        }
        
        // 立即执行任务
        function runTaskNow() {
            addLog('正在启动威胁情报爬取任务...');
            
            fetch('/api/run_task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('任务已开始执行，请查看后台日志', 'success');
                    // 延迟刷新状态
                    setTimeout(refreshStatus, 2000);
                } else {
                    addLog('启动任务失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                addLog('启动任务失败: ' + error.message, 'error');
            });
        }
        
        // 添加日志
        function addLog(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString('zh-CN');
            
            let className = 'text-dark';
            if (type === 'success') className = 'text-success';
            else if (type === 'error') className = 'text-danger';
            else if (type === 'warning') className = 'text-warning';
            
            const logEntry = document.createElement('p');
            logEntry.className = className + ' mb-1';
            logEntry.innerHTML = `<small class="text-muted">[${timestamp}]</small> ${message}`;
            
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }
    </script>
</body>
</html>
