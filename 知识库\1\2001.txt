CVE-2001-0001(PUBLISHED):cookiedecode function in PHP-Nuke 4.4 allows users to bypass authentication and gain access to other user accounts by extracting the authentication information from a cookie.
CVE-2001-0002(PUBLISHED):Internet Explorer 5.5 and earlier allows remote attackers to obtain the physical location of cached content and open the content in the Local Computer Zone, then use compiled HTML help (.chm) files to execute arbitrary programs.
CVE-2001-0003(PUBLISHED):Web Extender Client (WEC) in Microsoft Office 2000, Windows 2000, and Windows Me does not properly process Internet Explorer security settings for NTLM authentication, which allows attackers to obtain NTLM credentials and possibly obtain the password, aka the "Web Client NTLM Authentication" vulnerability.
CVE-2001-0004(PUBLISHED):IIS 5.0 and 4.0 allows remote attackers to read the source code for executable web server programs by appending "%3F+.htr" to the requested URL, which causes the files to be parsed by the .HTR ISAPI extension, aka a variant of the "File Fragment Reading via .HTR" vulnerability.
CVE-2001-0005(PUBLISHED):Buffer overflow in the parsing mechanism of the file loader in Microsoft PowerPoint 2000 allows attackers to execute arbitrary commands.
CVE-2001-0006(PUBLISHED):The Winsock2ProtocolCatalogMutex mutex in Windows NT 4.0 has inappropriate Everyone/Full Control permissions, which allows local users to modify the permissions to "No Access" and disable Winsock network connectivity to cause a denial of service, aka the "Winsock Mutex" vulnerability.
CVE-2001-0007(PUBLISHED):Buffer overflow in NetScreen Firewall WebUI allows remote attackers to cause a denial of service via a long URL request to the web administration interface.
CVE-2001-0008(PUBLISHED):Backdoor account in Interbase database server allows remote attackers to overwrite arbitrary files using stored procedures.
CVE-2001-0009(PUBLISHED):Directory traversal vulnerability in Lotus Domino 5.0.5 web server allows remote attackers to read arbitrary files via a .. attack.
CVE-2001-0010(PUBLISHED):Buffer overflow in transaction signature (TSIG) handling code in BIND 8 allows remote attackers to gain root privileges.
CVE-2001-0011(PUBLISHED):Buffer overflow in nslookupComplain function in BIND 4 allows remote attackers to gain root privileges.
CVE-2001-0012(PUBLISHED):BIND 4 and BIND 8 allow remote attackers to access sensitive information such as environment variables.
CVE-2001-0013(PUBLISHED):Format string vulnerability in nslookupComplain function in BIND 4 allows remote attackers to gain root privileges.
CVE-2001-0014(PUBLISHED):Remote Data Protocol (RDP) in Windows 2000 Terminal Service does not properly handle certain malformed packets, which allows remote attackers to cause a denial of service, aka the "Invalid RDP Data" vulnerability.
CVE-2001-0015(PUBLISHED):Network Dynamic Data Exchange (DDE) in Windows 2000 allows local users to gain SYSTEM privileges via a "WM_COPYDATA" message to an invisible window that is running with the privileges of the WINLOGON process.
CVE-2001-0016(PUBLISHED):NTLM Security Support Provider (NTLMSSP) service does not properly check the function number in an LPC request, which could allow local users to gain administrator level access.
CVE-2001-0017(PUBLISHED):Memory leak in PPTP server in Windows NT 4.0 allows remote attackers to cause a denial of service via a malformed data packet, aka the "Malformed PPTP Packet Stream" vulnerability.
CVE-2001-0018(PUBLISHED):Windows 2000 domain controller in Windows 2000 Server, Advanced Server, or Datacenter Server allows remote attackers to cause a denial of service via a flood of malformed service requests.
CVE-2001-0019(PUBLISHED):Arrowpoint (aka Cisco Content Services, or CSS) allows local users to cause a denial of service via a long argument to the "show script," "clear script," "show archive," "clear archive," "show log," or "clear log" commands.
CVE-2001-0020(PUBLISHED):Directory traversal vulnerability in Arrowpoint (aka Cisco Content Services, or CSS) allows local unprivileged users to read arbitrary files via a .. (dot dot) attack.
CVE-2001-0021(PUBLISHED):MailMan Webmail 3.0.25 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters in the alternate_template parameter.
CVE-2001-0022(PUBLISHED):simplestguest.cgi CGI program by Leif Wright allows remote attackers to execute arbitrary commands via shell metacharacters in the guestbook parameter.
CVE-2001-0023(PUBLISHED):everythingform.cgi CGI program by Leif Wright allows remote attackers to execute arbitrary commands via shell metacharacters in the config parameter.
CVE-2001-0024(PUBLISHED):simplestmail.cgi CGI program by Leif Wright allows remote attackers to execute arbitrary commands via shell metacharacters in the MyEmail parameter.
CVE-2001-0025(PUBLISHED):ad.cgi CGI program by Leif Wright allows remote attackers to execute arbitrary commands via shell metacharacters in the file parameter.
CVE-2001-0026(PUBLISHED):rp-pppoe PPPoE client allows remote attackers to cause a denial of service via the Clamp MSS option and a TCP packet with a zero-length TCP option.
CVE-2001-0027(PUBLISHED):mod_sqlpw module in ProFTPD does not reset a cached password when a user uses the "user" command to change accounts, which allows authenticated attackers to gain privileges of other users.
CVE-2001-0028(PUBLISHED):Buffer overflow in the HTML parsing code in oops WWW proxy server 1.5.2 and earlier allows remote attackers to execute arbitrary commands via a large number of " (quotation) characters.
CVE-2001-0029(PUBLISHED):Buffer overflow in oops WWW proxy server 1.4.6 (and possibly other versions) allows remote attackers to execute arbitrary commands via a long host or domain name that is obtained from a reverse DNS lookup.
CVE-2001-0030(PUBLISHED):FoolProof 3.9 allows local users to bypass program execution restrictions by downloading the restricted executables from another source and renaming them.
CVE-2001-0031(PUBLISHED):BroadVision One-To-One Enterprise allows remote attackers to determine the physical path of server files by requesting a .JSP file name that does not exist.
CVE-2001-0032(PUBLISHED):Format string vulnerability in ssldump possibly allows remote attackers to cause a denial of service and possibly gain root privileges via malicious format string specifiers in a URL.
CVE-2001-0033(PUBLISHED):KTH Kerberos IV allows local users to change the configuration of a Kerberos server running at an elevated privilege by specifying an alternate directory using with the KRBCONFDIR environmental variable, which allows the user to gain additional privileges.
CVE-2001-0034(PUBLISHED):KTH Kerberos IV allows local users to specify an alternate proxy using the krb4_proxy variable, which allows the user to generate false proxy responses and possibly gain privileges.
CVE-2001-0035(PUBLISHED):Buffer overflow in the kdc_reply_cipher function in KTH Kerberos IV allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long authentication request.
CVE-2001-0036(PUBLISHED):KTH Kerberos IV allows local users to overwrite arbitrary files via a symlink attack on a ticket file.
CVE-2001-0037(PUBLISHED):Directory traversal vulnerability in HomeSeer before 1.4.29 allows remote attackers to read arbitrary files via a URL containing .. (dot dot) specifiers.
CVE-2001-0038(PUBLISHED):Offline Explorer 1.4 before Service Release 2 allows remote attackers to read arbitrary files by specifying the drive letter (e.g. C:) in the requested URL.
CVE-2001-0039(PUBLISHED):IPSwitch IMail 6.0.5 allows remote attackers to cause a denial of service using the SMTP AUTH command by sending a base64-encoded user password whose length is between 80 and 136 bytes.
CVE-2001-0040(PUBLISHED):APC UPS daemon, apcupsd, saves its process ID in a world-writable file, which allows local users to kill an arbitrary process by specifying the target process ID in the apcupsd.pid file.
CVE-2001-0041(PUBLISHED):Memory leak in Cisco Catalyst 4000, 5000, and 6000 series switches allows remote attackers to cause a denial of service via a series of failed telnet authentication attempts.
CVE-2001-0042(PUBLISHED):PHP 3.x (PHP3) on Apache 1.3.6 allows remote attackers to read arbitrary files via a modified .. (dot dot) attack containing "%5c" (encoded backslash) sequences.
CVE-2001-0043(PUBLISHED):phpGroupWare before 0.9.7 allows remote attackers to execute arbitrary PHP commands by specifying a malicious include file in the phpgw_info parameter of the phpgw.inc.php program.
CVE-2001-0044(PUBLISHED):Multiple buffer overflows in Lexmark MarkVision printer driver programs allows local users to gain privileges via long arguments to the cat_network, cat_paraller, and cat_serial commands.
CVE-2001-0045(PUBLISHED):The default permissions for the RAS Administration key in Windows NT 4.0 allows local users to execute arbitrary commands by changing the value to point to a malicious DLL, aka one of the "Registry Permissions" vulnerabilities.
CVE-2001-0046(PUBLISHED):The default permissions for the SNMP Parameters registry key in Windows NT 4.0 allows remote attackers to read and possibly modify the SNMP community strings to obtain sensitive information or modify network configuration, aka one of the "Registry Permissions" vulnerabilities.
CVE-2001-0047(PUBLISHED):The default permissions for the MTS Package Administration registry key in Windows NT 4.0 allows local users to install or modify arbitrary Microsoft Transaction Server (MTS) packages and gain privileges, aka one of the "Registry Permissions" vulnerabilities.
CVE-2001-0048(PUBLISHED):The "Configure Your Server" tool in Microsoft 2000 domain controllers installs a blank password for the Directory Service Restore Mode, which allows attackers with physical access to the controller to install malicious programs, aka the "Directory Service Restore Mode Password" vulnerability.
CVE-2001-0049(PUBLISHED):WatchGuard SOHO FireWall 2.2.1 and earlier allows remote attackers to cause a denial of service via a large number of GET requests.
CVE-2001-0050(PUBLISHED):Buffer overflow in BitchX IRC client allows remote attackers to cause a denial of service and possibly execute arbitrary commands via an IP address that resolves to a long DNS hostname or domain name.
CVE-2001-0051(PUBLISHED):IBM DB2 Universal Database version 6.1 creates an account with a default user name and password, which allows remote attackers to gain access to the database.
CVE-2001-0052(PUBLISHED):IBM DB2 Universal Database version 6.1 allows users to cause a denial of service via a malformed query.
CVE-2001-0053(PUBLISHED):One-byte buffer overflow in replydirname function in BSD-based ftpd allows remote attackers to gain root privileges.
CVE-2001-0054(PUBLISHED):Directory traversal vulnerability in FTP Serv-U before 2.5i allows remote attackers to escape the FTP root and read arbitrary files by appending a string such as "/..%20." to a CD command, a variant of a .. (dot dot) attack.
CVE-2001-0055(PUBLISHED):CBOS 2.4.1 and earlier in Cisco 600 routers allows remote attackers to cause a denial of service via a slow stream of TCP SYN packets.
CVE-2001-0056(PUBLISHED):The Cisco Web Management interface in routers running CBOS 2.4.1 and earlier does not log invalid logins, which allows remote attackers to guess passwords without detection.
CVE-2001-0057(PUBLISHED):Cisco 600 routers running CBOS 2.4.1 and earlier allow remote attackers to cause a denial of service via a large ICMP echo (ping) packet.
CVE-2001-0058(PUBLISHED):The Web interface to Cisco 600 routers running CBOS 2.4.1 and earlier allow remote attackers to cause a denial of service via a URL that does not end in a space character.
CVE-2001-0059(PUBLISHED):patchadd in Solaris allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0060(PUBLISHED):Format string vulnerability in stunnel 3.8 and earlier allows attackers to execute arbitrary commands via a malformed ident username.
CVE-2001-0061(PUBLISHED):procfs in FreeBSD and possibly other operating systems does not properly restrict access to per-process mem and ctl files, which allows local users to gain root privileges by forking a child process and executing a privileged process from the child, while the parent retains access to the child's address space.
CVE-2001-0062(PUBLISHED):procfs in FreeBSD and possibly other operating systems allows local users to cause a denial of service by calling mmap on the process' own mem file, which causes the kernel to hang.
CVE-2001-0063(PUBLISHED):procfs in FreeBSD and possibly other operating systems allows local users to bypass access control restrictions for a jail environment and gain additional privileges.
CVE-2001-0064(PUBLISHED):Webconfig, IMAP, and other services in MDaemon 3.5.0 and earlier allows remote attackers to cause a denial of service via a long URL terminated by a "\r\n" string.
CVE-2001-0065(PUBLISHED):Buffer overflow in bftpd 1.0.13 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long SITE CHOWN command.
CVE-2001-0066(PUBLISHED):Secure Locate (slocate) allows local users to corrupt memory via a malformed database file that specifies an offset value that accesses memory outside of the intended buffer.
CVE-2001-0067(PUBLISHED):The installation of J-Pilot creates the .jpilot directory with the user's umask, which could allow local attackers to read other users' PalmOS backup information if their umasks are not securely set.
CVE-2001-0068(PUBLISHED):Mac OS Runtime for Java (MRJ) 2.2.3 allows remote attackers to use malicious applets to read files outside of the CODEBASE context via the ARCHIVE applet parameter.
CVE-2001-0069(PUBLISHED):dialog before 0.9a-20000118-3bis in Debian GNU/Linux allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0070(PUBLISHED):Buffer overflow in 1st Up Mail Server 4.1 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long MAIL FROM command.
CVE-2001-0071(PUBLISHED):gpg (aka GnuPG) 1.0.4 and other versions does not properly verify detached signatures, which allows attackers to modify the contents of a file without detection.
CVE-2001-0072(PUBLISHED):gpg (aka GnuPG) 1.0.4 and other versions imports both public and private keys from public key servers without notifying the user about the private keys, which could allow an attacker to break the web of trust.
CVE-2001-0073(PUBLISHED):Buffer overflow in the find_default_type function in libsecure in NSA Security-enhanced Linux, which may allow attackers to modify critical data in memory.
CVE-2001-0074(PUBLISHED):Directory traversal vulnerability in print.cgi in Technote allows remote attackers to read arbitrary files via a .. (dot dot) attack in the board parameter.
CVE-2001-0075(PUBLISHED):Directory traversal vulnerability in main.cgi in Technote allows remote attackers to read arbitrary files via a .. (dot dot) attack in the filename parameter.
CVE-2001-0076(PUBLISHED):register.cgi in Ikonboard 2.1.7b and earlier allows remote attackers to execute arbitrary commands via the SEND_MAIL parameter, which overwrites an internal program variable that references a program to be executed.
CVE-2001-0077(PUBLISHED):The clustmon service in Sun Cluster 2.x does not require authentication, which allows remote attackers to obtain sensitive information such as system logs and cluster configurations.
CVE-2001-0078(PUBLISHED):in.mond in Sun Cluster 2.x allows local users to read arbitrary files via a symlink attack on the status file of a host running HA-NFS.
CVE-2001-0079(PUBLISHED):Support Tools Manager (STM) A.22.00 for HP-UX allows local users to overwrite arbitrary files via a symlink attack on the tool_stat.txt log file.
CVE-2001-0080(PUBLISHED):Cisco Catalyst 6000, 5000, or 4000 switches allow remote attackers to cause a denial of service by connecting to the SSH service with a non-SSH client, which generates a protocol mismatch error.
CVE-2001-0081(PUBLISHED):swinit in nCipher does not properly disable the Operator Card Set recovery feature even when explicitly disabled by the user, which could allow attackers to gain access to application keys.
CVE-2001-0082(PUBLISHED):Check Point VPN-1/FireWall-1 4.1 SP2 with Fastmode enabled allows remote attackers to bypass access restrictions via malformed, fragmented packets.
CVE-2001-0083(PUBLISHED):Windows Media Unicast Service in Windows Media Services 4.0 and 4.1 does not properly shut down some types of connections, producing a memory leak that allows remote attackers to cause a denial of service via a series of severed connections, aka the "Severed Windows Media Server Connection" vulnerability.
CVE-2001-0084(PUBLISHED):GTK+ library allows local users to specify arbitrary modules via the GTK_MODULES environmental variable, which could allow local users to gain privileges if GTK+ is used by a setuid/setgid program.
CVE-2001-0085(PUBLISHED):Buffer overflow in Kermit communications software in HP-UX 11.0 and earlier allows local users to cause a denial of service and possibly execute arbitrary commands.
CVE-2001-0086(PUBLISHED):CGI Script Center Subscribe Me LITE 2.0 and earlier allows remote attackers to delete arbitrary mailing list users without authentication by directly calling subscribe.pl with the target address as a parameter.
CVE-2001-0087(PUBLISHED):itetris/xitetris 1.6.2 and earlier trusts the PATH environmental variable to find and execute the gunzip program, which allows local users to gain root privileges by changing their PATH so that it points to a malicious gunzip program.
CVE-2001-0088(PUBLISHED):common.inc.php in phpWebLog 0.4.2 does not properly initialize the $CONF array, which inadvertently sets the password to a single character, allowing remote attackers to easily guess the SiteKey and gain administrative privileges to phpWebLog.
CVE-2001-0089(PUBLISHED):Internet Explorer 5.0 through 5.5 allows remote attackers to read arbitrary files from the client via the INPUT TYPE element in an HTML form, aka the "File Upload via Form" vulnerability.
CVE-2001-0090(PUBLISHED):The Print Templates feature in Internet Explorer 5.5 executes arbitrary custom print templates without prompting the user, which could allow an attacker to execute arbitrary ActiveX controls, aka the "Browser Print Template" vulnerability.
CVE-2001-0091(PUBLISHED):The ActiveX control for invoking a scriptlet in Internet Explorer 5.0 through 5.5 renders arbitrary file types instead of HTML, which allows an attacker to read arbitrary files, aka a variant of the "Scriptlet Rendering" vulnerability.
CVE-2001-0092(PUBLISHED):A function in Internet Explorer 5.0 through 5.5 does not properly verify the domain of a frame within a browser window, which allows a remote attacker to read client files, aka a new variant of the "Frame Domain Verification" vulnerability.
CVE-2001-0093(PUBLISHED):Vulnerability in telnetd in FreeBSD 1.5 allows local users to gain root privileges by modifying critical environmental variables that affect the behavior of telnetd.
CVE-2001-0094(PUBLISHED):Buffer overflow in kdc_reply_cipher of libkrb (Kerberos 4 authentication library) in NetBSD 1.5 and FreeBSD 4.2 and earlier, as used in Kerberised applications such as telnetd and login, allows local users to gain root privileges.
CVE-2001-0095(PUBLISHED):catman in Solaris 2.7 and 2.8 allows local users to overwrite arbitrary files via a symlink attack on the sman_PID temporary file.
CVE-2001-0096(PUBLISHED):FrontPage Server Extensions (FPSE) in IIS 4.0 and 5.0 allows remote attackers to cause a denial of service via a malformed form, aka the "Malformed Web Form Submission" vulnerability.
CVE-2001-0097(PUBLISHED):The Web interface for Infinite Interchange 3.6.1 allows remote attackers to cause a denial of service (application crash) via a large POST request.
CVE-2001-0098(PUBLISHED):Buffer overflow in Bea WebLogic Server before 5.1.0 allows remote attackers to execute arbitrary commands via a long URL that begins with a ".."  string.
CVE-2001-0099(PUBLISHED):bsguest.cgi guestbook script allows remote attackers to execute arbitrary commands via shell metacharacters in the email address.
CVE-2001-0100(PUBLISHED):bslist.cgi mailing list script allows remote attackers to execute arbitrary commands via shell metacharacters in the email address.
CVE-2001-0101(PUBLISHED):Vulnerability in fetchmail 5.5.0-2 and earlier in the AUTHENTICATE GSSAPI command.
CVE-2001-0102(PUBLISHED):"Multiple Users" Control Panel in Mac OS 9 allows Normal users to gain Owner privileges by removing the Users & Groups Data File, which effectively removes the Owner password and allows the Normal user to log in as the Owner account without a password.
CVE-2001-0103(PUBLISHED):CoffeeCup Direct and Free FTP clients uses weak encryption to store passwords in the FTPServers.ini file, which could allow attackers to easily decrypt the passwords.
CVE-2001-0104(PUBLISHED):MDaemon Pro 3.5.1 and earlier allows local users to bypass the "lock server" security setting by pressing the Cancel button at the password prompt, then pressing the enter key.
CVE-2001-0105(PUBLISHED):Vulnerability in top in HP-UX 11.04 and earlier allows local users to overwrite files owned by the "sys" group.
CVE-2001-0106(PUBLISHED):Vulnerability in inetd server in HP-UX 11.04 and earlier allows attackers to cause a denial of service when the "swait" state is used by a server.
CVE-2001-0107(PUBLISHED):Veritas Backup agent on Linux allows remote attackers to cause a denial of service by establishing a connection without sending any data, which causes the process to hang.
CVE-2001-0108(PUBLISHED):PHP Apache module 4.0.4 and earlier allows remote attackers to bypass .htaccess access restrictions via a malformed HTTP request on an unrestricted page that causes PHP to use those access controls on the next page that is requested.
CVE-2001-0109(PUBLISHED):rctab in SuSE 7.0 and earlier allows local users to create or overwrite arbitrary files via a symlink attack on the rctmp temporary file.
CVE-2001-0110(PUBLISHED):Buffer overflow in jaZip Zip/Jaz drive manager allows local users to gain root privileges via a long DISPLAY environmental variable.
CVE-2001-0111(PUBLISHED):Format string vulnerability in splitvt before 1.6.5 allows local users to execute arbitrary commands via the -rcfile command line argument.
CVE-2001-0112(PUBLISHED):Multiple buffer overflows in splitvt before 1.6.5 allow local users to execute arbitrary commands.
CVE-2001-0113(PUBLISHED):statsconfig.pl in OmniHTTPd 2.07 allows remote attackers to execute arbitrary commands via the mostbrowsers parameter, whose value is used as part of a generated Perl script.
CVE-2001-0114(PUBLISHED):statsconfig.pl in OmniHTTPd 2.07 allows remote attackers to overwrite arbitrary files via the cgidir parameter.
CVE-2001-0115(PUBLISHED):Buffer overflow in arp command in Solaris 7 and earlier allows local users to execute arbitrary commands via a long -f parameter.
CVE-2001-0116(PUBLISHED):gpm 1.19.3 allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0117(PUBLISHED):sdiff 2.7 in the diffutils package allows local users to overwrite files via a symlink attack.
CVE-2001-0118(PUBLISHED):rdist 6.1.5 allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0119(PUBLISHED):getty_ps 2.0.7j allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0120(PUBLISHED):useradd program in shadow-utils program may allow local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0121(PUBLISHED):ImageCast Control Center 4.1.0 allows remote attackers to cause a denial of service (resource exhaustion or system crash) via a long string to port 12002.
CVE-2001-0122(PUBLISHED):Kernel leak in AfpaCache module of the Fast Response Cache Accelerator (FRCA) component of IBM HTTP Server 1.3.x and Websphere 3.52 allows remote attackers to cause a denial of service via a series of malformed HTTP requests that generate a "bad request" error.
CVE-2001-0123(PUBLISHED):Directory traversal vulnerability in eXtropia bbs_forum.cgi 1.0 allows remote attackers to read arbitrary files via a .. (dot dot) attack on the file parameter.
CVE-2001-0124(PUBLISHED):Buffer overflow in exrecover in Solaris 2.6 and earlier possibly allows local users to gain privileges via a long command line argument.
CVE-2001-0125(PUBLISHED):exmh 2.2 and earlier allows local users to overwrite arbitrary files via a symlink attack on the exmhErrorMsg temporary file.
CVE-2001-0126(PUBLISHED):Oracle XSQL servlet 1.0.3.0 and earlier allows remote attackers to execute arbitrary Java code by redirecting the XSQL server to another source via the xml-stylesheet parameter in the xslt stylesheet.
CVE-2001-0127(PUBLISHED):Buffer overflow in Olivier Debon Flash plugin (not the Macromedia plugin) allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long DefineSound tag.
CVE-2001-0128(PUBLISHED):Zope before 2.2.4 does not properly compute local roles, which could allow users to bypass specified access restrictions and gain privileges.
CVE-2001-0129(PUBLISHED):Buffer overflow in Tinyproxy HTTP proxy 1.3.3 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long connect request.
CVE-2001-0130(PUBLISHED):Buffer overflow in HTML parser of the Lotus R5 Domino Server before 5.06, and Domino Client before 5.05, allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a malformed font size specifier.
CVE-2001-0131(PUBLISHED):htpasswd and htdigest in Apache 2.0a9, 1.3.14, and others allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0132(PUBLISHED):Interscan VirusWall 3.6.x and earlier follows symbolic links when uninstalling the product, which allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0133(PUBLISHED):The web administration interface for Interscan VirusWall 3.6.x and earlier does not use encryption, which could allow remote attackers to obtain the administrator password to sniff the administrator password via the setpasswd.cgi program or other HTTP GET requests that contain base64 encoded usernames and passwords.
CVE-2001-0134(PUBLISHED):Buffer overflow in cpqlogin.htm in web-enabled agents for various Compaq management software products such as Insight Manager and Management Agents allows remote attackers to execute arbitrary commands via a long user name.
CVE-2001-0135(PUBLISHED):The default installation of Ultraboard 2000 2.11 creates the Skins, Database, and Backups directories with world-writeable permissions, which could allow local users to modify sensitive information or possibly insert and execute CGI programs.
CVE-2001-0136(PUBLISHED):Memory leak in ProFTPd 1.2.0rc2 allows remote attackers to cause a denial of service via a series of USER commands, and possibly SIZE commands if the server has been improperly installed.
CVE-2001-0137(PUBLISHED):Windows Media Player 7 allows remote attackers to execute malicious Java applets in Internet Explorer clients by enclosing the applet in a skin file named skin.wmz, then referencing that skin in the codebase parameter to an applet tag, aka the Windows Media Player Skins File Download" vulnerability.
CVE-2001-0138(PUBLISHED):privatepw program in wu-ftpd before 2.6.1-6 allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0139(PUBLISHED):inn 2.2.3 allows local users to overwrite arbitrary files via a symlink attack in some configurations.
CVE-2001-0140(PUBLISHED):arpwatch 2.1a4 allows local users to overwrite arbitrary files via a symlink attack in some configurations.
CVE-2001-0141(PUBLISHED):mgetty 1.1.22 allows local users to overwrite arbitrary files via a symlink attack in some configurations.
CVE-2001-0142(PUBLISHED):squid 2.3 and earlier allows local users to overwrite arbitrary files via a symlink attack in some configurations.
CVE-2001-0143(PUBLISHED):vpop3d program in linuxconf 1.23r and earlier allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0144(PUBLISHED):CORE SDI SSH1 CRC-32 compensation attack detector allows remote attackers to execute arbitrary commands on an SSH server or client via an integer overflow.
CVE-2001-0145(PUBLISHED):Buffer overflow in VCard handler in Outlook 2000 and 98, and Outlook Express 5.x, allows an attacker to execute arbitrary commands via a malformed vCard birthday field.
CVE-2001-0146(PUBLISHED):IIS 5.0 and Microsoft Exchange 2000 allow remote attackers to cause a denial of service (memory allocation error) by repeatedly sending a series of specially formatted URL's.
CVE-2001-0147(PUBLISHED):Buffer overflow in Windows 2000 event viewer snap-in allows attackers to execute arbitrary commands via a malformed field that is improperly handled during the detailed view of event records.
CVE-2001-0148(PUBLISHED):The WMP ActiveX Control in Windows Media Player 7 allows remote attackers to execute commands in Internet Explorer via javascript URLs, a variant of the "Frame Domain Verification" vulnerability.
CVE-2001-0149(PUBLISHED):Windows Scripting Host in Internet Explorer 5.5 and earlier allows remote attackers to read arbitrary files via the GetObject Javascript function and the htmlfile ActiveX object.
CVE-2001-0150(PUBLISHED):Internet Explorer 5.5 and earlier executes Telnet sessions using command line arguments that are specified by the web site, which could allow remote attackers to execute arbitrary commands if the IE client is using the Telnet client provided in Services for Unix (SFU) 2.0, which creates session transcripts.
CVE-2001-0151(PUBLISHED):IIS 5.0 allows remote attackers to cause a denial of service via a series of malformed WebDAV requests.
CVE-2001-0152(PUBLISHED):The password protection option for the Compressed Folders feature in Plus! for Windows 98 and Windows Me writes password information to a file, which allows local users to recover the passwords and read the compressed folders.
CVE-2001-0153(PUBLISHED):Buffer overflow in VB-TSQL debugger object (vbsdicli.exe) in Visual Studio 6.0 Enterprise Edition allows remote attackers to execute arbitrary commands.
CVE-2001-0154(PUBLISHED):HTML e-mail feature in Internet Explorer 5.5 and earlier allows attackers to execute attachments by setting an unusual MIME type for the attachment, which Internet Explorer does not process correctly.
CVE-2001-0155(PUBLISHED):Format string vulnerability in VShell SSH gateway 1.0.1 and earlier allows remote attackers to execute arbitrary commands via a user name that contains format string specifiers.
CVE-2001-0156(PUBLISHED):VShell SSH gateway 1.0.1 and earlier has a default port forwarding rule of 0.0.0.0/0.0.0.0, which could allow local users to conduct arbitrary port forwarding to other systems.
CVE-2001-0157(PUBLISHED):Debugging utility in the backdoor mode of Palm OS 3.5.2 and earlier allows attackers with physical access to a Palm device to bypass access restrictions and obtain passwords, even if the system lockout mechanism is enabled.
CVE-2001-0160(PUBLISHED):Lucent/ORiNOCO WaveLAN cards generate predictable Initialization Vector (IV) values for the Wireless Encryption Protocol (WEP) which allows remote attackers to quickly compile information that will let them decrypt messages.
CVE-2001-0161(PUBLISHED):Cisco 340-series Aironet access point using firmware 11.01 does not use 6 of the 24 available IV bits for WEP encryption, which makes it easier for remote attackers to mount brute force attacks.
CVE-2001-0162(PUBLISHED):WinCE 3.0.9348 generates predictable TCP Initial Sequence Numbers (ISNs), which allows remote attackers to spoof or hijack TCP connections.
CVE-2001-0163(PUBLISHED):Cisco AP340 base station produces predictable TCP Initial Sequence Numbers (ISNs), which allows remote attackers to spoof or hijack TCP connections.
CVE-2001-0164(PUBLISHED):Buffer overflow in Netscape Directory Server 4.12 and earlier allows remote attackers to cause a denial of service or execute arbitrary commands via a malformed recipient field.
CVE-2001-0165(PUBLISHED):Buffer overflow in ximp40 shared library in Solaris 7 and Solaris 8 allows local users to gain privileges via a long "arg0" (process name) argument.
CVE-2001-0166(PUBLISHED):Macromedia Shockwave Flash plugin version 8 and earlier allows remote attackers to cause a denial of service via malformed tag length specifiers in a SWF file.
CVE-2001-0167(PUBLISHED):Buffer overflow in AT&T WinVNC (Virtual Network Computing) client 3.3.3r7 and earlier allows remote attackers to execute arbitrary commands via a long rfbConnFailed packet with a long reason string.
CVE-2001-0168(PUBLISHED):Buffer overflow in AT&T WinVNC (Virtual Network Computing) server 3.3.3r7 and earlier allows remote attackers to execute arbitrary commands via a long HTTP GET request when the DebugLevel registry key is greater than 0.
CVE-2001-0169(PUBLISHED):When using the LD_PRELOAD environmental variable in SUID or SGID applications, glibc does not verify that preloaded libraries in /etc/ld.so.cache are also SUID/SGID, which could allow a local user to overwrite arbitrary files by loading a library from /lib or /usr/lib.
CVE-2001-0170(PUBLISHED):glibc 2.1.9x and earlier does not properly clear the RESOLV_HOST_CONF, HOSTALIASES, or RES_OPTIONS environmental variables when executing setuid/setgid programs, which could allow local users to read arbitrary files.
CVE-2001-0171(PUBLISHED):Buffer overflow in SlimServe HTTPd 1.0 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long GET request.
CVE-2001-0172(PUBLISHED):Buffer overflow in ReiserFS 3.5.28 in SuSE Linux allows local users to cause a denial of service and possibly execute arbitrary commands by via a long directory name.
CVE-2001-0173(PUBLISHED):Buffer overflow in qDecoder library 5.08 and earlier, as used in CrazyWWWBoard, CrazySearch, and other CGI programs, allows remote attackers to execute arbitrary commands via a long MIME Content-Type header.
CVE-2001-0174(PUBLISHED):Buffer overflow in Trend Micro Virus Buster 2001 8.00 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a large "To" address.
CVE-2001-0175(PUBLISHED):The caching module in Netscape Fasttrack Server 4.1 allows remote attackers to cause a denial of service (resource exhaustion) by requesting a large number of non-existent URLs.
CVE-2001-0176(PUBLISHED):The setuid doroot program in Voyant Sonata 3.x executes arbitrary command line arguments, which allows local users to gain root privileges.
CVE-2001-0177(PUBLISHED):WebMaster ConferenceRoom 1.8.1 allows remote attackers to cause a denial of service via a buddy relationship between the IRC server and a server clone.
CVE-2001-0178(PUBLISHED):kdesu program in KDE2 (KDE before 2.2.0-6) does not properly verify the owner of a UNIX socket that is used to send a password, which allows local users to steal passwords and gain privileges.
CVE-2001-0179(PUBLISHED):Allaire JRun 3.0 allows remote attackers to list contents of the WEB-INF directory, and the web.xml file in the WEB-INF directory, via a malformed URL that contains a "."
CVE-2001-0180(PUBLISHED):Lars Ellingsen guestserver.cgi allows remote attackers to execute arbitrary commands via shell metacharacters in the "email" parameter.
CVE-2001-0181(PUBLISHED):Format string vulnerability in the error logging code of DHCP server and client in Caldera Linux allows remote attackers to execute arbitrary commands.
CVE-2001-0182(PUBLISHED):FireWall-1 4.1 with a limited-IP license allows remote attackers to cause a denial of service by sending a large number of spoofed IP packets with various source addresses to the inside interface, which floods the console with warning messages and consumes CPU resources.
CVE-2001-0183(PUBLISHED):ipfw and ip6fw in FreeBSD 4.2 and earlier allows remote attackers to bypass access restrictions by setting the ECE flag in a TCP packet, which makes the packet appear to be part of an established connection.
CVE-2001-0184(PUBLISHED):eEye Iris 1.01 beta allows remote attackers to cause a denial of service via a malformed packet, which causes Iris to crash when a user views the packet.
CVE-2001-0185(PUBLISHED):Netopia R9100 router version 4.6 allows authenticated users to cause a denial of service by using the router's telnet program to connect to the router's IP address, which causes a crash.
CVE-2001-0186(PUBLISHED):Directory traversal vulnerability in Free Java Web Server 1.0 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2001-0187(PUBLISHED):Format string vulnerability in wu-ftp 2.6.1 and earlier, when running with debug mode enabled, allows remote attackers to execute arbitrary commands via a malformed argument that is recorded in a PASV port assignment.
CVE-2001-0188(PUBLISHED):GoodTech FTP server *******.1.0 and earlier allows remote attackers to cause a denial of service via a flood of connections to the server, which causes it to crash.
CVE-2001-0189(PUBLISHED):Directory traversal vulnerability in LocalWEB2000 HTTP server allows remote attackers to read arbitrary commands via a .. (dot dot) attack in an HTTP GET request.
CVE-2001-0190(PUBLISHED):Buffer overflow in /usr/bin/cu in Solaris 2.8 and earlier, and possibly other operating systems, allows local users to gain privileges by executing cu with a long program name (arg0).
CVE-2001-0191(PUBLISHED):gnuserv before 3.12, as shipped with XEmacs, does not properly check the specified length of an X Windows MIT-MAGIC-COOKIE cookie, which allows remote attackers to execute arbitrary commands via a buffer overflow, or brute force authentication by using a short cookie length.
CVE-2001-0192(PUBLISHED):Buffer overflows in CTRLServer in XMail allows attackers to execute arbitrary commands via the cfgfileget or domaindel functions.
CVE-2001-0193(PUBLISHED):Format string vulnerability in man in some Linux distributions allows local users to gain privileges via a malformed -l parameter.
CVE-2001-0194(PUBLISHED):Buffer overflow in httpGets function in CUPS 1.1.5 allows remote attackers to execute arbitrary commands via a long input line.
CVE-2001-0195(PUBLISHED):sash before 3.4-4 in Debian GNU/Linux does not properly clone /etc/shadow, which makes it world-readable and could allow local users to gain privileges via password cracking.
CVE-2001-0196(PUBLISHED):inetd ident server in FreeBSD 4.x and earlier does not properly set group permissions, which allows remote attackers to read the first 16 bytes of files that are accessible by the wheel group.
CVE-2001-0197(PUBLISHED):Format string vulnerability in print_client in icecast 1.3.8beta2 and earlier allows remote attackers to execute arbitrary commands.
CVE-2001-0198(PUBLISHED):Buffer overflow in QuickTime Player plugin 4.1.2 (Japanese) allows remote attackers to execute arbitrary commands via a long HREF parameter in an EMBED tag.
CVE-2001-0199(PUBLISHED):Directory traversal vulnerability in SEDUM HTTP Server 2.0 allows remote attackers to read arbitrary files via a .. (dot dot) attack in the HTTP GET request.
CVE-2001-0200(PUBLISHED):HSWeb 2.0 HTTP server allows remote attackers to obtain the physical path of the server via a request to the /cgi/ directory, which will list the path if directory browsing is enabled.
CVE-2001-0201(PUBLISHED):The Postaci frontend for PostgreSQL does not properly filter characters such as semicolons, which could allow remote attackers to execute arbitrary SQL queries via the deletecontact.php program.
CVE-2001-0202(PUBLISHED):Picserver web server allows remote attackers to read arbitrary files via a .. (dot dot) attack in an HTTP GET request.
CVE-2001-0203(PUBLISHED):Watchguard Firebox II firewall allows users with read-only access to gain read-write access, and administrative privileges, by accessing a file that contains hashed passphrases, and using the hashes during authentication.
CVE-2001-0204(PUBLISHED):Watchguard Firebox II allows remote attackers to cause a denial of service by establishing multiple connections and sending malformed PPTP packets.
CVE-2001-0205(PUBLISHED):Directory traversal vulnerability in AOLserver 3.2 and earlier allows remote attackers to read arbitrary files by inserting "..." into the requested pathname, a modified .. (dot dot) attack.
CVE-2001-0206(PUBLISHED):Directory traversal vulnerability in Soft Lite ServerWorx 3.00 allows remote attackers to read arbitrary files by inserting a .. (dot dot) or ... into the requested pathname of an HTTP GET request.
CVE-2001-0207(PUBLISHED):Buffer overflow in bing allows remote attackers to execute arbitrary commands via a long hostname, which is copied to a small buffer after a reverse DNS lookup using the gethostbyaddr function.
CVE-2001-0208(PUBLISHED):MicroFocus Cobol 4.1, with the AppTrack feature enabled, installs the mfaslmf directory and the nolicense file with insecure permissions, which allows local users to gain privileges by modifying files.
CVE-2001-0209(PUBLISHED):Buffer overflow in Shoutcast Distributed Network Audio Server (DNAS) 1.7.1 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long description.
CVE-2001-0210(PUBLISHED):Directory traversal vulnerability in commerce.cgi CGI program allows remote attackers to read arbitrary files via a .. (dot dot) attack in the page parameter.
CVE-2001-0211(PUBLISHED):Directory traversal vulnerability in WebSPIRS 3.1 allows remote attackers to read arbitrary files via a .. (dot dot) attack on the sp.nextform parameter.
CVE-2001-0212(PUBLISHED):Directory traversal vulnerability in HIS Auktion 1.62 allows remote attackers to read arbitrary files via a .. (dot dot) in the menue parameter, and possibly execute commands via shell metacharacters.
CVE-2001-0213(PUBLISHED):Buffer overflow in pi program in PlanetIntra 2.5 allows remote attackers to execute arbitrary commands.
CVE-2001-0214(PUBLISHED):Way-board CGI program allows remote attackers to read arbitrary files by specifying the filename in the db parameter and terminating the filename with a null byte.
CVE-2001-0215(PUBLISHED):ROADS search.pl program allows remote attackers to read arbitrary files by specifying the file name in the form parameter and terminating the filename with a null byte.
CVE-2001-0216(PUBLISHED):PALS Library System pals-cgi program allows remote attackers to execute arbitrary commands via shell metacharacters in the documentName parameter.
CVE-2001-0217(PUBLISHED):Directory traversal vulnerability in PALS Library System pals-cgi program allows remote attackers to read arbitrary files via a .. (dot dot) in the documentName parameter.
CVE-2001-0218(PUBLISHED):Format string vulnerability in mars_nwe 0.99.pl19 allows remote attackers to execute arbitrary commands.
CVE-2001-0219(PUBLISHED):Vulnerability in Support Tools Manager (xstm,cstm,stm) in HP-UX 11.11 and earlier allows local users to cause a denial of service.
CVE-2001-0220(PUBLISHED):Buffer overflow in ja-elvis and ko-helvis ports of elvis allow local users to gain root privileges.
CVE-2001-0221(PUBLISHED):Buffer overflow in ja-xklock 2.7.1 and earlier allows local users to gain root privileges.
CVE-2001-0222(PUBLISHED):webmin 0.84 and earlier allows local users to overwrite and create arbitrary files via a symlink attack.
CVE-2001-0223(PUBLISHED):Buffer overflow in wwwwais allows remote attackers to execute arbitrary commands via a long QUERY_STRING (HTTP GET request).
CVE-2001-0224(PUBLISHED):Muscat Empower CGI program allows remote attackers to obtain the absolute pathname of the server via an invalid request in the DB parameter.
CVE-2001-0225(PUBLISHED):fortran math component in Infobot 0.44.5.3 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2001-0226(PUBLISHED):Directory traversal vulnerability in BiblioWeb web server 2.0 allows remote attackers to read arbitrary files via a .. (dot dot) or ... attack in an HTTP GET request.
CVE-2001-0227(PUBLISHED):Buffer overflow in BiblioWeb web server 2.0 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long HTTP GET request.
CVE-2001-0228(PUBLISHED):Directory traversal vulnerability in GoAhead web server 2.1 and earlier allows remote attackers to read arbitrary files via a .. attack in an HTTP GET request.
CVE-2001-0229(PUBLISHED):Chili!Soft ASP for Linux before 3.6 does not properly set group privileges when running in inherited mode, which could allow attackers to gain privileges via malicious scripts.
CVE-2001-0230(PUBLISHED):Buffer overflow in dc20ctrl before 0.4_1 in FreeBSD, and possibly other operating systems, allows local users to gain privileges.
CVE-2001-0231(PUBLISHED):Directory traversal vulnerability in newsdesk.cgi in News Desk 1.2 allows remote attackers to read arbitrary files via a .. in the "t" parameter.
CVE-2001-0232(PUBLISHED):newsdesk.cgi in News Desk 1.2 allows remote attackers to read arbitrary files via shell metacharacters.
CVE-2001-0233(PUBLISHED):Buffer overflow in micq client 0.4.6 and earlier allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long Description field.
CVE-2001-0234(PUBLISHED):NewsDaemon before 0.21b allows remote attackers to execute arbitrary SQL queries and gain privileges via a malformed user_username parameter.
CVE-2001-0235(PUBLISHED):Vulnerability in crontab allows local users to read crontab files of other users by replacing the temporary file that is being edited while crontab is running.
CVE-2001-0236(PUBLISHED):Buffer overflow in Solaris snmpXdmid SNMP to DMI mapper daemon allows remote attackers to execute arbitrary commands via a long "indication" event.
CVE-2001-0237(PUBLISHED):Memory leak in Microsoft 2000 domain controller allows remote attackers to cause a denial of service by repeatedly connecting to the Kerberos service and then disconnecting without sending any data.
CVE-2001-0238(PUBLISHED):Microsoft Data Access Component Internet Publishing Provider 8.103.2519.0 and earlier allows remote attackers to bypass Security Zone restrictions via WebDAV requests.
CVE-2001-0239(PUBLISHED):Microsoft Internet Security and Acceleration (ISA) Server 2000 Web Proxy allows remote attackers to cause a denial of service via a long web request with a specific type.
CVE-2001-0240(PUBLISHED):Microsoft Word before Word 2002 allows attackers to automatically execute macros without warning the user via a Rich Text Format (RTF) document that links to a template with the embedded macro.
CVE-2001-0241(PUBLISHED):Buffer overflow in Internet Printing ISAPI extension in Windows 2000 allows remote attackers to gain root privileges via a long print request that is passed to the extension through IIS 5.0.
CVE-2001-0242(PUBLISHED):Buffer overflows in Microsoft Windows Media Player 7 and earlier allow remote attackers to execute arbitrary commands via (1) a long version tag in an .ASX file, or (2) a long banner tag, a variant of the ".ASX Buffer Overrun" vulnerability as discussed in MS:MS00-090.
CVE-2001-0243(PUBLISHED):Windows Media Player 7 and earlier stores Internet shortcuts in a user's Temporary Files folder with a fixed filename instead of in the Internet Explorer cache, which causes the HTML in those shortcuts to run in the Local Computer Zone instead of the Internet Zone, which allows remote attackers to read certain files.
CVE-2001-0244(PUBLISHED):Buffer overflow in Microsoft Index Server 2.0 allows remote attackers to execute arbitrary commands via a long search parameter.
CVE-2001-0245(PUBLISHED):Microsoft Index Server 2.0 in Windows NT 4.0, and Indexing Service in Windows 2000, allows remote attackers to read server-side include files via a malformed search request, aka a new variant of the "Malformed Hit-Highlighting" vulnerability.
CVE-2001-0246(PUBLISHED):Internet Explorer 5.5 and earlier does not properly verify the domain of a frame within a browser window, which allows remote web site operators to read certain files on the client by sending information from a local frame to a frame in a different domain, aka a variant of the "Frame Domain Verification" vulnerability.
CVE-2001-0247(PUBLISHED):Buffer overflows in BSD-based FTP servers allows remote attackers to execute arbitrary commands via a long pattern string containing a {} sequence, as seen in (1) g_opendir, (2) g_lstat, (3) g_stat, and (4) the glob0 buffer as used in the glob functions glob2 and glob3.
CVE-2001-0248(PUBLISHED):Buffer overflow in FTP server in HPUX 11 allows remote attackers to execute arbitrary commands by creating a long pathname and calling the STAT command, which uses glob to generate long strings.
CVE-2001-0249(PUBLISHED):Heap overflow in FTP daemon in Solaris 8 allows remote attackers to execute arbitrary commands by creating a long pathname and calling the LIST command, which uses glob to generate long strings.
CVE-2001-0250(PUBLISHED):The Web Publishing feature in Netscape Enterprise Server 4.x and earlier allows remote attackers to list arbitrary directories under the web server root via the INDEX command.
CVE-2001-0251(PUBLISHED):The Web Publishing feature in Netscape Enterprise Server 3.x allows remote attackers to cause a denial of service via the REVLOG command.
CVE-2001-0252(PUBLISHED):iPlanet (formerly Netscape) Enterprise Server 4.1 allows remote attackers to cause a denial of service via a long HTTP GET request that contains many "/../" (dot dot) sequences.
CVE-2001-0253(PUBLISHED):Directory traversal vulnerability in hsx.cgi program in iWeb Hyperseek 2000 allows remote attackers to read arbitrary files and directories via a .. (dot dot) attack in the show parameter.
CVE-2001-0254(PUBLISHED):FaSTream FTP++ Server 2.0 allows remote attackers to obtain the real pathname of the server via the "pwd" command.
CVE-2001-0255(PUBLISHED):FaSTream FTP++ Server 2.0 allows remote attackers to list arbitrary directories by using the "ls" command and including the drive letter name (e.g. C:) in the requested pathname.
CVE-2001-0256(PUBLISHED):FaSTream FTP++ Server 2.0 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long username.
CVE-2001-0257(PUBLISHED):Buffer overflow in Easycom/Safecom Print Server Web service, version 404.590 and earlier, allows remote attackers to execute arbitrary commands via (1) a long URL or (2) a long HTTP header field such as "Host:".
CVE-2001-0258(PUBLISHED):The Easycom/Safecom Print Server (firmware 404.590) PrintGuide server allows remote attackers to cause a denial of service via a large number of connections that send null characters.
CVE-2001-0259(PUBLISHED):ssh-keygen in ssh 1.2.27 - 1.2.30 with Secure-RPC can allow local attackers to recover a SUN-DES-1 magic phrase generated by another user, which the attacker can use to decrypt that user's private key file.
CVE-2001-0260(PUBLISHED):Buffer overflow in Lotus Domino Mail Server 5.0.5 and earlier allows a remote attacker to crash the server or execute arbitrary code via a long "RCPT TO" command.
CVE-2001-0261(PUBLISHED):Microsoft Windows 2000 Encrypted File System does not properly destroy backups of files that are encrypted, which allows a local attacker to recover the text of encrypted files.
CVE-2001-0262(PUBLISHED):Buffer overflow in Netscape SmartDownload 1.3 allows remote attackers (malicious web pages) to execute arbitrary commands via a long URL.
CVE-2001-0263(PUBLISHED):Gene6 G6 FTP Server 2.0 (aka BPFTP Server 2.10) allows attackers to read file attributes outside of the web root via the (1) SIZE and (2) MDTM commands when the "show relative paths" option is not enabled.
CVE-2001-0264(PUBLISHED):Gene6 G6 FTP Server 2.0 (aka BPFTP Server 2.10) allows remote attackers to obtain NETBIOS credentials by requesting information on a file that is in a network share, which causes the server to send the credentials to the host that owns the share, and allows the attacker to sniff the connection.
CVE-2001-0265(PUBLISHED):ASCII Armor parser in Windows PGP 7.0.3 and earlier allows attackers to create files in arbitrary locations via a malformed ASCII armored file.
CVE-2001-0266(PUBLISHED):Vulnerability in Software Distributor SD-UX in HP-UX 11.0 and earlier allows local users to gain privileges.
CVE-2001-0267(PUBLISHED):NM debug in HP MPE/iX 6.5 and earlier does not properly handle breakpoints, which allows local users to gain privileges.
CVE-2001-0268(PUBLISHED):The i386_set_ldt system call in NetBSD 1.5 and earlier, and OpenBSD 2.8 and earlier, when the USER_LDT kernel option is enabled, does not validate a call gate target, which allows local users to gain root privileges by creating a segment call gate in the Local Descriptor Table (LDT) with a target that specifies an arbitrary kernel address.
CVE-2001-0269(PUBLISHED):pam_ldap authentication module in Solaris 8 allows remote attackers to bypass authentication via a NULL password.
CVE-2001-0270(PUBLISHED):Marconi ASX-1000 ASX switches allow remote attackers to cause a denial of service in the telnet and web management interfaces via a malformed packet with the SYN-FIN and More Fragments attributes set.
CVE-2001-0271(PUBLISHED):mailnews.cgi 1.3 and earlier allows remote attackers to execute arbitrary commands via a user name that contains shell metacharacters.
CVE-2001-0272(PUBLISHED):Directory traversal vulnerability in sendtemp.pl in W3.org Anaya Web development server allows remote attackers to read arbitrary files via a .. (dot dot) attack in the templ parameter.
CVE-2001-0273(PUBLISHED):pgp4pine Pine/PGP interface version 1.75-6 does not properly check to see if a public key has expired when obtaining the keys via Gnu Privacy Guard (GnuPG), which causes the message to be sent in cleartext.
CVE-2001-0274(PUBLISHED):kicq IRC client 1.0.0, and possibly later versions, allows remote attackers to execute arbitrary commands via shell metacharacters in a URL.
CVE-2001-0275(PUBLISHED):Moby Netsuite Web Server 1.02 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long HTTP request.
CVE-2001-0276(PUBLISHED):ext.dll in BadBlue 1.02.07 Personal Edition web server allows remote attackers to determine the physical path of the server by directly calling ext.dll without any arguments, which produces an error message that contains the path.
CVE-2001-0277(PUBLISHED):Buffer overflow in ext.dll in BadBlue 1.02.07 Personal Edition allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long HTTP GET request.
CVE-2001-0278(PUBLISHED):Vulnerability in linkeditor in HP MPE/iX 6.5 and earlier allows local users to gain privileges.
CVE-2001-0279(PUBLISHED):Buffer overflow in sudo earlier than 1.6.3p6 allows local users to gain root privileges.
CVE-2001-0280(PUBLISHED):Buffer overflow in MERCUR SMTP server 3.30 allows remote attackers to execute arbitrary commands via a long EXPN command.
CVE-2001-0281(PUBLISHED):Format string vulnerability in DbgPrint function, used in debug messages for some Windows NT drivers (possibly when called through DebugMessage), may allow local users to gain privileges.
CVE-2001-0282(PUBLISHED):SEDUM 2.1 HTTP server allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long HTTP request.
CVE-2001-0283(PUBLISHED):Directory traversal vulnerability in SunFTP build 9 allows remote attackers to read arbitrary files via .. (dot dot) characters in various commands, including (1) GET, (2) MKDIR, (3) RMDIR, (4) RENAME, or (5) PUT.
CVE-2001-0284(PUBLISHED):Buffer overflow in IPSEC authentication mechanism for OpenBSD 2.8 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a malformed Authentication header (AH) IPv4 option.
CVE-2001-0285(PUBLISHED):Buffer overflow in A1 HTTP server 1.0a allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long HTTP request.
CVE-2001-0286(PUBLISHED):Directory traversal vulnerability in A1 HTTP server 1.0a allows remote attackers to read arbitrary files via a .. (dot dot) in an HTTP GET request.
CVE-2001-0287(PUBLISHED):VERITAS Cluster Server (VCS) 1.3.0 on Solaris allows local users to cause a denial of service (system panic) via the -L option to the lltstat command.
CVE-2001-0288(PUBLISHED):Cisco switches and routers running IOS 12.1 and earlier produce predictable TCP Initial Sequence Numbers (ISNs), which allows remote attackers to spoof or hijack TCP connections.
CVE-2001-0289(PUBLISHED):Joe text editor 2.8 searches the current working directory (CWD) for the .joerc configuration file, which could allow local users to gain privileges of other users by placing a Trojan Horse .joerc file into a directory, then waiting for users to execute joe from that directory.
CVE-2001-0290(PUBLISHED):Vulnerability in Mailman 2.0.1 and earlier allows list administrators to obtain user passwords.
CVE-2001-0291(PUBLISHED):Buffer overflow in post-query sample CGI program allows remote attackers to execute arbitrary commands via an HTTP POST request that contains at least 10001 parameters.
CVE-2001-0292(PUBLISHED):PHP-Nuke 4.4.1a allows remote attackers to modify a user's email address and obtain the password by guessing the user id (UID) and calling user.php with the saveuser operator.
CVE-2001-0293(PUBLISHED):Directory traversal vulnerability in FtpXQ FTP server 2.0.93 allows remote attackers to read arbitrary files via a .. (dot dot) in the GET command.
CVE-2001-0294(PUBLISHED):Directory traversal vulnerability in TYPSoft FTP Server 0.85 allows remote attackers to read arbitrary files via (1) a .. (dot dot) in a GET command, or (2) a ... in a CWD command.
CVE-2001-0295(PUBLISHED):Directory traversal vulnerability in War FTP 1.67.04 allows remote attackers to list directory contents and possibly read files via a "dir *./../.." command.
CVE-2001-0296(PUBLISHED):Buffer overflow in WFTPD Pro 3.00 allows remote attackers to execute arbitrary commands via a long CWD command.
CVE-2001-0297(PUBLISHED):Directory traversal vulnerability in Simple Server HTTPd 1.0 (originally Free Java Server) allows remote attackers to read arbitrary files via a .. (dot dot) in the URL.
CVE-2001-0298(PUBLISHED):Buffer overflow in WebReflex 1.55 HTTPd allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long HTTP GET request.
CVE-2001-0299(PUBLISHED):Buffer overflow in Voyager web administration server for Nokia IP440 allows local users to cause a denial of service, and possibly execute arbitrary commands, via a long URL.
CVE-2001-0300(PUBLISHED):oidldapd ******* in Oracle 8.1.7 records log files in a directory (ldaplog) that has world-writable permissions, which may allow local users to delete logs and/or overwrite other files via a symlink attack.
CVE-2001-0301(PUBLISHED):Buffer overflow in Analog before 4.16 allows remote attackers to execute arbitrary commands by using the ALIAS command to construct large strings.
CVE-2001-0302(PUBLISHED):Buffer overflow in tstisapi.dll in Pi3Web 1.0.1 web server allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long URL.
CVE-2001-0303(PUBLISHED):tstisapi.dll in Pi3Web 1.0.1 web server allows remote attackers to determine the physical path of the server via a URL that requests a non-existent file.
CVE-2001-0304(PUBLISHED):Directory traversal vulnerability in Caucho Resin 1.2.2 allows remote attackers to read arbitrary files via a "\.." (dot dot) in a URL request.
CVE-2001-0305(PUBLISHED):Directory traversal vulnerability in store.cgi in Thinking Arts ES.One package allows remote attackers to read arbitrary files via a .. (dot dot) in the StartID parameter.
CVE-2001-0306(PUBLISHED):Directory traversal vulnerability in ITAfrica WEBactive HTTP Server 1.00 allows remote attackers to read arbitrary files via a .. (dot dot) in a URL.
CVE-2001-0307(PUBLISHED):Bajie HTTP JServer 0.78, and other versions before 0.80, allows remote attackers to execute arbitrary commands via shell metacharacters in an HTTP request for a CGI program that does not exist.
CVE-2001-0308(PUBLISHED):UploadServlet in Bajie HTTP JServer 0.78, and possibly other versions before 0.80, allows remote attackers to execute arbitrary commands by calling the servlet to upload a program, then using a ... (modified ..) to access the file that was created for the program.
CVE-2001-0309(PUBLISHED):inetd in Red Hat 6.2 does not properly close sockets for internal services such as chargen, daytime, echo, etc., which allows remote attackers to cause a denial of service via a series of connections to the internal services.
CVE-2001-0310(PUBLISHED):sort in FreeBSD 4.1.1 and earlier, and possibly other operating systems, uses predictable temporary file names and does not properly handle when the temporary file already exists, which causes sort to crash and possibly impacts security-sensitive scripts.
CVE-2001-0311(PUBLISHED):Vulnerability in OmniBackII A.03.50 in HP 11.x and earlier allows attackers to gain unauthorized access to an OmniBack client.
CVE-2001-0312(PUBLISHED):IBM WebSphere plugin for Netscape Enterprise server allows remote attackers to read source code for JSP files via an HTTP request that contains a host header that references a host that is not in WebSphere's host aliases list, which will bypass WebSphere processing.
CVE-2001-0313(PUBLISHED):Borderware Firewall Server 6.1.2 allows remote attackers to cause a denial of service via a ping to the broadcast address of the public network on which the server is placed, which causes the server to continuously send pings (echo requests) to the network.
CVE-2001-0314(PUBLISHED):Buffer overflow in www.tol module in America Online (AOL) 5.0 may allow remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long URL in a link.
CVE-2001-0315(PUBLISHED):The locking feature in mIRC 5.7 allows local users to bypass the password mechanism by modifying the LockOptions registry key.
CVE-2001-0316(PUBLISHED):Linux kernel 2.4 and 2.2 allows local users to read kernel memory and possibly gain privileges via a negative argument to the sysctl call.
CVE-2001-0317(PUBLISHED):Race condition in ptrace in Linux kernel 2.4 and 2.2 allows local users to gain privileges by using ptrace to track and modify a running setuid process.
CVE-2001-0318(PUBLISHED):Format string vulnerability in ProFTPD 1.2.0rc2 may allow attackers to execute arbitrary commands by shutting down the FTP server while using a malformed working directory (cwd).
CVE-2001-0319(PUBLISHED):orderdspc.d2w macro in IBM Net.Commerce 3.x allows remote attackers to execute arbitrary SQL queries by inserting them into the order_rn option of the report capability.
CVE-2001-0320(PUBLISHED):bb_smilies.php and bbcode_ref.php in PHP-Nuke 4.4 allows remote attackers to read arbitrary files and gain PHP administrator privileges by inserting a null character and .. (dot dot) sequences into a malformed username argument.
CVE-2001-0321(PUBLISHED):opendir.php script in PHP-Nuke allows remote attackers to read arbitrary files by specifying the filename as an argument to the requesturl parameter.
CVE-2001-0322(PUBLISHED):MSHTML.DLL HTML parser in Internet Explorer 4.0, and other versions, allows remote attackers to cause a denial of service (application crash) via a script that creates and deletes an object that is associated with the browser window object.
CVE-2001-0323(PUBLISHED):The ICMP path MTU (PMTU) discovery feature in various UNIX systems allows remote attackers to cause a denial of service by spoofing "ICMP Fragmentation needed but Don't Fragment (DF) set" packets between two target hosts, which could cause one host to lower its MTU when transmitting to the other host.
CVE-2001-0324(PUBLISHED):Windows 98 and Windows 2000 Java clients allow remote attackers to cause a denial of service via a Java applet that opens a large number of UDP sockets, which prevents the host from establishing any additional UDP connections, and possibly causes a crash.
CVE-2001-0325(PUBLISHED):Buffer overflow in QNX RTP 5.60 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a large number of arguments to the stat command.
CVE-2001-0326(PUBLISHED):Oracle Java Virtual Machine (JVM ) for Oracle 8.1.7 and Oracle Application Server 9iAS Release *******.1 allows remote attackers to read arbitrary files via the .jsp and .sqljsp file extensions when the server is configured to use the <<ALL FILES>> FilePermission.
CVE-2001-0327(PUBLISHED):iPlanet Web Server Enterprise Edition 4.1 and earlier allows remote attackers to retrieve sensitive data from memory allocation pools, or cause a denial of service, via a URL-encoded Host: header in the HTTP request, which reveals memory in the Location: header that is returned by the server.
CVE-2001-0328(PUBLISHED):TCP implementations that use random increments for initial sequence numbers (ISN) can allow remote attackers to perform session hijacking or disruption by injecting a flood of packets with a range of ISN values, one of which may match the expected ISN.
CVE-2001-0329(PUBLISHED):Bugzilla 2.10 allows remote attackers to execute arbitrary commands via shell metacharacters in a username that is then processed by (1) the Bugzilla_login cookie in post_bug.cgi, or (2) the who parameter in process_bug.cgi.
CVE-2001-0330(PUBLISHED):Bugzilla 2.10 allows remote attackers to access sensitive information, including the database username and password, via an HTTP request for the globals.pl file, which is normally returned by the web server without being executed.
CVE-2001-0331(PUBLISHED):Buffer overflow in Embedded Support Partner (ESP) daemon (rpc.espd) in IRIX 6.5.8 and earlier allows remote attackers to execute arbitrary commands.
CVE-2001-0332(PUBLISHED):Internet Explorer 5.5 and earlier does not properly verify the domain of a frame within a browser window, which allows remote web site operators to read certain files on the client by sending information from a local frame to a frame in a different domain using MSScriptControl.ScriptControl and GetObject, aka a variant of the "Frame Domain Verification" vulnerability.
CVE-2001-0333(PUBLISHED):Directory traversal vulnerability in IIS 5.0 and earlier allows remote attackers to execute arbitrary commands by encoding .. (dot dot) and "\" characters twice.
CVE-2001-0334(PUBLISHED):FTP service in IIS 5.0 and earlier allows remote attackers to cause a denial of service via a wildcard sequence that generates a long string when it is expanded.
CVE-2001-0335(PUBLISHED):FTP service in IIS 5.0 and earlier allows remote attackers to enumerate Guest accounts in trusted domains by preceding the username with a special sequence of characters.
CVE-2001-0336(PUBLISHED):The Microsoft MS00-060 patch for IIS 5.0 and earlier introduces an error which allows attackers to cause a denial of service via a malformed request.
CVE-2001-0337(PUBLISHED):The Microsoft MS01-014 and MS01-016 patches for IIS 5.0 and earlier introduce a memory leak which allows attackers to cause a denial of service via a series of requests.
CVE-2001-0338(PUBLISHED):Internet Explorer 5.5 and earlier does not properly validate digital certificates when Certificate Revocation List (CRL) checking is enabled, which could allow remote attackers to spoof trusted web sites, aka the "Server certificate validation vulnerability."
CVE-2001-0339(PUBLISHED):Internet Explorer 5.5 and earlier allows remote attackers to display a URL in the address bar that is different than the URL that is actually being displayed, which could be used in web site spoofing attacks, aka the "Web page spoofing vulnerability."
CVE-2001-0340(PUBLISHED):An interaction between the Outlook Web Access (OWA) service in Microsoft Exchange 2000 Server and Internet Explorer allows attackers to execute malicious script code against a user's mailbox via a message attachment that contains HTML code, which is executed automatically.
CVE-2001-0341(PUBLISHED):Buffer overflow in Microsoft Visual Studio RAD Support sub-component of FrontPage Server Extensions allows remote attackers to execute arbitrary commands via a long registration request (URL) to fp30reg.dll.
CVE-2001-0344(PUBLISHED):An SQL query method in Microsoft SQL Server 2000 Gold and 7.0 using Mixed Mode allows local database users to gain privileges by reusing a cached connection of the sa administrator account.
CVE-2001-0345(PUBLISHED):Microsoft Windows 2000 telnet service allows attackers to prevent idle Telnet sessions from timing out, causing a denial of service by creating a large number of idle sessions.
CVE-2001-0346(PUBLISHED):Handle leak in Microsoft Windows 2000 telnet service allows attackers to cause a denial of service by starting a large number of sessions and terminating them.
CVE-2001-0347(PUBLISHED):Information disclosure vulnerability in Microsoft Windows 2000 telnet service allows remote attackers to determine the existence of user accounts such as Guest, or log in to the server without specifying the domain name, via a malformed userid.
CVE-2001-0348(PUBLISHED):Microsoft Windows 2000 telnet service allows attackers to cause a denial of service (crash) via a long logon command that contains a backspace.
CVE-2001-0349(PUBLISHED):Microsoft Windows 2000 telnet service creates named pipes with predictable names and does not properly verify them, which allows local users to execute arbitrary commands by creating a named pipe with the predictable name and associating a malicious program with it, the first of two variants of this vulnerability.
CVE-2001-0350(PUBLISHED):Microsoft Windows 2000 telnet service creates named pipes with predictable names and does not properly verify them, which allows local users to execute arbitrary commands by creating a named pipe with the predictable name and associating a malicious program with it, the second of two variants of this vulnerability.
CVE-2001-0351(PUBLISHED):Microsoft Windows 2000 telnet service allows a local user to make a certain system call that allows the user to terminate a Telnet session and cause a denial of service.
CVE-2001-0352(PUBLISHED):SNMP agents in 3Com AirConnect AP-4111 and Symbol 41X1 Access Point allow remote attackers to obtain the WEP encryption key by reading it from a MIB when the value should be write-only, via (1) dot11WEPDefaultKeyValue in the dot11WEPDefaultKeysTable of the IEEE 802.11b MIB, or (2) ap128bWepKeyValue in the ap128bWEPKeyTable in the Symbol MIB.
CVE-2001-0353(PUBLISHED):Buffer overflow in the line printer daemon (in.lpd) for Solaris 8 and earlier allows local and remote attackers to gain root privileges via a "transfer job" routine.
CVE-2001-0354(PUBLISHED):TheNet CheckBO 1.56 allows remote attackers to cause a denial of service via a flood of characters to the TCP ports which it is listening on.
CVE-2001-0355(PUBLISHED):Novell Groupwise 5.5 (sp1 and sp2) allows a remote user to access arbitrary files via an implementation error in Groupwise system policies.
CVE-2001-0357(PUBLISHED):FormMail.pl in FormMail 1.6 and earlier allows a remote attacker to send anonymous email (spam) by modifying the recipient and message parameters.
CVE-2001-0358(PUBLISHED):Buffer overflows in Sierra Half-Life build 1573 and earlier allow remote attackers to execute arbitrary code via (1) a long map command, (2) a long exec command, or (3) long input in a configuration file.
CVE-2001-0359(PUBLISHED):Format string vulnerability in Sierra Half-Life build 1573 and earlier allows a remote attacker to execute arbitrary code via the map command.
CVE-2001-0360(PUBLISHED):Directory traversal vulnerability in help.cgi in Ikonboard 2.1.7b and earlier allows a remote attacker to read arbitrary files via a .. (dot dot) attack in the helpon parameter.
CVE-2001-0361(PUBLISHED):Implementations of SSH version 1.5, including (1) OpenSSH up to version 2.3.0, (2) AppGate, and (3) ssh-1 up to version 1.2.31, in certain configurations, allow a remote attacker to decrypt and/or alter traffic via a "Bleichenbacher attack" on PKCS#1 version 1.5.
CVE-2001-0364(PUBLISHED):SSH Communications Security sshd 2.4 for Windows allows remote attackers to create a denial of service via a large number of simultaneous connections.
CVE-2001-0365(PUBLISHED):Eudora before 5.1 allows a remote attacker to execute arbitrary code, when the 'Use Microsoft Viewer' and 'allow executables in HTML content' options are enabled, via an HTML email message containing Javascript, with ActiveX controls and malicious code within IMG tags.
CVE-2001-0366(PUBLISHED):saposcol in SAP R/3 Web Application Server Demo before 1.5 trusts the PATH environmental variable to find and execute the expand program, which allows local users to obtain root access by modifying the PATH to point to a Trojan horse expand program.
CVE-2001-0367(PUBLISHED):Mirabilis ICQ WebFront Plug-in ICQ2000b Build 3278 allows a remote attacker to create a denial of service via HTTP URL requests containing a large number of % characters.
CVE-2001-0368(PUBLISHED):Directory traversal vulnerability in BearShare 2.2.2 and earlier allows a remote attacker to read certain files via a URL containing a series of . characters, a variation of the .. (dot dot) attack.
CVE-2001-0369(PUBLISHED):Buffer overflow in lpsched on DGUX version R4.20MU06 and MU02 allows a local attacker to obtain root access via a long command line argument (non-existent printer name).
CVE-2001-0370(PUBLISHED):fcheck prior to 2.57.59 calls the file signature checking program insecurely, which can allow a local user to run arbitrary commands via a file name that contains shell metacharacters.
CVE-2001-0371(PUBLISHED):Race condition in the UFS and EXT2FS file systems in FreeBSD 4.2 and earlier, and possibly other operating systems, makes deleted data available to user processes before it is zeroed out, which allows a local user to access otherwise restricted information.
CVE-2001-0372(PUBLISHED):Akopia Interchange 4.5.3 through 4.6.3 installs demo stores with a default group account :backup with no password, which allows a remote attacker to gain administrative access via the demo stores (1) barry, (2) basic, or (3) construct.
CVE-2001-0373(PUBLISHED):The default configuration of the Dr. Watson program in Windows NT and Windows 2000 generates user.dmp crash dump files with world-readable permissions, which could allow a local user to gain access to sensitive information.
CVE-2001-0374(PUBLISHED):The HTTP server in Compaq web-enabled management software for (1) Foundation Agents, (2) Survey, (3) Power Manager, (4) Availability Agents, (5) Intelligent Cluster Administrator, and (6) Insight Manager can be used as a generic proxy server, which allows remote attackers to bypass access restrictions via the management port, 2301.
CVE-2001-0375(PUBLISHED):Cisco PIX Firewall 515 and 520 with 5.1.4 OS running aaa authentication to a TACACS+ server allows remote attackers to cause a denial of service via a large number of authentication requests.
CVE-2001-0376(PUBLISHED):SonicWALL Tele2 and SOHO firewalls with ******* firmware using IPSEC with IKE pre-shared keys do not allow for the use of full 128 byte IKE pre-shared keys, which is the intended design of the IKE pre-shared key, and only support 48 byte keys.  This allows a remote attacker to brute force attack the pre-shared keys with significantly less resources than if the full 128 byte IKE pre-shared keys were used.
CVE-2001-0377(PUBLISHED):Infradig Inframail prior to 3.98a allows a remote attacker to create a denial of service via a malformed POST request which includes a space followed by a large string.
CVE-2001-0378(PUBLISHED):readline prior to 4.1, in OpenBSD 2.8 and earlier, creates history files with insecure permissions, which allows a local attacker to recover potentially sensitive information via readline history files.
CVE-2001-0379(PUBLISHED):Vulnerability in the newgrp program included with HP9000 servers running HP-UX 11.11 allows a local attacker to obtain higher access rights.
CVE-2001-0380(PUBLISHED):Crosscom/Olicom XLT-F running XL 80 IM Version 5.5 Build Level 2 allows a remote attacker SNMP read and write access via a default, undocumented community string 'ILMI'.
CVE-2001-0381(PUBLISHED):The OpenPGP PGP standard allows an attacker to determine the private signature key via a cryptanalytic attack in which the attacker alters the encrypted private key file and captures a single message signed with the signature key.
CVE-2001-0382(PUBLISHED):Computer Associates CCC\Harvest 5.0 for Windows NT/2000 uses weak encryption for passwords, which allows a remote attacker to gain privileges on the application.
CVE-2001-0383(PUBLISHED):banners.php in PHP-Nuke 4.4 and earlier allows remote attackers to modify banner ad URLs by directly calling the Change operation, which does not require authentication.
CVE-2001-0384(PUBLISHED):ppd in Reliant Sinix allows local users to corrupt arbitrary files via a symlink attack in the /tmp/ppd.trace file.
CVE-2001-0385(PUBLISHED):GoAhead webserver 2.1 allows remote attackers to cause a denial of service via an HTTP request to the /aux directory.
CVE-2001-0386(PUBLISHED):AnalogX SimpleServer:WWW 1.08 allows remote attackers to cause a denial of service via an HTTP request to the /aux directory.
CVE-2001-0387(PUBLISHED):Format string vulnerability in hfaxd in HylaFAX before 4.1.b2_2 allows local users to gain privileges via the -q command line argument.
CVE-2001-0388(PUBLISHED):time server daemon timed allows remote attackers to cause a denial of service via malformed packets.
CVE-2001-0389(PUBLISHED):IBM Websphere/NetCommerce3 3.1.2 allows remote attackers to determine the real path of the server by directly calling the macro.d2w macro with a NOEXISTINGHTMLBLOCK argument.
CVE-2001-0390(PUBLISHED):IBM Websphere/NetCommerce3 3.1.2 allows remote attackers to cause a denial of service by directly calling the macro.d2w macro with a long string of %0a characters.
CVE-2001-0391(PUBLISHED):Xitami 2.5d4 and earlier allows remote attackers to crash the server via an HTTP request to the /aux directory.
CVE-2001-0392(PUBLISHED):Navision Financials Server 2.60 and earlier allows remote attackers to cause a denial of service by sending a null character and a long string to the server port (2407), which causes the server to crash.
CVE-2001-0393(PUBLISHED):Navision Financials Server 2.0 allows remote attackers to cause a denial of service via a series of connections to the server without providing a username/password combination, which consumes the license limits.
CVE-2001-0394(PUBLISHED):Remote manager service in Website Pro 3.0.37 allows remote attackers to cause a denial of service via a series of malformed HTTP requests to the /dyn directory.
CVE-2001-0395(PUBLISHED):Lightwave ConsoleServer 3200 does not disconnect users after unsuccessful login attempts, which could allow remote attackers to conduct brute force password guessing.
CVE-2001-0396(PUBLISHED):The pre-login mode in the System Administrator interface of Lightwave ConsoleServer 3200 allows remote attackers to obtain sensitive information such as system status, configuration, and users.
CVE-2001-0397(PUBLISHED):Buffer overflow in Silent Runner Collector (SRC) 1.6.1 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long SMTP HELO command.
CVE-2001-0398(PUBLISHED):The BAT! mail client allows remote attackers to bypass user warnings of an executable attachment and execute arbitrary commands via an attachment whose file name contains many spaces, which also causes the BAT!  to misrepresent the attachment's type with a different icon.
CVE-2001-0399(PUBLISHED):Caucho Resin 1.3b1 and earlier allows remote attackers to read source code for Javabean files by inserting a .jsp before the WEB-INF specifier in an HTTP request.
CVE-2001-0400(PUBLISHED):nph-maillist.pl allows remote attackers to execute arbitrary commands via shell metacharacters ("`") in the email address.
CVE-2001-0401(PUBLISHED):Buffer overflow in tip in Solaris 8 and earlier allows local users to execute arbitrary commands via a long HOME environmental variable.
CVE-2001-0402(PUBLISHED):IPFilter 3.4.16 and earlier does not include sufficient session information in its cache, which allows remote attackers to bypass access restrictions by sending fragmented packets to a restricted port after sending unfragmented packets to an unrestricted port.
CVE-2001-0403(PUBLISHED):/opt/JSparm/bin/perfmon program in Solaris allows local users to create arbitrary files as root via the Logging File option in the GUI.
CVE-2001-0404(PUBLISHED):Directory traversal vulnerability in JavaServer Web Dev Kit (JSWDK) 1.0.1 allows remote attackers to read arbitrary files via a .. (dot dot) in an HTTP request to the WEB-INF directory.
CVE-2001-0405(PUBLISHED):ip_conntrack_ftp in the IPTables firewall for Linux 2.4 allows remote attackers to bypass access restrictions for an FTP server via a PORT command that lists an arbitrary IP address and port number, which is added to the RELATED table and allowed by the firewall.
CVE-2001-0406(PUBLISHED):Samba before 2.2.0 allows local attackers to overwrite arbitrary files via a symlink attack using (1) a printer queue query, (2) the more command in smbclient, or (3) the mput command in smbclient.
CVE-2001-0407(PUBLISHED):Directory traversal vulnerability in MySQL before 3.23.36 allows local users to modify arbitrary files and gain privileges by creating a database whose name starts with .. (dot dot).
CVE-2001-0408(PUBLISHED):vim (aka gvim) processes VIM control codes that are embedded in a file, which could allow attackers to execute arbitrary commands when another user opens a file containing malicious VIM control codes.
CVE-2001-0409(PUBLISHED):vim (aka gvim) allows local users to modify files being edited by other users via a symlink attack on the backup and swap files, when the victim is editing the file in a world writable directory.
CVE-2001-0410(PUBLISHED):Buffer overflow in Trend Micro Virus Buster 2001 8.02 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long "From" header.
CVE-2001-0411(PUBLISHED):Reliant Unix 5.44 and earlier allows remote attackers to cause a denial of service via an ICMP port unreachable packet, which causes Reliant to drop all connections to the source address of the packet.
CVE-2001-0412(PUBLISHED):Cisco Content Services (CSS) switch products 11800 and earlier, aka Arrowpoint, allows local users to gain privileges by entering debug mode.
CVE-2001-0413(PUBLISHED):BinTec X4000 Access router, and possibly other versions, allows remote attackers to cause a denial of service via a SYN port scan, which causes the router to hang.
CVE-2001-0414(PUBLISHED):Buffer overflow in ntpd ntp daemon 4.0.99k and earlier (aka xntpd and xntp3) allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long readvar argument.
CVE-2001-0415(PUBLISHED):REDIPlus program, REDI.exe, stores passwords and user names in cleartext in the StartLog.txt log file, which allows local users to gain access to other accounts.
CVE-2001-0416(PUBLISHED):sgml-tools (aka sgmltools) before 1.0.9-15 creates temporary files with insecure permissions, which allows other users to read files that are being processed by sgml-tools.
CVE-2001-0417(PUBLISHED):Kerberos 4 (aka krb4) allows local users to overwrite arbitrary files via a symlink attack on new ticket files.
CVE-2001-0418(PUBLISHED):content.pl script in NCM Content Management System allows remote attackers to read arbitrary contents of the content database by inserting SQL characters into the id parameter.
CVE-2001-0419(PUBLISHED):Buffer overflow in shared library ndwfn4.so for iPlanet Web Server (iWS) 4.1, when used as a web listener for Oracle application server *******, allows remote attackers to execute arbitrary commands via a long HTTP request that is passed to the application server, such as /jsp/.
CVE-2001-0420(PUBLISHED):Directory traversal vulnerability in talkback.cgi program allows remote attackers to read arbitrary files via a .. (dot dot) in the article parameter.
CVE-2001-0421(PUBLISHED):FTP server in Solaris 8 and earlier allows local and remote attackers to cause a core dump in the root directory, possibly with world-readable permissions, by providing a valid username with an invalid password followed by a CWD ~ command, which could release sensitive information such as shadowed passwords, or fill the disk partition.
CVE-2001-0422(PUBLISHED):Buffer overflow in Xsun in Solaris 8 and earlier allows local users to execute arbitrary commands via a long HOME environmental variable.
CVE-2001-0423(PUBLISHED):Buffer overflow in ipcs in Solaris 7 x86 allows local users to execute arbitrary code via a long TZ (timezone) environmental variable, a different vulnerability than CAN-2002-0093.
CVE-2001-0424(PUBLISHED):BubbleMon 1.31 does not properly drop group privileges before executing programs, which allows local users to execute arbitrary commands with the kmem group id.
CVE-2001-0425(PUBLISHED):AdLibrary.pm in AdCycle 0.78b allows remote attackers to gain privileges to AdCycle via a malformed Agent: header in the HTTP request, which is inserted into a resulting SQL query that is used to verify login information.
CVE-2001-0426(PUBLISHED):Buffer overflow in dtsession on Solaris, and possibly other operating systems, allows local users to gain privileges via a long LANG environmental variable.
CVE-2001-0427(PUBLISHED):Cisco VPN 3000 series concentrators before 2.5.2(F) allow remote attackers to cause a denial of service via a flood of invalid login requests to (1) the SSL service, or (2) the telnet service, which do not properly disconnect the user after several failed login attempts.
CVE-2001-0428(PUBLISHED):Cisco VPN 3000 series concentrators before 2.5.2(F) allow remote attackers to cause a denial of service via an IP packet with an invalid IP option.
CVE-2001-0429(PUBLISHED):Cisco Catalyst 5000 series switches 6.1(2) and earlier will forward an 802.1x frame on a Spanning Tree Protocol (STP) blocked port, which causes a network storm and a denial of service.
CVE-2001-0430(PUBLISHED):Vulnerability in exuberant-ctags before 3.2.4-0.1 insecurely creates temporary files.
CVE-2001-0431(PUBLISHED):Vulnerability in iPlanet Web Server Enterprise Edition 4.x.
CVE-2001-0432(PUBLISHED):Buffer overflows in various CGI programs in the remote administration service for Trend Micro Interscan VirusWall 3.01 allow remote attackers to execute arbitrary commands.
CVE-2001-0433(PUBLISHED):Buffer overflow in Savant 3.0 web server allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long Host HTTP header.
CVE-2001-0434(PUBLISHED):The LogDataListToFile ActiveX function used in (1) Knowledge Center and (2) Back web components of Compaq Presario computers allows remote attackers to modify arbitrary files and cause a denial of service.
CVE-2001-0435(PUBLISHED):The split key mechanism used by PGP 7.0 allows a key share holder to obtain access to the entire key by setting the "Cache passphrase while logged on" option and capturing the passphrases of other share holders as they authenticate.
CVE-2001-0436(PUBLISHED):dcboard.cgi in DCForum 2000 1.0 allows remote attackers to execute arbitrary commands by uploading a Perl program to the server and using a .. (dot dot) in the AZ parameter to reference the program.
CVE-2001-0437(PUBLISHED):upload_file.pl in DCForum 2000 1.0 allows remote attackers to upload arbitrary files without authentication by setting the az parameter to upload_file.
CVE-2001-0438(PUBLISHED):Preview version of Timbuktu for Mac OS X allows local users to modify System Preferences without logging in via the About Timbuktu menu.
CVE-2001-0439(PUBLISHED):licq before 1.0.3 allows remote attackers to execute arbitrary commands via shell metacharacters in a URL.
CVE-2001-0440(PUBLISHED):Buffer overflow in logging functions of licq before 1.0.3 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands.
CVE-2001-0441(PUBLISHED):Buffer overflow in (1) wrapping and (2) unwrapping functions of slrn news reader before 0.9.7.0 allows remote attackers to execute arbitrary commands via a long message header.
CVE-2001-0442(PUBLISHED):Buffer overflow in Mercury MTA POP3 server for NetWare 1.48 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long APOP command.
CVE-2001-0443(PUBLISHED):Buffer overflow in QPC QVT/Net Popd 4.20 in QVT/Net 5.0 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via (1) a long username, or (2) a long password.
CVE-2001-0444(PUBLISHED):Cisco CBOS 2.3.0.053 sends output of the "sh nat" (aka "show nat") command to the terminal of the next user who attempts to connect to the router via telnet, which could allow that user to obtain sensitive information.
CVE-2001-0446(PUBLISHED):IBM WCS (WebSphere Commerce Suite) 4.0.1 with Application Server 3.0.2 allows remote attackers to read source code for .jsp files by appending a / to the requested URL.
CVE-2001-0447(PUBLISHED):Web configuration server in 602Pro LAN SUITE allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long HTTP request containing "%2e" (dot dot) characters.
CVE-2001-0448(PUBLISHED):Web configuration server in 602Pro LAN SUITE allows remote attackers to cause a denial of service via an HTTP GET HTTP request to the aux directory, and possibly other directories with legacy DOS device names.
CVE-2001-0449(PUBLISHED):Buffer overflow in WinZip 8.0 allows attackers to execute arbitrary commands via a long file name that is processed by the /zipandemail command line option.
CVE-2001-0450(PUBLISHED):Directory traversal vulnerability in Transsoft FTP Broker before 5.5 allows attackers to (1) delete arbitrary files via DELETE, or (2) list arbitrary directories via LIST, via a .. (dot dot) in the file name.
CVE-2001-0451(PUBLISHED):INDEXU 2.0 beta and earlier allows remote attackers to bypass authentication and gain privileges by setting the cookie_admin_authenticated cookie value to 1.
CVE-2001-0452(PUBLISHED):BRS WebWeaver FTP server before 0.64 Beta allows remote attackers to obtain the real pathname of the server via a "CD *" command followed by an ls command.
CVE-2001-0453(PUBLISHED):Directory traversal vulnerability in BRS WebWeaver HTTP server allows remote attackers to read arbitrary files via a .. (dot dot) attack in the (1) syshelp, (2) sysimages, or (3) scripts directories.
CVE-2001-0454(PUBLISHED):Directory traversal vulnerability in SlimServe HTTPd 1.1a allows remote attackers to read arbitrary files via a ... (modified dot dot) in the HTTP request.
CVE-2001-0455(PUBLISHED):Cisco Aironet 340 Series wireless bridge before 8.55 does not properly disable access to the web interface, which allows remote attackers to modify its configuration.
CVE-2001-0456(PUBLISHED):postinst installation script for Proftpd in Debian 2.2 does not properly change the "run as uid/gid root" configuration when the user enables anonymous access, which causes the server to run at a higher privilege than intended.
CVE-2001-0457(PUBLISHED):man2html before 1.5-22 allows remote attackers to cause a denial of service (memory exhaustion).
CVE-2001-0458(PUBLISHED):Multiple buffer overflows in ePerl before 2.2.14-0.7 allow local and remote attackers to execute arbitrary commands.
CVE-2001-0459(PUBLISHED):Buffer overflows in ascdc Afterstep while running setuid allows local users to gain root privileges via a long (1) -d option, (2) -m option, or (3) -f option.
CVE-2001-0460(PUBLISHED):Websweeper 4.0 does not limit the length of certain HTTP headers, which allows remote attackers to cause a denial of service (memory exhaustion) via an extremely large HTTP Referrer: header.
CVE-2001-0461(PUBLISHED):template.cgi in Free On-Line Dictionary of Computing (FOLDOC) allows remote attackers to read files and execute commands via shell metacharacters in the argument to template.cgi.
CVE-2001-0462(PUBLISHED):Directory traversal vulnerability in Perl web server 0.3 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) in the URL.
CVE-2001-0463(PUBLISHED):Directory traversal vulnerability in cal_make.pl in PerlCal allows remote attackers to read arbitrary files via a .. (dot dot) in the p0 parameter.
CVE-2001-0464(PUBLISHED):Buffer overflow in websync.exe in Cyberscheduler allows remote attackers to execute arbitrary commands via a long tzs (timezone) parameter.
CVE-2001-0465(PUBLISHED):TurboTax saves passwords in a temporary file when a user imports investment tax information from a financial institution, which could allow local users to obtain sensitive information.
CVE-2001-0466(PUBLISHED):Directory traversal vulnerability in ustorekeeper 1.61 allows remote attackers to read arbitrary files via a .. (dot dot) in the file parameter.
CVE-2001-0467(PUBLISHED):Directory traversal vulnerability in RobTex Viking Web server before 1.07-381 allows remote attackers to read arbitrary files via a \... (modified dot dot) in an HTTP URL request.
CVE-2001-0468(PUBLISHED):Buffer overflow in FTPFS allows local users to gain root privileges via a long user name.
CVE-2001-0469(PUBLISHED):rwho daemon rwhod in FreeBSD 4.2 and earlier, and possibly other operating systems, allows remote attackers to cause a denial of service via malformed packets with a short length.
CVE-2001-0470(PUBLISHED):Buffer overflow in SNMP proxy agent snmpd in Solaris 8 may allow local users to gain root privileges by calling snmpd with a long program name.
CVE-2001-0471(PUBLISHED):SSH daemon version 1 (aka SSHD-1 or SSH-1) 1.2.30 and earlier does not log repeated login attempts, which could allow remote attackers to compromise accounts without detection via a brute force attack.
CVE-2001-0472(PUBLISHED):Hursley Software Laboratories Consumer Transaction Framework (HSLCTF) HTTP object allows remote attackers to cause a denial of service (crash) via an extremely long HTTP request.
CVE-2001-0473(PUBLISHED):Format string vulnerability in Mutt before 1.2.5 allows a remote malicious IMAP server to execute arbitrary commands.
CVE-2001-0474(PUBLISHED):Utah-glx in Mesa before 3.3-14 on Mandrake Linux 7.2 allows local users to overwrite arbitrary files via a symlink attack on the /tmp/glxmemory file.
CVE-2001-0475(PUBLISHED):index.php in Jelsoft vBulletin does not properly initialize a PHP variable that is used to store template information, which allows remote attackers to execute arbitrary PHP code via special characters in the templatecache parameter.
CVE-2001-0476(PUBLISHED):Multiple buffer overflows in s.cgi program in Aspseek search engine 1.03 and earlier allow remote attackers to execute arbitrary commands via (1) a long HTTP query string, or (2) a long tmpl parameter.
CVE-2001-0477(PUBLISHED):Vulnerability in WebCalendar 0.9.26 allows remote command execution.
CVE-2001-0478(PUBLISHED):Directory traversal vulnerability in phpMyAdmin 2.2.0 and earlier versions allows remote attackers to execute arbitrary code via a .. (dot dot) in an argument to the sql.php script.
CVE-2001-0479(PUBLISHED):Directory traversal vulnerability in phpPgAdmin 2.2.1 and earlier versions allows remote attackers to execute arbitrary code via a .. (dot dot) in an argument to the sql.php script.
CVE-2001-0480(PUBLISHED):Directory traversal vulnerability in Alex's FTP Server 0.7 allows remote attackers to read arbitrary files via a ... (modified dot dot) in the (1) GET or (2) CD commands.
CVE-2001-0481(PUBLISHED):Vulnerability in rpmdrake in Mandrake Linux 8.0 related to insecure temporary file handling.
CVE-2001-0482(PUBLISHED):Configuration error in Argus PitBull LX allows root users to bypass specified access control restrictions and cause a denial of service or execute arbitrary commands by modifying kernel variables such as MaxFiles, MaxInodes, and ModProbePath in /proc/sys via calls to sysctl.
CVE-2001-0483(PUBLISHED):Configuration error in Axent Raptor Firewall 6.5 allows remote attackers to use the firewall as a proxy to access internal web resources when the http.noproxy Rule is not set.
CVE-2001-0484(PUBLISHED):Tektronix PhaserLink 850 does not require authentication for access to configuration pages such as _ncl_subjects.shtml and _ncl_items.shtml, which allows remote attackers to modify configuration information and cause a denial of service by accessing the pages.
CVE-2001-0485(PUBLISHED):Unknown vulnerability in netprint in IRIX 6.2, and possibly other versions, allows local users with lp privileges attacker to execute arbitrary commands via the -n option.
CVE-2001-0486(PUBLISHED):Remote attackers can cause a denial of service in Novell BorderManager 3.6 and earlier by sending TCP SYN flood to port 353.
CVE-2001-0487(PUBLISHED):AIX SNMP server snmpd allows remote attackers to cause a denial of service via a RST during the TCP connection.
CVE-2001-0488(PUBLISHED):pcltotiff in HP-UX 10.x has unnecessary set group id permissions, which allows local users to cause a denial of service.
CVE-2001-0489(PUBLISHED):Format string vulnerability in gftp prior to 2.0.8 allows remote malicious FTP servers to execute arbitrary commands.
CVE-2001-0490(PUBLISHED):Buffer overflow in WINAMP 2.6x and 2.7x allows attackers to execute arbitrary code via a long string in an AIP file.
CVE-2001-0491(PUBLISHED):Directory traversal vulnerability in RaidenFTPD Server 2.1 before build 952 allows attackers to access files outside the ftp root via dot dot attacks, such as (1) .... in CWD, (2) .. in NLST, or (3) ... in NLST.
CVE-2001-0492(PUBLISHED):Netcruiser Web server version ******* and earlier allows remote attackers to determine the physical path of the server via a URL containing (1) con, (2) com2, or (3) com3.
CVE-2001-0493(PUBLISHED):Small HTTP server 2.03 allows remote attackers to cause a denial of service via a URL that contains an MS-DOS device name such as aux.
CVE-2001-0494(PUBLISHED):Buffer overflow in IPSwitch IMail SMTP server 6.06 and possibly prior versions allows remote attackers to execute arbitrary code via a long From: header.
CVE-2001-0495(PUBLISHED):Directory traversal in DataWizard WebXQ server 1.204 allows remote attackers to view files outside of the web root via a .. (dot dot) attack.
CVE-2001-0496(PUBLISHED):kdesu in kdelibs package creates world readable temporary files containing authentication info, which can allow local users to gain privileges.
CVE-2001-0497(PUBLISHED):dnskeygen in BIND 8.2.4 and earlier, and dnssec-keygen in BIND 9.1.2 and earlier, set insecure permissions for a HMAC-MD5 shared secret key file used for DNS Transactional Signatures (TSIG), which allows attackers to obtain the keys and perform dynamic DNS updates.
CVE-2001-0498(PUBLISHED):Transparent Network Substrate (TNS) over Net8 (SQLNet) in Oracle 8i 8.1.7 and earlier allows remote attackers to cause a denial of service via a malformed SQLNet connection request with a large offset in the header extension.
CVE-2001-0499(PUBLISHED):Buffer overflow in Transparent Network Substrate (TNS) Listener in Oracle 8i 8.1.7 and earlier allows remote attackers to gain privileges via a long argument to the commands (1) STATUS, (2) PING, (3) SERVICES, (4) TRC_FILE, (5) SAVE_CONFIG, or (6) RELOAD.
CVE-2001-0500(PUBLISHED):Buffer overflow in ISAPI extension (idq.dll) in Index Server 2.0 and Indexing Service 2000 in IIS 6.0 beta and earlier allows remote attackers to execute arbitrary commands via a long argument to Internet Data Administration (.ida) and Internet Data Query (.idq) files such as default.ida, as commonly exploited by Code Red.
CVE-2001-0501(PUBLISHED):Microsoft Word 2002 and earlier allows attackers to automatically execute macros without warning the user by embedding the macros in a manner that escapes detection by the security scanner.
CVE-2001-0502(PUBLISHED):Running Windows 2000 LDAP Server over SSL, a function does not properly check the permissions of a user request when the directory principal is a domain user and the data attribute is the domain password, which allows local users to modify the login password of other users.
CVE-2001-0503(PUBLISHED):Microsoft NetMeeting 3.01 with Remote Desktop Sharing enabled allows remote attackers to cause a denial of service via a malformed string to the NetMeeting service port, aka a variant of the "NetMeeting Desktop Sharing" vulnerability.
CVE-2001-0504(PUBLISHED):Vulnerability in authentication process for SMTP service in Microsoft Windows 2000 allows remote attackers to use incorrect credentials to gain privileges and conduct activities such as mail relaying.
CVE-2001-0505(PUBLISHED):Multiple memory leaks in Microsoft Services for Unix 2.0 allow remote attackers to cause a denial of service (memory exhaustion) via a large number of malformed requests to (1) the Telnet service, or (2) the NFS service.
CVE-2001-0506(PUBLISHED):Buffer overflow in ssinc.dll in IIS 5.0 and 4.0 allows local users to gain system privileges via a Server-Side Includes (SSI) directive for a long filename, which triggers the overflow when the directory name is added, aka the "SSI privilege elevation" vulnerability.
CVE-2001-0507(PUBLISHED):IIS 5.0 uses relative paths to find system files that will run in-process, which allows local users to gain privileges via a Trojan horse file, aka the "System file listing privilege elevation" vulnerability.
CVE-2001-0508(PUBLISHED):Vulnerability in IIS 5.0 allows remote attackers to cause a denial of service (restart) via a long, invalid WebDAV request.
CVE-2001-0509(PUBLISHED):Vulnerabilities in RPC servers in (1) Microsoft Exchange Server 2000 and earlier, (2) Microsoft SQL Server 2000 and earlier, (3) Windows NT 4.0, and (4) Windows 2000 allow remote attackers to cause a denial of service via malformed inputs.
CVE-2001-0513(PUBLISHED):Oracle listener process on Windows NT redirects connection requests to another port and creates a separate thread to process the request, which allows remote attackers to cause a denial of service by repeatedly connecting to the Oracle listener but not connecting to the redirected port.
CVE-2001-0514(PUBLISHED):SNMP service in Atmel 802.11b VNET-B Access Point 1.3 and earlier, as used in Netgear ME102 and Linksys WAP11, accepts arbitrary community strings with requested MIB modifications, which allows remote attackers to obtain sensitive information such as WEP keys, cause a denial of service, or gain access to the network.
CVE-2001-0515(PUBLISHED):Oracle Listener in Oracle 7.3 and 8i allows remote attackers to cause a denial of service via a malformed connection packet with a large offset_to_data value.
CVE-2001-0516(PUBLISHED):Oracle listener between Oracle 9i and Oracle 8.0 allows remote attackers to cause a denial of service via a malformed connection packet that contains an incorrect requester_version value that does not match an expected offset to the data.
CVE-2001-0517(PUBLISHED):Oracle listener in Oracle 8i on Solaris allows remote attackers to cause a denial of service via a malformed connection packet with a maximum transport data size that is set to 0.
CVE-2001-0518(PUBLISHED):Oracle listener before Oracle 9i allows attackers to cause a denial of service by repeatedly sending the first portion of a fragmented Oracle command without sending the remainder of the command, which causes the listener to hang.
CVE-2001-0519(PUBLISHED):Aladdin eSafe Gateway versions 2.x allows a remote attacker to circumvent HTML SCRIPT filtering via a special arrangement of HTML tags which includes SCRIPT tags embedded within other SCRIPT tags.
CVE-2001-0520(PUBLISHED):Aladdin eSafe Gateway versions 3.0 and earlier allows a remote attacker to circumvent filtering of SCRIPT tags by embedding the scripts within certain HTML tags including (1) onload in the BODY tag, (2) href in the A tag, (3) the BUTTON tag, (4) the INPUT tag, or (5) any other tag in which scripts can be defined.
CVE-2001-0521(PUBLISHED):Aladdin eSafe Gateway versions 3.0 and earlier allows a remote attacker to circumvent HTML SCRIPT filtering via the UNICODE encoding of SCRIPT tags within the HTML document.
CVE-2001-0522(PUBLISHED):Format string vulnerability in Gnu Privacy Guard (aka GnuPG or gpg) 1.05 and earlier can allow an attacker to gain privileges via format strings in the original filename that is stored in an encrypted file.
CVE-2001-0523(PUBLISHED):eEye SecureIIS versions 1.0.3 and earlier allows a remote attacker to bypass filtering of requests made to SecureIIS by escaping HTML characters within the request, which could allow a remote attacker to use restricted variables and perform directory traversal attacks on vulnerable programs that would otherwise be protected.
CVE-2001-0524(PUBLISHED):eEye SecureIIS versions 1.0.3 and earlier does not perform length checking on individual HTTP headers, which allows a remote attacker to send arbitrary length strings to IIS, contrary to an advertised feature of SecureIIS versions 1.0.3 and earlier.
CVE-2001-0525(PUBLISHED):Buffer overflow in dsh in dqs 3.2.7 in SuSE Linux 7.0 and earlier, and possibly other operating systems, allows local users to gain privileges via a long first command line argument.
CVE-2001-0526(PUBLISHED):Buffer overflow in the Xview library as used by mailtool in Solaris 8 and earlier allows a local attacker to gain privileges via the OPENWINHOME environment variable.
CVE-2001-0527(PUBLISHED):DCScripts DCForum versions 2000 and earlier allow a remote attacker to gain additional privileges by inserting pipe symbols (|) and newlines into the last name in the registration form, which will create an extra entry in the registration database.
CVE-2001-0528(PUBLISHED):Oracle E-Business Suite Release 11i Applications Desktop Integrator (ADI) version 7.x includes a debug version of FNDPUB11I.DLL, which logs the APPS schema password in cleartext in a debug file, which allows local users to obtain the password and gain privileges.
CVE-2001-0529(PUBLISHED):OpenSSH version 2.9 and earlier, with X forwarding enabled, allows a local attacker to delete any file named 'cookies' via a symlink attack.
CVE-2001-0530(PUBLISHED):Spearhead NetGAP 200 and 300 before build 78 allow a remote attacker to bypass file blocking and content inspection via specially encoded URLs which include '%' characters.
CVE-2001-0533(PUBLISHED):Buffer overflow in libi18n library in IBM AIX 5.1 and 4.3.x allows local users to gain root privileges via a long LANG environmental variable.
CVE-2001-0534(PUBLISHED):Multiple buffer overflows in RADIUS daemon radiusd in (1) Merit 3.6b and (2) Lucent 2.1-2 RADIUS allow remote attackers to cause a denial of service or execute arbitrary commands.
CVE-2001-0535(PUBLISHED):Example applications (Exampleapps) in ColdFusion Server 4.x do not properly restrict prevent access from outside the local host's domain, which allows remote attackers to conduct upload, read, or execute files by spoofing the "HTTP Host" (CGI.Host) variable in (1) the "Web Publish" example script, and (2) the "Email" example script.
CVE-2001-0537(PUBLISHED):HTTP server for Cisco IOS 11.3 to 12.2 allows attackers to bypass authentication and execute arbitrary commands, when local authorization is being used, by specifying a high access level in the URL.
CVE-2001-0538(PUBLISHED):Microsoft Outlook View ActiveX Control in Microsoft Outlook 2002 and earlier allows remote attackers to execute arbitrary commands via a malicious HTML e-mail message or web page.
CVE-2001-0540(PUBLISHED):Memory leak in Terminal servers in Windows NT and Windows 2000 allows remote attackers to cause a denial of service (memory exhaustion) via a large number of malformed Remote Desktop Protocol (RDP) requests to port 3389.
CVE-2001-0541(PUBLISHED):Buffer overflow in Microsoft Windows Media Player 7.1 and earlier allows remote attackers to execute arbitrary commands via a malformed Windows Media Station (.NSC) file.
CVE-2001-0542(PUBLISHED):Buffer overflows in Microsoft SQL Server 7.0 and 2000 allow attackers with access to SQL Server to execute arbitrary code through the functions (1) raiserror, (2) formatmessage, or (3) xp_sprintf.  NOTE: the C runtime format string vulnerability reported in MS01-060 is identified by CVE-2001-0879.
CVE-2001-0543(PUBLISHED):Memory leak in NNTP service in Windows NT 4.0 and Windows 2000 allows remote attackers to cause a denial of service (memory exhaustion) via a large number of malformed posts.
CVE-2001-0544(PUBLISHED):IIS 5.0 allows local users to cause a denial of service (hang) via by installing content that produces a certain invalid MIME Content-Type header, which corrupts the File Type table.
CVE-2001-0545(PUBLISHED):IIS 4.0 with URL redirection enabled allows remote attackers to cause a denial of service (crash) via a malformed request that specifies a length that is different than the actual length.
CVE-2001-0546(PUBLISHED):Memory leak in H.323 Gatekeeper Service in Microsoft Internet Security and Acceleration (ISA) Server 2000 allows remote attackers to cause a denial of service (resource exhaustion) via a large amount of malformed H.323 data.
CVE-2001-0547(PUBLISHED):Memory leak in the proxy service in Microsoft Internet Security and Acceleration (ISA) Server 2000 allows local attackers to cause a denial of service (resource exhaustion).
CVE-2001-0548(PUBLISHED):Buffer overflow in dtmail in Solaris 2.6 and 7 allows local users to gain privileges via the MAIL environment variable.
CVE-2001-0549(PUBLISHED):Symantec LiveUpdate 1.5 stores proxy passwords in cleartext in a registry key, which could allow local users to obtain the passwords.
CVE-2001-0550(PUBLISHED):wu-ftpd 2.6.1 allows remote attackers to execute arbitrary commands via a "~{" argument to commands such as CWD, which is not properly handled by the glob function (ftpglob).
CVE-2001-0551(PUBLISHED):Buffer overflow in CDE Print Viewer (dtprintinfo) allows local users to execute arbitrary code by copying text from the clipboard into the Help window.
CVE-2001-0552(PUBLISHED):ovactiond in HP OpenView Network Node Manager (NNM) 6.1 and Tivoli Netview 5.x and 6.x allows remote attackers to execute arbitrary commands via shell metacharacters in a certain SNMP trap message.
CVE-2001-0553(PUBLISHED):SSH Secure Shell 3.0.0 on Unix systems does not properly perform password authentication to the sshd2 daemon, which allows local users to gain access to accounts with short password fields, such as locked accounts that use "NP" in the password field.
CVE-2001-0554(PUBLISHED):Buffer overflow in BSD-based telnetd telnet daemon on various operating systems allows remote attackers to execute arbitrary commands via a set of options including AYT (Are You There), which is not properly handled by the telrcv function.
CVE-2001-0555(PUBLISHED):ScreamingMedia SITEWare versions 2.5 through 3.1 allows a remote attacker to read world-readable files via a ..  (dot dot) attack through (1) the SITEWare Editor's Desktop or (2) the template parameter in SWEditServlet.
CVE-2001-0556(PUBLISHED):The Nirvana Editor (NEdit) 5.1.1 and earlier allows a local attacker to overwrite other users' files via a symlink attack on (1) backup files or (2) temporary files used when nedit prints a file or portions of a file.
CVE-2001-0557(PUBLISHED):T. Hauck Jana Webserver 1.46 and earlier allows a remote attacker to view arbitrary files via a '..' (dot dot) attack which is URL encoded (%2e%2e).
CVE-2001-0558(PUBLISHED):T. Hauck Jana Webserver 2.01 beta 1 and earlier allows a remote attacker to create a denial of service via a URL request which includes a MS-DOS device name (i.e. GET /aux HTTP/1.0).
CVE-2001-0559(PUBLISHED):crontab in Vixie cron 3.0.1 and earlier does not properly drop privileges after the failed parsing of a modification operation, which could allow a local attacker to gain additional privileges when an editor is called to correct the error.
CVE-2001-0560(PUBLISHED):Buffer overflow in Vixie cron 3.0.1-56 and earlier could allow a local attacker to gain additional privileges via a long username (> 20 characters).
CVE-2001-0561(PUBLISHED):Directory traversal vulnerability in Drummond Miles A1Stats prior to 1.6 allows a remote attacker to read arbitrary files via a '..' (dot dot) attack in (1) a1disp2.cgi, (2) a1disp3.cgi, or (3) a1disp4.cgi.
CVE-2001-0562(PUBLISHED):a1disp.cgi program in Drummond Miles A1Stats prior to 1.6 allows a remote attacker to execute commands via a specially crafted URL which includes shell metacharacters.
CVE-2001-0563(PUBLISHED):ElectroSystems Engineering Inc. ElectroComm 2.0 and earlier allows a remote attacker to create a denial of service via large (> 160000 character) strings sent to port 23.
CVE-2001-0564(PUBLISHED):APC Web/SNMP Management Card prior to Firmware 310 only supports one telnet connection, which allows a remote attacker to create a denial of service via repeated failed logon attempts which temporarily locks the card.
CVE-2001-0565(PUBLISHED):Buffer overflow in mailx in Solaris 8 and earlier allows a local attacker to gain additional privileges via a long '-F' command line option.
CVE-2001-0566(PUBLISHED):Cisco Catalyst 2900XL switch allows a remote attacker to create a denial of service via an empty UDP packet sent to port 161 (SNMP) when SNMP  is disabled.
CVE-2001-0567(PUBLISHED):Digital Creations Zope 2.3.2 and earlier allows a local attacker to gain additional privileges via the changing of ZClass permission mappings for objects and methods in the ZClass.
CVE-2001-0568(PUBLISHED):Digital Creations Zope 2.3.1 b1 and earlier allows a local attacker (Zope user) with through-the-web scripting capabilities to alter ZClasses class attributes.
CVE-2001-0569(PUBLISHED):Digital Creations Zope 2.3.1 b1 and earlier contains a problem in the method return values related to the classes (1) ObjectManager, (2) PropertyManager, and (3) PropertySheet.
CVE-2001-0570(PUBLISHED):minicom 1.83.1 and earlier allows a local attacker to gain additional privileges via numerous format string attacks.
CVE-2001-0571(PUBLISHED):Directory traversal vulnerability in the web server for (1) Elron Internet Manager (IM) Message Inspector and (2) Anti-Virus before 3.0.4 allows remote attackers to read arbitrary files via a .. (dot dot) in the requested URL.
CVE-2001-0572(PUBLISHED):The SSH protocols 1 and 2 (aka SSH-2) as implemented in OpenSSH and other packages have various weaknesses which can allow a remote attacker to obtain the following information via sniffing: (1) password lengths or ranges of lengths, which simplifies brute force password guessing, (2) whether RSA or DSA authentication is being used, (3) the number of authorized_keys in RSA authentication, or (4) the lengths of shell commands.
CVE-2001-0573(PUBLISHED):lsfs in AIX 4.x allows a local user to gain additional privileges by creating Trojan horse programs named (1) grep or (2) lslv in a certain directory that is under the user's control, which cause lsfs to access the programs in that directory.
CVE-2001-0574(PUBLISHED):Directory traversal vulnerability in MP3Mystic prior to 1.04b3 allows a remote attacker to download arbitrary files via a '..' (dot dot) in the URL.
CVE-2001-0575(PUBLISHED):Buffer overflow in lpshut in SCO OpenServer 5.0.6 can allow a local attacker to gain additional privileges via a long first argument to lpshut.
CVE-2001-0576(PUBLISHED):lpusers as included with SCO OpenServer 5.0 through 5.0.6 allows a  local attacker to gain additional privileges via a buffer overflow attack in the '-u' command line parameter.
CVE-2001-0577(PUBLISHED):recon in SCO OpenServer 5.0 through 5.0.6 can allow a local attacker to gain additional privileges via a buffer overflow attack in the first command line argument.
CVE-2001-0578(PUBLISHED):Buffer overflow in lpforms in SCO OpenServer 5.0-5.0.6 can allow a local attacker to gain additional privileges via a long first argument to the lpforms command.
CVE-2001-0579(PUBLISHED):lpadmin in SCO OpenServer 5.0.6 can allow a local attacker to gain additional privileges via a buffer overflow attack in the first argument to the command.
CVE-2001-0580(PUBLISHED):Hughes Technologies Virtual DNS (VDNS) Server 1.0 allows a remote attacker to create a denial of service by connecting to port 6070, sending some data, and closing the connection.
CVE-2001-0581(PUBLISHED):Spytech Spynet Chat Server 6.5 allows a remote attacker to create a denial of service (crash) via a large number of connections to port 6387.
CVE-2001-0582(PUBLISHED):Ben Spink CrushFTP FTP Server 2.1.6 and earlier allows a local attacker to access arbitrary files via a '..' (dot dot) attack, or variations, in (1) GET, (2) CD, (3) NLST, (4) SIZE, (5) RETR.
CVE-2001-0583(PUBLISHED):Alt-N Technologies MDaemon 3.5.4 allows a remote attacker to create a denial of service via the URL request of a MS-DOS device (such as GET /aux) to (1) the Worldclient service at port 3000, or (2) the Webconfig service at port 3001.
CVE-2001-0584(PUBLISHED):IMAP server in Alt-N Technologies MDaemon 3.5.6 allows a local user to cause a denial of service (hang) via long (1) SELECT or (2) EXAMINE commands.
CVE-2001-0585(PUBLISHED):Gordano NTMail 6.0.3c allows a remote attacker to create a denial of service via a long (>= 255 characters) URL request to port 8000 or port 9000.
CVE-2001-0586(PUBLISHED):TrendMicro ScanMail for Exchange 3.5 Evaluation allows a local attacker to recover the administrative credentials for ScanMail via a combination of unprotected registry keys and weakly encrypted passwords.
CVE-2001-0587(PUBLISHED):deliver program in MMDF 2.43.3b in SCO OpenServer 5.0.6 can allow a local attacker to gain additional privileges via a buffer overflow in the first argument to the command.
CVE-2001-0588(PUBLISHED):sendmail 8.9.3, as included with the MMDF 2.43.3b package in SCO OpenServer 5.0.6, can allow a local attacker to gain additional privileges via a buffer overflow in the first argument to the command.
CVE-2001-0589(PUBLISHED):NetScreen ScreenOS prior to 2.5r6 on the NetScreen-10 and Netscreen-100 can allow a local attacker to bypass the DMZ 'denial' policy via specific traffic patterns.
CVE-2001-0590(PUBLISHED):Apache Software Foundation Tomcat Servlet prior to 3.2.2 allows a remote attacker to read the source code to arbitrary 'jsp' files via a malformed URL request which does not end with an HTTP protocol specification (i.e. HTTP/1.0).
CVE-2001-0591(PUBLISHED):Directory traversal vulnerability in Oracle JSP 1.0.x through 1.1.1 and Oracle 8.1.7 iAS Release 1.0.2 can allow a remote attacker to read or execute arbitrary .jsp files via a '..' (dot dot) attack.
CVE-2001-0592(PUBLISHED):Watchguard Firebox II prior to 4.6 allows a remote attacker to create a denial of service in the kernel via a large stream (>10,000) of malformed ICMP or TCP packets.
CVE-2001-0593(PUBLISHED):Anaconda Partners Clipper 3.3 and earlier allows a remote attacker to read arbitrary files via a '..' (dot dot) attack in the template parameter.
CVE-2001-0594(PUBLISHED):kcms_configure as included with Solaris 7 and 8 allows a local attacker to gain additional privileges via a buffer overflow in a command line argument.
CVE-2001-0595(PUBLISHED):Buffer overflow in the kcsSUNWIOsolf.so library in Solaris 7 and 8 allows local attackers to execute arbitrary commands via the KCMS_PROFILES environment variable, e.g. as demonstrated using the kcms_configure program.
CVE-2001-0596(PUBLISHED):Netscape Communicator before 4.77 allows remote attackers to execute arbitrary Javascript via a GIF image whose comment contains the Javascript.
CVE-2001-0597(PUBLISHED):Zetetic Secure Tool for Recalling Important Passwords (STRIP) 0.5 and earlier for the PalmOS allows a local attacker to recover passwords via a brute force attack.  This attack is made feasible by STRIP's use of SysRandom, which is seeded by TimeGetTicks, and an implementation flaw which vastly reduces the password 'search space'.
CVE-2001-0598(PUBLISHED):Symantec Ghost 6.5 and earlier allows a remote attacker to create a denial of service by sending large (> 45Kb) amounts of data to the Ghost Configuration Server on port 1347, which triggers an error that is not properly handled.
CVE-2001-0599(PUBLISHED):Sybase Adaptive Server Anywhere Database Engine 6.0.3.2747 and earlier as included with Symantec Ghost 6.5 allows a remote attacker to create a denial of service by sending large (> 45Kb) amounts of data to port 2638.
CVE-2001-0600(PUBLISHED):Lotus Domino R5 prior to 5.0.7 allows a remote attacker to create a denial of service via repeated URL requests with the same HTTP headers, such as (1) Accept, (2) Accept-Charset, (3) Accept-Encoding, (4) Accept-Language, and (5) Content-Type.
CVE-2001-0601(PUBLISHED):Lotus Domino R5 prior to 5.0.7 allows a remote attacker to create a denial of service via HTTP requests containing certain combinations of UNICODE characters.
CVE-2001-0602(PUBLISHED):Lotus Domino R5 prior to 5.0.7 allows a remote attacker to create a denial of service via repeated (>400) URL requests for DOS devices.
CVE-2001-0603(PUBLISHED):Lotus Domino R5 prior to 5.0.7 allows a remote attacker to create a denial of service via repeatedly sending large (> 10Kb) amounts of data to the DIIOP - CORBA service on TCP port 63148.
CVE-2001-0604(PUBLISHED):Lotus Domino R5 prior to 5.0.7 allows a remote attacker to create a denial of service via URL requests (>8Kb) containing a large number of '/' characters.
CVE-2001-0605(PUBLISHED):Headlight Software MyGetright prior to 1.0b allows a remote attacker to upload and/or overwrite arbitrary files via a malicious .dld (skins-data) file which contains long strings of random data.
CVE-2001-0606(PUBLISHED):Vulnerability in iPlanet Web Server 4.X in HP-UX 11.04 (VVOS) with VirtualVault A.04.00 allows a remote attacker to create a denial of service via the HTTPS service.
CVE-2001-0607(PUBLISHED):asecure as included with HP-UX 10.01 through 11.00 can allow a local attacker to create a denial of service and gain additional privileges via unsafe permissions on the asecure program, a different vulnerability than CVE-2000-0083.
CVE-2001-0608(PUBLISHED):HP architected interface facility (AIF) as includes with MPE/iX 5.5 through 6.5 running on a HP3000 allows an attacker to gain additional privileges and gain access to databases via the AIF - AIFCHANGELOGON program.
CVE-2001-0609(PUBLISHED):Format string vulnerability in Infodrom cfingerd 1.4.3 and earlier allows a remote attacker to gain additional privileges via a malformed ident reply that is passed to the syslog function.
CVE-2001-0610(PUBLISHED):kfm as included with KDE 1.x can allow a local attacker to gain additional privileges via a symlink attack in the kfm cache directory in /tmp.
CVE-2001-0611(PUBLISHED):Becky! 2.00.05 and earlier can allow a remote attacker to gain additional privileges via a buffer overflow attack on long messages without newline characters.
CVE-2001-0612(PUBLISHED):McAfee Remote Desktop 3.0 and earlier allows remote attackers to cause a denial of service (crash) via a large number of packets to port 5045.
CVE-2001-0613(PUBLISHED):Omnicron Technologies OmniHTTPD Professional 2.08 and earlier allows a remote attacker to create a denial of service via a long POST URL request.
CVE-2001-0614(PUBLISHED):Carello E-Commerce 1.2.1 and earlier allows a remote attacker to gain additional privileges and execute arbitrary commands via a specially constructed URL.
CVE-2001-0615(PUBLISHED):Directory traversal vulnerability in Faust Informatics Freestyle Chat server prior to 4.1 SR3 allows a remote attacker to read arbitrary files via a specially crafted URL which includes variations of a '..' (dot dot) attack such as '...' or '....'.
CVE-2001-0616(PUBLISHED):Faust Informatics Freestyle Chat server prior to 4.1 SR3 allows a remote attacker to create a denial of service via a URL request which includes a MS-DOS device name (e.g., GET /aux HTTP/1.0).
CVE-2001-0617(PUBLISHED):Allied Telesyn AT-AR220e cable/DSL router firmware 1.08a RC14 with the portmapper and the 'Virtual Server' enabled can allow a remote attacker to gain access to mapped services even though the single portmappings may be disabled.
CVE-2001-0618(PUBLISHED):Orinoco RG-1000 wireless Residential Gateway uses the last 5 digits of the 'Network Name' or SSID as the default Wired Equivalent Privacy (WEP) encryption key.  Since the SSID occurs in the clear during communications, a remote attacker could determine the WEP key and decrypt RG-1000 traffic.
CVE-2001-0619(PUBLISHED):The Lucent Closed Network protocol can allow remote attackers to join Closed Network networks which they do not have access to.  The 'Network Name' or SSID, which is used as a shared secret to join the network, is transmitted in the clear.
CVE-2001-0620(PUBLISHED):iPlanet Calendar Server 5.0p2 and earlier allows a local attacker to gain access to the Netscape Admin Server (NAS) LDAP database and read arbitrary files by obtaining the cleartext administrator username and password from the configuration file, which has insecure permissions.
CVE-2001-0621(PUBLISHED):The FTP server on Cisco Content Service 11000 series switches (CSS) before WebNS 4.01B23s and WebNS 4.10B13s allows an attacker who is an FTP user to read and write arbitrary files via GET or PUT commands.
CVE-2001-0622(PUBLISHED):The web management service on Cisco Content Service series 11000 switches (CSS) before WebNS 4.01B29s or WebNS 4.10B17s allows a remote attacker to gain additional privileges by directly requesting the web management URL instead of navigating through the interface.
CVE-2001-0623(PUBLISHED):sendfiled, as included with Simple Asynchronous File Transfer (SAFT), on various Linux systems does not properly drop privileges when sending notification emails, which allows local attackers to gain privileges.
CVE-2001-0624(PUBLISHED):QNX 2.4 allows a local user to read arbitrary files by directly accessing the mount point for the FAT disk partition, e.g. /fs-dos.
CVE-2001-0625(PUBLISHED):ftpdownload in Computer Associates InoculateIT 6.0 allows a local attacker to overwrite arbitrary files via a symlink attack on /tmp/ftpdownload.log .
CVE-2001-0626(PUBLISHED):O'Reilly Website Professional 2.5.4 and earlier allows remote attackers to determine the physical path to the root directory via a URL request containing a ":" character.
CVE-2001-0627(PUBLISHED):vi as included with SCO OpenServer 5.0 - 5.0.6 allows a local attacker to overwrite arbitrary files via a symlink attack.
CVE-2001-0628(PUBLISHED):Microsoft Word 2000 does not check AutoRecovery (.asd) files for macros, which allows a local attacker to execute arbitrary macros with the user ID of the Word user.
CVE-2001-0629(PUBLISHED):HP Event Correlation Service (ecsd) as included with OpenView Network Node  Manager 6.1 allows a remote attacker to gain addition privileges via a buffer overflow attack in the '-restore_config' command line parameter.
CVE-2001-0630(PUBLISHED):Directory traversal vulnerability in MIMAnet viewsrc.cgi 2.0 allows a remote attacker to read arbitrary files via a '..' (dot dot) attack in the 'loc' variable.
CVE-2001-0631(PUBLISHED):Centrinity First Class Internet Services 5.50 allows for the circumventing of the default 'spam' filters via the presence of '<@>' in the 'From:' field, which allows remote attackers to send spoofed email with the identity of local users.
CVE-2001-0632(PUBLISHED):Sun Chili!Soft 3.5.2 on Linux and 3.6 on AIX creates a default admin username and password in the default installation, which can allow a remote attacker to gain additional privileges.
CVE-2001-0633(PUBLISHED):Directory traversal vulnerability in Sun Chili!Soft ASP on multiple Unixes allows a remote attacker to read arbitrary files above the web root via a '..' (dot dot) attack in the sample script 'codebrws.asp'.
CVE-2001-0634(PUBLISHED):Sun Chili!Soft ASP has weak permissions on various configuration files, which allows a local attacker to gain additional privileges and create a denial of service.
CVE-2001-0635(PUBLISHED):Red Hat Linux 7.1 sets insecure permissions on swap files created during installation, which can allow a local attacker to gain additional privileges by reading sensitive information from the swap file, such as passwords.
CVE-2001-0636(PUBLISHED):Buffer overflows in Raytheon SilentRunner allow remote attackers to (1) cause a denial of service in the collector (cle.exe) component of SilentRunner 2.0 via traffic containing long passwords, or (2) execute arbitrary commands via long HTTP queries in the Knowledge Browser component in SilentRunner 2.0 and 2.0.1.  NOTE: It is highly likely that this candidate will be split into multiple candidates.
CVE-2001-0641(PUBLISHED):Buffer overflow in man program in various distributions of Linux allows local user to execute arbitrary code as group man via a long -S option.
CVE-2001-0642(PUBLISHED):Directory traversal vulnerability in IncrediMail version 1400185 and earlier allows local users to overwrite files on the local hard drive by appending .. (dot dot) sequences to filenames listed in the content.ini file.
CVE-2001-0643(PUBLISHED):Internet Explorer 5.5 does not display the Class ID (CLSID) when it is at the end of the file name, which could allow attackers to trick the user into executing dangerous programs by making it appear that the document is of a safe file type.
CVE-2001-0644(PUBLISHED):Maxum Rumpus FTP Server 1.3.3 and 2.0.3 dev 3 stores passwords in plaintext in the "Rumpus User Database" file in the prefs folder, which could allow attackers to gain privileges on the server.
CVE-2001-0645(PUBLISHED):Symantec/AXENT NetProwler 3.5.x contains several default passwords, which could allow remote attackers to (1) access to the management tier via the "admin" password, or (2) connect to a MySQL ODBC from the management tier using a blank password.
CVE-2001-0646(PUBLISHED):Maxum Rumpus FTP Server 1.3.3 and 2.0.3 dev 3 allows a remote attacker to perform a denial of service (hang) by creating a directory name of a specific length.
CVE-2001-0647(PUBLISHED):Orange Web Server 2.1, based on GoAhead, allows a remote attacker to perform a denial of service via an HTTP GET request that does not include the HTTP version.
CVE-2001-0648(PUBLISHED):Directory traversal vulnerability in PHProjekt 2.1 and earlier allows a remote attacker to conduct unauthorized activities via a dot dot (..) attack on the file module.
CVE-2001-0649(PUBLISHED):Personal Web Sharing 1.5.5 allows a remote attacker to cause a denial of service via a long HTTP request.
CVE-2001-0650(PUBLISHED):Cisco devices IOS 12.0 and earlier allow a remote attacker to cause a crash, or bad route updates, via malformed BGP updates with unrecognized transitive attribute.
CVE-2001-0652(PUBLISHED):Heap overflow in xlock in Solaris 2.6 through 8 allows local users to gain root privileges via a long (1) XFILESEARCHPATH or (2) XUSERFILESEARCHPATH environmental variable.
CVE-2001-0653(PUBLISHED):Sendmail 8.10.0 through 8.11.5, and 8.12.0 beta, allows local users to modify process memory and possibly gain privileges via a large value in the 'category' part of debugger (-d) command line arguments, which is interpreted as a negative number.
CVE-2001-0658(PUBLISHED):Cross-site scripting (CSS) vulnerability in Microsoft Internet Security and Acceleration (ISA) Server 2000 allows remote attackers to cause other clients to execute certain script or read cookies via malicious script in an invalid URL that is not properly quoted in an error message.
CVE-2001-0659(PUBLISHED):Buffer overflow in IrDA driver providing infrared data exchange on Windows 2000 allows attackers who are physically close to the machine to cause a denial of service (reboot) via a malformed IrDA packet.
CVE-2001-0660(PUBLISHED):Outlook Web Access (OWA) in Microsoft Exchange 5.5, SP4 and earlier, allows remote attackers to identify valid user email addresses by directly accessing a back-end function that processes the global address list (GAL).
CVE-2001-0662(PUBLISHED):RPC endpoint mapper in Windows NT 4.0 allows remote attackers to cause a denial of service (loss of RPC services) via a malformed request.
CVE-2001-0663(PUBLISHED):Terminal Server in Windows NT and Windows 2000 allows remote attackers to cause a denial of service via a sequence of invalid Remote Desktop Protocol (RDP) packets.
CVE-2001-0664(PUBLISHED):Internet Explorer 5.5 and 5.01 allows remote attackers to bypass security restrictions via malformed URLs that contain dotless IP addresses, which causes Internet Explorer to process the page in the Intranet Zone, which may have fewer security restrictions, aka the "Zone Spoofing vulnerability."
CVE-2001-0665(PUBLISHED):Internet Explorer 6 and earlier allows remote attackers to cause certain HTTP requests to be automatically executed and appear to come from the user, which could allow attackers to gain privileges or execute operations within web-based services, aka the "HTTP Request Encoding vulnerability."
CVE-2001-0666(PUBLISHED):Outlook Web Access (OWA) in Microsoft Exchange 2000 allows an authenticated user to cause a denial of service (CPU consumption) via a malformed OWA request for a deeply nested folder within the user's mailbox.
CVE-2001-0667(PUBLISHED):Internet Explorer 6 and earlier, when used with the Telnet client in Services for Unix (SFU) 2.0, allows remote attackers to execute commands by spawning Telnet with a log file option on the command line and writing arbitrary code into an executable file which is later executed, aka a new variant of the Telnet Invocation vulnerability as described in CVE-2001-0150.
CVE-2001-0668(PUBLISHED):Buffer overflow in line printer daemon (rlpdaemon) in HP-UX 10.01 through 11.11 allows remote attackers to execute arbitrary commands.
CVE-2001-0669(PUBLISHED):Various Intrusion Detection Systems (IDS) including (1) Cisco Secure Intrusion Detection System, (2) Cisco Catalyst 6000 Intrusion Detection System Module, (3) Dragon Sensor 4.x, (4) Snort before 1.8.1, (5) ISS RealSecure Network Sensor 5.x and 6.x before XPU 3.2, and (6) ISS RealSecure Server Sensor 5.5 and 6.0 for Windows, allow remote attackers to evade detection of HTTP attacks via non-standard "%u" Unicode encoding of ASCII characters in the requested URL.
CVE-2001-0670(PUBLISHED):Buffer overflow in BSD line printer daemon (in.lpd or lpd) in various BSD-based operating systems allows remote attackers to execute arbitrary code via an incomplete print job followed by a request to display the printer queue.
CVE-2001-0671(PUBLISHED):Buffer overflows in (1) send_status, (2) kill_print, and (3) chk_fhost in lpd in AIX 4.3 and 5.1 allow remote attackers to gain root privileges.
CVE-2001-0674(PUBLISHED):Directory traversal vulnerability in RobTex Viking Web server before 1.07-381 allows remote attackers to read arbitrary files via a hexadecimal encoded dot-dot attack (eg. http://www.server.com/%2e%2e/%2e%2e) in an HTTP URL request.
CVE-2001-0675(PUBLISHED):Rit Research Labs The Bat! 1.51 for Windows allows a remote attacker to cause a denial of service by sending an email to a user's account containing a carriage return <CR> that is not followed by a line feed <LF>.
CVE-2001-0676(PUBLISHED):Directory traversal vulnerability in Rit Research Labs The Bat! 1.48f and earlier allows a remote attacker to create arbitrary files via a "dot dot" attack in the filename for an attachment.
CVE-2001-0677(PUBLISHED):Eudora 5.0.2 allows a remote attacker to read arbitrary files via an email with the path of the target file in the "Attachment Converted" MIME header, which sends the file when the email is forwarded to the attacker by the user.
CVE-2001-0678(PUBLISHED):A buffer overflow in reggo.dll file used by Trend Micro InterScan VirusWall prior to 3.51 build 1349 for Windows NT 3.5 and InterScan WebManager 1.2 allows a local attacker to execute arbitrary code.
CVE-2001-0679(PUBLISHED):A buffer overflow in InterScan VirusWall 3.23 and 3.3 allows a remote attacker to execute arbitrary code by sending a long HELO command to the server.
CVE-2001-0680(PUBLISHED):Directory traversal vulnerability in ftpd in QPC QVT/Net 4.0 and AVT/Term 5.0 allows a remote attacker to traverse directories on the web server via a "dot dot" attack in a LIST (ls) command.
CVE-2001-0681(PUBLISHED):Buffer overflow in ftpd in QPC QVT/Net 5.0 and QVT/Term 5.0 allows a remote attacker to cause a denial of service via a long (1) username or (2) password.
CVE-2001-0682(PUBLISHED):ZoneAlarm and ZoneAlarm Pro allows a local attacker to cause a denial of service by running a trojan to initialize a ZoneAlarm mutex object which prevents ZoneAlarm from starting.
CVE-2001-0683(PUBLISHED):Memory leak in Netscape Collabra Server 3.5.4 and earlier allows a remote attacker to cause a denial of service (memory exhaustion) by repeatedly sending approximately 5K of data to TCP port 5238.
CVE-2001-0684(PUBLISHED):Netscape Collabra Server 3.5.4 and earlier allows a remote attacker to cause a denial of service by sending seven or more characters to TCP port 5239.
CVE-2001-0685(PUBLISHED):Thibault Godouet FCron prior to 1.1.1 allows a local user to corrupt another user's crontab file via a symlink attack on the fcrontab temporary file.
CVE-2001-0686(PUBLISHED):Buffer overflow in mail included with SunOS 5.8 for x86 allows a local user to gain privileges via a long HOME environment variable.
CVE-2001-0687(PUBLISHED):Broker FTP server 5.9.5 for Windows NT and 9x allows a remote attacker to retrieve privileged web server system information by (1) issuing a CD command (CD C:) followed by the LS command, (2) specifying arbitrary paths in the UNC format (\\computername\sharename).
CVE-2001-0688(PUBLISHED):Broker FTP Server 5.9.5.0 allows a remote attacker to cause a denial of service by repeatedly issuing an invalid CD or CWD ("CD . .") command.
CVE-2001-0689(PUBLISHED):Vulnerability in TrendMicro Virus Control System 1.8 allows a remote attacker to view configuration files and change the configuration via a certain CGI program.
CVE-2001-0690(PUBLISHED):Format string vulnerability in exim (3.22-10 in Red Hat, 3.12 in Debian and 3.16 in Conectiva) in batched SMTP mode allows a remote attacker to execute arbitrary code via format strings in SMTP mail headers.
CVE-2001-0691(PUBLISHED):Buffer overflows in Washington University imapd 2000a through 2000c could allow local users without shell access to execute code as themselves in certain configurations.
CVE-2001-0692(PUBLISHED):SMTP proxy in WatchGuard Firebox (2500 and 4500) 4.5 and 4.6 allows a remote attacker to bypass firewall filtering via a base64 MIME encoded email attachment whose boundary name ends in two dashes.
CVE-2001-0693(PUBLISHED):WebTrends HTTP Server 3.1c and 3.5 allows a remote attacker to view script source code via a filename followed by an encoded space (%20).
CVE-2001-0694(PUBLISHED):Directory traversal vulnerability in WFTPD 3.00 R5 allows a remote attacker to view arbitrary files via a dot dot attack in the CD command.
CVE-2001-0695(PUBLISHED):WFTPD 3.00 R5 allows a remote attacker to cause a denial of service by making repeated requests to cd to the floppy drive (A:\).
CVE-2001-0696(PUBLISHED):NetWin SurgeFTP 2.0a and 1.0b allows a remote attacker to cause a denial of service (crash) via a CD command to a directory with an MS-DOS device name such as con.
CVE-2001-0697(PUBLISHED):NetWin SurgeFTP prior to 1.1h allows a remote attacker to cause a denial of service (crash) via an 'ls ..' command.
CVE-2001-0698(PUBLISHED):Directory traversal vulnerability in NetWin SurgeFTP 2.0a and 1.0b allows a remote attacker to list arbitrary files and directories via the 'nlist ...' command.
CVE-2001-0699(PUBLISHED):Buffer overflow in cb_reset in the System Service Processor (SSP) package of SunOS 5.8 allows a local user to execute arbitrary code via a long argument.
CVE-2001-0700(PUBLISHED):Buffer overflow in w3m 0.2.1 and earlier allows a remote attacker to execute arbitrary code via a long base64 encoded MIME header.
CVE-2001-0701(PUBLISHED):Buffer overflow in ptexec in the Sun Validation Test Suite 4.3 and earlier allows a local user to gain privileges via a long -o argument.
CVE-2001-0702(PUBLISHED):Cerberus FTP 1.5 and earlier allows remote attackers to cause a denial of service, and possibly execute arbitrary code, via a long (1) username, (2) password, or (3) PASV command.
CVE-2001-0703(PUBLISHED):tradecli.dll in Arcadia Internet Store 1.0 allows a remote attacker to cause a denial of service via a URL request with an MS-DOS device name in the template parameter.
CVE-2001-0704(PUBLISHED):tradecli.dll in Arcadia Internet Store 1.0 allows a remote attacker to discover the full path to the working directory via a URL with a template argument for a file that does not exist.
CVE-2001-0705(PUBLISHED):Directory traversal vulnerability in tradecli.dll in Arcadia Internet Store 1.0 allows a remote attacker to read arbitrary files on the web server via a URL with "dot dot" sequences in the template argument.
CVE-2001-0706(PUBLISHED):Maximum Rumpus FTP Server 2.0.3 dev and before allows an attacker to cause a denial of service (crash) via a mkdir command that specifies a large number of sub-folders.
CVE-2001-0707(PUBLISHED):Denicomp RSHD 2.18 and earlier allows a remote attacker to cause a denial of service (crash) via a long string to port 514.
CVE-2001-0708(PUBLISHED):Denicomp REXECD 1.05 and earlier allows a remote attacker to cause a denial of service (crash) via a long string.
CVE-2001-0709(PUBLISHED):Microsoft IIS 4.0 and before, when installed on a FAT partition, allows a remote attacker to obtain source code of ASP files via a URL encoded with Unicode.
CVE-2001-0710(PUBLISHED):NetBSD 1.5 and earlier and FreeBSD 4.3 and earlier allows a remote attacker to cause a denial of service by sending a large number of IP fragments to the machine, exhausting the mbuf pool.
CVE-2001-0711(PUBLISHED):Cisco IOS 11.x and 12.0 with ATM support allows attackers to cause a denial of service via the undocumented Interim Local Management Interface (ILMI) SNMP community string.
CVE-2001-0712(PUBLISHED):The rendering engine in Internet Explorer determines the MIME type independently of the type that is specified by the server, which allows remote servers to automatically execute script which is placed in a file whose MIME type does not normally support scripting, such as text (.txt), JPEG (.jpg), etc.
CVE-2001-0713(PUBLISHED):Sendmail before 8.12.1 does not properly drop privileges when the -C option is used to load custom configuration files, which allows local users to gain privileges via malformed arguments in the configuration file whose names contain characters with the high bit set, such as (1) macro names that are one character long, (2) a variable setting which is processed by the setoption function, or (3) a Modifiers setting which is processed by the getmodifiers function.
CVE-2001-0714(PUBLISHED):Sendmail before 8.12.1, without the RestrictQueueRun option enabled, allows local users to cause a denial of service (data loss) by (1) setting a high initial message hop count option (-h), which causes Sendmail to drop queue entries, (2) via the -qR option, or (3) via the -qS option.
CVE-2001-0715(PUBLISHED):Sendmail before 8.12.1, without the RestrictQueueRun option enabled, allows local users to obtain potentially sensitive information about the mail queue by setting debugging flags to enable debug mode.
CVE-2001-0716(PUBLISHED):Citrix MetaFrame 1.8 Server with Service Pack 3, and XP Server Service Pack 1 and earlier, allows remote attackers to cause a denial of service (crash) via a large number of incomplete connections to the server.
CVE-2001-0717(PUBLISHED):Format string vulnerability in ToolTalk database server rpc.ttdbserverd allows remote attackers to execute arbitrary commands via format string specifiers that are passed to the syslog function.
CVE-2001-0718(PUBLISHED):Vulnerability in (1) Microsoft Excel 2002 and earlier and (2) Microsoft PowerPoint 2002 and earlier allows attackers to bypass macro restrictions and execute arbitrary commands by modifying the data stream in the document.
CVE-2001-0719(PUBLISHED):Buffer overflow in Microsoft Windows Media Player 6.4 allows remote attackers to execute arbitrary code via a malformed Advanced Streaming Format (ASF) file.
CVE-2001-0720(PUBLISHED):Internet Explorer 5.1 for Macintosh on Mac OS X allows remote attackers to execute arbitrary commands by causing a BinHex or MacBinary file type to be downloaded, which causes the files to be executed if automatic decoding is enabled.
CVE-2001-0721(PUBLISHED):Universal Plug and Play (UPnP) in Windows 98, 98SE, ME, and XP allows remote attackers to cause a denial of service (memory consumption or crash) via a malformed UPnP request.
CVE-2001-0722(PUBLISHED):Internet Explorer 5.5 and 6.0 allows remote attackers to read and modify user cookies via Javascript in an about: URL, aka the "First Cookie Handling Vulnerability."
CVE-2001-0723(PUBLISHED):Internet Explorer 5.5 and 6.0 allows remote attackers to read and modify user cookies via Javascript, aka the "Second Cookie Handling Vulnerability."
CVE-2001-0724(PUBLISHED):Internet Explorer 5.5 allows remote attackers to bypass security restrictions via malformed URLs that contain dotless IP addresses, which causes Internet Explorer to process the page in the Intranet Zone, which may have fewer security restrictions, aka the "Zone Spoofing Vulnerability variant" of CVE-2001-0664.
CVE-2001-0726(PUBLISHED):Outlook Web Access (OWA) in Microsoft Exchange 5.5 Server, when used with Internet Explorer, does not properly detect certain inline script, which can allow remote attackers to perform arbitrary actions on a user's Exchange mailbox via an HTML e-mail message.
CVE-2001-0727(PUBLISHED):Internet Explorer 6.0 allows remote attackers to execute arbitrary code by modifying the Content-Disposition and Content-Type header fields in a way that causes Internet Explorer to believe that the file is safe to open without prompting the user, aka the "File Execution Vulnerability."
CVE-2001-0728(PUBLISHED):Buffer overflow in Compaq Management Agents before 5.2, included in Compaq Web-enabled Management Software, allows local users to gain privileges.
CVE-2001-0729(PUBLISHED):Apache 1.3.20 on Windows servers allows remote attackers to bypass the default index page and list directory contents via a URL with a large number of / (slash) characters.
CVE-2001-0730(PUBLISHED):split-logfile in Apache 1.3.20 allows remote attackers to overwrite arbitrary files that end in the .log extension via an HTTP request with a / (slash) in the Host: header.
CVE-2001-0731(PUBLISHED):Apache 1.3.20 with Multiviews enabled allows remote attackers to view directory contents and bypass the index page via a URL containing the "M=D" query string.
CVE-2001-0733(PUBLISHED):The #sinclude directive in Embedded Perl (ePerl) 2.2.14 and earlier allows a remote attacker to execute arbitrary code by modifying the 'sinclude' file to point to another file that contains a #include directive that references a file that contains the code.
CVE-2001-0734(PUBLISHED):Hitachi Super-H architecture in NetBSD 1.5 and 1.4.1 allows a local user to gain privileges via modified Status Register contents, which are not properly handled by (1) the sigreturn system call or (2) the process_write_regs kernel routine.
CVE-2001-0735(PUBLISHED):Buffer overflow in cfingerd 1.4.3 and earlier with the ALLOW_LINE_PARSING option enabled allows local users to execute arbitrary code via a long line in the .nofinger file.
CVE-2001-0736(PUBLISHED):Vulnerability in (1) pine before 4.33 and (2) the pico editor, included with pine, allows local users local users to overwrite arbitrary files via a symlink attack.
CVE-2001-0737(PUBLISHED):A long 'synch' delay in Logitech wireless mice and keyboard receivers allows a remote attacker to hijack connections via a man-in-the-middle attack.
CVE-2001-0738(PUBLISHED):LogLine function in klogd in sysklogd 1.3 in various Linux distributions allows an attacker to cause a denial of service (hang) by causing null bytes to be placed in log messages.
CVE-2001-0739(PUBLISHED):Guardian Digital WebTool in EnGarde Secure Linux 1.0.1 allows restarted services to inherit some environmental variables, which could allow local users to gain root privileges.
CVE-2001-0740(PUBLISHED):3COM OfficeConnect 812 and 840 ADSL Router 4.2, running OCR812 router software 1.1.9 and earlier, allows remote attackers to cause a denial of service via a long string containing a large number of "%s" strings, possibly triggering a format string vulnerability.
CVE-2001-0741(PUBLISHED):Cisco Hot Standby Routing Protocol (HSRP) allows local attackers to cause a denial of service by spoofing HSRP packets.
CVE-2001-0742(PUBLISHED):Buffer overflow in Computalynx CMail POP3 mail server 2.4.9 allows remote attackers to run arbitrary code via a long HELO command.
CVE-2001-0743(PUBLISHED):Paging function in O'Reilly WebBoard Pager 4.10 allows remote attackers to cause a denial of service via a message with an escaped ' character followed by JavaScript commands.
CVE-2001-0744(PUBLISHED):Horde IMP 2.2.4 and earlier allows local users to overwrite files via a symlink attack on a temporary file.
CVE-2001-0745(PUBLISHED):Netscape 4.7x allows remote attackers to obtain sensitive information such as the user's login, mailbox location and installation path via Javascript that accesses the mailbox: URL in the document.referrer property.
CVE-2001-0746(PUBLISHED):Buffer overflow in Web Publisher in iPlanet Web Server Enterprise Edition 4.1 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via a request for a long URI with (1) GETPROPERTIES, (2) GETATTRIBUTENAMES, or other methods.
CVE-2001-0747(PUBLISHED):Buffer overflow in iPlanet Web Server (iWS) Enterprise Edition 4.1, service packs 3 through 7, allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long method name in an HTTP request.
CVE-2001-0748(PUBLISHED):Acme.Serve 1.7, as used in Cisco Secure ACS Unix and possibly other products, allows remote attackers to read arbitrary files by prepending several / (slash) characters to the URI.
CVE-2001-0749(PUBLISHED):Beck IPC GmbH IPC@CHIP Embedded-Webserver allows remote attackers to read arbitrary files via a webserver root directory set to system root.
CVE-2001-0750(PUBLISHED):Cisco IOS 12.1(2)T, 12.1(3)T allow remote attackers to cause a denial of service (reload) via a connection to TCP ports 3100-3999, 5100-5999, 7100-7999 and 10100-10999.
CVE-2001-0751(PUBLISHED):Cisco switches and routers running CBOS 2.3.8 and earlier use predictable TCP Initial Sequence Numbers (ISN), which allows remote attackers to spoof or hijack TCP connections.
CVE-2001-0752(PUBLISHED):Cisco CBOS 2.3.8 and earlier allows remote attackers to cause a denial of service via an ICMP ECHO REQUEST (ping) with the IP Record Route option set.
CVE-2001-0753(PUBLISHED):Cisco CBOS 2.3.8 and earlier stores the passwords for (1) exec and (2) enable in cleartext in the NVRAM and a configuration file, which could allow unauthorized users to obtain the passwords and gain privileges.
CVE-2001-0754(PUBLISHED):Cisco CBOS 2.3.8 and earlier allows remote attackers to cause a denial of service via a series of large ICMP ECHO REPLY (ping) packets, which cause it to enter ROMMON mode and stop forwarding packets.
CVE-2001-0755(PUBLISHED):Buffer overflow in ftp daemon (ftpd) 6.2 in Debian GNU/Linux allows attackers to cause a denial of service and possibly execute arbitrary code via a long SITE command.
CVE-2001-0756(PUBLISHED):CatalogMgr.pl in VirtualCatalog (incorrectly claimed to be in VirtualCart) allows remote attackers to execute arbitrary code via the template parameter.
CVE-2001-0757(PUBLISHED):Cisco 6400 Access Concentrator Node Route Processor 2 (NRP2) 12.1DC card does not properly disable access when a password has not been set for vtys, which allows remote attackers to obtain access via telnet.
CVE-2001-0758(PUBLISHED):Directory traversal vulnerability in Shambala 4.5 allows remote attackers to escape the FTP root directory via "CWD ..."  command.
CVE-2001-0759(PUBLISHED):Buffer overflow in bctool in Jetico BestCrypt 0.8.1 and earlier allows local users to execute arbitrary code via a file or directory with a long pathname, which is processed during an unmount.
CVE-2001-0760(PUBLISHED):Citrix Nfuse 1.51 allows remote attackers to obtain the absolute path of the web root via a malformed request to launch.asp that does not provide the session field.
CVE-2001-0761(PUBLISHED):Buffer overflow in HttpSave.dll in Trend Micro InterScan WebManager 1.2 allows remote attackers to execute arbitrary code via a long value to a certain parameter.
CVE-2001-0762(PUBLISHED):Buffer overflow in su-wrapper 1.1.1 allows local users to execute arbitrary code via a long first argument.
CVE-2001-0763(PUBLISHED):Buffer overflow in Linux xinetd 2.1.8.9pre11-1 and earlier may allow remote attackers to execute arbitrary code via a long ident response, which is not properly handled by the svc_logprint function.
CVE-2001-0764(PUBLISHED):Buffer overflow in ntping in scotty 2.1.0 allows local users to execute arbitrary code via a long hostname as a command line argument.
CVE-2001-0765(PUBLISHED):BisonFTP V4R1 allows local users to access directories outside of their home directory by uploading .bdl files, which can then be linked to other directories.
CVE-2001-0766(PUBLISHED):Apache on MacOS X Client 10.0.3 with the HFS+ file system allows remote attackers to bypass access restrictions via a URL that contains some characters whose case is not matched by Apache's filters.
CVE-2001-0767(PUBLISHED):Directory traversal vulnerability in GuildFTPd 0.9.7 allows attackers to list or read arbitrary files and directories via a .. in (1) LS or (2) GET.
CVE-2001-0768(PUBLISHED):GuildFTPd 0.9.7 stores user names and passwords in plaintext in the default.usr file, which allows local users to gain privileges as other FTP users by reading the file.
CVE-2001-0769(PUBLISHED):Memory leak in GuildFTPd Server 0.97 allows remote attackers to cause a denial of service via a request containing a null character.
CVE-2001-0770(PUBLISHED):Buffer overflow in GuildFTPd Server 0.97 allows remote attacker to execute arbitrary code via a long SITE command.
CVE-2001-0771(PUBLISHED):Spytech SpyAnywhere 1.50 allows remote attackers to gain administrator access via a single character in the "loginpass" field.
CVE-2001-0772(PUBLISHED):Buffer overflows and other vulnerabilities in multiple Common Desktop Environment (CDE) modules in HP-UX 10.10 through 11.11 allow attackers to cause a denial of service and possibly gain additional privileges.
CVE-2001-0773(PUBLISHED):Cayman 3220-H DSL Router 1.0 allows remote attacker to cause a denial of service (crash) via a series of SYN or TCP connect requests.
CVE-2001-0774(PUBLISHED):Tripwire 1.3.1, 2.2.1 and 2.3.0 allows local users to overwrite arbitrary files and possible gain privileges via a symbolic link attack on temporary files.
CVE-2001-0775(PUBLISHED):Buffer overflow in xloadimage 4.1 (aka xli 1.16 and 1.17) in Linux allows remote attackers to execute arbitrary code via a FACES format image containing a long (1) Firstname or (2) Lastname field.
CVE-2001-0776(PUBLISHED):Buffer overflow in DynFX MailServer version 2.10 allows remote attackers to conduct a denial of service via a long username to the POP3 service.
CVE-2001-0777(PUBLISHED):Omnicron OmniHTTPd 2.0.8 allows remote attackers to cause a denial of service (memory exhaustion) via a series of requests for PHP scripts.
CVE-2001-0778(PUBLISHED):OmniHTTPd 2.0.8 and earlier allow remote attackers to obtain source code via a GET request with the URL-encoded symbol for a space (%20).
CVE-2001-0779(PUBLISHED):Buffer overflow in rpc.yppasswdd (yppasswd server) in Solaris 2.6, 7 and 8 allows remote attackers to gain root access via a long username.
CVE-2001-0780(PUBLISHED):Directory traversal vulnerability in cosmicpro.cgi in Cosmicperl Directory Pro 2.0 allows remote attackers to gain sensitive information via a .. (dot dot) in the SHOW parameter.
CVE-2001-0781(PUBLISHED):Buffer overflow in SpoonFTP ******** allows remote attackers to execute arbitrary code via a long argument to the commands (1) CWD or (2) LIST.
CVE-2001-0782(PUBLISHED):KDE ktvision 0.1.1-271 and earlier allows local attackers to gain root privileges via a symlink attack on a user configuration file.
CVE-2001-0783(PUBLISHED):Cisco TFTP server 1.1 allows remote attackers to read arbitrary files via a ..(dot dot) attack in the GET command.
CVE-2001-0784(PUBLISHED):Directory traversal vulnerability in Icecast 1.3.10 and earlier allows remote attackers to read arbitrary files via a modified .. (dot dot)  attack using encoded URL characters.
CVE-2001-0785(PUBLISHED):Directory traversal in Webpaging interface in Internet Software Solutions Air Messenger LAN Server (AMLServer) 3.4.2 allows allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2001-0786(PUBLISHED):Internet Software Solutions Air Messenger LAN Server (AMLServer) 3.4.2 stores user passwords in plaintext in the pUser.Dat file.
CVE-2001-0787(PUBLISHED):LPRng in Red Hat Linux 7.0 and 7.1 does not properly drop memberships in supplemental groups when lowering privileges, which could allow a local user to elevate privileges.
CVE-2001-0788(PUBLISHED):Internet Software Solutions Air Messenger LAN Server (AMLServer) 3.4.2 allows remote attackers to obtain an absolute path for the server directory by viewing the Location header.
CVE-2001-0789(PUBLISHED):Format string vulnerability in avpkeeper in Kaspersky KAV ********* for Sendmail allows remote attackers to cause a denial of service or possibly execute arbitrary code via a malformed mail message.
CVE-2001-0790(PUBLISHED):Specter IDS version 4.5 and 5.0 allows a remote attacker to cause a denial of service (CPU exhaustion) via a port scan, which causes the server to consume CPU while preparing alerts.
CVE-2001-0791(PUBLISHED):Trend Micro InterScan VirusWall for Windows NT allows remote attackers to make configuration changes by directly calling certain CGI programs, which do not restrict access.
CVE-2001-0792(PUBLISHED):Format string vulnerability in XChat 1.2.x allows remote attackers to execute arbitrary code via a malformed nickname.
CVE-2001-0794(PUBLISHED):Buffer overflow in A-FTP Anonymous FTP Server allows remote attackers to cause a denial of service via a long USER command.
CVE-2001-0795(PUBLISHED):Perception LiteServe 1.25 allows remote attackers to obtain source code of CGI scripts via URLs that contain MS-DOS conventions such as (1) upper case letters or (2) 8.3 file names.
CVE-2001-0796(PUBLISHED):SGI IRIX 6.5 through 6.5.12f and possibly earlier versions, and FreeBSD 3.0, allows remote attackers to cause a denial of service via a malformed IGMP multicast packet with a small response delay.
CVE-2001-0797(PUBLISHED):Buffer overflow in login in various System V based operating systems allows remote attackers to execute arbitrary commands via a large number of arguments through services such as telnet and rlogin.
CVE-2001-0799(PUBLISHED):Buffer overflows in lpsched in IRIX 6.5.13f and earlier allow remote attackers to execute arbitrary commands via a long argument.
CVE-2001-0800(PUBLISHED):lpsched in IRIX 6.5.13f and earlier allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2001-0801(PUBLISHED):lpstat in IRIX 6.5.13f and earlier allows local users to gain root privileges by specifying a Trojan Horse nettype shared library.
CVE-2001-0803(PUBLISHED):Buffer overflow in the client connection routine of libDtSvc.so.1 in CDE Subprocess Control Service (dtspcd) allows remote attackers to execute arbitrary commands.
CVE-2001-0804(PUBLISHED):Directory traversal vulnerability in story.pl in Interactive Story 1.3 allows a remote attacker to read arbitrary files via a .. (dot dot) attack on the "next" parameter.
CVE-2001-0805(PUBLISHED):Directory traversal vulnerability in ttawebtop.cgi in Tarantella Enterprise 3.00 and 3.01 allows remote attackers to read arbitrary files via a .. (dot dot) in the pg parameter.
CVE-2001-0806(PUBLISHED):Apple MacOS X 10.0 and 10.1 allow a local user to read and write to a user's desktop folder via insecure default permissions for the Desktop when it is created in some languages.
CVE-2001-0807(PUBLISHED):Internet Explorer 5.0, and possibly other versions, may allow remote attackers (malicious web pages) to read known text files from a client's hard drive via a SCRIPT tag with a SRC value that points to the text file.
CVE-2001-0808(PUBLISHED):gnatsweb.pl in GNATS GnatsWeb 2.7 through 3.95 allows remote attackers to execute arbitrary commands via certain characters in the help_file parameter.
CVE-2001-0809(PUBLISHED):Vulnerability in CIFS/9000 Server (SAMBA) A.01.06 and earlier in HP-UX 11.0 and 11.11, when configured as a print server, allows local users to overwrite arbitrary files by modifying certain resources.
CVE-2001-0815(PUBLISHED):Buffer overflow in PerlIS.dll in Activestate ActivePerl 5.6.1.629 and earlier allows remote attackers to execute arbitrary code via an HTTP request for a long filename that ends in a .pl extension.
CVE-2001-0816(PUBLISHED):OpenSSH before 2.9.9, when running sftp using sftp-server and using restricted keypairs, allows remote authenticated users to bypass authorized_keys2 command= restrictions using sftp commands.
CVE-2001-0817(PUBLISHED):Vulnerability in HP-UX line printer daemon (rlpdaemon) in HP-UX 10.01 through 11.11 allows remote attackers to modify arbitrary files and gain root privileges via a certain print request.
CVE-2001-0818(PUBLISHED):A buffer overflow the '\s' console command in MDBMS 0.99b9 and earlier allows remote attackers to execute arbitrary commands by sending the command a large amount of data.
CVE-2001-0819(PUBLISHED):A buffer overflow in Linux fetchmail before 5.8.6 allows remote attackers to execute arbitrary code via a large 'To:' field in an email header.
CVE-2001-0820(PUBLISHED):Buffer overflows in GazTek ghttpd 1.4 allows a remote attacker to execute arbitrary code via long arguments that are passed to (1) the Log function in util.c, or (2) serveconnection in protocol.c.
CVE-2001-0821(PUBLISHED):The default configuration of DCShop 1.002 beta places sensitive files in the cgi-bin directory, which could allow remote attackers to read sensitive data via an HTTP GET request for (1) orders.txt or (2) auth_user_file.txt.
CVE-2001-0822(PUBLISHED):FPF kernel module 1.0 allows a remote attacker to cause a denial of service via fragmented packets.
CVE-2001-0823(PUBLISHED):The pmpost program in Performance Co-Pilot (PCP) before 2.2.1-3 allows a local user to gain privileges via a symlink attack on the NOTICES file in the PCP log directory (PCP_LOG_DIR).
CVE-2001-0824(PUBLISHED):Cross-site scripting vulnerability in IBM WebSphere 3.02 and 3.5 FP2 allows remote attackers to execute Javascript by inserting the Javascript into (1) a request for a .JSP file, or (2) a request to the webapp/examples/ directory, which inserts the Javascript into an error page.
CVE-2001-0825(PUBLISHED):Buffer overflow in internal string handling routines of xinetd before ******* allows remote attackers to execute arbitrary commands via a length argument of zero or less, which disables the length check.
CVE-2001-0826(PUBLISHED):Buffer overflows in CesarFTPD 0.98b allows remote attackers to execute arbitrary commands via long arguments to (1) HELP, (2) USER, (3) PASS, (4) PORT, (5) DELE, (6) REST, (7) RMD, or (8) MKD.
CVE-2001-0827(PUBLISHED):Cerberus FTP server 1.0 - 1.5 allows remote attackers to cause a denial of service (crash) via a large number of "PASV" requests.
CVE-2001-0828(PUBLISHED):A cross-site scripting vulnerability in Caucho Technology Resin before 1.2.4 allows a malicious webmaster to embed Javascript in a hyperlink that ends in a .jsp extension, which causes an error message that does not properly quote the Javascript.
CVE-2001-0829(PUBLISHED):A cross-site scripting vulnerability in Apache Tomcat 3.2.1 allows a malicious webmaster to embed Javascript in a request for a .JSP file, which causes the Javascript to be inserted into an error message.
CVE-2001-0830(PUBLISHED):6tunnel 0.08 and earlier does not properly close sockets that were initiated by a client, which allows remote attackers to cause a denial of service (resource exhaustion) by repeatedly connecting to and disconnecting from the server.
CVE-2001-0831(PUBLISHED):Unknown vulnerability in Oracle Label Security in Oracle 8.1.7 and 9.0.1, when audit functionality, SET_LABEL, or SQL*Predicate is being used, allows local users to gain additional access.
CVE-2001-0832(PUBLISHED):Vulnerability in Oracle 8.0.x through 9.0.1 on Unix allows local users to overwrite arbitrary files, possibly via a symlink attack or incorrect file permissions in (1) the ORACLE_HOME/rdbms/log directory or (2) an alternate directory as specified in the ORACLE_HOME environmental variable, aka the "Oracle File Overwrite Security Vulnerability."
CVE-2001-0833(PUBLISHED):Buffer overflow in otrcrep in Oracle 8.0.x through 9.0.1 allows local users to execute arbitrary code via a long ORACLE_HOME environment variable, aka the "Oracle Trace Collection Security Vulnerability."
CVE-2001-0834(PUBLISHED):htsearch CGI program in htdig (ht://Dig) 3.1.5 and earlier allows remote attackers to use the -c option to specify an alternate configuration file, which could be used to (1) cause a denial of service (CPU consumption) by specifying a large file such as /dev/zero, or (2) read arbitrary files by uploading an alternate configuration file that specifies the target file.
CVE-2001-0835(PUBLISHED):Cross-site scripting vulnerability in Webalizer 2.01-06, and possibly other versions, allows remote attackers to inject arbitrary HTML tags by specifying them in (1) search keywords embedded in HTTP referrer information, or (2) host names that are retrieved via a reverse DNS lookup.
CVE-2001-0836(PUBLISHED):Buffer overflow in Oracle9iAS Web Cache ******* allows remote attackers to execute arbitrary code via a long HTTP GET request.
CVE-2001-0837(PUBLISHED):DeltaThree Pc-To-Phone 3.0.3 places sensitive data in world-readable locations in the installation directory, which allows local users to read the information in (1) temp.html, (2) the log folder, and (3) the PhoneBook folder.
CVE-2001-0838(PUBLISHED):Format string vulnerability in Network Solutions Rwhoisd 1.5.x allows remote attackers to execute arbitrary code via format string specifiers in the -soa command.
CVE-2001-0839(PUBLISHED):ibillpm.pl in iBill password management system generates weak passwords based on a client's MASTER_ACCOUNT, which allows remote attackers to modify account information in the .htpasswd file via brute force password guessing.
CVE-2001-0840(PUBLISHED):Buffer overflow in Compaq Insight Manager XE 2.1b and earlier allows remote attackers to execute arbitrary code via (1) SNMP and (2) DMI.
CVE-2001-0841(PUBLISHED):Directory traversal vulnerability in Search.cgi in Ikonboard ib219 and earlier allows remote attackers to overwrite files and gain privileges via .. (dot dot) sequences in the amembernamecookie cookie.
CVE-2001-0842(PUBLISHED):Directory traversal vulnerability in Search.cgi in Leoboard LB5000 LB5000II 1029 and earlier allows remote attackers to overwrite files and gain privileges via .. (dot dot) sequences in the amembernamecookie cookie.
CVE-2001-0843(PUBLISHED):Squid proxy server 2.4 and earlier allows remote attackers to cause a denial of service (crash) via a mkdir-only FTP PUT request.
CVE-2001-0844(PUBLISHED):Vulnerability in (1) Book of guests and (2) Post it! allows remote attackers to execute arbitrary code via shell metacharacters in the email parameter.
CVE-2001-0845(PUBLISHED):Vulnerability in DECwindows Motif Server on OpenVMS VAX or Alpha 6.2 through 7.3, and SEVMS VAX or Alpha 6.2, allows local users to gain access to unauthorized resources.
CVE-2001-0846(PUBLISHED):Lotus Domino 5.x allows remote attackers to read files or execute arbitrary code by requesting the ReplicaID of the Web Administrator template file (webadmin.ntf).
CVE-2001-0847(PUBLISHED):Lotus Domino Web Server 5.x allows remote attackers to gain sensitive information by accessing the default navigator $defaultNav via (1) URL encoding the request, or (2) directly requesting the ReplicaID.
CVE-2001-0848(PUBLISHED):join.cfm in e-Zone Media Fuse Talk allows a local user to execute arbitrary SQL code via a semi-colon (;) in a form variable.
CVE-2001-0849(PUBLISHED):viralator CGI script in Viralator 0.9pre1 and earlier allows remote attackers to execute arbitrary code via a URL for a file being downloaded, which is insecurely passed to a call to wget.
CVE-2001-0850(PUBLISHED):A configuration error in the libdb1 package in OpenLinux 3.1 uses insecure versions of the snprintf and vsnprintf functions, which could allow local or remote users to exploit those functions with a buffer overflow.
CVE-2001-0851(PUBLISHED):Linux kernel 2.0, 2.2 and 2.4 with syncookies enabled allows remote attackers to bypass firewall rules by brute force guessing the cookie.
CVE-2001-0852(PUBLISHED):TUX HTTP server 2.1.0-2 in Red Hat Linux allows remote attackers to cause a denial of service via a long Host: header.
CVE-2001-0853(PUBLISHED):Directory traversal vulnerability in Entrust GetAccess allows remote attackers to read arbitrary files via a .. (dot dot) in the locale parameter to (1) helpwin.gas.bat or (2) AboutBox.gas.bat.
CVE-2001-0854(PUBLISHED):PHP-Nuke 5.2 allows remote attackers to copy and delete arbitrary files by calling case.filemanager.php with admin.php as an argument, which sets the $PHP_SELF variable and makes it appear that case.filemanager.php is being called by admin.php instead of the user.
CVE-2001-0855(PUBLISHED):Buffer overflow in db_loader in ClearCase 4.2 and earlier allows local users to gain root privileges via a long TERM environment variable.
CVE-2001-0856(PUBLISHED):Common Cryptographic Architecture (CCA) in IBM 4758 allows an attacker with physical access to the system and Combine_Key_Parts permissions, to steal DES and 3DES keys by using a brute force attack to create a 3DES exporter key.
CVE-2001-0857(PUBLISHED):Cross-site scripting vulnerability in status.php3 in Imp Webmail 2.2.6 and earlier allows remote attackers to gain access to the e-mail of other users by hijacking session cookies via the message parameter.
CVE-2001-0858(PUBLISHED):Buffer overflow in pppattach and other linked PPP utilities in Caldera Open Unix 8.0 and UnixWare 7.1.0 and 7.1.1 allows local users to gain privileges.
CVE-2001-0859(PUBLISHED):2.4.3-12 kernel in Red Hat Linux 7.1 Korean installation program sets the setting default umask for init to 000, which installs files with world-writeable permissions.
CVE-2001-0860(PUBLISHED):Terminal Services Manager MMC in Windows 2000 and XP trusts the Client Address (IP address) that is provided by the client instead of obtaining it from the packet headers, which allows clients to spoof their public IP address, e.g. through a Network Address Translation (NAT).
CVE-2001-0861(PUBLISHED):Cisco 12000 with IOS 12.0 and line cards based on Engine 2 and earlier allows remote attackers to cause a denial of service (CPU consumption) by flooding the router with traffic that generates a large number of ICMP Unreachable replies.
CVE-2001-0862(PUBLISHED):Cisco 12000 with IOS 12.0 and line cards based on Engine 2 does not block non-initial packet fragments, which allows remote attackers to bypass the ACL.
CVE-2001-0863(PUBLISHED):Cisco 12000 with IOS 12.0 and line cards based on Engine 2 does not handle the "fragment" keyword in a compiled ACL (Turbo ACL) for packets that are sent to the router, which allows remote attackers to cause a denial of service via a flood of fragments.
CVE-2001-0864(PUBLISHED):Cisco 12000 with IOS 12.0 and line cards based on Engine 2 does not properly handle the implicit "deny ip any any" rule in an outgoing ACL when the ACL contains exactly 448 entries, which can allow some outgoing packets to bypass access restrictions.
CVE-2001-0865(PUBLISHED):Cisco 12000 with IOS 12.0 and line cards based on Engine 2 does not support the "fragment" keyword in an outgoing ACL, which could allow fragmented packets in violation of the intended access.
CVE-2001-0866(PUBLISHED):Cisco 12000 with IOS 12.0 and lines card based on Engine 2 does not properly handle an outbound ACL when an input ACL is not configured on all the interfaces of a multi port line card, which could allow remote attackers to bypass the intended access controls.
CVE-2001-0867(PUBLISHED):Cisco 12000 with IOS 12.0 and line cards based on Engine 2 does not properly filter does not properly filter packet fragments even when the "fragment" keyword is used in an ACL, which allows remote attackers to bypass the intended access controls.
CVE-2001-0868(PUBLISHED):Red Hat Stronghold 2.3 to 3.0 allows remote attackers to retrieve system information via an HTTP GET request to (1) stronghold-info or (2) stronghold-status.
CVE-2001-0869(PUBLISHED):Format string vulnerability in the default logging callback function _sasl_syslog in common.c in Cyrus SASL library (cyrus-sasl) may allow remote attackers to execute arbitrary commands.
CVE-2001-0870(PUBLISHED):HTTP server in Alchemy Eye and Alchemy Network Monitor 1.9x through 2.6.18 is enabled without authentication by default, which allows remote attackers to obtain network monitoring logs with potentially sensitive information by directly requesting the eye.ini file.
CVE-2001-0871(PUBLISHED):Directory traversal vulnerability in HTTP server for Alchemy Eye and Alchemy Network Monitor allows remote attackers to execute arbitrary commands via an HTTP request containing (1) a .. in versions 2.0 through 2.6.18, or (2) a DOS device name followed by a .. in versions 2.6.19 through 3.0.10.
CVE-2001-0872(PUBLISHED):OpenSSH 3.0.1 and earlier with UseLogin enabled does not properly cleanse critical environment variables such as LD_PRELOAD, which allows local users to gain root privileges.
CVE-2001-0873(PUBLISHED):uuxqt in Taylor UUCP package does not properly remove dangerous long options, which allows local users to gain privileges by calling uux and specifying an alternate configuration file with the --config option.
CVE-2001-0874(PUBLISHED):Internet Explorer 5.5 and 6.0 allow remote attackers to read certain files via HTML that passes information from a frame in the client's domain to a frame in the web site's domain, a variant of the "Frame Domain Verification" vulnerability.
CVE-2001-0875(PUBLISHED):Internet Explorer 5.5 and 6.0 allows remote attackers to cause the File Download dialogue box to misrepresent the name of the file in the dialogue in a way that could fool users into thinking that the file type is safe to download.
CVE-2001-0876(PUBLISHED):Buffer overflow in Universal Plug and Play (UPnP) on Windows 98, 98SE, ME, and XP allows remote attackers to execute arbitrary code via a NOTIFY directive with a long Location URL.
CVE-2001-0877(PUBLISHED):Universal Plug and Play (UPnP) on Windows 98, 98SE, ME, and XP allows remote attackers to cause a denial of service via (1) a spoofed SSDP advertisement that causes the client to connect to a service on another machine that generates a large amount of traffic (e.g., chargen), or (2) via a spoofed SSDP announcement to broadcast or multicast addresses, which could cause all UPnP clients to send traffic to a single target system.
CVE-2001-0879(PUBLISHED):Format string vulnerability in the C runtime functions in SQL Server 7.0 and 2000 allows attackers to cause a denial of service.
CVE-2001-0884(PUBLISHED):Cross-site scripting vulnerability in Mailman email archiver before 2.08 allows attackers to obtain sensitive information or authentication credentials via a malicious link that is accessed by other web users.
CVE-2001-0886(PUBLISHED):Buffer overflow in glob function of glibc allows attackers to cause a denial of service (crash) and possibly execute arbitrary code via a glob pattern that ends in a brace "{" character.
CVE-2001-0887(PUBLISHED):xSANE 0.81 and earlier allows local users to modify files of other xSANE users via a symlink attack on temporary files.
CVE-2001-0888(PUBLISHED):Atmel Firmware 1.3 Wireless Access Point (WAP) allows remote attackers to cause a denial of service via a SNMP request with (1) a community string other than "public" or (2) an unknown OID, which causes the WAP to deny subsequent SNMP requests.
CVE-2001-0889(PUBLISHED):Exim 3.22 and earlier, in some configurations, does not properly verify the local part of an address when redirecting the address to a pipe, which could allow remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2001-0890(PUBLISHED):Certain backend drivers in the SANE library 1.0.3 and earlier, as used in frontend software such as XSane, allows local users to modify files via a symlink attack on temporary files.
CVE-2001-0891(PUBLISHED):Format string vulnerability in NQS daemon (nqsdaemon) in NQE ******** for CRAY UNICOS and SGI IRIX allows a local user to gain root privileges by using qsub to submit a batch job whose name contains formatting characters.
CVE-2001-0892(PUBLISHED):Acme Thttpd Secure Webserver before 2.22, with the chroot option enabled, allows remote attackers to view sensitive files under the document root (such as .htpasswd) via a GET request with a trailing /.
CVE-2001-0893(PUBLISHED):Acme mini_httpd before 1.16 allows remote attackers to view sensitive files under the document root (such as .htpasswd) via a GET request with a trailing /.
CVE-2001-0894(PUBLISHED):Vulnerability in Postfix SMTP server before 20010228-pl07, when configured to email the postmaster when SMTP errors cause the session to terminate, allows remote attackers to cause a denial of service (memory exhaustion) by generating a large number of SMTP errors, which forces the SMTP session log to grow too large.
CVE-2001-0895(PUBLISHED):Multiple Cisco networking products allow remote attackers to cause a denial of service on the local network via a series of ARP packets sent to the router's interface that contains a different MAC address for the router, which eventually causes the router to overwrite the MAC address in its ARP table.
CVE-2001-0896(PUBLISHED):Inetd in OpenServer 5.0.5 allows remote attackers to cause a denial of service (crash) via a port scan, e.g. with nmap -PO.
CVE-2001-0897(PUBLISHED):Cross-site scripting vulnerability in Infopop Ultimate Bulletin Board (UBB) before 5.47e allows remote attackers to steal user cookies via an [IMG] tag that references an about: URL with an onerror field.
CVE-2001-0898(PUBLISHED):Opera 6.0 and earlier allows remote attackers to access sensitive information such as cookies and links for other domains via Javascript that uses setTimeout to (1) access data after a new window to the domain has been opened or (2) access data via about:cache.
CVE-2001-0899(PUBLISHED):Network Tools 0.2 for PHP-Nuke allows remote attackers to execute commands on the server via shell metacharacters in the $hostinput variable.
CVE-2001-0900(PUBLISHED):Directory traversal vulnerability in modules.php in Gallery before 1.2.3 allows remote attackers to read arbitrary files via a .. (dot dot) in the include parameter.
CVE-2001-0901(PUBLISHED):Hypermail allows remote attackers to execute arbitrary commands on a server supporting SSI via an attachment with a .shtml extension, which is archived on the server and can then be executed by requesting the URL for the attachment.
CVE-2001-0902(PUBLISHED):Microsoft IIS 5.0 allows remote attackers to spoof web log entries via an HTTP request that includes hex-encoded newline or form-feed characters.
CVE-2001-0903(PUBLISHED):Linear key exchange process in High-bandwidth Digital Content Protection (HDCP) System allows remote attackers to access data as plaintext, avoid device blacklists, clone devices, and create new device keyvectors by computing and using alternate key combinations for authentication.
CVE-2001-0904(PUBLISHED):Internet Explorer 5.5 and 6 with the Q312461 (MS01-055) patch modifies the HTTP_USER_AGENT (UserAgent) information that indicates that the patch has been installed, which could allow remote malicious web sites to more easily identify and exploit vulnerable clients.
CVE-2001-0905(PUBLISHED):Race condition in signal handling of procmail 3.20 and earlier, when running setuid, allows local users to cause a denial of service or gain root privileges by sending a signal while a signal handling routine is already running.
CVE-2001-0906(PUBLISHED):teTeX filter before 1.0.7 allows local users to gain privileges via a symlink attack on temporary files that are produced when printing .dvi files using lpr.
CVE-2001-0907(PUBLISHED):Linux kernel 2.2.1 through 2.2.19, and 2.4.1 through 2.4.10, allows local users to cause a denial of service via a series of deeply nested symlinks, which causes the kernel to spend extra time when trying to access the link.
CVE-2001-0908(PUBLISHED):CITRIX Metaframe 1.8 logs the Client Address (IP address) that is provided by the client instead of obtaining it from the packet headers, which allows clients to spoof their public IP address, e.g. through Network Address Translation (NAT).
CVE-2001-0909(PUBLISHED):Buffer overflow in helpctr.exe program in Microsoft Help Center for Windows XP allows remote attackers to execute arbitrary code via a long hcp: URL.
CVE-2001-0910(PUBLISHED):Legato Networker before 6.1 allows remote attackers to bypass access restrictions and gain privileges on the Networker interface by spoofing the admin server name and IP address and connecting to Networker from an IP address whose hostname can not be determined by a DNS reverse lookup.
CVE-2001-0911(PUBLISHED):PHP-Nuke 5.1 stores user and administrator passwords in a base-64 encoded cookie, which could allow remote attackers to gain privileges by stealing or sniffing the cookie and decoding it.
CVE-2001-0912(PUBLISHED):Packaging error for expect 8.3.3 in Mandrake Linux 8.1 causes expect to search for its libraries in the /home/<USER>
CVE-2001-0913(PUBLISHED):Format string vulnerability in Network Solutions Rwhoisd ******* and earlier, when using syslog, allows remote attackers to corrupt memory and possibly execute arbitrary code via a rwhois request that contains format specifiers.
CVE-2001-0914(PUBLISHED):Linux kernel before 2.4.11pre3 in multiple Linux distributions allows local users to cause a denial of service (crash) by starting the core vmlinux kernel, possibly related to poor error checking during ELF loading.
CVE-2001-0915(PUBLISHED):Format string vulnerability in Berkeley parallel make (pmake) 2.1.33 and earlier allows a local user to gain root privileges via format specifiers in the check argument of a shell definition.
CVE-2001-0916(PUBLISHED):Buffer overflow in Berkeley parallel make (pmake) 2.1.33 and earlier allows a local user to gain root privileges via a long check argument of a shell definition.
CVE-2001-0917(PUBLISHED):Jakarta Tomcat 4.0.1 allows remote attackers to reveal physical path information by requesting a long URL with a .JSP extension.
CVE-2001-0918(PUBLISHED):Vulnerabilities in CGI scripts in susehelp in SuSE 7.2 and 7.3 allow remote attackers to execute arbitrary commands by not opening files securely.
CVE-2001-0919(PUBLISHED):Internet Explorer 5.50.4134.0100 on Windows ME with "Prompt to allow cookies to be stored on your machine" enabled does not warn a user when a cookie is set using Javascript.
CVE-2001-0920(PUBLISHED):Format string vulnerability in auto nice daemon (AND) 1.0.4 and earlier allows a local user to possibly execute arbitrary code via a process name containing a format string.
CVE-2001-0921(PUBLISHED):Netscape 4.79 and earlier for MacOS allows an attacker with access to the browser to obtain passwords from form fields by printing the document into which the password has been typed, which is printed in cleartext.
CVE-2001-0922(PUBLISHED):ndcgi.exe in Netdynamics 4.x through 5.x, and possibly earlier versions, allows remote attackers to steal session IDs and hijack user sessions by reading the SPIDERSESSION and uniqueValue variables from the login field, then using those variables after the next user logs in.
CVE-2001-0923(PUBLISHED):RPM Package Manager 4.0.x through 4.0.2.x allows an attacker to execute arbitrary code via corrupted data in the RPM file when the file is queried.
CVE-2001-0924(PUBLISHED):Directory traversal vulnerability in ifx CGI program in Informix Web DataBlade allows remote attackers to read arbitrary files via a .. (dot dot) in the LO parameter.
CVE-2001-0925(PUBLISHED):The default installation of Apache before 1.3.19 allows remote attackers to list directories instead of the multiview index.html file via an HTTP request for a path that contains many / (slash) characters, which causes the path to be mishandled by (1) mod_negotiation, (2) mod_dir, or (3) mod_autoindex.
CVE-2001-0926(PUBLISHED):SSIFilter in Allaire JRun 3.1, 3.0 and 2.3.3 allows remote attackers to obtain source code for Java server pages (.jsp) and other files in the web root via an HTTP request for a non-existent SSI page, in which the request's body has an #include statement.
CVE-2001-0927(PUBLISHED):Format string vulnerability in the permitted function of GNOME libgtop_daemon in libgtop 1.0.12 and earlier allows remote attackers to execute arbitrary code via an argument that contains format specifiers that are passed into the (1) syslog_message and (2) syslog_io_message functions.
CVE-2001-0928(PUBLISHED):Buffer overflow in the permitted function of GNOME gtop daemon (libgtop_daemon) in libgtop 1.0.13 and earlier may allow remote attackers to execute arbitrary code via long authentication data.
CVE-2001-0929(PUBLISHED):Cisco IOS Firewall Feature set, aka Context Based Access Control (CBAC) or Cisco Secure Integrated Software, for IOS 11.2P through 12.2T does not properly check the IP protocol type, which could allow remote attackers to bypass access control lists.
CVE-2001-0930(PUBLISHED):Sendpage.pl allows remote attackers to execute arbitrary commands via a message containing shell metacharacters.
CVE-2001-0931(PUBLISHED):Directory traversal vulnerability in Cooolsoft PowerFTP Server 2.03 allows attackers to list or read arbitrary files and directories via a .. (dot dot) in (1) LS or (2) GET.
CVE-2001-0932(PUBLISHED):Buffer overflow in Cooolsoft PowerFTP Server 2.03 allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long command.
CVE-2001-0933(PUBLISHED):Cooolsoft PowerFTP Server 2.03 allows remote attackers to list the contents of arbitrary drives via a ls (LIST) command that includes the drive letter as an argument, e.g. "ls C:".
CVE-2001-0934(PUBLISHED):Cooolsoft PowerFTP Server 2.03 allows remote attackers to obtain the physical path of the server root via the pwd command, which lists the full pathname.
CVE-2001-0935(PUBLISHED):Vulnerability in wu-ftpd 2.6.0, and possibly earlier versions, which is unrelated to the ftpglob bug described in CVE-2001-0550.
CVE-2001-0936(PUBLISHED):Buffer overflow in Frox transparent FTP proxy 0.6.6 and earlier, with the local caching method selected, allows remote FTP servers to run arbitrary code via a long response to an MDTM request.
CVE-2001-0937(PUBLISHED):PGPMail.pl 1.31 allows remote attackers to execute arbitrary commands via shell metacharacters in the (1) recipient or (2) pgpuserid parameters.
CVE-2001-0938(PUBLISHED):Directory traversal vulnerability in AspUpload 2.1, in certain configurations, allows remote attackers to upload and read arbitrary files, and list arbitrary directories, via a .. (dot dot) in the Filename parameter in (1) UploadScript11.asp or (2) DirectoryListing.asp.
CVE-2001-0939(PUBLISHED):Lotus Domino 5.08 and earlier allows remote attackers to cause a denial of service (crash) via a SunRPC NULL command to port 443.
CVE-2001-0940(PUBLISHED):Buffer overflow in the GUI authentication code of Check Point VPN-1/FireWall-1 Management Server 4.0 and 4.1 allows remote attackers to execute arbitrary code via a long user name.
CVE-2001-0941(PUBLISHED):Buffer overflow in dbsnmp in Oracle 8.0.6 through 9.0.1 allows local users to execute arbitrary code via a long ORACLE_HOME environment variable.
CVE-2001-0942(PUBLISHED):dbsnmp in Oracle 8.1.6 and 8.1.7 uses the ORACLE_HOME environment variable to find and execute the dbsnmp program, which allows local users to execute arbitrary programs by pointing the ORACLE_HOME to an alternate directory that contains a malicious version of dbsnmp.
CVE-2001-0943(PUBLISHED):dbsnmp in Oracle 8.0.5 and 8.1.5, under certain conditions, trusts the PATH environment variable to find and execute the (1) chown or (2) chgrp commands, which allows local users to execute arbitrary code by modifying the PATH to point to Trojan Horse programs.
CVE-2001-0944(PUBLISHED):DDE in mIRC allows local users to launch applications under another user's account via a DDE message that executes a command, which may be executed by the other user's process.
CVE-2001-0945(PUBLISHED):Buffer overflow in Outlook Express 5.0 through 5.02 for Macintosh allows remote attackers to cause a denial of service via an e-mail message that contains a long line.
CVE-2001-0946(PUBLISHED):apmscript in Apmd in Red Hat 7.2 "Enigma" allows local users to create or change the modification dates of arbitrary files via a symlink attack on the LOW_POWER temporary file, which could be used to cause a denial of service, e.g. by creating /etc/nologin and disabling logins.
CVE-2001-0947(PUBLISHED):Forms.exe CGI program in ValiCert Enterprise Validation Authority (EVA) 3.3 through 4.2.1 allows remote attackers to determine the real pathname of the server by requesting an invalid extension, which produces an error page that includes the path.
CVE-2001-0948(PUBLISHED):Cross-site scripting (CSS) vulnerability in ValiCert Enterprise Validation Authority (EVA) 3.3 through 4.2.1 allows remote attackers to execute arbitrary code or display false information by including HTML or script in the certificate's description, which is executed when the certificate is viewed.
CVE-2001-0949(PUBLISHED):Buffer overflows in forms.exe CGI program in ValiCert Enterprise Validation Authority (EVA) Administration Server 3.3 through 4.2.1 allows remote attackers to execute arbitrary code via long arguments to the parameters (1) Mode, (2) Certificate_File, (3) useExpiredCRLs, (4) listenLength, (5) maxThread, (6) maxConnPerSite, (7) maxMsgLen, (8) exitTime, (9) blockTime, (10) nextUpdatePeriod, (11) buildLocal, (12) maxOCSPValidityPeriod, (13) extension, and (14) a particular combination of parameters associated with private key generation that form a string of a certain length.
CVE-2001-0950(PUBLISHED):ValiCert Enterprise Validation Authority (EVA) Administration Server 3.3 through 4.2.1 uses insufficiently random data to (1) generate session tokens for HSMs using the C rand function, or (2) generate certificates or keys using /dev/urandom instead of another source which blocks when the entropy pool is low, which could make it easier for local or remote attackers to steal tokens or certificates via brute force guessing.
CVE-2001-0951(PUBLISHED):Windows 2000 allows remote attackers to cause a denial of service (CPU consumption) by flooding Internet Key Exchange (IKE) UDP port 500 with packets that contain a large number of dot characters.
CVE-2001-0952(PUBLISHED):THQ Volition Red Faction Game allows remote attackers to cause a denial of service (hang) of a client or server via packets to UDP port 7755.
CVE-2001-0953(PUBLISHED):Kebi WebMail allows remote attackers to access the administrator menu and gain privileges via the /a/ hidden directory, which is installed under the web document root.
CVE-2001-0954(PUBLISHED):Lotus Domino 5.0.5 and 5.0.8, and possibly other versions, allows remote attackers to cause a denial of service (block access to databases that have not been previously accessed) via a URL that includes the . (dot) directory.
CVE-2001-0955(PUBLISHED):Buffer overflow in fbglyph.c in XFree86 before 4.2.0, related to glyph clipping for large origins, allows attackers to cause a denial of service and possibly gain privileges via a large number of characters, possibly through the web page search form of KDE Konqueror or from an xterm command with a long title.
CVE-2001-0956(PUBLISHED):speechd 0.54 and earlier, with the Festival or rsynth speech synthesis package, allows attackers to execute arbitrary commands via shell metacharacters.
CVE-2001-0958(PUBLISHED):Buffer overflows in eManager plugin for Trend Micro InterScan VirusWall for NT 3.51 and 3.51J allow remote attackers to execute arbitrary code via long arguments to the CGI programs (1) register.dll, (2) ContentFilter.dll, (3) SFNofitication.dll, (4) register.dll, (5) TOP10.dll, (6) SpamExcp.dll, and (7) spamrule.dll.
CVE-2001-0959(PUBLISHED):Computer Associates ARCserve for NT 6.61 SP2a and ARCserve 2000 7.0 creates a hidden share named ARCSERVE$, which allows remote attackers to obtain sensitive information and overwrite critical files.
CVE-2001-0960(PUBLISHED):Computer Associates ARCserve for NT 6.61 SP2a and ARCserve 2000 7.0 stores the backup agent user name and password in cleartext in the aremote.dmp file in the ARCSERVE$ hidden share, which allows local and remote attackers to gain privileges.
CVE-2001-0961(PUBLISHED):Buffer overflow in tab expansion capability of the most program allows local or remote attackers to execute arbitrary code via a malformed file that is viewed with most.
CVE-2001-0962(PUBLISHED):IBM WebSphere Application Server 3.02 through 3.53 uses predictable session IDs for cookies, which allows remote attackers to gain privileges of WebSphere users via brute force guessing.
CVE-2001-0963(PUBLISHED):Directory traversal vulnerability in SpoonFTP 1.1 allows local and sometimes remote attackers to access files outside of the FTP root via a ... (modified dot dot) in the CD (CWD) command.
CVE-2001-0964(PUBLISHED):Buffer overflow in client for Half-Life 1.1.0.8 and earlier allows malicious remote servers to execute arbitrary code via a long console command.
CVE-2001-0965(PUBLISHED):glFTPD 1.23 allows remote attackers to cause a denial of service (CPU consumption) via a LIST command with an argument that contains a large number of * (asterisk) characters.
CVE-2001-0966(PUBLISHED):Directory traversal vulnerability in Nudester 1.10 and earlier allows remote attackers to read or write arbitrary files via a .. (dot dot) in the CD (CWD) command.
CVE-2001-0967(PUBLISHED):Knox Arkeia server 4.2, and possibly other versions, uses a constant salt when encrypting passwords using the crypt() function, which makes it easier for an attacker to conduct brute force password guessing.
CVE-2001-0968(PUBLISHED):Knox Arkeia server 4.2, and possibly other versions, installs its root user with a null password by default, which allows local and remote users to gain privileges.
CVE-2001-0969(PUBLISHED):ipfw in FreeBSD does not properly handle the use of "me" in its rules when point to point interfaces are used, which causes ipfw to allow connections from arbitrary remote hosts.
CVE-2001-0970(PUBLISHED):Cross-site scripting vulnerability in TDForum 1.2 CGI script (tdforum12.cgi) allows remote attackers to execute arbitrary script on other clients via a forum message that contains the script.
CVE-2001-0971(PUBLISHED):Directory traversal vulnerability in ACI 4d webserver allows remote attackers to read arbitrary files via a .. (dot dot) or drive letter (e.g., C:) in an HTTP request.
CVE-2001-0972(PUBLISHED):Surf-Net ASP Forum before 2.30 uses easily guessable cookies based on the UserID, which allows remote attackers to gain administrative privileges by calculating the value of the admin cookie (UserID 1), i.e. "0888888."
CVE-2001-0973(PUBLISHED):BSCW groupware system 3.3 through 4.0.2 beta allows remote attackers to read or modify arbitrary files by uploading and extracting a tar file with a symlink into the data-bag space.
CVE-2001-0974(PUBLISHED):Format string vulnerabilities in Oracle Internet Directory Server (LDAP) 2.1.1.x and 3.0.1 allow remote attackers to execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-0975(PUBLISHED):Buffer overflow vulnerabilities in Oracle Internet Directory Server (LDAP) 2.1.1.x and 3.0.1 allow remote attackers to execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-0976(PUBLISHED):Vulnerability in HP Process Resource Manager (PRM) C.01.08.2 and earlier, as used by HP-UX Workload Manager (WLM), allows local users to gain root privileges via modified libraries or environment variables.
CVE-2001-0977(PUBLISHED):slapd in OpenLDAP 1.x before 1.2.12, and 2.x before 2.0.8, allows remote attackers to cause a denial of service (crash) via an invalid Basic Encoding Rules (BER) length field.
CVE-2001-0978(PUBLISHED):login in HP-UX 10.26 does not record failed login attempts in /var/adm/btmp, which could allow attackers to conduct brute force password guessing attacks without being detected or observed using the lastb program.
CVE-2001-0979(PUBLISHED):Buffer overflow in swverify in HP-UX 11.0, and possibly other programs, allows local users to gain privileges via a long command line argument.
CVE-2001-0980(PUBLISHED):docview before 1.0-15 allows remote attackers to execute arbitrary commands via shell metacharacters that are processed when converting a man page to a web page.
CVE-2001-0981(PUBLISHED):HP CIFS/9000 Server (SAMBA) A.01.07 and earlier with the "unix password sync" option enabled calls the passwd program without specifying the username of the user making the request, which could cause the server to change the password of a different user.
CVE-2001-0982(PUBLISHED):Directory traversal vulnerability in IBM Tivoli WebSEAL Policy Director 3.01 through 3.7.1 allows remote attackers to read arbitrary files or directories via encoded .. (dot dot) sequences containing "%2e" strings.
CVE-2001-0983(PUBLISHED):UltraEdit uses weak encryption to record FTP passwords in the uedit32.ini file, which allows local users who can read the file to decrypt the passwords and gain privileges.
CVE-2001-0984(PUBLISHED):Password Safe 1.7(1) leaves cleartext passwords in memory when a user copies the password to the clipboard and minimizes Password Safe with the "Clear the password when minimized" and "Lock password database on minimize and prompt on restore" options enabled, which could allow an attacker with access to the memory (e.g. an administrator) to read the passwords.
CVE-2001-0985(PUBLISHED):shop.pl in Hassan Consulting Shopping Cart 1.23 allows remote attackers to execute arbitrary commands via shell metacharacters in the "page" parameter.
CVE-2001-0986(PUBLISHED):SQLQHit.asp sample file in Microsoft Index Server 2.0 allows remote attackers to obtain sensitive information such as the physical path, file attributes, or portions of source code by directly calling sqlqhit.asp with a CiScope parameter set to (1) webinfo, (2) extended_fileinfo, (3) extended_webinfo, or (4) fileinfo.
CVE-2001-0987(PUBLISHED):Cross-site scripting vulnerability in CGIWrap before 3.7 allows remote attackers to execute arbitrary Javascript on other web clients by causing the Javascript to be inserted into error messages that are generated by CGIWrap.
CVE-2001-0988(PUBLISHED):Arkeia backup server 4.2.8-2 and earlier creates its database files with world-writable permissions, which could allow local users to overwrite the files or obtain sensitive information.
CVE-2001-0989(PUBLISHED):Buffer overflows in Pileup before 1.2 allows local users to gain root privileges via (1) long command line arguments, or (2) a long callsign.
CVE-2001-0990(PUBLISHED):Inter7 vpopmail 4.10.35 and earlier, when using the MySQL module, compiles authentication information in cleartext into the libvpopmail.a library, which allows local users to obtain the MySQL username and password by inspecting the vpopmail programs that use the library.
CVE-2001-0991(PUBLISHED):Cross-site scripting vulnerability in Proxomitron Naoko-4 BetaFour and earlier allows remote attackers to execute arbitrary script on other clients via an incorrect URL containing the malicious script, which is printed back in an error message.
CVE-2001-0992(PUBLISHED):shopplus.cgi in ShopPlus shopping cart allows remote attackers to execute arbitrary commands via shell metacharacters in the "file" parameter.
CVE-2001-0993(PUBLISHED):sendmsg function in NetBSD 1.3 through 1.5 allows local users to cause a denial of service (kernel trap or panic) via a msghdr structure with a large msg_controllen length.
CVE-2001-0994(PUBLISHED):Marconi ForeThought 7.1 allows remote attackers to cause a denial of service by causing both telnet sessions to be locked via unusual input (e.g., from a port scanner), which prevents others from logging into the device.
CVE-2001-0995(PUBLISHED):PHProjekt before 2.4a allows remote attackers to perform actions as other PHProjekt users by modifying the ID number in an HTTP request to PHProjekt CGI programs.
CVE-2001-0996(PUBLISHED):POP3Lite before 0.2.4 does not properly quote a . (dot) in an email message, which could allow a remote attacker to append arbitrary text to the end of an email message, which could then be interpreted by various mail clients as valid POP server responses or other input that could cause clients to crash or otherwise behave unexpectedly.
CVE-2001-0997(PUBLISHED):Textor Webmasters Ltd listrec.pl CGI program allows remote attackers to execute arbitrary commands via shell metacharacters in the TEMPLATE parameter.
CVE-2001-0998(PUBLISHED):IBM HACMP 4.4 allows remote attackers to cause a denial of service via a completed TCP connection to HACMP ports (e.g., using a port scan) that does not send additional data, which causes a failure in snmpd.
CVE-2001-0999(PUBLISHED):Outlook Express 6.00 allows remote attackers to execute arbitrary script by embedding SCRIPT tags in a message whose MIME content type is text/plain, contrary to the expected behavior that text/plain messages will not run script.
CVE-2001-1000(PUBLISHED):rlmadmin RADIUS management utility in Merit AAA Server 3.8M, 5.01, and possibly other versions, allows local users to read arbitrary files via a symlink attack on the rlmadmin.help file.
CVE-2001-1002(PUBLISHED):The default configuration of the DVI print filter (dvips) in Red Hat Linux 7.0 and earlier does not run dvips in secure mode when dvips is executed by lpd, which could allow remote attackers to gain privileges by printing a DVI file that contains malicious commands.
CVE-2001-1003(PUBLISHED):Respondus 1.1.2 for WebCT uses weak encryption to remember usernames and passwords, which allows local users who can read the WEBCT.SVR file to decrypt the passwords and gain additional privileges.
CVE-2001-1004(PUBLISHED):Cross-site scripting (CSS) vulnerability in gnut Gnutella client before 0.4.27 allows remote attackers to execute arbitrary script on other clients by sharing a file whose name contains the script tags.
CVE-2001-1005(PUBLISHED):Starfish Truesync Desktop 2.0b as used on the REX 5000 PDA uses weak encryption to store the user password in a registry key, which allows attackers who have access to the registry key to decrypt the password and gain privileges.
CVE-2001-1006(PUBLISHED):Starfish Truesync Desktop 2.0b as used on the REX 5000 PDA does not encrypt sensitive files and relies solely on its password feature to restrict access, which allows an attacker to read the files using a different application.
CVE-2001-1007(PUBLISHED):Starfish Truesync Desktop 2.0b as used on the REX 5000 PDA uses a small keyspace for device keys and does not impose a delay when an incorrect key is entered, which allows attackers to more quickly guess the key via a brute force attack.
CVE-2001-1008(PUBLISHED):Java Plugin 1.4 for JRE 1.3 executes signed applets even if the certificate is expired, which could allow remote attackers to conduct unauthorized activities via an applet that has been signed by an expired certificate.
CVE-2001-1009(PUBLISHED):Fetchmail (aka fetchmail-ssl) before 5.8.17 allows a remote malicious (1) IMAP server or (2) POP/POP3 server to overwrite arbitrary memory and possibly gain privileges via a negative index number as part of a response to a LIST request.
CVE-2001-1010(PUBLISHED):Directory traversal vulnerability in pagecount CGI script in Sambar Server before 5.0 beta 5 allows remote attackers to overwrite arbitrary files via a .. (dot dot) attack on the page parameter.
CVE-2001-1011(PUBLISHED):index2.php in Mambo Site Server 3.0.0 through 3.0.5 allows remote attackers to gain Mambo administrator privileges by setting the PHPSESSID parameter and providing the appropriate administrator information in other parameters.
CVE-2001-1012(PUBLISHED):Vulnerability in screen before 3.9.10, related to a multi-attach error, allows local users to gain root privileges when there is a subdirectory under /tmp/screens/.
CVE-2001-1013(PUBLISHED):Apache on Red Hat Linux with with the UserDir directive enabled generates different error codes when a username exists and there is no public_html directory and when the username does not exist, which could allow remote attackers to determine valid usernames on the server.
CVE-2001-1014(PUBLISHED):eshop.pl in WebDiscount(e)shop allows remote attackers to execute arbitrary commands via shell metacharacters in the seite parameter.
CVE-2001-1015(PUBLISHED):Buffer overflow in Snes9x 1.37, when installed setuid root, allows local users to gain root privileges via a long command line argument.
CVE-2001-1016(PUBLISHED):PGP Corporate Desktop before 7.1, Personal Security before 7.0.3, Freeware before 7.0.3, and E-Business Server before 7.1 does not properly display when invalid userID's are used to sign a message, which could allow an attacker to make the user believe that the document has been signed by a trusted third party by adding a second, invalid user ID to a key which has already been signed by the third party, aka the "PGPsdk Key Validity Vulnerability."
CVE-2001-1017(PUBLISHED):rmuser utility in FreeBSD 4.2 and 4.3 creates a copy of the master.passwd file with world-readable permissions while updating the original file, which could allow local users to gain privileges by reading the copied file while rmuser is running, obtain the password hashes, and crack the passwords.
CVE-2001-1018(PUBLISHED):Lotus Domino web server 5.08 allows remote attackers to determine the internal IP address of the server when NAT is enabled via a GET request that contains a long sequence of / (slash) characters.
CVE-2001-1019(PUBLISHED):Directory traversal vulnerability in view_item CGI program in sglMerchant 1.0 allows remote attackers to read arbitrary files via a .. (dot dot) in the HTML_FILE parameter.
CVE-2001-1020(PUBLISHED):edit_image.php in Vibechild Directory Manager before 0.91 allows remote attackers to execute arbitrary commands via shell metacharacters in the userfile_name parameter, which is sent unfiltered to the PHP passthru function.
CVE-2001-1021(PUBLISHED):Buffer overflows in WS_FTP 2.02 allow remote attackers to execute arbitrary code via long arguments to (1) DELE, (2) MDTM, (3) MLST, (4) MKD, (5) RMD, (6) RNFR, (7) RNTO, (8) SIZE, (9) STAT, (10) XMKD, or (11) XRMD.
CVE-2001-1022(PUBLISHED):Format string vulnerability in pic utility in groff 1.16.1 and other versions, and jgroff before 1.15, allows remote attackers to bypass the -S option and execute arbitrary commands via format string specifiers in the plot command.
CVE-2001-1023(PUBLISHED):Xcache 2.1 allows remote attackers to determine the absolute path of web server documents by requesting a URL that is not cached by Xcache, which returns the full pathname in the Content-PageName header.
CVE-2001-1024(PUBLISHED):login.gas.bat and other CGI scripts in Entrust getAccess allow remote attackers to execute Java programs, and possibly arbitrary commands, by specifying an alternate -classpath argument.
CVE-2001-1025(PUBLISHED):PHP-Nuke 5.x allows remote attackers to perform arbitrary SQL operations by modifying the "prefix" variable when calling any scripts that do not already define the prefix variable (e.g., by including mainfile.php), such as article.php.
CVE-2001-1026(PUBLISHED):Trend Micro InterScan AppletTrap 2.0 does not properly filter URLs when they are modified in certain ways such as (1) using a double slash (//) instead of a single slash, (2) URL-encoded characters, (3) requesting the IP address instead of the domain name, or (4) using a leading 0 in an octet of an IP address.
CVE-2001-1027(PUBLISHED):Buffer overflow in WindowMaker (aka wmaker) 0.64 and earlier allows remote attackers to execute arbitrary code via a long window title.
CVE-2001-1028(PUBLISHED):Buffer overflow in ultimate_source function of man 1.5 and earlier allows local users to gain privileges.
CVE-2001-1029(PUBLISHED):libutil in OpenSSH on FreeBSD 4.4 and earlier does not drop privileges before verifying the capabilities for reading the copyright and welcome files, which allows local users to bypass the capabilities checks and read arbitrary files by specifying alternate copyright or welcome files.
CVE-2001-1030(PUBLISHED):Squid before 2.3STABLE5 in HTTP accelerator mode does not enable access control lists (ACLs) when the httpd_accel_host and http_accel_with_proxy off settings are used, which allows attackers to bypass the ACLs and conduct unauthorized activities such as port scanning.
CVE-2001-1031(PUBLISHED):Directory traversal vulnerability in Meteor FTP 1.0 allows remote attackers to read arbitrary files via (1) a .. (dot dot) in the ls/LIST command, or (2) a ... in the cd/CWD command.
CVE-2001-1032(PUBLISHED):admin.php in PHP-Nuke 5.2 and earlier, except 5.0RC1, does not check login credentials for upload operations, which allows remote attackers to copy and upload arbitrary files and read the PHP-Nuke configuration file by directly calling admin.php with an upload parameter and specifying the file to copy.
CVE-2001-1033(PUBLISHED):Compaq TruCluster 1.5 allows remote attackers to cause a denial of service via a port scan from a system that does not have a DNS PTR record, which causes the cluster to enter a "split-brain" state.
CVE-2001-1034(PUBLISHED):Format string vulnerability in Hylafax on FreeBSD allows local users to execute arbitrary code via format specifiers in the -h hostname argument for (1) faxrm or (2) faxalter.
CVE-2001-1035(PUBLISHED):Binary decoding feature of slrn 0.9 and earlier allows remote attackers to execute commands via shell scripts that are inserted into a news post.
CVE-2001-1036(PUBLISHED):GNU locate in findutils 4.1 on Slackware 7.1 and 8.0 allows local users to gain privileges via an old formatted filename database (locatedb) that contains an entry with an out-of-range offset, which causes locate to write to arbitrary process memory.
CVE-2001-1037(PUBLISHED):Cisco SN 5420 Storage Router 1.1(3) and earlier allows local users to access a developer's shell without a password and execute certain restricted commands without being logged.
CVE-2001-1038(PUBLISHED):Cisco SN 5420 Storage Router 1.1(3) and earlier allows remote attackers to cause a denial of service (reboot) via a series of connections to TCP port 8023.
CVE-2001-1039(PUBLISHED):The JetAdmin web interface for HP JetDirect does not set a password for the telnet interface when the admin password is changed, which allows remote attackers to gain access to the printer.
CVE-2001-1040(PUBLISHED):HP LaserJet, and possibly other JetDirect devices, resets the admin password when the device is turned off, which could allow remote attackers to access the device without the password.
CVE-2001-1041(PUBLISHED):oracle program in Oracle 8.0.x, 8.1.x and 9.0.1 allows local users to overwrite arbitrary files via a symlink attack on an Oracle log trace (.trc) file that is created in an alternate home directory identified by the ORACLE_HOME environment variable.
CVE-2001-1042(PUBLISHED):Transsoft Broker 5.9.5.0 allows remote attackers to read arbitrary files and directories by uploading a .lnk (link) file that points to the target file.
CVE-2001-1043(PUBLISHED):ArGoSoft FTP Server ******* allows remote attackers to read arbitrary files and directories by uploading a .lnk (link) file that points to the target file.
CVE-2001-1044(PUBLISHED):Basilix Webmail 0.9.7beta, and possibly other versions, stores *.class and *.inc files under the document root and does not restrict access, which could allows remote attackers to obtain sensitive information such as MySQL passwords and usernames from the mysql.class file.
CVE-2001-1045(PUBLISHED):Directory traversal vulnerability in basilix.php3 in Basilix Webmail 1.0.3beta and earlier allows remote attackers to read arbitrary files via a .. (dot dot) in the request_id[DUMMY] parameter.
CVE-2001-1046(PUBLISHED):Buffer overflow in qpopper (aka qpop or popper) 4.0 through 4.0.2 allows remote attackers to gain privileges via a long username.
CVE-2001-1047(PUBLISHED):Race condition in OpenBSD VFS allows local users to cause a denial of service (kernel panic) by (1) creating a pipe in one thread and causing another thread to set one of the file descriptors to NULL via a close, or (2) calling dup2 on a file descriptor in one process, then setting the descriptor to NULL via a close in another process that is created via rfork.
CVE-2001-1048(PUBLISHED):AWOL PHP script allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1049(PUBLISHED):Phorecast PHP script before 0.40 allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1050(PUBLISHED):CCCSoftware CCC PHP script allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1051(PUBLISHED):Dark Hart Portal (darkportal) PHP script allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1052(PUBLISHED):Empris PHP script allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1053(PUBLISHED):AdLogin.pm in AdCycle 1.15 and earlier allows remote attackers to bypass authentication and gain privileges by injecting SQL code in the $password argument.
CVE-2001-1054(PUBLISHED):PHPAdsNew PHP script allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1055(PUBLISHED):The Microsoft Windows network stack allows remote attackers to cause a denial of service (CPU consumption) via a flood of malformed ARP request packets with random source IP and MAC addresses, as demonstrated by ARPNuke.
CVE-2001-1056(PUBLISHED):IRC DCC helper in the ip_masq_irc IP masquerading module 2.2 allows remote attackers to bypass intended firewall restrictions by causing the target system to send a "DCC SEND" request to a malicious server which listens on port 6667, which may cause the module to believe that the traffic is a valid request and allow the connection to the port specified in the DCC SEND request.
CVE-2001-1057(PUBLISHED):The License Manager (mathlm) for Mathematica 4.0 and 4.1 allows remote attackers to cause a denial of service (resource exhaustion) by connecting to port 16286 and not disconnecting, which prevents users from making license requests.
CVE-2001-1058(PUBLISHED):The License Manager (mathlm) for Mathematica 4.0 and 4.1 allows remote attackers to bypass access control (specified by the -restrict argument) and steal a license via a client request that includes the name of a host that is allowed to obtain the license.
CVE-2001-1059(PUBLISHED):VMWare creates a temporary file vmware-log.USERNAME with insecure permissions, which allows local users to read or modify license information.
CVE-2001-1060(PUBLISHED):phpMyAdmin 2.2.0rc3 and earlier allows remote attackers to execute arbitrary commands by inserting them into (1) the strCopyTableOK argument in tbl_copy.php, or (2) the strRenameTableOK argument in tbl_rename.php.
CVE-2001-1061(PUBLISHED):Vulnerability in lsmcode in unknown versions of AIX, possibly related to a usage error.
CVE-2001-1062(PUBLISHED):Buffer overflow in mana in OpenServer 5.0.6a and earlier allows local users to execute arbitrary code.
CVE-2001-1063(PUBLISHED):Buffer overflow in uidadmin in Caldera Open Unix 8.0.0 and UnixWare 7 allows local users to gain root privileges via a long -S (scheme) command line argument.
CVE-2001-1064(PUBLISHED):Cisco 600 series routers running CBOS 2.0.1 through 2.4.2ap allows remote attackers to cause a denial of service via multiple connections to the router on the (1) HTTP or (2) telnet service, which causes the router to become unresponsive and stop forwarding packets.
CVE-2001-1065(PUBLISHED):Web-based configuration utility in Cisco 600 series routers running CBOS 2.0.1 through 2.4.2ap binds itself to port 80 even when web-based configuration services are disabled, which could leave the router open to attack.
CVE-2001-1066(PUBLISHED):ns6install installation script for Netscape 6.01 on Solaris, and other versions including 6.2.1 beta, allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-1067(PUBLISHED):Buffer overflow in AOLserver 3.0 allows remote attackers to cause a denial of service, and possibly execute arbitrary code, via an HTTP request with a long Authorization header.
CVE-2001-1068(PUBLISHED):qpopper 4.01 with PAM based authentication on Red Hat systems generates different error messages when an invalid username is provided instead of a valid name, which allows remote attackers to determine valid usernames on the system.
CVE-2001-1069(PUBLISHED):libCoolType library as used in Adobe Acrobat (acroread) on Linux creates the AdobeFnt.lst file with world-writable permissions, which allows local users to modify the file and possibly modify acroread's behavior.
CVE-2001-1070(PUBLISHED):Sage Software MAS 200 allows remote attackers to cause a denial of service by connecting to port 10000 and entering a series of control characters.
CVE-2001-1071(PUBLISHED):Cisco IOS 12.2 and earlier running Cisco Discovery Protocol (CDP) allows remote attackers to cause a denial of service (memory consumption) via a flood of CDP neighbor announcements.
CVE-2001-1072(PUBLISHED):Apache with mod_rewrite enabled on most UNIX systems allows remote attackers to bypass RewriteRules by inserting extra / (slash) characters into the requested path, which causes the regular expression in the RewriteRule to fail.
CVE-2001-1073(PUBLISHED):Webridge PX Application Suite allows remote attackers to obtain sensitive information via a malformed request that generates a server error message, which includes full pathname or internal IP address information in the variables (1) APPL_PHYSICAL_PATH, (2) PATH_TRANSLATED, and (3) LOCAL_ADDR.
CVE-2001-1074(PUBLISHED):Webmin 0.84 and earlier does not properly clear the HTTP_AUTHORIZATION environment variable when the web server is restarted, which makes authentication information available to all CGI programs and allows local users to gain privileges.
CVE-2001-1075(PUBLISHED):poprelayd script before 2.0 in Cobalt RaQ3 servers allows remote attackers to bypass authentication for relaying by causing a "POP login by user" string that includes the attacker's IP address to be injected into the maillog log file.
CVE-2001-1076(PUBLISHED):Buffer overflow in whodo in Solaris SunOS 5.5.1 through 5.8 allows local users to execute arbitrary code via a long (1) SOR or (2) CFIME environment variable.
CVE-2001-1077(PUBLISHED):Buffer overflow in tt_printf function of rxvt 2.6.2 allows local users to gain privileges via a long (1) -T or (2) -name argument.
CVE-2001-1078(PUBLISHED):Format string vulnerability in flog function of eXtremail 1.1.9 and earlier allows remote attackers to gain root privileges via format specifiers in the SMTP commands (1) HELO, (2) EHLO, (3) MAIL FROM, or (4) RCPT TO, and the POP3 commands (5) USER and (6) other commands that can be executed after POP3 authentication.
CVE-2001-1079(PUBLISHED):create_keyfiles in PSSP 3.2 with DCE 3.1 authentication on AIX creates keyfile directories with world-writable permissions, which could allow a local user to delete key files and cause a denial of service.
CVE-2001-1080(PUBLISHED):diagrpt in AIX 4.3.x and 5.1 uses the DIAGDATADIR environment variable to find and execute certain programs, which allows local users to gain privileges by modifying the variable to point to a Trojan horse program.
CVE-2001-1081(PUBLISHED):Format string vulnerabilities in Livingston/Lucent RADIUS before 2.1.va.1 may allow local or remote attackers to cause a denial of service and possibly execute arbitrary code via format specifiers that are injected into log messages.
CVE-2001-1082(PUBLISHED):Directory traversal vulnerability in Livingston/Lucent RADIUS before 2.1.va.1 may allow attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2001-1083(PUBLISHED):Icecast 1.3.7, and other versions before 1.3.11 with HTTP server file streaming support enabled allows remote attackers to cause a denial of service (crash) via a URL that ends in . (dot), / (forward slash), or \ (backward slash).
CVE-2001-1084(PUBLISHED):Cross-site scripting vulnerability in Allaire JRun 3.0 and 2.3.3 allows a malicious webmaster to embed Javascript in a request for a .JSP, .shtml, .jsp10, .jrun, or .thtml file that does not exist, which causes the Javascript to be inserted into an error message.
CVE-2001-1085(PUBLISHED):Lmail 2.7 and earlier allows local users to overwrite arbitrary files via a symlink attack on a temporary file.
CVE-2001-1086(PUBLISHED):XDM in XFree86 3.3 and 3.3.3 generates easily guessable cookies using gettimeofday() when compiled with the HasXdmXauth option, which allows remote attackers to gain unauthorized access to the X display via a brute force attack.
CVE-2001-1087(PUBLISHED):The default configuration of the config.http.tunnel.allow_ports option on NetCache devices is set to +all, which allows remote attackers to connect to arbitrary ports on remote systems behind the device.
CVE-2001-1088(PUBLISHED):Microsoft Outlook 8.5 and earlier, and Outlook Express 5 and earlier, with the "Automatically put people I reply to in my address book" option enabled, do not notify the user when the "Reply-To" address is different than the "From" address, which could allow an untrusted remote attacker to spoof legitimate addresses and intercept email from the client that is intended for another user.
CVE-2001-1089(PUBLISHED):libnss-pgsql in nss-pgsql 0.9.0 and earlier allows remote attackers to execute arbitrary SQL queries by inserting SQL code into an HTTP request.
CVE-2001-1090(PUBLISHED):nss_postgresql 0.6.1 and before allows a remote attacker to execute arbitrary SQL queries by inserting SQL code into an HTTP request.
CVE-2001-1091(PUBLISHED):The (1) dump and (2) dump_lfs commands in NetBSD 1.4.x through 1.5.1 do not properly drop privileges, which could allow local users to gain privileges via the RCMD_CMD environment variable.
CVE-2001-1092(PUBLISHED):msgchk in Digital UNIX 4.0G and earlier allows a local user to read the first line of arbitrary files via a symlink attack on the .mh_profile file.
CVE-2001-1093(PUBLISHED):Buffer overflow in msgchk in Digital UNIX 4.0G and earlier allows local users to execute arbitrary code via a long command line argument.
CVE-2001-1094(PUBLISHED):NetOp School 1.5 allows local users to bypass access restrictions on the administration version by logging into the student version, closing the student version, then starting the administration version.
CVE-2001-1095(PUBLISHED):Buffer overflow in uuq in AIX 4 could allow local users to execute arbitrary code via a long -r parameter.
CVE-2001-1096(PUBLISHED):Buffer overflows in muxatmd in AIX 4 allows an attacker to cause a core dump and possibly execute code.
CVE-2001-1097(PUBLISHED):Cisco routers and switches running IOS 12.0 through 12.2.1 allows a remote attacker to cause a denial of service via a flood of UDP packets.
CVE-2001-1098(PUBLISHED):Cisco PIX firewall manager (PFM) 4.3(2)g logs the enable password in plaintext in the pfm.log file, which could allow local users to obtain the password by reading the file.
CVE-2001-1099(PUBLISHED):The default configuration of Norton AntiVirus for Microsoft Exchange 2000 2.x allows remote attackers to identify the recipient's INBOX file path by sending an email with an attachment containing malicious content, which includes the path in the rejection notice.
CVE-2001-1100(PUBLISHED):sendmessage.cgi in W3Mail 1.0.2, and possibly other CGI programs, allows remote attackers to execute arbitrary commands via shell metacharacters in any field of the 'Compose Message' page.
CVE-2001-1101(PUBLISHED):The Log Viewer function in the Check Point FireWall-1 GUI for Solaris 3.0b through 4.1 SP2 does not check for the existence of '.log' files when saving files, which allows (1) remote authenticated users to overwrite arbitrary files ending in '.log', or (2) local users to overwrite arbitrary files via a symlink attack.
CVE-2001-1102(PUBLISHED):Check Point FireWall-1 3.0b through 4.1 for Solaris allows local users to overwrite arbitrary files via a symlink attack on temporary policy files that end in a .cpp extension, which are set world-writable.
CVE-2001-1103(PUBLISHED):FTP Voyager ActiveX control before 8.0, when it is marked as safe for scripting (the default) or if allowed by the IObjectSafety interface, allows remote attackers to execute arbitrary commands.
CVE-2001-1104(PUBLISHED):SonicWALL SOHO uses easily predictable TCP sequence numbers, which allows remote attackers to spoof or hijack sessions.
CVE-2001-1105(PUBLISHED):RSA BSAFE SSL-J 3.0, 3.0.1 and 3.1, as used in Cisco iCND 2.0, caches session IDs from failed login attempts, which could allow remote attackers to bypass SSL client authentication and gain access to sensitive data by logging in after an initial failure.
CVE-2001-1106(PUBLISHED):The default configuration of Sambar Server 5 and earlier uses a symmetric key that is compiled into the binary program for encrypting passwords, which could allow local users to break all user passwords by cracking the key or modifying a copy of the sambar program to call the decryption procedure.
CVE-2001-1107(PUBLISHED):SnapStream PVS 1.2a stores its passwords in plaintext in the file SSD.ini, which could allow a remote attacker to gain privileges on the server.
CVE-2001-1108(PUBLISHED):Directory traversal vulnerability in SnapStream PVS 1.2a allows remote attackers to read arbitrary files via a .. (dot dot) attack in the requested URL.
CVE-2001-1109(PUBLISHED):Directory traversal vulnerability in EFTP 2.0.7.337 allows remote authenticated users to reveal directory contents via a .. (dot dot) in the (1) LIST, (2) QUOTE SIZE, and (3) QUOTE MDTM commands.
CVE-2001-1110(PUBLISHED):EFTP 2.0.7.337 allows remote attackers to obtain NETBIOS credentials by requesting information on a file that is in a network share, which causes the server to send the credentials to the host that owns the share, and allows the attacker to sniff the connection.
CVE-2001-1111(PUBLISHED):EFTP 2.0.7.337 stores user passwords in plaintext in the eftp2users.dat file.
CVE-2001-1112(PUBLISHED):Buffer overflow in EFTP 2.0.7.337 allows remote attackers to execute arbitrary code by uploading a .lnk file containing a large number of characters.
CVE-2001-1113(PUBLISHED):Buffer overflow in TrollFTPD 1.26 and earlier allows local users to execute arbitrary code by creating a series of deeply nested directories with long names, then running the ls -R (recursive) command.
CVE-2001-1114(PUBLISHED):book.cgi in NetCode NC Book 0.2b allows remote attackers to execute arbitrary commands via shell metacharacters in the "current" parameter.
CVE-2001-1115(PUBLISHED):generate.cgi in SIX-webboard 2.01 and before allows remote attackers to read arbitrary files via a dot dot (..) in the content parameter.
CVE-2001-1116(PUBLISHED):Identix BioLogon 2.03 and earlier does not lock secondary displays on a multi-monitor system running Windows 98 or ME, which allows an attacker with physical access to the system to bypass authentication through a secondary display.
CVE-2001-1117(PUBLISHED):LinkSys EtherFast BEFSR41 Cable/DSL routers running firmware before 1.39.3 Beta allows a remote attacker to view administration and user passwords by connecting to the router and viewing the HTML source for (1) index.htm and (2) Password.htm.
CVE-2001-1118(PUBLISHED):A module in Roxen 2.0 before 2.0.92, and 2.1 before 2.1.264, does not properly decode UTF-8, Mac and ISO-2202 encoded URLs, which could allow a remote attacker to execute arbitrary commands or view arbitrary files via an encoded URL.
CVE-2001-1119(PUBLISHED):cda in xmcd 3.0.2 and 2.6 in SuSE Linux allows local users to overwrite arbitrary files via a symlink attack.
CVE-2001-1120(PUBLISHED):Vulnerabilities in ColdFusion 2.0 through 4.5.1 SP 2 allow remote attackers to (1) read or delete arbitrary files, or (2) overwrite ColdFusion Server templates.
CVE-2001-1122(PUBLISHED):Windows NT 4.0 SP 6a allows a local user with write access to winnt/system32 to cause a denial of service (crash in lsass.exe) by running the NT4ALL exploit program in 'SPECIAL' mode.
CVE-2001-1123(PUBLISHED):Vulnerability in Network Node Manager (NNM) 6.2 and earlier in HP OpenView allows a local user to execute arbitrary code, possibly via a buffer overflow in a long hostname or object ID.
CVE-2001-1124(PUBLISHED):rpcbind in HP-UX 11.00, 11.04 and 11.11 allows remote attackers to cause a denial of service (core dump) via a malformed RPC portmap requests, possibly related to a buffer overflow.
CVE-2001-1125(PUBLISHED):Symantec LiveUpdate before 1.6 does not use cryptography to ensure the integrity of download files, which allows remote attackers to execute arbitrary code via DNS spoofing of the update.symantec.com site.
CVE-2001-1126(PUBLISHED):Symantec LiveUpdate 1.4 through 1.6, and possibly later versions, allows remote attackers to cause a denial of service (flood) via DNS spoofing of the update.symantec.com site.
CVE-2001-1127(PUBLISHED):Buffer overflow in Progress database 8.3D and 9.1C could allow a local user to execute arbitrary code via (1) _proapsv, (2) _mprosrv, (3) _mprshut, (4) orarx, (5) sqlcpp, (6) _probrkr, (7) _sqlschema and (8) _sqldump.
CVE-2001-1128(PUBLISHED):Buffer overflow in Progress database 8.3D and 9.1C allows local users to execute arbitrary code via long entries in files that are specified by the (1) PROMSGS or (2) PROTERMCAP environment variables.
CVE-2001-1129(PUBLISHED):Format string vulnerabilities in (1) _probuild, (2) _dbutil, (3) _mprosrv, (4) _mprshut, (5) _proapsv, (6) _progres, (7) _proutil, (8) _rfutil and (9) prolib in Progress database 9.1C allows a local user to execute arbitrary code via format string specifiers in the file used by the PROMSGS environment variable.
CVE-2001-1130(PUBLISHED):Sdbsearch.cgi in SuSE Linux 6.0-7.2 could allow remote attackers to execute arbitrary commands by uploading a keylist.txt file that contains filenames with shell metacharacters, then causing the file to be searched using a .. in the HTTP referer (from the HTTP_REFERER variable) to point to the directory that contains the keylist.txt file.
CVE-2001-1131(PUBLISHED):Directory traversal vulnerability in WhitSoft Development SlimFTPd 2.2 allows an attacker to read arbitrary files and directories via a ... (modified dot dot) in the CD command.
CVE-2001-1132(PUBLISHED):Mailman 2.0.x before 2.0.6 allows remote attackers to gain access to list administrative pages when there is an empty site or list password, which is not properly handled during the call to the crypt function during authentication.
CVE-2001-1133(PUBLISHED):Vulnerability in a system call in BSDI 3.0 and 3.1 allows local users to cause a denial of service (reboot) in the kernel via a particular sequence of instructions.
CVE-2001-1134(PUBLISHED):Xerox DocuPrint N40 Printers allow remote attackers to cause a denial of service via malformed data, such as that produced by the Code Red worm.
CVE-2001-1135(PUBLISHED):ZyXEL Prestige 642R and 642R-I routers do not filter the routers' Telnet and FTP ports on the external WAN interface from inside access, allowing someone on an internal computer to reconfigure the router, if the password is known.
CVE-2001-1136(PUBLISHED):The libsecurity library in HP-UX 11.04 (VVOS) allows attackers to cause a denial of service.
CVE-2001-1137(PUBLISHED):D-Link DI-704 Internet Gateway firmware earlier than V2.56b6 allows remote attackers to cause a denial of service (reboot) via malformed IP datagram fragments.
CVE-2001-1138(PUBLISHED):Directory traversal vulnerability in r.pl (aka r.cgi) of Randy Parker Power Up HTML 0.8033beta allows remote attackers to read arbitrary files and possibly execute arbitrary code via a .. (dot dot) in the FILE parameter.
CVE-2001-1139(PUBLISHED):Directory traversal vulnerability in ASCII NT WinWrapper Professional allows remote attackers to read arbitrary files via a .. (dot dot) in the server request.
CVE-2001-1140(PUBLISHED):BadBlue Personal Edition v1.02 beta allows remote attackers to read source code for executable programs by appending a %00 (null byte) to the request.
CVE-2001-1141(PUBLISHED):The Pseudo-Random Number Generator (PRNG) in SSLeay and OpenSSL before 0.9.6b allows attackers to use the output of small PRNG requests to determine the internal state information, which could be used by attackers to predict future pseudo-random numbers.
CVE-2001-1142(PUBLISHED):ArGoSoft FTP Server ******* uses weak encryption for user passwords, which allows an attacker with access to the password file to gain privileges.
CVE-2001-1143(PUBLISHED):IBM DB2 7.0 allows a remote attacker to cause a denial of service (crash) via a single byte to (1) db2ccs.exe on port 6790, or (2) db2jds.exe on port 6789.
CVE-2001-1144(PUBLISHED):Directory traversal vulnerability in McAfee ASaP VirusScan agent 1.0 allows remote attackers to read arbitrary files via a .. (dot dot) in the HTTP request.
CVE-2001-1145(PUBLISHED):fts routines in FreeBSD 4.3 and earlier, NetBSD before 1.5.2, and OpenBSD 2.9 and earlier can be forced to change (chdir) into a different directory than intended when the directory above the current directory is moved, which could cause scripts to perform dangerous actions on the wrong directories.
CVE-2001-1146(PUBLISHED):AllCommerce with debugging enabled in EnGarde Secure Linux 1.0.1 creates temporary files with predictable names, which allows local users to modify files via a symlink attack.
CVE-2001-1147(PUBLISHED):The PAM implementation in /bin/login of the util-linux package before 2.11 causes a password entry to be rewritten across multiple PAM calls, which could provide the credentials of one user to a different user, when used in certain PAM modules such as pam_limits.
CVE-2001-1148(PUBLISHED):Multiple buffer overflows in programs used by scoadmin and sysadmsh in SCO OpenServer 5.0.6a and earlier allow local users to gain privileges via a long TERM environment variable to (1) atcronsh, (2) auditsh, (3) authsh, (4) backupsh, (5) lpsh, (6) sysadm.menu, or (7) termsh.
CVE-2001-1149(PUBLISHED):Panda Antivirus Platinum before 6.23.00 allows a remore attacker to cause a denial of service (crash) when a user selects an action for a malformed UPX packed executable file.
CVE-2001-1150(PUBLISHED):Vulnerability in cgiWebupdate.exe in Trend Micro OfficeScan Corporate Edition (aka Virus Buster) 3.5.2 through 3.5.4 allows remote attackers to read arbitrary files.
CVE-2001-1151(PUBLISHED):Trend Micro OfficeScan Corporate Edition (aka Virus Buster) 3.53 allows remote attackers to access sensitive information from the hotdownload directory without authentication, such as the ofcscan.ini configuration file, which contains a weakly encrypted password.
CVE-2001-1152(PUBLISHED):Baltimore Technologies WEBsweeper 4.02, when used to manage URL blacklists, allows remote attackers to bypass blacklist restrictions and connect to unauthorized web servers by modifying the requested URL, including (1) a // (double slash), (2) a /SUBDIR/.. where the desired file is in the parentdir, (3) a /./, or (4) URL-encoded characters.
CVE-2001-1153(PUBLISHED):lpsystem in OpenUnix 8.0.0 allows local users to cause a denial of service and possibly execute arbitrary code via a long command line argument.
CVE-2001-1154(PUBLISHED):Cyrus 2.0.15, 2.0.16, and 1.6.24 on BSDi 4.2, with IMAP enabled, allows remote attackers to cause a denial of service (hang) using PHP IMAP clients.
CVE-2001-1155(PUBLISHED):TCP Wrappers (tcp_wrappers) in FreeBSD 4.1.1 through 4.3 with the PARANOID ACL option enabled does not properly check the result of a reverse DNS lookup, which could allow remote attackers to bypass intended access restrictions via DNS spoofing.
CVE-2001-1156(PUBLISHED):TYPSoft FTP 0.95 allows remote attackers to cause a denial of service (CPU consumption) via a "../../*" argument to (1) STOR or (2) RETR.
CVE-2001-1157(PUBLISHED):Baltimore Technologies WEBsweeper 4.0 and 4.02 does not properly filter Javascript from HTML pages, which could allow remote attackers to bypass the filtering via (1) an extra leading < and one or more characters before the SCRIPT tag, or (2) tags using Unicode.
CVE-2001-1158(PUBLISHED):Check Point VPN-1/FireWall-1 4.1 base.def contains a default macro, accept_fw1_rdp, which can allow remote attackers to bypass intended restrictions with forged RDP (internal protocol) headers to UDP port 259 of arbitrary hosts.
CVE-2001-1159(PUBLISHED):load_prefs.php and supporting include files in SquirrelMail 1.0.4 and earlier do not properly initialize certain PHP variables, which allows remote attackers to (1) view sensitive files via the config_php and data_dir options, and (2) execute arbitrary code by using options_order.php to upload a message that could be interpreted as PHP.
CVE-2001-1160(PUBLISHED):udirectory.pl in Microburst Technologies uDirectory 2.0 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters in the category_file field.
CVE-2001-1161(PUBLISHED):Cross-site scripting (CSS) vulnerability in Lotus Domino 5.0.6 allows remote attackers to execute script on other web clients via a URL that ends in Javascript, which generates an error message that does not quote the resulting script.
CVE-2001-1162(PUBLISHED):Directory traversal vulnerability in the %m macro in the smb.conf configuration file in Samba before 2.2.0a allows remote attackers to overwrite certain files via a .. in a NETBIOS name, which is used as the name for a .log file.
CVE-2001-1163(PUBLISHED):Buffer overflow in Munica Corporation NetSQL 1.0 allows remote attackers to execute arbitrary code via a long CONNECT argument to port 6500.
CVE-2001-1164(PUBLISHED):Buffer overflow in uucp utilities in UnixWare 7 allows local users to execute arbitrary code via long command line arguments to (1) uucp, (2) uux, (3) bnuconvert, (4) uucico, (5) uuxcmd, or (6) uuxqt.
CVE-2001-1165(PUBLISHED):Intego FileGuard 4.0 uses weak encryption to store user information and passwords, which allows local users to gain privileges by decrypting the information, e.g., with the Disengage tool.
CVE-2001-1166(PUBLISHED):linprocfs on FreeBSD 4.3 and earlier does not properly restrict access to kernel memory, which allows one process with debugging rights on a privileged process to read restricted memory from that process.
CVE-2001-1168(PUBLISHED):Directory traversal vulnerability in index.php in PhpMyExplorer before 1.2.1 allows remote attackers to read arbitrary files via a ..%2F (modified dot dot) in the chemin parameter.
CVE-2001-1169(PUBLISHED):keyinit in S/Key does not require authentication to initialize a one-time password sequence, which allows an attacker who has gained privileges to a user account to create new one-time passwords for use in other activities that may use S/Key authentication, such as sudo.
CVE-2001-1170(PUBLISHED):AmTote International homebet program stores the homebet.log file in the homebet/ virtual directory, which allows remote attackers to steal account and PIN numbers.
CVE-2001-1171(PUBLISHED):Check Point Firewall-1 3.0b through 4.0 SP1 follows symlinks and creates a world-writable temporary .cpp file when compiling Policy rules, which could allow local users to gain privileges or modify the firewall policy.
CVE-2001-1172(PUBLISHED):OmniSecure HTTProtect 1.1.1 allows a superuser without omnish privileges to modify a protected file by creating a symbolic link to that file.
CVE-2001-1173(PUBLISHED):Vulnerability in MasqMail before 0.1.15 allows local users to gain privileges via piped aliases.
CVE-2001-1174(PUBLISHED):Buffer overflow in Elm 2.5.5 and earlier allows remote attackers to execute arbitrary code via a long Message-ID header.
CVE-2001-1175(PUBLISHED):vipw in the util-linux package before 2.10 causes /etc/shadow to be world-readable in some cases, which would make it easier for local users to perform brute force password guessing.
CVE-2001-1176(PUBLISHED):Format string vulnerability in Check Point VPN-1/FireWall-1 4.1 allows a remote authenticated firewall administrator to execute arbitrary code via format strings in the control connection.
CVE-2001-1177(PUBLISHED):ml85p in Samsung ML-85G GDI printer driver before 0.2.0 allows local users to overwrite arbitrary files via a symlink attack on temporary files.
CVE-2001-1178(PUBLISHED):Buffer overflow in xman allows local users to gain privileges via a long MANPATH environment variable.
CVE-2001-1179(PUBLISHED):xman allows local users to gain privileges by modifying the MANPATH to point to a man page whose filename contains shell metacharacters.
CVE-2001-1180(PUBLISHED):FreeBSD 4.3 does not properly clear shared signal handlers when executing a process, which allows local users to gain privileges by calling rfork with a shared signal handler, having the child process execute a setuid program, and sending a signal to the child.
CVE-2001-1181(PUBLISHED):Dynamically Loadable Kernel Module (dlkm) static kernel symbol table in HP-UX 11.11 is not properly configured, which allows local users to gain privileges.
CVE-2001-1182(PUBLISHED):Vulnerability in login in HP-UX 11.00, 11.11, and 10.20 allows restricted shell users to bypass certain security checks and gain privileges.
CVE-2001-1183(PUBLISHED):PPTP implementation in Cisco IOS 12.1 and 12.2 allows remote attackers to cause a denial of service (crash) via a malformed packet.
CVE-2001-1184(PUBLISHED):wrshdsp.exe in Denicomp Winsock RSHD/NT 2.21.00 and earlier allows remote attackers to cause a denial of service (CPU consumption) via (1) in 2.20.00 and earlier, an invalid port number such as a negative number, which causes a connection attempt to that port and all ports below 1024, and (2) in 2.21.00, a port number of 1024.
CVE-2001-1185(PUBLISHED):Some AIO operations in FreeBSD 4.4 may be delayed until after a call to execve, which could allow a local user to overwrite memory of the new process and gain privileges.
CVE-2001-1186(PUBLISHED):Microsoft IIS 5.0 allows remote attackers to cause a denial of service via an HTTP request with a content-length value that is larger than the size of the request, which prevents IIS from timing out the connection.
CVE-2001-1187(PUBLISHED):csvform.pl 0.1 allows remote attackers to execute arbitrary commands via metacharacters in the file parameter.
CVE-2001-1188(PUBLISHED):mailto.exe in Brian Dorricott MAILTO 1.0.9 and earlier allows remote attackers to send SPAM e-mail through remote servers by modifying the sendto, email, server, subject, and resulturl hidden form fields.
CVE-2001-1189(PUBLISHED):IBM Websphere Application Server 3.5.3 and earlier stores a password in cleartext in the sas.server.props file, which allows local users to obtain the passwords via a JSP script.
CVE-2001-1190(PUBLISHED):The default PAM files included with passwd in Mandrake Linux 8.1 do not support MD5 passwords, which could result in a lower level of password security than intended.
CVE-2001-1191(PUBLISHED):WebSeal in IBM Tivoli SecureWay Policy Director 3.8 allows remote attackers to cause a denial of service (crash) via a URL that ends in %2e.
CVE-2001-1192(PUBLISHED):Citrix Independent Computing Architecture (ICA) Client for Windows 6.1 allows remote malicious web sites to execute arbitrary code via a .ICA file, which is downloaded and automatically executed by the client.
CVE-2001-1193(PUBLISHED):Directory traversal vulnerability in EFTP 2.0.8.346 allows local users to read directories via a ... (modified dot dot) in the CWD command.
CVE-2001-1194(PUBLISHED):Zyxel Prestige 681 and 1600 SDSL Routers allow remote attackers to cause a denial of service via malformed packets with (1) an IP length less than actual packet size, or (2) fragmented packets whose size exceeds 64 kilobytes after reassembly.
CVE-2001-1195(PUBLISHED):Novell Groupwise 5.5 and 6.0 Servlet Gateway is installed with a default username and password for the servlet manager, which allows remote attackers to gain privileges.
CVE-2001-1196(PUBLISHED):Directory traversal vulnerability in edit_action.cgi of Webmin Directory 0.91 allows attackers to gain privileges via a '..' (dot dot) in the argument.
CVE-2001-1197(PUBLISHED):klprfax_filter in KDE2 KDEUtils allows local users to overwrite arbitrary files via a symlink attack on the klprfax.filter temporary file.
CVE-2001-1198(PUBLISHED):RLPDaemon in HP-UX 10.20 and 11.0 allows local users to overwrite arbitrary files and gain privileges by specifying the target file in the -L option.
CVE-2001-1199(PUBLISHED):Cross-site scripting vulnerability in agora.cgi for Agora 3.0a through 4.0g, when debug mode is enabled, allows remote attackers to execute Javascript on other clients via the cart_id parameter.
CVE-2001-1200(PUBLISHED):Microsoft Windows XP allows local users to bypass a locked screen and run certain programs that are associated with Hot Keys.
CVE-2001-1201(PUBLISHED):Buffer overflow in wmcube-gdk for WMCube/GDK 0.98 allows local users to execute arbitrary code via long lines in the object description file.
CVE-2001-1202(PUBLISHED):Cross-site scripting vulnerability in DeleGate 7.7.0 and 7.7.1 does not quote scripting commands within a "403 Forbidden" error page, which allows remote attackers to execute arbitrary Javascript on other clients via a URL that generates an error.
CVE-2001-1203(PUBLISHED):Format string vulnerability in gpm-root in gpm 1.17.8 through 1.17.18 allows local users to gain root privileges.
CVE-2001-1204(PUBLISHED):Directory traversal vulnerability in phprocketaddin in Total PC Solutions PHP Rocket Add-in for FrontPage 1.0 allows remote attackers to read arbitrary files via a .. (dot dot) in the page parameter.
CVE-2001-1205(PUBLISHED):Directory traversal vulnerability in lastlines.cgi for Last Lines 2.0 allows remote attackers to read arbitrary files via '..' sequences in the $error_log variable.
CVE-2001-1206(PUBLISHED):Matrix CGI vault Last Lines 2.0 allows remote attackers to execute arbitrary commands via shell metacharacters in the $error_log variable.
CVE-2001-1207(PUBLISHED):Buffer overflows in DayDream BBS 2.9 through 2.13 allow remote attackers to possibly execute arbitrary code via the control codes (1) ~#MC, (2) ~#TF, or (3) ~#RA.
CVE-2001-1208(PUBLISHED):Format string vulnerability in DayDream BBS allows remote attackers to execute arbitrary code via format string specifiers in a file containing a ~#RA control code.
CVE-2001-1209(PUBLISHED):Directory traversal vulnerability in zml.cgi allows remote attackers to read arbitrary files via a .. (dot dot) in the file parameter.
CVE-2001-1210(PUBLISHED):Cisco ubr900 series routers that conform to the Data-over-Cable Service Interface Specifications (DOCSIS) standard must ship without SNMP access restrictions, which can allow remote attackers to read and write information to the MIB using arbitrary community strings.
CVE-2001-1211(PUBLISHED):Ipswitch IMail 7.0.4 and earlier allows attackers with administrator privileges to read and modify user alias and mailing list information for other domains hosted by the same server via the (1) aliasadmin or (2) listadm1 CGI programs, which do not properly verify that an administrator is the administrator for the target domain.
CVE-2001-1212(PUBLISHED):Cross-site scripting vulnerability in catgy.cgi for Aktivate 1.03 allows remote attackers to execute arbitrary Javascript via the desc parameter.
CVE-2001-1213(PUBLISHED):The default configuration of DataWizard FtpXQ 2.0 and 2.1 includes a default username and password, which allows remote attackers to read and write arbitrary files in the root folder.
CVE-2001-1214(PUBLISHED):manual.php in Marcus S. Xenakis Unix Manual 1.0 allows remote attackers to execute arbitrary code via a URL that contains shell metacharacters.
CVE-2001-1215(PUBLISHED):Format string vulnerability in PFinger 0.7.5 through 0.7.7 allows remote attackers to execute arbitrary code via format string specifiers in a .plan file.
CVE-2001-1216(PUBLISHED):Buffer overflow in PL/SQL Apache module in Oracle 9i Application Server allows remote attackers to execute arbitrary code via a long request for a help page.
CVE-2001-1217(PUBLISHED):Directory traversal vulnerability in PL/SQL Apache module in Oracle Oracle 9i Application Server allows remote attackers to access sensitive information via a double encoded URL with .. (dot dot) sequences.
CVE-2001-1218(PUBLISHED):Microsoft Internet Explorer for Unix 5.0SP1 allows local users to possibly cause a denial of service (crash) in CDE or the X server on Solaris 2.6 by rapidly scrolling Chinese characters or maximizing the window.
CVE-2001-1219(PUBLISHED):Microsoft Internet Explorer 6.0 and earlier allows malicious website operators to cause a denial of service (client crash) via JavaScript that continually refreshes the window via self.location.
CVE-2001-1220(PUBLISHED):D-Link DWL-1000AP Firmware 3.2.28 #483 Wireless LAN Access Point stores the administrative password in plaintext in the default Management Information Base (MIB), which allows remote attackers to gain administrative privileges.
CVE-2001-1221(PUBLISHED):D-Link DWL-1000AP Firmware 3.2.28 #483 Wireless LAN Access Point uses a default SNMP community string of 'public' which allows remote attackers to gain sensitive information.
CVE-2001-1222(PUBLISHED):Plesk Server Administrator (PSA) 1.0 allows remote attackers to obtain PHP source code via an HTTP request containing the target's IP address and a valid account name for the domain.
CVE-2001-1223(PUBLISHED):The web administration server for ELSA Lancom 1100 Office does not require authentication, which allows arbitrary remote attackers to gain administrative privileges by connecting to the server.
CVE-2001-1224(PUBLISHED):get_input in adrotate.pm for Les VanBrunt AdRotate Pro 2.0 allows remote attackers to modify the database and possibly execute arbitrary commands via a SQL code injection attack.
CVE-2001-1225(PUBLISHED):Hughes Technology Mini SQL 2.0.10 through 2.0.12 allows local users to cause a denial of service by creating a very large array in a table, which causes miniSQL to crash when the table is queried.
CVE-2001-1226(PUBLISHED):AdCycle 1.17 and earlier allow remote attackers to modify SQL queries, which are not properly sanitized before being passed to the MySQL database.
CVE-2001-1227(PUBLISHED):Zope before 2.2.4 allows partially trusted users to bypass security controls for certain methods by accessing the methods through the fmt attribute of dtml-var tags.
CVE-2001-1228(PUBLISHED):Buffer overflows in gzip 1.3x, 1.2.4, and other versions might allow attackers to execute code via a long file name, possibly remotely if gzip is run on an FTP server.
CVE-2001-1229(PUBLISHED):Buffer overflows in (1) Icecast before 1.3.9 and (2) libshout before 1.0.4 allow remote attackers to cause a denial of service (crash) and execute arbitrary code.
CVE-2001-1230(PUBLISHED):Buffer overflows in Icecast before 1.3.10 allow remote attackers to cause a denial of service (crash) and execute arbitrary code.
CVE-2001-1231(PUBLISHED):GroupWise 5.5 and 6 running in live remote or smart caching mode allows remote attackers to read arbitrary users' mailboxes by extracting usernames and passwords from sniffed network traffic, as addressed by the "Padlock" fix.
CVE-2001-1232(PUBLISHED):GroupWise WebAccess 5.5 with directory indexing enabled allows a remote attacker to view arbitrary directory contents via an HTTP request with a lowercase "get".
CVE-2001-1233(PUBLISHED):Netware Enterprise Web Server 5.1 running GroupWise WebAccess 5.5 with Novell Directory Services (NDS) enabled allows remote attackers to enumerate user names, group names and other system information by accessing ndsobj.nlm.
CVE-2001-1234(PUBLISHED):Bharat Mediratta Gallery PHP script before 1.2.1 allows remote attackers to execute arbitrary code by including files from remote web sites via an HTTP request that modifies the includedir variable.
CVE-2001-1235(PUBLISHED):pSlash PHP script 0.7 and earlier allows remote attackers to execute arbitrary code by including files from remote web sites, using an HTTP request that modifies the includedir variable.
CVE-2001-1236(PUBLISHED):myphpPagetool PHP script 0.4.3-1 and earlier allows remote attackers to execute arbitrary code by including files from remote web sites, using an HTTP request that modifies the includedir variable.
CVE-2001-1237(PUBLISHED):Phormation PHP script 0.9.1 and earlier allows remote attackers to execute arbitrary code by including files from remote web sites, using an HTTP request that modifies the phormationdir variable.
CVE-2001-1238(PUBLISHED):Task Manager in Windows 2000 does not allow local users to end processes with uppercase letters named (1) winlogon.exe, (2) csrss.exe, (3) smss.exe and (4) services.exe via the Process tab which could allow local users to install Trojan horses that cannot be stopped with the Task Manager.
CVE-2001-1239(PUBLISHED):PowerNet IX allows remote attackers to cause a denial of service via a port scan.
CVE-2001-1240(PUBLISHED):The default configuration of sudo in Engarde Secure Linux 1.0.1 allows any user in the admin group to run certain commands that could be leveraged to gain full root access.
CVE-2001-1241(PUBLISHED):Un-CGI 1.9 and earlier does not verify that a CGI script has the execution bits set before executing it, which allows remote attackers to execute arbitrary commands by directing Un-CGI to a document that begins with "#!"  and the desired program name.
CVE-2001-1242(PUBLISHED):Directory traversal vulnerability in Un-CGI 1.9 and earlier allows remote attackers to execute arbitrary code via a .. (dot dot) in an HTML form.
CVE-2001-1243(PUBLISHED):Scripting.FileSystemObject in asp.dll for Microsoft IIS 4.0 and 5.0 allows local or remote attackers to cause a denial of service (crash) via (1) creating an ASP program that uses Scripting.FileSystemObject to open a file with an MS-DOS device name, or (2) remotely injecting the device name into ASP programs that internally use Scripting.FileSystemObject.
CVE-2001-1244(PUBLISHED):Multiple TCP implementations could allow remote attackers to cause a denial of service (bandwidth and CPU exhaustion) by setting the maximum segment size (MSS) to a very small number and requesting large amounts of data, which generates more packets with less TCP-level data that amplify network traffic and consume more server CPU to process.
CVE-2001-1245(PUBLISHED):Opera 5.0 for Linux does not properly handle malformed HTTP headers, which allows remote attackers to cause a denial of service, possibly with a header whose value is the same as a MIME header name.
CVE-2001-1246(PUBLISHED):PHP 4.0.5 through 4.1.0 in safe mode does not properly cleanse the 5th parameter to the mail() function, which allows local users and possibly remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2001-1247(PUBLISHED):PHP 4.0.4pl1 and 4.0.5 in safe mode allows remote attackers to read and write files owned by the web server UID by uploading a PHP script that uses the error_log function to access the files.
CVE-2001-1248(PUBLISHED):vWebServer 1.2.0 allows remote attackers to view arbitrary ASP scripts via a request for an ASP script that ends with a URL-encoded space character (%20).
CVE-2001-1249(PUBLISHED):vWebServer 1.2.0 allows remote attackers to cause a denial of service via a URL that contains MS-DOS device names.
CVE-2001-1250(PUBLISHED):vWebServer 1.2.0 allows remote attackers to cause a denial of service (hang) via a small number of long URL requests, possibly due to a buffer overflow.
CVE-2001-1251(PUBLISHED):SmallHTTP 1.204 through 3.00 beta 8 allows remote attackers to cause a denial of service via multiple long URL requests.
CVE-2001-1252(PUBLISHED):Network Associates PGP Keyserver 7.0 allows remote attackers to bypass authentication and access the administrative web interface via URLs that directly access cgi-bin instead of keyserver/cgi-bin for the programs (1) console, (2) cs, (3) multi_config and (4) directory.
CVE-2001-1253(PUBLISHED):Alexis 2.0 and 2.1 in COM2001 InternetPBX stores voicemail passwords in plain text in the com2001.ini file, which could allow local users to make long distance calls as other users.
CVE-2001-1254(PUBLISHED):Web Access component for COM2001 Alexis 2.0 and 2.1 in InternetPBX sends username and voice mail passwords in the clear via a Java applet that sends the information to port 8888 of the server, which could allow remote attackers to steal the passwords via sniffing.
CVE-2001-1255(PUBLISHED):WinMySQLadmin 1.1 stores the MySQL password in plain text in the my.ini file, which allows local users to obtain unathorized access the MySQL database.
CVE-2001-1256(PUBLISHED):kmmodreg in HP-UX 11.11, 11.04 and 11.00 allows local users to create arbitrary world-writeable files via a symlink attack on the (1) /tmp/.kmmodreg_lock and (2) /tmp/kmpath.tmp temporary files.
CVE-2001-1257(PUBLISHED):Cross-site scripting vulnerability in Horde Internet Messaging Program (IMP) before 2.2.6 and 1.2.6 allows remote attackers to execute arbitrary Javascript embedded in an email.
CVE-2001-1258(PUBLISHED):Horde Internet Messaging Program (IMP) before 2.2.6 allows local users to read IMP configuration files and steal the Horde database password by placing the prefs.lang file containing PHP code on the server.
CVE-2001-1259(PUBLISHED):Avaya Argent Office allows remote attackers to cause a denial of service by sending UDP packets to port 53 with no payload.
CVE-2001-1260(PUBLISHED):Avaya Argent Office uses weak encryption (trivial encoding) for passwords, which allows remote attackers to gain administrator privileges by sniffing and decrypting the sniffing the passwords during a system reboot.
CVE-2001-1261(PUBLISHED):Avaya Argent Office 2.1 may allow remote attackers to change hold music by spoofing a legitimate server's response to a TFTP broadcast and providing an alternate HoldMusic file.
CVE-2001-1262(PUBLISHED):Avaya Argent Office 2.1 compares a user-provided SNMP community string with the correct string only up to the length of the user-provided string, which allows remote attackers to bypass authentication with a 0 length community string.
CVE-2001-1263(PUBLISHED):telnet95.exe in Pragma InterAccess 4.0 build 5 allows remote attackers to cause a denial of service (crash) via a large number of characters to port 23, possibly due to a buffer overflow.
CVE-2001-1264(PUBLISHED):Vulnerability in mkacct in HP-UX 11.04 running Virtualvault Operating System (VVOS) 4.0 and 4.5 allows attackers to elevate privileges.
CVE-2001-1265(PUBLISHED):Directory traversal vulnerability in IBM alphaWorks Java TFTP server 1.21 allows remote attackers to conduct unauthorized operations on arbitrary files via a .. (dot dot) attack.
CVE-2001-1266(PUBLISHED):Directory traversal vulnerability in Doug Neal's HTTPD Daemon (DNHTTPD) before 0.4.1 allows remote attackers to view arbitrary files via a .. (dot dot) attack using the dot hex code '%2E'.
CVE-2001-1267(PUBLISHED):Directory traversal vulnerability in GNU tar 1.13.19 and earlier allows local users to overwrite arbitrary files during archive extraction via a tar file whose filenames contain a .. (dot dot).
CVE-2001-1268(PUBLISHED):Directory traversal vulnerability in Info-ZIP UnZip 5.42 and earlier allows attackers to overwrite arbitrary files during archive extraction via a .. (dot dot) in an extracted filename.
CVE-2001-1269(PUBLISHED):Info-ZIP UnZip 5.42 and earlier allows attackers to overwrite arbitrary files during archive extraction via filenames in the archive that begin with the '/' (slash) character.
CVE-2001-1270(PUBLISHED):Directory traversal vulnerability in the console version of PKZip (pkzipc) 4.00 and earlier allows attackers to overwrite arbitrary files during archive extraction with the -rec (recursive) option via a .. (dot dot) attack on the archived files.
CVE-2001-1271(PUBLISHED):Directory traversal vulnerability in rar 2.02 and earlier allows attackers to overwrite arbitrary files during archive extraction via a ..  (dot dot) attack on archived filenames.
CVE-2001-1272(PUBLISHED):wmtv 0.6.5 and earlier does not properly drop privileges, which allows local users to execute arbitrary commands via the -e (external command) option.
CVE-2001-1273(PUBLISHED):The "mxcsr P4" vulnerability in the Linux kernel before 2.2.17-14, when running on certain Intel CPUs, allows local users to cause a denial of service (system halt).
CVE-2001-1274(PUBLISHED):Buffer overflow in MySQL before 3.23.31 allows attackers to cause a denial of service and possibly gain privileges.
CVE-2001-1275(PUBLISHED):MySQL before 3.23.31 allows users with a MySQL account to use the SHOW GRANTS command to obtain the encrypted administrator password from the mysql.user table and possibly gain privileges via password cracking.
CVE-2001-1276(PUBLISHED):ispell before 3.1.20 allows local users to overwrite files of other users via a symlink attack on a temporary file.
CVE-2001-1277(PUBLISHED):makewhatis in the man package before 1.5i2 allows an attacker in group man to overwrite arbitrary files via a man page whose name contains shell metacharacters.
CVE-2001-1278(PUBLISHED):Zope before 2.2.4 allows partially trusted users to bypass security controls for certain methods by accessing the methods through the fmt attribute of dtml-var tags.
CVE-2001-1279(PUBLISHED):Buffer overflow in print-rx.c of tcpdump 3.x (probably 3.6x) allows remote attackers to cause a denial of service and possibly execute arbitrary code via AFS RPC packets with invalid lengths that trigger an integer signedness error, a different vulnerability than CVE-2000-1026.
CVE-2001-1280(PUBLISHED):POP3 Server for Ipswitch IMail 7.04 and earlier generates different responses to valid and invalid user names, which allows remote attackers to determine users on the system.
CVE-2001-1281(PUBLISHED):Web Messaging Server for Ipswitch IMail 7.04 and earlier allows remote authenticated users to change information for other users by modifying the olduser parameter in the "Change User Information" web form.
CVE-2001-1282(PUBLISHED):Ipswitch IMail 7.04 and earlier records the physical path of attachments in an e-mail message header, which could allow remote attackers to obtain potentially sensitive configuration information.
CVE-2001-1283(PUBLISHED):The webmail interface for Ipswitch IMail 7.04 and earlier allows remote authenticated users to cause a denial of service (crash) via a mailbox name that contains a large number of . (dot) or other characters to programs such as (1) readmail.cgi or (2) printmail.cgi, possibly due to a buffer overflow that may allow execution of arbitrary code.
CVE-2001-1284(PUBLISHED):Ipswitch IMail 7.04 and earlier uses predictable session IDs for authentication, which allows remote attackers to hijack sessions of other users.
CVE-2001-1285(PUBLISHED):Directory traversal vulnerability in readmail.cgi for Ipswitch IMail 7.04 and earlier allows remote attackers to access the mailboxes of other users via a .. (dot dot) in the mbx parameter.
CVE-2001-1286(PUBLISHED):Ipswitch IMail 7.04 and earlier stores a user's session ID in a URL, which could allow remote attackers to hijack sessions by obtaining the URL, e.g. via an HTML email that causes the Referrer to be sent to a URL under the attacker's control.
CVE-2001-1287(PUBLISHED):Buffer overflow in Web Calendar in Ipswitch IMail 7.04 and earlier allows remote attackers to execute arbitrary code via a long HTTP GET request.
CVE-2001-1288(PUBLISHED):Windows 2000 and Windows NT allows local users to cause a denial of service (reboot) by executing a command at the command prompt and pressing the F7 and enter keys several times while the command is executing, possibly related to an exception handling error in csrss.exe.
CVE-2001-1289(PUBLISHED):Quake 3 arena 1.29f and 1.29g allows remote attackers to cause a denial of service (crash) via a malformed connection packet that begins with several char-255 characters.
CVE-2001-1290(PUBLISHED):admin.cgi in Active Classifieds Free Edition 1.0, and possibly commercial versions, allows remote attackers to modify the configuration, gain privileges, and execute arbitrary Perl code via the table_width parameter.
CVE-2001-1291(PUBLISHED):The telnet server for 3Com hardware such as PS40 SuperStack II does not delay or disconnect remote attackers who provide an incorrect username or password, which makes it easier to break into the server via brute force password guessing.
CVE-2001-1292(PUBLISHED):Sambar Telnet Proxy/Server allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long password.
CVE-2001-1293(PUBLISHED):Buffer overflow in web server of 3com HomeConnect Cable Modem External with USB (#3CR29223) allows remote attackers to cause a denial of service (crash) via a long HTTP request.
CVE-2001-1294(PUBLISHED):Buffer overflow in A-V Tronics Inetserv 3.2.1 and earlier allows remote attackers to cause a denial of service (crash) in the Webmail interface via a long username and password.
CVE-2001-1295(PUBLISHED):Directory traversal vulnerability in Cerberus FTP Server 1.5 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) in the CD command.
CVE-2001-1296(PUBLISHED):More.groupware PHP script allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1297(PUBLISHED):PHP remote file inclusion vulnerability in Actionpoll PHP script before 1.1.2 allows remote attackers to execute arbitrary PHP code via a URL in the includedir parameter.
CVE-2001-1298(PUBLISHED):Webodex PHP script 1.0 and earlier allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1299(PUBLISHED):Zorbat Zorbstats PHP script before 0.9 allows remote attackers to include arbitrary files from remote web sites via an HTTP request that sets the includedir variable.
CVE-2001-1300(PUBLISHED):Directory traversal vulnerability in Dynu FTP server 1.05 and earlier allows remote attackers to read arbitrary files via a .. in the CD (CWD) command.
CVE-2001-1301(PUBLISHED):rcs2log, as used in Emacs 20.4, xemacs 21.1.10 and other versions before 21.4, and possibly other packages, allows local users to modify files of other users via a symlink attack on a temporary file.
CVE-2001-1302(PUBLISHED):The change password option in the Windows Security interface for Windows 2000 allows attackers to use the option to attempt to change passwords of other users on other systems or identify valid accounts by monitoring error messages, possibly due to a problem in the NetuserChangePassword function.
CVE-2001-1303(PUBLISHED):The default configuration of SecuRemote for Check Point Firewall-1 allows remote attackers to obtain sensitive configuration information for the protected network without authentication.
CVE-2001-1304(PUBLISHED):Buffer overflow in SHOUTcast Server 1.8.2 allows remote attackers to cause a denial of service (crash) via several HTTP requests with a long (1) user-agent or (2) host HTTP header.
CVE-2001-1305(PUBLISHED):ICQ 2001a Alpha and earlier allows remote attackers to automatically add arbitrary UINs to an ICQ user's contact list via a URL to a web page with a Content-Type of application/x-icq, which is processed by Internet Explorer.
CVE-2001-1306(PUBLISHED):iPlanet Directory Server 4.1.4 and earlier (LDAP) allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via invalid BER length of length fields, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1307(PUBLISHED):Buffer overflows in iPlanet Directory Server 4.1.4 and earlier (LDAP) allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1308(PUBLISHED):Format string vulnerabilities in iPlanet Directory Server 4.1.4 and earlier (LDAP) allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1309(PUBLISHED):Buffer overflows in IBM SecureWay 3.2.1 allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1310(PUBLISHED):IBM SecureWay 3.2.1 allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, via invalid encodings for the L field of a BER encoding, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1311(PUBLISHED):Buffer overflows in Lotus Domino R5 before R5.0.7a allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1312(PUBLISHED):Format string vulnerabilities in Lotus Domino R5 before R5.0.7a allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1313(PUBLISHED):Lotus Domino R5 before R5.0.7a allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via miscellaneous packets with semi-valid BER encodings, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1314(PUBLISHED):Buffer overflows in Critical Path (1) InJoin Directory Server or (2) LiveContent Directory allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1315(PUBLISHED):Critical Path (1) InJoin Directory Server or (2) LiveContent Directory allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via malformed BER encodings, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1316(PUBLISHED):Buffer overflows in Teamware Office Enterprise Directory allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1317(PUBLISHED):Teamware Office Enterprise Directory allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, via invalid encodings for certain BER object types, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1318(PUBLISHED):Vulnerabilities in Qualcomm Eudora WorldMail Server may allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1319(PUBLISHED):Microsoft Exchange 5.5 2000 allows remote attackers to cause a denial of service (hang) via exceptional BER encodings for the LDAP filter type field, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1320(PUBLISHED):Network Associates PGP Keyserver 7.0 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via exceptional BER encodings (possibly buffer overflows), as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1321(PUBLISHED):Oracle Internet Directory Server 2.1.1.x and 3.0.1 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via invalid encodings of BER OBJECT-IDENTIFIER values, as demonstrated by the PROTOS LDAPv3 test suite.
CVE-2001-1322(PUBLISHED):xinetd 2.1.8 and earlier runs with a default umask of 0, which could allow local users to read or modify files that are created by an application that runs under xinetd but does not set its own safe umask.
CVE-2001-1323(PUBLISHED):Buffer overflow in MIT Kerberos 5 (krb5) 1.2.2 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via base-64 encoded data, which is not properly handled when the radix_encode function processes file glob output from the ftpglob function.
CVE-2001-1324(PUBLISHED):cvmlogin and statfile in Paul Jarc idtools before 2001.06.27 do not properly check the return value of a call to the pathexec_env function, which could cause the setstate utility to setuid to the UID environment variable and allow local users to gain privileges.
CVE-2001-1325(PUBLISHED):Internet Explorer 5.0 and 5.5, and Outlook Express 5.0 and 5.5, allow remote attackers to execute scripts when Active Scripting is disabled by including the scripts in XML stylesheets (XSL) that are referenced using an IFRAME tag, possibly due to a vulnerability in Windows Scripting Host (WSH).
CVE-2001-1326(PUBLISHED):Eudora 5.1 allows remote attackers to execute arbitrary code when the "Use Microsoft Viewer" option is enabled and the "allow executables in HTML content" option is disabled, via an HTML email with a form that is activated from an image that the attacker spoofs as a link, which causes the user to execute the form and access embedded attachments.
CVE-2001-1327(PUBLISHED):pmake before 2.1.35 in Turbolinux 6.05 and earlier is installed with setuid root privileges, which could allow local users to gain privileges by exploiting vulnerabilities in pmake or programs that are used by pmake.
CVE-2001-1328(PUBLISHED):Buffer overflow in ypbind daemon in Solaris 5.4 through 8 allows remote attackers to execute arbitrary code.
CVE-2001-1329(PUBLISHED):Buffer overflow in rsh on AIX ******* may allow local users to gain root privileges via a long command line argument.
CVE-2001-1330(PUBLISHED):Buffer overflow in rsh on AIX ******* may allow local users to gain root privileges via a long command line argument.
CVE-2001-1331(PUBLISHED):mandb in the man-db package before 2.3.16-3 allows local users to overwrite arbitrary files via the command line options (1) -u or (2) -c, which do not drop privileges and follow symlinks.
CVE-2001-1332(PUBLISHED):Buffer overflows in Linux CUPS before 1.1.6 may allow remote attackers to execute arbitrary code.
CVE-2001-1333(PUBLISHED):Linux CUPS before 1.1.6 does not securely handle temporary files, possibly due to a symlink vulnerability that could allow local users to overwrite files.
CVE-2001-1334(PUBLISHED):Block_render_url.class in PHPSlash 0.6.1 allows remote attackers with PHPSlash administrator privileges to read arbitrary files by creating a block and specifying the target file as the source URL.
CVE-2001-1335(PUBLISHED):Directory traversal vulnerability in CesarFTP 0.98b and earlier allows remote authenticated users (such as anonymous) to read arbitrary files via a GET with a filename that contains a ...%5c (modified dot dot).
CVE-2001-1336(PUBLISHED):CesarFTP 0.98b and earlier stores usernames and passwords in plaintext in the settings.ini file, which allows attackers to gain privileges.
CVE-2001-1337(PUBLISHED):Beck IPC GmbH IPC@CHIP Embedded-Webserver allows remote attackers to cause a denial of service via a long HTTP request.
CVE-2001-1338(PUBLISHED):Beck IPC GmbH IPC@CHIP TelnetD server generates different responses when given valid and invalid login names, which allows remote attackers to determine accounts on the system.
CVE-2001-1339(PUBLISHED):Beck IPC GmbH IPC@CHIP telnet service does not delay or disconnect users from the service when bad passwords are entered, which makes it easier for remote attackers to conduct brute force password guessing attacks.
CVE-2001-1340(PUBLISHED):Beck GmbH IPC@Chip TelnetD service supports only one connection and does not disconnect a user who does not complete the login process, which allows remote attackers to lock out the administrator account by connecting to the service.
CVE-2001-1341(PUBLISHED):The Beck GmbH IPC@Chip embedded web server installs the chipcfg.cgi program by default, which allows remote attackers to obtain sensitive network information via a request to the program.
CVE-2001-1342(PUBLISHED):Apache before 1.3.20 on Windows and OS/2 systems allows remote attackers to cause a denial of service (GPF) via an HTTP request for a URI that contains a large number of / (slash) or other characters, which causes certain functions to dereference a null pointer.
CVE-2001-1343(PUBLISHED):ws_mail.cgi in WebStore 400/400CS 4.14 allows remote authenticated WebStore administrators to execute arbitrary code via shell metacharacters in the kill parameter.
CVE-2001-1344(PUBLISHED):WSSecurity.pl in WebStore allows remote attackers to bypass authentication by providing the program with a filename that exists, which is made easier by (1) inserting a null character or (2) .. (dot dot).
CVE-2001-1345(PUBLISHED):bctool in Jetico BestCrypt 0.7 and earlier trusts the user-supplied PATH to find and execute an fsck utility program, which allows local users to gain privileges by modifying the PATH to point to a Trojan horse program.
CVE-2001-1346(PUBLISHED):Computer Associates ARCserveIT 6.61 and 6.63 (also called ARCservIT) allows local users to overwrite arbitrary files via a symlink attack on the temporary files (1) asagent.tmp or (2) inetd.tmp.
CVE-2001-1347(PUBLISHED):Windows 2000 allows local users to cause a denial of service and possibly gain privileges by setting a hardware breakpoint that is handled using global debug registers, which could cause other processes to terminate due to an exception, and allow hijacking of resources such as named pipes.
CVE-2001-1348(PUBLISHED):TWIG 2.6.2 and earlier allows remote attackers to perform unauthorized database operations via a SQL injection attack on the id parameter.
CVE-2001-1349(PUBLISHED):Sendmail before 8.11.4, and 8.12.0 before 8.12.0.Beta10, allows local users to cause a denial of service and possibly corrupt the heap and gain privileges via race conditions in signal handlers.
CVE-2001-1350(PUBLISHED):Cross-site scripting vulnerability in namazu.cgi for Namazu 2.0.7 and earlier allows remote attackers to execute arbitrary Javascript as other web users via the lang parameter.
CVE-2001-1351(PUBLISHED):Cross-site scripting vulnerability in Namazu 2.0.8 and earlier allows remote attackers to execute arbitrary Javascript as other web users via the index file name that is displayed when displaying hit numbers.
CVE-2001-1352(PUBLISHED):Cross-site scripting vulnerability in Namazu 2.0.9 and earlier allows remote attackers to execute arbitrary Javascript as other web users via an error message that is returned when an invalid index file is specified in the idxname parameter.
CVE-2001-1353(PUBLISHED):ghostscript before 6.51 allows local users to read and write arbitrary files as the 'lp' user via the file operator, even with -dSAFER enabled.
CVE-2001-1354(PUBLISHED):NetWin Authentication module (NWAuth) 2.0 and 3.0b, as implemented in SurgeFTP, DMail, and possibly other packages, uses weak password hashing, which could allow local users to decrypt passwords or use a different password that has the same hash value as the correct password.
CVE-2001-1355(PUBLISHED):Buffer overflows in NetWin Authentication Module (NWAuth) 3.0b and earlier, as implemented in DMail, SurgeFTP, and possibly other packages, could allow attackers to execute arbitrary code via long arguments to (1) the -del command or (2) the -lookup command.
CVE-2001-1356(PUBLISHED):NetWin SurgeFTP 2.0f and earlier encrypts passwords using weak hashing, a fixed salt value and modulo 40 calculations, which allows remote attackers to conduct brute force password guessing attacks against the administrator account on port 7021.
CVE-2001-1357(PUBLISHED):Multiple vulnerabilities in phpMyChat before 0.14.5 exist in (1) input.php3, (2) handle_inputH.php3, or (3) index.lib.php3 with unknown consequences, possibly related to user spoofing or improperly initialized variables.
CVE-2001-1358(PUBLISHED):Vulnerabilities in phpMyChat before 0.14.4 allow local and possibly remote attackers to gain privileges by specifying an alternate library file in the L (localization) parameter.
CVE-2001-1359(PUBLISHED):Volution clients 1.0.7 and earlier attempt to contact the computer creation daemon (CCD) when an LDAP authentication failure occurs, which allows remote attackers to fully control clients via a Trojan horse Volution server.
CVE-2001-1360(PUBLISHED):Vulnerability in Scanner Access Now Easy (SANE) before 1.0.5, related to pnm and saned.
CVE-2001-1361(PUBLISHED):Vulnerability in The Web Information Gateway (TWIG) 2.7.1, possibly related to incorrect security rights and/or the generation of mailto links.
CVE-2001-1362(PUBLISHED):Vulnerability in the server for nPULSE before 0.53p4.
CVE-2001-1363(PUBLISHED):Vulnerability in phpWebSite before 0.7.9 related to running multiple instances in the same domain, which may allow attackers to gain administrative privileges.
CVE-2001-1364(PUBLISHED):Vulnerability in autodns.pl for AutoDNS before 0.0.4 related to domain names that are not fully qualified.
CVE-2001-1365(PUBLISHED):Vulnerability in IntraGnat before 1.4.
CVE-2001-1366(PUBLISHED):netscript before 1.6.3 parses dynamic variables, which could allow remote attackers to alter program behavior or obtain sensitive information.
CVE-2001-1367(PUBLISHED):The checkAccess function in PHPSlice 0.1.4, and all other versions between 0.1.1 and 0.1.6, does not properly verify the administrative access level, which could allow remote attackers to gain privileges.
CVE-2001-1368(PUBLISHED):Vulnerability in iPlanet Web Server 4 included in Virtualvault Operating System (VVOS) 4.0 running HP-UX 11.04 could allow attackers to corrupt data.
CVE-2001-1369(PUBLISHED):Leon J Breedt pam-pgsql before 0.5.2 allows remote attackers to execute arbitrary SQL code and bypass authentication or modify user account records by injecting SQL statements into user or password fields.
CVE-2001-1370(PUBLISHED):prepend.php3 in PHPLib before 7.2d, when register_globals is enabled for PHP, allows remote attackers to execute arbitrary scripts via an HTTP request that modifies $_PHPLIB[libdir] to point to malicious code on another server, as seen in Horde 1.2.5 and earlier, IMP before 2.2.6, and other packages that use PHPLib.
CVE-2001-1371(PUBLISHED):The default configuration of Oracle Application Server 9iAS ******* enables SOAP and allows anonymous users to deploy applications by default via urn:soap-service-manager and urn:soap-provider-manager.
CVE-2001-1372(PUBLISHED):Oracle 9i Application Server 1.0.2 allows remote attackers to obtain the physical path of a file under the server root via a request for a non-existent .JSP file, which leaks the pathname in an error message.
CVE-2001-1373(PUBLISHED):MailSafe in Zone Labs ZoneAlarm 2.6 and earlier and ZoneAlarm Pro 2.6 and 2.4 does not block prohibited file types with long file names, which allows remote attackers to send potentially dangerous attachments.
CVE-2001-1374(PUBLISHED):expect before 5.32 searches for its libraries in /var/tmp before other directories, which could allow local users to gain root privileges via a Trojan horse library that is accessed by mkpasswd.
CVE-2001-1375(PUBLISHED):tcl/tk package (tcltk) 8.3.1 searches for its libraries in the current working directory before other directories, which could allow local users to execute arbitrary code via a Trojan horse library that is under a user-controlled directory.
CVE-2001-1376(PUBLISHED):Buffer overflow in digest calculation function of multiple RADIUS implementations allows remote attackers to cause a denial of service and possibly execute arbitrary code via shared secret data.
CVE-2001-1377(PUBLISHED):Multiple RADIUS implementations do not properly validate the Vendor-Length of the Vendor-Specific attribute, which allows remote attackers to cause a denial of service (crash) via a Vendor-Length that is less than 2.
CVE-2001-1378(PUBLISHED):fetchmailconf in fetchmail before 5.7.4 allows local users to overwrite files of other users via a symlink attack on temporary files.
CVE-2001-1379(PUBLISHED):The PostgreSQL authentication modules (1) mod_auth_pgsql 0.9.5, and (2) mod_auth_pgsql_sys 0.9.4, allow remote attackers to bypass authentication and execute arbitrary SQL via a SQL injection attack on the user name.
CVE-2001-1380(PUBLISHED):OpenSSH before 2.9.9, while using keypairs and multiple keys of different types in the ~/.ssh/authorized_keys2 file, may not properly handle the "from" option associated with a key, which could allow remote attackers to login from unauthorized IP addresses.
CVE-2001-1382(PUBLISHED):The "echo simulation" traffic analysis countermeasure in OpenSSH before 2.9.9p2 sends an additional echo packet after the password and carriage return is entered, which could allow remote attackers to determine that the countermeasure is being used.
CVE-2001-1383(PUBLISHED):initscript in setserial 2.17-4 and earlier uses predictable temporary file names, which could allow local users to conduct unauthorized operations on files.
CVE-2001-1384(PUBLISHED):ptrace in Linux 2.2.x through 2.2.19, and 2.4.x through 2.4.9, allows local users to gain root privileges by running ptrace on a setuid or setgid program that itself calls an unprivileged program, such as newgrp.
CVE-2001-1385(PUBLISHED):The Apache module for PHP 4.0.0 through PHP 4.0.4, when disabled with the 'engine = off' option for a virtual host, may disable PHP for other virtual hosts, which could cause Apache to serve the source code of PHP scripts.
CVE-2001-1386(PUBLISHED):WFTPD 3.00 allows remote attackers to read arbitrary files by uploading a (link) file that ends in a ".lnk." extension, which bypasses WFTPD's check for a ".lnk" extension.
CVE-2001-1387(PUBLISHED):iptables-save in iptables before 1.2.4 records the "--reject-with icmp-host-prohibited" rule as "--reject-with tcp-reset," which causes iptables to generate different responses than specified by the administrator, possibly leading to an information leak.
CVE-2001-1388(PUBLISHED):iptables before 1.2.4 does not accurately convert rate limits that are specified on the command line, which could allow attackers or users to generate more or less traffic than intended by the administrator.
CVE-2001-1389(PUBLISHED):Multiple vulnerabilities in xinetd 2.3.0 and earlier, and additional variants until 2.3.3, may allow remote attackers to cause a denial of service or execute arbitrary code, primarily via buffer overflows or improper NULL termination.
CVE-2001-1390(PUBLISHED):Unknown vulnerability in binfmt_misc in the Linux kernel before 2.2.19, related to user pages.
CVE-2001-1391(PUBLISHED):Off-by-one vulnerability in CPIA driver of Linux kernel before 2.2.19 allows users to modify kernel memory.
CVE-2001-1392(PUBLISHED):The Linux kernel before 2.2.19 does not have unregister calls for (1) CPUID and (2) MSR drivers, which could cause a DoS (crash) by unloading and reloading the drivers.
CVE-2001-1393(PUBLISHED):Unknown vulnerability in classifier code for Linux kernel before 2.2.19 could result in denial of service (hang).
CVE-2001-1394(PUBLISHED):Signedness error in (1) getsockopt and (2) setsockopt for Linux kernel before 2.2.19 allows local users to cause a denial of service.
CVE-2001-1395(PUBLISHED):Unknown vulnerability in sockfilter for Linux kernel before 2.2.19 related to "boundary cases," with unknown impact.
CVE-2001-1396(PUBLISHED):Unknown vulnerabilities in strnlen_user for Linux kernel before 2.2.19, with unknown impact.
CVE-2001-1397(PUBLISHED):The System V (SYS5) shared memory implementation for Linux kernel before 2.2.19 could allow attackers to modify recently freed memory.
CVE-2001-1398(PUBLISHED):Masquerading code for Linux kernel before 2.2.19 does not fully check packet lengths in certain cases, which may lead to a vulnerability.
CVE-2001-1399(PUBLISHED):Certain operations in Linux kernel before 2.2.19 on the x86 architecture copy the wrong number of bytes, which might allow attackers to modify memory, aka "User access asm bug on x86."
CVE-2001-1400(PUBLISHED):Unknown vulnerabilities in the UDP port allocation for Linux kernel before 2.2.19 could allow local users to cause a denial of service (deadlock).
CVE-2001-1401(PUBLISHED):Bugzilla before 2.14 does not properly restrict access to confidential bugs, which could allow Bugzilla users to bypass viewing permissions via modified bug id parameters in (1) process_bug.cgi, (2) show_activity.cgi, (3) showvotes.cgi, (4) showdependencytree.cgi, (5) showdependencygraph.cgi, (6) showattachment.cgi, or (7) describecomponents.cgi.
CVE-2001-1402(PUBLISHED):Bugzilla before 2.14 does not properly escape untrusted parameters, which could allow remote attackers to conduct unauthorized activities via cross-site scripting (CSS) and possibly SQL injection attacks on (1) the product or output form variables for reports.cgi, (2) the voteon, bug_id, and user variables for showvotes.cgi, (3) an invalid email address in createaccount.cgi, (4) an invalid ID in showdependencytree.cgi, (5) invalid usernames and other fields in process_bug.cgi, and (6) error messages in buglist.cgi.
CVE-2001-1403(PUBLISHED):Bugzilla before 2.14 includes the username and password in URLs, which could allow attackers to gain privileges by reading the information from the web server logs, or by "shoulder-surfing" and observing the web browser's location bar.
CVE-2001-1404(PUBLISHED):Bugzilla before 2.14 stores user passwords in plaintext and sends password requests in an email message, which could allow attackers to gain privileges.
CVE-2001-1405(PUBLISHED):Bugzilla before 2.14 does not restrict access to sanitycheck.cgi, which allows local users to cause a denial of service (CPU consumption) via a flood of requests to sanitycheck.cgi.
CVE-2001-1406(PUBLISHED):process_bug.cgi in Bugzilla before 2.14 does not set the "groupset" bit when a bug is moved between product groups, which will cause the bug to have the old group's restrictions, which might not be as stringent.
CVE-2001-1407(PUBLISHED):Bugzilla before 2.14 allows Bugzilla users to bypass group security checks by marking a bug as the duplicate of a restricted bug, which adds the user to the CC list of the restricted bug and allows the user to view the bug.
CVE-2001-1408(PUBLISHED):Directory traversal vulnerability in readmsg.php in WebMail 2.0.1 in Cobalt Qube 3 allows remote attackers to read arbitrary files via a .. (dot dot) in the mailbox parameter.
CVE-2001-1409(PUBLISHED):dexconf in XFree86 Xserver 4.1.0-2 creates the /dev/dri directory with insecure permissions (666), which allows local users to replace or create files in the root file system.
CVE-2001-1410(PUBLISHED):Internet Explorer 6 and earlier allows remote attackers to create chromeless windows using the Javascript window.createPopup method, which could allow attackers to simulate a victim's display and conduct unauthorized activities or steal sensitive data via social engineering.
CVE-2001-1411(PUBLISHED):Format string vulnerability in gm4 (aka m4) on Mac OS X may allow local users to gain privileges if gm4 is called by setuid programs.
CVE-2001-1412(PUBLISHED):nidump on MacOS X before 10.3 allows local users to read the encrypted passwords from the password file by specifying passwd as a command line argument.
CVE-2001-1413(PUBLISHED):Stack-based buffer overflow in the comprexx function for ncompress 4.2.4 and earlier, when used in situations that cross security boundaries (such as FTP server), may allow remote attackers to execute arbitrary code via a long filename argument.
CVE-2001-1414(PUBLISHED):The Basic Security Module (BSM) for Solaris 2.5.1, 2.6, 7, and 8 does not log anonymous FTP access, which allows remote attackers to hide their activities, possibly when certain BSM audit files are not present under the FTP root.
CVE-2001-1415(PUBLISHED):vi.recover in OpenBSD before 3.1 allows local users to remove arbitrary zero-byte files such as device nodes.
CVE-2001-1416(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in the log messages in certain Alpha versions of AOL Instant Messenger (AIM) 4.4 allow remote attackers to execute arbitrary web script or HTML via an image in the (1) DATA, (2) STYLE, or (3) BINARY tags.
CVE-2001-1417(PUBLISHED):AOL Instant Messenger (AIM) 4.7 allows remote attackers to cause a denial of service (application hang or crash) via a buddy icon GIF file whose length and width values are larger than the actual image data.
CVE-2001-1418(PUBLISHED):AOL Instant Messenger (AIM) 4.7 allows remote attackers to cause a denial of service (application crash) via a malformed WAV file.
CVE-2001-1419(PUBLISHED):AOL Instant Messenger (AIM) 4.7.2480 and earlier allows remote attackers to cause a denial of service (application crash) via an instant message that contains a large amount of "<!--" HTML comments.
CVE-2001-1420(PUBLISHED):AOL Instant Messenger (AIM) 4.7 allows remote attackers to cause a denial of service (application crash) via a long filename, possibly caused by a buffer overflow.
CVE-2001-1421(PUBLISHED):AOL Instant Messenger (AIM) 4.7 and earlier allows remote attackers to cause a denial of service (application crash) via a large number of different fonts followed by an HTML HR tag.
CVE-2001-1422(PUBLISHED):WinVNC 3.3.3 and earlier generates the same challenge string for multiple connections, which allows remote attackers to bypass VNC authentication by sniffing the challenge and response of other users.
CVE-2001-1423(PUBLISHED):Advanced Poll before 1.61, when using a flat file database, allows remote attackers to gain privileges by setting the logged_in parameter.
CVE-2001-1424(PUBLISHED):Alcatel Speed Touch ADSL modem running firmware KHDSAA.108, KHDSAA.132, KHDSBA.133, and KHDSAA.134 has a blank default password, which allows remote attackers to gain unauthorized access.
CVE-2001-1425(PUBLISHED):The challenge-response authentication of the EXPERT user for Alcatel Speed Touch running firmware KHDSAA.108 and KHDSAA.132 through KHDSAA.134 allows remote attackers to gain privileges by directly computing the response based on information that is provided by the device during login.
CVE-2001-1426(PUBLISHED):Alcatel Speed Touch running firmware KHDSAA.108 and KHDSAA.132 through KHDSAA.134 has a TFTP server running without a password, which allows remote attackers to change firmware versions or the device's configurations.
CVE-2001-1427(PUBLISHED):Unknown vulnerability in ColdFusion Server 2.0 through 4.5.1 SP2 allows remote attackers to overwrite templates with zero byte files via unknown attack vectors.
CVE-2001-1428(PUBLISHED):The (1) FTP and (2) Telnet services in Beck GmbH IPC@Chip are shipped with a default password, which allows remote attackers to gain unauthorized access.
CVE-2001-1429(PUBLISHED):Buffer overflow in mcedit in Midnight Commander 4.5.1 allows local users to cause a denial of service (segmentation fault) and possibly execute arbitrary code via a crafted text file.
CVE-2001-1430(PUBLISHED):Cayman 3220-H DSL Router 1.0 ship without a password set, which allows remote attackers to gain unauthorized access.
CVE-2001-1431(PUBLISHED):Nokia Firewall Appliances running IPSO 3.3 and VPN-1/FireWall-1 4.1 Service Pack 3, IPSO 3.4 and VPN-1/FireWall-1 4.1 Service Pack 4, and IPSO 3.4 or IPSO 3.4.1 and VPN-1/FireWall-1 4.1 Service Pack 5, when SYN Defender is configured in Active Gateway mode, does not properly rewrite the third packet of a TCP three-way handshake to use the NAT IP address, which allows remote attackers to gain sensitive information.
CVE-2001-1432(PUBLISHED):Directory traversal vulnerability in Cherokee Web Server allows remote attackers to read arbitrary files via a .. (dot dot) in the URL.
CVE-2001-1433(PUBLISHED):Cherokee web server before 0.2.7 does not properly drop root privileges after binding to port 80, which could allow remote attackers to gain privileges via other vulnerabilities.
CVE-2001-1434(PUBLISHED):Cisco IOS 12.0(5)XU through 12.1(2) allows remote attackers to read system administration and topology information via an "snmp-server host" command, which creates a readable "community" community string if one has not been previously created.
CVE-2001-1435(PUBLISHED):inetd in Compaq Tru64 UNIX 5.1 allows attackers to cause a denial of service (network connection loss) by causing one of the services handled by inetd to core dump during startup, which causes inetd to stop accepting connections to all of its services.
CVE-2001-1436(PUBLISHED):Dallas Semiconductor iButton DS1991 returns predictable values when given an incorrect password, which makes it easier for users with physical access to conduct dictionary attacks against the device password.
CVE-2001-1437(PUBLISHED):easyScripts easyNews 1.5 allows remote attackers to obtain the full path of the web root via a view request with a non-integer news message id field, which leaks the path in a PHP error message when the script times out.
CVE-2001-1438(PUBLISHED):Handspring Visor 1.0 and 1.0.1 with the VisorPhone Springboard module installed allows remote attackers to cause a denial of service (PalmOS crash and VisorPhone database corruption) by sending a large or crafted SMS image.
CVE-2001-1439(PUBLISHED):Buffer overflow in the text editor functionality in HP-UX 10.01 through 11.04 on HP9000 Series 700 and Series 800 allows local users to cause a denial of service ("system availability") via text editors such as (1) e, (2) ex, (3) vi, (4) edit, (5) view, and (6) vedit.
CVE-2001-1440(PUBLISHED):Unknown vulnerability in login for AIX 5.1L, when using loadable authentication modules, allows remote attackers to gain access to the system.
CVE-2001-1441(PUBLISHED):Cross-site scripting (XSS) vulnerability in VisualAge for Java 3.5 Professional allows remote attackers to execute JavaScript on other clients via the URL, which injects the script in the resulting error message.
CVE-2001-1442(PUBLISHED):Buffer overflow in innfeed for ISC InterNetNews (INN) before 2.3.0 allows local users in the "news" group to gain privileges via a long -c command line argument.
CVE-2001-1443(PUBLISHED):KTH Kerberos IV and Kerberos V (Heimdal) for Telnet clients do not encrypt connections if the server does not support the requested encryption, which allows remote attackers to read communications via a man-in-the-middle attack.
CVE-2001-1444(PUBLISHED):The Kerberos Telnet protocol, as implemented by KTH Kerberos IV and Kerberos V (Heimdal), does not encrypt authentication and encryption options sent from the server, which allows remote attackers to downgrade authentication and encryption mechanisms via a man-in-the-middle attack.
CVE-2001-1445(PUBLISHED):Unknown vulnerability in the SMTP server in Lotus Domino 5.0 through 5.7 allows remote attackers to bypass mail relaying restrictions via crafted e-mail addresses in "RCPT TO" commands.
CVE-2001-1446(PUBLISHED):Find-By-Content in Mac OS X 10.0 through 10.0.4 creates world-readable index files named .FBCIndex in every directory, which allows remote attackers to learn the contents of files in web accessible directories.
CVE-2001-1447(PUBLISHED):NetInfo Manager for Mac OS X 10.0 through 10.1 allows local users to gain root privileges by opening applications using the (1) "recent items" and (2) "services" menus, which causes the applications to run with root privileges.
CVE-2001-1448(PUBLISHED):Magic eDeveloper Enterprise Edition 8.30-5 and earlier allows local users to overwrite arbitrary files and possibly execute code via a symlink attack on temporary files created by the (1) mkuserproc, (2) mgrnt, and (3) mgdatasrvr.sc scripts.
CVE-2001-1449(PUBLISHED):The default installation of Apache before 1.3.19 on Mandrake Linux 7.1 through 8.0 and Linux Corporate Server 1.0.1 allows remote attackers to list the directory index of arbitrary web directories.
CVE-2001-1450(PUBLISHED):Microsoft Internet Explorer 5.0 through 6.0 allows attackers to cause a denial of service (browser crash) via a crafted FTP URL such as "/.#./".
CVE-2001-1451(PUBLISHED):Memory leak in the SNMP LAN Manager (LANMAN) MIB extension for Microsoft Windows 2000 before SP3, when the Print Spooler is not running, allows remote attackers to cause a denial of service (memory consumption) via a large number of GET or GETNEXT requests.
CVE-2001-1452(PUBLISHED):By default, DNS servers on Windows NT 4.0 and Windows 2000 Server cache glue records received from non-delegated name servers, which allows remote attackers to poison the DNS cache via spoofed DNS responses.
CVE-2001-1453(PUBLISHED):Buffer overflow in libmysqlclient.so in MySQL 3.23.33 and earlier allows remote attackers to execute arbitrary code via a long host parameter.
CVE-2001-1454(PUBLISHED):Buffer overflow in MySQL before 3.23.33 allows remote attackers to execute arbitrary code via a long drop database request.
CVE-2001-1455(PUBLISHED):Netegrity SiteMinder 3.6 through 4.5.1 allows remote attackers to bypass filtering via URLs containing Unicode characters.
CVE-2001-1456(PUBLISHED):Buffer overflow in the (1) smap/smapd and (2) CSMAP daemons for Gauntlet Firewall 5.0 through 6.0 allows remote attackers to execute arbitrary code via a crafted mail message.
CVE-2001-1457(PUBLISHED):Buffer overflow in CrazyWWWBoard 2000p4 and 2000LEp5 allows remote attackers to execute arbitrary code via a long HTTP_USER_AGENT CGI environment variable.
CVE-2001-1458(PUBLISHED):Directory traversal vulnerability in Novell GroupWise 5.5 and 6.0 allows remote attackers to read arbitrary files via a request for /servlet/webacc?User.html= that contains "../" (dot dot) sequences and a null character.
CVE-2001-1459(PUBLISHED):OpenSSH 2.9 and earlier does not initiate a Pluggable Authentication Module (PAM) session if commands are executed with no pty, which allows local users to bypass resource limits (rlimits) set in pam.d.
CVE-2001-1460(PUBLISHED):SQL injection vulnerability in article.php in PostNuke 0.62 through 0.64 allows remote attackers to bypass authentication via the user parameter.
CVE-2001-1461(PUBLISHED):Directory traversal vulnerability in WebID in RSA Security SecurID 5.0 as used by ACE/Agent for Windows, Windows NT and Windows 2000 allows attackers to access restricted resources via URL-encoded (1) /.. or (2) \.. sequences.
CVE-2001-1462(PUBLISHED):WebID in RSA Security SecurID 5.0 as used by ACE/Agent for Windows, Windows NT and Windows 2000 allows attackers to cause the WebID agent to enter debug mode via a URL containing null characters, which may allow attackers to obtain sensitive information.
CVE-2001-1463(PUBLISHED):The remote administration client for RhinoSoft Serv-U 3.0 sends the user password in plaintext even when S/KEY One-Time Password (OTP) authentication is enabled, which allows remote attackers to sniff passwords.
CVE-2001-1464(PUBLISHED):Crystal Reports, when displaying data for a password protected database using HTML pages, embeds the username and password in cleartext in the HTML page and the URL, which allows remote attackers to obtain passwords.
CVE-2001-1465(PUBLISHED):SurfControl SuperScout only filters packets containing both an HTTP GET request and a Host header, which allows local users to bypass filtering by fragmenting packets so that no packet contains both data elements.
CVE-2001-1466(PUBLISHED):Buffer overflow in VanDyke SecureCRT before 3.4.2, when using the SSH-1 protocol, allows remote attackers to execute arbitrary code via a long (1) username or (2) password.
CVE-2001-1467(PUBLISHED):mkpasswd in expect 5.2.8, as used by Red Hat Linux 6.2 through 7.0, seeds its random number generator with its process ID, which limits the space of possible seeds and makes it easier for attackers to conduct brute force password attacks.
CVE-2001-1468(PUBLISHED):PHP remote file inclusion vulnerability in checklogin.php in phpSecurePages 0.24 and earlier allows remote attackers to execute arbitrary PHP code by modifying the cfgProgDir parameter to reference a URL on a remote web server that contains the code.
CVE-2001-1469(PUBLISHED):The RC4 stream cipher as used by SSH1 allows remote attackers to modify messages without detection by XORing the original message's cyclic redundancy check (CRC) with the CRC of a mask consisting of all the bits of the original message that were modified.
CVE-2001-1470(PUBLISHED):The IDEA cipher as implemented by SSH1 does not protect the final block of a message against modification, which allows remote attackers to modify the block without detection by changing its cyclic redundancy check (CRC) to match the modifications to the message.
CVE-2001-1471(PUBLISHED):prefs.php in phpBB 1.4.0 and earlier allows remote authenticated users to execute arbitrary PHP code via an invalid language value, which prevents the variables (1) $l_statsblock in prefs.php or (2) $l_privnotify in auth.php from being properly initialized, which can be modified by the user and later used in an eval statement.
CVE-2001-1472(PUBLISHED):SQL injection vulnerability in prefs.php in phpBB 1.4.0 and 1.4.1 allows remote authenticated users to execute arbitrary SQL commands and gain administrative access via the viewemail parameter.
CVE-2001-1473(PUBLISHED):The SSH-1 protocol allows remote servers to conduct man-in-the-middle attacks and replay a client challenge response to a target server by creating a Session ID that matches the Session ID of the target, but which uses a public key pair that is weaker than the target's public key, which allows the attacker to compute the corresponding private key and use the target's Session ID with the compromised key pair to masquerade as the target.
CVE-2001-1474(PUBLISHED):SSH before 2.0 disables host key checking when connecting to the localhost, which allows remote attackers to silently redirect connections to the localhost by poisoning the client's DNS cache.
CVE-2001-1475(PUBLISHED):SSH before 2.0, when using RC4 and password authentication, allows remote attackers to replay messages until a new server key (VK) is generated.
CVE-2001-1476(PUBLISHED):SSH before 2.0, with RC4 encryption and the "disallow NULL passwords" option enabled, makes it easier for remote attackers to guess portions of user passwords by replaying user sessions with certain modifications, which trigger different messages depending on whether the guess is correct or not.
CVE-2001-1477(PUBLISHED):The Domain gateway in BEA Tuxedo 7.1 does not perform authorization checks for imported services and qspaces on remote domains, even when an ACL exists, which allows users to access services in a remote domain.
CVE-2001-1478(PUBLISHED):Buffer overflow in xlock in UnixWare 7.1.0 and 7.1.1 and Open Unix 8.0.0 allows local users to execute arbitrary code.
CVE-2001-1479(PUBLISHED):smcboot in Sun SMC (Sun Management Center) 2.0 in Solaris 8 allows local users to delete arbitrary files via a symlink attack on /tmp/smc$SMC_PORT.
CVE-2001-1480(PUBLISHED):Java Runtime Environment (JRE) and SDK 1.2 through 1.3.0_04 allows untrusted applets to access the system clipboard.
CVE-2001-1481(PUBLISHED):Xitami 2.4 through 2.5 b4 stores the Administrator password in plaintext in the default.aut file, whose default permissions are world-readable, which allows remote attackers to gain privileges.
CVE-2001-1482(PUBLISHED):SQL injection vulnerability in bb_memberlist.php for phpBB 1.4.2 allows remote attackers to execute arbitrary SQL queries via the $sortby variable.
CVE-2001-1483(PUBLISHED):One-Time Passwords In Everything (a.k.a OPIE) 2.32 and 2.4 allows remote attackers to determine the existence of user accounts by printing random passphrases if the user account does not exist and static passphrases if the user account does exist.
CVE-2001-1484(PUBLISHED):Alcatel ADSL modems allow remote attackers to access the Trivial File Transfer Protocol (TFTP) to modify firmware and configuration via a bounce attack from a system on the local area network (LAN) side, which is allowed to access TFTP without authentication.
CVE-2001-1487(PUBLISHED):popauth utility in Qualcomm Qpopper 4.0 and earlier allows local users to overwrite arbitrary files and execute commands as the pop user via a symlink attack on the -trace file option.
CVE-2001-1488(PUBLISHED):Open Projects Network Internet Relay Chat (IRC) daemon u2.10.05.18 does not perform a double-reverse DNS lookup, which allows remote attackers to spoof any valid hostname on the Internet.  NOTE: a followup post suggests that this is not an issue in the daemon.
CVE-2001-1489(PUBLISHED):Microsoft Internet Explorer 6 allows remote attackers to cause a denial of service (CPU consumption and memory leak) via a web page with a large number of images.
CVE-2001-1490(PUBLISHED):Mozilla 0.9.6 allows remote attackers to cause a denial of service (CPU consumption and memory leak) via a web page with a large number of images.
CVE-2001-1491(PUBLISHED):Opera 5.11 allows remote attackers to cause a denial of service (CPU consumption and memory leak) via a web page with a large number of images.
CVE-2001-1494(PUBLISHED):script command in the util-linux package before 2.11n allows local users to overwrite arbitrary files by setting a hardlink from the typescript log file to any file on the system, then having root execute the script command.
CVE-2001-1495(PUBLISHED):network_query.php in Network Query Tool 1.0 allows remote attackers to execute arbitrary commands via shell metacharacters in the target parameter.
CVE-2001-1496(PUBLISHED):Off-by-one buffer overflow in Basic Authentication in Acme Labs thttpd 1.95 through 2.20 allows remote attackers to cause a denial of service and possibly execute arbitrary code.
CVE-2001-1497(PUBLISHED):Microsoft Internet Explorer 4.0 through 6.0 could allow local users to differentiate between alphanumeric and non-alphanumeric characters used in a password by pressing certain control keys that jump between non-alphanumeric characters, which makes it easier to conduct a brute-force password guessing attack.
CVE-2001-1498(PUBLISHED):Buffer overflow in mod_bf 0.2 allows local users to execute arbitrary commands via a long script.
CVE-2001-1499(PUBLISHED):Check Point VPN-1 4.1SP4 using SecuRemote returns different error messages for valid and invalid users, with prompts that vary depending on the authentication method being used, which makes it easier for remote attackers to conduct brute force attacks.
CVE-2001-1500(PUBLISHED):ProFTPD 1.2.2rc2, and possibly other versions, does not properly verify reverse-resolved hostnames by performing forward resolution, which allows remote attackers to bypass ACLs or cause an incorrect client hostname to be logged.
CVE-2001-1501(PUBLISHED):The glob functionality in ProFTPD 1.2.1, and possibly other versions allows remote attackers to cause a denial of service (CPU and memory consumption) via commands with large numbers of wildcard and other special characters, as demonstrated using an ls command with multiple (1) "*/..", (2) "*/.*", or (3) ".*./*?/" sequences in the argument.
CVE-2001-1502(PUBLISHED):webcart.cgi in Mountain Network Systems WebCart 8.4 allows remote attackers to execute arbitrary commands via shell metacharacters in the NEXTPAGE parameter.
CVE-2001-1503(PUBLISHED):The finger daemon (in.fingerd) in Sun Solaris 2.5 through 8 and SunOS 5.5 through 5.8 allows remote attackers to list all accounts on a host by typing finger 'a b c d e f g h'@host.
CVE-2001-1504(PUBLISHED):Lotus Notes R5 Client 4.6 allows remote attackers to execute arbitrary commands via a Lotus Notes object with code in an event, which is automatically executed when the user processes the e-mail message.
CVE-2001-1505(PUBLISHED):tinc 1.0pre3 and 1.0pre4 allows remote attackers to inject data into user sessions by sniffing and replaying packets.
CVE-2001-1506(PUBLISHED):Unknown vulnerability in the file system protection subsystem in HP Secure OS Software for Linux 1.0 allows additional user privileges on some files beyond what is specified in the file system protection rules, which allows local users to conduct unauthorized operations on restricted files.
CVE-2001-1507(PUBLISHED):OpenSSH before 3.0.1 with Kerberos V enabled does not properly authenticate users, which could allow remote attackers to login unchallenged.
CVE-2001-1508(PUBLISHED):Buffer overflow in lpstat in SCO OpenServer 5.0 through 5.0.6a allows local users to execute arbitrary code as group bin via a long command line argument.
CVE-2001-1509(PUBLISHED):geteuid in Itanium Architecture (IA) running on HP-UX 11.20 does not properly identify a user's effective user id, which could allow local users to gain privileges.
CVE-2001-1510(PUBLISHED):Allaire JRun 2.3.3, 3.0 and 3.1 running on IIS 4.0 and 5.0, iPlanet, Apache, JRun web server (JWS), and possibly other web servers allows remote attackers to read arbitrary files and directories by appending (1) "%3f.jsp", (2) "?.jsp" or (3) "?" to the requested URL.
CVE-2001-1511(PUBLISHED):JRun 3.0 and 3.1 running on JRun Web Server (JWS) and IIS allows remote attackers to read arbitrary JavaServer Pages (JSP) source code via a request URL containing the source filename ending in (1) "jsp%00" or (2) "js%2570".
CVE-2001-1512(PUBLISHED):Unknown vulnerability in Allaire JRun 3.1 allows remote attackers to directly access the WEB-INF and META-INF directories and execute arbitrary JavaServer Pages (JSP), a variant of CVE-2000-1050.
CVE-2001-1513(PUBLISHED):Macromedia JRun 3.0 and 3.1 allows remote attackers to obtain duplicate active user session IDs and perform actions as other users via a URL request for the web application directory without the trailing '/' (slash), as demonstrated using ctx.
CVE-2001-1514(PUBLISHED):ColdFusion 4.5 and 5, when running on Windows with the advanced security sandbox type set to "operating system," does not properly pass security context to (1) child processes created with <CFEXECUTE> and (2) child processes that call the CreateProcess function and are executed with <CFOBJECT> or end with the CFX extension, which allows attackers to execute programs with the permissions of the System account.
CVE-2001-1515(PUBLISHED):Macintosh clients, when using NT file system volumes on Windows 2000 SP1, create subdirectories and automatically modify the inherited NTFS permissions, which may cause the directories to have less restrictive permissions than intended.
CVE-2001-1516(PUBLISHED):Cross-site scripting (XSS) vulnerability in phpReview 0.9.0 rc2 and earlier allows remote attackers to inject arbitrary web script or HTML via user-submitted reviews.
CVE-2001-1517(PUBLISHED):RunAs (runas.exe) in Windows 2000 stores cleartext authentication information in memory, which could allow attackers to obtain usernames and passwords by executing a process that is allocated the same memory page after termination of a RunAs command.  NOTE: the vendor disputes this issue, saying that administrative privileges are already required to exploit it, and the original researcher did not respond to requests for additional information
CVE-2001-1518(PUBLISHED):RunAs (runas.exe) in Windows 2000 only creates one session instance at a time, which allows local users to cause a denial of service (RunAs hang) by creating a named pipe session with the authentication server without any request for service.  NOTE: the vendor disputes this vulnerability, however the vendor also presents a scenario in which other users could be affected if running on a Terminal Server. Therefore this is a vulnerability.
CVE-2001-1519(PUBLISHED):RunAs (runas.exe) in Windows 2000 allows local users to create a spoofed named pipe when the service is stopped, then capture cleartext usernames and passwords when clients connect to the service.  NOTE: the vendor disputes this issue, saying that administrative privileges are already required to exploit it
CVE-2001-1520(PUBLISHED):Xircom REX 6000 allows local users to obtain the 10 digit PIN by starting a serial monitor, connecting to the personal digital assistant (PDA) via Rextools, and capturing the cleartext PIN.
CVE-2001-1521(PUBLISHED):Cross-site scripting (XSS) vulnerability in user.php in PostNuke 0.64 allows remote attackers to inject arbitrary web script or HTML via the uname parameter.
CVE-2001-1522(PUBLISHED):Cross-site scripting (XSS) vulnerability in im.php in IMessenger for PHP-Nuke allows remote attackers to inject arbitrary web script or HTML via a message.
CVE-2001-1523(PUBLISHED):Cross-site scripting (XSS) vulnerability in the DMOZGateway module for PHP-Nuke allows remote attackers to inject arbitrary web script or HTML via the topic parameter.
CVE-2001-1524(PUBLISHED):Cross-site scripting (XSS) vulnerability in PHP-Nuke 5.3.1 and earlier allows remote attackers to inject arbitrary web script or HTML via the (1) uname parameter in user.php, (2) ttitle, letter and file parameters in modules.php, (3) subject, story and storyext parameters in submit.php, (4) upload parameter in admin.php and (5) fname parameter in friend.php.
CVE-2001-1525(PUBLISHED):Directory traversal vulnerability in the comments action in easyNews 1.5 and earlier allows remote attackers to modify news.dat, template.dat and possibly other files via a ".." in the cid parameter.
CVE-2001-1526(PUBLISHED):Cross-site scripting (XSS) vulnerability in the comments action in index.php in easyNews 1.5 and earlier allows remote attackers to inject arbitrary web script or HTML via the zeit parameter.
CVE-2001-1527(PUBLISHED):easyNews 1.5 and earlier stores administration passwords in cleartext in settings.php, which allows local users to obtain the passwords and gain access.
CVE-2001-1528(PUBLISHED):AmTote International homebet program returns different error messages when invalid account numbers and PIN codes are provided, which allows remote attackers to determine the existence of valid account numbers via a brute force attack.
CVE-2001-1529(PUBLISHED):Buffer overflow in rpc.yppasswdd (yppasswd server) in AIX allows attackers to gain unauthorized access via a long string.  NOTE: due to lack of details in the vendor advisory, it is not clear if this is the same issue as CVE-2001-0779.
CVE-2001-1530(PUBLISHED):run.cgi in Webmin 0.80 and 0.88 creates temporary files with world-writable permissions, which allows local users to execute arbitrary commands.
CVE-2001-1531(PUBLISHED):Buffer overflow in Claris Emailer 2.0v2 allows remote attackers to cause a denial of service and possibly execute arbitrary code via an email attachment with a long filename.
CVE-2001-1532(PUBLISHED):WebX stores authentication information in the HTTP_REFERER variable, which is included in URL links within bulletin board messages posted by users, which could allow remote attackers to hijack user sessions.
CVE-2001-1533(PUBLISHED):Microsoft Internet Security and Acceleration (ISA) Server 2000 allows remote attackers to cause a denial of service via a flood of fragmented UDP packets.  NOTE: the vendor disputes this issue, saying that it requires high bandwidth to exploit, and the server does not experience any instability.  Therefore this "laws of physics" issue might not be included in CVE
CVE-2001-1534(PUBLISHED):mod_usertrack in Apache 1.3.11 through 1.3.20 generates session ID's using predictable information including host IP address, system time and server process ID, which allows local users to obtain session ID's and bypass authentication when these session ID's are used for authentication.
CVE-2001-1535(PUBLISHED):Slashcode 2.0 creates new accounts with an 8-character random password, which could allow local users to obtain session ID's from cookies and gain unauthorized access via a brute force attack.
CVE-2001-1536(PUBLISHED):Autogalaxy stores usernames and passwords in cleartext in cookies, which makes it easier for remote attackers to obtain authentication information and gain unauthorized access via sniffing or a cross-site scripting attack.
CVE-2001-1537(PUBLISHED):The default "basic" security setting' in config.php for TWIG webmail 2.7.4 and earlier stores cleartext usernames and passwords in cookies, which could allow attackers to obtain authentication information and gain privileges.
CVE-2001-1538(PUBLISHED):SpeedXess HA-120 DSL router has a default administrative password of "speedxess", which allows remote attackers to gain access.
CVE-2001-1539(PUBLISHED):Stack consumption vulnerability in Internet Explorer The JavaScript settimeout function in Internet Explorer allows remote attackers to cause a denial of service (crash) via the JavaScript settimeout function.  NOTE: the vendor could not reproduce the problem.
CVE-2001-1540(PUBLISHED):IPRoute 0.973, 0.974 and 1.18 allows remote attackers to cause a denial of service via fragmented IP packets that split the TCP header.
CVE-2001-1541(PUBLISHED):Buffer overflow in Unix-to-Unix Copy Protocol (UUCP) in BSDI BSD/OS 3.0 through 4.2 allows local users to execute arbitrary code via a long command line argument.
CVE-2001-1542(PUBLISHED):NAI WebShield SMTP 4.5 and possibly 4.5 MR1a does not filter improperly MIME encoded email attachments, which could allow remote attackers to bypass filtering and possibly execute arbitrary code in email clients that process the invalid attachments.
CVE-2001-1543(PUBLISHED):Axis network camera 2120, 2110, 2100, 200+ and 200 contains a default administration password "pass", which allows remote attackers to gain access to the camera.
CVE-2001-1544(PUBLISHED):Directory traversal vulnerability in Macromedia JRun Web Server (JWS) 2.3.3, 3.0 and 3.1 allows remote attackers to read arbitrary files via a .. (dot dot) in the HTTP GET request.
CVE-2001-1545(PUBLISHED):Macromedia JRun 3.0 and 3.1 appends the jsessionid to URL requests (a.k.a. rewriting) when client browsers have cookies enabled, which allows remote attackers to obtain session IDs and hijack sessions via HTTP referrer fields or sniffing.
CVE-2001-1546(PUBLISHED):Pathways Homecare 6.5 uses weak encryption for user names and passwords, which allows local users to gain privileges by recovering the passwords from the pwhc.ini file.
CVE-2001-1547(PUBLISHED):Outlook Express 6.0, with "Do not allow attachments to be saved or opened that could potentially be a virus" enabled, does not block email attachments from forwarded messages, which could allow remote attackers to execute arbitrary code.
CVE-2001-1548(PUBLISHED):ZoneAlarm 2.1 through 2.6 and ZoneAlarm Pro 2.4 and 2.6 allows local users to bypass filtering via non-standard TCP packets created with non-Windows protocol adapters.
CVE-2001-1549(PUBLISHED):Tiny Personal Firewall 1.0 and 2.0 allows local users to bypass filtering via non-standard TCP packets created with non-Windows protocol adapters.
CVE-2001-1550(PUBLISHED):CentraOne 5.2 and Centra ASP with basic authentication enabled creates world-writable base64 encoded log files, which allows local users to obtain cleartext passwords from decoded log files and impersonate users.
CVE-2001-1551(PUBLISHED):Linux kernel 2.2.19 enables CAP_SYS_RESOURCE for setuid processes, which allows local users to exceed disk quota restrictions during execution of setuid programs.
CVE-2001-1552(PUBLISHED):ssdpsrv.exe in Windows ME allows remote attackers to cause a denial of service by sending multiple newlines in a Simple Service Discovery Protocol (SSDP) message.  NOTE: multiple replies to the original post state that the problem could not be reproduced.
CVE-2001-1553(PUBLISHED):Buffer overflow in setiathome for SETI@home 3.03, if installed setuid, could allow local users to execute arbitrary code via long command line options (1) socks_server, (2) socks_user, and (3) socks_passwd. NOTE: since the default configuration of setiathome is not setuid, perhaps this issue should not be included in CVE.
CVE-2001-1554(PUBLISHED):IBM AIX 430 does not properly unlock IPPMTU_LOCK, which allows remote attackers to cause a denial of service (hang) via Path Maximum Transmit Unit (PMTU) IP packets.
CVE-2001-1555(PUBLISHED):pt_chmod in Solaris 8 does not call fdetach to reset terminal privileges when users log out of terminals, which allows local users to write to other users' terminals by modifying the ACL of a TTY.
CVE-2001-1556(PUBLISHED):The log files in Apache web server contain information directly supplied by clients and does not filter or quote control characters, which could allow remote attackers to hide HTTP requests and spoof source IP addresses when logs are viewed with UNIX programs such as cat, tail, and grep.
CVE-2001-1557(PUBLISHED):Buffer overflow in ftpd in IBM AIX 4.3 and 5.1 allows attackers to gain privileges.
CVE-2001-1558(PUBLISHED):Unknown vulnerability in IP defragmenter (frag2) in Snort before 1.8.3 allows attackers to cause a denial of service (crash).
CVE-2001-1559(PUBLISHED):The uipc system calls (uipc_syscalls.c) in OpenBSD 2.9 and 3.0 provide user mode return instead of versus rval kernel mode values to the fdrelease function, which allows local users to cause a denial of service and trigger a null dereference.
CVE-2001-1560(PUBLISHED):Win32k.sys (aka Graphics Device Interface (GDI)) in Windows 2000 and XP allows local users to cause a denial of service (system crash) by calling the ShowWindow function after receiving a WM_NCCREATE message.
CVE-2001-1561(PUBLISHED):Buffer overflow in Xvt 2.1 in Debian Linux 2.2 allows local users to execute arbitrary code via long (1) -name and (2) -T arguments.
CVE-2001-1562(PUBLISHED):Format string vulnerability in nvi before 1.79 allows local users to gain privileges via format string specifiers in a filename.
CVE-2001-1563(PUBLISHED):Unknown vulnerability in Tomcat 3.2.1 running on HP Secure OS for Linux 1.0 allows attackers to access servlet resources.  NOTE: due to the vagueness of the vendor advisory, it is not clear whether this issue is already covered by other CVE identifiers.
CVE-2001-1564(PUBLISHED):setrlimit in HP-UX 10.01, 10.10, 10.24, 10.20, 11.00, 11.04 and 11.11 does not properly enforce core file size on processes after setuid or setgid privileges are dropped, which could allow local users to cause a denial of service by exhausting available disk space.
CVE-2001-1565(PUBLISHED):Point to Point Protocol daemon (pppd) in MacOS x 10.0 and 10.1 through 10.1.5 provides the username and password on the command line, which allows local users to obtain authentication information via the ps command.
CVE-2001-1566(PUBLISHED):Format string vulnerability in libvanessa_logger 0.0.1 in Perdition 0.1.8 allows remote attackers to execute arbitrary code via format string specifiers in the __vanessa_logger_log function.
CVE-2001-1567(PUBLISHED):Lotus Domino server 5.0.9a and earlier allows remote attackers to bypass security restrictions and view Notes database files and possibly sensitive Notes template files (.ntf) via an HTTP request with a large number of "+" characters before the .nsf file extension, which are converted to spaces by Domino.
CVE-2001-1568(PUBLISHED):CMG WAP gateway does not verify the fully qualified domain name URL with X.509 certificates from root certificate authorities, which allows remote attackers to spoof SSL certificates via a man-in-the-middle attack.
CVE-2001-1569(PUBLISHED):Openwave WAP gateway does not verify the fully qualified domain name URL with X.509 certificates from root certificate authorities, which allows remote attackers to spoof SSL certificates via a man-in-the-middle attack.
CVE-2001-1570(PUBLISHED):Windows XP with fast user switching and account lockout enabled allows local users to deny user account access by setting the fast user switch to the same user (self) multiple times, which causes other accounts to be locked out.
CVE-2001-1571(PUBLISHED):The Remote Desktop client in Windows XP sends the most recent user account name in cleartext, which could allow remote attackers to obtain terminal server user account names via sniffing.
CVE-2001-1572(PUBLISHED):The MAC module in Netfilter in Linux kernel 2.4.1 through 2.4.11, when configured to filter based on MAC addresses, allows remote attackers to bypass packet filters via small packets.
CVE-2001-1573(PUBLISHED):Buffer overflow in smtpscan.dll for Trend Micro InterScan VirusWall 3.51 for Windows NT has allows remote attackers to execute arbitrary code via a certain configuration parameter.
CVE-2001-1574(PUBLISHED):Buffer overflow in (1) HttpSaveCVP.dll and (2) HttpSaveCSP.dll in Trend Micro InterScan VirusWall 3.5.1 allows remote attackers to execute arbitrary code.
CVE-2001-1575(PUBLISHED):Apple Personal Web Sharing (PWS) 1.1, 1.5, and 1.5.5, when Web Sharing authentication is enabled, allows remote attackers to cause a denial of service via a long password, possibly due to a buffer overflow.
CVE-2001-1576(PUBLISHED):Buffer overflow in cron in Caldera UnixWare 7 allows local users to execute arbitrary code via a command line argument.
CVE-2001-1577(PUBLISHED):Unknown vulnerability in CDE in Caldera OpenUnix 7.1.0, 7.1.1, and 8.0 allows an xterm session to gain privileges when the session is reused.
CVE-2001-1578(PUBLISHED):Unknown vulnerability in SCO OpenServer 5.0.6 and earlier allows local users to modify critical information such as certain CPU registers and segment descriptors.
CVE-2001-1579(PUBLISHED):The timed program (in.timed) in UnixWare 7 and OpenUnix 8.0.0 does not properly terminate certain strings with a null, which allows remote attackers to cause a denial of service.
CVE-2001-1580(PUBLISHED):Directory traversal vulnerability in ScriptEase viewcode.jse for Netware 5.1 before 5.1 SP3 allows remote attackers to read arbitrary files via ".." sequences in the query string.
CVE-2001-1581(PUBLISHED):The File Blocker feature in Clearswift MAILsweeper for SMTP 4.2 allows remote attackers to bypass e-mail attachment filtering policies via a modified name in a Content-Type header.
CVE-2001-1582(PUBLISHED):Buffer overflow in the LDAP naming services library (libsldap) in Sun Solaris 8 allows local users to execute arbitrary code via a long LDAP_OPTIONS environment variable to a privileged program that uses libsldap.
CVE-2001-1583(PUBLISHED):lpd daemon (in.lpd) in Solaris 8 and earlier allows remote attackers to execute arbitrary commands via a job request with a crafted control file that is not properly handled when lpd invokes a mail program. NOTE: this might be the same vulnerability as CVE-2000-1220.
CVE-2001-1584(PUBLISHED):CardBoard 2.4 greeting card CGI by Michael Barretto allows remote attackers to execute arbitrary commands via shell metacharacters in the recipient field.
CVE-2001-1585(PUBLISHED):SSH protocol 2 (aka SSH-2) public key authentication in the development snapshot of OpenSSH 2.3.1, available from 2001-01-18 through 2001-02-08, does not perform a challenge-response step to ensure that the client has the proper private key, which allows remote attackers to bypass authentication as other users by supplying a public key from that user's authorized_keys file.
CVE-2001-1586(PUBLISHED):Directory traversal vulnerability in SimpleServer:WWW 1.13 and earlier allows remote attackers to execute arbitrary programs via encoded ../ ("%2E%2E%2F%") sequences in a request to the cgi-bin/ directory, a different vulnerability than CVE-2000-0664.
CVE-2001-1587(PUBLISHED):NWFTPD.nlm before 5.01w in the FTP server in Novell NetWare allows remote attackers to cause a denial of service (abend) via an anonymous STOU command.
CVE-2001-1593(PUBLISHED):The tempname_ensure function in lib/routines.h in a2ps 4.14 and earlier, as used by the spy_user function and possibly other functions, allows local users to modify arbitrary files via a symlink attack on a temporary file.
CVE-2001-1594(PUBLISHED):GE Healthcare eNTEGRA P&R has a password of (1) entegra for the entegra user, (2) passme for the super user of the Polestar/Polestar-i Starlink 4 upgrade, (3) 0 for the entegra user of the Codonics printer FTP service, (4) eNTEGRA for the eNTEGRA P&R user account, (5) insite for the WinVNC Login, and possibly other accounts, which has unspecified impact and attack vectors.  NOTE: it is not clear whether this password is default, hardcoded, or dependent on another system or product that requires a fixed value.
