<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每周威胁情报 | CTI 威胁情报分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 70px;
            background-color: #f8f9fa;
        }
        .card {
            box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
            border: none;
            border-radius: 0.5rem;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0,0,0,.075);
            padding: 1rem;
        }
        .intel-date {
            font-size: 14px;
            color: #6c757d;
        }
        .threat-badge {
            font-size: 12px;
            padding: 3px 8px;
        }
        .threat-high {
            background-color: #dc3545;
        }
        .threat-medium {
            background-color: #fd7e14;
        }
        .threat-low {
            background-color: #ffc107;
            color: #212529;
        }
        .threat-info {
            background-color: #0dcaf0;
        }
        .page-header {
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        .ioc-badge {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            vertical-align: top;
            font-size: 11px;
            padding: 4px 8px;
            margin: 2px;
            cursor: pointer;
            position: relative;
        }
        .ioc-badge:hover {
            max-width: none;
            white-space: normal;
            word-break: break-all;
            z-index: 1000;
            position: relative;
            background-color: #495057 !important;
        }
        .ioc-container {
            max-height: 80px;
            overflow-y: auto;
            padding: 5px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-active {
            background-color: #28a745;
        }
        .status-inactive {
            background-color: #dc3545;
        }
        .status-pending {
            background-color: #ffc107;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .intel-card:hover {
            transform: translateY(-3px);
            transition: transform 0.3s ease;
        }
        .task-controls {
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .empty-state {
            text-align: center;
            padding: 3rem 0;
        }
        .empty-state i {
            font-size: 3rem;
            color: #adb5bd;
            margin-bottom: 1rem;
            display: block;
        }
         .pagination {
        display: flex !important;
        flex-direction: row !important;
        }
        
        .pagination .page-item {
            display: inline-block;
        }
        
        .pagination .page-link {
            min-width: 40px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <i class="bi bi-shield-lock me-2 text-primary"></i>
                <strong>CTI 威胁情报分析系统</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="bi bi-house-door"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis"><i class="bi bi-search"></i> 分析工具</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/config_management"><i class="bi bi-clock-history"></i> 爬取网站配置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-1"><i class="bi bi-clock-history text-primary"></i> 每周威胁情报</h1>
                <p class="text-muted">展示最新一周内收集的安全威胁情报数据</p>
            </div>
            <div>
                <button class="btn btn-outline-secondary" id="refresh-btn">
                    <i class="bi bi-arrow-clockwise"></i> 刷新数据
                </button>
            </div>
        </div>

        
        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="card-title" id="total-articles">-</h3>
                        <p class="card-text text-muted">本周文章总数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="card-title" id="high-threats">-</h3>
                        <p class="card-text text-danger">高危威胁</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="card-title" id="vulns-found">-</h3>
                        <p class="card-text text-warning">发现漏洞数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="card-title" id="iocs-found">-</h3>
                        <p class="card-text text-info">IOC指标数</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据过滤栏 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="filter-threat" class="form-label">威胁类型</label>
                            <select id="filter-threat" class="form-select">
                                <option value="all">全部类型</option>
                                <option value="malware">恶意软件</option>
                                <option value="vulnerability">漏洞</option>
                                <option value="apt">APT组织</option>
                                <option value="ransom">勒索软件</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="filter-severity" class="form-label">严重程度</label>
                            <select id="filter-severity" class="form-select">
                                <option value="all">全部等级</option>
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="search-intel" class="form-label">搜索关键词</label>
                            <input type="text" id="search-intel" class="form-control" placeholder="输入关键词搜索...">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 加载指示器 -->
        <div id="loader-container" class="text-center py-5" style="display: none;">
            <div class="loader"></div>
            <p class="mt-3 text-muted">正在加载威胁情报数据...</p>
        </div>
        
        <!-- 数据展示区域 -->
        <div id="intel-container">
            <!-- 数据将通过JavaScript动态填充 -->
        </div>
        
        <!-- 空数据状态 -->
        <div id="empty-state" class="empty-state" style="display: none;">
            <i class="bi bi-inbox"></i>
            <h3>暂无威胁情报数据</h3>
            <p class="text-muted">本周尚未收集到相关威胁情报，或者尝试更改过滤条件。</p>
            <button id="run-now-empty" class="btn btn-primary mt-2">
                <i class="bi bi-lightning-charge"></i> 立即执行数据收集
            </button>
        </div>
        
        <!-- 分页控件 -->
        <div class="d-flex justify-content-center mt-4 mb-5">
            <nav aria-label="分页导航" id="pagination-container" style="display: none;">
                <ul class="pagination pagination-sm flex-wrap">
                    <!-- 分页将通过JavaScript动态填充 -->
                </ul>
            </nav>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 状态和元素引用
            const state = {
                intelData: [],
                filteredData: [],
                currentPage: 1,
                itemsPerPage: 10,
                loading: false
            };
            
            // DOM 元素引用 - 使用直接选择器而不是依赖缓存引用
            function getElement(id) {
                return document.getElementById(id);
            }
            
            // 检查过滤器元素是否存在
            console.log("过滤器元素检查:");
            console.log("- 威胁类型过滤器:", getElement('filter-threat') ? "已找到" : "未找到");
            console.log("- 严重性过滤器:", getElement('filter-severity') ? "已找到" : "未找到");
            console.log("- 搜索框:", getElement('search-intel') ? "已找到" : "未找到");
            
            // 初始化加载数据
            loadWeeklyIntel();
            checkTaskStatus();
            
            // 绑定事件 - 使用直接选择器
            getElement('refresh-btn').addEventListener('click', loadWeeklyIntel);
            if (getElement('start-task-btn')) {
                getElement('start-task-btn').addEventListener('click', toggleScheduledTask);
            }
            if (getElement('run-now-btn')) {
                getElement('run-now-btn').addEventListener('click', runTaskNow);
            }
            if (getElement('run-now-empty')) {
                getElement('run-now-empty').addEventListener('click', runTaskNow);
            }
            
            // 过滤器事件绑定 - 使用直接方法
            getElement('filter-threat').addEventListener('change', function() {
                console.log("威胁类型变化被触发:", this.value);
                applyFilters();
            });
            
            getElement('filter-severity').addEventListener('change', function() {
                console.log("严重性变化被触发:", this.value);
                applyFilters();
            });
            
            getElement('search-intel').addEventListener('input', debounce(function() {
                console.log("搜索关键词变化:", this.value);
                applyFilters();
            }, 300));
            
            // 加载每周威胁情报数据
            function loadWeeklyIntel() {
                state.loading = true;
                updateUI();
                
                fetch('/api/weekly_intel')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`状态码 ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log("获取到数据:", data.length, "条记录");
                        state.intelData = data;
                        applyFilters(); // 应用过滤并更新UI
                        updateStats(data);
                    })
                    .catch(error => {
                        console.error('加载威胁情报数据失败:', error);
                        showError('加载数据失败，请稍后再试');
                    })
                    .finally(() => {
                        state.loading = false;
                        updateUI();
                    });
            }
            
            // 应用过滤器并更新展示
            function applyFilters() {
                // 直接从DOM获取当前值，确保最新
                const threatType = getElement('filter-threat').value;
                const severity = getElement('filter-severity').value;
                const searchText = getElement('search-intel').value.toLowerCase().trim();

                console.log("应用过滤条件:", threatType, severity, searchText);
                console.log("过滤前数据量:", state.intelData.length);
                
                // 检查数据是否存在
                if (!state.intelData || state.intelData.length === 0) {
                    console.log("没有可过滤的数据");
                    state.filteredData = [];
                    renderIntelData();
                    return;
                }
                
                // 添加更多日志以跟踪过滤过程
                console.log("数据示例:", state.intelData[0]);
                
                state.filteredData = state.intelData.filter(item => {
                    // 添加空值检查
                    if (!item) return false;
                    
                    const matchesThreat = threatType === 'all' || 
                                         (item.threat_type && item.threat_type === threatType);
                    const matchesSeverity = severity === 'all' || 
                                          (item.severity && item.severity === severity);
                    const matchesSearch = !searchText || 
                                         (item.title && item.title.toLowerCase().includes(searchText)) || 
                                         (item.description && item.description.toLowerCase().includes(searchText));
                    
                    return matchesThreat && matchesSeverity && matchesSearch;
                });

                console.log("过滤后数据量:", state.filteredData.length);
                
                // 重置页码
                state.currentPage = 1;
                
                // 重新渲染数据
                renderIntelData();
                
                // 强制更新UI状态
                updateUI();
            }
            
            // 更新统计信息
            function updateStats(data) {
                getElement('total-articles').textContent = data.length;
                getElement('high-threats').textContent = data.filter(item => item.severity === 'high').length;
                getElement('vulns-found').textContent = data.filter(item => item.threat_type === 'vulnerability').length;
                
                // 计算所有IOC的数量
                const iocsCount = data.reduce((total, item) => {
                    const iocs = item.iocs || [];
                    return total + iocs.length;
                }, 0);
                getElement('iocs-found').textContent = iocsCount;
            }
            
            // 渲染情报数据
            function renderIntelData() {
                const startIndex = (state.currentPage - 1) * state.itemsPerPage;
                const endIndex = startIndex + state.itemsPerPage;
                const pageItems = state.filteredData.slice(startIndex, endIndex);
                
                // 检查是否有数据显示
                if (state.filteredData.length === 0) {
                    getElement('empty-state').style.display = 'block';
                    getElement('intel-container').innerHTML = '';
                    getElement('pagination-container').style.display = 'none';
                    return;
                }
                
                getElement('empty-state').style.display = 'none';
                
                // 构建情报卡片
                let intelHtml = '<div class="row">';
                
                pageItems.forEach(item => {
                    const severityClass = getSeverityClass(item.severity);
                    const threatTypeLabel = getThreatTypeLabel(item.threat_type);
                    const formattedDate = new Date(item.date).toLocaleDateString('zh-CN');
                    
                    // 构建IOC标签
                    let iocsHtml = '';
                    if (item.iocs && item.iocs.length > 0) {
                        iocsHtml = '<div class="mt-3"><strong>关键指标:</strong><div class="ioc-container mt-2">';
                        item.iocs.slice(0, 5).forEach(ioc => {
                            const iocType = getIocTypeClass(ioc.type);
                            const truncatedValue = truncateIocValue(ioc.value, ioc.type);
                            const fullValue = ioc.value.replace(/"/g, '&quot;').replace(/'/g, '&#39;');
                            iocsHtml += `<span class="badge ioc-badge ${iocType}" title="${fullValue}" data-bs-toggle="tooltip">${truncatedValue}</span>`;
                        });
                        if (item.iocs.length > 5) {
                            iocsHtml += `<span class="badge bg-secondary ioc-badge">+${item.iocs.length - 5} 更多</span>`;
                        }
                        iocsHtml += '</div></div>';
                    }
                    
                    intelHtml += `
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 intel-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge ${severityClass}">${threatTypeLabel}</span>
                                    <span class="intel-date">${formattedDate}</span>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">${item.title}</h5>
                                    <p class="card-text">${item.description}</p>
                                    ${iocsHtml}
                                </div>
                                <div class="card-footer bg-transparent">
                                    <a href="/analysis/${item.analysis_id}" class="btn btn-sm btn-outline-primary">查看详情</a>
                                    <a href="${item.source_url}" target="_blank" class="btn btn-sm btn-outline-secondary ms-2">原文链接</a>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                intelHtml += '</div>';
                getElement('intel-container').innerHTML = intelHtml;

                // 初始化Bootstrap tooltips
                initializeTooltips();

                // 渲染分页
                renderPagination();
            }
            
            // 渲染分页控件
            function renderPagination() {
                const totalPages = Math.ceil(state.filteredData.length / state.itemsPerPage);
                
                if (totalPages <= 1) {
                    getElement('pagination-container').style.display = 'none';
                    return;
                }
                
                getElement('pagination-container').style.display = 'block';
                
                let paginationHtml = '';
                
                // 上一页按钮
                paginationHtml += `
                    <li class="page-item ${state.currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${state.currentPage - 1}">上一页</a>
                    </li>
                `;
                
                // 页码按钮
                const startPage = Math.max(1, state.currentPage - 2);
                const endPage = Math.min(totalPages, startPage + 4);
                
                for (let i = startPage; i <= endPage; i++) {
                    paginationHtml += `
                        <li class="page-item ${i === state.currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `;
                }
                
                // 下一页按钮
                paginationHtml += `
                    <li class="page-item ${state.currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${state.currentPage + 1}">下一页</a>
                    </li>
                `;
                
                // 使用innerHTML而不是appendChild，确保正确替换所有内容
                const paginationEl = getElement('pagination-container').querySelector('.pagination');
                if (paginationEl) {
                    paginationEl.innerHTML = paginationHtml;
                    
                    // 绑定分页事件
                    document.querySelectorAll('.pagination .page-link').forEach(link => {
                        link.addEventListener('click', function(e) {
                            e.preventDefault();
                            const newPage = parseInt(this.dataset.page);
                            if (newPage !== state.currentPage && newPage >= 1 && newPage <= totalPages) {
                                state.currentPage = newPage;
                                renderIntelData();
                                // 滚动到顶部
                                window.scrollTo(0, 0);
                            }
                        });
                    });
                }
            }
            
            // 更新UI状态
            function updateUI() {
                if (state.loading) {
                    getElement('loader-container').style.display = 'block';
                    getElement('intel-container').style.display = 'none';
                    getElement('empty-state').style.display = 'none';
                    getElement('pagination-container').style.display = 'none';
                } else {
                    getElement('loader-container').style.display = 'none';
                    
                    // 确保过滤后的数据正确显示
                    if (state.filteredData && state.filteredData.length > 0) {
                        getElement('intel-container').style.display = 'block';
                        getElement('pagination-container').style.display = 'block';
                        getElement('empty-state').style.display = 'none';
                    } else {
                        getElement('intel-container').style.display = 'none';
                        getElement('pagination-container').style.display = 'none';
                        getElement('empty-state').style.display = 'block';
                    }
                }
            }
            
            // 显示通用错误
            function showError(message) {
                const alertEl = document.createElement('div');
                alertEl.className = 'alert alert-danger alert-dismissible fade show mt-3';
                alertEl.innerHTML = `
                    <strong>错误：</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                document.querySelector('.container').insertBefore(alertEl, getElement('intel-container'));
                
                // 5秒后自动关闭
                setTimeout(() => {
                    alertEl.remove();
                }, 5000);
            }
            
            // 获取威胁类型对应的标签文本
            function getThreatTypeLabel(type) {
                const labels = {
                    'malware': '恶意软件',
                    'vulnerability': '漏洞',
                    'apt': 'APT组织',
                    'ransom': '勒索软件',
                    'phishing': '网络钓鱼',
                    'backdoor': '后门',
                    'exploit': '漏洞利用'
                };
                return labels[type] || type || '未知';
            }
            
            // 获取IOC类型对应的CSS类
            function getIocTypeClass(type) {
                const classes = {
                    'ip': 'bg-warning text-dark',
                    'domain': 'bg-info text-white',
                    'url': 'bg-primary',
                    'hash': 'bg-danger',
                    'file': 'bg-secondary'
                };
                return classes[type] || 'bg-dark';
            }
            
            // 获取严重程度对应的CSS类
            function getSeverityClass(severity) {
                const classes = {
                    'high': 'bg-danger',
                    'medium': 'bg-warning text-dark',
                    'low': 'bg-info'
                };
                return classes[severity] || 'bg-secondary';
            }

            // 截断IOC值以适应显示
            function truncateIocValue(value, type) {
                if (!value) return '';

                // 根据IOC类型设置不同的截断长度
                let maxLength;
                switch(type) {
                    case 'ip':
                        return value; // IP地址不需要截断
                    case 'domain':
                        maxLength = 25;
                        break;
                    case 'url':
                        maxLength = 30;
                        break;
                    case 'hash':
                        maxLength = 16;
                        break;
                    case 'file':
                        maxLength = 20;
                        break;
                    default:
                        maxLength = 25;
                }

                if (value.length <= maxLength) {
                    return value;
                }

                return value.substring(0, maxLength) + '...';
            }
            
            // 初始化Bootstrap tooltips
            function initializeTooltips() {
                // 销毁现有的tooltips
                const existingTooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
                existingTooltips.forEach(element => {
                    const tooltip = bootstrap.Tooltip.getInstance(element);
                    if (tooltip) {
                        tooltip.dispose();
                    }
                });

                // 初始化新的tooltips
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl, {
                        placement: 'top',
                        trigger: 'hover focus'
                    });
                });
            }

            // Debounce函数，减少高频事件触发
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
            
            // 检查任务状态
            function checkTaskStatus() {
                // 如果没有相关元素，跳过
                if (!getElement('status-indicator')) return;
                
                fetch('/api/task_status')
                    .then(response => response.json())
                    .then(data => {
                        const statusIndicator = getElement('status-indicator');
                        const statusText = getElement('status-text');
                        const lastRunTime = getElement('last-run-time');
                        const nextRunTime = getElement('next-run-time');
                        const startTaskBtn = getElement('start-task-btn');
                        
                        if (data.is_active) {
                            statusIndicator.className = 'status-indicator status-active';
                            statusText.textContent = '已启用';
                            startTaskBtn.textContent = '停止任务';
                            startTaskBtn.className = 'btn btn-outline-danger';
                        } else {
                            statusIndicator.className = 'status-indicator status-inactive';
                            statusText.textContent = '已停用';
                            startTaskBtn.textContent = '启用任务';
                            startTaskBtn.className = 'btn btn-outline-success';
                        }
                        
                        if (data.last_run) {
                            lastRunTime.textContent = new Date(data.last_run).toLocaleString('zh-CN');
                        } else {
                            lastRunTime.textContent = '尚未执行';
                        }
                        
                        if (data.next_run) {
                            nextRunTime.textContent = new Date(data.next_run).toLocaleString('zh-CN');
                        } else {
                            nextRunTime.textContent = '计划未启用';
                        }
                    })
                    .catch(error => {
                        console.error('获取任务状态失败:', error);
                    });
            }
            
            // 切换定时任务状态
            function toggleScheduledTask() {
                fetch('/api/toggle_task', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        checkTaskStatus();
                        showAlert(`任务已${data.is_active ? '启用' : '停用'}`, data.is_active ? 'success' : 'warning');
                    } else {
                        showError(data.message || '操作失败');
                    }
                })
                .catch(error => {
                    console.error('切换任务状态失败:', error);
                    showError('操作失败，请稍后再试');
                });
            }
            
            // 立即执行任务
            function runTaskNow() {
                fetch('/api/run_task', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('任务已开始执行，请稍后刷新查看结果', 'success');
                        setTimeout(() => {
                            loadWeeklyIntel();
                            checkTaskStatus();
                        }, 2000);
                    } else {
                        showError(data.message || '执行任务失败');
                    }
                })
                .catch(error => {
                    console.error('执行任务失败:', error);
                    showError('执行失败，请稍后再试');
                });
            }
            
            // 显示通用提醒
            function showAlert(message, type = 'info') {
                const alertEl = document.createElement('div');
                alertEl.className = `alert alert-${type} alert-dismissible fade show mt-3`;
                alertEl.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                document.querySelector('.container').insertBefore(alertEl, document.querySelector('.card.mb-4'));
                
                // 3秒后自动删除
                setTimeout(() => {
                    alertEl.remove();
                }, 3000);
            }
            
            // 添加调试工具
            window.debugFilters = function() {
                console.log("=== 过滤器调试 ===");
                console.log("威胁过滤器值:", getElement('filter-threat').value);
                console.log("严重性过滤器值:", getElement('filter-severity').value);
                console.log("搜索框值:", getElement('search-intel').value);
                console.log("数据状态:", state.intelData.length, state.filteredData.length);
                
                // 手动触发过滤
                console.log("手动触发过滤器...");
                applyFilters();
            };
        });
    </script>
</body>
</html>