{"chunk_id": "line-1", "filename": "2000.txt", "content": "CVE-2000-0001(PUBLISHED):RealMedia server allows remote attackers to cause a denial of service via a long ramgen request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-2", "filename": "2000.txt", "content": "CVE-2000-0002(PUBLISHED):Buffer overflow in ZBServer Pro 1.50 allows remote attackers to execute commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-3", "filename": "2000.txt", "content": "CVE-2000-0003(PUBLISHED):Buffer overflow in UnixWare rtpm program allows local users to gain privileges via a long environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-4", "filename": "2000.txt", "content": "CVE-2000-0004(PUBLISHED):ZBServer Pro allows remote attackers to read source code for executable files by inserting a . (dot) into the URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-5", "filename": "2000.txt", "content": "CVE-2000-0005(PUBLISHED):HP-UX aserver program allows local users to gain privileges via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-6", "filename": "2000.txt", "content": "CVE-2000-0006(PUBLISHED):strace allows local users to read arbitrary files via memory mapped file names.", "metadata": {"filename": "2000.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-7", "filename": "2000.txt", "content": "CVE-2000-0007(PUBLISHED):Trend Micro PC-Cillin does not restrict access to its internal proxy port, allowing remote attackers to conduct a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-8", "filename": "2000.txt", "content": "CVE-2000-0008(PUBLISHED):FTPPro allows local users to read sensitive information, which is stored in plain text.", "metadata": {"filename": "2000.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-9", "filename": "2000.txt", "content": "CVE-2000-0009(PUBLISHED):The bna_pass program in Optivity NETarchitect uses the PATH environmental variable for finding the \"rm\" program, which allows local users to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-10", "filename": "2000.txt", "content": "CVE-2000-0010(PUBLISHED):WebWho+ whois.cgi program allows remote attackers to execute commands via shell metacharacters in the TLD parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-11", "filename": "2000.txt", "content": "CVE-2000-0011(PUBLISHED):Buffer overflow in AnalogX SimpleServer:WWW HTTP server allows remote attackers to execute commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-12", "filename": "2000.txt", "content": "CVE-2000-0012(PUBLISHED):Buffer overflow in w3-msql CGI program in miniSQL package allows remote attackers to execute commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-13", "filename": "2000.txt", "content": "CVE-2000-0013(PUBLISHED):IRIX soundplayer program allows local users to gain privileges by including shell metacharacters in a .wav file, which is executed via the midikeys program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-14", "filename": "2000.txt", "content": "CVE-2000-0014(PUBLISHED):Denial of service in Savant web server via a null character in the requested URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-15", "filename": "2000.txt", "content": "CVE-2000-0015(PUBLISHED):CascadeView TFTP server allows local users to gain privileges via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-16", "filename": "2000.txt", "content": "CVE-2000-0016(PUBLISHED):Buffer overflow in Internet Anywhere POP3 Mail Server allows remote attackers to cause a denial of service or execute commands via a long username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-17", "filename": "2000.txt", "content": "CVE-2000-0017(PUBLISHED):Buffer overflow in Linux linuxconf package allows remote attackers to gain root privileges via a long parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-18", "filename": "2000.txt", "content": "CVE-2000-0018(PUBLISHED):wmmon in FreeBSD allows local users to gain privileges via the .wmmonrc configuration file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-19", "filename": "2000.txt", "content": "CVE-2000-0019(PUBLISHED):IMail POP3 daemon uses weak encryption, which allows local users to read files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-20", "filename": "2000.txt", "content": "CVE-2000-0020(PUBLISHED):DNS PRO allows remote attackers to conduct a denial of service via a large number of connections.", "metadata": {"filename": "2000.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-21", "filename": "2000.txt", "content": "CVE-2000-0021(PUBLISHED):Lotus Domino HTTP server allows remote attackers to determine the real path of the server via a request to a non-existent script in /cgi-bin.", "metadata": {"filename": "2000.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-22", "filename": "2000.txt", "content": "CVE-2000-0022(PUBLISHED):Lotus Domino HTTP server does not properly disable anonymous access for the cgi-bin directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-23", "filename": "2000.txt", "content": "CVE-2000-0023(PUBLISHED):Buffer overflow in Lotus Domino HTTP server allows remote attackers to cause a denial of service via a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-24", "filename": "2000.txt", "content": "CVE-2000-0024(PUBLISHED):IIS does not properly canonicalize URLs, potentially allowing remote attackers to bypass access restrictions in third-party software via escape characters, aka the \"Escape Character Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-25", "filename": "2000.txt", "content": "CVE-2000-0025(PUBLISHED):IIS 4.0 and Site Server 3.0 allow remote attackers to read source code for ASP files if the file is in a virtual directory whose name includes extensions such as .com, .exe, .sh, .cgi, or .dll, aka the \"Virtual Directory Naming\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-26", "filename": "2000.txt", "content": "CVE-2000-0026(PUBLISHED):Buffer overflow in UnixWare i2odialogd daemon allows remote attackers to gain root access via a long username/password authorization string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-27", "filename": "2000.txt", "content": "CVE-2000-0027(PUBLISHED):IBM Network Station Manager NetStation allows local users to gain privileges via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-28", "filename": "2000.txt", "content": "CVE-2000-0028(PUBLISHED):Internet Explorer 5.0 and 5.01 allows remote attackers to bypass the cross frame security policy and read files via the external.NavigateAndFind function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-29", "filename": "2000.txt", "content": "CVE-2000-0029(PUBLISHED):UnixWare pis and mkpis commands allow local users to gain privileges via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-30", "filename": "2000.txt", "content": "CVE-2000-0030(PUBLISHED):Solaris dmispd dmi_cmd allows local users to fill up restricted disk space by adding files to the /var/dmi/db database.", "metadata": {"filename": "2000.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-31", "filename": "2000.txt", "content": "CVE-2000-0031(PUBLISHED):The initscripts package in Red Hat Linux allows local users to gain privileges via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-32", "filename": "2000.txt", "content": "CVE-2000-0032(PUBLISHED):Solaris dmi_cmd allows local users to crash the dmispd daemon by adding a malformed file to the /var/dmi/db database.", "metadata": {"filename": "2000.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-33", "filename": "2000.txt", "content": "CVE-2000-0033(PUBLISHED):InterScan VirusWall SMTP scanner does not properly scan messages with malformed attachments.", "metadata": {"filename": "2000.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-34", "filename": "2000.txt", "content": "CVE-2000-0034(PUBLISHED):Netscape 4.7 records user passwords in the preferences.js file during an IMAP or POP session, even if the user has not enabled \"remember passwords.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-35", "filename": "2000.txt", "content": "CVE-2000-0035(PUBLISHED):resend command in Majordomo allows local users to gain privileges via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-36", "filename": "2000.txt", "content": "CVE-2000-0036(PUBLISHED):Outlook Express 5 for Macintosh downloads attachments to HTML mail without prompting the user, aka the \"HTML Mail Attachment\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-37", "filename": "2000.txt", "content": "CVE-2000-0037(PUBLISHED):Majordomo wrapper allows local users to gain privileges by specifying an alternate configuration file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-38", "filename": "2000.txt", "content": "CVE-2000-0038(PUBLISHED):glFtpD includes a default glftpd user account with a default password and a UID of 0.", "metadata": {"filename": "2000.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-39", "filename": "2000.txt", "content": "CVE-2000-0039(PUBLISHED):AltaVista search engine allows remote attackers to read files above the document root via a .. (dot dot) in the query.cgi CGI program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-40", "filename": "2000.txt", "content": "CVE-2000-0040(PUBLISHED):glFtpD allows local users to gain privileges via metacharacters in the SITE ZIPCHK command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-41", "filename": "2000.txt", "content": "CVE-2000-0041(PUBLISHED):Macintosh systems generate large ICMP datagrams in response to malformed datagrams, allowing them to be used as amplifiers in a flood attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-42", "filename": "2000.txt", "content": "CVE-2000-0042(PUBLISHED):Buffer overflow in CSM mail server allows remote attackers to cause a denial of service or execute commands via a long HELO command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-43", "filename": "2000.txt", "content": "CVE-2000-0043(PUBLISHED):Buffer overflow in CamShot WebCam HTTP server allows remote attackers to execute commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-44", "filename": "2000.txt", "content": "CVE-2000-0044(PUBLISHED):Macros in War FTP 1.70 and 1.67b2 allow local or remote attackers to read arbitrary files or execute commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-45", "filename": "2000.txt", "content": "CVE-2000-0045(PUBLISHED):MySQL allows local users to modify passwords for arbitrary MySQL users via the GRANT privilege.", "metadata": {"filename": "2000.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-46", "filename": "2000.txt", "content": "CVE-2000-0046(PUBLISHED):Buffer overflow in ICQ 99b 1.1.1.1 client allows remote attackers to execute commands via a malformed URL within an ICQ message.", "metadata": {"filename": "2000.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-47", "filename": "2000.txt", "content": "CVE-2000-0047(PUBLISHED):Buffer overflow in Yahoo Pager/Messenger client allows remote attackers to cause a denial of service via a long URL within a message.", "metadata": {"filename": "2000.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-48", "filename": "2000.txt", "content": "CVE-2000-0048(PUBLISHED):get_it program in Corel Linux Update allows local users to gain root access by specifying an alternate PATH for the cp program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-49", "filename": "2000.txt", "content": "CVE-2000-0049(PUBLISHED):Buffer overflow in Winamp client allows remote attackers to execute commands via a long entry in a .pls file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-50", "filename": "2000.txt", "content": "CVE-2000-0050(PUBLISHED):The Allaire Spectra Webtop allows authenticated users to access other Webtop sections by specifying explicit URLs.", "metadata": {"filename": "2000.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-51", "filename": "2000.txt", "content": "CVE-2000-0051(PUBLISHED):The Allaire Spectra Configuration Wizard allows remote attackers to cause a denial of service by repeatedly resubmitting data collections for indexing via a URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-52", "filename": "2000.txt", "content": "CVE-2000-0052(PUBLISHED):Red Hat userhelper program in the usermode package allows local users to gain root access via PAM and a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-53", "filename": "2000.txt", "content": "CVE-2000-0053(PUBLISHED):Microsoft Commercial Internet System (MCIS) IMAP server allows remote attackers to cause a denial of service via a malformed IMAP request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-54", "filename": "2000.txt", "content": "CVE-2000-0054(PUBLISHED):search.cgi in the SolutionScripts Home Free package allows remote attackers to view directories via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-55", "filename": "2000.txt", "content": "CVE-2000-0055(PUBLISHED):Buffer overflow in Solaris chkperm command allows local users to gain root access via a long -n option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-56", "filename": "2000.txt", "content": "CVE-2000-0056(PUBLISHED):IMail IMONITOR status.cgi CGI script allows remote attackers to cause a denial of service with many calls to status.cgi.", "metadata": {"filename": "2000.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-57", "filename": "2000.txt", "content": "CVE-2000-0057(PUBLISHED):Cold Fusion CFCACHE tag places temporary cache files within the web document root, allowing remote attackers to obtain sensitive system information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-58", "filename": "2000.txt", "content": "CVE-2000-0058(PUBLISHED):Network HotSync program in Handspring Visor does not have authentication, which allows remote attackers to retrieve email and files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-59", "filename": "2000.txt", "content": "CVE-2000-0059(PUBLISHED):PHP3 with safe_mode enabled does not properly filter shell metacharacters from commands that are executed by popen, which could allow remote attackers to execute commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-60", "filename": "2000.txt", "content": "CVE-2000-0060(PUBLISHED):Buffer overflow in aVirt Rover POP3 server 1.1 allows remote attackers to cause a denial of service via a long user name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-61", "filename": "2000.txt", "content": "CVE-2000-0061(PUBLISHED):Internet Explorer 5 does not modify the security zone for a document that is being loaded into a window until after the document has been loaded, which could allow remote attackers to execute Javascript in a different security context while the document is loading.", "metadata": {"filename": "2000.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-62", "filename": "2000.txt", "content": "CVE-2000-0062(PUBLISHED):The DTML implementation in the Z Object Publishing Environment (Zope) allows remote attackers to conduct unauthorized activities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-63", "filename": "2000.txt", "content": "CVE-2000-0063(PUBLISHED):cgiproc CGI script in Nortel Contivity HTTP server allows remote attackers to read arbitrary files by specifying the filename in a parameter to the script.", "metadata": {"filename": "2000.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-64", "filename": "2000.txt", "content": "CVE-2000-0064(PUBLISHED):cgiproc CGI script in Nortel Contivity HTTP server allows remote attackers to cause a denial of service via a malformed URL that includes shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-65", "filename": "2000.txt", "content": "CVE-2000-0065(PUBLISHED):Buffer overflow in InetServ 3.0 allows remote attackers to execute commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-66", "filename": "2000.txt", "content": "CVE-2000-0066(PUBLISHED):WebSite Pro allows remote attackers to determine the real pathname of webdirectories via a malformed URL request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-67", "filename": "2000.txt", "content": "CVE-2000-0067(PUBLISHED):CyberCash Merchant Connection Kit (MCK) allows local users to modify files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-68", "filename": "2000.txt", "content": "CVE-2000-0068(PUBLISHED):daynad program in Intel InBusiness E-mail Station does not require authentication, which allows remote attackers to modify its configuration, delete files, or read mail.", "metadata": {"filename": "2000.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-69", "filename": "2000.txt", "content": "CVE-2000-0069(PUBLISHED):The recover program in Solstice Backup allows local users to restore sensitive files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-70", "filename": "2000.txt", "content": "CVE-2000-0070(PUBLISHED):NtImpersonateClientOfPort local procedure call in Windows NT 4.0 allows local users to gain privileges, aka \"Spoofed LPC Port Request.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-71", "filename": "2000.txt", "content": "CVE-2000-0071(PUBLISHED):IIS 4.0 allows a remote attacker to obtain the real pathname of the document root by requesting non-existent files with .ida or .idq extensions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-72", "filename": "2000.txt", "content": "CVE-2000-0072(PUBLISHED):Visual Casel (Vcasel) does not properly prevent users from executing files, which allows local users to use a relative pathname to specify an alternate file which has an approved name and possibly gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-73", "filename": "2000.txt", "content": "CVE-2000-0073(PUBLISHED):Buffer overflow in Microsoft Rich Text Format (RTF) reader allows attackers to cause a denial of service via a malformed control word.", "metadata": {"filename": "2000.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-74", "filename": "2000.txt", "content": "CVE-2000-0074(PUBLISHED):PowerScripts PlusMail CGI program allows remote attackers to execute commands via a password file with improper permissions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-75", "filename": "2000.txt", "content": "CVE-2000-0075(PUBLISHED):Super Mail Transfer Package (SMTP), later called MsgCore, has a memory leak which allows remote attackers to cause a denial of service by repeating multiple HELO, MAIL FROM, RCPT TO, and DATA commands in the same session.", "metadata": {"filename": "2000.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-76", "filename": "2000.txt", "content": "CVE-2000-0076(PUBLISHED):nviboot boot script in the Debian nvi package allows local users to delete files via malformed entries in vi.recover.", "metadata": {"filename": "2000.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-77", "filename": "2000.txt", "content": "CVE-2000-0077(PUBLISHED):The October 1998 version of the HP-UX aserver program allows local users to gain privileges by specifying an alternate PATH which aserver uses to find the ps and grep commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-78", "filename": "2000.txt", "content": "CVE-2000-0078(PUBLISHED):The June 1999 version of the HP-UX aserver program allows local users to gain privileges by specifying an alternate PATH which aserver uses to find the awk command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-79", "filename": "2000.txt", "content": "CVE-2000-0079(PUBLISHED):The W3C CERN httpd HTTP server allows remote attackers to determine the real pathnames of some commands via a request for a nonexistent URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-80", "filename": "2000.txt", "content": "CVE-2000-0080(PUBLISHED):AIX techlibss allows local users to overwrite files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-81", "filename": "2000.txt", "content": "CVE-2000-0081(PUBLISHED):Hotmail does not properly filter JavaScript code from a user's mailbox, which allows a remote attacker to execute the code by using hexadecimal codes to specify the javascript: protocol, e.g. j&#x41;vascript.", "metadata": {"filename": "2000.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-82", "filename": "2000.txt", "content": "CVE-2000-0082(PUBLISHED):WebTV email client allows remote attackers to force the client to send email without the user's knowledge via HTML.", "metadata": {"filename": "2000.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-83", "filename": "2000.txt", "content": "CVE-2000-0083(PUBLISHED):HP asecure creates the Audio Security File audio.sec with insecure permissions, which allows local users to cause a denial of service or gain additional privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-84", "filename": "2000.txt", "content": "CVE-2000-0084(PUBLISHED):CuteFTP uses weak encryption to store password information in its tree.dat file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-85", "filename": "2000.txt", "content": "CVE-2000-0085(PUBLISHED):Hotmail does not properly filter JavaScript code from a user's mailbox, which allows a remote attacker to execute code via the LOWSRC or DYNRC parameters in the IMG tag.", "metadata": {"filename": "2000.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-86", "filename": "2000.txt", "content": "CVE-2000-0086(PUBLISHED):Netopia Timbuktu Pro sends user IDs and passwords in cleartext, which allows remote attackers to obtain them via sniffing.", "metadata": {"filename": "2000.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-87", "filename": "2000.txt", "content": "CVE-2000-0087(PUBLISHED):Netscape Mail Notification (nsnotify) utility in Netscape Communicator uses IMAP without SSL, even if the user has set a preference for Communicator to use an SSL connection, allowing a remote attacker to sniff usernames and passwords in plaintext.", "metadata": {"filename": "2000.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-88", "filename": "2000.txt", "content": "CVE-2000-0088(PUBLISHED):Buffer overflow in the conversion utilities for Japanese, Korean and Chinese Word 5 documents allows an attacker to execute commands, aka the \"Malformed Conversion Data\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-89", "filename": "2000.txt", "content": "CVE-2000-0089(PUBLISHED):The rdisk utility in Microsoft Terminal Server Edition and Windows NT 4.0 stores registry hive information in a temporary file with permissions that allow local users to read it, aka the \"RDISK Registry Enumeration File\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-90", "filename": "2000.txt", "content": "CVE-2000-0090(PUBLISHED):VMWare 1.1.2 allows local users to cause a denial of service via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-91", "filename": "2000.txt", "content": "CVE-2000-0091(PUBLISHED):Buffer overflow in vchkpw/vpopmail POP authentication package allows remote attackers to gain root privileges via a long username or password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-92", "filename": "2000.txt", "content": "CVE-2000-0092(PUBLISHED):The BSD make program allows local users to modify files via a symlink attack when the -j option is being used.", "metadata": {"filename": "2000.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-93", "filename": "2000.txt", "content": "CVE-2000-0093(PUBLISHED):An installation of Red Hat uses DES password encryption with crypt() for the initial password, instead of md5.", "metadata": {"filename": "2000.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-94", "filename": "2000.txt", "content": "CVE-2000-0094(PUBLISHED):procfs in BSD systems allows local users to gain root privileges by modifying the /proc/pid/mem interface via a modified file descriptor for stderr.", "metadata": {"filename": "2000.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-95", "filename": "2000.txt", "content": "CVE-2000-0095(PUBLISHED):The PMTU discovery procedure used by HP-UX 10.30 and 11.00 for determining the optimum MTU generates large amounts of traffic in response to small packets, allowing remote attackers to cause the system to be used as a packet amplifier.", "metadata": {"filename": "2000.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-96", "filename": "2000.txt", "content": "CVE-2000-0096(PUBLISHED):Buffer overflow in qpopper 3.0 beta versions allows local users to gain privileges via a long LIST command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-97", "filename": "2000.txt", "content": "CVE-2000-0097(PUBLISHED):The WebHits ISAPI filter in Microsoft Index Server allows remote attackers to read arbitrary files, aka the \"Malformed Hit-Highlighting Argument\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-98", "filename": "2000.txt", "content": "CVE-2000-0098(PUBLISHED):Microsoft Index Server allows remote attackers to determine the real path for a web directory via a request to an Internet Data Query file that does not exist.", "metadata": {"filename": "2000.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-99", "filename": "2000.txt", "content": "CVE-2000-0099(PUBLISHED):Buffer overflow in UnixWare ppptalk command allows local users to gain privileges via a long prompt argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-100", "filename": "2000.txt", "content": "CVE-2000-0100(PUBLISHED):The SMS Remote Control program is installed with insecure permissions, which allows local users to gain privileges by modifying or replacing the program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-101", "filename": "2000.txt", "content": "CVE-2000-0101(PUBLISHED):The Make-a-Store OrderPage shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-102", "filename": "2000.txt", "content": "CVE-2000-0102(PUBLISHED):The SalesCart shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-103", "filename": "2000.txt", "content": "CVE-2000-0103(PUBLISHED):The SmartCart shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-104", "filename": "2000.txt", "content": "CVE-2000-0104(PUBLISHED):The Shoptron shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-105", "filename": "2000.txt", "content": "CVE-2000-0105(PUBLISHED):Outlook Express 5.01 and Internet Explorer 5.01 allow remote attackers to view a user's email messages via a script that accesses a variable that references subsequent email messages that are read by the client.", "metadata": {"filename": "2000.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-106", "filename": "2000.txt", "content": "CVE-2000-0106(PUBLISHED):The EasyCart shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-107", "filename": "2000.txt", "content": "CVE-2000-0107(PUBLISHED):Linux apcd program allows local attackers to modify arbitrary files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-108", "filename": "2000.txt", "content": "CVE-2000-0108(PUBLISHED):The Intellivend shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-109", "filename": "2000.txt", "content": "CVE-2000-0109(PUBLISHED):The mcsp Client Site Processor system (MultiCSP) in Standard and Poor's ComStock is installed with several accounts that have no passwords or easily guessable default passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-110", "filename": "2000.txt", "content": "CVE-2000-0110(PUBLISHED):The WebSiteTool shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-111", "filename": "2000.txt", "content": "CVE-2000-0111(PUBLISHED):The RightFax web client uses predictable session numbers, which allows remote attackers to hijack user sessions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-112", "filename": "2000.txt", "content": "CVE-2000-0112(PUBLISHED):The default installation of Debian GNU/Linux uses an insecure Master Boot Record (MBR) which allows a local user to boot from a floppy disk during the installation.", "metadata": {"filename": "2000.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-113", "filename": "2000.txt", "content": "CVE-2000-0113(PUBLISHED):The SyGate Remote Management program does not properly restrict access to its administration service, which allows remote attackers to cause a denial of service, or access network traffic statistics.", "metadata": {"filename": "2000.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-114", "filename": "2000.txt", "content": "CVE-2000-0114(PUBLISHED):Frontpage Server Extensions allows remote attackers to determine the name of the anonymous account via an RPC POST request to shtml.dll in the /_vti_bin/ virtual directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-115", "filename": "2000.txt", "content": "CVE-2000-0115(PUBLISHED):IIS allows local users to cause a denial of service via invalid regular expressions in a Visual Basic script in an ASP page.", "metadata": {"filename": "2000.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-116", "filename": "2000.txt", "content": "CVE-2000-0116(PUBLISHED):Firewall-1 does not properly filter script tags, which allows remote attackers to bypass the \"Strip Script Tags\" restriction by including an extra < in front of the SCRIPT tag.", "metadata": {"filename": "2000.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-117", "filename": "2000.txt", "content": "CVE-2000-0117(PUBLISHED):The siteUserMod.cgi program in Cobalt RaQ2 servers allows any Site Administrator to modify passwords for other users, site administrators, and possibly admin (root).", "metadata": {"filename": "2000.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-118", "filename": "2000.txt", "content": "CVE-2000-0118(PUBLISHED):The Red Hat Linux su program does not log failed password guesses if the su process is killed before it times out, which allows local attackers to conduct brute force password guessing.", "metadata": {"filename": "2000.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-119", "filename": "2000.txt", "content": "CVE-2000-0119(PUBLISHED):The default configurations for McAfee Virus Scan and Norton Anti-Virus virus checkers do not check files in the RECYCLED folder that is used by the Windows Recycle Bin utility, which allows attackers to store malicious code without detection.", "metadata": {"filename": "2000.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-120", "filename": "2000.txt", "content": "CVE-2000-0120(PUBLISHED):The Remote Access Service invoke.cfm template in Allaire Spectra 1.0 allows users to bypass authentication via the bAuthenticated parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-121", "filename": "2000.txt", "content": "CVE-2000-0121(PUBLISHED):The Recycle Bin utility in Windows NT and Windows 2000 allows local users to read or modify files by creating a subdirectory with the victim's SID in the recycler directory, aka the \"Recycle Bin Creation\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-122", "filename": "2000.txt", "content": "CVE-2000-0122(PUBLISHED):Frontpage Server Extensions allows remote attackers to determine the physical path of a virtual directory via a GET request to the htimage.exe CGI program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-123", "filename": "2000.txt", "content": "CVE-2000-0123(PUBLISHED):The shopping cart application provided with Filemaker allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-124", "filename": "2000.txt", "content": "CVE-2000-0124(PUBLISHED):surfCONTROL SuperScout does not properly asign a category to web sites with a . (dot) at the end, which may allow users to bypass web access restrictions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-125", "filename": "2000.txt", "content": "CVE-2000-0125(PUBLISHED):wwwthreads does not properly cleanse numeric data or table names that are passed to SQL queries, which allows remote attackers to gain privileges for wwwthreads forums.", "metadata": {"filename": "2000.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-126", "filename": "2000.txt", "content": "CVE-2000-0126(PUBLISHED):Sample Internet Data Query (IDQ) scripts in IIS 3 and 4 allow remote attackers to read files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-127", "filename": "2000.txt", "content": "CVE-2000-0127(PUBLISHED):The Webspeed configuration program does not properly disable access to the WSMadmin utility, which allows remote attackers to gain privileges via wsisa.dll.", "metadata": {"filename": "2000.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-128", "filename": "2000.txt", "content": "CVE-2000-0128(PUBLISHED):The Finger Server 0.82 allows remote attackers to execute commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-129", "filename": "2000.txt", "content": "CVE-2000-0129(PUBLISHED):Buffer overflow in the SHGetPathFromIDList function of the Serv-U FTP server allows attackers to cause a denial of service by performing a LIST command on a malformed .lnk file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-130", "filename": "2000.txt", "content": "CVE-2000-0130(PUBLISHED):Buffer overflow in SCO scohelp program allows remote attackers to execute commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-131", "filename": "2000.txt", "content": "CVE-2000-0131(PUBLISHED):Buffer overflow in War FTPd 1.6x allows users to cause a denial of service via long MKD and CWD commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-132", "filename": "2000.txt", "content": "CVE-2000-0132(PUBLISHED):Microsoft Java Virtual Machine allows remote attackers to read files via the getSystemResourceAsStream function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-133", "filename": "2000.txt", "content": "CVE-2000-0133(PUBLISHED):Buffer overflows in Tiny FTPd 0.52 beta3 FTP server allows users to execute commands via the STOR, RNTO, MKD, XMKD, RMD, XRMD, APPE, SIZE, and RNFR commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-134", "filename": "2000.txt", "content": "CVE-2000-0134(PUBLISHED):The Check It Out shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-135", "filename": "2000.txt", "content": "CVE-2000-0135(PUBLISHED):The @Retail shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-136", "filename": "2000.txt", "content": "CVE-2000-0136(PUBLISHED):The Cart32 shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-137", "filename": "2000.txt", "content": "CVE-2000-0137(PUBLISHED):The CartIt shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-138", "filename": "2000.txt", "content": "CVE-2000-0138(PUBLISHED):A system has a distributed denial of service (DDOS) attack master, agent, or zombie installed, such as (1) Trinoo, (2) Tribe Flood Network (TFN), (3) Tribe Flood Network 2000 (TFN2K), (4) stacheldraht, (5) mstream, or (6) shaft.", "metadata": {"filename": "2000.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-139", "filename": "2000.txt", "content": "CVE-2000-0139(PUBLISHED):Internet Anywhere POP3 Mail Server allows local users to cause a denial of service via a malformed RETR command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-140", "filename": "2000.txt", "content": "CVE-2000-0140(PUBLISHED):Internet Anywhere POP3 Mail Server allows remote attackers to cause a denial of service via a large number of connections.", "metadata": {"filename": "2000.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-141", "filename": "2000.txt", "content": "CVE-2000-0141(PUBLISHED):Infopop Ultimate Bulletin Board (UBB) allows remote attackers to execute commands via shell metacharacters in the topic hidden field.", "metadata": {"filename": "2000.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-142", "filename": "2000.txt", "content": "CVE-2000-0142(PUBLISHED):The authentication protocol in Timbuktu Pro 2.0b650 allows remote attackers to cause a denial of service via connections to port 407 and 1417.", "metadata": {"filename": "2000.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-143", "filename": "2000.txt", "content": "CVE-2000-0143(PUBLISHED):The SSH protocol server sshd allows local users without shell access to redirect a TCP connection through a service that uses the standard system password database for authentication, such as POP or FTP.", "metadata": {"filename": "2000.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-144", "filename": "2000.txt", "content": "CVE-2000-0144(PUBLISHED):Axis 700 Network Scanner does not properly restrict access to administrator URLs, which allows users to bypass the password protection via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-145", "filename": "2000.txt", "content": "CVE-2000-0145(PUBLISHED):The libguile.so library file used by gnucash in Debian GNU/Linux is installed with world-writable permissions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-146", "filename": "2000.txt", "content": "CVE-2000-0146(PUBLISHED):The Java Server in the Novell GroupWise Web Access Enhancement Pack allows remote attackers to cause a denial of service via a long URL to the servlet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-147", "filename": "2000.txt", "content": "CVE-2000-0147(PUBLISHED):snmpd in SCO OpenServer has an SNMP community string that is writable by default, which allows local attackers to modify the host's configuration.", "metadata": {"filename": "2000.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-148", "filename": "2000.txt", "content": "CVE-2000-0148(PUBLISHED):MySQL 3.22 allows remote attackers to bypass password authentication and access a database via a short check string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-149", "filename": "2000.txt", "content": "CVE-2000-0149(PUBLISHED):Zeus web server allows remote attackers to view the source code for CGI programs via a null character (%00) at the end of a URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-150", "filename": "2000.txt", "content": "CVE-2000-0150(PUBLISHED):Check Point Firewall-1 allows remote attackers to bypass port access restrictions on an FTP server by forcing it to send malicious packets that Firewall-1 misinterprets as a valid 227 response to a client's PASV attempt.", "metadata": {"filename": "2000.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-151", "filename": "2000.txt", "content": "CVE-2000-0151(PUBLISHED):GNU make follows symlinks when it reads a Makefile from stdin, which allows other local users to execute commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-152", "filename": "2000.txt", "content": "CVE-2000-0152(PUBLISHED):Remote attackers can cause a denial of service in Novell BorderManager 3.5 by pressing the enter key in a telnet connection to port 2000.", "metadata": {"filename": "2000.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-153", "filename": "2000.txt", "content": "CVE-2000-0153(PUBLISHED):FrontPage Personal Web Server (PWS) allows remote attackers to read files via a .... (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-154", "filename": "2000.txt", "content": "CVE-2000-0154(PUBLISHED):The ARCserve agent in UnixWare allows local attackers to modify arbitrary files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-155", "filename": "2000.txt", "content": "CVE-2000-0155(PUBLISHED):Windows NT Autorun executes the autorun.inf file on non-removable media, which allows local attackers to specify an alternate program to execute when other users access a drive.", "metadata": {"filename": "2000.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-156", "filename": "2000.txt", "content": "CVE-2000-0156(PUBLISHED):Internet Explorer 4.x and 5.x allows remote web servers to access files on the client that are outside of its security domain, aka the \"Image Source Redirect\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-157", "filename": "2000.txt", "content": "CVE-2000-0157(PUBLISHED):NetBSD ptrace call on VAX allows local users to gain privileges by modifying the PSL contents in the debugging process.", "metadata": {"filename": "2000.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-158", "filename": "2000.txt", "content": "CVE-2000-0158(PUBLISHED):Buffer overflow in MMDF server allows remote attackers to gain privileges via a long MAIL FROM command to the SMTP daemon.", "metadata": {"filename": "2000.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-159", "filename": "2000.txt", "content": "CVE-2000-0159(PUBLISHED):HP Ignite-UX does not save /etc/passwd when it creates an image of a trusted system, which can set the password field to a blank and allow an attacker to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-160", "filename": "2000.txt", "content": "CVE-2000-0160(PUBLISHED):The Microsoft Active Setup ActiveX component in Internet Explorer 4.x and 5.x allows a remote attacker to install software components without prompting the user by stating that the software's manufacturer is Microsoft.", "metadata": {"filename": "2000.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-161", "filename": "2000.txt", "content": "CVE-2000-0161(PUBLISHED):Sample web sites on Microsoft Site Server 3.0 Commerce Edition do not validate an identification number, which allows remote attackers to execute SQL commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-162", "filename": "2000.txt", "content": "CVE-2000-0162(PUBLISHED):The Microsoft virtual machine (VM) in Internet Explorer 4.x and 5.x allows a remote attacker to read files via a malicious Java applet that escapes the Java sandbox, aka the \"VM File Reading\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-163", "filename": "2000.txt", "content": "CVE-2000-0163(PUBLISHED):asmon and ascpu in FreeBSD allow local users to gain root privileges via a configuration file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-164", "filename": "2000.txt", "content": "CVE-2000-0164(PUBLISHED):The installation of Sun Internet Mail Server (SIMS) creates a world-readable file that allows local users to obtain passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-165", "filename": "2000.txt", "content": "CVE-2000-0165(PUBLISHED):The Delegate application proxy has several buffer overflows which allow a remote attacker to execute commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-166", "filename": "2000.txt", "content": "CVE-2000-0166(PUBLISHED):Buffer overflow in the InterAccess telnet server TelnetD allows remote attackers to execute commands via a long login name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-167", "filename": "2000.txt", "content": "CVE-2000-0167(PUBLISHED):IIS Inetinfo.exe allows local users to cause a denial of service by creating a mail file with a long name and a .txt.eml extension in the pickup directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-168", "filename": "2000.txt", "content": "CVE-2000-0168(PUBLISHED):Microsoft Windows 9x operating systems allow an attacker to cause a denial of service via a pathname that includes file device names, aka the \"DOS Device in Path Name\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-169", "filename": "2000.txt", "content": "CVE-2000-0169(PUBLISHED):Batch files in the Oracle web listener ows-bin directory allow remote attackers to execute commands via a malformed URL that includes '?&'.", "metadata": {"filename": "2000.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-170", "filename": "2000.txt", "content": "CVE-2000-0170(PUBLISHED):Buffer overflow in the man program in Linux allows local users to gain privileges via the MANPAGER environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-171", "filename": "2000.txt", "content": "CVE-2000-0171(PUBLISHED):atsadc in the atsar package for Linux does not properly check the permissions of an output file, which allows local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-172", "filename": "2000.txt", "content": "CVE-2000-0172(PUBLISHED):The mtr program only uses a seteuid call when attempting to drop privileges, which could allow local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-173", "filename": "2000.txt", "content": "CVE-2000-0173(PUBLISHED):Vulnerability in the EELS system in SCO UnixWare 7.1.x allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-174", "filename": "2000.txt", "content": "CVE-2000-0174(PUBLISHED):StarOffice StarScheduler web server allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-175", "filename": "2000.txt", "content": "CVE-2000-0175(PUBLISHED):Buffer overflow in StarOffice StarScheduler web server allows remote attackers to gain root access via a long GET command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-176", "filename": "2000.txt", "content": "CVE-2000-0176(PUBLISHED):The default configuration of Serv-U 2.5d and earlier allows remote attackers to determine the real pathname of the server by requesting a URL for a directory or file that does not exist.", "metadata": {"filename": "2000.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-177", "filename": "2000.txt", "content": "CVE-2000-0177(PUBLISHED):DNSTools CGI applications allow remote attackers to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-178", "filename": "2000.txt", "content": "CVE-2000-0178(PUBLISHED):ServerIron switches by Foundry Networks have predictable TCP/IP sequence numbers, which allows remote attackers to spoof or hijack sessions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-179", "filename": "2000.txt", "content": "CVE-2000-0179(PUBLISHED):HP OpenView OmniBack 2.55 allows remote attackers to cause a denial of service via a large number of connections to port 5555.", "metadata": {"filename": "2000.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-180", "filename": "2000.txt", "content": "CVE-2000-0180(PUBLISHED):Sojourn search engine allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-181", "filename": "2000.txt", "content": "CVE-2000-0181(PUBLISHED):Firewall-1 3.0 and 4.0 leaks packets with private IP address information, which could allow remote attackers to determine the real IP address of the host that is making the connection.", "metadata": {"filename": "2000.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-182", "filename": "2000.txt", "content": "CVE-2000-0182(PUBLISHED):iPlanet Web Server 4.1 allows remote attackers to cause a denial of service via a large number of GET commands, which consumes memory and causes a kernel panic.", "metadata": {"filename": "2000.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-183", "filename": "2000.txt", "content": "CVE-2000-0183(PUBLISHED):Buffer overflow in ircII 4.4 IRC client allows remote attackers to execute commands via the DCC chat capability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-184", "filename": "2000.txt", "content": "CVE-2000-0184(PUBLISHED):Linux printtool sets the permissions of printer configuration files to be world-readable, which allows local attackers to obtain printer share passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-185", "filename": "2000.txt", "content": "CVE-2000-0185(PUBLISHED):RealMedia RealServer reveals the real IP address of a Real Server, even if the address is supposed to be private.", "metadata": {"filename": "2000.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-186", "filename": "2000.txt", "content": "CVE-2000-0186(PUBLISHED):Buffer overflow in the dump utility in the Linux ext2fs backup package allows local users to gain privileges via a long command line argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-187", "filename": "2000.txt", "content": "CVE-2000-0187(PUBLISHED):EZShopper 3.0 loadpage.cgi CGI script allows remote attackers to read arbitrary files via a .. (dot dot) attack or execute commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-188", "filename": "2000.txt", "content": "CVE-2000-0188(PUBLISHED):EZShopper 3.0 search.cgi CGI script allows remote attackers to read arbitrary files via a .. (dot dot) attack or execute commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-189", "filename": "2000.txt", "content": "CVE-2000-0189(PUBLISHED):ColdFusion Server 4.x allows remote attackers to determine the real pathname of the server via an HTTP request to the application.cfm or onrequestend.cfm files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-190", "filename": "2000.txt", "content": "CVE-2000-0190(PUBLISHED):AOL Instant Messenger (AIM) client allows remote attackers to cause a denial of service via a message with a malformed ASCII value.", "metadata": {"filename": "2000.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-191", "filename": "2000.txt", "content": "CVE-2000-0191(PUBLISHED):Axis StorPoint CD allows remote attackers to access administrator URLs without authentication via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-192", "filename": "2000.txt", "content": "CVE-2000-0192(PUBLISHED):The default installation of Caldera OpenLinux 2.3 includes the CGI program rpm_query, which allows remote attackers to determine what packages are installed on the system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-193", "filename": "2000.txt", "content": "CVE-2000-0193(PUBLISHED):The default configuration of Dosemu in Corel Linux 1.0 allows local users to execute the system.com program and gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-194", "filename": "2000.txt", "content": "CVE-2000-0194(PUBLISHED):buildxconf in Corel Linux allows local users to modify or create arbitrary files via the -x or -f parameters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-195", "filename": "2000.txt", "content": "CVE-2000-0195(PUBLISHED):setxconf in Corel Linux allows local users to gain root access via the -T parameter, which executes the user's .xserverrc file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-196", "filename": "2000.txt", "content": "CVE-2000-0196(PUBLISHED):Buffer overflow in mhshow in the Linux nmh package allows remote attackers to execute commands via malformed MIME headers in an email message.", "metadata": {"filename": "2000.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-197", "filename": "2000.txt", "content": "CVE-2000-0197(PUBLISHED):The Windows NT scheduler uses the drive mapping of the interactive user who is currently logged onto the system, which allows the local user to gain privileges by providing a Trojan horse batch file in place of the original batch file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-198", "filename": "2000.txt", "content": "CVE-2000-0198(PUBLISHED):Buffer overflow in POP3 and IMAP servers in the MERCUR mail server suite allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-199", "filename": "2000.txt", "content": "CVE-2000-0199(PUBLISHED):When a new SQL Server is registered in Enterprise Manager for Microsoft SQL Server 7.0 and the \"Always prompt for login name and password\" option is not set, then the Enterprise Manager uses weak encryption to store the login ID and password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-200", "filename": "2000.txt", "content": "CVE-2000-0200(PUBLISHED):Buffer overflow in Microsoft Clip Art Gallery allows remote attackers to cause a denial of service or execute commands via a malformed CIL (clip art library) file, aka the \"Clip Art Buffer Overrun\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-201", "filename": "2000.txt", "content": "CVE-2000-0201(PUBLISHED):The window.showHelp() method in Internet Explorer 5.x does not restrict HTML help files (.chm) to be executed from the local host, which allows remote attackers to execute arbitrary commands via Microsoft Networking.", "metadata": {"filename": "2000.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-202", "filename": "2000.txt", "content": "CVE-2000-0202(PUBLISHED):Microsoft SQL Server 7.0 and Microsoft Data Engine (MSDE) 1.0 allow remote attackers to gain privileges via a malformed Select statement in an SQL query.", "metadata": {"filename": "2000.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-203", "filename": "2000.txt", "content": "CVE-2000-0203(PUBLISHED):The Trend Micro OfficeScan client tmlisten.exe allows remote attackers to cause a denial of service via malformed data to port 12345.", "metadata": {"filename": "2000.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-204", "filename": "2000.txt", "content": "CVE-2000-0204(PUBLISHED):The Trend Micro OfficeScan client allows remote attackers to cause a denial of service by making 5 connections to port 12345, which raises CPU utilization to 100%.", "metadata": {"filename": "2000.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-205", "filename": "2000.txt", "content": "CVE-2000-0205(PUBLISHED):Trend Micro OfficeScan allows remote attackers to replay administrative commands and modify the configuration of OfficeScan clients.", "metadata": {"filename": "2000.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-206", "filename": "2000.txt", "content": "CVE-2000-0206(PUBLISHED):The installation of Oracle 8.1.5.x on Linux follows symlinks and creates the orainstRoot.sh file with world-writeable permissions, which allows local users to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-207", "filename": "2000.txt", "content": "CVE-2000-0207(PUBLISHED):SGI InfoSearch CGI program infosrch.cgi allows remote attackers to execute commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-208", "filename": "2000.txt", "content": "CVE-2000-0208(PUBLISHED):The htdig (ht://Dig) CGI program htsearch allows remote attackers to read arbitrary files by enclosing the file name with backticks (`) in parameters to htsearch.", "metadata": {"filename": "2000.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-209", "filename": "2000.txt", "content": "CVE-2000-0209(PUBLISHED):Buffer overflow in Lynx 2.x allows remote attackers to crash Lynx and possibly execute commands via a long URL in a malicious web page.", "metadata": {"filename": "2000.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-210", "filename": "2000.txt", "content": "CVE-2000-0210(PUBLISHED):The lit program in Sun Flex License Manager (FlexLM) follows symlinks, which allows local users to modify arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-211", "filename": "2000.txt", "content": "CVE-2000-0211(PUBLISHED):The Windows Media server allows remote attackers to cause a denial of service via a series of client handshake packets that are sent in an improper sequence, aka the \"Misordered Windows Media Services Handshake\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-212", "filename": "2000.txt", "content": "CVE-2000-0212(PUBLISHED):InterAccess TelnetD Server 4.0 allows remote attackers to conduct a denial of service via malformed terminal client configuration information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-213", "filename": "2000.txt", "content": "CVE-2000-0213(PUBLISHED):The Sambar server includes batch files ECHO.BAT and HELLO.BAT in the CGI directory, which allow remote attackers to execute commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-214", "filename": "2000.txt", "content": "CVE-2000-0214(PUBLISHED):FTP Explorer uses weak encryption for storing the username, password, and profile of FTP sites.", "metadata": {"filename": "2000.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-215", "filename": "2000.txt", "content": "CVE-2000-0215(PUBLISHED):Vulnerability in SCO cu program in UnixWare 7.x allows local users to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-216", "filename": "2000.txt", "content": "CVE-2000-0216(PUBLISHED):Microsoft email clients in Outlook, Exchange, and Windows Messaging automatically respond to Read Receipt and Delivery Receipt tags, which could allow an attacker to flood a mail system with responses by forging a Read Receipt request that is redirected to a large distribution list.", "metadata": {"filename": "2000.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-217", "filename": "2000.txt", "content": "CVE-2000-0217(PUBLISHED):The default configuration of SSH allows X forwarding, which could allow a remote attacker to control a client's X sessions via a malicious xauth program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-218", "filename": "2000.txt", "content": "CVE-2000-0218(PUBLISHED):Buffer overflow in Linux mount and umount allows local users to gain root privileges via a long relative pathname.", "metadata": {"filename": "2000.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-219", "filename": "2000.txt", "content": "CVE-2000-0219(PUBLISHED):Red Hat 6.0 allows local users to gain root access by booting single user and hitting ^C at the password prompt.", "metadata": {"filename": "2000.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-220", "filename": "2000.txt", "content": "CVE-2000-0220(PUBLISHED):ZoneAlarm sends sensitive system and network information in cleartext to the Zone Labs server if a user requests more information about an event.", "metadata": {"filename": "2000.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-221", "filename": "2000.txt", "content": "CVE-2000-0221(PUBLISHED):The Nautica Marlin bridge allows remote attackers to cause a denial of service via a zero length UDP packet to the SNMP port.", "metadata": {"filename": "2000.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-222", "filename": "2000.txt", "content": "CVE-2000-0222(PUBLISHED):The installation for Windows 2000 does not activate the Administrator password until the system has rebooted, which allows remote attackers to connect to the ADMIN$ share without a password until the reboot occurs.", "metadata": {"filename": "2000.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-223", "filename": "2000.txt", "content": "CVE-2000-0223(PUBLISHED):Buffer overflow in the wmcdplay CD player program for the WindowMaker desktop allows local users to gain root privileges via a long parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-224", "filename": "2000.txt", "content": "CVE-2000-0224(PUBLISHED):ARCserve agent in SCO UnixWare 7.x allows local attackers to gain root privileges via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-225", "filename": "2000.txt", "content": "CVE-2000-0225(PUBLISHED):The Pocsag POC32 program does not properly prevent remote users from accessing its server port, even if the option has been disabled.", "metadata": {"filename": "2000.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-226", "filename": "2000.txt", "content": "CVE-2000-0226(PUBLISHED):IIS 4.0 allows attackers to cause a denial of service by requesting a large buffer in a POST or PUT command which consumes memory, aka the \"Chunked Transfer Encoding Buffer Overflow Vulnerability.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-227", "filename": "2000.txt", "content": "CVE-2000-0227(PUBLISHED):The Linux 2.2.x kernel does not restrict the number of Unix domain sockets as defined by the wmem_max parameter, which allows local users to cause a denial of service by requesting a large number of sockets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-228", "filename": "2000.txt", "content": "CVE-2000-0228(PUBLISHED):Microsoft Windows Media License Manager allows remote attackers to cause a denial of service by sending a malformed request that causes the manager to halt, aka the \"Malformed Media License Request\" Vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-229", "filename": "2000.txt", "content": "CVE-2000-0229(PUBLISHED):gpm-root in the gpm package does not properly drop privileges, which allows local users to gain privileges by starting a utility from gpm-root.", "metadata": {"filename": "2000.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-230", "filename": "2000.txt", "content": "CVE-2000-0230(PUBLISHED):Buffer overflow in imwheel allows local users to gain root privileges via the imwheel-solo script and a long HOME environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-231", "filename": "2000.txt", "content": "CVE-2000-0231(PUBLISHED):Linux kreatecd trusts a user-supplied path that is used to find the cdrecord program, allowing local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-232", "filename": "2000.txt", "content": "CVE-2000-0232(PUBLISHED):Microsoft TCP/IP Printing Services, aka Print Services for Unix, allows an attacker to cause a denial of service via a malformed TCP/IP print request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-233", "filename": "2000.txt", "content": "CVE-2000-0233(PUBLISHED):SuSE Linux IMAP server allows remote attackers to bypass IMAP authentication and gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-234", "filename": "2000.txt", "content": "CVE-2000-0234(PUBLISHED):The default configuration of Cobalt RaQ2 and RaQ3 as specified in access.conf allows remote attackers to view sensitive contents of a .htaccess file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-235", "filename": "2000.txt", "content": "CVE-2000-0235(PUBLISHED):Buffer overflow in the huh program in the orville-write package allows local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-236", "filename": "2000.txt", "content": "CVE-2000-0236(PUBLISHED):Netscape Enterprise Server with Directory Indexing enabled allows remote attackers to list server directories via web publishing tags such as ?wp-ver-info and ?wp-cs-dump.", "metadata": {"filename": "2000.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-237", "filename": "2000.txt", "content": "CVE-2000-0237(PUBLISHED):Netscape Enterprise Server with Web Publishing enabled allows remote attackers to list arbitrary directories via a GET request for the /publisher directory, which provides a Java applet that allows the attacker to browse the directories.", "metadata": {"filename": "2000.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-238", "filename": "2000.txt", "content": "CVE-2000-0238(PUBLISHED):Buffer overflow in the web server for Norton AntiVirus for Internet Email Gateways allows remote attackers to cause a denial of service via a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-239", "filename": "2000.txt", "content": "CVE-2000-0239(PUBLISHED):Buffer overflow in the MERCUR WebView WebMail server allows remote attackers to cause a denial of service via a long mail_user parameter in the GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-240", "filename": "2000.txt", "content": "CVE-2000-0240(PUBLISHED):vqSoft vqServer program allows remote attackers to read arbitrary files via a /........../ in the URL, a variation of a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-241", "filename": "2000.txt", "content": "CVE-2000-0241(PUBLISHED):vqSoft vqServer stores sensitive information such as passwords in cleartext in the server.cfg file, which allows attackers to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-242", "filename": "2000.txt", "content": "CVE-2000-0242(PUBLISHED):WindMail allows remote attackers to read arbitrary files or execute commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-243", "filename": "2000.txt", "content": "CVE-2000-0243(PUBLISHED):AnalogX SimpleServer:WWW HTTP server 1.03 allows remote attackers to cause a denial of service via a short GET request to cgi-bin.", "metadata": {"filename": "2000.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-244", "filename": "2000.txt", "content": "CVE-2000-0244(PUBLISHED):The Citrix ICA (Independent Computing Architecture) protocol uses weak encryption (XOR) for user authentication.", "metadata": {"filename": "2000.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-245", "filename": "2000.txt", "content": "CVE-2000-0245(PUBLISHED):Vulnerability in SGI IRIX objectserver daemon allows remote attackers to create user accounts.", "metadata": {"filename": "2000.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-246", "filename": "2000.txt", "content": "CVE-2000-0246(PUBLISHED):IIS 4.0 and 5.0 does not properly perform ISAPI extension processing if a virtual directory is mapped to a UNC share, which allows remote attackers to read the source code of ASP and other files, aka the \"Virtualized UNC Share\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-247", "filename": "2000.txt", "content": "CVE-2000-0247(PUBLISHED):Unknown vulnerability in Generic-NQS (GNQS) allows local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-248", "filename": "2000.txt", "content": "CVE-2000-0248(PUBLISHED):The web GUI for the Linux Virtual Server (LVS) software in the Red Hat Linux Piranha package has a backdoor password that allows remote attackers to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-249", "filename": "2000.txt", "content": "CVE-2000-0249(PUBLISHED):The AIX Fast Response Cache Accelerator (FRCA) allows local users to modify arbitrary files via the configuration capability in the frcactrl program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-250", "filename": "2000.txt", "content": "CVE-2000-0250(PUBLISHED):The crypt function in QNX uses weak encryption, which allows local users to decrypt passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-251", "filename": "2000.txt", "content": "CVE-2000-0251(PUBLISHED):HP-UX 11.04 VirtualVault (VVOS) sends data to unprivileged processes via an interface that has multiple aliased IP addresses.", "metadata": {"filename": "2000.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-252", "filename": "2000.txt", "content": "CVE-2000-0252(PUBLISHED):The dansie shopping cart application cart.pl allows remote attackers to execute commands via a shell metacharacters in a form variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-253", "filename": "2000.txt", "content": "CVE-2000-0253(PUBLISHED):The dansie shopping cart application cart.pl allows remote attackers to modify sensitive purchase information via hidden form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-254", "filename": "2000.txt", "content": "CVE-2000-0254(PUBLISHED):The dansie shopping cart application cart.pl allows remote attackers to obtain the shopping cart database and configuration information via a URL that references either the env, db, or vars form variables.", "metadata": {"filename": "2000.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-255", "filename": "2000.txt", "content": "CVE-2000-0255(PUBLISHED):The Nbase-Xyplex EdgeBlaster router allows remote attackers to cause a denial of service via a scan for the FormMail CGI program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-256", "filename": "2000.txt", "content": "CVE-2000-0256(PUBLISHED):Buffer overflows in htimage.exe and Imagemap.exe in FrontPage 97 and 98 Server Extensions allow a user to conduct activities that are not otherwise available through the web site, aka the \"Server-Side Image Map Components\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-257", "filename": "2000.txt", "content": "CVE-2000-0257(PUBLISHED):Buffer overflow in the NetWare remote web administration utility allows remote attackers to cause a denial of service or execute commands via a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-258", "filename": "2000.txt", "content": "CVE-2000-0258(PUBLISHED):IIS 4.0 and 5.0 allows remote attackers to cause a denial of service by sending many URLs with a large number of escaped characters, aka the \"Myriad Escaped Characters\" Vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-259", "filename": "2000.txt", "content": "CVE-2000-0259(PUBLISHED):The default permissions for the Cryptography\\Offload registry key used by the OffloadModExpo in Windows NT 4.0 allows local users to obtain compromise the cryptographic keys of other users.", "metadata": {"filename": "2000.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-260", "filename": "2000.txt", "content": "CVE-2000-0260(PUBLISHED):Buffer overflow in the dvwssr.dll DLL in Microsoft Visual Interdev 1.0 allows users to cause a denial of service or execute commands, aka the \"Link View Server-Side Component\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-261", "filename": "2000.txt", "content": "CVE-2000-0261(PUBLISHED):The AVM KEN! web server allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-262", "filename": "2000.txt", "content": "CVE-2000-0262(PUBLISHED):The AVM KEN! ISDN Proxy server allows remote attackers to cause a denial of service via a malformed request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-263", "filename": "2000.txt", "content": "CVE-2000-0263(PUBLISHED):The X font server xfs in Red Hat Linux 6.x allows an attacker to cause a denial of service via a malformed request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-264", "filename": "2000.txt", "content": "CVE-2000-0264(PUBLISHED):Panda Security 3.0 with registry editing disabled allows users to edit the registry and gain privileges by directly executing a .reg file or using other methods.", "metadata": {"filename": "2000.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-265", "filename": "2000.txt", "content": "CVE-2000-0265(PUBLISHED):Panda Security 3.0 allows users to uninstall the Panda software via its Add/Remove Programs applet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-266", "filename": "2000.txt", "content": "CVE-2000-0266(PUBLISHED):Internet Explorer 5.01 allows remote attackers to bypass the cross frame security policy via a malicious applet that interacts with the Java JSObject to modify the DOM properties to set the IFRAME to an arbitrary Javascript URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-267", "filename": "2000.txt", "content": "CVE-2000-0267(PUBLISHED):Cisco Catalyst 5.4.x allows a user to gain access to the \"enable\" mode without a password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-268", "filename": "2000.txt", "content": "CVE-2000-0268(PUBLISHED):Cisco IOS 11.x and 12.x allows remote attackers to cause a denial of service by sending the ENVIRON option to the Telnet daemon before it is ready to accept it, which causes the system to reboot.", "metadata": {"filename": "2000.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-269", "filename": "2000.txt", "content": "CVE-2000-0269(PUBLISHED):Emacs 20 does not properly set permissions for a slave PTY device when starting a new subprocess, which allows local users to read or modify communications between Emacs and the subprocess.", "metadata": {"filename": "2000.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-270", "filename": "2000.txt", "content": "CVE-2000-0270(PUBLISHED):The make-temp-name Lisp function in Emacs 20 creates temporary files with predictable names, which allows attackers to conduct a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-271", "filename": "2000.txt", "content": "CVE-2000-0271(PUBLISHED):read-passwd and other Lisp functions in Emacs 20 do not properly clear the history of recently typed keys, which allows an attacker to read unencrypted passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-272", "filename": "2000.txt", "content": "CVE-2000-0272(PUBLISHED):RealNetworks RealServer allows remote attackers to cause a denial of service by sending malformed input to the server at port 7070.", "metadata": {"filename": "2000.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-273", "filename": "2000.txt", "content": "CVE-2000-0273(PUBLISHED):PCAnywhere allows remote attackers to cause a denial of service by terminating the connection before PCAnywhere provides a login prompt.", "metadata": {"filename": "2000.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-274", "filename": "2000.txt", "content": "CVE-2000-0274(PUBLISHED):The Linux trustees kernel patch allows attackers to cause a denial of service by accessing a file or directory with a long name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-275", "filename": "2000.txt", "content": "CVE-2000-0275(PUBLISHED):CRYPTOCard CryptoAdmin for PalmOS uses weak encryption to store a user's PIN number, which allows an attacker with access to the .PDB file to generate valid PT-1 tokens after cracking the PIN.", "metadata": {"filename": "2000.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-276", "filename": "2000.txt", "content": "CVE-2000-0276(PUBLISHED):BeOS 4.5 and 5.0 allow local users to cause a denial of service via malformed direct system calls using interrupt 37.", "metadata": {"filename": "2000.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-277", "filename": "2000.txt", "content": "CVE-2000-0277(PUBLISHED):Microsoft Excel 97 and 2000 does not warn the user when executing Excel Macro Language (XLM) macros in external text files, which could allow an attacker to execute a macro virus, aka the \"XLM Text Macro\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-278", "filename": "2000.txt", "content": "CVE-2000-0278(PUBLISHED):The SalesLogix Eviewer allows remote attackers to cause a denial of service by accessing the URL for the slxweb.dll administration program, which does not authenticate the user.", "metadata": {"filename": "2000.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-279", "filename": "2000.txt", "content": "CVE-2000-0279(PUBLISHED):BeOS allows remote attackers to cause a denial of service via malformed packets whose length field is less than the length of the headers.", "metadata": {"filename": "2000.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-280", "filename": "2000.txt", "content": "CVE-2000-0280(PUBLISHED):Buffer overflow in the RealNetworks RealPlayer client versions 6 and 7 allows remote attackers to cause a denial of service via a long Location URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-281", "filename": "2000.txt", "content": "CVE-2000-0281(PUBLISHED):Buffer overflow in the Napster client beta 5 allows remote attackers to cause a denial of service via a long message.", "metadata": {"filename": "2000.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-282", "filename": "2000.txt", "content": "CVE-2000-0282(PUBLISHED):TalentSoft webpsvr daemon in the Web+ shopping cart application allows remote attackers to read arbitrary files via a .. (dot dot) attack on the webplus CGI program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-283", "filename": "2000.txt", "content": "CVE-2000-0283(PUBLISHED):The default installation of IRIX Performance Copilot allows remote attackers to access sensitive system information via the pmcd daemon.", "metadata": {"filename": "2000.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-284", "filename": "2000.txt", "content": "CVE-2000-0284(PUBLISHED):Buffer overflow in University of Washington imapd version 4.7 allows users with a valid account to execute commands via LIST or other commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-285", "filename": "2000.txt", "content": "CVE-2000-0285(PUBLISHED):Buffer overflow in XFree86 3.3.x allows local users to execute arbitrary commands via a long -xkbmap parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-286", "filename": "2000.txt", "content": "CVE-2000-0286(PUBLISHED):X fontserver xfs allows local users to cause a denial of service via malformed input to the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-287", "filename": "2000.txt", "content": "CVE-2000-0287(PUBLISHED):The BizDB CGI script bizdb-search.cgi allows remote attackers to execute arbitrary commands via shell metacharacters in the dbname parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-288", "filename": "2000.txt", "content": "CVE-2000-0288(PUBLISHED):Infonautics getdoc.cgi allows remote attackers to bypass the payment phase for accessing documents via a modified form variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-289", "filename": "2000.txt", "content": "CVE-2000-0289(PUBLISHED):IP masquerading in Linux 2.2.x allows remote attackers to route UDP packets through the internal interface by modifying the external source IP address and port number to match those of an established connection.", "metadata": {"filename": "2000.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-290", "filename": "2000.txt", "content": "CVE-2000-0290(PUBLISHED):Buffer overflow in Webstar HTTP server allows remote attackers to cause a denial of service via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-291", "filename": "2000.txt", "content": "CVE-2000-0291(PUBLISHED):Buffer overflow in Star Office 5.1 allows attackers to cause a denial of service by embedding a long URL within a document.", "metadata": {"filename": "2000.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-292", "filename": "2000.txt", "content": "CVE-2000-0292(PUBLISHED):The Adtran MX2800 M13 Multiplexer allows remote attackers to cause a denial of service via a ping flood to the Ethernet interface, which causes the device to crash.", "metadata": {"filename": "2000.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-293", "filename": "2000.txt", "content": "CVE-2000-0293(PUBLISHED):aaa_base in SuSE Linux 6.3, and cron.daily in earlier versions, allow local users to delete arbitrary files by creating files whose names include spaces, which are then incorrectly interpreted by aaa_base when it deletes expired files from the /tmp directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-294", "filename": "2000.txt", "content": "CVE-2000-0294(PUBLISHED):Buffer overflow in healthd for FreeBSD allows local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-295", "filename": "2000.txt", "content": "CVE-2000-0295(PUBLISHED):Buffer overflow in LCDproc allows remote attackers to gain root privileges via the screen_add command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-296", "filename": "2000.txt", "content": "CVE-2000-0296(PUBLISHED):fcheck allows local users to gain privileges by embedding shell metacharacters into file names that are processed by fcheck.", "metadata": {"filename": "2000.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-297", "filename": "2000.txt", "content": "CVE-2000-0297(PUBLISHED):Allaire Forums 2.0.5 allows remote attackers to bypass access restrictions to secure conferences via the rightAccessAllForums or rightModerateAllForums variables.", "metadata": {"filename": "2000.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-298", "filename": "2000.txt", "content": "CVE-2000-0298(PUBLISHED):The unattended installation of Windows 2000 with the OEMPreinstall option sets insecure permissions for the All Users and Default Users directories.", "metadata": {"filename": "2000.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-299", "filename": "2000.txt", "content": "CVE-2000-0299(PUBLISHED):Buffer overflow in WebObjects.exe in the WebObjects Developer 4.5 package allows remote attackers to cause a denial of service via an HTTP request with long headers such as Accept.", "metadata": {"filename": "2000.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-300", "filename": "2000.txt", "content": "CVE-2000-0300(PUBLISHED):The default encryption method of PcAnywhere 9.x uses weak encryption, which allows remote attackers to sniff and decrypt PcAnywhere or NT domain accounts.", "metadata": {"filename": "2000.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-301", "filename": "2000.txt", "content": "CVE-2000-0301(PUBLISHED):Ipswitch IMAIL server 6.02 and earlier allows remote attackers to cause a denial of service via the AUTH CRAM-MD5 command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-302", "filename": "2000.txt", "content": "CVE-2000-0302(PUBLISHED):Microsoft Index Server allows remote attackers to view the source code of ASP files by appending a %20 to the filename in the CiWebHitsFile argument to the null.htw URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-303", "filename": "2000.txt", "content": "CVE-2000-0303(PUBLISHED):Quake3 Arena allows malicious server operators to read or modify files on a client via a dot dot (..) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-304", "filename": "2000.txt", "content": "CVE-2000-0304(PUBLISHED):Microsoft IIS 4.0 and 5.0 with the IISADMPWD virtual directory installed allows a remote attacker to cause a denial of service via a malformed request to the inetinfo.exe program, aka the \"Undelimited .HTR Request\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-305", "filename": "2000.txt", "content": "CVE-2000-0305(PUBLISHED):Windows 95, Windows 98, Windows 2000, Windows NT 4.0, and Terminal Server systems allow a remote attacker to cause a denial of service by sending a large number of identical fragmented IP packets, aka jolt2 or the \"IP Fragment Reassembly\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-306", "filename": "2000.txt", "content": "CVE-2000-0306(PUBLISHED):Buffer overflow in calserver in SCO OpenServer allows remote attackers to gain root access via a long message.", "metadata": {"filename": "2000.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-307", "filename": "2000.txt", "content": "CVE-2000-0307(PUBLISHED):Vulnerability in xserver in SCO UnixWare 2.1.x and OpenServer 5.05 and earlier allows an attacker to cause a denial of service which prevents access to reserved port numbers below 1024.", "metadata": {"filename": "2000.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-308", "filename": "2000.txt", "content": "CVE-2000-0308(PUBLISHED):Insecure file permissions for Netscape FastTrack Server 2.x, Enterprise Server 2.0, and Proxy Server 2.5 in SCO UnixWare 7.0.x and 2.1.3 allow an attacker to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-309", "filename": "2000.txt", "content": "CVE-2000-0309(PUBLISHED):The i386 trace-trap handling in OpenBSD 2.4 with DDB enabled allows a local user to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-310", "filename": "2000.txt", "content": "CVE-2000-0310(PUBLISHED):IP fragment assembly in OpenBSD 2.4 allows a remote attacker to cause a denial of service by sending a large number of fragmented packets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-311", "filename": "2000.txt", "content": "CVE-2000-0311(PUBLISHED):The Windows 2000 domain controller allows a malicious user to modify Active Directory information by modifying an unprotected attribute, aka the \"Mixed Object Access\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-312", "filename": "2000.txt", "content": "CVE-2000-0312(PUBLISHED):cron in OpenBSD 2.5 allows local users to gain root privileges via an argv[] that is not NULL terminated, which is passed to cron's fake popen function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-313", "filename": "2000.txt", "content": "CVE-2000-0313(PUBLISHED):Vulnerability in OpenBSD 2.6 allows a local user to change interface media configurations.", "metadata": {"filename": "2000.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-314", "filename": "2000.txt", "content": "CVE-2000-0314(PUBLISHED):traceroute in NetBSD 1.3.3 and Linux systems allows local users to flood other systems by providing traceroute with a large waittime (-w) option, which is not parsed properly and sets the time delay for sending packets to zero.", "metadata": {"filename": "2000.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-315", "filename": "2000.txt", "content": "CVE-2000-0315(PUBLISHED):traceroute in NetBSD 1.3.3 and Linux systems allows local unprivileged users to modify the source address of the packets, which could be used in spoofing attacks.", "metadata": {"filename": "2000.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-316", "filename": "2000.txt", "content": "CVE-2000-0316(PUBLISHED):Buffer overflow in Solaris 7 lp allows local users to gain root privileges via a long -d option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-317", "filename": "2000.txt", "content": "CVE-2000-0317(PUBLISHED):Buffer overflow in Solaris 7 lpset allows local users to gain root privileges via a long -r option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-318", "filename": "2000.txt", "content": "CVE-2000-0318(PUBLISHED):Atrium Mercur Mail Server 3.2 allows local attackers to read other user's email and create arbitrary files via a dot dot (..) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-319", "filename": "2000.txt", "content": "CVE-2000-0319(PUBLISHED):mail.local in Sendmail 8.10.x does not properly identify the .\\n string which identifies the end of message text, which allows a remote attacker to cause a denial of service or corrupt mailboxes via a message line that is 2047 characters long and ends in .\\n.", "metadata": {"filename": "2000.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-320", "filename": "2000.txt", "content": "CVE-2000-0320(PUBLISHED):Qpopper 2.53 and 3.0 does not properly identify the \\n string which identifies the end of message text, which allows a remote attacker to cause a denial of service or corrupt mailboxes via a message line that is 1023 characters long and ends in \\n.", "metadata": {"filename": "2000.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-321", "filename": "2000.txt", "content": "CVE-2000-0321(PUBLISHED):Buffer overflow in IC Radius package allows a remote attacker to cause a denial of service via a long user name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-322", "filename": "2000.txt", "content": "CVE-2000-0322(PUBLISHED):The passwd.php3 CGI script in the Red Hat Piranha Virtual Server Package allows local users to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-323", "filename": "2000.txt", "content": "CVE-2000-0323(PUBLISHED):The Microsoft Jet database engine allows an attacker to modify text files via a database query, aka the \"Text I-ISAM\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-324", "filename": "2000.txt", "content": "CVE-2000-0324(PUBLISHED):pcAnywhere 8.x and 9.0 allows remote attackers to cause a denial of service via a TCP SYN scan, e.g. by nmap.", "metadata": {"filename": "2000.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-325", "filename": "2000.txt", "content": "CVE-2000-0325(PUBLISHED):The Microsoft Jet database engine allows an attacker to execute commands via a database query, aka the \"VBA Shell\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-326", "filename": "2000.txt", "content": "CVE-2000-0326(PUBLISHED):Meeting Maker uses weak encryption (a polyalphabetic substitution cipher) for passwords, which allows remote attackers to sniff and decrypt passwords for Meeting Maker accounts.", "metadata": {"filename": "2000.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-327", "filename": "2000.txt", "content": "CVE-2000-0327(PUBLISHED):Microsoft Virtual Machine (VM) allows remote attackers to escape the Java sandbox and execute commands via an applet containing an illegal cast operation, aka the \"Virtual Machine Verifier\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-328", "filename": "2000.txt", "content": "CVE-2000-0328(PUBLISHED):Windows NT 4.0 generates predictable random TCP initial sequence numbers (ISN), which allows remote attackers to perform spoofing and session hijacking.", "metadata": {"filename": "2000.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-329", "filename": "2000.txt", "content": "CVE-2000-0329(PUBLISHED):A Microsoft ActiveX control allows a remote attacker to execute a malicious cabinet file via an attachment and an embedded script in an HTML mail, aka the \"Active Setup Control\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-330", "filename": "2000.txt", "content": "CVE-2000-0330(PUBLISHED):The networking software in Windows 95 and Windows 98 allows remote attackers to execute commands via a long file name string, aka the \"File Access URL\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-331", "filename": "2000.txt", "content": "CVE-2000-0331(PUBLISHED):Buffer overflow in Microsoft command processor (CMD.EXE) for Windows NT and Windows 2000 allows a local user to cause a denial of service via a long environment variable, aka the \"Malformed Environment Variable\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-332", "filename": "2000.txt", "content": "CVE-2000-0332(PUBLISHED):UltraBoard.pl or UltraBoard.cgi CGI scripts in UltraBoard 1.6 allows remote attackers to read arbitrary files via a pathname string that includes a dot dot (..) and ends with a null byte.", "metadata": {"filename": "2000.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-333", "filename": "2000.txt", "content": "CVE-2000-0333(PUBLISHED):tcpdump, Ethereal, and other sniffer packages allow remote attackers to cause a denial of service via malformed DNS packets in which a jump offset refers to itself, which causes tcpdump to enter an infinite loop while decompressing the packet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-334", "filename": "2000.txt", "content": "CVE-2000-0334(PUBLISHED):The Allaire Spectra container editor preview tool does not properly enforce object security, which allows an attacker to conduct unauthorized activities via an object-method that is added to the container object with a publishing rule.", "metadata": {"filename": "2000.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-335", "filename": "2000.txt", "content": "CVE-2000-0335(PUBLISHED):The resolver in glibc 2.1.3 uses predictable IDs, which allows a local attacker to spoof DNS query results.", "metadata": {"filename": "2000.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-336", "filename": "2000.txt", "content": "CVE-2000-0336(PUBLISHED):Linux OpenLDAP server allows local users to modify arbitrary files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-337", "filename": "2000.txt", "content": "CVE-2000-0337(PUBLISHED):Buffer overflow in Xsun X server in Solaris 7 allows local users to gain root privileges via a long -dev parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-338", "filename": "2000.txt", "content": "CVE-2000-0338(PUBLISHED):Concurrent Versions Software (CVS) uses predictable temporary file names for locking, which allows local users to cause a denial of service by creating the lock directory before it is created for use by a legitimate CVS user.", "metadata": {"filename": "2000.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-339", "filename": "2000.txt", "content": "CVE-2000-0339(PUBLISHED):ZoneAlarm 2.1.10 and earlier does not filter UDP packets with a source port of 67, which allows remote attackers to bypass the firewall rules.", "metadata": {"filename": "2000.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-340", "filename": "2000.txt", "content": "CVE-2000-0340(PUBLISHED):Buffer overflow in Gnomelib in SuSE Linux 6.3 allows local users to execute arbitrary commands via the DISPLAY environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-341", "filename": "2000.txt", "content": "CVE-2000-0341(PUBLISHED):ATRIUM Cassandra NNTP Server 1.10 allows remote attackers to cause a denial of service via a long login name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-342", "filename": "2000.txt", "content": "CVE-2000-0342(PUBLISHED):Eudora 4.x allows remote attackers to bypass the user warning for executable attachments such as .exe, .com, and .bat by using a .lnk file that refers to the attachment, aka \"Stealth Attachment.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-343", "filename": "2000.txt", "content": "CVE-2000-0343(PUBLISHED):Buffer overflow in Sniffit 0.3.x with the -L logging option enabled allows remote attackers to execute arbitrary commands via a long MAIL FROM mail header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-344", "filename": "2000.txt", "content": "CVE-2000-0344(PUBLISHED):The knfsd NFS server in Linux kernel 2.2.x allows remote attackers to cause a denial of service via a negative size value.", "metadata": {"filename": "2000.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-345", "filename": "2000.txt", "content": "CVE-2000-0345(PUBLISHED):The on-line help system options in Cisco routers allows non-privileged users without \"enabled\" access to obtain sensitive information via the show command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-346", "filename": "2000.txt", "content": "CVE-2000-0346(PUBLISHED):AppleShare IP 6.1 and later allows a remote attacker to read potentially sensitive information via an invalid range request to the web server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-347", "filename": "2000.txt", "content": "CVE-2000-0347(PUBLISHED):Windows 95 and Windows 98 allow a remote attacker to cause a denial of service via a NetBIOS session request packet with a NULL source name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-348", "filename": "2000.txt", "content": "CVE-2000-0348(PUBLISHED):A vulnerability in the Sendmail configuration file sendmail.cf as installed in SCO UnixWare 7.1.0 and earlier allows an attacker to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-349", "filename": "2000.txt", "content": "CVE-2000-0349(PUBLISHED):Vulnerability in the passthru driver in SCO UnixWare 7.1.0 allows an attacker to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-350", "filename": "2000.txt", "content": "CVE-2000-0350(PUBLISHED):A debugging feature in NetworkICE ICEcap 2.0.23 and earlier is enabled, which allows a remote attacker to bypass the weak authentication and post unencrypted events.", "metadata": {"filename": "2000.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-351", "filename": "2000.txt", "content": "CVE-2000-0351(PUBLISHED):Some packaging commands in SCO UnixWare 7.1.0 have insecure privileges, which allows local users to add or remove software packages.", "metadata": {"filename": "2000.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-352", "filename": "2000.txt", "content": "CVE-2000-0352(PUBLISHED):Pine before version 4.21 does not properly filter shell metacharacters from URLs, which allows remote attackers to execute arbitrary commands via a malformed URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-353", "filename": "2000.txt", "content": "CVE-2000-0353(PUBLISHED):Pine 4.x allows a remote attacker to execute arbitrary commands via an index.html file which executes lynx and obtains a uudecoded file from a malicious web server, which is then executed by Pine.", "metadata": {"filename": "2000.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-354", "filename": "2000.txt", "content": "CVE-2000-0354(PUBLISHED):mirror 2.8.x in Linux systems allows remote attackers to create files one level above the local target directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-355", "filename": "2000.txt", "content": "CVE-2000-0355(PUBLISHED):pg and pb in SuSE pbpg 1.x package allows an attacker to read arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-356", "filename": "2000.txt", "content": "CVE-2000-0356(PUBLISHED):Pluggable Authentication Modules (PAM) in Red Hat Linux 6.1 does not properly lock access to disabled NIS accounts.", "metadata": {"filename": "2000.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-357", "filename": "2000.txt", "content": "CVE-2000-0357(PUBLISHED):ORBit and esound in Red Hat Linux 6.1 do not use sufficiently random numbers, which allows local users to guess the authentication keys.", "metadata": {"filename": "2000.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-358", "filename": "2000.txt", "content": "CVE-2000-0358(PUBLISHED):ORBit and gnome-session in Red Hat Linux 6.1 allows remote attackers to crash a program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-359", "filename": "2000.txt", "content": "CVE-2000-0359(PUBLISHED):Buffer overflow in Trivial HTTP (THTTPd) allows remote attackers to cause a denial of service or execute arbitrary commands via a long If-Modified-Since header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-360", "filename": "2000.txt", "content": "CVE-2000-0360(PUBLISHED):Buffer overflow in INN 2.2.1 and earlier allows remote attackers to cause a denial of service via a maliciously formatted article.", "metadata": {"filename": "2000.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-361", "filename": "2000.txt", "content": "CVE-2000-0361(PUBLISHED):The PPP wvdial.lxdialog script in wvdial 1.4 and earlier creates a .config file with world readable permissions, which allows a local attacker in the dialout group to access login and password information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-362", "filename": "2000.txt", "content": "CVE-2000-0362(PUBLISHED):Buffer overflows in Linux cdwtools 093 and earlier allows local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-363", "filename": "2000.txt", "content": "CVE-2000-0363(PUBLISHED):Linux cdwtools 093 and earlier allows local users to gain root privileges via the /tmp directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-364", "filename": "2000.txt", "content": "CVE-2000-0364(PUBLISHED):screen and rxvt in Red Hat Linux 6.0 do not properly set the modes of tty devices, which allows local users to write to other ttys.", "metadata": {"filename": "2000.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-365", "filename": "2000.txt", "content": "CVE-2000-0365(PUBLISHED):Red Hat Linux 6.0 installs the /dev/pts file system with insecure modes, which allows local users to write to other tty devices.", "metadata": {"filename": "2000.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-366", "filename": "2000.txt", "content": "CVE-2000-0366(PUBLISHED):dump in Debian GNU/Linux 2.1 does not properly restore symlinks, which allows a local user to modify the ownership of arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-367", "filename": "2000.txt", "content": "CVE-2000-0367(PUBLISHED):Vulnerability in eterm 0.8.8 in Debian GNU/Linux allows an attacker to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-368", "filename": "2000.txt", "content": "CVE-2000-0368(PUBLISHED):Classic Cisco IOS 9.1 and later allows attackers with access to the login prompt to obtain portions of the command history of previous users, which may allow the attacker to access sensitive data.", "metadata": {"filename": "2000.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-369", "filename": "2000.txt", "content": "CVE-2000-0369(PUBLISHED):The IDENT server in Caldera Linux 2.3 creates multiple threads for each IDENT request, which allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-370", "filename": "2000.txt", "content": "CVE-2000-0370(PUBLISHED):The debug option in Caldera Linux smail allows remote attackers to execute commands via shell metacharacters in the -D option for the rmail command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-371", "filename": "2000.txt", "content": "CVE-2000-0371(PUBLISHED):The libmediatool library used for the KDE mediatool allows local users to create arbitrary files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-372", "filename": "2000.txt", "content": "CVE-2000-0372(PUBLISHED):Vulnerability in Caldera rmt command in the dump package 0.4b4 allows a local user to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-373", "filename": "2000.txt", "content": "CVE-2000-0373(PUBLISHED):Vulnerabilities in the KDE kvt terminal program allow local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-374", "filename": "2000.txt", "content": "CVE-2000-0374(PUBLISHED):The default configuration of kdm in Caldera and Mandrake Linux, and possibly other distributions, allows XDMCP connections from any host, which allows remote attackers to obtain sensitive information or bypass additional access restrictions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-375", "filename": "2000.txt", "content": "CVE-2000-0375(PUBLISHED):The kernel in FreeBSD 3.2 follows symbolic links when it creates core dump files, which allows local attackers to modify arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-376", "filename": "2000.txt", "content": "CVE-2000-0376(PUBLISHED):Buffer overflow in the HTTP proxy server for the i-drive Filo software allows remote attackers to execute arbitrary commands via a long HTTP GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-377", "filename": "2000.txt", "content": "CVE-2000-0377(PUBLISHED):The Remote Registry server in Windows NT 4.0 allows local authenticated users to cause a denial of service via a malformed request, which causes the winlogon process to fail, aka the \"Remote Registry Access Authentication\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-378", "filename": "2000.txt", "content": "CVE-2000-0378(PUBLISHED):The pam_console PAM module in Linux systems performs a chown on various devices upon a user login, but an open file descriptor for those devices can be maintained after the user logs out, which allows that user to sniff activity on these devices when subsequent users log in.", "metadata": {"filename": "2000.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-379", "filename": "2000.txt", "content": "CVE-2000-0379(PUBLISHED):The Netopia R9100 router does not prevent authenticated users from modifying SNMP tables, even if the administrator has configured it to do so.", "metadata": {"filename": "2000.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-380", "filename": "2000.txt", "content": "CVE-2000-0380(PUBLISHED):The IOS HTTP service in Cisco routers and switches running IOS 11.1 through 12.1 allows remote attackers to cause a denial of service by requesting a URL that contains a %% string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-381", "filename": "2000.txt", "content": "CVE-2000-0381(PUBLISHED):The Gossamer Threads DBMan db.cgi CGI script allows remote attackers to view environmental variables and setup information by referencing a non-existing database in the db parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-382", "filename": "2000.txt", "content": "CVE-2000-0382(PUBLISHED):ColdFusion ClusterCATS appends stale query string arguments to a URL during HTML redirection, which may provide sensitive information to the redirected site.", "metadata": {"filename": "2000.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-383", "filename": "2000.txt", "content": "CVE-2000-0383(PUBLISHED):The file transfer component of AOL Instant Messenger (AIM) reveals the physical path of the transferred file to the remote recipient.", "metadata": {"filename": "2000.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-384", "filename": "2000.txt", "content": "CVE-2000-0384(PUBLISHED):NetStructure 7110 and 7180 have undocumented accounts (servnow, root, and wizard) whose passwords are easily guessable from the NetStructure's MAC address, which could allow remote attackers to gain root access.", "metadata": {"filename": "2000.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-385", "filename": "2000.txt", "content": "CVE-2000-0385(PUBLISHED):FileMaker Pro 5 Web Companion allows remote attackers to bypass Field-Level database security restrictions via the XML publishing or email capabilities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-386", "filename": "2000.txt", "content": "CVE-2000-0386(PUBLISHED):FileMaker Pro 5 Web Companion allows remote attackers to send anonymous or forged email.", "metadata": {"filename": "2000.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-387", "filename": "2000.txt", "content": "CVE-2000-0387(PUBLISHED):The makelev program in the golddig game from the FreeBSD ports collection allows local users to overwrite arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-388", "filename": "2000.txt", "content": "CVE-2000-0388(PUBLISHED):Buffer overflow in FreeBSD libmytinfo library allows local users to execute commands via a long TERMCAP environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-389", "filename": "2000.txt", "content": "CVE-2000-0389(PUBLISHED):Buffer overflow in krb_rd_req function in Kerberos 4 and 5 allows remote attackers to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-390", "filename": "2000.txt", "content": "CVE-2000-0390(PUBLISHED):Buffer overflow in krb425_conv_principal function in Kerberos 5 allows remote attackers to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-391", "filename": "2000.txt", "content": "CVE-2000-0391(PUBLISHED):Buffer overflow in krshd in Kerberos 5 allows remote attackers to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-392", "filename": "2000.txt", "content": "CVE-2000-0392(PUBLISHED):Buffer overflow in ksu in Kerberos 5 allows local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-393", "filename": "2000.txt", "content": "CVE-2000-0393(PUBLISHED):The KDE kscd program does not drop privileges when executing a program specified in a user's SHELL environmental variable, which allows the user to gain privileges by specifying an alternate program to execute.", "metadata": {"filename": "2000.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-394", "filename": "2000.txt", "content": "CVE-2000-0394(PUBLISHED):NetProwler 3.0 allows remote attackers to cause a denial of service by sending malformed IP packets that trigger NetProwler's Man-in-the-Middle signature.", "metadata": {"filename": "2000.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-395", "filename": "2000.txt", "content": "CVE-2000-0395(PUBLISHED):Buffer overflow in CProxy 3.3 allows remote users to cause a denial of service via a long HTTP request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-396", "filename": "2000.txt", "content": "CVE-2000-0396(PUBLISHED):The add.exe program in the Carello shopping cart software allows remote attackers to duplicate files on the server, which could allow the attacker to read source code for web scripts such as .ASP files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-397", "filename": "2000.txt", "content": "CVE-2000-0397(PUBLISHED):The EMURL web-based email account software encodes predictable identifiers in user session URLs, which allows a remote attacker to access a user's email account.", "metadata": {"filename": "2000.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-398", "filename": "2000.txt", "content": "CVE-2000-0398(PUBLISHED):Buffer overflow in wconsole.dll in Rockliffe MailSite Management Agent allows remote attackers to execute arbitrary commands via a long query_string parameter in the HTTP GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-399", "filename": "2000.txt", "content": "CVE-2000-0399(PUBLISHED):Buffer overflow in MDaemon POP server allows remote attackers to cause a denial of service via a long user name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-400", "filename": "2000.txt", "content": "CVE-2000-0400(PUBLISHED):The Microsoft Active Movie ActiveX Control in Internet Explorer 5 does not restrict which file types can be downloaded, which allows an attacker to download any type of file to a user's system by encoding it within an email message or news post.", "metadata": {"filename": "2000.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-401", "filename": "2000.txt", "content": "CVE-2000-0401(PUBLISHED):Buffer overflows in redirect.exe and changepw.exe in PDGSoft shopping cart allow remote attackers to execute arbitrary commands via a long query string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-402", "filename": "2000.txt", "content": "CVE-2000-0402(PUBLISHED):The Mixed Mode authentication capability in Microsoft SQL Server 7.0 stores the System Administrator (sa) account in plaintext in a log file which is readable by any user, aka the \"SQL Server 7.0 Service Pack Password\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-403", "filename": "2000.txt", "content": "CVE-2000-0403(PUBLISHED):The CIFS Computer Browser service on Windows NT 4.0 allows a remote attacker to cause a denial of service by sending a large number of host announcement requests to the master browse tables, aka the \"HostAnnouncement Flooding\" or \"HostAnnouncement Frame\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-404", "filename": "2000.txt", "content": "CVE-2000-0404(PUBLISHED):The CIFS Computer Browser service allows remote attackers to cause a denial of service by sending a ResetBrowser frame to the Master Browser, aka the \"ResetBrowser Frame\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-405", "filename": "2000.txt", "content": "CVE-2000-0405(PUBLISHED):Buffer overflow in L0pht AntiSniff allows remote attackers to execute arbitrary commands via a malformed DNS response packet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-406", "filename": "2000.txt", "content": "CVE-2000-0406(PUBLISHED):Netscape Communicator before version 4.73 and Navigator 4.07 do not properly validate SSL certificates, which allows remote attackers to steal information by redirecting traffic from a legitimate web server to their own malicious server, aka the \"Acros-Suencksen SSL\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-407", "filename": "2000.txt", "content": "CVE-2000-0407(PUBLISHED):Buffer overflow in Solaris netpr program allows local users to execute arbitrary commands via a long -p option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-408", "filename": "2000.txt", "content": "CVE-2000-0408(PUBLISHED):IIS 4.05 and 5.0 allow remote attackers to cause a denial of service via a long, complex URL that appears to contain a large number of file extensions, aka the \"Malformed Extension Data in URL\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-409", "filename": "2000.txt", "content": "CVE-2000-0409(PUBLISHED):Netscape 4.73 and earlier follows symlinks when it imports a new certificate, which allows local users to overwrite files of the user importing the certificate.", "metadata": {"filename": "2000.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-410", "filename": "2000.txt", "content": "CVE-2000-0410(PUBLISHED):ColdFusion Server 4.5.1 allows remote attackers to cause a denial of service by making repeated requests to a CFCACHE tagged cache file that is not stored in memory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-411", "filename": "2000.txt", "content": "CVE-2000-0411(PUBLISHED):Matt Wright's FormMail CGI script allows remote attackers to obtain environmental variables via the env_report parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-412", "filename": "2000.txt", "content": "CVE-2000-0412(PUBLISHED):The gnapster and knapster clients for Napster do not properly restrict access only to MP3 files, which allows remote attackers to read arbitrary files from the client by specifying the full pathname for the file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-413", "filename": "2000.txt", "content": "CVE-2000-0413(PUBLISHED):The shtml.exe program in the FrontPage extensions package of IIS 4.0 and 5.0 allows remote attackers to determine the physical path of HTML, HTM, ASP, and SHTML files by requesting a file that does not exist, which generates an error message that reveals the path.", "metadata": {"filename": "2000.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-414", "filename": "2000.txt", "content": "CVE-2000-0414(PUBLISHED):Vulnerability in shutdown command for HP-UX 11.X and 10.X allows allows local users to gain privileges via malformed input variables.", "metadata": {"filename": "2000.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-415", "filename": "2000.txt", "content": "CVE-2000-0415(PUBLISHED):Buffer overflow in Outlook Express 4.x allows attackers to cause a denial of service via a mail or news message that has a .jpg or .bmp attachment with a long file name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-416", "filename": "2000.txt", "content": "CVE-2000-0416(PUBLISHED):NTMail 5.x allows network users to bypass the NTMail proxy restrictions by redirecting their requests to NTMail's web configuration server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-417", "filename": "2000.txt", "content": "CVE-2000-0417(PUBLISHED):The HTTP administration interface to the Cayman 3220-H DSL router allows remote attackers to cause a denial of service via a long username or password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-418", "filename": "2000.txt", "content": "CVE-2000-0418(PUBLISHED):The Cayman 3220-H DSL router allows remote attackers to cause a denial of service via oversized ICMP echo (ping) requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-419", "filename": "2000.txt", "content": "CVE-2000-0419(PUBLISHED):The Office 2000 UA ActiveX Control is marked as \"safe for scripting,\" which allows remote attackers to conduct unauthorized activities via the \"Show Me\" function in Office Help, aka the \"Office 2000 UA Control\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-420", "filename": "2000.txt", "content": "CVE-2000-0420(PUBLISHED):The default configuration of SYSKEY in Windows 2000 stores the startup key in the registry, which could allow an attacker tor ecover it and use it to decrypt Encrypted File System (EFS) data.", "metadata": {"filename": "2000.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-421", "filename": "2000.txt", "content": "CVE-2000-0421(PUBLISHED):The process_bug.cgi script in Bugzilla allows remote attackers to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-422", "filename": "2000.txt", "content": "CVE-2000-0422(PUBLISHED):Buffer overflow in Netwin DMailWeb CGI program allows remote attackers to execute arbitrary commands via a long utoken parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-423", "filename": "2000.txt", "content": "CVE-2000-0423(PUBLISHED):Buffer overflow in Netwin DNEWSWEB CGI program allows remote attackers to execute arbitrary commands via long parameters such as group, cmd, and utag.", "metadata": {"filename": "2000.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-424", "filename": "2000.txt", "content": "CVE-2000-0424(PUBLISHED):The CGI counter 4.0.7 by George Burgyan allows remote attackers to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-425", "filename": "2000.txt", "content": "CVE-2000-0425(PUBLISHED):Buffer overflow in the Web Archives component of L-Soft LISTSERV 1.8 allows remote attackers to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-426", "filename": "2000.txt", "content": "CVE-2000-0426(PUBLISHED):UltraBoard 1.6 and other versions allow remote attackers to cause a denial of service by referencing UltraBoard in the Session parameter, which causes UltraBoard to fork copies of itself.", "metadata": {"filename": "2000.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-427", "filename": "2000.txt", "content": "CVE-2000-0427(PUBLISHED):The Aladdin Knowledge Systems eToken device allows attackers with physical access to the device to obtain sensitive information without knowing the PIN of the owner by resetting the PIN in the EEPROM.", "metadata": {"filename": "2000.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-428", "filename": "2000.txt", "content": "CVE-2000-0428(PUBLISHED):Buffer overflow in the SMTP gateway for InterScan Virus Wall 3.32 and earlier allows a remote attacker to execute arbitrary commands via a long filename for a uuencoded attachment.", "metadata": {"filename": "2000.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-429", "filename": "2000.txt", "content": "CVE-2000-0429(PUBLISHED):A backdoor password in Cart32 3.0 and earlier allows remote attackers to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-430", "filename": "2000.txt", "content": "CVE-2000-0430(PUBLISHED):Cart32 allows remote attackers to access sensitive debugging information by appending /expdate to the URL request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-430", "line_number": 430, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-431", "filename": "2000.txt", "content": "CVE-2000-0431(PUBLISHED):Cobalt RaQ2 and RaQ3 does not properly set the access permissions and ownership for files that are uploaded via FrontPage, which allows attackers to bypass cgiwrap and modify files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-432", "filename": "2000.txt", "content": "CVE-2000-0432(PUBLISHED):The calender.pl and the calendar_admin.pl calendar scripts by Matt Kruse allow remote attackers to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-433", "filename": "2000.txt", "content": "CVE-2000-0433(PUBLISHED):The SuSE aaa_base package installs some system accounts with home directories set to /tmp, which allows local users to gain privileges to those accounts by creating standard user startup scripts such as profiles.", "metadata": {"filename": "2000.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-434", "filename": "2000.txt", "content": "CVE-2000-0434(PUBLISHED):The administrative password for the Allmanage web site administration software is stored in plaintext in a file which could be accessed by remote attackers.", "metadata": {"filename": "2000.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-435", "filename": "2000.txt", "content": "CVE-2000-0435(PUBLISHED):The allmanageup.pl file upload CGI script in the Allmanage Website administration software 2.6 can be called directly by remote attackers, which allows them to modify user accounts or web pages.", "metadata": {"filename": "2000.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-436", "filename": "2000.txt", "content": "CVE-2000-0436(PUBLISHED):MetaProducts Offline Explorer 1.2 and earlier allows remote attackers to access arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-437", "filename": "2000.txt", "content": "CVE-2000-0437(PUBLISHED):Buffer overflow in the CyberPatrol daemon \"cyberdaemon\" used in gauntlet and WebShield allows remote attackers to cause a denial of service or execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-438", "filename": "2000.txt", "content": "CVE-2000-0438(PUBLISHED):Buffer overflow in fdmount on Linux systems allows local users in the \"floppy\" group to execute arbitrary commands via a long mountpoint parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-439", "filename": "2000.txt", "content": "CVE-2000-0439(PUBLISHED):Internet Explorer 4.0 and 5.0 allows a malicious web site to obtain client cookies from another domain by including that domain name and escaped characters in a URL, aka the \"Unauthorized Cookie Access\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-440", "filename": "2000.txt", "content": "CVE-2000-0440(PUBLISHED):NetBSD 1.4.2 and earlier allows remote attackers to cause a denial of service by sending a packet with an unaligned IP timestamp option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-441", "filename": "2000.txt", "content": "CVE-2000-0441(PUBLISHED):Vulnerability in AIX 3.2.x and 4.x allows local users to gain write access to files on locally or remotely mounted AIX filesystems.", "metadata": {"filename": "2000.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-442", "filename": "2000.txt", "content": "CVE-2000-0442(PUBLISHED):Qpopper 2.53 and earlier allows local users to gain privileges via a formatting string in the From: header, which is processed by the euidl command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-443", "filename": "2000.txt", "content": "CVE-2000-0443(PUBLISHED):The web interface server in HP Web JetAdmin 5.6 allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-444", "filename": "2000.txt", "content": "CVE-2000-0444(PUBLISHED):HP Web JetAdmin 6.0 allows remote attackers to cause a denial of service via a malformed URL to port 8000.", "metadata": {"filename": "2000.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-445", "filename": "2000.txt", "content": "CVE-2000-0445(PUBLISHED):The pgpk command in PGP 5.x on Unix systems uses an insufficiently random data source for non-interactive key pair generation, which may produce predictable keys.", "metadata": {"filename": "2000.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-446", "filename": "2000.txt", "content": "CVE-2000-0446(PUBLISHED):Buffer overflow in MDBMS database server allows remote attackers to execute arbitrary commands via a long string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-447", "filename": "2000.txt", "content": "CVE-2000-0447(PUBLISHED):Buffer overflow in WebShield SMTP 4.5.44 allows remote attackers to execute arbitrary commands via a long configuration parameter to the WebShield remote management service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-448", "filename": "2000.txt", "content": "CVE-2000-0448(PUBLISHED):The WebShield SMTP Management Tool version 4.5.44 does not properly restrict access to the management port when an IP address does not resolve to a hostname, which allows remote attackers to access the configuration via the GET_CONFIG command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-449", "filename": "2000.txt", "content": "CVE-2000-0449(PUBLISHED):Omnis Studio 2.4 uses weak encryption (trivial encoding) for encrypting database fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-450", "filename": "2000.txt", "content": "CVE-2000-0450(PUBLISHED):Vulnerability in bbd server in Big Brother System and Network Monitor allows an attacker to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-451", "filename": "2000.txt", "content": "CVE-2000-0451(PUBLISHED):The Intel express 8100 ISDN router allows remote attackers to cause a denial of service via oversized or fragmented ICMP packets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-452", "filename": "2000.txt", "content": "CVE-2000-0452(PUBLISHED):Buffer overflow in the ESMTP service of Lotus Domino Server 5.0.1 allows remote attackers to cause a denial of service via a long MAIL FROM command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-453", "filename": "2000.txt", "content": "CVE-2000-0453(PUBLISHED):XFree86 3.3.x and 4.0 allows a user to cause a denial of service via a negative counter value in a malformed TCP packet that is sent to port 6000.", "metadata": {"filename": "2000.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-454", "filename": "2000.txt", "content": "CVE-2000-0454(PUBLISHED):Buffer overflow in Linux cdrecord allows local users to gain privileges via the dev parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-455", "filename": "2000.txt", "content": "CVE-2000-0455(PUBLISHED):Buffer overflow in xlockmore xlock program version 4.16 and earlier allows local users to read sensitive data from memory via a long -mode option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-456", "filename": "2000.txt", "content": "CVE-2000-0456(PUBLISHED):NetBSD 1.4.2 and earlier allows local users to cause a denial of service by repeatedly running certain system calls in the kernel which do not yield the CPU, aka \"cpu-hog\".", "metadata": {"filename": "2000.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-457", "filename": "2000.txt", "content": "CVE-2000-0457(PUBLISHED):ISM.DLL in IIS 4.0 and 5.0 allows remote attackers to read file contents by requesting the file and appending a large number of encoded spaces (%20) and terminated with a .htr extension, aka the \".HTR File Fragment Reading\" or \"File Fragment Reading via .HTR\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-458", "filename": "2000.txt", "content": "CVE-2000-0458(PUBLISHED):The MSWordView application in IMP creates world-readable files in the /tmp directory, which allows other local users to read potentially sensitive information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-459", "filename": "2000.txt", "content": "CVE-2000-0459(PUBLISHED):IMP does not remove files properly if the MSWordView application quits, which allows local users to cause a denial of service by filling up the disk space by requesting a large number of documents and prematurely stopping the request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-460", "filename": "2000.txt", "content": "CVE-2000-0460(PUBLISHED):Buffer overflow in KDE kdesud on Linux allows local uses to gain privileges via a long DISPLAY environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-461", "filename": "2000.txt", "content": "CVE-2000-0461(PUBLISHED):The undocumented semconfig system call in BSD freezes the state of semaphores, which allows local users to cause a denial of service of the semaphore system by using the semconfig call.", "metadata": {"filename": "2000.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-462", "filename": "2000.txt", "content": "CVE-2000-0462(PUBLISHED):ftpd in NetBSD 1.4.2 does not properly parse entries in /etc/ftpchroot and does not chroot the specified users, which allows those users to access other files outside of their home directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-463", "filename": "2000.txt", "content": "CVE-2000-0463(PUBLISHED):BeOS 5.0 allows remote attackers to cause a denial of service via fragmented TCP packets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-464", "filename": "2000.txt", "content": "CVE-2000-0464(PUBLISHED):Internet Explorer 4.x and 5.x allows remote attackers to execute arbitrary commands via a buffer overflow in the ActiveX parameter parsing capability, aka the \"Malformed Component Attribute\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-465", "filename": "2000.txt", "content": "CVE-2000-0465(PUBLISHED):Internet Explorer 4.x and 5.x does not properly verify the domain of a  frame within a browser window, which allows a remote attacker to read client files via the frame, aka the \"Frame Domain Verification\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-466", "filename": "2000.txt", "content": "CVE-2000-0466(PUBLISHED):AIX cdmount allows local users to gain root privileges via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-467", "filename": "2000.txt", "content": "CVE-2000-0467(PUBLISHED):Buffer overflow in Linux splitvt 1.6.3 and earlier allows local users to gain root privileges via a long password in the screen locking function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-468", "filename": "2000.txt", "content": "CVE-2000-0468(PUBLISHED):man in HP-UX 10.20 and 11 allows local attackers to overwrite files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-469", "filename": "2000.txt", "content": "CVE-2000-0469(PUBLISHED):Selena Sol WebBanner 4.0 allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-470", "filename": "2000.txt", "content": "CVE-2000-0470(PUBLISHED):Allegro RomPager HTTP server allows remote attackers to cause a denial of service via a malformed authentication request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-471", "filename": "2000.txt", "content": "CVE-2000-0471(PUBLISHED):Buffer overflow in ufsrestore in Solaris 8 and earlier allows local users to gain root privileges via a long pathname.", "metadata": {"filename": "2000.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-472", "filename": "2000.txt", "content": "CVE-2000-0472(PUBLISHED):Buffer overflow in innd 2.2.2 allows remote attackers to execute arbitrary commands via a cancel request containing a long message ID.", "metadata": {"filename": "2000.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-473", "filename": "2000.txt", "content": "CVE-2000-0473(PUBLISHED):Buffer overflow in AnalogX SimpleServer 1.05 allows a remote attacker to cause a denial of service via a long GET request for a program in the cgi-bin directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-474", "filename": "2000.txt", "content": "CVE-2000-0474(PUBLISHED):Real Networks RealServer 7.x allows remote attackers to cause a denial of service via a malformed request for a page in the viewsource directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-475", "filename": "2000.txt", "content": "CVE-2000-0475(PUBLISHED):Windows 2000 allows a local user process to access another user's desktop within the same windows station, aka the \"Desktop Separation\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-476", "filename": "2000.txt", "content": "CVE-2000-0476(PUBLISHED):xterm, Eterm, and rxvt allow an attacker to cause a denial of service by embedding certain escape characters which force the window to be resized.", "metadata": {"filename": "2000.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-477", "filename": "2000.txt", "content": "CVE-2000-0477(PUBLISHED):Buffer overflow in Norton Antivirus for Exchange (NavExchange) allows remote attackers to cause a denial of service via a .zip file that contains long file names.", "metadata": {"filename": "2000.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-478", "filename": "2000.txt", "content": "CVE-2000-0478(PUBLISHED):In some cases, Norton Antivirus for Exchange (NavExchange) enters a \"fail-open\" state which allows viruses to pass through the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-479", "filename": "2000.txt", "content": "CVE-2000-0479(PUBLISHED):Dragon FTP server allows remote attackers to cause a denial of service via a long USER command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-480", "filename": "2000.txt", "content": "CVE-2000-0480(PUBLISHED):Dragon telnet server allows remote attackers to cause a denial of service via a long username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-481", "filename": "2000.txt", "content": "CVE-2000-0481(PUBLISHED):Buffer overflow in KDE Kmail allows a remote attacker to cause a denial of service via an attachment with a long file name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-482", "filename": "2000.txt", "content": "CVE-2000-0482(PUBLISHED):Check Point Firewall-1 allows remote attackers to cause a denial of service by sending a large number of malformed fragmented IP packets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-483", "filename": "2000.txt", "content": "CVE-2000-0483(PUBLISHED):The DocumentTemplate package in Zope 2.2 and earlier allows a remote attacker to modify DTMLDocuments or DTMLMethods without authorization.", "metadata": {"filename": "2000.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-484", "filename": "2000.txt", "content": "CVE-2000-0484(PUBLISHED):Small HTTP Server ver 3.06 contains a memory corruption bug causing a memory overflow. The overflowed buffer crashes into a Structured Exception Handler resulting in a Denial of Service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-485", "filename": "2000.txt", "content": "CVE-2000-0485(PUBLISHED):Microsoft SQL Server allows local users to obtain database passwords via the Data Transformation Service (DTS) package Properties dialog, aka the \"DTS Password\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-486", "filename": "2000.txt", "content": "CVE-2000-0486(PUBLISHED):Buffer overflow in Cisco TACACS+ tac_plus server allows remote attackers to cause a denial of service via a malformed packet with a long length field.", "metadata": {"filename": "2000.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-487", "filename": "2000.txt", "content": "CVE-2000-0487(PUBLISHED):The Protected Store in Windows 2000 does not properly select the strongest encryption when available, which causes it to use a default of 40-bit encryption instead of 56-bit DES encryption, aka the \"Protected Store Key Length\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-488", "filename": "2000.txt", "content": "CVE-2000-0488(PUBLISHED):Buffer overflow in ITHouse mail server 1.04 allows remote attackers to execute arbitrary commands via a long RCPT TO mail command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-489", "filename": "2000.txt", "content": "CVE-2000-0489(PUBLISHED):FreeBSD, NetBSD, and OpenBSD allow an attacker to cause a denial of service by creating a large number of socket pairs using the socketpair function, setting a large buffer size via setsockopt, then writing large buffers.", "metadata": {"filename": "2000.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-490", "filename": "2000.txt", "content": "CVE-2000-0490(PUBLISHED):Buffer overflow in the NetWin DSMTP 2.7q in the NetWin dmail package allows remote attackers to execute arbitrary commands via a long ETRN request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-491", "filename": "2000.txt", "content": "CVE-2000-0491(PUBLISHED):Buffer overflow in the XDMCP parsing code of GNOME gdm, KDE kdm, and wdm allows remote attackers to execute arbitrary commands or cause a denial of service via a long FORWARD_QUERY request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-492", "filename": "2000.txt", "content": "CVE-2000-0492(PUBLISHED):PassWD 1.2 uses weak encryption (trivial encoding) to store passwords, which allows an attacker who can read the password file to easliy decrypt the passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-493", "filename": "2000.txt", "content": "CVE-2000-0493(PUBLISHED):Buffer overflow in Simple Network Time Sync (SMTS) daemon allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-494", "filename": "2000.txt", "content": "CVE-2000-0494(PUBLISHED):Veritas Volume Manager creates a world writable .server_pids file, which allows local users to add arbitrary commands into the file, which is then executed by the vmsa_server script.", "metadata": {"filename": "2000.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-495", "filename": "2000.txt", "content": "CVE-2000-0495(PUBLISHED):Microsoft Windows Media Encoder allows remote attackers to cause a denial of service via a malformed request, aka the \"Malformed Windows Media Encoder Request\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-496", "filename": "2000.txt", "content": "CVE-2000-0497(PUBLISHED):IBM WebSphere server 3.0.2 allows a remote attacker to view source code of a JSP program by requesting a URL which provides the JSP extension in upper case.", "metadata": {"filename": "2000.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-497", "filename": "2000.txt", "content": "CVE-2000-0498(PUBLISHED):Unify eWave ServletExec allows a remote attacker to view source code of a JSP program by requesting a URL which provides the JSP extension in upper case.", "metadata": {"filename": "2000.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-498", "filename": "2000.txt", "content": "CVE-2000-0499(PUBLISHED):The default configuration of BEA WebLogic 3.1.8 through 4.5.1 allows a remote attacker to view source code of a JSP program by requesting a URL which provides the JSP extension in upper case.", "metadata": {"filename": "2000.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-499", "filename": "2000.txt", "content": "CVE-2000-0500(PUBLISHED):The default configuration of BEA WebLogic 5.1.0 allows a remote attacker to view source code of programs by requesting a URL beginning with /file/, which causes the default servlet to display the file without further processing.", "metadata": {"filename": "2000.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-500", "filename": "2000.txt", "content": "CVE-2000-0501(PUBLISHED):Race condition in MDaemon 2.8.5.0 POP server allows local users to cause a denial of service by entering a UIDL command and quickly exiting the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-501", "filename": "2000.txt", "content": "CVE-2000-0502(PUBLISHED):Mcafee VirusScan 4.03 does not properly restrict access to the alert text file before it is sent to the Central Alert Server, which allows local users to modify alerts in an arbitrary fashion.", "metadata": {"filename": "2000.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-502", "filename": "2000.txt", "content": "CVE-2000-0503(PUBLISHED):The IFRAME of the WebBrowser control in Internet Explorer 5.01 allows a remote attacker to violate the cross frame security policy via the NavigateComplete2 event.", "metadata": {"filename": "2000.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-503", "filename": "2000.txt", "content": "CVE-2000-0504(PUBLISHED):libICE in XFree86 allows remote attackers to cause a denial of service by specifying a large value which is not properly checked by the SKIP_STRING macro.", "metadata": {"filename": "2000.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-504", "filename": "2000.txt", "content": "CVE-2000-0505(PUBLISHED):The Apache 1.3.x HTTP server for Windows platforms allows remote attackers to list directory contents by requesting a URL containing a large number of / characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-505", "filename": "2000.txt", "content": "CVE-2000-0506(PUBLISHED):The \"capabilities\" feature in Linux before 2.2.16 allows local users to cause a denial of service or gain privileges by setting the capabilities to prevent a setuid program from dropping privileges, aka the \"Linux kernel setuid/setcap vulnerability.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-506", "filename": "2000.txt", "content": "CVE-2000-0507(PUBLISHED):Imate Webmail Server 2.5 allows remote attackers to cause a denial of service via a long HELO command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-507", "filename": "2000.txt", "content": "CVE-2000-0508(PUBLISHED):rpc.lockd in Red Hat Linux 6.1 and 6.2 allows remote attackers to cause a denial of service via a malformed request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-508", "filename": "2000.txt", "content": "CVE-2000-0509(PUBLISHED):Buffer overflows in the finger and whois demonstration scripts in Sambar Server 4.3 allow remote attackers to execute arbitrary commands via a long hostname.", "metadata": {"filename": "2000.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-509", "filename": "2000.txt", "content": "CVE-2000-0510(PUBLISHED):CUPS (Common Unix Printing System) 1.04 and earlier allows remote attackers to cause a denial of service via a malformed IPP request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-510", "filename": "2000.txt", "content": "CVE-2000-0511(PUBLISHED):CUPS (Common Unix Printing System) 1.04 and earlier allows remote attackers to cause a denial of service via a CGI POST request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-511", "filename": "2000.txt", "content": "CVE-2000-0512(PUBLISHED):CUPS (Common Unix Printing System) 1.04 and earlier does not properly delete request files, which allows a remote attacker to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-512", "filename": "2000.txt", "content": "CVE-2000-0513(PUBLISHED):CUPS (Common Unix Printing System) 1.04 and earlier allows remote attackers to cause a denial of service by authenticating with a user name that does not exist or does not have a shadow password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-513", "filename": "2000.txt", "content": "CVE-2000-0514(PUBLISHED):GSSFTP FTP daemon in Kerberos 5 1.1.x does not properly restrict access to some FTP commands, which allows remote attackers to cause a denial of service, and local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-514", "filename": "2000.txt", "content": "CVE-2000-0515(PUBLISHED):The snmpd.conf configuration file for the SNMP daemon (snmpd) in HP-UX 11.0 is world writable, which allows local users to modify SNMP configuration or gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-515", "filename": "2000.txt", "content": "CVE-2000-0516(PUBLISHED):When configured to store configuration information in an LDAP directory, Shiva Access Manager 5.0.0 stores the root DN (Distinguished Name) name and password in cleartext in a file that is world readable, which allows local users to compromise the LDAP server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-516", "filename": "2000.txt", "content": "CVE-2000-0517(PUBLISHED):Netscape 4.73 and earlier does not properly warn users about a potentially invalid certificate if the user has previously accepted the certificate for a different web site, which could allow remote attackers to spoof a legitimate web site by compromising that site's DNS information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-517", "filename": "2000.txt", "content": "CVE-2000-0518(PUBLISHED):Internet Explorer 4.x and 5.x does not properly verify all contents of an SSL certificate if a connection is made to the server via an image or a frame, aka one of two different \"SSL Certificate Validation\" vulnerabilities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-518", "filename": "2000.txt", "content": "CVE-2000-0519(PUBLISHED):Internet Explorer 4.x and 5.x does not properly re-validate an SSL certificate if the user establishes a new SSL session with the same server during the same Internet Explorer session, aka one of two different \"SSL Certificate Validation\" vulnerabilities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-519", "filename": "2000.txt", "content": "CVE-2000-0520(PUBLISHED):Buffer overflow in restore program 0.4b17 and earlier in dump package allows local users to execute arbitrary commands via a long tape name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-520", "filename": "2000.txt", "content": "CVE-2000-0521(PUBLISHED):Savant web server allows remote attackers to read source code of CGI scripts via a GET request that does not include the HTTP version number.", "metadata": {"filename": "2000.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-521", "filename": "2000.txt", "content": "CVE-2000-0522(PUBLISHED):RSA ACE/Server allows remote attackers to cause a denial of service by flooding the server's authentication request port with UDP packets, which causes the server to crash.", "metadata": {"filename": "2000.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-522", "filename": "2000.txt", "content": "CVE-2000-0523(PUBLISHED):Buffer overflow in the logging feature of EServ 2.9.2 and earlier allows an attacker to execute arbitrary commands via a long MKD command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-523", "filename": "2000.txt", "content": "CVE-2000-0524(PUBLISHED):Microsoft Outlook and Outlook Express allow remote attackers to cause a denial of service by sending email messages with blank fields such as BCC, Reply-To, Return-Path, or From.", "metadata": {"filename": "2000.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-524", "filename": "2000.txt", "content": "CVE-2000-0525(PUBLISHED):OpenSSH does not properly drop privileges when the UseLogin option is enabled, which allows local users to execute arbitrary commands by providing the command to the ssh daemon.", "metadata": {"filename": "2000.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-525", "filename": "2000.txt", "content": "CVE-2000-0526(PUBLISHED):mailview.cgi CGI program in MailStudio 2000 2.0 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-526", "filename": "2000.txt", "content": "CVE-2000-0527(PUBLISHED):userreg.cgi CGI program in MailStudio 2000 2.0 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-527", "filename": "2000.txt", "content": "CVE-2000-0528(PUBLISHED):Net Tools PKI Server does not properly restrict access to remote attackers when the XUDA template files do not contain absolute pathnames for other files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-528", "filename": "2000.txt", "content": "CVE-2000-0529(PUBLISHED):Net Tools PKI Server allows remote attackers to cause a denial of service via a long HTTP request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-529", "filename": "2000.txt", "content": "CVE-2000-0530(PUBLISHED):The KApplication class in the KDE 1.1.2 configuration file management capability allows local users to overwrite arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-530", "filename": "2000.txt", "content": "CVE-2000-0531(PUBLISHED):Linux gpm program allows local users to cause a denial of service by flooding the /dev/gpmctl device with STREAM sockets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-531", "filename": "2000.txt", "content": "CVE-2000-0532(PUBLISHED):A FreeBSD patch for SSH on 2000-01-14 configures ssh to listen on port 722 as well as port 22, which might allow remote attackers to access SSH through port 722 even if port 22 is otherwise filtered.", "metadata": {"filename": "2000.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-532", "filename": "2000.txt", "content": "CVE-2000-0533(PUBLISHED):Vulnerability in cvconnect in SGI IRIX WorkShop allows local users to overwrite arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-533", "filename": "2000.txt", "content": "CVE-2000-0534(PUBLISHED):The apsfilter software in the FreeBSD ports package does not properly read user filter configurations, which allows local users to execute commands as the lpd user.", "metadata": {"filename": "2000.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-534", "filename": "2000.txt", "content": "CVE-2000-0535(PUBLISHED):OpenSSL 0.9.4 and OpenSSH for FreeBSD do not properly check for the existence of the /dev/random or /dev/urandom devices, which are absent on FreeBSD Alpha systems, which causes them to produce weak keys which may be more easily broken.", "metadata": {"filename": "2000.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-535", "filename": "2000.txt", "content": "CVE-2000-0536(PUBLISHED):xinetd 2.1.8.x does not properly restrict connections if hostnames are used for access control and the connecting host does not have a reverse DNS entry.", "metadata": {"filename": "2000.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-536", "filename": "2000.txt", "content": "CVE-2000-0537(PUBLISHED):BRU backup software allows local users to append data to arbitrary files by specifying an alternate configuration file with the BRUEXECLOG environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-537", "filename": "2000.txt", "content": "CVE-2000-0538(PUBLISHED):ColdFusion Administrator for ColdFusion 4.5.1 and earlier allows remote attackers to cause a denial of service via a long login password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-538", "filename": "2000.txt", "content": "CVE-2000-0539(PUBLISHED):Servlet examples in Allaire JRun 2.3.x allow remote attackers to obtain sensitive information, e.g. listing HttpSession ID's via the SessionServlet servlet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-539", "filename": "2000.txt", "content": "CVE-2000-0540(PUBLISHED):JSP sample files in Allaire JRun 2.3.x allow remote attackers to access arbitrary files (e.g. via viewsource.jsp) or obtain configuration information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-540", "filename": "2000.txt", "content": "CVE-2000-0541(PUBLISHED):The Panda Antivirus console on port 2001 allows local users to execute arbitrary commands without authentication via the CMD command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-541", "filename": "2000.txt", "content": "CVE-2000-0542(PUBLISHED):Tigris remote access server before ********* does not properly record Radius accounting information when a user fails the initial login authentication but subsequently succeeds.", "metadata": {"filename": "2000.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-542", "filename": "2000.txt", "content": "CVE-2000-0543(PUBLISHED):The command port for PGP Certificate Server 2.5.0 and 2.5.1 allows remote attackers to cause a denial of service if their hostname does not have a reverse DNS entry and they connect to port 4000.", "metadata": {"filename": "2000.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-543", "filename": "2000.txt", "content": "CVE-2000-0544(PUBLISHED):Windows NT and Windows 2000 hosts allow a remote attacker to cause a denial of service via malformed DCE/RPC SMBwriteX requests that contain an invalid data length.", "metadata": {"filename": "2000.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-544", "filename": "2000.txt", "content": "CVE-2000-0545(PUBLISHED):Buffer overflow in mailx mail command (aka Mail) on Linux systems allows local users to gain privileges via a long -c (carbon copy) parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-545", "filename": "2000.txt", "content": "CVE-2000-0546(PUBLISHED):Buffer overflow in Kerberos 4 KDC program allows remote attackers to cause a denial of service via the lastrealm variable in the set_tgtkey function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-546", "filename": "2000.txt", "content": "CVE-2000-0547(PUBLISHED):Buffer overflow in Kerberos 4 KDC program allows remote attackers to cause a denial of service via the localrealm variable in the process_v4 function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-547", "filename": "2000.txt", "content": "CVE-2000-0548(PUBLISHED):Buffer overflow in Kerberos 4 KDC program allows remote attackers to cause a denial of service via the e_msg variable in the kerb_err_reply function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-548", "filename": "2000.txt", "content": "CVE-2000-0549(PUBLISHED):Kerberos 4 KDC program does not properly check for null termination of AUTH_MSG_KDC_REQUEST requests, which allows remote attackers to cause a denial of service via a malformed request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-549", "filename": "2000.txt", "content": "CVE-2000-0550(PUBLISHED):Kerberos 4 KDC program improperly frees memory twice (aka \"double-free\"), which allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-550", "filename": "2000.txt", "content": "CVE-2000-0551(PUBLISHED):The file transfer mechanism in Danware NetOp 6.0 does not provide authentication, which allows remote attackers to access and modify arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-551", "filename": "2000.txt", "content": "CVE-2000-0552(PUBLISHED):ICQwebmail client for ICQ 2000A creates a world readable temporary file during login and does not delete it, which allows local users to obtain sensitive information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-552", "filename": "2000.txt", "content": "CVE-2000-0553(PUBLISHED):Race condition in IPFilter firewall 3.4.3 and earlier, when configured with overlapping \"return-rst\" and \"keep state\" rules, allows remote attackers to bypass access restrictions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-553", "filename": "2000.txt", "content": "CVE-2000-0554(PUBLISHED):Ceilidh allows remote attackers to obtain the real path of the Ceilidh directory via the translated_path hidden form field.", "metadata": {"filename": "2000.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-554", "filename": "2000.txt", "content": "CVE-2000-0555(PUBLISHED):Ceilidh allows remote attackers to cause a denial of service via a large number of POST requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-555", "filename": "2000.txt", "content": "CVE-2000-0556(PUBLISHED):Buffer overflow in the web interface for Cmail 2.4.7 allows remote attackers to cause a denial of service by sending a large user name to the user dialog running on port 8002.", "metadata": {"filename": "2000.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-556", "filename": "2000.txt", "content": "CVE-2000-0557(PUBLISHED):Buffer overflow in the web interface for Cmail 2.4.7 allows remote attackers to execute arbitrary commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-557", "filename": "2000.txt", "content": "CVE-2000-0558(PUBLISHED):Buffer overflow in HP Openview Network Node Manager 6.1 allows remote attackers to execute arbitrary commands via the Alarm service (OVALARMSRV) on port 2345.", "metadata": {"filename": "2000.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-558", "filename": "2000.txt", "content": "CVE-2000-0559(PUBLISHED):eTrust Intrusion Detection System (formerly SessionWall-3) uses weak encryption (XOR) to store administrative passwords in the registry, which allows local users to easily decrypt the passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-559", "filename": "2000.txt", "content": "CVE-2000-0561(PUBLISHED):Buffer overflow in WebBBS 1.15 allows remote attackers to execute arbitrary commands via a long HTTP GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-560", "filename": "2000.txt", "content": "CVE-2000-0562(PUBLISHED):BlackIce Defender 2.1 and earlier, and BlackIce Pro 2.0.23 and earlier, do not properly block Back Orifice traffic when the security setting is Nervous or lower.", "metadata": {"filename": "2000.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-561", "filename": "2000.txt", "content": "CVE-2000-0563(PUBLISHED):The URLConnection function in MacOS Runtime Java (MRJ) 2.1 and earlier and the Microsoft virtual machine (VM) for MacOS allows a malicious web site operator to connect to arbitrary hosts using a HTTP redirection, in violation of the Java security model.", "metadata": {"filename": "2000.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-562", "filename": "2000.txt", "content": "CVE-2000-0564(PUBLISHED):The guestbook CGI program in ICQ Web Front service for ICQ 2000a, 99b, and others allows remote attackers to cause a denial of service via a URL with a long name parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-563", "filename": "2000.txt", "content": "CVE-2000-0565(PUBLISHED):SmartFTP Daemon 0.2 allows a local user to access arbitrary files by uploading and specifying an alternate user configuration file via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-564", "filename": "2000.txt", "content": "CVE-2000-0566(PUBLISHED):makewhatis in Linux man package allows local users to overwrite files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-565", "filename": "2000.txt", "content": "CVE-2000-0567(PUBLISHED):Buffer overflow in Microsoft Outlook and Outlook Express allows remote attackers to execute arbitrary commands via a long Date field in an email header, aka the \"Malformed E-mail Header\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-566", "filename": "2000.txt", "content": "CVE-2000-0568(PUBLISHED):Sybergen Secure Desktop 2.1 does not properly protect against false router advertisements (ICMP type 9), which allows remote attackers to modify default routes.", "metadata": {"filename": "2000.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-567", "filename": "2000.txt", "content": "CVE-2000-0569(PUBLISHED):Sybergen Sygate allows remote attackers to cause a denial of service by sending a malformed DNS UDP packet to its internal interface.", "metadata": {"filename": "2000.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-568", "filename": "2000.txt", "content": "CVE-2000-0570(PUBLISHED):FirstClass Internet Services server 5.770, and other versions before 6.1, allows remote attackers to cause a denial of service by sending an email with a long To: mail header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-569", "filename": "2000.txt", "content": "CVE-2000-0571(PUBLISHED):LocalWEB HTTP server 1.2.0 allows remote attackers to cause a denial of service via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-570", "filename": "2000.txt", "content": "CVE-2000-0572(PUBLISHED):The Razor configuration management tool uses weak encryption for its password file, which allows local users to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-571", "filename": "2000.txt", "content": "CVE-2000-0573(PUBLISHED):The lreply function in wu-ftpd 2.6.0 and earlier does not properly cleanse an untrusted format string, which allows remote attackers to execute arbitrary commands via the SITE EXEC command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-572", "filename": "2000.txt", "content": "CVE-2000-0574(PUBLISHED):FTP servers such as OpenBSD ftpd, NetBSD ftpd, ProFTPd and Opieftpd do not properly cleanse untrusted format strings that are used in the setproctitle function (sometimes called by set_proc_title), which allows remote attackers to cause a denial of service or execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-573", "filename": "2000.txt", "content": "CVE-2000-0575(PUBLISHED):SSH 1.2.27 with Kerberos authentication support stores Kerberos tickets in a file which is created in the current directory of the user who is logging in, which could allow remote attackers to sniff the ticket cache if the home directory is installed on NFS.", "metadata": {"filename": "2000.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-574", "filename": "2000.txt", "content": "CVE-2000-0576(PUBLISHED):Oracle Web Listener for AIX versions 4.0.7.0.0 and 4.0.8.1.0 allows remote attackers to cause a denial of service via a malformed URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-575", "filename": "2000.txt", "content": "CVE-2000-0577(PUBLISHED):Netscape Professional Services FTP Server 1.3.6 allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-576", "filename": "2000.txt", "content": "CVE-2000-0578(PUBLISHED):SGI MIPSPro compilers C, C++, F77 and F90 generate temporary files in /tmp with predictable file names, which could allow local users to insert malicious contents into these files as they are being compiled by another user.", "metadata": {"filename": "2000.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-577", "filename": "2000.txt", "content": "CVE-2000-0579(PUBLISHED):IRIX crontab creates temporary files with predictable file names and with the umask of the user, which could allow local users to modify another user's crontab file as it is being edited.", "metadata": {"filename": "2000.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-578", "filename": "2000.txt", "content": "CVE-2000-0580(PUBLISHED):Windows 2000 Server allows remote attackers to cause a denial of service by sending a continuous stream of binary zeros to various TCP and UDP ports, which significantly increases the CPU utilization.", "metadata": {"filename": "2000.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-579", "filename": "2000.txt", "content": "CVE-2000-0581(PUBLISHED):Windows 2000 Telnet Server allows remote attackers to cause a denial of service by sending a continuous stream of binary zeros, which causes the server to crash.", "metadata": {"filename": "2000.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-580", "filename": "2000.txt", "content": "CVE-2000-0582(PUBLISHED):Check Point FireWall-1 4.0 and 4.1 allows remote attackers to cause a denial of service by sending a stream of invalid commands (such as binary zeros) to the SMTP Security Server proxy.", "metadata": {"filename": "2000.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-581", "filename": "2000.txt", "content": "CVE-2000-0583(PUBLISHED):vchkpw program in vpopmail before version 4.8 does not properly cleanse an untrusted format string used in a call to syslog, which allows remote attackers to cause a denial of service via a USER or PASS command that contains arbitrary formatting directives.", "metadata": {"filename": "2000.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-582", "filename": "2000.txt", "content": "CVE-2000-0584(PUBLISHED):Buffer overflow in Canna input system allows remote attackers to execute arbitrary commands via an SR_INIT command with a long user name or group name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-583", "filename": "2000.txt", "content": "CVE-2000-0585(PUBLISHED):ISC DHCP client program dhclient allows remote attackers to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-584", "filename": "2000.txt", "content": "CVE-2000-0586(PUBLISHED):Buffer overflow in Dalnet IRC server 4.6.5 allows remote attackers to cause a denial of service or execute arbitrary commands via the SUMMON command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-585", "filename": "2000.txt", "content": "CVE-2000-0587(PUBLISHED):The privpath directive in glftpd 1.18 allows remote attackers to bypass access restrictions for directories by using the file name completion capability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-586", "filename": "2000.txt", "content": "CVE-2000-0588(PUBLISHED):SawMill 5.0.21 CGI program allows remote attackers to read the first line of arbitrary files by listing the file in the rfcf parameter, whose contents SawMill attempts to parse as configuration commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-587", "filename": "2000.txt", "content": "CVE-2000-0589(PUBLISHED):SawMill 5.0.21 uses weak encryption to store passwords, which allows attackers to easily decrypt the password and modify the SawMill configuration.", "metadata": {"filename": "2000.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-588", "filename": "2000.txt", "content": "CVE-2000-0590(PUBLISHED):Poll It 2.0 CGI script allows remote attackers to read arbitrary files by specifying the file name in the data_dir parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-589", "filename": "2000.txt", "content": "CVE-2000-0591(PUBLISHED):Novell BorderManager 3.0 and 3.5 allows remote attackers to bypass URL filtering by encoding characters in the requested URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-590", "filename": "2000.txt", "content": "CVE-2000-0592(PUBLISHED):Buffer overflows in POP3 service in WinProxy 2.0 and 2.0.1 allow remote attackers to execute arbitrary commands via long USER, PASS, LIST, RETR, or DELE commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-591", "filename": "2000.txt", "content": "CVE-2000-0593(PUBLISHED):WinProxy 2.0 and 2.0.1 allows remote attackers to cause a denial of service by sending an HTTP GET request without listing an HTTP version number.", "metadata": {"filename": "2000.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-592", "filename": "2000.txt", "content": "CVE-2000-0594(PUBLISHED):BitchX IRC client does not properly cleanse an untrusted format string, which allows remote attackers to cause a denial of service via an invite to a channel whose name includes special formatting characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-593", "filename": "2000.txt", "content": "CVE-2000-0595(PUBLISHED):libedit searches for the .editrc file in the current directory instead of the user's home directory, which may allow local users to execute arbitrary commands by installing a modified .editrc in another directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-594", "filename": "2000.txt", "content": "CVE-2000-0596(PUBLISHED):Internet Explorer 5.x does not warn a user before opening a Microsoft Access database file that is referenced within ActiveX OBJECT tags in an HTML document, which could allow remote attackers to execute arbitrary commands, aka the \"IE Script\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-595", "filename": "2000.txt", "content": "CVE-2000-0597(PUBLISHED):Microsoft Office 2000 (Excel and PowerPoint) and PowerPoint 97 are marked as safe for scripting, which allows remote attackers to force Internet Explorer or some email clients to save files to arbitrary locations via the Visual Basic for Applications (VBA) SaveAs function, aka the \"Office HTML Script\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-596", "filename": "2000.txt", "content": "CVE-2000-0598(PUBLISHED):Fortech Proxy+ allows remote attackers to bypass access restrictions for to the administration service by redirecting their connections through the telnet proxy.", "metadata": {"filename": "2000.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-597", "filename": "2000.txt", "content": "CVE-2000-0599(PUBLISHED):Buffer overflow in iMesh 1.02 allows remote attackers to execute arbitrary commands via a long string to the iMesh port.", "metadata": {"filename": "2000.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-598", "filename": "2000.txt", "content": "CVE-2000-0600(PUBLISHED):Netscape Enterprise Server in NetWare 5.1 allows remote attackers to cause a denial of service or execute arbitrary commands via a malformed URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-599", "filename": "2000.txt", "content": "CVE-2000-0601(PUBLISHED):LeafChat 1.7 IRC client allows a remote IRC server to cause a denial of service by rapidly sending a large amount of error messages.", "metadata": {"filename": "2000.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-600", "filename": "2000.txt", "content": "CVE-2000-0602(PUBLISHED):Secure Locate (slocate) in Red Hat Linux allows local users to gain privileges via a malformed configuration file that is specified in the LOCATE_PATH environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-601", "filename": "2000.txt", "content": "CVE-2000-0603(PUBLISHED):Microsoft SQL Server 7.0 allows a local user to bypass permissions for stored procedures by referencing them via a temporary stored procedure, aka the \"Stored Procedure Permissions\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-602", "filename": "2000.txt", "content": "CVE-2000-0604(PUBLISHED):gkermit in Red Hat Linux is improperly installed with setgid uucp, which allows local users to modify files owned by uucp.", "metadata": {"filename": "2000.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-603", "filename": "2000.txt", "content": "CVE-2000-0605(PUBLISHED):Blackboard CourseInfo 4.0 stores the local and SQL administrator user names and passwords in cleartext in a registry key whose access control allows users to access the passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-604", "filename": "2000.txt", "content": "CVE-2000-0606(PUBLISHED):Buffer overflow in kon program in Kanji on Console (KON) package on Linux may allow local users to gain root privileges via a long -StartupMessage parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-605", "filename": "2000.txt", "content": "CVE-2000-0607(PUBLISHED):Buffer overflow in fld program in Kanji on Console (KON) package on Linux may allow local users to gain root privileges via an input file containing long CHARSET_REGISTRY or CHARSET_ENCODING settings.", "metadata": {"filename": "2000.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-606", "filename": "2000.txt", "content": "CVE-2000-0608(PUBLISHED):NetWin dMailWeb and cwMail 2.6i and earlier allows remote attackers to cause a denial of service via a long POP parameter (pophost).", "metadata": {"filename": "2000.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-607", "filename": "2000.txt", "content": "CVE-2000-0609(PUBLISHED):NetWin dMailWeb and cwMail 2.6g and earlier allows remote attackers to cause a denial of service via a long username parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-608", "filename": "2000.txt", "content": "CVE-2000-0610(PUBLISHED):NetWin dMailWeb and cwMail 2.6g and earlier allows remote attackers to bypass authentication and use the server for mail relay via a username that contains a carriage return.", "metadata": {"filename": "2000.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-609", "filename": "2000.txt", "content": "CVE-2000-0611(PUBLISHED):The default configuration of NetWin dMailWeb and cwMail trusts all POP servers, which allows attackers to bypass normal authentication and cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-610", "filename": "2000.txt", "content": "CVE-2000-0612(PUBLISHED):Windows 95 and Windows 98 do not properly process spoofed ARP packets, which allows remote attackers to overwrite static entries in the cache table.", "metadata": {"filename": "2000.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-611", "filename": "2000.txt", "content": "CVE-2000-0613(PUBLISHED):Cisco Secure PIX Firewall does not properly identify forged TCP Reset (RST) packets, which allows remote attackers to force the firewall to close legitimate connections.", "metadata": {"filename": "2000.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-612", "filename": "2000.txt", "content": "CVE-2000-0614(PUBLISHED):Tnef program in Linux systems allows remote attackers to overwrite arbitrary files via TNEF encoded compressed attachments which specify absolute path names for the decompressed output.", "metadata": {"filename": "2000.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-613", "filename": "2000.txt", "content": "CVE-2000-0615(PUBLISHED):LPRng 3.6.x improperly installs lpd as setuid root, which can allow local users to append lpd trace and logging messages to files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-614", "filename": "2000.txt", "content": "CVE-2000-0616(PUBLISHED):Vulnerability in HP TurboIMAGE DBUTIL allows local users to gain additional privileges via DBUTIL.PUB.SYS.", "metadata": {"filename": "2000.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-615", "filename": "2000.txt", "content": "CVE-2000-0617(PUBLISHED):Buffer overflow in xconq and cconq game programs on Red Hat Linux allows local users to gain additional privileges via long USER environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-616", "filename": "2000.txt", "content": "CVE-2000-0618(PUBLISHED):Buffer overflow in xconq and cconq game programs on Red Hat Linux allows local users to gain additional privileges via long DISPLAY environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-617", "filename": "2000.txt", "content": "CVE-2000-0619(PUBLISHED):Top Layer AppSwitch 2500 allows remote attackers to cause a denial of service via malformed ICMP packets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-618", "filename": "2000.txt", "content": "CVE-2000-0620(PUBLISHED):libX11 X library allows remote attackers to cause a denial of service via a resource mask of 0, which causes libX11 to go into an infinite loop.", "metadata": {"filename": "2000.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-619", "filename": "2000.txt", "content": "CVE-2000-0621(PUBLISHED):Microsoft Outlook 98 and 2000, and Outlook Express 4.0x and 5.0x, allow remote attackers to read files on the client's system via a malformed HTML message that stores files outside of the cache, aka the \"Cache Bypass\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-620", "filename": "2000.txt", "content": "CVE-2000-0622(PUBLISHED):Buffer overflow in Webfind CGI program in O'Reilly WebSite Professional web server 2.x allows remote attackers to execute arbitrary commands via a URL containing a long \"keywords\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-621", "filename": "2000.txt", "content": "CVE-2000-0623(PUBLISHED):Buffer overflow in O'Reilly WebSite Professional web server 2.4 and earlier allows remote attackers to execute arbitrary commands via a long GET request or Referrer header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-622", "filename": "2000.txt", "content": "CVE-2000-0624(PUBLISHED):Buffer overflow in Winamp 2.64 and earlier allows remote attackers to execute arbitrary commands via a long #EXTINF: extension in the M3U playlist.", "metadata": {"filename": "2000.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-623", "filename": "2000.txt", "content": "CVE-2000-0625(PUBLISHED):NetZero 3.0 and earlier uses weak encryption for storing a user's login information, which allows a local user to decrypt the password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-624", "filename": "2000.txt", "content": "CVE-2000-0626(PUBLISHED):Buffer overflow in Alibaba web server allows remote attackers to cause a denial of service via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-625", "filename": "2000.txt", "content": "CVE-2000-0627(PUBLISHED):BlackBoard CourseInfo 4.0 does not properly authenticate users, which allows local users to modify CourseInfo database information and gain privileges by directly calling the supporting CGI programs such as user_update_passwd.pl and user_update_admin.pl.", "metadata": {"filename": "2000.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-626", "filename": "2000.txt", "content": "CVE-2000-0628(PUBLISHED):The source.asp example script in the Apache ASP module Apache::ASP 1.93 and earlier allows remote attackers to modify files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-627", "filename": "2000.txt", "content": "CVE-2000-0629(PUBLISHED):The default configuration of the Sun Java web server 2.0 and earlier allows remote attackers to execute arbitrary commands by uploading Java code to the server via board.html, then directly calling the JSP compiler servlet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-628", "filename": "2000.txt", "content": "CVE-2000-0630(PUBLISHED):IIS 4.0 and 5.0 allows remote attackers to obtain fragments of source code by appending a +.htr to the URL, a variant of the \"File Fragment Reading via .HTR\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-629", "filename": "2000.txt", "content": "CVE-2000-0631(PUBLISHED):An administrative script from IIS 3.0, later included in IIS 4.0 and 5.0, allows remote attackers to cause a denial of service by accessing the script without a particular argument, aka the \"Absent Directory Browser Argument\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-630", "filename": "2000.txt", "content": "CVE-2000-0632(PUBLISHED):Buffer overflow in the web archive component of L-Soft Listserv 1.8d and earlier allows remote attackers to execute arbitrary commands via a long query string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-631", "filename": "2000.txt", "content": "CVE-2000-0633(PUBLISHED):Vulnerability in Mandrake Linux usermode package allows local users to to reboot or halt the system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-632", "filename": "2000.txt", "content": "CVE-2000-0634(PUBLISHED):The web administration interface for CommuniGate Pro 3.2.5 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-633", "filename": "2000.txt", "content": "CVE-2000-0635(PUBLISHED):The view_page.html sample page in the MiniVend shopping cart program allows remote attackers to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-634", "filename": "2000.txt", "content": "CVE-2000-0636(PUBLISHED):HP JetDirect printers versions G.08.20 and H.08.20 and earlier allow remote attackers to cause a denial of service via a malformed FTP quote command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-635", "filename": "2000.txt", "content": "CVE-2000-0637(PUBLISHED):Microsoft Excel 97 and 2000 allows an attacker to execute arbitrary commands by specifying a malicious .dll using the Register.ID function, aka the \"Excel REGISTER.ID Function\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-636", "filename": "2000.txt", "content": "CVE-2000-0638(PUBLISHED):bb-hostsvc.sh in Big Brother 1.4h1 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack on the HOSTSVC parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-637", "filename": "2000.txt", "content": "CVE-2000-0639(PUBLISHED):The default configuration of Big Brother 1.4h2 and earlier does not include proper access restrictions, which allows remote attackers to execute arbitrary commands by using bbd to upload a file whose extension will cause it to be executed as a CGI script by the web server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-638", "filename": "2000.txt", "content": "CVE-2000-0640(PUBLISHED):Guild FTPd allows remote attackers to determine the existence of files outside the FTP root via a .. (dot dot) attack, which provides different error messages depending on whether the file exists or not.", "metadata": {"filename": "2000.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-639", "filename": "2000.txt", "content": "CVE-2000-0641(PUBLISHED):Savant web server allows remote attackers to execute arbitrary commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-640", "filename": "2000.txt", "content": "CVE-2000-0642(PUBLISHED):The default configuration of WebActive HTTP Server 1.00 stores the web access log active.log in the document root, which allows remote attackers to view the logs by directly requesting the page.", "metadata": {"filename": "2000.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-641", "filename": "2000.txt", "content": "CVE-2000-0643(PUBLISHED):Buffer overflow in WebActive HTTP Server 1.00 allows remote attackers to cause a denial of service via a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-642", "filename": "2000.txt", "content": "CVE-2000-0644(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows remote attackers to cause a denial of service by executing a STAT command while the LIST command is still executing.", "metadata": {"filename": "2000.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-643", "filename": "2000.txt", "content": "CVE-2000-0645(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows remote attackers to cause a denial of service by using the RESTART (REST) command and writing beyond the end of a file, or writing to a file that does not exist, via commands such as STORE UNIQUE (STOU), STORE (STOR), or APPEND (APPE).", "metadata": {"filename": "2000.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-644", "filename": "2000.txt", "content": "CVE-2000-0646(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows remote attackers to obtain the real pathname for a file by executing a STATUS (STAT) command while the file is being transferred.", "metadata": {"filename": "2000.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-645", "filename": "2000.txt", "content": "CVE-2000-0647(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows remote attackers to cause a denial of service by executing an MLST command before logging into the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-646", "filename": "2000.txt", "content": "CVE-2000-0648(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows local users to cause a denial of service by executing the RENAME TO (RNTO) command before a RENAME FROM (RNFR) command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-647", "filename": "2000.txt", "content": "CVE-2000-0649(PUBLISHED):IIS 4.0 allows remote attackers to obtain the internal IP address of the server via an HTTP 1.0 request for a web page which is protected by basic authentication and has no realm defined.", "metadata": {"filename": "2000.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-648", "filename": "2000.txt", "content": "CVE-2000-0650(PUBLISHED):The default installation of VirusScan 4.5 and NetShield 4.5 has insecure permissions for the registry key that identifies the AutoUpgrade directory, which allows local users to execute arbitrary commands by replacing SETUP.EXE in that directory with a Trojan Horse.", "metadata": {"filename": "2000.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-649", "filename": "2000.txt", "content": "CVE-2000-0651(PUBLISHED):The ClientTrust program in Novell BorderManager does not properly verify the origin of authentication requests, which could allow remote attackers to impersonate another user by replaying the authentication requests and responses from port 3024 of the victim's machine.", "metadata": {"filename": "2000.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-650", "filename": "2000.txt", "content": "CVE-2000-0652(PUBLISHED):IBM WebSphere allows remote attackers to read source code for executable web files by directly calling the default InvokerServlet using a URL which contains the \"/servlet/file\" string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-651", "filename": "2000.txt", "content": "CVE-2000-0653(PUBLISHED):Microsoft Outlook Express allows remote attackers to monitor a user's email by creating a persistent browser link to the Outlook Express windows, aka the \"Persistent Mail-Browser Link\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-652", "filename": "2000.txt", "content": "CVE-2000-0654(PUBLISHED):Microsoft Enterprise Manager allows local users to obtain database passwords via the Data Transformation Service (DTS) package Registered Servers Dialog dialog, aka a variant of the \"DTS Password\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-653", "filename": "2000.txt", "content": "CVE-2000-0655(PUBLISHED):Netscape Communicator 4.73 and earlier allows remote attackers to cause a denial of service or execute arbitrary commands via a JPEG image containing a comment with an illegal field length of 1.", "metadata": {"filename": "2000.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-654", "filename": "2000.txt", "content": "CVE-2000-0656(PUBLISHED):Buffer overflow in AnalogX proxy server 4.04 and earlier allows remote attackers to cause a denial of service via a long USER command in the FTP protocol.", "metadata": {"filename": "2000.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-655", "filename": "2000.txt", "content": "CVE-2000-0657(PUBLISHED):Buffer overflow in AnalogX proxy server 4.04 and earlier allows remote attackers to cause a denial of service via a long HELO command in the SMTP protocol.", "metadata": {"filename": "2000.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-656", "filename": "2000.txt", "content": "CVE-2000-0658(PUBLISHED):Buffer overflow in AnalogX proxy server 4.04 and earlier allows remote attackers to cause a denial of service via a long USER command in the POP3 protocol.", "metadata": {"filename": "2000.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-657", "filename": "2000.txt", "content": "CVE-2000-0659(PUBLISHED):Buffer overflow in AnalogX proxy server 4.04 and earlier allows remote attackers to cause a denial of service via a long user ID in a SOCKS4 CONNECT request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-658", "filename": "2000.txt", "content": "CVE-2000-0660(PUBLISHED):The WDaemon web server for WorldClient 2.1 allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-659", "filename": "2000.txt", "content": "CVE-2000-0661(PUBLISHED):WircSrv IRC Server 5.07s allows remote attackers to cause a denial of service via a long string to the server port.", "metadata": {"filename": "2000.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-660", "filename": "2000.txt", "content": "CVE-2000-0662(PUBLISHED):Internet Explorer 5.x and Microsoft Outlook allows remote attackers to read arbitrary files by redirecting the contents of an IFRAME using the DHTML Edit Control (DHTMLED).", "metadata": {"filename": "2000.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-661", "filename": "2000.txt", "content": "CVE-2000-0663(PUBLISHED):The registry entry for the Windows Shell executable (Explorer.exe) in Windows NT and Windows 2000 uses a relative path name, which allows local users to execute arbitrary commands by inserting a Trojan Horse named Explorer.exe into the %Systemdrive% directory, aka the \"Relative Shell Path\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-662", "filename": "2000.txt", "content": "CVE-2000-0664(PUBLISHED):AnalogX SimpleServer:WWW 1.06 and earlier allows remote attackers to read arbitrary files via a modified .. (dot dot) attack that uses the %2E URL encoding for the dots.", "metadata": {"filename": "2000.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-663", "filename": "2000.txt", "content": "CVE-2000-0665(PUBLISHED):GAMSoft TelSrv telnet server 1.5 and earlier allows remote attackers to cause a denial of service via a long username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-664", "filename": "2000.txt", "content": "CVE-2000-0666(PUBLISHED):rpc.statd in the nfs-utils package in various Linux distributions does not properly cleanse untrusted format strings, which allows remote attackers to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-665", "filename": "2000.txt", "content": "CVE-2000-0667(PUBLISHED):Vulnerability in gpm in Caldera Linux allows local users to delete arbitrary files or conduct a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-666", "filename": "2000.txt", "content": "CVE-2000-0668(PUBLISHED):pam_console PAM module in Linux systems allows a user to access the system console and reboot the system when a display manager such as gdm or kdm has XDMCP enabled.", "metadata": {"filename": "2000.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-667", "filename": "2000.txt", "content": "CVE-2000-0669(PUBLISHED):Novell NetWare 5.0 allows remote attackers to cause a denial of service by flooding port 40193 with random data.", "metadata": {"filename": "2000.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-668", "filename": "2000.txt", "content": "CVE-2000-0670(PUBLISHED):The cvsweb CGI script in CVSWeb 1.80 allows remote attackers with write access to a CVS repository to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-669", "filename": "2000.txt", "content": "CVE-2000-0671(PUBLISHED):Roxen web server earlier than 2.0.69 allows allows remote attackers to bypass access restrictions, list directory contents, and read source code by inserting a null character (%00) to the URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-670", "filename": "2000.txt", "content": "CVE-2000-0672(PUBLISHED):The default configuration of Jakarta Tomcat does not restrict access to the /admin context, which allows remote attackers to read arbitrary files by directly calling the administrative servlets to add a context for the root directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-671", "filename": "2000.txt", "content": "CVE-2000-0673(PUBLISHED):The NetBIOS Name Server (NBNS) protocol does not perform authentication, which allows remote attackers to cause a denial of service by sending a spoofed Name Conflict or Name Release datagram, aka the \"NetBIOS Name Server Protocol Spoofing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-671", "line_number": 671, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-672", "filename": "2000.txt", "content": "CVE-2000-0674(PUBLISHED):ftp.pl CGI program for Virtual Visions FTP browser allows remote attackers to read directories outside of the document root via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-673", "filename": "2000.txt", "content": "CVE-2000-0675(PUBLISHED):Buffer overflow in Infopulse Gatekeeper 3.5 and earlier allows remote attackers to execute arbitrary commands via a long string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-673", "line_number": 673, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-674", "filename": "2000.txt", "content": "CVE-2000-0676(PUBLISHED):Netscape Communicator and Navigator 4.04 through 4.74 allows remote attackers to read arbitrary files by using a Java applet to open a connection to a URL using the \"file\", \"http\", \"https\", and \"ftp\" protocols, as demonstrated by Brown Orifice.", "metadata": {"filename": "2000.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-675", "filename": "2000.txt", "content": "CVE-2000-0677(PUBLISHED):Buffer overflow in IBM Net.Data db2www CGI program allows remote attackers to execute arbitrary commands via a long PATH_INFO environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-676", "filename": "2000.txt", "content": "CVE-2000-0678(PUBLISHED):PGP 5.5.x through 6.5.3 does not properly check if an Additional Decryption Key (ADK) is stored in the signed portion of a public certificate, which allows an attacker who can modify a victim's public certificate to decrypt any data that has been encrypted with the modified certificate.", "metadata": {"filename": "2000.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-677", "filename": "2000.txt", "content": "CVE-2000-0679(PUBLISHED):The CVS 1.10.8 client trusts pathnames that are provided by the CVS server, which allows the server to force the client to create arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-677", "line_number": 677, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-678", "filename": "2000.txt", "content": "CVE-2000-0680(PUBLISHED):The CVS 1.10.8 server does not properly restrict users from creating arbitrary Checkin.prog or Update.prog programs, which allows remote CVS committers to modify or create Trojan horse programs with the Checkin.prog or Update.prog names, then performing a CVS commit action.", "metadata": {"filename": "2000.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-679", "filename": "2000.txt", "content": "CVE-2000-0681(PUBLISHED):Buffer overflow in BEA WebLogic server proxy plugin allows remote attackers to execute arbitrary commands via a long URL with a .JSP extension.", "metadata": {"filename": "2000.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-680", "filename": "2000.txt", "content": "CVE-2000-0682(PUBLISHED):BEA WebLogic 5.1.x allows remote attackers to read source code for parsed pages by inserting /ConsoleHelp/ into the URL, which invokes the FileServlet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-681", "filename": "2000.txt", "content": "CVE-2000-0683(PUBLISHED):BEA WebLogic 5.1.x allows remote attackers to read source code for parsed pages by inserting /*.shtml/ into the URL, which invokes the SSIServlet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-681", "line_number": 681, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-682", "filename": "2000.txt", "content": "CVE-2000-0684(PUBLISHED):BEA WebLogic 5.1.x does not properly restrict access to the JSPServlet, which could allow remote attackers to compile and execute Java JSP code by directly invoking the servlet on any source file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-683", "filename": "2000.txt", "content": "CVE-2000-0685(PUBLISHED):BEA WebLogic 5.1.x does not properly restrict access to the PageCompileServlet, which could allow remote attackers to compile and execute Java JHTML code by directly invoking the servlet on any source file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-684", "filename": "2000.txt", "content": "CVE-2000-0686(PUBLISHED):Auction Weaver CGI script 1.03 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack in the fromfile parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-685", "filename": "2000.txt", "content": "CVE-2000-0687(PUBLISHED):Auction Weaver CGI script 1.03 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack in the catdir parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-686", "filename": "2000.txt", "content": "CVE-2000-0688(PUBLISHED):Subscribe Me LITE does not properly authenticate attempts to change the administrator password, which allows remote attackers to gain privileges for the Account Manager by directly calling the subscribe.pl script with the setpwd parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-687", "filename": "2000.txt", "content": "CVE-2000-0689(PUBLISHED):Account Manager LITE does not properly authenticate attempts to change the administrator password, which allows remote attackers to gain privileges for the Account Manager by directly calling the amadmin.pl script with the setpasswd parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-688", "filename": "2000.txt", "content": "CVE-2000-0690(PUBLISHED):Auction Weaver CGI script 1.02 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters in the fromfile parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-689", "filename": "2000.txt", "content": "CVE-2000-0691(PUBLISHED):The faxrunq and faxrunqd in the mgetty package allows local users to create or modify arbitrary files via a symlink attack which creates a symlink in from /var/spool/fax/outgoing/.last_run to the target file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-690", "filename": "2000.txt", "content": "CVE-2000-0692(PUBLISHED):ISS RealSecure 3.2.1 and 3.2.2 allows remote attackers to cause a denial of service via a flood of fragmented packets with the SYN flag set.", "metadata": {"filename": "2000.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-691", "filename": "2000.txt", "content": "CVE-2000-0693(PUBLISHED):pgxconfig in the Raptor GFX configuration tool uses a relative path name for a system call to the \"cp\" program, which allows local users to execute arbitrary commands by modifying their path to point to an alternate \"cp\" program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-692", "filename": "2000.txt", "content": "CVE-2000-0694(PUBLISHED):pgxconfig in the Raptor GFX configuration tool allows local users to gain privileges via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-692", "line_number": 692, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-693", "filename": "2000.txt", "content": "CVE-2000-0695(PUBLISHED):Buffer overflows in pgxconfig in the Raptor GFX configuration tool allow local users to gain privileges via command line options.", "metadata": {"filename": "2000.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-694", "filename": "2000.txt", "content": "CVE-2000-0696(PUBLISHED):The administration interface for the dwhttpd web server in Solaris AnswerBook2 does not properly authenticate requests to its supporting CGI scripts, which allows remote attackers to add user accounts to the interface by directly calling the admin CGI script.", "metadata": {"filename": "2000.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-695", "filename": "2000.txt", "content": "CVE-2000-0697(PUBLISHED):The administration interface for the dwhttpd web server in Solaris AnswerBook2 allows interface users to remotely execute commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-696", "filename": "2000.txt", "content": "CVE-2000-0698(PUBLISHED):Minicom 1.82.1 and earlier on some Linux systems allows local users to create arbitrary files owned by the uucp user via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-697", "filename": "2000.txt", "content": "CVE-2000-0699(PUBLISHED):Format string vulnerability in ftpd in HP-UX 10.20 allows remote attackers to cause a denial of service or execute arbitrary commands via format strings in the PASS command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-698", "filename": "2000.txt", "content": "CVE-2000-0700(PUBLISHED):Cisco Gigabit Switch Routers (GSR) with Fast Ethernet / Gigabit Ethernet cards, from IOS versions 11.2(15)GS1A up to 11.2(19)GS0.2 and some versions of 12.0, do not properly handle line card failures, which allows remote attackers to bypass ACLs or force the interface to stop forwarding packets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-699", "filename": "2000.txt", "content": "CVE-2000-0701(PUBLISHED):The wrapper program in mailman 2.0beta3 and 2.0beta4 does not properly cleanse untrusted format strings, which allows local users to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-700", "filename": "2000.txt", "content": "CVE-2000-0702(PUBLISHED):The net.init rc script in HP-UX 11.00 (S008net.init) allows local users to overwrite arbitrary files via a symlink attack that points from /tmp/stcp.conf to the targeted file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-701", "filename": "2000.txt", "content": "CVE-2000-0703(PUBLISHED):suidperl (aka sperl) does not properly cleanse the escape sequence \"~!\" before calling /bin/mail to send an error report, which allows local users to gain privileges by setting the \"interactive\" environmental variable and calling suidperl with a filename that contains the escape sequence.", "metadata": {"filename": "2000.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-702", "filename": "2000.txt", "content": "CVE-2000-0704(PUBLISHED):Buffer overflow in SGI Omron WorldView Wnn allows remote attackers to execute arbitrary commands via long JS_OPEN, JS_MKDIR, or JS_FILE_INFO commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-703", "filename": "2000.txt", "content": "CVE-2000-0705(PUBLISHED):ntop running in web mode allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-704", "filename": "2000.txt", "content": "CVE-2000-0706(PUBLISHED):Buffer overflows in ntop running in web mode allows remote attackers to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-705", "filename": "2000.txt", "content": "CVE-2000-0707(PUBLISHED):PCCS MySQLDatabase Admin Tool Manager 1.2.4 and earlier installs the file dbconnect.inc within the web root, which allows remote attackers to obtain sensitive information such as the administrative password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-706", "filename": "2000.txt", "content": "CVE-2000-0708(PUBLISHED):Buffer overflow in Pragma Systems TelnetServer 2000 version 4.0 allows remote attackers to cause a denial of service via a long series of null characters to the rexec port.", "metadata": {"filename": "2000.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-707", "filename": "2000.txt", "content": "CVE-2000-0709(PUBLISHED):The shtml.exe component of Microsoft FrontPage 2000 Server Extensions 1.1 allows remote attackers to cause a denial of service in some components by requesting a URL whose name includes a standard DOS device name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-708", "filename": "2000.txt", "content": "CVE-2000-0710(PUBLISHED):The shtml.exe component of Microsoft FrontPage 2000 Server Extensions 1.1 allows remote attackers to determine the physical path of the server components by requesting an invalid URL whose name includes a standard DOS device name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-708", "line_number": 708, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-709", "filename": "2000.txt", "content": "CVE-2000-0711(PUBLISHED):Netscape Communicator does not properly prevent a ServerSocket object from being created by untrusted entities, which allows remote attackers to create a server on the victim's system via a malicious applet, as demonstrated by Brown Orifice.", "metadata": {"filename": "2000.txt", "chunk_id": "line-709", "line_number": 709, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-710", "filename": "2000.txt", "content": "CVE-2000-0712(PUBLISHED):Linux Intrusion Detection System (LIDS) 0.9.7 allows local users to gain root privileges when LIDS is disabled via the security=0 boot option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-710", "line_number": 710, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-711", "filename": "2000.txt", "content": "CVE-2000-0713(PUBLISHED):Buffer overflow in Adobe Acrobat 4.05, Reader, Business Tools, and Fill In products that handle PDF files allows attackers to execute arbitrary commands via a long /Registry or /Ordering specifier.", "metadata": {"filename": "2000.txt", "chunk_id": "line-711", "line_number": 711, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-712", "filename": "2000.txt", "content": "CVE-2000-0714(PUBLISHED):umb-scheme 3.2-11 for Red Hat Linux is installed with world-writeable files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-712", "line_number": 712, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-713", "filename": "2000.txt", "content": "CVE-2000-0715(PUBLISHED):DiskCheck script diskcheck.pl in Red Hat Linux 6.2 allows local users to create or overwrite arbitrary files via a symlink attack on a temporary file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-713", "line_number": 713, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-714", "filename": "2000.txt", "content": "CVE-2000-0716(PUBLISHED):WorldClient email client in MDaemon 2.8 includes the session ID in the referer field of an HTTP request when the user clicks on a URL, which allows the visited web site to hijack the session ID and read the user's email.", "metadata": {"filename": "2000.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-715", "filename": "2000.txt", "content": "CVE-2000-0717(PUBLISHED):GoodTech FTP server allows remote attackers to cause a denial of service via a large number of RNTO commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-716", "filename": "2000.txt", "content": "CVE-2000-0718(PUBLISHED):A race condition in MandrakeUpdate allows local users to modify RPM files while they are in the /tmp directory before they are installed.", "metadata": {"filename": "2000.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-717", "filename": "2000.txt", "content": "CVE-2000-0719(PUBLISHED):VariCAD 7.0 is installed with world-writeable files, which allows local users to replace the VariCAD programs with a Trojan horse program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-717", "line_number": 717, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-718", "filename": "2000.txt", "content": "CVE-2000-0720(PUBLISHED):news.cgi in GWScripts News Publisher does not properly authenticate requests to add an author to the author index, which allows remote attackers to add new authors by directly posting an HTTP request to the new.cgi program with an addAuthor parameter, and setting the Referer to the news.cgi program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-718", "line_number": 718, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-719", "filename": "2000.txt", "content": "CVE-2000-0721(PUBLISHED):The FSserial, FlagShip_c, and FlagShip_p programs in the FlagShip package are installed world-writeable, which allows local users to replace them with Trojan horses.", "metadata": {"filename": "2000.txt", "chunk_id": "line-719", "line_number": 719, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-720", "filename": "2000.txt", "content": "CVE-2000-0722(PUBLISHED):Helix GNOME Updater helix-update 0.5 and earlier allows local users to install arbitrary RPM packages by creating the /tmp/helix-install installation directory before root has begun installing packages.", "metadata": {"filename": "2000.txt", "chunk_id": "line-720", "line_number": 720, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-721", "filename": "2000.txt", "content": "CVE-2000-0723(PUBLISHED):Helix GNOME Updater helix-update 0.5 and earlier does not properly create /tmp directories, which allows local users to create empty system configuration files such as /etc/config.d/bashrc, /etc/config.d/csh.cshrc, and /etc/rc.config.", "metadata": {"filename": "2000.txt", "chunk_id": "line-721", "line_number": 721, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-722", "filename": "2000.txt", "content": "CVE-2000-0724(PUBLISHED):The go-gnome Helix GNOME pre-installer allows local users to overwrite arbitrary files via a symlink attack on various files in /tmp, including uudecode, snarf, and some installer files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-722", "line_number": 722, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-723", "filename": "2000.txt", "content": "CVE-2000-0725(PUBLISHED):Zope before 2.2.1 does not properly restrict access to the getRoles method, which allows users who can edit DTML to add or modify roles by modifying the roles list that is included in a request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-723", "line_number": 723, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-724", "filename": "2000.txt", "content": "CVE-2000-0726(PUBLISHED):CGIMail.exe CGI program in Stalkerlab Mailers 1.1.2 allows remote attackers to read arbitrary files by specifying the file in the $Attach$ hidden form variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-725", "filename": "2000.txt", "content": "CVE-2000-0727(PUBLISHED):xpdf PDF viewer client earlier than 0.91 does not properly launch a web browser for embedded URL's, which allows an attacker to execute arbitrary commands via a URL that contains shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-726", "filename": "2000.txt", "content": "CVE-2000-0728(PUBLISHED):xpdf PDF viewer client earlier than 0.91 allows local users to overwrite arbitrary files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-726", "line_number": 726, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-727", "filename": "2000.txt", "content": "CVE-2000-0729(PUBLISHED):FreeBSD 5.x, 4.x, and 3.x allows local users to cause a denial of service by executing a program with a malformed ELF image header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-727", "line_number": 727, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-728", "filename": "2000.txt", "content": "CVE-2000-0730(PUBLISHED):Vulnerability in newgrp command in HP-UX 11.0 allows local users to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-729", "filename": "2000.txt", "content": "CVE-2000-0731(PUBLISHED):Directory traversal vulnerability in Worm HTTP server allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-729", "line_number": 729, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-730", "filename": "2000.txt", "content": "CVE-2000-0732(PUBLISHED):Worm HTTP server allows remote attackers to cause a denial of service via a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-731", "filename": "2000.txt", "content": "CVE-2000-0733(PUBLISHED):Telnetd telnet server in IRIX 5.2 through 6.1 does not properly cleans user-injected format strings, which allows remote attackers to execute arbitrary commands via a long RLD variable in the IAC-SB-TELOPT_ENVIRON request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-732", "filename": "2000.txt", "content": "CVE-2000-0734(PUBLISHED):eEye IRIS 1.01 beta allows remote attackers to cause a denial of service via a large number of UDP connections.", "metadata": {"filename": "2000.txt", "chunk_id": "line-732", "line_number": 732, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-733", "filename": "2000.txt", "content": "CVE-2000-0735(PUBLISHED):Buffer overflow in Becky! Internet Mail client 1.26.03 and earlier allows remote attackers to cause a denial of service via a long Content-type: MIME header when the user replies to a message.", "metadata": {"filename": "2000.txt", "chunk_id": "line-733", "line_number": 733, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-734", "filename": "2000.txt", "content": "CVE-2000-0736(PUBLISHED):Buffer overflow in Becky! Internet Mail client 1.26.04 and earlier allows remote attackers to cause a denial of service via a long Content-type: MIME header when the user forwards a message.", "metadata": {"filename": "2000.txt", "chunk_id": "line-734", "line_number": 734, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-735", "filename": "2000.txt", "content": "CVE-2000-0737(PUBLISHED):The Service Control Manager (SCM) in Windows 2000 creates predictable named pipes, which allows a local user with console access to gain administrator privileges, aka the \"Service Control Manager Named Pipe Impersonation\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-735", "line_number": 735, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-736", "filename": "2000.txt", "content": "CVE-2000-0738(PUBLISHED):WebShield SMTP 4.5 allows remote attackers to cause a denial of service by sending e-mail with a From: address that has a . (period) at the end, which causes WebShield to continuously send itself copies of the e-mail.", "metadata": {"filename": "2000.txt", "chunk_id": "line-736", "line_number": 736, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-737", "filename": "2000.txt", "content": "CVE-2000-0739(PUBLISHED):Directory traversal vulnerability in strong.exe program in NAI Net Tools PKI server 1.0 before HotFix 3 allows remote attackers to read arbitrary files via a .. (dot dot) attack in an HTTPS request to the enrollment server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-738", "filename": "2000.txt", "content": "CVE-2000-0740(PUBLISHED):Buffer overflow in strong.exe program in NAI Net Tools PKI server 1.0 before HotFix 3 allows remote attackers to execute arbitrary commands via a long URL in the HTTPS port.", "metadata": {"filename": "2000.txt", "chunk_id": "line-738", "line_number": 738, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-739", "filename": "2000.txt", "content": "CVE-2000-0741(PUBLISHED):Format string vulnerability in strong.exe program in NAI Net Tools PKI server 1.0 before HotFix 3 allows remote attackers to execute arbitrary code via format strings in a URL with a .XUDA extension.", "metadata": {"filename": "2000.txt", "chunk_id": "line-739", "line_number": 739, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-740", "filename": "2000.txt", "content": "CVE-2000-0742(PUBLISHED):The IPX protocol implementation in Microsoft Windows 95 and 98 allows remote attackers to cause a denial of service by sending a ping packet with a source IP address that is a broadcast address, aka the \"Malformed IPX Ping Packet\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-740", "line_number": 740, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-741", "filename": "2000.txt", "content": "CVE-2000-0743(PUBLISHED):Buffer overflow in University of Minnesota (UMN) gopherd 2.x allows remote attackers to execute arbitrary commands via a DES key generation request (GDESkey) that contains a long ticket value.", "metadata": {"filename": "2000.txt", "chunk_id": "line-741", "line_number": 741, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-742", "filename": "2000.txt", "content": "CVE-2000-0745(PUBLISHED):admin.php3 in PHP-Nuke does not properly verify the PHP-Nuke administrator password, which allows remote attackers to gain privileges by requesting a URL that does not specify the aid or pwd parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-743", "filename": "2000.txt", "content": "CVE-2000-0746(PUBLISHED):Vulnerabilities in IIS 4.0 and 5.0 do not properly protect against cross-site scripting (CSS) attacks.  They allow a malicious web site operator to embed scripts in a link to a trusted site, which are returned without quoting in an error message back to the client.  The client then executes those scripts in the same context as the trusted site, aka the \"IIS Cross-Site Scripting\" vulnerabilities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-744", "filename": "2000.txt", "content": "CVE-2000-0747(PUBLISHED):The logrotate script for OpenLDAP before 1.2.11 in Conectiva Linux sends an improper signal to the kernel log daemon (klogd) and kills it.", "metadata": {"filename": "2000.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-745", "filename": "2000.txt", "content": "CVE-2000-0748(PUBLISHED):OpenLDAP 1.2.11 and earlier improperly installs the ud binary with group write permissions, which could allow any user in that group to replace the binary with a Trojan horse.", "metadata": {"filename": "2000.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-746", "filename": "2000.txt", "content": "CVE-2000-0749(PUBLISHED):Buffer overflow in the Linux binary compatibility module in FreeBSD 3.x through 5.x allows local users to gain root privileges via long filenames in the linux shadow file system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-747", "filename": "2000.txt", "content": "CVE-2000-0750(PUBLISHED):Buffer overflow in mopd (Maintenance Operations Protocol loader daemon) allows remote attackers to execute arbitrary commands via a long file name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-748", "filename": "2000.txt", "content": "CVE-2000-0751(PUBLISHED):mopd (Maintenance Operations Protocol loader daemon) does not properly cleanse user-injected format strings, which allows remote attackers to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-749", "filename": "2000.txt", "content": "CVE-2000-0752(PUBLISHED):Buffer overflows in brouted in FreeBSD and possibly other OSes allows local users to gain root privileges via long command line arguments.", "metadata": {"filename": "2000.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-750", "filename": "2000.txt", "content": "CVE-2000-0753(PUBLISHED):The Microsoft Outlook mail client identifies the physical path of the sender's machine within a winmail.dat attachment to Rich Text Format (RTF) files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-750", "line_number": 750, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-751", "filename": "2000.txt", "content": "CVE-2000-0754(PUBLISHED):Vulnerability in HP OpenView Network Node Manager (NMM) version 6.1 related to passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-752", "filename": "2000.txt", "content": "CVE-2000-0755(PUBLISHED):Vulnerability in the newgrp command in HP-UX 11.00 allows local users to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-753", "filename": "2000.txt", "content": "CVE-2000-0756(PUBLISHED):Microsoft Outlook 2000 does not properly process long or malformed fields in vCard (.vcf) files, which allows attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-754", "filename": "2000.txt", "content": "CVE-2000-0757(PUBLISHED):The sysgen service in Aptis Totalbill does not perform authentication, which allows remote attackers to gain root privileges by connecting to the service and specifying the commands to be executed.", "metadata": {"filename": "2000.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-755", "filename": "2000.txt", "content": "CVE-2000-0758(PUBLISHED):The web interface for Lyris List Manager 3 and 4 allows list subscribers to obtain administrative access by modifying the value of the list_admin hidden form field.", "metadata": {"filename": "2000.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-756", "filename": "2000.txt", "content": "CVE-2000-0759(PUBLISHED):Jakarta Tomcat 3.1 under Apache reveals physical path information when a remote attacker requests a URL that does not exist, which generates an error message that includes the physical path.", "metadata": {"filename": "2000.txt", "chunk_id": "line-756", "line_number": 756, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-757", "filename": "2000.txt", "content": "CVE-2000-0760(PUBLISHED):The Snoop servlet in Jakarta Tomcat 3.1 and 3.0 under Apache reveals sensitive system information when a remote attacker requests a nonexistent URL with a .snp extension.", "metadata": {"filename": "2000.txt", "chunk_id": "line-757", "line_number": 757, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-758", "filename": "2000.txt", "content": "CVE-2000-0761(PUBLISHED):OS2/Warp 4.5 FTP server allows remote attackers to cause a denial of service via a long username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-759", "filename": "2000.txt", "content": "CVE-2000-0762(PUBLISHED):The default installation of eTrust Access Control (formerly SeOS) uses a default encryption key, which allows remote attackers to spoof the eTrust administrator and gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-760", "filename": "2000.txt", "content": "CVE-2000-0763(PUBLISHED):xlockmore and xlockf do not properly cleanse user-injected format strings, which allows local users to gain root privileges via the -d option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-760", "line_number": 760, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-761", "filename": "2000.txt", "content": "CVE-2000-0764(PUBLISHED):Intel Express 500 series switches allow a remote attacker to cause a denial of service via a malformed IP packet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-761", "line_number": 761, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-762", "filename": "2000.txt", "content": "CVE-2000-0765(PUBLISHED):Buffer overflow in the HTML interpreter in Microsoft Office 2000 allows an attacker to execute arbitrary commands via a long embedded object tag, aka the \"Microsoft Office HTML Object Tag\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-762", "line_number": 762, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-763", "filename": "2000.txt", "content": "CVE-2000-0766(PUBLISHED):Buffer overflow in vqSoft vqServer 1.4.49 allows remote attackers to cause a denial of service or possibly gain privileges via a long HTTP GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-763", "line_number": 763, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-764", "filename": "2000.txt", "content": "CVE-2000-0767(PUBLISHED):The ActiveX control for invoking a scriptlet in Internet Explorer 4.x and 5.x renders arbitrary file types instead of HTML, which allows an attacker to read arbitrary files, aka the \"Scriptlet Rendering\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-765", "filename": "2000.txt", "content": "CVE-2000-0768(PUBLISHED):A function in Internet Explorer 4.x and 5.x does not properly verify the domain of a frame within a browser window, which allows a remote attacker to read client files, aka a variant of the \"Frame Domain Verification\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-766", "filename": "2000.txt", "content": "CVE-2000-0769(PUBLISHED):O'Reilly WebSite Pro 2.3.7 installs the uploader.exe program with execute permissions for all users, which allows remote attackers to create and execute arbitrary files by directly calling uploader.exe.", "metadata": {"filename": "2000.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-767", "filename": "2000.txt", "content": "CVE-2000-0770(PUBLISHED):IIS 4.0 and 5.0 does not properly restrict access to certain types of files when their parent folders have less restrictive permissions, which could allow remote attackers to bypass access restrictions to some files, aka the \"File Permission Canonicalization\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-768", "filename": "2000.txt", "content": "CVE-2000-0771(PUBLISHED):Microsoft Windows 2000 allows local users to cause a denial of service by corrupting the local security policy via malformed RPC traffic, aka the \"Local Security Policy Corruption\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-769", "filename": "2000.txt", "content": "CVE-2000-0772(PUBLISHED):The installation of Tumbleweed Messaging Management System (MMS) 4.6 and earlier (formerly Worldtalk Worldsecure) creates a default account \"sa\" with no password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-770", "filename": "2000.txt", "content": "CVE-2000-0773(PUBLISHED):Bajie HTTP web server 0.30a allows remote attackers to read arbitrary files via a URL that contains a \"....\", a variant of the dot dot directory traversal attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-771", "filename": "2000.txt", "content": "CVE-2000-0774(PUBLISHED):The sample Java servlet \"test\" in Bajie HTTP web server 0.30a reveals the real pathname of the web document root.", "metadata": {"filename": "2000.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-772", "filename": "2000.txt", "content": "CVE-2000-0775(PUBLISHED):Buffer overflow in RobTex Viking server earlier than 1.06-370 allows remote attackers to cause a denial of service or execute arbitrary commands via a long HTTP GET request, or long Unless-Modified-Since, If-Range, or If-Modified-Since headers.", "metadata": {"filename": "2000.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-773", "filename": "2000.txt", "content": "CVE-2000-0776(PUBLISHED):Mediahouse Statistics Server 5.02x allows remote attackers to execute arbitrary commands via a long HTTP GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-774", "filename": "2000.txt", "content": "CVE-2000-0777(PUBLISHED):The password protection feature of Microsoft Money can store the password in plaintext, which allows attackers with physical access to the system to obtain the password, aka the \"Money Password\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-775", "filename": "2000.txt", "content": "CVE-2000-0778(PUBLISHED):IIS 5.0 allows remote attackers to obtain source code for .ASP files and other scripts via an HTTP GET request with a \"Translate: f\" header, aka the \"Specialized Header\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-776", "filename": "2000.txt", "content": "CVE-2000-0779(PUBLISHED):Checkpoint Firewall-1 with the RSH/REXEC setting enabled allows remote attackers to bypass access restrictions and connect to a RSH/REXEC client via malformed connection requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-777", "filename": "2000.txt", "content": "CVE-2000-0780(PUBLISHED):The web server in IPSWITCH IMail 6.04 and earlier allows remote attackers to read and delete arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-778", "filename": "2000.txt", "content": "CVE-2000-0781(PUBLISHED):uagentsetup in ARCServeIT Client Agent 6.62 does not properly check for the existence or ownership of a temporary file which is moved to the agent.cfg configuration file, which allows local users to execute arbitrary commands by modifying the temporary file before it is moved.", "metadata": {"filename": "2000.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-779", "filename": "2000.txt", "content": "CVE-2000-0782(PUBLISHED):netauth.cgi program in Netwin Netauth 4.2e and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-780", "filename": "2000.txt", "content": "CVE-2000-0783(PUBLISHED):Watchguard Firebox II allows remote attackers to cause a denial of service by sending a malformed URL to the authentication service on port 4100.", "metadata": {"filename": "2000.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-781", "filename": "2000.txt", "content": "CVE-2000-0784(PUBLISHED):sshd program in the Rapidstream 2.1 Beta VPN appliance has a hard-coded \"rsadmin\" account with a null password, which allows remote attackers to execute arbitrary commands via ssh.", "metadata": {"filename": "2000.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-782", "filename": "2000.txt", "content": "CVE-2000-0785(PUBLISHED):WircSrv IRC Server 5.07s allows IRC operators to read arbitrary files via the importmotd command, which sets the Message of the Day (MOTD) to the specified file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-783", "filename": "2000.txt", "content": "CVE-2000-0786(PUBLISHED):GNU userv 1.0.0 and earlier does not properly perform file descriptor swapping, which can corrupt the USERV_GROUPS and USERV_GIDS environmental variables and allow local users to bypass some access restrictions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-784", "filename": "2000.txt", "content": "CVE-2000-0787(PUBLISHED):IRC Xchat client versions 1.4.2 and earlier allows remote attackers to execute arbitrary commands by encoding shell metacharacters into a URL which XChat uses to launch a web browser.", "metadata": {"filename": "2000.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-785", "filename": "2000.txt", "content": "CVE-2000-0788(PUBLISHED):The Mail Merge tool in Microsoft Word does not prompt the user before executing Visual Basic (VBA) scripts in an Access database, which could allow an attacker to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-786", "filename": "2000.txt", "content": "CVE-2000-0789(PUBLISHED):WinU 5.x and earlier uses weak encryption to store its configuration password, which allows local users to decrypt the password and gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-787", "filename": "2000.txt", "content": "CVE-2000-0790(PUBLISHED):The web-based folder display capability in Microsoft Internet Explorer 5.5 on Windows 98 allows local users to insert Trojan horse programs by modifying the Folder.htt file and using the InvokeVerb method in the ShellDefView ActiveX control to specify a default execute option for the first file that is listed in the folder.", "metadata": {"filename": "2000.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-788", "filename": "2000.txt", "content": "CVE-2000-0791(PUBLISHED):Trustix installs the httpsd program for Apache-SSL with world-writeable permissions, which allows local users to replace it with a Trojan horse.", "metadata": {"filename": "2000.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-789", "filename": "2000.txt", "content": "CVE-2000-0792(PUBLISHED):Gnome Lokkit firewall package before 0.41 does not properly restrict access to some ports, even if a user does not make any services available.", "metadata": {"filename": "2000.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-790", "filename": "2000.txt", "content": "CVE-2000-0793(PUBLISHED):Norton AntiVirus 5.00.01C with the Novell Netware client does not properly restart the auto-protection service after the first user has logged off of the system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-791", "filename": "2000.txt", "content": "CVE-2000-0794(PUBLISHED):Buffer overflow in IRIX libgl.so library allows local users to gain root privileges via a long HOME variable to programs such as (1) gmemusage and (2) gr_osview.", "metadata": {"filename": "2000.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-792", "filename": "2000.txt", "content": "CVE-2000-0795(PUBLISHED):Buffer overflow in lpstat in IRIX 6.2 and 6.3 allows local users to gain root privileges via a long -n option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-793", "filename": "2000.txt", "content": "CVE-2000-0796(PUBLISHED):Buffer overflow in dmplay in IRIX 6.2 and 6.3 allows local users to gain root privileges via a long command line option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-794", "filename": "2000.txt", "content": "CVE-2000-0797(PUBLISHED):Buffer overflow in gr_osview in IRIX 6.2 and 6.3 allows local users to gain privileges via a long -D option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-795", "filename": "2000.txt", "content": "CVE-2000-0798(PUBLISHED):The truncate function in IRIX 6.x does not properly check for privileges when the file is in the xfs file system, which allows local users to delete the contents of arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-796", "filename": "2000.txt", "content": "CVE-2000-0799(PUBLISHED):inpview in InPerson in SGI IRIX 5.3 through IRIX 6.5.10 allows local users to gain privileges via a symlink attack on the .ilmpAAA temporary file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-796", "line_number": 796, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-797", "filename": "2000.txt", "content": "CVE-2000-0800(PUBLISHED):String parsing error in rpc.kstatd in the linuxnfs or knfsd packages in SuSE and possibly other Linux systems allows remote attackers to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-798", "filename": "2000.txt", "content": "CVE-2000-0801(PUBLISHED):Buffer overflow in bdf program in HP-UX 11.00 may allow local users to gain root privileges via a long -t option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-799", "filename": "2000.txt", "content": "CVE-2000-0802(PUBLISHED):The BAIR program does not properly restrict access to the Internet Explorer Internet options menu, which allows local users to obtain access to the menu by modifying the registry key that starts BAIR.", "metadata": {"filename": "2000.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-800", "filename": "2000.txt", "content": "CVE-2000-0803(PUBLISHED):GNU Groff uses the current working directory to find a device description file, which allows a local user to gain additional privileges by including a malicious postpro directive in the description file, which is executed when another user runs groff.", "metadata": {"filename": "2000.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-801", "filename": "2000.txt", "content": "CVE-2000-0804(PUBLISHED):Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to bypass the directionality check via fragmented TCP connection requests or reopening closed TCP connection requests, aka \"One-way Connection Enforcement Bypass.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-802", "filename": "2000.txt", "content": "CVE-2000-0805(PUBLISHED):Check Point VPN-1/FireWall-1 4.1 and earlier improperly retransmits encapsulated FWS packets, even if they do not come from a valid FWZ client, aka \"Retransmission of Encapsulated Packets.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-803", "filename": "2000.txt", "content": "CVE-2000-0806(PUBLISHED):The inter-module authentication mechanism (fwa1) in Check Point VPN-1/FireWall-1 4.1 and earlier may allow remote attackers to conduct a denial of service, aka \"Inter-module Communications Bypass.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-804", "filename": "2000.txt", "content": "CVE-2000-0807(PUBLISHED):The OPSEC communications authentication mechanism (fwn1) in Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to spoof connections, aka the \"OPSEC Authentication Vulnerability.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-805", "filename": "2000.txt", "content": "CVE-2000-0808(PUBLISHED):The seed generation mechanism in the inter-module S/Key authentication mechanism in Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to bypass authentication via a brute force attack, aka \"One-time (s/key) Password Authentication.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-806", "filename": "2000.txt", "content": "CVE-2000-0809(PUBLISHED):Buffer overflow in Getkey in the protocol checker in the inter-module communication mechanism in Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-807", "filename": "2000.txt", "content": "CVE-2000-0810(PUBLISHED):Auction Weaver 1.0 through 1.04 does not properly validate the names of form fields, which allows remote attackers to delete arbitrary files and directories via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-808", "filename": "2000.txt", "content": "CVE-2000-0811(PUBLISHED):Auction Weaver 1.0 through 1.04 allows remote attackers to read arbitrary files via a .. (dot dot) attack on the username or bidfile form fields.", "metadata": {"filename": "2000.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-809", "filename": "2000.txt", "content": "CVE-2000-0812(PUBLISHED):The administration module in Sun Java web server allows remote attackers to execute arbitrary commands by uploading Java code to the module and invoke the com.sun.server.http.pagecompile.jsp92.JspServlet by requesting a URL that begins with a /servlet/ tag.", "metadata": {"filename": "2000.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-810", "filename": "2000.txt", "content": "CVE-2000-0813(PUBLISHED):Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to redirect FTP connections to other servers (\"FTP Bounce\") via invalid FTP commands that are processed improperly by FireWall-1, aka \"FTP Connection Enforcement Bypass.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-811", "filename": "2000.txt", "content": "CVE-2000-0816(PUBLISHED):Linux tmpwatch --fuser option allows local users to execute arbitrary commands by creating files whose names contain shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-812", "filename": "2000.txt", "content": "CVE-2000-0817(PUBLISHED):Buffer overflow in the HTTP protocol parser for Microsoft Network Monitor (Netmon) allows remote attackers to execute arbitrary commands via malformed data, aka the \"Netmon Protocol Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-813", "filename": "2000.txt", "content": "CVE-2000-0818(PUBLISHED):The default installation for the Oracle listener program 7.3.4, 8.0.6, and 8.1.6 allows an attacker to cause logging information to be appended to arbitrary files and execute commands via the SET TRC_FILE or SET LOG_FILE commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-813", "line_number": 813, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-814", "filename": "2000.txt", "content": "CVE-2000-0824(PUBLISHED):The unsetenv function in glibc 2.1.1 does not properly unset an environmental variable if the variable is provided twice to a program, which could allow local users to execute arbitrary commands in setuid programs by specifying their own duplicate environmental variables such as LD_PRELOAD or LD_LIBRARY_PATH.", "metadata": {"filename": "2000.txt", "chunk_id": "line-814", "line_number": 814, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-815", "filename": "2000.txt", "content": "CVE-2000-0825(PUBLISHED):Ipswitch Imail 6.0 allows remote attackers to cause a denial of service via a large number of connections in which a long Host: header is sent, which causes a thread to crash.", "metadata": {"filename": "2000.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-816", "filename": "2000.txt", "content": "CVE-2000-0826(PUBLISHED):Buffer overflow in ddicgi.exe program in Mobius DocumentDirect for the Internet 1.2 allows remote attackers to execute arbitrary commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-817", "filename": "2000.txt", "content": "CVE-2000-0827(PUBLISHED):Buffer overflow in the web authorization form of Mobius DocumentDirect for the Internet 1.2 allows remote attackers to cause a denial of service or execute arbitrary commands via a long username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-817", "line_number": 817, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-818", "filename": "2000.txt", "content": "CVE-2000-0828(PUBLISHED):Buffer overflow in ddicgi.exe in Mobius DocumentDirect for the Internet 1.2 allows remote attackers to execute arbitrary commands via a long User-Agent parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-818", "line_number": 818, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-819", "filename": "2000.txt", "content": "CVE-2000-0829(PUBLISHED):The tmpwatch utility in Red Hat Linux forks a new process for each directory level, which allows local users to cause a denial of service by creating deeply nested directories in /tmp or /var/tmp/.", "metadata": {"filename": "2000.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-820", "filename": "2000.txt", "content": "CVE-2000-0830(PUBLISHED):annclist.exe in webTV for Windows allows remote attackers to cause a denial of service by via a large, malformed UDP packet to ports 22701 through 22705.", "metadata": {"filename": "2000.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-821", "filename": "2000.txt", "content": "CVE-2000-0831(PUBLISHED):Buffer overflow in Fastream FTP++ 2.0 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-822", "filename": "2000.txt", "content": "CVE-2000-0832(PUBLISHED):Htgrep CGI program allows remote attackers to read arbitrary files by specifying the full pathname in the hdr parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-823", "filename": "2000.txt", "content": "CVE-2000-0833(PUBLISHED):Buffer overflow in WinSMTP 1.06f and 2.X allows remote attackers to cause a denial of service via a long (1) USER or (2) HELO command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-824", "filename": "2000.txt", "content": "CVE-2000-0834(PUBLISHED):The Windows 2000 telnet client attempts to perform NTLM authentication by default, which allows remote attackers to capture and replay the NTLM challenge/response via a telnet:// URL that points to the malicious server, aka the \"Windows 2000 Telnet Client NTLM Authentication\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-825", "filename": "2000.txt", "content": "CVE-2000-0835(PUBLISHED):search.dll Sambar ISAPI Search utility in Sambar Server 4.4 Beta 3 allows remote attackers to read arbitrary directories by specifying the directory in the query parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-826", "filename": "2000.txt", "content": "CVE-2000-0836(PUBLISHED):Buffer overflow in CamShot WebCam Trial2.6 allows remote attackers to execute arbitrary commands via a long Authorization header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-827", "filename": "2000.txt", "content": "CVE-2000-0837(PUBLISHED):FTP Serv-U 2.5e allows remote attackers to cause a denial of service by sending a large number of null bytes.", "metadata": {"filename": "2000.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-828", "filename": "2000.txt", "content": "CVE-2000-0838(PUBLISHED):Fastream FUR HTTP server 1.0b allows remote attackers to cause a denial of service via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-829", "filename": "2000.txt", "content": "CVE-2000-0839(PUBLISHED):WinCOM LPD 1.00.90 allows remote attackers to cause a denial of service via a large number of LPD options to the LPD port (515).", "metadata": {"filename": "2000.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-830", "filename": "2000.txt", "content": "CVE-2000-0840(PUBLISHED):Buffer overflow in XMail POP3 server before version 0.59 allows remote attackers to execute arbitrary commands via a long USER command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-831", "filename": "2000.txt", "content": "CVE-2000-0841(PUBLISHED):Buffer overflow in XMail POP3 server before version 0.59 allows remote attackers to execute arbitrary commands via a long APOP command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-832", "filename": "2000.txt", "content": "CVE-2000-0842(PUBLISHED):The search97cgi/vtopic\" in the UnixWare 7 scohelphttp webserver allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-833", "filename": "2000.txt", "content": "CVE-2000-0843(PUBLISHED):Buffer overflow in pam_smb and pam_ntdom pluggable authentication modules (PAM) allow remote attackers to execute arbitrary commands via a login with a long user name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-834", "filename": "2000.txt", "content": "CVE-2000-0844(PUBLISHED):Some functions that implement the locale subsystem on Unix do not  properly cleanse user-injected format strings, which allows local attackers to execute arbitrary commands via functions such as gettext and catopen.", "metadata": {"filename": "2000.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-835", "filename": "2000.txt", "content": "CVE-2000-0845(PUBLISHED):kdebug daemon (kdebugd) in Digital Unix 4.0F allows remote attackers to read arbitrary files by specifying the full file name in the initialization packet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-836", "filename": "2000.txt", "content": "CVE-2000-0846(PUBLISHED):Buffer overflow in Darxite 0.4 and earlier allows a remote attacker to execute arbitrary commands via a long username or password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-837", "filename": "2000.txt", "content": "CVE-2000-0847(PUBLISHED):Buffer overflow in University of Washington c-client library (used by pine and other programs) allows remote attackers to execute arbitrary commands via a long X-Keywords header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-838", "filename": "2000.txt", "content": "CVE-2000-0848(PUBLISHED):Buffer overflow in IBM WebSphere web application server (WAS) allows remote attackers to execute arbitrary commands via a long Host:  request header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-839", "filename": "2000.txt", "content": "CVE-2000-0849(PUBLISHED):Race condition in Microsoft Windows Media server allows remote attackers to cause a denial of service in the Windows Media Unicast Service via a malformed request, aka the \"Unicast Service Race Condition\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-840", "filename": "2000.txt", "content": "CVE-2000-0850(PUBLISHED):Netegrity SiteMinder before 4.11 allows remote attackers to bypass its authentication mechanism by appending \"$/FILENAME.ext\" (where ext is .ccc, .class, or .jpg) to the requested URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-841", "filename": "2000.txt", "content": "CVE-2000-0851(PUBLISHED):Buffer overflow in the Still Image Service in Windows 2000 allows local users to gain additional privileges via a long WM_USER message, aka the \"Still Image Service Privilege Escalation\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-842", "filename": "2000.txt", "content": "CVE-2000-0852(PUBLISHED):Multiple buffer overflows in eject on FreeBSD and possibly other OSes allows local users to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-843", "filename": "2000.txt", "content": "CVE-2000-0853(PUBLISHED):YaBB Bulletin Board 9.1.2000 allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-844", "filename": "2000.txt", "content": "CVE-2000-0854(PUBLISHED):When a Microsoft Office 2000 document is launched, the directory of that document is first used to locate DLL's such as riched20.dll and msi.dll, which could allow an attacker to execute arbitrary commands by inserting a Trojan Horse DLL into the same directory as the document.", "metadata": {"filename": "2000.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-845", "filename": "2000.txt", "content": "CVE-2000-0855(PUBLISHED):SunFTP build 9(1) allows remote attackers to cause a denial of service by connecting to the server and disconnecting before sending a newline.", "metadata": {"filename": "2000.txt", "chunk_id": "line-845", "line_number": 845, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-846", "filename": "2000.txt", "content": "CVE-2000-0856(PUBLISHED):Buffer overflow in SunFTP build 9(1) allows remote attackers to cause a denial of service or possibly execute arbitrary commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-846", "line_number": 846, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-847", "filename": "2000.txt", "content": "CVE-2000-0857(PUBLISHED):The logging capability in muh 2.05d IRC server does not properly cleanse user-injected format strings, which allows remote attackers to cause a denial of service or execute arbitrary commands via a malformed nickname.", "metadata": {"filename": "2000.txt", "chunk_id": "line-847", "line_number": 847, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-848", "filename": "2000.txt", "content": "CVE-2000-0858(PUBLISHED):Vulnerability in Microsoft Windows NT 4.0 allows remote attackers to cause a denial of service in IIS by sending it a series of malformed requests which cause INETINFO.EXE to fail, aka the \"Invalid URL\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-849", "filename": "2000.txt", "content": "CVE-2000-0859(PUBLISHED):The web configuration server for NTMail V5 and V6 allows remote attackers to cause a denial of service via a series of partial HTTP requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-850", "filename": "2000.txt", "content": "CVE-2000-0860(PUBLISHED):The file upload capability in PHP versions 3 and 4 allows remote attackers to read arbitrary files by setting hidden form fields whose names match the names of internal PHP script variables.", "metadata": {"filename": "2000.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-851", "filename": "2000.txt", "content": "CVE-2000-0861(PUBLISHED):Mailman 1.1 allows list administrators to execute arbitrary commands via shell metacharacters in the %(listname) macro expansion.", "metadata": {"filename": "2000.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-852", "filename": "2000.txt", "content": "CVE-2000-0862(PUBLISHED):Vulnerability in an administrative interface utility for Allaire Spectra 1.0.1 allows remote attackers to read and modify sensitive configuration information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-853", "filename": "2000.txt", "content": "CVE-2000-0863(PUBLISHED):Buffer overflow in listmanager earlier than 2.105.1 allows local users to gain additional privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-854", "filename": "2000.txt", "content": "CVE-2000-0864(PUBLISHED):Race condition in the creation of a Unix domain socket in GNOME esound 0.2.19 and earlier allows a local user to change the permissions of arbitrary files and directories, and gain additional privileges, via a  symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-855", "filename": "2000.txt", "content": "CVE-2000-0865(PUBLISHED):Buffer overflow in dvtermtype in Tridia Double Vision 3.07.00 allows local users to gain root privileges via a long terminal type argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-856", "filename": "2000.txt", "content": "CVE-2000-0866(PUBLISHED):Interbase 6 SuperServer for Linux allows an attacker to cause a denial of service via a query containing 0 bytes.", "metadata": {"filename": "2000.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-857", "filename": "2000.txt", "content": "CVE-2000-0867(PUBLISHED):Kernel logging daemon (klogd) in Linux does not properly cleanse user-injected format strings, which allows local users to gain root privileges by triggering malformed kernel messages.", "metadata": {"filename": "2000.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-858", "filename": "2000.txt", "content": "CVE-2000-0868(PUBLISHED):The default configuration of Apache 1.3.12 in SuSE Linux 6.4 allows remote attackers to read source code for CGI scripts by replacing the /cgi-bin/ in the requested URL with /cgi-bin-sdb/.", "metadata": {"filename": "2000.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-859", "filename": "2000.txt", "content": "CVE-2000-0869(PUBLISHED):The default configuration of Apache 1.3.12 in SuSE Linux 6.4 enables WebDAV, which allows remote attackers to list arbitrary directories via the PROPFIND HTTP request method.", "metadata": {"filename": "2000.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-860", "filename": "2000.txt", "content": "CVE-2000-0870(PUBLISHED):Buffer overflow in EFTP allows remote attackers to cause a denial of service via a long string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-861", "filename": "2000.txt", "content": "CVE-2000-0871(PUBLISHED):Buffer overflow in EFTP allows remote attackers to cause a denial of service by sending a string that does not contain a newline, then disconnecting from the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-862", "filename": "2000.txt", "content": "CVE-2000-0872(PUBLISHED):explorer.php in PhotoAlbum 0.9.9 allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-863", "filename": "2000.txt", "content": "CVE-2000-0873(PUBLISHED):netstat in AIX 4.x.x does not properly restrict access to the -Zi option, which allows local users to clear network interface statistics and possibly hide evidence of unusual network activities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-864", "filename": "2000.txt", "content": "CVE-2000-0874(PUBLISHED):Eudora mail client includes the absolute path of the sender's host within a virtual card (VCF).", "metadata": {"filename": "2000.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-865", "filename": "2000.txt", "content": "CVE-2000-0875(PUBLISHED):WFTPD and WFTPD Pro 2.41 RC12 allows remote attackers to cause a denial of service by sending a long string of unprintable characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-866", "filename": "2000.txt", "content": "CVE-2000-0876(PUBLISHED):WFTPD and WFTPD Pro 2.41 RC12 allows remote attackers to obtain the  full pathname of the server via a \"%C\" command, which generates an error message that includes the pathname.", "metadata": {"filename": "2000.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-867", "filename": "2000.txt", "content": "CVE-2000-0877(PUBLISHED):mailform.pl CGI script in MailForm 2.0 allows remote attackers to read arbitrary files by specifying the file name in the XX-attach_file parameter, which MailForm then sends to the attacker.", "metadata": {"filename": "2000.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-868", "filename": "2000.txt", "content": "CVE-2000-0878(PUBLISHED):The mailto CGI script allows remote attacker to execute arbitrary commands via shell metacharacters in the emailadd form field.", "metadata": {"filename": "2000.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-869", "filename": "2000.txt", "content": "CVE-2000-0879(PUBLISHED):LPPlus programs dccsched, dcclpdser, dccbkst, dccshut, dcclpdshut, and dccbkstshut are installed setuid root and world executable, which allows arbitrary local users to start and stop various LPD services.", "metadata": {"filename": "2000.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-870", "filename": "2000.txt", "content": "CVE-2000-0880(PUBLISHED):LPPlus creates the lpdprocess file with world-writeable permissions, which allows local users to kill arbitrary processes by specifying an alternate process ID and using the setuid dcclpdshut program to kill the process that was specified in the lpdprocess file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-871", "filename": "2000.txt", "content": "CVE-2000-0881(PUBLISHED):The dccscan setuid program in LPPlus does not properly check if the user has the permissions to print the file that is specified to dccscan, which allows local users to print arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-872", "filename": "2000.txt", "content": "CVE-2000-0882(PUBLISHED):Intel Express 500 series switches allow a remote attacker to cause a denial of service via a malformed ICMP packet, which causes the CPU to crash.", "metadata": {"filename": "2000.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-873", "filename": "2000.txt", "content": "CVE-2000-0883(PUBLISHED):The default configuration of mod_perl for Apache as installed on Mandrake Linux 6.1 through 7.1 sets the /perl/ directory to be browseable, which allows remote attackers to list the contents of that directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-874", "filename": "2000.txt", "content": "CVE-2000-0884(PUBLISHED):IIS 4.0 and 5.0 allows remote attackers to read documents outside of the web root, and possibly execute arbitrary commands, via malformed URLs that contain UNICODE encoded characters, aka the \"Web Server Folder Traversal\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-875", "filename": "2000.txt", "content": "CVE-2000-0885(PUBLISHED):Buffer overflows in Microsoft Network Monitor (Netmon) allow remote attackers to execute arbitrary commands via a long Browser Name in a CIFS Browse Frame, a long SNMP community name, or a long username or filename in an SMB session, aka the \"Netmon Protocol Parsing\" vulnerability.  NOTE: It is highly likely that this candidate will be split into multiple candidates.", "metadata": {"filename": "2000.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-876", "filename": "2000.txt", "content": "CVE-2000-0886(PUBLISHED):IIS 5.0 allows remote attackers to execute arbitrary commands via a malformed request for an executable file whose name is appended with operating system commands, aka the \"Web Server File Request Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-877", "filename": "2000.txt", "content": "CVE-2000-0887(PUBLISHED):named in BIND 8.2 through 8.2.2-P6 allows remote attackers to cause a denial of service by making a compressed zone transfer (ZXFR) request and performing a name service query on an authoritative record that is not cached, aka the \"zxfr bug.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-877", "line_number": 877, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-878", "filename": "2000.txt", "content": "CVE-2000-0888(PUBLISHED):named in BIND 8.2 through 8.2.2-P6 allows remote attackers to cause a denial of service by sending an SRV record to the server, aka the \"srv bug.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-878", "line_number": 878, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-879", "filename": "2000.txt", "content": "CVE-2000-0889(PUBLISHED):Two Sun security certificates have been compromised, which could allow attackers to insert malicious code such as applets and make it appear that it is signed by Sun.", "metadata": {"filename": "2000.txt", "chunk_id": "line-879", "line_number": 879, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-880", "filename": "2000.txt", "content": "CVE-2000-0890(PUBLISHED):periodic in FreeBSD 4.1.1 and earlier, and possibly other operating systems, allows local users to overwrite arbitrary files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-880", "line_number": 880, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-881", "filename": "2000.txt", "content": "CVE-2000-0891(PUBLISHED):A default ECL in Lotus Notes before 5.02 allows remote attackers to execute arbitrary commands by attaching a malicious program in an email message that is automatically executed when the user opens the email.", "metadata": {"filename": "2000.txt", "chunk_id": "line-881", "line_number": 881, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-882", "filename": "2000.txt", "content": "CVE-2000-0892(PUBLISHED):Some telnet clients allow remote telnet servers to request environment variables from the client that may contain sensitive information, or remote web servers to obtain the information via a telnet: URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-882", "line_number": 882, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-883", "filename": "2000.txt", "content": "CVE-2000-0893(PUBLISHED):The presence of the Distributed GL Daemon (dgld) service on port 5232 on SGI IRIX systems allows remote attackers to identify the target host as an SGI system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-883", "line_number": 883, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-884", "filename": "2000.txt", "content": "CVE-2000-0894(PUBLISHED):HTTP server on the WatchGuard SOHO firewall does not properly restrict access to administrative functions such as password resets or rebooting, which allows attackers to cause a denial of service or conduct unauthorized activities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-884", "line_number": 884, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-885", "filename": "2000.txt", "content": "CVE-2000-0895(PUBLISHED):Buffer overflow in HTTP server on the WatchGuard SOHO firewall allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-885", "line_number": 885, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-886", "filename": "2000.txt", "content": "CVE-2000-0896(PUBLISHED):WatchGuard SOHO firewall allows remote attackers to cause a denial of service via a flood of fragmented IP packets, which causes the firewall to drop connections and stop forwarding packets.", "metadata": {"filename": "2000.txt", "chunk_id": "line-886", "line_number": 886, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-887", "filename": "2000.txt", "content": "CVE-2000-0897(PUBLISHED):Small HTTP Server 2.03 and earlier allows remote attackers to cause a denial of service by repeatedly requesting a URL that references a directory that does not contain an index.html file, which consumes memory that is not released after the request is completed.", "metadata": {"filename": "2000.txt", "chunk_id": "line-887", "line_number": 887, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-888", "filename": "2000.txt", "content": "CVE-2000-0898(PUBLISHED):Small HTTP Server 2.01 does not properly process Server Side Includes (SSI) tags that contain null values, which allows local users, and possibly remote attackers, to cause the server to crash by inserting the SSI into an HTML file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-888", "line_number": 888, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-889", "filename": "2000.txt", "content": "CVE-2000-0899(PUBLISHED):Small HTTP Server 2.01 allows remote attackers to cause a denial of service by connecting to the server and sending out multiple GET, HEAD, or POST requests and closing the connection before the server responds to the requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-889", "line_number": 889, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-890", "filename": "2000.txt", "content": "CVE-2000-0900(PUBLISHED):Directory traversal vulnerability in ssi CGI program in thttpd 2.19 and earlier allows remote attackers to read arbitrary files via a \"%2e%2e\" string, a variation of the .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-890", "line_number": 890, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-891", "filename": "2000.txt", "content": "CVE-2000-0901(PUBLISHED):Format string vulnerability in screen 3.9.5 and earlier allows local users to gain root privileges via format characters in the vbell_msg initialization variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-891", "line_number": 891, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-892", "filename": "2000.txt", "content": "CVE-2000-0902(PUBLISHED):getalbum.php in PhotoAlbum before 0.9.9 allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-892", "line_number": 892, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-893", "filename": "2000.txt", "content": "CVE-2000-0903(PUBLISHED):Directory traversal vulnerability in Voyager web server 2.01B in the demo disks for QNX 405 allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-893", "line_number": 893, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-894", "filename": "2000.txt", "content": "CVE-2000-0904(PUBLISHED):Voyager web server 2.01B in the demo disks for QNX 405 stores sensitive web client information in the .photon directory in the web document root, which allows remote attackers to obtain that information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-894", "line_number": 894, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-895", "filename": "2000.txt", "content": "CVE-2000-0905(PUBLISHED):QNX Embedded Resource Manager in Voyager web server 2.01B in the demo disks for QNX 405 allows remote attackers to read sensitive system statistics information via the embedded.html web page.", "metadata": {"filename": "2000.txt", "chunk_id": "line-895", "line_number": 895, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-896", "filename": "2000.txt", "content": "CVE-2000-0906(PUBLISHED):Directory traversal vulnerability in Moreover.com cached_feed.cgi script version 4.July.00 allows remote attackers to read arbitrary files via a .. (dot dot) attack on the category or format parameters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-896", "line_number": 896, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-897", "filename": "2000.txt", "content": "CVE-2000-0907(PUBLISHED):EServ 2.92 Build 2982 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via long HELO and MAIL FROM commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-897", "line_number": 897, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-898", "filename": "2000.txt", "content": "CVE-2000-0908(PUBLISHED):BrowseGate 2.80 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via long Authorization or Referer MIME headers in the HTTP request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-898", "line_number": 898, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-899", "filename": "2000.txt", "content": "CVE-2000-0909(PUBLISHED):Buffer overflow in the automatic mail checking component of Pine 4.21 and earlier allows remote attackers to execute arbitrary commands via a long From: header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-899", "line_number": 899, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-900", "filename": "2000.txt", "content": "CVE-2000-0910(PUBLISHED):Horde library 1.02 allows attackers to execute arbitrary commands via shell metacharacters in the \"from\" address.", "metadata": {"filename": "2000.txt", "chunk_id": "line-900", "line_number": 900, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-901", "filename": "2000.txt", "content": "CVE-2000-0911(PUBLISHED):IMP 2.2 and earlier allows attackers to read and delete arbitrary files by modifying the attachment_name hidden form variable, which causes IMP to send the file to the attacker as an attachment.", "metadata": {"filename": "2000.txt", "chunk_id": "line-901", "line_number": 901, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-902", "filename": "2000.txt", "content": "CVE-2000-0912(PUBLISHED):MultiHTML CGI script allows remote attackers to read arbitrary files and possibly execute arbitrary commands by specifying the file name to the \"multi\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-902", "line_number": 902, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-903", "filename": "2000.txt", "content": "CVE-2000-0913(PUBLISHED):mod_rewrite in Apache 1.3.12 and earlier allows remote attackers to read arbitrary files if a RewriteRule directive is expanded to include a filename whose name contains a regular expression.", "metadata": {"filename": "2000.txt", "chunk_id": "line-903", "line_number": 903, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-904", "filename": "2000.txt", "content": "CVE-2000-0914(PUBLISHED):OpenBSD 2.6 and earlier allows remote attackers to cause a denial of service by flooding the server with ARP requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-904", "line_number": 904, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-905", "filename": "2000.txt", "content": "CVE-2000-0915(PUBLISHED):fingerd in FreeBSD 4.1.1 allows remote attackers to read arbitrary files by specifying the target file name instead of a regular user name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-905", "line_number": 905, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-906", "filename": "2000.txt", "content": "CVE-2000-0916(PUBLISHED):FreeBSD 4.1.1 and earlier, and possibly other BSD-based OSes, uses an insufficient random number generator to generate initial TCP sequence numbers (ISN), which allows remote attackers to spoof TCP connections.", "metadata": {"filename": "2000.txt", "chunk_id": "line-906", "line_number": 906, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-907", "filename": "2000.txt", "content": "CVE-2000-0917(PUBLISHED):Format string vulnerability in use_syslog() function in LPRng 3.6.24 allows remote attackers to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-907", "line_number": 907, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-908", "filename": "2000.txt", "content": "CVE-2000-0918(PUBLISHED):Format string vulnerability in kvt in KDE 1.1.2 may allow local users to execute arbitrary commands via a DISPLAY environmental variable that contains formatting characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-908", "line_number": 908, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-909", "filename": "2000.txt", "content": "CVE-2000-0919(PUBLISHED):Directory traversal vulnerability in PHPix Photo Album 1.0.2 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-909", "line_number": 909, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-910", "filename": "2000.txt", "content": "CVE-2000-0920(PUBLISHED):Directory traversal vulnerability in BOA web server 0.94.8.2 and earlier allows remote attackers to read arbitrary files via a modified .. (dot dot) attack in the GET HTTP request that uses a \"%2E\" instead of a \".\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-910", "line_number": 910, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-911", "filename": "2000.txt", "content": "CVE-2000-0921(PUBLISHED):Directory traversal vulnerability in Hassan Consulting shop.cgi shopping cart program allows remote attackers to read arbitrary files via a .. (dot dot) attack on the page parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-911", "line_number": 911, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-912", "filename": "2000.txt", "content": "CVE-2000-0922(PUBLISHED):Directory traversal vulnerability in Bytes Interactive Web Shopper shopping cart program (shopper.cgi) 2.0 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack on the newpage parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-912", "line_number": 912, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-913", "filename": "2000.txt", "content": "CVE-2000-0923(PUBLISHED):authenticate.cgi CGI program in Aplio PRO allows remote attackers to execute arbitrary commands via shell metacharacters in the password parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-913", "line_number": 913, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-914", "filename": "2000.txt", "content": "CVE-2000-0924(PUBLISHED):Directory traversal vulnerability in search.cgi CGI script in Armada Master Index allows remote attackers to read arbitrary files via a .. (dot dot) attack in the \"catigory\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-914", "line_number": 914, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-915", "filename": "2000.txt", "content": "CVE-2000-0925(PUBLISHED):The default installation of SmartWin CyberOffice Shopping Cart 2 (aka CyberShop) installs the _private directory with world readable permissions, which allows remote attackers to obtain sensitive information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-915", "line_number": 915, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-916", "filename": "2000.txt", "content": "CVE-2000-0926(PUBLISHED):SmartWin CyberOffice Shopping Cart 2 (aka CyberShop) allows remote attackers to modify price information by changing the \"Price\" hidden form variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-916", "line_number": 916, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-917", "filename": "2000.txt", "content": "CVE-2000-0927(PUBLISHED):WQuinn QuotaAdvisor 4.1 does not properly record file sizes if they are stored in alternative data streams, which allows users to bypass quota restrictions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-917", "line_number": 917, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-918", "filename": "2000.txt", "content": "CVE-2000-0928(PUBLISHED):WQuinn QuotaAdvisor 4.1 allows users to list directories and files by running a report on the targeted shares.", "metadata": {"filename": "2000.txt", "chunk_id": "line-918", "line_number": 918, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-919", "filename": "2000.txt", "content": "CVE-2000-0929(PUBLISHED):Microsoft Windows Media Player 7 allows attackers to cause a denial of service in RTF-enabled email clients via an embedded OCX control that is not closed properly, aka the \"OCX Attachment\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-919", "line_number": 919, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-920", "filename": "2000.txt", "content": "CVE-2000-0930(PUBLISHED):Pegasus Mail 3.12 allows remote attackers to read arbitrary files via an embedded URL that calls the mailto: protocol with a -F switch.", "metadata": {"filename": "2000.txt", "chunk_id": "line-920", "line_number": 920, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-921", "filename": "2000.txt", "content": "CVE-2000-0931(PUBLISHED):Buffer overflow in Pegasus Mail 3.11 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long email message containing binary data.", "metadata": {"filename": "2000.txt", "chunk_id": "line-921", "line_number": 921, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-922", "filename": "2000.txt", "content": "CVE-2000-0932(PUBLISHED):MAILsweeper for SMTP 3.x does not properly handle corrupt CDA documents in a ZIP file and hangs, which allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-922", "line_number": 922, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-923", "filename": "2000.txt", "content": "CVE-2000-0933(PUBLISHED):The Input Method Editor (IME) in the Simplified Chinese version of Windows 2000 does not disable access to privileged functionality that should normally be restricted, which allows local users to gain privileges, aka the \"Simplified Chinese IME State Recognition\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-923", "line_number": 923, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-924", "filename": "2000.txt", "content": "CVE-2000-0934(PUBLISHED):Glint in Red Hat Linux 5.2 allows local users to overwrite arbitrary files and cause a denial of service via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-924", "line_number": 924, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-925", "filename": "2000.txt", "content": "CVE-2000-0935(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 allows local users to overwrite arbitrary files via a symlink attack on the cgi.log file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-925", "line_number": 925, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-926", "filename": "2000.txt", "content": "CVE-2000-0936(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 installs the cgi.log logging file with world readable permissions, which allows local users to read sensitive information such as user names and passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-926", "line_number": 926, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-927", "filename": "2000.txt", "content": "CVE-2000-0937(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 does not log login attempts in which the username is correct but the password is wrong, which allows remote attackers to conduct brute force password guessing attacks.", "metadata": {"filename": "2000.txt", "chunk_id": "line-927", "line_number": 927, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-928", "filename": "2000.txt", "content": "CVE-2000-0938(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 supplies a different error message when a valid username is provided versus an invalid name, which allows remote attackers to identify valid users on the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-928", "line_number": 928, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-929", "filename": "2000.txt", "content": "CVE-2000-0939(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 allows remote attackers to cause a denial of service by repeatedly submitting a nonstandard URL in the GET HTTP request and forcing it to restart.", "metadata": {"filename": "2000.txt", "chunk_id": "line-929", "line_number": 929, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-930", "filename": "2000.txt", "content": "CVE-2000-0940(PUBLISHED):Directory traversal vulnerability in Metertek pagelog.cgi allows remote attackers to read arbitrary files via a .. (dot dot) attack on the \"name\" or \"display\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-930", "line_number": 930, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-931", "filename": "2000.txt", "content": "CVE-2000-0941(PUBLISHED):Kootenay Web KW Whois 1.0 CGI program allows remote attackers to execute arbitrary commands via shell metacharacters in the \"whois\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-931", "line_number": 931, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-932", "filename": "2000.txt", "content": "CVE-2000-0942(PUBLISHED):The CiWebHitsFile component in Microsoft Indexing Services for Windows 2000 allows remote attackers to conduct a cross site scripting (CSS) attack via a CiRestriction parameter in a .htw request, aka the \"Indexing Services Cross Site Scripting\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-932", "line_number": 932, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-933", "filename": "2000.txt", "content": "CVE-2000-0943(PUBLISHED):Buffer overflow in bftp daemon (bftpd) 1.0.11 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long USER command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-933", "line_number": 933, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-934", "filename": "2000.txt", "content": "CVE-2000-0944(PUBLISHED):CGI Script Center News Update 1.1 does not properly validate the original news administration password during a password change operation, which allows remote attackers to modify the password without knowing the original password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-934", "line_number": 934, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-935", "filename": "2000.txt", "content": "CVE-2000-0945(PUBLISHED):The web configuration interface for Catalyst 3500 XL switches allows remote attackers to execute arbitrary commands without authentication when the enable password is not set, via a URL containing the /exec/ directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-935", "line_number": 935, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-936", "filename": "2000.txt", "content": "CVE-2000-0946(PUBLISHED):Compaq Easy Access Keyboard software 1.3 does not properly disable access to custom buttons when the screen is locked, which could allow an attacker to gain privileges or execute programs without authorization.", "metadata": {"filename": "2000.txt", "chunk_id": "line-936", "line_number": 936, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-937", "filename": "2000.txt", "content": "CVE-2000-0947(PUBLISHED):Format string vulnerability in cfd daemon in GNU CFEngine before 1.6.0a11 allows attackers to execute arbitrary commands via format characters in the CAUTH command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-937", "line_number": 937, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-938", "filename": "2000.txt", "content": "CVE-2000-0948(PUBLISHED):GnoRPM before 0.95 allows local users to modify arbitrary files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-938", "line_number": 938, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-939", "filename": "2000.txt", "content": "CVE-2000-0949(PUBLISHED):Heap overflow in savestr function in LBNL traceroute 1.4a5 and earlier allows a local user to execute arbitrary commands via the -g option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-939", "line_number": 939, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-940", "filename": "2000.txt", "content": "CVE-2000-0950(PUBLISHED):Format string vulnerability in x-gw in TIS Firewall Toolkit (FWTK) allows local users to execute arbitrary commands via a malformed display name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-940", "line_number": 940, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-941", "filename": "2000.txt", "content": "CVE-2000-0951(PUBLISHED):A misconfiguration in IIS 5.0 with Index Server enabled and the Index property set allows remote attackers to list directories in the web root via a Web Distributed Authoring and Versioning (WebDAV) search.", "metadata": {"filename": "2000.txt", "chunk_id": "line-941", "line_number": 941, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-942", "filename": "2000.txt", "content": "CVE-2000-0952(PUBLISHED):global.cgi CGI program in Global 3.55 and earlier on NetBSD allows remote attackers to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-942", "line_number": 942, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-943", "filename": "2000.txt", "content": "CVE-2000-0953(PUBLISHED):Shambala Server 4.5 allows remote attackers to cause a denial of service by opening then closing a connection.", "metadata": {"filename": "2000.txt", "chunk_id": "line-943", "line_number": 943, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-944", "filename": "2000.txt", "content": "CVE-2000-0954(PUBLISHED):Shambala Server 4.5 stores passwords in plaintext, which could allow local users to obtain the passwords and compromise the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-944", "line_number": 944, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-945", "filename": "2000.txt", "content": "CVE-2000-0955(PUBLISHED):Cisco Virtual Central Office 4000 (VCO/4K) uses weak encryption to store usernames and passwords in the SNMP MIB, which allows an attacker who knows the community name to crack the password and gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-945", "line_number": 945, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-946", "filename": "2000.txt", "content": "CVE-2000-0956(PUBLISHED):cyrus-sasl before 1.5.24 in Red Hat Linux 7.0 does not properly verify the authorization for a local user, which could allow the users to bypass specified access restrictions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-946", "line_number": 946, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-947", "filename": "2000.txt", "content": "CVE-2000-0957(PUBLISHED):The pluggable authentication module for mysql (pam_mysql) before 0.4.7 does not properly cleanse user input when constructing SQL statements, which allows attackers to obtain plaintext passwords or hashes.", "metadata": {"filename": "2000.txt", "chunk_id": "line-947", "line_number": 947, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-948", "filename": "2000.txt", "content": "CVE-2000-0958(PUBLISHED):HotJava Browser 3.0 allows remote attackers to access the DOM of a web page by opening a javascript: URL in a named window.", "metadata": {"filename": "2000.txt", "chunk_id": "line-948", "line_number": 948, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-949", "filename": "2000.txt", "content": "CVE-2000-0959(PUBLISHED):glibc2 does not properly clear the LD_DEBUG_OUTPUT and LD_DEBUG environmental variables when a program is spawned from a setuid program, which could allow local users to overwrite files via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-949", "line_number": 949, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-950", "filename": "2000.txt", "content": "CVE-2000-0960(PUBLISHED):The POP3 server in Netscape Messaging Server 4.15p1 generates different error messages for incorrect user names versus incorrect passwords, which allows remote attackers to determine valid users on the system and harvest email addresses for spam abuse.", "metadata": {"filename": "2000.txt", "chunk_id": "line-950", "line_number": 950, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-951", "filename": "2000.txt", "content": "CVE-2000-0961(PUBLISHED):Buffer overflow in IMAP server in Netscape Messaging Server 4.15 Patch 2 allows local users to execute arbitrary commands via a long LIST command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-951", "line_number": 951, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-952", "filename": "2000.txt", "content": "CVE-2000-0962(PUBLISHED):The IPSEC implementation in OpenBSD 2.7 does not properly handle empty AH/ESP packets, which allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-952", "line_number": 952, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-953", "filename": "2000.txt", "content": "CVE-2000-0963(PUBLISHED):Buffer overflow in ncurses library allows local users to execute arbitrary commands via long environmental information such as TERM or TERMINFO_DIRS.", "metadata": {"filename": "2000.txt", "chunk_id": "line-953", "line_number": 953, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-954", "filename": "2000.txt", "content": "CVE-2000-0964(PUBLISHED):Buffer overflow in the web administration service for the HiNet LP5100 IP-phone allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-954", "line_number": 954, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-955", "filename": "2000.txt", "content": "CVE-2000-0965(PUBLISHED):The NSAPI plugins for TGA and the Java Servlet proxy in HP-UX VVOS 10.24 and 11.04 allows an attacker to cause a denial of service (high CPU utilization).", "metadata": {"filename": "2000.txt", "chunk_id": "line-955", "line_number": 955, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-956", "filename": "2000.txt", "content": "CVE-2000-0966(PUBLISHED):Buffer overflows in lpspooler in the fileset PrinterMgmt.LP-SPOOL of HP-UX 11.0 and earlier allows local users to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-956", "line_number": 956, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-957", "filename": "2000.txt", "content": "CVE-2000-0967(PUBLISHED):PHP 3 and 4 do not properly cleanse user-injected format strings, which allows remote attackers to execute arbitrary commands by triggering error messages that are improperly written to the error logs.", "metadata": {"filename": "2000.txt", "chunk_id": "line-957", "line_number": 957, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-958", "filename": "2000.txt", "content": "CVE-2000-0968(PUBLISHED):Buffer overflow in Half Life dedicated server before build 3104 allows remote attackers to execute arbitrary commands via a long rcon command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-958", "line_number": 958, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-959", "filename": "2000.txt", "content": "CVE-2000-0969(PUBLISHED):Format string vulnerability in Half Life dedicated server build 3104 and earlier allows remote attackers to execute arbitrary commands by injecting format strings into the changelevel command, via the system console or rcon.", "metadata": {"filename": "2000.txt", "chunk_id": "line-959", "line_number": 959, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-960", "filename": "2000.txt", "content": "CVE-2000-0970(PUBLISHED):IIS 4.0 and 5.0 .ASP pages send the same Session ID cookie for secure and insecure web sessions, which could allow remote attackers to hijack the secure web session of the user if that user moves to an insecure session, aka the \"Session ID Cookie Marking\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-960", "line_number": 960, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-961", "filename": "2000.txt", "content": "CVE-2000-0971(PUBLISHED):Avirt Mail 4.0 and 4.2 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long \"RCPT TO\" or \"MAIL FROM\" command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-961", "line_number": 961, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-962", "filename": "2000.txt", "content": "CVE-2000-0972(PUBLISHED):HP-UX 11.00 crontab allows local users to read arbitrary files via the -e option by creating a symlink to the target file during the crontab session, quitting the session, and reading the error messages that crontab generates.", "metadata": {"filename": "2000.txt", "chunk_id": "line-962", "line_number": 962, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-963", "filename": "2000.txt", "content": "CVE-2000-0973(PUBLISHED):Buffer overflow in curl earlier than 6.0-1.1, and curl-ssl earlier than 6.0-1.2, allows remote attackers to execute arbitrary commands by forcing a long error message to be generated.", "metadata": {"filename": "2000.txt", "chunk_id": "line-963", "line_number": 963, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-964", "filename": "2000.txt", "content": "CVE-2000-0974(PUBLISHED):GnuPG (gpg) 1.0.3 does not properly check all signatures of a file containing multiple documents, which allows an attacker to modify contents of all documents but the first without detection.", "metadata": {"filename": "2000.txt", "chunk_id": "line-964", "line_number": 964, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-965", "filename": "2000.txt", "content": "CVE-2000-0975(PUBLISHED):Directory traversal vulnerability in apexec.pl in Anaconda Foundation Directory allows remote attackers to read arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-965", "line_number": 965, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-966", "filename": "2000.txt", "content": "CVE-2000-0976(PUBLISHED):Buffer overflow in xlib in XFree 3.3.x possibly allows local users to execute arbitrary commands via a long DISPLAY environment variable or a -display command line parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-966", "line_number": 966, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-967", "filename": "2000.txt", "content": "CVE-2000-0977(PUBLISHED):mailfile.cgi CGI program in MailFile 1.10 allows remote attackers to read arbitrary files by specifying the target file name in the \"filename\" parameter in a POST request, which is then sent by email to the address specified in the \"email\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-967", "line_number": 967, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-968", "filename": "2000.txt", "content": "CVE-2000-0978(PUBLISHED):bbd server in Big Brother System and Network Monitor before 1.5c2 allows remote attackers to execute arbitrary commands via the \"&\" shell metacharacter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-968", "line_number": 968, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-969", "filename": "2000.txt", "content": "CVE-2000-0979(PUBLISHED):File and Print Sharing service in Windows 95, Windows 98, and Windows Me does not properly check the password for a file share, which allows remote attackers to bypass share access controls by sending a 1-byte password that matches the first character of the real password, aka the \"Share Level Password\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-969", "line_number": 969, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-970", "filename": "2000.txt", "content": "CVE-2000-0980(PUBLISHED):NMPI (Name Management Protocol on IPX) listener in Microsoft NWLink does not properly filter packets from a broadcast address, which allows remote attackers to cause a broadcast storm and flood the network.", "metadata": {"filename": "2000.txt", "chunk_id": "line-970", "line_number": 970, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-971", "filename": "2000.txt", "content": "CVE-2000-0981(PUBLISHED):MySQL Database Engine uses a weak authentication method which leaks information that could be used by a remote attacker to recover the password.", "metadata": {"filename": "2000.txt", "chunk_id": "line-971", "line_number": 971, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-972", "filename": "2000.txt", "content": "CVE-2000-0982(PUBLISHED):Internet Explorer before 5.5 forwards cached user credentials for a secure web site to insecure pages on the same web site, which could allow remote attackers to obtain the credentials by monitoring connections to the web server, aka the \"Cached Web Credentials\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-972", "line_number": 972, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-973", "filename": "2000.txt", "content": "CVE-2000-0983(PUBLISHED):Microsoft NetMeeting with Remote Desktop Sharing enabled allows remote attackers to cause a denial of service (CPU utilization) via a sequence of null bytes to the NetMeeting port, aka the \"NetMeeting Desktop Sharing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-973", "line_number": 973, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-974", "filename": "2000.txt", "content": "CVE-2000-0984(PUBLISHED):The HTTP server in Cisco IOS 12.0 through 12.1 allows local users to cause a denial of service (crash and reload) via a URL containing a \"?/\" string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-974", "line_number": 974, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-975", "filename": "2000.txt", "content": "CVE-2000-0985(PUBLISHED):Buffer overflow in All-Mail 1.1 allows remote attackers to execute arbitrary commands via a long \"MAIL FROM\" or \"RCPT TO\" command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-975", "line_number": 975, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-976", "filename": "2000.txt", "content": "CVE-2000-0986(PUBLISHED):Buffer overflow in Oracle 8.1.5 applications such as names, namesctl, onrsd, osslogin, tnslsnr, tnsping, trcasst, and trcroute possibly allow local users to gain privileges via a long ORACLE_HOME environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-976", "line_number": 976, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-977", "filename": "2000.txt", "content": "CVE-2000-0987(PUBLISHED):Buffer overflow in oidldapd in Oracle 8.1.6 allow local users to gain privileges via a long \"connect\" command line parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-977", "line_number": 977, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-978", "filename": "2000.txt", "content": "CVE-2000-0988(PUBLISHED):WinU 1.0 through 5.1 has a backdoor password that allows remote attackers to gain access to its administrative interface and modify configuration.", "metadata": {"filename": "2000.txt", "chunk_id": "line-978", "line_number": 978, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-979", "filename": "2000.txt", "content": "CVE-2000-0989(PUBLISHED):Buffer overflow in Intel InBusiness eMail Station 1.04.87 POP service allows remote attackers to cause a denial of service and possibly execute commands via a long username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-979", "line_number": 979, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-980", "filename": "2000.txt", "content": "CVE-2000-0990(PUBLISHED):cmd5checkpw 0.21 and earlier allows remote attackers to cause a denial of service via an \"SMTP AUTH\" command with an unknown username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-980", "line_number": 980, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-981", "filename": "2000.txt", "content": "CVE-2000-0991(PUBLISHED):Buffer overflow in Hilgraeve, Inc. HyperTerminal client on Windows 98, ME, and 2000 allows remote attackers to execute arbitrary commands via a long telnet URL, aka the \"HyperTerminal Buffer Overflow\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-981", "line_number": 981, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-982", "filename": "2000.txt", "content": "CVE-2000-0992(PUBLISHED):Directory traversal vulnerability in scp in sshd 1.2.xx allows a remote malicious scp server to overwrite arbitrary files via a .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-982", "line_number": 982, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-983", "filename": "2000.txt", "content": "CVE-2000-0993(PUBLISHED):Format string vulnerability in pw_error function in BSD libutil library allows local users to gain root privileges via a malformed password in commands such as chpass or passwd.", "metadata": {"filename": "2000.txt", "chunk_id": "line-983", "line_number": 983, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-984", "filename": "2000.txt", "content": "CVE-2000-0994(PUBLISHED):Format string vulnerability in OpenBSD fstat program (and possibly other BSD-based operating systems) allows local users to gain root privileges via the PWD environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-984", "line_number": 984, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-985", "filename": "2000.txt", "content": "CVE-2000-0995(PUBLISHED):Format string vulnerability in OpenBSD yp_passwd program (and possibly other BSD-based operating systems) allows attackers to gain root privileges a malformed name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-985", "line_number": 985, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-986", "filename": "2000.txt", "content": "CVE-2000-0996(PUBLISHED):Format string vulnerability in OpenBSD su program (and possibly other BSD-based operating systems) allows local attackers to gain root privileges via a malformed shell.", "metadata": {"filename": "2000.txt", "chunk_id": "line-986", "line_number": 986, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-987", "filename": "2000.txt", "content": "CVE-2000-0997(PUBLISHED):Format string vulnerabilities in eeprom program in OpenBSD, NetBSD, and possibly other operating systems allows local attackers to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-987", "line_number": 987, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-988", "filename": "2000.txt", "content": "CVE-2000-0998(PUBLISHED):Format string vulnerability in top program allows local attackers to gain root privileges via the \"kill\" or \"renice\" function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-988", "line_number": 988, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-989", "filename": "2000.txt", "content": "CVE-2000-0999(PUBLISHED):Format string vulnerabilities in OpenBSD ssh program (and possibly other BSD-based operating systems) allow attackers to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-989", "line_number": 989, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-990", "filename": "2000.txt", "content": "CVE-2000-1000(PUBLISHED):Format string vulnerability in AOL Instant Messenger (AIM) 4.1.2010 allows remote attackers to cause a denial of service and possibly execute arbitrary commands by transferring a file whose name includes format characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-990", "line_number": 990, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-991", "filename": "2000.txt", "content": "CVE-2000-1001(PUBLISHED):add_2_basket.asp in Element InstantShop allows remote attackers to modify price information via the \"price\" hidden form variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-991", "line_number": 991, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-992", "filename": "2000.txt", "content": "CVE-2000-1002(PUBLISHED):POP3 daemon in Stalker CommuniGate Pro 3.3.2 generates different error messages for invalid usernames versus invalid passwords, which allows remote attackers to determine valid email addresses on the server for SPAM attacks.", "metadata": {"filename": "2000.txt", "chunk_id": "line-992", "line_number": 992, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-993", "filename": "2000.txt", "content": "CVE-2000-1003(PUBLISHED):NETBIOS client in Windows 95 and Windows 98 allows a remote attacker to cause a denial of service by changing a file sharing service to return an unknown driver type, which causes the client to crash.", "metadata": {"filename": "2000.txt", "chunk_id": "line-993", "line_number": 993, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-994", "filename": "2000.txt", "content": "CVE-2000-1004(PUBLISHED):Format string vulnerability in OpenBSD photurisd allows local users to execute arbitrary commands via a configuration file directory name that contains formatting characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-994", "line_number": 994, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-995", "filename": "2000.txt", "content": "CVE-2000-1005(PUBLISHED):Directory traversal vulnerability in html_web_store.cgi and web_store.cgi CGI programs in eXtropia WebStore allows remote attackers to read arbitrary files via a .. (dot dot) attack on the page parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-995", "line_number": 995, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-996", "filename": "2000.txt", "content": "CVE-2000-1006(PUBLISHED):Microsoft Exchange Server 5.5 does not properly handle a MIME header with a blank charset specified, which allows remote attackers to cause a denial of service via a charset=\"\" command, aka the \"Malformed MIME Header\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-996", "line_number": 996, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-997", "filename": "2000.txt", "content": "CVE-2000-1007(PUBLISHED):I-gear 3.5.7 and earlier does not properly process log entries in which a URL is longer than 255 characters, which allows an attacker to cause reporting errors.", "metadata": {"filename": "2000.txt", "chunk_id": "line-997", "line_number": 997, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-998", "filename": "2000.txt", "content": "CVE-2000-1008(PUBLISHED):PalmOS 3.5.2 and earlier uses weak encryption to store the user password, which allows attackers with physical access to the Palm device to decrypt the password and gain access to the device.", "metadata": {"filename": "2000.txt", "chunk_id": "line-998", "line_number": 998, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-999", "filename": "2000.txt", "content": "CVE-2000-1009(PUBLISHED):dump in Red Hat Linux 6.2 trusts the pathname specified by the RSH environmental variable, which allows local users to obtain root privileges by modifying the RSH variable to point to a Trojan horse program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-999", "line_number": 999, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1000", "filename": "2000.txt", "content": "CVE-2000-1010(PUBLISHED):Format string vulnerability in talkd in OpenBSD and possibly other BSD-based OSes allows remote attackers to execute arbitrary commands via a user name that contains format characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1000", "line_number": 1000, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1001", "filename": "2000.txt", "content": "CVE-2000-1011(PUBLISHED):Buffer overflow in catopen() function in FreeBSD 5.0 and earlier, and possibly other OSes, allows local users to gain root privileges via a long environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1001", "line_number": 1001, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1002", "filename": "2000.txt", "content": "CVE-2000-1012(PUBLISHED):The catopen function in FreeBSD 5.0 and earlier, and possibly other OSes, allows local users to read arbitrary files via the LANG environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1002", "line_number": 1002, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1003", "filename": "2000.txt", "content": "CVE-2000-1013(PUBLISHED):The setlocale function in FreeBSD 5.0 and earlier, and possibly other OSes, allows local users to read arbitrary files via the LANG environmental variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1003", "line_number": 1003, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1004", "filename": "2000.txt", "content": "CVE-2000-1014(PUBLISHED):Format string vulnerability in the search97.cgi CGI script in SCO help http server for Unixware 7 allows remote attackers to execute arbitrary commands via format characters in the queryText parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1004", "line_number": 1004, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1005", "filename": "2000.txt", "content": "CVE-2000-1015(PUBLISHED):The default configuration of Slashcode before version 2.0 Alpha has a default administrative password, which allows remote attackers to gain Slashcode privileges and possibly execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1005", "line_number": 1005, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1006", "filename": "2000.txt", "content": "CVE-2000-1016(PUBLISHED):The default configuration of Apache (httpd.conf) on SuSE 6.4 includes an alias for the /usr/doc directory, which allows remote attackers to read package documentation and obtain system configuration information via an HTTP request for the /doc/packages URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1006", "line_number": 1006, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1007", "filename": "2000.txt", "content": "CVE-2000-1017(PUBLISHED):Webteachers Webdata allows remote attackers with valid Webdata accounts to read arbitrary files by posting a request to import the file into the WebData database.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1007", "line_number": 1007, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1008", "filename": "2000.txt", "content": "CVE-2000-1018(PUBLISHED):shred 1.0 file wiping utility does not properly open a file for overwriting or flush its buffers, which prevents shred from properly replacing the file's data and allows local users to recover the file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1008", "line_number": 1008, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1009", "filename": "2000.txt", "content": "CVE-2000-1019(PUBLISHED):Search engine in Ultraseek 3.1 and 3.1.10 (aka Inktomi Search) allows remote attackers to cause a denial of service via a malformed URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1009", "line_number": 1009, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1010", "filename": "2000.txt", "content": "CVE-2000-1020(PUBLISHED):Heap overflow in Worldclient in Mdaemon 3.1.1 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1010", "line_number": 1010, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1011", "filename": "2000.txt", "content": "CVE-2000-1021(PUBLISHED):Heap overflow in WebConfig in Mdaemon 3.1.1 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1011", "line_number": 1011, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1012", "filename": "2000.txt", "content": "CVE-2000-1022(PUBLISHED):The mailguard feature in Cisco Secure PIX Firewall 5.2(2) and earlier does not properly restrict access to SMTP commands, which allows remote attackers to execute restricted commands by sending a DATA command before sending the restricted commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1012", "line_number": 1012, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1013", "filename": "2000.txt", "content": "CVE-2000-1023(PUBLISHED):The Alabanza Control Panel does not require passwords to access administrative commands, which allows remote attackers to modify domain name information via the nsManager.cgi CGI program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1013", "line_number": 1013, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1014", "filename": "2000.txt", "content": "CVE-2000-1024(PUBLISHED):eWave ServletExec 3.0C and earlier does not restrict access to the UploadServlet Java/JSP servlet, which allows remote attackers to upload files and execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1014", "line_number": 1014, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1015", "filename": "2000.txt", "content": "CVE-2000-1025(PUBLISHED):eWave ServletExec JSP/Java servlet engine, versions 3.0C and earlier, allows remote attackers to cause a denial of service via a URL that contains the \"/servlet/\" string, which invokes the ServletExec servlet and causes an exception if the servlet is already running.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1015", "line_number": 1015, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1016", "filename": "2000.txt", "content": "CVE-2000-1026(PUBLISHED):Multiple buffer overflows in LBNL tcpdump allow remote attackers to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1016", "line_number": 1016, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1017", "filename": "2000.txt", "content": "CVE-2000-1027(PUBLISHED):Cisco Secure PIX Firewall 5.2(2) allows remote attackers to determine the real IP address of a target FTP server by flooding the server with PASV requests, which includes the real IP address in the response when passive mode is established.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1017", "line_number": 1017, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1018", "filename": "2000.txt", "content": "CVE-2000-1028(PUBLISHED):Buffer overflow in cu program in HP-UX 11.0 may allow local users to gain privileges via a long -l command line argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1018", "line_number": 1018, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1019", "filename": "2000.txt", "content": "CVE-2000-1029(PUBLISHED):Buffer overflow in host command allows a remote attacker to execute arbitrary commands via a long response to an AXFR query.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1019", "line_number": 1019, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1020", "filename": "2000.txt", "content": "CVE-2000-1030(PUBLISHED):CS&T CorporateTime for the Web returns different error messages for invalid usernames and invalid passwords, which allows remote attackers to determine valid usernames on the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1020", "line_number": 1020, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1021", "filename": "2000.txt", "content": "CVE-2000-1031(PUBLISHED):Buffer overflow in dtterm in HP-UX 11.0 and HP Tru64 UNIX 4.0f through 5.1a allows local users to execute arbitrary code via a long -tn option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1021", "line_number": 1021, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1022", "filename": "2000.txt", "content": "CVE-2000-1032(PUBLISHED):The client authentication interface for Check Point Firewall-1 4.0 and earlier generates different error messages for invalid usernames versus invalid passwords, which allows remote attackers to identify valid usernames on the firewall.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1022", "line_number": 1022, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1023", "filename": "2000.txt", "content": "CVE-2000-1033(PUBLISHED):Serv-U FTP Server allows remote attackers to bypass its anti-hammering feature by first logging on as a valid user (possibly anonymous) and then attempting to guess the passwords of other users.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1023", "line_number": 1023, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1024", "filename": "2000.txt", "content": "CVE-2000-1034(PUBLISHED):Buffer overflow in the System Monitor ActiveX control in Windows 2000 allows remote attackers to execute arbitrary commands via a long LogFileName parameter in HTML source code, aka the \"ActiveX Parameter Validation\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1024", "line_number": 1024, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1025", "filename": "2000.txt", "content": "CVE-2000-1035(PUBLISHED):Buffer overflows in TYPSoft FTP Server 0.78 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long USER, PASS, or CWD command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1025", "line_number": 1025, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1026", "filename": "2000.txt", "content": "CVE-2000-1036(PUBLISHED):Directory traversal vulnerability in Extent RBS ISP web server allows remote attackers to read sensitive information via a .. (dot dot) attack on the Image parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1026", "line_number": 1026, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1027", "filename": "2000.txt", "content": "CVE-2000-1037(PUBLISHED):Check Point Firewall-1 session agent 3.0 through 4.1 generates different error messages for invalid user names versus invalid passwords, which allows remote attackers to determine valid usernames and guess a password via a brute force attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1027", "line_number": 1027, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1028", "filename": "2000.txt", "content": "CVE-2000-1038(PUBLISHED):The web administration interface for IBM AS/400 Firewall allows remote attackers to cause a denial of service via an empty GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1028", "line_number": 1028, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1029", "filename": "2000.txt", "content": "CVE-2000-1039(PUBLISHED):Various TCP/IP stacks and network applications allow remote attackers to cause a denial of service by flooding a target host with TCP connection attempts and completing the TCP/IP handshake without maintaining the connection state on the attacker host, aka the \"NAPTHA\" class of vulnerabilities.  NOTE: this candidate may change significantly as the security community discusses the technical nature of NAPTHA and learns more about the affected applications. This candidate is at a higher level of abstraction than is typical for CVE.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1029", "line_number": 1029, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1030", "filename": "2000.txt", "content": "CVE-2000-1040(PUBLISHED):Format string vulnerability in logging function of ypbind 3.3, while running in debug mode, leaks file descriptors and allows an attacker to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1030", "line_number": 1030, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1031", "filename": "2000.txt", "content": "CVE-2000-1041(PUBLISHED):Buffer overflow in ypbind 3.3 possibly allows an attacker to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1031", "line_number": 1031, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1032", "filename": "2000.txt", "content": "CVE-2000-1042(PUBLISHED):Buffer overflow in ypserv in Mandrake Linux 7.1 and earlier, and possibly other Linux operating systems, allows an attacker to gain root privileges when ypserv is built without a vsyslog() function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1032", "line_number": 1032, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1033", "filename": "2000.txt", "content": "CVE-2000-1043(PUBLISHED):Format string vulnerability in ypserv in Mandrake Linux 7.1 and earlier, and possibly other Linux operating systems, allows an attacker to gain root privileges when ypserv is built without a vsyslog() function.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1033", "line_number": 1033, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1034", "filename": "2000.txt", "content": "CVE-2000-1044(PUBLISHED):Format string vulnerability in ypbind-mt in SuSE SuSE-6.2, and possibly other Linux operating systems, allows an attacker to gain root privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1034", "line_number": 1034, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1035", "filename": "2000.txt", "content": "CVE-2000-1045(PUBLISHED):nss_ldap earlier than 121, when run with nscd (name service caching daemon), allows remote attackers to cause a denial of service via a flood of LDAP requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1035", "line_number": 1035, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1036", "filename": "2000.txt", "content": "CVE-2000-1046(PUBLISHED):Multiple buffer overflows in the ESMTP service of Lotus Domino 5.0.2c and earlier allow remote attackers to cause a denial of service and possibly execute arbitrary code via long (1) \"RCPT TO,\" (2) \"SAML FROM,\" or (3) \"SOML FROM\" commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1036", "line_number": 1036, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1037", "filename": "2000.txt", "content": "CVE-2000-1047(PUBLISHED):Buffer overflow in SMTP service of Lotus Domino 5.0.4 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long ENVID keyword in the \"MAIL FROM\" command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1037", "line_number": 1037, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1038", "filename": "2000.txt", "content": "CVE-2000-1048(PUBLISHED):Directory traversal vulnerability in the logfile service of Wingate 4.1 Beta A and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack via an HTTP GET request that uses encoded characters in the URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1038", "line_number": 1038, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1039", "filename": "2000.txt", "content": "CVE-2000-1049(PUBLISHED):Allaire JRun 3.0 http servlet server allows remote attackers to cause a denial of service via a URL that contains a long string of \".\" characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1039", "line_number": 1039, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1040", "filename": "2000.txt", "content": "CVE-2000-1050(PUBLISHED):Allaire JRun 3.0 http servlet server allows remote attackers to directly access the WEB-INF directory via a URL request that contains an extra \"/\" in the beginning of the request (aka the \"extra leading slash\").", "metadata": {"filename": "2000.txt", "chunk_id": "line-1040", "line_number": 1040, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1041", "filename": "2000.txt", "content": "CVE-2000-1051(PUBLISHED):Directory traversal vulnerability in Allaire JRun 2.3 server allows remote attackers to read arbitrary files via the SSIFilter servlet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1041", "line_number": 1041, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1042", "filename": "2000.txt", "content": "CVE-2000-1052(PUBLISHED):Allaire JRun 2.3 server allows remote attackers to obtain source code for executable content by directly calling the SSIFilter servlet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1042", "line_number": 1042, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1043", "filename": "2000.txt", "content": "CVE-2000-1053(PUBLISHED):Allaire JRun 2.3.3 server allows remote attackers to compile and execute JSP code by inserting it via a cross-site scripting (CSS) attack and directly calling the com.livesoftware.jrun.plugins.JSP JSP servlet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1043", "line_number": 1043, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1044", "filename": "2000.txt", "content": "CVE-2000-1054(PUBLISHED):Buffer overflow in CSAdmin module in CiscoSecure ACS Server 2.4(2) and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a large packet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1044", "line_number": 1044, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1045", "filename": "2000.txt", "content": "CVE-2000-1055(PUBLISHED):Buffer overflow in CiscoSecure ACS Server 2.4(2) and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a large TACACS+ packet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1045", "line_number": 1045, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1046", "filename": "2000.txt", "content": "CVE-2000-1056(PUBLISHED):CiscoSecure ACS Server 2.4(2) and earlier allows remote attackers to bypass LDAP authentication on the server if the LDAP server allows null passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1046", "line_number": 1046, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1047", "filename": "2000.txt", "content": "CVE-2000-1057(PUBLISHED):Vulnerabilities in database configuration scripts in HP OpenView Network Node Manager (NNM) 6.1 and earlier allows local users to gain privileges, possibly via insecure permissions.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1047", "line_number": 1047, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1048", "filename": "2000.txt", "content": "CVE-2000-1058(PUBLISHED):Buffer overflow in OverView5 CGI program in HP OpenView Network Node Manager (NNM) 6.1 and earlier allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, in the SNMP service (snmp.exe), aka the \"Java SNMP MIB Browser Object ID parsing problem.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-1048", "line_number": 1048, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1049", "filename": "2000.txt", "content": "CVE-2000-1059(PUBLISHED):The default configuration of the Xsession file in Mandrake Linux 7.1 and 7.0 bypasses the Xauthority access control mechanism with an \"xhost + localhost\" command, which allows local users to sniff X Windows events and gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1049", "line_number": 1049, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1050", "filename": "2000.txt", "content": "CVE-2000-1060(PUBLISHED):The default configuration of XFCE 3.5.1 bypasses the Xauthority access control mechanism with an \"xhost + localhost\" command in the xinitrc program, which allows local users to sniff X Windows traffic and gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1050", "line_number": 1050, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1051", "filename": "2000.txt", "content": "CVE-2000-1061(PUBLISHED):Microsoft Virtual Machine (VM) in Internet Explorer 4.x and 5.x allows an unsigned applet to create and use ActiveX controls, which allows a remote attacker to bypass Internet Explorer's security settings and execute arbitrary commands via a malicious web page or email, aka the \"Microsoft VM ActiveX Component\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1051", "line_number": 1051, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1052", "filename": "2000.txt", "content": "CVE-2000-1062(PUBLISHED):Buffer overflow in the FTP service in HP JetDirect printer card Firmware x.08.20 and earlier allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1052", "line_number": 1052, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1053", "filename": "2000.txt", "content": "CVE-2000-1063(PUBLISHED):Buffer overflow in the Telnet service in HP JetDirect printer card Firmware x.08.20 and earlier allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1053", "line_number": 1053, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1054", "filename": "2000.txt", "content": "CVE-2000-1064(PUBLISHED):Buffer overflow in the LPD service in HP JetDirect printer card Firmware x.08.20 and earlier allows remote attackers to cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1054", "line_number": 1054, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1055", "filename": "2000.txt", "content": "CVE-2000-1065(PUBLISHED):Vulnerability in IP implementation of HP JetDirect printer card Firmware x.08.20 and earlier allows remote attackers to cause a denial of service (printer crash) via a malformed packet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1055", "line_number": 1055, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1056", "filename": "2000.txt", "content": "CVE-2000-1066(PUBLISHED):The getnameinfo function in FreeBSD 4.1.1 and earlier, and possibly other operating systems, allows a remote attacker to cause a denial of service via a long DNS hostname.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1056", "line_number": 1056, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1057", "filename": "2000.txt", "content": "CVE-2000-1068(PUBLISHED):pollit.cgi in Poll It 2.0 allows remote attackers to execute arbitrary commands via shell metacharacters in the poll_options parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1057", "line_number": 1057, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1058", "filename": "2000.txt", "content": "CVE-2000-1069(PUBLISHED):pollit.cgi in Poll It 2.01 and earlier allows remote attackers to access administrative functions without knowing the real password by specifying the same value to the entered_password and admin_password parameters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1058", "line_number": 1058, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1059", "filename": "2000.txt", "content": "CVE-2000-1070(PUBLISHED):pollit.cgi in Poll It 2.01 and earlier uses data files that are located under the web document root, which allows remote attackers to access sensitive or private information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1059", "line_number": 1059, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1060", "filename": "2000.txt", "content": "CVE-2000-1071(PUBLISHED):The GUI installation for iCal 2.1 Patch 2 disables access control for the X server using an \"xhost +\" command, which allows remote attackers to monitor X Windows events and gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1060", "line_number": 1060, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1061", "filename": "2000.txt", "content": "CVE-2000-1072(PUBLISHED):iCal 2.1 Patch 2 installs many files with world-writeable permissions, which allows local users to modify the iCal configuration and execute arbitrary commands by replacing the iplncal.sh program with a Trojan horse.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1061", "line_number": 1061, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1062", "filename": "2000.txt", "content": "CVE-2000-1073(PUBLISHED):csstart program in iCal 2.1 Patch 2 searches for the cshttpd program in the current working directory, which allows local users to gain root privileges by creating a Trojan Horse cshttpd program in a directory and calling csstart from that directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1062", "line_number": 1062, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1063", "filename": "2000.txt", "content": "CVE-2000-1074(PUBLISHED):csstart program in iCal 2.1 Patch 2 uses relative pathnames to install the libsocket and libnsl libraries, which could allow the icsuser account to gain root privileges by creating a Trojan Horse library in the current or parent directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1063", "line_number": 1063, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1064", "filename": "2000.txt", "content": "CVE-2000-1075(PUBLISHED):Directory traversal vulnerability in iPlanet Certificate Management System 4.2 and Directory Server 4.12 allows remote attackers to read arbitrary files via a .. (dot dot) attack in the Agent, End Entity, or Administrator services.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1064", "line_number": 1064, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1065", "filename": "2000.txt", "content": "CVE-2000-1076(PUBLISHED):Netscape (iPlanet) Certificate Management System 4.2 and Directory Server 4.12 stores the administrative password in plaintext, which could allow local and possibly remote attackers to gain administrative privileges on the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1065", "line_number": 1065, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1066", "filename": "2000.txt", "content": "CVE-2000-1077(PUBLISHED):Buffer overflow in the SHTML logging functionality of iPlanet Web Server 4.x allows remote attackers to execute arbitrary commands via a long filename with a .shtml extension.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1066", "line_number": 1066, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1067", "filename": "2000.txt", "content": "CVE-2000-1078(PUBLISHED):ICQ Web Front HTTPd allows remote attackers to cause a denial of service by requesting a URL that contains a \"?\" character.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1067", "line_number": 1067, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1068", "filename": "2000.txt", "content": "CVE-2000-1079(PUBLISHED):Interactions between the CIFS Browser Protocol and NetBIOS as implemented in Microsoft Windows 95, 98, NT, and 2000 allow remote attackers to modify dynamic NetBIOS name cache entries via a spoofed Browse Frame Request in a unicast or UDP broadcast datagram.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1068", "line_number": 1068, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1069", "filename": "2000.txt", "content": "CVE-2000-1080(PUBLISHED):Quake 1 (quake1) and ProQuake 1.01 and earlier allow remote attackers to cause a denial of service via a malformed (empty) UDP packet.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1069", "line_number": 1069, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1070", "filename": "2000.txt", "content": "CVE-2000-1081(PUBLISHED):The xp_displayparamstmt function in SQL Server and Microsoft SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the \"Extended Stored Procedure Parameter Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1070", "line_number": 1070, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1071", "filename": "2000.txt", "content": "CVE-2000-1082(PUBLISHED):The xp_enumresultset function in SQL Server and Microsoft SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the \"Extended Stored Procedure Parameter Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1071", "line_number": 1071, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1072", "filename": "2000.txt", "content": "CVE-2000-1083(PUBLISHED):The xp_showcolv function in SQL Server and Microsoft SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the \"Extended Stored Procedure Parameter Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1072", "line_number": 1072, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1073", "filename": "2000.txt", "content": "CVE-2000-1084(PUBLISHED):The xp_updatecolvbm function in SQL Server and Microsoft SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the \"Extended Stored Procedure Parameter Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1073", "line_number": 1073, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1074", "filename": "2000.txt", "content": "CVE-2000-1085(PUBLISHED):The xp_peekqueue function in Microsoft SQL Server 2000 and SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the \"Extended Stored Procedure Parameter Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1074", "line_number": 1074, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1075", "filename": "2000.txt", "content": "CVE-2000-1086(PUBLISHED):The xp_printstatements function in Microsoft SQL Server 2000 and SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the \"Extended Stored Procedure Parameter Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1075", "line_number": 1075, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1076", "filename": "2000.txt", "content": "CVE-2000-1087(PUBLISHED):The xp_proxiedmetadata function in Microsoft SQL Server 2000 and SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the \"Extended Stored Procedure Parameter Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1076", "line_number": 1076, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1077", "filename": "2000.txt", "content": "CVE-2000-1088(PUBLISHED):The xp_SetSQLSecurity function in Microsoft SQL Server 2000 and SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the \"Extended Stored Procedure Parameter Parsing\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1077", "line_number": 1077, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1078", "filename": "2000.txt", "content": "CVE-2000-1089(PUBLISHED):Buffer overflow in Microsoft Phone Book Service allows local users to execute arbitrary commands, aka the \"Phone Book Service Buffer Overflow\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1078", "line_number": 1078, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1079", "filename": "2000.txt", "content": "CVE-2000-1090(PUBLISHED):Microsoft IIS for Far East editions 4.0 and 5.0 allows remote attackers to read source code for parsed pages via a malformed URL that uses the lead-byte of a double-byte character.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1079", "line_number": 1079, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1080", "filename": "2000.txt", "content": "CVE-2000-1092(PUBLISHED):loadpage.cgi CGI program in EZshopper 3.0 and 2.0 allows remote attackers to list and read files in the EZshopper data directory by inserting a \"/\" in front of the target filename in the \"file\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1080", "line_number": 1080, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1081", "filename": "2000.txt", "content": "CVE-2000-1093(PUBLISHED):Buffer overflow in AOL Instant Messenger before 4.3.2229 allows remote attackers to execute arbitrary commands via a long \"goim\" command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1081", "line_number": 1081, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1082", "filename": "2000.txt", "content": "CVE-2000-1094(PUBLISHED):Buffer overflow in AOL Instant Messenger (AIM) before 4.3.2229 allows remote attackers to execute arbitrary commands via a \"buddyicon\" command with a long \"src\" argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1082", "line_number": 1082, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1083", "filename": "2000.txt", "content": "CVE-2000-1095(PUBLISHED):modprobe in the modutils 2.3.x package on Linux systems allows a local user to execute arbitrary commands via shell metacharacters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1083", "line_number": 1083, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1084", "filename": "2000.txt", "content": "CVE-2000-1096(PUBLISHED):crontab by Paul Vixie uses predictable file names for a temporary file and does not properly ensure that the file is owned by the user executing the crontab -e command, which allows local users with write access to the crontab spool directory to execute arbitrary commands by creating world-writeable temporary files and modifying them while the victim is editing the file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1084", "line_number": 1084, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1085", "filename": "2000.txt", "content": "CVE-2000-1097(PUBLISHED):The web server for the SonicWALL SOHO firewall allows remote attackers to cause a denial of service via a long username in the authentication page.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1085", "line_number": 1085, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1086", "filename": "2000.txt", "content": "CVE-2000-1098(PUBLISHED):The web server for the SonicWALL SOHO firewall allows remote attackers to cause a denial of service via an empty GET or POST request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1086", "line_number": 1086, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1087", "filename": "2000.txt", "content": "CVE-2000-1099(PUBLISHED):Java Runtime Environment in Java Development Kit (JDK) 1.2.2_05 and earlier can allow an untrusted Java class to call into a disallowed class, which could allow an attacker to escape the Java sandbox and conduct unauthorized activities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1087", "line_number": 1087, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1088", "filename": "2000.txt", "content": "CVE-2000-1100(PUBLISHED):The default configuration for PostACI webmail system installs the /includes/global.inc configuration file within the web root, which allows remote attackers to read sensitive information such as database usernames and passwords via a direct HTTP GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1088", "line_number": 1088, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1089", "filename": "2000.txt", "content": "CVE-2000-1101(PUBLISHED):Directory traversal vulnerability in Winsock FTPd (WFTPD) 3.00 and 2.41 with the \"Restrict to home directory\" option enabled allows local users to escape the home directory via a \"/../\" string, a variation of the .. (dot dot) attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1089", "line_number": 1089, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1090", "filename": "2000.txt", "content": "CVE-2000-1102(PUBLISHED):PTlink IRCD 3.5.3 and PTlink Services 1.8.1 allow remote attackers to cause a denial of service (server crash) via \"mode +owgscfxeb\" and \"oper\" commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1090", "line_number": 1090, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1091", "filename": "2000.txt", "content": "CVE-2000-1103(PUBLISHED):rcvtty in BSD 3.0 and 4.0 does not properly drop privileges before executing a script, which allows local attackers to gain privileges by specifying an alternate Trojan horse script on the command line.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1091", "line_number": 1091, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1092", "filename": "2000.txt", "content": "CVE-2000-1104(PUBLISHED):Variant of the \"IIS Cross-Site Scripting\" vulnerability as originally discussed in MS:MS00-060 (CVE-2000-0746) allows a malicious web site operator to embed scripts in a link to a trusted site, which are returned without quoting in an error message back to the client.  The client then executes those scripts in the same context as the trusted site.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1092", "line_number": 1092, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1093", "filename": "2000.txt", "content": "CVE-2000-1105(PUBLISHED):The ixsso.query ActiveX Object is marked as safe for scripting, which allows malicious web site operators to embed a script that remotely determines the existence of files on visiting Windows 2000 systems that have Indexing Services enabled.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1093", "line_number": 1093, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1094", "filename": "2000.txt", "content": "CVE-2000-1106(PUBLISHED):Trend Micro InterScan VirusWall creates an \"Intscan\" share to the \"InterScan\" directory with permissions that grant Full Control permissions to the Everyone group, which allows attackers to gain privileges by modifying the VirusWall programs.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1094", "line_number": 1094, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1095", "filename": "2000.txt", "content": "CVE-2000-1107(PUBLISHED):in.identd ident server in SuSE Linux 6.x and 7.0 allows remote attackers to cause a denial of service via a long request, which causes the server to access a NULL pointer and crash.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1095", "line_number": 1095, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1096", "filename": "2000.txt", "content": "CVE-2000-1108(PUBLISHED):cons.saver in Midnight Commander (mc) 4.5.42 and earlier does not properly verify if an output file descriptor is a TTY, which allows local users to corrupt files by creating a symbolic link to the target file, calling mc, and specifying that link as a TTY argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1096", "line_number": 1096, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1097", "filename": "2000.txt", "content": "CVE-2000-1109(PUBLISHED):Midnight Commander (mc) 4.5.51 and earlier does not properly process malformed directory names when a user opens a directory, which allows other local users to gain privileges by creating directories that contain special characters followed by the commands to be executed.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1097", "line_number": 1097, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1098", "filename": "2000.txt", "content": "CVE-2000-1110(PUBLISHED):document.d2w CGI program in the IBM Net.Data db2www package allows remote attackers to determine the physical path of the web server by sending a nonexistent command to the program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1098", "line_number": 1098, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1099", "filename": "2000.txt", "content": "CVE-2000-1111(PUBLISHED):Telnet Service for Windows 2000 Professional does not properly terminate incomplete connection attempts, which allows remote attackers to cause a denial of service by connecting to the server and not providing any input.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1099", "line_number": 1099, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1100", "filename": "2000.txt", "content": "CVE-2000-1112(PUBLISHED):Microsoft Windows Media Player 7 executes scripts in custom skin (.WMS) files, which could allow remote attackers to gain privileges via a skin that contains a malicious script, aka the \".WMS Script Execution\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1100", "line_number": 1100, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1101", "filename": "2000.txt", "content": "CVE-2000-1113(PUBLISHED):Buffer overflow in Microsoft Windows Media Player allows remote attackers to execute arbitrary commands via a malformed Active Stream Redirector (.ASX) file, aka the \".ASX Buffer Overrun\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1101", "line_number": 1101, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1102", "filename": "2000.txt", "content": "CVE-2000-1114(PUBLISHED):Unify ServletExec AS v3.0C allows remote attackers to read source code for JSP pages via an HTTP request that ends with characters such as \".\", or \"+\", or \"%20\".", "metadata": {"filename": "2000.txt", "chunk_id": "line-1102", "line_number": 1102, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1103", "filename": "2000.txt", "content": "CVE-2000-1115(PUBLISHED):Buffer overflow in remote web administration component (webprox.dll) of 602Pro LAN SUITE before 2000.0.1.33 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1103", "line_number": 1103, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1104", "filename": "2000.txt", "content": "CVE-2000-1116(PUBLISHED):Buffer overflow in TransSoft Broker FTP Server before 4.3.0.1 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1104", "line_number": 1104, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1105", "filename": "2000.txt", "content": "CVE-2000-1117(PUBLISHED):The Extended Control List (ECL) feature of the Java Virtual Machine (JVM) in Lotus Notes Client R5 allows malicious web site operators to determine the existence of files on the client by measuring delays in the execution of the getSystemResource method.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1105", "line_number": 1105, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1106", "filename": "2000.txt", "content": "CVE-2000-1118(PUBLISHED):24Link 1.06 web server allows remote attackers to bypass access restrictions by prepending strings such as \"/+/\" or \"/.\" to the HTTP GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1106", "line_number": 1106, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1107", "filename": "2000.txt", "content": "CVE-2000-1119(PUBLISHED):Buffer overflow in setsenv command in IBM AIX 4.3.x and earlier allows local users to execute arbitrary commands via a long \"x=\" argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1107", "line_number": 1107, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1108", "filename": "2000.txt", "content": "CVE-2000-1120(PUBLISHED):Buffer overflow in digest command in IBM AIX 4.3.x and earlier allows local users to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1108", "line_number": 1108, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1109", "filename": "2000.txt", "content": "CVE-2000-1121(PUBLISHED):Buffer overflow in enq command in IBM AIX 4.3.x and earlier may allow local users to execute arbitrary commands via a long -M argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1109", "line_number": 1109, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1110", "filename": "2000.txt", "content": "CVE-2000-1122(PUBLISHED):Buffer overflow in setclock command in IBM AIX 4.3.x and earlier may allow local users to execute arbitrary commands via a long argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1110", "line_number": 1110, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1111", "filename": "2000.txt", "content": "CVE-2000-1123(PUBLISHED):Buffer overflow in pioout command in IBM AIX 4.3.x and earlier may allow local users to execute arbitrary commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1111", "line_number": 1111, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1112", "filename": "2000.txt", "content": "CVE-2000-1124(PUBLISHED):Buffer overflow in piobe command in IBM AIX 4.3.x allows local users to gain privileges via long environmental variables.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1112", "line_number": 1112, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1113", "filename": "2000.txt", "content": "CVE-2000-1125(PUBLISHED):restore 0.4b15 and earlier in Red Hat Linux 6.2 trusts the pathname specified by the RSH environmental variable, which allows local users to obtain root privileges by modifying the RSH variable to point to a Trojan horse program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1113", "line_number": 1113, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1114", "filename": "2000.txt", "content": "CVE-2000-1126(PUBLISHED):Vulnerability in auto_parms and set_parms in HP-UX 11.00 and earlier allows remote attackers to execute arbitrary commands or cause a denial of service.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1114", "line_number": 1114, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1115", "filename": "2000.txt", "content": "CVE-2000-1127(PUBLISHED):registrar in the HP resource monitor service allows local users to read and modify arbitrary files by renaming the original registrar.log log file and creating a symbolic link to the target file, to which registrar appends log information and sets the permissions to be world readable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1115", "line_number": 1115, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1116", "filename": "2000.txt", "content": "CVE-2000-1128(PUBLISHED):The default configuration of McAfee VirusScan 4.5 does not quote the ImagePath variable, which improperly sets the search path and allows local users to place a Trojan horse \"common.exe\" program in the C:\\Program Files directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1116", "line_number": 1116, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1117", "filename": "2000.txt", "content": "CVE-2000-1129(PUBLISHED):McAfee WebShield SMTP 4.5 allows remote attackers to cause a denial of service via a malformed recipient field.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1117", "line_number": 1117, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1118", "filename": "2000.txt", "content": "CVE-2000-1130(PUBLISHED):McAfee WebShield SMTP 4.5 allows remote attackers to bypass email content filtering rules by including Extended ASCII characters in name of the attachment.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1118", "line_number": 1118, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1119", "filename": "2000.txt", "content": "CVE-2000-1131(PUBLISHED):Bill Kendrick web site guestbook (GBook) allows remote attackers to execute arbitrary commands via shell metacharacters in the _MAILTO form variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1119", "line_number": 1119, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1120", "filename": "2000.txt", "content": "CVE-2000-1132(PUBLISHED):DCForum cgforum.cgi CGI script allows remote attackers to read arbitrary files, and delete the program itself, via a malformed \"forum\" variable.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1120", "line_number": 1120, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1121", "filename": "2000.txt", "content": "CVE-2000-1133(PUBLISHED):Authentix Authentix100 allows remote attackers to bypass authentication by inserting a . (dot) into the URL for a protected directory.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1121", "line_number": 1121, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1122", "filename": "2000.txt", "content": "CVE-2000-1134(PUBLISHED):Multiple shell programs on various Unix systems, including (1) tcsh, (2) csh, (3) sh, and (4) bash, follow symlinks when processing << redirects (aka here-documents or in-here documents), which allows local users to overwrite files of other users via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1122", "line_number": 1122, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1123", "filename": "2000.txt", "content": "CVE-2000-1135(PUBLISHED):fshd (fsh daemon) in Debian GNU/Linux allows local users to overwrite files of other users via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1123", "line_number": 1123, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1124", "filename": "2000.txt", "content": "CVE-2000-1136(PUBLISHED):elvis-tiny before 1.4-10 in Debian GNU/Linux, and possibly other Linux operating systems, allows local users to overwrite files of other users via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1124", "line_number": 1124, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1125", "filename": "2000.txt", "content": "CVE-2000-1137(PUBLISHED):GNU ed before 0.2-18.1 allows local users to overwrite the files of other users via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1125", "line_number": 1125, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1126", "filename": "2000.txt", "content": "CVE-2000-1138(PUBLISHED):Lotus Notes R5 client R5.0.5 and earlier does not properly warn users when an S/MIME email message has been modified, which could allow an attacker to modify the email in transit without being detected.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1126", "line_number": 1126, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1127", "filename": "2000.txt", "content": "CVE-2000-1139(PUBLISHED):The installation of Microsoft Exchange 2000 before Rev. A creates a user account with a known password, which could allow attackers to gain privileges, aka the \"Exchange User Account\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1127", "line_number": 1127, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1128", "filename": "2000.txt", "content": "CVE-2000-1140(PUBLISHED):Recourse ManTrap 1.6 does not properly hide processes from attackers, which could allow attackers to determine that they are in a honeypot system by comparing the results from kill commands with the process listing in the /proc filesystem.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1128", "line_number": 1128, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1129", "filename": "2000.txt", "content": "CVE-2000-1141(PUBLISHED):Recourse ManTrap 1.6 modifies the kernel so that \"..\" does not appear in the /proc listing, which allows attackers to determine that they are in a honeypot system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1129", "line_number": 1129, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1130", "filename": "2000.txt", "content": "CVE-2000-1142(PUBLISHED):Recourse ManTrap 1.6 generates an error when an attacker cd's to /proc/self/cwd and executes the pwd command, which allows attackers to determine that they are in a honeypot system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1130", "line_number": 1130, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1131", "filename": "2000.txt", "content": "CVE-2000-1143(PUBLISHED):Recourse ManTrap 1.6 hides the first 4 processes that run on a Solaris system, which allows attackers to determine that they are in a honeypot system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1131", "line_number": 1131, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1132", "filename": "2000.txt", "content": "CVE-2000-1144(PUBLISHED):Recourse ManTrap 1.6 sets up a chroot environment to hide the fact that it is running, but the inode number for the resulting \"/\" file system is higher than normal, which allows attackers to determine that they are in a chroot environment.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1132", "line_number": 1132, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1133", "filename": "2000.txt", "content": "CVE-2000-1145(PUBLISHED):Recourse ManTrap 1.6 allows attackers who have gained root access to use utilities such as crash or fsdb to read /dev/mem and raw disk devices to identify ManTrap processes or modify arbitrary data files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1133", "line_number": 1133, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1134", "filename": "2000.txt", "content": "CVE-2000-1146(PUBLISHED):Recourse ManTrap 1.6 allows attackers to cause a denial of service via a sequence of commands that navigate into and out of the /proc/self directory and executing various commands such as ls or pwd.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1134", "line_number": 1134, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1135", "filename": "2000.txt", "content": "CVE-2000-1147(PUBLISHED):Buffer overflow in IIS ISAPI .ASP parsing mechanism allows attackers to execute arbitrary commands via a long string to the \"LANGUAGE\" argument in a script tag.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1135", "line_number": 1135, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1136", "filename": "2000.txt", "content": "CVE-2000-1148(PUBLISHED):The installation of VolanoChatPro chat server sets world-readable permissions for its configuration file and stores the server administrator passwords in plaintext, which allows local users to gain privileges on the server.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1136", "line_number": 1136, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1137", "filename": "2000.txt", "content": "CVE-2000-1149(PUBLISHED):Buffer overflow in RegAPI.DLL used by Windows NT 4.0 Terminal Server allows remote attackers to execute arbitrary commands via a long username, aka the \"Terminal Server Login Buffer Overflow\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1137", "line_number": 1137, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1138", "filename": "2000.txt", "content": "CVE-2000-1150(PUBLISHED):Felix IRC client in BeOS r5 pro and earlier allows remote attackers to conduct a denial of service via a message that contains a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1138", "line_number": 1138, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1139", "filename": "2000.txt", "content": "CVE-2000-1151(PUBLISHED):Baxter IRC client in BeOS r5 pro and earlier allows remote attackers to conduct a denial of service via a message that contains a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1139", "line_number": 1139, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1140", "filename": "2000.txt", "content": "CVE-2000-1152(PUBLISHED):Browser IRC client in BeOS r5 pro and earlier allows remote attackers to conduct a denial of service via a message that contains a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1140", "line_number": 1140, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1141", "filename": "2000.txt", "content": "CVE-2000-1153(PUBLISHED):PostMaster 1.0 in BeOS r5 pro and earlier allows remote attackers to conduct a denial of service via a message that contains a long URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1141", "line_number": 1141, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1142", "filename": "2000.txt", "content": "CVE-2000-1154(PUBLISHED):RHConsole in RobinHood 1.1 web server in BeOS r5 pro and earlier allows remote attackers to cause a denial of service via long HTTP request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1142", "line_number": 1142, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1143", "filename": "2000.txt", "content": "CVE-2000-1155(PUBLISHED):RHDaemon in RobinHood 1.1 web server in BeOS r5 pro and earlier allows remote attackers to cause a denial of service via long HTTP request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1143", "line_number": 1143, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1144", "filename": "2000.txt", "content": "CVE-2000-1156(PUBLISHED):StarOffice 5.2 follows symlinks and sets world-readable permissions for the /tmp/soffice.tmp directory, which allows a local user to read files of the user who is using StarOffice.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1144", "line_number": 1144, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1145", "filename": "2000.txt", "content": "CVE-2000-1157(PUBLISHED):Buffer overflow in NAI Sniffer Agent allows remote attackers to execute arbitrary commands via a long SNMP community name.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1145", "line_number": 1145, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1146", "filename": "2000.txt", "content": "CVE-2000-1158(PUBLISHED):NAI Sniffer Agent uses base64 encoding for authentication, which allows attackers to sniff the network and easily decrypt usernames and passwords.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1146", "line_number": 1146, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1147", "filename": "2000.txt", "content": "CVE-2000-1159(PUBLISHED):NAI Sniffer Agent allows remote attackers to gain privileges on the agent by sniffing the initial UDP authentication packets and spoofing commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1147", "line_number": 1147, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1148", "filename": "2000.txt", "content": "CVE-2000-1160(PUBLISHED):NAI Sniffer Agent allows remote attackers to cause a denial of service (crash) by sending a large number of login requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1148", "line_number": 1148, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1149", "filename": "2000.txt", "content": "CVE-2000-1161(PUBLISHED):The installation of AdCycle banner management system leaves the build.cgi program in a web-accessible directory, which allows remote attackers to execute the program and view passwords or delete databases.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1149", "line_number": 1149, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1150", "filename": "2000.txt", "content": "CVE-2000-1162(PUBLISHED):ghostscript before 5.10-16 allows local users to overwrite files of other users via a symlink attack.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1150", "line_number": 1150, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1151", "filename": "2000.txt", "content": "CVE-2000-1163(PUBLISHED):ghostscript before 5.10-16 uses an empty LD_RUN_PATH environmental variable to find libraries in the current directory, which could allow local users to execute commands as other users by placing a Trojan horse library into a directory from which another user executes ghostscript.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1151", "line_number": 1151, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1152", "filename": "2000.txt", "content": "CVE-2000-1164(PUBLISHED):WinVNC installs the WinVNC3 registry key with permissions that give Special Access (read and modify) to the Everybody group, which allows users to read and modify sensitive information such as passwords and gain access to the system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1152", "line_number": 1152, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1153", "filename": "2000.txt", "content": "CVE-2000-1165(PUBLISHED):Balabit syslog-ng allows remote attackers to cause a denial of service (application crash) via a malformed log message that does not have a closing > in the priority specifier.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1153", "line_number": 1153, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1154", "filename": "2000.txt", "content": "CVE-2000-1166(PUBLISHED):Twig webmail system does not properly set the \"vhosts\" variable if it is not configured on the site, which allows remote attackers to insert arbitrary PHP (PHP3) code by specifying an alternate vhosts as an argument to the index.php3 program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1154", "line_number": 1154, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1155", "filename": "2000.txt", "content": "CVE-2000-1167(PUBLISHED):ppp utility in FreeBSD 4.1.1 and earlier does not properly restrict access as specified by the \"nat deny_incoming\" command, which allows remote attackers to connect to the target system.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1155", "line_number": 1155, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1156", "filename": "2000.txt", "content": "CVE-2000-1168(PUBLISHED):IBM HTTP Server 1.3.6 (based on Apache) allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1156", "line_number": 1156, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1157", "filename": "2000.txt", "content": "CVE-2000-1169(PUBLISHED):OpenSSH SSH client before 2.3.0 does not properly disable X11 or agent forwarding, which could allow a malicious SSH server to gain access to the X11 display and sniff X11 events, or gain access to the ssh-agent.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1157", "line_number": 1157, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1158", "filename": "2000.txt", "content": "CVE-2000-1170(PUBLISHED):Buffer overflow in Netsnap webcam HTTP server before 1.2.9 allows remote attackers to execute arbitrary commands via a long GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1158", "line_number": 1158, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1159", "filename": "2000.txt", "content": "CVE-2000-1171(PUBLISHED):Directory traversal vulnerability in cgiforum.pl script in CGIForum 1.0 allows remote attackers to ready arbitrary files via a .. (dot dot) attack in the \"thesection\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1159", "line_number": 1159, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1160", "filename": "2000.txt", "content": "CVE-2000-1172(PUBLISHED):Buffer overflow in Gaim 0.10.3 and earlier using the OSCAR protocol allows remote attackers to conduct a denial of service and possibly execute arbitrary commands via a long HTML tag.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1160", "line_number": 1160, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1161", "filename": "2000.txt", "content": "CVE-2000-1173(PUBLISHED):Microsys CyberPatrol uses weak encryption (trivial encoding) for credit card numbers and uses no encryption for the remainder of the information during registration, which could allow attackers to sniff network traffic and obtain this sensitive information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1161", "line_number": 1161, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1162", "filename": "2000.txt", "content": "CVE-2000-1174(PUBLISHED):Multiple buffer overflows in AFS ACL parser for Ethereal 0.8.13 and earlier allows remote attackers to execute arbitrary commands via a packet with a long username.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1162", "line_number": 1162, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1163", "filename": "2000.txt", "content": "CVE-2000-1175(PUBLISHED):Buffer overflow in Koules 1.4 allows local users to execute arbitrary commands via a long command line argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1163", "line_number": 1163, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1164", "filename": "2000.txt", "content": "CVE-2000-1176(PUBLISHED):Directory traversal vulnerability in YaBB search.pl CGI script allows remote attackers to read arbitrary files via a .. (dot dot) attack in the \"catsearch\" form field.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1164", "line_number": 1164, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1165", "filename": "2000.txt", "content": "CVE-2000-1177(PUBLISHED):bb-hist.sh, bb-histlog.sh, bb-hostsvc.sh, bb-rep.sh, bb-replog.sh, and bb-ack.sh in Big Brother (BB) before 1.5d3 allows remote attackers to determine the existence of files and user ID's by specifying the target file in the HISTFILE parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1165", "line_number": 1165, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1166", "filename": "2000.txt", "content": "CVE-2000-1178(PUBLISHED):Joe text editor follows symbolic links when creating a rescue copy called DEADJOE during an abnormal exit, which allows local users to overwrite the files of other users whose joe session crashes.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1166", "line_number": 1166, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1167", "filename": "2000.txt", "content": "CVE-2000-1179(PUBLISHED):Netopia ISDN Router 650-ST before 4.3.5 allows remote attackers to read system logs without authentication by directly connecting to the login screen and typing certain control characters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1167", "line_number": 1167, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1168", "filename": "2000.txt", "content": "CVE-2000-1180(PUBLISHED):Buffer overflow in cmctl program in Oracle 8.1.5 Connection Manager Control allows local users to gain privileges via a long command line argument.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1168", "line_number": 1168, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1169", "filename": "2000.txt", "content": "CVE-2000-1181(PUBLISHED):Real Networks RealServer 7 and earlier allows remote attackers to obtain portions of RealServer's memory contents, possibly including sensitive information, by accessing the /admin/includes/ URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1169", "line_number": 1169, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1170", "filename": "2000.txt", "content": "CVE-2000-1182(PUBLISHED):WatchGuard Firebox II allows remote attackers to cause a denial of service by flooding the Firebox with a large number of FTP or SMTP requests, which disables proxy handling.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1170", "line_number": 1170, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1171", "filename": "2000.txt", "content": "CVE-2000-1183(PUBLISHED):Buffer overflow in socks5 server on Linux allows attackers to execute arbitrary commands via a long connection request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1171", "line_number": 1171, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1172", "filename": "2000.txt", "content": "CVE-2000-1184(PUBLISHED):telnetd in FreeBSD 4.2 and earlier, and possibly other operating systems, allows remote attackers to cause a denial of service by specifying an arbitrary large file in the TERMCAP environmental variable, which consumes resources as the server processes the file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1172", "line_number": 1172, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1173", "filename": "2000.txt", "content": "CVE-2000-1185(PUBLISHED):The telnet proxy in RideWay PN proxy server allows remote attackers to cause a denial of service via a flood of connections that contain malformed requests.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1173", "line_number": 1173, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1174", "filename": "2000.txt", "content": "CVE-2000-1186(PUBLISHED):Buffer overflow in phf CGI program allows remote attackers to execute arbitrary commands by specifying a large number of arguments and including a long MIME header.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1174", "line_number": 1174, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1175", "filename": "2000.txt", "content": "CVE-2000-1187(PUBLISHED):Buffer overflow in the HTML parser for Netscape 4.75 and earlier allows remote attackers to execute arbitrary commands via a long password value in a form field.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1175", "line_number": 1175, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1176", "filename": "2000.txt", "content": "CVE-2000-1188(PUBLISHED):Directory traversal vulnerability in Quikstore shopping cart program allows remote attackers to read arbitrary files via a .. (dot dot) attack in the \"page\" parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1176", "line_number": 1176, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1177", "filename": "2000.txt", "content": "CVE-2000-1189(PUBLISHED):Buffer overflow in pam_localuser PAM module in Red Hat Linux 7.x and 6.x allows attackers to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1177", "line_number": 1177, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1178", "filename": "2000.txt", "content": "CVE-2000-1190(PUBLISHED):imwheel-solo in imwheel package allows local users to modify arbitrary files via a symlink attack from the .imwheelrc file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1178", "line_number": 1178, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1179", "filename": "2000.txt", "content": "CVE-2000-1191(PUBLISHED):htsearch program in htDig 3.2 beta, 3.1.6, 3.1.5, and earlier allows remote attackers to determine the physical path of the server by requesting a non-existent configuration file using the config parameter, which generates an error message that includes the full path.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1179", "line_number": 1179, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1180", "filename": "2000.txt", "content": "CVE-2000-1192(PUBLISHED):Buffer overflow in BTT Software SNMP Trap Watcher 1.16 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long string trap.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1180", "line_number": 1180, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1181", "filename": "2000.txt", "content": "CVE-2000-1193(PUBLISHED):Performance Metrics Collector Daemon (PMCD) in Performance Copilot in IRIX 6.x allows remote attackers to cause a denial of service (resource exhaustion) via an extremely long string to the PMCD port.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1181", "line_number": 1181, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1182", "filename": "2000.txt", "content": "CVE-2000-1194(PUBLISHED):Argosoft FRP server 1.0 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long string to the (1) USER or (2) CWD commands.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1182", "line_number": 1182, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1183", "filename": "2000.txt", "content": "CVE-2000-1195(PUBLISHED):telnet daemon (telnetd) from the Linux netkit package before netkit-telnet-0.16 allows remote attackers to bypass authentication when telnetd is running with the -L command line option.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1183", "line_number": 1183, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1184", "filename": "2000.txt", "content": "CVE-2000-1196(PUBLISHED):PSCOErrPage.htm in Netscape PublishingXpert 2.5 before SP2 allows remote attackers to read arbitrary files by specifying the target file in the errPagePath parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1184", "line_number": 1184, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1185", "filename": "2000.txt", "content": "CVE-2000-1197(PUBLISHED):POP2 or POP3 server (pop3d) in imap-uw IMAP package on FreeBSD and other operating systems creates lock files with predictable names, which allows local users to cause a denial of service (lack of mail access) for other users by creating lock files for other mail boxes.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1185", "line_number": 1185, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1186", "filename": "2000.txt", "content": "CVE-2000-1198(PUBLISHED):qpopper POP server creates lock files with predictable names, which allows local users to cause a denial of service for other users (lack of mail access) by creating lock files for other mail boxes.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1186", "line_number": 1186, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1187", "filename": "2000.txt", "content": "CVE-2000-1199(PUBLISHED):PostgreSQL stores usernames and passwords in plaintext in (1) pg_shadow and (2) pg_pwd, which allows attackers with sufficient privileges to gain access to databases.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1187", "line_number": 1187, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1188", "filename": "2000.txt", "content": "CVE-2000-1200(PUBLISHED):Windows NT allows remote attackers to list all users in a domain by obtaining the domain SID with the LsaQueryInformationPolicy policy function via a null session and using the SID to list the users.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1188", "line_number": 1188, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1189", "filename": "2000.txt", "content": "CVE-2000-1201(PUBLISHED):Check Point FireWall-1 allows remote attackers to cause a denial of service (high CPU) via a flood of packets to port 264.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1189", "line_number": 1189, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1190", "filename": "2000.txt", "content": "CVE-2000-1202(PUBLISHED):ikeyman in IBM IBMHSSSB 1.0 sets the CLASSPATH environmental variable to include the user's own CLASSPATH directories before the system's directories, which allows a malicious local user to execute arbitrary code as root via a Trojan horse Ikeyman class.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1190", "line_number": 1190, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1191", "filename": "2000.txt", "content": "CVE-2000-1203(PUBLISHED):Lotus Domino SMTP server 4.63 through 5.08 allows remote attackers to cause a denial of service (CPU consumption) by forging an email message with the sender as bounce@[127.0.0.1] (localhost), which causes Domino to enter a mail loop.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1191", "line_number": 1191, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1192", "filename": "2000.txt", "content": "CVE-2000-1204(PUBLISHED):Vulnerability in the mod_vhost_alias virtual hosting module for Apache 1.3.9, 1.3.11 and 1.3.12 allows remote attackers to obtain the source code for CGI programs if the cgi-bin directory is under the document root.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1192", "line_number": 1192, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1193", "filename": "2000.txt", "content": "CVE-2000-1205(PUBLISHED):Cross site scripting vulnerabilities in Apache 1.3.0 through 1.3.11 allow remote attackers to execute script as other web site visitors via (1) the printenv CGI (printenv.pl), which does not encode its output, (2) pages generated by the ap_send_error_response function such as a default 404, which does not add an explicit charset, or (3) various messages that are generated by certain Apache modules or core code.  NOTE: the printenv issue might still exist for web browsers that can render text/plain content types as HTML, such as Internet Explorer, but CVE regards this as a design limitation of those browsers, not Apache.  The printenv.pl/acuparam vector, discloser on 20070724, is one such variant.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1193", "line_number": 1193, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1194", "filename": "2000.txt", "content": "CVE-2000-1206(PUBLISHED):Vulnerability in Apache httpd before 1.3.11, when configured for mass virtual hosting using mod_rewrite, or mod_vhost_alias in Apache 1.3.9, allows remote attackers to retrieve arbitrary files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1194", "line_number": 1194, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1195", "filename": "2000.txt", "content": "CVE-2000-1207(PUBLISHED):userhelper in the usermode package on Red Hat Linux executes non-setuid programs as root, which does not activate the security measures in glibc and allows the programs to be exploited via format string vulnerabilities in glibc via the LANG or LC_ALL environment variables (CVE-2000-0844).", "metadata": {"filename": "2000.txt", "chunk_id": "line-1195", "line_number": 1195, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1196", "filename": "2000.txt", "content": "CVE-2000-1208(PUBLISHED):Format string vulnerability in startprinting() function of printjob.c in BSD-based lpr lpd package may allow local users to gain privileges via an improper syslog call that uses format strings from the checkremote() call.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1196", "line_number": 1196, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1197", "filename": "2000.txt", "content": "CVE-2000-1209(PUBLISHED):The \"sa\" account is installed with a default null password on (1) Microsoft SQL Server 2000, (2) SQL Server 7.0, and (3) Data Engine (MSDE) 1.0, including third party packages that use these products such as (4) Tumbleweed Secure Mail (MMS) (5) Compaq Insight Manager, and (6) Visio 2000, which allows remote attackers to gain privileges, as exploited by worms such as Voyager Alpha Force and Spida.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1197", "line_number": 1197, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1198", "filename": "2000.txt", "content": "CVE-2000-1210(PUBLISHED):Directory traversal vulnerability in source.jsp of Apache Tomcat before 3.1 allows remote attackers to read arbitrary files via a .. (dot dot) in the argument to source.jsp.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1198", "line_number": 1198, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1199", "filename": "2000.txt", "content": "CVE-2000-1211(PUBLISHED):Zope 2.2.0 through 2.2.4 does not properly perform security registration for legacy names of object constructors such as DTML method objects, which could allow attackers to perform unauthorized activities.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1199", "line_number": 1199, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1200", "filename": "2000.txt", "content": "CVE-2000-1212(PUBLISHED):Zope 2.2.0 through 2.2.4 does not properly protect a data updating method on Image and File objects, which allows attackers with DTML editing privileges to modify the raw data of these objects.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1200", "line_number": 1200, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1201", "filename": "2000.txt", "content": "CVE-2000-1213(PUBLISHED):ping in iputils before 20001010, as distributed on Red Hat Linux 6.2 through 7J and other operating systems, does not drop privileges after acquiring a raw socket, which increases ping's exposure to bugs that otherwise would occur at lower privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1201", "line_number": 1201, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1202", "filename": "2000.txt", "content": "CVE-2000-1214(PUBLISHED):Buffer overflows in the (1) outpack or (2) buf variables of ping in iputils before 20001010, as distributed on Red Hat Linux 6.2 through 7J and other operating systems, may allow local users to gain privileges.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1202", "line_number": 1202, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1203", "filename": "2000.txt", "content": "CVE-2000-1215(PUBLISHED):The default configuration of Lotus Domino server 5.0.8 includes system information (version, operating system, and build date) in the HTTP headers of replies, which allows remote attackers to obtain sensitive information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1203", "line_number": 1203, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1204", "filename": "2000.txt", "content": "CVE-2000-1216(PUBLISHED):Buffer overflow in portmir for AIX 4.3.0 allows local users to corrupt lock files and gain root privileges via the echo_error routine.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1204", "line_number": 1204, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1205", "filename": "2000.txt", "content": "CVE-2000-1217(PUBLISHED):Microsoft Windows 2000 before Service Pack 2 (SP2), when running in a non-Windows 2000 domain and using NTLM authentication, and when credentials of an account are locally cached, allows local users to bypass account lockout policies and make an unlimited number of login attempts, aka the \"Domain Account Lockout\" vulnerability.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1205", "line_number": 1205, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1206", "filename": "2000.txt", "content": "CVE-2000-1218(PUBLISHED):The default configuration for the domain name resolver for Microsoft Windows 98, NT 4.0, 2000, and XP sets the QueryIpMatching parameter to 0, which causes Windows to accept DNS updates from hosts that it did not query, which allows remote attackers to poison the DNS cache.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1206", "line_number": 1206, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1207", "filename": "2000.txt", "content": "CVE-2000-1219(PUBLISHED):The -ftrapv compiler option in gcc and g++ 3.3.3 and earlier does not handle all types of integer overflows, which may leave applications vulnerable to vulnerabilities related to overflows.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1207", "line_number": 1207, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1208", "filename": "2000.txt", "content": "CVE-2000-1220(PUBLISHED):The line printer daemon (lpd) in the lpr package in multiple Linux operating systems allows local users to gain root privileges by causing sendmail to execute with arbitrary command line arguments, as demonstrated using the -C option to specify a configuration file.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1208", "line_number": 1208, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1209", "filename": "2000.txt", "content": "CVE-2000-1221(PUBLISHED):The line printer daemon (lpd) in the lpr package in multiple Linux operating systems authenticates by comparing the reverse-resolved hostname of the local machine to the hostname of the print server as returned by gethostname, which allows remote attackers to bypass intended access controls by modifying the DNS for the attacking IP.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1209", "line_number": 1209, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1210", "filename": "2000.txt", "content": "CVE-2000-1222(PUBLISHED):AIX sysback before ******** uses a relative path to find and execute the hostname program, which allows local users to gain privileges by modifying the path to point to a malicious hostname program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1210", "line_number": 1210, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1211", "filename": "2000.txt", "content": "CVE-2000-1223(PUBLISHED):quikstore.cgi in Quikstore Shopping Cart allows remote attackers to execute arbitrary commands via shell metacharacters in the URL portion of an HTTP GET request.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1211", "line_number": 1211, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1212", "filename": "2000.txt", "content": "CVE-2000-1224(PUBLISHED):Caucho Technology Resin 1.2 and possibly earlier allows remote attackers to view JSP source via an HTTP request to a .jsp file with certain characters appended to the file name, such as (1) \"..\", (2) \"%2e..\", (3) \"%81\", (4) \"%82\", and others.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1212", "line_number": 1212, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1213", "filename": "2000.txt", "content": "CVE-2000-1225(PUBLISHED):Xitami 2.5b installs the testcgi.exe program by default in the cgi-bin directory, which allows remote attackers to gain sensitive configuration information about the web server by accessing the program.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1213", "line_number": 1213, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1214", "filename": "2000.txt", "content": "CVE-2000-1226(PUBLISHED):Snort 1.6, when running in straight ASCII packet logging mode or IDS mode with straight decoded ASCII packet logging selected, allows remote attackers to cause a denial of service (crash) by sending non-IP protocols that Snort does not know about, as demonstrated by an nmap protocol scan.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1214", "line_number": 1214, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1215", "filename": "2000.txt", "content": "CVE-2000-1227(PUBLISHED):Windows NT 4.0 and Windows 2000 hosts allow remote attackers to cause a denial of service (unavailable connections) by sending multiple SMB SMBnegprots requests but not reading the response that is sent back.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1215", "line_number": 1215, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1216", "filename": "2000.txt", "content": "CVE-2000-1228(PUBLISHED):Phorum 3.0.7 allows remote attackers to change the administrator password without authentication via an HTTP request for admin.php3 that sets step, option, confirm and newPssword variables.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1216", "line_number": 1216, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1217", "filename": "2000.txt", "content": "CVE-2000-1229(PUBLISHED):Directory traversal vulnerability in Phorum 3.0.7 allows remote Phorum administrators to read arbitrary files via \"..\" (dot dot) sequences in the default .langfile name field in the Master Settings administrative function, which causes the file to be displayed in admin.php3.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1217", "line_number": 1217, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1218", "filename": "2000.txt", "content": "CVE-2000-1230(PUBLISHED):Backdoor in auth.php3 in Phorum 3.0.7 allows remote attackers to access restricted web pages via an HTTP request with the PHP_AUTH_USER parameter set to \"boogieman\".", "metadata": {"filename": "2000.txt", "chunk_id": "line-1218", "line_number": 1218, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1219", "filename": "2000.txt", "content": "CVE-2000-1231(PUBLISHED):code.php3 in Phorum 3.0.7 allows remote attackers to read arbitrary files in the phorum directory via the query string.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1219", "line_number": 1219, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1220", "filename": "2000.txt", "content": "CVE-2000-1232(PUBLISHED):upgrade.php3 in Phorum 3.0.7 could allow remote attackers to modify certain Phorum database tables via an unknown method.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1220", "line_number": 1220, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1221", "filename": "2000.txt", "content": "CVE-2000-1233(PUBLISHED):SQL injection vulnerability in read.php3 and other scripts in Phorum 3.0.7 allows remote attackers to execute arbitrary SQL queries via the sSQL parameter.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1221", "line_number": 1221, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1222", "filename": "2000.txt", "content": "CVE-2000-1234(PUBLISHED):violation.php3 in Phorum 3.0.7 allows remote attackers to send e-mails to arbitrary addresses and possibly use Phorum as a \"spam proxy\" by setting the Mod and ForumName parameters.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1222", "line_number": 1222, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1223", "filename": "2000.txt", "content": "CVE-2000-1235(PUBLISHED):The default configurations of (1) the port listener and (2) modplsql in Oracle Internet Application Server (IAS) 3.0.7 and earlier allow remote attackers to view privileged database information via HTTP requests for Database Access Descriptor (DAD) files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1223", "line_number": 1223, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1224", "filename": "2000.txt", "content": "CVE-2000-1236(PUBLISHED):SQL injection vulnerability in mod_sql in Oracle Internet Application Server (IAS) 3.0.7 and earlier allows remote attackers to execute arbitrary SQL commands via the query string of the URL.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1224", "line_number": 1224, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1225", "filename": "2000.txt", "content": "CVE-2000-1237(PUBLISHED):The POP3 server in FTGate returns an -ERR code after receiving an invalid USER request, which makes it easier for remote attackers to determine valid usernames and conduct brute force password guessing.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1225", "line_number": 1225, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1226", "filename": "2000.txt", "content": "CVE-2000-1238(PUBLISHED):BEA Systems WebLogic Express and WebLogic Server 5.1 SP1-SP6 allows remote attackers to bypass access controls for restricted JSP or servlet pages via a URL with multiple / (forward slash) characters before the restricted pages.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1226", "line_number": 1226, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1227", "filename": "2000.txt", "content": "CVE-2000-1239(PUBLISHED):The HTTP interface of Tivoli Lightweight Client Framework (LCF) in IBM Tivoli Management Framework 3.7.1 sets http_disable to zero at install time, which allows remote authenticated users to bypass file permissions on Tivoli Endpoint Configuration data files via an unspecified manipulation of log files.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1227", "line_number": 1227, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1228", "filename": "2000.txt", "content": "CVE-2000-1240(PUBLISHED):Unspecified vulnerability in siteman.php3 in AnyPortal(php) before 22 APR 00 allows remote attackers to obtain sensitive information via unknown attack vectors, which reveal the absolute path.  NOTE: the provenance of this information is unknown; the details are obtained from third party information.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1228", "line_number": 1228, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1229", "filename": "2000.txt", "content": "CVE-2000-1241(PUBLISHED):Unspecified vulnerability in Haakon Nilsen simple, integrated publishing system (SIPS) before 0.2.4 has an unknown impact and attack vectors, related to a \"grave security fault.\"", "metadata": {"filename": "2000.txt", "chunk_id": "line-1229", "line_number": 1229, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1230", "filename": "2000.txt", "content": "CVE-2000-1242(PUBLISHED):The HTTP service in American Power Conversion (APC) PowerChute uses a default username and password, which allows remote attackers to gain system access.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1230", "line_number": 1230, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1231", "filename": "2000.txt", "content": "CVE-2000-1243(PUBLISHED):Privacy leak in Dansie Shopping Cart 3.04, and probably earlier versions, sends sensitive information such as user credentials to an e-mail address controlled by the product developers.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1231", "line_number": 1231, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1232", "filename": "2000.txt", "content": "CVE-2000-1244(PUBLISHED):Computer Associates InoculateIT Agent for Exchange Server does not recognize an e-mail virus attachment if the SMTP header is missing the \"From\" field, which allows remote attackers to bypass virus protection.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1232", "line_number": 1232, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1233", "filename": "2000.txt", "content": "CVE-2000-1245(PUBLISHED):Multiple unspecified vulnerabilities in NWFTPD.nlm before 5.01o in the FTP server in Novell NetWare 5.1 SP3 allow remote attackers to bypass intended restrictions on anonymous access via unknown vectors.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1233", "line_number": 1233, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1234", "filename": "2000.txt", "content": "CVE-2000-1246(PUBLISHED):NWFTPD.nlm before 5.01o in the FTP server in Novell NetWare 5.1 SP3 allows remote authenticated users to cause a denial of service (abend) by sending an RNTO command after a failed RNFR command.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1234", "line_number": 1234, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1235", "filename": "2000.txt", "content": "CVE-2000-1247(PUBLISHED):The default configuration of the jserv-status handler in jserv.conf in Apache JServ 1.1.2 includes an \"allow from 127.0.0.1\" line, which allows local users to discover JDBC passwords or other sensitive information via a direct request to the jserv/ URI.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1235", "line_number": 1235, "source": "知识库\\cve\\2000.txt"}}
{"chunk_id": "line-1236", "filename": "2000.txt", "content": "CVE-2000-1254(PUBLISHED):crypto/rsa/rsa_gen.c in OpenSSL before 0.9.6 mishandles C bitwise-shift operations that exceed the size of an expression, which makes it easier for remote attackers to defeat cryptographic protection mechanisms by leveraging improper RSA key generation on 64-bit HP-UX platforms.", "metadata": {"filename": "2000.txt", "chunk_id": "line-1236", "line_number": 1236, "source": "知识库\\cve\\2000.txt"}}
