{"chunk_id": "line-1", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 4", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-2", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "INTRODUCTION", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-3", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware is short for ‘malicious software’, that is, any program that performs malicious activi-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-4", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ties. We use the terms malware and malicious code interchangeably. Malware comes with a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-5", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "wide range of shapes and forms, and with different classiﬁcations accordingly, e.g., viruses,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-6", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Trojans, worms, spyware, botnet malware, ransomware, etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-7", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware carries out many of the cyberattacks on the Internet, including nation-state cyberwar,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-8", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "cybercrime, fraud and scams. For example, Trojans can introduce a backdoor access to a gov-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-9", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ernment network to allow nation-state attackers to steal classiﬁed information. Ransomware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-10", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can encrypt data on a user’s computer and thus making it unaccessible to the user, and only", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-11", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "decrypt the data after the user pays a sum of money. Botnet malware is responsible for many", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-12", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of the Distributed Denial-of-Service (DDoS) attacks as well as spam and phishing activities.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-13", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "We need to study the techniques behind malware development and deployment in order to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-14", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "better understand cyberattacks and develop the appropriate countermeasures.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-15", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "As the political and ﬁnancial stakes become higher, the sophistication and robustness of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-16", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "both the cyber defence mechanisms and the malware technologies and operation models", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-17", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "have also increased. For example, attackers now use various obfuscation techniques such as", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-18", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "packing and polymorphism as well as metamorphism to evade malware detection systems [1],", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-19", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and they set up adaptive network infrastructures on the Internet to support malware updates,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-20", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "command-and-control, and other logistics such as transits of stolen data. In short, it is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-21", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "becoming more important but also more challenging to study malware.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-22", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The rest of this chapter is organised as follows. We will provide a taxonomy of malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-23", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and discuss their typical malicious activities as well as their eco-system and support infras-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-24", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tructures. We will then describe the tools and techniques to analyse malware behaviours,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-25", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and network- and host- based detection methods to identify malware activities, as well as", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-26", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "processes and techniques including forensic analysis and attribution to respond to malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-27", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attacks.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-28", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "CONTENT", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-30", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "A TAXONOMY OF MALWARE", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-31", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[2, c6]", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-32", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "There are many types of malware [2]. It is instructive to create a taxonomy to systematically", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-33", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "categorise the wide spectrum of malware types. This taxonomy describes the common char-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-34", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "acteristics of each type of malware and thus can guide the development of countermeasures", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-35", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "applicable to an entire category of malware (rather than a speciﬁc malware). Since there", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-36", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "many facets of malware technologies and attack operations, based on which malware can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-37", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "be categorised and named, our taxonomy can include many dimensions. We discuss a few", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-38", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "important ones below. It should be borne in mind that other, more specialised, attributes could", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-39", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "also be used such as target processor architecture or operating system.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-40", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 3", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-41", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 5", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-42", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The ﬁrst dimension of our taxonomy is whether malware is a standalone (or, independent)", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-43", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "program or just a sequence of instructions to be embedded in another program. Standalone", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-44", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware is a complete program that can run on its own once it is installed on a compromised", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-45", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "machine and executed. For example, worms and botnet malware belong to this type. The", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-46", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "second type requires a host program to run, that is, it must infect a program on a computer", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-47", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "by inserting its instructions into the program so that when the program is run, the malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-48", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "instructions are also executed. For example, document macro viruses and malicious browser", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-49", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "plug-ins belong to this type. In general, it is easier to detect standalone malware because it is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-50", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "a program or a running process in its own right and its presence can be detected by operating", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-51", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "system or security tools.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-52", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The second dimension is whether malware is persistent or transient. Most malware is installed", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-53", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in persistent storage (typically, a ﬁle system) as either standalone malware or an infection of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-54", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "another program that already resides in persistent storage. Other malware is memory-resident", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-55", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "such that if the computer is rebooted or the infected running program terminates, it no longer", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-56", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "exists anywhere on the system. Memory-resident malware can evade detection by many", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-57", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "anti-virus systems that rely on ﬁle scanning. Such transient malware also has the advantage", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-58", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of being easy to clean up (or, cover-up) its attack operations. The traditional way for malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-59", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to become memory-resident is to remove the malware program (that was downloaded and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-60", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "installed previously) from the ﬁle system as soon as it gets executed. Newer approaches", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-61", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "exploit system administrative and security tools such as PowerShell to inject malware directly", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-62", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "into memory [3]. For example, according to one report [4], after an initial exploit that led to the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-63", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "unauthorised execution of PowerShell, meterpreter code was downloaded and injected into", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-64", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "memory using PowerShell commands and it harvested passwords on the infected computer.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-65", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The third dimension generally applies to only persistent malware and categorises malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-66", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "based on the layer of the system stack the malware is installed and run on. These layers,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-67", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in the ascending order, include ﬁrmware, boot-sector, operating system kernel, drivers and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-68", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Application Programing Interfaces (APIs), and user applications. Typically, malware in the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-69", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "lower layers is harder to detect and remove, and wreaks greater havoc because it has more", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-70", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "control of the compromised computer. On the other hand, it is also harder to write malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-71", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that can be installed at a lower layer because there are greater constraints, e.g., a more limited", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-72", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "programming environment in terms of both the types and amount of code allowed.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-73", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The fourth dimension is whether malware is run and spread automatically vs. activated by a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-74", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "user action. When an auto-spreading malware runs, it looks for other vulnerable machines on", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-75", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the Internet, compromises these machines and installs itself on them; the copies of malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-76", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "on these newly infected machines immediately do the same – run and spread. Obviously, auto-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-77", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "spreading malware can spread on the Internet very quickly, often being able to exponentially", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-78", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "increase the number of compromised computers. On the other hand, user-activated malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-79", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is run on a computer only because a user accidentally downloads and executes it, e.g., by", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-80", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "clicking on an attachment or URL in a received email. More importantly, when this malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-81", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "runs, although it can ‘spread’, e.g., by sending email with itself as the attachment to contacts", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-82", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in the user’s address book, this spreading is not successful unless a user who receives this", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-83", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "email activates the malware.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-84", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The ﬁfth dimension is whether malware is static or one-time vs. dynamically updated. Most", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-85", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "modern malware is supported by an infrastructure such that a compromised computer can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-86", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "receive a software update from a malware server, that is, a new version of the malware is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-87", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "installed on the compromised computer. From an attacker’s point-of-view, there are many", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-88", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "beneﬁts of updating malware. For example, updated malware can evade detection techniques", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-89", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 4", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-90", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 6", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-91", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that are based on the characteristics of older malware instances.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-92", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The sixth dimension is whether malware acts alone or is part of a coordinated network (i.e., a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-93", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet). While botnets are responsible for many cyberattacks such as DDoS, spam, phishing,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-94", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "etc., isolated malware has become increasingly common in the forms of targeted attack. That", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-95", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is, malware can be speciﬁcally designed to infect a target organisation and perform malicious", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-96", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "activities according to those assets of the organisation valuable to the attacker.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-97", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Most modern malware uses some form of obfuscation in order to avoid detection (and hence", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-98", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "we do not explicitly include obfuscation in this taxonomy). There is a range of obfuscation", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-99", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "techniques and there are tools freely available on the Internet for a malware author to use. For", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-100", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "example, polymorphism can be used to defeat detection methods that are based on ‘signatures’", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-101", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "or patterns of malware code. That is, the identiﬁable malware features are changed to be", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-102", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "unique to each instance of the malware. Therefore, malware instances look different from", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-103", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "each other, but they all maintain the same malware functionality. Some common polymorphic", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-104", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware techniques include packing, which involves compressing and encrypting part of the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-105", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware, and rewriting identiﬁable malicious instructions into other equivalent instructions.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-106", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "standalone or", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-107", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "host-program", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-108", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "persistent or", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-109", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "transient", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-110", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "layers of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-111", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "system stack", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-112", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "auto-spreading?", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-113", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "dynamically", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-114", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "updatable?", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-115", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "coordinated?", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-116", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "viruses", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-117", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "host-program", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-118", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "persistent", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-119", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ﬁrmware and up", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-123", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malicious", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-124", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "browser extensions", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-125", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "host-program", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-126", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "persistent", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-127", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "application", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-131", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-132", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "both", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-133", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "persistent", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-134", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "kernel and up", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-138", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "memory-resident", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-139", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-140", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "standalone", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-141", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "transient", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-142", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "kernel and up", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-146", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Table 1: Use of the Taxonomy to Classify Representative Malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-147", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "As an illustration, we can apply this taxonomy to several types (or names) of malware. See", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-148", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Table 1. In particular, a virus needs a host-program to run because it infects the host-program", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-149", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "by inserting a malicious code sequence into the program. When the host-program runs, the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-150", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malicious code executes and, in addition to performing the intended malicious activities, it", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-151", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can look for other programs to infect. A virus is typically persistent and can reside in all layers", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-152", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of the system stack except hardware. It can spread on its own because it can inject itself", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-153", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "into programs automatically. A virus can also be dynamically updated provided that it can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-154", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "connect to a malware update server. A polymorphic malware virus can mutate itself so that", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-155", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "new copies look different, although the algorithm of this mutation is embedded into its own", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-156", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "code. A virus is typically not part of a coordinated network because while the infection can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-157", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "affect many computers, the virus code typically does not perform coordinated activities.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-158", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Other malware that requires a host-program includes malicious browser plug-ins and exten-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-159", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "sions, scripts (e.g., JavaScript on a web page), and document macros (e.g., macro viruses", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-160", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and PDF malware). These types of malware can be updated dynamically, form a coordinated", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-161", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "network, and can be obfuscated.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-162", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Botnet malware refers to any malware that is part of a coordinated network with a botnet", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-163", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "infrastructure that provides command-and-control. A botnet infrastructure typically also", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-164", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 5", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-165", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 7", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-166", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "provides malware update, and other logistic support. Botnet malware is persistent and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-167", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "typically obfuscated, and usually resides in the kernel, driver, or application layers. Some", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-168", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet malware requires a host-program, e.g., malicious browser plug-ins and extensions,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-169", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and needs user activation to spread (e.g., malicious JavaScript). Other botnet malware is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-170", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "standalone, and can spread automatically by exploiting vulnerable computers or users on", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-171", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the Internet. These include trojans, key-loggers, ransomware, click bots, spam bots, mobile", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-172", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware, etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-173", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "1.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-174", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Potentially unwanted programs (PUPs)", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-175", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "A potentially unwanted program (PUP) is typically a piece of code that is part of a useful", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-176", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "program downloaded by a user. For example, when a user downloads the free version of a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-177", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "mobile game app, it may include adware, a form of PUP that displays ad banners on the game", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-178", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "window. Often, the adware also collects user data (such as geo-location, time spent on the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-179", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "game, friends, etc.) without the user’s knowledge and consent, in order to serve more targeted", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-180", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ads to the user to improve the effectiveness of the advertising. In this case, the adware is also", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-181", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "considered spyware, which is deﬁned as unwanted program that steals information about", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-182", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "a computer and its users. PUPs are in a grey area because, while the download agreement", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-183", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "often contains information on these questionable behaviours, most users tend not to read the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-184", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ﬁner details and thus fail to understand exactly what they are downloading.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-185", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "From the point of view of cybersecurity, it is prudent to classify PUPs towards malware, and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-186", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "this is the approach taken by many security products. The simple reason is that a PUP has all", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-187", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the potential to become full-ﬂedged malware; once it is installed, the user is at the mercy of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-188", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the PUP operator. For example, a spyware that is part of a spellchecker browser extension", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-189", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can gather information on which websites the user tends to visit. But it can also harvest user", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-190", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "account information including logins and passwords. In this case, the spyware has become a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-191", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware from just a PUP.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-193", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "MALICIOUS ACTIVITIES BY MALWARE", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-194", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[2, c6][1, c11-12]", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-195", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware essentially codiﬁes the malicious activities intended by an attacker. Cyberattacks", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-196", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can be analysed using the Cyber Kill Chain Model [5], which, as shown in Table 2, represents", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-197", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "(iterations of) steps typically involved in a cyberattack. The ﬁrst step is Reconnaissance where", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-198", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "an attacker identiﬁes or attracts the potential targets. This can be accomplished, for example,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-199", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "by scanning the Internet for vulnerable computers (i.e., computers that run network services,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-200", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "such as sendmail, that have known vulnerabilities), or sending phishing emails to a group of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-201", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "users. The next phase is to gain access to the targets, for example, by sending crafted input", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-202", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to trigger a vulnerability such as a buffer overﬂow in the vulnerable network service program", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-203", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "or embedding malware in a web page that will compromise a user’s browser and gain control", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-204", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of his computer. This corresponds to the Weaponization and Delivery (of exploits) steps in the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-205", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Cyber Kill Chain Model. Once the target is compromised, typically another piece of malware is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-206", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "downloaded and installed; this corresponds to the Installation (of malware) step in the Cyber", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-207", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Kill Chain Model. This latter malware is the real workhorse for the attacker and can carry out", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-208", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "a wide range of activities, which amount to attacks on:", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-209", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 6", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-210", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 8", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-211", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "• conﬁdentiality – it can steal valuable data, e.g., user’s authentication information, and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-212", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ﬁnancial and health data;", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-213", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "• integrity – it can inject falsiﬁed information (e.g., send spam and phish emails, create", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-214", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "fraudulent clicks, etc.) or modify data;", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-215", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "• availability – it can send trafﬁc as part of a distributed denial-of-service (DDoS) attack,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-216", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "use up a large amount of compute-resources (e.g., to mine cryptocurrencies), or encrypt", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-217", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "valuable data and demand a ransom payment.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-218", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Step", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-219", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Activities", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-221", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Reconnaissance", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-222", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Harvesting email addresses,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-223", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "identifying vulnerable computers and accounts, etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-225", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Weaponization", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-226", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Designing exploits into a deliverable payload.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-228", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Delivery", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-229", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Delivering the exploit payload to a victim via email,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-230", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Web download, etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-232", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Exploitation", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-233", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Exploiting a vulnerability and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-234", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "executing malicious code on the victim’s system.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-236", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Installation", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-237", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Installing (additional) malware on the victim’s system.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-239", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Command & Control", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-240", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Establishing a command and control channel for attackers", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-241", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to remotely commandeer the victim’s system.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-243", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Actions on Objectives", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-244", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Carrying out malicious activities on the victim’s system and network.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-245", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Table 2: The Cyber Kill Chain Model", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-246", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Most modern malware performs a combination of these attack actions because there are", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-247", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "toolkits (e.g., a key-logger) freely available for carrying out many ‘standard’ activities (e.g.,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-248", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "recording user passwords) [1], and malware can be dynamically updated to include or activate", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-249", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "new activities and take part in a longer or larger ‘campaign’ rather than just performing isolated,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-250", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "one-off actions. These are the Actions on Objectives in the Cyber Kill Chain Model.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-251", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Botnets exemplify long-running and coordinated malware. A botnet is a network of bots (or,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-252", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "compromised computers) under the control of an attacker. Botnet malware runs on each bot", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-253", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and communicates with the botnet command-and-control (C&C) server regularly to receive", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-254", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "instructions on speciﬁc malicious activities or updates to the malware. For example, every", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-255", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "day the C&C server of a spamming botnet sends each bot a spam template and a list of email", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-256", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "addresses so that collectively the botnet sends a very large number of spam messages. If the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-257", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet is disrupted because of detection and response actions, e.g., the current C&C server is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-258", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "taken down, the botnet malware is already programmed to contact an alternative server and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-259", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can receive updates to change to a botnet that uses peer-to-peer for C&C. In general, botnets", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-260", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "are quite noisy, i.e., relatively easy to detect, because there are many bots in many networks.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-261", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Botnet C&C is an example of the Command & Control step in the Cyber Kill Chain Model.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-262", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "In contrast to botnets, malware behind the so-called advanced persistent threats (APTs)", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-263", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "typically targets a speciﬁc organisation rather than aiming to launch large-scale attacks. For", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-264", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "example, it may look for a particular type of controller in the organisation to infect and cause it", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-265", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to send the wrong control signals that lead to eventual failures in machineries. APT malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-266", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is typically designed to be long-lived (hence the term ‘persistent’). This means it not only", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-267", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "receives regular updates. but also evades detection by limiting its activity volume and intensity", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-268", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "(i.e., ‘low and slow’), moving around the organisation (i.e., ‘lateral movements’) and covering", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-269", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "its tracks. For example, rather than sending the stolen data out to a ‘drop site’ all at once, it", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-270", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can send a small piece at a time and only when the server is already sending legitimate trafﬁc;", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-271", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "after it has ﬁnished stealing from a server it moves to another (e.g., by exploiting the trust", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-272", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 7", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-273", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 9", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-274", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "relations between the two) and removes logs and even patches the vulnerabilities in the ﬁrst", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-275", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "server.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-276", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "When we use the Cyber Kill Chain Model to analyze a cyberattack, we need to examine its", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-277", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "activities in each step. This requires knowledge of the attack techniques involved. The", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-278", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ATT&CK Knowledge Base [6] documents the up-to-date attack tactics and techniques based", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-279", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "on real-world observations, and is a valuable reference for analysts.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-280", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "2.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-281", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The Underground Eco-System", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-282", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The early-day malware activities were largely nuisance attacks (such as defacing or putting", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-283", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "grafﬁti on an organisation’s web page). Present-day malware attacks are becoming full-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-284", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "blown cyberwars (e.g., attacks on critical infrastructures) and sophisticated crimes (e.g.,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-285", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ransomware, fake-AntiVirus tools, etc.). An underground eco-system has also emerged to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-286", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "support the full malware lifecycle that includes development, deployment, operations and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-287", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "monetisation. In this eco-system, there are actors specialising in key parts of the malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-288", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "lifecycle, and by providing their services to others they also get a share of the (ﬁnancial) gains", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-289", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and rewards. Such specialisation improves the quality of malware. For example, an attacker", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-290", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can hire the best exploit researcher to write the part of the malware responsible for remotely", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-291", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "compromising a vulnerable computer. Specialisation can also provide plausible deniability", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-292", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "or at the least limit liability. For example, a spammer only ‘rents’ a botnet to send spam and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-293", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is not guilty of compromising computers and turning them into bots; likewise, the exploit", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-294", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "‘researcher’ is just experimenting and not responsible for creating the botnet as long as he did", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-295", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "not release the malware himself. That is, while they are all liable for the damage by malware,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-296", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "they each bear only a portion of the full responsibility.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-298", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "MALWARE ANALYSIS", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-299", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[1, c1-10] [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-300", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "There are many beneﬁts in analysing malware. First, we can understand the intended malicious", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-301", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "activities to be carried out by the malware. This will allow us to update our network and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-302", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "endpoint sensors to detect and block such activities, and identify which machines have", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-303", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the malware and take corrective actions such as removing it or even completely wiping the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-304", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "computer clean and reinstalling everything. Second, by analysing the malware structure (e.g.,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-305", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the libraries and toolkits that it includes) and coding styles, we may be able to gain information", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-306", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that is potentially useful to attribution, which means being able to identify the likely author", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-307", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and operator. Third, by comparing it with historical as well as geo-location data, we can better", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-308", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "understand and predict the scope and trend of malware attacks, e.g., what kinds of activities", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-309", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "(e.g., mining cryptocurrencies) are on the rise and if a cybercrime is moving from one region to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-310", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "another. In short, malware analysis is the basis for detecting and responding to cyberattacks.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-311", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware analysis typically involves running a malware instance in an analysis environment.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-312", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "There are ways to ‘capture’ malware instances on the infection sites. A network sensor", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-313", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can examine trafﬁc (e.g., web trafﬁc, email attachment) to identify possible malware (e.g.,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-314", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "payload that contains binary or program-like data from a website with a low reputation) and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-315", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "run it in a sandbox to conﬁrm. If a network sensor is able to detect outgoing malicious", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-316", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "trafﬁc from an internal host, a host-based sensor can further identify the program, i.e., the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-317", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware, responsible for such trafﬁc. There are also malware collection and sharing efforts", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-318", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 8", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-319", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 10", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-320", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "where trusted organisations can upload malware samples found in their networks and also", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-321", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "receive samples contributed by other organisations. Academic researchers can typically", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-322", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "just obtain malware samples without needing to contribute. When acquiring and sharing", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-323", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware samples, we must consider our legal and ethical responsibilities carefully [19]. For", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-324", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "example, we must protect the identities of the infection sites from which the malware samples", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-325", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "were captured, and we must not share the malware samples with any organisation that is an", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-326", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "unknown entity or that does not have the commitment or technical capabilities to analyse", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-327", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware safely.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-328", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The malware analysis pipeline typically includes the following steps: 1) identifying the format", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-329", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of a malware sample (e.g., binary or source code, Windows or Linux, etc.), 2) static analysis", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-330", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "using disassembly (if the malware is in binary format), program analysis, statistical analysis", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-331", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of the ﬁle contents, etc., and 3) dynamic analysis using an analysis environment. Steps 2 and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-332", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "3 can be combined and iterated.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-333", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "3.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-334", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Analysis Techniques", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-335", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware analysis is the process of learning malware behaviours. Due to the large volume", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-336", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and increasing complexity of malware, we need to be able to rapidly analyse samples in a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-337", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "complete, reliable and scalable way. To achieve this, we need to employ techniques such", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-338", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "as static analysis, dynamic analysis, symbolic execution and concolic execution [1]. These", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-339", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "program analysis techniques have been developed to support the software development cycle,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-340", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and they often need to be customized or extended for malware analysis because malicious", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-341", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "programs typically include code constructed speciﬁcally to resist analysis. That is, the main", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-342", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "challenge in malware analysis is to detect and bypass anti-analysis mechanisms.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-343", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "3.1.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-344", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Static Analysis", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-345", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Static analysis involves examining the code (source, intermediate, or binary) to assess the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-346", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "behaviours of a program without actually executing it [1]. A wide range of malware analysis", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-347", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "techniques fall into the category of static analysis. One limitation is that the analysis output", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-348", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "may not be consistent with the actual malware behaviours (at runtime). This is because in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-349", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "many cases it is not possible to precisely determine a program’s behaviours statically (i.e.,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-350", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "without the actual run-time input data). A more serious problem is that malware authors", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-351", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "are well aware of the limitations of static analysis and they leverage code obfuscation and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-352", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "packing to thwart static-analysis altogether. For example, the packed code cannot be statically", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-353", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "analysed because it is encrypted and compressed data until unpacked into executable code", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-354", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "at run-time.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-355", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 9", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-356", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 12", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-357", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "3.1.5", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-358", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Concolic Execution", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-359", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "While symbolic execution can traverse all paths in theory, it has major limitations [24], e.g.,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-360", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "it may not converge quickly (if at all) when dealing with large symbol space and complex", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-361", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "formulas and predicates. Concolic execution, which combines CONCrete and symbOLIC", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-362", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "execution, can reduce the symbolic space but keep the general input space.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-363", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Ofﬂine Concolic Execution is a technique that uses concrete traces to drive symbolic execution;", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-364", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "it is also known as a Trace Based Executor [9]. The execution trace obtained by concrete", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-365", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "execution is used to generate the path formulas and constraints. The path formulas for the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-366", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "corresponding branch is negated and Satisﬁability Modulo Theories (SMT) solvers are used", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-367", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to ﬁnd a valid input that can satisfy the not-taken branches. Generated inputs are fed into", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-368", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the program and re-run from the beginning. This technique iteratively explores the feasible", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-369", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "not-taken branches encountered during executions. It requires the repetitive execution of all", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-370", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the instructions from the beginning and knowledge of the input format.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-371", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Online Concolic Execution is a technique that generates constraints along with the concrete", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-372", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "execution [10]. Whenever the concrete execution hits a branch, if both directions are feasible,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-373", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "execution is forked to work on both branches. Unlike the ofﬂine executor, this approach can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-374", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "explore multiple paths.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-375", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Hybrid Execution: This approach switches automatically between online and ofﬂine modes to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-376", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "avoid the drawbacks of non-hybrid approaches [11].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-377", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Concolic Execution can use whole-system emulators [10, 27] or dynamic binary instrumenta-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-378", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tion tools [11, 25]. Another approach is to interpret Intermediate Representation (IR) to imitate", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-379", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the effects of execution [8, 12]. This technique allows context-free concolic execution, which", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-380", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "analyses any part of the binary at function and basic block levels.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-381", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Path Exploration is a systematical approach to examine program paths. Path explosion is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-382", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "also inevitable in concolic execution due to the nature of symbolic space. There are a variety", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-383", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of algorithms used to prioritise the directions of concolic execution, e.g., Depth-First Search", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-384", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "(DFS) or distance computation [28]. Another approach is to prioritise the directions favouring", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-385", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "newly explored code blocks or symbolic memory dependence [11]. Other popular techniques", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-386", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "include path pruning, state merging [10, 29, 30], under-constrained symbolic execution [12]", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-387", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and fuzzing support [7, 9].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-388", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "3.2", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-389", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Analysis Environments", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-390", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware analysis typically requires a dedicated environment to run the dynamic analysis", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-391", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tools [1]. The design choice of the environment determines the analysis methods that can be", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-392", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "utilised and, therefore, the results and limitations of analysis. Creating an environment requires", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-393", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "balancing the cost it takes to analyse a malware sample against the richness of the resulting", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-394", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "report. In this context, cost is commonly measured in terms of time and manual human", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-395", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "effort. For example, having an expert human analyst study a sample manually can produce a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-396", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "very in-depth and thorough report, but at great cost. Safety is a critical design consideration", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-397", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "because of the concern that malware being executed and analysed in the environment can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-398", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "break out of its containment and cause damage to the analysis system and its connected", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-399", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "network including the Internet (see 3.2.1 Safety and Live-Environment Requirements). An", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-400", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "example is running a sample of a botnet malware that performs a DDoS attack, and thus if", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-401", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the analysis environment is not safe, it will contribute to that attack.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-402", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 11", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-403", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 15", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-404", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "constants [50], which allows the attacker to hide what values will be loaded into registers", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-405", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "during runtime. This in turn makes it very hard for static malware analysis to extract the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-406", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "control-ﬂow graph and variables from the binary. A more effective approach is to combine", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-407", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "static and dynamic analysis. For example, such an approach has been shown to be able to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-408", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "disassemble the highly obfuscated binary code [51].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-409", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "A less common but much more potent obfuscation technique is code emulation. Borrowing", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-410", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "techniques originally designed to provide software copyright protection [52], malware authors", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-411", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "convert native malware binaries into bytecode programs using a randomly generated instruc-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-412", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tion set, paired with a native binary emulator that interprets the instruction set. That is, with this", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-413", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "approach, the malware ‘binary’ is the emulator, and the original malware code becomes ‘data’", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-414", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "used by the emulator program. Note that, for the same original malware, the malware author", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-415", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can turn it into many instances of emulated malware instances, each with its own random", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-416", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "bytecode instruction set and a corresponding emulator binary. It is extremely hard to analyse", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-417", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "emulated malware. Firstly, static analysis of the emulator code yields no information about", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-418", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the speciﬁc malware behaviours because the emulator processes all possible programs in the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-419", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "bytecode instruction set. Static analysis of the malware bytecode entails ﬁrst understanding", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-420", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the instruction set format (e.g., by static analysing the emulator ﬁrst), and developing tools", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-421", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "for the instruction set; but this process needs to be repeated for every instance of emulated", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-422", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware. Secondly, standard dynamic analysis is not directly useful because it observes the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-423", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "run-time instructions and behaviours of an emulator and not of the malware.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-424", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "A specialised dynamic analysis approach is needed to analyse emulated malware [17]. The", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-425", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "main idea is to execute the malware emulator and record the entire instruction traces. Ap-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-426", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "plying dynamic dataﬂow and taint analysis techniques to these traces, we then identify data", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-427", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "regions containing the bytecode, syntactic information showing how bytecodes are parsed", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-428", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "into opcodes and operands, and semantic information about control transfer instructions. The", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-429", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "output of this approach is data structures, such as a control-ﬂow graph (CFG) of the malware,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-430", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "which provides the foundation for subsequent malware analysis.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-430", "line_number": 430, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-431", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware often uses ﬁngerprinting techniques to detect the presence of an analysis environ-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-432", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ment and evade dynamic analysis (e.g., it stops executing the intended malware code). More", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-433", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "generally, malware behaviours can be ‘trigger-based’ where a trigger is a run-time condition", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-434", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that must be true. Examples of conditions include the correct date and time, the presence", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-435", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of certain ﬁles or directories, an established connection to the Internet, the absence of a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-436", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "speciﬁc mutex object etc. If a condition is not true, the malware does not execute the intended", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-437", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malicious logic. When using standard dynamic analysis, the test inputs are not guaranteed to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-438", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "trigger some of these conditions and, as a result, the corresponding malware behaviours may", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-439", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "be missed. To uncover trigger-based behaviours a multi-path analysis approach [15] explores", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-440", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "multiple execution paths of a malware. The analyser monitors how the malware code uses", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-441", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "condition-like inputs to make control-ﬂow decisions. For each decision point, the analyser", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-442", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "makes a snapshot of the current malware execution state and allows the malware to execute", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-443", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the correct malware path for the given input value; for example, the input value suggests", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-444", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that the triggering condition is not met and the malware path does not include the intended", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-445", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malicious logic. The analyser then comes back to the snapshot and rewrites the input value", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-446", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "so that the other branch is taken; for example, now the triggering condition is rewritten to be", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-447", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "true, and the malware branch is the intended malicious logic.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-448", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 14", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-449", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 17", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-450", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "4.1.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-451", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Finding Malware in a Haystack", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-452", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "In order to identify malware, we must ﬁrst have an understanding of how malware is distributed", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-453", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to their victims’ hosts. Malware is commonly distributed via an Internet download [69]. A", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-454", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "vulnerable Internet-facing program running on a computer can be exploited to download", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-455", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware onto the computer. A user on the computer can be socially engineered to open an", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-456", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "email attachment or visit a web page, both may lead to an exploit and malware download.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-457", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Whilst being downloaded onto a host, the malware’s contents can be seen in the payload", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-458", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "section of the network trafﬁc (i.e., network packet) [1]. As a defense, an Antivirus (AV) solution,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-459", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "or Intrusion Detection System (IDS), can analyse each network packet transported to an end-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-460", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "host for known malicious content, and block (prevent) the download. On the other hand, trafﬁc", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-461", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "content encrypted as HTTPS is widely and increasingly adopted by websites. Using domain", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-462", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "reputation systems [70], network trafﬁc coming from domains and IP addresses known to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-463", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "be associated with malicious activities can be automatically blocked without analysing the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-464", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "trafﬁc’s payload.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-465", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "After being installed on a computer, malware can reside within the host’s ﬁlesystem or memory", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-466", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "(or both). At this point, the malware can sleep (where the executable does nothing to the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-467", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "system) until a later point in time [71] as speciﬁed by the malware author. An AV or IDS can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-468", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "periodically scan the host’s ﬁlesystem and memory for known malicious programs [1]. As a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-469", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ﬁrst layer of defence, malware detectors can analyse static features that suggest malicious", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-470", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "executable contents. These include characteristics of instructions, control-ﬂow graphs, call", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-471", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "graphs, byte-value patterns [72] etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-472", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "If malware is not detected during its distribution state, i.e., a detection system misses its", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-473", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "presence in the payloads of network trafﬁc or the ﬁlesystem and memory of the end-host,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-474", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "it can still be detected when it executes and, for example, begins contacting its command-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-475", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and-control (C&C) server and performing malicious actions over the Internet or on the victim", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-476", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "computer system. An AV or IDS on the network perimeter continuously monitors network", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-477", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "packets travelling out of an end-host. If the AV or IDS sees that the host is contacting known", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-478", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malicious domain names or IP addresses it can surmise that the host has been infected by", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-479", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware. In addition, an AV or IDS on the end-host can look for behaviour patterns that are", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-480", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "associated with known malware activities, such as system or API calls that reveal the speciﬁc", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-481", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ﬁles read or written.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-482", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Evasion and Countermeasures", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-483", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Since Antivirus and IDS solutions can generate signatures", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-484", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "for malware executables, malware authors often morph the contents of their malware. They", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-485", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can change the contents of the executables while generating identically functional copies of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-486", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "their malware (i.e., the malware will perform the same dynamic behaviours when executed).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-487", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Since its static contents have been changed, the malware can evade an AV or IDS that uses", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-488", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "these static features. On the other hand, the malware can still be detected by an AV or IDS", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-489", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that uses the dynamic features (i.e., what the malware does).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-490", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Heuristics, e.g., signatures of a packing tool, or high entropy due to encryption, can be used to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-491", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detect and block contents that suggest the presence of packed malware, but this may lead to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-492", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "false alarms because packing can also be used by benign software and services, such as video", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-493", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "games, to protect proprietary information. The most reliable way to detect packed malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-494", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is to simply monitor its run-time behaviours because the packed code will be unpacked and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-495", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "executed, and the corresponding malicious behaviours can then be identiﬁed [58].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-496", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "In addition to changing the malware executable, an attacker can also change the contents", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-497", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 16", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-498", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 18", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-499", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of its malicious network trafﬁc by using polymorphism to modify payloads so that the same", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-500", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attacks look different across multiple trafﬁc captures. However, classic polymorphic malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-501", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "techniques [73] make the payloads look so different that even a naive IDS can easily differ-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-502", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "entiate them from benign payloads. On the other hand, with polymorphic malware blending", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-503", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attacks [59] malicious payloads can be made to look statistically similar to benign payloads.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-504", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware authors often implement updating routines, similar to updates for operating systems", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-505", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and applications such as web browsers and ofﬁce tools. This allows malware authors the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-506", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ﬂexibility to make changes to the malware to not only include new malicious activities but", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-507", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "also evade detection by AVs and IDS that have started using patterns of the old malware and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-508", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "its old behaviours.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-509", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "4.2", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-510", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Detection of Malware Attacks", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-511", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "We have discussed ways to identify static and behaviour patterns of malware, which can then", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-512", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "be used to detect instances of the same, or similar malware. Although many popular variants", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-513", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of malware families have existed at one time or another (e.g., Zeus [74, 75], Spyeye [76, 77],", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-514", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Mirai [78]), there will always be new malware families that cannot be detected by malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-515", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection models (such as AV signatures). Therefore, we need to go beyond identifying", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-516", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "speciﬁc malware instances: we need to detect malicious activities in general.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-517", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "4.2.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-518", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Host-based and Network-Based Monitoring", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-519", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "The most general approach to detect malicious activities is anomaly detection [60, 79, 61]. An", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-520", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "anomaly in system or network behaviour is an activity that deviates from normal (or seen)", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-521", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "behaviour. Anomaly detection can identify both old and new attacks. It is important to note", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-522", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that an anomalous behaviour is not the same as a malicious behaviour. Anomalous behaviours", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-523", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "describe behaviours that deviate from the norm, and of course it is possible to have abnormal", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-524", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "benign activities occurring on a system or network.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-525", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "On the other hand, a more efﬁcient and arguably more accurate approach to detect an old", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-526", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attack is to ﬁnd the patterns or signatures of the known attack activities [1]. This is often", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-527", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "called the misuse detection approach. Examples of signatures include: unauthorised write to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-528", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "system ﬁles (e.g., Windows Registry), connection to known botnet C&C servers, etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-529", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Two different, but complementary approaches to deploy attack detection systems are: 1)", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-530", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "host-based monitoring of system activities, and 2) network-based monitoring of trafﬁc. Host-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-531", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "based monitoring systems monitor activities that take place in a host, to determine if the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-532", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "host is compromised. These systems typically collect and monitor activities related to the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-533", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ﬁle system, processes, and system calls [1, 62]. Network-based monitoring systems analyse", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-534", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "activities that are network-wide, e.g., temporal characteristics of access patterns of network", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-535", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "trafﬁc ﬂows, the domain names the network hosts reach out to, the characteristics of the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-536", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "network packet payloads that cross the network perimeter, etc. [1, 63].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-537", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Let us look at several examples of malicious activities and the corresponding detection", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-538", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "approaches. The ﬁrst-generation spam detection systems focused on analysing the email", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-539", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "contents to distinguish legitimate messages from spam. Latter systems included network-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-540", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "level behaviours indicative of spam trafﬁc [80], e.g., spikes in email trafﬁc volumes due to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-541", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "large amount of spam messages being sent.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-542", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "For DDoS detection, the main idea is to analyse the statistical properties of trafﬁc, e.g., the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-543", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 17", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-544", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 19", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-545", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "number of requests within a short time window sent to a network server. Once a host is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-546", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "identiﬁed to be sending such trafﬁc, it is considered to be participating in a DDoS attack and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-547", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "its trafﬁc is blocked. Attackers have evolved their techniques to DDoS attacks, in particular,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-548", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "by employing multiple compromised hosts, or bots, to send trafﬁc in a synchronised manner,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-549", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "e.g., by using DDoS-as-a-service malware kits [81]. That is, each bot no longer needs to send", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-550", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "a large amount of trafﬁc. Correspondingly, DDoS detection involves correlating hosts that", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-551", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "send very similar trafﬁc to the victim at the same time.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-552", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "For ransomware detection, the main approaches include monitoring host activities involved in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-553", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "encryption. If there is a process making a large number of signiﬁcant modiﬁcations to a large", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-554", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "number of ﬁles, this is indicative of a ransomware attack [82]. The ‘signiﬁcant’ modiﬁcations", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-555", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "reﬂect the fact that encrypting a ﬁle will result in its contents changing drastically from its", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-556", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "original contents.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-557", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Host-based and network-based monitoring approaches can be beneﬁcially combined. For", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-558", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "example, if we see contents from various sensitive ﬁles on our system (e.g., ﬁnancial records,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-559", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "password-related ﬁles, etc.) being transmitted in network trafﬁc, it is indicative that data are", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-560", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "being exﬁltrated (without the knowledge and consent of the user) to an attacker’s server. We", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-561", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can then apply host-based analysis tools to further determine the attack provenance and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-562", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "effects on a victim host [83].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-563", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Since many malicious activities are carried out by botnets, it is important to include botnet", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-564", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection methods. By deﬁnition, bots of the same botnet are controlled by the same attacker", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-565", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and perform coordinated malicious activities [84, 64]. Therefore, a general approach to botnet", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-566", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection is to look for synchronised activities both in C&C like trafﬁc and malicious trafﬁc", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-567", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "(e.g., scan, spam, DDoS, etc.) across the hosts of a network.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-568", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "4.2.2", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-569", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Machine Learning-Based Security Analytics", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-570", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Since the late 1990s, machine learning (ML) has been applied to automate the process of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-571", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "building models for detecting malware and attacks. The beneﬁt of machine learning is its", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-572", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ability to generalise over a population of samples, given various features (descriptions) of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-573", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "those samples. For example, after providing an ML algorithm samples of different malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-574", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "families for ‘training’, the resultant model is able to classify new, unseen malware as belonging", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-575", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to one of those families [65].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-576", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Both static and dynamic features of malware and attacks can be employed by ML-based", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-577", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection models. Examples of static features include: instructions, control-ﬂow graphs,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-578", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "call graphs, etc. Examples of dynamic features include: system call sequences and other", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-579", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "statistics (e.g., frequency and existence of system calls), system call parameters, data-ﬂow", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-580", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "graphs [85], network payload features, etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-581", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "An example of success stories in applying machine learning to detect malware and attacks is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-582", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet detection [86]. ML techniques were developed to efﬁciently classify domain names as", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-583", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ones produced by domain generation algorithm (DGA), C&C domains, or legitimate domains", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-584", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "using features extracted from DNS trafﬁc. ML techniques have also been developed to identify", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-585", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "C&C servers as well as bots in an enterprise network based on features derived from network", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-586", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "trafﬁc data [64].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-587", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "A major obstacle in applying (classical) machine learning to security is that we must select or", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-588", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "even engineer features that are useful in classifying benign and malicious activities. Feature", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-589", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "engineering is very knowledge- and labour- intensive and is the bottleneck in applying ML to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-590", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 18", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-591", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 20", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-592", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "any problem domain. Deep learning has shown some promise in learning from a large amount", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-593", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of data without much feature engineering, and already has great success in applications such", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-594", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "as image classiﬁcation [87]. However, unlike many classical ML models (such as decision", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-595", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "trees and inductive rules) that are human-readable, and hence reviewable by security analysts", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-596", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "before making deployment decisions, deep learning outputs blackbox models that are not", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-597", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "readable and not easily explainable. It is often not possible to understand what features are", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-598", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "being used (and how) to arrive at a classiﬁcation decision. That is, with deep learning, security", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-599", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "analysts can no longer check if the output even makes sense from the point-of-view of domain", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-600", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "or expert knowledge.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-601", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "4.2.3", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-602", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Evasion, Countermeasures, and Limitations", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-603", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Attackers are well aware of the detection methods that have been developed, and they are", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-604", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "employing evasion techniques to make their attacks hard to detect. For example, they can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-605", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "limit the volume and intensity of attack activities to stay below the detection threshold, and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-606", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "they can mimic legitimate user behaviours such as sending stolen data (a small amount at a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-607", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "time) to a ‘drop site’ only when a user is also browsing the Internet. Every misuse or anomaly", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-608", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection model is potentially evadable.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-609", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "It should also come as no surprise that no sooner had researchers begun using ML than", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-610", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attackers started to ﬁnd ways to defeat the ML-based detection models.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-611", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "One of the most famous attacks is the Mimicry attack on detection models based on system", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-612", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "call data [66]. The idea is simple: the goal is to morph malicious features to look exactly the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-613", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "same as the benign features, so that the detection models will mistakenly classify the attack", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-614", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "as benign. The Mimicry attack inserts system calls that are inconsequential to the intended", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-615", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malicious actions so that the resultant sequences, while containing system calls for malicious", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-616", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "activities, are still legitimate because such sequences exist in benign programs. A related", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-617", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attack is polymorphic blending [59] that can be used to evade ML models based on network", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-618", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "payload statistics (e.g., the frequency distribution of n-grams in payload data to a network", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-619", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "service). An attack payload can be encoded and padded with additional n-grams so that it", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-620", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "matches the statistics of benign payloads. Targeted noise injection [67] is an attack designed", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-621", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to trick a machine-learning algorithm, while training a detection model, to focus on features", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-622", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "not belonging to malicious activities at all. This attack exploits a fundamental weakness of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-623", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "machine learning: garbage in, garbage out. That is, if you give a machine-learning algorithm", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-624", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "bad data, then it will learn to classify data ‘badly’. For example, an attacker can insert various", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-625", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "no-op features into the attack payload data, which will statistically produce a strong signal for", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-626", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the ML algorithm to select them as ‘the important, distinguishing features’. As long as such", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-627", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "features exist, and as they are under the attacker’s control, any ML algorithm can be misled to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-628", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "learn an incorrect detection model. Noise injection is also known as ‘data poisoning’ in the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-629", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "machine learning community.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-630", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "We can make attacks on ML harder to succeed. For example, one approach is to squeeze", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-631", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "features [88] so that the feature set is not as obvious to an attacker, and the attacker has a", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-632", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "smaller target to hit when creating adversarial samples. Another approach is to train separating", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-633", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "classes, which distance the decision boundary between classes [89]. This makes it more", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-634", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "difﬁcult for an attacker to simply make small changes to features to ‘jump’ across decision", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-635", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "boundaries and cause the model to misclassify the sample. Another interesting approach", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-636", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is to have an ML model forget samples it has learned over time, so that an attacker has to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-637", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "continuously poison every dataset [90].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-638", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 19", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-639", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 21", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-640", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "A more general approach is to employ a combination of different ML-based detection models", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-641", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "so that defeating all of them simultaneously is very challenging. For example, we can model", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-642", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "multiple feature sets simultaneously through ensemble learning, i.e., using multiple classiﬁers", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-643", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "trained on different feature sets to classify a sample rather than relying on singular classiﬁer", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-644", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and feature set. This would force an attacker to have to create attacks that can evade each", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-645", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and every classiﬁer and feature set [68].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-646", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "As discussed earlier, deep learning algorithms produce models that cannot be easily examined.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-647", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "But if we do not understand how a detection model really works, we cannot foresee how", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-648", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attackers can attempt to defeat it and how we can improve its robustness. That is, a model", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-649", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that seemingly performs very well on data seen thus far can, in fact, be very easily defeated in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-650", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the future - we just have no way of knowing. For example, in image recognition it turned out", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-651", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that some deep learning models focused on high-frequency image signals (that are not visible", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-652", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to the human eye) rather than the structural and contextual information of an image (which is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-653", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "more relevant for identifying an object) and, as a result, a small change in the high-frequency", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-654", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "data is sufﬁcient to cause a mis-classiﬁcation by these models, while to the human eye the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-655", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "image has not changed at all [91].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-656", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "There are promising approaches to improve the ‘explainability’ of deep learning models. For", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-657", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "example, an attention model [92] can highlight locations within an image to show which", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-658", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "portions it is focusing on when classifying the image. Another example is LEMNA [93], which", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-659", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "generates a small set of interpretable features from an input sample to explain how the sample", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-660", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is classiﬁed, essentially approximating a local area of the complex deep learning decision", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-661", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "boundary using a simpler interpretable model.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-662", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "In both the machine learning and security communities, adversarial machine learning [94]", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-663", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is and will continue to be a very important and active research area. In general, attacks on", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-664", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "machine learning can be categorised as data poisoning (i.e., injecting malicious noise into", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-665", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "training data) and evasion (i.e., morphing the input to cause mis-classiﬁcation). What we", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-666", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "have discussed above are just examples of evasion and poisoning attacks on ML models for", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-667", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "security analytics. These attacks have motivated the development of new machine-learning", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-668", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "paradigms that are more robust against adversarial manipulations, and we have discussed", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-669", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "here examples of promising approaches.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-670", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "In general, attack detection is a very challenging problem. A misuse detection method which", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-671", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is based on patterns of known attacks is usually not effective against new attacks or even new", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-671", "line_number": 671, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-672", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "variants of old attacks. An anomaly detection method which is based on a normal proﬁle can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-673", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "produce many false alarms because it is often impossible to include all legitimate behaviours", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-673", "line_number": 673, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-674", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in a normal proﬁle. While machine learning can be used to automatically produce detection", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-675", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "models, potential ‘concept drift’ can render the detection models less effective over time [95].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-676", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "That is, most machine-learning algorithms assume that the training data and the testing data", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-677", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "have the same statistical properties, whereas in reality, user behaviours and network and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-677", "line_number": 677, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-678", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "system conﬁgurations can change after a detection model is deployed.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-679", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 20", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-680", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 22", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-682", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "MALWARE RESPONSE", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-683", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[96, 97, 98, 99, 100, 101]", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-684", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "If we have an infected host in front of us, we can remove the malware, and recover the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-685", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "data and services from secure backups. At the local network access point, we can update", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-686", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "corresponding Firewall and Network intrusion detection system rules, to prevent and detect", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-687", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "future attacks. It is unfeasible to execute these remediation strategies if the infected machines", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-688", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "cannot be accessed directly (e.g., they are in private residences), and if the scale of infection", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-689", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "is large. In these cases, we can attempt to take down malware command-and-control (C&C)", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-690", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "infrastructure instead [96, 97], typically at the internet service provider (ISP) or the top-level", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-691", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "domain (TLD) level. Takedowns aim to disrupt the malware communication channel, even if", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-692", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the hosts remain infected. Last but not least, we can perform attack attribution using multiple", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-692", "line_number": 692, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-693", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "sources of data to identify the actors behind the attack.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-694", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "5.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-695", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Disruption of Malware Operations", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-696", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "There are several types of takedowns to disrupt malware operations. If the malware uses", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-697", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "domain names to look up and to communicate with centralised C&C servers, we perform", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-698", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "takedown of C&C domains by ‘sinkholing’ the domains, i.e., making the C&C domains resolve to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-699", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the defender’s servers so that botnet trafﬁc is ‘trapped’ (that is, redirected) to these servers [96].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-700", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "If the malware uses peer-to-peer (P2P) protocol as a decentralised C&C mechanism, we can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-701", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "partition the P2P botnet into isolated sub-networks, create a sinkholing node, or poison the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-702", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "communication channel by issuing commands to stop the malicious activities [97]. However, it", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-703", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "should be borne in mind that, in most territories active defence or intelligence gathering, such", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-704", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "as hack-backs, access to or modiﬁcation of servers, DNS, or networks, is unlawful without", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-705", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "appropriate legal authority.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-706", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "5.1.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-707", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Evasion and Countermeasures", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-708", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware often utilises agility provided by DNS fast-ﬂux network and Domain-name Generation", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-708", "line_number": 708, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-709", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Algorithms (DGAs) to evade the takedown. A DNS fast-ﬂux network points the C&C domain", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-709", "line_number": 709, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-710", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "names to a large pool of compromised machines, and the resolution changes rapidly [102].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-710", "line_number": 710, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-711", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "DGAs make use of an algorithm to automatically generate candidate C&C domains, usually", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-711", "line_number": 711, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-712", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "based on some random seed. Among the algorithm-generated domains, the botmaster can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-712", "line_number": 712, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-713", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "pick a few to register (e.g., on a daily basis) and make them resolve to the C&C servers. What", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-713", "line_number": 713, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-714", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "makes the matter worse are the so-called bullet-proof hosting (BPH) services, which are re-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-715", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "silient against takedowns because they ignore abuse complaints and takedown requests [98].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-716", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "We can detect the agile usage of C&C mechanisms. As the botmaster has little control of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-717", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the IP address diversity and down-time for compromised machines in a fast-ﬂux network, we", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-717", "line_number": 717, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-718", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can use these features to detect fast-ﬂux [103]. We can also identify DGA domains by mining", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-718", "line_number": 718, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-719", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "NXDomains trafﬁc using infected hosts features and domain name characteristic features [86],", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-719", "line_number": 719, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-720", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "or reverse-engineering the malware to recover the algorithm. To counter bullet-proof hosting,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-720", "line_number": 720, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-721", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "we need to put legal, political and economic pressures on hosting providers. For example, the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-721", "line_number": 721, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-722", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "FBI’s operation ghost click issued a court order for the takedown of DNSChanger [104, 105].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-722", "line_number": 722, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-723", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Malware has also become increasingly resilient by including contingency plans. A centralised", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-723", "line_number": 723, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-724", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet can have P2P as a fallback mechanism in case the DNS C&C fails. Likewise, a P2P", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-725", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet can use DNS C&C as a contingency plan. A takedown is effective only if all the C&C", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-726", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 21", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-726", "line_number": 726, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-727", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 23", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-727", "line_number": 727, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-728", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "channels are removed from the malware. Otherwise, the malware can bootstrap the C&C", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-729", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "communication again using the remaining channels. If we hastily conduct botnet takedowns", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-729", "line_number": 729, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-730", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "without thoroughly enumerating and verifying all the possible C&C channels, we can fail to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-731", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "actually disrupt the malware operations and risk collateral damage to benign machines. For", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-732", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "example, the Kelihos takedown [106] did not account for the backup P2P channel, and the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-732", "line_number": 732, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-733", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "3322.org takedown disabled the dynamic DNS service for many benign users.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-733", "line_number": 733, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-734", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "We need to have a complete view of the C&C domains and other channels that are likely to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-734", "line_number": 734, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-735", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "be used by a botnet, by using multiple sources of intelligence including domain reputation,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-735", "line_number": 735, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-736", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware query association and malware interrogation [96]. We start from a seed set of C&C", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-736", "line_number": 736, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-737", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "domains used by a botnet. Then, we use passive DNS data to retrieve related historical IP", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-738", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "addresses associated with the seed set. We remove sinkholing, parking, and cloud hosting", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-738", "line_number": 738, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-739", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "provider IP addresses from them to mitigate the collateral damage from the takedowns. The", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-739", "line_number": 739, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-740", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "resulting IPs can also give us related historical domains that have resolved to them. After", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-740", "line_number": 740, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-741", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "following these steps, we have an extended set of domains that are likely to be used by the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-741", "line_number": 741, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-742", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet. This set captures agile and evasive C&C behaviours such as fast-ﬂux networks. Within", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-743", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the extended set, we combine 1) low reputation domains, 2) domains related to malware,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-744", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and 3) other domains obtained by interrogating the related malware. Malware interrogation", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-745", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "simulates situations where the default C&C communication mechanism fails through blocking", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-746", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "DNS resolution and TCP connection [101]. By doing so, we can force the malware to reveal", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-747", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the backup C&C plans, e.g., DGA or P2P. After enumerating the C&C infrastructure, we can", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-748", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "disable the complete list of domains to take the botnet down.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-749", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "5.2", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-750", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Attribution", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-750", "line_number": 750, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-751", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Ideally, law enforcement wants to identify the actual criminal behind the attacks. Identifying", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-752", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the virtual attacker is an important ﬁrst step toward this goal. An attacker may have consistent", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-753", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "coding styles, reuse the same resources or infrastructures, or use similar C&C practices.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-754", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "From the malware data, we can compare its ‘characteristics’ with those of known historical", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-755", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "adversaries, e.g., coding styles, server conﬁgurations, etc. [99]. At the source code level, we", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-756", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "can use features that reﬂect programming styles and code quality. For instance, linguistic", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-756", "line_number": 756, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-757", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "features, formatting style, bugs and vulnerabilities, structured features such as execution", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-757", "line_number": 757, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-758", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "path, abstract syntax tree (AST), Control Flow Graph (CFG), and program dependence graph", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-759", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "(PDG) can be used. Other features extracted from the binary ﬁle can also indicate authorship,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-760", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "e.g., the sequence of instructions and register ﬂow graph.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-760", "line_number": 760, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-761", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "From the enumerated attack infrastructure, we can associate the expanded domain name set", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-761", "line_number": 761, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-762", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "with previously known adversaries. For instance, unknown TDSS/TDL4 botnet ad-fraud C&C", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-762", "line_number": 762, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-763", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "domains share the same IP infrastructure with known domains, and they are registered by the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-763", "line_number": 763, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-764", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "same set of email addresses and name servers. This allows us to attribute unknown domains", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-765", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "to known TDSS/TDL4 actors [100].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-766", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 22", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-767", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 24", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-768", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "5.2.1", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-769", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Evasion and Countermeasures", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-770", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Many malware authors reuse different kits for the convenience offered by the business model", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-771", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of the underground economy. Common for-sale kits allow malware authors to easily customise", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-772", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "their own malware. They can also evade attribution by intentionally planting ‘false ﬂags’ in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-773", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-774", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Domain registration information, WHOIS, is a strong signal for attack attribution. The same", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-775", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attacker often uses a fake name, address and company information following a pattern.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-776", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "However, WHOIS privacy protection has become ubiquitous and is even offered for free for the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-777", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ﬁrst year when a user purchases a domain name. This removes the registration information", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-778", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that could be used for attack attribution.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-779", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "We need to combine multiple, different streams of data for the analysis. For instance, malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-780", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "interrogation helps recover more C&C domains used by the fallback mechanism, which offers", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-781", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "more opportunity for attribution [101, 107].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-782", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "CONCLUSION", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-783", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Attackers use malware to carry out malicious activities on their behalf. Malware can reside", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-784", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in any layer of the system stack, and can be a program by itself or embedded in another", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-785", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "application or document. Modern malware comes with a support infrastructure for coordinated", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-786", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attacks and automated updates, and can operate low-and-slow and cover its tracks to avoid", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-787", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection and attribution. While malware can cause wide-spread infection and harm on the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-788", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Internet, it can also be customised for attacks targeting a speciﬁc organisation. Malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-789", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "analysis is an important step in understanding malicious behaviours and properly updating", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-790", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "our attack prevention and detection systems. Malware employs a wide range of evasion", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-791", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "techniques, which include detecting the analysis environment, obfuscating malicious code,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-792", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "using trigger-conditions to execute, and applying polymorphism to attack payloads, etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-793", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Accordingly, we need to make analysis environments transparent to malware, continue to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-794", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "develop specialised program analysis algorithms and machine-learning based detection", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-795", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "techniques, and apply a combination of these approaches. Response to malware attacks", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-796", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "goes beyond detection and mitigation, and can include take-down and attribution, but the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-796", "line_number": 796, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-797", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "challenge is enumerating the entire malware infrastructure, and correlating multiple pieces of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-798", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "evidence to avoid false ﬂags planted by the attackers.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-799", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "CROSS-REFERENCE OF TOPICS VS REFERENCE MATERIAL", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-800", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 23", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-801", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 26", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-802", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Network and Distributed System Security Symposium (NDSS), 2008.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-803", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[10] V. Chipounov, V. Kuznetsov, and G. Candea, “S2E: A platform for in-vivo multi-path", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-804", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "analysis of software systems,” ACM Sigplan Notices, vol. 46, no. 3, pp. 265–278, 2011.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-805", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[11] S. K. Cha, T. Avgerinos, A. Rebert, and D. Brumley, “Unleashing mayhem on binary code,”", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-806", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in IEEE Symposium on Security and Privacy (SP).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-807", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2012, pp. 380–394.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-808", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[12] D. A. Ramos and D. R. Engler, “Under-constrained symbolic execution: Correctness", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-809", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "checking for real code.” in USENIX Security Symposium, 2015, pp. 49–64.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-810", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[13] C. Kreibich, N. Weaver, C. Kanich, W. Cui, and V. Paxson, “GQ: Practical containment", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-811", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "for measuring modern malware systems,” in Proceedings of the 2011 ACM SIGCOMM", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-812", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "conference on Internet measurement conference.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-813", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ACM, 2011, pp. 397–412.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-813", "line_number": 813, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-814", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[14] A. Dinaburg, P. Royal, M. Sharif, and W. Lee, “Ether: malware analysis via hardware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-814", "line_number": 814, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-815", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "virtualization extensions,” in Proceedings of the 15th ACM conference on Computer and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-816", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "communications security.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-817", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ACM, 2008, pp. 51–62.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-817", "line_number": 817, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-818", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[15] A. Moser, C. Kruegel, and E. Kirda, “Exploring multiple execution paths for malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-818", "line_number": 818, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-819", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "analysis,” in IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-820", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2007.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-821", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[16] D. Kirat, G. Vigna, and C. Kruegel, “Barecloud: Bare-metal analysis-based evasive mal-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-822", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ware detection.” in USENIX Security Symposium, 2014, pp. 287–301.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-823", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[17] M. Sharif, A. Lanzi, J. Gifﬁn, and W. Lee, “Automatic reverse engineering of malware", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-824", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "emulators,” in 30th IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-825", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2009, pp. 94–109.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-826", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[18] S. Mariani, L. Fontana, F. Gritti, and S. D’Alessio, “PinDemonium: a DBI-based generic", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-827", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "unpacker for Windows executables,” in Black Hat USA 2016, 2016.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-828", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[19] E. Kenneally, M. Bailey, and D. Maughan, “A framework for understanding and applying", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-829", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ethical principles in network and security research,” in Workshop on Ethics in Computer", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-830", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Security Research (WECSR ’10), 2010.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-831", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[20] M. K. Shankarapani, S. Ramamoorthy, R. S. Movva, and S. Mukkamala, “Malware detec-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-832", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tion using assembly and API call sequences,” Journal in computer virology, vol. 7, no. 2,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-833", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "pp. 107–119, 2011.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-834", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[21] M. Bailey, J. Oberheide, J. Andersen, Z. M. Mao, F. Jahanian, and J. Nazario, “Automated", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-835", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "classiﬁcation and analysis of internet malware,” in International Workshop on Recent", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-836", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Advances in Intrusion Detection.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-837", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Springer, 2007, pp. 178–197.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-838", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[22] M. Zalewski, “American fuzzy lop.” [Online]. Available: http://lcamtuf.coredump.cx/aﬂ/", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-839", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[23] I. Yun, S. Lee, M. Xu, Y. Jang, and T. Kim, “QSYM: A practical concolic execution engine", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-840", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tailored for hybrid fuzzing,” in Proceedings of the 27th USENIX Security Symposium,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-841", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "2018.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-842", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[24] C. Cadar and K. Sen, “Symbolic execution for software testing: Three decades later,” in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-843", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Communications of the ACM, 2013.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-844", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[25] D. Brumley, I. Jager, T. Avgerinos, and E. J. Schwartz, “BAP: A binary analysis platform,”", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-845", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in International Conference on Computer Aided Veriﬁcation.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-845", "line_number": 845, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-846", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Springer, 2011.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-846", "line_number": 846, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-847", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[26] C. Cadar, D. Dunbar, and D. R. Engler, “KLEE: Unassisted and automatic generation", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-847", "line_number": 847, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-848", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of high-coverage tests for complex systems programs,” in 8th USENIX Symposium on", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-849", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Operating Systems Design and Implementation, vol. 8, 2008, pp. 209–224.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-850", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[27] D. Song, D. Brumley, H. Yin, J. Caballero, I. Jager, M. G. Kang, Z. Liang, J. Newsome,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-851", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "P. Poosankam, and P. Saxena, “BitBlaze: A new approach to computer security via binary", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-852", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "analysis,” in International Conference on Information Systems Security.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-853", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Springer, 2008,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-854", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "pp. 1–25.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-855", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[28] M. B¨ohme, V.-T. Pham, M.-D. Nguyen, and A. Roychoudhury, “Directed greybox fuzzing,”", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-856", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in Proceedings of the 2017 ACM SIGSAC Conference on Computer and Communications", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-857", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Security.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-858", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ACM, 2017, pp. 2329–2344.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-859", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[29] V. Kuznetsov, J. Kinder, S. Bucur, and G. Candea, “Efﬁcient state merging in symbolic", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-860", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 25", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-861", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 28", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-862", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and Anti-VM Technologies, Black Hat USA Conference, 2012.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-863", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[54] A. Vasudevan and R. Yerraballi, “Cobra: Fine-grained malware analysis using stealth", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-864", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "localized-executions,” in IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-865", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2006.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-866", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[55] C. Willems, T. Holz, and F. Freiling, “Toward automated dynamic malware analysis using", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-867", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "CWSandbox,” IEEE Security & Privacy, vol. 5, no. 2, 2007.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-868", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[56] F. Peng, Z. Deng, X. Zhang, D. Xu, Z. Lin, and Z. Su, “X-Force: Force-executing binary", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-869", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "programs for security applications,” in The 23rd USENIX Security Symposium (USENIX", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-870", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Security 14, 2014, pp. 829–844.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-871", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[57] L.-K. Yan, M. Jayachandra, M. Zhang, and H. Yin, “V2E: Combining hardware virtualization", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-872", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and softwareemulation for transparent and extensible malware analysis,” ACM Sigplan", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-873", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Notices, vol. 47, no. 7, pp. 227–238, 2012.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-874", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[58] P. Royal, M. Halpin, D. Dagon, R. Edmonds, and W. Lee, “Polyunpack: Automating the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-875", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "hidden-code extraction of unpack-executing malware,” in Computer Security Applica-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-876", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tions Conference, 2006. ACSAC’06. 22nd Annual.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-877", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2006, pp. 289–300.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-877", "line_number": 877, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-878", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[59] P. Fogla, M. I. Sharif, R. Perdisci, O. M. Kolesnikov, and W. Lee, “Polymorphic blending", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-878", "line_number": 878, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-879", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "attacks,” in USENIX Security, 2006.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-879", "line_number": 879, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-880", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[60] D. Denning and P. G. Neumann, Requirements and model for IDES-a real-time intrusion-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-880", "line_number": 880, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-881", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection expert system.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-881", "line_number": 881, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-882", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "SRI International, 1985.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-882", "line_number": 882, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-883", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[61] H. S. Javitz and A. Valdes, “The NIDES statistical component:", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-883", "line_number": 883, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-884", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Description", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-884", "line_number": 884, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-885", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and justiﬁcation,” Contract, vol. 39, no. 92-C, p. 0015, 1993. [Online]. Available:", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-885", "line_number": 885, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-886", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "http://www.csl.sri.com/papers/statreport/", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-886", "line_number": 886, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-887", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[62] K. Ilgun, R. Kemmerer, and P. Porras, “State transition analysis: A rule-based intrusion", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-887", "line_number": 887, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-888", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection approach,” IEEE Transactions on Software Engineering, vol. 21, no. 3, pp. 181–", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-888", "line_number": 888, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-889", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "199, 1995.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-889", "line_number": 889, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-890", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[63] V. Paxson, “Bro: a system for detecting network intruders in real-time,” Computer net-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-890", "line_number": 890, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-891", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "works, vol. 31, no. 23-24, pp. 2435–2463, 1999.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-891", "line_number": 891, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-892", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[64] G. Gu, R. Perdisci, J. Zhang, and W. Lee, “BotMiner: Clustering analysis of network trafﬁc", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-892", "line_number": 892, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-893", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "for protocol- and structure-independent botnet detection,” in Proceedings of the 17th", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-893", "line_number": 893, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-894", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "USENIX Security Symposium (Security’08), 2008.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-894", "line_number": 894, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-895", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[65] W. Lee, S. J. Stolfo, and K. W. Mok, “A data mining framework for building intrusion", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-895", "line_number": 895, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-896", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "detection models,” in Proceedings of the 1999 IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-896", "line_number": 896, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-897", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 1999, pp. 120–132.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-897", "line_number": 897, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-898", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[66] D. Wagner and P. Soto, “Mimicry attacks on host-based intrusion detection systems,”", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-898", "line_number": 898, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-899", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in Proceedings of the 9th ACM Conference on Computer and Communications Security.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-899", "line_number": 899, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-900", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ACM, 2002, pp. 255–264.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-900", "line_number": 900, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-901", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[67] R. Perdisci, D. Dagon, W. Lee, P. Fogla, and M. Sharif, “Misleading worm signature", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-901", "line_number": 901, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-902", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "generators using deliberate noise injection,” in 2006 IEEE Symposium on Security and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-902", "line_number": 902, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-903", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Privacy (S&P’06).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-903", "line_number": 903, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-904", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2006, pp. 15–pp.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-904", "line_number": 904, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-905", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[68] A. Kantchelian, J. D. Tygar, and A. D. Joseph, “Evasion and hardening of tree ensemble", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-905", "line_number": 905, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-906", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "classiﬁers,” arXiv preprint arXiv:1509.07892, 2015.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-906", "line_number": 906, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-907", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[69] G. Cleary, M. Corpin, O. Cox, H. Lau, B. Nahorney, D. O’Brien, B. O’Gorman, J.-P. Power,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-907", "line_number": 907, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-908", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "S. Wallace, P. Wood, and C. Wueest, “Internet security threat report,” Symantec, Tech.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-908", "line_number": 908, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-909", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Rep., 2018.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-909", "line_number": 909, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-910", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[70] M. Antonakakis, R. Perdisci, D. Dagon, W. Lee, and N. Feamster, “Building a dynamic", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-910", "line_number": 910, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-911", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "reputation system for DNS,” in USENIX security symposium, 2010, pp. 273–290.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-911", "line_number": 911, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-912", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[71] C. Kolbitsch, E. Kirda, and C. Kruegel, “The power of procrastination: detection and miti-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-912", "line_number": 912, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-913", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "gation of execution-stalling malicious code,” in Proceedings of the 18th ACM conference", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-913", "line_number": 913, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-914", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "on Computer and communications security.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-914", "line_number": 914, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-915", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ACM, 2011, pp. 285–296.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-915", "line_number": 915, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-916", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[72] K. Wang and S. J. Stolfo, “Anomalous payload-based network intrusion detection,” in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-916", "line_number": 916, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-917", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 27", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-917", "line_number": 917, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-918", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 30", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-918", "line_number": 918, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-919", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[90] Y. Cao and J. Yang, “Towards making systems forget with machine unlearning,” in 2015", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-919", "line_number": 919, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-920", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-920", "line_number": 920, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-921", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2015, pp. 463–480.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-921", "line_number": 921, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-922", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[91] N. Carlini and D. Wagner, “Towards evaluating the robustness of neural networks,” in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-922", "line_number": 922, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-923", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-923", "line_number": 923, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-924", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2017, pp. 39–57.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-924", "line_number": 924, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-925", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[92] D. Bahdanau, K. Cho, and Y. Bengio, “Neural machine translation by jointly learning to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-925", "line_number": 925, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-926", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "align and translate,” arXiv preprint arXiv:1409.0473, 2014.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-926", "line_number": 926, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-927", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[93] W. Guo, D. Mu, J. Xu, P. Su, G. Wang, and X. Xing, “LEMNA: Explaining deep learning", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-927", "line_number": 927, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-928", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "based security applications,” in Proceedings of the 25th ACM Conference on Computer", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-928", "line_number": 928, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-929", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and Communications Security (CCS ’18), 2018.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-929", "line_number": 929, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-930", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[94] N. Dalvi, P. Domingos, S. Sanghai, D. Verma, and others, “Adversarial classiﬁcation,” in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-930", "line_number": 930, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-931", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Proceedings of the tenth ACM SIGKDD international conference on Knowledge discovery", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-931", "line_number": 931, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-932", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and data mining.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-932", "line_number": 932, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-933", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ACM, 2004, pp. 99–108.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-933", "line_number": 933, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-934", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[95] R. Jordaney, K. Sharad, S. K. Dash, Z. Wang, D. Papini, I. Nouretdinov, and L. Cavallaro,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-934", "line_number": 934, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-935", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "“Transcend: Detecting concept drift in malware classiﬁcation models,” in Proceedings", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-935", "line_number": 935, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-936", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of the 26th USENIX Security Symposium, 2017.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-936", "line_number": 936, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-937", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[96] Y. Nadji, M. Antonakakis, R. Perdisci, D. Dagon, and W. Lee, “Beheading hydras: Perform-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-937", "line_number": 937, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-938", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ing effective botnet takedowns,” in Proceedings of the 2013 ACM SIGSAC Conference", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-938", "line_number": 938, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-939", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "on Computer & Communications Security.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-939", "line_number": 939, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-940", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ACM, 2013, pp. 121–132.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-940", "line_number": 940, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-941", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[97] C. Rossow, D. Andriesse, T. Werner, B. Stone-Gross, D. Plohmann, C. J. Dietrich, and", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-941", "line_number": 941, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-942", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "H. Bos, “SoK: P2PWNED-modeling and evaluating the resilience of peer-to-peer botnets,”", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-942", "line_number": 942, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-943", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "in IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-943", "line_number": 943, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-944", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2013, pp. 97–111.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-944", "line_number": 944, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-945", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[98] S. Alrwais, X. Liao, X. Mi, P. Wang, X. Wang, F. Qian, R. Beyah, and D. McCoy, “Under the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-945", "line_number": 945, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-946", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "shadow of sunshine: Understanding and detecting bulletproof hosting on legitimate", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-946", "line_number": 946, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-947", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "service provider networks,” in IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-947", "line_number": 947, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-948", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "IEEE, 2017, pp.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-948", "line_number": 948, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-949", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "805–823.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-949", "line_number": 949, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-950", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[99] S. Alrabaee, P. Shirani, M. Debbabi, and L. Wang, “On the feasibility of malware author-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-950", "line_number": 950, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-951", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ship attribution,” in International Symposium on Foundations and Practice of Security.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-951", "line_number": 951, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-952", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Springer, 2016, pp. 256–272.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-952", "line_number": 952, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-953", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[100] Y. Chen, P. Kintis, M. Antonakakis, Y. Nadji, D. Dagon, W. Lee, and M. Farrell, “Financial", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-953", "line_number": 953, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-954", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "lower bounds of online advertising abuse,” in International conference on Detection of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-954", "line_number": 954, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-955", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Intrusions and Malware, and Vulnerability Assessment.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-955", "line_number": 955, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-956", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Springer, 2016, pp. 231–254.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-956", "line_number": 956, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-957", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[101] Y. Nadji, M. Antonakakis, R. Perdisci, and W. Lee, “Understanding the prevalence and use", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-957", "line_number": 957, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-958", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of alternative plans in malware with network games,” in Proceedings of the 27th Annual", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-958", "line_number": 958, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-959", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Computer Security Applications Conference.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-959", "line_number": 959, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-960", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ACM, 2011, pp. 1–10.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-960", "line_number": 960, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-961", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[102] M. Konte, N. Feamster, and J. Jung, “Fast ﬂux service networks: Dynamics and roles in", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-961", "line_number": 961, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-962", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "hosting online scams,” Georgia Institute of Technology, Tech. Rep., 2008.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-962", "line_number": 962, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-963", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[103] T. Holz, C. Gorecki, K. Rieck, and F. C. Freiling, “Measuring and detecting fast-ﬂux service", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-963", "line_number": 963, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-964", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "networks.” in NDSS, 2008.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-964", "line_number": 964, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-965", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[104] FBI", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-965", "line_number": 965, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-966", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "New", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-966", "line_number": 966, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-967", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "York", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-967", "line_number": 967, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-968", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Field", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-968", "line_number": 968, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-969", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Ofﬁce,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-969", "line_number": 969, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-970", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "“Operation", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-970", "line_number": 970, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-971", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ghost", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-971", "line_number": 971, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-972", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "click:", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-972", "line_number": 972, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-973", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Interna-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-973", "line_number": 973, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-974", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tional", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-974", "line_number": 974, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-975", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "cyber", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-975", "line_number": 975, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-976", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ring", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-976", "line_number": 976, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-977", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "that", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-977", "line_number": 977, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-978", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "infected", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-978", "line_number": 978, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-979", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "millions", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-979", "line_number": 979, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-981", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "computers", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-981", "line_number": 981, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-982", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "disman-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-982", "line_number": 982, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-983", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tled,”", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-983", "line_number": 983, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-984", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "April", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-984", "line_number": 984, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-985", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "2012.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-985", "line_number": 985, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-986", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[Online].", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-986", "line_number": 986, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-987", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Available:", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-987", "line_number": 987, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-988", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "https://www.fbi.gov/news/stories/", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-988", "line_number": 988, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-989", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "international-cyber-ring-that-infected-millions-of-computers-dismantled", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-989", "line_number": 989, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-990", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[105] W. Meng, R. Duan, and W. Lee, “DNS changer remediation study,” in M3AAWG 27th", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-990", "line_number": 990, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-991", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "General Meeting, 2013.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-991", "line_number": 991, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-992", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[106] Civil Action No: 1:11cv1O17 (JCC/IDD), Microsoft Corporation v. Dominique Alexander", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-992", "line_number": 992, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-993", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Piatti, Dotfree Group SRO John Does 1–22, Controlling a computer botnet thereby injuring", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-993", "line_number": 993, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-994", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Microsoft and its customers.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-994", "line_number": 994, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-995", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "UNITED STATES DISTRICT COURT FOR THE EASTERN", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-995", "line_number": 995, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-996", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "DISTRICT OF VIRGINIA, Feb 2013.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-996", "line_number": 996, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-997", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "[107] B. Bartholomew and J. A. Guerrero-Saade, “Wave your false ﬂags!” [Online]. Available:", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-997", "line_number": 997, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-998", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 29", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-998", "line_number": 998, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-999", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 32", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-999", "line_number": 999, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1000", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "URL Uniform Resource Locator.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1000", "line_number": 1000, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1001", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "VMI Virtual Machine Inspection.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1001", "line_number": 1001, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1002", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "GLOSSARY", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1002", "line_number": 1002, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1003", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "advanced persistent threat An attack to an organization that continues its activities and yet", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1003", "line_number": 1003, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1004", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "remains undetected for an extended period of time.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1004", "line_number": 1004, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1005", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "botnet A network of compromised computers (or, bots) that is controlled by an attacker to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1005", "line_number": 1005, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1006", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "launch coordinated malicious activities.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1006", "line_number": 1006, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1007", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "CyBOK Refers to the Cyber Security Body of Knowledge.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1007", "line_number": 1007, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1008", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "exploit Software or data that takes advantage of a vulnerability in a system to cause unin-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1008", "line_number": 1008, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1009", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "tended consequences. (Source = NCSC Glossary).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1009", "line_number": 1009, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1010", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "indicator of compromise Recognised action, speciﬁc, generalized, or theoretical, that an", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1010", "line_number": 1010, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1011", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "adversary might be expected to take in preparation for an attack. (Source = NIST IR", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1011", "line_number": 1011, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1012", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "7298).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1012", "line_number": 1012, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1013", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "key-logger A virus or physical device that logs keystrokes to secretly capture private infor-", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1013", "line_number": 1013, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1014", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "mation such as passwords or credit card details.(Source = BSI Glossary).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1014", "line_number": 1014, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1015", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "macro virus A virus that attaches itself to documents and uses the macro programming", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1015", "line_number": 1015, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1016", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "capabilities of the document’s application to execute and propagate.(Source = NIST IR", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1016", "line_number": 1016, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1017", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "7298).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1017", "line_number": 1017, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1018", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware A program inserted into a system, usually covertly, with the intent of compromising", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1018", "line_number": 1018, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1019", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the conﬁdentiality, integrity, or availability of the victim’s data, applications or operating", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1019", "line_number": 1019, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1020", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "system, or of otherwise annoying or disrupting the victim. Synonym = malicious code.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1020", "line_number": 1020, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1021", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "(Source = NIST IR 7298r2).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1021", "line_number": 1021, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1022", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware analysis The process of analyzing malware code and understanding its intended", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1022", "line_number": 1022, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1023", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "functionalities.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1023", "line_number": 1023, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1024", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malware detection The process of detecting the presence of malware in a system.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1024", "line_number": 1024, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1025", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "metamorphic malware Malware of which each iteration or instance has different code from", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1025", "line_number": 1025, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1026", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the preceding one. The code changes make it difﬁcult to recognize the different iterations", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1026", "line_number": 1026, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1027", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "are the same malware (contrast with polymorphic malware).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1027", "line_number": 1027, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1028", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "meterpreter A tool that allows an attacker to control a victim’s computer by running an", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1028", "line_number": 1028, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1029", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "invisible shell and establishing a communication channel back to the attacking machine.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1029", "line_number": 1029, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1030", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "packed malware Packed malware is obfuscated malware in which the malicious program is", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1030", "line_number": 1030, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1031", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "compressed and cannot be analysed statically.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1031", "line_number": 1031, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1032", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "packing A technique to obfuscate malware (see packed malware).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1032", "line_number": 1032, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1033", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 31", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1033", "line_number": 1033, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1034", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "## Page 33", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1034", "line_number": 1034, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1035", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "passive dns A mechanism to collect large amounts of DNS data by storing DNS responses", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1035", "line_number": 1035, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1036", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "from servers. (Source = RFC7719.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1036", "line_number": 1036, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1037", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "polymorphic malware Malware that changes each instance to avoid detection. It typically", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1037", "line_number": 1037, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1038", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "has two parts: the decryptor and the encrypted program body. Each instance can encrypt", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1038", "line_number": 1038, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1039", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the malware program differently and hence has a different decryptor; however, once", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1039", "line_number": 1039, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1040", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "decrypted, the same malware code is executed. (contrast with metamorphic malware).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1040", "line_number": 1040, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1041", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "polymorphism See polymorphic malware.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1041", "line_number": 1041, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1042", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "potentially unwanted program A program that may not be wanted by a user and is often", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1042", "line_number": 1042, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1043", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "downloaded along with a program that the user wants. Examples include adware,", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1043", "line_number": 1043, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1044", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "spyware, etc.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1044", "line_number": 1044, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1045", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "ransomware Malicious software that makes data or systems unusable until the victim makes", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1045", "line_number": 1045, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1046", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "a payment. (Source = NIST IR 7298).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1046", "line_number": 1046, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1047", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "safety In the context of malware analysis, a requirement that malware should be prevented", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1047", "line_number": 1047, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1048", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "from causing damage to the connected systems and networks while it runs in the", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1048", "line_number": 1048, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1049", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "analysis environment.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1049", "line_number": 1049, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1050", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "sinkholing A technique used by a DNS server to give out false information to prevent the use", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1050", "line_number": 1050, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1051", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "of a domain name.", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1051", "line_number": 1051, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1052", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "spam The abuse of electronic messaging systems to indiscriminately send unsolicited bulk", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1052", "line_number": 1052, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1053", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "messages. (Source = NIST IR 7298).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1053", "line_number": 1053, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1054", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "spyware Software that is secretly or surreptitiously installed into an information system to", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1054", "line_number": 1054, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1055", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "gather information on individuals or organizations without their knowledge; a type of", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1055", "line_number": 1055, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1056", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "malicious code. (Source = NIST IR 7298).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1056", "line_number": 1056, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1057", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "trojan A computer program that appears to have a useful function, but also has a hidden", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1057", "line_number": 1057, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1058", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "and potentially malicious function that evades security mechanisms, sometimes by", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1058", "line_number": 1058, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1059", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "exploiting legitimate authorizations of a system entity that invokes the program. (Source", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1059", "line_number": 1059, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1060", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "= NIST IR 7298).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1060", "line_number": 1060, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1061", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "virus A hidden, self-replicating section of computer software, usually malicious logic, that", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1061", "line_number": 1061, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1062", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "propagates by infecting - i.e., inserting a copy of itself into and becoming part of - another", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1062", "line_number": 1062, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1063", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "program. A virus cannot run by itself; it requires that its host program be run to make", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1063", "line_number": 1063, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1064", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "the virus active. (Source = SANS security glossary).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1064", "line_number": 1064, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1065", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "worm A computer program that can run independently, can propagate a complete working", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1065", "line_number": 1065, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1066", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "version of itself onto other hosts on a network, and may consume computer resources", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1066", "line_number": 1066, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1067", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "destructively. (Source = SANS security glossary).", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1067", "line_number": 1067, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
{"chunk_id": "line-1068", "filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "content": "Page 32", "metadata": {"filename": "Malware_Attack_Technologies_v1.0.1_processed.txt", "chunk_id": "line-1068", "line_number": 1068, "source": "知识库\\output\\Malware_Attack_Technologies_v1.0.1_processed.txt"}}
