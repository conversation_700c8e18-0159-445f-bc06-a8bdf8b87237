CVE-2025-5054(PUBLISHED):Race condition in Canonical apport up to and including 2.32.0 allows a local attacker to leak sensitive information via PID-reuse by leveraging namespaces. When handling a crash, the function `_check_global_pid_and_forward`, which detects if the crashing process resided in a container, was being called before `consistency_checks`, which attempts to detect if the crashing process had been replaced. Because of this, if a process crashed and was quickly replaced with a containerized one, apport could be made to forward the core dump to the container, potentially leaking sensitive information. `consistency_checks` is now being called before `_check_global_pid_and_forward`. Additionally, given that the PID-reuse race condition cannot be reliably detected from userspace alone, crashes are only forwarded to containers if the kernel provided a pidfd, or if the crashing process was unprivileged (i.e., if dump mode == 1). | CVSS v3: 4.7 (MEDIUM) | Published: 2025-05-30
CVE-2025-27038(PUBLISHED):Memory corruption while rendering graphics using Adreno GPU drivers in Chrome. | CVSS v3: 7.5 (HIGH) | Published: 2025-06-03
CVE-2025-21479(PUBLISHED):Memory corruption due to unauthorized command execution in GPU micronode while executing specific sequence of commands. | CVSS v3: 8.6 (HIGH) | Published: 2025-06-03
CVE-2025-21480(PUBLISHED):Memory corruption due to unauthorized command execution in GPU micronode while executing specific sequence of commands. | CVSS v3: 8.6 (HIGH) | Published: 2025-06-03
CVE-2025-49113(PUBLISHED):Roundcube Webmail before 1.5.10 and 1.6.x before 1.6.11 allows remote code execution by authenticated users because the _from parameter in a URL is not validated in program/actions/settings/upload.php, leading to PHP Object Deserialization. | CVSS v3: 9.9 (CRITICAL) | Published: 2025-06-02
CVE-2025-48757(PUBLISHED):An insufficient database Row-Level Security policy in Lovable through 2025-04-15 allows remote unauthenticated attackers to read or write to arbitrary database tables of generated sites. | CVSS v3: 9.3 (CRITICAL) | Published: 2025-05-30
CVE-2025-31324(PUBLISHED):SAP NetWeaver Visual Composer Metadata Uploader is not protected with a proper authorization, allowing unauthenticated agent to upload potentially malicious executable binaries that could severely harm the host system. This could significantly affect the confidentiality, integrity, and availability of the targeted system. | CVSS v3: 10.0 (CRITICAL) | Published: 2025-04-24
CVE-2025-32023(PUBLISHED):Redis is an open source, in-memory database that persists on disk. From 2.8 to before 8.0.3, 7.4.5, 7.2.10, and 6.2.19, an authenticated user may use a specially crafted string to trigger a stack/heap out of bounds write on hyperloglog operations, potentially leading to remote code execution. The bug likely affects all Redis versions with hyperloglog operations implemented. This vulnerability is fixed in 8.0.3, 7.4.5, 7.2.10, and 6.2.19. An additional workaround to mitigate the problem without patching the redis-server executable is to prevent users from executing hyperloglog operations. This can be done using ACL to restrict HLL commands. | CVSS v3: 7.0 (HIGH) | Published: 2025-07-07
