M0801:Access Management,Access Management technologies can be used to enforce authorization polices and decisions, especially when existing field devices do not provide sufficient capabilities to support user identification and authentication.  These technologies typically utilize an in-line network device or gateway system to prevent access to unauthenticated users, while also integrating with an authentication service to first verify user credentials.
M0936:Account Use Policies,Configure features related to account use like login attempt lockouts, specific login times, etc.
M0915:Active Directory Configuration,Configure Active Directory to prevent use of certain techniques; use security identifier (SID) Filtering, etc.
M0949:Antivirus/Antimalware,Use signatures or heuristics to detect malicious software.  Within industrial control environments, antivirus/antimalware installations should be limited to assets that are not involved in critical or real-time operations. To minimize the impact to system availability, all products should first be validated within a representative test environment before deployment to production systems.
M0913:Application Developer Guidance,This mitigation describes any guidance or training given to developers of applications to avoid introducing security weaknesses that an adversary may be able to take advantage of.
M0948:Application Isolation and Sandboxing,Restrict the execution of code to a virtual environment on or in-transit to an endpoint system.
M0947:Audit,Perform audits or scans of systems, permissions, insecure software, insecure configurations, etc. to identify potential weaknesses. Perform periodic integrity checks of the device to validate the correctness of the firmware, software, programs, and configurations. Integrity checks, which typically include cryptographic hashes or digital signatures, should be compared to those obtained at known valid states, especially after events like device reboots, program downloads, or program restarts.
M0800:Authorization Enforcement,The device or system should restrict read, manipulate, or execute privileges to only authenticated users who require access based on approved security policies.  Role-based Access Control (RBAC) schemes can help reduce the overhead of assigning permissions to the large number of devices within an ICS. For example, IEC 62351 provides examples of roles used to support common system operations within the electric power sector  , while IEEE 1686 defines standard permissions for users of IEDs.
M0946:Boot Integrity,Use secure methods to boot a system and verify the integrity of the operating system and loading mechanisms.
M0945:Code Signing,Enforce binary and application integrity with digital signature verification to prevent untrusted code from executing.
M0802:Communication Authenticity,When communicating over an untrusted network, utilize secure network protocols that both authenticate the message sender and can verify its integrity. This can be done either through message authentication codes (MACs) or digital signatures, to detect spoofed network messages and unauthorized connections.
M0953:Data Backup,Take and store data backups from end user systems and critical servers. Ensure backup and storage systems are hardened and kept separate from the corporate network to prevent compromise.   Maintain and exercise incident response plans  , including the management of  'gold-copy' back-up images and configurations for key systems to enable quick recovery and response from adversarial activities that impact control, view, or availability.
M0803:Data Loss Prevention,Data Loss Prevention (DLP) technologies can be used to help identify adversarial attempts to exfiltrate operational information, such as engineering plans, trade secrets, recipes, intellectual property, or process telemetry. DLP functionality may be built into other security products such as firewalls or standalone suites running on the network and host-based agents. DLP may be configured to prevent the transfer of information through corporate resources such as email, web, and physical media such as USB for host-based solutions.
M0942:Disable or Remove Feature or Program,Remove or deny access to unnecessary and potentially vulnerable software to prevent abuse by adversaries.
M0808:Encrypt Network Traffic,Utilize strong cryptographic techniques and protocols to prevent eavesdropping on network communications.
M0941:Encrypt Sensitive Information,Protect sensitive data-at-rest with strong encryption.
M0938:Execution Prevention,Block execution of code on a system through application control, and/or script blocking.
M0950:Exploit Protection,Use capabilities to detect and block conditions that may lead to or be indicative of a software exploit occurring.
M0937:Filter Network Traffic,Use network appliances to filter ingress or egress traffic and perform protocol-based filtering. Configure software on endpoints to filter network traffic.   Perform inline allow/denylisting of network messages based on the application layer (OSI Layer 7) protocol, especially for automation protocols. Application allowlists are beneficial when there are well-defined communication sequences, types, rates, or patterns needed during expected system operations. Application denylists may be needed if all acceptable communication sequences cannot be defined, but instead a set of known malicious uses can be denied (e.g., excessive communication  attempts, shutdown messages, invalid commands).  Devices performing these functions are often referred to as deep-packet inspection (DPI) firewalls, context-aware firewalls, or firewalls blocking specific automation/SCADA protocol aware firewalls.
M0804:Human User Authentication,Require user authentication before allowing access to data or accepting commands to a device. While strong multi-factor authentication is preferable, it is not always feasible within ICS environments. Performing strong user authentication also requires additional security controls and processes which are often the target of related adversarial techniques (e.g., Valid Accounts, Default Credentials). Therefore, associated ATT&CK mitigations should be considered in addition to this, including Multi-factor Authentication, Account Use Policies, Password Policies, User Account Management, Privileged Account Management, and User Account Control.
M0935:Limit Access to Resource Over Network,Prevent access to file shares, remote access to systems, unnecessary services. Mechanisms to limit access may include use of network concentrators, RDP gateways, etc.
M0934:Limit Hardware Installation,Block users or groups from installing or using unapproved hardware on systems, including USB devices.
M0805:Mechanical Protection Layers,Utilize a layered protection design based on physical or mechanical protection systems to prevent damage to property, equipment, human safety, or the environment. Examples include interlocks, rupture disk, release values, etc.
M0806:Minimize Wireless Signal Propagation,Wireless signals frequently propagate outside of organizational boundaries, which provide opportunities for adversaries to monitor or gain unauthorized access to the wireless network.  To minimize this threat, organizations should implement measures to detect, understand, and reduce unnecessary RF propagation.
M0816:Mitigation Limited or Not Effective,This type of attack technique cannot be easily mitigated with preventative controls since it is based on the abuse of system features.
M0932:Multi-factor Authentication,Use two or more pieces of evidence to authenticate to a system; such as username and password in addition to a token from a physical smart card or token generator.  Within industrial control environments assets such as low-level controllers, workstations, and HMIs have real-time operational control and safety requirements which may restrict the use of multi-factor.
M0807:Network Allowlists,Network allowlists can be implemented through either host-based files or system hosts files to specify what connections (e.g., IP address, MAC address, port, protocol) can be made from a device. Allowlist techniques that operate at the  application layer (e.g., DNP3, Modbus, HTTP) are addressed in Filter Network Traffic mitigation.
M0931:Network Intrusion Prevention,Use intrusion detection signatures to block traffic at network boundaries.  In industrial control environments, network intrusion prevention should be configured so it will not disrupt protocols and communications responsible for real-time functions related to control or safety.
M0930:Network Segmentation,Architect sections of the network to isolate critical systems, functions, or resources. Use physical and logical segmentation to prevent access to potentially sensitive systems and information. Use a DMZ to contain any internet-facing services that should not be exposed from the internal network.  Restrict network access to only required systems and services. In addition, prevent systems from other networks or business functions (e.g., enterprise) from accessing critical process control systems. For example, in IEC 62443, systems within the same secure level should be grouped into a zone, and access to that zone is restricted by a conduit, or mechanism to restrict data flows between zones by segmenting the network.
M0928:Operating System Configuration,Make configuration changes related to the operating system or a common feature of the operating system that result in system hardening against techniques.
M0809:Operational Information Confidentiality,Deploy mechanisms to protect the confidentiality of information related to operational processes, facility locations, device configurations, programs, or databases that may have information that can be used to infer organizational trade-secrets, recipes, and other intellectual property (IP).
M0810:Out-of-Band Communications Channel,Have alternative methods to support communication requirements during communication failures and data integrity attacks.
M0927:Password Policies,Set and enforce secure password policies for accounts.
M0926:Privileged Account Management,Manage the creation, modification, use, and permissions associated to privileged accounts, including SYSTEM and root.
M0811:Redundancy of Service,Redundancy could be provided for both critical ICS devices and services, such as back-up devices or hot-standbys.
M0922:Restrict File and Directory Permissions,Restrict access by setting directory and file permissions that are not specific to users or privileged accounts.
M0944:Restrict Library Loading,Prevent abuse of library loading mechanisms in the operating system and software to load untrusted code by configuring appropriate library loading mechanisms and investigating potential vulnerable software.
M0924:Restrict Registry Permissions,Restrict the ability to modify certain hives or keys in the Windows Registry.
M0921:Restrict Web-Based Content,Restrict use of certain websites, block downloads/attachments, block Javascript, restrict browser extensions, etc.
M0812:Safety Instrumented Systems,Utilize Safety Instrumented Systems (SIS) to provide an additional layer of protection to hazard scenarios that may cause property damage. A SIS will typically include sensors, logic solvers, and a final control element that can be used to automatically respond to an hazardous condition   . Ensure that all SISs are segmented from operational networks to prevent them from being targeted by additional adversarial behavior.
M0954:Software Configuration,Implement configuration changes to software (other than the operating system) to mitigate security risks associated with how the software operates.
M0813:Software Process and Device Authentication,Require the authentication of devices and software processes where appropriate. Devices that connect remotely to other systems should require strong authentication to prevent spoofing of communications. Furthermore, software processes should also require authentication when accessing APIs.
M0920:SSL/TLS Inspection,Break and inspect SSL/TLS sessions to look at encrypted web traffic for adversary activity.
M0814:Static Network Configuration,Configure hosts and devices to use static network configurations when possible, protocols that require dynamic discovery/addressing (e.g., ARP, DHCP, DNS) can be used to manipulate network message forwarding and enable various AiTM attacks. This mitigation may not always be usable due to limited device features or challenges introduced with different network configurations.
M0817:Supply Chain Management,Implement a supply chain management program, including policies and procedures to ensure all devices and components originate from a trusted supplier and are tested to verify their integrity.
M0919:Threat Intelligence Program,A threat intelligence program helps an organization generate their own threat intelligence information and track trends to inform defensive priorities to mitigate risk.
M0951:Update Software,Perform regular software updates to mitigate exploitation risk. Software updates may need to be scheduled around operational down times.
M0918:User Account Management,Manage the creation, modification, use, and permissions associated to user accounts.
M0917:User Training,Train users to be aware of access or manipulation attempts by an adversary to reduce the risk of successful spearphishing, social engineering, and other techniques that involve user interaction.
M0818:Validate Program Inputs,Devices and programs designed to interact with control system parameters should validate the format and content of all user inputs and actions to ensure the values are within intended operational ranges. These values should be evaluated and further enforced through the program logic running on the field controller. If a problematic or invalid input is identified, the programs should either utilize a predetermined safe value or enter a known safe state, while also logging or alerting on the event.
M0916:Vulnerability Scanning,Vulnerability scanning is used to find potentially exploitable software vulnerabilities to remediate them.
M0815:Watchdog Timers,Utilize watchdog timers to ensure devices can quickly detect whether a system is unresponsive.
