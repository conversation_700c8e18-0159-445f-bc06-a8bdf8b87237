# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

from pikerag.prompts.qa.generation import (
    generation_qa_protocol, generation_qa_template, generation_qa_with_reference_protocol,
    generation_qa_with_reference_template, GenerationQaParser,
    
)
from pikerag.prompts.qa.multiple_choice import (
    multiple_choice_qa_protocol, multiple_choice_qa_template, MultipleChoiceQaParser, MultipleChoiceQaWithReferenceParser,
    cti_multiple_choice_qa_with_reference_protocol
)

__all__ = [
    "generation_qa_protocol", "generation_qa_template",
    "generation_qa_with_reference_protocol", "generation_qa_with_reference_template",
    "GenerationQaParser",
    "multiple_choice_qa_protocol", "multiple_choice_qa_template",
    "MultipleChoiceQaParser", "MultipleChoiceQaWithReferenceParser",
    "cti_multiple_choice_qa_with_reference_protocol",
]
