调研有没有现成的模型调参实现这种功能

收敛一下分析结果 比如只生成文章摘要，提取攻击组织

类似APT组织动态一样的东西

大模型分析文档后按照这个格式进行输出

1.APT组织活动追踪
（1）IPMsg 安装程序被武器化：Lazarus 集团瞄准加密货币和金融（2024/12/30）
2024年12月30日，360威胁情报中心揭露了臭名昭著的APT-C-26（Lazarus）组织的新活动，该组织针对金融机构和加密货币交易所，通过武器化流行的IPMsg安装程序来传递后门和窃取敏感信息。
攻击涉及部署合法的IPMsg安装程序和恶意DLL文件，后者用于建立与命令和控制（C2）服务器的通信，下载更多有效负载并窃取数据。
Lazarus组织采用了先进的规避技术，包括基于堆栈的检查和看似合法的域名，以绕过传统安全措施。
关键词：
Lazarus
IPMsg
https://securityonline.info/ipmsg-installer-weaponized-lazarus-group-targets-crypto-finance/
IOCS：
https://mp.weixin.qq.com/s/XuaMRmZSomKFoaX7XrqpYA
组织名称	别名	类型	域名	URL	Hash
Lazarus	APT-C-26	APT	cryptocopedia[.]com	https://cryptocopedia[.]com/upgrade/latest.asp
https://cryptocopedia[.]com/explorer/search.asp	a7b23cd8b09a3ce918a77de355e9d3e5



第一步先让其提取指标关键词，然后按照关键词进行分析和总结。形成这样的报告，需要外部知识库的加入把，RAG，还是说使用推理模型进行生成

https://www.gptsecurity.info/2024/05/26/RAG/


高效的embedding 很关键

合适的网络安全的embedding模型

搜索增强生成（RAG）是一种优化大型语言模型（LLM）的方法，启动能够在生成响应之前引用数据训练之外的权威知识库。

RAG（搜索增强生成）旨在甚至解决以下大模型落地应用的痛点：

垂直领域知识的幻觉：通过检索外部权威知识库，RAG可以提供更准确和可靠的领域特定知识，减少生成幻觉的可能性。
大模型知识持续更新的困难：需要重新训练模型，RAG可以通过访问最新的外部知识库，保持输出的时效性和准确性。
无法整合长尾语义知识：RAG能够从广泛的知识库中检索长尾语义知识，从而生成更丰富和全面的响应。
可能泄露的训练数据隐私问题：通过使用外部知识库而不依赖内部训练数据，RAG 减少了隐私泄露。
支持更长的上下文：RAG可以通过检索相关信息，提供更长、更详细的上下文支持，从而提高响应的质量和连贯性。


流程：
检索到的信息将用作生成模型的输入或附加上下文。
通过整合检索到的信息，生成模型可以利用基于检索的模型的准确性和特异性来生成更相关、更准确的文本。
这有助于生成模型立足于现有知识，生成与检索信息一致的文本。


目前已经实现了RAG的测试代码

通过对外部知识库文档进行分片、embedding，存储到向量数据库中，使用检索模型进行检索，最后将检索到的信息传递给生成模型进行生成。

1、分片的问题？----整个文档比较大，不能直接检索，输入到LLM中去

分片的方式有很多种，使用langchain进行分片，速度比较慢，按段落分片；

使用scapy进行分片，速度比较快，按句分片，可选多少句；

使用LLM智能分片，同时对分片进行摘要的生成，质量较高，但费tokens，速度较慢。


2、embedding？ ------将文本转化成机器能读懂的向量，embedding的质量直接影响到检索的效果

通用的嵌入模型和网络安全嵌入模型，模型的获取，网络安全嵌入模型太少了

使用sentence-transformers进行embedding，存储到向量数据库中，使用langchain进行检索和生成。


3、计划

------  从ATT&CK,OSNTI,CVE,CNVD等等都可以进行获取
已加知识库
https://www.cybok.org/knowledgebase/

https://attack.mitre.org

https://cve.mitre.org/

https://capec.mitre.org/data/index.html

总计 423780 个文档片段

。。。。。。



知识库的获取，准确，高质量的威胁情报的标准，这样才能更好的指导LLM的生成

对知识库的分片逻辑的完善----怎么才能更好的存储和检索


向量语义匹配检索   -----找一个网络安全领域的嵌入模型（替换嵌入和检索模型，比如使用BAAI/bge-m3）

BlueAvenir/sti_cyber_security_model_updated  是网络安全的


知识库有点大了，检索时间稍微有点长，尤其是关键词检索，


'sentence-transformers/all-MiniLM-L6-v2' 对这个进行微调，加入威胁情报的数据集，微调成专门的语义匹配，以及混合、重排序检索

词袋模型检索-----加入了词频、IOC\CVE\TTPS的正则提取当关键词，这个权重高一点

最后在进行一次检索，找到相关的知识库信息



未完成

幻觉问题的解决，目前一些报告的输出中会出现不该出现的内容，或者错误的内容，或者是文档中没有的内容，这些问题需要进一步优化。

和向量匹配检索的模型有关系吧

检索和提示词进行优化，生成的回答进行后处理看看，



优化语义匹配模型，专门用于网络威胁情报领域的，提高匹配准确性，







    
https://www.gptsecurity.info/     AI+安全

https://www.gptsecurity.info/2025/01/03/PaperReading/   人工智能驱动的网络威胁情报自动化


https://www.gptsecurity.info/2024/08/01/PaperReading/    在安全运营中心使用大语言模型来实现威胁情报分析工作流程的自动化


https://www.gptsecurity.info/2024/08/09/PaperReading-2/    LLMCloudHunter：利用基于云的网络威胁情报（CTI）中的大语言模型（LLMs）自动提取检测规则




https://github.com/ADEOSec/mcp-shodan