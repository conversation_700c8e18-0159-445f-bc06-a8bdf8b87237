## Page 5

the mean time to identify that a breach had occurred was 197 days, and the mean time
to ﬁnd and ﬁx a vulnerability once the breach was detected was an additional 69 days.
• Patches can introduce new vulnerabilities or other problems. Vulnerability patches are
considered urgent and can be rushed out, potentially introducing new problems to a
system. For example, Microsoft’s early patches for the Meltdown1 chip ﬂaw introduced
an even more serious vulnerability in Windows 72. The new vulnerability allowed attackers
to read kernel memory much faster and to write their own memory, and could allow an
attacker to access every user-level computing process running on a machine.
• Patches often go unapplied by customers. Users and system administrators may be
reluctant to apply security patches. For example, the highly-publicised Heartbleed3
vulnerability in OpenSSL allows attackers to easily and quietly exploit vulnerable systems,
stealing passwords, cookies, private crypto-keys, and much more. The vulnerability was
reported in April 2014; but in January 2017 a scan revealed 200,000 Internet-accessible
devices remained unpatched [7]. Once a vulnerability is publicly reported, attackers
formulate a new mechanism to exploit the vulnerability with the knowledge that many
organisations will not adopt the ﬁx.
In 1998, <PERSON> [5] advocated moving beyond the penetrate and patch approach based upon
his work on a DARPA-funded research effort investigating the application of software engi-
neering to the assessment of software vulnerabilities. He contended that proactive rigorous
software analysis should play an increasingly important role in assessing and preventing
vulnerabilities in applications based upon the well-known fact that security violations occur
because of errors in software design and coding. In 2002, Viega and McGraw published
the ﬁrst book on developing secure programs, Building Secure Software [6], with a focus on
preventing the injection of vulnerabilities and reducing security risk through an integration of
security into a software development process.
In the early 2000s, attackers became more aggressive, and Microsoft was a focus of this
aggression with exposure of security weaknesses in their products, particularly the Internet
Information Services (IIS). Gartner, a leading research and advisory company who seldom
advises its clients to steer clear of speciﬁc software, advised companies to stop using IIS. In
response to customer concerns and mounting bad press, the then Microsoft CEO, Bill Gates,
sent the Trustworthy Computing memo [2] to all employees on January 15, 2002. The memo
was also widely circulated on the Internet. An excerpt of the memo deﬁnes Trustworthy
Computing:
‘Trustworthy Computing is the highest priority for all the work we are doing. We
must lead the industry to a whole new level of Trustworthiness in computing ...
Trustworthy Computing is computing that is as available, reliable and secure as
electricity, water services and telephony’.
The Trustworthy Computing memo caused a shift in the company. Two weeks later, Microsoft
announced the delay of the release of Windows .NET Server [8] to ensure a proper security
review (referred to as the Windows Security Push), as mandated by Microsoft’s Trustworthy
Computing initiative outlined in this memo. In 2003, Microsoft employees Howard and Le
Blanc [9] publicly published a second edition of a book on writing secure code to prevent
1https://meltdownattack.com/ Meltdown lets hackers get around a barrier between applications and computer
memory to steal sensitive data.
2https://www.cyberscoop.com/microsoft-meltdown-patches-windows-7-memory-management/
3http://heartbleed.com/
Page 4
## Page 9

5. Establish Design Requirements. Design requirements guide the implementation of
’secure features’ (i.e., features that are well engineered with respect to security). Addi-
tionally, the architecture and design must be resistant to known threats in the intended
operational environment.
The design of secure features involves abiding by the timeless security principles set
forth by Saltzer and Schroeder [16] in 1975 and restated by Viega and McGraw [6] in
2002. The eight Saltzer and Schroeder principles are:
• Economy of mechanism. Keep the design of the system as simple and small as
possible.
• Fail-safe defaults. Base access decisions on permissions rather than exclusion; the
default condition is lack of access and the protection scheme identiﬁes conditions
under which access is permitted. Design a security mechanism so that a failure
will follow the same execution path as disallowing the operation.
• Complete mediation. Every access to every object must be checked for authorisa-
tion.
• Open design. The design should not depend upon the ignorance of attackers but
rather on the possession of keys or passwords.
• Separation of privilege. A protection mechanism that requires two keys to unlock
is more robust than one that requires a single key when two or more decisions
must be made before access should be granted.
• Least privilege. Every program and every user should operate using the least set
of privileges necessary to complete the job.
• Least common mechanism. Minimise the amount of mechanisms common to
more than one user and depended on by all users.
• Psychological acceptability. The human interface should be designed for ease of
use so that users routinely and automatically apply the mechanisms correctly and
securely.
Two other important secure design principles include the following:
• Defense in depth. Provide multiple layers of security controls to provide redundancy
in the event a security breach.
• Design for updating. The software security must be designed for change, such as
for security patches and security property changes.
Design requirements also involve the selection of security features, such as cryptography,
authentication and logging to reduce the risks identiﬁed through threat modelling. Teams
also take actions to reduce the attack surface of their system design. The attack surface,
a concept introduced by Howard [15] in 2003, can be thought of as the sum of the points
where attackers can try to enter data to or extract data from a system [24, 23].
In 2014, the IEEE Center for Secure Design [17] enumerated the top ten security design
ﬂaws and provided guidelines on techniques for avoiding them. These guidelines are as
follows:
(a) Earn or give, but never assume, trust.
Page 8
## Page 10

(b) Use an authentication mechanism that cannot be bypassed or tampered with.
(c) Authorise after you authenticate.
(d) Strictly separate data and control instructions, and never process control instruc-
tions received from untrusted sources.
(e) Deﬁne an approach that ensures all data are explicitly validated.
(f) Use cryptography correctly.
(g) Identify sensitive data and how they should be handled.
(h) Always consider the users.
(i) Understand how integrating external components changes your attack surface.
(j) Be ﬂexible when considering future changes to objects and actors.
6. Deﬁne and Use Cryptography Standards. The use of cryptography is an important
design feature for a system to ensure security- and privacy-sensitive data is protected
from unintended disclosure or alteration when it is transmitted or stored. However, an
incorrect choice in the use of cryptography can render the intended protection weak or
ineffective. Experts should be consulted in the use of clear encryption standards that
provide speciﬁcs on every element of the encryption implementation and on the use
of only properly vetted encryption libraries. Systems should be designed to allow the
encryption libraries to be easily replaced, if needed, in the event the library is broken by
an attacker, such as was done to the Data Encryption Standard (DES) through ’Deep
Crack’9, a brute force search of every possible key as designed by Paul Kocher, president
of Cryptography Research.
7. Manage the Security Risk of Using Third-Party Components. The vast majority of
software projects are built using proprietary and open-source third-party components.
The Black Duck On-Demand audit services group [18] conducted open-source audits
on over 1,100 commercial applications and found open-source components in 95%
of the applications with an average 257 components per application. Each of these
components can have vulnerabilities upon adoption or in the future. An organisation
should have an accurate inventory of third-party components [32], continuously use
a tool to scan for vulnerabilities in its components, and have a plan to respond when
new vulnerabilities are discovered. Freely available and proprietary tools can be used to
identify project component dependencies and to check if there are any known, publicly
disclosed, vulnerabilities in these components.
8. Use Approved Tools. An organisation should publish a list of approved tools and their
associated security checks and settings such as compiler/linker options and warn-
ings. Engineers should use the latest version of these tools, such as compiler versions,
and take advantage of new security analysis functionality and protections. Often, the
resultant software must be backward compatible with previous versions.
9. Perform Static Analysis Security Testing (SAST). SAST tools can be used for an auto-
mated security code review to ﬁnd instances of insecure coding patterns and to help
ensure that secure coding policies are being followed. SAST can be integrated into the
commit and deployment pipeline as a check-in gate to identify vulnerabilities each time
the software is built or packaged. For increased efﬁciency, SAST tools can integrate into
9https://w2.eff.org/Privacy/Crypto/Crypto misc/DESCracker/HTML/19980716 eff des faq.html
Page 9
## Page 11

the developer environment and be run by the developer during coding. Some SAST tools
spot certain implementation bugs, such as the existence of unsafe or other banned
functions and automatically replace with (or suggest) safer alternatives as the developer
is actively coding. See also the Software Security CyBOK Knowledge Area [1].
10. Perform Dynamic Analysis Security Testing (DAST). DAST performs run-time veriﬁca-
tion of compiled or packaged software to check functionality that is only apparent when
all components are integrated and running. DAST often involves the use of a suite
of pre-built attacks and malformed strings that can detect memory corruption, user
privilege issues, injection attacks, and other critical security problems. DAST tools may
employ fuzzing, an automated technique of inputting known invalid and unexpected test
cases at an application, often in large volume. Similar to SAST, DAST can be run by the
developer and/or integrated into the build and deployment pipeline as a check-in gate.
DAST can be considered to be automated penetration testing. See also the Software
Security CyBOK Knowledge Area [1].
11. Perform Penetration Testing. Manual penetration testing is black box testing of a
running system to simulate the actions of an attacker. Penetration testing is often
performed by skilled security professionals, who can be internal to an organisation or
consultants, opportunistically simulating the actions of a hacker. The objective of a
penetration test is to uncover any form of vulnerability - from small implementation
bugs to major design ﬂaws resulting from coding errors, system conﬁguration faults,
design ﬂaws or other operational deployment weaknesses. Tests should attempt both
unauthorised misuse of and access to target assets and violations of the assumptions.
A widely-referenced resource for structuring penetration tests is the OWASP Top 10
Most Critical Web Application Security Risks10. As such, penetration testing can ﬁnd the
broadest variety of vulnerabilities, although usually less efﬁciently compared with SAST
and DAST [19]. Penetration testers can be referred to as white hat hackers or ethical
hackers. In the penetration and patch model, penetration testing was the only line of
security analysis prior to deploying a system.
12. Establish a Standard Incident Response Process. Despite a secure software lifecycle,
organisations must be prepared for inevitable attacks. Organisations should proactively
prepare an Incident Response Plan (IRP). The plan should include who to contact in
case of a security emergency, establish the protocol for efﬁcient vulnerability mitigation,
for customer response and communication, and for the rapid deployment of a ﬁx. The
IRP should include plans for code inherited from other groups within the organisation
and for third-party code. The IRP should be tested before it is needed. Lessons learned
through responses to actual attack should be factored back into the SDL.
10https://www.owasp.org/index.php/Category:OWASP Top Ten Project
Page 10
## Page 12

2.1.2
Touchpoints
International software security consultant, Gary McGraw, provided seven Software Security
Touchpoints [10] by codifying extensive industrial experience with building secure products.
McGraw uses the term touchpoint to refer to software security best practices which can be
incorporated into a secure software lifecycle. McGraw differentiates vulnerabilities that are
implementation bugs and those that are design ﬂaws [17]. Implementation bugs are localized
errors, such as buffer overﬂow and input validation errors, in a single piece of code, making
spotting and comprehension easier. Design ﬂaws are systemic problems at the design level
of the code, such as error-handling and recovery systems that fail in an insecure fashion or
object-sharing systems that mistakenly include transitive trust issues [10]. Kuhn et al. [32]
analysed the 2008 - 2016 vulnerability data from the US National Vulnerability Database (NVD)11
and found that 67% of the vulnerabilities were implementation bugs. The seven touchpoints
help to prevent and detect both bugs and ﬂaws.
These seven touchpoints are described below and are provided in order of effectiveness
based upon McGraw’s experience with the utility of each practice over many years, hence
prescriptive:
1. Code Review (Tools).
Code review is used to detect implementation bugs. Manual code review may be used,
but requires that the auditors are knowledgeable about security vulnerabilities before
they can rigorously examine the code. ’Code review with a tool’ (a.k.a. the use of static
analysis tools or SAST) has been shown to be effective and can be used by engineers
that do not have expert security knowledge. For further discussion on static analysis,
see Section 2.1.1 bullet 9.
2. Architectural Risk Analysis.
Architectural Risk Analysis, which can also be referred to as threat modelling (see Section
2.1.1 bullet 4), is used to prevent and detect design ﬂaws. Designers and architects
provide a high-level view of the target system and documentation for assumptions, and
identify possible attacks. Through architectural risk analysis, security analysts uncover
and rank architectural and design ﬂaws so mitigation can begin. For example, risk
analysis may identify a possible attack type, such as the ability for data to be intercepted
and read. This identiﬁcation would prompt the designers to look at all their code’s
trafﬁcs ﬂows to see if interception was a worry, and whether adequate protection (i.e.
encryption) was in place. That review that the analysis prompted is what uncovers
design ﬂaws, such as sensitive data is transported in the clear.
No system can be perfectly secure, so risk analysis must be used to prioritise security
efforts and to link system-level concerns to probability and impact measures that matter
to the business building the software. Risk exposure is computed by multiplying the
probability of occurrence of an adverse event by the cost associated with that event [33].
McGraw proposes three basic steps for architectural risk analysis:
• Attack resistance analysis. Attack resistance analysis uses a checklist/systematic
approach of considering each system component relative to known threats, as is
done in Microsoft threat modelling discussed in Section 2.1.1 bullet 4. Information
about known attacks and attack patterns are used during the analysis, identifying
11http://nvd.nist.gov
Page 11
## Page 16

(c) Software Engineering Institute (SEI) CERT Secure Coding Standards 18
Special care must also be given to handling unanticipated errors in a controlled and
graceful way through generic error handlers or exception handlers that log the events.
If the generic handlers are invoked, the application should be considered to be in an
unsafe state such that further execution is no longer considered trusted.
4. Manage Security Risk Inherent in the Use of Third-Party Components. See Section
2.1.1 bullet 7.
5. Testing and Validation. See Section 2.1.1 bullets 9-11 and Section 2.1.2 bullets 1, 3 and
4.
6. Manage Security Findings. The ﬁrst ﬁve practices produce artifacts that contain or
generate ﬁndings related to the security of the product (or lack thereof). The ﬁndings in
these artifacts should be tracked and actions should be taken to remediate vulnerabilities,
such as is laid out in the Common Criteria (see Section 4.3) ﬂaw remediation procedure
[36]. Alternatively, the team may consciously accept the security risk when the risk is
determined to be acceptable. Acceptance of risk must be tracked, including a severity
rating; a remediation plan, an expiration or a re-review deadline; and the area for re-
review/validation.
Clear deﬁnitions of severity are important to ensure that all participants have and com-
municate with a consistent understanding of a security issue and its potential impact.
A possible starting point is mapping to the severity levels, attributes, and thresholds
used by the Common Vulnerability Scoring System (CVSS)19 such as 10–8.5 is critical,
8.4–7.0 is high, etc. The severity levels are used to prioritise mitigations based upon
their complexity of exploitation and impact on the properties of a system.
7. Vulnerability Response and Disclosure. Even with following a secure software lifecycle,
no product can be ’perfectly secure’ because of the constantly changing threat land-
scapes. Vulnerabilities will be exploited and the software will eventually be compromised.
An organisation must develop a vulnerability response and disclosure process to help
drive the resolution of externally discovered vulnerabilities and to keep all stakeholders
informed of progress. ISO provides industry-proven standards20 for vulnerability dis-
closure and handling. To prevent vulnerabilities from re-occurring in new or updated
products, the team should perform a root cause analysis and feed the lessons learned
into the secure software lifecycle practices. For further discussion, see Sections 2.1.1
bullet 12 and 2.1.2 bullet 7.
8. Planning the Implementation and Deployment of Secure Development. A healthy and
mature secure development lifecycle includes the above seven practices but also an
integration of these practices into the business process and the entire organisation,
including program management, stakeholder management, deployment planning, met-
rics and indicators, and a plan for continuous improvement. The culture, expertise
and skill level of the organisation needs to be considered when planning to deploy a
secure software lifecycle. Based upon past history, the organisation may respond better
to a corporate mandate, to a bottom-up groundswell approach or to a series of pilot
programs. Training will be needed (see Section 2.1.1 bullet 1). The speciﬁcation of the
organisation’s secure software lifecycle including the roles and responsibilities should
18https://wiki.sei.cmu.edu/conﬂuence/display/seccode/SEI+CERT+Coding+Standards
19https://www.ﬁrst.org/cvss/
20https://www.iso.org/standard/45170.html and https://www.iso.org/standard/53231.html
Page 15
## Page 21

3.2
Mobile
Security concerns for mobile apps differ from traditional desktop software in some important
ways, including local data storage, inter-app communication, proper usage of cryptographic
APIs and secure network communication. The OWASP Mobile Security Project [40] is a
resource for developers and security teams to build and maintain secure mobile applications;
see also the Web & Mobile Security CyBOK Knowledge Area [47].
Four resources are provided to aid in the secure software lifecycle of mobile applications:
1. OWASP Mobile Application Security Veriﬁcation Standard (MASVS) Security Require-
ments and Veriﬁcation. The MASVS deﬁnes a mobile app security model and lists
generic security requirements for mobile apps. The MASVS can be used by architects,
developers, testers, security professionals, and consumers to deﬁne and understand
the qualities of a secure mobile app.
2. Mobile Security Testing Guide (MSTG). The guide25 is a comprehensive manual for
mobile application security testing and reverse engineering for iOS and Android mobile
security testers. The guide provides the following content:
(a) A general mobile application testing guide that contains a mobile app security test-
ing methodology and general vulnerability analysis techniques as they apply to
mobile app security. The guide also contains additional technical test cases that are
operating system independent, such as authentication and session management,
network communications, and cryptography.
(b) Operating system-dependent testing guides for mobile security testing on the An-
droid and iOS platforms, including security basics; security test cases; reverse
engineering techniques and prevention; and tampering techniques and prevention.
(c) Detailed test cases that map to the requirements in the MASVS.
3. Mobile App Security Checklist. The checklist26 is used for security assessments and
contains links to the MSTG test case for each requirement.
4. Mobile Threat Model. The threat model [41] provides a checklist of items that should
be documented, reviewed and discussed when developing a mobile application. Five
areas are considered in the threat model:
(a) Mobile Application Architecture. The mobile application architecture describes
device-speciﬁc features used by the application, wireless transmission protocols,
data transmission medium, interaction with hardware components and other appli-
cations. The attack surface can be assessed through a mapping to the architecture.
(b) Mobile Data. This section of the threat model deﬁnes the data the application
stores, transmits and receives. The data ﬂow diagrams should be reviewed to
determine exactly how data are handled and managed by the application.
(c) Threat Agent Identiﬁcation. The threat agents are enumerated, including humans
and automated programs.
(d) Methods of Attack. The most common attacks utilised by threat agents are deﬁned
so that controls can be developed to mitigate attacks.
25https://www.owasp.org/index.php/OWASP Mobile Security Testing Guide
26https://github.com/OWASP/owasp-mstg/tree/master/Checklists
Page 20
## Page 22

(e) Controls. The controls to mitigate attacks are deﬁned.
3.3
Cloud Computing
The emergence of cloud computing bring unique security risks and challenges. In conjunction
with the Cloud Security Alliance (CSA)27, SAFECode has provided a ’Practices for Secure
Development of Cloud Applications’ [42] guideline as a supplement to the ’Fundamental
Practices for Secure Software Development’ guideline [34] discussed in Section 2.1.3 - see
also the Distributed Systems Security CyBOK Knowledge Area [48]. The Cloud guideline
provides additional secure development recommendations to address six threats unique to
cloud computing and to identify speciﬁc security design and implementation practices in the
context of these threats. These threats and associated practices are provided:
1. Threat: Multitenancy. Multitenancy allows multiple consumers or tenants to maintain
a presence in a cloud service provider’s environment, but in a manner where the compu-
tations, processes, and data (both at rest and in transit) of one tenant are isolated from
and inaccessible to another tenant. Practices:
(a) Model the application’s interfaces in threat models. Ensure that the multitenancy
threats, such as information disclosure and privilege elevation are modeled for each
of these interfaces, and ensure that these threats are mitigated in the application
code and/or conﬁguration settings.
(b) Use a ’separate schema’ database design and tables for each tenant when building
multitenant applications rather than relying on a ’TenantID’ column in each table.
(c) When developing applications that leverage a cloud service provider’s Platform as
a Service (PaaS) services, ensure common services are designed and deployed in
a way that ensures that the tenant segregation is maintained.
2. Tokenisation of Sensitive Data. An organisation may not wish to generate and store
intellectual property in a cloud environment not under its control. Tokenisation is a
method of removing sensitive data from systems where they do not need to exist or
disassociating the data from the context or the identity that makes them sensitive.
The sensitive data are replaced with a token for those data. The token is later used to
rejoin the sensitive data with other data in the cloud system. The sensitive data are
encrypted and secured within an organisation’s central system which can be protected
with multiple layers of protection and appropriate redundancy for disaster recovery and
business continuity. Practices:
(a) When designing a cloud application, determine if the application needs to process
sensitive data and if so, identify any organisational, government, or industry regu-
lations that pertain to that type of sensitive data and assess their impact on the
application design.
(b) Consider implementing tokenisation to reduce or eliminate the amount of sensitive
data that need to be processed and or stored in cloud environments.
(c) Consider data masking, an approach that can be used in pre-production test and
debug systems in which a representative data set is used, but does not need to
27https://cloudsecurityalliance.org/
Page 21
## Page 23

have access to actual sensitive data. This approach allows the test and debug
systems to be exempt from sensitive data protection requirements.
3. Trusted Compute Pools. Trusted Compute Pools are either physical or logical groupings
of compute resources/systems in a data centre that share a security posture. These
systems provide measured veriﬁcation of the boot and runtime infrastructure for mea-
sured launch and trust veriﬁcation. The measurements are stored in a trusted location
on the system (referred to as a Trusted Platform Module (TPM)) and veriﬁcation occurs
when an agent, service or application requests the trust quote from the TPM. Practices:
(a) Ensure the platform for developing cloud applications provides trust measurement
capabilities and the APIs and services necessary for your applications to both
request and verify the measurements of the infrastructure they are running on.
(b) Verify the trust measurements as either part of the initialisation of your application
or as a separate function prior to launching the application.
(c) Audit the trust of the environments your applications run on using attestation
services or native attestation features from your infrastructure provider.
4. Data Encryption and Key Management. Encryption is the most pervasive means of
protecting sensitive data both at rest and in transit. When encryption is used, both
providers and tenants must ensure that the associated cryptographic key materials are
properly generated, managed and stored. Practices:
(a) When developing an application for the cloud, determine if cryptographic and key
management capabilities need to be directly implemented in the application or
if the application can leverage cryptographic and key management capabilities
provided by the PaaS environment.
(b) Make sure that appropriate key management capabilities are integrated into the
application to ensure continued access to data encryption keys, particularly as the
data move across cloud boundaries, such as enterprise to cloud or public to private
cloud.
5. Authentication and Identity Management. As an authentication consumer, the appli-
cation may need to authenticate itself to the PaaS to access interfaces and services
provided by the PaaS. As an authentication provider, the application may need to authen-
ticate the users of the application itself. Practices:
(a) Cloud application developers should implement the authentication methods and
credentials required for accessing PaaS interfaces and services.
(b) Cloud application developers need to implement appropriate authentication meth-
ods for their environments (private, hybrid or public).
(c) When developing cloud applications to be used by enterprise users, developers
should consider supporting Single Sign On (SSO) solutions.
6. Shared-Domain Issues. Several cloud providers offer domains that developers can use
to store user content, or for staging and testing their cloud applications. As such, these
domains, which may be used by multiple vendors, are considered ’shared domains’ when
running client-side script (such as JavaScript) and from reading data. Practices:
(a) Ensure that your cloud applications are using custom domains whenever the cloud
provider’s architecture allows you to do so.
Page 22
## Page 24

(b) Review your source code for any references to shared domains.
The European Union Agency for Cybersecurity (ENISA) [44] conducted an in-depth and indepen-
dent analysis of the information security beneﬁts and key security risks of cloud computing.
The analysis reports that the massive concentrations of resources and data in the cloud
present a more attractive target to attackers, but cloud-based defences can be more robust,
scalable and cost-effective.
3.4
Internet of Things (IoT)
The Internet of Things (IoT) is utilised in almost every aspect of our daily life, including the
extension into industrial sectors and applications (i.e. Industrial IOT (IIoT)). IoT and IIoT
constitute an area of rapid growth that presents unique security challenges. [From this point
forth we include IIoT when we use IoT.] Some of these are considered in the Cyber-Physical
Systems Security CyBOK Knowledge Area [49], but we consider speciﬁcally software lifecycle
issues here. Devices must be securely provisioned, connectivity between these devices and
the cloud must be secure, and data in storage and in transit must be protected. However, the
devices are small, cheap, resource-constrained. Building security into each device may not be
considered to be cost effective by its manufacturer, depending upon the value of the device
and the importance of the data it collects. An IoT-based solution often has a large number of
geographically-distributed devices. As a result of these technical challenges, trust concerns
exist with the IoT, most of which currently have no resolution and are in need of research.
However, the US National Institute of Standards and Technology (NIST) [43] recommends
four practices for the development of secure IoT-based systems.
1. Use of Radio-Frequency Identiﬁcation (RFID) tags. Sensors and their data may be tam-
pered with, deleted, dropped, or transmitted insecurely. Counterfeit ’things’ exist in the
marketplace. Unique identiﬁers can mitigate this problem by attaching Radio-Frequency
Identiﬁcation (RFID) tags to devices. Readers activate a tag, causing the device to
broadcast radio waves within a bandwidth reserved for RFID usage by governments
internationally. The radio waves transmit identiﬁers or codes that reference unique
information associated with the device.
2. Not using or allowing the use of default passwords or credentials. IoT devices are often
not developed to require users and administrators to change default passwords during
system set up. Additionally, devices often lack intuitive user interfaces for changing
credentials. Recommended practices are to require passwords to be changed or to
design in intuitive interfaces. Alternatively, manufacturers can randomise passwords
per device rather than having a small number of default passwords.
3. Use of the Manufacturer Usage Description (MUD) speciﬁcation. The Manufacturer
Usage Description (MUD)28 speciﬁcation allows manufacturers to specify authorised
and expected user trafﬁc patterns to reduce the threat surface of an IoT device by
restricting communications to/from the device to sources and destinations intended by
the manufacturer.
4. Development of a Secure Upgrade Process. In non-IoT systems, updates are usually
delivered via a secure process in which the computer can authenticate the source
pushing the patches and feature and conﬁguration updates. IoT manufacturers have,
generally, not established such a secure upgrade process, which enables attackers
28https://tools.ietf.org/id/draft-ietf-opsawg-mud-22.html
Page 23
## Page 30

3. Domain: Secure software development lifecycle touchpoints
(a) Architecture analysis
(b) Code review
(c) Security testing
4. Domain: Deployment
(a) Penetration testing
(b) Software environment
(c) Conﬁguration management and vulnerability management
BSIMM assessments are conducted through in-person interviews by software security profes-
sionals at Cigital (now Synopsys) with security leaders in a ﬁrm. Via the interviews, the ﬁrm
obtains a scorecard on which of the 113 software security activities the ﬁrm uses. After the
ﬁrm completes the interviews, they are provided information comparing themselves with the
other organisations that have been assessed. BSIMM assessments have been conducted
since 2008. Annually, the overall results of the assessments from all ﬁrms are published,
resulting in the BSIMM1 through BSIMM9 reports. Since the BSIMM study began in 2008,
167 ﬁrms have participated in BSIMM assessment, sometimes multiple times, comprising
389 distinct measurements. To ensure the continued relevance of the data reported, the
BSIMM9 report excluded measurements older than 42 months and reported on 320 distinct
measurements collected from 120 ﬁrms.
4.3
The Common Criteria
The purpose of this Common Criteria (CC)41 is to provide a vehicle for international recog-
nition of a secure information technology (IT) product (where the SAMM and BSIMM were
assessments of a development process). The objective of the CC is for IT products that
have earned a CC certiﬁcate from an authorised Certiﬁcation/Validation Body (CB) to be
procured or used with no need for further evaluation. The Common Criteria seek to provide
grounds for conﬁdence in the reliability of the judgments on which the original certiﬁcate
was based by requiring that a CB issuing Common Criteria certiﬁcates should meet high and
consistent standards. A developer of a new product range may provide guidelines for the
secure development and conﬁguration of that product. This guideline can be submitted as a
Protection Proﬁle (the pattern for similar products that follow on). Any other developer can
add to or change this guideline. Products that earn certiﬁcation in this product range use the
protection proﬁle as the delta against which they build.
Based upon the assessment of the CB, a product receives an Evaluation Assurance Level
(EAL). A product or system must meet speciﬁc assurance requirements to achieve a particular
EAL. Requirements involve design documentation, analysis and functional or penetration
testing. The highest level provides the highest guarantee that the system’s principal security
features are reliably applied. The EAL indicates to what extent the product or system was
tested:
• EAL 1: Functionally tested. Applies when security threats are not viewed as serious.
The evaluation provides evidence that the system functions in a manner consistent with
41https://www.commoncriteriaportal.org/ccra/index.cfm
Page 29
## Page 32

DISCUSSION
[53]
This chapter has provided an overview of of three prominent and prescriptive secure software
lifecycle processes and six adaptations of these processes that can be applied in a speciﬁed
domain. However, the cybersecurity landscape in terms of threats, vulnerabilities, tools, and
practices is ever evolving. For example, a practice has has not be been mentioned in any of
these nine processes is the use of a bug bounty program for the identiﬁcation and resolution
of vulnerabilities. With a bug bounty program, organisations compensate individuals and/or
researchers for ﬁnding and reporting vulnerabilities. These individuals are external to the
organisation producing the software and may work independently or through a bug bounty
organisation, such as HackerOne42.
While the majority of this knowledge area focuses on technical practices, the successful
adoption of these practices involves organisational and cultural changes in an organisation.
The organisation, starting from executive leadership, must support the extra training, resources,
and steps needed to use a secure development lifecycle. Additionally, every developer must
uphold his or her responsibility to take part in such a process.
A team and an organisation need to choose the appropriate software security practices to de-
velop a customised secure software lifecycle based upon team and technology characteristics
and upon the security risk of the product.
While this chapter has provided practices for developing secure products, information in-
security is often due to economic disincentives [53] which drives software organizations
to choose the rapid deployment and release of functionality over the production of secure
products. As a result, increasingly governments and industry groups are imposing cyber
security standards on organisations as a matter of legal compliance or as a condition for
being considered as a vendor. Compliance requirements may lead to faster adoption of a
secure development lifecycle. However, this compliance-driven adoption may divert efforts
away from the real security issues by driving an over-focus on compliance requirements rather
than on the pragmatic prevention and detection of the most risky security concerns.
42https://www.hackerone.com
Page 31
## Page 34

Software Security: Building Security In [10]
This book discusses seven software securing best practices, called touchpoints. It also
provides information on software security fundamentals and contexts for a software security
program in an enterprise.
The Security Development Lifecycle (Original Book) [3]
This seminal book provides the foundation for the other processes laid out in this knowledge
area, and was customised over the years by other organisations, such as Cisco 43. The book
lays out 13 stages for integrating practices into a software development lifecycle such that
the product is more secure. This book is out of print, but is avaialble as a free download44.
The Security Development Lifecycle (Current Microsoft Resources) [11]
The Microsoft SDL are practices that are used internally to build secure products and services,
and address security compliance requirements by introducing security practices throughout
every phase of the development process. This webpage is a continuously-updated version of
the seminal book [3] based on Microsoft’s growing experience with new scenarios such as
the cloud, the Internet of Things (IoT) and Artiﬁcial Intelligence (AI).
Software Security Engineering: A Guide for Project Managers [26]
This book is a management guide for selecting from among sound software development
practices that have been shown to increase the security and dependability of a software
product, both during development and subsequently during its operation. Additionally, this
book discusses governance and the need for a dynamic risk management approach for
identifying priorities throughout the product lifecycle.
Cyber Security Engineering: A Practical Approach for Systems and Software
Assurance [54]
This book provides a tutorial on the best practices for building software systems that exhibit
superior operational security, and for considering security throughout your full system de-
velopment and acquisition lifecycles. This book provides seven core principles of software
assurance, and shows how to apply them coherently and systematically. This book addresses
important topics, including the use of standards, engineering security requirements for acquir-
ing COTS software, applying DevOps, analysing malware to anticipate future vulnerabilities,
and planning ongoing improvements.
43https://www.cisco.com/c/en/us/about/trust-center/technology-built-in-security.html#∼stickynav=2
44https://blogs.msdn.microsoft.com/microsoft press/2016/04/19/free-ebook-the-security-development-lifecycle/
Page 33
## Page 35

SAFECode’s Fundamental Practices for Secure Software Development: Es-
sential Elements of a Secure Development Lifecycle Program, Third Edition
[34]
Eight practices for secure development are provided based upon the experiences of member
companies of the SAFECode organisation.
OWASP’s Secure Software Development Lifecycle Project (S-SDLC) [12]
Based upon a committee of industry participants, the Secure-Software Development Lifecycle
Project (S-SDLC) deﬁnes a standard Secure Software Development Life Cycle and provides
resources to help developers know what should be considered or best practices at each
phase of a development lifecycle (e.g., Design Phase/Coding Phase/Maintain Phase/etc.) The
committee of industry participants are members of the Open Web Application Security Project
(OWASP)45, an international not-for-proﬁt organisation focused on improving the security of
web application software. The earliest secure software lifecycle contributions from OWASP
were referred to as the Comprehensive, Lightweight Application Security Process (CLASP).
Security controls
Government and standards organizations have provided security controls to be integrated in
a secure software or systems lifecyle:
1. The Trustworthy Software Foundation 46 provides the the Trustworthy Software Frame-
work (TSFr) 47 a collection of good practice, existing guidance and relevant standards
across the ﬁve main facets of trustworthiness: Safety; Reliability; Availability; Resilience;
and Security. The purpose of the TSFr is to provide a minimum set of controls such that,
when applied, all software (irrespective of implementation constraints) can be speciﬁed,
realised and used in a trustworthy manner.
2. The US National Institute of Standards and Technology (NIST) has authored the Systems
Security Engineering Cyber Resiliency Considerations for the Engineering [55] framework
(NIST SP 800-160). This Framework provides resources on cybersecurity Knowledge,
Skills and Abilitiess (KSAs), and tasks for a number of work roles for achieving the
identiﬁed cyber resiliency outcomes based on a systems engineering perspective on
system life cycle processes.
3. The Software Engineering Institute (SEI) has collaborated with professional organisa-
tions, industry partners and institutions of higher learning to develop freely-available
curricula and educational materials. Included in these materials are resources for a
software assurance program48 to train professionals to build security and correct func-
tionality into software and systems.
4. The UK National Cyber Security Centre (NCSC)49 provide resources for secure software
development:
45https://www.owasp.org/
46https://tsfdn.org
47https://tsfdn.org/ts-framework/
48https://www.sei.cmu.edu/education-outreach/curricula/software-assurance/index.cfm
49https://www.ncsc.gov.uk/
Page 34
## Page 38

[20] P. Hope, G. McGraw, and A. I. Anton, “Misuse and abuse cases: getting past the positive,”
IEEE Security and Privacy, vol. 2, no. 3, pp. 90–92, May 2004.
[21] G. Sindre and A. L. Opdahl, “Eliciting security requirements by misuse cases,” in Pro-
ceedings 37th International Conference on Technology of Object-Oriented Languages and
Systems. TOOLS-Paciﬁc 2000, Nov 2000, pp. 120–131.
[22] K. Tuma, G. Calikli, and R. Scandariato, “Threat analysis of software systems: A systematic
literature review,” Journal of Systems and Software, vol. 144, 06 2018.
[23] P. K. Manadhata and J. M. Wing, “An attack surface metric,” IEEE Trans. Softw. Eng., vol. 37,
no. 3, pp. 371–386, May 2011. [Online]. Available: http://dx.doi.org/10.1109/TSE.2010.60
[24] C. Theisen, N. Munaiah, M. Al-Zyoud, J. C. Carver, A. Meneely, and L. Williams, “Attack
surface deﬁnitions: A systematic literature review,” Information and Software Technology,
vol. 104, pp. 94 – 103, 2018. [Online]. Available: http://www.sciencedirect.com/science/
article/pii/S0950584918301514
[25] N. M. Mohammed, M. Niazi, M. Alshayeb, and S. Mahmood, “Exploring software
security approaches in software development lifecycle: A systematic mapping study,”
Comput. Stand. Interfaces, vol. 50, no. C, pp. 107–115, Feb. 2017. [Online]. Available:
https://doi.org/10.1016/j.csi.2016.10.001
[26] J. H. Allen, S. Barnum, R. J. Ellison, G. McGraw, and N. R. Mead, Software Security En-
gineering: A Guide for Project Managers (The SEI Series in Software Engineering), 1st ed.
Addison-Wesley Professional, 2008.
[27] A. van Lamsweerde, “Elaborating security requirements by construction of intentional anti-
models,” in Proceedings of the 26th International Conference on Software Engineering,
ser. ICSE ’04.
Washington, DC, USA: IEEE Computer Society, 2004, pp. 148–157. [Online].
Available: http://dl.acm.org.prox.lib.ncsu.edu/citation.cfm?id=998675.999421
[28] G. Elahi and E. Yu, “A goal oriented approach for modeling and analyzing security
trade-offs,” in Proceedings of the 26th International Conference on Conceptual Modeling,
ser. ER’07.
Berlin, Heidelberg: Springer-Verlag, 2007, pp. 375–390. [Online]. Available:
http://dl.acm.org.prox.lib.ncsu.edu/citation.cfm?id=1784489.1784524
[29] V. Saini, Q. Duan, and V. Paruchuri, “Threat modeling using attack trees,” J.
Comput. Sci. Coll., vol. 23, no. 4, pp. 124–131, Apr. 2008. [Online]. Available:
http://dl.acm.org.prox.lib.ncsu.edu/citation.cfm?id=1352079.1352100
[30] L. Williams, A. Meneely, and G. Shipley, “Protection poker: The new software security
”game”;,” IEEE Security Privacy, vol. 8, no. 3, pp. 14–20, May 2010.
[31] G. McGraw, “The new killer app for security: Software inventory,” Computer, vol. 51, no. 2,
pp. 60–62, February 2018.
[32] R. Kuhn, M. Raunak, and R. Kacker, “What proportion of vulnerabilities can
be attributed to ordinary coding errors?:
Poster,”
in Proceedings of the 5th
Annual Symposium and Bootcamp on Hot Topics in the Science of Security, ser.
HoTSoS ’18.
New York, NY, USA: ACM, 2018, pp. 30:1–30:1. [Online]. Available:
http://doi.acm.org/10.1145/3190619.3191686
[33] NIST Computer Security, “Guide for conducting risk assessments,” National Institute
of Standards and Technology, Tech. Rep. Special Publication 800-30 Revision 1, 2012.
[Online]. Available: https://csrc.nist.gov/publications/detail/sp/800-30/rev-1/ﬁnal
[34] SAFECode, “Fundamental practices for secure software development: Essential elements
of a secure development lifecycle program,” SAFECode, Tech. Rep. Third Edition, March
2018. [Online]. Available: https://safecode.org/wp-content/uploads/2018/03/SAFECode
Fundamental Practices for Secure Software Development March 2018.pdf
[35] Joint Task Force Transformation Initiative, “Security and privacy controls for federal
information systems and organizations,” National Institute of Standards and Technology,
Page 37
