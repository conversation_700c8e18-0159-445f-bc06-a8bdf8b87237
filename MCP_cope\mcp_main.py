"""
MCP Web Analyzer - MCP服务器版本

实现MCP协议的Web内容爬取、处理和分析工具 - 基于FastMCP实现
"""
import os
import sys
import json
import traceback
from typing import Dict, Any
from datetime import datetime
import time

# 导入FastMCP
try:
    from mcp.server.fastmcp import FastMCP
except ImportError:
    print("错误: 无法导入FastMCP，请安装: pip install mcp-server", file=sys.stderr)
    sys.exit(1)

# 导入cope_mcp.py中的类和函数
try:
    from cope_mcp import (
        HttpManager, CrawlerDB, ContentAnalyzer,
        crawl_url, process_html_content, process_pdf_content, download_pdf
    )
except ImportError:
    print("错误: 无法导入cope_mcp.py模块，请确保它在同一目录下", file=sys.stderr)
    sys.exit(1)

# 全局变量
DB_PATH = "mcp_crawler.db"
USE_PROXY = False

# 初始化MCP服务器
mcp = FastMCP("web-analyzer", log_level="INFO")

# 初始化数据库和分析器
try:
    db = CrawlerDB(DB_PATH)
    analyzer = ContentAnalyzer()
    print(f"已初始化数据库连接: {DB_PATH}", file=sys.stderr)
except Exception as e:
    print(f"数据库初始化失败: {str(e)}", file=sys.stderr)
    traceback.print_exc(file=sys.stderr)
    sys.exit(1)


def format_analysis_result(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """格式化分析结果，使其更易于阅读"""
    formatted = {
        "主题": analysis.get("main_topic", "未知"),
        "情感得分": round(analysis.get("sentiment_score", 0), 2),
        "摘要": analysis.get("summary", "无摘要"),
        "分析时间": analysis.get("analysis_time", datetime.now().isoformat())
    }
    
    # 添加关键实体
    entities = analysis.get("key_entities", [])
    if entities:
        formatted["关键实体"] = []
        for entity in entities:
            formatted["关键实体"].append({
                "名称": entity.get("name", ""),
                "类型": entity.get("type", "未知")
            })
    
    # 添加详细分析报告
    report = analysis.get("analysis_report", {})
    if report and isinstance(report, dict):
        formatted["详细分析"] = {}
        
        # 文本统计
        if "text_stats" in report:
            stats = report["text_stats"]
            formatted["详细分析"]["文本统计"] = {
                "单词数": stats.get("word_count", 0),
                "句子数": stats.get("sentence_count", 0),
                "平均句长": stats.get("avg_sentence_length", 0)
            }
        
        # 关键词
        if "top_keywords" in report and report["top_keywords"]:
            formatted["详细分析"]["关键词"] = report["top_keywords"]
        
        # 可读性
        if "readability" in report:
            read = report["readability"]
            formatted["详细分析"]["可读性"] = {
                "级别": read.get("level", "未知"),
                "得分": read.get("score", 0)
            }
        
        # 来源信息
        if "source_info" in report:
            source = report["source_info"]
            formatted["详细分析"]["来源"] = {
                "域名": source.get("domain", "未知"),
                "可信度": source.get("credibility_score", 0)
            }
    
    return formatted


@mcp.tool()
def web_analyze(url: str) -> Dict[str, Any]:
    """
    分析指定URL的网页内容
    
    Args:
        url: 要分析的网页URL
    
    Returns:
        包含分析结果的字典
    """
    print(f"处理URL: {url}", file=sys.stderr)
    
    try:
        # 1. 添加URL到队列
        db.add_url(url, "mcp_request")
        
        # 2. 爬取URL
        content, status, content_type = crawl_url(url, "mcp_request", USE_PROXY)
        
        # 更新爬取结果
        db.update_crawl_result(url, content, status, content_type)
        
        if status != 1:
            return {"error": f"爬取失败，状态码: {status}"}
            
        # 3. 提取内容
        extracted_text = None
        if content_type == "text/html":
            extracted_text = process_html_content(content)
        elif content_type == "application/pdf":
            pdf_text = download_pdf(url)
            if pdf_text:
                extracted_text = process_pdf_content(pdf_text)
                
        if not extracted_text:
            return {"error": "无法提取内容"}
            
        # 更新提取的文本
        db.update_extracted_text(url, extracted_text)
        
        # 4. 分析内容
        analysis_result = analyzer.analyze_content(extracted_text, url)
        
        # 找到数据库中的ID
        result = db.get_result_by_url(url)
        if result and "id" in result:
            db.update_analysis_result(result["id"], analysis_result)
        
        # 5. 返回结果
        formatted_result = format_analysis_result(analysis_result)
        return {
            "success": True,
            "url": url,
            "analysis": formatted_result
        }
        
    except Exception as e:
        print(f"分析过程中出错: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        return {"error": f"处理失败: {str(e)}"}


@mcp.tool()
def web_result(url: str) -> Dict[str, Any]:
    """
    获取已有的URL分析结果
    
    Args:
        url: 目标URL
        
    Returns:
        包含分析结果的字典
    """
    try:
        result = db.get_result_by_url(url)
        if not result:
            return {"error": "未找到该URL的分析结果"}
            
        # 格式化返回结果
        return {
            "success": True,
            "url": url,
            "result": result
        }
    except Exception as e:
        return {"error": f"获取结果失败: {str(e)}"}


@mcp.resource("config://web_analyzer")
def get_analyzer_config() -> Dict[str, Any]:
    """提供Web分析器的配置信息"""
    return {
        "db_path": DB_PATH,
        "use_proxy": USE_PROXY,
        "version": "1.0.0"
    }


@mcp.prompt(name="web_analysis")
def web_analysis_prompt(url: str) -> str:
    """生成网页分析的提示模板"""
    return f"""请分析以下网页内容:
URL: {url}

请提供关于:
1. 主要主题和关键点
2. 文章的整体情感倾向
3. 提到的关键实体和术语
4. 内容的可信度评估
"""

# 在启动代码中修改，明确指定端口

if __name__ == "__main__":
    # 默认端口
    port = 8000  # 改为实际运行的端口
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效的端口号: {sys.argv[1]}", file=sys.stderr)
            print(f"使用默认端口: {port}", file=sys.stderr)

    print(f"MCP Web Analyzer 服务器启动在端口 {port}", file=sys.stderr)
    print(f"数据库: {DB_PATH}, 使用代理: {USE_PROXY}", file=sys.stderr)
    
    # 创建带有端口配置的FastMCP实例
    # 注意：这里假设FastMCP接受这些参数
    mcp = FastMCP("web-analyzer", log_level="INFO", port=port)
    
    try:
        # 使用websocket传输 - 可能更兼容于0.4.6版本
        try:
            print("尝试使用 websocket 传输...", file=sys.stderr)
            mcp.run(transport="websocket")
        except ValueError:
            # 回退到SSE
            print("websocket传输失败，尝试使用SSE传输...", file=sys.stderr)
            mcp.run(transport="sse")
        except ValueError:
            # 最后回退到stdio
            print("SSE传输失败，尝试使用STDIO传输...", file=sys.stderr)
            mcp.run(transport="stdio")
    except KeyboardInterrupt:
        print("正在关闭服务器...", file=sys.stderr)
        db.close()
        print("服务器已关闭", file=sys.stderr)