"""
MCP Web Analyzer - Web content crawler, processor and analyzer

An enhanced version of the original script with added MCP (Model Content Processing) functionality
that allows direct URL input, content crawling, extraction, and intelligent analysis.
"""
import os
import io
import json
import sqlite3
import time
import re
import hashlib
import sys
import argparse
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple
import urllib3
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from readability import Document
from pathlib import Path
import requests
from requests.exceptions import RequestException
import PyPDF2
from io import BytesIO
from bs4 import XMLParsedAsHTMLWarning
import warnings

# Suppress XML parsing warnings
warnings.filterwarnings("ignore", category=XMLParsedAsHTMLWarning)

# Disable SSL warnings
urllib3.disable_warnings()

class HttpManager:
    """Custom HTTP client with proxy support and retry logic"""
    def __init__(self, use_proxy=False):
        # Configure proxy settings
        self.proxy_url = "http://127.0.0.1:7890" if use_proxy else None
        
        # Configure retry policy
        retries = urllib3.Retry(
            total=3,
            backoff_factor=0.1,
            status_forcelist=[500, 502, 503, 504],
            redirect=3
        )
        
        # Create connection pool (with or without proxy)
        if use_proxy:
            self.http = urllib3.ProxyManager(
                self.proxy_url,
                cert_reqs='CERT_NONE',  # Disable certificate verification
                retries=retries,
                timeout=urllib3.Timeout(connect=5.0, read=10.0)
            )
        else:
            self.http = urllib3.PoolManager(
                cert_reqs='CERT_NONE',
                retries=retries,
                timeout=urllib3.Timeout(connect=5.0, read=10.0)
            )

    def request(self, method, url, headers=None):
        """Send HTTP request"""
        return self.http.request(method, url, headers=headers)


class CrawlerDB:
    """Database operations encapsulation class"""
    def __init__(self, db_path: str):
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row  # Enable row access by name
        self._migrate_schema()
        
    def _migrate_schema(self):
        """Database schema migration with analysis columns"""
        # Initial table creation
        self.conn.execute('''
        CREATE TABLE IF NOT EXISTS crawled_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            query TEXT NOT NULL,
            title TEXT,
            link TEXT UNIQUE,
            snippet TEXT,
            position INTEGER
        )
        ''')

        # Dynamic addition of missing fields
        new_columns = [
            ('html_content', 'TEXT'),
            ('extracted_text', 'TEXT'),
            ('crawl_status', 'INTEGER DEFAULT 0'),
            ('crawl_time', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        ]

        # Analysis-related fields
        analysis_columns = [
            ('analysis_report', 'TEXT'),
            ('analysis_status', 'INTEGER DEFAULT 0'),  # 0=pending, 1=analyzed, 2=failed
            ('analysis_time', 'TIMESTAMP'),
            ('analysis_version', 'TEXT'),
            ('content_type', 'TEXT'),  # html, pdf, etc.
            ('main_topic', 'TEXT'),
            ('sentiment_score', 'REAL'),
            ('key_entities', 'TEXT'),
            ('summary', 'TEXT')
        ]

        # Add all columns to the schema check
        all_columns = new_columns + analysis_columns

        # Check existing columns
        cur = self.conn.cursor()
        cur.execute("PRAGMA table_info(crawled_data)")
        existing_columns = [col[1] for col in cur.fetchall()]

        # Add missing columns
        for col_name, col_type in all_columns:
            if col_name not in existing_columns:
                try:
                    self.conn.execute(f'''
                    ALTER TABLE crawled_data
                    ADD COLUMN {col_name} {col_type}
                    ''')
                    print(f"Successfully added column: {col_name}")
                except sqlite3.OperationalError as e:
                    print(f"Column addition failed ({col_name}): {str(e)}")

        self.conn.commit()

    def add_url(self, url: str, query: str = "direct_input"):
        """Add a new URL to the database"""
        try:
            self.conn.execute('''
            INSERT OR IGNORE INTO crawled_data (query, link, crawl_status)
            VALUES (?, ?, 0)
            ''', (query, url))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Database insertion failed: {str(e)}")
            return False

    def get_pending_urls(self, limit=100) -> List[Dict]:
        """Get list of URLs pending crawl"""
        cur = self.conn.cursor()
        cur.execute('''
        SELECT query, link 
        FROM crawled_data 
        WHERE crawl_status = 0 
        LIMIT ?
        ''', (limit,))
        return [{'query': row['query'], 'link': row['link']} for row in cur.fetchall()]

    def get_pending_analysis(self, limit=100) -> List[Dict]:
        """Get list of content pending analysis"""
        cur = self.conn.cursor()
        cur.execute('''
        SELECT id, link, extracted_text
        FROM crawled_data 
        WHERE crawl_status = 1 AND analysis_status = 0
        LIMIT ?
        ''', (limit,))
        return [{'id': row['id'], 'link': row['link'], 'text': row['extracted_text']} for row in cur.fetchall()]

    def update_crawl_result(self, link: str, html_content: str, status: int, content_type: str = None):
        """Update crawl results"""
        try:
            self.conn.execute('''
            UPDATE crawled_data 
            SET html_content = ?,
                crawl_status = ?,
                content_type = ?,
                crawl_time = CURRENT_TIMESTAMP
            WHERE link = ?
            ''', (html_content, status, content_type, link))
            self.conn.commit()
        except sqlite3.Error as e:
            print(f"Database update failed: {str(e)}")

    def update_extracted_text(self, link: str, extracted_text: str):
        """Update extracted text"""
        try:
            self.conn.execute('''
            UPDATE crawled_data 
            SET extracted_text = ?
            WHERE link = ?
            ''', (extracted_text, link))
            self.conn.commit()
        except sqlite3.Error as e:
            print(f"Text update failed: {str(e)}")

    def update_analysis_result(self, item_id: int, analysis_data: Dict):
        """Update content analysis results"""
        try:
            # Convert nested structures to JSON strings
            for key, value in analysis_data.items():
                if isinstance(value, (dict, list)):
                    analysis_data[key] = json.dumps(value, ensure_ascii=False)
                    
            # Update analysis fields
            self.conn.execute('''
            UPDATE crawled_data 
            SET analysis_report = ?,
                analysis_status = 1,
                analysis_time = CURRENT_TIMESTAMP,
                analysis_version = ?,
                main_topic = ?,
                sentiment_score = ?,
                key_entities = ?,
                summary = ?
            WHERE id = ?
            ''', (
                analysis_data.get('analysis_report'),
                analysis_data.get('analysis_version', '1.0'),
                analysis_data.get('main_topic'),
                analysis_data.get('sentiment_score'),
                analysis_data.get('key_entities'),
                analysis_data.get('summary'),
                item_id
            ))
            self.conn.commit()
        except sqlite3.Error as e:
            print(f"Analysis update failed: {str(e)}")
            
    def get_result_by_url(self, url: str) -> Dict:
        """Get complete results for a URL"""
        cur = self.conn.cursor()
        cur.execute('''
        SELECT * FROM crawled_data WHERE link = ?
        ''', (url,))
        row = cur.fetchone()
        if row:
            result = dict(row)
            # Parse JSON strings back to objects
            for key in ['key_entities', 'analysis_report']:
                if result.get(key):
                    try:
                        result[key] = json.loads(result[key])
                    except:
                        pass
            return result
        return None
            
    def close(self):
        """Close database connection"""
        self.conn.close()


class ContentAnalyzer:
    """Content analysis functionality"""
    def __init__(self):
        self.version = "1.0"
        
    def analyze_content(self, text: str, url: str) -> Dict:
        """Core analysis method - analyzes content from text and URL"""
        if not text or len(text) < 100:
            return self._generate_error_analysis("Text too short for meaningful analysis")
            
        try:
            # Generate detailed analysis report
            analysis = {
                'analysis_version': self.version,
                'analysis_time': datetime.now().isoformat(),
                'content_length': len(text),
                'analysis_report': self._analyze_full_content(text, url),
                'main_topic': self._extract_main_topic(text),
                'sentiment_score': self._analyze_sentiment(text),
                'key_entities': self._extract_key_entities(text),
                'summary': self._generate_summary(text)
            }
            return analysis
        except Exception as e:
            return self._generate_error_analysis(f"Analysis failed: {str(e)}")
    
    def _analyze_full_content(self, text: str, url: str) -> Dict:
        """Generate a comprehensive content analysis"""
        # Extract domain from URL
        domain = urlparse(url).netloc
        
        # Text statistics
        word_count = len(text.split())
        sentences = re.split(r'[.!?]+', text)
        sentence_count = len([s for s in sentences if s.strip()])
        avg_sentence_len = word_count / max(sentence_count, 1)
        
        # Keyword extraction (simple frequency-based)
        words = re.findall(r'\b[a-zA-Z]{3,15}\b', text.lower())
        word_freq = {}
        for word in words:
            if word in word_freq:
                word_freq[word] += 1
            else:
                word_freq[word] = 1
        
        # Filter common words
        common_words = {'the', 'and', 'that', 'have', 'for', 'not', 'with', 'you', 'this', 'but'}
        keywords = {word: freq for word, freq in word_freq.items() 
                   if word not in common_words and freq > 1}
        
        # Sort keywords by frequency
        top_keywords = sorted(keywords.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Content classification
        content_type = self._classify_content_type(text)
        
        # Structure detection
        has_sections = bool(re.search(r'\n#{1,3}\s+\w+', text))
        
        # Readability score (simplified)
        long_words = len([w for w in words if len(w) > 6])
        readability_score = min(10, max(1, 5 + (avg_sentence_len / 20) + (long_words / word_count * 10)))
        
        # URL credibility (basic check)
        credibility_score = 7  # Default moderate score
        if 'gov' in domain or 'edu' in domain:
            credibility_score = 9
        elif re.search(r'news|blog|article', domain):
            credibility_score = 6
            
        return {
            'text_stats': {
                'word_count': word_count,
                'sentence_count': sentence_count,
                'avg_sentence_length': round(avg_sentence_len, 1)
            },
            'top_keywords': dict(top_keywords),
            'content_classification': content_type,
            'structure': {
                'has_sections': has_sections,
                'has_lists': '* ' in text or '- ' in text,
                'paragraphs': text.count('\n\n') + 1
            },
            'readability': {
                'score': round(readability_score, 1),
                'level': self._get_readability_level(readability_score)
            },
            'source_info': {
                'domain': domain,
                'credibility_score': credibility_score
            }
        }
    
    def _extract_main_topic(self, text: str) -> str:
        """Extract the main topic of the content"""
        # Simple approach: use first paragraph and extract nouns
        first_para = text.split('\n\n')[0]
        first_sentence = re.split(r'[.!?]', first_para)[0]
        
        # Look for potential topic indicators
        topic_matches = re.search(r'about\s+([^.,:;]+)', first_para)
        if topic_matches:
            return topic_matches.group(1).strip()
            
        # Extract possible noun phrases as topics
        words = first_sentence.split()
        if len(words) > 3:
            # Take a simple phrase from the first sentence
            return ' '.join(words[1:min(len(words), 5)])
        
        # Fallback: use first few words
        return first_sentence[:40] + "..." if len(first_sentence) > 40 else first_sentence
    
    def _analyze_sentiment(self, text: str) -> float:
        """Analyze sentiment of the text (naive implementation)"""
        # Very simplified sentiment analysis
        positive_words = ['good', 'great', 'excellent', 'positive', 'amazing', 'wonderful', 
                         'best', 'happy', 'recommend', 'beneficial', 'success', 'improved']
        negative_words = ['bad', 'poor', 'terrible', 'negative', 'awful', 'worst', 
                         'disappointed', 'failure', 'problem', 'issue', 'difficult', 'hard']
        
        # Case insensitive matching
        text_lower = text.lower()
        
        # Count positive and negative words
        positive_count = sum(text_lower.count(' ' + word + ' ') for word in positive_words)
        negative_count = sum(text_lower.count(' ' + word + ' ') for word in negative_words)
        
        # Sentiment calculation (-1 to +1 scale)
        total = positive_count + negative_count
        if total == 0:
            return 0  # Neutral
        
        return (positive_count - negative_count) / total
    
    def _extract_key_entities(self, text: str) -> List[Dict]:
        """Extract key entities from text"""
        entities = []
        
        # Look for people (simplified)
        person_patterns = [
            r'Mr\.\s+([A-Z][a-z]+)',
            r'Ms\.\s+([A-Z][a-z]+)',
            r'Dr\.\s+([A-Z][a-z]+)',
            r'Professor\s+([A-Z][a-z]+)',
            r'([A-Z][a-z]+)\s+([A-Z][a-z]+)'  # Potential names
        ]
        
        for pattern in person_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    name = ' '.join(match)
                else:
                    name = match
                if len(name) > 3 and name not in [e['name'] for e in entities]:
                    entities.append({'type': 'person', 'name': name})
        
        # Look for organizations (simplified)
        org_patterns = [
            r'([A-Z][A-Za-z]*\s+(?:Inc|Corp|LLC|Company|Organization))',
            r'([A-Z][A-Za-z]*\s+[A-Z][A-Za-z]*\s+(?:Inc|Corp|LLC|Company|Organization))',
            r'University\s+of\s+([A-Za-z\s]+)',
            r'([A-Z][A-Za-z]+\s+University)'
        ]
        
        for pattern in org_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if match and len(match) > 3:
                    entities.append({'type': 'organization', 'name': match})
        
        # Look for locations (simplified)
        loc_patterns = [
            r'in\s+([A-Z][a-z]+,\s+[A-Z]{2})',
            r'in\s+([A-Z][a-z]+,\s+[A-Z][a-z]+)',
            r'from\s+([A-Z][a-z]+,\s+[A-Z][a-z]+)'
        ]
        
        for pattern in loc_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if match and len(match) > 3:
                    entities.append({'type': 'location', 'name': match})
                    
        # Limit to top 10 unique entities
        seen = set()
        unique_entities = []
        for entity in entities:
            if entity['name'] not in seen:
                seen.add(entity['name'])
                unique_entities.append(entity)
                if len(unique_entities) >= 10:
                    break
                    
        return unique_entities
    
    def _generate_summary(self, text: str) -> str:
        """Generate a summary of the content"""
        # Extract first and most important sentences
        sentences = re.split(r'(?<=[.!?])\s+', text)
        
        # Base summary on first 2 sentences
        summary = ' '.join(sentences[:2])
        
        # Look for "summary" sections in the text
        summary_match = re.search(r'summary[:\s]+(.*?)(?=\n\n|\Z)', text, re.IGNORECASE)
        if summary_match:
            summary += ' ' + summary_match.group(1).strip()
            
        # Ensure reasonable length
        if len(summary) > 300:
            summary = summary[:297] + '...'
            
        return summary
        
    def _classify_content_type(self, text: str) -> str:
        """Classify the type of content"""
        # Check for news article patterns
        if re.search(r'\b\d{1,2}\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b', text):
            return 'news_article'
            
        # Check for academic content
        if re.search(r'Abstract|Introduction|Methodology|Results|Conclusion|References', text, re.IGNORECASE):
            return 'academic'
            
        # Check for tutorial/guide
        if re.search(r'Step\s+\d|How\s+to|Guide|Tutorial|Instructions', text, re.IGNORECASE):
            return 'tutorial'
            
        # Check for product description
        if re.search(r'Product|Features|Specifications|Price|\$\d+', text, re.IGNORECASE):
            return 'product'
            
        # Default to general article
        return 'general_article'
    
    def _get_readability_level(self, score: float) -> str:
        """Convert readability score to text level"""
        if score < 3:
            return "Very Easy"
        elif score < 5:
            return "Easy"
        elif score < 7:
            return "Moderate"
        elif score < 9:
            return "Difficult"
        else:
            return "Very Difficult"
            
    def _generate_error_analysis(self, error_message: str) -> Dict:
        """Generate a placeholder analysis for error cases"""
        return {
            'analysis_version': self.version,
            'analysis_time': datetime.now().isoformat(),
            'error': error_message,
            'analysis_report': {'error': error_message},
            'main_topic': 'Unknown',
            'sentiment_score': 0,
            'key_entities': [],
            'summary': 'Content analysis failed: ' + error_message
        }


def is_detail_page(url, html_content):
    """Improved detection of detail pages vs. listings/index pages"""
    # Check if it's an XML file
    if url.lower().endswith('.xml'):
        return False

    # Check URL patterns - exclude non-detail pages
    exclude_patterns = [
        '/tag/', '/category/', '/author/',  
        '/search/', '/page/', '/feed/',
        'sitemap.xml', 'robots.txt'
    ]
    
    # Simple string contains check
    for pattern in exclude_patterns:
        if pattern in url:
            return False
    
    # Check content patterns that suggest listing pages
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Count links - listing pages typically have many links
    links = soup.find_all('a', href=True)
    if len(links) > 100:  # High number of links suggests a listing page
        return False
    
    # Check for pagination elements
    pagination_elements = soup.find_all(['div', 'nav', 'ul'], class_=lambda c: c and any(
        term in str(c).lower() for term in ['paginat', 'pages', 'page-numbers']
    ))
    if pagination_elements:
        return False
        
    return True


def sanitize_filename(filename):
    """Clean illegal filename characters"""
    return re.sub(r'[\\/*?:"<>|]', '_', filename)


def should_filter_text(text):
    """Enhanced filter rules for noise text"""
    filter_patterns = [
        r'Log\s*in', 
        r'Reset(\s*\w*)*',
        r'Search\s*the\s*site',
        r'All\s*categories',
        r'Sign\s*up',
        r'Table\s*of\s*contents',
        r'Share\s*this\s*post',
        r'Comments?\s*are\s*closed',
        r'Read\s*more',
        r'Follow\s*us',
        r'(value|respect|protect)[s]?[\s\w]*privacy',  
        r'cookie[s]?[\s\w]*use',
        r'by\s*(continuing|using|accessing)',
        r'terms\s*of\s*(use|service)',
        r'privacy\s*(policy|notice|statement)',
        r'同意使用cookies?',
        r'使用Cookie',
    ]
    return any(re.search(pattern, text, re.IGNORECASE) for pattern in filter_patterns)


def clean_extracted_text(text):
    """Improved text cleanup"""
    # Merge duplicate paragraphs
    text = re.sub(r'(.+?)\n\s*\1', r'\1', text, flags=re.DOTALL)
    # Standardize line breaks
    text = re.sub(r'\n{3,}', '\n\n', text)
    # Remove short line noise
    text = re.sub(r'^\W{0,5}$', '', text, flags=re.MULTILINE)
    # Remove excessive whitespace
    text = re.sub(r' {2,}', ' ', text)
    return text.strip()


def download_pdf(url):
    """Download and extract PDF text"""
    try:
        # Set up HTTP session with proper headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...'
        }
        
        response = requests.get(url, headers=headers, verify=False)
        response.raise_for_status()
        
        # Process with PyPDF2
        pdf_file = BytesIO(response.content)
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        
        text = ""
        for page_num in range(len(pdf_reader.pages)):
            text += pdf_reader.pages[page_num].extract_text()
            
        return text
    except Exception as e:
        print(f"PDF processing failed: {str(e)}")
        return None


def process_pdf_content(pdf_text):
    """Structured processing of PDF text content"""
    # Split into lines
    lines = pdf_text.split('\n')
    sections = []
    current_paragraph = []
    previous_is_heading = False
    
    # Identify titles and paragraphs
    for line in lines:
        line = line.strip()
        if not line:
            # Empty line indicates end of paragraph
            if current_paragraph:
                paragraph_text = ' '.join(current_paragraph)
                if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
                    sections.append(paragraph_text)
                current_paragraph = []
                previous_is_heading = False
            continue
            
        # Possible heading characteristics: short, high proportion of uppercase, no punctuation at end
        is_heading = (len(line) < 80 and 
                     sum(1 for c in line if c.isupper()) / len(line) > 0.3 and
                     not line[-1] in '.,:;?!')
        
        if is_heading:
            # Add accumulated paragraph content to sections
            if current_paragraph:
                paragraph_text = ' '.join(current_paragraph)
                if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
                    sections.append(paragraph_text)
                current_paragraph = []
            
            # Add separator before heading (except first heading)
            if sections and not previous_is_heading:
                sections.append('-'*40 + '\n')
                
            # Add heading
            if not should_filter_text(line):
                sections.append('\n' + line + '\n')
                previous_is_heading = True
        else:
            # Accumulate paragraph content
            current_paragraph.append(line)
    
    # Process final paragraph
    if current_paragraph:
        paragraph_text = ' '.join(current_paragraph)
        if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
            sections.append(paragraph_text)
    
    return clean_extracted_text('\n'.join(sections))


def extract_main_content(html):
    """Extract main content from HTML using readability"""
    doc = Document(html)
    return doc.summary()


def process_html_content(html_content: str, text_length: int = 300) -> Optional[str]:
    """Process and extract main content from HTML"""
    try:
        # Extract main content using readability
        doc = Document(html_content)
        main_content = doc.summary()
        
        # Refined processing
        soup = BeautifulSoup(main_content, 'html.parser')
        
        # Remove interfering elements
        for tag in ['aside', 'figure', 'svg', 'footer', 'header', 'script', 'style']:
            for element in soup(tag):
                element.decompose()

        # Structured extraction
        sections = []
        current_heading = None
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'p', 'pre']):
            if element.name.startswith('h'):
                current_heading = element.get_text(strip=True)
                sections.append(f"\n# {current_heading}\n")
            else:
                text = element.get_text(separator=' ', strip=True)
                if len(text) > 50 and not should_filter_text(text):
                    sections.append(text)
        
        cleaned_text = clean_extracted_text('\n'.join(sections))
        if not cleaned_text or len(cleaned_text) <= text_length:
            print(f"Warning: Extracted content too short or empty ({len(cleaned_text) if cleaned_text else 0} chars)")
        return cleaned_text if len(cleaned_text) > text_length else None
    
    except Exception as e:
        print(f"Content extraction failed: {str(e)}")
        return None


def crawl_url(url: str, query: str = "direct_input", use_proxy: bool = False) -> Tuple[str, int, str]:
    """
    Crawl a single URL and return its content
    Returns: (content, status_code, content_type)
    """
    pdf_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pdf_files")
    if not os.path.exists(pdf_dir):
        os.makedirs(pdf_dir)

    try:
        # Check if URL points to a PDF file
        is_pdf_url = url.lower().endswith('.pdf')
        
        client = HttpManager(use_proxy=use_proxy)
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...'
        }

        resp = client.request('GET', url, headers=headers)
        if resp.status != 200:
            print(f"Request failed HTTP {resp.status}")
            return None, 2, None  # Failed status

        # PDF file processing
        if is_pdf_url or 'application/pdf' in resp.headers.get('Content-Type', ''):
            print(f"Detected PDF file: {url}")
            
            # Extract filename
            url_parts = urlparse(url)
            file_name = os.path.basename(url_parts.path)
            if not file_name.endswith('.pdf'):
                file_name = hashlib.md5(url.encode()).hexdigest() + '.pdf'
            
            # Save PDF file
            safe_filename = sanitize_filename(file_name)
            pdf_path = os.path.join(pdf_dir, safe_filename)
            
            with open(pdf_path, 'wb') as f:
                f.write(resp.data)
            
            print(f"PDF saved: {pdf_path}")
            
            # Return PDF path and success status
            return pdf_path, 1, "application/pdf"  
        
        # HTML content processing
        content_type = resp.headers.get('Content-Type', '')
        encoding = re.search(r'charset=([\w-]+)', content_type)
        encoding = encoding.group(1) if encoding else 'utf-8'

        html_content = resp.data.decode(encoding, errors='replace')
        
        # Content filtering check
        is_detail = is_detail_page(url, html_content)
        if not is_detail:
            print(f"Skipping non-detail page: {url}")
            return None, 3, "text/html"  # Skipped status
        else:
            # Return HTML content and success status
            return html_content, 1, "text/html"
    except RequestException as e:
        print(f"Request failed: {str(e)}")
        return None, 2, None