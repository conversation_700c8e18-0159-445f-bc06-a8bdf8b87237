<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>威胁情报分析报告 | CTI 威胁情报分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px 0;
        }
        .card {
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
            border-top-left-radius: 10px !important;
            border-top-right-radius: 10px !important;
        }
        .card-body {
            padding: 20px;
        }
        .breadcrumb {
            background-color: transparent;
            padding: 0;
            margin-bottom: 20px;
        }
        .report-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            color: #212529;
        }
        .report-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        .report-meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .tab-content {
            padding: 20px 0;
        }
        .nav-tabs {
            border-bottom: 2px solid #dee2e6;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 10px 15px;
        }
        .nav-tabs .nav-link:hover {
            border: none;
            color: #495057;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            background-color: transparent;
            border-bottom: 2px solid #0d6efd;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin: 50px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 30px 0 15px;
            color: #343a40;
        }
        /* 自定义 Markdown 样式 */
        .markdown-body h1 {
            font-size: 1.8rem;
            margin-top: 30px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eaecef;
        }
        .markdown-body h2 {
            font-size: 1.5rem;
            margin-top: 24px;
            margin-bottom: 12px;
        }
        .markdown-body h3 {
            font-size: 1.3rem;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .markdown-body p {
            margin-bottom: 16px;
            line-height: 1.6;
        }
        .markdown-body ul, .markdown-body ol {
            padding-left: 2em;
            margin-bottom: 16px;
        }
        .markdown-body li {
            margin-bottom: 4px;
        }
        .ref-item {
            border-left: 3px solid #dee2e6;
            padding: 10px 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
            border-radius: 0 5px 5px 0;
        }
        .error-container {
            text-align: center;
            padding: 50px 20px;
        }
        .error-container i {
            font-size: 3rem;
            color: #dc3545;
            margin-bottom: 15px;
        }
        /* 参考信息列表样式 */
        .references-list {
            padding-left: 1.5em;
            margin-top: 15px;
        }
        
        .references-list li {
            margin-bottom: 12px;
            line-height: 1.5;
            position: relative;
            padding-left: 5px;
        }
        
        .references-list li::marker {
            color: #6c757d;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">首页</a></li>
                <li class="breadcrumb-item active" aria-current="page">报告详情</li>
            </ol>
        </nav>
        
        <!-- 加载中指示器 -->
        <div id="loader" class="loader"></div>
        
        <!-- 错误信息 -->
        <div id="error-container" class="error-container" style="display:none;">
            <i class="bi bi-exclamation-triangle"></i>
            <h3>无法加载报告</h3>
            <p id="error-message">发生错误，请稍后重试</p>
            <button onclick="window.history.back()" class="btn btn-primary mt-3">返回</button>
        </div>
        
        <!-- 报告内容 -->
        <div id="report-container" style="display:none;">

            <h1 id="report-title" class="report-title"></h1>
            
            <div class="report-meta">
                <div class="report-meta-item">
                    <i class="bi bi-calendar-event"></i>
                    <span id="report-date"></span>
                </div>
                <div class="report-meta-item">
                    <i class="bi bi-clock"></i>
                    <span id="analysis-time"></span>
                </div>
                <div class="report-meta-item">
                    <i class="bi bi-check-circle"></i>
                    <span id="analysis-status"></span>
                </div>
                <div class="report-meta-item">
                    <i class="bi bi-clock-history"></i>
                    <span id="crawl-time"></span>
                </div>
                <div class="report-meta-item">
                    <i class="bi bi-file-text"></i>
                    <span id="analysis-type"></span>
                </div>
                <div class="report-meta-item">
                    <i class="bi bi-link-45deg"></i>
                    <a id="source-link" href="#" target="_blank"></a>
                    <span id="snapshot-info" class="ms-2 text-muted small">
                        <i class="bi bi-hourglass-split"></i> 查询快照中...
                    </span>
                </div>

                 <div id="analysis-actions" class="ms-auto"></div>
            </div>
            
            <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="analysis-tab" data-bs-toggle="tab" 
                            data-bs-target="#analysis-content" type="button" role="tab" 
                            aria-controls="analysis-content" aria-selected="true">
                        分析报告
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="references-tab" data-bs-toggle="tab" 
                            data-bs-target="#references-content" type="button" role="tab" 
                            aria-controls="references-content" aria-selected="false">
                        参考信息
                    </button>
                </li>
            </ul>
            
            <div class="tab-content" id="reportTabContent">
                <div class="tab-pane fade show active" id="analysis-content" role="tabpanel" 
                     aria-labelledby="analysis-tab">
                    <div class="card">
                        <div class="card-body markdown-body" id="report-body">
                            <!-- 分析内容将在这里动态插入 -->
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="references-content" role="tabpanel" 
                     aria-labelledby="references-tab">
                    <div class="card">
                        <div class="card-body">
                            <div id="references-body">
                                <!-- 参考信息将在这里动态插入 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4 mb-5">
                <a href="/" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log("页面DOM已加载，初始化分析内容页...");
            
            // 获取报告ID
            const pathSegments = window.location.pathname.split('/');
            const reportId = pathSegments[pathSegments.length - 1];
            
            // 获取action参数
            const urlParams = new URLSearchParams(window.location.search);
            const action = urlParams.get('action') || 'view';
            
            console.log(`页面初始化 - reportId: ${reportId}, action: ${action}`);
            
            if (!reportId || isNaN(parseInt(reportId))) {
                showError('无效的报告ID');
                return;
            }
            
            // 根据action参数处理不同的行为
            if (action === 'analyze') {
                // 分析模式
                console.log("进入分析模式");
                
                // 添加分析按钮
                const actionsElement = document.getElementById('analysis-actions');
                if (actionsElement) {
                    actionsElement.innerHTML = `
                        <button id="start-analysis-btn" class="btn btn-success">
                            <i class="bi bi-lightning"></i> 开始分析
                        </button>
                    `;
                    document.getElementById('start-analysis-btn').addEventListener('click', function() {
                        startAnalysis(reportId);
                    });
                }
                
                // 获取原始爬取内容用于分析
                fetchCrawledData(reportId);
            } else {
                // 查看模式
                console.log("进入查看模式");
                // document.getElementById('page-title').textContent = '报告详情';
                fetchReportDetails(reportId);
            }
        });

        //startAnalysis函数
        function startAnalysis(reportId) {
            // 显示加载状态
            document.getElementById('loader').style.display = 'block';
            document.getElementById('start-analysis-btn').disabled = true;
            document.getElementById('start-analysis-btn').innerHTML = '<span class="spinner-border spinner-border-sm"></span> 分析中...';
            
            // 调用分析API
            fetch(`/api/analysis_content/${reportId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reportId: reportId,
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 检查是否返回了analysis_id
                    if (data.analysis_id) {
                        const analysisId = data.analysis_id;
                        console.log(`分析成功完成! 新的分析ID: ${analysisId}`);
                        
                        // 显示成功消息
                        document.getElementById('report-body').innerHTML = `
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i> 
                                分析已完成，正在加载结果...
                            </div>
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        `;
                        
                        // 短暂延迟后渲染结果（给用户一个视觉反馈）
                        setTimeout(() => {
                            if (data.analysis_content) {
                                renderDirectAnalysis(data, analysisId);
                            } else {
                                fetchAnalysisResult(analysisId, 5); // 最多重试5次
                            }
                        }, 1000);
                    } else {
                        console.error('分析API未返回analysis_id字段');
                        showError('分析API未返回正确的分析ID，请联系管理员检查后端实现。');
                    }
                } else {
                    // 分析失败
                    showError('分析失败: ' + (data.error || '未知错误'));
                    
                    document.getElementById('start-analysis-btn').disabled = false;
                    document.getElementById('start-analysis-btn').innerHTML = '<i class="bi bi-lightning"></i> 重试分析';
                }
            })
            .catch(error => {
                console.error('分析请求错误:', error);
                showError('请求失败: ' + error);
            });
        }
        // 渲染分析结果
        function renderDirectAnalysis(data, analysisId) {
            console.log(`直接渲染分析结果，ID: ${analysisId}`);
            
            // 创建报告对象
            const report = {
                analysis_id: analysisId,
                article_title: document.getElementById('report-title').textContent,
                analysis_content: data.analysis_content,
                analysis_time: new Date().toISOString(),
                report_time: data.report_date,
                source_url: document.getElementById('source-link').href,
                analysis_type: 'all',
                rationale: data.rationale // 直接保存分析理由
            };
            
            // 处理引用信息
            if (data.references && Array.isArray(data.references)) {
                report.references_text = formatReferences(data.references);
            }
            
            // 渲染报告
            renderReport(report);
            
            // 更新UI状态
            updateUIAfterAnalysis(analysisId);
        }


        // 添加分析理由标签页
        function addRationaleTab() {
            // 检查是否已有该标签页
            if (!document.getElementById('rationale-tab')) {
                // 添加标签到导航
                const tabNav = document.getElementById('reportTabs');
                
                if (tabNav) {
                    const rationaleTabItem = document.createElement('li');
                    rationaleTabItem.className = 'nav-item';
                    rationaleTabItem.role = 'presentation';
                    rationaleTabItem.innerHTML = `
                        <button class="nav-link" id="rationale-tab" data-bs-toggle="tab" 
                                data-bs-target="#rationale-content" type="button" role="tab" 
                                aria-controls="rationale-content" aria-selected="false">
                            分析理由
                        </button>
                    `;
                    tabNav.appendChild(rationaleTabItem);
                    
                    // 添加标签内容区域
                    const tabContent = document.getElementById('reportTabContent');
                    
                    if (tabContent) {
                        const rationalePane = document.createElement('div');
                        rationalePane.className = 'tab-pane fade';
                        rationalePane.id = 'rationale-content';
                        rationalePane.role = 'tabpanel';
                        rationalePane.setAttribute('aria-labelledby', 'rationale-tab');
                        rationalePane.innerHTML = `
                            <div class="card">
                                <div class="card-body">
                                    <div id="rationale-body" class="markdown-body">
                                        <!-- 分析理由将在这里动态插入 -->
                                    </div>
                                </div>
                            </div>
                        `;
                        tabContent.appendChild(rationalePane);
                    }
                }
            }
        }

        // 添加获取分析结果的函数，支持重试
        function fetchAnalysisResult(analysisId, retriesLeft) {
            console.log(`尝试获取分析结果，ID: ${analysisId}，剩余尝试次数: ${retriesLeft}`);
            
            fetch(`/api/analysis/${analysisId}`)
                .then(response => {
                    if (!response.ok) {
                        if (response.status === 404 && retriesLeft > 0) {
                            // 如果是404错误且还有重试次数，则延迟后重试
                            console.log(`分析ID ${analysisId} 的结果尚未准备好，将在2秒后重试，剩余重试次数: ${retriesLeft-1}`);
                            setTimeout(() => {
                                fetchAnalysisResult(analysisId, retriesLeft - 1);
                            }, 2000); // 每2秒重试一次
                            return null;
                        }
                        throw new Error(response.status === 404 ? `分析结果尚未准备好(ID: ${analysisId})` : '服务器错误');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data) {
                        console.log(`成功获取到分析结果，ID: ${analysisId}`);
                        // 处理直接从analyze_single_record返回的数据格式
                        if (data.references && !data.references_text) {
                            data.references_text = formatReferences(data.references);
                        }
                        
                        // 渲染分析结果
                        renderReport(data);
                        
                        // 更新UI状态
                        updateUIAfterAnalysis(analysisId);
                    }
                })
                .catch(error => {
                    console.error(`获取分析结果错误(ID: ${analysisId}):`, error);
                    // 显示错误但保留重试按钮
                    document.getElementById('report-body').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i> 
                            ${error.message || '获取分析结果失败'}
                        </div>
                        <div class="text-center">
                            <button class="btn btn-primary" onclick="fetchAnalysisResult('${analysisId}', 3)">
                                <i class="bi bi-arrow-clockwise"></i> 重试加载结果
                            </button>
                        </div>
                    `;
                })
                .finally(() => {
                    document.getElementById('loader').style.display = 'none';
                });
        }


        // 获取原始爬取的内容
        function fetchCrawledData(reportId) {
            console.time('fetchCrawledData');
            document.getElementById('loader').style.display = 'block';
            
            // 先显示部分UI，给用户一些视觉反馈
            document.getElementById('report-container').style.display = 'block';
            document.getElementById('error-container').style.display = 'none';
            
            // 预先设置一些基本信息，提供骨架屏效果
            document.getElementById('report-title').textContent = '加载中...';
            document.getElementById('report-date').textContent = '加载中...';
            document.getElementById('report-body').innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 
                    正在加载爬取内容，请稍候...
                </div>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                </div>
            `;
            
            // 修改：增加超时时间到 60 秒
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('请求超时，请检查网络连接')), 60000); // 改为 60 秒
            });
            
            // 实际请求
            const fetchPromise = fetch(`/api/crawled_data/${reportId}`);
            
            // 使用 Promise.race 竞争
            Promise.race([fetchPromise, timeoutPromise])
                .then(response => {
                    if (!response.ok) {
                        throw new Error(response.status === 404 ? '数据不存在' : '服务器错误');
                    }
                    return response.json();
                })
                .then(data => {
                    console.timeEnd('fetchCrawledData');
                    renderCrawledData(data);
                })
                .catch(error => {
                    console.error('获取数据错误:', error);
                    // 改进错误显示，提供重试按钮
                    document.getElementById('loader').style.display = 'none';
                    document.getElementById('report-body').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i> 
                            ${error.message || '获取数据失败'}。服务器处理时间可能较长，请稍后重试。
                        </div>
                        <div class="text-center">
                            <button class="btn btn-primary" onclick="fetchCrawledData('${reportId}')">
                                <i class="bi bi-arrow-clockwise"></i> 重试加载
                            </button>
                        </div>
                    `;
                });
        }

        // 优化渲染原始爬取内容的函数
        function renderCrawledData(data) {
            // 批量缓存DOM引用，减少DOM查询次数
            const elements = {
                title: document.getElementById('report-title'),
                date: document.getElementById('report-date'),
                status: document.getElementById('analysis-status'),
                crawlTime: document.getElementById('crawl-time'),
                analysisTime: document.getElementById('analysis-time'),
                analysisType: document.getElementById('analysis-type'),
                sourceLink: document.getElementById('source-link'),
                reportBody: document.getElementById('report-body'),
                loader: document.getElementById('loader'),
                container: document.getElementById('report-container')
            };
            
            // 准备所有数据，然后一次性更新DOM
            const reportDate = data.report_date || '待获取';
            elements.date.textContent = reportDate;
            const domainText = getDomain(data.link) || '未知来源';


            // 如果日期是"待获取"，则订阅更新
            if (reportDate === '待获取') {
                subscribeToDateUpdates(data.crawled_data_id);
            }
    
            
            // 批量更新DOM - 减少重绘次数
            elements.title.textContent = data.article_title || '未命名报告';
            elements.date.textContent = reportDate;
            elements.status.innerHTML = '<span class="badge bg-secondary">未分析</span>';
            elements.crawlTime.textContent = formatDate(data.crawl_time) || '未知时间';
            elements.analysisTime.textContent = '未分析';
            elements.analysisType.textContent = '待分析';
            
            // 设置源链接
            elements.sourceLink.textContent = domainText;
            elements.sourceLink.href = data.link || '#';
            
            // 加载快照信息
            loadSnapshotInfo(data.link);
            
            // 准备报告主体内容
            const bodyContent = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 
                    此内容尚未分析。点击上方"开始分析"按钮进行分析。
                </div>`;
            
            // 一次性设置内容
            elements.reportBody.innerHTML = bodyContent;
            
            // 显示报告容器，隐藏加载器
            elements.loader.style.display = 'none';
            elements.container.style.display = 'block';
            
            // 设置页面标题
            document.title = (data.article_title || '准备分析内容') + ' | CTI 威胁情报分析系统';
            
            // 检查"开始分析"按钮状态
            const startAnalysisBtn = document.getElementById('start-analysis-btn');
            if (startAnalysisBtn) {
                startAnalysisBtn.disabled = false;
            }
            
            // 检查是否已分析，更新UI状态
            if (data.is_analyzed) {
                // 已分析 - 显示查看结果按钮
                const startAnalysisBtn = document.getElementById('start-analysis-btn');
                if (startAnalysisBtn) {
                    startAnalysisBtn.textContent = '查看分析结果';
                    startAnalysisBtn.className = 'btn btn-primary';
                    startAnalysisBtn.onclick = function() {
                        window.location.href = `/analysis/${data.analysis_id}`;
                    }
                }
                
                elements.status.innerHTML = '<span class="badge bg-success">已分析</span>';
                elements.analysisTime.textContent = data.analysis_time || '未知时间';
                elements.analysisType.textContent = '全面分析';
                
                elements.reportBody.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 
                        此内容已经分析过。点击上方"查看分析结果"按钮查看分析报告。
                    </div>`;
            } else {
                // 未分析 - 显示开始分析按钮
                elements.status.innerHTML = '<span class="badge bg-secondary">未分析</span>';
                elements.analysisTime.textContent = '未分析';
                elements.analysisType.textContent = '待分析';
                
                elements.reportBody.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 
                        此内容尚未分析。点击上方"开始分析"按钮进行分析。
                    </div>`;
            }
        }


        // 添加日期更新订阅函数
        function subscribeToDateUpdates(crawledDataId) {
            console.log(`订阅ID ${crawledDataId} 的日期更新`);
            
            // 创建EventSource对象
            const eventSource = new EventSource(`/api/date_updates/${crawledDataId}`);
            
            // 当接收到更新时
            eventSource.onmessage = function(event) {
                const update = JSON.parse(event.data);
                
                // 如果收到实际日期
                if (update.report_date && update.report_date !== '待获取') {
                    console.log(`收到日期更新: ${update.report_date}`);
                    
                    // 更新DOM
                    const dateElement = document.getElementById('report-date');
                    if (dateElement) {
                        dateElement.textContent = update.report_date;
                        
                        // 添加一个简短的突出显示效果
                        dateElement.classList.add('date-updated');
                        setTimeout(() => {
                            dateElement.classList.remove('date-updated');
                        }, 2000);
                    }
                    
                    // 关闭连接
                    eventSource.close();
                } else if (update.status === "timeout") {
                    // 超时关闭连接
                    console.log("等待日期更新超时");
                    eventSource.close();
                }
            };
            
            // 错误处理
            eventSource.onerror = function() {
                console.log("SSE连接错误");
                eventSource.close();
            };
            
            // 5分钟后自动关闭连接（防止资源泄漏）
            setTimeout(() => {
                if (eventSource.readyState !== EventSource.CLOSED) {
                    console.log("关闭长时间未更新的SSE连接");
                    eventSource.close();
                }
            }, 300000);
        }


        // 获取报告详情
        function fetchReportDetails(reportId) {
            document.getElementById('loader').style.display = 'block';
            document.getElementById('report-container').style.display = 'none';
            document.getElementById('error-container').style.display = 'none';
            
            fetch(`/api/analysis/${reportId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(response.status === 404 ? '报告不存在' : '服务器错误');
                    }
                    return response.json();
                })
                .then(data => {
                    renderReport(data);
                })
                .catch(error => {
                    console.error('获取报告详情错误:', error);
                    showError(error.message);
                });
        }


        function renderReport(report) {
            // 填充基本信息
            document.getElementById('report-title').textContent = report.article_title || '未命名报告';
            document.getElementById('report-date').textContent = formatDate(report.report_time) || '未知日期';
            document.getElementById('analysis-time').textContent = formatDate(report.analysis_time) || '未分析';
            document.getElementById('analysis-type').textContent = getAnalysisTypeText(report.analysis_type) || '未分析';
            
            const sourceLink = document.getElementById('source-link');
            sourceLink.textContent = getDomain(report.source_url) || '未知来源';
            sourceLink.href = report.source_url || '#';

            // 加载快照信息
            loadSnapshotInfo(report.source_url);

            // 填充报告内容
            const reportBody = document.getElementById('report-body');
            if (report.analysis_html) {
                // 如果有预渲染的HTML，直接使用
                reportBody.innerHTML = report.analysis_html;
            } else if (report.analysis_content) {
                // 否则使用marked.js渲染Markdown
                let analysisHtml = marked.parse(report.analysis_content);
                
                // 在IOC相关内容后添加"原网页"链接
                analysisHtml = addSourceLinkToIOCs(analysisHtml, report.source_url);
                
                reportBody.innerHTML = analysisHtml;
            } else {
                reportBody.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i> 该报告尚未完成分析
                    </div>`;
            }

            // 处理引用信息
            processReferences(report);

            // 填充分析理由
            if (report.rationale) {
                // 确保分析理由标签页存在
                addRationaleTab();
                
                const rationaleBody = document.getElementById('rationale-body');
                if (rationaleBody) {
                    rationaleBody.innerHTML = marked.parse(report.rationale);
                }
            }
            
            // 显示报告容器
            document.getElementById('loader').style.display = 'none';
            document.getElementById('report-container').style.display = 'block';
            
        }

        // 新增辅助函数：处理引用信息
        function processReferences(report) {
            const referencesBody = document.getElementById('references-body');
            
            // 如果有references但没有references_text，则转换
            if (report.references && Array.isArray(report.references) && !report.references_text) {
                report.references_text = formatReferences(report.references);
            }
            
            if (report.references_text && report.references_text.trim()) {
                // 先进行文本清洗
                const cleanedReferencesText = cleanReferencesText(report.references_text);
                
                if (!cleanedReferencesText.trim()) {
                    referencesBody.innerHTML = '<p class="text-muted">无有效的参考信息（内容已被过滤）</p>';
                    return;
                }
                
                // 尝试识别参考信息的格式
                let referencesHtml = '<h3 class="section-title">参考信息</h3>';
                
                // 先检查是否已经是按行或段落分隔的格式
                let references = [];
                
                if (cleanedReferencesText.includes('---\n')) {
                    references = cleanedReferencesText.split('---\n').filter(ref => ref.trim());
                } else if (cleanedReferencesText.includes('\n\n')) {
                    references = cleanedReferencesText.split('\n\n').filter(ref => ref.trim());
                } else if (cleanedReferencesText.includes('\n')) {
                    references = cleanedReferencesText.split('\n').filter(ref => ref.trim());
                } else {
                    references = [cleanedReferencesText.trim()];
                }
                
                // 应用数组清洗过滤
                references = cleanReferencesArray(references);
                
                // 检测是否每个参考条目都是以数字或符号开头的列表项
                const itemPattern = /^(\d+\.|\-|\*|\+)\s/;
                let isNumberedList = references.length > 0 && references.every(ref => itemPattern.test(ref.trim()));
                
                if (references.length > 0) {
                    if (isNumberedList) {
                        // 已经是列表格式，保持原样
                        referencesHtml += '<ol class="references-list">';
                        references.forEach(ref => {
                            // 删除开头的数字/符号及空格
                            const cleanRef = ref.trim().replace(itemPattern, '');
                            referencesHtml += `<li>${sanitizeHTML(cleanRef)}</li>`;
                        });
                        referencesHtml += '</ol>';
                    } else {
                        // 转换为有序列表
                        referencesHtml += '<ol class="references-list">';
                        references.forEach(ref => {
                            referencesHtml += `<li>${sanitizeHTML(ref.trim())}</li>`;
                        });
                        referencesHtml += '</ol>';
                    }
                    
                    // 添加过滤统计信息
                    const originalCount = (report.references_text.match(/\n/g) || []).length + 1;
                    if (references.length < originalCount) {
                        referencesHtml += `<small class="text-muted mt-2 d-block">
                            <i class="bi bi-info-circle"></i> 已过滤 ${originalCount - references.length} 条不可读引用，显示 ${references.length} 条有效引用
                        </small>`;
                    }
                    
                    referencesBody.innerHTML = referencesHtml;
                } else {
                    referencesBody.innerHTML = '<p class="text-muted">无有效的参考信息</p>';
                }
            } else {
                referencesBody.innerHTML = '<p class="text-muted">无参考信息</p>';
            }
        }


        // 格式化引用数组为文本
        function formatReferences(references) {
            if (!references || !Array.isArray(references) || references.length === 0) {
                return "";
            }
            
            // 先清洗引用数组
            const cleanedRefs = cleanReferencesArray(references);
            
            return cleanedRefs.map((ref, index) => {
                return `${index + 1}. ${ref}`;
            }).join('\n');
        }


        // 分析后的UI状态
        function updateUIAfterAnalysis(analysisId) {
            // 更新URL但不刷新页面
            const newUrl = `/analysis/${analysisId}?action=view`;
            window.history.pushState({ path: newUrl }, '', newUrl);
            
            // 更新分析状态
            const statusElement = document.getElementById('analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<span class="badge bg-success">已分析</span>';
            }
            
            // 更改分析按钮为刷新按钮
            document.getElementById('analysis-actions').innerHTML = `
                <button class="btn btn-outline-primary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            `;
            
            // 清除可能存在的"开始分析"按钮的事件监听器
            const oldButton = document.getElementById('start-analysis-btn');
            if (oldButton) {
                oldButton.replaceWith(oldButton.cloneNode(true));
            }
            
            // 隐藏加载器
            document.getElementById('loader').style.display = 'none';
            
            // 显示报告容器
            document.getElementById('report-container').style.display = 'block';

            
        }


        function showError(message) {
            document.getElementById('loader').style.display = 'none';
            document.getElementById('report-container').style.display = 'none';
            
            const errorContainer = document.getElementById('error-container');
            document.getElementById('error-message').textContent = message || '加载报告时出错';
            errorContainer.style.display = 'block';
        }
        
        // 辅助函数: 格式化日期
        function formatDate(dateString) {
            if (!dateString || dateString === '未知' || dateString === '未分析') {
                return dateString;
            }
            
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return dateString;
            }
            
            return date.toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit'
            }).replace(/\//g, '-');
        }
        
        // 辅助函数: 从URL获取域名
        function getDomain(url) {
            try {
                const hostname = new URL(url).hostname;
                return hostname;
            } catch (e) {
                return url || '';
            }
        }
        
        // 辅助函数: 获取分析类型文本
        function getAnalysisTypeText(type) {
            switch (type) {
                case 'all': return '全面分析';
                case 'summary': return '内容摘要';
                case 'threats': return '威胁行为者';
                case 'vulns': return '漏洞分析';
                case 'iocs': return '威胁指标(IOCs)';
                default: return type || '未知类型';
            }
        }
        
        // 辅助函数: 净化HTML内容
        function sanitizeHTML(text) {
            const temp = document.createElement('div');
            temp.textContent = text;
            return temp.innerHTML;
        }
        
        // 引用信息清洗函数 - 清洗文本内容
        function cleanReferencesText(text) {
            if (!text || typeof text !== 'string') {
                return '';
            }
            
            // 移除内存转储和寄存器内容的模式
            let cleaned = text
                // 移除十六进制内存转储 (如: 0x1234abcd: 48 65 6c 6c...)
                .replace(/0x[0-9a-fA-F]+:\s*([0-9a-fA-F]{2}\s*)+/g, '')
                // 移除寄存器内容 (如: RAX: 0x123456, RBX: 0x789abc...)
                .replace(/\b[A-Z]{2,4}:\s*0x[0-9a-fA-F]+/g, '')
                // 移除文件路径模式 (如: C:\Users\<USER>\\[^\s]+|\/[a-zA-Z0-9/_.-]+/g, '')
                // 移除时间戳模式 (如: 2023-01-01 12:34:56)
                .replace(/\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}/g, '')
                // 移除IP地址后跟端口的模式 (如: ***********:8080)
                .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+\b/g, '')
                // 移除大量连续的十六进制字符 (超过20个字符)
                .replace(/\b[0-9a-fA-F]{20,}\b/g, '')
                // 移除多余的空白字符
                .replace(/\s+/g, ' ')
                .trim();
            
            return cleaned;
        }
        
        // 引用信息清洗函数 - 清洗数组
        function cleanReferencesArray(references) {
            if (!Array.isArray(references)) {
                return [];
            }
            
            return references
                .map(ref => {
                    // 如果是对象，提取有用信息
                    if (typeof ref === 'object' && ref !== null) {
                        const parts = [];
                        if (ref.title) parts.push(ref.title);
                        if (ref.source) parts.push(`来源: ${ref.source}`);
                        if (ref.url) parts.push(`链接: ${ref.url}`);
                        if (ref.description) parts.push(ref.description);
                        return parts.join(' - ');
                    }
                    return String(ref);
                })
                .map(ref => cleanReferencesText(ref)) // 应用文本清洗
                .filter(ref => {
                    // 过滤条件：
                    // 1. 长度至少50个字符
                    // 2. 不能全是数字和符号
                    // 3. 不能是纯粹的技术数据
                    if (ref.length < 50) return false;
                    
                    // 检查是否包含有意义的词汇
                    const meaningfulWords = /[一-龥]|[a-zA-Z]{3,}/;
                    if (!meaningfulWords.test(ref)) return false;
                    
                    // 排除主要由十六进制、数字构成的引用
                    const hexOrNumberRatio = (ref.match(/[0-9a-fA-F\s]/g) || []).length / ref.length;
                    if (hexOrNumberRatio > 0.7) return false;
                    
                    return true;
                })
                .slice(0, 20); // 限制最多显示20条引用，避免页面过长
        }
        
        // 在IOC相关内容后添加"原网页"链接
        function addSourceLinkToIOCs(htmlContent, sourceUrl) {
            if (!sourceUrl || sourceUrl === '#') {
                return htmlContent;
            }
            
            // 匹配IOC相关的标题和内容
            const iocPatterns = [
                /(<h[1-6][^>]*>.*?所有相关威胁指标.*?<\/h[1-6]>)/gi,
                /(<h[1-6][^>]*>.*?威胁指标.*?<\/h[1-6]>)/gi,
                /(<h[1-6][^>]*>.*?IOC.*?<\/h[1-6]>)/gi,
                /(<p[^>]*>.*?所有相关威胁指标.*?<\/p>)/gi,
                /(<strong>.*?所有相关威胁指标.*?<\/strong>)/gi
            ];
            
            let modifiedContent = htmlContent;
            
            // 遍历所有可能的IOC标题模式
            for (const pattern of iocPatterns) {
                modifiedContent = modifiedContent.replace(pattern, (match) => {
                    // 检测到IOC标题，但不添加单独的链接，保持原样
                    return match;
                });
            }
            
            // 检查是否有IOC内容（无论是否有标题）
            let hasIOCs = false;
            
            // 首先检查是否有IOC标题，并且标题后有真实的IOC内容
            for (const pattern of iocPatterns) {
                const matches = htmlContent.match(pattern);
                if (matches) {
                    // 找到IOC标题，检查标题后的内容
                    const titleMatch = matches[0];
                    const titleIndex = htmlContent.indexOf(titleMatch);
                    const contentAfterTitle = htmlContent.substring(titleIndex + titleMatch.length, titleIndex + titleMatch.length + 500);
                    
                    // 检查标题后是否包含"没有提供"、"无相关信息"等否定词汇
                    const hasNegativeIndicators = /没有提供|无相关信息|参考资料中没有|参考资料中没有提供具体的|未找到|不包含|无此信息|暂无|无法获取/gi.test(contentAfterTitle);
                    
                    if (!hasNegativeIndicators) {
                        // 没有否定词汇，检查是否有实际的IOC内容
                        const hasActualIOCs = /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b|[a-fA-F0-9]{32}|[a-fA-F0-9]{40}|[a-fA-F0-9]{64}|https?:\/\/|MD5:|SHA1:|SHA256:|IP地址：|域名：|文件哈希：/gi.test(contentAfterTitle);
                        
                        if (hasActualIOCs) {
                            hasIOCs = true;
                            break;
                        }
                    }
                }
            }
            
            // 如果没有IOC标题或标题后没有实际内容，再检查是否有IOC列表内容
            if (!hasIOCs) {
                const iocListPatterns = [
                    /(IP地址：.*?)(\n|<br>|$)/gi,
                    /(域名：.*?)(\n|<br>|$)/gi,
                    /(文件哈希：.*?)(\n|<br>|$)/gi,
                    /(URL：.*?)(\n|<br>|$)/gi,
                    /MD5:\s*[a-fA-F0-9]{32}/gi,
                    /SHA1:\s*[a-fA-F0-9]{40}/gi,
                    /SHA256:\s*[a-fA-F0-9]{64}/gi,
                    /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/gi, // IP地址
                    /\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b/gi // 域名
                ];
                
                for (const pattern of iocListPatterns) {
                    if (pattern.test(modifiedContent)) {
                        hasIOCs = true;
                        break;
                    }
                }
            }
            
            // 如果检测到任何IOC内容，在文档末尾添加链接
            if (hasIOCs) {
                const sourceLinkHtml = `<small class="text-muted mt-2 d-block">
                    <i class="bi bi-info-circle"></i> 以上是报告中的IOC，详请查看 <a href="${sourceUrl}" target="_blank" class="text-primary">原网页</a>
                </small>`;
                
                // 在文档末尾添加链接
                modifiedContent += sourceLinkHtml;
            }
            
            return modifiedContent;
        }

        // 查询快照信息的函数
        async function loadSnapshotInfo(url) {
            const snapshotElement = document.getElementById('snapshot-info');
            if (!snapshotElement || !url) {
                console.log('loadSnapshotInfo: 缺少必要参数', { snapshotElement: !!snapshotElement, url });
                return;
            }

            // 检查是否是文件分析
            if (url === '#' || url === 'file analysis' || !url.includes('://')) {
                console.log('loadSnapshotInfo: 本地文件分析，跳过快照查询', url);
                snapshotElement.innerHTML = `
                    <span class="text-muted small" title="本地文件分析">
                        <i class="bi bi-file-text"></i> 本地文件
                    </span>
                `;
                return;
            }

            console.log('loadSnapshotInfo: 开始查询快照信息', url);

            try {
                const apiUrl = `/api/snapshot_by_url?url=${encodeURIComponent(url)}`;
                console.log('loadSnapshotInfo: 发送请求到', apiUrl);

                const response = await fetch(apiUrl);
                console.log('loadSnapshotInfo: 收到响应', { status: response.status, ok: response.ok });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: 查询快照信息失败`);
                }

                const data = await response.json();
                console.log('loadSnapshotInfo: 解析响应数据', data);

                if (data.success) {
                    if (data.has_snapshot) {
                        // 有快照
                        console.log('loadSnapshotInfo: 找到快照', { snapshot_id: data.snapshot_id, snapshot_url: data.snapshot_url, service_running: data.service_running });

                        let linkClass = 'text-primary';
                        let linkTitle = `查看ArchiveBox快照 (ID: ${data.snapshot_id})`;
                        let warningIcon = '';

                        // 如果ArchiveBox服务未运行，显示警告
                        if (data.service_running === false) {
                            linkClass = 'text-warning';
                            linkTitle = `ArchiveBox服务未运行，快照可能无法访问 (ID: ${data.snapshot_id})`;
                            warningIcon = '<i class="bi bi-exclamation-triangle-fill text-warning me-1"></i>';
                        }

                        snapshotElement.innerHTML = `
                            ${warningIcon}<a href="${data.snapshot_url}" target="_blank" class="${linkClass} text-decoration-none" title="${linkTitle}">
                                <i class="bi bi-archive"></i> ArchiveBox快照
                            </a>
                        `;

                        // 如果有警告信息，显示提示
                        if (data.warning) {
                            console.warn('loadSnapshotInfo: 警告', data.warning);
                        }
                    } else {
                        // 无快照
                        console.log('loadSnapshotInfo: 未找到快照', data.message);
                        snapshotElement.innerHTML = `
                            <span class="text-muted small" title="${data.message}">
                                <i class="bi bi-archive"></i> 该链接没有使用ArchiveBox存档
                            </span>
                        `;
                    }
                } else {
                    throw new Error(data.error || '查询快照信息失败');
                }
            } catch (error) {
                console.error('loadSnapshotInfo: 加载快照信息失败', error);
                snapshotElement.innerHTML = `
                    <span class="text-muted small" title="快照信息查询失败: ${error.message}">
                        <i class="bi bi-exclamation-triangle"></i> 查询失败
                    </span>
                `;
            }
        }
    </script>
</body>
</html>