"""
威胁情报网站配置管理模块
提供网站配置的CRUD操作和数据库交互功能
"""

import mysql.connector
from mysql.connector import Error, pooling
from datetime import datetime
from typing import List, Dict, Optional
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ThreatIntelConfigManager:
    """威胁情报配置管理器"""

    def __init__(self, db_config: dict, connection_pool=None):
        """
        初始化配置管理器

        Args:
            db_config: 数据库配置字典
            connection_pool: 全局连接池（可选）
        """
        self.db_config = db_config
        self.connection_pool = connection_pool
        # 添加缓存
        self._sites_cache = None
        self._cache_timestamp = 0
        self._cache_ttl = 30  # 缓存30秒

        # 如果没有传入连接池，则创建自己的连接池
        if not self.connection_pool:
            self._initialize_connection_pool()

        self._initialize_database()

    def _initialize_connection_pool(self):
        """初始化数据库连接池"""
        try:
            pool_config = self.db_config.copy()
            pool_config.update({
                'pool_name': 'config_pool',
                'pool_size': 5,  # 连接池大小
                'pool_reset_session': True
            })

            self.connection_pool = pooling.MySQLConnectionPool(**pool_config)
            logger.info("数据库连接池初始化成功")
        except Error as e:
            logger.error(f"连接池初始化失败: {e}")
            # 如果连接池失败，回退到直接连接
            self.connection_pool = None

    def get_connection(self):
        """获取数据库连接"""
        try:
            if self.connection_pool:
                # 使用连接池
                connection = self.connection_pool.get_connection()
            else:
                # 回退到直接连接
                connection = mysql.connector.connect(**self.db_config)
            return connection
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def _initialize_database(self):
        """初始化数据库表和数据"""
        try:
            self._create_tables()
            self._insert_initial_data()
            logger.info("威胁情报配置数据库初始化完成")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            # 不抛出异常，允许系统继续运行

    def _create_tables(self):
        """创建数据库表"""
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # 创建威胁情报网站配置表
            create_table_sql = """
                CREATE TABLE IF NOT EXISTS threat_intel_sites (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL COMMENT '网站名称',
                    domain VARCHAR(255) NOT NULL COMMENT '域名',
                    url VARCHAR(500) NOT NULL COMMENT '完整URL',
                    category ENUM('international', 'chinese', 'crawl_url') NOT NULL DEFAULT 'international' COMMENT '网站分类',
                    language ENUM('en', 'zh', 'mixed') NOT NULL DEFAULT 'en' COMMENT '语言',
                    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
                    priority INT DEFAULT 5 COMMENT '优先级(1-10，数字越小优先级越高)',
                    description TEXT COMMENT '描述',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_url (url),
                    INDEX idx_category (category),
                    INDEX idx_status (status),
                    INDEX idx_priority (priority)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='威胁情报网站配置表'
            """

            cursor.execute(create_table_sql)
            connection.commit()
            logger.info("威胁情报网站配置表创建/检查完成")

        except Error as e:
            logger.error(f"创建数据库表失败: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()

    def _insert_initial_data(self):
        """插入初始数据"""
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # 检查是否已有数据
            cursor.execute("SELECT COUNT(*) FROM threat_intel_sites")
            count = cursor.fetchone()[0]

            if count > 0:
                logger.info(f"威胁情报网站配置表已有 {count} 条数据，跳过初始化")
                return

            # 插入国际威胁情报域名配置
            international_sites = [
                ('Microsoft Security Blog - Threat Intelligence', 'www.microsoft.com', 'www.microsoft.com/en-us/security/blog/topic/threat-intelligence', 'international', 'en', 'active', 1, '微软威胁情报博客'),
                ('Microsoft Security Blog', 'www.microsoft.com', 'www.microsoft.com/en-us/security/blog', 'international', 'en', 'active', 1, '微软安全博客'),
                ('Trellix Threat Research', 'www.trellix.com', 'www.trellix.com/blogs/research', 'international', 'en', 'active', 2, 'Trellix威胁研究博客'),
                ('Symantec Enterprise Threat Intelligence', 'www.security.com', 'www.security.com/threat-intelligence', 'international', 'en', 'active', 2, 'Symantec企业威胁情报'),
                ('ASEC AhnLab', 'asec.ahnlab.com', 'asec.ahnlab.com/en', 'international', 'en', 'active', 3, 'AhnLab威胁分析'),
                ('Fortinet Threat Research', 'www.fortinet.com', 'www.fortinet.com/blog/threat-research', 'international', 'en', 'active', 2, 'Fortinet威胁研究博客'),
                ('Infoblox Threat Intelligence', 'blogs.infoblox.com', 'blogs.infoblox.com/category/threat-intelligence', 'international', 'en', 'active', 3, 'Infoblox威胁情报博客'),
                ('Kaspersky Securelist', 'securelist.com', 'securelist.com', 'international', 'en', 'active', 1, '卡巴斯基Securelist主站'),
                ('Kaspersky - Spam and Phishing', 'securelist.com', 'securelist.com/threat-category/spam-and-phishing', 'international', 'en', 'active', 2, '卡巴斯基垃圾邮件和钓鱼威胁'),
                ('Kaspersky - Vulnerabilities', 'securelist.com', 'securelist.com/threat-category/vulnerabilities-and-exploits', 'international', 'en', 'active', 2, '卡巴斯基漏洞和利用'),
                ('Kaspersky - APT', 'securelist.com', 'securelist.com/threat-category/apt-targeted-attacks', 'international', 'en', 'active', 1, '卡巴斯基APT定向攻击'),
                ('Proofpoint Threat Insight', 'www.proofpoint.com', 'www.proofpoint.com/us/blog/threat-insight', 'international', 'en', 'active', 2, 'Proofpoint威胁洞察'),
                ('ESET WeliveSecurity', 'www.welivesecurity.com', 'www.welivesecurity.com/en/about-eset-research', 'international', 'en', 'active', 2, 'ESET威胁研究'),
                ('Trend Micro Research', 'www.trendmicro.com', 'www.trendmicro.com/en_us/research.html', 'international', 'en', 'active', 2, '趋势科技威胁研究'),
                ('Cofense Blog', 'cofense.com', 'cofense.com/blog', 'international', 'en', 'active', 3, 'Cofense威胁分析博客'),
                ('SentinelOne - From the Front Lines', 'www.sentinelone.com', 'www.sentinelone.com/blog/category/from-the-front-lines', 'international', 'en', 'active', 2, 'SentinelOne前线威胁分析'),
                ('The Hacker News', 'thehackernews.com', 'thehackernews.com', 'international', 'en', 'active', 2, 'The Hacker News威胁情报'),
                ('PolySwarm Blog', 'blog.polyswarm.io', 'blog.polyswarm.io', 'international', 'en', 'active', 3, 'PolySwarm威胁分析'),
                ('Cyfirma Research', 'cyfirma.com', 'cyfirma.com/research/', 'international', 'en', 'active', 3, 'Cyfirma威胁情报研究'),
                ('Unit 42 Palo Alto Networks', 'unit42.paloaltonetworks.com', 'unit42.paloaltonetworks.com/category/threat-research/', 'international', 'en', 'active', 1, 'Unit 42威胁研究'),
                ('Trustwave SpiderLabs', 'trustwave.com', 'trustwave.com/en-us/resources/blogs/spiderlabs-blog', 'international', 'en', 'active', 2, 'Trustwave SpiderLabs博客'),
                ('Darktrace Blog', 'darktrace.com', 'darktrace.com/blog', 'international', 'en', 'active', 3, 'Darktrace博客')
            ]

            insert_sql = """
                INSERT INTO threat_intel_sites (name, domain, url, category, language, status, priority, description)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor.executemany(insert_sql, international_sites)

            # 插入中文威胁情报域名配置
            chinese_sites = [
                ('奇安信威胁情报', 'ti.qianxin.com', 'ti.qianxin.com/blog', 'chinese', 'zh', 'active', 1, '奇安信威胁情报博客'),
                ('安恒信息威胁情报', 'ti.dbappsecurity.com.cn', 'ti.dbappsecurity.com.cn/info', 'chinese', 'zh', 'active', 2, '安恒信息威胁情报'),
                ('SecRSS威胁态势', 'www.secrss.com', 'www.secrss.com/articles', 'chinese', 'zh', 'active', 2, 'SecRSS威胁态势'),
                ('绿盟科技博客', 'blog.nsfocus.net', 'blog.nsfocus.net', 'chinese', 'zh', 'active', 2, '绿盟科技威胁告警')
            ]

            cursor.executemany(insert_sql, chinese_sites)

            # 插入爬取URL配置
            crawl_urls = [
                ('Microsoft Threat Intelligence Blog', 'www.microsoft.com', 'https://www.microsoft.com/en-us/security/blog/topic/threat-intelligence/', 'crawl_url', 'en', 'active', 1, '微软威胁情报博客列表页'),
                ('KnowBe4 Blog', 'blog.knowbe4.com', 'https://blog.knowbe4.com/page/1', 'crawl_url', 'en', 'active', 2, 'KnowBe4博客列表页'),
                ('Recorded Future Blog', 'www.recordedfuture.com', 'https://www.recordedfuture.com/blog', 'crawl_url', 'en', 'active', 2, 'Recorded Future博客'),
                ('ThreatConnect Blog', 'threatconnect.com', 'https://threatconnect.com/blog/', 'crawl_url', 'en', 'active', 2, 'ThreatConnect博客'),
                ('Varonis Blog', 'www.varonis.com', 'https://www.varonis.com/blog/page/1#blog-listing', 'crawl_url', 'en', 'active', 3, 'Varonis博客列表页'),
                ('ClearSky Security Blog', 'www.clearskysec.com', 'https://www.clearskysec.com/blog/page/4/', 'crawl_url', 'en', 'active', 3, 'ClearSky安全博客'),
                ('Trellix Platform Blog', 'www.trellix.com', 'https://www.trellix.com/blogs/platform/', 'crawl_url', 'en', 'active', 2, 'Trellix平台博客')
            ]

            cursor.executemany(insert_sql, crawl_urls)

            connection.commit()

            total_inserted = len(international_sites) + len(chinese_sites) + len(crawl_urls)
            logger.info(f"成功插入 {total_inserted} 条初始威胁情报网站配置数据")

        except Error as e:
            logger.error(f"插入初始数据失败: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    # ==================== 网站配置管理 ====================
    
    def get_all_sites(self, category: Optional[str] = None, status: Optional[str] = None) -> List[Dict]:
        """
        获取所有网站配置

        Args:
            category: 网站分类过滤 ('international', 'chinese', 'crawl_url')
            status: 状态过滤 ('active', 'inactive')

        Returns:
            网站配置列表
        """
        # 如果没有过滤条件，尝试使用缓存
        if not category and not status:
            current_time = time.time()
            if (self._sites_cache is not None and
                current_time - self._cache_timestamp < self._cache_ttl):
                logger.debug("使用缓存的网站配置数据")
                return self._sites_cache

        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            query = "SELECT * FROM threat_intel_sites WHERE 1=1"
            params = []

            if category:
                query += " AND category = %s"
                params.append(category)

            if status:
                query += " AND status = %s"
                params.append(status)

            query += " ORDER BY priority ASC, created_at DESC"

            cursor.execute(query, params)
            sites = cursor.fetchall()

            # 如果没有过滤条件，更新缓存
            if not category and not status:
                self._sites_cache = sites
                self._cache_timestamp = time.time()
                logger.debug(f"更新网站配置缓存，共 {len(sites)} 条记录")

            return sites

        except Error as e:
            logger.error(f"获取网站配置失败: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()

    def _clear_cache(self):
        """清理缓存"""
        self._sites_cache = None
        self._cache_timestamp = 0
        logger.debug("已清理网站配置缓存")

    def get_site_by_id(self, site_id: int) -> Optional[Dict]:
        """
        根据ID获取网站配置
        
        Args:
            site_id: 网站ID
            
        Returns:
            网站配置字典或None
        """
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            cursor.execute("SELECT * FROM threat_intel_sites WHERE id = %s", (site_id,))
            site = cursor.fetchone()
            
            return site
            
        except Error as e:
            logger.error(f"获取网站配置失败: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def add_site(self, site_data: Dict) -> int:
        """
        添加新网站配置

        Args:
            site_data: 网站配置数据

        Returns:
            新创建的网站ID
        """
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            # 检查是否已存在相同的域名或URL
            check_query = """
                SELECT id, name, domain, url FROM threat_intel_sites
                WHERE domain = %s OR url = %s
            """

            cursor.execute(check_query, (site_data['domain'], site_data['url']))
            existing_sites = cursor.fetchall()

            if existing_sites:
                # 找到重复的网站
                duplicate_site = existing_sites[0]
                duplicate_field = "域名和URL" if duplicate_site['domain'] == site_data['domain'] and duplicate_site['url'] == site_data['url'] else \
                                 "域名" if duplicate_site['domain'] == site_data['domain'] else "URL"

                error_msg = f"添加失败：已存在相同{duplicate_field}的网站配置 (ID: {duplicate_site['id']}, 名称: {duplicate_site['name']})"
                logger.warning(error_msg)
                raise ValueError(error_msg)

            # 没有重复，执行插入
            cursor = connection.cursor()
            query = """
                INSERT INTO threat_intel_sites
                (name, domain, url, category, language, status, priority, description)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """

            params = (
                site_data['name'],
                site_data['domain'],
                site_data['url'],
                site_data.get('category', 'international'),
                site_data.get('language', 'en'),
                site_data.get('status', 'active'),
                site_data.get('priority', 5),
                site_data.get('description', '')
            )
            
            cursor.execute(query, params)
            connection.commit()
            
            site_id = cursor.lastrowid
            logger.info(f"成功添加网站配置，ID: {site_id}")
            # 清理缓存
            self._clear_cache()
            return site_id
            
        except Error as e:
            if connection:
                connection.rollback()
            logger.error(f"添加网站配置失败: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def update_site(self, site_id: int, site_data: Dict) -> bool:
        """
        更新网站配置
        
        Args:
            site_id: 网站ID
            site_data: 更新的网站配置数据
            
        Returns:
            是否更新成功
        """
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)

            # 检查是否已存在相同的域名或URL（排除当前编辑的记录）
            check_query = """
                SELECT id, name, domain, url FROM threat_intel_sites
                WHERE (domain = %s OR url = %s) AND id != %s
            """

            cursor.execute(check_query, (site_data['domain'], site_data['url'], site_id))
            existing_sites = cursor.fetchall()

            if existing_sites:
                # 找到重复的网站
                duplicate_site = existing_sites[0]
                duplicate_field = "域名和URL" if duplicate_site['domain'] == site_data['domain'] and duplicate_site['url'] == site_data['url'] else \
                                 "域名" if duplicate_site['domain'] == site_data['domain'] else "URL"

                error_msg = f"更新失败：已存在相同{duplicate_field}的网站配置 (ID: {duplicate_site['id']}, 名称: {duplicate_site['name']})"
                logger.warning(error_msg)
                raise ValueError(error_msg)

            # 没有重复，执行更新
            cursor = connection.cursor()
            query = """
                UPDATE threat_intel_sites
                SET name = %s, domain = %s, url = %s, category = %s,
                    language = %s, status = %s, priority = %s, description = %s,
                    updated_at = NOW()
                WHERE id = %s
            """

            params = (
                site_data['name'],
                site_data['domain'],
                site_data['url'],
                site_data.get('category', 'international'),
                site_data.get('language', 'en'),
                site_data.get('status', 'active'),
                site_data.get('priority', 5),
                site_data.get('description', ''),
                site_id
            )
            
            cursor.execute(query, params)
            connection.commit()
            
            affected_rows = cursor.rowcount
            if affected_rows > 0:
                logger.info(f"成功更新网站配置，ID: {site_id}")
                # 清理缓存
                self._clear_cache()
                return True
            else:
                logger.warning(f"未找到要更新的网站配置，ID: {site_id}")
                return False
                
        except Error as e:
            if connection:
                connection.rollback()
            logger.error(f"更新网站配置失败: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def delete_site(self, site_id: int) -> bool:
        """
        删除网站配置
        
        Args:
            site_id: 网站ID
            
        Returns:
            是否删除成功
        """
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            cursor.execute("DELETE FROM threat_intel_sites WHERE id = %s", (site_id,))
            connection.commit()
            
            affected_rows = cursor.rowcount
            if affected_rows > 0:
                logger.info(f"成功删除网站配置，ID: {site_id}")
                # 清理缓存
                self._clear_cache()
                return True
            else:
                logger.warning(f"未找到要删除的网站配置，ID: {site_id}")
                return False
                
        except Error as e:
            if connection:
                connection.rollback()
            logger.error(f"删除网站配置失败: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def toggle_site_status(self, site_id: int) -> bool:
        """
        切换网站状态（active <-> inactive）
        
        Args:
            site_id: 网站ID
            
        Returns:
            是否切换成功
        """
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # 先获取当前状态
            cursor.execute("SELECT status FROM threat_intel_sites WHERE id = %s", (site_id,))
            result = cursor.fetchone()
            
            if not result:
                return False
                
            current_status = result[0]
            new_status = 'inactive' if current_status == 'active' else 'active'
            
            cursor.execute(
                "UPDATE threat_intel_sites SET status = %s, updated_at = NOW() WHERE id = %s",
                (new_status, site_id)
            )
            connection.commit()
            
            logger.info(f"成功切换网站状态，ID: {site_id}, 新状态: {new_status}")
            # 清理缓存
            self._clear_cache()
            return True
            
        except Error as e:
            if connection:
                connection.rollback()
            logger.error(f"切换网站状态失败: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def get_config_summary(self) -> Dict:
        """
        获取配置摘要信息

        Returns:
            配置摘要字典
        """
        connection = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # 统计网站配置
            cursor.execute("""
                SELECT
                    category,
                    status,
                    COUNT(*) as count
                FROM threat_intel_sites
                GROUP BY category, status
            """)

            site_stats = cursor.fetchall()

            return {
                'site_statistics': site_stats,
                'last_updated': datetime.now().isoformat()
            }

        except Error as e:
            logger.error(f"获取配置摘要失败: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
