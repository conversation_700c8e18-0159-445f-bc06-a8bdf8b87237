""""
构建关键词+域名语法，并使用https://www.searchapi.io/检索，保存成json格式，例

  {
    "query": "\"Lazarus\" site:blog.sekoia.io",
    "title": "Lazarus ClickFake Interview Campaign: From Contagious ...",
    "link": "https://blog.sekoia.io/clickfake-interview-campaign-by-lazarus/",
    "snippet": "<PERSON> is a state-sponsored threat actor which has been targeting the cryptocurrency industry since at least 2017 to generate revenue for North Korea. It is ...",
    "position": 1
  },

"""
import os
import time
import re
import socket
import random
import requests
import pandas as pd
import sys
import sqlite3
import random
import numpy as np
from typing import List, Dict
import subprocess
from datetime import datetime
from difflib import get_close_matches
from dateutil.relativedelta import relativedelta
from concurrent.futures import ThreadPoolExecutor
from warnings import simplefilter

from tqdm import tqdm
simplefilter(action='ignore', category=FutureWarning)

# 1. 导入 MySQL 连接器替换 SQLite
import mysql.connector
from mysql.connector import Error

# # 数据库配置（需用户修改）
# 配置数据库连接
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

 
"""
工具，提供快捷日期范围选择功能
域名可用性检测

"""

def get_date_range():
    """提供快捷日期范围选择功能"""
    today = datetime.today()
    choice = input(
        "请选择时间范围：\n"
        "1. 最近一周\n"
        "2. 最近一月\n"
        "3. 最近一年\n"
        "4. 自定义\n"
        "> 请输入选项(1-4): "
    ).strip()

    date_ranges = {
        '1': ('最近一周', relativedelta(weeks=1)),
        '2': ('最近一月', relativedelta(months=1)),
        '3': ('最近一年', relativedelta(years=1))
    }

    if choice in date_ranges:
        name, delta = date_ranges[choice]
        start_date = (today - delta).strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        print(f"\n已选择 {name} 范围: {start_date} 至 {end_date}")
        return start_date, end_date

    elif choice == '4':
        print("\n自定义日期范围")
        while True:
            try:
                pass  # Add your code logic here
            except Exception as e:
                print(f"An error occurred: {e}")
                start_str = input("请输入开始日期(YYYY-MM-DD): ").strip()
                start_date = datetime.strptime(start_str, "%Y-%m-%d")
                
                end_str = input("请输入结束日期(YYYY-MM-DD): ").strip()
                end_date = datetime.strptime(end_str, "%Y-%m-%d")

                if start_date > end_date:
                    print("错误：开始日期不能晚于结束日期\n")
                    continue
                    
                return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
                
            except ValueError:
                print("日期格式错误，请使用 YYYY-MM-DD 格式\n")
    else:
        print("无效选项，默认不限制日期范围")
        return None, None

def check_website_availability(website, timeout=5):
    try:
        # 统一添加协议头
        if not website.startswith(('http://', 'https://')):
            website = f'http://{website}'
        
        # 提取主机名用于DNS解析
        hostname = website.split('//')[1].split('/')[0]
        
        # 先进行DNS解析
        socket.gethostbyname(hostname)
        
        # 发起HEAD请求（节省带宽）
        response = requests.head(
            website,
            timeout=timeout,
            allow_redirects=True,
            headers={'User-Agent': 'Mozilla/5.0'}
        )
        
        # 判断状态码（2xx/3xx视为可用）
        return (response.status_code < 400, hostname)
        
    except (requests.exceptions.RequestException, 
           socket.gaierror,
           UnicodeError) as e:
        return (False, hostname)

# 检测域名可用性
def filter_available_websites(websites_df, max_workers=20):

    websites = websites_df['website'].unique().tolist()
    
    print(f"开始检查 {len(websites)} 个域名的可用性...")
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(check_website_availability, websites))
    
    # 构建结果集
    available_sites = []
    unavailable_sites = []
    
    for (status, hostname), original in zip(results, websites):
        if status:
            available_sites.append(hostname)
        else:
            unavailable_sites.append(original)
    
    # 输出统计信息
    print(f"可用域名: {len(available_sites)} 个")
    print(f"不可用域名示例: {unavailable_sites[:3]}{'...' if len(unavailable_sites)>3 else ''}")
    
    return websites_df[websites_df['website'].isin(available_sites)]


"""
---------语法拼接---------

"""

# 2. 替换 SQLite 连接函数为 MySQL 连接函数
def get_mysql_connection():
    """创建并返回 MySQL 数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        print(f"MySQL 连接错误: {e}")
        return None

# 3. 修改从数据库获取关键词的函数
def get_keywords_from_db(db_config=DB_CONFIG):
    """从MySQL数据库获取关键词数据"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)
        
        # 获取所有关键词相关表名（gp/ms/其他）
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM information_schema.tables 
            WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME NOT IN ('ca', 'li')
                AND TABLE_NAME != ''
        """, (db_config['database'],))
        
        table_names = [row['TABLE_NAME'] for row in cursor.fetchall()]
        
        # 合并所有关键词表
        all_data = []
        for tbl in table_names:
            cursor.execute(f"SELECT * FROM `{tbl}`")
            all_data.extend(cursor.fetchall())
            
        # 将结果转换为 DataFrame
        combined = pd.DataFrame(all_data)
        
        # 数据清洗管道（保持原有逻辑）
        if not combined.empty:
            processed = (
                combined
                .assign(
                    Keywords_name=lambda x: x.Keywords_name.str.strip(),
                    Keywords_type=lambda x: x.Keywords_type.str.strip().str.lower(),
                    Describe=lambda x: x.Describe.fillna('').str.strip(),
                    Combined_Text=lambda x: x.Keywords_name + " " + x.Describe
                )
                .dropna(subset=['Keywords_name', 'Keywords_type'])
                .drop_duplicates(subset=['Keywords_name', 'Keywords_type'])
            )
            
            return processed[['Keywords_name', 'Keywords_type', 'Describe', 'Combined_Text']]
        else:
            return pd.DataFrame(columns=['Keywords_name', 'Keywords_type', 'Describe', 'Combined_Text'])

    except Error as e:
        print(f"MySQL数据库连接失败: {str(e)}")
        raise
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

# 4. 修改从数据库获取网站的函数
def get_websites_from_db(db_config=DB_CONFIG, check_availability=True):
    """从MySQL数据库获取网站数据"""
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)
        
        # 从li表读取数据
        cursor.execute("SELECT website, quality FROM li")
        websites_data = cursor.fetchall()
        
        # 转换为DataFrame
        websites_df = pd.DataFrame(websites_data)
        
        # 数据清洗管道（保持原有逻辑）
        if not websites_df.empty:
            processed = (
                websites_df
                .assign(
                    website=lambda x: x.website.str.strip().str.lower(),
                    quality=lambda x: x.quality.str.strip().str.lower()
                )
                .dropna(subset=['website', 'quality'])
                .drop_duplicates(subset=['website', 'quality'])
            )

            # 启用可用性检测
            if check_availability:
                valid_websites = filter_available_websites(processed)
                print(f"有效域名数量: {len(valid_websites)}/{len(processed)}")
                return valid_websites
            else:
                return processed
        else:
            return pd.DataFrame(columns=['website', 'quality'])

    except Error as e:
        print(f"MySQL数据库连接失败: {str(e)}")
        raise
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()


# 3、将关键字和域名进行拼接---生成语法--需要按照 site:  用  来进行判断组合，关键字使用OR,AND进行组合---保存到数据库中

# def generate_search_syntax(keywords, links, bool_ops=None, after_date=None, before_date=None):
#     # 计算日期参数的预留空间
#     date_space = 0
#     if after_date:
#         date_space += len(" after:YYYY-MM-DD")
#     if before_date:
#         date_space += len(" before:YYYY-MM-DD")
    
#     # 简化处理所有关键词
#     keyword_syntax_parts = [f'"{keyword}"' for keyword in keywords]

#     bool_ops = ["OR", "AND"]

#     if not bool_ops:
#         keyword_syntax = ' OR '.join(keyword_syntax_parts)
#     else:
#         # 插入布尔运算符
#         for idx in range(len(bool_ops)):
#             keyword_syntax_parts.insert(2 * idx + 1, bool_ops[idx])
#         keyword_syntax = ' '.join(keyword_syntax_parts)

#     site_syntax = " OR ".join([f"site:{link}" for link in links])

#     # 只返回关键词语法和站点语法
#     return f"{keyword_syntax} {site_syntax}"


def generate_google_dorks(keywords_df, websites_df, char_limit=70, query_limit=25, 
                         after_date=None, before_date=None, exclude_keywords=None, random_seed=None):
    """
    优化的Google Dorks生成器，使用固定随机种子确保结果一致性
    """
    
    # 重置所有随机状态，确保完全确定性
    if random_seed is not None:
        print(f"使用随机种子: {random_seed}")
        # 设置所有可能用到的随机数生成器的种子
        random.seed(random_seed)
        np.random.seed(random_seed)  # numpy的随机数生成器
        
        # 尝试以兼容不同pandas版本的方式设置随机种子
        try:
            # 针对更新版本的pandas
            pd.set_option('compute.use_seed', random_seed)
        except:
            # 对于旧版本pandas，numpy的种子设置已足够
            pass
    else:
        # 使用当前时间作为随机种子
        import time
        current_seed = int(time.time())
        print(f"使用动态随机种子: {current_seed}")
        random.seed(current_seed)
        np.random.seed(current_seed)
        try:
            pd.set_option('compute.use_seed', current_seed)
        except:
            pass

    # 确保输入数据的一致性 - 复制并重置索引
    keywords_df = keywords_df.copy().reset_index(drop=True)
    websites_df = websites_df.copy().reset_index(drop=True)
    
    quality_order = ['高', '中', '低']

    # 创建覆盖跟踪器 - 使用有序字典确保一致的迭代顺序
    from collections import OrderedDict
    keyword_coverage = OrderedDict((kw, 0) for kw in sorted(keywords_df['Keywords_name']))
    website_coverage = OrderedDict((site, 0) for site in sorted(websites_df['website']))
    
    # 关键词分组 - 使用有序字典
    alphabet_groups = OrderedDict()
    for _, row in keywords_df.iterrows():
        if not row['Keywords_name']:
            continue
        first_letter = row['Keywords_name'][0].upper()
        if first_letter not in alphabet_groups:
            alphabet_groups[first_letter] = []
        alphabet_groups[first_letter].append(row['Keywords_name'])
    
    # 对每个字母组内的关键词排序，确保一致性
    for letter in alphabet_groups:
        alphabet_groups[letter] = sorted(alphabet_groups[letter])
    
        
    # 对域名按质量和域名本身排序，使用稳定排序
    sorted_websites = (
        websites_df
        .assign(quality=pd.Categorical(websites_df['quality'], categories=quality_order, ordered=True))
        .sort_values(['quality', 'website'], ascending=[True, True], kind='mergesort')  # 使用稳定排序
        .reset_index(drop=True)
    )
    
    # 计算日期参数的预留空间
    date_space = 0
    if after_date:
        date_space += len(" after:YYYY-MM-DD")
    if before_date:
        date_space += len(" before:YYYY-MM-DD")
    
    # 处理排除关键词
    exclude_syntax = ""
    if exclude_keywords:
        # 排序确保一致性
        sorted_excludes = sorted(exclude_keywords)
        exclude_syntax = " " + " ".join([f"-{kw}" for kw in sorted_excludes])
    
    all_queries = []
    
    # 第一轮：确保每个域名都与至少一些关键词组合
    for _, site_row in sorted_websites.iterrows():
        domain = site_row.website
        website_coverage[domain] = 0  # 重置计数器
        
        # 为每个域名创建多个查询批次，确保覆盖各种首字母
        all_letters = sorted(alphabet_groups.keys())  # 使用排序后的字母列表
        # 使用固定种子的伪随机打乱
        letters_with_seed = list(zip(all_letters, [random.random() for _ in range(len(all_letters))]))
        letters_with_seed.sort(key=lambda x: x[1])
        all_letters = [letter for letter, _ in letters_with_seed]
        
        domain_queries = 0
        for letter in all_letters:
            if domain_queries >= query_limit:
                break
                
            # 从当前字母组选择关键词
            keywords_batch = alphabet_groups[letter].copy()
            # 使用固定种子的伪随机打乱
            keywords_with_seed = list(zip(keywords_batch, [random.random() for _ in range(len(keywords_batch))]))
            keywords_with_seed.sort(key=lambda x: x[1])
            keywords_batch = [kw for kw, _ in keywords_with_seed]
            
            # 计算基本语法长度
            base_length = len(f'site:{domain}')
            options_length = date_space + len(exclude_syntax)
            remaining_chars = min(char_limit, 70) - base_length - options_length - 3
            
            current_combined_query = []
            current_length = 0
            
            for keyword in keywords_batch:
                quoted_keyword = f'"{keyword}"'
                kw_length = len(quoted_keyword)
                
                # 如果单个关键词太长，跳过
                if kw_length > remaining_chars:
                    continue
                    
                # 如果添加此关键词会超出限制，保存当前批次并开始新批次
                if current_length > 0 and current_length + kw_length + 4 > remaining_chars:
                    break
                
                # 添加关键词到当前组合
                current_combined_query.append(quoted_keyword)
                # 更新当前长度 (加4是为了考虑 " OR "的长度)
                current_length += kw_length + (4 if len(current_combined_query) > 1 else 0)
                
                keyword_coverage[keyword] += 1
            
            # 保存当前批次
            if current_combined_query:
                combined = ' OR '.join(current_combined_query)
                full_query = f"{combined} site:{domain}{exclude_syntax}"
                
                # 确保不超过长度限制
                if len(full_query) <= 70:
                    all_queries.append(full_query)
                    domain_queries += 1
                    website_coverage[domain] += 1
                else:
                    # 尝试剪裁查询以适应限制
                    trimmed_query = full_query[:67] + "..."
                    all_queries.append(trimmed_query)
                    domain_queries += 1
                    website_coverage[domain] += 1
    
    # 第二轮：确保所有未使用的关键词都被覆盖
    unused_keywords = sorted([k for k, count in keyword_coverage.items() if count == 0])
    if unused_keywords:
        print(f"\n发现 {len(unused_keywords)} 个未使用的关键词，正在添加额外查询...")
        
        # 对域名按使用频率排序，优先使用使用次数少的域名
        domain_usage = [(site, count) for site, count in website_coverage.items()]
        domain_usage.sort(key=lambda x: (x[1], x[0]))

        batch_size = 2  # 每批处理2个关键词

        # 按批次处理未使用的关键词
        for i in range(0, len(unused_keywords), batch_size):
            if i >= len(unused_keywords):
                break

            batch = unused_keywords[i:i+batch_size]
            if not batch:
                continue
                
            # 选择使用次数最少的域名 (使用确定性选择)
            domain, _ = domain_usage[i % len(domain_usage)]
            
            # 构建查询
            quoted_keywords = [f'"{kw}"' for kw in batch]
            combined = ' OR '.join(quoted_keywords[:3])  # 限制每批最多3个关键词，确保不超长
            full_query = f"{combined} site:{domain}{exclude_syntax}"
            
            # 确保不超过长度限制
            if len(full_query) <= 70:
                all_queries.append(full_query)
                website_coverage[domain] += 1
                for kw in batch[:3]:
                    keyword_coverage[kw] += 1
    
    unique_queries = []
    seen = set()
    for q in all_queries:
        if q not in seen:
            seen.add(q)
            unique_queries.append(q)
    
    keywords_used = sum(1 for _, count in keyword_coverage.items() if count > 0)
    websites_used = sum(1 for _, count in website_coverage.items() if count > 0)
    
    print(f"\n生成查询统计:")
    print(f"- 总查询数: {len(unique_queries)}")
    print(f"- 关键词覆盖率: {keywords_used}/{len(keyword_coverage)} ({keywords_used/len(keyword_coverage)*100:.1f}%)")
    print(f"- 域名覆盖率: {websites_used}/{len(website_coverage)} ({websites_used/len(website_coverage)*100:.1f}%)")
    
    return unique_queries


# 5. 修改保存搜索语法的函数
def save_search_syntax_to_db(db_config=DB_CONFIG, search_queries=None) -> int:
    if not search_queries:
        print("没有搜索语法需要保存")
        return 0

    # 输入列表去重（保留顺序）
    seen = set()
    search_queries = [q for q in search_queries if not (q in seen or seen.add(q))]

    conn = None
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        # 查询数据库中已存在的搜索语法
        format_strings = ','.join(['%s'] * len(search_queries))
        cursor.execute(f"SELECT search_syntax FROM NewRetrieval WHERE search_syntax IN ({format_strings})", search_queries)
        existing_syntax = set(row[0] for row in cursor.fetchall())
        
        # 过滤掉已存在的搜索语法
        new_queries = [q for q in search_queries if q not in existing_syntax]
        
        if not new_queries:
            print("没有新的搜索语法需要保存（全部已存在）")
            return 0
            
        # 批量插入数据
        insert_data = [(query, 0) for query in new_queries]  # use_status=0
        cursor.executemany('''
            INSERT IGNORE INTO NewRetrieval (search_syntax, use_status)
            VALUES (%s, %s);
        ''', insert_data)

        inserted_count = cursor.rowcount
        conn.commit()
        print(f"成功保存 {inserted_count} 条搜索语法（已过滤库中已有语法）")
        return inserted_count

    except mysql.connector.IntegrityError as e:  # 更精准的异常捕获
        print(f"数据库唯一性冲突（已跳过）: {str(e)}")
        return 0
    except Exception as e:
        print(f"数据库操作失败: {str(e)}")
        if conn:
            conn.rollback()
        return -1
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()


# 4、使用（https://www.searchapi.io/）进行爬取---一级页面，获取威胁情报的链接
class GoogleSearchAPI:
    def __init__(self, api_keys=None):
        # 支持多个API密钥
        if isinstance(api_keys, str):
            self.api_keys = [api_keys]
        elif isinstance(api_keys, list):
            self.api_keys = api_keys
        else:
            # 默认API密钥列表
            self.api_keys = [ ]
            
        if not self.api_keys:
            raise ValueError("必须提供至少一个API密钥")
        
        self.current_key_index = 0
        self.base_url = "https://www.searchapi.io/api/v1/search"

        # 初始化 headers，包含当前 API 密钥
        self.headers = {
            "Authorization": f"Bearer {self.api_keys[self.current_key_index]}",
            "Content-Type": "application/json"
        }

    # 添加切换 API 密钥的方法
    def switch_api_key(self):
        """切换到下一个可用的 API 密钥"""
        self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
        self.headers = {
            "Authorization": f"Bearer {self.api_keys[self.current_key_index]}",
            "Content-Type": "application/json"
        }
        print(f"切换到 API 密钥 #{self.current_key_index+1}")


    def safe_search(self, query, max_retries=3, delay=2, start_date=None, end_date=None):
        # 添加日期过滤条件
        if start_date or end_date:
            date_conditions = []
            if start_date:
                date_conditions.append(f"after:{start_date}")
            if end_date:
                date_conditions.append(f"before:{end_date}")
            query += " " + " ".join(date_conditions)

        params = {
            "engine": "google",
            "q": query,
            "gl": "us",
            "hl": "en",
            "num": 20
        }

        # 调试输出最终查询语句
        print(f"\n执行搜索：{query}")

        for attempt in range(max_retries):
            try:
                response = requests.get(
                    self.base_url,
                    params=params,
                    headers=self.headers,
                    timeout=(10,30)
                )
                response.raise_for_status()

                return self.parse_results(response.json())
            
            except requests.exceptions.HTTPError as e:
                
                if e.response.status_code == 401 and len(self.api_keys) > 1:
                    print(f"API密钥无效(401错误)，尝试切换到下一个密钥...")
                    self.switch_api_key()
                    continue
                elif e.response.status_code == 429 and len(self.api_keys) > 1:
                    print(f"触发速率限制，尝试切换 API 密钥...")
                    self.switch_api_key()
                    continue
                elif e.response.status_code == 429:
                    sleep_time = delay * (attempt + 1)
                    print(f"触发速率限制，等待{sleep_time}秒后重试...")
                    time.sleep(sleep_time)
                    continue
                print(f"HTTP错误 {e.response.status_code}")
                return e
            except Exception as e:
                print(f"搜索异常: {str(e)}")
                return e
        # 如果所有重试都失败
        raise requests.exceptions.RequestException(f"达到单个链接最大重试次数 ({max_retries})，搜索失败")


    @staticmethod
    def parse_results(data):
        """解析API返回的JSON数据"""
        results = []
        if not isinstance(data.get('organic_results'), list):
            return results

        for item in data['organic_results']:
            results.append({
                'title': item.get('title'),
                'link': item.get('link'),
                'snippet': item.get('snippet'),
                'position': item.get('position')
            })
        return results


# 4、使用（https://serpapi.com/）进行爬取---一级页面，获取威胁情报的链接
# class GoogleSearchAPI:
#     def __init__(self, api_keys=None):
#         # 支持多个API密钥
#         if isinstance(api_keys, str):
#             self.api_keys = [api_keys]
#         elif isinstance(api_keys, list):
#             self.api_keys = api_keys
#         else:
#             # 默认API密钥列表
#             self.api_keys = [ ]
            
#         if not self.api_keys:
#             raise ValueError("必须提供至少一个API密钥")
        
#         self.current_key_index = 0
#         # 使用 SerpAPI 的基础 URL
#         self.base_url = "https://serpapi.com/search"

#     # 添加切换 API 密钥的方法
#     def switch_api_key(self):
#         """切换到下一个可用的 API 密钥"""
#         self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
#         print(f"切换到 API 密钥 #{self.current_key_index+1}")

#     def safe_search(self, query, max_retries=3, delay=2, start_date=None, end_date=None):
#         # 添加日期过滤条件
#         if start_date or end_date:
#             date_conditions = []
#             if start_date:
#                 date_conditions.append(f"after:{start_date}")
#             if end_date:
#                 date_conditions.append(f"before:{end_date}")
#             query += " " + " ".join(date_conditions)

#         # 构建 SerpAPI 参数
#         params = {
#             "engine": "google",
#             "q": query,
#             "api_key": self.api_keys[self.current_key_index],
#             "gl": "us",
#             "hl": "en",
#             "num": 20,
#             "google_domain": "google.com"
#         }

#         # 调试输出最终查询语句
#         print(f"\n执行搜索：{query}")

#         for attempt in range(max_retries):
#             try:
#                 response = requests.get(
#                     self.base_url,
#                     params=params,
#                     timeout=(10, 30)
#                 )
#                 response.raise_for_status()

#                 return self.parse_results(response.json())
            
#             except requests.exceptions.HTTPError as e:
                
#                 if e.response.status_code == 401 and len(self.api_keys) > 1:
#                     print(f"API密钥无效(401错误)，尝试切换到下一个密钥...")
#                     self.switch_api_key()
#                     params["api_key"] = self.api_keys[self.current_key_index]
#                     continue
#                 elif e.response.status_code == 429 and len(self.api_keys) > 1:
#                     print(f"触发速率限制，尝试切换 API 密钥...")
#                     self.switch_api_key()
#                     params["api_key"] = self.api_keys[self.current_key_index]
#                     continue
#                 elif e.response.status_code == 429:
#                     sleep_time = delay * (attempt + 1)
#                     print(f"触发速率限制，等待{sleep_time}秒后重试...")
#                     time.sleep(sleep_time)
#                     continue
#                 print(f"HTTP错误 {e.response.status_code}")
#                 return []
#             except Exception as e:
#                 print(f"搜索异常: {str(e)}")
#                 return []

#     @staticmethod
#     def parse_results(data):
#         """解析SerpAPI返回的JSON数据"""
#         results = []
        
#         # SerpAPI 结果位于 organic_results 字段
#         organic_results = data.get('organic_results', [])
#         if not isinstance(organic_results, list):
#             return results

#         for position, item in enumerate(organic_results, 1):
#             results.append({
#                 'title': item.get('title', ''),
#                 'link': item.get('link', ''),
#                 'snippet': item.get('snippet', ''),
#                 'position': position
#             })
#         return results


"""
抓取数据保存到数据库中

"""

# 6. 修改保存爬取结果的函数
def save_to_database(db_config=DB_CONFIG, all_results=None, rollback_on_failure=True, return_records=False):
    """
    将爬取结果存储到MySQL数据库，避免重复link
    
    Args:
        db_config: 数据库配置
        all_results: 爬取结果列表
        rollback_on_failure: 失败时是否回滚
        return_records: 是否返回插入记录
        
    Returns:
        如果return_records=False，返回插入记录数量的整数
        如果return_records=True，返回元组(插入记录数量, 插入记录列表)
    """
    inserted_count = 0
    conn = None
    records_to_return = []  # 用于存储要返回的记录

    

    try:
        # 输入数据预处理：去重 + query清理
        seen_links = set()
        unique_results = []
        for item in all_results:
            # 确保每个item是字典
            if not isinstance(item, dict):
                continue
                
            # 清理query字段
            if 'query' in item:
                item['query'] = re.sub(r'(after|before):\d{4}-\d{2}-\d{2}', '', item['query'])
            
            # 去重处理
            if item['link'] not in seen_links:
                seen_links.add(item['link'])
                unique_results.append(item)
        all_results = unique_results

        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        # 查询数据库已存在的link
        links_to_check = [item['link'] for item in all_results]
        if links_to_check:
            format_strings = ','.join(['%s'] * len(links_to_check))
            cursor.execute(f"SELECT link FROM crawled_data WHERE link IN ({format_strings})", links_to_check)
            existing_links = set(row[0] for row in cursor.fetchall())
        else:
            existing_links = set()

        # 过滤掉数据库已存在的link
        filtered_results = [item for item in all_results if item['link'] not in existing_links]

        # 收集所有查询语句，用于更新状态
        all_queries = set(item.get('query', '') for item in all_results)
        
        if not filtered_results:
            print("没有新数据需要保存（全部为重复链接）")
            
            # 即使没有新数据，也应更新这些查询的状态为已检索
            if all_queries:
                try:
                    cursor.executemany('''
                        UPDATE NewRetrieval
                        SET use_status = 1
                        WHERE search_syntax = %s
                    ''', [(query,) for query in all_queries])
                    updated_count = cursor.rowcount
                    conn.commit()
                    print(f"更新了 {updated_count} 条查询状态为已检索（虽然没有新链接）")
                except Exception as e:
                    print(f"更新查询状态时出错: {str(e)}")
            
            # 返回空记录列表
            if return_records:
                return 0, []
            return 0

        # 准备批量插入数据
        insert_data = []
        required_fields = ['query', 'link']
        link_to_item_map = {}  # 用于后续关联ID与原始数据

        for idx, item in enumerate(filtered_results, 1):
            # 字段验证
            if not all(field in item for field in required_fields):
                raise ValueError(f"第 {idx}条记录缺少必填字段，必须包含：{required_fields}")

            # 构建插入元组
            data = (
                item['query'],
                item.get('title', ''),
                item['link'],
                item.get('snippet', ''),
                item.get('html_content', ''),
                item.get('extracted_text', ''),
                item.get('crawl_status', 1),
            )
            insert_data.append(data)
            link_to_item_map[item['link']] = item

        # 执行批量插入，使用MySQL语法
        cursor.executemany('''
            INSERT INTO crawled_data 
            (query, title, link, snippet, html_content, extracted_text, crawl_status)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        ''', insert_data)

        conn.commit()

        inserted_count = cursor.rowcount
        
        # 如果需要返回记录，获取刚插入的记录
        if return_records and inserted_count > 0:
            # 查询刚插入的记录
            links = [item['link'] for item in filtered_results]
            format_strings = ','.join(['%s'] * len(links))
            cursor.execute(f"SELECT id, title, link, snippet, html_content, extracted_text FROM crawled_data WHERE link IN ({format_strings})", links)
            
            # 创建映射：link -> 数据库记录
            db_records = {row[2]: row for row in cursor.fetchall()}
            
            # 创建要返回的记录列表
            for item in filtered_results:
                link = item['link']
                if link in db_records:
                    db_row = db_records[link]
                    record = {
                        'id': db_row[0],
                        'title': db_row[1],
                        'link': db_row[2],
                        'snippet': db_row[3],
                        'html_content': db_row[4],
                        'extracted_text': db_row[5],
                        'publication_date': item.get('publication_date', None),  # 从原始数据中获取
                        'query': item['query']
                    }
                    records_to_return.append(record)

        # 修改NewRetrieval表中use_status为1，已检索（对所有查询，不仅是有新链接的）
        if all_queries:
            try:
                cursor.executemany('''
                    UPDATE NewRetrieval
                    SET use_status = 1
                    WHERE search_syntax = %s
                ''', [(query,) for query in all_queries])
                conn.commit()
                print(f"use_status成功更新，插入 {inserted_count} 条新记录")
            except Exception as e:
                print(f"更新use_status时出错: {str(e)}")
                conn.rollback()
        
        if return_records:
            return inserted_count, records_to_return
        return inserted_count

    except Error as e:
        print(f"数据库错误: {str(e)}")
        if conn and rollback_on_failure:
            conn.rollback()
            print("已执行事务回滚")
        if return_records:
            return 0, []
        return 0
    except Exception as e:
        print(f"数据库存储失败: {str(e)}")
        if conn and rollback_on_failure:
            conn.rollback()
            print("已执行事务回滚")
        if return_records:
            return -1, []
        return -1
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()


"""
--------开始抓取---------
"""

# 7. 修改主函数中的数据库连接部分
def main():
    # 使用MySQL配置，不再使用本地SQLite文件
    conn = None
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 1、拿到数据 
        try:
            # 从数据库获取数据
            result_df = get_keywords_from_db()
            website_df = get_websites_from_db()

            print(f"\n成功处理 {len(result_df)} 条有效记录")
            print("数据类型分布:")
            print(result_df.Keywords_type.value_counts().to_string())

            print(website_df.head(3).to_string(index=False))
            print(f"共获取 {len(website_df)} 个有效域名")

            # 2、进行谷歌检索语法的拼接
            # 判断数据库的NewRetrieval表中是否有数据
            cursor.execute("SELECT COUNT(*) FROM NewRetrieval")
            count = cursor.fetchone()[0]
            if count > 0:
                # 从数据库中获取已存在的搜索语法
                cursor.execute("SELECT search_syntax FROM NewRetrieval WHERE use_status = 0")
                existing_syntax = cursor.fetchall()
                search_syntax = [item[0] for item in existing_syntax]
                print(f"使用表中已拼接且未被检索的数据---{len(search_syntax)}条!!!")
            else:
                print("NewRetrieval表中无数据，开始拼接")
                # 生成谷歌检索语法
                search_syntax = generate_google_dorks(result_df, website_df)

                # 将生成的语法保存到数据库
                saved_count = save_search_syntax_to_db(search_queries=search_syntax)
                if saved_count > 0:
                    print(f"成功将 {saved_count} 条搜索语法保存到数据库")
                else:
                    print("搜索语法保存失败或无需保存")

                print(f"生成 {len(search_syntax)} 条相关搜索语法")

        except Exception as e:
            print(f"数据获取错误：{str(e)}")
            return False

        # 3、使用searchapi
        try:
            api_keys = [
                "ACDaaZzg8G7uYu9HDLGWtPEn",
                "2UThqFnSKuP6S1QvFC2LeYQ4",
                "GuUPW53tXFkV9T1s3C7H3KEd",
            ]

            searcher = GoogleSearchAPI(api_keys=api_keys)


            # 获取日期范围
            start_date, end_date = get_date_range()

            all_results = []
            batch_size = 3  # 每处理3条查询保存一次
            success_count = 0
            failed_queries = []
            consecutive_failures = 0  # 新增：连续失败计数器

            try:
                for idx in tqdm(range(len(search_syntax)), desc="搜索进度", unit="条"):
                    query = search_syntax[idx]
                    try:
                        print(f"正在搜索 ({idx+1}/{len(search_syntax)})：{query}")
                        
                        results = searcher.safe_search(
                            query,
                            start_date=start_date,
                            end_date=end_date,
                        )

                        # 需要判断是否有数据，再确认use_status的值，将已检索但没有数据的use_status设为2
                        if not results:
                            cursor.execute('''
                                UPDATE NewRetrieval
                                SET use_status = 2
                                WHERE search_syntax = ?
                            ''', (query,))
                            conn.commit()
                            print(f"没有找到数据，更新use_status为2")
                        
                        # 添加结果到当前批次
                        current_results = [{'query': query, **r} for r in results]
                        all_results.extend(current_results)
                        success_count += 1
                        consecutive_failures = 0  # 重置连续失败计数

                        # 每处理batch_size条查询或到达最后一条时保存一次
                        if (idx + 1) % batch_size == 0 or idx == len(search_syntax) - 1:
                            if all_results:
                                print(f"\n正在保存批次结果 ({len(all_results)} 条)...")
                                inserted_count = save_to_database(
                                    db_config=DB_CONFIG,
                                    all_results=all_results
                                )
                                
                                if inserted_count == -1:
                                    print(f"批次数据存储失败，将继续处理后续查询")
                                else:
                                    print(f"成功存储批次数据：{inserted_count} 条")
                                
                                # 清空已保存的结果，释放内存
                                all_results = []
                    
                    except Exception as e:
                        print(f"处理查询 '{query}' 时出错: {str(e)}")
                        failed_queries.append(query)
                        consecutive_failures += 1  # 增加连续失败计数
                        
                        # 连续失败达到3次，中断搜索
                        if consecutive_failures >= 3:
                            print(f"\n⚠️ 警告: 连续失败已达到3次，终止搜索任务")
                            return False 
            except Exception as e:
                print(f"未知错误：{str(e)}")
                return False
        except Exception as e:
            print(f"搜索失败: {str(e)}")
            return False

        # 4、在获取威胁情报的链接后，对每个链接进行爬取，将整个网页保存成HTML的，标题是该链接的域名+关键字。
        # 转到data_cope.py处理
        processing_data_name = 'data_cope.py'
        try:
            print(f"🕷️ 开始保存HTML并提取出正文, 正在执行{processing_data_name}，请等待......")
            # 修改编码处理方式
            proc = subprocess.Popen(
                [sys.executable, processing_data_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )

            output = []
            while True:
                line_bytes = proc.stdout.readline()
                if not line_bytes:
                    if proc.poll() is not None:
                        break
                    continue  # 防止空数据时提前退出
                
                # 兼容性解码
                try:
                    line = line_bytes.decode('utf-8', errors='replace').rstrip()
                except UnicodeDecodeError:
                    line = line_bytes.decode('gbk', errors='replace').rstrip()
                
                print(f"[DATA_COPE] {line}")
                output.append(line)

            exit_code = proc.wait()

            if exit_code != 0:
                raise subprocess.CalledProcessError(
                    exit_code, 
                    proc.args, 
                    '\n'.join(output)
                )
            
            # joined_output = '\n'.join(output)
            print(f"{processing_data_name} 执行成功\n")
            # print(f"{processing_data_name} 输出日志\n{joined_output}")

            # 5、使用DeepSeek V3模型进行分析提取到的正文
            A_data_name = 'AI_Cope_HTML.py'
            try:
                print(f" 开始使用DeepSeek V3模型进行分析提取到的正文, 正在执行{A_data_name}，请等待......")
                # 修改编码处理方式
                proc = subprocess.Popen(
                    [sys.executable, A_data_name],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT
                )

                output = []
                while True:
                    line_bytes = proc.stdout.readline()
                    if not line_bytes:
                        if proc.poll() is not None:
                            break
                        continue  # 防止空数据时提前退出
                    
                    # 兼容性解码
                    try:
                        line = line_bytes.decode('utf-8', errors='replace').rstrip()
                    except UnicodeDecodeError:
                        line = line_bytes.decode('gbk', errors='replace').rstrip()
                    
                    print(f"[DATA_COPE] {line}")
                    output.append(line)

                exit_code = proc.wait()

                if exit_code != 0:
                    raise subprocess.CalledProcessError(
                        exit_code, 
                        proc.args, 
                        '\n'.join(output)
                    )
                
                # joined_output = '\n'.join(output)
                print(f"{A_data_name} 执行成功\n")

            except subprocess.CalledProcessError as e:
                print(f"执行失败，错误代码: {e.returncode}")
                print(f"错误输出: {e.output.decode('utf-8', errors='replace')}")
                return False
            
            return True
        except Exception as e:
            print(f"未知处理错误：{str(e)}")
            return False
        finally:
            conn.close()
    except Error as e:
        print(f"MySQL数据库连接失败: {str(e)}")
        return False
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()

if __name__ == "__main__":
    main()

# 目前检索语法有 810 条，随机种子为 666
# 生成查询统计:
# - 总查询数: 810
# - 关键词覆盖率: 1068/1070 (99.8%)
# - 域名覆盖率: 26/26 (100.0%)
# 成功保存 810 条搜索语法到数据库
# 成功将 810 条搜索语法保存到数据库