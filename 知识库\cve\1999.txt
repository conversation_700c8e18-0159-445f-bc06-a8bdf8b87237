CVE-1999-0001(PUBLISHED):ip_input.c in BSD-derived TCP/IP implementations allows remote attackers to cause a denial of service (crash or hang) via crafted packets.
CVE-1999-0002(PUBLISHED):Buffer overflow in NFS mountd gives root access to remote attackers, mostly in Linux systems.
CVE-1999-0003(PUBLISHED):Execute commands as root via buffer overflow in Tooltalk database server (rpc.ttdbserverd).
CVE-1999-0004(PUBLISHED):MIME buffer overflow in email clients, e.g. Solaris mailtool and Outlook.
CVE-1999-0005(PUBLISHED):Arbitrary command execution via IMAP buffer overflow in authenticate command.
CVE-1999-0006(PUBLISHED):Buffer overflow in POP servers based on BSD/Qualcomm's qpopper allows remote attackers to gain root access using a long PASS command.
CVE-1999-0007(PUBLISHED):Information from SSL-encrypted sessions via PKCS #1.
CVE-1999-0008(PUBLISHED):Buffer overflow in NIS+, in Sun's rpc.nisd program.
CVE-1999-0009(PUBLISHED):Inverse query buffer overflow in BIND 4.9 and BIND 8 Releases.
CVE-1999-0010(PUBLISHED):Denial of Service vulnerability in BIND 8 Releases via maliciously formatted DNS messages.
CVE-1999-0011(PUBLISHED):Denial of Service vulnerabilities in BIND 4.9 and BIND 8 Releases via CNAME record and zone transfer.
CVE-1999-0012(PUBLISHED):Some web servers under Microsoft Windows allow remote attackers to bypass access restrictions for files with long file names.
CVE-1999-0013(PUBLISHED):Stolen credentials from SSH clients via ssh-agent program, allowing other local users to access remote accounts belonging to the ssh-agent user.
CVE-1999-0014(PUBLISHED):Unauthorized privileged access or denial of service via dtappgather program in CDE.
CVE-1999-0015(PUBLISHED):Teardrop IP denial of service.
CVE-1999-0016(PUBLISHED):Land IP denial of service.
CVE-1999-0017(PUBLISHED):FTP servers can allow an attacker to connect to arbitrary ports on machines other than the FTP client, aka FTP bounce.
CVE-1999-0018(PUBLISHED):Buffer overflow in statd allows root privileges.
CVE-1999-0019(PUBLISHED):Delete or create a file via rpc.statd, due to invalid information.
CVE-1999-0021(PUBLISHED):Arbitrary command execution via buffer overflow in Count.cgi (wwwcount) cgi-bin program.
CVE-1999-0022(PUBLISHED):Local user gains root privileges via buffer overflow in rdist, via expstr() function.
CVE-1999-0023(PUBLISHED):Local user gains root privileges via buffer overflow in rdist, via lookup() function.
CVE-1999-0024(PUBLISHED):DNS cache poisoning via BIND, by predictable query IDs.
CVE-1999-0025(PUBLISHED):root privileges via buffer overflow in df command on SGI IRIX systems.
CVE-1999-0026(PUBLISHED):root privileges via buffer overflow in pset command on SGI IRIX systems.
CVE-1999-0027(PUBLISHED):root privileges via buffer overflow in eject command on SGI IRIX systems.
CVE-1999-0028(PUBLISHED):root privileges via buffer overflow in login/scheme command on SGI IRIX systems.
CVE-1999-0029(PUBLISHED):root privileges via buffer overflow in ordist command on SGI IRIX systems.
CVE-1999-0030(PUBLISHED):root privileges via buffer overflow in xlock command on SGI IRIX systems.
CVE-1999-0031(PUBLISHED):JavaScript in Internet Explorer 3.x and 4.x, and Netscape 2.x, 3.x and 4.x, allows remote attackers to monitor a user's web activities, aka the Bell Labs vulnerability.
CVE-1999-0032(PUBLISHED):Buffer overflow in lpr, as used in BSD-based systems including Linux, allows local users to execute arbitrary code as root via a long -C (classification) command line option.
CVE-1999-0033(PUBLISHED):Command execution in Sun systems via buffer overflow in the at program.
CVE-1999-0034(PUBLISHED):Buffer overflow in suidperl (sperl), Perl 4.x and 5.x.
CVE-1999-0035(PUBLISHED):Race condition in signal handling routine in ftpd, allowing read/write arbitrary files.
CVE-1999-0036(PUBLISHED):IRIX login program with a nonzero LOCKOUT parameter allows creation or damage to files.
CVE-1999-0037(PUBLISHED):Arbitrary command execution via metamail package using message headers, when user processes attacker's message using metamail.
CVE-1999-0038(PUBLISHED):Buffer overflow in xlock program allows local users to execute commands as root.
CVE-1999-0039(PUBLISHED):webdist CGI program (webdist.cgi) in SGI IRIX allows remote attackers to execute arbitrary commands via shell metacharacters in the distloc parameter.
CVE-1999-0040(PUBLISHED):Buffer overflow in Xt library of X Windowing System allows local users to execute commands with root privileges.
CVE-1999-0041(PUBLISHED):Buffer overflow in NLS (Natural Language Service).
CVE-1999-0042(PUBLISHED):Buffer overflow in University of Washington's implementation of IMAP and POP servers.
CVE-1999-0043(PUBLISHED):Command execution via shell metachars in INN daemon (innd) 1.5 using "newgroup" and "rmgroup" control messages, and others.
CVE-1999-0044(PUBLISHED):fsdump command in IRIX allows local users to obtain root access by modifying sensitive files.
CVE-1999-0045(PUBLISHED):List of arbitrary files on Web host via nph-test-cgi script.
CVE-1999-0046(PUBLISHED):Buffer overflow of rlogin program using TERM environmental variable.
CVE-1999-0047(PUBLISHED):MIME conversion buffer overflow in sendmail versions 8.8.3 and 8.8.4.
CVE-1999-0048(PUBLISHED):Talkd, when given corrupt DNS information, can be used to execute arbitrary commands with root privileges.
CVE-1999-0049(PUBLISHED):Csetup under IRIX allows arbitrary file creation or overwriting.
CVE-1999-0050(PUBLISHED):Buffer overflow in HP-UX newgrp program.
CVE-1999-0051(PUBLISHED):Arbitrary file creation and program execution using FLEXlm LicenseManager, from versions 4.0 to 5.0, in IRIX.
CVE-1999-0052(PUBLISHED):IP fragmentation denial of service in FreeBSD allows a remote attacker to cause a crash.
CVE-1999-0053(PUBLISHED):TCP RST denial of service in FreeBSD.
CVE-1999-0054(PUBLISHED):Sun's ftpd daemon can be subjected to a denial of service.
CVE-1999-0055(PUBLISHED):Buffer overflows in Sun libnsl allow root access.
CVE-1999-0056(PUBLISHED):Buffer overflow in Sun's ping program can give root access to local users.
CVE-1999-0057(PUBLISHED):Vacation program allows command execution by remote users through a sendmail command.
CVE-1999-0058(PUBLISHED):Buffer overflow in PHP cgi program, php.cgi allows shell access.
CVE-1999-0059(PUBLISHED):IRIX fam service allows an attacker to obtain a list of all files on the server.
CVE-1999-0060(PUBLISHED):Attackers can cause a denial of service in Ascend MAX and Pipeline routers with a malformed packet to the discard port, which is used by the Java Configurator tool.
CVE-1999-0061(PUBLISHED):File creation and deletion, and remote execution, in the BSD line printer daemon (lpd).
CVE-1999-0062(PUBLISHED):The chpass command in OpenBSD allows a local user to gain root access through file descriptor leakage.
CVE-1999-0063(PUBLISHED):Cisco IOS 12.0 and other versions can be crashed by malicious UDP packets to the syslog port.
CVE-1999-0064(PUBLISHED):Buffer overflow in AIX lquerylv program gives root access to local users.
CVE-1999-0065(PUBLISHED):Multiple buffer overflows in how dtmail handles attachments allows a remote attacker to execute commands.
CVE-1999-0066(PUBLISHED):AnyForm CGI remote execution.
CVE-1999-0067(PUBLISHED):phf CGI program allows remote command execution through shell metacharacters.
CVE-1999-0068(PUBLISHED):CGI PHP mylog script allows an attacker to read any file on the target server.
CVE-1999-0069(PUBLISHED):Solaris ufsrestore buffer overflow.
CVE-1999-0070(PUBLISHED):test-cgi program allows an attacker to list files on the server.
CVE-1999-0071(PUBLISHED):Apache httpd cookie buffer overflow for versions 1.1.1 and earlier.
CVE-1999-0072(PUBLISHED):Buffer overflow in AIX xdat gives root access to local users.
CVE-1999-0073(PUBLISHED):Telnet allows a remote client to specify environment variables including LD_LIBRARY_PATH, allowing an attacker to bypass the normal system libraries and gain root access.
CVE-1999-0074(PUBLISHED):Listening TCP ports are sequentially allocated, allowing spoofing attacks.
CVE-1999-0075(PUBLISHED):PASV core dump in wu-ftpd daemon when attacker uses a QUOTE PASV command after specifying a username and password.
CVE-1999-0076(PUBLISHED):Buffer overflow in wu-ftp from PASV command causes a core dump.
CVE-1999-0077(PUBLISHED):Predictable TCP sequence numbers allow spoofing.
CVE-1999-0078(PUBLISHED):pcnfsd (aka rpc.pcnfsd) allows local users to change file permissions, or execute arbitrary commands through arguments in the RPC call.
CVE-1999-0079(PUBLISHED):Remote attackers can cause a denial of service in FTP by issuing multiple PASV commands, causing the server to run out of available ports.
CVE-1999-0080(PUBLISHED):Certain configurations of wu-ftp FTP server 2.4 use a _PATH_EXECPATH setting to a directory with dangerous commands, such as /bin, which allows remote authenticated users to gain root access via the "site exec" command.
CVE-1999-0081(PUBLISHED):wu-ftp allows files to be overwritten via the rnfr command.
CVE-1999-0082(PUBLISHED):CWD ~root command in ftpd allows root access.
CVE-1999-0083(PUBLISHED):getcwd() file descriptor leak in FTP.
CVE-1999-0084(PUBLISHED):Certain NFS servers allow users to use mknod to gain privileges by creating a writable kmem device and setting the UID to 0.
CVE-1999-0085(PUBLISHED):Buffer overflow in rwhod on AIX and other operating systems allows remote attackers to execute arbitrary code via a UDP packet with a long hostname.
CVE-1999-0086(PUBLISHED):AIX routed allows remote users to modify sensitive files.
CVE-1999-0087(PUBLISHED):Denial of service in AIX telnet can freeze a system and prevent users from accessing the server.
CVE-1999-0088(PUBLISHED):IRIX and AIX automountd services (autofsd) allow remote users to execute root commands.
CVE-1999-0089(PUBLISHED):Buffer overflow in AIX libDtSvc library can allow local users to gain root access.
CVE-1999-0090(PUBLISHED):Buffer overflow in AIX rcp command allows local users to obtain root access.
CVE-1999-0091(PUBLISHED):Buffer overflow in AIX writesrv command allows local users to obtain root access.
CVE-1999-0092(PUBLISHED):Various vulnerabilities in the AIX portmir command allows local users to obtain root access.
CVE-1999-0093(PUBLISHED):AIX nslookup command allows local users to obtain root access by not dropping privileges correctly.
CVE-1999-0094(PUBLISHED):AIX piodmgrsu command allows local users to gain additional group privileges.
CVE-1999-0095(PUBLISHED):The debug command in Sendmail is enabled, allowing attackers to execute commands as root.
CVE-1999-0096(PUBLISHED):Sendmail decode alias can be used to overwrite sensitive files.
CVE-1999-0097(PUBLISHED):The AIX FTP client can be forced to execute commands from a malicious server through shell metacharacters (e.g. a pipe character).
CVE-1999-0098(PUBLISHED):Buffer overflow in SMTP HELO command in Sendmail allows a remote attacker to hide activities.
CVE-1999-0099(PUBLISHED):Buffer overflow in syslog utility allows local or remote attackers to gain root privileges.
CVE-1999-0100(PUBLISHED):Remote access in AIX innd 1.5.1, using control messages.
CVE-1999-0101(PUBLISHED):Buffer overflow in AIX and Solaris "gethostbyname" library call allows root access through corrupt DNS host names.
CVE-1999-0102(PUBLISHED):Buffer overflow in SLmail 3.x allows attackers to execute commands using a large FROM line.
CVE-1999-0103(PUBLISHED):Echo and chargen, or other combinations of UDP services, can be used in tandem to flood the server, a.k.a. UDP bomb or UDP packet storm.
CVE-1999-0104(PUBLISHED):A later variation on the Teardrop IP denial of service attack, a.k.a. Teardrop-2.
CVE-1999-0105(PUBLISHED):finger allows recursive searches by using a long string of @ symbols.
CVE-1999-0106(PUBLISHED):Finger redirection allows finger bombs.
CVE-1999-0107(PUBLISHED):Buffer overflow in Apache 1.2.5 and earlier allows a remote attacker to cause a denial of service with a large number of GET requests containing a large number of / characters.
CVE-1999-0108(PUBLISHED):The printers program in IRIX has a buffer overflow that gives root access to local users.
CVE-1999-0109(PUBLISHED):Buffer overflow in ffbconfig in Solaris 2.5.1.
CVE-1999-0111(PUBLISHED):RIP v1 is susceptible to spoofing.
CVE-1999-0112(PUBLISHED):Buffer overflow in AIX dtterm program for the CDE.
CVE-1999-0113(PUBLISHED):Some implementations of rlogin allow root access if given a -froot parameter.
CVE-1999-0114(PUBLISHED):Local users can execute commands as other users, and read other users' files, through the filter command in the Elm elm-2.4 mail package using a symlink attack.
CVE-1999-0115(PUBLISHED):AIX bugfiler program allows local users to gain root access.
CVE-1999-0116(PUBLISHED):Denial of service when an attacker sends many SYN packets to create multiple connections without ever sending an ACK to complete the connection, aka SYN flood.
CVE-1999-0117(PUBLISHED):AIX passwd allows local users to gain root access.
CVE-1999-0118(PUBLISHED):AIX infod allows local users to gain root access through an X display.
CVE-1999-0119(PUBLISHED):Windows NT 4.0 beta allows users to read and delete shares.
CVE-1999-0120(PUBLISHED):Sun/Solaris utmp file allows local users to gain root access if it is writable by users other than root.
CVE-1999-0121(PUBLISHED):Buffer overflow in dtaction command gives root access.
CVE-1999-0122(PUBLISHED):Buffer overflow in AIX lchangelv gives root access.
CVE-1999-0123(PUBLISHED):Race condition in Linux mailx command allows local users to read user files.
CVE-1999-0124(PUBLISHED):Vulnerabilities in UMN gopher and gopher+ versions 1.12 and 2.0x allow an intruder to read any files that can be accessed by the gopher daemon.
CVE-1999-0125(PUBLISHED):Buffer overflow in SGI IRIX mailx program.
CVE-1999-0126(PUBLISHED):SGI IRIX buffer overflow in xterm and Xaw allows root access.
CVE-1999-0127(PUBLISHED):swinstall and swmodify commands in SD-UX package in HP-UX systems allow local users to create or overwrite arbitrary files to gain root access.
CVE-1999-0128(PUBLISHED):Oversized ICMP ping packets can result in a denial of service, aka Ping o' Death.
CVE-1999-0129(PUBLISHED):Sendmail allows local users to write to a file and gain group permissions via a .forward or :include: file.
CVE-1999-0130(PUBLISHED):Local users can start Sendmail in daemon mode and gain root privileges.
CVE-1999-0131(PUBLISHED):Buffer overflow and denial of service in Sendmail 8.7.5 and earlier through GECOS field gives root access to local users.
CVE-1999-0132(PUBLISHED):Expreserve, as used in vi and ex, allows local users to overwrite arbitrary files and gain root access.
CVE-1999-0133(PUBLISHED):fm_fls license server for Adobe Framemaker allows local users to overwrite arbitrary files and gain root access.
CVE-1999-0134(PUBLISHED):vold in Solaris 2.x allows local users to gain root access.
CVE-1999-0135(PUBLISHED):admintool in Solaris allows a local user to write to arbitrary files and gain root access.
CVE-1999-0136(PUBLISHED):Kodak Color Management System (KCMS) on Solaris allows a local user to write to arbitrary files and gain root access.
CVE-1999-0137(PUBLISHED):The dip program on many Linux systems allows local users to gain root access via a buffer overflow.
CVE-1999-0138(PUBLISHED):The suidperl and sperl program do not give up root privileges when changing UIDs back to the original users, allowing root access.
CVE-1999-0139(PUBLISHED):Buffer overflow in Solaris x86 mkcookie allows local users to obtain root access.
CVE-1999-0140(PUBLISHED):Denial of service in RAS/PPTP on NT systems.
CVE-1999-0141(PUBLISHED):Java Bytecode Verifier allows malicious applets to execute arbitrary commands as the user of the applet.
CVE-1999-0142(PUBLISHED):The Java Applet Security Manager implementation in Netscape Navigator 2.0 and Java Developer's Kit 1.0 allows an applet to connect to arbitrary hosts.
CVE-1999-0143(PUBLISHED):Kerberos 4 key servers allow a user to masquerade as another by breaking and generating session keys.
CVE-1999-0144(PUBLISHED):Denial of service in Qmail by specifying a large number of recipients with the RCPT command.
CVE-1999-0145(PUBLISHED):Sendmail WIZ command enabled, allowing root access.
CVE-1999-0146(PUBLISHED):The campas CGI program provided with some NCSA web servers allows an attacker to execute arbitrary commands via encoded carriage return characters in the query string, as demonstrated by reading the password file.
CVE-1999-0147(PUBLISHED):The aglimpse CGI program of the Glimpse package allows remote execution of arbitrary commands.
CVE-1999-0148(PUBLISHED):The handler CGI program in IRIX allows arbitrary command execution.
CVE-1999-0149(PUBLISHED):The wrap CGI program in IRIX allows remote attackers to view arbitrary directory listings via a .. (dot dot) attack.
CVE-1999-0150(PUBLISHED):The Perl fingerd program allows arbitrary command execution from remote users.
CVE-1999-0151(PUBLISHED):The SATAN session key may be disclosed if the user points the web browser to other sites, possibly allowing root access.
CVE-1999-0152(PUBLISHED):The DG/UX finger daemon allows remote command execution through shell metacharacters.
CVE-1999-0153(PUBLISHED):Windows 95/NT out of band (OOB) data denial of service through NETBIOS port, aka WinNuke.
CVE-1999-0154(PUBLISHED):IIS 2.0 and 3.0 allows remote attackers to read the source code for ASP pages by appending a . (dot) to the end of the URL.
CVE-1999-0155(PUBLISHED):The ghostscript command with the -dSAFER option allows remote attackers to execute commands.
CVE-1999-0156(PUBLISHED):wu-ftpd FTP daemon allows any user and password combination.
CVE-1999-0157(PUBLISHED):Cisco PIX firewall and CBAC IP fragmentation attack results in a denial of service.
CVE-1999-0158(PUBLISHED):Cisco PIX firewall manager (PFM) on Windows NT allows attackers to connect to port 8080 on the PFM server and retrieve any file whose name and location is known.
CVE-1999-0159(PUBLISHED):Attackers can crash a Cisco IOS router or device, provided they can get to an interactive prompt (such as a login).  This applies to some IOS 9.x, 10.x, and 11.x releases.
CVE-1999-0160(PUBLISHED):Some classic Cisco IOS devices have a vulnerability in the PPP CHAP authentication to establish unauthorized PPP connections.
CVE-1999-0161(PUBLISHED):In Cisco IOS 10.3, with the tacacs-ds or tacacs keyword, an extended IP access control list could bypass filtering.
CVE-1999-0162(PUBLISHED):The "established" keyword in some Cisco IOS software allowed an attacker to bypass filtering.
CVE-1999-0163(PUBLISHED):In older versions of Sendmail, an attacker could use a pipe character to execute root commands.
CVE-1999-0164(PUBLISHED):A race condition in the Solaris ps command allows an attacker to overwrite critical files.
CVE-1999-0165(PUBLISHED):NFS cache poisoning.
CVE-1999-0166(PUBLISHED):NFS allows users to use a "cd .." command to access other directories besides the exported file system.
CVE-1999-0167(PUBLISHED):In SunOS, NFS file handles could be guessed, giving unauthorized access to the exported file system.
CVE-1999-0168(PUBLISHED):The portmapper may act as a proxy and redirect service requests from an attacker, making the request appear to come from the local host, possibly bypassing authentication that would otherwise have taken place.  For example, NFS file systems could be mounted through the portmapper despite export restrictions.
CVE-1999-0169(PUBLISHED):NFS allows attackers to read and write any file on the system by specifying a false UID.
CVE-1999-0170(PUBLISHED):Remote attackers can mount an NFS file system in Ultrix or OSF, even if it is denied on the access list.
CVE-1999-0171(PUBLISHED):Denial of service in syslog by sending it a large number of superfluous messages.
CVE-1999-0172(PUBLISHED):FormMail CGI program allows remote execution of commands.
CVE-1999-0173(PUBLISHED):FormMail CGI program can be used by web servers other than the host server that the program resides on.
CVE-1999-0174(PUBLISHED):The view-source CGI program allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-1999-0175(PUBLISHED):The convert.bas program in the Novell web server allows a remote attackers to read any file on the system that is internally accessible by the web server.
CVE-1999-0176(PUBLISHED):The Webgais program allows a remote user to execute arbitrary commands.
CVE-1999-0177(PUBLISHED):The uploader program in the WebSite web server allows a remote attacker to execute arbitrary programs.
CVE-1999-0178(PUBLISHED):Buffer overflow in the win-c-sample program (win-c-sample.exe) in the WebSite web server 1.1e allows remote attackers to execute arbitrary code via a long query string.
CVE-1999-0179(PUBLISHED):Windows NT crashes or locks up when a Samba client executes a "cd .." command on a file share.
CVE-1999-0180(PUBLISHED):in.rshd allows users to login with a NULL username and execute commands.
CVE-1999-0181(PUBLISHED):The wall daemon can be used for denial of service, social engineering attacks, or to execute remote commands.
CVE-1999-0182(PUBLISHED):Samba has a buffer overflow which allows a remote attacker to obtain root access by specifying a long password.
CVE-1999-0183(PUBLISHED):Linux implementations of TFTP would allow access to files outside the restricted directory.
CVE-1999-0184(PUBLISHED):When compiled with the -DALLOW_UPDATES option, bind allows dynamic updates to the DNS server, allowing for malicious modification of DNS records.
CVE-1999-0185(PUBLISHED):In SunOS or Solaris, a remote user could connect from an FTP server's data port to an rlogin server on a host that trusts the FTP server, allowing remote command execution.
CVE-1999-0186(PUBLISHED):In Solaris, an SNMP subagent has a default community string that allows remote attackers to execute arbitrary commands as root, or modify system parameters.
CVE-1999-0188(PUBLISHED):The passwd command in Solaris can be subjected to a denial of service.
CVE-1999-0189(PUBLISHED):Solaris rpcbind listens on a high numbered UDP port, which may not be filtered since the standard port number is 111.
CVE-1999-0190(PUBLISHED):Solaris rpcbind can be exploited to overwrite arbitrary files and gain root access.
CVE-1999-0191(PUBLISHED):IIS newdsn.exe CGI script allows remote users to overwrite files.
CVE-1999-0192(PUBLISHED):Buffer overflow in telnet daemon tgetent routing allows remote attackers to gain root access via the TERMCAP environmental variable.
CVE-1999-0193(PUBLISHED):Denial of service in Ascend and 3com routers, which can be rebooted by sending a zero length TCP option.
CVE-1999-0194(PUBLISHED):Denial of service in in.comsat allows attackers to generate messages.
CVE-1999-0195(PUBLISHED):Denial of service in RPC portmapper allows attackers to register or unregister RPC services or spoof RPC services using a spoofed source IP address such as 127.0.0.1.
CVE-1999-0196(PUBLISHED):websendmail in Webgais 1.0 allows a remote user to access arbitrary files and execute arbitrary code via the receiver parameter ($VAR_receiver variable).
CVE-1999-0197(PUBLISHED):finger 0@host on some systems may print information on some user accounts.
CVE-1999-0198(PUBLISHED):finger .@host on some systems may print information on some user accounts.
CVE-1999-0199(PUBLISHED):manual/search.texi in the GNU C Library (aka glibc) before 2.2 lacks a statement about the unspecified tdelete return value upon deletion of a tree's root, which might allow attackers to access a dangling pointer in an application whose developer was unaware of a documentation update from 1999.
CVE-1999-0200(PUBLISHED):Windows NT FTP server (WFTP) with the guest account enabled without a password allows an attacker to log into the FTP server using any username and password.
CVE-1999-0201(PUBLISHED):A quote cwd command on FTP servers can reveal the full path of the home directory of the "ftp" user.
CVE-1999-0202(PUBLISHED):The GNU tar command, when used in FTP sessions, may allow an attacker to execute arbitrary commands.
CVE-1999-0203(PUBLISHED):In Sendmail, attackers can gain root privileges via SMTP by specifying an improper "mail from" address and an invalid "rcpt to" address that would cause the mail to bounce to a program.
CVE-1999-0204(PUBLISHED):Sendmail 8.6.9 allows remote attackers to execute root commands, using ident.
CVE-1999-0205(PUBLISHED):Denial of service in Sendmail 8.6.11 and 8.6.12.
CVE-1999-0206(PUBLISHED):MIME buffer overflow in Sendmail 8.8.0 and 8.8.1 gives root access.
CVE-1999-0207(PUBLISHED):Remote attacker can execute commands through Majordomo using the Reply-To field and a "lists" command.
CVE-1999-0208(PUBLISHED):rpc.ypupdated (NIS) allows remote users to execute arbitrary commands.
CVE-1999-0209(PUBLISHED):The SunView (SunTools) selection_svc facility allows remote users to read files.
CVE-1999-0210(PUBLISHED):Automount daemon automountd allows local or remote users to gain privileges via shell metacharacters.
CVE-1999-0211(PUBLISHED):Extra long export lists over 256 characters in some mount daemons allows NFS directories to be mounted by anyone.
CVE-1999-0212(PUBLISHED):Solaris rpc.mountd generates error messages that allow a remote attacker to determine what files are on the server.
CVE-1999-0213(PUBLISHED):libnsl in Solaris allowed an attacker to perform a denial of service of rpcbind.
CVE-1999-0214(PUBLISHED):Denial of service by sending forged ICMP unreachable packets.
CVE-1999-0215(PUBLISHED):Routed allows attackers to append data to files.
CVE-1999-0216(PUBLISHED):Denial of service of inetd on Linux through SYN and RST packets.
CVE-1999-0217(PUBLISHED):Malicious option settings in UDP packets could force a reboot in SunOS 4.1.3 systems.
CVE-1999-0218(PUBLISHED):Livingston portmaster machines could be rebooted via a series of commands.
CVE-1999-0219(PUBLISHED):Buffer overflow in FTP Serv-U 2.5 allows remote authenticated users to cause a denial of service (crash) via a long (1) CWD or (2) LS (list) command.
CVE-1999-0220(PUBLISHED):Attackers can do a denial of service of IRC by crashing the server.
CVE-1999-0221(PUBLISHED):Denial of service of Ascend routers through port 150 (remote administration).
CVE-1999-0222(PUBLISHED):Denial of service in Cisco IOS web server allows attackers to reboot the router using a long URL.
CVE-1999-0223(PUBLISHED):Solaris syslogd crashes when receiving a message from a host that doesn't have an inverse DNS entry.
CVE-1999-0224(PUBLISHED):Denial of service in Windows NT messenger service through a long username.
CVE-1999-0225(PUBLISHED):Windows NT 4.0 allows remote attackers to cause a denial of service via a malformed SMB logon request in which the actual data size does not match the specified size.
CVE-1999-0226(PUBLISHED):Windows NT TCP/IP processes fragmented IP packets improperly, causing a denial of service.
CVE-1999-0227(PUBLISHED):Access violation in LSASS.EXE (LSA/LSARPC) program in Windows NT allows a denial of service.
CVE-1999-0228(PUBLISHED):Denial of service in RPCSS.EXE program (RPC Locator) in Windows NT.
CVE-1999-0229(PUBLISHED):Denial of service in Windows NT IIS server using ..\..
CVE-1999-0230(PUBLISHED):Buffer overflow in Cisco 7xx routers through the telnet service.
CVE-1999-0231(PUBLISHED):Buffer overflow in IP-Switch IMail and Seattle Labs Slmail 2.6 packages using a long VRFY command, causing a denial of service and possibly remote access.
CVE-1999-0232(PUBLISHED):Buffer overflow in NCSA WebServer (version 1.5c) gives remote access.
CVE-1999-0233(PUBLISHED):IIS 1.0 allows users to execute arbitrary commands using .bat or .cmd files.
CVE-1999-0234(PUBLISHED):Bash treats any character with a value of 255 as a command separator.
CVE-1999-0235(PUBLISHED):Buffer overflow in NCSA WebServer (1.4.1 and below) gives remote access.
CVE-1999-0236(PUBLISHED):ScriptAlias directory in NCSA and Apache httpd allowed attackers to read CGI programs.
CVE-1999-0237(PUBLISHED):Remote execution of arbitrary commands through Guestbook CGI program.
CVE-1999-0238(PUBLISHED):php.cgi allows attackers to read any file on the system.
CVE-1999-0239(PUBLISHED):Netscape FastTrack Web server lists files when a lowercase "get" command is used instead of an uppercase GET.
CVE-1999-0240(PUBLISHED):Some filters or firewalls allow fragmented SYN packets with IP reserved bits in violation of their implemented policy.
CVE-1999-0241(PUBLISHED):Guessable magic cookies in X Windows allows remote attackers to execute commands, e.g. through xterm.
CVE-1999-0242(PUBLISHED):Remote attackers can access mail files via POP3 in some Linux systems that are using shadow passwords.
CVE-1999-0243(PUBLISHED):Linux cfingerd could be exploited to gain root access.
CVE-1999-0244(PUBLISHED):Livingston RADIUS code has a buffer overflow which can allow remote execution of commands as root.
CVE-1999-0245(PUBLISHED):Some configurations of NIS+ in Linux allowed attackers to log in as the user "+".
CVE-1999-0246(PUBLISHED):HP Remote Watch allows a remote user to gain root access.
CVE-1999-0247(PUBLISHED):Buffer overflow in nnrpd program in INN up to version 1.6 allows remote users to execute arbitrary commands.
CVE-1999-0248(PUBLISHED):A race condition in the authentication agent mechanism of sshd 1.2.17 allows an attacker to steal another user's credentials.
CVE-1999-0249(PUBLISHED):Windows NT RSHSVC program allows remote users to execute arbitrary commands.
CVE-1999-0250(PUBLISHED):Denial of service in Qmail through long SMTP commands.
CVE-1999-0251(PUBLISHED):Denial of service in talk program allows remote attackers to disrupt a user's display.
CVE-1999-0252(PUBLISHED):Buffer overflow in listserv allows arbitrary command execution.
CVE-1999-0253(PUBLISHED):IIS 3.0 with the iis-fix hotfix installed allows remote intruders to read source code for ASP programs by using a %2e instead of a . (dot) in the URL.
CVE-1999-0254(PUBLISHED):A hidden SNMP community string in HP OpenView allows remote attackers to modify MIB tables and obtain sensitive information.
CVE-1999-0255(PUBLISHED):Buffer overflow in ircd allows arbitrary command execution.
CVE-1999-0256(PUBLISHED):Buffer overflow in War FTP allows remote execution of commands.
CVE-1999-0257(PUBLISHED):Nestea variation of teardrop IP fragmentation denial of service.
CVE-1999-0258(PUBLISHED):Bonk variation of teardrop IP fragmentation denial of service.
CVE-1999-0259(PUBLISHED):cfingerd lists all users on a system via search.**@target.
CVE-1999-0260(PUBLISHED):The jj CGI program allows command execution via shell metacharacters.
CVE-1999-0261(PUBLISHED):Netmanager Chameleon SMTPd has several buffer overflows that cause a crash.
CVE-1999-0262(PUBLISHED):Hylafax faxsurvey CGI script on Linux allows remote attackers to execute arbitrary commands via shell metacharacters in the query string.
CVE-1999-0263(PUBLISHED):Solaris SUNWadmap can be exploited to obtain root access.
CVE-1999-0264(PUBLISHED):htmlscript CGI program allows remote read access to files.
CVE-1999-0265(PUBLISHED):ICMP redirect messages may crash or lock up a host.
CVE-1999-0266(PUBLISHED):The info2www CGI script allows remote file access or remote command execution.
CVE-1999-0267(PUBLISHED):Buffer overflow in NCSA HTTP daemon v1.3 allows remote command execution.
CVE-1999-0268(PUBLISHED):MetaInfo MetaWeb web server allows users to upload, execute, and read scripts.
CVE-1999-0269(PUBLISHED):Netscape Enterprise servers may list files through the PageServices query.
CVE-1999-0270(PUBLISHED):Directory traversal vulnerability in pfdispaly.cgi program (sometimes referred to as "pfdisplay") for SGI's Performer API Search Tool (performer_tools) allows remote attackers to read arbitrary files.
CVE-1999-0271(PUBLISHED):Progressive Networks Real Video server (pnserver) can be crashed remotely.
CVE-1999-0272(PUBLISHED):Denial of service in Slmail v2.5 through the POP3 port.
CVE-1999-0273(PUBLISHED):Denial of service through Solaris 2.5.1 telnet by sending ^D characters.
CVE-1999-0274(PUBLISHED):Denial of service in Windows NT DNS servers through malicious packet which contains a response to a query that wasn't made.
CVE-1999-0275(PUBLISHED):Denial of service in Windows NT DNS servers by flooding port 53 with too many characters.
CVE-1999-0276(PUBLISHED):mSQL v2.0.1 and below allows remote execution through a buffer overflow.
CVE-1999-0277(PUBLISHED):The WorkMan program can be used to overwrite any file to get root access.
CVE-1999-0278(PUBLISHED):In IIS, remote attackers can obtain source code for ASP files by appending "::$DATA" to the URL.
CVE-1999-0279(PUBLISHED):Excite for Web Servers (EWS) allows remote command execution via shell metacharacters.
CVE-1999-0280(PUBLISHED):Remote command execution in Microsoft Internet Explorer using .lnk and .url files.
CVE-1999-0281(PUBLISHED):Denial of service in IIS using long URLs.
CVE-1999-0283(PUBLISHED):The Java Web Server would allow remote users to obtain the source code for CGI programs.
CVE-1999-0284(PUBLISHED):Denial of service to NT mail servers including Ipswitch, Mdaemon, and Exchange through a buffer overflow in the SMTP HELO command.
CVE-1999-0285(PUBLISHED):Denial of service in telnet from the Windows NT Resource Kit, by opening then immediately closing a connection.
CVE-1999-0286(PUBLISHED):In some NT web servers, appending a space at the end of a URL may allow attackers to read source code for active pages.
CVE-1999-0287(PUBLISHED):Vulnerability in the Wguest CGI program.
CVE-1999-0288(PUBLISHED):The WINS server in Microsoft Windows NT 4.0 before SP4 allows remote attackers to cause a denial of service (process termination) via invalid UDP frames to port 137 (NETBIOS Name Service), as demonstrated via a flood of random packets.
CVE-1999-0289(PUBLISHED):The Apache web server for Win32 may provide access to restricted files when a . (dot) is appended to a requested URL.
CVE-1999-0290(PUBLISHED):The WinGate telnet proxy allows remote attackers to cause a denial of service via a large number of connections to localhost.
CVE-1999-0291(PUBLISHED):The WinGate proxy is installed without a password, which allows remote attackers to redirect connections without authentication.
CVE-1999-0292(PUBLISHED):Denial of service through Winpopup using large user names.
CVE-1999-0293(PUBLISHED):AAA authentication on Cisco systems allows attackers to execute commands without authorization.
CVE-1999-0294(PUBLISHED):All records in a WINS database can be deleted through SNMP for a denial of service.
CVE-1999-0295(PUBLISHED):Solaris sysdef command allows local users to read kernel memory, potentially leading to root privileges.
CVE-1999-0296(PUBLISHED):Solaris volrmmount program allows attackers to read any file.
CVE-1999-0297(PUBLISHED):Buffer overflow in Vixie Cron library up to version 3.0 allows local users to obtain root access via a long environmental variable.
CVE-1999-0298(PUBLISHED):ypbind with -ypset and -ypsetme options activated in Linux Slackware and SunOS allows local and remote attackers to overwrite files via a .. (dot dot) attack.
CVE-1999-0299(PUBLISHED):Buffer overflow in FreeBSD lpd through long DNS hostnames.
CVE-1999-0300(PUBLISHED):nis_cachemgr for Solaris NIS+ allows attackers to add malicious NIS+ servers.
CVE-1999-0301(PUBLISHED):Buffer overflow in SunOS/Solaris ps command.
CVE-1999-0302(PUBLISHED):SunOS/Solaris FTP clients can be forced to execute arbitrary commands from a malicious FTP server.
CVE-1999-0303(PUBLISHED):Buffer overflow in BNU UUCP daemon (uucpd) through long hostnames.
CVE-1999-0304(PUBLISHED):mmap function in BSD allows local attackers in the kmem group to modify memory through devices.
CVE-1999-0305(PUBLISHED):The system configuration control (sysctl) facility in BSD based operating systems OpenBSD 2.2 and earlier, and FreeBSD 2.2.5 and earlier, does not properly restrict source routed packets even when the (1) dosourceroute or (2) forwarding variables are set, which allows remote attackers to spoof TCP connections.
CVE-1999-0306(PUBLISHED):buffer overflow in HP xlock program.
CVE-1999-0307(PUBLISHED):Buffer overflow in HP-UX cstm program allows local users to gain root privileges.
CVE-1999-0308(PUBLISHED):HP-UX gwind program allows users to modify arbitrary files.
CVE-1999-0309(PUBLISHED):HP-UX vgdisplay program gives root access to local users.
CVE-1999-0310(PUBLISHED):SSH 1.2.25 on HP-UX allows access to new user accounts.
CVE-1999-0311(PUBLISHED):fpkg2swpk in HP-UX allows local users to gain root access.
CVE-1999-0312(PUBLISHED):HP ypbind allows attackers with root privileges to modify NIS data.
CVE-1999-0313(PUBLISHED):disk_bandwidth on SGI IRIX 6.4 S2MP for Origin/Onyx2 allows local users to gain root access using relative pathnames.
CVE-1999-0314(PUBLISHED):ioconfig on SGI IRIX 6.4 S2MP for Origin/Onyx2 allows local users to gain root access using relative pathnames.
CVE-1999-0315(PUBLISHED):Buffer overflow in Solaris fdformat command gives root access to local users.
CVE-1999-0316(PUBLISHED):Buffer overflow in Linux splitvt command gives root access to local users.
CVE-1999-0317(PUBLISHED):Buffer overflow in Linux su command gives root access to local users.
CVE-1999-0318(PUBLISHED):Buffer overflow in xmcd 2.0p12 allows local users to gain access through an environmental variable.
CVE-1999-0319(PUBLISHED):Buffer overflow in xmcd 2.1 allows local users to gain access through a user resource setting.
CVE-1999-0320(PUBLISHED):SunOS rpc.cmsd allows attackers to obtain root access by overwriting arbitrary files.
CVE-1999-0321(PUBLISHED):Buffer overflow in Solaris kcms_configure command allows local users to gain root access.
CVE-1999-0322(PUBLISHED):The open() function in FreeBSD allows local attackers to write to arbitrary files.
CVE-1999-0323(PUBLISHED):FreeBSD mmap function allows users to modify append-only or immutable files.
CVE-1999-0324(PUBLISHED):ppl program in HP-UX allows local users to create root files through symlinks.
CVE-1999-0325(PUBLISHED):vhe_u_mnt program in HP-UX allows local users to create root files through symlinks.
CVE-1999-0326(PUBLISHED):Vulnerability in HP-UX mediainit program.
CVE-1999-0327(PUBLISHED):SGI syserr program allows local users to corrupt files.
CVE-1999-0328(PUBLISHED):SGI permissions program allows local users to gain root privileges.
CVE-1999-0329(PUBLISHED):SGI mediad program allows local users to gain root access.
CVE-1999-0330(PUBLISHED):Linux bdash game has a buffer overflow that allows local users to gain root access.
CVE-1999-0331(PUBLISHED):Buffer overflow in Internet Explorer 4.0(1).
CVE-1999-0332(PUBLISHED):Buffer overflow in NetMeeting allows denial of service and remote command execution.
CVE-1999-0333(PUBLISHED):HP OpenView Omniback allows remote execution of commands as root via spoofing, and local users can gain root access via a symlink attack.
CVE-1999-0334(PUBLISHED):In Solaris 2.2 and 2.3, when fsck fails on startup, it allows a local user with physical access to obtain root access.
CVE-1999-0336(PUBLISHED):Buffer overflow in mstm in HP-UX allows local users to gain root access.
CVE-1999-0337(PUBLISHED):AIX batch queue (bsh) allows local and remote users to gain additional privileges when network printing is enabled.
CVE-1999-0338(PUBLISHED):AIX Licensed Program Product performance tools allow local users to gain root access.
CVE-1999-0339(PUBLISHED):Buffer overflow in the libauth library in Solaris allows local users to gain additional privileges, possibly root access.
CVE-1999-0340(PUBLISHED):Buffer overflow in Linux Slackware crond program allows local users to gain root access.
CVE-1999-0341(PUBLISHED):Buffer overflow in the Linux mail program "deliver" allows local users to gain root access.
CVE-1999-0342(PUBLISHED):Linux PAM modules allow local users to gain root access using temporary files.
CVE-1999-0343(PUBLISHED):A malicious Palace server can force a client to execute arbitrary programs.
CVE-1999-0344(PUBLISHED):NT users can gain debug-level access on a system process using the Sechole exploit.
CVE-1999-0345(PUBLISHED):Jolt ICMP attack causes a denial of service in Windows 95 and Windows NT systems.
CVE-1999-0346(PUBLISHED):CGI PHP mlog script allows an attacker to read any file on the target server.
CVE-1999-0347(PUBLISHED):Internet Explorer 4.01 allows remote attackers to read local files and spoof web pages via a "%01" character in an "about:" Javascript URL, which causes Internet Explorer to use the domain specified after the character.
CVE-1999-0348(PUBLISHED):IIS ASP caching problem releases sensitive information when two virtual servers share the same physical directory.
CVE-1999-0349(PUBLISHED):A buffer overflow in the FTP list (ls) command in IIS allows remote attackers to conduct a denial of service and, in some cases, execute arbitrary commands.
CVE-1999-0350(PUBLISHED):Race condition in the db_loader program in ClearCase gives local users root access by setting SUID bits.
CVE-1999-0351(PUBLISHED):FTP PASV "Pizza Thief" denial of service and unauthorized data access.  Attackers can steal data by connecting to a port that was intended for use by a client.
CVE-1999-0352(PUBLISHED):ControlIT 4.5 and earlier (aka Remotely Possible) has weak password encryption.
CVE-1999-0353(PUBLISHED):rpc.pcnfsd in HP gives remote root access by changing the permissions on the main printer spool directory.
CVE-1999-0354(PUBLISHED):Internet Explorer 4.x or 5.x with Word 97 allows arbitrary execution of Visual Basic programs to the IE client through the Word 97 template, which doesn't warn the user that the template contains executable content.  Also applies to Outlook when the client views a malicious email message.
CVE-1999-0355(PUBLISHED):Local or remote users can force ControlIT 4.5 to reboot or force a user to log out, resulting in a denial of service.
CVE-1999-0356(PUBLISHED):ControlIT v4.5 and earlier uses weak encryption to store usernames and passwords in an address book.
CVE-1999-0357(PUBLISHED):Windows 98 and other operating systems allows remote attackers to cause a denial of service via crafted "oshare" packets, possibly involving invalid fragmentation offsets.
CVE-1999-0358(PUBLISHED):Digital Unix 4.0 has a buffer overflow in the inc program of the mh package.
CVE-1999-0359(PUBLISHED):ptylogin in Unix systems allows users to perform a denial of service by locking out modems, dial out with that modem, or obtain passwords.
CVE-1999-0360(PUBLISHED):MS Site Server 2.0 with IIS 4 can allow users to upload content, including ASP, to the target web site, thus allowing them to execute commands remotely.
CVE-1999-0361(PUBLISHED):NetWare version of LaserFiche stores usernames and passwords unencrypted, and allows administrative changes without logging.
CVE-1999-0362(PUBLISHED):WS_FTP server remote denial of service through cwd command.
CVE-1999-0363(PUBLISHED):SuSE 5.2 PLP lpc program has a buffer overflow that leads to root compromise.
CVE-1999-0364(PUBLISHED):Microsoft Access 97 stores a database password as plaintext in a foreign mdb, allowing access to data.
CVE-1999-0365(PUBLISHED):The metamail package allows remote command execution using shell metacharacters that are not quoted in a mailcap entry.
CVE-1999-0366(PUBLISHED):In some cases, Service Pack 4 for Windows NT 4.0 can allow access to network shares using a blank password, through a problem with a null NT hash value.
CVE-1999-0367(PUBLISHED):NetBSD netstat command allows local users to access kernel memory.
CVE-1999-0368(PUBLISHED):Buffer overflows in wuarchive ftpd (wu-ftpd) and ProFTPD lead to remote root access, a.k.a. palmetto.
CVE-1999-0369(PUBLISHED):The Sun sdtcm_convert calendar utility for OpenWindows has a buffer overflow which can gain root access.
CVE-1999-0370(PUBLISHED):In Sun Solaris and SunOS, man and catman contain vulnerabilities that allow overwriting arbitrary files.
CVE-1999-0371(PUBLISHED):Lynx allows a local user to overwrite sensitive files through /tmp symlinks.
CVE-1999-0372(PUBLISHED):The installer for BackOffice Server includes account names and passwords in a setup file (reboot.ini) which is not deleted.
CVE-1999-0373(PUBLISHED):Buffer overflow in the "Super" utility in Debian GNU/Linux, and other operating systems, allows local users to execute commands as root.
CVE-1999-0374(PUBLISHED):Debian GNU/Linux cfengine package is susceptible to a symlink attack.
CVE-1999-0375(PUBLISHED):Buffer overflow in webd in Network Flight Recorder (NFR) 2.0.2-Research allows remote attackers to execute commands.
CVE-1999-0376(PUBLISHED):Local users in Windows NT can obtain administrator privileges by changing the KnownDLLs list to reference malicious programs.
CVE-1999-0377(PUBLISHED):Process table attack in Unix systems allows a remote attacker to perform a denial of service by filling a machine's process tables through multiple connections to network services.
CVE-1999-0378(PUBLISHED):InterScan VirusWall for Solaris doesn't scan files for viruses when a single HTTP request includes two GET commands.
CVE-1999-0379(PUBLISHED):Microsoft Taskpads allows remote web sites to execute commands on the visiting user's machine via certain methods that are marked as Safe for Scripting.
CVE-1999-0380(PUBLISHED):SLMail 3.1 and 3.2 allows local users to access any file in the NTFS file system when the Remote Administration Service (RAS) is enabled by setting a user's Finger File to point to the target file, then running finger on the user.
CVE-1999-0381(PUBLISHED):super 3.11.6 and other versions have a buffer overflow in the syslog utility which allows a local user to gain root access.
CVE-1999-0382(PUBLISHED):The screen saver in Windows NT does not verify that its security context has been changed properly, allowing attackers to run programs with elevated privileges.
CVE-1999-0383(PUBLISHED):ACC Tigris allows public access without a login.
CVE-1999-0384(PUBLISHED):The Forms 2.0 ActiveX control (included with Visual Basic for Applications 5.0) can be used to read text from a user's clipboard when the user accesses documents with ActiveX content.
CVE-1999-0385(PUBLISHED):The LDAP bind function in Exchange 5.5 has a buffer overflow that allows a remote attacker to conduct a denial of service or execute commands.
CVE-1999-0386(PUBLISHED):Microsoft Personal Web Server and FrontPage Personal Web Server in some Windows systems allows a remote attacker to read files on the server by using a nonstandard URL.
CVE-1999-0387(PUBLISHED):A legacy credential caching mechanism used in Windows 95 and Windows 98 systems allows attackers to read plaintext network passwords.
CVE-1999-0388(PUBLISHED):DataLynx suGuard trusts the PATH environment variable to execute the ps command, allowing local users to execute commands as root.
CVE-1999-0389(PUBLISHED):Buffer overflow in the bootp server in the Debian Linux netstd package.
CVE-1999-0390(PUBLISHED):Buffer overflow in Dosemu Slang library in Linux.
CVE-1999-0391(PUBLISHED):The cryptographic challenge of SMB authentication in Windows 95 and Windows 98 can be reused, allowing an attacker to replay the response and impersonate a user.
CVE-1999-0392(PUBLISHED):Buffer overflow in Thomas Boutell's cgic library version up to 1.05.
CVE-1999-0393(PUBLISHED):Remote attackers can cause a denial of service in Sendmail 8.8.x and 8.9.2 by sending messages with a large number of headers.
CVE-1999-0394(PUBLISHED):DPEC Online Courseware allows an attacker to change another user's password without knowing the original password.
CVE-1999-0395(PUBLISHED):A race condition in the BackWeb Polite Agent Protocol allows an attacker to spoof a BackWeb server.
CVE-1999-0396(PUBLISHED):A race condition between the select() and accept() calls in NetBSD TCP servers allows remote attackers to cause a denial of service.
CVE-1999-0397(PUBLISHED):The demo version of the Quakenbush NT Password Appraiser sends passwords across the network in plaintext.
CVE-1999-0398(PUBLISHED):In some instances of SSH 1.2.27 and 2.0.11 on Linux systems, SSH will allow users with expired accounts to login.
CVE-1999-0399(PUBLISHED):The DCC server command in the Mirc 5.5 client doesn't filter characters from file names properly, allowing remote attackers to place a malicious file in a different location, possibly allowing the attacker to execute commands.
CVE-1999-0400(PUBLISHED):Denial of service in Linux 2.2.0 running the ldd command on a core file.
CVE-1999-0401(PUBLISHED):A race condition in Linux 2.2.1 allows local users to read arbitrary memory from /proc files.
CVE-1999-0402(PUBLISHED):wget 1.5.3 follows symlinks to change permissions of the target file instead of the symlink itself.
CVE-1999-0403(PUBLISHED):A bug in Cyrix CPUs on Linux allows local users to perform a denial of service.
CVE-1999-0404(PUBLISHED):Buffer overflow in the Mail-Max SMTP server for Windows systems allows remote command execution.
CVE-1999-0405(PUBLISHED):A buffer overflow in lsof allows local users to obtain root privilege.
CVE-1999-0406(PUBLISHED):Digital Unix Networker program nsralist has a buffer overflow which allows local users to obtain root privilege.
CVE-1999-0407(PUBLISHED):By default, IIS 4.0 has a virtual directory /IISADMPWD which contains files that can be used as proxies for brute force password attacks, or to identify valid users on the system.
CVE-1999-0408(PUBLISHED):Files created from interactive shell sessions in Cobalt RaQ microservers (e.g. .bash_history) are world readable, and thus are accessible from the web server.
CVE-1999-0409(PUBLISHED):Buffer overflow in gnuplot in Linux version 3.5 allows local users to obtain root access.
CVE-1999-0410(PUBLISHED):The cancel command in Solaris 2.6 (i386) has a buffer overflow that allows local users to obtain root access.
CVE-1999-0411(PUBLISHED):Several startup scripts in SCO OpenServer Enterprise System v 5.0.4p, including S84rpcinit, S95nis, S85tcp, and S89nfs, are vulnerable to a symlink attack, allowing a local user to gain root access.
CVE-1999-0412(PUBLISHED):In IIS and other web servers, an attacker can attack commands as SYSTEM if the server is running as SYSTEM and loading an ISAPI extension.
CVE-1999-0413(PUBLISHED):A buffer overflow in the SGI X server allows local users to gain root access through the X server font path.
CVE-1999-0414(PUBLISHED):In Linux before version 2.0.36, remote attackers can spoof a TCP connection and pass data to the application layer before fully establishing the connection.
CVE-1999-0415(PUBLISHED):The HTTP server in Cisco 7xx series routers 3.2 through 4.2 is enabled by default, which allows remote attackers to change the router's configuration.
CVE-1999-0416(PUBLISHED):Vulnerability in Cisco 7xx series routers allows a remote attacker to cause a system reload via a TCP connection to the router's TELNET port.
CVE-1999-0417(PUBLISHED):64 bit Solaris 7 procfs allows local users to perform a denial of service.
CVE-1999-0418(PUBLISHED):Denial of service in SMTP applications such as Sendmail, when a remote attacker (e.g. spammer) uses many "RCPT TO" commands in the same connection.
CVE-1999-0419(PUBLISHED):When the Microsoft SMTP service attempts to send a message to a server and receives a 4xx error code, it quickly and repeatedly attempts to redeliver the message, causing a denial of service.
CVE-1999-0420(PUBLISHED):umapfs allows local users to gain root privileges by changing their uid through a malicious mount_umap program.
CVE-1999-0421(PUBLISHED):During a reboot after an installation of Linux Slackware 3.6, a remote attacker can obtain root access by logging in to the root account without a password.
CVE-1999-0422(PUBLISHED):In some cases, NetBSD 1.3.3 mount allows local users to execute programs in some file systems that have the "noexec" flag set.
CVE-1999-0423(PUBLISHED):Vulnerability in hpterm on HP-UX 10.20 allows local users to gain additional privileges.
CVE-1999-0424(PUBLISHED):talkback in Netscape 4.5 allows a local user to overwrite arbitrary files of another user whose Netscape crashes.
CVE-1999-0425(PUBLISHED):talkback in Netscape 4.5 allows a local user to kill an arbitrary process of another user whose Netscape crashes.
CVE-1999-0426(PUBLISHED):The default permissions of /dev/kmem in Linux versions before 2.0.36 allows IP spoofing.
CVE-1999-0427(PUBLISHED):Eudora 4.1 allows remote attackers to perform a denial of service by sending attachments with long file names.
CVE-1999-0428(PUBLISHED):OpenSSL and SSLeay allow remote attackers to reuse SSL sessions and bypass access controls.
CVE-1999-0429(PUBLISHED):The Lotus Notes 4.5 client may send a copy of encrypted mail in the clear across the network if the user does not set the "Encrypt Saved Mail" preference.
CVE-1999-0430(PUBLISHED):Cisco Catalyst LAN switches running Catalyst 5000 supervisor software allows remote attackers to perform a denial of service by forcing the supervisor module to reload.
CVE-1999-0431(PUBLISHED):Linux 2.2.3 and earlier allow a remote attacker to perform an IP fragmentation attack, causing a denial of service.
CVE-1999-0432(PUBLISHED):ftp on HP-UX 11.00 allows local users to gain privileges.
CVE-1999-0433(PUBLISHED):XFree86 startx command is vulnerable to a symlink attack, allowing local users to create files in restricted directories, possibly allowing them to gain privileges or cause a denial of service.
CVE-1999-0434(PUBLISHED):XFree86 xfs command is vulnerable to a symlink attack, allowing local users to create files in restricted directories, possibly allowing them to gain privileges or cause a denial of service.
CVE-1999-0435(PUBLISHED):MC/ServiceGuard and MC/LockManager in HP-UX allows local users to gain privileges through SAM.
CVE-1999-0436(PUBLISHED):Domain Enterprise Server Management System (DESMS) in HP-UX allows local users to gain privileges.
CVE-1999-0437(PUBLISHED):Remote attackers can perform a denial of service in WebRamp systems by sending a malicious string to the HTTP port.
CVE-1999-0438(PUBLISHED):Remote attackers can perform a denial of service in WebRamp systems by sending a malicious UDP packet to port 5353, changing its IP address.
CVE-1999-0439(PUBLISHED):Buffer overflow in procmail before version 3.12 allows remote or local attackers to execute commands via expansions in the procmailrc configuration file.
CVE-1999-0440(PUBLISHED):The byte code verifier component of the Java Virtual Machine (JVM) allows remote execution through malicious web pages.
CVE-1999-0441(PUBLISHED):Remote attackers can perform a denial of service in WinGate machines using a buffer overflow in the Winsock Redirector Service.
CVE-1999-0442(PUBLISHED):Solaris ff.core allows local users to modify files.
CVE-1999-0443(PUBLISHED):Patrol management software allows a remote attacker to conduct a replay attack to steal the administrator password.
CVE-1999-0444(PUBLISHED):Remote attackers can perform a denial of service in Windows machines using malicious ARP packets, forcing a message box display for each packet or filling up log files.
CVE-1999-0445(PUBLISHED):In Cisco routers under some versions of IOS 12.0 running NAT, some packets may not be filtered by input access list filters.
CVE-1999-0446(PUBLISHED):Local users can perform a denial of service in NetBSD 1.3.3 and earlier versions by creating an unusual symbolic link with the ln command, triggering a bug in VFS.
CVE-1999-0447(PUBLISHED):Local users can gain privileges using the debug utility in the MPE/iX operating system.
CVE-1999-0448(PUBLISHED):IIS 4.0 and Apache log HTTP request methods, regardless of how long they are, allowing a remote attacker to hide the URL they really request.
CVE-1999-0449(PUBLISHED):The ExAir sample site in IIS 4 allows remote attackers to cause a denial of service (CPU consumption) via a direct request to the (1) advsearch.asp, (2) query.asp, or (3) search.asp scripts.
CVE-1999-0450(PUBLISHED):In IIS, an attacker could determine a real path using a request for a non-existent URL that would be interpreted by Perl (perl.exe).
CVE-1999-0451(PUBLISHED):Denial of service in Linux 2.0.36 allows local users to prevent any server from listening on any non-privileged port.
CVE-1999-0452(PUBLISHED):A service or application has a backdoor password that was placed there by the developer.
CVE-1999-0453(PUBLISHED):An attacker can identify a CISCO device by sending a SYN packet to port 1999, which is for the Cisco Discovery Protocol (CDP).
CVE-1999-0454(PUBLISHED):A remote attacker can sometimes identify the operating system of a host based on how it reacts to some IP or ICMP packets, using a tool such as nmap or queso.
CVE-1999-0455(PUBLISHED):The Expression Evaluator sample application in ColdFusion allows remote attackers to read or delete files on the server via exprcalc.cfm, which does not restrict access to the server properly.
CVE-1999-0457(PUBLISHED):Linux ftpwatch program allows local users to gain root privileges.
CVE-1999-0458(PUBLISHED):L0phtcrack 2.5 used temporary files in the system TEMP directory which could contain password information.
CVE-1999-0459(PUBLISHED):Local users can perform a denial of service in Alpha Linux, using MILO to force a reboot.
CVE-1999-0460(PUBLISHED):Buffer overflow in Linux autofs module through long directory names allows local users to perform a denial of service.
CVE-1999-0461(PUBLISHED):Versions of rpcbind including Linux, IRIX, and Wietse Venema's rpcbind allow a remote attacker to insert and delete entries by spoofing a source address.
CVE-1999-0462(PUBLISHED):suidperl in Linux Perl does not check the nosuid mount option on file systems, allowing local users to gain root access by placing a setuid script in a mountable file system, e.g. a CD-ROM or floppy disk.
CVE-1999-0463(PUBLISHED):Remote attackers can perform a denial of service using IRIX fcagent.
CVE-1999-0464(PUBLISHED):Local users can perform a denial of service in Tripwire 1.2 and earlier using long filenames.
CVE-1999-0465(PUBLISHED):Remote attackers can crash Lynx and Internet Explorer using an IMG tag with a large width parameter.
CVE-1999-0466(PUBLISHED):The SVR4 /dev/wabi special device file in NetBSD 1.3.3 and earlier allows a local user to read or write arbitrary files on the disk associated with that device.
CVE-1999-0467(PUBLISHED):The Webcom CGI Guestbook programs wguest.exe and rguest.exe allow a remote attacker to read arbitrary files using the "template" parameter.
CVE-1999-0468(PUBLISHED):Internet Explorer 5.0 allows a remote server to read arbitrary files on the client's file system using the Microsoft Scriptlet Component.
CVE-1999-0469(PUBLISHED):Internet Explorer 5.0 allows window spoofing, allowing a remote attacker to spoof a legitimate web site and capture information from the client.
CVE-1999-0470(PUBLISHED):A weak encryption algorithm is used for passwords in Novell Remote.NLM, allowing them to be easily decrypted.
CVE-1999-0471(PUBLISHED):The remote proxy server in Winroute allows a remote attacker to reconfigure the proxy without authentication through the "cancel" button.
CVE-1999-0472(PUBLISHED):The SNMP default community name "public" is not properly removed in NetApps C630 Netcache, even if the administrator tries to disable it.
CVE-1999-0473(PUBLISHED):The rsync command before rsync 2.3.1 may inadvertently change the permissions of the client's working directory to the permissions of the directory being transferred.
CVE-1999-0474(PUBLISHED):The ICQ Webserver allows remote attackers to use .. to access arbitrary files outside of the user's personal directory.
CVE-1999-0475(PUBLISHED):A race condition in how procmail handles .procmailrc files allows a local user to read arbitrary files available to the user who is running procmail.
CVE-1999-0476(PUBLISHED):A weak encryption algorithm is used for passwords in SCO TermVision, allowing them to be easily decrypted by a local user.
CVE-1999-0477(PUBLISHED):The Expression Evaluator in the ColdFusion Application Server allows a remote attacker to upload files to the server via openfile.cfm, which does not restrict access to the server properly.
CVE-1999-0478(PUBLISHED):Denial of service in HP-UX sendmail 8.8.6 related to accepting connections.
CVE-1999-0479(PUBLISHED):Denial of service Netscape Enterprise Server with VirtualVault on HP-UX VVOS systems.
CVE-1999-0480(PUBLISHED):Local attackers can conduct a denial of service in Midnight Commander 4.x with a symlink attack.
CVE-1999-0481(PUBLISHED):Denial of service in "poll" in OpenBSD.
CVE-1999-0482(PUBLISHED):OpenBSD kernel crash through TSS handling, as caused by the crashme program.
CVE-1999-0483(PUBLISHED):OpenBSD crash using nlink value in FFS and EXT2FS filesystems.
CVE-1999-0484(PUBLISHED):Buffer overflow in OpenBSD ping.
CVE-1999-0485(PUBLISHED):Remote attackers can cause a system crash through ipintr() in ipq in OpenBSD.
CVE-1999-0486(PUBLISHED):Denial of service in AOL Instant Messenger when a remote attacker sends a malicious hyperlink to the receiving client, potentially causing a system crash.
CVE-1999-0487(PUBLISHED):The DHTML Edit ActiveX control in Internet Explorer allows remote attackers to read arbitrary files.
CVE-1999-0488(PUBLISHED):Internet Explorer 4.0 and 5.0 allows a remote attacker to execute security scripts in a different security context using malicious URLs, a variant of the "cross frame" vulnerability.
CVE-1999-0489(PUBLISHED):MSHTML.DLL in Internet Explorer 5.0 allows a remote attacker to paste a file name into the file upload intrinsic control, a variant of "untrusted scripted paste" as described in MS:MS98-013.
CVE-1999-0490(PUBLISHED):MSHTML.DLL in Internet Explorer 5.0 allows a remote attacker to learn information about a local user's files via an IMG SRC tag.
CVE-1999-0491(PUBLISHED):The prompt parsing in bash allows a local user to execute commands as another user by creating a directory with the name of the command to execute.
CVE-1999-0492(PUBLISHED):The ffingerd 1.19 allows remote attackers to identify users on the target system based on its responses.
CVE-1999-0493(PUBLISHED):rpc.statd allows remote attackers to forward RPC calls to the local operating system via the SM_MON and SM_NOTIFY commands, which in turn could be used to remotely exploit other bugs such as in automountd.
CVE-1999-0494(PUBLISHED):Denial of service in WinGate proxy through a buffer overflow in POP3.
CVE-1999-0495(PUBLISHED):A remote attacker can gain access to a file system using ..  (dot dot) when accessing SMB shares.
CVE-1999-0496(PUBLISHED):A Windows NT 4.0 user can gain administrative rights by forcing NtOpenProcessToken to succeed regardless of the user's permissions, aka GetAdmin.
CVE-1999-0497(PUBLISHED):Anonymous FTP is enabled.
CVE-1999-0498(PUBLISHED):TFTP is not running in a restricted directory, allowing a remote attacker to access sensitive information such as password files.
CVE-1999-0499(PUBLISHED):NETBIOS share information may be published through SNMP registry keys in NT.
CVE-1999-0501(PUBLISHED):A Unix account has a guessable password.
CVE-1999-0502(PUBLISHED):A Unix account has a default, null, blank, or missing password.
CVE-1999-0503(PUBLISHED):A Windows NT local user or administrator account has a guessable password.
CVE-1999-0504(PUBLISHED):A Windows NT local user or administrator account has a default, null, blank, or missing password.
CVE-1999-0505(PUBLISHED):A Windows NT domain user or administrator account has a guessable password.
CVE-1999-0506(PUBLISHED):A Windows NT domain user or administrator account has a default, null, blank, or missing password.
CVE-1999-0507(PUBLISHED):An account on a router, firewall, or other network device has a guessable password.
CVE-1999-0508(PUBLISHED):An account on a router, firewall, or other network device has a default, null, blank, or missing password.
CVE-1999-0509(PUBLISHED):Perl, sh, csh, or other shell interpreters are installed in the cgi-bin directory on a WWW site, which allows remote attackers to execute arbitrary commands.
CVE-1999-0510(PUBLISHED):A router or firewall allows source routed packets from arbitrary hosts.
CVE-1999-0511(PUBLISHED):IP forwarding is enabled on a machine which is not a router or firewall.
CVE-1999-0512(PUBLISHED):A mail server is explicitly configured to allow SMTP mail relay, which allows abuse by spammers.
CVE-1999-0513(PUBLISHED):ICMP messages to broadcast addresses are allowed, allowing for a Smurf attack that can cause a denial of service.
CVE-1999-0514(PUBLISHED):UDP messages to broadcast addresses are allowed, allowing for a Fraggle attack that can cause a denial of service by flooding the target.
CVE-1999-0515(PUBLISHED):An unrestricted remote trust relationship for Unix systems has been set up, e.g. by using a + sign in /etc/hosts.equiv.
CVE-1999-0516(PUBLISHED):An SNMP community name is guessable.
CVE-1999-0517(PUBLISHED):An SNMP community name is the default (e.g. public), null, or missing.
CVE-1999-0518(PUBLISHED):A NETBIOS/SMB share password is guessable.
CVE-1999-0519(PUBLISHED):A NETBIOS/SMB share password is the default, null, or missing.
CVE-1999-0520(PUBLISHED):A system-critical NETBIOS/SMB share has inappropriate access control.
CVE-1999-0521(PUBLISHED):An NIS domain name is easily guessable.
CVE-1999-0522(PUBLISHED):The permissions for a system-critical NIS+ table (e.g. passwd) are inappropriate.
CVE-1999-0523(PUBLISHED):ICMP echo (ping) is allowed from arbitrary hosts.
CVE-1999-0524(PUBLISHED):ICMP information such as (1) netmask and (2) timestamp is allowed from arbitrary hosts.
CVE-1999-0525(PUBLISHED):IP traceroute is allowed from arbitrary hosts.
CVE-1999-0526(PUBLISHED):An X server's access control is disabled (e.g. through an "xhost +" command) and allows anyone to connect to the server.
CVE-1999-0527(PUBLISHED):The permissions for system-critical data in an anonymous FTP account are inappropriate.  For example, the root directory is writeable by world, a real password file is obtainable, or executable commands such as "ls" can be overwritten.
CVE-1999-0528(PUBLISHED):A router or firewall forwards external packets that claim to come from inside the network that the router/firewall is in front of.
CVE-1999-0529(PUBLISHED):A router or firewall forwards packets that claim to come from IANA reserved or private addresses, e.g. 10.x.x.x, 127.x.x.x, 217.x.x.x, etc.
CVE-1999-0530(PUBLISHED):A system is operating in "promiscuous" mode which allows it to perform packet sniffing.
CVE-1999-0532(PUBLISHED):A DNS server allows zone transfers.
CVE-1999-0533(PUBLISHED):A DNS server allows inverse queries.
CVE-1999-0534(PUBLISHED):A Windows NT user has inappropriate rights or privileges, e.g. Act as System, Add Workstation, Backup, Change System Time, Create Pagefile, Create Permanent Object, Create Token Name, Debug, Generate Security Audit, Increase Priority, Increase Quota, Load Driver, Lock Memory, Profile Single Process, Remote Shutdown, Replace Process Token, Restore, System Environment, Take Ownership, or Unsolicited Input.
CVE-1999-0535(PUBLISHED):A Windows NT account policy for passwords has inappropriate, security-critical settings, e.g. for password length, password age, or uniqueness.
CVE-1999-0537(PUBLISHED):A configuration in a web browser such as Internet Explorer or Netscape Navigator allows execution of active content such as ActiveX, Java, Javascript, etc.
CVE-1999-0539(PUBLISHED):A trust relationship exists between two Unix hosts.
CVE-1999-0541(PUBLISHED):A password for accessing a WWW URL is guessable.
CVE-1999-0546(PUBLISHED):The Windows NT guest account is enabled.
CVE-1999-0547(PUBLISHED):An SSH server allows authentication through the .rhosts file.
CVE-1999-0548(PUBLISHED):A superfluous NFS server is running, but it is not importing or exporting any file systems.
CVE-1999-0549(PUBLISHED):Windows NT automatically logs in an administrator upon rebooting.
CVE-1999-0550(PUBLISHED):A router's routing tables can be obtained from arbitrary hosts.
CVE-1999-0551(PUBLISHED):HP OpenMail can be misconfigured to allow users to run arbitrary commands using malicious print requests.
CVE-1999-0554(PUBLISHED):NFS exports system-critical data to the world, e.g. / or a password file.
CVE-1999-0555(PUBLISHED):A Unix account with a name other than "root" has UID 0, i.e. root privileges.
CVE-1999-0556(PUBLISHED):Two or more Unix accounts have the same UID.
CVE-1999-0559(PUBLISHED):A system-critical Unix file or directory has inappropriate permissions.
CVE-1999-0560(PUBLISHED):A system-critical Windows NT file or directory has inappropriate permissions.
CVE-1999-0561(PUBLISHED):IIS has the #exec function enabled for Server Side Include (SSI) files.
CVE-1999-0562(PUBLISHED):The registry in Windows NT can be accessed remotely by users who are not administrators.
CVE-1999-0564(PUBLISHED):An attacker can force a printer to print arbitrary documents (e.g. if the printer doesn't require a password) or to become disabled.
CVE-1999-0565(PUBLISHED):A Sendmail alias allows input to be piped to a program.
CVE-1999-0566(PUBLISHED):An attacker can write to syslog files from any location, causing a denial of service by filling up the logs, and hiding activities.
CVE-1999-0568(PUBLISHED):rpc.admind in Solaris is not running in a secure mode.
CVE-1999-0569(PUBLISHED):A URL for a WWW directory allows auto-indexing, which provides a list of all files in that directory if it does not contain an index.html file.
CVE-1999-0570(PUBLISHED):Windows NT is not using a password filter utility, e.g. PASSFILT.DLL.
CVE-1999-0571(PUBLISHED):A router's configuration service or management interface (such as a web server or telnet) is configured to allow connections from arbitrary hosts.
CVE-1999-0572(PUBLISHED):.reg files are associated with the Windows NT registry editor (regedit), making the registry susceptible to Trojan Horse attacks.
CVE-1999-0575(PUBLISHED):A Windows NT system's user audit policy does not log an event success or failure, e.g. for Logon and Logoff, File and Object Access, Use of User Rights, User and Group Management, Security Policy Changes, Restart, Shutdown, and System, and Process Tracking.
CVE-1999-0576(PUBLISHED):A Windows NT system's file audit policy does not log an event success or failure for security-critical files or directories.
CVE-1999-0577(PUBLISHED):A Windows NT system's file audit policy does not log an event success or failure for non-critical files or directories.
CVE-1999-0578(PUBLISHED):A Windows NT system's registry audit policy does not log an event success or failure for security-critical registry keys.
CVE-1999-0579(PUBLISHED):A Windows NT system's registry audit policy does not log an event success or failure for non-critical registry keys.
CVE-1999-0580(PUBLISHED):The HKEY_LOCAL_MACHINE key in a Windows NT system has inappropriate, system-critical permissions.
CVE-1999-0581(PUBLISHED):The HKEY_CLASSES_ROOT key in a Windows NT system has inappropriate, system-critical permissions.
CVE-1999-0582(PUBLISHED):A Windows NT account policy has inappropriate, security-critical settings for lockout, e.g. lockout duration, lockout after bad logon attempts, etc.
CVE-1999-0583(PUBLISHED):There is a one-way or two-way trust relationship between Windows NT domains.
CVE-1999-0584(PUBLISHED):A Windows NT file system is not NTFS.
CVE-1999-0585(PUBLISHED):A Windows NT administrator account has the default name of Administrator.
CVE-1999-0586(PUBLISHED):A network service is running on a nonstandard port.
CVE-1999-0587(PUBLISHED):A WWW server is not running in a restricted file system, e.g. through a chroot, thus allowing access to system-critical data.
CVE-1999-0588(PUBLISHED):A filter in a router or firewall allows unusual fragmented packets.
CVE-1999-0589(PUBLISHED):A system-critical Windows NT registry key has inappropriate permissions.
CVE-1999-0590(PUBLISHED):A system does not present an appropriate legal message or warning to a user who is accessing it.
CVE-1999-0591(PUBLISHED):An event log in Windows NT has inappropriate access permissions.
CVE-1999-0592(PUBLISHED):The Logon box of a Windows NT system displays the name of the last user who logged in.
CVE-1999-0593(PUBLISHED):The default setting for the Winlogon key entry ShutdownWithoutLogon in Windows NT allows users with physical access to shut down a Windows NT system without logging in.
CVE-1999-0594(PUBLISHED):A Windows NT system does not restrict access to removable media drives such as a floppy disk drive or CDROM drive.
CVE-1999-0595(PUBLISHED):A Windows NT system does not clear the system page file during shutdown, which might allow sensitive information to be recorded.
CVE-1999-0596(PUBLISHED):A Windows NT log file has an inappropriate maximum size or retention period.
CVE-1999-0597(PUBLISHED):A Windows NT account policy does not forcibly disconnect remote users from the server when their logon hours expire.
CVE-1999-0598(PUBLISHED):A network intrusion detection system (IDS) does not properly handle packets that are sent out of order, allowing an attacker to escape detection.
CVE-1999-0599(PUBLISHED):A network intrusion detection system (IDS) does not properly handle packets with improper sequence numbers.
CVE-1999-0600(PUBLISHED):A network intrusion detection system (IDS) does not verify the checksum on a packet.
CVE-1999-0601(PUBLISHED):A network intrusion detection system (IDS) does not properly handle data within TCP handshake packets.
CVE-1999-0602(PUBLISHED):A network intrusion detection system (IDS) does not properly reassemble fragmented packets.
CVE-1999-0603(PUBLISHED):In Windows NT, an inappropriate user is a member of a group, e.g. Administrator, Backup Operators, Domain Admins, Domain Guests, Power Users, Print Operators, Replicators, System Operators, etc.
CVE-1999-0604(PUBLISHED):An incorrect configuration of the WebStore 1.0 shopping cart CGI program "web_store.cgi" could disclose private information.
CVE-1999-0605(PUBLISHED):An incorrect configuration of the Order Form 1.0 shopping cart  CGI program could disclose private information.
CVE-1999-0606(PUBLISHED):An incorrect configuration of the EZMall 2000 shopping cart  CGI program "mall2000.cgi" could disclose private information.
CVE-1999-0607(PUBLISHED):quikstore.cgi in QuikStore shopping cart stores quikstore.cfg under the web document root with insufficient access control, which allows remote attackers to obtain the cleartext administrator password and gain privileges.
CVE-1999-0608(PUBLISHED):An incorrect configuration of the PDG Shopping Cart CGI program "shopper.cgi" could disclose private information.
CVE-1999-0609(PUBLISHED):An incorrect configuration of the SoftCart CGI program "SoftCart.exe" could disclose private information.
CVE-1999-0610(PUBLISHED):An incorrect configuration of the Webcart CGI program could disclose private information.
CVE-1999-0611(PUBLISHED):A system-critical Windows NT registry key has an inappropriate value.
CVE-1999-0612(PUBLISHED):A version of finger is running that exposes valid user information to any entity on the network.
CVE-1999-0613(PUBLISHED):The rpc.sprayd service is running.
CVE-1999-0618(PUBLISHED):The rexec service is running.
CVE-1999-0624(PUBLISHED):The rstat/rstatd service is running.
CVE-1999-0625(PUBLISHED):The rpc.rquotad service is running.
CVE-1999-0626(PUBLISHED):A version of rusers is running that exposes valid user information to any entity on the network.
CVE-1999-0627(PUBLISHED):The rexd service is running, which uses weak authentication that can allow an attacker to execute commands.
CVE-1999-0628(PUBLISHED):The rwho/rwhod service is running, which exposes machine status and user information.
CVE-1999-0629(PUBLISHED):The ident/identd service is running.
CVE-1999-0630(PUBLISHED):The NT Alerter and Messenger services are running.
CVE-1999-0632(PUBLISHED):The RPC portmapper service is running.
CVE-1999-0635(PUBLISHED):The echo service is running.
CVE-1999-0636(PUBLISHED):The discard service is running.
CVE-1999-0637(PUBLISHED):The systat service is running.
CVE-1999-0638(PUBLISHED):The daytime service is running.
CVE-1999-0639(PUBLISHED):The chargen service is running.
CVE-1999-0640(PUBLISHED):The Gopher service is running.
CVE-1999-0641(PUBLISHED):The UUCP service is running.
CVE-1999-0650(PUBLISHED):The netstat service is running, which provides sensitive information to remote attackers.
CVE-1999-0651(PUBLISHED):The rsh/rlogin service is running.
CVE-1999-0653(PUBLISHED):A component service related to NIS+ is running.
CVE-1999-0654(PUBLISHED):The OS/2 or POSIX subsystem in NT is enabled.
CVE-1999-0656(PUBLISHED):The ugidd RPC interface, by design, allows remote attackers to enumerate valid usernames by specifying arbitrary UIDs that ugidd maps to local user and group names.
CVE-1999-0657(PUBLISHED):WinGate is being used.
CVE-1999-0661(PUBLISHED):A system is running a version of software that was replaced with a Trojan Horse at one of its distribution points, such as (1) TCP Wrappers 7.6, (2) util-linux 2.9g, (3) wuarchive ftpd (wuftpd) 2.2 and 2.1f, (4) IRC client (ircII) ircII 2.2.9, (5) OpenSSH 3.4p1, or (6) Sendmail 8.12.6.
CVE-1999-0662(PUBLISHED):A system-critical program or library does not have the appropriate patch, hotfix, or service pack installed, or is outdated or obsolete.
CVE-1999-0663(PUBLISHED):A system-critical program, library, or file has a checksum or other integrity measurement that indicates that it has been modified.
CVE-1999-0664(PUBLISHED):An application-critical Windows NT registry key has inappropriate permissions.
CVE-1999-0665(PUBLISHED):An application-critical Windows NT registry key has an inappropriate value.
CVE-1999-0667(PUBLISHED):The ARP protocol allows any host to spoof ARP replies and poison the ARP cache to conduct IP address spoofing or a denial of service.
CVE-1999-0668(PUBLISHED):The scriptlet.typelib ActiveX control is marked as "safe for scripting" for Internet Explorer, which allows a remote attacker to execute arbitrary commands as demonstrated by Bubbleboy.
CVE-1999-0669(PUBLISHED):The Eyedog ActiveX control is marked as "safe for scripting" for Internet Explorer, which allows a remote attacker to execute arbitrary commands as demonstrated by Bubbleboy.
CVE-1999-0670(PUBLISHED):Buffer overflow in the Eyedog ActiveX control allows a remote attacker to execute arbitrary commands.
CVE-1999-0671(PUBLISHED):Buffer overflow in ToxSoft NextFTP client through CWD command.
CVE-1999-0672(PUBLISHED):Buffer overflow in Fujitsu Chocoa IRC client via IRC channel topics.
CVE-1999-0673(PUBLISHED):Buffer overflow in ALMail32 POP3 client via From: or To: headers.
CVE-1999-0674(PUBLISHED):The BSD profil system call allows a local user to modify the internal data space of a program via profiling and execve.
CVE-1999-0675(PUBLISHED):Check Point FireWall-1 can be subjected to a denial of service via UDP packets that are sent through VPN-1 to port 0 of a host.
CVE-1999-0676(PUBLISHED):sdtcm_convert in Solaris 2.6 allows a local user to overwrite sensitive files via a symlink attack.
CVE-1999-0677(PUBLISHED):The WebRamp web administration utility has a default password.
CVE-1999-0678(PUBLISHED):A default configuration of Apache on Debian GNU/Linux sets the ServerRoot to /usr/doc, which allows remote users to read documentation files for the entire server.
CVE-1999-0679(PUBLISHED):Buffer overflow in hybrid-6 IRC server commonly used on EFnet allows remote attackers to execute commands via m_invite invite option.
CVE-1999-0680(PUBLISHED):Windows NT Terminal Server performs extra work when a client opens a new connection but before it is authenticated, allowing for a denial of service.
CVE-1999-0681(PUBLISHED):Buffer overflow in Microsoft FrontPage Server Extensions (PWS) 3.0.2.926 on Windows 95, and possibly other versions, allows remote attackers to cause a denial of service via a long URL.
CVE-1999-0682(PUBLISHED):Microsoft Exchange 5.5 allows a remote attacker to relay email (i.e. spam) using encapsulated SMTP addresses, even if the anti-relaying features are enabled.
CVE-1999-0683(PUBLISHED):Denial of service in Gauntlet Firewall via a malformed ICMP packet.
CVE-1999-0684(PUBLISHED):Denial of service in Sendmail 8.8.6 in HPUX.
CVE-1999-0685(PUBLISHED):Buffer overflow in Netscape Communicator via EMBED tags in the pluginspage option.
CVE-1999-0686(PUBLISHED):Denial of service in Netscape Enterprise Server (NES) in HP Virtual Vault (VVOS) via a long URL.
CVE-1999-0687(PUBLISHED):The ToolTalk ttsession daemon uses weak RPC authentication, which allows a remote attacker to execute commands.
CVE-1999-0688(PUBLISHED):Buffer overflows in HP Software Distributor (SD) for HPUX 10.x and 11.x.
CVE-1999-0689(PUBLISHED):The CDE dtspcd daemon allows local users to execute arbitrary commands via a symlink attack.
CVE-1999-0690(PUBLISHED):HP CDE program includes the current directory in root's PATH variable.
CVE-1999-0691(PUBLISHED):Buffer overflow in the AddSuLog function of the CDE dtaction utility allows local users to gain root privileges via a long user name.
CVE-1999-0692(PUBLISHED):The default configuration of the Array Services daemon (arrayd) disables authentication, allowing remote users to gain root privileges.
CVE-1999-0693(PUBLISHED):Buffer overflow in TT_SESSION environment variable in ToolTalk shared library allows local users to gain root privileges.
CVE-1999-0694(PUBLISHED):Denial of service in AIX ptrace system call allows local users to crash the system.
CVE-1999-0695(PUBLISHED):The Sybase PowerDynamo personal web server allows attackers to read arbitrary files through a .. (dot dot) attack.
CVE-1999-0696(PUBLISHED):Buffer overflow in CDE Calendar Manager Service Daemon (rpc.cmsd).
CVE-1999-0697(PUBLISHED):SCO Doctor allows local users to gain root privileges through a Tools option.
CVE-1999-0698(PUBLISHED):Denial of service in IP protocol logger (ippl) on Red Hat and Debian Linux.
CVE-1999-0699(PUBLISHED):The Bluestone Sapphire web server allows session hijacking via easily guessable session IDs.
CVE-1999-0700(PUBLISHED):Buffer overflow in Microsoft Phone Dialer (dialer.exe), via a malformed dialer entry in the dialer.ini file.
CVE-1999-0701(PUBLISHED):After an unattended installation of Windows NT 4.0, an installation file could include sensitive information such as the local Administrator password.
CVE-1999-0702(PUBLISHED):Internet Explorer 5.0 and 5.01 allows remote attackers to modify or execute files via the Import/Export Favorites feature, aka the "ImportExportFavorites" vulnerability.
CVE-1999-0703(PUBLISHED):OpenBSD, BSDI, and other Unix operating systems allow users to set chflags and fchflags on character and block devices.
CVE-1999-0704(PUBLISHED):Buffer overflow in Berkeley automounter daemon (amd) logging facility provided in the Linux am-utils package and others.
CVE-1999-0705(PUBLISHED):Buffer overflow in INN inews program.
CVE-1999-0706(PUBLISHED):Linux xmonisdn package allows local users to gain root privileges by modifying the IFS or PATH environmental variables.
CVE-1999-0707(PUBLISHED):The default FTP configuration in HP Visualize Conference allows conference users to send a file to other participants without authorization.
CVE-1999-0708(PUBLISHED):Buffer overflow in cfingerd allows local users to gain root privileges via a long GECOS field.
CVE-1999-0710(PUBLISHED):The Squid package in Red Hat Linux 5.2 and 6.0, and other distributions, installs cachemgr.cgi in a public web directory, which allows remote attackers to use it as an intermediary to connect to other systems.
CVE-1999-0711(PUBLISHED):The oratclsh interpreter in Oracle 8.x Intelligent Agent for Unix allows local users to execute Tcl commands as root.
CVE-1999-0712(PUBLISHED):A vulnerability in Caldera Open Administration System (COAS) allows the /etc/shadow password file to be made world-readable.
CVE-1999-0713(PUBLISHED):The dtlogin program in Compaq Tru64 UNIX allows local users to gain root privileges.
CVE-1999-0714(PUBLISHED):Vulnerability in Compaq Tru64 UNIX edauth command.
CVE-1999-0715(PUBLISHED):Buffer overflow in Remote Access Service (RAS) client allows an attacker to execute commands or cause a denial of service via a malformed phonebook entry.
CVE-1999-0716(PUBLISHED):Buffer overflow in Windows NT 4.0 help file utility via a malformed help file.
CVE-1999-0717(PUBLISHED):A remote attacker can disable the virus warning mechanism in Microsoft Excel 97.
CVE-1999-0718(PUBLISHED):IBM GINA, when used for OS/2 domain authentication of Windows NT users, allows local users to gain administrator privileges by changing the GroupMapping registry key.
CVE-1999-0719(PUBLISHED):The Guile plugin for the Gnumeric spreadsheet package allows attackers to execute arbitrary code.
CVE-1999-0720(PUBLISHED):The pt_chown command in Linux allows local users to modify TTY terminal devices that belong to other users.
CVE-1999-0721(PUBLISHED):Denial of service in Windows NT Local Security Authority (LSA) through a malformed LSA request.
CVE-1999-0722(PUBLISHED):The default configuration of Cobalt RaQ2 servers allows remote users to install arbitrary software packages.
CVE-1999-0723(PUBLISHED):The Windows NT Client Server Runtime Subsystem (CSRSS) can be subjected to a denial of service when all worker threads are waiting for user input.
CVE-1999-0724(PUBLISHED):Buffer overflow in OpenBSD procfs and fdescfs file systems via uio_offset in the readdir() function.
CVE-1999-0725(PUBLISHED):When IIS is run with a default language of Chinese, Korean, or Japanese, it allows a remote attacker to view the source code of certain files, a.k.a. "Double Byte Code Page".
CVE-1999-0726(PUBLISHED):An attacker can conduct a denial of service in Windows NT by executing a program with a malformed file image header.
CVE-1999-0727(PUBLISHED):A kernel leak in the OpenBSD kernel allows IPsec packets to be sent unencrypted.
CVE-1999-0728(PUBLISHED):A Windows NT user can disable the keyboard or mouse by directly calling the IOCTLs which control them.
CVE-1999-0729(PUBLISHED):Buffer overflow in Lotus Notes LDAP (NLDAP) allows an attacker to conduct a denial of service through the ldap_search request.
CVE-1999-0730(PUBLISHED):The zsoelim program in the Debian man-db package allows local users to overwrite files via a symlink attack.
CVE-1999-0731(PUBLISHED):The KDE klock program allows local users to unlock a session using malformed input.
CVE-1999-0732(PUBLISHED):The logging facility of the Debian smtp-refuser package allows local users to delete arbitrary files using symbolic links.
CVE-1999-0733(PUBLISHED):Buffer overflow in VMWare 1.0.1 for Linux via a long HOME environmental variable.
CVE-1999-0734(PUBLISHED):A default configuration of CiscoSecure Access Control Server (ACS) allows remote users to modify the server database without authentication.
CVE-1999-0735(PUBLISHED):KDE K-Mail allows local users to gain privileges via a symlink attack in temporary user directories.
CVE-1999-0736(PUBLISHED):The showcode.asp sample file in IIS and Site Server allows remote attackers to read arbitrary files.
CVE-1999-0737(PUBLISHED):The viewcode.asp sample file in IIS and Site Server allows remote attackers to read arbitrary files.
CVE-1999-0738(PUBLISHED):The code.asp sample file in IIS and Site Server allows remote attackers to read arbitrary files.
CVE-1999-0739(PUBLISHED):The codebrws.asp sample file in IIS and Site Server allows remote attackers to read arbitrary files.
CVE-1999-0740(PUBLISHED):Remote attackers can cause a denial of service on Linux in.telnetd telnet daemon through a malformed TERM environmental variable.
CVE-1999-0741(PUBLISHED):QMS CrownNet Unix Utilities for 2060 allows root to log on without a password.
CVE-1999-0742(PUBLISHED):The Debian mailman package uses weak authentication, which allows attackers to gain privileges.
CVE-1999-0743(PUBLISHED):Trn allows local users to overwrite other users' files via symlinks.
CVE-1999-0744(PUBLISHED):Buffer overflow in Netscape Enterprise Server and FastTrask Server allows remote attackers to gain privileges via a long HTTP GET request.
CVE-1999-0745(PUBLISHED):Buffer overflow in Source Code Browser Program Database Name Server Daemon (pdnsd) for the IBM AIX C Set ++ compiler.
CVE-1999-0746(PUBLISHED):A default configuration of in.identd in SuSE Linux waits 120 seconds between requests, allowing a remote attacker to conduct a denial of service.
CVE-1999-0747(PUBLISHED):Denial of service in BSDi Symmetric Multiprocessing (SMP) when an fstat call is made when the system has a high CPU load.
CVE-1999-0748(PUBLISHED):Buffer overflows in Red Hat net-tools package.
CVE-1999-0749(PUBLISHED):Buffer overflow in Microsoft Telnet client in Windows 95 and Windows 98 via a malformed Telnet argument.
CVE-1999-0750(PUBLISHED):Hotmail allows Javascript to be executed via the HTML STYLE tag, allowing remote attackers to execute commands on the user's Hotmail account.
CVE-1999-0751(PUBLISHED):Buffer overflow in Accept command in Netscape Enterprise Server 3.6 with the SSL Handshake Patch.
CVE-1999-0752(PUBLISHED):Denial of service in Netscape Enterprise Server via a buffer overflow in the SSL handshake.
CVE-1999-0753(PUBLISHED):The w3-msql CGI script provided with Mini SQL allows remote attackers to view restricted directories.
CVE-1999-0754(PUBLISHED):The INN inndstart program allows local users to gain privileges by specifying an alternate configuration file using the INNCONF environmental variable.
CVE-1999-0755(PUBLISHED):Windows NT RRAS and RAS clients cache a user's password even if the user has not selected the "Save password" option.
CVE-1999-0756(PUBLISHED):ColdFusion Administrator with Advanced Security enabled allows remote users to stop the ColdFusion server via the Start/Stop utility.
CVE-1999-0757(PUBLISHED):The ColdFusion CFCRYPT program for encrypting CFML templates has weak encryption, allowing attackers to decrypt the templates.
CVE-1999-0758(PUBLISHED):Netscape Enterprise 3.5.1 and FastTrack 3.01 servers allow a remote attacker to view source code to scripts by appending a %20 to the script's URL.
CVE-1999-0759(PUBLISHED):Buffer overflow in FuseMAIL POP service via long USER and PASS commands.
CVE-1999-0760(PUBLISHED):Undocumented ColdFusion Markup Language (CFML) tags and functions in the ColdFusion Administrator allow users to gain additional privileges.
CVE-1999-0761(PUBLISHED):Buffer overflow in FreeBSD fts library routines allows local user to modify arbitrary files via the periodic program.
CVE-1999-0762(PUBLISHED):When Javascript is embedded within the TITLE tag, Netscape Communicator allows a remote attacker to use the "about" protocol to gain access to browser information.
CVE-1999-0763(PUBLISHED):NetBSD on a multi-homed host allows ARP packets on one network to modify ARP entries on another connected network.
CVE-1999-0764(PUBLISHED):NetBSD allows ARP packets to overwrite static ARP entries.
CVE-1999-0765(PUBLISHED):SGI IRIX midikeys program allows local users to modify arbitrary files via a text editor.
CVE-1999-0766(PUBLISHED):The Microsoft Java Virtual Machine allows a malicious Java applet to execute arbitrary commands outside of the sandbox environment.
CVE-1999-0767(PUBLISHED):Buffer overflow in Solaris libc, ufsrestore, and rcp via LC_MESSAGES environmental variable.
CVE-1999-0768(PUBLISHED):Buffer overflow in Vixie Cron on Red Hat systems via the MAILTO environmental variable.
CVE-1999-0769(PUBLISHED):Vixie Cron on Linux systems allows local users to set parameters of sendmail commands via the MAILTO environmental variable.
CVE-1999-0770(PUBLISHED):Firewall-1 sets a long timeout for connections that begin with ACK or other packets except SYN, allowing an attacker to conduct a denial of service via a large number of connection attempts to unresponsive systems.
CVE-1999-0771(PUBLISHED):The web components of Compaq Management Agents and the Compaq Survey Utility allow a remote attacker to read arbitrary files via a .. (dot dot) attack.
CVE-1999-0772(PUBLISHED):Denial of service in Compaq Management Agents and the Compaq Survey Utility via a long string sent to port 2301.
CVE-1999-0773(PUBLISHED):Buffer overflow in Solaris lpset program allows local users to gain root access.
CVE-1999-0774(PUBLISHED):Buffer overflows in Mars NetWare Emulation (NWE, mars_nwe) package via long directory names.
CVE-1999-0775(PUBLISHED):Cisco Gigabit Switch routers running IOS allow remote attackers to forward unauthorized packets due to improper handling of the "established" keyword in an access list.
CVE-1999-0776(PUBLISHED):Alibaba HTTP server allows remote attackers to read files via a .. (dot dot) attack.
CVE-1999-0777(PUBLISHED):IIS FTP servers may allow a remote attacker to read or delete files on the server, even if they have "No Access" permissions.
CVE-1999-0778(PUBLISHED):Buffer overflow in Xi Graphics Accelerated-X server allows local users to gain root access via a long display or query parameter.
CVE-1999-0779(PUBLISHED):Denial of service in HP-UX SharedX recserv program.
CVE-1999-0780(PUBLISHED):KDE klock allows local users to kill arbitrary processes by specifying an arbitrary PID in the .kss.pid file.
CVE-1999-0781(PUBLISHED):KDE allows local users to execute arbitrary commands by setting the KDEDIR environmental variable to modify the search path that KDE uses to locate its executables.
CVE-1999-0782(PUBLISHED):KDE kppp allows local users to create a directory in an arbitrary location via the HOME environmental variable.
CVE-1999-0783(PUBLISHED):FreeBSD allows local users to conduct a denial of service by creating a hard link from a device special file to a file on an NFS file system.
CVE-1999-0784(PUBLISHED):Denial of service in Oracle TNSLSNR SQL*Net Listener via a malformed string to the listener port, aka NERP.
CVE-1999-0785(PUBLISHED):The INN inndstart program allows local users to gain root privileges via the "pathrun" parameter in the inn.conf file.
CVE-1999-0786(PUBLISHED):The dynamic linker in Solaris allows a local user to create arbitrary files via the LD_PROFILE environmental variable and a symlink attack.
CVE-1999-0787(PUBLISHED):The SSH authentication agent follows symlinks via a UNIX domain socket.
CVE-1999-0788(PUBLISHED):Arkiea nlservd allows remote attackers to conduct a denial of service.
CVE-1999-0789(PUBLISHED):Buffer overflow in AIX ftpd in the libc library.
CVE-1999-0790(PUBLISHED):A remote attacker can read information from a Netscape user's cache via JavaScript.
CVE-1999-0791(PUBLISHED):Hybrid Network cable modems do not include an authentication mechanism for administration, allowing remote attackers to compromise the system through the HSMP protocol.
CVE-1999-0792(PUBLISHED):ROUTERmate has a default SNMP community name which allows remote attackers to modify its configuration.
CVE-1999-0793(PUBLISHED):Internet Explorer allows remote attackers to read files by redirecting data to a Javascript applet.
CVE-1999-0794(PUBLISHED):Microsoft Excel does not warn a user when a macro is present in a Symbolic Link (SYLK) format file.
CVE-1999-0795(PUBLISHED):The NIS+ rpc.nisd server allows remote attackers to execute certain RPC calls without authentication to obtain system information, disable logging, or modify caches.
CVE-1999-0796(PUBLISHED):FreeBSD T/TCP Extensions for Transactions can be subjected to spoofing attacks.
CVE-1999-0797(PUBLISHED):NIS finger allows an attacker to conduct a denial of service via a large number of finger requests, resulting in a large number of NIS queries.
CVE-1999-0798(PUBLISHED):Buffer overflow in bootpd on OpenBSD, FreeBSD, and Linux systems via a malformed header type.
CVE-1999-0799(PUBLISHED):Buffer overflow in bootpd 2.4.3 and earlier via a long boot file location.
CVE-1999-0800(PUBLISHED):The GetFile.cfm file in Allaire Forums allows remote attackers to read files through a parameter to GetFile.cfm.
CVE-1999-0801(PUBLISHED):BMC Patrol allows remote attackers to gain access to an agent by spoofing frames.
CVE-1999-0802(PUBLISHED):Buffer overflow in Internet Explorer 5 allows remote attackers to execute commands via a malformed Favorites icon.
CVE-1999-0803(PUBLISHED):The fwluser script in AIX eNetwork Firewall allows local users to write to arbitrary files via a symlink attack.
CVE-1999-0804(PUBLISHED):Denial of service in Linux 2.2.x kernels via malformed ICMP packets containing unusual types, codes, and IP header lengths.
CVE-1999-0805(PUBLISHED):Novell NetWare Transaction Tracking System (TTS) in Novell 4.11 and earlier allows remote attackers to cause a denial of service via a large number of requests.
CVE-1999-0806(PUBLISHED):Buffer overflow in Solaris dtprintinfo program.
CVE-1999-0807(PUBLISHED):The Netscape Directory Server installation procedure leaves sensitive information in a file that is accessible to local users.
CVE-1999-0808(PUBLISHED):Multiple buffer overflows in ISC DHCP Distribution server (dhcpd) 1.0 and 2.0 allow a remote attacker to cause a denial of service (crash) and possibly execute arbitrary commands via long options.
CVE-1999-0809(PUBLISHED):Netscape Communicator 4.x with Javascript enabled does not warn a user of cookie settings, even if they have selected the option to "Only accept cookies originating from the same server as the page being viewed".
CVE-1999-0810(PUBLISHED):Denial of service in Samba NETBIOS name service daemon (nmbd).
CVE-1999-0811(PUBLISHED):Buffer overflow in Samba smbd program via a malformed message command.
CVE-1999-0812(PUBLISHED):Race condition in Samba smbmnt allows local users to mount file systems in arbitrary locations.
CVE-1999-0813(PUBLISHED):Cfingerd with ALLOW_EXECUTION enabled does not properly drop privileges when it executes a program on behalf of the user, allowing local users to gain root privileges.
CVE-1999-0814(PUBLISHED):Red Hat pump DHCP client allows remote attackers to gain root access in some configurations.
CVE-1999-0815(PUBLISHED):Memory leak in SNMP agent in Windows NT 4.0 before SP5 allows remote attackers to conduct a denial of service (memory exhaustion) via a large number of queries.
CVE-1999-0816(PUBLISHED):The Motorola CableRouter allows any remote user to connect to and configure the router on port 1024.
CVE-1999-0817(PUBLISHED):Lynx WWW client allows a remote attacker to specify command-line parameters which Lynx uses when calling external programs to handle certain protocols, e.g. telnet.
CVE-1999-0818(PUBLISHED):Buffer overflow in Solaris kcms_configure via a long NETPATH environmental variable.
CVE-1999-0819(PUBLISHED):NTMail does not disable the VRFY command, even if the administrator has explicitly disabled it.
CVE-1999-0820(PUBLISHED):FreeBSD seyon allows users to gain privileges via a modified PATH variable for finding the xterm and seyon-emu commands.
CVE-1999-0821(PUBLISHED):FreeBSD seyon allows local users to gain privileges by providing a malicious program in the -emulator argument.
CVE-1999-0822(PUBLISHED):Buffer overflow in Qpopper (qpop) 3.0 allows remote root access via AUTH command.
CVE-1999-0823(PUBLISHED):Buffer overflow in FreeBSD xmindpath allows local users to gain privileges via -f argument.
CVE-1999-0824(PUBLISHED):A Windows NT user can use SUBST to map a drive letter to a folder, which is not unmapped after the user logs off, potentially allowing that user to modify the location of folders accessed by later users.
CVE-1999-0825(PUBLISHED):The default permissions for UnixWare /var/mail allow local users to read and modify other users' mail.
CVE-1999-0826(PUBLISHED):Buffer overflow in FreeBSD angband allows local users to gain privileges.
CVE-1999-0827(PUBLISHED):By default, Internet Explorer 5.0 and other versions enables the "Navigate sub-frames across different domains" option, which allows frame spoofing.
CVE-1999-0828(PUBLISHED):UnixWare pkg commands such as pkginfo, pkgcat, and pkgparam allow local users to read arbitrary files via the dacread permission.
CVE-1999-0829(PUBLISHED):HP Secure Web Console uses weak encryption.
CVE-1999-0830(PUBLISHED):Buffer overflow in SCO UnixWare Xsco command via a long argument.
CVE-1999-0831(PUBLISHED):Denial of service in Linux syslogd via a large number of connections.
CVE-1999-0832(PUBLISHED):Buffer overflow in NFS server on Linux allows attackers to execute commands via a long pathname.
CVE-1999-0833(PUBLISHED):Buffer overflow in BIND 8.2 via NXT records.
CVE-1999-0834(PUBLISHED):Buffer overflow in RSAREF2 via the encryption and decryption functions in the RSAREF library.
CVE-1999-0835(PUBLISHED):Denial of service in BIND named via malformed SIG records.
CVE-1999-0836(PUBLISHED):UnixWare uidadmin allows local users to modify arbitrary files via a symlink attack.
CVE-1999-0837(PUBLISHED):Denial of service in BIND by improperly closing TCP sessions via so_linger.
CVE-1999-0838(PUBLISHED):Buffer overflow in Serv-U FTP 2.5 allows remote users to conduct a denial of service via the SITE command.
CVE-1999-0839(PUBLISHED):Windows NT Task Scheduler installed with Internet Explorer 5 allows a user to gain privileges by modifying the job after it has been scheduled.
CVE-1999-0840(PUBLISHED):Buffer overflow in CDE dtmail and dtmailpr programs allows local users to gain privileges via a long -f option.
CVE-1999-0841(PUBLISHED):Buffer overflow in CDE mailtool allows local users to gain root privileges via a long MIME Content-Type.
CVE-1999-0842(PUBLISHED):Symantec Mail-Gear 1.0 web interface server allows remote users to read arbitrary files via a .. (dot dot) attack.
CVE-1999-0843(PUBLISHED):Denial of service in Cisco routers running NAT via a PORT command from an FTP client to a Telnet port.
CVE-1999-0844(PUBLISHED):Denial of service in MDaemon WorldClient and WebConfig services via a long URL.
CVE-1999-0845(PUBLISHED):Buffer overflow in SCO su program allows local users to gain root access via a long username.
CVE-1999-0846(PUBLISHED):Denial of service in MDaemon 2.7 via a large number of connection attempts.
CVE-1999-0847(PUBLISHED):Buffer overflow in free internet chess server (FICS) program, xboard.
CVE-1999-0848(PUBLISHED):Denial of service in BIND named via consuming more than "fdmax" file descriptors.
CVE-1999-0849(PUBLISHED):Denial of service in BIND named via maxdname.
CVE-1999-0850(PUBLISHED):The default permissions for Endymion MailMan allow local users to read email or modify files.
CVE-1999-0851(PUBLISHED):Denial of service in BIND named via naptr.
CVE-1999-0852(PUBLISHED):IBM WebSphere sets permissions that allow a local user to modify a deinstallation script or its data files stored in /usr/bin.
CVE-1999-0853(PUBLISHED):Buffer overflow in Netscape Enterprise Server and Netscape FastTrack Server allows remote attackers to gain privileges via the HTTP Basic Authentication procedure.
CVE-1999-0854(PUBLISHED):Ultimate Bulletin Board stores data files in the cgi-bin directory, allowing remote attackers to view the data if an error occurs when the HTTP server attempts to execute the file.
CVE-1999-0855(PUBLISHED):Buffer overflow in FreeBSD gdc program.
CVE-1999-0856(PUBLISHED):login in Slackware 7.0 allows remote attackers to identify valid users on the system by reporting an encryption error when an account is locked or does not exist.
CVE-1999-0857(PUBLISHED):FreeBSD gdc program allows local users to modify files via a symlink attack.
CVE-1999-0858(PUBLISHED):Internet Explorer 5 allows a remote attacker to modify the IE client's proxy configuration via a malicious Web Proxy Auto-Discovery (WPAD) server.
CVE-1999-0859(PUBLISHED):Solaris arp allows local users to read files via the -f parameter, which lists lines in the file that do not parse properly.
CVE-1999-0860(PUBLISHED):Solaris chkperm allows local users to read files owned by bin via the VMSYS environmental variable and a symlink attack.
CVE-1999-0861(PUBLISHED):Race condition in the SSL ISAPI filter in IIS and other servers may leak information in plaintext.
CVE-1999-0862(PUBLISHED):Insecure directory permissions in RPM distribution for PostgreSQL allows local users to gain privileges by reading a plaintext password file.
CVE-1999-0863(PUBLISHED):Buffer overflow in FreeBSD seyon via HOME environmental variable, -emulator argument, -modems argument, or the GUI.
CVE-1999-0864(PUBLISHED):UnixWare programs that dump core allow a local user to modify files via a symlink attack on the ./core.pid file.
CVE-1999-0865(PUBLISHED):Buffer overflow in CommuniGatePro via a long string to the HTTP configuration port.
CVE-1999-0866(PUBLISHED):Buffer overflow in UnixWare xauto program allows local users to gain root privilege.
CVE-1999-0867(PUBLISHED):Denial of service in IIS 4.0 via a flood of HTTP requests with malformed headers.
CVE-1999-0868(PUBLISHED):ucbmail allows remote attackers to execute commands via shell metacharacters that are passed to it from INN.
CVE-1999-0869(PUBLISHED):Internet Explorer 3.x to 4.01 allows a remote attacker to insert malicious content into a frame of another web site, aka frame spoofing.
CVE-1999-0870(PUBLISHED):Internet Explorer 4.01 allows remote attackers to read arbitrary files by pasting a file name into the file upload control, aka untrusted scripted paste.
CVE-1999-0871(PUBLISHED):Internet Explorer 4.0 and 4.01 allow a remote attacker to read files via IE's cross frame security, aka the "Cross Frame Navigate" vulnerability.
CVE-1999-0872(PUBLISHED):Buffer overflow in Vixie cron allows local users to gain root access via a long MAILTO environment variable in a crontab file.
CVE-1999-0873(PUBLISHED):Buffer overflow in Skyfull mail server via MAIL FROM command.
CVE-1999-0874(PUBLISHED):Buffer overflow in IIS 4.0 allows remote attackers to cause a denial of service via a malformed request for files with .HTR, .IDC, or .STM extensions.
CVE-1999-0875(PUBLISHED):DHCP clients with ICMP Router Discovery Protocol (IRDP) enabled allow remote attackers to modify their default routes.
CVE-1999-0876(PUBLISHED):Buffer overflow in Internet Explorer 4.0 via EMBED tag.
CVE-1999-0877(PUBLISHED):Internet Explorer 5 allows remote attackers to read files via an ExecCommand method called on an IFRAME.
CVE-1999-0878(PUBLISHED):Buffer overflow in WU-FTPD and related FTP servers allows remote attackers to gain root privileges via MAPPING_CHDIR.
CVE-1999-0879(PUBLISHED):Buffer overflow in WU-FTPD and related FTP servers allows remote attackers to gain root privileges via macro variables in a message file.
CVE-1999-0880(PUBLISHED):Denial of service in WU-FTPD via the SITE NEWER command, which does not free memory properly.
CVE-1999-0881(PUBLISHED):Falcon web server allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-1999-0882(PUBLISHED):Falcon web server allows remote attackers to determine the absolute path of the web root via long file names.
CVE-1999-0883(PUBLISHED):Zeus web server allows remote attackers to read arbitrary files by specifying the file name in an option to the search engine.
CVE-1999-0884(PUBLISHED):The Zeus web server administrative interface uses weak encryption for its passwords.
CVE-1999-0885(PUBLISHED):Alibaba web server allows remote attackers to execute commands via a pipe character in a malformed URL.
CVE-1999-0886(PUBLISHED):The security descriptor for RASMAN allows users to point to an alternate location via the Windows NT Service Control Manager.
CVE-1999-0887(PUBLISHED):FTGate web interface server allows remote attackers to read files via a .. (dot dot) attack.
CVE-1999-0888(PUBLISHED):dbsnmp in Oracle Intelligent Agent allows local users to gain privileges by setting the ORACLE_HOME environmental variable, which dbsnmp uses to find the nmiconf.tcl script.
CVE-1999-0889(PUBLISHED):Cisco 675 routers running CBOS allow remote attackers to establish telnet sessions if an exec or superuser password has not been set.
CVE-1999-0890(PUBLISHED):iHTML Merchant allows remote attackers to obtain sensitive information or execute commands via a code parsing error.
CVE-1999-0891(PUBLISHED):The "download behavior" in Internet Explorer 5 allows remote attackers to read arbitrary files via a server-side redirect.
CVE-1999-0892(PUBLISHED):Buffer overflow in Netscape Communicator before 4.7 via a dynamic font whose length field is less than the size of the font.
CVE-1999-0893(PUBLISHED):userOsa in SCO OpenServer allows local users to corrupt files via a symlink attack.
CVE-1999-0894(PUBLISHED):Red Hat Linux screen program does not use Unix98 ptys, allowing local users to write to other terminals.
CVE-1999-0895(PUBLISHED):Firewall-1 does not properly restrict access to LDAP attributes.
CVE-1999-0896(PUBLISHED):Buffer overflow in RealNetworks RealServer administration utility allows remote attackers to execute arbitrary commands via a long username and password.
CVE-1999-0897(PUBLISHED):iChat ROOMS Webserver allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-1999-0898(PUBLISHED):Buffer overflows in Windows NT 4.0 print spooler allow remote attackers to gain privileges or cause a denial of service via a malformed spooler request.
CVE-1999-0899(PUBLISHED):The Windows NT 4.0 print spooler allows a local user to execute arbitrary commands due to inappropriate permissions that allow the user to specify an alternate print provider.
CVE-1999-0900(PUBLISHED):Buffer overflow in rpc.yppasswdd allows a local user to gain privileges via MD5 hash generation.
CVE-1999-0901(PUBLISHED):ypserv allows a local user to modify the GECOS and login shells of other users.
CVE-1999-0902(PUBLISHED):ypserv allows local administrators to modify password tables.
CVE-1999-0903(PUBLISHED):genfilt in the AIX Packet Filtering Module does not properly filter traffic to destination ports greater than 32767.
CVE-1999-0904(PUBLISHED):Buffer overflow in BFTelnet allows remote attackers to cause a denial of service via a long username.
CVE-1999-0905(PUBLISHED):Denial of service in Axent Raptor firewall via malformed zero-length IP options.
CVE-1999-0906(PUBLISHED):Buffer overflow in sccw allows local users to gain root access via the HOME environmental variable.
CVE-1999-0907(PUBLISHED):sccw allows local users to read arbitrary files.
CVE-1999-0908(PUBLISHED):Denial of service in Solaris TCP streams driver via a malicious connection that causes the server to panic as a result of recursive calls to mutex_enter.
CVE-1999-0909(PUBLISHED):Multihomed Windows systems allow a remote attacker to bypass IP source routing restrictions via a malformed packet with IP options, aka the "Spoofed Route Pointer" vulnerability.
CVE-1999-0910(PUBLISHED):Microsoft Site Server and Commercial Internet System (MCIS) do not set an expiration for a cookie, which could then be cached by a proxy and inadvertently used by a different user.
CVE-1999-0911(PUBLISHED):Buffer overflow in ProFTPD, wu-ftpd, and beroftpd allows remote attackers to gain root access via a series of MKD and CWD commands that create nested directories.
CVE-1999-0912(PUBLISHED):FreeBSD VFS cache (vfs_cache) allows local users to cause a denial of service by opening a large number of files.
CVE-1999-0913(PUBLISHED):dfire.cgi script in Dragon-Fire IDS allows remote users to execute commands via shell metacharacters.
CVE-1999-0914(PUBLISHED):Buffer overflow in the FTP client in the Debian GNU/Linux netstd package.
CVE-1999-0915(PUBLISHED):URL Live! web server allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-1999-0916(PUBLISHED):WebTrends software stores account names and passwords in a file which does not have restricted access permissions.
CVE-1999-0917(PUBLISHED):The Preloader ActiveX control used by Internet Explorer allows remote attackers to read arbitrary files.
CVE-1999-0918(PUBLISHED):Denial of service in various Windows systems via malformed, fragmented IGMP packets.
CVE-1999-0919(PUBLISHED):A memory leak in a Motorola CableRouter allows remote attackers to conduct a denial of service via a large number of telnet connections.
CVE-1999-0920(PUBLISHED):Buffer overflow in the pop-2d POP daemon in the IMAP package allows remote attackers to gain privileges via the FOLD command.
CVE-1999-0921(PUBLISHED):BMC Patrol allows any remote attacker to flood its UDP port, causing a denial of service.
CVE-1999-0922(PUBLISHED):An example application in ColdFusion Server 4.0 allows remote attackers to view source code via the sourcewindow.cfm file.
CVE-1999-0923(PUBLISHED):Sample runnable code snippets in ColdFusion Server 4.0 allow remote attackers to read files, conduct a denial of service, or use the server as a proxy for other HTTP calls.
CVE-1999-0924(PUBLISHED):The Syntax Checker in ColdFusion Server 4.0 allows remote attackers to conduct a denial of service.
CVE-1999-0925(PUBLISHED):UnityMail allows remote attackers to conduct a denial of service via a large number of MIME headers.
CVE-1999-0926(PUBLISHED):Apache allows remote attackers to conduct a denial of service via a large number of MIME headers.
CVE-1999-0927(PUBLISHED):NTMail allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-1999-0928(PUBLISHED):Buffer overflow in SmartDesk WebSuite allows remote attackers to cause a denial of service via a long URL.
CVE-1999-0929(PUBLISHED):Novell NetWare with Novell-HTTP-Server or YAWN web servers allows remote attackers to conduct a denial of service via a large number of HTTP GET requests.
CVE-1999-0930(PUBLISHED):wwwboard allows a remote attacker to delete message board articles via a malformed argument.
CVE-1999-0931(PUBLISHED):Buffer overflow in Mediahouse Statistics Server allows remote attackers to execute commands.
CVE-1999-0932(PUBLISHED):Mediahouse Statistics Server allows remote attackers to read the administrator password, which is stored in cleartext in the ss.cfg file.
CVE-1999-0933(PUBLISHED):TeamTrack web server allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-1999-0934(PUBLISHED):classifieds.cgi allows remote attackers to read arbitrary files via shell metacharacters.
CVE-1999-0935(PUBLISHED):classifieds.cgi allows remote attackers to execute arbitrary commands by specifying them in a hidden variable in a CGI form.
CVE-1999-0936(PUBLISHED):BNBSurvey survey.cgi program allows remote attackers to execute commands via shell metacharacters.
CVE-1999-0937(PUBLISHED):BNBForm allows remote attackers to read arbitrary files via the automessage hidden form variable.
CVE-1999-0938(PUBLISHED):MBone SDR Package allows remote attackers to execute commands via shell metacharacters in Session Initiation Protocol (SIP) messages.
CVE-1999-0939(PUBLISHED):Denial of service in Debian IRC Epic/epic4 client via a long string.
CVE-1999-0940(PUBLISHED):Buffer overflow in mutt mail client allows remote attackers to execute commands via malformed MIME messages.
CVE-1999-0941(PUBLISHED):Mutt mail client allows a remote attacker to execute commands via shell metacharacters.
CVE-1999-0942(PUBLISHED):UnixWare dos7utils allows a local user to gain root privileges by using the STATICMERGE environmental variable to find a script which it executes.
CVE-1999-0943(PUBLISHED):Buffer overflow in OpenLink 3.2 allows remote attackers to gain privileges via a long GET request to the web configurator.
CVE-1999-0944(PUBLISHED):IBM WebSphere ikeyman tool uses weak encryption to store a password for a key database that is used for SSL connections.
CVE-1999-0945(PUBLISHED):Buffer overflow in Internet Mail Service (IMS) for Microsoft Exchange 5.5 and 5.0 allows remote attackers to conduct a denial of service via AUTH or AUTHINFO commands.
CVE-1999-0946(PUBLISHED):Buffer overflow in Yamaha MidiPlug via a Text variable in an EMBED tag.
CVE-1999-0947(PUBLISHED):AN-HTTPd provides example CGI scripts test.bat, input.bat, input2.bat, and envout.bat, which allow remote attackers to execute commands via shell metacharacters.
CVE-1999-0948(PUBLISHED):Buffer overflow in uum program for Canna input system allows local users to gain root privileges.
CVE-1999-0949(PUBLISHED):Buffer overflow in canuum program for Canna input system allows local users to gain root privileges.
CVE-1999-0950(PUBLISHED):Buffer overflow in WFTPD FTP server allows remote attackers to gain root access via	a series of MKD and CWD commands that create nested directories.
CVE-1999-0951(PUBLISHED):Buffer overflow in OmniHTTPd CGI program imagemap.exe allows remote attackers to execute commands.
CVE-1999-0952(PUBLISHED):Buffer overflow in Solaris lpstat via class argument allows local users to gain root access.
CVE-1999-0953(PUBLISHED):WWWBoard stores encrypted passwords in a password file that is under the web root and thus accessible by remote attackers.
CVE-1999-0954(PUBLISHED):WWWBoard has a default username and default password.
CVE-1999-0955(PUBLISHED):Race condition in wu-ftpd and BSDI ftpd allows remote attackers to gain root access via the SITE EXEC command.
CVE-1999-0956(PUBLISHED):The NeXT NetInfo _writers property allows local users to gain root privileges or conduct a denial of service.
CVE-1999-0957(PUBLISHED):MajorCool mj_key_cache program allows local users to modify files via a symlink attack.
CVE-1999-0958(PUBLISHED):sudo 1.5.x allows local users to execute arbitrary commands via a .. (dot dot) attack.
CVE-1999-0959(PUBLISHED):IRIX startmidi program allows local users to modify arbitrary files via a symlink attack.
CVE-1999-0960(PUBLISHED):IRIX cdplayer allows local users to create directories in arbitrary locations via a command line option.
CVE-1999-0961(PUBLISHED):HPUX sysdiag allows local users to gain root privileges via a symlink attack during log file creation.
CVE-1999-0962(PUBLISHED):Buffer overflow in HPUX passwd command allows local users to gain root privileges via a command line option.
CVE-1999-0963(PUBLISHED):FreeBSD mount_union command allows local users to gain root privileges via a symlink attack.
CVE-1999-0964(PUBLISHED):Buffer overflow in FreeBSD setlocale in the libc module allows attackers to execute arbitrary code via a long PATH_LOCALE environment variable.
CVE-1999-0965(PUBLISHED):Race condition in xterm allows local users to modify arbitrary files via the logging option.
CVE-1999-0966(PUBLISHED):Buffer overflow in Solaris getopt in libc allows local users to gain root privileges via a long argv[0].
CVE-1999-0967(PUBLISHED):Buffer overflow in the HTML library used by Internet Explorer, Outlook Express, and Windows Explorer via the res: local resource protocol.
CVE-1999-0968(PUBLISHED):Buffer overflow in BNC IRC proxy allows remote attackers to gain privileges.
CVE-1999-0969(PUBLISHED):The Windows NT RPC service allows remote attackers to conduct a denial of service using spoofed malformed RPC packets which generate an error message that is sent to the spoofed host, potentially setting up a loop, aka Snork.
CVE-1999-0970(PUBLISHED):The OmniHTTPD visadmin.exe program allows a remote attacker to conduct a denial of service via a malformed URL which causes a large number of temporary files to be created.
CVE-1999-0971(PUBLISHED):Buffer overflow in Exim allows local users to gain root privileges via a long :include: option in a .forward file.
CVE-1999-0972(PUBLISHED):Buffer overflow in Xshipwars xsw program.
CVE-1999-0973(PUBLISHED):Buffer overflow in Solaris snoop program allows remote attackers to gain root privileges via a long domain name when snoop is running in verbose mode.
CVE-1999-0974(PUBLISHED):Buffer overflow in Solaris snoop allows remote attackers to gain root privileges via GETQUOTA requests to the rpc.rquotad service.
CVE-1999-0975(PUBLISHED):The Windows help system can allow a local user to execute commands as another user by editing a table of contents metafile with a .CNT extension and modifying the topic action to include the commands to be executed when the .hlp file is accessed.
CVE-1999-0976(PUBLISHED):Sendmail allows local users to reinitialize the aliases database via the newaliases command, then cause a denial of service by interrupting Sendmail.
CVE-1999-0977(PUBLISHED):Buffer overflow in Solaris sadmind allows remote attackers to gain root privileges using a NETMGT_PROC_SERVICE request.
CVE-1999-0978(PUBLISHED):htdig allows remote attackers to execute commands via filenames with shell metacharacters.
CVE-1999-0979(PUBLISHED):The SCO UnixWare privileged process system allows local users to gain root privileges by using a debugger such as gdb to insert traps into _init before the privileged process is executed.
CVE-1999-0980(PUBLISHED):Windows NT Service Control Manager (SCM) allows remote attackers to cause a denial of service via a malformed argument in a resource enumeration request.
CVE-1999-0981(PUBLISHED):Internet Explorer 5.01 and earlier allows a remote attacker to create a reference to a client window and use a server-side redirect to access local files via that window, aka "Server-side Page Reference Redirect."
CVE-1999-0982(PUBLISHED):The Sun Web-Based Enterprise Management (WBEM) installation script stores a password in plaintext in a world readable file.
CVE-1999-0983(PUBLISHED):Whois Internic Lookup program whois.cgi allows remote attackers to execute commands via shell metacharacters in the domain entry.
CVE-1999-0984(PUBLISHED):Matt's Whois program whois.cgi allows remote attackers to execute commands via shell metacharacters in the domain entry.
CVE-1999-0985(PUBLISHED):CC Whois program whois.cgi allows remote attackers to execute commands via shell metacharacters in the domain entry.
CVE-1999-0986(PUBLISHED):The ping command in Linux 2.0.3x allows local users to cause a denial of service by sending large packets with the -R (record route) option.
CVE-1999-0987(PUBLISHED):Windows NT does not properly download a system policy if the domain user logs into the domain with a space at the end of the domain name.
CVE-1999-0988(PUBLISHED):UnixWare pkgtrans allows local users to read arbitrary files via a symlink attack.
CVE-1999-0989(PUBLISHED):Buffer overflow in Internet Explorer 5 directshow filter (MSDXM.OCX) allows remote attackers to execute commands via the vnd.ms.radio protocol.
CVE-1999-0990(PUBLISHED):Error messages generated by gdm with the VerboseAuth setting allows an attacker to identify valid users on a system.
CVE-1999-0991(PUBLISHED):Buffer overflow in GoodTech Telnet Server NT allows remote users to cause a denial of service via a long login name.
CVE-1999-0992(PUBLISHED):HP VirtualVault with the PHSS_17692 patch allows unprivileged processes to bypass access restrictions via the Trusted Gateway Proxy (TGP).
CVE-1999-0993(PUBLISHED):Modifications to ACLs (Access Control Lists) in Microsoft Exchange  5.5 do not take effect until the directory store cache is refreshed.
CVE-1999-0994(PUBLISHED):Windows NT with SYSKEY reuses the keystream that is used for encrypting SAM password hashes, allowing an attacker to crack passwords.
CVE-1999-0995(PUBLISHED):Windows NT Local Security Authority (LSA) allows remote attackers to cause a denial of service via malformed arguments to the LsaLookupSids function which looks up the SID, aka "Malformed Security Identifier Request."
CVE-1999-0996(PUBLISHED):Buffer overflow in Infoseek Ultraseek search engine allows remote attackers to execute commands via a long GET request.
CVE-1999-0997(PUBLISHED):wu-ftp with FTP conversion enabled allows an attacker to execute commands via a malformed file name that is interpreted as an argument to the program that does the conversion, e.g. tar or uncompress.
CVE-1999-0998(PUBLISHED):Cisco Cache Engine allows an attacker to replace content in the cache.
CVE-1999-0999(PUBLISHED):Microsoft SQL 7.0 server allows a remote attacker to cause a denial of service via a malformed TDS packet.
CVE-1999-1000(PUBLISHED):The web administration interface for Cisco Cache Engine allows remote attackers to view performance statistics.
CVE-1999-1001(PUBLISHED):Cisco Cache Engine allows a remote attacker to gain access via a null username and password.
CVE-1999-1002(PUBLISHED):Netscape Navigator uses weak encryption for storing a user's Netscape mail password.
CVE-1999-1003(PUBLISHED):War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.
CVE-1999-1004(PUBLISHED):Buffer overflow in the POP server POProxy for the Norton Anti-Virus protection NAV2000 program via a large USER command.
CVE-1999-1005(PUBLISHED):Groupwise web server GWWEB.EXE allows remote attackers to read arbitrary files with .htm extensions via a .. (dot dot) attack using the HELP parameter.
CVE-1999-1006(PUBLISHED):Groupwise web server GWWEB.EXE allows remote attackers to determine the real path of the web server via the HELP parameter.
CVE-1999-1007(PUBLISHED):Buffer overflow in VDO Live Player allows remote attackers to execute commands on the VDO client via a malformed .vdo file.
CVE-1999-1008(PUBLISHED):xsoldier program allows local users to gain root access via a long argument.
CVE-1999-1009(PUBLISHED):The Disney Go Express Search allows remote attackers to access and modify search information for users by connecting to an HTTP server on the user's system.
CVE-1999-1010(PUBLISHED):An SSH 1.2.27 server allows a client to use the "none" cipher, even if it is not allowed by the server policy.
CVE-1999-1011(PUBLISHED):The Remote Data Service (RDS) DataFactory component of Microsoft Data Access Components (MDAC) in IIS 3.x and 4.x exposes unsafe methods, which allows remote attackers to execute arbitrary commands.
CVE-1999-1012(PUBLISHED):SMTP component of Lotus Domino 4.6.1 on AS/400, and possibly other operating systems, allows a remote attacker to crash the mail server via a long string.
CVE-1999-1013(PUBLISHED):named-xfer in AIX 4.1.5 and 4.2.1 allows members of the system group to overwrite system files to gain root access via the -f parameter and a malformed zone file.
CVE-1999-1014(PUBLISHED):Buffer overflow in mail command in Solaris 2.7 and 2.7 allows local users to gain privileges via a long -m argument.
CVE-1999-1015(PUBLISHED):Buffer overflow in Apple AppleShare Mail Server 5.0.3 on MacOS 8.1 and earlier allows a remote attacker to cause a denial of service (crash) via a long HELO command.
CVE-1999-1016(PUBLISHED):Microsoft HTML control as used in (1) Internet Explorer 5.0, (2) FrontPage Express, (3) Outlook Express 5, and (4) Eudora, and possibly others, allows remote malicious web site or HTML emails to cause a denial of service (100% CPU consumption) via large HTML form fields such as text inputs in a table cell.
CVE-1999-1017(PUBLISHED):Seattle Labs Emurl 2.0, and possibly earlier versions, stores e-mail attachments in a specific directory with scripting enabled, which allows a malicious ASP file attachment to execute when the recipient opens the message.
CVE-1999-1018(PUBLISHED):IPChains in Linux kernels 2.2.10 and earlier does not reassemble IP fragments before checking the header information, which allows a remote attacker to bypass the filtering rules using several fragments with 0 offsets.
CVE-1999-1019(PUBLISHED):SpectroSERVER in Cabletron Spectrum Enterprise Manager 5.0 installs a directory tree with insecure permissions, which allows local users to replace a privileged executable (processd) with a Trojan horse, facilitating a root or Administrator compromise.
CVE-1999-1020(PUBLISHED):The installation of Novell Netware NDS 5.99 provides an unauthenticated client with Read access for the tree, which allows remote attackers to access sensitive information such as users, groups, and readable objects via CX.EXE and NLIST.EXE.
CVE-1999-1021(PUBLISHED):NFS on SunOS 4.1 through 4.1.2 ignores the high order 16 bits in a 32 bit UID, which allows a local user to gain root access if the lower 16 bits are set to 0, as fixed by the NFS jumbo patch upgrade.
CVE-1999-1022(PUBLISHED):serial_ports administrative program in IRIX 4.x and 5.x trusts the user's PATH environmental variable to find and execute the ls program, which allows local users to gain root privileges via a Trojan horse ls program.
CVE-1999-1023(PUBLISHED):useradd in Solaris 7.0 does not properly interpret certain date formats as specified in the "-e" (expiration date) argument, which could allow users to login after their accounts have expired.
CVE-1999-1024(PUBLISHED):ip_print procedure in Tcpdump 3.4a allows remote attackers to cause a denial of service via a packet with a zero length header, which causes an infinite loop and core dump when tcpdump prints the packet.
CVE-1999-1025(PUBLISHED):CDE screen lock program (screenlock) on Solaris 2.6 does not properly lock an unprivileged user's console session when the host is an NIS+ client, which allows others with physical access to login with any string.
CVE-1999-1026(PUBLISHED):aspppd on Solaris 2.5 x86 allows local users to modify arbitrary files and gain root privileges via a symlink attack on the /tmp/.asppp.fifo file.
CVE-1999-1027(PUBLISHED):Solaris 2.6 HW3/98 installs admintool with world-writable permissions, which allows local users to gain privileges by replacing it with a Trojan horse program.
CVE-1999-1028(PUBLISHED):Symantec pcAnywhere 8.0 allows remote attackers to cause a denial of service (CPU utilization) via a large amount of data to port 5631.
CVE-1999-1029(PUBLISHED):SSH server (sshd2) before 2.0.12 does not properly record login attempts if the connection is closed before the maximum number of tries, allowing a remote attacker to guess the password without showing up in the audit logs.
CVE-1999-1030(PUBLISHED):counter.exe 2.70 allows a remote attacker to cause a denial of service (hang) via an HTTP request that ends in %0A (newline), which causes a malformed entry in the counter log that produces an access violation.
CVE-1999-1031(PUBLISHED):counter.exe 2.70 allows a remote attacker to cause a denial of service (hang) via a long argument.
CVE-1999-1032(PUBLISHED):Vulnerability in LAT/Telnet Gateway (lattelnet) on Ultrix 4.1 and 4.2 allows attackers to gain root privileges.
CVE-1999-1033(PUBLISHED):Microsoft Outlook Express before 4.72.3612.1700 allows a malicious user to send a message that contains a .., which can inadvertently cause Outlook to re-enter POP3 command mode and cause the POP3 session to hang.
CVE-1999-1034(PUBLISHED):Vulnerability in login in AT&T System V Release 4 allows local users to gain privileges.
CVE-1999-1035(PUBLISHED):IIS 3.0 and 4.0 on x86 and Alpha allows remote attackers to cause a denial of service (hang) via a malformed GET request, aka the IIS "GET" vulnerability.
CVE-1999-1036(PUBLISHED):COPS 1.04 allows local users to overwrite or create arbitrary files via a symlink attack on temporary files in (1) res_diff, (2) ca.src, and (3) mail.chk.
CVE-1999-1037(PUBLISHED):rex.satan in SATAN 1.1.1 allows local users to overwrite arbitrary files via a symlink attack on the /tmp/rex.$$ file.
CVE-1999-1038(PUBLISHED):Tiger 2.2.3 allows local users to overwrite arbitrary files via a symlink attack on various temporary files in Tiger's default working directory, as defined by the WORKDIR variable.
CVE-1999-1039(PUBLISHED):Vulnerability in (1) diskalign and (2) diskperf in IRIX 6.4 patches 2291 and 2848 allow a local user to create root-owned files leading to a root compromise.
CVE-1999-1040(PUBLISHED):Vulnerabilities in (1) ipxchk and (2) ipxlink in NetWare Client 1.0 on IRIX 6.3 and 6.4 allows local users to gain root access via a modified IFS environmental variable.
CVE-1999-1041(PUBLISHED):Buffer overflow in mscreen on SCO OpenServer 5.0 and SCO UNIX 3.2v4 allows a local user to gain root access via (1) a long TERM environmental variable and (2) a long entry in the .mscreenrc file.
CVE-1999-1042(PUBLISHED):Cisco Resource Manager (CRM) 1.0 and 1.1 creates world-readable log files and temporary files, which may expose sensitive information, to local users such as user IDs, passwords and SNMP community strings.
CVE-1999-1043(PUBLISHED):Microsoft Exchange Server 5.5 and 5.0 does not properly handle (1) malformed NNTP data, or (2) malformed SMTP data, which allows remote attackers to cause a denial of service (application error).
CVE-1999-1044(PUBLISHED):Vulnerability in Advanced File System Utility (advfs) in Digital UNIX 4.0 through 4.0d allows local users to gain privileges.
CVE-1999-1045(PUBLISHED):pnserver in RealServer 5.0 and earlier allows remote attackers to cause a denial of service by sending a short, malformed request.
CVE-1999-1046(PUBLISHED):Buffer overflow in IMonitor in IMail 5.0 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long string to port 8181.
CVE-1999-1047(PUBLISHED):When BSDI patches for Gauntlet 5.0 BSDI are installed in a particular order, Gauntlet allows remote attackers to bypass firewall access restrictions, and does not log the activities.
CVE-1999-1048(PUBLISHED):Buffer overflow in bash 2.0.0, 1.4.17, and other versions allows local attackers to gain privileges by creating an extremely large directory name, which is inserted into the password prompt via the \w option in the PS1 environmental variable when another user changes into that directory.
CVE-1999-1049(PUBLISHED):ARCserve NT agents use weak encryption (XOR) for passwords, which allows remote attackers to sniff the authentication request to port 6050 and decrypt the password.
CVE-1999-1050(PUBLISHED):Directory traversal vulnerability in Matt Wright FormHandler.cgi script allows remote attackers to read arbitrary files via (1) a .. (dot dot) in the reply_message_attach attachment parameter, or (2) by specifying the filename as a template.
CVE-1999-1051(PUBLISHED):Default configuration in Matt Wright FormHandler.cgi script allows arbitrary directories to be used for attachments, and only restricts access to the /etc/ directory, which allows remote attackers to read arbitrary files via the reply_message_attach attachment parameter.
CVE-1999-1052(PUBLISHED):Microsoft FrontPage stores form results in a default location in /_private/form_results.txt, which is world-readable and accessible in the document root, which allows remote attackers to read possibly sensitive information submitted by other users.
CVE-1999-1053(PUBLISHED):guestbook.pl cleanses user-inserted SSI commands by removing text between "<!--" and "-->" separators, which allows remote attackers to execute arbitrary commands when guestbook.pl is run on Apache 1.3.9 and possibly other versions, since Apache allows other closing sequences besides "-->".
CVE-1999-1054(PUBLISHED):The default configuration of FLEXlm license manager 6.0d, and possibly other versions, allows remote attackers to shut down the server via the lmdown command.
CVE-1999-1055(PUBLISHED):Microsoft Excel 97 does not warn the user before executing worksheet functions, which could allow attackers to execute arbitrary commands by using the CALL function to execute a malicious DLL, aka the Excel "CALL Vulnerability."
CVE-1999-1057(PUBLISHED):VMS 4.0 through 5.3 allows local users to gain privileges via the ANALYZE/PROCESS_DUMP dcl command.
CVE-1999-1058(PUBLISHED):Buffer overflow in Vermillion FTP Daemon VFTPD 1.23 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via several long CWD commands.
CVE-1999-1059(PUBLISHED):Vulnerability in rexec daemon (rexecd) in AT&T TCP/IP 4.0 for various SVR4 systems allows remote attackers to execute arbitrary commands.
CVE-1999-1060(PUBLISHED):Buffer overflow in Tetrix TetriNet daemon 1.13.16 allows remote attackers to cause a denial of service and possibly execute arbitrary commands by connecting to port 31457 from a host with a long DNS hostname.
CVE-1999-1061(PUBLISHED):HP Laserjet printers with JetDirect cards, when configured with TCP/IP, can be configured without a password, which allows remote attackers to connect to the printer and change its IP address or disable logging.
CVE-1999-1062(PUBLISHED):HP Laserjet printers with JetDirect cards, when configured with TCP/IP, allow remote attackers to bypass print filters by directly sending PostScript documents to TCP ports 9099 and 9100.
CVE-1999-1063(PUBLISHED):CDomain whois_raw.cgi whois CGI script allows remote attackers to execute arbitrary commands via shell metacharacters in the fqdn parameter.
CVE-1999-1064(PUBLISHED):Multiple buffer overflows in WindowMaker 0.52 through 0.60.0 allow attackers to cause a denial of service and possibly execute arbitrary commands by executing WindowMaker with a long program name (argv[0]).
CVE-1999-1065(PUBLISHED):Palm Pilot HotSync Manager 3.0.4 in Windows 98 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long string to port 14238 while the manager is in network mode.
CVE-1999-1066(PUBLISHED):Quake 1 server responds to an initial UDP game connection request with a large amount of traffic, which allows remote attackers to use the server as an amplifier in a "Smurf" style attack on another host, by spoofing the connection request.
CVE-1999-1067(PUBLISHED):SGI MachineInfo CGI program, installed by default on some web servers, prints potentially sensitive system status information, which could be used by remote attackers for information gathering activities.
CVE-1999-1068(PUBLISHED):Oracle Webserver 2.1, when serving PL/SQL stored procedures, allows remote attackers to cause a denial of service via a long HTTP GET request.
CVE-1999-1069(PUBLISHED):Directory traversal vulnerability in carbo.dll in iCat Carbo Server 3.0.0 allows remote attackers to read arbitrary files via a .. (dot dot) in the icatcommand parameter.
CVE-1999-1070(PUBLISHED):Buffer overflow in ping CGI program in Xylogics Annex terminal service allows remote attackers to cause a denial of service via a long query parameter.
CVE-1999-1071(PUBLISHED):Excite for Web Servers (EWS) 1.1 installs the Architext.conf authentication file with world-writeable permissions, which allows local users to gain access to Excite accounts by modifying the file.
CVE-1999-1072(PUBLISHED):Excite for Web Servers (EWS) 1.1 allows local users to gain privileges by obtaining the encrypted password from the world-readable Architext.conf authentication file and replaying the encrypted password in an HTTP request to AT-generated.cgi or AT-admin.cgi.
CVE-1999-1073(PUBLISHED):Excite for Web Servers (EWS) 1.1 records the first two characters of a plaintext password in the beginning of the encrypted password, which makes it easier for an attacker to guess passwords via a brute force or dictionary attack.
CVE-1999-1074(PUBLISHED):Webmin before 0.5 does not restrict the number of invalid passwords that are entered for a valid username, which could allow remote attackers to gain privileges via brute force password cracking.
CVE-1999-1075(PUBLISHED):inetd in AIX 4.1.5 dynamically assigns a port N when starting ttdbserver (ToolTalk server), but also inadvertently listens on port N-1 without passing control to ttdbserver, which allows remote attackers to cause a denial of service via a large number of connections to port N-1, which are not properly closed by inetd.
CVE-1999-1076(PUBLISHED):Idle locking function in MacOS 9 allows local users to bypass the password protection of idled sessions by selecting the "Log Out" option and selecting a "Cancel" option in the dialog box for an application that attempts to verify that the user wants to log out, which returns the attacker into the locked session.
CVE-1999-1077(PUBLISHED):Idle locking function in MacOS 9 allows local attackers to bypass the password protection of idled sessions via the programmer's switch or CMD-PWR keyboard sequence, which brings up a debugger that the attacker can use to disable the lock.
CVE-1999-1078(PUBLISHED):WS_FTP Pro 6.0 uses weak encryption for passwords in its initialization files, which allows remote attackers to easily decrypt the passwords and gain privileges.
CVE-1999-1079(PUBLISHED):Vulnerability in ptrace in AIX 4.3 allows local users to gain privileges by attaching to a setgid program.
CVE-1999-1080(PUBLISHED):rmmount in SunOS 5.7 may mount file systems without the nosuid flag set, contrary to the documentation and its use in previous versions of SunOS, which could allow local users with physical access to gain root privileges by mounting a floppy or CD-ROM that contains a setuid program and running volcheck, when the file systems do not have the nosuid option specified in rmmount.conf.
CVE-1999-1081(PUBLISHED):Vulnerability in files.pl script in Novell WebServer Examples Toolkit 2 allows remote attackers to read arbitrary files.
CVE-1999-1082(PUBLISHED):Directory traversal vulnerability in Jana proxy web server 1.40 allows remote attackers to ready arbitrary files via a "......" (modified dot dot) attack.
CVE-1999-1083(PUBLISHED):Directory traversal vulnerability in Jana proxy web server 1.45 allows remote attackers to ready arbitrary files via a .. (dot dot) attack.
CVE-1999-1084(PUBLISHED):The "AEDebug" registry key is installed with insecure permissions, which allows local users to modify the key to specify a Trojan Horse debugger which is automatically executed on a system crash.
CVE-1999-1085(PUBLISHED):SSH 1.2.25, 1.2.23, and other versions, when used in in CBC (Cipher Block Chaining) or CFB (Cipher Feedback 64 bits) modes, allows remote attackers to insert arbitrary data into an existing stream between an SSH client and server by using a known plaintext attack and computing a valid CRC-32 checksum for the packet, aka the "SSH insertion attack."
CVE-1999-1086(PUBLISHED):Novell 5 and earlier, when running over IPX with a packet signature level less than 3, allows remote attackers to gain administrator privileges by spoofing the MAC address in IPC fragmented packets that make NetWare Core Protocol (NCP) calls.
CVE-1999-1087(PUBLISHED):Internet Explorer 4 treats a 32-bit number ("dotless IP address") in the a URL as the hostname instead of an IP address, which causes IE to apply Local Intranet Zone settings to the resulting web page, allowing remote malicious web servers to conduct unauthorized activities by using URLs that contain the dotless IP address for their server.
CVE-1999-1088(PUBLISHED):Vulnerability in chsh command in HP-UX 9.X through 10.20 allows local users to gain privileges.
CVE-1999-1089(PUBLISHED):Buffer overflow in chfn command in HP-UX 9.X through 10.20 allows local users to gain privileges via a long command line argument.
CVE-1999-1090(PUBLISHED):The default configuration of NCSA Telnet package for Macintosh and PC enables FTP, even though it does not include an "ftp=yes" line, which allows remote attackers to read and modify arbitrary files.
CVE-1999-1091(PUBLISHED):UNIX news readers tin and rtin create the /tmp/.tin_log file with insecure permissions and follow symlinks, which allows attackers to modify the permissions of files writable by the user via a symlink attack.
CVE-1999-1092(PUBLISHED):tin 1.40 creates the .tin directory with insecure permissions, which allows local users to read passwords from the .inputhistory file.
CVE-1999-1093(PUBLISHED):Buffer overflow in the Window.External function in the JScript Scripting Engine in Internet Explorer 4.01 SP1 and earlier allows remote attackers to execute arbitrary commands via a malicious web page.
CVE-1999-1094(PUBLISHED):Buffer overflow in Internet Explorer 4.01 and earlier allows remote attackers to execute arbitrary commands via a long URL with the "mk:" protocol, aka the "MK Overrun security issue."
CVE-1999-1095(PUBLISHED):sort creates temporary files and follows symbolic links, which allows local users to modify arbitrary files that are writable by the user running sort, as observed in updatedb and other programs that use sort.
CVE-1999-1096(PUBLISHED):Buffer overflow in kscreensaver in KDE klock allows local users to gain root privileges via a long HOME environmental variable.
CVE-1999-1097(PUBLISHED):Microsoft NetMeeting 2.1 allows one client to read the contents of another client's clipboard via a CTRL-C in the chat box when the box is empty.
CVE-1999-1098(PUBLISHED):Vulnerability in BSD Telnet client with encryption and Kerberos 4 authentication allows remote attackers to decrypt the session via sniffing.
CVE-1999-1099(PUBLISHED):Kerberos 4 allows remote attackers to obtain sensitive information via a malformed UDP packet that generates an error string that inadvertently includes the realm name and the last user.
CVE-1999-1100(PUBLISHED):Cisco PIX Private Link 4.1.6 and earlier does not properly process certain commands in the configuration file, which reduces the effective key length of the DES key to 48 bits instead of 56 bits, which makes it easier for an attacker to find the proper key via a brute force attack.
CVE-1999-1101(PUBLISHED):Kabsoftware Lydia utility uses weak encryption to store user passwords in the lydia.ini file, which allows local users to easily decrypt the passwords and gain privileges.
CVE-1999-1102(PUBLISHED):lpr on SunOS 4.1.1, BSD 4.3, A/UX 2.0.1, and other BSD-based operating systems allows local users to create or overwrite arbitrary files via a symlink attack that is triggered after invoking lpr 1000 times.
CVE-1999-1103(PUBLISHED):dxconsole in DEC OSF/1 3.2C and earlier allows local users to read arbitrary files by specifying the file with the -file parameter.
CVE-1999-1104(PUBLISHED):Windows 95 uses weak encryption for the password list (.pwl) file used when password caching is enabled, which allows local users to gain privileges by decrypting the passwords.
CVE-1999-1105(PUBLISHED):Windows 95, when Remote Administration and File Sharing for NetWare Networks is enabled, creates a share (C$) when an administrator logs in remotely, which allows remote attackers to read arbitrary files by mapping the network drive.
CVE-1999-1106(PUBLISHED):Buffer overflow in kppp in KDE allows local users to gain root access via a long -c (account_name) command line argument.
CVE-1999-1107(PUBLISHED):Buffer overflow in kppp in KDE allows local users to gain root access via a long PATH environmental variable.
CVE-1999-1109(PUBLISHED):Sendmail before 8.10.0 allows remote attackers to cause a denial of service by sending a series of ETRN commands then disconnecting from the server, while Sendmail continues to process the commands after the connection has been terminated.
CVE-1999-1110(PUBLISHED):Windows Media Player ActiveX object as used in Internet Explorer 5.0 returns a specific error code when a file does not exist, which allows remote malicious web sites to determine the existence of files on the client.
CVE-1999-1111(PUBLISHED):Vulnerability in StackGuard before 1.21 allows remote attackers to bypass the Random and Terminator Canary security mechanisms by using a non-linear attack which directly modifies a pointer to a return address instead of using a buffer overflow to reach the return address entry itself.
CVE-1999-1112(PUBLISHED):Buffer overflow in IrfanView32 3.07 and earlier allows attackers to execute arbitrary commands via a long string after the "8BPS" image type in a Photo Shop image header.
CVE-1999-1113(PUBLISHED):Buffer overflow in Eudora Internet Mail Server (EIMS) 2.01 and earlier on MacOS systems allows remote attackers to cause a denial of service via a long USER command to port 106.
CVE-1999-1114(PUBLISHED):Buffer overflow in Korn Shell (ksh) suid_exec program on IRIX 6.x and earlier, and possibly other operating systems, allows local users to gain root privileges.
CVE-1999-1115(PUBLISHED):Vulnerability in the /etc/suid_exec program in HP Apollo Domain/OS sr10.2 and sr10.3 beta, related to the Korn Shell (ksh).
CVE-1999-1116(PUBLISHED):Vulnerability in runpriv in Indigo Magic System Administration subsystem of SGI IRIX 6.3 and 6.4 allows local users to gain root privileges.
CVE-1999-1117(PUBLISHED):lquerypv in AIX 4.1 and 4.2 allows local users to read arbitrary files by specifying the file in the -h command line parameter.
CVE-1999-1118(PUBLISHED):ndd in Solaris 2.6 allows local users to cause a denial of service by modifying certain TCP/IP parameters.
CVE-1999-1119(PUBLISHED):FTP installation script anon.ftp in AIX insecurely configures anonymous FTP, which allows remote attackers to execute arbitrary commands.
CVE-1999-1120(PUBLISHED):netprint in SGI IRIX 6.4 and earlier trusts the PATH environmental variable for finding and executing the disable program, which allows local users to gain privileges.
CVE-1999-1121(PUBLISHED):The default configuration for UUCP in AIX before 3.2 allows local users to gain root privileges.
CVE-1999-1122(PUBLISHED):Vulnerability in restore in SunOS 4.0.3 and earlier allows local users to gain privileges.
CVE-1999-1123(PUBLISHED):The installation of Sun Source (sunsrc) tapes allows local users to gain root privileges via setuid root programs (1) makeinstall or (2) winstall.
CVE-1999-1124(PUBLISHED):HTTP Client application in ColdFusion allows remote attackers to bypass access restrictions for web pages on other ports by providing the target page to the mainframeset.cfm application, which requests the page from the server, making it look like the request is coming from the local host.
CVE-1999-1125(PUBLISHED):Oracle Webserver 2.1 and earlier runs setuid root, but the configuration file is owned by the oracle account, which allows any local or remote attacker who obtains access to the oracle account to gain privileges or modify arbitrary files by modifying the configuration file.
CVE-1999-1126(PUBLISHED):Cisco Resource Manager (CRM) 1.1 and earlier creates certain files with insecure permissions that allow local users to obtain sensitive configuration information including usernames, passwords, and SNMP community strings, from (1) swim_swd.log, (2) swim_debug.log, (3) dbi_debug.log, and (4) temporary files whose names begin with "DPR_".
CVE-1999-1127(PUBLISHED):Windows NT 4.0 does not properly shut down invalid named pipe RPC connections, which allows remote attackers to cause a denial of service (resource exhaustion) via a series of connections containing malformed data, aka the "Named Pipes Over RPC" vulnerability.
CVE-1999-1128(PUBLISHED):Internet Explorer 3.01 on Windows 95 allows remote malicious web sites to execute arbitrary commands via a .isp file, which is automatically downloaded and executed without prompting the user.
CVE-1999-1129(PUBLISHED):Cisco Catalyst 2900 Virtual LAN (VLAN) switches allow remote attackers to inject 802.1q frames into another VLAN by forging the VLAN identifier in the trunking tag.
CVE-1999-1130(PUBLISHED):Default configuration of the search engine in Netscape Enterprise Server 3.5.1, and possibly other versions, allows remote attackers to read the source of JHTML files by specifying a search command using the HTML-tocrec-demo1.pat pattern file.
CVE-1999-1131(PUBLISHED):Buffer overflow in OSF Distributed Computing Environment (DCE) security demon (secd) in IRIX 6.4 and earlier allows attackers to cause a denial of service via a long principal, group, or organization.
CVE-1999-1132(PUBLISHED):Windows NT 4.0 allows remote attackers to cause a denial of service (crash) via extra source routing data such as (1) a Routing Information Field (RIF) field with a hop count greater than 7, or (2) a list containing duplicate Token Ring IDs.
CVE-1999-1133(PUBLISHED):HP-UX 9.x and 10.x running X windows may allow local attackers to gain privileges via (1) vuefile, (2) vuepad, (3) dtfile, or (4) dtpad, which do not authenticate users.
CVE-1999-1134(PUBLISHED):Vulnerability in Vue 3.0 in HP 9.x allows local users to gain root privileges, as fixed by PHSS_4038, PHSS_4055, and PHSS_4066.
CVE-1999-1135(PUBLISHED):Vulnerability in VUE 3.0 in HP 9.x allows local users to gain root privileges, as fixed by PHSS_4994 and PHSS_5438.
CVE-1999-1136(PUBLISHED):Vulnerability in Predictive on HP-UX 11.0 and earlier, and MPE/iX 5.5 and earlier, allows attackers to compromise data transfer for Predictive messages (using e-mail or modem) between customer and Response Center Predictive systems.
CVE-1999-1137(PUBLISHED):The permissions for the /dev/audio device on Solaris 2.2 and earlier, and SunOS 4.1.x, allow any local user to read from the device, which could be used by an attacker to monitor conversations happening near a machine that has a microphone.
CVE-1999-1138(PUBLISHED):SCO UNIX System V/386 Release 3.2, and other SCO products, installs the home directories (1) /tmp for the dos user, and (2) /usr/tmp for the asg user, which allows other users to gain access to those accounts since /tmp and /usr/tmp are world-writable.
CVE-1999-1139(PUBLISHED):Character-Terminal User Environment (CUE) in HP-UX 11.0 and earlier allows local users to overwrite arbitrary files and gain root privileges via a symlink attack on the IOERROR.mytty file.
CVE-1999-1140(PUBLISHED):Buffer overflow in CrackLib 2.5 may allow local users to gain root privileges via a long GECOS field.
CVE-1999-1141(PUBLISHED):Ascom Timeplex router allows remote attackers to obtain sensitive information or conduct unauthorized activities by entering debug mode through a sequence of CTRL-D characters.
CVE-1999-1142(PUBLISHED):SunOS 4.1.2 and earlier allows local users to gain privileges via "LD_*" environmental variables to certain dynamically linked setuid or setgid programs such as (1) login, (2) su, or (3) sendmail, that change the real and effective user ids to the same user.
CVE-1999-1143(PUBLISHED):Vulnerability in runtime linker program rld in SGI IRIX 6.x and earlier allows local users to gain privileges via setuid and setgid programs.
CVE-1999-1144(PUBLISHED):Certain files in MPower in HP-UX 10.x are installed with insecure permissions, which allows local users to gain privileges.
CVE-1999-1145(PUBLISHED):Vulnerability in Glance programs in GlancePlus for HP-UX 10.20 and earlier allows local users to access arbitrary files and gain privileges.
CVE-1999-1146(PUBLISHED):Vulnerability in Glance and gpm programs in GlancePlus for HP-UX 9.x and earlier allows local users to access arbitrary files and gain privileges.
CVE-1999-1147(PUBLISHED):Buffer overflow in Platinum Policy Compliance Manager (PCM) 7.0 allows remote attackers to execute arbitrary commands via a long string to the Agent port (1827), which is handled by smaxagent.exe.
CVE-1999-1148(PUBLISHED):FTP service in IIS 4.0 and earlier allows remote attackers to cause a denial of service (resource exhaustion) via many passive (PASV) connections at the same time.
CVE-1999-1149(PUBLISHED):Buffer overflow in CSM Proxy 4.1 allows remote attackers to cause a denial of service (crash) via a long string to the FTP port.
CVE-1999-1150(PUBLISHED):Livingston Portmaster routers running ComOS use the same initial sequence number (ISN) for TCP connections, which allows remote attackers to conduct spoofing and hijack TCP sessions.
CVE-1999-1151(PUBLISHED):Compaq/Microcom 6000 Access Integrator does not cause a session timeout after prompting for a username or password, which allows remote attackers to cause a denial of service by connecting to the integrator without providing a username or password.
CVE-1999-1152(PUBLISHED):Compaq/Microcom 6000 Access Integrator does not disconnect a client after a certain number of failed login attempts, which allows remote attackers to guess usernames or passwords via a brute force attack.
CVE-1999-1153(PUBLISHED):HAMcards Postcard CGI script 1.0 allows remote attackers to execute arbitrary commands via shell metacharacters in the recipient email address.
CVE-1999-1154(PUBLISHED):LakeWeb Filemail CGI script allows remote attackers to execute arbitrary commands via shell metacharacters in the recipient email address.
CVE-1999-1155(PUBLISHED):LakeWeb Mail List CGI script allows remote attackers to execute arbitrary commands via shell metacharacters in the recipient email address.
CVE-1999-1156(PUBLISHED):BisonWare FTP Server 4.1 and earlier allows remote attackers to cause a denial of service via a malformed PORT command that contains a non-numeric character and a large number of carriage returns.
CVE-1999-1157(PUBLISHED):Tcpip.sys in Windows NT 4.0 before SP4 allows remote attackers to cause a denial of service via an ICMP Subnet Mask Address Request packet, when certain multiple IP addresses are bound to the same network interface.
CVE-1999-1158(PUBLISHED):Buffer overflow in (1) pluggable authentication module (PAM) on Solaris 2.5.1 and 2.5 and (2) unix_scheme in Solaris 2.4 and 2.3 allows local users to gain root privileges via programs that use these modules such as passwd, yppasswd, and nispasswd.
CVE-1999-1159(PUBLISHED):SSH 2.0.11 and earlier allows local users to request remote forwarding from privileged ports without being root.
CVE-1999-1160(PUBLISHED):Vulnerability in ftpd/kftpd in HP-UX 10.x and 9.x allows local and possibly remote users to gain root privileges.
CVE-1999-1161(PUBLISHED):Vulnerability in ppl in HP-UX 10.x and earlier allows local users to gain root privileges by forcing ppl to core dump.
CVE-1999-1162(PUBLISHED):Vulnerability in passwd in SCO UNIX 4.0 and earlier allows attackers to cause a denial of service by preventing users from being able to log into the system.
CVE-1999-1163(PUBLISHED):Vulnerability in HP Series 800 S/X/V Class servers allows remote attackers to gain access to the S/X/V Class console via the Service Support Processor (SSP) Teststation.
CVE-1999-1164(PUBLISHED):Microsoft Outlook client allows remote attackers to cause a denial of service by sending multiple email messages with the same X-UIDL headers, which causes Outlook to hang.
CVE-1999-1165(PUBLISHED):GNU fingerd 1.37 does not properly drop privileges before accessing user information, which could allow local users to (1) gain root privileges via a malicious program in the .fingerrc file, or (2) read arbitrary files via symbolic links from .plan, .forward, or .project files.
CVE-1999-1166(PUBLISHED):Linux 2.0.37 does not properly encode the Custom segment limit, which allows local users to gain root privileges by accessing and modifying kernel memory.
CVE-1999-1167(PUBLISHED):Cross-site scripting vulnerability in Third Voice Web annotation utility allows remote users to read sensitive data and generate fake web pages for other Third Voice users by injecting malicious Javascript into an annotation.
CVE-1999-1168(PUBLISHED):install.iss installation script for Internet Security Scanner (ISS) for Linux, version 5.3, allows local users to change the permissions of arbitrary files via a symlink attack on a temporary file.
CVE-1999-1169(PUBLISHED):nobo 1.2 allows remote attackers to cause a denial of service (crash) via a series of large UDP packets.
CVE-1999-1170(PUBLISHED):IPswitch IMail allows local users to gain additional privileges and modify or add mail accounts by setting the "flags" registry key to 1920.
CVE-1999-1171(PUBLISHED):IPswitch WS_FTP allows local users to gain additional privileges and modify or add mail accounts by setting the "flags" registry key to 1920.
CVE-1999-1172(PUBLISHED):By design, Maximizer Enterprise 4 calendar and address book program allows arbitrary users to modify the calendar of other users when the calendar is being shared.
CVE-1999-1173(PUBLISHED):Corel Word Perfect 8 for Linux creates a temporary working directory with world-writable permissions, which allows local users to (1) modify Word Perfect behavior by modifying files in the working directory, or (2) modify files of other users via a symlink attack.
CVE-1999-1174(PUBLISHED):ZIP drive for Iomega ZIP-100 disks allows attackers with physical access to the drive to bypass password protection by inserting a known disk with a known password, waiting for the ZIP drive to power down, manually replacing the known disk with the target disk, and using the known password to access the target disk.
CVE-1999-1175(PUBLISHED):Web Cache Control Protocol (WCCP) in Cisco Cache Engine for Cisco IOS 11.2 and earlier does not use authentication, which allows remote attackers to redirect HTTP traffic to arbitrary hosts via WCCP packets to UDP port 2048.
CVE-1999-1176(PUBLISHED):Buffer overflow in cidentd ident daemon allows local users to gain root privileges via a long line in the .authlie script.
CVE-1999-1177(PUBLISHED):Directory traversal vulnerability in nph-publish before 1.2 allows remote attackers to overwrite arbitrary files via a .. (dot dot) in the pathname for an upload operation.
CVE-1999-1178(PUBLISHED):Sambar Server 4.1 beta allows remote attackers to obtain sensitive information about the server via an HTTP request for the dumpenv.pl script.
CVE-1999-1179(PUBLISHED):Vulnerability in man.sh CGI script, included in May 1998 issue of SysAdmin Magazine, allows remote attackers to execute arbitrary commands.
CVE-1999-1180(PUBLISHED):O'Reilly WebSite 1.1e and Website Pro 2.0 allows remote attackers to execute arbitrary commands via shell metacharacters in an argument to (1) args.cmd or (2) args.bat.
CVE-1999-1181(PUBLISHED):Vulnerability in On-Line Customer Registration software for IRIX 6.2 through 6.4 allows local users to gain root privileges.
CVE-1999-1182(PUBLISHED):Buffer overflow in run-time linkers (1) ld.so or (2) ld-linux.so for Linux systems allows local users to gain privileges by calling a setuid program with a long program name (argv[0]) and forcing ld.so/ld-linux.so to report an error.
CVE-1999-1183(PUBLISHED):System Manager sysmgr GUI in SGI IRIX 6.4 and 6.3 allows remote attackers to execute commands by providing a trojan horse (1) runtask or (2) runexec descriptor file, which is used to execute a System Manager Task when the user's Mailcap entry supports the x-sgi-task or x-sgi-exec type.
CVE-1999-1184(PUBLISHED):Buffer overflow in Elm 2.4 and earlier allows local users to gain privileges via a long TERM environmental variable.
CVE-1999-1185(PUBLISHED):Buffer overflow in SCO mscreen allows local users to gain root privileges via a long terminal entry (TERM) in the .mscreenrc file.
CVE-1999-1186(PUBLISHED):rxvt, when compiled with the PRINT_PIPE option in various Linux operating systems including Linux Slackware 3.0 and RedHat 2.1, allows local users to gain root privileges by specifying a malicious program using the -print-pipe command line parameter.
CVE-1999-1187(PUBLISHED):Pine before version 3.94 allows local users to gain privileges via a symlink attack on a lockfile that is created when a user receives new mail.
CVE-1999-1188(PUBLISHED):mysqld in MySQL 3.21 creates log files with world-readable permissions, which allows local users to obtain passwords for users who are added to the user database.
CVE-1999-1189(PUBLISHED):Buffer overflow in Netscape Navigator/Communicator 4.7 for Windows 95 and Windows 98 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long argument after the ? character in a URL that references an .asp, .cgi, .html, or .pl file.
CVE-1999-1190(PUBLISHED):Buffer overflow in POP3 server of Admiral Systems EmailClub 1.05 allows remote attackers to execute arbitrary commands via a long "From" header in an e-mail message.
CVE-1999-1191(PUBLISHED):Buffer overflow in chkey in Solaris 2.5.1 and earlier allows local users to gain root privileges via a long command line argument.
CVE-1999-1192(PUBLISHED):Buffer overflow in eeprom in Solaris 2.5.1 and earlier allows local users to gain root privileges via a long command line argument.
CVE-1999-1193(PUBLISHED):The "me" user in NeXT NeXTstep 2.1 and earlier has wheel group privileges, which could allow the me user to use the su command to become root.
CVE-1999-1194(PUBLISHED):chroot in Digital Ultrix 4.1 and 4.0 is insecurely installed, which allows local users to gain privileges.
CVE-1999-1195(PUBLISHED):NAI VirusScan NT 4.0.2 does not properly modify the scan.dat virus definition file during an update via FTP, but it reports that the update was successful, which could cause a system administrator to believe that the definitions have been updated correctly.
CVE-1999-1196(PUBLISHED):Hummingbird Exceed X version 5 allows remote attackers to cause a denial of service via malformed data to port 6000.
CVE-1999-1197(PUBLISHED):TIOCCONS in SunOS 4.1.1 does not properly check the permissions of a user who tries to redirect console output and input, which could allow a local user to gain privileges.
CVE-1999-1198(PUBLISHED):BuildDisk program on NeXT systems before 2.0 does not prompt users for the root password, which allows local users to gain root privileges.
CVE-1999-1199(PUBLISHED):Apache WWW server 1.3.1 and earlier allows remote attackers to cause a denial of service (resource exhaustion) via a large number of MIME headers with the same name, aka the "sioux" vulnerability.
CVE-1999-1200(PUBLISHED):Vintra SMTP MailServer allows remote attackers to cause a denial of service via a malformed "EXPN *@" command.
CVE-1999-1201(PUBLISHED):Windows 95 and Windows 98 systems, when configured with multiple TCP/IP stacks bound to the same MAC address, allow remote attackers to cause a denial of service (traffic amplification) via a certain ICMP echo (ping) packet, which causes all stacks to send a ping response, aka TCP Chorusing.
CVE-1999-1202(PUBLISHED):StarTech (1) POP3 proxy server and (2) telnet server allows remote attackers to cause a denial of service via a long USER command.
CVE-1999-1203(PUBLISHED):Multilink PPP for ISDN dialup users in Ascend before 4.6 allows remote attackers to cause a denial of service via a spoofed endpoint identifier.
CVE-1999-1204(PUBLISHED):Check Point Firewall-1 does not properly handle certain restricted keywords (e.g., Mail, auth, time) in user-defined objects, which could produce a rule with a default "ANY" address and result in access to more systems than intended by the administrator.
CVE-1999-1205(PUBLISHED):nettune in HP-UX 10.01 and 10.00 is installed setuid root, which allows local users to cause a denial of service by modifying critical networking configuration information.
CVE-1999-1206(PUBLISHED):SystemSoft SystemWizard package in HP Pavilion PC with Windows 98, and possibly other platforms and operating systems, installs two ActiveX controls that are marked as safe for scripting, which allows remote attackers to execute arbitrary commands via a malicious web page that references (1) the Launch control, or (2) the RegObj control.
CVE-1999-1207(PUBLISHED):Buffer overflow in web-admin tool in NetXRay 2.6 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long HTTP request.
CVE-1999-1208(PUBLISHED):Buffer overflow in ping in AIX 4.2 and earlier allows local users to gain root privileges via a long command line argument.
CVE-1999-1209(PUBLISHED):Vulnerability in scoterm in SCO OpenServer 5.0 and SCO Open Desktop/Open Server 3.0 allows local users to gain root privileges.
CVE-1999-1210(PUBLISHED):xterm in Digital UNIX 4.0B *with* patch kit 5 allows local users to overwrite arbitrary files via a symlink attack on a core dump file, which is created when xterm is called with a DISPLAY environmental variable set to a display that xterm cannot access.
CVE-1999-1211(PUBLISHED):Vulnerability in in.telnetd in SunOS 4.1.1 and earlier allows local users to gain root privileges.
CVE-1999-1212(PUBLISHED):Vulnerability in in.rlogind in SunOS 4.0.3 and 4.0.3c allows local users to gain root privileges.
CVE-1999-1213(PUBLISHED):Vulnerability in telnet service in HP-UX 10.30 allows attackers to cause a denial of service.
CVE-1999-1214(PUBLISHED):The asynchronous I/O facility in 4.4 BSD kernel does not check user credentials when setting the recipient of I/O notification, which allows local users to cause a denial of service by using certain ioctl and fcntl calls to cause the signal to be sent to an arbitrary process ID.
CVE-1999-1215(PUBLISHED):LOGIN.EXE program in Novell Netware 4.0 and 4.01 temporarily writes user name and password information to disk, which could allow local users to gain privileges.
CVE-1999-1216(PUBLISHED):Cisco routers 9.17 and earlier allow remote attackers to bypass security restrictions via certain IP source routed packets that should normally be denied using the "no ip source-route" command.
CVE-1999-1217(PUBLISHED):The PATH in Windows NT includes the current working directory (.), which could allow local users to gain privileges by placing Trojan horse programs with the same name as commonly used system programs into certain directories.
CVE-1999-1218(PUBLISHED):Vulnerability in finger in Commodore Amiga UNIX 2.1p2a and earlier allows local users to read arbitrary files.
CVE-1999-1219(PUBLISHED):Vulnerability in sgihelp in the SGI help system and print manager in IRIX 5.2 and earlier allows local users to gain root privileges, possibly through the clogin command.
CVE-1999-1220(PUBLISHED):Majordomo 1.94.3 and earlier allows remote attackers to execute arbitrary commands when the advertise or noadvertise directive is used in a configuration file, via shell metacharacters in the Reply-To header.
CVE-1999-1221(PUBLISHED):dxchpwd in Digital Unix (OSF/1) 3.x allows local users to modify arbitrary files via a symlink attack on the dxchpwd.log file.
CVE-1999-1222(PUBLISHED):Netbt.sys in Windows NT 4.0 allows remote malicious DNS servers to cause a denial of service (crash) by returning 0.0.0.0 as the IP address for a DNS host name lookup.
CVE-1999-1223(PUBLISHED):IIS 3.0 allows remote attackers to cause a denial of service via a request to an ASP page in which the URL contains a large number of / (forward slash) characters.
CVE-1999-1224(PUBLISHED):IMAP 4.1 BETA, and possibly other versions, does not properly handle the SIGABRT (abort) signal, which allows local users to crash the server (imapd) via certain sequences of commands, which causes a core dump that may contain sensitive password information.
CVE-1999-1225(PUBLISHED):rpc.mountd on Linux, Ultrix, and possibly other operating systems, allows remote attackers to determine the existence of a file on the server by attempting to mount that file, which generates different error messages depending on whether the file exists or not.
CVE-1999-1226(PUBLISHED):Netscape Communicator 4.7 and earlier allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long certificate key.
CVE-1999-1227(PUBLISHED):Ethereal allows local users to overwrite arbitrary files via a symlink attack on the packet capture file.
CVE-1999-1228(PUBLISHED):Various modems that do not implement a guard time, or are configured with a guard time of 0, can allow remote attackers to execute arbitrary modem commands such as ATH, ATH0, etc., via a "+++" sequence that appears in ICMP packets, the subject of an e-mail message, IRC commands, and others.
CVE-1999-1229(PUBLISHED):Quake 2 server 3.13 on Linux does not properly check file permissions for the config.cfg configuration file, which allows local users to read arbitrary files via a symlink from config.cfg to the target file.
CVE-1999-1230(PUBLISHED):Quake 2 server allows remote attackers to cause a denial of service via a spoofed UDP packet with a source address of 127.0.0.1, which causes the server to attempt to connect to itself.
CVE-1999-1231(PUBLISHED):ssh 2.0.12, and possibly other versions, allows valid user names to attempt to enter the correct password multiple times, but only prompts an invalid user name for a password once, which allows remote attackers to determine user account names on the server.
CVE-1999-1232(PUBLISHED):Untrusted search path vulnerability in day5datacopier in SGI IRIX 6.2 allows local users to execute arbitrary commands via a modified PATH environment variable that points to a malicious cp program.
CVE-1999-1233(PUBLISHED):IIS 4.0 does not properly restrict access for the initial session request from a user's IP address if the address does not resolve to a DNS domain, aka the "Domain Resolution" vulnerability.
CVE-1999-1234(PUBLISHED):LSA (LSASS.EXE) in Windows NT 4.0 allows remote attackers to cause a denial of service via a NULL policy handle in a call to (1) SamrOpenDomain, (2) SamrEnumDomainUsers, and (3) SamrQueryDomainInfo.
CVE-1999-1235(PUBLISHED):Internet Explorer 5.0 records the username and password for FTP servers in the URL history, which could allow (1) local users to read the information from another user's index.dat, or (2) people who are physically observing ("shoulder surfing") another user to read the information from the status bar when the user moves the mouse over a link.
CVE-1999-1236(PUBLISHED):Internet Anywhere Mail Server 2.3.1 stores passwords in plaintext in the msgboxes.dbf file, which could allow local users to gain privileges by extracting the passwords from msgboxes.dbf.
CVE-1999-1237(PUBLISHED):Multiple buffer overflows in smbvalid/smbval SMB authentication library, as used in Apache::AuthenSmb and possibly other modules, allows remote attackers to execute arbitrary commands via (1) a long username, (2) a long password, and (3) other unspecified methods.
CVE-1999-1238(PUBLISHED):Vulnerability in CORE-DIAG fileset in HP message catalog in HP-UX 9.05 and earlier allows local users to gain privileges.
CVE-1999-1239(PUBLISHED):HP-UX 9.x does not properly enable the Xauthority mechanism in certain conditions, which could allow local users to access the X display even when they have not explicitly been authorized to do so.
CVE-1999-1240(PUBLISHED):Buffer overflow in cddbd CD database server allows remote attackers to execute arbitrary commands via a long log message.
CVE-1999-1241(PUBLISHED):Internet Explorer, with a security setting below Medium, allows remote attackers to execute arbitrary commands via a malicious web page that uses the FileSystemObject ActiveX object.
CVE-1999-1242(PUBLISHED):Vulnerability in subnetconfig in HP-UX 9.01 and 9.0 allows local users to gain privileges.
CVE-1999-1243(PUBLISHED):SGI Desktop Permissions Tool in IRIX 6.0.1 and earlier allows local users to modify permissions for arbitrary files and gain privileges.
CVE-1999-1244(PUBLISHED):IPFilter 3.2.3 through 3.2.10 allows local users to modify arbitrary files via a symlink attack on the saved output file.
CVE-1999-1245(PUBLISHED):vacm ucd-snmp SNMP server, version 3.52, does not properly disable access to the public community string, which could allow remote attackers to obtain sensitive information.
CVE-1999-1246(PUBLISHED):Direct Mailer feature in Microsoft Site Server 3.0 saves user domain names and passwords in plaintext in the TMLBQueue network share, which has insecure default permissions, allowing remote attackers to read the passwords and gain privileges.
CVE-1999-1247(PUBLISHED):Vulnerability in HP Camera component of HP DCE/9000 in HP-UX 9.x allows attackers to gain root privileges.
CVE-1999-1248(PUBLISHED):Vulnerability in Support Watch (aka SupportWatch) in HP-UX 8.0 through 9.0 allows local users to gain privileges.
CVE-1999-1249(PUBLISHED):movemail in HP-UX 10.20 has insecure permissions, which allows local users to gain privileges.
CVE-1999-1250(PUBLISHED):Vulnerability in CGI program in the Lasso application by Blue World, as used on WebSTAR and other servers, allows remote attackers to read arbitrary files.
CVE-1999-1251(PUBLISHED):Vulnerability in direct audio user space code on HP-UX 10.20 and 10.10 allows local users to cause a denial of service.
CVE-1999-1252(PUBLISHED):Vulnerability in a certain system call in SCO UnixWare 2.0.x and 2.1.0 allows local users to access arbitrary files and gain root privileges.
CVE-1999-1253(PUBLISHED):Vulnerability in a kernel error handling routine in SCO OpenServer 5.0.2 and earlier, and SCO Internet FastStart 1.0, allows local users to gain root privileges.
CVE-1999-1254(PUBLISHED):Windows 95, 98, and NT 4.0 allow remote attackers to cause a denial of service by spoofing ICMP redirect messages from a router, which causes Windows to change its routing tables.
CVE-1999-1255(PUBLISHED):Hyperseek allows remote attackers to modify the hyperseek configuration by directly calling the admin.cgi program with an edit_file action parameter.
CVE-1999-1256(PUBLISHED):Oracle Database Assistant 1.0 in Oracle 8.0.3 Enterprise Edition stores the database master password in plaintext in the spoolmain.log file when a new database is created, which allows local users to obtain the password from that file.
CVE-1999-1257(PUBLISHED):Xyplex terminal server 6.0.1S1, and possibly other versions, allows remote attackers to bypass the password prompt by entering (1) a CTRL-Z character, or (2) a ? (question mark).
CVE-1999-1258(PUBLISHED):rpc.pwdauthd in SunOS 4.1.1 and earlier does not properly prevent remote access to the daemon, which allows remote attackers to obtain sensitive system information.
CVE-1999-1259(PUBLISHED):Microsoft Office 98, Macintosh Edition, does not properly initialize the disk space used by Office 98 files and effectively inserts data from previously deleted files into the Office file, which could allow attackers to obtain sensitive information.
CVE-1999-1260(PUBLISHED):mSQL (Mini SQL) 2.0.6 allows remote attackers to obtain sensitive server information such as logged users, database names, and server version via the ServerStats query.
CVE-1999-1261(PUBLISHED):Buffer overflow in Rainbow Six Multiplayer allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long nickname (nick) command.
CVE-1999-1262(PUBLISHED):Java in Netscape 4.5 does not properly restrict applets from connecting to other hosts besides the one from which the applet was loaded, which violates the Java security model and could allow remote attackers to conduct unauthorized activities.
CVE-1999-1263(PUBLISHED):Metamail before 2.7-7.2 allows remote attackers to overwrite arbitrary files via an e-mail message containing a uuencoded attachment that specifies the full pathname for the file to be modified, which is processed by uuencode in Metamail scripts such as sun-audio-file.
CVE-1999-1264(PUBLISHED):WebRamp M3 router does not disable remote telnet or HTTP access to itself, even when access has been explicitly disabled.
CVE-1999-1265(PUBLISHED):SMTP server in SLmail 3.1 and earlier allows remote attackers to cause a denial of service via malformed commands whose arguments begin with a "(" (parenthesis) character, such as (1) SEND, (2) VRFY, (3) EXPN, (4) MAIL FROM, (5) RCPT TO.
CVE-1999-1266(PUBLISHED):rsh daemon (rshd) generates different error messages when a valid username is provided versus an invalid name, which allows remote attackers to determine valid users on the system.
CVE-1999-1267(PUBLISHED):KDE file manager (kfm) uses a TCP server for certain file operations, which allows remote attackers to modify arbitrary files by sending a copy command to the server.
CVE-1999-1268(PUBLISHED):Vulnerability in KDE konsole allows local users to hijack or observe sessions of other users by accessing certain devices.
CVE-1999-1269(PUBLISHED):Screen savers in KDE beta 3 allows local users to overwrite arbitrary files via a symlink attack on the .kss.pid file.
CVE-1999-1270(PUBLISHED):KMail in KDE 1.0 provides a PGP passphrase as a command line argument to other programs, which could allow local users to obtain the passphrase and compromise the PGP keys of other users by viewing the arguments via programs that list process information, such as ps.
CVE-1999-1271(PUBLISHED):Macromedia Dreamweaver uses weak encryption to store FTP passwords, which could allow local users to easily decrypt the passwords of other users.
CVE-1999-1272(PUBLISHED):Buffer overflows in CDROM Confidence Test program (cdrom) allow local users to gain root privileges.
CVE-1999-1273(PUBLISHED):Squid Internet Object Cache 1.1.20 allows users to bypass access control lists (ACLs) by encoding the URL with hexadecimal escape sequences.
CVE-1999-1274(PUBLISHED):iPass RoamServer 3.1 creates temporary files with world-writable permissions.
CVE-1999-1275(PUBLISHED):Lotus cc:Mail release 8 stores the postoffice password in plaintext in a hidden file which has insecure permissions, which allows local users to gain privileges.
CVE-1999-1276(PUBLISHED):fte-console in the fte package before 0.46b-4.1 does not drop root privileges, which allows local users to gain root access via the virtual console device.
CVE-1999-1277(PUBLISHED):BackWeb client stores the username and password in cleartext for proxy authentication in the Communication registry key, which could allow other local users to gain privileges by reading the password.
CVE-1999-1278(PUBLISHED):nlog CGI scripts do not properly filter shell metacharacters from the IP address argument, which could allow remote attackers to execute certain commands via (1) nlog-smb.pl or (2) rpc-nlog.pl.
CVE-1999-1279(PUBLISHED):An interaction between the AS/400 shared folders feature and Microsoft SNA Server 3.0 and earlier allows users to view each other's folders when the users share the same Local APPC LU.
CVE-1999-1280(PUBLISHED):Hummingbird Exceed ******* inadvertently includes a DLL that was meant for development and testing, which logs user names and passwords in cleartext in the test.log file.
CVE-1999-1281(PUBLISHED):Development version of Breeze Network Server allows remote attackers to cause the system to reboot by accessing the configbreeze CGI program.
CVE-1999-1282(PUBLISHED):RealSystem G2 server stores the administrator password in cleartext in a world-readable configuration file, which allows local users to gain privileges.
CVE-1999-1283(PUBLISHED):Opera 3.2.1 allows remote attackers to cause a denial of service (application crash) via a URL that contains an extra / in the http:// tag.
CVE-1999-1284(PUBLISHED):NukeNabber allows remote attackers to cause a denial of service by connecting to the NukeNabber port (1080) without sending any data, which causes the CPU usage to rise to 100% from the report.exe program that is executed upon the connection.
CVE-1999-1285(PUBLISHED):Linux 2.1.132 and earlier allows local users to cause a denial of service (resource exhaustion) by reading a large buffer from a random device (e.g. /dev/urandom), which cannot be interrupted until the read has completed.
CVE-1999-1286(PUBLISHED):addnetpr in SGI IRIX 6.2 and earlier allows local users to modify arbitrary files and possibly gain root access via a symlink attack on a temporary file.
CVE-1999-1287(PUBLISHED):Vulnerability in Analog 3.0 and earlier allows remote attackers to read arbitrary files via the forms interface.
CVE-1999-1288(PUBLISHED):Samba 1.9.18 inadvertently includes a prototype application, wsmbconf, which is installed with incorrect permissions including the setgid bit, which allows local users to read and write files and possibly gain privileges via bugs in the program.
CVE-1999-1289(PUBLISHED):ICQ 98 beta on Windows NT leaks the internal IP address of a client in the TCP data segment of an ICQ packet instead of the public address (e.g. through NAT), which provides remote attackers with potentially sensitive information about the client or the internal network configuration.
CVE-1999-1290(PUBLISHED):Buffer overflow in nftp FTP client version 1.40 allows remote malicious FTP servers to cause a denial of service, and possibly execute arbitrary commands, via a long response string.
CVE-1999-1291(PUBLISHED):TCP/IP implementation in Microsoft Windows 95, Windows NT 4.0, and possibly others, allows remote attackers to reset connections by forcing a reset (RST) via a PSH ACK or other means, obtaining the target's last sequence number from the resulting packet, then spoofing a reset to the target.
CVE-1999-1292(PUBLISHED):Buffer overflow in web administration feature of Kolban Webcam32 4.8.3 and earlier allows remote attackers to execute arbitrary commands via a long URL.
CVE-1999-1293(PUBLISHED):mod_proxy in Apache 1.2.5 and earlier allows remote attackers to cause a denial of service via malformed FTP commands, which causes Apache to dump core.
CVE-1999-1294(PUBLISHED):Office Shortcut Bar (OSB) in Windows 3.51 enables backup and restore permissions, which are inherited by programs such as File Manager that are started from the Shortcut Bar, which could allow local users to read folders for which they do not have permission.
CVE-1999-1295(PUBLISHED):Transarc DCE Distributed File System (DFS) 1.1 for Solaris 2.4 and 2.5 does not properly initialize the grouplist for users who belong to a large number of groups, which could allow those users to gain access to resources that are protected by DFS.
CVE-1999-1296(PUBLISHED):Buffer overflow in Kerberos IV compatibility libraries as used in Kerberos V allows local users to gain root privileges via a long line in a kerberos configuration file, which can be specified via the KRB_CONF environmental variable.
CVE-1999-1297(PUBLISHED):cmdtool in OpenWindows 3.0 and XView 3.0 in SunOS 4.1.4 and earlier allows attackers with physical access to the system to display unechoed characters (such as those from password prompts) via the L2/AGAIN key.
CVE-1999-1298(PUBLISHED):Sysinstall in FreeBSD 2.2.1 and earlier, when configuring anonymous FTP, creates the ftp user without a password and with /bin/date as the shell, which could allow attackers to gain access to certain system resources.
CVE-1999-1299(PUBLISHED):rcp on various Linux systems including Red Hat 4.0 allows a "nobody" user or other user with UID of 65535 to overwrite arbitrary files, since 65535 is interpreted as -1 by chown and other system calls, which causes the calls to fail to modify the ownership of the file.
CVE-1999-1300(PUBLISHED):Vulnerability in accton in Cray UNICOS 6.1 and 6.0 allows local users to read arbitrary files and modify system accounting configuration.
CVE-1999-1301(PUBLISHED):A design flaw in the Z-Modem protocol allows the remote sender of a file to execute arbitrary programs on the client, as implemented in rz in the rzsz module of FreeBSD before 2.1.5, and possibly other programs.
CVE-1999-1302(PUBLISHED):Unspecified vulnerability in pt_chmod in SCO UNIX 4.2 and earlier allows local users to gain root access.
CVE-1999-1303(PUBLISHED):Vulnerability in prwarn in SCO UNIX 4.2 and earlier allows local users to gain root access.
CVE-1999-1304(PUBLISHED):Vulnerability in login in SCO UNIX 4.2 and earlier allows local users to gain root access.
CVE-1999-1305(PUBLISHED):Vulnerability in "at" program in SCO UNIX 4.2 and earlier allows local users to gain root access.
CVE-1999-1306(PUBLISHED):Cisco IOS 9.1 and earlier does not properly handle extended IP access lists when the IP route cache is enabled and the "established" keyword is set, which could allow attackers to bypass filters.
CVE-1999-1307(PUBLISHED):Vulnerability in urestore in Novell UnixWare 1.1 allows local users to gain root privileges.
CVE-1999-1308(PUBLISHED):Certain programs in HP-UX 10.20 do not properly handle large user IDs (UID) or group IDs (GID) over 60000, which could allow local users to gain privileges.
CVE-1999-1309(PUBLISHED):Sendmail before 8.6.7 allows local users to gain root access via a large value in the debug (-d) command line option.
CVE-1999-1311(PUBLISHED):Vulnerability in dtlogin and dtsession in HP-UX 10.20 and 10.10 allows local users to bypass authentication and gain privileges.
CVE-1999-1312(PUBLISHED):Vulnerability in DEC OpenVMS VAX 5.5-2 through 5.0, and OpenVMS AXP 1.0, allows local users to gain system privileges.
CVE-1999-1313(PUBLISHED):Manual page reader (man) in FreeBSD 2.2 and earlier allows local users to gain privileges via a sequence of commands.
CVE-1999-1314(PUBLISHED):Vulnerability in union file system in FreeBSD 2.2 and earlier, and possibly other operating systems, allows local users to cause a denial of service (system reload) via a series of certain mount_union commands.
CVE-1999-1315(PUBLISHED):Vulnerabilities in DECnet/OSI for OpenVMS before 5.8 on DEC Alpha AXP and VAX/VMS systems allow local users to gain privileges or cause a denial of service.
CVE-1999-1316(PUBLISHED):Passfilt.dll in Windows NT SP2 allows users to create a password that contains the user's name, which could make it easier for an attacker to guess.
CVE-1999-1317(PUBLISHED):Windows NT 4.0 SP4 and earlier allows local users to gain privileges by modifying the symbolic link table in the \?? object folder using a different case letter (upper or lower) to point to a different device.
CVE-1999-1318(PUBLISHED):/usr/5bin/su in SunOS 4.1.3 and earlier uses a search path that includes the current working directory (.), which allows local users to gain privileges via Trojan horse programs.
CVE-1999-1319(PUBLISHED):Vulnerability in object server program in SGI IRIX 5.2 through 6.1 allows remote attackers to gain root privileges in certain configurations.
CVE-1999-1320(PUBLISHED):Vulnerability in Novell NetWare 3.x and earlier allows local users to gain privileges via packet spoofing.
CVE-1999-1321(PUBLISHED):Buffer overflow in ssh 1.2.26 client with Kerberos V enabled could allow remote attackers to cause a denial of service or execute arbitrary commands via a long DNS hostname that is not properly handled during TGT ticket passing.
CVE-1999-1322(PUBLISHED):The installation of 1ArcServe Backup and Inoculan AV client modules for Exchange create a log file, exchverify.log, which contains usernames and passwords in plaintext.
CVE-1999-1323(PUBLISHED):Norton AntiVirus for Internet Email Gateways (NAVIEG) ******* and earlier, and Norton AntiVirus for MS Exchange (NAVMSE) 1.5 and earlier, store the administrator password in cleartext in (1) the navieg.ini file for NAVIEG, and (2) the ModifyPassword registry key in NAVMSE.
CVE-1999-1324(PUBLISHED):VAXstations running Open VMS 5.3 through 5.5-2 with VMS DECwindows or MOTIF do not properly disable access to user accounts that exceed the break-in limit threshold for failed login attempts, which makes it easier for attackers to conduct brute force password guessing.
CVE-1999-1325(PUBLISHED):SAS System 5.18 on VAX/VMS is installed with insecure permissions for its directories and startup file, which allows local users to gain privileges.
CVE-1999-1326(PUBLISHED):wu-ftpd 2.4 FTP server does not properly drop privileges when an ABOR (abort file transfer) command is executed during a file transfer, which causes a signal to be handled incorrectly and allows local and possibly remote attackers to read arbitrary files.
CVE-1999-1327(PUBLISHED):Buffer overflow in linuxconf 1.11r11-rh2 on Red Hat Linux 5.1 allows local users to gain root privileges via a long LANG environmental variable.
CVE-1999-1328(PUBLISHED):linuxconf before 1.11.r11-rh3 on Red Hat Linux 5.1 allows local users to overwrite arbitrary files and gain root access via a symlink attack.
CVE-1999-1329(PUBLISHED):Buffer overflow in SysVInit in Red Hat Linux 5.1 and earlier allows local users to gain privileges.
CVE-1999-1330(PUBLISHED):The snprintf function in the db library 1.85.4 ignores the size parameter, which could allow attackers to exploit buffer overflows that would be prevented by a properly implemented snprintf.
CVE-1999-1331(PUBLISHED):netcfg 2.16-1 in Red Hat Linux 4.2 allows the Ethernet interface to be controlled by users on reboot when an option is set, which allows local users to cause a denial of service by shutting down the interface.
CVE-1999-1332(PUBLISHED):gzexe in the gzip package on Red Hat Linux 5.0 and earlier allows local users to overwrite files of other users via a symlink attack on a temporary file.
CVE-1999-1333(PUBLISHED):automatic download option in ncftp 2.4.2 FTP client in Red Hat Linux 5.0 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters in the names of files that are to be downloaded.
CVE-1999-1334(PUBLISHED):Multiple buffer overflows in filter command in Elm 2.4 allows attackers to execute arbitrary commands via (1) long From: headers, (2) long Reply-To: headers, or (3) via a long -f (filterfile) command line argument.
CVE-1999-1335(PUBLISHED):snmpd server in cmu-snmp SNMP package before 3.3-1 in Red Hat Linux 4.0 is configured to allow remote attackers to read and write sensitive information.
CVE-1999-1336(PUBLISHED):3Com HiPer Access Router Card (HiperARC) 4.0 through 4.2.29 allows remote attackers to cause a denial of service (reboot) via a flood of IAC packets to the telnet port.
CVE-1999-1337(PUBLISHED):FTP client in Midnight Commander (mc) before 4.5.11 stores usernames and passwords for visited sites in plaintext in the world-readable history file, which allows other local users to gain privileges.
CVE-1999-1338(PUBLISHED):Delegate proxy 5.9.3 and earlier creates files and directories in the DGROOT with world-writable permissions.
CVE-1999-1339(PUBLISHED):Vulnerability when Network Address Translation (NAT) is enabled in Linux 2.2.10 and earlier with ipchains, or FreeBSD 3.2 with ipfw, allows remote attackers to cause a denial of service (kernel panic) via a ping -R (record route) command.
CVE-1999-1340(PUBLISHED):Buffer overflow in faxalter in hylafax 4.0.2 allows local users to gain privileges via a long -m command line argument.
CVE-1999-1341(PUBLISHED):Linux kernel before 2.3.18 or 2.2.13pre15, with SLIP and PPP options, allows local unprivileged users to forge IP packets via the TIOCSETD option on tty devices.
CVE-1999-1342(PUBLISHED):ICQ ActiveList Server allows remote attackers to cause a denial of service (crash) via malformed packets to the server's UDP port.
CVE-1999-1343(PUBLISHED):HTTP server for Xerox DocuColor 4 LP allows remote attackers to cause a denial of service (hang) via a long URL that contains a large number of . characters.
CVE-1999-1344(PUBLISHED):Auto_FTP.pl script in Auto_FTP 0.2 stores usernames and passwords in plaintext in the auto_ftp.conf configuration file.
CVE-1999-1345(PUBLISHED):Auto_FTP.pl script in Auto_FTP 0.2 uses the /tmp/ftp_tmp as a shared directory with insecure permissions, which allows local users to (1) send arbitrary files to the remote server by placing them in the directory, and (2) view files that are being transferred.
CVE-1999-1346(PUBLISHED):PAM configuration file for rlogin in Red Hat Linux 6.1 and earlier includes a less restrictive rule before a more restrictive one, which allows users to access the host via rlogin even if rlogin has been explicitly disabled using the /etc/nologin file.
CVE-1999-1347(PUBLISHED):Xsession in Red Hat Linux 6.1 and earlier can allow local users with restricted accounts to bypass execution of the .xsession file by starting kde, gnome or anotherlevel from kdm.
CVE-1999-1348(PUBLISHED):Linuxconf on Red Hat Linux 6.0 and earlier does not properly disable PAM-based access to the shutdown command, which could allow local users to cause a denial of service.
CVE-1999-1349(PUBLISHED):NFS daemon (nfsd.exe) for Omni-NFS/X 6.1 allows remote attackers to cause a denial of service (resource exhaustion) via certain packets, possibly with the Urgent (URG) flag set, to port 111.
CVE-1999-1350(PUBLISHED):ARCAD Systemhaus 0.078-5 installs critical programs and files with world-writeable permissions, which could allow local users to gain privileges by replacing a program with a Trojan horse.
CVE-1999-1351(PUBLISHED):Directory traversal vulnerability in KVIrc IRC client 0.9.0 with the "Listen to !nick <soundname> requests" option enabled allows remote attackers to read arbitrary files via a .. (dot dot) in a DCC GET request.
CVE-1999-1352(PUBLISHED):mknod in Linux 2.2 follows symbolic links, which could allow local users to overwrite files or gain privileges.
CVE-1999-1353(PUBLISHED):Nosque MsgCore 2.14 stores passwords in cleartext: (1) the administrator password in the AdmPasswd registry key, and (2) user passwords in the Userbase.dbf data file, which could allow local users to gain privileges.
CVE-1999-1354(PUBLISHED):E-mail client in Softarc FirstClass Internet Server 5.506 and earlier stores usernames and passwords in cleartext in the files (1) home.fc for version 5.506, (2) network.fc for version 3.5, or (3) FCCLIENT.LOG when logging is enabled.
CVE-1999-1355(PUBLISHED):BMC Patrol component, when installed with Compaq Insight Management Agent 4.23 and earlier, or Management Agents for Servers 4.40 and earlier, creates a PFCUser account with a default password and potentially dangerous privileges.
CVE-1999-1356(PUBLISHED):Compaq Integration Maintenance Utility as used in Compaq Insight Manager agent before SmartStart 4.50 modifies the legal notice caption (LegalNoticeCaption) and text (LegalNoticeText) in Windows NT, which could produce a legal notice that is in violation of the security policy.
CVE-1999-1357(PUBLISHED):Netscape Communicator 4.04 through 4.7 (and possibly other versions) in various UNIX operating systems converts the 0x8b character to a "<" sign, and the 0x9b character to a ">" sign, which could allow remote attackers to attack other clients via cross-site scripting (CSS) in CGI programs that do not filter these characters.
CVE-1999-1358(PUBLISHED):When an administrator in Windows NT or Windows 2000 changes a user policy, the policy is not properly updated if the local ntconfig.pol is not writable by the user, which could allow local users to bypass restrictions that would otherwise be enforced by the policy, possibly by changing the policy file to be read-only.
CVE-1999-1359(PUBLISHED):When the Ntconfig.pol file is used on a server whose name is longer than 13 characters, Windows NT does not properly enforce policies for global groups, which could allow users to bypass restrictions that were intended by those policies.
CVE-1999-1360(PUBLISHED):Windows NT 4.0 allows local users to cause a denial of service via a user mode application that closes a handle that was opened in kernel mode, which causes a crash when the kernel attempts to close the handle.
CVE-1999-1361(PUBLISHED):Windows NT 3.51 and 4.0 running WINS (Windows Internet Name Service) allows remote attackers to cause a denial of service (resource exhaustion) via a flood of malformed packets, which causes the server to slow down and fill the event logs with error messages.
CVE-1999-1362(PUBLISHED):Win32k.sys in Windows NT 4.0 before SP2 allows local users to cause a denial of service (crash) by calling certain WIN32K functions with incorrect parameters.
CVE-1999-1363(PUBLISHED):Windows NT 3.51 and 4.0 allow local users to cause a denial of service (crash) by running a program that creates a large number of locks on a file, which exhausts the NonPagedPool.
CVE-1999-1364(PUBLISHED):Windows NT 4.0 allows local users to cause a denial of service (crash) via an illegal kernel mode address to the functions (1) GetThreadContext or (2) SetThreadContext.
CVE-1999-1365(PUBLISHED):Windows NT searches a user's home directory (%systemroot% by default) before other directories to find critical programs such as NDDEAGNT.EXE, EXPLORER.EXE, USERINIT.EXE or TASKMGR.EXE, which could allow local users to bypass access restrictions or gain privileges by placing a Trojan horse program into the root directory, which is writable by default.
CVE-1999-1366(PUBLISHED):Pegasus e-mail client 3.0 and earlier uses weak encryption to store POP3 passwords in the pmail.ini file, which allows local users to easily decrypt the passwords and read e-mail.
CVE-1999-1367(PUBLISHED):Internet Explorer 5.0 does not properly reset the username/password cache for Web sites that do not use standard cache controls, which could allow users on the same system to access restricted web sites that were visited by other users.
CVE-1999-1368(PUBLISHED):AV Option for MS Exchange Server option for InoculateIT 4.53, and possibly other versions, only scans the Inbox folder tree of a Microsoft Exchange server, which could allow viruses to escape detection if a user's rules cause the message to be moved to a different mailbox.
CVE-1999-1369(PUBLISHED):Real Media RealServer (rmserver) 6.0.3.353 stores a password in plaintext in the world-readable rmserver.cfg file, which allows local users to gain privileges.
CVE-1999-1370(PUBLISHED):The setup wizard (ie5setup.exe) for Internet Explorer 5.0 disables (1) the screen saver, which could leave the system open to users with physical access if a failure occurs during an unattended installation, and (2) the Task Scheduler Service, which might prevent the scheduled execution of security-critical programs.
CVE-1999-1371(PUBLISHED):Buffer overflow in /usr/bin/write in Solaris 2.6 and 7 allows local users to gain privileges via a long string in the terminal name argument.
CVE-1999-1372(PUBLISHED):Triactive Remote Manager with Basic authentication enabled stores the username and password in cleartext in registry keys, which could allow local users to gain privileges.
CVE-1999-1373(PUBLISHED):FORE PowerHub before 5.0.1 allows remote attackers to cause a denial of service (hang) via a TCP SYN scan with TCP/IP OS fingerprinting, e.g. via nmap.
CVE-1999-1374(PUBLISHED):perlshop.cgi shopping cart program stores sensitive customer information in directories and files that are under the web root, which allows remote attackers to obtain that information via an HTTP request.
CVE-1999-1375(PUBLISHED):FileSystemObject (FSO) in the showfile.asp Active Server Page (ASP) allows remote attackers to read arbitrary files by specifying the name in the file parameter.
CVE-1999-1376(PUBLISHED):Buffer overflow in fpcount.exe in IIS 4.0 with FrontPage Server Extensions allows remote attackers to execute arbitrary commands.
CVE-1999-1377(PUBLISHED):Matt Wright's download.cgi 1.0 allows remote attackers to read arbitrary files via a .. (dot dot) in the f parameter.
CVE-1999-1378(PUBLISHED):dbmlparser.exe CGI guestbook program does not perform a chroot operation properly, which allows remote attackers to read arbitrary files.
CVE-1999-1379(PUBLISHED):DNS allows remote attackers to use DNS name servers as traffic amplifiers via a UDP DNS query with a spoofed source address, which produces more traffic to the victim than was sent by the attacker.
CVE-1999-1380(PUBLISHED):Symantec Norton Utilities 2.0 for Windows 95 marks the TUNEOCX.OCX ActiveX control as safe for scripting, which allows remote attackers to execute arbitrary commands via the run option through malicious web pages that are accessed by browsers such as Internet Explorer 3.0.
CVE-1999-1381(PUBLISHED):Buffer overflow in dbadmin CGI program 1.0.1 on Linux allows remote attackers to execute arbitrary commands.
CVE-1999-1382(PUBLISHED):NetWare NFS mode 1 and 2 implements the "Read Only" flag in Unix by changing the ownership of a file to root, which allows local users to gain root privileges by creating a setuid program and setting it to "Read Only," which NetWare-NFS changes to a setuid root program.
CVE-1999-1383(PUBLISHED):(1) bash before 1.14.7, and (2) tcsh 6.05 allow local users to gain privileges via directory names that contain shell metacharacters (` back-tick), which can cause the commands enclosed in the directory name to be executed when the shell expands filenames using the \w option in the PS1 variable.
CVE-1999-1384(PUBLISHED):Indigo Magic System Tour in the SGI system tour package (systour) for IRIX 5.x through 6.3 allows local users to gain root privileges via a Trojan horse .exitops program, which is called by the inst command that is executed by the RemoveSystemTour program.
CVE-1999-1385(PUBLISHED):Buffer overflow in ppp program in FreeBSD 2.1 and earlier allows local users to gain privileges via a long HOME environment variable.
CVE-1999-1386(PUBLISHED):Perl 5.004_04 and earlier follows symbolic links when running with the -e option, which allows local users to overwrite arbitrary files via a symlink attack on the /tmp/perl-eaXXXXX file.
CVE-1999-1387(PUBLISHED):Windows NT 4.0 SP2 allows remote attackers to cause a denial of service (crash), possibly via malformed inputs or packets, such as those generated by a Linux smbmount command that was compiled on the Linux 2.0.29 kernel but executed on Linux 2.0.25.
CVE-1999-1388(PUBLISHED):passwd in SunOS 4.1.x allows local users to overwrite arbitrary files via a symlink attack and the -F command line argument.
CVE-1999-1389(PUBLISHED):US Robotics/3Com Total Control Chassis with Frame Relay between 3.6.22 and 3.7.24 does not properly enforce access filters when the "set host prompt" setting is made for a port, which allows attackers to bypass restrictions by providing the hostname twice at the "host: " prompt.
CVE-1999-1390(PUBLISHED):suidexec in suidmanager 0.18 on Debian 2.0 allows local users to gain root privileges by specifying a malicious program on the command line.
CVE-1999-1391(PUBLISHED):Vulnerability in NeXT 1.0a and 1.0 with publicly accessible printers allows local users to gain privileges via a combination of the npd program and weak directory permissions.
CVE-1999-1392(PUBLISHED):Vulnerability in restore0.9 installation script in NeXT 1.0a and 1.0 allows local users to gain root privileges.
CVE-1999-1393(PUBLISHED):Control Panel "Password Security" option for Apple Powerbooks allows attackers with physical access to the machine to bypass the security by booting it with an emergency startup disk and using a disk editor to modify the on/off toggle or password in the aaaaaaaAPWD file, which is normally inaccessible.
CVE-1999-1394(PUBLISHED):BSD 4.4 based operating systems, when running at security level 1, allow the root user to clear the immutable and append-only flags for files by unmounting the file system and using a file system editor such as fsdb to directly modify the file through a device.
CVE-1999-1395(PUBLISHED):Vulnerability in Monitor utility (SYS$SHARE:SPISHR.EXE) in VMS 5.0 through 5.4-2 allows local users to gain privileges.
CVE-1999-1396(PUBLISHED):Vulnerability in integer multiplication emulation code on SPARC architectures for SunOS 4.1 through 4.1.2 allows local users to gain root access or cause a denial of service (crash).
CVE-1999-1397(PUBLISHED):Index Server 2.0 on IIS 4.0 stores physical path information in the ContentIndex\Catalogs subkey of the AllowedPaths registry key, whose permissions allows local and remote users to obtain the physical paths of directories that are being indexed.
CVE-1999-1398(PUBLISHED):Vulnerability in xfsdump in SGI IRIX may allow local users to obtain root privileges via the bck.log log file, possibly via a symlink attack.
CVE-1999-1399(PUBLISHED):spaceball program in SpaceWare 7.3 v1.0 in IRIX 6.2 allows local users to gain root privileges by setting the HOSTNAME environmental variable to contain the commands to be executed.
CVE-1999-1400(PUBLISHED):The Economist screen saver 1999 with the "Password Protected" option enabled allows users with physical access to the machine to bypass the screen saver and read files by running Internet Explorer while the screen is still locked.
CVE-1999-1401(PUBLISHED):Vulnerability in Desktop searchbook program in IRIX 5.0.x through 6.2 sets insecure permissions for certain user files (iconbook and searchbook).
CVE-1999-1402(PUBLISHED):The access permissions for a UNIX domain socket are ignored in Solaris 2.x and SunOS 4.x, and other BSD-based operating systems before 4.4, which could allow local users to connect to the socket and possibly disrupt or control the operations of the program using that socket.
CVE-1999-1403(PUBLISHED):IBM/Tivoli OPC Tracker Agent version 2 release 1 creates files, directories, and IPC message queues with insecure permissions (world-readable and world-writable), which could allow local users to disrupt operations and possibly gain privileges by modifying or deleting files.
CVE-1999-1404(PUBLISHED):IBM/Tivoli OPC Tracker Agent version 2 release 1 allows remote attackers to cause a denial of service (resource exhaustion) via malformed data to the localtracker client port (5011), which prevents the connection from being closed properly.
CVE-1999-1405(PUBLISHED):snap command in AIX before 4.3.2 creates the /tmp/ibmsupt directory with world-readable permissions and does not remove or clear the directory when snap -a is executed, which could allow local users to access the shadowed password file by creating /tmp/ibmsupt/general/passwd before root runs snap -a.
CVE-1999-1406(PUBLISHED):dumpreg in Red Hat Linux 5.1 opens /dev/mem with O_RDWR access, which allows local users to cause a denial of service (crash) by redirecting fd 1 (stdout) to the kernel.
CVE-1999-1407(PUBLISHED):ifdhcpc-done script for configuring DHCP on Red Hat Linux 5 allows local users to append text to arbitrary files via a symlink attack on the dhcplog file.
CVE-1999-1408(PUBLISHED):Vulnerability in AIX 4.1.4 and HP-UX 10.01 and 9.05 allows local users to cause a denial of service (crash) by using a socket to connect to a port on the localhost, calling shutdown to clear the socket, then using the same socket to connect to a different port on localhost.
CVE-1999-1409(PUBLISHED):The at program in IRIX 6.2 and NetBSD 1.3.2 and earlier allows local users to read portions of arbitrary files by submitting the file to at with the -f argument, which generates error messages that at sends to the user via e-mail.
CVE-1999-1410(PUBLISHED):addnetpr in IRIX 5.3 and 6.2 allows local users to overwrite arbitrary files and possibly gain root privileges via a symlink attack on the printers temporary file.
CVE-1999-1411(PUBLISHED):The installation of the fsp package 2.71-10 in Debian GNU/Linux 2.0 adds the anonymous FTP user without notifying the administrator, which could automatically enable anonymous FTP on some servers such as wu-ftp.
CVE-1999-1412(PUBLISHED):A possible interaction between Apple MacOS X release 1.0 and Apache HTTP server allows remote attackers to cause a denial of service (crash) via a flood of HTTP GET requests to CGI programs, which generates a large number of processes.
CVE-1999-1413(PUBLISHED):Solaris 2.4 before kernel jumbo patch -35 allows set-gid programs to dump core even if the real user id is not in the set-gid group, which allows local users to overwrite or create files at higher privileges by causing a core dump, e.g. through dmesg.
CVE-1999-1414(PUBLISHED):IBM Netfinity Remote Control allows local users to gain administrator privileges by starting programs from the process manager, which runs with system level privileges.
CVE-1999-1415(PUBLISHED):Vulnerability in /usr/bin/mail in DEC ULTRIX before 4.2 allows local users to gain privileges.
CVE-1999-1416(PUBLISHED):AnswerBook2 (AB2) web server dwhttpd 3.1a4 allows remote attackers to cause a denial of service (resource exhaustion) via an HTTP POST request with a large content-length.
CVE-1999-1417(PUBLISHED):Format string vulnerability in AnswerBook2 (AB2) web server dwhttpd 3.1a4 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via encoded % characters in an HTTP request, which is improperly logged.
CVE-1999-1418(PUBLISHED):ICQ99 ICQ web server build 1701 with "Active Homepage" enabled generates allows remote attackers to determine the existence of files on the server by comparing server responses when a file exists ("404 Forbidden") versus when a file does not exist ("404 not found").
CVE-1999-1419(PUBLISHED):Buffer overflow in nss_nisplus.so.1 library in NIS+ in Solaris 2.3 and 2.4 allows local users to gain root privileges.
CVE-1999-1420(PUBLISHED):NBase switches NH2012, NH2012R, NH2015, and NH2048 have a back door password that cannot be disabled, which allows remote attackers to modify the switch's configuration.
CVE-1999-1421(PUBLISHED):NBase switches NH208 and NH215 run a TFTP server which allows remote attackers to send software updates to modify the switch or cause a denial of service (crash) by guessing the target filenames, which have default names.
CVE-1999-1422(PUBLISHED):The default configuration of Slackware 3.4, and possibly other versions, includes . (dot, the current directory) in the PATH environmental variable, which could allow local users to create Trojan horse programs that are inadvertently executed by other users.
CVE-1999-1423(PUBLISHED):ping in Solaris 2.3 through 2.6 allows local users to cause a denial of service (crash) via a ping request to a multicast address through the loopback interface, e.g. via ping -i.
CVE-1999-1424(PUBLISHED):Solaris Solstice AdminSuite (AdminSuite) 2.1 uses unsafe permissions when adding new users to the NIS+ password table, which allows local users to gain root access by modifying their password table entries.
CVE-1999-1425(PUBLISHED):Solaris Solstice AdminSuite (AdminSuite) 2.1 incorrectly sets write permissions on source files for NIS maps, which could allow local users to gain privileges by modifying /etc/passwd.
CVE-1999-1426(PUBLISHED):Solaris Solstice AdminSuite (AdminSuite) 2.1 follows symbolic links when updating an NIS database, which allows local users to overwrite arbitrary files.
CVE-1999-1427(PUBLISHED):Solaris Solstice AdminSuite (AdminSuite) 2.1 and 2.2 create lock files insecurely, which allows local users to gain root privileges.
CVE-1999-1428(PUBLISHED):Solaris Solstice AdminSuite (AdminSuite) 2.1 and 2.2 allows local users to gain privileges via the save option in the Database Manager, which is running with setgid bin privileges.
CVE-1999-1429(PUBLISHED):DIT TransferPro installs devices with world-readable and world-writable permissions, which could allow local users to damage disks through the ff device driver.
CVE-1999-1430(PUBLISHED):PIM software for Royal daVinci does not properly password-protext access to data stored in the .mdb (Microsoft Access) file, which allows local users to read the data without a password by directly accessing the files with a different application, such as Access.
CVE-1999-1431(PUBLISHED):ZAK in Appstation mode allows users to bypass the "Run only allowed apps" policy by starting Explorer from Office 97 applications (such as Word), installing software into the TEMP directory, and changing the name to that for an allowed application, such as Winword.exe.
CVE-1999-1432(PUBLISHED):Power management (Powermanagement) on Solaris 2.4 through 2.6 does not start the xlock process until after the sys-suspend has completed, which allows an attacker with physical access to input characters to the last active application from the keyboard for a short period after the system is restoring, which could lead to increased privileges.
CVE-1999-1433(PUBLISHED):HP JetAdmin D.01.09 on Solaris allows local users to change the permissions of arbitrary files via a symlink attack on the /tmp/jetadmin.log file.
CVE-1999-1434(PUBLISHED):login in Slackware Linux 3.2 through 3.5 does not properly check for an error when the /etc/group file is missing, which prevents it from dropping privileges, causing it to assign root privileges to any local user who logs on to the server.
CVE-1999-1435(PUBLISHED):Buffer overflow in libsocks5 library of Socks 5 (socks5) 1.0r5 allows local users to gain privileges via long environmental variables.
CVE-1999-1436(PUBLISHED):Ray Chan WWW Authorization Gateway 0.1 CGI program allows remote attackers to execute arbitrary commands via shell metacharacters in the "user" parameter.
CVE-1999-1437(PUBLISHED):ePerl 2.2.12 allows remote attackers to read arbitrary files and possibly execute certain commands by specifying a full pathname of the target file as an argument to bar.phtml.
CVE-1999-1438(PUBLISHED):Vulnerability in /bin/mail in SunOS 4.1.1 and earlier allows local users to gain root privileges via certain command line arguments.
CVE-1999-1439(PUBLISHED):gcc 2.7.2 allows local users to overwrite arbitrary files via a symlink attack on temporary .i, .s, or .o files.
CVE-1999-1440(PUBLISHED):Win32 ICQ 98a 1.30, and possibly other versions, does not display the entire portion of long filenames, which could allow attackers to send an executable file with a long name that contains so many spaces that the .exe extension is not displayed, which could make the user believe that the file is safe to open from the client.
CVE-1999-1441(PUBLISHED):Linux 2.0.34 does not properly prevent users from sending SIGIO signals to arbitrary processes, which allows local users to cause a denial of service by sending SIGIO to processes that do not catch it.
CVE-1999-1442(PUBLISHED):Bug in AMD K6 processor on Linux 2.0.x and 2.1.x kernels allows local users to cause a denial of service (crash) via a particular sequence of instructions, possibly related to accessing addresses outside of segments.
CVE-1999-1443(PUBLISHED):Micah Software Full Armor Network Configurator and Zero Administration allow local users with physical access to bypass the desktop protection by (1) using <CTRL><ALT><DEL> and kill the process using the task manager, (2) booting the system from a separate disk, or (3) interrupting certain processes that execute while the system is booting.
CVE-1999-1444(PUBLISHED):genkey utility in Alibaba 2.0 generates RSA key pairs with an exponent of 1, which results in transactions that are sent in cleartext.
CVE-1999-1445(PUBLISHED):Vulnerability in imapd and ipop3d in Slackware 3.4 and 3.3 with shadowing enabled, and possibly other operating systems, allows remote attackers to cause a core dump via a short sequence of USER and PASS commands that do not provide valid usernames or passwords.
CVE-1999-1446(PUBLISHED):Internet Explorer 3 records a history of all URL's that are visited by a user in DAT files located in the Temporary Internet Files and History folders, which are not cleared when the user selects the "Clear History" option, and are not visible when the user browses the folders because of tailored displays.
CVE-1999-1447(PUBLISHED):Internet Explorer 4.0 allows remote attackers to cause a denial of service (crash) via HTML code that contains a long CLASSID parameter in an OBJECT tag.
CVE-1999-1448(PUBLISHED):Eudora and Eudora Light before 3.05 allows remote attackers to cause a crash and corrupt the user's mailbox via an e-mail message with certain dates, such as (1) dates before 1970, which cause a Divide By Zero error, or (2) dates that are 100 years after the current date, which causes a segmentation fault.
CVE-1999-1449(PUBLISHED):SunOS 4.1.4 on a Sparc 20 machine allows local users to cause a denial of service (kernel panic) by reading from the /dev/tcx0 TCX device.
CVE-1999-1450(PUBLISHED):Vulnerability in (1) rlogin daemon rshd and (2) scheme on SCO UNIX OpenServer 5.0.5 and earlier, and SCO UnixWare 7.0.1 and earlier, allows remote attackers to gain privileges.
CVE-1999-1451(PUBLISHED):The Winmsdp.exe sample file in IIS 4.0 and Site Server 3.0 allows remote attackers to read arbitrary files.
CVE-1999-1452(PUBLISHED):GINA in Windows NT 4.0 allows attackers with physical access to display a portion of the clipboard of the user who has locked the workstation by pasting (CTRL-V) the contents into the username prompt.
CVE-1999-1453(PUBLISHED):Internet Explorer 4 allows remote attackers (malicious web site operators) to read the contents of the clipboard via the Internet WebBrowser ActiveX object.
CVE-1999-1454(PUBLISHED):Macromedia "The Matrix" screen saver on Windows 95 with the "Password protected" option enabled allows attackers with physical access to the machine to bypass the password prompt by pressing the ESC (Escape) key.
CVE-1999-1455(PUBLISHED):RSH service utility RSHSVC in Windows NT 3.5 through 4.0 does not properly restrict access as specified in the .Rhosts file when a user comes from an authorized host, which could allow unauthorized users to access the service by logging in from an authorized host.
CVE-1999-1456(PUBLISHED):thttpd HTTP server 2.03 and earlier allows remote attackers to read arbitrary files via a GET request with more than one leading / (slash) character in the filename.
CVE-1999-1457(PUBLISHED):Buffer overflow in thttpd HTTP server before 2.04-31 allows remote attackers to execute arbitrary commands via a long date string, which is not properly handled by the tdate_parse function.
CVE-1999-1458(PUBLISHED):Buffer overflow in at program in Digital UNIX 4.0 allows local users to gain root privileges via a long command line argument.
CVE-1999-1459(PUBLISHED):BMC PATROL Agent before 3.2.07 allows local users to gain root privileges via a symlink attack on a temporary file.
CVE-1999-1460(PUBLISHED):BMC PATROL SNMP Agent before 3.2.07 allows local users to create arbitrary world-writeable files as root by specifying the target file as the second argument to the snmpmagt program.
CVE-1999-1461(PUBLISHED):inpview in InPerson on IRIX 5.3 through IRIX 6.5.10 trusts the PATH environmental variable to find and execute the ttsession program, which allows local users to obtain root access by modifying the PATH to point to a Trojan horse ttsession program.
CVE-1999-1462(PUBLISHED):Vulnerability in bb-hist.sh CGI History module in Big Brother 1.09b and 1.09c allows remote attackers to read portions of arbitrary files.
CVE-1999-1463(PUBLISHED):Windows NT 4.0 before SP3 allows remote attackers to bypass firewall restrictions or cause a denial of service (crash) by sending improperly fragmented IP packets without the first fragment, which the TCP/IP stack incorrectly reassembles into a valid session.
CVE-1999-1464(PUBLISHED):Vulnerability in Cisco IOS 11.1CC and 11.1CT with distributed fast switching (DFS) enabled allows remote attackers to bypass certain access control lists when the router switches traffic from a DFS-enabled interface to an interface that does not have DFS enabled, as described by Cisco bug CSCdk35564.
CVE-1999-1465(PUBLISHED):Vulnerability in Cisco IOS 11.1 through 11.3 with distributed fast switching (DFS) enabled allows remote attackers to bypass certain access control lists when the router switches traffic from a DFS-enabled input interface to an output interface with a logical subinterface, as described by Cisco bug CSCdk43862.
CVE-1999-1466(PUBLISHED):Vulnerability in Cisco routers versions 8.2 through 9.1 allows remote attackers to bypass access control lists when extended IP access lists are used on certain interfaces, the IP route cache is enabled, and the access list uses the "established" keyword.
CVE-1999-1467(PUBLISHED):Vulnerability in rcp on SunOS 4.0.x allows remote attackers from trusted hosts to execute arbitrary commands as root, possibly related to the configuration of the nobody user.
CVE-1999-1468(PUBLISHED):rdist in various UNIX systems uses popen to execute sendmail, which allows local users to gain root privileges by modifying the IFS (Internal Field Separator) variable.
CVE-1999-1469(PUBLISHED):Buffer overflow in w3-auth CGI program in miniSQL package allows remote attackers to execute arbitrary commands via an HTTP request with (1) a long URL, or (2) a long User-Agent MIME header.
CVE-1999-1470(PUBLISHED):Eastman Work Management 3.21 stores passwords in cleartext in the COMMON and LOCATOR registry keys, which could allow local users to gain privileges.
CVE-1999-1471(PUBLISHED):Buffer overflow in passwd in BSD based operating systems 4.3 and earlier allows local users to gain root privileges by specifying a long shell or GECOS field.
CVE-1999-1472(PUBLISHED):Internet Explorer 4.0 allows remote attackers to read arbitrary text and HTML files on the user's machine via a small IFRAME that uses Dynamic HTML (DHTML) to send the data to the attacker, aka the Freiburg text-viewing issue.
CVE-1999-1473(PUBLISHED):When a Web site redirects the browser to another site, Internet Explorer 3.02 and 4.0 automatically resends authentication information to the second site, aka the "Page Redirect Issue."
CVE-1999-1474(PUBLISHED):PowerPoint 95 and 97 allows remote attackers to cause an application to be run automatically without prompting the user, possibly through the slide show, when the document is opened in browsers such as Internet Explorer.
CVE-1999-1475(PUBLISHED):ProFTPd 1.2 compiled with the mod_sqlpw module records user passwords in the wtmp log file, which allows local users to obtain the passwords and gain privileges by reading wtmp, e.g. via the last command.
CVE-1999-1476(PUBLISHED):A bug in Intel Pentium processor (MMX and Overdrive) allows local users to cause a denial of service (hang) in Intel-based operating systems such as Windows NT and Windows 95, via an invalid instruction, aka the "Invalid Operand with Locked CMPXCHG8B Instruction" problem.
CVE-1999-1477(PUBLISHED):Buffer overflow in GNOME libraries 1.0.8 allows local user to gain root access via a long --espeaker argument in programs such as nethack.
CVE-1999-1478(PUBLISHED):The Sun HotSpot Performance Engine VM allows a remote attacker to cause a denial of service on any server running HotSpot via a URL that includes the [ character.
CVE-1999-1479(PUBLISHED):The textcounter.pl by Matt Wright allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-1999-1480(PUBLISHED):(1) acledit and (2) aclput in AIX 4.3 allow local users to create or modify files via a symlink attack.
CVE-1999-1481(PUBLISHED):Squid 2.2.STABLE5 and below, when using external authentication, allows attackers to bypass access controls via a newline in the user/password pair.
CVE-1999-1482(PUBLISHED):SVGAlib zgv 3.0-7 and earlier allows local users to gain root access via a privilege leak of the iopl(3) privileges to child processes.
CVE-1999-1483(PUBLISHED):Buffer overflow in zgv in svgalib 1.2.10 and earlier allows local users to execute arbitrary code via a long HOME environment variable.
CVE-1999-1484(PUBLISHED):Buffer overflow in MSN Setup BBS 4.71.0.10 ActiveX control (setupbbs.ocx) allows a remote attacker to execute arbitrary commands via the methods (1) vAddNewsServer or (2) bIsNewsServerConfigured.
CVE-1999-1485(PUBLISHED):nsd in IRIX 6.5 through 6.5.2 exports a virtual filesystem on a UDP port, which allows remote attackers to view files and cause a possible denial of service by mounting the nsd virtual file system.
CVE-1999-1486(PUBLISHED):sadc in IBM AIX 4.1 through 4.3, when called from programs such as timex that are setgid adm, allows local users to overwrite arbitrary files via a symlink attack.
CVE-1999-1487(PUBLISHED):Vulnerability in digest in AIX 4.3 allows printq users to gain root privileges by creating and/or modifing any file on the system.
CVE-1999-1488(PUBLISHED):sdrd daemon in IBM SP2 System Data Repository (SDR) allows remote attackers to read files without authentication.
CVE-1999-1489(PUBLISHED):Buffer overflow in TestChip function in XFree86 SuperProbe in Slackware Linux 3.1 allows local users to gain root privileges via a long -nopr argument.
CVE-1999-1490(PUBLISHED):xosview 1.5.1 in Red Hat 5.1 allows local users to gain root access via a long HOME environmental variable.
CVE-1999-1491(PUBLISHED):abuse.console in Red Hat 2.1 uses relative pathnames to find and execute the undrv program, which allows local users to execute arbitrary commands via a path that points to a Trojan horse program.
CVE-1999-1492(PUBLISHED):Vulnerability in (1) diskperf and (2) diskalign in IRIX 6.4 allows local attacker to create arbitrary root owned files, leading to root privileges.
CVE-1999-1493(PUBLISHED):Vulnerability in crp in Hewlett Packard Apollo Domain OS SR10 through SR10.3 allows remote attackers to gain root privileges via insecure system calls, (1) pad_$dm_cmd and (2) pad_$def_pfk().
CVE-1999-1494(PUBLISHED):colorview in Silicon Graphics IRIX 5.1, 5.2, and 6.0 allows local attackers to read arbitrary files via the -text argument.
CVE-1999-1495(PUBLISHED):xtvscreen in SuSE Linux 6.0 allows local users to overwrite arbitrary files via a symlink attack on the pic000.pnm file.
CVE-1999-1496(PUBLISHED):Sudo 1.5 in Debian Linux 2.1 and Red Hat 6.0 allows local users to determine the existence of arbitrary files by attempting to execute the target filename as a program, which generates a different error message when the file does not exist.
CVE-1999-1497(PUBLISHED):Ipswitch IMail 5.0 and 6.0 uses weak encryption to store passwords in registry keys, which allows local attackers to read passwords for e-mail accounts.
CVE-1999-1498(PUBLISHED):Slackware Linux 3.4 pkgtool allows local attacker to read and write to arbitrary files via a symlink attack on the reply file.
CVE-1999-1499(PUBLISHED):named in ISC BIND 4.9 and 8.1 allows local users to destroy files via a symlink attack on (1) named_dump.db when root kills the process with a SIGINT, or (2) named.stats when SIGIOT is used.
CVE-1999-1500(PUBLISHED):Internet Anywhere POP3 Mail Server 2.3.1 allows remote attackers to cause a denial of service (crash) via (1) LIST, (2) TOP, or (3) UIDL commands using letters as arguments.
CVE-1999-1501(PUBLISHED):(1) ipxchk and (2) ipxlink in SGI OS2 IRIX 6.3 does not properly clear the IFS environmental variable before executing system calls, which allows local users to execute arbitrary commands.
CVE-1999-1502(PUBLISHED):Buffer overflows in Quake 1.9 client allows remote malicious servers to execute arbitrary commands via long (1) precache paths, (2) server name, (3) server address, or (4) argument to the map console command.
CVE-1999-1503(PUBLISHED):Network Flight Recorder (NFR) 1.5 and 1.6 allows remote attackers to cause a denial of service in nfrd (crash) via a TCP packet with a null header and data field.
CVE-1999-1504(PUBLISHED):Stalker Internet Mail Server 1.6 allows a remote attacker to cause a denial of service (crash) via a long HELO command.
CVE-1999-1505(PUBLISHED):Buffer overflow in QuakeWorld 2.10 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary commands via a long initial connect packet.
CVE-1999-1506(PUBLISHED):Vulnerability in SMI Sendmail 4.0 and earlier, on SunOS up to 4.0.3, allows remote attackers to access user bin.
CVE-1999-1507(PUBLISHED):Sun SunOS 4.1 through 4.1.3 allows local attackers to gain root access via insecure permissions on files and directories such as crash.
CVE-1999-1508(PUBLISHED):Web server in Tektronix PhaserLink Printer 840.0 and earlier allows a remote attacker to gain administrator access by directly calling undocumented URLs such as ncl_items.html and ncl_subjects.html.
CVE-1999-1509(PUBLISHED):Directory traversal vulnerability in Etype Eserv 2.50 web server allows a remote attacker to read any file in the file system via a .. (dot dot) in a URL.
CVE-1999-1510(PUBLISHED):Buffer overflows in Bisonware FTP server prior to 4.1 allow remote attackers to cause a denial of service, and possibly execute arbitrary commands, via long (1) USER, (2) LIST, or (3) CWD commands.
CVE-1999-1511(PUBLISHED):Buffer overflows in Xtramail 1.11 allow attackers to cause a denial of service (crash) and possibly execute arbitrary commands via (1) a long PASS command in the POP3 service, (2) a long HELO command in the SMTP service, or (3) a long user name in the Control Service.
CVE-1999-1512(PUBLISHED):The AMaViS virus scanner 0.2.0-pre4 and earlier allows remote attackers to execute arbitrary commands as root via an infected mail message with shell metacharacters in the reply-to field.
CVE-1999-1513(PUBLISHED):Management information base (MIB) for a 3Com SuperStack II hub running software version 2.10 contains an object identifier (.1.3.6.1.4.1.43.10.4.2) that is accessible by a read-only community string, but lists the entire table of community strings, which could allow attackers to conduct unauthorized activities.
CVE-1999-1514(PUBLISHED):Buffer overflow in Celtech ExpressFS FTP server 2.x allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long USER command.
CVE-1999-1515(PUBLISHED):A non-default configuration in TenFour TFS Gateway 4.0 allows an attacker to cause a denial of service via messages with incorrect sender and recipient addresses, which causes the gateway to continuously try to return the message every 10 seconds.
CVE-1999-1516(PUBLISHED):A buffer overflow in TenFour TFS Gateway SMTP mail server 3.2 allows an attacker to crash the mail server and possibly execute arbitrary code by offering more than 128 bytes in a MAIL FROM string.
CVE-1999-1517(PUBLISHED):runtar in the Amanda backup system used in various UNIX operating systems executes tar with root privileges, which allows a user to overwrite or read arbitrary files by providing the target files to runtar.
CVE-1999-1518(PUBLISHED):Operating systems with shared memory implementations based on BSD 4.4 code allow a user to conduct a denial of service and bypass memory limits (e.g., as specified with rlimits) using mmap or shmget to allocate memory and cause page faults.
CVE-1999-1519(PUBLISHED):Gene6 G6 FTP Server 2.0 allows a remote attacker to cause a denial of service (resource exhaustion) via a long (1) user name or (2) password.
CVE-1999-1520(PUBLISHED):A configuration problem in the Ad Server Sample directory (AdSamples) in Microsoft Site Server 3.0 allows an attacker to obtain the SITE.CSC file, which exposes sensitive SQL database information.
CVE-1999-1521(PUBLISHED):Computalynx CMail 2.4 and CMail 2.3 SP2 SMTP servers are vulnerable to a buffer overflow attack in the MAIL FROM command that may allow a remote attacker to execute arbitrary code on the server.
CVE-1999-1522(PUBLISHED):Vulnerability in htmlparse.pike in Roxen Web Server 1.3.11 and earlier, possibly related to recursive parsing and referer tags in RXML.
CVE-1999-1523(PUBLISHED):Buffer overflow in Sambar Web Server 4.2.1 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long HTTP GET request.
CVE-1999-1524(PUBLISHED):FlowPoint DSL router firmware versions prior to 3.0.8 allows a remote attacker to exploit a password recovery feature from the network and conduct brute force password guessing, instead of limiting the feature to the serial console port.
CVE-1999-1525(PUBLISHED):Macromedia Shockwave before 6.0 allows a malicious webmaster to read a user's mail box and possibly access internal web servers via the GetNextText command on a Shockwave movie.
CVE-1999-1526(PUBLISHED):Auto-update feature of Macromedia Shockwave 7 transmits a user's password and hard disk information back to Macromedia.
CVE-1999-1527(PUBLISHED):Internal HTTP server in Sun Netbeans Java IDE in Netbeans Developer 3.0 Beta and Forte Community Edition 1.0 Beta does not properly restrict access to IP addresses as specified in its configuration, which allows arbitrary remote attackers to access the server.
CVE-1999-1528(PUBLISHED):ProSoft Netware Client 5.12 on Macintosh MacOS 9 does not automatically log a user out of the NDS tree when the user logs off the system, which allows other users of the same system access to the unprotected NDS session.
CVE-1999-1529(PUBLISHED):A buffer overflow exists in the HELO command in Trend Micro Interscan VirusWall SMTP gateway 3.23/3.3 for NT, which may allow an attacker to execute arbitrary code.
CVE-1999-1530(PUBLISHED):cgiwrap as used on Cobalt RaQ 2.0 and RaQ 3i does not properly identify the user for running certain scripts, which allows a malicious site administrator to view or modify data located at another virtual site on the same system.
CVE-1999-1531(PUBLISHED):Buffer overflow in IBM HomePagePrint 1.0.7 for Windows98J allows a malicious Web site to execute arbitrary code on a viewer's system via a long IMG_SRC HTML tag.
CVE-1999-1532(PUBLISHED):Netscape Messaging Server 3.54, 3.55, and 3.6 allows a remote attacker to cause a denial of service (memory exhaustion) via a series of long RCPT TO commands.
CVE-1999-1533(PUBLISHED):Eicon Technology Diva LAN ISDN modem allows a remote attacker to cause a denial of service (hang) via a long password argument to the login.htm file in its HTTP service.
CVE-1999-1534(PUBLISHED):Buffer overflow in (1) nlservd and (2) rnavc in Knox Software Arkeia backup product allows local users to obtain root access via a long HOME environmental variable.
CVE-1999-1535(PUBLISHED):Buffer overflow in AspUpload.dll in Persits Software AspUpload before ******* allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long argument in the HTTP request.
CVE-1999-1536(PUBLISHED):.sbstart startup script in AcuShop Salesbuilder is world writable, which allows local users to gain privileges by appending commands to the file.
CVE-1999-1537(PUBLISHED):IIS 3.x and 4.x does not distinguish between pages requiring encryption and those that do not, which allows remote attackers to cause a denial of service (resource exhaustion) via SSL requests to the HTTPS port for normally unencrypted files, which will cause IIS to perform extra work to send the files over SSL.
CVE-1999-1538(PUBLISHED):When IIS 2 or 3 is upgraded to IIS 4, ism.dll is inadvertently left in /scripts/iisadmin, which does not restrict access to the local machine and allows an unauthorized user to gain access to sensitive server information, including the Administrator's password.
CVE-1999-1539(PUBLISHED):Buffer overflow in FTP server in QPC Software's QVT/Term Plus versions 4.2d and 4.3 and QVT/Net 4.3 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long (1) user name or (2) password.
CVE-1999-1540(PUBLISHED):shell-lock in Cactus Software Shell Lock uses weak encryption (trivial encoding) which allows attackers to easily decrypt and obtain the source code.
CVE-1999-1541(PUBLISHED):shell-lock in Cactus Software Shell Lock allows local users to read or modify decoded shell files before they are executed, via a symlink attack on a temporary file.
CVE-1999-1542(PUBLISHED):RPMMail before 1.4 allows remote attackers to execute commands via an e-mail message with shell metacharacters in the "MAIL FROM" command.
CVE-1999-1543(PUBLISHED):MacOS uses weak encryption for passwords that are stored in the Users & Groups Data File.
CVE-1999-1544(PUBLISHED):Buffer overflow in FTP server in Microsoft IIS 3.0 and 4.0 allows local and sometimes remote attackers to cause a denial of service via a long NLST (ls) command.
CVE-1999-1545(PUBLISHED):Joe's Own Editor (joe) 2.8 sets the world-readable permission on its crash-save file, DEADJOE, which could allow local users to read files that were being edited by other users.
CVE-1999-1546(PUBLISHED):netstation.navio-com.rte ******* configuration script for Navio NC on IBM AIX exports /tmp over NFS as world-readable and world-writable.
CVE-1999-1547(PUBLISHED):Oracle Web Listener 2.1 allows remote attackers to bypass access restrictions by replacing a character in the URL with its HTTP-encoded (hex) equivalent.
CVE-1999-1548(PUBLISHED):Cabletron SmartSwitch Router (SSR) 8000 firmware 2.x can only handle 200 ARP requests per second allowing a denial of service attack to succeed with a flood of ARP requests exceeding that limit.
CVE-1999-1549(PUBLISHED):Lynx 2.x does not properly distinguish between internal and external HTML, which may allow a local attacker to read a "secure" hidden form value from a temporary file and craft a LYNXOPTIONS: URL that causes Lynx to modify the user's configuration file and execute commands.
CVE-1999-1550(PUBLISHED):bigconf.conf in F5 BIG/ip 2.1.2 and earlier allows remote attackers to read arbitrary files by specifying the target file in the "file" parameter.
CVE-1999-1551(PUBLISHED):Buffer overflow in Ipswitch IMail Service 5.0 allows an attacker to cause a denial of service (crash) and possibly execute arbitrary commands via a long URL.
CVE-1999-1552(PUBLISHED):dpsexec (DPS Server) when running under XDM in IBM AIX 3.2.5 and earlier does not properly check privileges, which allows local users to overwrite arbitrary files and gain privileges.
CVE-1999-1553(PUBLISHED):Buffer overflow in XCmail 0.99.6 with autoquote enabled allows remote attackers to execute arbitrary commands via a long subject line.
CVE-1999-1554(PUBLISHED):/usr/sbin/Mail on SGI IRIX 3.3 and 3.3.1 does not properly set the group ID to the group ID of the user who started Mail, which allows local users to read the mail of other users.
CVE-1999-1555(PUBLISHED):Cheyenne InocuLAN Anti-Virus Server in Inoculan 4.0 before Service Pack 2 creates an update directory with "EVERYONE FULL CONTROL" permissions, which allows local users to cause Inoculan's antivirus update feature to install a Trojan horse dll.
CVE-1999-1556(PUBLISHED):Microsoft SQL Server 6.5 uses weak encryption for the password for the SQLExecutiveCmdExec account and stores it in an accessible portion of the registry, which could allow local users to gain privileges by reading and decrypting the CmdExecAccount value.
CVE-1999-1557(PUBLISHED):Buffer overflow in the login functions in IMAP server (imapd) in Ipswitch IMail 5.0 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via (1) a long user name or (2) a long password.
CVE-1999-1558(PUBLISHED):Vulnerability in loginout in Digital OpenVMS 7.1 and earlier allows unauthorized access when external authentication is enabled.
CVE-1999-1559(PUBLISHED):Xylan OmniSwitch before 3.2.6 allows remote attackers to bypass the login prompt via a CTRL-D (control d) character, which locks other users out of the switch because it only supports one session at a time.
CVE-1999-1560(PUBLISHED):Vulnerability in a script in Texas A&M University (TAMU) Tiger allows local users to execute arbitrary commands as the Tiger user, usually root.
CVE-1999-1561(PUBLISHED):Nullsoft SHOUTcast server stores the administrative password in plaintext in a configuration file (sc_serv.conf), which could allow a local user to gain administrative privileges on the server.
CVE-1999-1562(PUBLISHED):gFTP FTP client 1.13, and other versions before 2.0.0, records a password in plaintext in (1) the log window, or (2) in a log file.
CVE-1999-1563(PUBLISHED):Nachuatec D435 and D445 printer allows remote attackers to cause a denial of service via ICMP redirect storm.
CVE-1999-1564(PUBLISHED):FreeBSD 3.2 and possibly other versions allows a local user to cause a denial of service (panic) with a large number accesses of an NFS v3 mounted directory from a large number of processes.
CVE-1999-1565(PUBLISHED):Man2html 2.1 and earlier allows local users to overwrite arbitrary files via a symlink attack on a temporary file.
CVE-1999-1566(PUBLISHED):Buffer overflow in iParty server 1.2 and earlier allows remote attackers to cause a denial of service (crash) by connecting to default port 6004 and sending repeated extended characters.
CVE-1999-1567(PUBLISHED):Seapine Software TestTrack server allows a remote attacker to cause a denial of service (high CPU) via (1) TestTrackWeb.exe and (2) ttcgi.exe by connecting to port 99 and disconnecting without sending any data.
CVE-1999-1568(PUBLISHED):Off-by-one error in NcFTPd FTP server before 2.4.1 allows a remote attacker to cause a denial of service (crash) via a long PORT command.
CVE-1999-1569(PUBLISHED):Quake 1 and NetQuake servers allow remote attackers to cause a denial of service (resource exhaustion or forced disconnection) via a flood of spoofed UDP connection packets, which exceeds the server's player limit.
CVE-1999-1570(PUBLISHED):Buffer overflow in sar for OpenServer 5.0.5 allows local users to gain root privileges via a long -o parameter.
CVE-1999-1571(PUBLISHED):Buffer overflow in sar for SCO OpenServer 5.0.0 through 5.0.5 may allow local users to gain root privileges via a long -f parameter, a different vulnerability than CVE-1999-1570.
CVE-1999-1572(PUBLISHED):cpio on FreeBSD 2.1.0, Debian GNU/Linux 3.0, and possibly other operating systems, uses a 0 umask when creating files using the -O (archive) or -F options, which creates the files with mode 0666 and allows local users to read or overwrite those files.
CVE-1999-1573(PUBLISHED):Multiple unknown vulnerabilities in the "r-cmnds" (1) remshd, (2) rexecd, (3) rlogind, (4) rlogin, (5) remsh, (6) rcp, (7) rexec, and (8) rdist for HP-UX 10.00 through 11.00 allow attackers to gain privileges or access files.
CVE-1999-1574(PUBLISHED):Buffer overflow in the lex routines of nslookup for AIX 4.3 may allow attackers to cause a core dump and possibly execute arbitrary code via "long input strings."
CVE-1999-1575(PUBLISHED):The Kodak/Wang (1) Image Edit (imgedit.ocx), (2) Image Annotation (imgedit.ocx), (3) Image Scan (imgscan.ocx), (4) Thumbnail Image (imgthumb.ocx), (5) Image Admin (imgadmin.ocx), (6) HHOpen (hhopen.ocx), (7) Registration Wizard (regwizc.dll), and (8) IE Active Setup (setupctl.dll) ActiveX controls for Internet Explorer (IE) 4.01 and 5.0 are marked as "Safe for Scripting," which allows remote attackers to create and modify files and execute arbitrary commands.
CVE-1999-1576(PUBLISHED):Buffer overflow in Adobe Acrobat ActiveX control (pdf.ocx, PDF.PdfCtrl.1) 1.3.188 for Acrobat Reader 4.0 allows remote attackers to execute arbitrary code via the pdf.setview method.
CVE-1999-1577(PUBLISHED):Buffer overflow in HHOpen ActiveX control (hhopen.ocx) ******* for Internet Explorer 4.01 and 5 allows remote attackers to execute arbitrary commands via long arguments to the OpenHelp method.
CVE-1999-1578(PUBLISHED):Buffer overflow in Registration Wizard ActiveX control (regwizc.dll, InvokeRegWizard) ******* for Internet Explorer 4.01 and 5 allows remote attackers to execute arbitrary commands.
CVE-1999-1579(PUBLISHED):The Cenroll ActiveX control (xenroll.dll) for Terminal Server Editions of Windows NT 4.0 and Windows NT Server 4.0 before SP6 allows remote attackers to cause a denial of service (resource consumption) by creating a large number of arbitrary files on the target machine.
CVE-1999-1580(PUBLISHED):SunOS sendmail 5.59 through 5.65 uses popen to process a forwarding host argument, which allows local users to gain root privileges by modifying the IFS (Internal Field Separator) variable and passing crafted values to the -oR option.
CVE-1999-1581(PUBLISHED):Memory leak in Simple Network Management Protocol (SNMP) agent (snmp.exe) for Windows NT 4.0 before Service Pack 4 allows remote attackers to cause a denial of service (memory consumption) via a large number of SNMP packets with Object Identifiers (OIDs) that cannot be decoded.
CVE-1999-1582(PUBLISHED):By design, the "established" command on the Cisco PIX firewall allows connections from one host to arbitrary ports of a target host if an alternative conduit has already been allowed, which can cause administrators to configure less restrictive access controls than intended if they do not understand this functionality.
CVE-1999-1583(PUBLISHED):Buffer overflow in nslookup for AIX 4.3 allows local users to execute arbitrary code via a long hostname command line argument.
CVE-1999-1584(PUBLISHED):Unknown vulnerability in (1) loadmodule, and (2) modload if modload is installed with setuid/setgid privileges, in SunOS 4.1.1 through 4.1.3c, and Open Windows 3.0, allows local users to gain root privileges via environment variables, a different vulnerability than CVE-1999-1586.
CVE-1999-1585(PUBLISHED):The (1) rcS and (2) mountall programs in Sun Solaris 2.x, possibly before 2.4, start a privileged shell on the system console if fsck fails while the system is booting, which allows attackers with physical access to gain root privileges.
CVE-1999-1586(PUBLISHED):loadmodule in SunOS 4.1.x, as used by xnews, does not properly sanitize its environment, which allows local users to gain privileges, a different vulnerability than CVE-1999-1584.
CVE-1999-1587(PUBLISHED):/usr/ucb/ps in Sun Microsystems Solaris 8 and 9, and certain earlier releases, allows local users to view the environment variables and values of arbitrary processes via the -e option.
CVE-1999-1588(PUBLISHED):Buffer overflow in nlps_server in Sun Solaris x86 2.4, 2.5, and 2.5.1 allows remote attackers to execute arbitrary code as root via a long string beginning with "NLPS:002:002:" to the listen (aka System V listener) port, TCP port 2766.
CVE-1999-1589(PUBLISHED):Unspecified vulnerability in crontab in IBM AIX 3.2 allows local users to gain root privileges via unknown attack vectors.
CVE-1999-1590(PUBLISHED):Directory traversal vulnerability in Muhammad A. Muquit wwwcount (Count.cgi) 2.3 allows remote attackers to read arbitrary GIF files via ".." sequences in the image parameter, a different vulnerability than CVE-1999-0021.
CVE-1999-1591(PUBLISHED):Microsoft Internet Information Services (IIS) server 4.0 SP4, without certain hotfixes released for SP4, does not require authentication credentials under certain conditions, which allows remote attackers to bypass authentication requirements, as demonstrated by connecting via Microsoft Visual InterDev 6.0.
CVE-1999-1592(PUBLISHED):Multiple unspecified vulnerabilities in sendmail 5, as installed on Sun SunOS 4.1.3_U1 and 4.1.4, have unspecified attack vectors and impact.  NOTE: this might overlap CVE-1999-0129.
CVE-1999-1593(PUBLISHED):Windows Internet Naming Service (WINS) allows remote attackers to cause a denial of service (connectivity loss) or steal credentials via a 1Ch registration that causes WINS to change the domain controller to point to a malicious server.  NOTE: this problem may be limited when Windows 95/98 clients are used, or if the primary domain controller becomes unavailable.
