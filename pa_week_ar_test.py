import csv
import os
import subprocess
import sys
import re
import time
import traceback
from urllib.parse import urlparse
from urllib.robotparser import RobotFileParser
from bs4 import BeautifulSoup
from dotenv import load_dotenv
from htmldate import find_date
import mysql
from mysql.connector import Error
import requests
from tqdm import tqdm
import logging
from datetime import datetime, timedelta

import urllib

from Archivebox_cti import remove_archivebox_snapshot
from auto_search_api_register import get_fresh_search_api_keys
from mian_data_pa import GoogleSearchAPI, save_to_database
from RAG.utils import extract_page_pattern, is_detail_page, normalize_url, normalize_url_for_domain_comparison, parse_date_to_datetime, process_analysis_content, web_analyze

# $env:PYTHONPATH = "K:\CTI\Data_Excation;$env:PYTHONPATH"

dotenv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db.env')
load_dotenv(dotenv_path)

DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}
# === Windows编码修复 ===
# 在Windows环境下修复subprocess编码问题
import locale
import functools

# 日志
# 保存到output.txt
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logging.FileHandler('output.txt', mode='w', encoding='utf-8')


def patch_subprocess_for_utf8():
    """修补subprocess以确保在Windows下使用UTF-8编码"""
    import subprocess
    
    # 保存原始的run和Popen函数
    original_run = subprocess.run
    original_popen = subprocess.Popen
    
    def utf8_run(*args, **kwargs):
        """修补后的subprocess.run，强制使用UTF-8编码"""
        if 'encoding' not in kwargs and kwargs.get('text', False):
            kwargs['encoding'] = 'utf-8'
            kwargs['errors'] = 'replace'
        return original_run(*args, **kwargs)
    
    def utf8_popen(*args, **kwargs):
        """修补后的subprocess.Popen，强制使用UTF-8编码"""
        if 'encoding' not in kwargs and kwargs.get('text', False):
            kwargs['encoding'] = 'utf-8'
            kwargs['errors'] = 'replace'
        return original_popen(*args, **kwargs)
    
    # 替换原始函数
    subprocess.run = utf8_run
    subprocess.Popen = utf8_popen
    print("已应用subprocess UTF-8编码修补")

# 应用修补
patch_subprocess_for_utf8()

logging.info("正在初始化RAG分析器...")

# 全局变量声明
_analyzer_instance = None

# 初始化部分 - 移除第一个初始化代码块
try:
    from AI_copy import RAGAnalyzer
    from RAG.utils import web_analyze
    
    # 只有在不是主应用初始化时才初始化
    if 'RAG_INITIALIZING' not in os.environ:
        # 直接初始化分析器实例
        _analyzer_instance = RAGAnalyzer(db_config=DB_CONFIG)
        logging.info("RAG分析器初始化成功")
    else:
        print("pa_week: 检测到主应用正在初始化，跳过RAG初始化")
except ImportError as e:
    _analyzer_instance = None
    logging.error(f"无法导入 RAGAnalyzer 类，错误信息: {e}")
    logging.error(f"当前工作目录: {os.getcwd()}")
    logging.error(f"Python路径: {sys.path}")
    traceback.print_exc()
except Exception as e:
    _analyzer_instance = None
    logging.error(f"初始化RAG分析器失败: {e}")
    traceback.print_exc()

def get_analyzer_instance():
    """获取RAGAnalyzer实例"""
    global _analyzer_instance
    if _analyzer_instance is None:
        # 如果初始化失败，尝试重新初始化
        try:
            logging.info("尝试重新初始化RAG分析器...")
            _analyzer_instance = RAGAnalyzer(db_config=DB_CONFIG)
            logging.info("RAG分析器重新初始化成功")
        except Exception as e:
            logging.error(f"初始化RAG分析器失败: {e}")
            traceback.print_exc()
    return _analyzer_instance


def extract_domains_from_query(query):
    """
    从搜索语法中提取域名信息
    
    Args:
        query (str): 搜索查询语法，例如 "site:blog.talosintelligence.com"
    
    Returns:
        set: 包含域名的集合，如果没有找到域名则返回空集合
    """
    domains = set()
    
    # 匹配 site: 语法中的域名
    site_pattern = r'site:([a-zA-Z0-9\.-]+\.[a-zA-Z]{2,})'
    site_matches = re.findall(site_pattern, query, re.IGNORECASE)
    
    for domain in site_matches:
        # 清理域名，移除可能的路径部分
        clean_domain = domain.split('/')[0].lower()
        domains.add(clean_domain)
    
    # 匹配 inurl: 语法中包含的域名
    inurl_pattern = r'inurl:([a-zA-Z0-9\.-]+\.[a-zA-Z]{2,})'
    inurl_matches = re.findall(inurl_pattern, query, re.IGNORECASE)
    
    for domain in inurl_matches:
        clean_domain = domain.split('/')[0].lower()
        domains.add(clean_domain)
    
    # 匹配直接包含在查询中的域名（不带协议前缀）
    domain_pattern = r'\b([a-zA-Z0-9\.-]+\.[a-zA-Z]{2,})\b'
    all_matches = re.findall(domain_pattern, query)
    
    for match in all_matches:
        # 验证是否为有效域名（包含至少一个点，且不是纯数字）
        if '.' in match and not match.replace('.', '').isdigit():
            clean_domain = match.lower()
            # 排除常见的非域名匹配
            if not any(keyword in clean_domain for keyword in ['www.', 'http', 'https']):
                domains.add(clean_domain)
    
    return domains

def is_domain_match(url, allowed_domains):
    """
    检查URL的域名是否在允许的域名列表中
    
    Args:
        url (str): 要检查的URL
        allowed_domains (set): 允许的域名集合
    
    Returns:
        bool: 如果域名匹配则返回True，否则返回False
    """
    if not allowed_domains:
        # 如果没有指定域名限制，则允许所有域名
        return True
    
    try:
        parsed_url = urlparse(url)
        url_domain = parsed_url.netloc.lower()
        
        # 移除 www. 前缀进行比较
        if url_domain.startswith('www.'):
            url_domain_clean = url_domain[4:]
        else:
            url_domain_clean = url_domain
        
        # 检查完全匹配
        for allowed_domain in allowed_domains:
            allowed_clean = allowed_domain.lower()
            if allowed_clean.startswith('www.'):
                allowed_clean = allowed_clean[4:]
            
            # 完全匹配
            if url_domain_clean == allowed_clean:
                return True
            
            # 子域名匹配（例如：blog.example.com 匹配 example.com）
            if url_domain_clean.endswith('.' + allowed_clean):
                return True
            
            # 反向匹配（例如：example.com 匹配 blog.example.com）
            if allowed_clean.endswith('.' + url_domain_clean):
                return True
        
        return False
        
    except Exception as e:
        print(f"域名匹配检查时出错 {url}: {str(e)}")
        return False

def filter_urls_by_domain(urls_with_queries, verbose=True):
    """
    根据搜索语法中的域名过滤URL列表
    
    Args:
        urls_with_queries (list): 包含URL和查询信息的字典列表
        verbose (bool): 是否显示详细的过滤信息
    
    Returns:
        tuple: (过滤后的URL列表, 过滤统计信息)
    """
    filtered_urls = []
    filter_stats = {
        'total_input': len(urls_with_queries),
        'domain_matched': 0,
        'domain_filtered': 0,
        'no_domain_constraint': 0,
        'filter_details': []
    }
    
    if verbose:
        print("\n=== 开始域名一致性过滤 ===")
    
    for idx, url_item in enumerate(urls_with_queries):
        url = url_item.get('url', '')
        query = url_item.get('query', '')
        
        # 从查询中提取域名
        allowed_domains = extract_domains_from_query(query)
        
        if not allowed_domains:
            # 没有域名约束的查询，保留所有结果
            filtered_urls.append(url_item)
            filter_stats['no_domain_constraint'] += 1
            if verbose and idx < 5:  # 只显示前5个示例
                print(f"  无域名约束: {url[:80]}... (查询: {query[:50]}...)")
        else:
            # 检查域名匹配
            if is_domain_match(url, allowed_domains):
                filtered_urls.append(url_item)
                filter_stats['domain_matched'] += 1
                if verbose and idx < 5:  # 只显示前5个示例
                    print(f"  域名匹配: {url[:80]}... (允许: {', '.join(allowed_domains)})")
            else:
                filter_stats['domain_filtered'] += 1
                filter_details = {
                    'url': url,
                    'query': query,
                    'allowed_domains': list(allowed_domains),
                    'actual_domain': urlparse(url).netloc
                }
                filter_stats['filter_details'].append(filter_details)
                if verbose and len(filter_stats['filter_details']) <= 5:  # 只显示前5个示例
                    print(f"  域名不匹配: {url[:80]}... (实际: {urlparse(url).netloc}, 期望: {', '.join(allowed_domains)})")
    
    if verbose:
        print(f"\n域名过滤统计:")
        print(f"  - 输入总数: {filter_stats['total_input']}")
        print(f"  - 域名匹配保留: {filter_stats['domain_matched']}")
        print(f"  - 无域名约束保留: {filter_stats['no_domain_constraint']}")
        print(f"  - 域名不匹配过滤: {filter_stats['domain_filtered']}")
        print(f"  - 最终保留: {len(filtered_urls)}")
        
        # 显示被过滤的域名统计
        if filter_stats['filter_details']:
            filtered_domains = {}
            for detail in filter_stats['filter_details']:
                domain = detail['actual_domain']
                if domain not in filtered_domains:
                    filtered_domains[domain] = 0
                filtered_domains[domain] += 1
            
            print(f"\n被过滤的域名统计 (top 10):")
            for domain, count in sorted(filtered_domains.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"  - {domain}: {count} 个链接")
    
    return filtered_urls, filter_stats

def check_content_quality(content, title, url):
    """
    检查内容质量，决定是否值得进行深度分析
    基于标题、IOC指标和威胁情报关键词进行简化判断

    Args:
        content (str): 提取的文本内容
        title (str): 文章标题
        url (str): 文章URL

    Returns:
        dict: {'should_analyze': bool, 'reason': str}
    """
    try:
        import re

        # 基本检查
        if not content or len(content.strip()) < 150:
            return {'should_analyze': False, 'reason': '内容过短'}

        if not title or len(title.strip()) < 10:
            return {'should_analyze': False, 'reason': '标题过短'}

        # 检查是否是错误页面或无效内容
        error_indicators = [
            'took too long to respond', 'err_timed_out', 'connection timed out',
            'checking the connection', 'checking the proxy', 'network error',
            'page not found', '404', '403', '500', 'server error',
            'access denied', 'forbidden', 'bad gateway', 'service unavailable',
            'this site can\'t be reached', 'unable to connect', 'connection refused',
            'dns_probe_finished_nxdomain', 'err_name_not_resolved'
        ]

        # 检查是否是Cookie同意页面或其他无价值页面
        cookie_page_indicators = [
            'we use cookies', 'cookie settings', 'accept all', 'consent to the use',
            'cookie policy', 'privacy policy', 'terms of service', 'terms and conditions',
            'by clicking accept', 'controlled consent', 'remembering your preferences',
            'most relevant experience', 'repeat visits'
        ]

        # 检查是否是登录页面或其他功能页面
        functional_page_indicators = [
            'sign in', 'log in', 'login', 'register', 'sign up', 'forgot password',
            'reset password', 'create account', 'username', 'password',
            'email address', 'confirm password', 'captcha'
        ]

        content_lower = content.lower()
        title_lower = title.lower()
        combined_text = f"{title} {content}".lower()

        # 如果标题只是域名且内容包含错误信息，直接过滤
        if (len(title.split()) <= 2 and
            any(error in combined_text for error in error_indicators)):
            return {'should_analyze': False, 'reason': '网络错误页面或无效内容'}

        # 如果内容主要是错误信息
        error_count = sum(1 for error in error_indicators if error in combined_text)
        if error_count >= 3:
            return {'should_analyze': False, 'reason': '包含大量错误信息，可能是无效页面'}

        # 检查是否是Cookie同意页面
        cookie_count = sum(1 for indicator in cookie_page_indicators if indicator in combined_text)
        if cookie_count >= 3:
            return {'should_analyze': False, 'reason': 'Cookie同意页面或隐私政策页面'}

        # 检查内容是否过短且主要是Cookie相关
        if len(content.strip()) < 500 and cookie_count >= 2:
            return {'should_analyze': False, 'reason': '内容过短且主要是Cookie相关信息'}

        # 检查是否是登录或功能页面
        functional_count = sum(1 for indicator in functional_page_indicators if indicator in combined_text)
        if functional_count >= 3:
            return {'should_analyze': False, 'reason': '登录页面或功能页面'}

        # 1. 首先检查是否包含IOC指标 - 有IOC的直接保留
        ioc_patterns = {
            'ip': r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
            'domain': r'\b[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.(?:[a-zA-Z]{2,})\b',
            'hash_md5': r'\b[a-fA-F0-9]{32}\b',
            'hash_sha1': r'\b[a-fA-F0-9]{40}\b',
            'hash_sha256': r'\b[a-fA-F0-9]{64}\b',
            'cve': r'CVE-\d{4}-\d{4,7}',
            'url': r'https?://[^\s<>"{}|\\^`\[\]]+',
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        }

        ioc_found = 0
        for ioc_type, pattern in ioc_patterns.items():
            matches = re.findall(pattern, combined_text, re.IGNORECASE)
            if matches:
                # 过滤掉一些常见的非恶意域名和IP，以及Cookie相关的误判
                if ioc_type == 'domain':
                    matches = [m for m in matches if not any(common in m.lower()
                              for common in ['example.com', 'localhost', 'test.com', 'google.com', 'microsoft.com',
                                           'consent.cookie', 'cookie.settings', 'privacy.policy', 'terms.service'])]
                elif ioc_type == 'ip':
                    matches = [m for m in matches if not (m.startswith('127.') or m.startswith('192.168.') or m.startswith('10.'))]

                if matches:
                    ioc_found += len(matches)

        # 如果发现IOC指标，直接保留
        if ioc_found >= 3:
            return {'should_analyze': True, 'reason': f'发现{ioc_found}个IOC指标，高价值内容'}

        # 2. 检查威胁情报核心关键词
        threat_keywords = [
            'cve-', 'vulnerability', '漏洞', 'exploit', 'malware', '恶意软件',
            'apt', 'backdoor', '后门', 'ransomware', '勒索', 'phishing', '钓鱼',
            '0day', 'payload', '威胁情报', 'ioc', 'trojan', '木马', 'virus', '病毒',
            'attack', '攻击', 'hacker', '黑客', 'security', '安全'
        ]

        # 简化计分：只要包含关键词就加分，不区分标题和内容
        threat_score = 0
        for keyword in threat_keywords:
            if keyword in combined_text:
                threat_score += 1

        # 3. 检查低价值关键词（新闻报道类）
        low_value_keywords = [
            '被处罚', '行政处罚', '网警提示', '素材：', '来源：',
            '经查', '近日', '据悉', '据了解', '据报道', '公安部门',
            'breaking news', 'press release', 'according to', 'sources say',
            '记者', '报道', '通讯员', '编辑', 'published by'
        ]

        low_value_count = sum(1 for keyword in low_value_keywords
                             if keyword in combined_text)

        # 有IOC指标 -> 可以使用
        if ioc_found >= 1:
            return {'should_analyze': True, 'reason': f'发现IOC指标'}

        # 威胁情报相关 -> 可以使用（降低阈值）
        if threat_score >= 1:
            return {'should_analyze': True, 'reason': '可以使用'}

        # 明显的新闻报道 -> 过滤丢掉
        if low_value_count >= 3:
            return {'should_analyze': False, 'reason': '过滤丢掉'}

        # 其他情况 -> 可以使用
        return {'should_analyze': True, 'reason': '可以使用'}

    except Exception as e:
        print(f"内容质量检查出错: {e}")
        # 出错时默认进行分析
        return {'should_analyze': True, 'reason': '质量检查出错，默认分析'}

def get_date_range():
    """获取当天向前一周的日期范围

    Returns:
        tuple: (start_date, end_date) 格式为YYYY-MM-DD的字符串
    """
    end_date = datetime.now()  # 当前日期
    start_date = end_date - timedelta(days=7)  # 7天前

    # 转换为字符串格式
    end_date_str = end_date.strftime('%Y-%m-%d')
    start_date_str = start_date.strftime('%Y-%m-%d')

    print(f"搜索日期范围: {start_date_str} 至 {end_date_str}")

    return start_date_str, end_date_str

# 爬取每周更新，对搜索加入日期限制,调用mian_data_pa.py中的，获取到链接就可以了
# 1、写个用临时邮箱自动化注册searchapi的100次的api-key
# 变成4. 手动收集  质量高高的文章  域名记下来  加到自动爬虫的脚本中  

# 5. 不用爬虫爬数据  search api 拿到文章的URL  提供给Archive box  存档 循环读取
# 每周中加入微信公众号链接的获取,可以调用threat_Wechat_spider文件中的函数
def get_wechat_links():
    """
    调用微信公众号爬虫获取威胁情报相关文章链接
    
    Returns:
        list: 包含微信公众号文章信息的列表，每个元素为字典格式：
              {
                  'url': '文章链接',
                  'title': '文章标题',
                  'source': '来源公众号名称',
                  'snippet': '文章摘要',
                  'query': '微信公众号',
                  'date': '发布日期'
              }
    """
    import csv
    import os
    from datetime import datetime
    
    wechat_articles = []
    
    try:
        # 导入微信爬虫模块
        import threat_Wechat_spider
        
        print('----- 开始调用微信公众号爬虫获取威胁情报文章 -----')
        print(f'----- 当前时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")} -----')
        
        # 执行微信公众号爬取
        threat_Wechat_spider.start()
        
        # 读取爬取结果CSV文件
        csv_file_path = os.path.join(threat_Wechat_spider.file_save_path, 'articles_info.csv')
        
        if not os.path.exists(csv_file_path):
            print('----- 未找到微信公众号爬取结果文件 -----')
            return []
        
        # 读取CSV文件中的所有文章信息并转换为统一格式
        with open(csv_file_path, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # 转换为与搜索API一致的格式
                article_data = {
                    'url': row['文章链接'],
                    'link': row['文章链接'],  # 兼容性字段
                    'title': row['文章标题'],
                    'source': row['来源公众号名称'],
                    'snippet': f"来自微信公众号: {row['来源公众号名称']}",
                    'query': '微信公众号威胁情报',
                    'date': row['文章日期'],
                    'create_time': row['文章日期'],  # 兼容性字段
                    'content_source': 'wechat'
                }
                wechat_articles.append(article_data)
        
        print(f'----- 微信公众号爬取完成，共获取 {len(wechat_articles)} 篇威胁情报文章 -----')
        
        # 显示统计信息
        if wechat_articles:
            # 按公众号统计
            source_stats = {}
            for article in wechat_articles:
                source = article['source']
                source_stats[source] = source_stats.get(source, 0) + 1
            
            print(f'涉及公众号数量: {len(source_stats)}')
            print('各公众号文章数量:')
            for source, count in sorted(source_stats.items(), key=lambda x: x[1], reverse=True):
                print(f'  {source}: {count}篇')
            
            # 显示最新几篇文章示例
            print('\n最新文章示例:')
            for i, article in enumerate(wechat_articles[:3]):
                print(f'  {i+1}. [{article["source"]}] {article["title"]}')
                print(f'     时间: {article["date"]}')
                print(f'     链接: {article["url"][:80]}...')
        
        return wechat_articles
        
    except ImportError as e:
        print(f'----- 导入微信爬虫模块失败: {str(e)} -----')
        print('----- 请确保 threat_Wechat_spider.py 文件存在且可导入 -----')
        return []
    except Exception as e:
        print(f'----- 微信公众号爬取失败: {str(e)} -----')
        import traceback
        traceback.print_exc()
        return []

def week_data():
    """
    周度自动威胁情报采集函数 - 优化版本：先批量检索，后批量归档
    
    流程：
    1. Search API 批量获取所有威胁情报相关链接
    2. 去重并筛选有效链接
    3. 批量将链接提交给 ArchiveBox 进行归档
    """
    from Archivebox_cti import add_url_to_archivebox, extract_single_url_by_id, get_existing_links_from_archivebox
   
    conn = None
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        try:
            # 导入配置
            from threat_intel_config_simplified import generate_high_quality_search_syntax, get_search_statistics
            
            # 生成高质量搜索语法
            print("=== 生成高质量威胁情报搜索语法 ===")
            search_syntax = generate_high_quality_search_syntax()


            # 显示统计信息
            stats = get_search_statistics()
            print(f"配置统计:")
            print(f"  - 高质量域名: {stats['total_domains']} 个 (国际: {stats['international_domains']}, 中文: {stats['chinese_domains']})")
            print(f"  - 威胁关键词: {stats['total_keywords']} 个 (英文: {stats['english_keywords']}, 中文: {stats['chinese_keywords']})")
            print(f"  - 生成搜索语法: {stats['total_search_syntax']} 条")
            print(f"  - 英文域名+关键词组合: {stats['english_domain_combinations']} 条")
            print(f"  - 中文域名+关键词组合: {stats['chinese_domain_combinations']} 条")
            print(f"  - 公众号检索: {stats['wechat_searches']} 条")
            print(f"  - 跨域名核心威胁检索: {stats['cross_domain_searches']} 条")
            print(f"  - RSS备用检索: {stats['rss_backup_searches']} 条")
            print(f"  - 一周内时间范围: {'已启用' if stats['time_range_enabled'] else '未启用'}")
            
            print("\n搜索语法示例:")
            for i, syntax in enumerate(search_syntax[:5]):
                print(f"  {i+1}. {syntax}")
            print("  ...")

        except Exception as e:
            print(f"数据获取错误：{str(e)}")
            return False

        # === 阶段1：批量检索所有URL ===
        # search_syntax先拿10个测试
        search_syntax=search_syntax[:1]


        print("\n=== 阶段1：批量检索所有URL ===")
        try:
            # 从环境变量获取API密钥，支持多个密钥用逗号分隔
            api_keys_env = os.getenv('SEARCH_API_KEYS', '')
            if api_keys_env:
                # 从环境变量获取，支持逗号分隔的多个密钥
                api_keys = [key.strip() for key in api_keys_env.split(',') if key.strip()]
                print(f"从环境变量加载了 {len(api_keys)} 个API密钥")
                print(f"api_keys {api_keys}")
            else:
                # 备用：从配置文件或默认值获取
                try:
                    from threat_intel_config_simplified import get_api_keys_config
                    api_keys = get_api_keys_config()
                    print(f"从配置文件加载了 {len(api_keys)} 个API密钥")
                except ImportError:
                    # 最后备用：硬编码的API密钥
                    print("使用备用API")
                    api_keys = [
                        "9XWqzQxypAdSMZ9ErGKaAiPP",
                        "Jeabs8XDL6pFHbakHLHcHUfm"
                    ]
                    print(f"使用默认API密钥配置 ({len(api_keys)} 个)")
            
            # 验证API密钥是否有效
            if not api_keys or not any(key.strip() for key in api_keys):
                print("警告: 没有可用的API密钥")
                return False

            searcher = GoogleSearchAPI(api_keys=api_keys)

            # 获取日期范围
            start_date, end_date = get_date_range()

            # 收集所有搜索结果
            all_collected_urls = []
            search_success_count = 0
            search_failed_queries = []
            consecutive_failures = 0

            print(f"开始批量检索 {len(search_syntax)} 个搜索语法...")
            
            for idx in tqdm(range(len(search_syntax)), desc="批量检索进度", unit="条"):
                query = search_syntax[idx]
                try:
                    print(f"正在搜索 ({idx+1}/{len(search_syntax)})：{query}")

                    results = searcher.safe_search(
                        query,
                        start_date=start_date,
                        end_date=end_date,
                    )

                    # 检查搜索结果
                    if not results:
                        print(f"没有找到相关数据: {query}")
                        continue
                    
                    # 添加结果到收集列表 - 先收集所有结果用于域名过滤
                    query_results = []
                    for result in results:
                        url = result.get('link') or result.get('url')
                        if url:
                            query_results.append({
                                'url': url,
                                'title': result.get('title', ''),
                                'snippet': result.get('snippet', ''),
                                'query': query,
                                'source': 'search_api'
                            })
                    
                    # 对当前查询的结果进行域名过滤
                    if query_results:
                        filtered_results, filter_stats = filter_urls_by_domain(query_results, verbose=False)
                        all_collected_urls.extend(filtered_results)
                        
                        # 显示过滤结果
                        filtered_count = len(query_results) - len(filtered_results)
                        if filtered_count > 0:
                            print(f"成功检索到 {len(results)} 个链接，域名过滤后保留 {len(filtered_results)} 个 (过滤掉 {filtered_count} 个)")
                        else:
                            print(f"成功检索到 {len(results)} 个链接，域名过滤后全部保留")
                    else:
                        print(f"成功检索到 {len(results)} 个链接，但没有有效URL")
                    
                    search_success_count += 1
                    consecutive_failures = 0  # 重置连续失败计数
                    
                except Exception as e:
                    print(f"搜索查询 '{query}' 时出错: {str(e)}")
                    search_failed_queries.append(query)
                    consecutive_failures += 1
                    
                    # 连续失败达到3次，中断搜索
                    if consecutive_failures >= 3:
                        print(f"\n警告: 连续失败已达到3次，终止搜索任务")
                        break

            print(f"\n=== 阶段1完成 ===")
            print(f"成功搜索查询: {search_success_count}/{len(search_syntax)}")
            print(f"收集到的原始链接: {len(all_collected_urls)} 个")
            
            # 显示域名过滤的总体统计
            if all_collected_urls:
                # 统计各查询的域名匹配情况
                domain_constrained_urls = []
                no_constraint_urls = []
                
                for item in all_collected_urls:
                    query = item.get('query', '')
                    allowed_domains = extract_domains_from_query(query)
                    if allowed_domains:
                        domain_constrained_urls.append(item)
                    else:
                        no_constraint_urls.append(item)
                
                print(f"域名约束统计:")
                print(f"  - 有域名约束的链接: {len(domain_constrained_urls)} 个")
                print(f"  - 无域名约束的链接: {len(no_constraint_urls)} 个")
                
                # 统计被域名过滤影响的查询
                if domain_constrained_urls:
                    affected_queries = set()
                    for item in domain_constrained_urls:
                        affected_queries.add(item.get('query', ''))
                    print(f"  - 涉及域名过滤的查询: {len(affected_queries)} 条")
            
            if search_failed_queries:
                print(f"失败的查询 ({len(search_failed_queries)}):")
                for query in search_failed_queries:
                    print(f"  - {query}")

            # === 阶段1.5：获取微信公众号链接 ===
            print("\n=== 阶段1.5：获取微信公众号威胁情报链接 ===")
            try:
                wechat_articles = get_wechat_links()
                
                if wechat_articles:
                    print(f"成功获取微信公众号文章: {len(wechat_articles)} 个")
                    # 将微信文章添加到收集列表中，格式与搜索API结果一致
                    for article in wechat_articles:
                        all_collected_urls.append({
                            'url': article['url'],
                            'title': article['title'],
                            'snippet': article['snippet'],
                            'query': article['query'],
                            'source': 'wechat',
                            'wechat_source': article['source'],  # 保存公众号名称
                            'date': article.get('date', '')
                        })
                    
                    print(f"微信文章已合并到链接池，总链接数: {len(all_collected_urls)} 个")
                else:
                    print("未获取到微信公众号文章")
                    
            except Exception as e:
                print(f"获取微信公众号链接时出错: {str(e)}")
                # 微信获取失败不影响主流程继续执行

            if not all_collected_urls:
                print("没有收集到任何链接，终止任务")
                return False

        except Exception as e:
            print(f"批量检索阶段失败: {str(e)}")
            traceback.print_exc()
            return False

        # === 阶段2：去重并筛选有效链接 ===
        print("\n=== 阶段2：去重并筛选有效链接 ===")
        
        # 步骤2.1：去重 - 使用URL作为键
        unique_urls = {}
        for item in all_collected_urls:
            url = item['url']
            if url not in unique_urls:
                unique_urls[url] = item
            else:
                # 如果URL已存在，保留标题更长的那个
                if len(item['title']) > len(unique_urls[url]['title']):
                    unique_urls[url] = item

        print(f"去重后的唯一链接: {len(unique_urls)} 个")

        # 步骤2.2：基本URL格式验证
        format_valid_urls = []
        for url, item in unique_urls.items():
            try:
                parsed = urlparse(url)
                if parsed.scheme in ['http', 'https'] and parsed.netloc:
                    format_valid_urls.append(item)
                else:
                    print(f"跳过无效URL格式: {url}")
            except Exception as e:
                print(f"解析URL失败: {url}, 错误: {str(e)}")

        print(f"格式有效链接: {len(format_valid_urls)} 个")

        # 步骤2.3：检查数据库中是否已存在相同链接
        print("正在检查数据库中的重复链接...")
        existing_links_in_db = set()
        try:
            cursor.execute("SELECT link FROM crawled_data")
            db_results = cursor.fetchall()
            existing_links_in_db = {row[0] for row in db_results}
            print(f"数据库中已有链接: {len(existing_links_in_db)} 个")
        except Exception as e:
            print(f"查询数据库失败: {str(e)}")

        # 过滤掉数据库中已存在的链接
        new_urls_only = []
        duplicate_count = 0
        for item in format_valid_urls:
            url = item['url']
            if url not in existing_links_in_db:
                new_urls_only.append(item)
            else:
                duplicate_count += 1

        print(f"过滤掉数据库重复链接: {duplicate_count} 个，剩余新链接: {len(new_urls_only)} 个")

        # 步骤2.3：检查ArchiveBOX中是否已存在相同链接
        print("正在检查ArchiveBOX中的重复链接...")
        existing_links_in_archivebox = set()
        docker_available = True  
        try:
            # 直接使用数据库查询，跳过命令行检查（避免超时）
            print("使用数据库查询检查ArchiveBox重复链接...")
            existing_links_in_archivebox = get_existing_links_from_archivebox()
            print(f"ArchiveBOX中已有链接: {len(existing_links_in_archivebox)} 个")
        except Exception as e:
            print(f"查询ArchiveBOX失败: {str(e)}")
            existing_links_in_archivebox = set()  # 确保是空集合
            docker_available = False  # 如果数据库查询失败，标记为不可用

        # 过滤掉ArchiveBox中已存在的链接
        archivebox_filtered_urls = []
        archivebox_duplicate_count = 0
        for item in new_urls_only:
            url = item['url']
            if url not in existing_links_in_archivebox:
                archivebox_filtered_urls.append(item)
            else:
                archivebox_duplicate_count += 1

        print(f"过滤掉ArchiveBox重复链接: {archivebox_duplicate_count} 个，剩余新链接: {len(archivebox_filtered_urls)} 个")

        # 步骤2.4：预筛选内容质量（检查是否为详情页且内容充足）
        print("正在预筛选内容质量...")
        quality_urls = []
        low_quality_count = 0
        content_check_failed = 0
        
        for idx, item in enumerate(tqdm(archivebox_filtered_urls, desc="内容质量检查", unit="链接")):
            url = item['url']
            try:
                # 首先检查是否为详情页
                if not is_detail_page(url):
                    low_quality_count += 1
                    continue
                
                # 快速检查内容长度（使用轻量级方法）
                try:
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                    response = requests.get(url, headers=headers, timeout=10)
                    
                    if response.status_code == 200:
                        # 简单检查HTML内容长度
                        content_length = len(response.text)
                        if content_length < 2000:  # HTML少于2000字符通常内容不足
                            low_quality_count += 1
                            continue
                        
                        # 使用BeautifulSoup快速提取文本内容长度
                        try:
                            soup = BeautifulSoup(response.text, 'html.parser')
                            text_content = soup.get_text(strip=True)
                            if len(text_content) < 300:  # 纯文本少于300字符
                                low_quality_count += 1
                                continue
                        except Exception:
                            # 如果解析失败，但HTML长度足够，仍然保留
                            pass
                        
                        # 通过所有检查，添加到高质量列表
                        quality_urls.append(item)
                        
                    else:
                        content_check_failed += 1
                        continue
                        
                except requests.exceptions.Timeout:
                    content_check_failed += 1
                    continue
                except requests.exceptions.RequestException:
                    content_check_failed += 1
                    continue
                    
            except Exception as e:
                print(f"检查链接质量时出错 {url}: {str(e)}")
                content_check_failed += 1
                continue

        print(f"内容质量筛选结果:")
        print(f"  - 高质量链接: {len(quality_urls)} 个")
        print(f"  - 低质量/内容不足: {low_quality_count} 个") 
        print(f"  - 检查失败: {content_check_failed} 个")

        # 最终有效链接
        valid_urls = quality_urls
        print(f"最终有效高质量链接: {len(valid_urls)} 个")

        if not valid_urls:
            print("没有有效链接，终止任务")
            return False

        # === 阶段3：批量归档到ArchiveBox ===
        print("\n=== 阶段3：批量归档到ArchiveBox ===")
        
        # 记录归档结果
        archive_results = []
        archive_success_count = 0
        archive_failure_count = 0

        # 拿5个测试
        # valid_urls= valid_urls
        
        print(f"开始批量归档 {len(valid_urls)} 个链接...")
        
        for idx, url_item in enumerate(tqdm(valid_urls, desc="批量归档进度", unit="链接")):
            url = url_item['url']
            try:
                print(f"\n[{idx+1}/{len(valid_urls)}] 正在归档: {url}")
                
                if docker_available:
                    # 只使用ArchiveBox归档，不使用备用方法
                    try:
                        success, snapshot_id = add_url_to_archivebox(url, timeout=1000)

                        if success and snapshot_id:
                            archive_results.append({
                                **url_item,
                                'snapshot_id': snapshot_id,
                                'archive_status': 'success',
                                'archive_method': 'archivebox'
                            })
                            archive_success_count += 1
                            print(f"ArchiveBox归档成功，snapshot_id: {snapshot_id}")
                        else:
                            print(f"ArchiveBox归档失败: {url}")
                            archive_failure_count += 1

                    except Exception as e:
                        print(f"ArchiveBox归档异常: {str(e)}")
                        archive_failure_count += 1
                else:
                    print(f"ArchiveBox不可用，跳过归档: {url}")

                    
            except Exception as e:
                print(f"归档链接时出错 {url}: {str(e)}")
                archive_failure_count += 1
                continue

        print(f"\n=== 阶段3完成 ===")
        print(f"成功归档: {archive_success_count}/{len(valid_urls)}")
        print(f"归档失败: {archive_failure_count}")
        if docker_available:
            print("使用方法: ArchiveBox")
        else:
            print("ArchiveBox不可用，无法进行归档")

        if not archive_results:
            print("没有成功归档的链接，终止任务")
            return False

        # === 最终统计 ===
        print(f"\n=== 最终统计 ===")
        print(f"搜索语法数: {len(search_syntax)}")
        print(f"搜索成功: {search_success_count}")
        
        # 统计链接来源
        search_api_links = len([url for url in all_collected_urls if url.get('source') == 'search_api'])
        wechat_links = len([url for url in all_collected_urls if url.get('source') == 'wechat'])
        
        print(f"收集原始链接: {len(all_collected_urls)} (搜索API: {search_api_links}, 微信公众号: {wechat_links})")
        print(f"去重后链接: {len(unique_urls)}")
        print(f"有效链接: {len(valid_urls)}")
        print(f"归档成功: {archive_success_count}")
        
        # 显示微信公众号统计
        if wechat_links > 0:
            wechat_sources = set()
            for url in all_collected_urls:
                if url.get('source') == 'wechat' and url.get('wechat_source'):
                    wechat_sources.add(url['wechat_source'])
            
            print(f"微信公众号数量: {len(wechat_sources)}")
            if wechat_sources:
                print(f"涉及公众号: {', '.join(list(wechat_sources)[:5])}")
        
        print("=== 高质量威胁情报采集完成 ===")
        return True
        
    except Exception as e:
        print(f"主流程出现异常: {str(e)}")
        traceback.print_exc()
        return False
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()


def discover_new_content(csv_path=None, new_data="2025-06-01", resume=True):
    """
    发现新的内容：从数据库获取已有链接，重点处理分页URL以获取最新文章
    
    Args:
        csv_path (str): CSV文件路径，用于保存和断点续传
        new_data (int): 限制只获取该年份及之后的文章
        resume (bool): 是否启用断点续传
    """
    conn = None
    try:
        # 创建日志目录确保文件可写入
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建两个断点续爬记录文件
        processed_list_pages_file = os.path.join(log_dir, f"processed_list_pages_{datetime.now().strftime('%Y%m%d')}.txt")
        processed_detail_pages_file = os.path.join(log_dir, f"processed_detail_pages_{datetime.now().strftime('%Y%m%d')}.txt")
        
        # 加载已处理的列表页
        processed_source_links = set()
        processed_source_links_alt = set()  # 存储替代形式
        
        # 加载已处理的详情页
        processed_detail_links = set()
        
        if resume:
            # 加载已处理的列表页
            if os.path.exists(processed_list_pages_file):
                try:
                    with open(processed_list_pages_file, mode='r', encoding='utf-8') as f:
                        for line in f:
                            url = line.strip()
                            if url:
                                url_norm, url_alt = normalize_url(url)
                                processed_source_links.add(url_norm)
                                processed_source_links_alt.add(url_alt)
                    logging.info(f"已从文件加载 {len(processed_source_links)} 个已处理的列表页")
                except Exception as e:
                    logging.error(f"读取列表页文件失败: {str(e)}")
            
            # 加载已处理的详情页
            if os.path.exists(processed_detail_pages_file):
                try:
                    with open(processed_detail_pages_file, mode='r', encoding='utf-8') as f:
                        for line in f:
                            url = line.strip()
                            if url:
                                processed_detail_links.add(url)
                    logging.info(f"已从文件加载 {len(processed_detail_links)} 个已处理的详情页")
                except Exception as e:
                    logging.error(f"读取详情页文件失败: {str(e)}")
            
            # 从CSV加载已处理数据作为备份 
            if os.path.exists(csv_path):
                try:
                    with open(csv_path, mode='r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            if 'source_link' in row and row['source_link']:
                                url, url_alt = normalize_url(row['source_link'])
                                processed_source_links.add(url)
                                processed_source_links_alt.add(url_alt)
                            if 'link' in row and row['link']:
                                processed_detail_links.add(row['link'])
                    logging.info(f"已从CSV加载额外的已处理链接")
                except Exception as e:
                    logging.error(f"读取CSV文件失败: {str(e)}")

        # 连接数据库
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)

        # 从数据库中拿取page_urls
        cursor.execute("SELECT link FROM crawled_data WHERE crawl_status = 3")
        db_links = cursor.fetchall()
        # 过滤出包含http的链接
        filtered_links = [row['link'] for row in db_links if 'http' in row['link']]
        # 去重
        unique_links = list(set(filtered_links))
        # 过滤出包含分页的链接
        existing_page_links = [link for link in unique_links if '/page/' in link or '/p/' in link or '?page=' in link or '?p=' in link]
            
        logging.info(f"共找到 {len(existing_page_links)} 个分页链接")

        # 再拿一个数据库中已有的链接，用于后续新的链接的去重
        cursor.execute("SELECT link FROM crawled_data WHERE crawl_status = 1")
        processed_links_rows = cursor.fetchall()
        processed_links_set = set(row['link'] for row in processed_links_rows)
        logging.info(f"共找到 {len(processed_links_set)} 个已处理的链接")

        # 添加数据库中的链接到已处理集合
        processed_detail_links.update(processed_links_set)
        logging.info(f"总计已处理的详情页链接: {len(processed_detail_links)} 个")

        # 去掉不需要处理的链接
        no_use = [
            # 添加不需要处理的链接
        ]
        existing_page_links = [link for link in existing_page_links if link not in no_use]

        logging.info(f"过滤后的分页链接: {existing_page_links}")

        # 打印几个样例URL以及它们的标准化结果
        print("\n检查待过滤的链接列表:")
        intezer_in_existing = [link for link in existing_page_links if 'intezer.com' in link]
        print(f"待过滤链接中包含'intezer.com'的URL: {len(intezer_in_existing)}个")
        for link in intezer_in_existing:
            print(f"  - {link}")

        # 移除已经处理过的列表页
        if resume:
            # 首先执行精确的URL匹配过滤
            precise_filtered_urls = []
            precise_matches = 0
            matched_urls = []  # 存储匹配成功的URL

            for link in existing_page_links:
                # 标准化URL以便精确比较
                link_norm, link_alt = normalize_url(link)
                
                # 精确匹配 - 如果链接已被处理过，直接跳过
                if link_norm in processed_source_links:
                    precise_matches += 1
                    matched_urls.append(f"{link} -> {link_norm} (通过标准形式匹配)")
                    continue
                elif link_alt in processed_source_links_alt:
                    precise_matches += 1
                    matched_urls.append(f"{link} -> {link_alt} (通过替代形式匹配)")
                    continue
                
                # 额外检查 - 博客基本路径匹配
                # 例如：如果processed_source_links中有 "https://intezer.com/blog"，
                # 而link是 "https://intezer.com/blog/page/3/"
                base_path_match = False
                for processed_url in processed_source_links:
                    # 检查link_norm是否以processed_url开头
                    if link_norm.startswith(processed_url + '/'):
                        precise_matches += 1
                        matched_urls.append(f"{link} -> {processed_url} (通过基本路径匹配)")
                        base_path_match = True
                        break
                
                if base_path_match:
                    continue
                
                # 如果所有匹配都失败，添加到精确过滤后的URL列表
                precise_filtered_urls.append(link)
            
            # 继续进行域名级别的模式匹配过滤
            domain_groups = {}
            for url in processed_source_links:
                domain_key = normalize_url_for_domain_comparison(url)
                if domain_key not in domain_groups:
                    domain_groups[domain_key] = []
                domain_groups[domain_key].append(url)
            
            # 打印域名分组信息
            print(f"已处理的链接包含 {len(domain_groups)} 个不同域名组")
            
            # 过滤掉已处理域名的链接
            filtered_page_urls = []
            pattern_matches = 0
            
            for link in precise_filtered_urls:  # 注意这里使用的是前一步过滤后的链接列表
                domain_key = normalize_url_for_domain_comparison(link)
                
                # 如果该域名组已经处理过，检查是否需要跳过
                if domain_key in domain_groups:
                    # 检查是否已有相似页面被处理
                    skip = False
                    processed_urls_in_domain = domain_groups[domain_key]
                    
                    # 对于同一域名，查看页码模式是否已处理
                    link_page_pattern = extract_page_pattern(link)
                    
                    for processed_url in processed_urls_in_domain:
                        processed_page_pattern = extract_page_pattern(processed_url)
                        
                        # 如果页码模式相同，则跳过
                        if link_page_pattern and processed_page_pattern and link_page_pattern == processed_page_pattern:
                            skip = True
                            pattern_matches += 1
                            break
                    
                    if not skip:
                        filtered_page_urls.append(link)
                else:
                    # 完全不同的域名，添加到待处理列表
                    filtered_page_urls.append(link)
            
            page_urls = filtered_page_urls
            logging.info(f"页码模式匹配: 过滤掉 {pattern_matches} 个已处理的链接")
            logging.info(f"移除了总计 {len(existing_page_links) - len(page_urls)} 个已处理的列表页，剩余 {len(page_urls)} 个待处理")
        else:
            page_urls = existing_page_links


        # 从数据库动态加载要爬取的博客列表页
        try:
            from simple_config_loader import load_crawl_urls
            new_urls = load_crawl_urls()
            print(f"从数据库加载了 {len(new_urls)} 个爬取URL")
        except Exception as e:
            print(f"从数据库加载爬取URL失败: {e}")
            print("使用备用硬编码URL...")
            # 备用硬编码列表
            new_urls = [
                "https://www.microsoft.com/en-us/security/blog/topic/threat-intelligence/",
                'https://blog.knowbe4.com/page/1',
                'https://www.recordedfuture.com/blog',
                'https://threatconnect.com/blog/',
                'https://www.varonis.com/blog/page/1#blog-listing',
                'https://www.clearskysec.com/blog/page/4/',
                'https://www.trellix.com/blogs/platform/',
            ]

        page_urls.extend(new_urls)

        #  测试 
        page_urls=page_urls[:5]

        print(f"最后使用的page_urls: {len(page_urls)}个")

        discovered_urls = []
        article_count = 0
        page_failure_count = 0  
        total_failure_count = 0 


        # 对每个分页URL尝试替换页码，总是检查前几页以获取最新文章
        for url_index, base_url in enumerate(page_urls):
            try:
                logging.info(f"\n处理URL [{url_index+1}/{len(page_urls)}]: {base_url}")
                
                # 如果启用断点续传并且该列表页已处理，则跳过
                if resume and base_url in processed_source_links:
                    logging.info(f"该列表页已处理过，跳过: {base_url}")
                    continue
                
                # 优先匹配URL路径中的页码，而不是查询参数中的页码
                path_page_patterns = [
                    r'/page/(\d+)/?', 
                    r'/page/(\d+)$',
                    r'/page(\d+)/?',
                    r'/p/(\d+)/?',
                ]
                
                # 查询参数中的页码
                query_page_patterns = [
                    r'[?&]page=(\d+)',
                    r'[?&]p=(\d+)',
                    r'[?&]pg=(\d+)',
                ]
                
                # 先尝试匹配路径中的页码
                current_page = None
                matched_pattern = None
                pattern_location = "path"  # 标记页码在路径中还是查询参数中
                matched_text = None  # 存储完整匹配的文本
                
                # 首先检查路径中的页码
                for pattern in path_page_patterns:
                    match = re.search(pattern, base_url)
                    if match:
                        current_page = int(match.group(1))
                        matched_pattern = pattern
                        matched_text = match.group(0)  # 保存完整匹配的文本
                        break
                
                # 如果路径中没有找到页码，再检查查询参数
                if not current_page or not matched_pattern:
                    for pattern in query_page_patterns:
                        match = re.search(pattern, base_url)
                        if match:
                            current_page = int(match.group(1))
                            matched_pattern = pattern
                            pattern_location = "query"
                            matched_text = match.group(0)  # 保存完整匹配的文本
                            break
                
                if not current_page or not matched_pattern:
                    logging.warning(f"无法在URL {base_url} 中找到可替换的页码，跳过")
                    # 记录为已处理，避免重复处理
                    processed_source_links.add(base_url)
                    # 保存已处理的列表页
                    with open(processed_list_pages_file, 'a', encoding='utf-8') as f:
                        f.write(f"{base_url}\n")
                    continue

                # 包含最新的文章,其实只爬一周的数据，第一页就够了
                test_pages = [1, 2]
                
                # 如果当前页已经是前2页中的一个，则排除掉
                if current_page in test_pages:
                    test_pages.remove(current_page)

                logging.info(f"将尝试固定页码: {test_pages}，以获取最新文章")

                discovered_page_links = []
                page_successfully_processed = False  # 标记是否成功处理了至少一个页面

                articles_found_in_current_base_url = False  # 跟踪当前基础URL是否找到文章

                for new_page in test_pages:
                    # 如果是第一页之后的页面，并且第一页没有发现文章，则跳过
                    if new_page != 1 and new_page != test_pages[0] and not articles_found_in_current_base_url:
                        print(f"第一页没有发现新文章，跳过剩余页码")
                        break
                        
                    # 替换页码 - 使用完整匹配的文本直接替换，避免正则表达式问题
                    if pattern_location == "path":
                        # 如果页码在路径中
                        if "/page/" in matched_text:
                            new_matched_text = f"/page/{new_page}/"
                        elif "/page" in matched_text:
                            new_matched_text = f"/page{new_page}/"
                        elif "/p/" in matched_text:
                            new_matched_text = f"/p/{new_page}/"
                        else:
                            new_matched_text = matched_text.replace(str(current_page), str(new_page))
                        
                        # 防止重复斜杠问题
                        new_url = base_url.replace(matched_text, new_matched_text)
                        # 修复可能产生的双问号问题
                        new_url = new_url.replace("//", "/").replace("??", "?")
                    else:
                        # 如果页码在查询参数中
                        if "page=" in matched_text:
                            new_matched_text = matched_text.replace(f"page={current_page}", f"page={new_page}")
                        elif "p=" in matched_text:
                            new_matched_text = matched_text.replace(f"p={current_page}", f"p={new_page}")
                        elif "pg=" in matched_text:
                            new_matched_text = matched_text.replace(f"pg={current_page}", f"pg={new_page}")
                        else:
                            new_matched_text = matched_text.replace(str(current_page), str(new_page))
                        
                        new_url = base_url.replace(matched_text, new_matched_text)
                                    
                    # 修复URL格式问题
                    new_url = new_url.replace("//", "/").replace("/?", "?").replace("??", "?")
                    # 恢复协议中的双斜杠
                    if "://" not in new_url and "http" in new_url:
                        new_url = new_url.replace("http:/", "http://").replace("https:/", "https://")
                    
                    # 如果该页面已处理过，跳过
                    if resume and new_url in processed_source_links:
                        print(f"该列表页已处理过，跳过: {new_url}")
                        continue
                        
                    # 测试URL是否可访问
                    try:
                        headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                            'Accept-Language': 'en-US,en;q=0.5',
                            'Connection': 'keep-alive',
                            'Upgrade-Insecure-Requests': '1',
                        }

                        # 直接使用GET请求，并允许重定向
                        print(f"尝试新的分页URL: {new_url}")
                        response = requests.get(new_url, headers=headers, timeout=15, allow_redirects=True)
                        
                        # 如果是重定向，输出最终URL
                        if response.url != new_url:
                            print(f"页面已重定向: {new_url} -> {response.url}")
                            new_url = response.url  # 使用重定向后的URL 
 
                        # 获取页面内容
                        response = requests.get(new_url, headers=headers, timeout=10)
                        
                        if response.status_code == 200:
                            # 标记此列表页为已处理
                            processed_source_links.add(new_url)
                            # 保存已处理的列表页
                            with open(processed_list_pages_file, 'a', encoding='utf-8') as f:
                                f.write(f"{new_url}\n")
                            
                            soup = BeautifulSoup(response.text, 'html.parser')
                            
                            # 显示找到的链接数量
                            all_links = soup.find_all('a', href=True)
                            print(f"页面中共找到 {len(all_links)} 个链接")
                            
                            # 查找所有文章容器 - 通常按日期排序的列表页有特定的容器类
                            article_containers = []
                            
                            # 尝试常见的文章容器标识
                            container_selectors = [
                                'article', '.post', '.entry', '.article', '.news-item',
                                'div.post', 'div.hentry', 'div.blog-entry', '.story'
                            ]
                            
                            for selector in container_selectors:
                                containers = soup.select(selector)
                                if containers:
                                    print(f"使用选择器 '{selector}' 找到 {len(containers)} 个文章容器")
                                    article_containers.extend(containers)
                            
                            # 如果找到文章容器，从中提取链接
                            article_links = []
                            if article_containers:
                                # 从容器中提取链接
                                for container in article_containers:
                                    links = container.find_all('a', href=True)
                                    for link in links:
                                        href = link['href']
                                        title = link.get_text().strip()
                                        if title and len(title) > 10:  # 只获取有实际标题的链接
                                            article_links.append((href, title))
                                
                                # 去除重复链接
                                unique_articles = []
                                seen_urls = set()
                                for href, title in article_links:
                                    full_url = urllib.parse.urljoin(new_url, href)
                                    if full_url not in seen_urls:
                                        seen_urls.add(full_url)
                                        unique_articles.append((full_url, title))
                                
                                print(f"从文章容器中找到 {len(unique_articles)} 个唯一文章链接")
                                
                                # 如果找不到特定容器，回退到直接处理所有链接
                                if not unique_articles:
                                    print("未找到文章容器，尝试直接分析页面中的链接...")
                                    # 跳至下面的备用链接提取逻辑
                            else:
                                print("未找到文章容器，尝试直接分析页面中的链接...")
                                # 直接跳至下面的备用链接提取逻辑

                            # === 备用链接提取逻辑 ===
                            # 如果没有通过容器找到文章，尝试直接从所有链接中筛选
                            if not article_containers or not unique_articles:
                                print("使用备用方法提取链接...")
                                potential_articles = []
                                seen_urls = set()
                                
                                # 找出所有可能的文章链接
                                for a_tag in all_links:
                                    href = a_tag['href']
                                    title = a_tag.get_text().strip()
                                    
                                    # 过滤明显的非文章链接
                                    skip_patterns = ['/category/', '/tag/', '/page/', '?page=', '/wp-', '/feed/', '/about/']
                                    if any(pattern in href.lower() for pattern in skip_patterns):
                                        continue
                                        
                                    # 简单过滤 - 标题必须有一定长度，URL必须是同站的
                                    if len(title) > 20 and new_url.split('/')[2] in href:
                                        full_url = urllib.parse.urljoin(new_url, href)
                                        if full_url not in seen_urls:
                                            seen_urls.add(full_url)
                                            potential_articles.append((full_url, title))
                                
                                # 创建一个有序的唯一链接列表
                                unique_articles = []
                                for url, title in potential_articles:
                                    if url not in [item[0] for item in unique_articles]:
                                        unique_articles.append((url, title))
                                
                                print(f"通过备用方法找到 {len(unique_articles)} 个可能的文章链接")

                            # 现按发现顺序排列的链接列表，取前50条进行处理,拿取2025年的
                            max_articles_to_process = 50
                            articles_to_process = unique_articles[:max_articles_to_process]
                            
                            print(f"将处理前 {len(articles_to_process)} 条文章链接:")
                            for i, (article_url, article_title) in enumerate(articles_to_process):
                                print(f"{i+1}. {article_title[:50]}... -> {article_url}")
                            
                            # 处理获取到的文章链接
                            print(f"正在处理 {len(articles_to_process)} 篇文章链接...")
                            page_article_count = 0
                            for full_url, title_text in articles_to_process:
                                try:
                                    # 检查是否已经添加过这个链接
                                    if full_url in [link['link'] for link in discovered_urls] or full_url in processed_detail_links:
                                        print(f"链接已存在，跳过")
                                        continue

                                    # 跳过非文章详情页
                                    if not is_detail_page(full_url):
                                        print(f" 非详情页链接或者内容不足500字符，跳过: {full_url}")
                                        continue
                                    
                                    # 修改为：
                                    new_data = parse_date_to_datetime(new_data)

                                    if not new_data:
                                        print(f"无法解析日期 {new_data}，使用默认的近期日期")
                                        new_data = datetime.now() - timedelta(days=14)  # 默认使用两周前的日期

                                    report_date = find_date(full_url)

                                    # 日期检查，修改为年月日进行判断
                                    if report_date:
                                        report_date_obj = parse_date_to_datetime(report_date)
                                        if report_date_obj:
                                            
                                            # 更精确的日期比较（包括日）
                                            if report_date_obj < new_data:
                                                print(f"文章太旧 ({report_date})，跳过")
                                                continue
                                        
                                    # 检查URL是否有效
                                    parsed_url = urllib.parse.urlparse(full_url)
                                    if not all([parsed_url.scheme, parsed_url.netloc]):
                                        print(f"无效URL，跳过: {full_url}")
                                        continue

                                    # 构建链接数据
                                    link_data = {
                                        'link': full_url,
                                        'source_link': new_url,
                                        'query': f"discovered_from:{new_url}",
                                        'title': title_text[:255],
                                        'snippet': f"从列表页 {new_url} 发现的文章"
                                    }
                                    
                                    # 添加到结果列表
                                    discovered_urls.append(link_data)
                                    page_article_count += 1
                                    
                                    # 将此详情页标记为已处理
                                    processed_detail_links.add(full_url)
                                    
                                    # 保存已处理的详情页
                                    with open(processed_detail_pages_file, 'a', encoding='utf-8') as f:
                                        f.write(f"{full_url}\n")
                                    
                                    # 保存到CSV，和processed_links_set进行去重
                                    try:
                                        with open(csv_path, mode='a', newline='', encoding='utf-8') as f:
                                            writer = csv.DictWriter(f, fieldnames=['link', 'source_link', 'title', 'query', 'snippet'])
                                            if os.path.getsize(csv_path) == 0:
                                                writer.writeheader()
                                            if link_data['link'] not in processed_links_set:  # 使用集合进行O(1)时间复杂度查找
                                                writer.writerow(link_data)
                                        print(f"已保存到CSV: {title_text[:40]}...")
                                    except Exception as e:
                                        print(f"CSV保存失败: {str(e)}")
                                
                                except Exception as e:
                                    print(f"处理链接时出错: {str(e)}")
                                    continue
                            
                            print(f"从页面 {new_url} 成功提取了 {page_article_count} 篇文章")
                            article_count += page_article_count
                            
                            # 标记当前页面成功处理
                            if page_article_count > 2:
                                articles_found_in_current_base_url = True
                                page_successfully_processed = True
                                if new_page == 1 or new_page == test_pages[0]:
                                    print(f"在首页找到 {page_article_count} 篇新文章，继续检查后续页面")
                            elif new_page == 1 or new_page == test_pages[0]:
                                print(f"该链接首页未找到新文章，跳过后续页面")
                                break  # 如果第一页没有新文章，直接跳出循环
                        else:
                            print(f"无法访问页面 {new_url}，状态码: {response.status_code}")
                            page_failure_count += 1 
                            continue
                    
                    except Exception as e:
                        print(f"处理页面时出错: {str(e)}")
                        traceback.print_exc()
                        total_failure_count += 1

                        # 检查当前页面失败次数
                        if page_failure_count >= 5:
                            print(f"当前URL已失败 {page_failure_count} 次，跳过剩余页码")
                            break
                            
                        # 检查总失败次数
                        if total_failure_count >= 7:
                            print(f"总失败次数已达 {total_failure_count} 次，终止搜索")
                            return {
                                'success': True,
                                'results': discovered_urls,
                                'count': len(discovered_urls),
                                'terminated_early': True
                            }
                        continue
                
                # 如果成功处理了至少一个页面，将原始URL也标记为已处理
                if page_successfully_processed:
                    processed_source_links.add(base_url)
                    # 保存已处理的列表页
                    with open(processed_list_pages_file, 'a', encoding='utf-8') as f:
                        f.write(f"{base_url}\n")
                
            except Exception as e:
                print(f"处理基础URL {base_url} 时出错: {str(e)}")
                traceback.print_exc()
                continue

        # 生成爬取统计报告
        print("\n" + "="*50)
        print(f"爬取完成! 统计数据:")
        print(f"- 总共处理列表页: {len(processed_source_links)} 个")
        print(f"- 发现新文章: {len(discovered_urls)} 篇")
        print(f"- 失败次数: {total_failure_count} 次")
        print("="*50 + "\n")

        # 处理CSV文件中的重复数据
        if os.path.exists(csv_path):
            try:
                # 读取CSV文件
                all_links = []
                with open(csv_path, mode='r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        all_links.append(row)
                
                print(f"从CSV文件读取了 {len(all_links)} 条记录")
                
                # 检查是否有重复的link
                unique_links = set()
                unique_data = []
                duplicates = 0
                
                for item in all_links:
                    link = item.get('link', '')
                    if link and link not in unique_links:
                        unique_links.add(link)
                        unique_data.append(item)
                    else:
                        duplicates += 1
                
                # 输出统计信息
                print(f"发现并移除了 {duplicates} 条重复链接，保留了 {len(unique_data)} 条唯一链接")
                
                # 如果有重复项被移除，则重写CSV文件
                if duplicates > 0:
                    with open(csv_path, mode='w', newline='', encoding='utf-8') as f:
                        if unique_data:
                            fieldnames = unique_data[0].keys()
                            writer = csv.DictWriter(f, fieldnames=fieldnames)
                            writer.writeheader()
                            writer.writerows(unique_data)
                            print(f"已重写CSV文件，移除了所有重复项")
                        else:
                            print(f"没有唯一记录，CSV文件已清空")
                
            except Exception as e:
                print(f"处理CSV文件时出错: {str(e)}")
                traceback.print_exc()
        
        return {
            'success': True,
            'results': discovered_urls,
            'count': len(discovered_urls)
        }
    
    except Exception as e:
        print(f"发现新内容时出错: {str(e)}")
        traceback.print_exc()
        return {'success': False, 'error': str(e)}
    
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()



def process_discovered_content(csv_path=None, new_data=None):
    """
    处理发现的新内容，通过ArchiveBox存档后由分析线程统一分析

    新流程：
    1. 发现新链接并保存到CSV
    2. 将链接提交给ArchiveBox进行存档
    3. 分析线程会自动检测并分析新快照

    Args:
        csv_path (str): CSV文件路径
        new_data (datetime): 数据时间范围
    """
    print("\n=== 新流程：通过ArchiveBox存档后统一分析 ===")

    from Archivebox_cti import add_url_to_archivebox

    # 如果未提供日期，使用默认值
    if not new_data:
        new_data = datetime.now() - timedelta(days=14)  # 默认获取近两周的文章

    # 1. 发现新内容
    print("步骤1: 发现新内容...")
    if not os.path.exists(csv_path):
        discovery_result = discover_new_content(csv_path, new_data)

        if not discovery_result.get('success', False):
            logging.error("发现内容过程失败或没有新内容")
            return False
    else:
        logging.info(f"使用现有CSV文件: {csv_path}")
        discovery_result = {'success': True, 'count': 1}

    # 获取结果
    count = discovery_result.get('count', 0)

    # 2. 检查CSV文件内容
    print("步骤2: 检查CSV文件内容...")
    if not os.path.exists(csv_path):
        logging.error(f"CSV文件不存在: {csv_path}")
        return False

    # 读取CSV中的链接
    links_to_archive = []
    try:
        with open(csv_path, mode='r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                link = row['link']
                title = row.get('title', '未知标题')

                # 基本过滤：只处理详情页
                if is_detail_page(link) or len(link) > 35:
                    links_to_archive.append({'link': link, 'title': title})

    except Exception as e:
        print(f"读取CSV文件出错: {e}")
        return False

    total_links = len(links_to_archive)
    if total_links == 0:
        print("没有待处理的有效链接")
        return False

    print(f"发现 {total_links} 个待存档链接")


    # 3. 批量提交到ArchiveBox进行存档
    print("步骤3: 批量提交到ArchiveBox进行存档...")
    archived_count = 0
    failed_count = 0

    # 测试：只处理前5个链接
    links_to_archive = links_to_archive[:5]
    total_links = len(links_to_archive)  # 重新计算实际要处理的链接数量

    for i, link_data in enumerate(links_to_archive):
        link = link_data['link']
        title = link_data['title']

        print(f"[{i+1}/{total_links}] 存档: {title[:50]}...")

        try:
            success, snapshot_id = add_url_to_archivebox(link, timeout=1000)
            if success:
                archived_count += 1
                print(f"✓ 存档成功: {snapshot_id}")
            else:
                failed_count += 1
                print(f"✗ 存档失败")
        except Exception as e:
            failed_count += 1
            print(f"✗ 存档出错: {e}")

    print(f"存档完成: 成功 {archived_count} 个, 失败 {failed_count} 个")

    print(f"✓ 链接存档完成，分析线程会自动检测并分析新快照")
    return True

    
def update_task_status(status, error_message=None):
    """更新当前任务状态，如果任务完成且有定时调度，则创建下一个等待任务"""
    try:
        import mysql.connector
        from mysql.connector import Error
        import json

        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 查找最近的weekly_crawl任务
        cursor.execute("""
            SELECT id, task_params FROM analysis_tasks
            WHERE task_type = 'weekly_crawl'
            AND task_status = 'running'
            ORDER BY start_time DESC
            LIMIT 1
        """)

        result = cursor.fetchone()
        if result:
            task_id = result[0]
            task_params_json = result[1]

            # 更新当前任务状态
            if error_message:
                cursor.execute("""
                    UPDATE analysis_tasks
                    SET task_status = %s, error_message = %s, end_time = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (status, error_message, task_id))
            else:
                cursor.execute("""
                    UPDATE analysis_tasks
                    SET task_status = %s, end_time = CURRENT_TIMESTAMP
                    WHERE id = %s
                """, (status, task_id))

            conn.commit()
            print(f"任务状态已更新为: {status}")

            # 如果任务成功完成，检查是否需要创建下一个等待任务
            if status == 'completed' and task_params_json:
                try:
                    task_params = json.loads(task_params_json) if isinstance(task_params_json, str) else task_params_json
                    is_scheduled = task_params.get('scheduled', False)
                    is_manual = task_params.get('manual', False)

                    # 检查是否应该创建下一个任务
                    should_create_next = False

                    if is_scheduled:
                        # 定时调度任务，直接创建下一个任务
                        should_create_next = True
                        print("检测到定时调度任务完成，创建下一个等待任务...")
                    elif is_manual:
                        # 手动任务，检查是否需要为定时调度器创建下一个任务
                        # 简化逻辑：如果是手动任务且用户希望启动定时调度，也创建下一个任务
                        # 这里我们总是为手动任务创建下一个任务，让定时调度器决定是否执行
                        should_create_next = True
                        print("检测到手动任务完成，创建下一个等待任务以供定时调度器使用...")

                    if should_create_next:
                        # 创建下一个pending状态的任务
                        next_task_params = {
                            'script_path': 'pa_week_ar_test.py',
                            'log_file': 'crawl_week.log',
                            'description': '每周威胁情报网站爬取任务（等待下次执行）',
                            'scheduled': True,
                            'next_scheduled': True
                        }

                        cursor.execute("""
                            INSERT INTO analysis_tasks (
                                task_type, task_status, task_params, total_count,
                                completed_count, success_count, error_count, created_by
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            'weekly_crawl', 'pending', json.dumps(next_task_params), 1,
                            0, 0, 0, 'scheduler'
                        ))

                        conn.commit()
                        next_task_id = cursor.lastrowid
                        print(f"已创建下次执行的任务记录，任务ID: {next_task_id}")

                except Exception as e:
                    print(f"创建下次任务记录失败: {e}")
                    import traceback
                    traceback.print_exc()

        else:
            print("未找到运行中的weekly_crawl任务")

    except Exception as e:
        print(f"更新任务状态失败: {e}")
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()


# 现在使用batch_analyze_snapshots函数，它会：
# 1. 提取快照内容 (extract_single_url_by_id)
# 2. 进行内容质量检查 (check_content_quality)
# 3. 分析内容 (analyze_archived_content)
# 4. 自动保存到crawled_data和rag_analysis两个表
# 下次查询时会自动排除已分析的快照（通过数据库查询）

def check_pending_analysis():
    """检查是否有待分析的快照 - 完全复用app.py的batch_analyze_unanalyzed逻辑"""
    try:
        from Archivebox_cti import SnapshotManager

        # 创建SnapshotManager实例（复用app.py的逻辑）
        snapshot_manager = SnapshotManager()

        # 获取所有待处理的快照（现在会查询ArchiveBox数据库）
        pending_snapshots = snapshot_manager.get_pending_snapshots(limit=10)

        if not pending_snapshots:
            print("没有找到待分析的快照")
            return []

        print(f"[分析线程] SnapshotManager找到 {len(pending_snapshots)} 个待分析快照")

        # 显示快照信息用于调试（按时间排序，旧的在前）
        for i, snapshot in enumerate(pending_snapshots[:3]):  # 只显示前3个
            snapshot_id = snapshot.get('id', 'unknown')
            snapshot_url = snapshot.get('url', 'unknown')
            print(f"[分析线程]   [{i+1}] {snapshot_id} - {snapshot_url[:50]}... (按时间排序，优先分析旧快照)")

        # 直接返回SnapshotManager的结果，不需要额外过滤
        # SnapshotManager.get_pending_snapshots()已经处理了所有逻辑
        return pending_snapshots

    except Exception as e:
        print(f"检查待分析快照时出错: {e}")
        import traceback
        traceback.print_exc()
        return []

def batch_analyze_snapshots(snapshots):
    """批量分析快照 - 完全复用app.py的batch_analyze_unanalyzed逻辑"""
    try:
        from Archivebox_cti import extract_single_url_by_id, analyze_archived_content

        success_count = 0
        error_count = 0

        print(f"[分析线程] 开始批量分析 {len(snapshots)} 个快照...")

        # 批量处理快照（完全复用app.py的逻辑）
        for i, snapshot in enumerate(snapshots):
            try:
                snapshot_id = snapshot['id']
                snapshot_url = snapshot['url']

                print(f"[分析线程] [{i+1}/{len(snapshots)}] 分析快照: {snapshot_id} - {snapshot_url[:50]}...")

                # 提取快照内容
                content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id, snapshot_url)

                if content or html_content:
                    print(f"[分析线程] 成功提取快照内容: {snapshot_id}")

                    # 内容质量检查 - 在分析前过滤低价值内容
                    quality_check = check_content_quality(content, title, snapshot_url)

                    if not quality_check['should_analyze']:
                        print(f"[分析线程] 检测到低价值内容: {snapshot_id} - {quality_check['reason']}")

                        # 删除低价值快照，避免重复处理
                        from Archivebox_cti import remove_archivebox_snapshot
                        delete_result = remove_archivebox_snapshot(snapshot_id)
                        if delete_result == "success":
                            print(f"[分析线程] 已删除低价值快照: {snapshot_id}")
                        elif delete_result == "not_found":
                            print(f"[分析线程] 快照不存在，可能已被删除: {snapshot_id}")
                        else:
                            print(f"[分析线程] 删除快照失败: {snapshot_id}")

                        continue

                    print(f"[分析线程] 内容质量检查通过: {quality_check['reason']}")

                    # 分析内容
                    analysis_result = analyze_archived_content(
                        content=content,
                        html_content=html_content,
                        url=snapshot_url,
                        title=title,
                        publication_date=publication_date,
                        analyze_after_crawl=True,  # 强制分析
                        snapshot_id=snapshot_id  # 传入快照ID，用于删除无效快照
                    )

                    if analysis_result and analysis_result.get('success', False):
                        success_count += 1
                        print(f"[分析线程] 快照分析完成: {snapshot_id}")
                    else:
                        error_count += 1
                        error_reason = analysis_result.get('error', '未知错误') if analysis_result else '分析结果为空'
                        print(f"[分析线程] 快照分析失败: {snapshot_id} - {error_reason}")
                else:
                    error_count += 1
                    print(f"[分析线程] 快照内容提取失败: {snapshot_id}")

                # 避免过于频繁的分析，稍作延迟
                time.sleep(1)

            except Exception as e:
                error_count += 1
                error_msg = str(e)
                print(f"处理快照时出错 {snapshot.get('id', 'unknown')}: {error_msg}")

        print(f"[分析线程] 批量分析完成: 成功 {success_count} 个, 失败 {error_count} 个")
        return success_count

    except Exception as e:
        print(f"批量分析快照时出错: {e}")
        import traceback
        traceback.print_exc()
        return 0

def main():
    """主调度程序：新的三线程架构 - 单次执行版本"""
    print(f"\n=== 开始执行威胁情报爬取任务 ===")
    print(f"执行时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
            # 获取当前日期，并默认获取前一周的文章
            now_date = datetime.now()
            logging.info(f"当前日期: {now_date.strftime('%Y-%m-%d')}")
            new_data = now_date - timedelta(weeks=1)

            # 生成动态CSV路径
            csv_path = f"threat_intel_links_{now_date.strftime('%Y%m%d')}.csv"

            print("开始执行新的三线程架构...")

            import threading
            import time

            # 线程结果字典
            thread_results = {
                'week_data_success': False,
                'archive_success': False,
                'analysis_count': 0,
                'threads_running': True
            }

            def week_data_worker():
                """线程1: Week Data线程 - 搜索和发现新的威胁情报链接"""
                try:
                    print("启动Week Data线程 - 只负责搜索API爬取...")

                    # 只执行搜索API
                    search_success = week_data()
                    thread_results['week_data_success'] = search_success

                    print(f"Week Data线程完成 - {'成功' if search_success else '失败'}")

                except Exception as e:
                    print(f"Week Data线程出错: {e}")
                    thread_results['week_data_success'] = False

            def url_archive_worker():
                """线程2: URL存档线程 - 读取CSV并提交给ArchiveBox存档"""
                try:
                    print("启动URL存档线程 - 发现新内容并存档...")

                    # 步骤1: 发现新内容，生成CSV
                    print("步骤1: 发现新内容...")
                    discovery_result = discover_new_content(csv_path, new_data)

                    if discovery_result.get('success', False):
                        print("步骤2: 存档发现的链接...")
                        # 步骤2: 存档CSV中的链接
                        archive_success = process_discovered_content(csv_path, new_data)
                        thread_results['archive_success'] = archive_success
                        print(f"URL存档完成 - {'成功' if archive_success else '失败'}")
                    else:
                        print("未发现新内容，跳过存档")
                        thread_results['archive_success'] = False

                except Exception as e:
                    print(f"URL存档线程出错: {e}")
                    thread_results['archive_success'] = False

            def analysis_worker():
                """线程3: 分析线程 - 持续检测并分析新快照"""
                try:
                    print("启动分析线程 - 持续检测新快照...")
                    analysis_count = 0

                    while thread_results['threads_running']:
                        # 检查是否有待分析的快照
                        pending_snapshots = check_pending_analysis()

                        if pending_snapshots:
                            print(f"[分析线程] 发现 {len(pending_snapshots)} 个待分析快照")
                            success_count = batch_analyze_snapshots(pending_snapshots)
                            analysis_count += success_count
                            print(f"[分析线程] 本轮分析完成: 成功 {success_count} 个")
                        else:
                            print("[分析线程] 暂无待分析内容，等待5分钟...")

                        # 等待5分钟后再次检查
                        time.sleep(300)  # 5分钟 = 300秒

                    thread_results['analysis_count'] = analysis_count
                    print(f"分析线程完成，总共分析了 {analysis_count} 个快照")

                except Exception as e:
                    print(f"分析线程出错: {e}")
                    thread_results['analysis_count'] = 0

            # 启动三线程架构 - 按正确顺序
            print("启动三线程架构...")

            # 创建线程对象
            week_data_thread = threading.Thread(target=week_data_worker, name="WeekDataWorker")
            url_archive_thread = threading.Thread(target=url_archive_worker, name="URLArchiveWorker")
            analysis_thread = threading.Thread(target=analysis_worker, name="AnalysisWorker")

            # 第一步：同时启动Week Data线程和分析线程
            print("启动Week Data线程和分析线程...")
            week_data_thread.start()
            analysis_thread.start()

            # 第二步：等待Week Data线程完成
            print("等待Week Data线程完成...")
            week_data_thread.join()
            print(f"Week Data线程完成 - {'成功' if thread_results['week_data_success'] else '失败'}")

            # 第三步：启动URL存档线程
            print("启动URL存档线程...")
            url_archive_thread.start()

            # 等待URL存档线程完成
            print("等待URL存档线程完成...")
            url_archive_thread.join()
            print(f"URL存档线程完成 - {'成功' if thread_results['archive_success'] else '失败'}")

            # 第四步：停止分析线程
            print("停止分析线程...")
            thread_results['threads_running'] = False
            analysis_thread.join(timeout=60)  # 最多等待1分钟

            print(f"三线程架构完成:")
            print(f"   - Week Data: {'成功' if thread_results['week_data_success'] else '失败'}")
            print(f"   - URL存档: {'成功' if thread_results['archive_success'] else '失败'}")
            print(f"   - 分析快照: {thread_results['analysis_count']} 个")

            # 最终检查是否还有遗漏的快照需要分析
            print("\n最终检查遗漏的快照...")
            try:
                final_check_pending = check_pending_analysis()
                if final_check_pending:
                    print(f"发现 {len(final_check_pending)} 个遗漏的待分析快照，立即处理...")
                    success_count = batch_analyze_snapshots(final_check_pending)
                    thread_results['analysis_count'] += success_count
                    print(f"最终检查完成，额外分析了 {success_count} 个快照")
                else:
                    print("没有遗漏的快照")
            except Exception as e:
                print(f"最终检查出错: {e}")

            # 判断整体执行结果
            overall_success = (thread_results['week_data_success'] or
                             thread_results['archive_success'] or
                             thread_results['analysis_count'] > 0)

            if overall_success:
                print(f"本次执行成功")
                update_task_status('completed')
            else:
                print("本次执行未发现新数据或处理失败")
                update_task_status('completed')  # 即使没有新数据，任务也算完成

    except KeyboardInterrupt:
        print("\n用户手动终止程序")
        # 更新任务状态为取消
        update_task_status('cancelled', '用户手动终止')
        return False
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")
        traceback.print_exc()
        print("本次执行失败")
        # 更新任务状态为失败
        update_task_status('failed', str(e))
        return False

    # 任务执行完成
    print("威胁情报爬取任务执行完成")
    return True


if __name__ == "__main__":
    main()
