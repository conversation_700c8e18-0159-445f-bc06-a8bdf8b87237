""""
处理关键词+域名语法检索到的网页链接，保存成html,以供后续处理

"""
import os
import io
import json
import time
import re
import hashlib
import sys
from typing import Dict, List, Optional
import urllib3
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from readability import Document
from pathlib import Path
import requests
from requests.exceptions import RequestException
import mysql.connector
from mysql.connector import Error

import PyPDF2
from io import BytesIO

from bs4 import XMLParsedAsHTMLWarning
import warnings

warnings.filterwarnings("ignore", category=XMLParsedAsHTMLWarning)


# # 强制标准流使用UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

# 禁用SSL警告
urllib3.disable_warnings()

# MySQL 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

""""
代理，工具

"""

def sanitize_filename(filename):
    """清理非法文件名字符"""
    return re.sub(r'[\\/*?:"<>|]', '_', filename)

def extract_keyword(query):
    """从搜索语句中提取主关键词"""
    match = re.search(r'"([^"]+)"', query)
    return match.group(1) if match else "unknown"

class HttpManager:
    """自定义HTTP客户端"""
    def __init__(self):
        # 配置代理
        self.proxy_url = "http://127.0.0.1:7890"
        
        # 配置重试策略
        retries = urllib3.Retry(
            total=3,
            backoff_factor=0.1,
            status_forcelist=[500, 502, 503, 504],
            redirect=3  # 处理重定向
        )
        
        # 创建代理连接池
        self.http = urllib3.ProxyManager(
            self.proxy_url,
            cert_reqs='CERT_NONE',  # 禁用证书验证
            retries=retries,
            timeout=urllib3.Timeout(connect=5.0, read=10.0)
        )

    def request(self, method, url, headers=None):
        """发送HTTP请求"""
        return self.http.request(method, url, headers=headers)

def is_detail_page(url, html_content):
    """简单版本的详情页检测函数"""
    # 检查是否为XML文件
    if url.lower().endswith('.xml'):
        return False

    # 检查URL模式 - 排除非详情页URL
    exclude_patterns = [
        '/tag/', '/category/', '/author/',  
        '/search/', '/page/', '/feed/',
        'sitemap.xml', 'robots.txt'
    ]
    
    # 简单字符串包含检查，不使用正则表达式
    for pattern in exclude_patterns:
        if pattern in url:
            # print(f"排除非详情页: {url}")
            return False
    
    return True

""""
数据库类 - 修改为使用MySQL

"""

class CrawlerDB:
    """数据库操作封装类 - MySQL版本"""
    def __init__(self, db_config=None):
        if db_config is None:
            db_config = DB_CONFIG
            
        self.db_config = db_config
        self.conn = mysql.connector.connect(**db_config)
        self.cursor = self.conn.cursor(dictionary=True)
        self._migrate_schema()
        
    def _migrate_schema(self):
        """数据库架构维护 - MySQL版本"""
        # 不需要动态添加字段，我们假设表结构已经存在
        # 只检查表是否存在，如果不存在则创建
        try:
            self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS `crawled_data` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                query TEXT NOT NULL,
                title TEXT,
                link VARCHAR(1024) NOT NULL,
                snippet TEXT,
                html_content LONGTEXT,
                extracted_text LONGTEXT,
                crawl_status TINYINT DEFAULT 0,
                crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY `unique_link` (`link`(768))
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            self.conn.commit()
            print("表结构已检查/创建")
        except Error as e:
            print(f"表结构维护失败: {e}")

    def get_pending_urls(self, limit=100) -> List[Dict]:
        """获取待爬取的URL列表"""
        try:
            self.cursor.execute("""
            SELECT query, link 
            FROM crawled_data 
            WHERE crawl_status = 0 
            LIMIT %s
            """, (limit,))
            
            return self.cursor.fetchall()
        except Error as e:
            print(f"获取待处理URL失败: {e}")
            return []

    def update_crawl_result(self, link: str, html_content: str, status: int):
        """更新爬取结果"""
        try:
            self.cursor.execute("""
            UPDATE crawled_data 
            SET html_content = %s,
                crawl_status = %s,
                crawl_time = CURRENT_TIMESTAMP
            WHERE link = %s
            """, (html_content, status, link))
            
            self.conn.commit()
        except Error as e:
            print(f"数据库更新失败: {str(e)}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        if self.conn.is_connected():
            self.cursor.close()
            self.conn.close()
            print("MySQL连接已关闭")


""""
处理HTML文件，提取正文
"""

def should_filter_text(text):
    """增强版过滤规则"""
    filter_patterns = [
        r'Log\s*in', 
        r'Reset(\s*\w*)*',
        r'Search\s*the\s*site',
        r'All\s*categories',
        r'Sign\s*up',
        r'Table\s*of\s*contents',
        r'Share\s*this\s*post',
        r'Comments?\s*are\s*closed',
        r'Read\s*more',
        r'Follow\s*us',
        r'(value|respect|protect)[s]?[\s\w]*privacy',  
        r'cookie[s]?[\s\w]*use',
        r'by\s*(continuing|using|accessing)',
        r'terms\s*of\s*(use|service)',
        r'privacy\s*(policy|notice|statement)',
        r'同意使用cookies?',
        r'使用Cookie',
    ]
    return any(re.search(pattern, text, re.IGNORECASE) for pattern in filter_patterns)

def clean_extracted_text(text):
    """优化后的文本清理"""
    # 合并重复段落
    text = re.sub(r'(.+?)\n\s*\1', r'\1', text, flags=re.DOTALL)
    # 标准化换行符
    text = re.sub(r'\n{3,}', '\n\n', text)
    # 移除短行噪音
    text = re.sub(r'^\W{0,5}$', '', text, flags=re.MULTILINE)
    return text.strip()

def extract_main_content(html):
    """使用readability-lxml提取正文"""
    doc = Document(html)
    return doc.summary()

# 下载pdf,并提取内容
def download_pdf(url):
    """使用PyPDF2下载并提取PDF文本"""
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        # 使用PyPDF2处理
        pdf_file = BytesIO(response.content)
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        
        text = ""
        for page_num in range(len(pdf_reader.pages)):
            text += pdf_reader.pages[page_num].extract_text()
            
        return text
    except Exception as e:
        print(f"PDF处理失败: {str(e)}")
        return None


def process_pdf_content(pdf_text):
    """结构化处理PDF文本内容"""
    # 分割成行
    lines = pdf_text.split('\n')
    sections = []
    current_paragraph = []
    previous_is_heading = False
    
    # 识别标题和段落
    for line in lines:
        line = line.strip()
        if not line:
            # 空行表示段落结束
            if current_paragraph:
                paragraph_text = ' '.join(current_paragraph)
                if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
                    sections.append(paragraph_text)
                current_paragraph = []
                previous_is_heading = False
            continue
            
        # 可能是标题的特征：短、大写比例高、末尾无标点
        is_heading = (len(line) < 80 and 
                     sum(1 for c in line if c.isupper()) / len(line) > 0.3 and
                     not line[-1] in '.,:;?!')
        
        if is_heading:
            # 将积累的段落内容添加到sections
            if current_paragraph:
                paragraph_text = ' '.join(current_paragraph)
                if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
                    sections.append(paragraph_text)
                current_paragraph = []
            
            # 在标题前添加分隔线（第一个标题除外）
            if sections and not previous_is_heading:
                sections.append('-'*40 + '\n')
                
            # 添加标题
            if not should_filter_text(line):
                sections.append('\n' + line + '\n')
                previous_is_heading = True
        else:
            # 累积段落内容
            current_paragraph.append(line)
    
    # 处理最后一个段落
    if current_paragraph:
        paragraph_text = ' '.join(current_paragraph)
        if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
            sections.append(paragraph_text)
    
    return clean_extracted_text('\n'.join(sections))


def save_webpage(url: str, query: str, stats: Dict[str, int], db: CrawlerDB) -> Optional[str]:
    """
    修改后的爬取函数，返回HTML内容或PDF内容
    支持PDF文件的保存和处理
    """
    # 创建保存PDF的目录
    pdf_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pdf_files")
    if not os.path.exists(pdf_dir):
        os.makedirs(pdf_dir)

    try:
        # 检测URL是否指向PDF文件
        is_pdf = url.lower().endswith('.pdf')
        
        client = HttpManager()
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...'
        }

        resp = client.request('GET', url, headers=headers)
        if resp.status != 200:
            print(f"请求失败 HTTP {resp.status}")
            stats['failed'] += 1
            db.update_crawl_result(url, None, 2)
            return None

        # PDF文件处理
        if is_pdf or 'application/pdf' in resp.headers.get('Content-Type', ''):
            print(f"检测到PDF文件: {url}")
            
            # 提取文件名
            url_parts = urlparse(url)
            file_name = os.path.basename(url_parts.path)
            if not file_name.endswith('.pdf'):
                file_name = hashlib.md5(url.encode()).hexdigest() + '.pdf'
            
            # 保存PDF文件
            safe_filename = sanitize_filename(file_name)
            pdf_path = os.path.join(pdf_dir, safe_filename)
            
            with open(pdf_path, 'wb') as f:
                f.write(resp.data)
            
            print(f"PDF已保存: {pdf_path}")
            stats['success'] += 1
            
            # 将PDF内容作为HTML内容返回，让后续处理
            db.update_crawl_result(url, pdf_path, 1)  # 成功状态，保存文件路径
            return pdf_path  # 返回PDF路径以便后续处理
        
        # HTML内容处理
        content_type = resp.headers.get('Content-Type', '')
        encoding = re.search(r'charset=([\w-]+)', content_type)
        encoding = encoding.group(1) if encoding else 'utf-8'

        html_content = resp.data.decode(encoding, errors='replace')
        
        # 内容过滤判断 - 修复逻辑
        is_detail = is_detail_page(url, html_content)
        if not is_detail:
            print(f"跳过非详情页: {url}")
            stats['skipped'] += 1  # 更新跳过计数
            db.update_crawl_result(url, None, 3)  # 3 = 跳过状态
            return None
        else:
            # 是详情页，继续处理
            return html_content
    
    except RequestException as e:
        print(f"请求异常 [{url}]: {str(e)}")
        stats['failed'] += 1
        db.update_crawl_result(url, None, 2)
        return None
    
    except RequestException as e:
        print(f"请求异常 [{url}]: {str(e)}")
        stats['failed'] += 1
        db.update_crawl_result(url, None, 2)
        return None
    

def process_html_content_from_db(html_content: str, text_length: int = 300) -> Optional[str]:
    """直接从数据库HTML内容提取正文"""
    try:
        # 使用readability提取主体内容
        doc = Document(html_content)
        main_content = doc.summary()
        
        # 精细处理
        soup = BeautifulSoup(main_content, 'html.parser')
        
        # 移除干扰元素
        for tag in ['aside', 'figure', 'svg', 'footer', 'header', 'script', 'style']:
            for element in soup(tag):
                element.decompose()

        # 结构化提取
        sections = []
        current_heading = None
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'p', 'pre']):
            if element.name.startswith('h'):
                current_heading = element.get_text(strip=True)
                sections.append(f"\n# {current_heading}\n")
            else:
                text = element.get_text(separator=' ', strip=True)
                if len(text) > 50 and not should_filter_text(text):
                    sections.append(text)
        
        cleaned_text = clean_extracted_text('\n'.join(sections))
        if not cleaned_text or len(cleaned_text) <= 300:
            print(f"警告: 提取内容过短或为空 ({len(cleaned_text) if cleaned_text else 0} 字符)")
        return cleaned_text if len(cleaned_text) > 300 else None
    
    except Exception as e:
        print(f"内容提取失败: {str(e)}")
        return None


def crawl_from_database(db_config=None, batch_size=20):
    """
    增强版爬取任务 - 整合内容提取
    支持HTML和PDF文件的处理 - MySQL版本
    """
    if db_config is None:
        db_config = DB_CONFIG
        
    db = CrawlerDB(db_config)
    stats = {'success': 0, 'failed': 0, 'empty': 0, 'skipped': 0, 'pdf': 0}
    # 0表示未处理，1表示成功，2表示失败，3表示跳过，4表示空白
    batch_count = 0 

    try:
        print("开始爬取网页...")
        while True:
            batch_count += 1  # 增加批次计数
            pending = db.get_pending_urls(batch_size)
            if not pending:
                print("所有任务处理完成")
                break
            print(f"\n处理批次 #{batch_count} ({len(pending)} 条)")

            for item in pending:
                url = item['link']
                query = item['query']
                
                try:
                    # 检查是否为PDF URL
                    is_pdf_url = url.lower().endswith('.pdf')
                    
                    if is_pdf_url:
                        # 直接使用download_pdf函数处理PDF
                        print(f"检测到PDF URL，直接处理: {url}")
                        stats['pdf'] += 1
                        pdf_text = download_pdf(url)
                        
                        if not pdf_text:
                            print(f"PDF处理失败: {url}")
                            stats['failed'] += 1
                            db.update_crawl_result(url, None, 2)
                            continue
                            
                        # 处理PDF文本
                        extracted_text = process_pdf_content(pdf_text)
                        status = 1 if extracted_text else 4
                        
                        # 更新数据库
                        db.update_crawl_result(
                            link=url,
                            html_content=url,  # 存储URL作为引用
                            status=status
                        )
                    else:
                        # 正常处理HTML内容
                        html_content = save_webpage(url, query, stats, db)
                        if html_content is None:
                            continue
                        
                        # 处理HTML内容
                        extracted_text = process_html_content_from_db(html_content)
                        status = 1 if extracted_text else 4
                        
                        # 更新数据库
                        db.update_crawl_result(
                            link=url,
                            html_content=html_content if status == 1 else None,
                            status=status
                        )
                    
                    # 更新统计和数据库 - 使用MySQL方式更新提取文本
                    if status == 1:
                        stats['success'] += 1
                        db.cursor.execute('''
                            UPDATE crawled_data 
                            SET extracted_text = %s
                            WHERE link = %s
                        ''', (extracted_text, url))
                        db.conn.commit()
                    else:
                        stats['empty'] += 1
                
                except Exception as e:
                    print(f"处理异常 [{url}]: {str(e)}")
                    stats['failed'] += 1
                    db.update_crawl_result(url, None, 2)
                
                # 请求间隔
                time.sleep(1.5)

            print(f"成功处理 {stats['success']} 条，失败 {stats['failed']} 条，空白 {stats['empty']} 条，跳过 {stats['skipped']} 条，PDF {stats['pdf']} 条")
        
    finally:
        db.close()
        print("\n处理总结:")
        print(json.dumps(stats, indent=2))



if __name__ == "__main__":
    # 使用MySQL配置直接调用
    crawl_from_database(db_config=DB_CONFIG, batch_size=20)
