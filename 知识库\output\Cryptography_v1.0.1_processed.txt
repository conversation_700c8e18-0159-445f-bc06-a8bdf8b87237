## Page 4

INTRODUCTION
The purpose of this chapter is to explain the various aspects of cryptography which we feel
should be known to an expert in cyber-security. The presentation is at a level needed for an
instructor in a module in cryptography; so they can select the depth needed in each topic.
Whilst not all experts in cyber-security need be aware of all the technical aspects mentioned
below, we feel they should be aware of all the overall topics and have an intuitive grasp as
to what they mean, and what services they can provide. Our focus is mainly on primitives,
schemes and protocols which are widely used, or which are suitably well studied that they
could be used (or are currently being used) in speciﬁc application domains.
Cryptography by its very nature is one of the more mathematical aspects of cyber-security;
thus this chapter contains a lot more mathematics than one has in some of the other chap-
ters. The overall presentation assumes a basic knowledge of either ﬁrst-year undergraduate
mathematics, or that found in a discrete mathematics course of an undergraduate Computer
Science degree.
The chapter is structured as follows: After a quick recap on some basic mathematical notation
(Section 1), we then give an introduction to how security is deﬁned in modern cryptography.
This section (Section 2) forms the basis of our discussions in the other sections. Section
3 discusses information theoretic constructions, in particular the one-time pad, and secret
sharing. Sections 4 and 5 then detail modern symmetric cryptography; by discussing primitives
(such as block cipher constructions) and then speciﬁc schemes (such as modes-of-operation).
Then in Sections 6 and 7 we discuss the standard methodologies for performing public
key encryption and public key signatures, respectively. Then in Section 8 we discuss how
these basic schemes are used in various standard protocols; such as for authentication
and key agreement. All of the sections, up to and including Section 8, focus exclusively on
constructions which have widespread deployment.
Section 9 begins our treatment of constructions and protocols which are less widely used;
but which do have a number of niche applications. These sections are included to enable the
instructor to prepare students for the wider applications of the cryptography that they may
encounter as niche applications become more mainstream. In particular, Section 9 covers
Oblivious Transfer, Zero-Knowledge, and Multi-Party Computation. Section 10 covers public
key schemes with special properties, such as group signatures, identity-based encryption and
homomorphic encryption.
The chapter assumes the reader wants to use cryptographic constructs in order to build
secure systems, it is not meant to introduce the reader to attack techniques on cryptographic
primitives. Indeed, all primitives here can be assumed to have been selected to avoid speciﬁc
attack vectors, or key lengths chosen to avoid them. Further details on this can be found in
the regular European Key Size and Algorithms report, of which the most up to date version is
[1].
For a similar reason we do not include a discussion of historical aspects of cryptography, or
historical ciphers such as Caesar, Vigen`ere or Enigma. These are at best toy examples, and
so have no place in a such a body of knowledge. They are best left to puzzle books. However
the interested reader is referred to [2].
Page 3
## Page 5

CONTENT
1
MATHEMATICS
[3, c8–c9,App B][4, c1–c5]
Cryptography is inherently mathematical in nature, the reader is therefore going to be assumed
to be familiar with a number of concepts. A good textbook to cover the basics needed, and
more, is that of Galbraith [5].
Before proceeding we will set up some notation: The ring of integers is denoted by Z, whilst
the ﬁelds of rational, real and complex numbers are denoted by Q, R and C. The ring of
integers modulo N will be denoted by Z/NZ, when N is a prime p this is a ﬁnite ﬁeld often
denoted by Fp. The set of invertible elements will be written (Z/NZ)∗or F∗
p. An RSA modulus
N will denote an integer N, which is the product of two (large) prime factors N = p · q.
Finite abelian groups of prime order q are also a basic construct. These are either written
multiplicatively, in which case an element is written as gx for some x ∈Z/qZ; when written
additively an element can be written as [x] · P. The element g (in the multiplicative case) and
P (in the additive case) is called the generator.
The standard example of ﬁnite abelian groups of prime order used in cryptography are elliptic
curves. An elliptic curve over a ﬁnite ﬁeld Fp is the set of solutions (X, Y ) to an equation of
the form
E : Y 2 = X3 + A · X + B
where A and B are ﬁxed constants. Such a set of solutions, plus a special point at inﬁnity
denoted by O, form a ﬁnite abelian group denoted by E(Fp). The group law is a classic law
dating back to Newton and Fermat called the chord-tangent process. When A and B are
selected carefully one can ensure that the size of E(Fp) is a prime q. This will be important
later in Section 2.3 to ensure the discrete logarithm problem in the elliptic curve is hard.
Some cryptographic schemes make use of lattices which are discrete subgroups of the
subgroups of Rn. A lattice can be deﬁned by a generating matrix B ∈Rn·m, where each
column of B forms a basis element. The lattice is then the set of elements of the form
y = B · x where x ranges over all elements in Zm. Since a lattice is discrete it has a well-
deﬁned length of the shortest non-zero vector. In Section 2.3 we note that ﬁnding this shortest
non-zero vector is a hard computational problem.
Sampling a uniformly random element from a set A will be denoted by x ←A. If the set A
consists of a single element a we will write this as the assignment x ←a; with the equality
symbol = being reserved for equalities as opposed to assignments. If A is a randomized
algorithm, then we write x ←A(y; r) for the assignment to x of the output of running A on
input y with random coins r.
Page 4
## Page 6

2
CRYPTOGRAPHIC SECURITY MODELS
[3, c1–c4][4, c11]
Modern cryptography has adopted a methodology of ‘Provable Security’ to deﬁne and under-
stand the security of cryptographic constructions. The basic design procedure is to deﬁne the
syntax for a cryptographic scheme. This gives the input and output behaviours of the algo-
rithms making up the scheme and deﬁnes correctness. Then a security model is presented
which deﬁnes what security goals are expected of the given scheme. Then, given a speciﬁc
instantiation which meets the given syntax, a formal security proof for the instantiation is
given relative to some known hard problems.
The security proof is not an absolute guarantee of security. It is a proof that the given
instantiation, when implemented correctly, satisﬁes the given security model assuming some
hard problems are indeed hard. Thus, if an attacker can perform operations which are outside
the model, or manages to break the underlying hard problem, then the proof is worthless.
However, a security proof, with respect to well studied models and hard problems, can give
strong guarantees that the given construction has no fundamental weaknesses.
In the next subsections we shall go into these ideas in more detail, and then give some
examples of security statements; further details of the syntax and security deﬁnitions can be
found in [6, 7]. At a high level the reason for these deﬁnitions is that the intuitive notion of a
cryptographic construction being secure is not sufﬁcient enough. For example the natural
deﬁnition for encryption security is that an attacker should be unable to recover the decryption
key, or the attacker should be unable to recover a message encrypted under one ciphertext.
Whilst these ideas are necessary for any secure scheme they are not sufﬁcient. We need to
protect against an attacker aims for ﬁnd some information about an encrypted message, when
the attacker is able to mount chosen plaintext and chosen ciphertext attacks on a legitimate
user.
2.1
Syntax of Basic Schemes
The syntax of a cryptographic scheme is deﬁned by the algorithms which make up the scheme,
as well as a correctness deﬁnition. The correctness deﬁnition gives what behaviour one can
expect when there is no adversarial behaviour. For example, a symmetric encryption scheme
is deﬁned by three algorithms (KeyGen, Enc, Dec). The KeyGen algorithm is a probabilistic
algorithm which outputs a symmetric key k ←KeyGen(); Enc is a probabilistic algorithm which
takes a message m ∈M, some randomness r ∈R and a key and returns a c ←Enc(m, k; r) ∈
C; whilst Dec is (usually) a deterministic algorithm which takes a ciphertext and a key and
returns the underlying plaintext. The correctness deﬁnition is:
∀k ←KeyGen(), r ←R, m ←M, Dec(Enc(m, k; r), k) = m.
For public key encryption schemes the deﬁnitions are similar, but now KeyGen() outputs key
pairs and the correctness deﬁnition becomes:
∀(pk, sk) ←KeyGen(), r ←R, m ←M, Dec(Enc(m, pk; r), sk) = m.
The equivalent constructions for authentication mechanisms are Message Authentication
Codes (or MACs) in the symmetric key setting, and digital signatures schemes in the public key
setting. A MAC scheme is given by a triple of algorithms (KeyGen, MAC, Verify), where the MAC
Page 5
## Page 7

function outputs a tag given a message and a key (and possibly some random coins), and the
Verify function checks the message, tag and key are consistent. A signature scheme is given
by a similar triple (KeyGen, Sign, Verify), where now the tag produced is called a ‘signature’.
Thus the correctness deﬁnitions for these constructions are as follows
k ←KeyGen(), r ←R, m ←M, Verify(m, MAC(m, k; r), k) = true.
and
(pk, sk) ←KeyGen(), r ←R, m ←M, Verify(m, Sign(m, sk; r), pk) = true.
Note, that for deterministic MACs the veriﬁcation algorithm is usually just to recompute the
MAC tag MAC(m, k), and then check it was what was received.
2.2
Basic Security Deﬁnitions
A security deﬁnition is usually given in the context of an attacker’s security goal, followed by
their capabilities. So, for example, a naive security goal for encryption could be to recover the
underlying plaintext, so-called One-Way (or OW) security. This process of an attacker trying to
obtain a speciﬁc goal is called a security game, with the attacker winning the game, if they
can break this security goal with greater probability than random guessing. This advantage in
probability over random guessing is called the adversary’s advantage. The capabilities are
expressed in terms of what oracles, or functions, we give the adversary access to. So, for
example, in a naive security game for encryption we may give the adversary no oracles at all,
producing a so-called Passive Attack (or PASS) capability.
The attacker is modelled as an arbitrary algorithm, or Turing machine, A, and if we give the
adversary access to oracles then we write these as subscripts AO. In our naive security game
(called OW-PASS) the adversary has no oracles and its goal is simply to recover the message
underlying a given ciphertext. The precise deﬁnition is given in Figure 1, where AdvOW−PASS(A, t)
denote the advantage over a random guess that a given adversary has after running for time
t. We say that a given construction is secure in the given model (which our naive example
would be named OW-PASS), if the above advantage is negligible for all probabilistic polynomial
time adversaries A. Here, negligible and polynomial time are measured in terms of a security
parameter (which one can think of as the key size). Note, for OW-PASS this assumes that the
message space is not bigger than the space of all possible keys. Also note, that this is an
asymptotic deﬁnition, which in the context of schemes with ﬁxed key size, makes no sense.
In such situations we require that (t/Adv) is greater than some given concrete bound such as
2128, since it is believed that performing an algorithm requiring 2128 steps is infeasible even for
a nation-state adversary.
In the context of encryption (both symmetric and public key) the above naive security goal is
not seen as being suitable for real applications. Instead, the security goal of Indistinguishable
encryptions (or IND) is usually used. This asks the adversary to ﬁrst come up with two
plaintexts, of equal length, and then the challenger (or environment) encrypts one of them
and gives the resulting challenge to the adversary. The adversary’s goal is then to determine
which plaintext was encrypted. In the context of a passive attack this gives an advantage
statement as given in the second part of Figure 1, where the two stages of the adversary are
given by A1 and A2.
In terms of encryption, the above passive attack is almost always not sufﬁcient in terms
of capturing real-world adversarial capabilities, since real systems almost always give the
attacker additional attack vectors. Thus two other (increasingly strong) attack capabilities
Page 6
## Page 9

message/tag (resp. message/signature) pair which passes the veriﬁcation algorithm, a so-
called Universal Forgery (or UF) attack. We make no assumption about whether the message
has any meaning, indeed, the attacker wins if he is able to create a signature on any bit-string.
If the adversary is given no oracles then he is said to be mounting a passive attack, whilst
if the adversary is given a tag generation (resp. signing oracle) he is said to be executing a
Chosen Message Attack (CMA). In the latter case the ﬁnal forgery must not be one of the
outputs of the given oracle. In the case of MAC security, one may also give the adversary
access to a tag veriﬁcation oracle. However, for deterministic MACs this is implied by the
CMA capability and is hence usually dropped, since veriﬁcation only involves re-computing
the MAC.
Again we deﬁne an advantage and require this to be negligible in the security parameter. For
digital signatures the advantage for the UF-CMA game is given by the fourth equation in Figure
1.
2.3
Hard Problems
As explained above, security proofs are always relative to some hard problems. These hard
problems are often called cryptographic primitives, since they are the smallest atomic object
from which cryptographic schemes and protocols can be built. Such cryptographic primitives
come in two ﬂavours: Either they are keyed complexity theoretic deﬁnitions of functions, or
they are mathematical hard problems.
In the former case one could consider a function Fk(·) : D −→C selected from a function
family {Fk} and indexed by some index k (thought of as a key of varying length). One can
then ask whether the function selected is indistinguishable (by a probabilistic polynomial
time algorithm A which has oracle access to the function) from a uniform random function
from D to C. If such an assumption holds, then we say the function family deﬁnes a (keyed)
Pseudo-Random Function (PRF). In the case when the domain D is equal to the co-domain C
we can ask whether the function is indistinguishable from a randomly chosen permutation, in
which case we say the family deﬁnes a (keyed) Pseudo-Random Permutation (PRP).
In the case of a block cipher, such as AES (see later), where one has C = D = {0, 1}128, it is a
basic assumption that the AES function family (indexed by the key k) is a Pseudo-Random
Permutation.
In the case of mathematical hard problems we have a similar formulation, but the deﬁnitions
are often more intuitive. For example, one can ask the question whether a given RSA modulus
N = p · q can be factored into its prime components p and q, the so-called factoring problem.
The RSA group Z/NZ deﬁnes a ﬁnite abelian group of unknown order (the order is known
to the person who created N), ﬁnding the order of this group is equivalent to factoring N.
The RSA function x −→xe (mod N) is believed to be hard to invert, leading to the so-called
RSA-inversion problem of, given y ∈(Z/NZ)∗, ﬁnding x such that xe = y (mod N). It is known
that the function can easily be inverted if the modulus N can be factored, but it is unknown if
inverting the function implies N can be factored. Thus we have a situation where one problem
(factoring) seems to be harder to solve than another problem (the RSA problem). However,
in practice, we assume that both problems are hard, given appropriately chosen parameters.
Details on the best method to factor large numbers, the so-called Number Field Sieve, can be
found in [8].
In ﬁnite abelian groups of known order (usually assumed to be prime), one can deﬁne other
Page 8
## Page 10

problems. The problem of inverting the function x −→gx, is known as the Discrete Logarithm
Problem (DLP). The problem of, given gx and gy, determining gx·y is known as the Difﬁe–
Hellman Problem (DHP). The problem of distinguishing between triples of the form (gx, gy, gz)
and (gx, gy, gx·y) for random x, y, z is known as the Decision Difﬁe–Hellman (DDH) problem.
When written additively in an elliptic curve group, a DDH triple has the form ([x]·P, [y]·P, [z]·P).
Generally speaking, the mathematical hard problems are used to establish the security of
public key primitives. A major issue is that the above problems (Factoring, RSA-problem,
DLP, DHP, DDH), on which we base all of our main existing public key algorithms, are easily
solved by large-scale quantum computers. This has led designers to try to build cryptographic
schemes on top of mathematical primitives which do not appear to be able to be broken
by a quantum computer. Examples of such problems are the problem of determining the
shortest vector in a high dimensional lattice, the so-called Shortest Vector Problem (SVP),
and the problem of determining the closest lattice vector to a non-lattice vector, the so-called
Closest Vector Problem (CVP). The best algorithms to solve these hard problems are lattice
reduction algorithms, a nice survey of these algorithms and applications can be found in [9].
The SVP and CVP problems, and others, give rise to a whole new area called Post-Quantum
Cryptography (PQC).
Example:
Putting the above ideas together, one may encounter statements such as: The
public key encryption XYZ is IND-CCA secure assuming the RSA-problem is hard and AES is a
PRP. This statement tells us that any attack against the XYZ scheme must either be against
some weakness in the implementation, or must come from some attack not captured in the
IND-CCA model, or must come from solving the RSA-problem, or must come from showing
that AES is not a PRP.
2.4
Setup Assumptions
Some cryptographic protocols require some setup assumptions. These are assumptions
about the environment, or some data, which need to be satisﬁed before the protocol can
be considered secure. These assumptions come in a variety of ﬂavours. For example, one
common setup assumption is that there exists a so-called Public-Key Infrastructure (PKI),
meaning that we have a trusted binding between entities’ public keys and their identities.
Another setup assumption is the existence of a string (called the Common Reference String
or CRS) available to all parties, and which has been set up in a trusted manner, i.e. such that
no party has control of this string.
Other setup assumptions could be physical, for example, that the algorithms have access to
good sources of random numbers, or that their internal workings are not susceptible to an
invasive attacker, i.e. they are immune to side-channel attacks.
Page 9
## Page 11

2.5
Simulation and UC Security
The above deﬁnitions of security make extensive use of the notion of indistinguishability
between two executions. Indeed, many of the proof techniques used in the security proofs
construct simulations of cryptographic operations. A simulation is an execution which is
indistinguishable from the real execution, but does not involve (typically) the use of any key
material. Another method to produce security models is the so-called simulation paradigm,
where we ask that an adversary cannot tell the simulation from a real execution (unless they
can solve some hard problem). This paradigm is often used to establish security results for
more complex cryptographic protocols.
A problem with both the game/advantage-based deﬁnitions deﬁned earlier and the simulation
deﬁnitions is that they only apply to stand-alone executions, i.e. executions of one instance of
the protocol in one environment. To cope with arbitrarily complex executions and composition
of cryptographic protocols an extension to the simulation paradigm exists called the Universal
Composability (UC) framework.
3
INFORMATION-THEORETICALLY SECURE
CONSTRUCTIONS
[3, c2][4, c19]
Whilst much of cryptography is focused on securing against adversaries that are modelled
as probabilistic polynomial time Turing machines, some constructions are known to provide
security against unbounded adversaries. These are called information-theoretically secure
constructions. A nice introduction to the information theoretic side of cryptography can be
found in [10].
3.1
One-Time Pad
The most famous primitive which provides information-theoretic security is the one-time
pad. Here, a binary message m ∈{0, 1}t is encrypted by taking a key k ∈{0, 1}t uniformly at
random, and then producing the ciphertext c = m ⊕k. In terms of our earlier security models,
this is an IND-PASS scheme even in the presence of a computationally unbounded adversary.
However, the fact that it does not provide IND-CPA security is obvious, as the encryption
scheme is determinisitic. The scheme is unsuitable in almost all modern environments as
one requires a key as long as the message and the key may only be used once; hence the
name one-time pad.
Page 10
## Page 12

3.2
Secret Sharing
Secret sharing schemes allow a secret to be shared among a set of parties so that only a
given subset can reconstruct the secret by bringing their shares together. The person who
constructs the sharing of the secret is called the dealer. The set of parties who can reconstruct
the secret are called qualiﬁed sets, with the set of all qualiﬁed sets being called an access
structure.
Any set which is not qualiﬁed is said to be an unqualiﬁed set, and the set of all unqualiﬁed sets
is called an adversary structure. The access structure is usually assumed to be monotone, in
that if the parties in A can reconstruct the secret, then so can any super-set of A.
Many secret sharing schemes provided information-theoretic security, in that any set of
parties which is unqualiﬁed can obtain no information about the shared secret even if they
have unbounded computing power.
A special form of access structure is a so-called threshold structure. Here we allow any subset
of t + 1 parties to reconstruct the secret, whereas any subset of t parties is unable to learn
anything. The value t is being called the threshold. One example construction of a threshold
secret sharing scheme for a secret s in a ﬁeld Fp, with n > p is via Shamir’s secret sharing
scheme.
In Shamir secret sharing, one selects a polynomial f(X) ∈Fp[X] of degree t with constant
coefﬁcients s, the value one wishes to share. The share values are then given by si = f(i)
(mod p), for i = 1, . . . , n, with party i being given si. Reconstruction of the value s from a
subset of more than t values si can be done using Lagrange interpolation.
Due to an equivalence with Reed-Solomon error correcting codes, if t < n/2, then on receipt
of n share values si, a reconstructing party can detect if any party has given it an invalid share.
Additionally, if t < n/3 then the reconstructing party can correct for any invalid shares.
Replicated secret sharing is a second popular scheme which supports any monotone access
structure. Given a boolean formula deﬁning who should have access to the secret, one can
deﬁne a secret sharing scheme from this formula by replacing all occurrences of AND with +
and all occurrences of OR with a new secret. For example, given the formulae
(P1 AND P2) OR (P2 AND P3),
one can share a secret s by writing it as s = s1 +s2 = s′
2 +s3 and then, giving party P1 the value
s1, party P2 the pair of values s2 and s′
2, and party P3 the value s3. Replicated secret sharing is
the scheme obtained in this way when putting the boolean formulae into Conjunctive Normal
Form.
Of importance in applications of secret sharing, especially to Secure Multi-Party Computation
(see Section 9.4) is whether the adversary structure is Q2 or Q3. An adversary structure is
said to be Qi if no set of i unqualiﬁed sets have union the full set of players. Shamir’s secret
sharing scheme is Q2 if t < n/2 and Q3 when t < n/3. The error detection (resp. correction)
properties of Shamir’s secret sharing scheme mentioned above follow through to any Q2 (resp.
Q3) adversary structure.
Page 11
## Page 13

4
SYMMETRIC PRIMITIVES
[3, c3–c6][4, c11–c14]
Symmetric primitives are a key component of many cryptographic constructions. There are
three such basic primitives: block ciphers, stream ciphers, and hash functions. Theoretically,
all are keyed functions, i.e. they take as input a secret key, whilst in practice one often considers
hash functions which are unkeyed. At a basic level, all are functions f : K × D −→C where K
is the key space, D is the domain (which is of a ﬁxed ﬁnite size for block ciphers and stream
ciphers).
As explained in the introduction we will not be discussing in this report cryptanalysis of
symmetric primitives, we will only be examining secure constructions. However, the main two
techniques for attacks in this space are so-called differential and linear cryptanalysis. The
interested reader is referred to the excellent tutorial by Howard Heys [11] on these topics, or
the book [12].
4.1
Block Ciphers
A block cipher is a function f : K × {0, 1}b −→{0, 1}b, where b is the block size. Despite their
names such functions should not be thought of as an encryption algorithm. It is, however,
a building block in many encryption algorithms. The design of block ciphers is a deep area
of subject in cryptography, analogous to the design of number theoretic one-way functions.
Much like number-theoretic one-way functions, cryptographic constructions are proved secure
relative to an associated hard problem which a given block cipher is assumed to satisfy.
For a ﬁxed key, a block cipher is assumed to act as a permutation on the set {0, 1}b, i.e. for
a ﬁxed key k, the map fk : {0, 1}b −→{0, 1}b is a bijection. It is also assumed that inverting
this permutation is also easy (if the key is known). A block cipher is considered secure if
no polynomial time adversary, given oracle access to a permutation on {0, 1}b, can tell the
difference between being given a uniformly random permutation or the function fk for some
ﬁxed hidden key k, i.e. the block cipher is a PRP. In some applications, we only require that
the block cipher is a function, i.e. not a bijection. In which case we require the block cipher is
a PRF.
One can never prove that a block cipher is a PRP, so the design criteria is usually a task
of building a mathematical construction which resists all known attacks. The main such
attacks which one resists are so-called linear cryptanalysis, where one approximates non-
linear components within the block cipher by linear functions, and differential cryptanalysis,
where one looks at how two outputs vary on related input messages, e.g. one applies fk to
various inputs m0 and m1 where m0 ⊕m1 = ∆a ﬁxed value.
The design of a block cipher is made up of a number of simpler components. There are usually
layers of simple ﬁxed permutations, and layers of table lookups. These table lookups are
called S-boxes, where the S stands for substitutions. There are two main techniques to design
block ciphers. Both repeat a simple operation (called a round) a number of times. Each round
consists of a combination of permutations and substitutions, and a key addition. The main
key is ﬁrst expanded into round-keys, with each round having a different round-key.
In the ﬁrst methodology, called a Feistel Network, the S-Boxes allowed in each round can
be non-injective, i.e. non-invertible. Despite this, the Feistel constructions still maintain the
Page 12
## Page 14

overall invertibility of the block cipher construction. The second method is a Substitution-
Permutation Network design in which each round consists of a round-key addition, followed
by a ﬁxed permutation, followed by the application of bijective S-boxes. In general, the Feistel
construction requires more rounds than the Substitution-Permutation network construction.
The DES (Data Encryption Standard) block cipher (with an original key of 56-bits and block
size of b = 64) is a Feistel construction. The DES algorithm dates from the 1970s, and the
key size is now considered far too small for any application. However, one can extend DES
into a 112- or 168-bit key block cipher to construct an algorithm called 2DES or 3DES. The use
of 2DES or 3DES is still considered secure, although in some applications, the block size of
64-bits is considered insecure for real-world use.
The AES (Advanced Encryption Standard) block cipher is the modern replacement for DES,
and it is a block cipher with a 128-, 192- or 256-bit key, and with a block size of b = 128 bits.
The AES algorithm has hardware support on many microprocessors, making operations using
AES much faster than using other cryptographic primitives. Readers who wish to understand
more about the design of the AES block cipher referred to [13].
4.2
Stream Ciphers
A stream cipher is one which produces an arbitrary length string of output bits, i.e. the co-
domain of the function is essentially unbounded. Stream ciphers can be constructed from
block ciphers, by using a block cipher in Counter Mode (see Section 5.1). However, the stream
cipher is usually reserved for constructions which are special-purpose and for which the
hardware complexity is much reduced.
Clearly, a stream cipher cannot be a permutation, but we require that no polynomial time
adversary can distinguish oracle access to the stream cipher from oracle access to a uniformly
random function with inﬁnite co-domain. The design of stream ciphers is more ad-hoc than
that of the design of block ciphers. In addition, there is less widespread adoption outside
speciﬁc application areas. The interested reader is referred to the outcome of the eStream
competition for details of speciﬁc ad-hoc stream cipher designs [14].
4.3
Hash Functions
Hash functions are much like block ciphers in that they should act as PRFs. However, the input
domain can be unbounded. Since a PRF needs to be keyed to make any sense in theoretical
tracts, a hash function is usually a keyed object. In practice, we often require an unkeyed
object, in which case one considers the actual hash function used to have an implicit inbuilt
ﬁxed key, and have been chosen from a function family already.
When considering a ﬁxed hash function, one is usually interested in the intractability of inverting
the hash function (the one-way property), the intractability of ﬁnding two inputs with the same
output (the collision resistance property), or the intractability of ﬁnding, given an input/output
pair, a new input which gives the same output (the second-preimage resistance property).
Page 13
## Page 16

4.3.3
Random Oracle Model
Many cryptographic constructions are only secure if one assumes that the hash function
used in the construction behaves ‘like a random oracle’. Such constructions are believed to
be secure in the real world, but theoretically, they are less pleasing. One can think of a proof
of security in the random oracle model as a proof in which we allow the attacker to have
their usual powers; however, when they (or any of the partners they are attacking) call the
underlying hash function the call is made to an external party via an oracle call. This external
party then simply plays back a random value, i.e. it does not use any algorithm to generate
the random values. All that is required is that if the input is given to the oracle twice, then the
same output is always returned.
This clearly does not capture attacks in which the adversary makes clever use of exactly
how the hash function is deﬁned etc, and how this deﬁnition interacts with other aspects
of the scheme/protocol under analysis. However, this modelling methodology has proved
remarkably good in enabling cryptographers to design schemes which are secure in the real
world.
5
SYMMETRIC ENCRYPTION AND AUTHENTICATION
[3, c3–c4][4, c13–c14]
A block cipher, such as AES or DES, does not provide an effective form of data encryption or
data/entity authentication on its own. To provide such symmetric cryptographic constructions,
one needs a scheme, which takes the primitive and then utilizes this in a more complex
construction to provide the required cryptographic service. In the context of symmetric
encryption, these are provided by modes of operation. In the case of authentication, it is
provided by a MAC construction. Additionally, block ciphers are often used to take some
entropy and then expand, or collapse, this into a pseudo-random stream or key; a so-called XOF
(or Extendable Output Function) or KDF (or Key Derivation Function). Further details on block
cipher based constructions can be found at [16], whereas further details on Sponger/Keccak
based constructions can be found at [15].
ENC
P0
k
C0
ENC
P1
k
C1
ENC
P2
k
C2
IV
· · · · · ·
ENC
Pn
k
Cn
· · · · · ·
ENC
Pn
k
Cn
Figure 2: CBC Mode Encryption (All Figures are produced using TikZ for Cryptographers
https://www.iacr.org/authors/tikz/).
Page 15
## Page 17

5.1
Modes of Operation
Historically, there have been four traditional modes of operation to turn a block cipher into an
encryption algorithm. These were ECB, CBC, OFB and CFB modes. In recent years, the CTR
mode has also been added to this list. Among these, only CBC mode (given in Figure 2) and
CTR mode (given in Figure 3) are used widely within current systems. In these Figures, the
block cipher is represented by the function Enc
ENC
IV, Ctr+0
C0
k
P0
ENC
IV, Ctr+1
C1
k
P1
ENC
IV, Ctr+2
C2
k
P2
· · · · · ·
ENC
IV, Ctr+n
Cn
k
Pn
Figure 3: CTR Mode Encryption
On their own, however, CBC and CTR modes only provide IND-CPA security. This is far weaker
than the ‘gold standard’ of security, namely IND-CCA (discussed earlier). Thus, modern
systems use modes which provide this level of security, also enabling additional data (such
as session identiﬁers) to be tagged into the encryption algorithm. Such algorithms are called
AEAD methods (or Authenticated Encryption with Associated Data). In such algorithms, the
encryption primitive takes as input a message to be encrypted, plus some associated data.
To decrypt, the ciphertext is given, along with the associated data. Decryption will only work
if both the key is correct and the associated data is what was input during the encryption
process.
The simplest method to obtain an AEAD algorithm is to take an IND-CPA mode of operation
such as CBC or CTR, and then to apply a MAC to the ciphertext and the data to be authenticated,
giving us the so-called Encrypt-then-MAC paradigm. Thus, to encrypt m with authenticated
data a, one applies the transform
c1 ←Enc(m, k1; r),
c2 ←MAC(c1∥a, k2; r),
with the ciphertext being (c1, c2). In such a construction, it is important that the MAC is applied
to the ciphertext as opposed to the message.
A major issue with the Encrypt-then-MAC construction is that one needs to pass the data to
the underlying block cipher twice, with two different keys. Thus, new constructions of AEAD
schemes have been given which are more efﬁcient. The most widely deployed of these is
GCM (or Galois Counter Mode), see Figure 4, which is widely deployed due to the support for
this in modern processors.
One time AEAD constructions, otherwise known as DEMs, can be obtained by simply making
the randomized AEAD deterministic by ﬁxing the IV to zero.
Page 16
## Page 19

As HMAC is designed speciﬁcally for use with Merkle–Damg˚ard-based hash functions, it
makes no-sense to use this construction when using a sponge based hash function such as
SHA-3. The standardized MAC function derived from SHA-3 is called KMAC (or Keccak MAC).
In this function, the sponge construction is used to input a suitably padded message, then the
required MAC output is taken as the squeezed output of the sponge; whereas as many bits as
squeezed are as needed for the MAC output.
5.3
Key Derivation and Extendable Output Functions
The security deﬁnition of a deterministic MAC is essentially equivalent to the deﬁnition that the
output of the MAC function is indistinguishable from a random string, if one does not know the
underlying secret key. As such, MAC functions can be used for other cryptographic operations.
For example, in many situations, one must derive a long (or short) string of random bits, given
some random input bits. Such functions are called KDFs or XOFs (for Key Derivation Function
and Extendable Output Function). Usually, one uses the term KDF when the output is of a ﬁxed
length, and XOF when the output could be of an arbitrary length. But the constructions are,
usually, essentially the same in both cases.
Such functions can take an arbitrary length input string, and produce another arbitrary length
output string which is pseudo-random. There are three main constructions for such functions;
one based on block ciphers, one on the Merkle–Damg˚ard hash functions, and one based on
sponge-based hash functions.
The constructions based on a block cipher are, at their heart, using CBC-MAC, with a zero key
to compress the input string into a cryptographic key and then use the CTR mode of operation
under this key to produce the output string. Hence, the construction is essentially given by
k ←CBC-MAC(m, 0),
o1 ←Enc(1, k),
o2 ←Enc(2, k),
. . .
where Enc is the underlying block cipher.
The constructions based on the Merkle–Damg˚ard hash function use a similar structure, but
using one hash function application per output block, in a method similar to the following
o1 ←H(m∥1),
o2 ←H(m∥2),
. . .
Due to the way Merkle–Damg˚ard hash functions are constructed, the above construction (for
large enough m) can be done more efﬁciently than simply applying H as many times as the
number of output blocks will dictate.
As one can imagine, the functions based on Keccak are simpler—one simply inputs the suitably
padded message into the sponge and then squeezes as many output bits out as required.
Special KDFs can also be deﬁned which take as input a low entropy input, such as a password
or PIN, and produce a key for use in a symmetric algorithm. These password based key
derivation functions are designed to be computationally expensive, so as to mitigate problems
associated to brute force attacking of the underlying low entropy input.
Page 18
## Page 20

5.4
Merkle-Trees and Blockchains
An application of cryptographic hash functions which has recently come to prominance is
that of using Merkle-Trees and by extension blockchains. A Merkle-Tree, or hash-tree, is a tree
in which each leaf node contains data, and each internal node is the hash of its child nodes.
The root node is then publicly published. Merkle-Trees enable efﬁcient demonstration that a
leaf node is contained in the tree, in that one simply presents the path of hashes from the leaf
up to the root node. Thus veriﬁcation is logarithmic in the number of leaf nodes. Merkle-Trees
can verify any form of stored data and have been used in various protocols such as version
control systems, such as Git, and backup systems.
A blockchain is a similar structure, but now the data items are aligned in a chain, and each
node hashes both the data item and a link to the previous item in the chain. Blockchains
are used in cryptocurrencies such as Bitcoin, but they have wider application. The key prop-
erty a blockchain provides is that (assuming the current head of the chain is authenticated
and trusted) the data provides an open distributed ledger in which previous data items are
immutable, and the ordering of data items is preserved.
6
PUBLIC KEY ENCRYPTION
[3, c11][4, c15–c17]
As explained above, public key encryption involves two keys, a public one pk and a private one
sk. The encryption algorithm uses the public key, whilst the decryption algorithm uses the
secret key. Much of public key cryptography is based on number theoretic constructions, thus
[5] provides a good coverage of much in this section. The standard security requirement for
public key encryption is that the scheme should be IND-CCA. Note that since the encryption
key is public we have that IND-PASS is the same as IND-CPA for a public key encryption
scheme.
6.1
KEM-DEM Philosophy
In general, public key encryption schemes are orders of magnitude less efﬁcient than symmet-
ric key encryption schemes. Thus, the usual method in utilizing a public key scheme, when
large messages need to be encrypted, is via a hybrid method. This hybrid methodology is
called the KEM-DEM philosophy A KEM, which stands for Key Encapsulation Mechanism, a
public key method to transmit a short key, selected at random from a set K, to a designated
recipient. Whereas, a DEM, or Data Encryption Mechanism, is essentially the same as an
IND-CCA symmetric encryption scheme, which has key space K. Since a DEM is only ever
used once with the same key, we can actually use a weaker notion of IND-CCA encryption for
the DEM, in which the adversary is not given access to an encryption oracle; which means the
DEM can be deterministic.
For a KEM, we call the encryption and decryption mechanisms encapsulation and decapsula-
tion, respectively. It is usual for the syntax of the encapsulation algorithm to not take any input,
bar the randomness, and then to return both the ciphertext and the key which it encapsulates.
Thus, the syntax, and correctness, of a KEM becomes
(pk, sk) ←KEMKeyGen(), r ←R, (k, c) ←KEMEnc(pk; r), KEMDec(c, sk) = k.
Page 19
## Page 22

RSA-KEM, on the other hand, is a KEM which is much simpler to execute. To produce the
encapsulated key and the ciphertext, one takes the random input r (which one thinks of as a
uniformly random element in (Z/NZ)∗). Then the KEM is deﬁned by
c ←re
(mod N), k ←H(r),
where H : (Z/NZ) −→K is a hash function, which we model as a random oracle.
6.3
Constructions based on Elliptic Curves
Elliptic Curve Cryptography, or ECC, uses the fact that elliptic curves form a ﬁnite abelian
group. In terms of encryption schemes, the standard method is to use ECIES (Elliptic Curve
Integrated Encryption Scheme) to deﬁne a public key, KEM which is IND-CCA in the random
oracle model, assuming the DDH problem in the subgroup of the elliptic curve being used.
In practice, this means that one selects a curve E(Fp) for which there is a point P ∈E(Fp)
whose order is a prime q > 2256.
For ECIES, the KeyGen algorithm is deﬁned as follows. A secret key sk ←F∗
q is selected
uniformly at random, and then the public key is set to be Q ←[sk]P. Key encapsulation is very
similar to RSA-KEM in that it is deﬁned by
r ←F∗
q, C ←[r] · P, k ←H([r] · Q),
where H : E(Fp) −→K is a hash function (modelled as a random oracle). To decapsulate the
key is recovered via
k ←H([sk]C).
Compared to RSA-based primitives, ECC-based primitives are relatively fast and use less
bandwidth. This is because, at the time of writing, one can select elliptic curve parameters
with p ≈q ≈2256 to obtain security equivalent to a work-factor of 2128 operations. Hence, in
current systems elliptic curve-based systems are preferred over RSA-based ones.
6.4
Lattice-based Constructions
A major problem with both RSA and ECC primitives is that they are not secure against quantum
computers; namely, Shor’s algorithm will break both the RSA and ECC hard problems in
polynomial time. Hence, the search is on for public key schemes which would resist the
advent of a quantum computer. The National Institute of Standards and Technology (NIST)
is currently engaged in a process to determine potential schemes which are post-quantum
secure, see [17] for more details on this.
The most prominent of these so-called post-quantum schemes are those based on hard
problems on lattices. In particular, the NTRU schemes and a variety of schemes based on
the Learning With Errors (LWE) problem, and its generalisation to polynomial rings, known as
the Ring-LWE problem. There are other proposals based on hard probles in coding theory, on
the difﬁculty of computing isogenies between elliptic curves and other constructs. NIST is
currently conducting a program to select potential post-quantum replacements.
Page 21
## Page 25

for the Schnorr algorithm, one computes it via e ←H(m∥r). Then the signature equation is
applied which, in the case of EC-DSA, is
s ←(e + x · r)/k
(mod q)
and, in the case of Schnorr, is
s ←(k + e · x)
(mod q).
Finally, the output signature is given by (r, s) for EC-DSA and (e, s) for Schnorr.
Veriﬁcation is done by checking the equation
r = x −coord([e/s] · P + [r/s] · Q)
in the case of EC-DSA, and by checking
e = H (m∥x −coord([s] · P −e · Q))
in the case of Schnorr. The key difference in the two algorithms is not the signing and
veriﬁcation equations (although these do affect performance), but the fact that, with the
Schnorr scheme, the r value is also entered into the hash function to produce e. This small
distinction results in the different provable security properties of the two algorithms.
A key aspect of both EC-DSA and Schnorr signatures is that they are very brittle to exposure
of the per-message random nonce k. If only a small number of bits of k leak to the attacker
with every signing operation, then the attacker can easily recover the secret key.
8
STANDARD PROTOCOLS
[4, c18]
Cryptographic protocols are interactive operations conducted between two or more parties in
order to realize some cryptographic goal. Almost all cryptographic protocols make use of the
primitives we have already discussed (encryption, message authentication, secret sharing).
In this section, we discuss the two most basic forms of protocol, namely authentication and
key agreement.
8.1
Authentication Protocols
In an authentication protocol, one entity (the Prover) convinces the other entity (the Veriﬁer)
that they are who they claim to be, and that they are ‘online’; where ‘online’ means that the
verifying party is assured that the proving party is actually responding and it is not a replay.
There are three basic types of protocol: Encryption based, Message Authentication based
and Zero-Knowledge based.
Page 24
## Page 26

8.1.1
Encryption-Based Protocols
These can operate in the symmetric or public key setting. In the symmetric key setting, both
the prover and the veriﬁer hold the same secret key, whilst in the public key setting, the prover
holds the private key and the veriﬁer holds the public key. In both settings, the veriﬁer ﬁrst
encrypts a random nonce to the prover, the prover then decrypts this and returns it to the
veriﬁer, the veriﬁer checks that the random nonce and the returned value are equivalent.
Veriﬁer
Prover
N ←M
c ←Enc(N, pk; r)
c
−→
m
←−
m ←Dec(c, sk)
N ?= m
The encryption scheme needs to be IND-CCA secure for the above protocol to be secure
against active attacks. The nonce N is used to prevent replay attacks.
8.1.2
Message Authentication-Based Protocols
These also operate in the public key or the symmetric setting. In these protocols, the veriﬁer
sends a nonce in the clear to the prover, the prover then produces a digital signature (or a
MAC in the symmetric key setting) on this nonce and passes it back to the veriﬁer. The veriﬁer
then veriﬁes the digital signature (or veriﬁes the MAC). In the following diagram we give the
public key/digital signature based variant.
Veriﬁer
Prover
N ←M
N
−→
σ
−→
σ ←Sign(N, sk)
Verify(N, σ, pk) ?= true
8.1.3
Zero-Knowledge-Based
Zero-knowledge-based authentication protocols are the simplest examples of zero-knowledge
protocols (see Section 9.3) available. The basic protocol is a so-called Σ- (or Sigma-) protocol
consisting of three message ﬂows; a commitment, a challenge and a response. The simplest
example is the Schnorr identiﬁcation protocol, based on the hardness of computing discrete
logarithms. In this protocol, the Prover is assumed to have a long-term secret x and an
associated public key Q = [x]·P. One should note the similarity of this protocol to the Schnorr
signature scheme above.
Veriﬁer
Prover
k ←Z/qZ
r
←−
R ←[k] · P
e ←Z/qZ
e
−→
s
←−
s ←(k + e · x)
(mod q)
R ?= [s] · P −e · Q
Indeed, the conversion of the Schnorr authentication protocol into the Schnorr signature
scheme is an example of the Fiat–Shamir transform, which transforms any Σ-protocol into a
signature scheme. If the underlying Σ-protocol is secure, in the sense of a zero-knowledge
proofs of knowledge (see Section 9.3), then the resulting signature scheme is UF-CMA.
Page 25
## Page 27

8.2
Key Agreement Protocols
A key agreement protocol allows two parties to agree on a secret key for use in subsequent
protocols. The security requirements of key agreement protocols are very subtle, leading to
various subtle security properties that many deployed protocols may or may not have. We
recap on basic properties of key agreement protocols here, but a more complete discussion
can be found in [18]. The basic security requirements are
• The underlying key should be indistinguishable from random to the adversary, or that
at least it should be able to be used in the subsequent protocol without the adversary
breaking the subsequent protocol.
• Each party is assured that only the other party has access to the secret key. This is
so-called mutual authentication. In many application scenarios (e.g. in the standard
application of Transport Layer Security (TLS) to web browsing protocol), one only requires
this property of one-party, in which case we are said to only have one-way authentication.
Kerberos is an example of a (usually) symmetric key-based key agreement system. This is
a protocol that requires trusted parties to relay and generate secret keys from one party to
another. It is most suited to closed corporate networks. On the public internet, protocols
like Kerberos are less useful. Thus, here one uses public key-based protocols such as TLS
and IPSec. More advanced properties required of modern public key-based protocols are as
follows.
• Key Conﬁrmation: The parties know that the other party has received the same secret
key. Sometimes this can be eliminated as the correct execution of the subsequent
protocol using the secret key provides this conﬁrmation. This later process is called
implicit key conﬁrmation.
• Forward Secrecy: The compromise of a participant’s long-term secret in the future does
not compromise the security of the secret key derived now, i.e. current conversations
are still secure in the future.
• Unknown Key Share Security: This prevents one party (Alice) sharing a key with Bob,
whereas Bob thinks he shares a key with Charlie, despite sharing it with Alice.
Variations on the theme of key agreement protocols include group key agreement, which
enables a group of users to agree on a key, or password based key agreement, in which two
parties only agree on a (high entropy) key if they also agree on a shared password.
8.2.1
Key Transport
The most basic form of key agreement protocol is a form of key transport in which the parties
use public key encryption to exchange a random key. In the case of a one-way authenticated
protocol, this was the traditional method of TLS operation (up until TLS version 1.2) between
a server and a client
Client
Server
pk
←−
k ←K
c ←Enc(k, pk; r)
c
−→
k ←Dec(c, sk)
This protocol produced the pre-master secret in older versions of TLS (pre-TLS 1.2). To derive
the ﬁnal secret in TLS, further nonces were exchanged between the parties (to ensure that
Page 26
## Page 28

both parties were alive and the key was fresh). Then, a master secret was derived from the pre-
master secret and the nonces. Finally, key conﬁrmation was provided by the entire protocol
transcript being hashed and encrypted under the master secret (the so-called FINISHED
message). In TLS, the resulting key is not indistinguishable from random as the encrypted
FINISHED message provides the adversary with a trivial check to determine whether a key is
real or not. However, the protocol can be shown to be secure for the purposes of using the
master secret to produce a secure bi-directional channel between the server and the client.
A more basic issue with the above protocol is that it is not forward-secure. Any adversary who
records a session now, and in the future manages to obtain the server’s long-term secret sk,
can obtain the pre-master secret, and hence decrypt the entire session.
8.2.2
Difﬁe–Hellman Key Agreement
To avoid the issues with forward secrecy of RSA-based key transport, modern protocols make
use of Difﬁe–Hellman key exchange. This allows two parties to agree on a uniformly random
key, which is indistinguishable from random assuming the Decision Difﬁe–Hellman problem
is hard
Alice
Bob
a ←Z/qZ
b ←Z/qZ
QA ←[a] · P
QA
−−→
QB
←−−
QB ←[b] · P
K ←[a] · QB
K ←[b] · QA
This protocol provides forward secrecy, but provides no form of authentication. Due to this,
the protocol suffers from a man-in-the-middle attack. To obtain mutual authentication, the
message ﬂow of QA is signed by Alice’s public key and the message ﬂow of QB is signed by
Bob’s public key. This prevents the man-in-the-middle attack. However, since the signatures
are not bound into the message, the signed-Difﬁe–Hellman protocol suffers from an unknown-
key-share attack; an adversary (Charlie) can strip Alice’s signature from QA and replace it with
their signature. The adversary does not learn the secret, but does convince Bob he is talking
to another entity.
The one-way authenticated version of Difﬁe–Hellman key agreement is the preferred method
of key agreement in modern TLS deployments, and is the only method of key agreement
supported by TLS 1.3. In TLS, the FINISHED message, which hashes the entire transcript,
prevents the above unknown-key-share attack. However, it also prevents the protocol from
producing keys which are indistinguishable from random, as mentioned above.
8.2.3
Station-to-Station Protocol
The Station-to-Station (STS) protocol can be used to prevent unknown-key-share attacks
on signed Difﬁe–Hellman and maintain key indistinguishability. In this protocol, the Difﬁe–
Hellman derived key is used to encrypt the signatures, thus ensuring the signatures cannot be
stripped off the messages. In addition, the signatures are applied to the transcript so as to
Page 27
## Page 30

messages are two elements M0 and M1 in an elliptic curve group E(Fp) of prime order q.
Sender
Receiver
C ←E(Fp)
C
−→
x ←(Z/qZ)
Qb ←[x] · P
Q1−b ←C −Qb
Q0
←−
Q1 ←C −Q0
k ←(Z/qZ)
C1 ←[k] · P
E0 ←M0 + [k] · Q0
E1 ←M1 + [k] · Q1
C1,E0,E1
−→
Mb ←Eb −[x] · C1
The extension to an actively secure protocol is only a little more complex, but beyond the
scope of this article.
9.2
Private Information Retrieval and ORAM
A Private Information Retrieval (PIR) protocol is one which enables a computer to retrieve
data from a server held database, without revealing the exact item which is retrieved. If the
server has n data items then this is related to a 1-out-of-n OT protocol. However, in PIR we
do not insist that the user does not learn anything else about the servers data, we only care
about privacy of the user query. In addition protocols for PIR are meant to be run many times,
and we are interested in hiding the total set of access patterns, i.e. even whether a data item
is retrieved multiple times. The goal of PIR protocols is to obtain greater efﬁciency than the
trivial solution of the server sending the user the entire database.
An Oblivious Random Access Memory (ORAM) protocol is similar but now we not only allow
the user to obliviously read from the server’s database, we also allow the user to write to the
database. So as to protect the write queries the server held database must now be held in
an encrypted form (so what is written cannot be determined by the server). In addition the
access patterns, i.e. where data is written to and read from, needs to be hidden from the
server.
Page 29
## Page 31

9.3
Zero-Knowledge
A Zero-Knowledge protocol is a protocol executed between a Prover and a Veriﬁer in which
the Prover demonstrates that a statement is true, without revealing why the statement is
true. The concept is used in many places in cryptography, to construct signature schemes, to
attest ones identity, and to construct more advanced protocols. An introduction to the more
theoretical aspects of zero-knowledge can be found in [6]. More formally, consider an NP
language L (i.e. a set of statements x which can be veriﬁed to be true in polynomial time given
a witness or proof w). An interactive proof system for L is a sequence of protocol executions
by an (inﬁnitely powerful) Prover P and a (probabilistic polynomial time) Veriﬁer V , which on
joint input x proceeds as follows:
Veriﬁer
Prover
p1
←−
(p1, s′
1) ←P1(x)
(v1, s1) ←V1(x, p1)
v1
−→
p2
←−
(p2, s′
2) ←P2(s′
1, v1)
(v2, s2) ←V2(s1, p2)
v2
−→
p3
←−
(p3, s′
3) ←P3(s′
2, v2)
...
...
pr
−→
(pr, s′
r) ←Pr(sr1, vr1)
By the end of the protocol, the Veriﬁer will output either true or false. An interactive proof
system is one which is both complete and sound
• Completeness: If the statement x is true, i.e. x ∈L, then if the Prover is honest then the
Veriﬁer will output true.
• Soundness: If the statement is false, i.e. x ̸∈L, then no cheating Prover can convince
the Veriﬁer with probability greater than p.
Note that even if p is large (say p = 0.5) then repeating the proof multiple times can reduce
the soundness probability to anything desired. Of course, protocols with small p to start with
are going to be more efﬁcient.
For any NP statement, there is a trivial proof system. Namely, the Prover simply sends over
the witness w which the Veriﬁer then veriﬁes. However, this reveals the witness. In a zero-
knowledge proof, we obtain the same goal, but the Veriﬁer learns nothing bar the fact that
x ∈L. To formally deﬁne zero-knowledge, we insist that there is a (probabilistic polynomial
time) simulator S which can produce protocol transcripts identical to the transcripts produced
between a Veriﬁer and an honest Prover; except the simulator has no access to the Prover.
This implies that the Veriﬁer cannot use the transcript to perform any other task, since what it
learned from the transcript it could have produced without the Prover by simply running the
simulator.
A zero-knowledge proof is said to be perfect zero-knowledge if the distribution of transcripts
produced by the simulator is identical to those produced between a valid prover and veriﬁer. If
the two distributions only cannot be distinguished by an efﬁcient algorithm we say we have
computational zero-knowledge.
A zero-knowledge proof is said to be a proof of knowledge if a Veriﬁer given rewinding access
to the prover (i.e. the Veriﬁer can keep resetting the Prover to a previous protocol state and
Page 30
## Page 32

continue executing) can extract the underlying witness w. This implies that the Prover must
‘know’ w since we can extract w from it.
A non-interactive zero-knowledge proof is one in which there is no message ﬂowing from the
Veriﬁer to the Prover, and only one message ﬂowing from the Prover to the Veriﬁer. Such non-
interactive proofs require additional setup assumptions, such as a Common Reference String
(CRS), or they require one to assume the Random Oracle Model. Traditionally these are applied
to speciﬁc number theoretic statements, such to show knowledge of a discrete logarithm
(see the next section on Σ-protocols), however recently so called Succinct Non-Interactive
Arguments of Knowledge (SNARKs) have been developed which enable such non-interactive
arguments for more complex statements. Such SNARKs are ﬁnding applications in some
blockchain systems.
9.3.1
Σ-Protocols
The earlier Σ-protocol for identiﬁcation is a zero-knowledge proof of knowledge.
Veriﬁer
Prover
k ←Z/qZ
R
←−
R ←[k] · P
e ←Z/qZ
e
−→
s
←−
s ←(k + e · x)
(mod q)
R ?= [s] · P −e · Q
The protocol is obviously complete since Q = [x] · P, and the soundness error is 1/q. That it is
zero-knowledge follows from the following simulation, which ﬁrst samples e, s ←Z/qZ and
then computes R = [s]P −e · Q; the resulting simulated transcript being (R, e, s). Namely, the
simulator computes things in the wrong order.
The protocol is also a proof of knowledge since if we execute two protocol runs with the
same R value but different e-values (e1 and e2) then we obtain two s-values (s1 and s2). This is
done by rewinding the prover to just after it has sent its ﬁrst message. If the two obtained
transcripts (R, e1, s1) and (R, e2, s2) are both valid then we have
R = [s1] · P −e1 · Q = [s2] · P −e2 · Q
and so
[s1 −s2] · P = [e1 −e2] · Q
and hence
Q =
s1 −s2
e1 −e2

· P
and hence we ‘extract’ the secret x from x = (s1 −s2)/(e1 −e2) (mod q).
Page 31
## Page 33

9.4
Secure Multi-Party Computation
Multi-Party Computation (MPC) is a technique to enable a set of parties to compute on
data, without learning anything about the data. Consider n parties P1, . . . , Pn each with input
x1, . . . , xn. MPC allows these parties to compute any function f(x1, . . . , xn) of these inputs
without revealing any information about the xi to each other, bar what can be deduced from
the output of the function f. A general introduction to the theory of such protocols can be
found in [7].
In an MPC protocol, we assume that a subset of the parties A is corrupt. In statically secure
protocols, this set is deﬁned at the start of the protocol, but remains unknown to the honest
parties. In an adaptively secure protocol, the set can be chosen by the adversary as the
protocol progresses. An MPC protocol is said to be passively secure if the parties in A follow
the protocol, but try to learn data about the honest parties’ inputs from their joint view. In an
actively secure protocol, the parties in A can arbitrarily deviate from the protocol.
An MPC protocol should be correct, i.e. it outputs the correct answer if all parties follow the
protocol. It should also be secure, i.e. the dishonest parties should learn nothing about the
inputs of the honest parties. In the case of active adversaries, a protocol is said to be robust if
the honest parties will obtain the correct output, even when the dishonest parties deviate from
the protocol. A protocol which is not robust, but which aborts with overwhelming probability
when a dishonest party deviates, is said to be an actively secure MPC protocol with abort.
MPC protocols are catagorized by whether they utilize information-theoretic primitives (namely
secret sharing), or they utilize computationally secure primitives (such as symmetric-key and
public-key encryption). They are also further characterized by the properties of the set A. Of
particular interest is when the size t of A is bounded by a function of n (so-called threshold
schemes). The cases of particular interest are t < n, t < n/2, and t < n/3; the threshold cases
of t < n/2 and t < n/3 can be generalized to Q2 and Q3 access structures, as discussed in
Section 3.2.
In the information-theoretic setting, one can achieve passively secure MPC in the case of
t < n/2 (or Q2 access structures). Actively secure robust MPC is possible in the information-
theoretic setting when we have t < n/3 (or Q3 access structures). All of these protocols
are achieved using secret sharing schemes. A detailed study of secret sharing based MPC
protocols is given in [19].
In the computational setting, one can achieve actively secure robust computation when
t < n/2, using Oblivious Transfer as the basic computational foundation. The interesting case
of two party computation is done using the Yao protocol. This protocol has one party (the
Circuit Creator, also called the Garbler) ‘encrypting’ a boolean function gate by gate using a
cipher such as AES, the circuit is then sent to the other party (called the Circuit Evaluator).
The Evaluator then obtains the ‘keys’ for their input values from the Creator using Oblivious
Transfer, and can then evaluate the circuit. A detailed study of two party Yao based protocols
is given in [20].
Modern MPC protocols have looked at active security with abort in the case of t < n. The
modern protocols are divided into a function-dependent ofﬂine phase, which requires public
key functionality but which is function independent, then a function-dependent online phase
which mainly uses information-theoretic primitives. Since information theoretic primitives
are usually very fast, this means the time-critical online phase can be executed as fast as
possible.
Page 32
## Page 34

10
PUBLIC KEY ENCRYPTION/SIGNATURES WITH SPECIAL
PROPERTIES
[3, c13]
A major part of modern cryptography over the last twenty years has been the construction of
encryption and signature algorithms with special properties or advanced functionalities. A
number of the following have been deployed in specialized systems (for example, U-PROVE,
IDEMIX, attestation protocols and some cryptocurrencies). We recap the main variants below,
giving for each one the basic idea behind their construction.
10.1
Group Signatures
A group signature scheme deﬁned a group public key pk, associated to a number of secret
keys sk1, . . . , skn. The public key is usually determined by an entity called a Group Manager,
during an interaction with the group members. Given a group signature s, one cannot tell which
secret key signed it, although one is guaranteed that one did. Thus group signatures provide
the anonymity of a Signer. Most group signature algorithms have a special entity called an
Opener who has some secret information which enables them to revoke the anonymity of a
Signer. This last property ensures one can identify group members who act dishonestly in
some way.
A group signature scheme can either support static or dynamic groups. In a static group
signature scheme, the group members are ﬁxed at the start of the protocol, when the public
key is ﬁxed. In a dynamic group signature scheme the group manager can add members into
the group as the protocol proceeds, and (often) revoke members as well.
An example of this type of signature scheme which is currently deployed is the Direct Anony-
mous Attestation (DAA) protocol; which is essentially a group signature scheme in which the
Opener is replaced with a form of user controlled linkability; i.e. a signer can decide whether
two signatures output by the speciﬁc signer can be linked or not.
10.2
Ring Signatures
A ring signature scheme is much like a group signature scheme, but in a ring signature there is
no group manager. Each user in a ring signature scheme has a public/private key pair (pki, ski).
At the point of signing, the Signer selects a subset of the public keys (containing this own),
which is called a ring of public keys, and then produces a signature. The Receiver knows the
signature was produced by someone in the ring, but not which member of the ring.
Page 33
## Page 35

10.3
Blind Signatures
A blind signature scheme is a two party protocol in which a one party (the User) wants to
obtain the signature on a message by a second party (the Signer). However, the Signer is
not allowed to know which message is being signed. For example, the Signer may be simply
notarising that something happened, but does not need to know precisely what. Security
requires that the Signer should not learn anything about any message passed to it for signing,
and the user should not obtain the signature on any message other than those they submitted
for signing.
10.4
Identity-Based Encryption
In normal public key encryption, a user obtains a public key pk, along with a certiﬁcate C. The
certiﬁcate is produced by a trusted third party, and binds the public key to the identity. Usually,
a certiﬁcate is a digitally signed statement containing the public key and the associated user
identity. So, when sending a message to Alice the Sender is sure that Alice is the legitimate
holder of public key pk.
Identity Based Encryption (IBE) is an encryption scheme which dispenses with the need for
certiﬁcate authorities, and certiﬁcates. To encrypt to a user, say Alice, we simply use her
identity Alice as the public key, plus a global ‘system’ public key. However, to enable Alice to
decrypt, we must have a trusted third party, called a Key Generation Centre, which can provide
Alice with her secret key. This third party uses its knowledge of the ‘system’ secret key to be
able to derive Alice’s secret key. Whilst dispensing with certiﬁcates, an IBE system inherently
has a notion of key escrow; the Key Generation Centre can decrypt all messages.
10.5
Linearly Homomorphic Encryption
In a linearly homomorphic encryption scheme one can perform a number of linear operations
on ciphertexts, which result in a ciphertext encrypting a message having had the same
operations performed on the plaintext. Thus, given two encryptions c1 ←Enc(m1, pk; r1) and
c2 ←Enc(m2, pk; r2) one can form a ‘sum’ operation c ←c1 ⊕c2 such that c decrypts to m1 +m2.
The standard example of such encryption schemes is the Paillier encryption scheme, which
encrypts elements m ∈(Z/NZ), for an RSA-modulus N by computing c ←(1 + N)m · rN
(mod N 2) where r is selected in Z/NZ.
Such encryption algorithms can never be IND-CCA secure, as the homomorphic property
produces a trivial malleability which can be exploited by a CCA attacker. However, they can
have applications in many interesting areas. For example, one can use a linearly homomorphic
encryption scheme to add up votes in a digitally balloted election for two candidates, where
each vote is an encryption of either the message zero or one.
Page 34
## Page 36

10.6
Fully Homomorphic Encryption
Fully Homomorphic Encryption (or FHE) is an extension to linearly homomorphic encryption,
in that one can not only homomorphically evaluate linear functions, but also non-linear ones.
In particular, the ability to homomorphically evaluate both addition and multiplication on
encrypted data enables one to (theoretically) evaluate any function. Applications of FHE which
have been envisioned are things such as performing complex search queries on encrypted
medical data etc. Thus, FHE is very interesting in a cloud environment.
All existing FHE schemes are highly inefﬁcient. Thus only very simple functions can be
evaluated in suitable time limits. A scheme which can perform homomorphic operations from
a restricted class of functions (for example, to homomorphically evaluate all multi-variate
polynomials of total degree ﬁve) is called a Somewhat Homomorphic Encryption (or SHE)
scheme. Obviously, if the set of functions are all multi-variate polynomials of degree one, then
the SHE scheme is a linear homomorphic encryption scheme.
11
IMPLEMENTATION ASPECTS
There are two aspects one needs to bear in mind with respect to cryptographic implementation.
Firstly security and secondly performance.
In terms of security the main concern is one of side-channel attacks. These can be mounted
against both hardware implementations, for example cryptographic circuits implemented on
smart-cards, or against software implementations running on commodity processors. Any
measurable difference which occurs when running an algorithm on one set of inputs versus
another can lead to an attack. Such measurements may involve timing differences, power
comsumption differences, differences in electromagnetic radiation, or even differences in the
sound produced by the fan on the processor. It is even possible to mount remote side-channel
attacks where one measures differences in response times from a remote server. A good
survey of such attacks, focused on power analysis applied to symmetric algorithms such as
AES, can be found in [21].
To protect against such side-channel attacks at the hardware level various techniques have
been proposed including utilizing techniques based on secret-sharing (called masking in the
side-channel community). In the area of software one needs to ensure code is constant-time
at the least (i.e. every execution path takes the same amount of time), indeed having multiple
execution paths can itself lead to attacks via power-analysis.
To enable increased performance it is becoming increasingly common for processor manu-
facturers to supply special instructions to enable improvements to cryptographic algorithms.
This is similar to the multi-media extensions which have been common place for other appli-
cations for some decades. An example of this is special instructions on x86 chips to perform
operations related to AES, to perform GCM-mode and to perform some ECC operations.
Public key, i.e. number theoretic constructions, are particularly expensive in terms of compu-
tational resources. Thus it is common for these speciﬁc algorithms to be implemented in low
level machine code, which is tuned to a speciﬁc architecture. However, this needs to be done
with care so as to take into account the earlier mentioned side-channel attacks.
Finally an implementation can also be prone to fault attacks. These are attacks in which an
attacker injects faults (either physical faults on hardware, or datagram faults into a protocol).
Page 35
