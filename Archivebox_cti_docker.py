import argparse
import csv
import os
import subprocess
import sys
import re
import time
import traceback
from urllib.parse import urlparse
from urllib.robotparser import RobotFileParser
from bs4 import BeautifulSoup
from htmldate import find_date
import mysql
from mysql.connector import Error
import requests
from tqdm import tqdm
import logging
from datetime import datetime, timedelta

import urllib

# 在linux中
# try:
#     # 尝试导入并替换sqlite3
#     __import__('pysqlite3')
#     sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')
#     # 验证版本
#     import sqlite3
#     print(f"Using pysqlite3 version: {sqlite3.sqlite_version}")
# except ImportError:
#     print("无法导入pysqlite3，请先安装: pip install pysqlite3-binary")
#     print("继续使用系统sqlite3，但可能会出错")


from mian_data_pa import save_to_database
from utils import extract_wechat_article_date


# $env:PYTHONPATH = "K:\CTI\Data_Excation;$env:PYTHONPATH"

DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}


# 日志
# 保存到output.txt
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logging.FileHandler('output.txt', mode='w', encoding='utf-8')

logging.info("正在初始化RAG分析器...")

# 全局变量声明
_analyzer_instance = None

# 初始化部分 - 移除第一个初始化代码块
try:
    from AI_copy import RAGAnalyzer
    from RAG.utils import web_analyze
    
    # 只有在不是主应用初始化时才初始化
    if 'RAG_INITIALIZING' not in os.environ:
        # 直接初始化分析器实例
        _analyzer_instance = RAGAnalyzer(db_config=DB_CONFIG)
        logging.info("RAG分析器初始化成功")
    else:
        print("pa_week: 检测到主应用正在初始化，跳过RAG初始化")
except ImportError as e:
    _analyzer_instance = None
    logging.error(f"无法导入 RAGAnalyzer 类，错误信息: {e}")
    logging.error(f"当前工作目录: {os.getcwd()}")
    logging.error(f"Python路径: {sys.path}")
    traceback.print_exc()
except Exception as e:
    _analyzer_instance = None
    logging.error(f"初始化RAG分析器失败: {e}")
    traceback.print_exc()

def get_analyzer_instance():
    """获取RAGAnalyzer实例"""
    global _analyzer_instance
    if _analyzer_instance is None:
        # 如果初始化失败，尝试重新初始化
        try:
            logging.info("尝试重新初始化RAG分析器...")
            _analyzer_instance = RAGAnalyzer(db_config=DB_CONFIG)
            logging.info("RAG分析器重新初始化成功")
        except Exception as e:
            logging.error(f"初始化RAG分析器失败: {e}")
            traceback.print_exc()
    return _analyzer_instance



import os
import json
import logging
from datetime import datetime

class SnapshotManager:
    """管理ArchiveBox快照ID的类"""
    
    def __init__(self, storage_file="snapshot_registry.json"):
        self.storage_file = storage_file
        self.snapshots = self._load_snapshots()
        
    def _load_snapshots(self):
        """加载已有的快照记录"""
        if not os.path.exists(self.storage_file):
            return {"processed": [], "pending": [], "failed": []}
            
        try:
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 确保数据结构完整
                if "processed" not in data:
                    data["processed"] = []
                if "pending" not in data:
                    data["pending"] = []
                if "failed" not in data:
                    data["failed"] = []
                return data
        except Exception as e:
            logging.error(f"加载快照记录失败: {str(e)}")
            return {"processed": [], "pending": [], "failed": []}
    
    def save_snapshots(self):
        """保存快照记录到文件"""
        try:
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(self.snapshots, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logging.error(f"保存快照记录失败: {str(e)}")
            return False
    
    def add_snapshot(self, snapshot_id, url, status="pending"):
        """添加新的快照ID"""
        if status not in ["processed", "pending", "failed"]:
            logging.warning(f"无效的状态: {status}，使用默认状态 'pending'")
            status = "pending"
            
        # 创建快照记录
        snapshot_record = {
            "id": snapshot_id,
            "url": url,
            "added_time": datetime.now().isoformat(),
            "last_update": datetime.now().isoformat(),
            "attempts": 0
        }
        
        # 检查是否已存在
        existing = self.get_snapshot_by_id(snapshot_id)
        if existing:
            # 更新现有记录
            for item in self.snapshots[existing["status"]]:
                if item["id"] == snapshot_id:
                    item.update(snapshot_record)
                    item["status"] = status
                    break
            # 如果状态改变，需要移动到新列表
            if existing["status"] != status:
                self.snapshots[existing["status"]] = [
                    s for s in self.snapshots[existing["status"]] 
                    if s["id"] != snapshot_id
                ]
                self.snapshots[status].append(snapshot_record)
        else:
            # 添加新记录
            snapshot_record["status"] = status
            self.snapshots[status].append(snapshot_record)
            
        return self.save_snapshots()
    
    def mark_processed(self, snapshot_id, success=True):
        """将快照标记为已处理"""
        snapshot = self.get_snapshot_by_id(snapshot_id)
        if not snapshot:
            logging.warning(f"找不到快照ID: {snapshot_id}")
            return False
            
        # 从原状态列表中删除
        old_status = snapshot["status"]
        self.snapshots[old_status] = [
            s for s in self.snapshots[old_status] 
            if s["id"] != snapshot_id
        ]
        
        # 更新记录并添加到新状态
        snapshot["last_update"] = datetime.now().isoformat()
        snapshot["attempts"] += 1
        snapshot["status"] = "processed" if success else "failed"
        
        self.snapshots[snapshot["status"]].append(snapshot)
        return self.save_snapshots()
    
    def get_snapshot_by_id(self, snapshot_id):
        """根据ID获取快照记录"""
        for status in ["pending", "processed", "failed"]:
            for snapshot in self.snapshots[status]:
                if snapshot["id"] == snapshot_id:
                    snapshot["status"] = status  # 添加状态字段
                    return snapshot
        return None
    
    def get_pending_snapshots(self, limit=10):
        """获取等待处理的快照"""
        return self.snapshots["pending"][:limit]
    
    def get_stats(self):
        """获取快照统计信息"""
        return {
            "total": len(self.snapshots["pending"]) + 
                     len(self.snapshots["processed"]) + 
                     len(self.snapshots["failed"]),
            "pending": len(self.snapshots["pending"]),
            "processed": len(self.snapshots["processed"]),
            "failed": len(self.snapshots["failed"])
        }
    
    def __str__(self):
        stats = self.get_stats()
        return f"SnapshotManager: {stats['total']}个快照 (待处理: {stats['pending']}, 已处理: {stats['processed']}, 失败: {stats['failed']})"



# 先使用ArchiveBox获取网页内容
def add_url_to_archivebox(target_url, timeout=600):  
    """将单个URL添加到ArchiveBox并实时显示输出，同时返回snapshot_id"""
    
    print(f"正在添加URL到ArchiveBox: {target_url}")
    print(f"设置超时时间: {timeout}秒")
    
    # 只使用readability提取器
    add_cmd = f"docker compose run archivebox add \"{target_url}\""

    try:
        # 使用Popen进行更细粒度的控制
        start_time = time.time()
        process = subprocess.Popen(
            add_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',  # 处理解码错误
            bufsize=1  # 行缓冲
        )
        
        # 实时读取并显示输出
        stdout_data = []
        stderr_data = []
        snapshot_id = None
        
        # 正则表达式用于从输出中匹配snapshot_id
        # 修改正则表达式，增加更多匹配模式
        snapshot_patterns = [
            re.compile(r'Saved (?:to|in|as) ([0-9]+\.[0-9]+)'),  # 原始模式
            re.compile(r'URL already exists in index: \S+/archive/([0-9]+\.[0-9]+)/index\.json'),  # 已存在URL模式
            re.compile(r'\./archive/([0-9]+\.[0-9]+)'),  # 新模式，匹配 ./archive/1751002879.392538
            re.compile(r'/archive/([0-9]+\.[0-9]+)'),  # 备用模式
            re.compile(r'> ([0-9]+\.[0-9]+)')  # 更宽松的匹配
        ]
        
        # 不使用select模块，改用轮询方式
        import io
        import threading
        
        def read_stream(stream, data_list, prefix=''):
            """线程函数：持续读取流并处理输出"""
            nonlocal snapshot_id
            try:
                for line in iter(stream.readline, ''):
                    if not line:
                        break
                    # 确保line是字符串且正确处理编码
                    if isinstance(line, bytes):
                        line = line.decode('utf-8', errors='replace')
                    print(f"{prefix}{line.rstrip()}")
                    data_list.append(line)
                    
                    # 检查snapshot_id，遍历所有正则表达式模式
                    if not snapshot_id:  # 只在尚未找到ID时尝试匹配
                        for pattern in snapshot_patterns:
                            match = pattern.search(line)
                            if match:
                                potential_id = match.group(1)
                                # 验证ID格式，要求包含小数点，且至少为8位
                                if '.' in potential_id and len(potential_id) >= 8:
                                    snapshot_id = potential_id
                                    print(f"从输出中提取到snapshot_id: {snapshot_id} (使用模式: {pattern.pattern})")
                                    break
            except UnicodeDecodeError as e:
                print(f"编码错误，使用UTF-8 replace模式: {e}")
            except Exception as e:
                print(f"读取流时出错: {e}")
        
        print("开始执行命令，等待输出...")
        
        # 启动读取线程
        stdout_thread = threading.Thread(target=read_stream, args=(process.stdout, stdout_data))
        stderr_thread = threading.Thread(target=read_stream, args=(process.stderr, stderr_data, 'ERROR: '))
        
        stdout_thread.daemon = True
        stderr_thread.daemon = True
        
        stdout_thread.start()
        stderr_thread.start()
        
        # 等待进程完成或超时
        while True:
            # 检查超时
            if time.time() - start_time > timeout:
                print(f"命令执行超时（{timeout}秒），终止进程")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                return False, None
            
            # 检查进程是否结束
            if process.poll() is not None:
                break
                
            # 定期显示进度
            elapsed = int(time.time() - start_time)
            if elapsed % 20 == 0 and elapsed > 0:
                print(f"命令仍在执行中... 已用时 {elapsed} 秒")
            
            time.sleep(0.5)  # 减少CPU使用率
        
        # 等待读取线程完成
        stdout_thread.join(timeout=5)
        stderr_thread.join(timeout=5)
        
        # 如果未找到snapshot_id，尝试从完整输出中再次查找
        if not snapshot_id:
            print("尝试从完整输出中查找snapshot_id...")
            full_output = ''.join(stdout_data)
            for pattern in snapshot_patterns:
                match = pattern.search(full_output)
                if match:
                    snapshot_id = match.group(1)
                    print(f"从完整输出中提取到snapshot_id: {snapshot_id} (使用模式: {pattern.pattern})")
                    break
            
        # 如果仍未找到snapshot_id，尝试自定义查询        
        if not snapshot_id:
            print("未从输出中找到snapshot_id，尝试自定义查询...")
            
            # 首先尝试直接列出最近的存档
            try:
                # 使用ls命令查找最新添加的目录
                list_cmd = f"docker compose exec archivebox bash -c \"ls -la /data/archive/ | grep -v index | tail -n 5\""
                list_result = subprocess.run(list_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=30)
                
                print("最近5个存档目录:")
                print(list_result.stdout)
                
                # 尝试从输出中提取数字格式的ID
                for line in list_result.stdout.split('\n'):
                    # 查找形如 1751002879.392538 的ID
                    id_match = re.search(r'([0-9]+\.[0-9]+)', line)
                    if id_match:
                        potential_id = id_match.group(1)
                        # 验证这是我们要找的目录
                        verify_cmd = f"docker compose exec archivebox bash -c \"grep -l '{target_url}' /data/archive/{potential_id}/index.json 2>/dev/null\""
                        verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=10)
                        if verify_result.stdout.strip():
                            snapshot_id = potential_id
                            print(f"通过目录列表找到匹配的snapshot_id: {snapshot_id}")
                            break
            except Exception as ls_error:
                print(f"列出最近存档目录失败: {str(ls_error)}")
            
            # 如果上面方法失败，尝试通过URL查询
            if not snapshot_id:
                print("尝试使用URL查找snapshot_id...")
                snapshot_id = find_snapshot_id_by_url(target_url)
        
        if process.returncode == 0 or snapshot_id:
            print(f"成功添加URL: {target_url}")
            if snapshot_id:
                print(f"最终获取的snapshot_id: {snapshot_id}")
                return True, snapshot_id
            else:
                print("警告: 添加成功但未能获取snapshot_id，尝试最后一次查询")
                # 最后一次尝试，直接用grep命令查找
                try:
                    grep_cmd = f"docker compose exec archivebox bash -c \"grep -l '{target_url}' /data/archive/*/index.json\""
                    grep_result = subprocess.run(grep_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=30)
                    if grep_result.stdout:
                        # 从路径中提取ID
                        path_match = re.search(r'/archive/([0-9]+\.[0-9]+)/index\.json', grep_result.stdout)
                        if path_match:
                            final_id = path_match.group(1)
                            print(f"通过grep命令找到snapshot_id: {final_id}")
                            return True, final_id
                except Exception as grep_error:
                    print(f"grep查询失败: {str(grep_error)}")
                
                print("所有方法都未能获取snapshot_id")
                return True, None
        else:
            print(f"添加URL失败，退出代码: {process.returncode}")
            return False, None
            
    except Exception as e:
        print(f"执行命令时出错: {str(e)}")
        traceback.print_exc()
        return False, None


def find_snapshot_id_by_url(target_url, timeout=30):
    """根据URL查找snapshot_id"""
    print(f"尝试查找URL的快照ID: {target_url}")
    
    try:
        # 使用archivebox list命令查找匹配的URL
        cmd = f"docker compose exec archivebox bash -c \"cd /data && archivebox list --json --filter-url='{target_url}' | head -n 1\""
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=timeout)
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                entries = json.loads(result.stdout)
                if entries and len(entries) > 0:
                    for entry in entries:
                        if 'url' in entry and entry['url'] == target_url and 'timestamp' in entry:
                            snapshot_id = entry['timestamp']
                            print(f"找到URL对应的snapshot_id: {snapshot_id}")
                            return snapshot_id
            except json.JSONDecodeError:
                print("解析返回的JSON失败")
                
    except subprocess.TimeoutExpired:
        print(f"查询命令执行超时({timeout}秒)")
    except Exception as e:
        print(f"查找snapshot_id时出错: {str(e)}")
        
    return None


def extract_single_url_by_id(snapshot_id, url):
    """通过snapshot_id直接提取内容"""
    print(f"通过快照ID直接提取内容: {snapshot_id}")
    
    # 确认快照存在
    check_cmd = f"docker compose exec archivebox bash -c \"test -d /data/archive/{snapshot_id} && echo 'exists' || echo 'not exists'\""
    check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
    
    if "exists" not in check_result.stdout:
        print(f"快照ID不存在: {snapshot_id}")
        return None, None, None, None
    
    # === 首先初始化所有变量 ===
    content = ''
    html_cont = ''
    publication_date = None
    title = "Unknown Title"
    
    # === 1. 提取标题 ===
    # 获取完整的index.json以正确提取标题
    index_cmd = f"docker compose exec archivebox bash -c \"cat /data/archive/{snapshot_id}/index.json 2>/dev/null\""
    try:
        result = subprocess.run(index_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
        index_content = result.stdout.strip()
        
        # 解析JSON内容
        import json
        index_data = json.loads(index_content)
        
        # 检查title字段类型并根据不同结构提取标题
        if 'title' in index_data:
            title_field = index_data['title']
            
            # 情况1: title字段是字符串
            if isinstance(title_field, str):
                title = title_field
                print(f"直接从title字段提取到标题: '{title}'")
            
            # 情况2: title字段是列表
            elif isinstance(title_field, list) and title_field:
                for item in title_field:
                    # 情况2a: 列表项是包含output键的对象
                    if isinstance(item, dict) and 'output' in item and item['output']:
                        title = item['output']
                        print(f"从title列表的output提取到标题: '{title}'")
                        break
                    # 情况2b: 列表项是字符串
                    elif isinstance(item, str):
                        title = item
                        print(f"从title列表提取到标题: '{title}'")
                        break
                        
            # 情况3: title字段是对象
            elif isinstance(title_field, dict):
                if 'output' in title_field and title_field['output']:
                    title = title_field['output']
                    print(f"从title对象的output提取到标题: '{title}'")
        
        # 如果上述方法都提取不到标题，尝试使用不同的jq命令
        if not title or title == "Unknown Title":
            print("从索引JSON解析标题失败，尝试jq命令")
            
            # 尝试多种jq命令格式
            jq_commands = [
                f"docker compose exec archivebox bash -c \"jq -r '.title' /data/archive/{snapshot_id}/readability/article.json 2>/dev/null\"",
            ]
            
            for cmd in jq_commands:
                try:
                    jq_result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
                    jq_title = jq_result.stdout.strip()
                    if jq_title and jq_title != "null" and jq_title != "Unknown Title":
                        title = jq_title
                        print(f"使用jq命令提取到标题: '{title}'")
                        break
                except Exception as jq_e:
                    print(f"jq命令提取标题失败: {str(jq_e)}")
        
    except Exception as e:
        print(f"从index.json获取标题失败: {str(e)}")
        title = "Unknown Title"
    
    # === 2. 提取文本内容 ===
    cmd_txt = f"docker compose exec archivebox bash -c \"cat /data/archive/{snapshot_id}/readability/content.txt 2>/dev/null || echo ''\""
    try:
        result_txt = subprocess.run(cmd_txt, shell=True, capture_output=True, text=False)
        raw_content = result_txt.stdout
        
        # 尝试多种编码方式
        for encoding in ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']:
            try:
                content = raw_content.decode(encoding)
                print(f"成功使用 {encoding} 从 readability/content.txt 提取了 {len(content)} 字符")
                break
            except UnicodeDecodeError:
                continue
                
        if not content and raw_content:
            content = raw_content.decode('utf-8', errors='replace')
    except Exception as e:
        print(f"提取文本内容失败: {str(e)}")
    
    # === 3. 提取HTML内容 ===
    cmd_html = f"docker compose exec archivebox bash -c \"cat /data/archive/{snapshot_id}/readability/content.html 2>/dev/null || echo ''\""
    try:
        result_html = subprocess.run(cmd_html, shell=True, capture_output=True, text=False)
        raw_html = result_html.stdout
        
        # 尝试多种编码方式
        for encoding in ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']:
            try:
                html_cont = raw_html.decode(encoding)
                print(f"成功使用 {encoding} 从 readability/content.html 提取了 {len(html_cont)} 字符")
                break
            except UnicodeDecodeError:
                continue
                
        if not html_cont and raw_html:
            html_cont = raw_html.decode('utf-8', errors='replace')
    except Exception as e:
        print(f"提取HTML内容失败: {str(e)}")
    
    # === 4. 提取发布日期 - 多种方法 ===
    # 首先检查是否为微信公众号文章
    if 'mp.weixin.qq.com' in url:
        publication_date = extract_wechat_article_date(url)

    # 方法1: 从article.json提取多种可能的日期字段
    try:
        # 尝试多种常见的日期字段名
        date_fields = ['publishedTime', 'published_time', 'datePublished', 'date_published', 
                      'created', 'date', 'timestamp', 'pubDate', 'publication_date']
        
        for field in date_fields:
            jq_cmd = f"docker compose exec archivebox bash -c \"jq -r '.{field}' /data/archive/{snapshot_id}/readability/article.json 2>/dev/null\""
            result = subprocess.run(jq_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
            extracted_date = result.stdout.strip()
            
            if extracted_date and extracted_date != "null" and extracted_date != "":
                publication_date = extracted_date
                print(f"从article.json的'{field}'字段提取到publication_date: {publication_date}")
                break
                
    except Exception as e:
        print(f"从article.json提取publication_date时出错: {str(e)}")
    
    # 方法2: 从index.json提取日期信息
    if not publication_date:
        try:
            index_date_cmd = f"docker compose exec archivebox bash -c \"jq -r '.timestamp' /data/archive/{snapshot_id}/index.json 2>/dev/null\""
            result = subprocess.run(index_date_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
            index_date = result.stdout.strip()
            
            if index_date and index_date != "null":
                # 将时间戳转换为可读日期格式
                try:
                    from datetime import datetime
                    if '.' in index_date:
                        # 处理带小数的时间戳
                        timestamp = float(index_date)
                    else:
                        timestamp = int(index_date)
                    
                    publication_date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                    print(f"从index.json的timestamp转换得到publication_date: {publication_date}")
                except (ValueError, OSError) as e:
                    print(f"时间戳转换失败: {str(e)}")
        except Exception as e:
            print(f"从index.json提取日期时出错: {str(e)}")
    
    # 方法3: 从HTML内容中提取日期元数据
    if not publication_date and html_cont:
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_cont, 'html.parser')
            
            # 查找各种日期相关的meta标签
            date_selectors = [
                'meta[property="article:published_time"]',
                'meta[name="date"]',
                'meta[name="publication-date"]',
                'meta[name="pubdate"]',
                'meta[property="og:published_time"]',
                'meta[name="article:published_time"]',
                'time[datetime]',
                'time[pubdate]'
            ]
            
            for selector in date_selectors:
                meta_tag = soup.select_one(selector)
                if meta_tag:
                    date_value = meta_tag.get('content') or meta_tag.get('datetime')
                    if date_value:
                        publication_date = date_value
                        print(f"从HTML meta标签提取到publication_date: {publication_date} (使用选择器: {selector})")
                        break
                        
        except Exception as e:
            print(f"从HTML内容提取日期时出错: {str(e)}")
    
    # 方法4: 使用htmldate库从URL提取日期（改进版本）
    if not publication_date:
        print(f"方法4: 开始使用htmldate库提取日期，当前publication_date: {publication_date}")
        try:
            # 首先尝试从URL提取
            url_date = find_date(url)
            print(f"方法4: 从URL提取结果: {url_date}")
            if url_date:
                publication_date = url_date
                print(f"使用htmldate从URL提取到publication_date: {publication_date}")
            else:
                # 如果URL提取失败，尝试从HTML内容提取
                if html_cont:
                    print(f"方法4: URL提取失败，尝试从HTML内容提取（HTML长度: {len(html_cont)}）")
                    content_date = find_date(html_cont)
                    print(f"方法4: 从HTML内容提取结果: {content_date}")
                    if content_date:
                        publication_date = content_date
                        print(f"使用htmldate从HTML提取到publication_date: {publication_date}")
                else:
                    print(f"方法4: 无HTML内容可用于提取")
        except Exception as e:
            print(f"使用htmldate提取日期时出错: {str(e)}")
    else:
        print(f"方法4: 跳过（publication_date已有值: {publication_date}）")
    
    # 方法5: 从htmltotext.txt前5000字符中用正则提取日期（优先标题附近）
    if not publication_date:
        print(f"方法5: 开始从htmltotext.txt文本中提取日期，当前publication_date: {publication_date}")
        try:
            htmltotext_cmd = f"docker compose exec archivebox bash -c \"head -c 5000 /data/archive/{snapshot_id}/htmltotext.txt 2>/dev/null || echo ''\""
            result = subprocess.run(htmltotext_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
            text_head = result.stdout.strip()
            print(f"方法5: 从htmltotext.txt读取到 {len(text_head)} 个字符")
            if text_head:
                # 1. 去除所有URL和无用链接
                import re
                text_no_links = re.sub(r'https?://\S+|www\.\S+', '', text_head)
                # 2. 定位标题（优先用已提取title，否则用htmltotext前几行）
                title_for_search = title if title and title != 'Unknown Title' else None
                lines = text_no_links.splitlines()
                title_idx = -1
                if title_for_search:
                    for idx, line in enumerate(lines):
                        if title_for_search.strip()[:10] in line:
                            title_idx = idx
                            break
                # 3. 在标题前后各取5行（或无title时取前20行）
                search_scope = ''
                if title_idx != -1:
                    start = max(0, title_idx - 5)
                    end = min(len(lines), title_idx + 6)
                    search_scope = '\n'.join(lines[start:end])
                else:
                    search_scope = '\n'.join(lines[:20])
                # 4. 正则提取日期
                date_patterns = [
                    r'([12][0-9]{3}[-/年\.][01]?[0-9][-/月\.][0-3]?[0-9])',  # 2025-06-30 或 2025/06/30 或 2025年06月30日
                    r'([A-Z][a-z]+ [0-9]{1,2}, [12][0-9]{3})',              # June 30, 2025
                    r'([0-3]?[0-9] [A-Z][a-z]+ [12][0-9]{3})',              # 30 June 2025
                ]
                found = None
                for i, pat in enumerate(date_patterns):
                    m = re.search(pat, search_scope)
                    if m:
                        found = m.group(1)
                        print(f"方法5: 在标题附近使用模式{i+1}匹配到日期: {found}")
                        break
                if not found:
                    # 如果标题附近没找到，再全局查找
                    for i, pat in enumerate(date_patterns):
                        m = re.search(pat, text_no_links)
                        if m:
                            found = m.group(1)
                            print(f"方法5: 全文使用模式{i+1}匹配到日期: {found}")
                            break
                if found:
                    publication_date = found
                    print(f"从htmltotext.txt文本中正则提取到publication_date: {publication_date}")
                else:
                    print(f"方法5: 未在文本中找到匹配的日期格式")
            else:
                print(f"方法5: htmltotext.txt文件为空或不存在")
        except Exception as e:
            print(f"从htmltotext.txt文本正则提取日期时出错: {str(e)}")
    else:
        print(f"方法5: 跳过（publication_date已有值: {publication_date}）")

    # 设置默认值
    if not publication_date:
        publication_date = "未知时间"
    
    print(f"最终确定的publication_date: {publication_date}")
    
    # === 5. 如果缺少标题，尝试从HTML内容提取 ===
    if (not title or title == 'Unknown Title' or title == 'null') and html_cont:
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_cont, 'html.parser')
            if soup.title:
                title = soup.title.text.strip()
                print(f"从HTML提取到标题: {title}")
        except:
            pass
    
    return content, html_cont, publication_date, title


def process_pending_snapshots(limit=10, analyze=True):
    """处理等待中的快照"""
    # 初始化快照管理器
    snapshot_manager = SnapshotManager()
    pending_snapshots = snapshot_manager.get_pending_snapshots(limit)
    
    if not pending_snapshots:
        logging.info("没有待处理的快照")
        return
    
    logging.info(f"开始处理 {len(pending_snapshots)} 个待处理快照...")
    
    # 处理结果统计
    results = {
        'total': len(pending_snapshots),
        'extracted': 0,
        'analyzed': 0,
        'saved': 0,
        'failed': 0
    }
    
    for i, snapshot in enumerate(pending_snapshots):
        snapshot_id = snapshot['id']
        url = snapshot['url']
        
        logging.info(f"\n处理快照 [{i+1}/{len(pending_snapshots)}]: {snapshot_id} (URL: {url})")
        
        try:
            # 提取内容
            content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id,url)
            
            if not content and not html_content:
                logging.warning(f"未能提取内容，标记为失败: {snapshot_id}")
                snapshot_manager.mark_processed(snapshot_id, success=False)
                results['failed'] += 1
                continue
            
            results['extracted'] += 1
            
            # 分析内容
            if analyze and content:
                analysis_result = analyze_archived_content(
                    content, html_content, url, title, publication_date,
                    analyze_after_crawl=True, snapshot_id=snapshot_id
                )
                
                if analysis_result == "success":
                    results['analyzed'] += 1
                    logging.info(f"成功分析文章: {url}")
                    snapshot_manager.mark_processed(snapshot_id, success=True)
                else:
                    logging.warning(f"分析失败: {url}")
                    snapshot_manager.mark_processed(snapshot_id, success=False)
                    results['failed'] += 1
            else:
                logging.info(f"跳过分析: {url}")
                snapshot_manager.mark_processed(snapshot_id, success=True)
        
        except Exception as e:
            logging.error(f"处理快照时出错: {snapshot_id}, 错误: {str(e)}")
            traceback.print_exc()
            snapshot_manager.mark_processed(snapshot_id, success=False)
            results['failed'] += 1
    
    # 显示最终结果
    logging.info("\n" + "="*50)
    logging.info("处理完成! 统计信息:")
    logging.info(f"总快照数量: {results['total']}")
    logging.info(f"成功提取内容: {results['extracted']}")
    logging.info(f"成功分析文章: {results['analyzed']}")
    logging.info(f"失败数量: {results['failed']}")
    logging.info("="*50)
    
    return results


def analyze_archived_content(content, html_content, url, title=None, publication_date=None, analyze_after_crawl=True):
    """分析归档的内容并存储到数据库"""
    print("\n分析文章内容...")
    analyzer = get_analyzer_instance()
    if not analyzer:
        print("无法初始化RAG分析器，只执行爬取任务")
        analyze_after_crawl = False
    else:
        analyze_after_crawl = True
        print("RAG分析器初始化成功，将在爬取后立即分析")
    success_count = 0
    failure_count = 0
    failure_reasons = {"storage_error": 0, "analysis_error": 0}
    analysis_id = None  # 初始化analysis_id

     # 检查内容编码
    print(f"内容编码检查 - 类型: {type(content)}, 长度: {len(content) if content else 0}")
    if content and isinstance(content, str):
        # 检查是否包含可疑的乱码字符
        problematic_chars = sum(1 for c in content[:100] if ord(c) > 0xFFFF)
        if problematic_chars > 5:  # 如果前100个字符中有超过5个高码点字符，可能是编码问题
            print("警告：检测到可能的编码问题，尝试重新编码内容")
            # 重新编码内容
            content = content.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')
    
    # 同样检查HTML内容
    if html_content and isinstance(html_content, str):
        problematic_chars = sum(1 for c in html_content[:100] if ord(c) > 0xFFFF)
        if problematic_chars > 5:
            print("警告：检测到HTML内容可能存在编码问题，尝试重新编码")
            html_content = html_content.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')

    # 保存到数据库中
    try:
        # 修改：保存函数返回完整记录而非仅ID
        print(f"准备存储文档，标题: '{title}'")
        
        # 确保数据库连接使用正确的字符集
        db_config = DB_CONFIG.copy()
        db_config['charset'] = 'utf8mb4'
        db_config['collation'] = 'utf8mb4_unicode_ci'
        
        inserted_count, new_records = save_to_database(
            db_config=db_config,
            all_results=[{
                'link': url,
                'title': title,
                'snippet': f"{url}",
                'query': f"discovered_from:{url}",
                'extracted_text': content,
                'html_content': html_content,
                'publication_date': publication_date,  # 添加发布日期
            }],
            return_records=True  
        )
        
        if inserted_count == -1:
            print(f"数据库存储失败，继续处理下一条")
            failure_count += 1
            failure_reasons["storage_error"] += 1
        else:
            print(f"✓ 成功存储: {inserted_count} 条")
            success_count += 1
            
            # 处理分析逻辑
            if analyze_after_crawl:
                # 如果有新记录，分析新记录
                if new_records:
                    print(f"开始分析新爬取的数据...")
                    
                    # 直接使用存储返回的记录进行分析，避免二次查询
                    for record in new_records:
                        try:
                            # 使用记录中的ID（如果存在）
                            record_id = record.get('id', '未知ID')
                            print(f"分析文档 ID: {record_id}...")
                            
                            # 直接使用记录数据进行调用
                            result = analyzer.analyze_from_crawled_data(
                                record_data=record,  # 传入完整记录
                                analysis_type="all"
                            )

                            # 拿取分析数据的id,返回的# 返回成功信息
                            current_analysis_id = result.get("analysis_id")
                            if current_analysis_id:
                                analysis_id = current_analysis_id  # 保存最后一个成功的analysis_id

                            if result.get("success"):
                                print(f"✓ 成功分析文档 {record_id}")
                            else:
                                error_msg = result.get('error', '未知错误')
                                print(f"分析文档 {record_id} 失败: {error_msg}")
                                
                        except Exception as e:
                            print(f"分析文档时出错: {str(e)}")
                            traceback.print_exc()
                            
                elif inserted_count == 0:
                    # 重复链接情况，检查是否已有分析结果
                    print(f"检测到重复链接，检查是否已有分析结果...")
                    try:
                        with mysql.connector.connect(**DB_CONFIG) as conn:
                            cursor = conn.cursor(dictionary=True)
                            
                            # 查找该URL的最新分析结果
                            cursor.execute("""
                                SELECT ra.id as analysis_id, ra.analysis_status, cd.id as crawled_data_id
                                FROM crawled_data cd
                                LEFT JOIN rag_analysis ra ON cd.id = ra.crawled_data_id
                                WHERE cd.link = %s
                                ORDER BY ra.analysis_time DESC
                                LIMIT 1
                            """, (url,))
                            
                            existing_record = cursor.fetchone()
                            
                            if existing_record:
                                if existing_record['analysis_id'] and existing_record['analysis_status'] == 1:
                                    # 已有成功的分析结果
                                    analysis_id = existing_record['analysis_id']
                                    success_count += 1  # 标记为成功
                                    print(f"找到已有分析结果，analysis_id: {analysis_id}")
                                else:
                                    # 有记录但未分析或分析失败，重新分析
                                    crawled_data_id = existing_record['crawled_data_id']
                                    print(f"找到未分析记录，重新分析 ID: {crawled_data_id}")
                                    
                                    # 获取完整记录信息
                                    cursor.execute("""
                                        SELECT id, title, link, extracted_text, html_content
                                        FROM crawled_data WHERE id = %s
                                    """, (crawled_data_id,))
                                    
                                    record_data = cursor.fetchone()
                                    if record_data:
                                        # 添加 publication_date 到记录数据
                                        record_data['publication_date'] = publication_date
                                        
                                        result = analyzer.analyze_from_crawled_data(
                                            record_data=record_data,
                                            analysis_type="all"
                                        )
                                        
                                        current_analysis_id = result.get("analysis_id")
                                        if current_analysis_id:
                                            analysis_id = current_analysis_id
                                            success_count += 1  # 标记为成功
                                            print(f"✓ 成功重新分析文档 {crawled_data_id}")
                                        else:
                                            failure_count += 1  # 标记为失败
                                            print(f"重新分析失败: {result.get('error', '未知错误')}")
                            else:
                                print(f"警告: 未找到对应的数据库记录")
                                failure_count += 1  # 标记为失败
                                
                    except Exception as e:
                        print(f"检查重复链接分析结果时出错: {str(e)}")
                        traceback.print_exc()
                        failure_count += 1  # 标记为失败
                        
    except mysql.connector.Error as db_error:
        print(f"数据异常: {db_error}")
        failure_count += 1
        failure_reasons["storage_error"] += 1

    # 返回处理结果
    return {
        "success": success_count > 0,
        "analysis_id": analysis_id,
        "success_count": success_count,
        "failure_count": failure_count,
        "total_processed": success_count + failure_count
    }

def process_archive_box_content(urls=None, csv_file=None, batch_size=10, analyze=True):
    """处理和分析ArchiveBox中的内容"""
    # 初始化快照管理器
    snapshot_manager = SnapshotManager()
    
    # 获取URL列表
    if csv_file:
        logging.info(f"从CSV文件加载URL: {csv_file}")
        urls = load_urls_from_csv(csv_file)
    
    if not urls:
        logging.warning("没有提供URL，程序退出")
        return False
    
    logging.info(f"准备处理 {len(urls)} 个URL")
    
    # 添加URL到ArchiveBox，并获取快照ID
    successful_urls_with_ids = []
    for url in urls:
        # 首先检查是否已经存在该URL的快照
        existing_snapshot_id = find_snapshot_id_by_url(url)
        if existing_snapshot_id:
            logging.info(f"URL已存在于ArchiveBox中: {url}, 快照ID: {existing_snapshot_id}")
            successful_urls_with_ids.append((url, existing_snapshot_id))
            # 添加到快照管理器，状态为pending
            snapshot_manager.add_snapshot(existing_snapshot_id, url, "pending")
            continue
        
        # 如果不存在，添加新URL
        success, snapshot_id = add_url_to_archivebox(url, timeout=300)
        if success:
            if snapshot_id:
                logging.info(f"成功添加URL: {url}, 快照ID: {snapshot_id}")
                successful_urls_with_ids.append((url, snapshot_id))
                # 将快照ID添加到管理系统
                snapshot_manager.add_snapshot(snapshot_id, url, "pending")
            else:
                logging.warning(f"添加成功但未获得快照ID: {url}")
                # 尝试再次查找
                found_id = find_snapshot_id_by_url(url)
                if found_id:
                    successful_urls_with_ids.append((url, found_id))
                    snapshot_manager.add_snapshot(found_id, url, "pending")
                    logging.info(f"在二次查询中找到快照ID: {found_id}")
        else:
            logging.warning(f"添加URL失败: {url}")
    
    if not successful_urls_with_ids:
        logging.warning("没有成功添加的URL，程序退出")
        return False
    
    # 确保快照管理器保存状态
    snapshot_manager.save_snapshots()
    
    # 处理结果统计
    results = {
        'total': len(successful_urls_with_ids),
        'extracted': 0,
        'analyzed': 0,
        'saved': 0,
        'failed': 0
    }
    
    # 如果启用了立即分析，则处理每个URL
    if analyze:
        for i, (url, snapshot_id) in enumerate(successful_urls_with_ids):
            if not snapshot_id:
                logging.warning(f"缺少快照ID，跳过: {url}")
                continue
                
            logging.info(f"\n处理URL [{i+1}/{len(successful_urls_with_ids)}]: {url} (快照ID: {snapshot_id})")
            
            try:
                # 从ArchiveBox中提取内容
                content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id,url)
                
                if not content and not html_content:
                    logging.warning(f"未能提取内容，跳过: {url}")
                    snapshot_manager.mark_processed(snapshot_id, success=False)
                    results['failed'] += 1
                    continue
                    
                results['extracted'] += 1
                
                # 分析内容
                if content:
                    analysis_result = analyze_archived_content(
                        content, html_content, url, title, publication_date,
                        analyze_after_crawl=True, snapshot_id=snapshot_id
                    )
                    
                    if analysis_result.get("success", False):
                        results['analyzed'] += 1
                        logging.info(f"成功分析文章: {url}")
                        snapshot_manager.mark_processed(snapshot_id, success=True)
                    else:
                        logging.warning(f"分析失败: {url}")
                        snapshot_manager.mark_processed(snapshot_id, success=False)
                else:
                    logging.warning(f"保存到数据库失败: {url}")
                    snapshot_manager.mark_processed(snapshot_id, success=False)
            
            except Exception as e:
                logging.error(f"处理URL时出错: {url}, 错误: {str(e)}")
                traceback.print_exc()
                if snapshot_id:
                    snapshot_manager.mark_processed(snapshot_id, success=False)
                results['failed'] += 1
    else:
        logging.info("分析功能已禁用，快照将在后续处理")
        # 即使不立即分析，也已经将快照ID保存在了管理器中
    
    # 确保保存更新后的状态
    snapshot_manager.save_snapshots()
    
    # 显示最终结果
    logging.info("\n" + "="*50)
    logging.info("处理完成! 统计信息:")
    logging.info(f"总URL数量: {results['total']}")
    
    if analyze:
        logging.info(f"成功提取内容: {results['extracted']}")
        logging.info(f"成功分析文章: {results['analyzed']}")
        logging.info(f"失败数量: {results['failed']}")
    
    # 显示快照状态
    stats = snapshot_manager.get_stats()
    logging.info(f"快照状态: 总计 {stats['total']} (待处理: {stats['pending']}, 已处理: {stats['processed']}, 失败: {stats['failed']})")
    logging.info("="*50)
    
    return results

def load_urls_from_csv(file_path):
    """从CSV文件中加载URL"""
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return []
    
    urls = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if 'link' in row and row['link']:
                    urls.append(row['link'])
    except Exception as e:
        logging.error(f"读取CSV文件时出错: {str(e)}")
        traceback.print_exc()
    
    return urls


def validate_database_encoding():
    """确认数据库使用正确的字符编码"""
    try:
        with mysql.connector.connect(**DB_CONFIG) as conn:
            with conn.cursor() as cursor:
                # 检查数据库字符集
                cursor.execute("SHOW VARIABLES LIKE 'character_set_database'")
                db_charset = cursor.fetchone()
                
                cursor.execute("SHOW VARIABLES LIKE 'collation_database'")
                db_collation = cursor.fetchone()
                
                cursor.execute("SHOW VARIABLES LIKE 'character_set_connection'")
                conn_charset = cursor.fetchone()
                
                logging.info(f"数据库字符集: {db_charset}")
                logging.info(f"数据库排序规则: {db_collation}")
                logging.info(f"连接字符集: {conn_charset}")
                
                # 如果不是utf8mb4，打印警告
                if db_charset and db_charset[1] != 'utf8mb4':
                    logging.warning(f"数据库字符集不是utf8mb4，可能导致字符编码问题: {db_charset}")
                    logging.warning("建议设置数据库字符集为utf8mb4")
                    
        return True
    except Exception as e:
        logging.error(f"验证数据库编码时出错: {e}")
        return False


def inspect_snapshot(snapshot_path):
    """全面检查快照文件系统状态"""
    # 从路径中提取snapshot_id
    # 可能的格式: './archive/1750922791.820951/index.json' 或 '1750922791.820951'
    if '/' in snapshot_path:
        path_parts = snapshot_path.split('/')
        # 查找数字格式的部分作为ID
        for part in path_parts:
            if re.match(r'^\d+\.\d+$', part):
                snapshot_id = part
                break
        else:
            # 如果没有找到匹配的数字格式，使用倒数第二个部分
            snapshot_id = path_parts[-2] if len(path_parts) > 1 else snapshot_path
    else:
        snapshot_id = snapshot_path
    
    print(f"\n===== 开始检查快照 {snapshot_id} =====\n")
    
    # 1. 基本文件列表
    print("\n1. 基本文件列表")
    list_cmd = f"docker compose exec archivebox bash -c \"find /data/archive/{snapshot_id} -type f | sort\""
    subprocess.run(list_cmd, shell=True)
    
    # 2. 详细文件状态
    print("\n2. 详细文件状态")
    ls_cmd = f"docker compose exec archivebox bash -c \"ls -la /data/archive/{snapshot_id}/\""
    subprocess.run(ls_cmd, shell=True)
    
    # 3. 子目录状态
    print("\n3. 重要子目录状态")
    for subdir in ['readability', 'singlefile', 'dom', 'wget', 'mercury']:
        subdir_cmd = f"docker compose exec archivebox bash -c \"ls -la /data/archive/{snapshot_id}/{subdir}/ 2>/dev/null || echo '{subdir} 目录不存在'\""
        print(f"\n--- {subdir} 目录 ---")
        subprocess.run(subdir_cmd, shell=True)
    
    # 4. 查看重要文件内容预览
    print("\n4. 重要文件内容预览")
    for file_path in [
        '/data/archive/{}/index.json',
        '/data/archive/{}/readability/content.txt',
        '/data/archive/{}/readability/article.json'
    ]:
        full_path = file_path.format(snapshot_id)
        preview_cmd = f"docker compose exec archivebox bash -c \"head -20 {full_path} 2>/dev/null || echo '文件 {full_path} 不存在或为空'\""
        print(f"\n--- {full_path} 预览 ---")
        subprocess.run(preview_cmd, shell=True)
    
    # 5. 查看空间使用情况
    print("\n5. 空间使用情况")
    du_cmd = f"docker compose exec archivebox bash -c \"du -sh /data/archive/{snapshot_id}/* 2>/dev/null\""
    subprocess.run(du_cmd, shell=True)
    
    print(f"\n===== 快照 {snapshot_id} 检查完成 =====\n")

def main():
    parser = argparse.ArgumentParser(description='ArchiveBox内容提取与分析工具')
    parser.add_argument('--csv', type=str, help='包含URL列表的CSV文件路径')
    parser.add_argument('--url', type=str, help='单个URL进行处理')
    parser.add_argument('--batch-size', type=int, default=10, help='批处理大小，默认为10')
    parser.add_argument('--no-analyze', action='store_true', help='仅存档而不进行分析')
    parser.add_argument('--process-snapshots', action='store_true', help='处理待处理的快照')
    parser.add_argument('--limit', type=int, default=10, help='处理快照的数量限制，默认为10')
    parser.add_argument('--snapshot-info', type=str, help='显示指定快照ID的信息')
    parser.add_argument('--cleanup', action='store_true', help='清理失败的快照记录')
    parser.add_argument('--timeout', type=int, default=600, help='添加URL的超时时间(秒)，默认600秒')
    parser.add_argument('--retry', type=int, default=3, help='失败操作的重试次数，默认3次')
    
    args = parser.parse_args()
    
    # 验证数据库编码
    validate_database_encoding()
    
    # 清理失败的快照
    if args.cleanup:
        snapshot_manager = SnapshotManager()
        failed_count = len(snapshot_manager.snapshots["failed"])
        for snapshot in snapshot_manager.snapshots["failed"]:
            snapshot_manager.add_snapshot(snapshot["id"], snapshot["url"], "pending")
        print(f"已将 {failed_count} 个失败的快照重置为待处理状态")
        return
    
    # 处理快照信息查询
    if args.snapshot_info:
        snapshot_id = args.snapshot_info
        print(f"查询快照信息: {snapshot_id}")
        content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id,args.url)

        print(f"快照标题: {title}")
        print(f"发布日期: {publication_date}")
        print(f"内容长度: {len(content) if content else 0}字符")
        print(f"HTML长度: {len(html_content) if html_content else 0}字符")
        return
    
    # 处理待处理的快照
    if args.process_snapshots:
        process_pending_snapshots(limit=args.limit, analyze=not args.no_analyze)
        return
    
    # 处理URL添加
    if not args.csv and not args.url:
        logging.error("请提供CSV文件路径或单个URL，或使用--process-snapshots选项处理待处理快照")
        parser.print_help()
        return
    
    urls = []
    if args.url:
        urls.append(args.url)
    
    process_archive_box_content(
        urls=urls,
        csv_file=args.csv,
        batch_size=args.batch_size,
        analyze=not args.no_analyze
    )

if __name__ == "__main__":
    main()

# 添加url到ArchiveBox但不进行分析
# python Archivebox_cti.py --url "https://example.com/article" --no-analyze

# 处理未分析的记录
# python Archivebox_cti.py --process-snapshots --limit 20

# 查看链接信息
# python Archivebox_cti.py --snapshot-info "1750922791.820951"

def normalize_date_format(date_string):
    """标准化日期格式为 YYYY-MM-DD"""
    if not date_string or date_string == "null":
        return None
    
    try:
        from datetime import datetime
        import re
        
        # 移除常见的无关字符和前缀
        date_string = date_string.strip()
        
        # 常见的日期格式模式
        date_patterns = [
            # ISO 8601 格式
            r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})',  # 2024-12-25T10:30:45
            r'(\d{4}-\d{2}-\d{2})',                      # 2024-12-25
            
            # 美式格式
            r'(\d{1,2}/\d{1,2}/\d{4})',                 # 12/25/2024 或 1/1/2024
            r'(\d{1,2}-\d{1,2}-\d{4})',                 # 12-25-2024
            
            # 英式格式
            r'(\d{1,2}/\d{1,2}/\d{4})',                 # 25/12/2024
            
            # 长格式
            r'(\w+ \d{1,2}, \d{4})',                    # December 25, 2024
            r'(\d{1,2} \w+ \d{4})',                     # 25 December 2024
            
            # 时间戳格式（秒）
            r'^(\d{10})$',                               # 1703519400
            r'^(\d{10}\.\d+)$',                          # 1703519400.123
        ]
        
        # 首先尝试直接解析ISO格式
        if re.match(r'^\d{4}-\d{2}-\d{2}$', date_string):
            return date_string
        
        # 尝试各种模式
        for pattern in date_patterns:
            match = re.search(pattern, date_string)
            if match:
                matched_date = match.group(1)
                
                # 处理时间戳
                if re.match(r'^\d{10}(\.\d+)?$', matched_date):
                    try:
                        timestamp = float(matched_date)
                        dt = datetime.fromtimestamp(timestamp)
                        return dt.strftime('%Y-%m-%d')
                    except (ValueError, OSError):
                        continue
                
                # 尝试解析其他格式
                # 日期 'Jul 04, 2025' 无法标准化，将使用 NULL
                try:
                    # 尝试多种日期格式
                    date_formats = [
                        '%Y-%m-%dT%H:%M:%S',    # ISO format
                        '%Y-%m-%d',              # Simple date
                        '%m/%d/%Y',              # American format
                        '%d/%m/%Y',              # European format
                        '%m-%d-%Y',              # American with dashes
                        '%d-%m-%Y',              # European with dashes
                        '%B %d, %Y',             # December 25, 2024
                        '%d %B %Y',              # 25 December 2024
                        '%b %d, %Y',             # Dec 25, 2024
                        '%d %b %Y',              # 25 Dec 2024
                    ]
                    
                    for fmt in date_formats:
                        try:
                            dt = datetime.strptime(matched_date, fmt)
                            return dt.strftime('%Y-%m-%d')
                        except ValueError:
                            continue
                            
                except Exception:
                    continue
        
        # 如果所有模式都失败，尝试使用dateutil库（如果可用）
        try:
            from dateutil import parser
            dt = parser.parse(date_string)
            return dt.strftime('%Y-%m-%d')
        except (ImportError, ValueError):
            pass
            
        print(f"警告: 无法解析日期格式: {date_string}")
        return None
        
    except Exception as e:
        print(f"日期格式标准化时出错: {str(e)}")
        return None

def get_existing_links_from_archivebox(timeout=60):
    """
    获取ArchiveBox中已存在的所有链接
    
    Returns:
        set: 包含所有已归档URL的集合
    """
    print("正在查询ArchiveBox中的所有已归档链接...")
    existing_links = set()
    
    try:
        list_cmd = "docker compose run --rm archivebox archivebox list --json"
        
        print("执行ArchiveBox list命令...")
        result = subprocess.run(list_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=timeout)
        
        if result.returncode != 0:
            print(f"ArchiveBox list命令执行失败，返回码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
            # 尝试备用命令
            print("尝试备用命令...")
            backup_cmd = "docker compose exec --user=archivebox archivebox archivebox list --json"
            result = subprocess.run(backup_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=timeout)
            
            if result.returncode != 0:
                print(f"备用命令也失败，返回码: {result.returncode}")
                print(f"错误输出: {result.stderr}")
                return existing_links
        
        # 解析JSON输出
        stdout_content = result.stdout.strip()
        if not stdout_content:
            print("ArchiveBox返回空内容")
            return existing_links
        
        try:
            import json
            # 尝试解析为JSON
            archived_entries = json.loads(stdout_content)
            
            if not isinstance(archived_entries, list):
                print(f"ArchiveBox返回的数据格式不正确，预期为list，实际为: {type(archived_entries)}")
                return existing_links
            
            # 从每个条目中提取URL
            for entry in archived_entries:
                if isinstance(entry, dict) and 'url' in entry:
                    url = entry['url']
                    if url:
                        existing_links.add(url)
            
            print(f"成功从ArchiveBox获取到 {len(existing_links)} 个已归档链接")
            
        except json.JSONDecodeError as e:
            print(f"解析ArchiveBox JSON输出失败: {str(e)}")
            print(f"原始输出前200字符: {stdout_content[:200]}")
            
            # 如果JSON解析失败，尝试逐行解析（兼容模式）
            print("尝试使用兼容模式逐行解析...")
            for line in stdout_content.split('\n'):
                line = line.strip()
                if not line:
                    continue
                try:
                    entry = json.loads(line)
                    if isinstance(entry, dict) and 'url' in entry:
                        url = entry['url']
                        if url:
                            existing_links.add(url)
                except json.JSONDecodeError:
                    continue
            
            print(f"兼容模式解析到 {len(existing_links)} 个已归档链接")
        
    except subprocess.TimeoutExpired:
        print(f"查询ArchiveBox链接超时（{timeout}秒）")
    except Exception as e:
        print(f"查询ArchiveBox链接时出错: {str(e)}")
        # 如果主方法失败，尝试备用方法
        try:
            print("尝试备用方法查询ArchiveBox链接...")
            # 使用简化的命令，通过文件系统直接读取
            simple_cmd = "docker compose run --rm archivebox bash -c \"find /data/archive -name 'index.json' -exec grep -l 'url' {} + | head -100\""
            result = subprocess.run(simple_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=30)
            
            if result.returncode == 0 and result.stdout:
                # 从找到的index.json文件中提取URL
                for index_file in result.stdout.strip().split('\n'):
                    if index_file.strip():
                        try:
                            cat_cmd = f"docker compose run --rm archivebox bash -c \"cat '{index_file}'\""
                            cat_result = subprocess.run(cat_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=10)
                            
                            if cat_result.returncode == 0:
                                entry_data = json.loads(cat_result.stdout)
                                if 'url' in entry_data:
                                    existing_links.add(entry_data['url'])
                        except Exception:
                            continue
                
                print(f"备用方法获取到 {len(existing_links)} 个已归档链接")
        except Exception as backup_e:
            print(f"备用方法也失败: {str(backup_e)}")
    
    return existing_links

def get_archivebox_snapshot_url(snapshot_id):
    """
    根据快照ID获取ArchiveBox查看URL
    
    Args:
        snapshot_id (str): ArchiveBox快照ID
        
    Returns:
        str: 完整的ArchiveBox查看URL，如果失败则返回None
    """
    try:
        # 默认ArchiveBox服务器地址（可以从环境变量配置）
        archivebox_base_url = os.getenv('ARCHIVEBOX_BASE_URL', 'http://localhost:8000')
        
        # 构建快照查看URL
        snapshot_url = f"{archivebox_base_url}/archive/{snapshot_id}/"
        
        # 可选：验证URL是否可访问（简单检查）
        try:
            response = requests.head(snapshot_url, timeout=5)
            if response.status_code == 200:
                return snapshot_url
            else:
                logging.warning(f"快照URL返回状态码 {response.status_code}: {snapshot_url}")
                return snapshot_url  # 仍然返回URL，让用户自己判断
        except requests.RequestException as e:
            logging.warning(f"无法验证快照URL可访问性: {e}")
            return snapshot_url  # 仍然返回URL，网络问题不应阻止返回URL
            
    except Exception as e:
        logging.error(f"生成快照URL时出错: {e}")
        return None