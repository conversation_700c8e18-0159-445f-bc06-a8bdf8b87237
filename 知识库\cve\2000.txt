CVE-2000-0001(PUBLISHED):RealMedia server allows remote attackers to cause a denial of service via a long ramgen request.
CVE-2000-0002(PUBLISHED):Buffer overflow in ZBServer Pro 1.50 allows remote attackers to execute commands via a long GET request.
CVE-2000-0003(PUBLISHED):Buffer overflow in UnixWare rtpm program allows local users to gain privileges via a long environmental variable.
CVE-2000-0004(PUBLISHED):ZBServer Pro allows remote attackers to read source code for executable files by inserting a . (dot) into the URL.
CVE-2000-0005(PUBLISHED):HP-UX aserver program allows local users to gain privileges via a symlink attack.
CVE-2000-0006(PUBLISHED):strace allows local users to read arbitrary files via memory mapped file names.
CVE-2000-0007(PUBLISHED):Trend Micro PC-Cillin does not restrict access to its internal proxy port, allowing remote attackers to conduct a denial of service.
CVE-2000-0008(PUBLISHED):FTPPro allows local users to read sensitive information, which is stored in plain text.
CVE-2000-0009(PUBLISHED):The bna_pass program in Optivity NETarchitect uses the PATH environmental variable for finding the "rm" program, which allows local users to execute arbitrary commands.
CVE-2000-0010(PUBLISHED):WebWho+ whois.cgi program allows remote attackers to execute commands via shell metacharacters in the TLD parameter.
CVE-2000-0011(PUBLISHED):Buffer overflow in AnalogX SimpleServer:WWW HTTP server allows remote attackers to execute commands via a long GET request.
CVE-2000-0012(PUBLISHED):Buffer overflow in w3-msql CGI program in miniSQL package allows remote attackers to execute commands.
CVE-2000-0013(PUBLISHED):IRIX soundplayer program allows local users to gain privileges by including shell metacharacters in a .wav file, which is executed via the midikeys program.
CVE-2000-0014(PUBLISHED):Denial of service in Savant web server via a null character in the requested URL.
CVE-2000-0015(PUBLISHED):CascadeView TFTP server allows local users to gain privileges via a symlink attack.
CVE-2000-0016(PUBLISHED):Buffer overflow in Internet Anywhere POP3 Mail Server allows remote attackers to cause a denial of service or execute commands via a long username.
CVE-2000-0017(PUBLISHED):Buffer overflow in Linux linuxconf package allows remote attackers to gain root privileges via a long parameter.
CVE-2000-0018(PUBLISHED):wmmon in FreeBSD allows local users to gain privileges via the .wmmonrc configuration file.
CVE-2000-0019(PUBLISHED):IMail POP3 daemon uses weak encryption, which allows local users to read files.
CVE-2000-0020(PUBLISHED):DNS PRO allows remote attackers to conduct a denial of service via a large number of connections.
CVE-2000-0021(PUBLISHED):Lotus Domino HTTP server allows remote attackers to determine the real path of the server via a request to a non-existent script in /cgi-bin.
CVE-2000-0022(PUBLISHED):Lotus Domino HTTP server does not properly disable anonymous access for the cgi-bin directory.
CVE-2000-0023(PUBLISHED):Buffer overflow in Lotus Domino HTTP server allows remote attackers to cause a denial of service via a long URL.
CVE-2000-0024(PUBLISHED):IIS does not properly canonicalize URLs, potentially allowing remote attackers to bypass access restrictions in third-party software via escape characters, aka the "Escape Character Parsing" vulnerability.
CVE-2000-0025(PUBLISHED):IIS 4.0 and Site Server 3.0 allow remote attackers to read source code for ASP files if the file is in a virtual directory whose name includes extensions such as .com, .exe, .sh, .cgi, or .dll, aka the "Virtual Directory Naming" vulnerability.
CVE-2000-0026(PUBLISHED):Buffer overflow in UnixWare i2odialogd daemon allows remote attackers to gain root access via a long username/password authorization string.
CVE-2000-0027(PUBLISHED):IBM Network Station Manager NetStation allows local users to gain privileges via a symlink attack.
CVE-2000-0028(PUBLISHED):Internet Explorer 5.0 and 5.01 allows remote attackers to bypass the cross frame security policy and read files via the external.NavigateAndFind function.
CVE-2000-0029(PUBLISHED):UnixWare pis and mkpis commands allow local users to gain privileges via a symlink attack.
CVE-2000-0030(PUBLISHED):Solaris dmispd dmi_cmd allows local users to fill up restricted disk space by adding files to the /var/dmi/db database.
CVE-2000-0031(PUBLISHED):The initscripts package in Red Hat Linux allows local users to gain privileges via a symlink attack.
CVE-2000-0032(PUBLISHED):Solaris dmi_cmd allows local users to crash the dmispd daemon by adding a malformed file to the /var/dmi/db database.
CVE-2000-0033(PUBLISHED):InterScan VirusWall SMTP scanner does not properly scan messages with malformed attachments.
CVE-2000-0034(PUBLISHED):Netscape 4.7 records user passwords in the preferences.js file during an IMAP or POP session, even if the user has not enabled "remember passwords."
CVE-2000-0035(PUBLISHED):resend command in Majordomo allows local users to gain privileges via shell metacharacters.
CVE-2000-0036(PUBLISHED):Outlook Express 5 for Macintosh downloads attachments to HTML mail without prompting the user, aka the "HTML Mail Attachment" vulnerability.
CVE-2000-0037(PUBLISHED):Majordomo wrapper allows local users to gain privileges by specifying an alternate configuration file.
CVE-2000-0038(PUBLISHED):glFtpD includes a default glftpd user account with a default password and a UID of 0.
CVE-2000-0039(PUBLISHED):AltaVista search engine allows remote attackers to read files above the document root via a .. (dot dot) in the query.cgi CGI program.
CVE-2000-0040(PUBLISHED):glFtpD allows local users to gain privileges via metacharacters in the SITE ZIPCHK command.
CVE-2000-0041(PUBLISHED):Macintosh systems generate large ICMP datagrams in response to malformed datagrams, allowing them to be used as amplifiers in a flood attack.
CVE-2000-0042(PUBLISHED):Buffer overflow in CSM mail server allows remote attackers to cause a denial of service or execute commands via a long HELO command.
CVE-2000-0043(PUBLISHED):Buffer overflow in CamShot WebCam HTTP server allows remote attackers to execute commands via a long GET request.
CVE-2000-0044(PUBLISHED):Macros in War FTP 1.70 and 1.67b2 allow local or remote attackers to read arbitrary files or execute commands.
CVE-2000-0045(PUBLISHED):MySQL allows local users to modify passwords for arbitrary MySQL users via the GRANT privilege.
CVE-2000-0046(PUBLISHED):Buffer overflow in ICQ 99b ******* client allows remote attackers to execute commands via a malformed URL within an ICQ message.
CVE-2000-0047(PUBLISHED):Buffer overflow in Yahoo Pager/Messenger client allows remote attackers to cause a denial of service via a long URL within a message.
CVE-2000-0048(PUBLISHED):get_it program in Corel Linux Update allows local users to gain root access by specifying an alternate PATH for the cp program.
CVE-2000-0049(PUBLISHED):Buffer overflow in Winamp client allows remote attackers to execute commands via a long entry in a .pls file.
CVE-2000-0050(PUBLISHED):The Allaire Spectra Webtop allows authenticated users to access other Webtop sections by specifying explicit URLs.
CVE-2000-0051(PUBLISHED):The Allaire Spectra Configuration Wizard allows remote attackers to cause a denial of service by repeatedly resubmitting data collections for indexing via a URL.
CVE-2000-0052(PUBLISHED):Red Hat userhelper program in the usermode package allows local users to gain root access via PAM and a .. (dot dot) attack.
CVE-2000-0053(PUBLISHED):Microsoft Commercial Internet System (MCIS) IMAP server allows remote attackers to cause a denial of service via a malformed IMAP request.
CVE-2000-0054(PUBLISHED):search.cgi in the SolutionScripts Home Free package allows remote attackers to view directories via a .. (dot dot) attack.
CVE-2000-0055(PUBLISHED):Buffer overflow in Solaris chkperm command allows local users to gain root access via a long -n option.
CVE-2000-0056(PUBLISHED):IMail IMONITOR status.cgi CGI script allows remote attackers to cause a denial of service with many calls to status.cgi.
CVE-2000-0057(PUBLISHED):Cold Fusion CFCACHE tag places temporary cache files within the web document root, allowing remote attackers to obtain sensitive system information.
CVE-2000-0058(PUBLISHED):Network HotSync program in Handspring Visor does not have authentication, which allows remote attackers to retrieve email and files.
CVE-2000-0059(PUBLISHED):PHP3 with safe_mode enabled does not properly filter shell metacharacters from commands that are executed by popen, which could allow remote attackers to execute commands.
CVE-2000-0060(PUBLISHED):Buffer overflow in aVirt Rover POP3 server 1.1 allows remote attackers to cause a denial of service via a long user name.
CVE-2000-0061(PUBLISHED):Internet Explorer 5 does not modify the security zone for a document that is being loaded into a window until after the document has been loaded, which could allow remote attackers to execute Javascript in a different security context while the document is loading.
CVE-2000-0062(PUBLISHED):The DTML implementation in the Z Object Publishing Environment (Zope) allows remote attackers to conduct unauthorized activities.
CVE-2000-0063(PUBLISHED):cgiproc CGI script in Nortel Contivity HTTP server allows remote attackers to read arbitrary files by specifying the filename in a parameter to the script.
CVE-2000-0064(PUBLISHED):cgiproc CGI script in Nortel Contivity HTTP server allows remote attackers to cause a denial of service via a malformed URL that includes shell metacharacters.
CVE-2000-0065(PUBLISHED):Buffer overflow in InetServ 3.0 allows remote attackers to execute commands via a long GET request.
CVE-2000-0066(PUBLISHED):WebSite Pro allows remote attackers to determine the real pathname of webdirectories via a malformed URL request.
CVE-2000-0067(PUBLISHED):CyberCash Merchant Connection Kit (MCK) allows local users to modify files via a symlink attack.
CVE-2000-0068(PUBLISHED):daynad program in Intel InBusiness E-mail Station does not require authentication, which allows remote attackers to modify its configuration, delete files, or read mail.
CVE-2000-0069(PUBLISHED):The recover program in Solstice Backup allows local users to restore sensitive files.
CVE-2000-0070(PUBLISHED):NtImpersonateClientOfPort local procedure call in Windows NT 4.0 allows local users to gain privileges, aka "Spoofed LPC Port Request."
CVE-2000-0071(PUBLISHED):IIS 4.0 allows a remote attacker to obtain the real pathname of the document root by requesting non-existent files with .ida or .idq extensions.
CVE-2000-0072(PUBLISHED):Visual Casel (Vcasel) does not properly prevent users from executing files, which allows local users to use a relative pathname to specify an alternate file which has an approved name and possibly gain privileges.
CVE-2000-0073(PUBLISHED):Buffer overflow in Microsoft Rich Text Format (RTF) reader allows attackers to cause a denial of service via a malformed control word.
CVE-2000-0074(PUBLISHED):PowerScripts PlusMail CGI program allows remote attackers to execute commands via a password file with improper permissions.
CVE-2000-0075(PUBLISHED):Super Mail Transfer Package (SMTP), later called MsgCore, has a memory leak which allows remote attackers to cause a denial of service by repeating multiple HELO, MAIL FROM, RCPT TO, and DATA commands in the same session.
CVE-2000-0076(PUBLISHED):nviboot boot script in the Debian nvi package allows local users to delete files via malformed entries in vi.recover.
CVE-2000-0077(PUBLISHED):The October 1998 version of the HP-UX aserver program allows local users to gain privileges by specifying an alternate PATH which aserver uses to find the ps and grep commands.
CVE-2000-0078(PUBLISHED):The June 1999 version of the HP-UX aserver program allows local users to gain privileges by specifying an alternate PATH which aserver uses to find the awk command.
CVE-2000-0079(PUBLISHED):The W3C CERN httpd HTTP server allows remote attackers to determine the real pathnames of some commands via a request for a nonexistent URL.
CVE-2000-0080(PUBLISHED):AIX techlibss allows local users to overwrite files via a symlink attack.
CVE-2000-0081(PUBLISHED):Hotmail does not properly filter JavaScript code from a user's mailbox, which allows a remote attacker to execute the code by using hexadecimal codes to specify the javascript: protocol, e.g. j&#x41;vascript.
CVE-2000-0082(PUBLISHED):WebTV email client allows remote attackers to force the client to send email without the user's knowledge via HTML.
CVE-2000-0083(PUBLISHED):HP asecure creates the Audio Security File audio.sec with insecure permissions, which allows local users to cause a denial of service or gain additional privileges.
CVE-2000-0084(PUBLISHED):CuteFTP uses weak encryption to store password information in its tree.dat file.
CVE-2000-0085(PUBLISHED):Hotmail does not properly filter JavaScript code from a user's mailbox, which allows a remote attacker to execute code via the LOWSRC or DYNRC parameters in the IMG tag.
CVE-2000-0086(PUBLISHED):Netopia Timbuktu Pro sends user IDs and passwords in cleartext, which allows remote attackers to obtain them via sniffing.
CVE-2000-0087(PUBLISHED):Netscape Mail Notification (nsnotify) utility in Netscape Communicator uses IMAP without SSL, even if the user has set a preference for Communicator to use an SSL connection, allowing a remote attacker to sniff usernames and passwords in plaintext.
CVE-2000-0088(PUBLISHED):Buffer overflow in the conversion utilities for Japanese, Korean and Chinese Word 5 documents allows an attacker to execute commands, aka the "Malformed Conversion Data" vulnerability.
CVE-2000-0089(PUBLISHED):The rdisk utility in Microsoft Terminal Server Edition and Windows NT 4.0 stores registry hive information in a temporary file with permissions that allow local users to read it, aka the "RDISK Registry Enumeration File" vulnerability.
CVE-2000-0090(PUBLISHED):VMWare 1.1.2 allows local users to cause a denial of service via a symlink attack.
CVE-2000-0091(PUBLISHED):Buffer overflow in vchkpw/vpopmail POP authentication package allows remote attackers to gain root privileges via a long username or password.
CVE-2000-0092(PUBLISHED):The BSD make program allows local users to modify files via a symlink attack when the -j option is being used.
CVE-2000-0093(PUBLISHED):An installation of Red Hat uses DES password encryption with crypt() for the initial password, instead of md5.
CVE-2000-0094(PUBLISHED):procfs in BSD systems allows local users to gain root privileges by modifying the /proc/pid/mem interface via a modified file descriptor for stderr.
CVE-2000-0095(PUBLISHED):The PMTU discovery procedure used by HP-UX 10.30 and 11.00 for determining the optimum MTU generates large amounts of traffic in response to small packets, allowing remote attackers to cause the system to be used as a packet amplifier.
CVE-2000-0096(PUBLISHED):Buffer overflow in qpopper 3.0 beta versions allows local users to gain privileges via a long LIST command.
CVE-2000-0097(PUBLISHED):The WebHits ISAPI filter in Microsoft Index Server allows remote attackers to read arbitrary files, aka the "Malformed Hit-Highlighting Argument" vulnerability.
CVE-2000-0098(PUBLISHED):Microsoft Index Server allows remote attackers to determine the real path for a web directory via a request to an Internet Data Query file that does not exist.
CVE-2000-0099(PUBLISHED):Buffer overflow in UnixWare ppptalk command allows local users to gain privileges via a long prompt argument.
CVE-2000-0100(PUBLISHED):The SMS Remote Control program is installed with insecure permissions, which allows local users to gain privileges by modifying or replacing the program.
CVE-2000-0101(PUBLISHED):The Make-a-Store OrderPage shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0102(PUBLISHED):The SalesCart shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0103(PUBLISHED):The SmartCart shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0104(PUBLISHED):The Shoptron shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0105(PUBLISHED):Outlook Express 5.01 and Internet Explorer 5.01 allow remote attackers to view a user's email messages via a script that accesses a variable that references subsequent email messages that are read by the client.
CVE-2000-0106(PUBLISHED):The EasyCart shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0107(PUBLISHED):Linux apcd program allows local attackers to modify arbitrary files via a symlink attack.
CVE-2000-0108(PUBLISHED):The Intellivend shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0109(PUBLISHED):The mcsp Client Site Processor system (MultiCSP) in Standard and Poor's ComStock is installed with several accounts that have no passwords or easily guessable default passwords.
CVE-2000-0110(PUBLISHED):The WebSiteTool shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0111(PUBLISHED):The RightFax web client uses predictable session numbers, which allows remote attackers to hijack user sessions.
CVE-2000-0112(PUBLISHED):The default installation of Debian GNU/Linux uses an insecure Master Boot Record (MBR) which allows a local user to boot from a floppy disk during the installation.
CVE-2000-0113(PUBLISHED):The SyGate Remote Management program does not properly restrict access to its administration service, which allows remote attackers to cause a denial of service, or access network traffic statistics.
CVE-2000-0114(PUBLISHED):Frontpage Server Extensions allows remote attackers to determine the name of the anonymous account via an RPC POST request to shtml.dll in the /_vti_bin/ virtual directory.
CVE-2000-0115(PUBLISHED):IIS allows local users to cause a denial of service via invalid regular expressions in a Visual Basic script in an ASP page.
CVE-2000-0116(PUBLISHED):Firewall-1 does not properly filter script tags, which allows remote attackers to bypass the "Strip Script Tags" restriction by including an extra < in front of the SCRIPT tag.
CVE-2000-0117(PUBLISHED):The siteUserMod.cgi program in Cobalt RaQ2 servers allows any Site Administrator to modify passwords for other users, site administrators, and possibly admin (root).
CVE-2000-0118(PUBLISHED):The Red Hat Linux su program does not log failed password guesses if the su process is killed before it times out, which allows local attackers to conduct brute force password guessing.
CVE-2000-0119(PUBLISHED):The default configurations for McAfee Virus Scan and Norton Anti-Virus virus checkers do not check files in the RECYCLED folder that is used by the Windows Recycle Bin utility, which allows attackers to store malicious code without detection.
CVE-2000-0120(PUBLISHED):The Remote Access Service invoke.cfm template in Allaire Spectra 1.0 allows users to bypass authentication via the bAuthenticated parameter.
CVE-2000-0121(PUBLISHED):The Recycle Bin utility in Windows NT and Windows 2000 allows local users to read or modify files by creating a subdirectory with the victim's SID in the recycler directory, aka the "Recycle Bin Creation" vulnerability.
CVE-2000-0122(PUBLISHED):Frontpage Server Extensions allows remote attackers to determine the physical path of a virtual directory via a GET request to the htimage.exe CGI program.
CVE-2000-0123(PUBLISHED):The shopping cart application provided with Filemaker allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0124(PUBLISHED):surfCONTROL SuperScout does not properly asign a category to web sites with a . (dot) at the end, which may allow users to bypass web access restrictions.
CVE-2000-0125(PUBLISHED):wwwthreads does not properly cleanse numeric data or table names that are passed to SQL queries, which allows remote attackers to gain privileges for wwwthreads forums.
CVE-2000-0126(PUBLISHED):Sample Internet Data Query (IDQ) scripts in IIS 3 and 4 allow remote attackers to read files via a .. (dot dot) attack.
CVE-2000-0127(PUBLISHED):The Webspeed configuration program does not properly disable access to the WSMadmin utility, which allows remote attackers to gain privileges via wsisa.dll.
CVE-2000-0128(PUBLISHED):The Finger Server 0.82 allows remote attackers to execute commands via shell metacharacters.
CVE-2000-0129(PUBLISHED):Buffer overflow in the SHGetPathFromIDList function of the Serv-U FTP server allows attackers to cause a denial of service by performing a LIST command on a malformed .lnk file.
CVE-2000-0130(PUBLISHED):Buffer overflow in SCO scohelp program allows remote attackers to execute commands.
CVE-2000-0131(PUBLISHED):Buffer overflow in War FTPd 1.6x allows users to cause a denial of service via long MKD and CWD commands.
CVE-2000-0132(PUBLISHED):Microsoft Java Virtual Machine allows remote attackers to read files via the getSystemResourceAsStream function.
CVE-2000-0133(PUBLISHED):Buffer overflows in Tiny FTPd 0.52 beta3 FTP server allows users to execute commands via the STOR, RNTO, MKD, XMKD, RMD, XRMD, APPE, SIZE, and RNFR commands.
CVE-2000-0134(PUBLISHED):The Check It Out shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0135(PUBLISHED):The @Retail shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0136(PUBLISHED):The Cart32 shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0137(PUBLISHED):The CartIt shopping cart application allows remote users to modify sensitive purchase information via hidden form fields.
CVE-2000-0138(PUBLISHED):A system has a distributed denial of service (DDOS) attack master, agent, or zombie installed, such as (1) Trinoo, (2) Tribe Flood Network (TFN), (3) Tribe Flood Network 2000 (TFN2K), (4) stacheldraht, (5) mstream, or (6) shaft.
CVE-2000-0139(PUBLISHED):Internet Anywhere POP3 Mail Server allows local users to cause a denial of service via a malformed RETR command.
CVE-2000-0140(PUBLISHED):Internet Anywhere POP3 Mail Server allows remote attackers to cause a denial of service via a large number of connections.
CVE-2000-0141(PUBLISHED):Infopop Ultimate Bulletin Board (UBB) allows remote attackers to execute commands via shell metacharacters in the topic hidden field.
CVE-2000-0142(PUBLISHED):The authentication protocol in Timbuktu Pro 2.0b650 allows remote attackers to cause a denial of service via connections to port 407 and 1417.
CVE-2000-0143(PUBLISHED):The SSH protocol server sshd allows local users without shell access to redirect a TCP connection through a service that uses the standard system password database for authentication, such as POP or FTP.
CVE-2000-0144(PUBLISHED):Axis 700 Network Scanner does not properly restrict access to administrator URLs, which allows users to bypass the password protection via a .. (dot dot) attack.
CVE-2000-0145(PUBLISHED):The libguile.so library file used by gnucash in Debian GNU/Linux is installed with world-writable permissions.
CVE-2000-0146(PUBLISHED):The Java Server in the Novell GroupWise Web Access Enhancement Pack allows remote attackers to cause a denial of service via a long URL to the servlet.
CVE-2000-0147(PUBLISHED):snmpd in SCO OpenServer has an SNMP community string that is writable by default, which allows local attackers to modify the host's configuration.
CVE-2000-0148(PUBLISHED):MySQL 3.22 allows remote attackers to bypass password authentication and access a database via a short check string.
CVE-2000-0149(PUBLISHED):Zeus web server allows remote attackers to view the source code for CGI programs via a null character (%00) at the end of a URL.
CVE-2000-0150(PUBLISHED):Check Point Firewall-1 allows remote attackers to bypass port access restrictions on an FTP server by forcing it to send malicious packets that Firewall-1 misinterprets as a valid 227 response to a client's PASV attempt.
CVE-2000-0151(PUBLISHED):GNU make follows symlinks when it reads a Makefile from stdin, which allows other local users to execute commands.
CVE-2000-0152(PUBLISHED):Remote attackers can cause a denial of service in Novell BorderManager 3.5 by pressing the enter key in a telnet connection to port 2000.
CVE-2000-0153(PUBLISHED):FrontPage Personal Web Server (PWS) allows remote attackers to read files via a .... (dot dot) attack.
CVE-2000-0154(PUBLISHED):The ARCserve agent in UnixWare allows local attackers to modify arbitrary files via a symlink attack.
CVE-2000-0155(PUBLISHED):Windows NT Autorun executes the autorun.inf file on non-removable media, which allows local attackers to specify an alternate program to execute when other users access a drive.
CVE-2000-0156(PUBLISHED):Internet Explorer 4.x and 5.x allows remote web servers to access files on the client that are outside of its security domain, aka the "Image Source Redirect" vulnerability.
CVE-2000-0157(PUBLISHED):NetBSD ptrace call on VAX allows local users to gain privileges by modifying the PSL contents in the debugging process.
CVE-2000-0158(PUBLISHED):Buffer overflow in MMDF server allows remote attackers to gain privileges via a long MAIL FROM command to the SMTP daemon.
CVE-2000-0159(PUBLISHED):HP Ignite-UX does not save /etc/passwd when it creates an image of a trusted system, which can set the password field to a blank and allow an attacker to gain privileges.
CVE-2000-0160(PUBLISHED):The Microsoft Active Setup ActiveX component in Internet Explorer 4.x and 5.x allows a remote attacker to install software components without prompting the user by stating that the software's manufacturer is Microsoft.
CVE-2000-0161(PUBLISHED):Sample web sites on Microsoft Site Server 3.0 Commerce Edition do not validate an identification number, which allows remote attackers to execute SQL commands.
CVE-2000-0162(PUBLISHED):The Microsoft virtual machine (VM) in Internet Explorer 4.x and 5.x allows a remote attacker to read files via a malicious Java applet that escapes the Java sandbox, aka the "VM File Reading" vulnerability.
CVE-2000-0163(PUBLISHED):asmon and ascpu in FreeBSD allow local users to gain root privileges via a configuration file.
CVE-2000-0164(PUBLISHED):The installation of Sun Internet Mail Server (SIMS) creates a world-readable file that allows local users to obtain passwords.
CVE-2000-0165(PUBLISHED):The Delegate application proxy has several buffer overflows which allow a remote attacker to execute commands.
CVE-2000-0166(PUBLISHED):Buffer overflow in the InterAccess telnet server TelnetD allows remote attackers to execute commands via a long login name.
CVE-2000-0167(PUBLISHED):IIS Inetinfo.exe allows local users to cause a denial of service by creating a mail file with a long name and a .txt.eml extension in the pickup directory.
CVE-2000-0168(PUBLISHED):Microsoft Windows 9x operating systems allow an attacker to cause a denial of service via a pathname that includes file device names, aka the "DOS Device in Path Name" vulnerability.
CVE-2000-0169(PUBLISHED):Batch files in the Oracle web listener ows-bin directory allow remote attackers to execute commands via a malformed URL that includes '?&'.
CVE-2000-0170(PUBLISHED):Buffer overflow in the man program in Linux allows local users to gain privileges via the MANPAGER environmental variable.
CVE-2000-0171(PUBLISHED):atsadc in the atsar package for Linux does not properly check the permissions of an output file, which allows local users to gain root privileges.
CVE-2000-0172(PUBLISHED):The mtr program only uses a seteuid call when attempting to drop privileges, which could allow local users to gain root privileges.
CVE-2000-0173(PUBLISHED):Vulnerability in the EELS system in SCO UnixWare 7.1.x allows remote attackers to cause a denial of service.
CVE-2000-0174(PUBLISHED):StarOffice StarScheduler web server allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0175(PUBLISHED):Buffer overflow in StarOffice StarScheduler web server allows remote attackers to gain root access via a long GET command.
CVE-2000-0176(PUBLISHED):The default configuration of Serv-U 2.5d and earlier allows remote attackers to determine the real pathname of the server by requesting a URL for a directory or file that does not exist.
CVE-2000-0177(PUBLISHED):DNSTools CGI applications allow remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2000-0178(PUBLISHED):ServerIron switches by Foundry Networks have predictable TCP/IP sequence numbers, which allows remote attackers to spoof or hijack sessions.
CVE-2000-0179(PUBLISHED):HP OpenView OmniBack 2.55 allows remote attackers to cause a denial of service via a large number of connections to port 5555.
CVE-2000-0180(PUBLISHED):Sojourn search engine allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0181(PUBLISHED):Firewall-1 3.0 and 4.0 leaks packets with private IP address information, which could allow remote attackers to determine the real IP address of the host that is making the connection.
CVE-2000-0182(PUBLISHED):iPlanet Web Server 4.1 allows remote attackers to cause a denial of service via a large number of GET commands, which consumes memory and causes a kernel panic.
CVE-2000-0183(PUBLISHED):Buffer overflow in ircII 4.4 IRC client allows remote attackers to execute commands via the DCC chat capability.
CVE-2000-0184(PUBLISHED):Linux printtool sets the permissions of printer configuration files to be world-readable, which allows local attackers to obtain printer share passwords.
CVE-2000-0185(PUBLISHED):RealMedia RealServer reveals the real IP address of a Real Server, even if the address is supposed to be private.
CVE-2000-0186(PUBLISHED):Buffer overflow in the dump utility in the Linux ext2fs backup package allows local users to gain privileges via a long command line argument.
CVE-2000-0187(PUBLISHED):EZShopper 3.0 loadpage.cgi CGI script allows remote attackers to read arbitrary files via a .. (dot dot) attack or execute commands via shell metacharacters.
CVE-2000-0188(PUBLISHED):EZShopper 3.0 search.cgi CGI script allows remote attackers to read arbitrary files via a .. (dot dot) attack or execute commands via shell metacharacters.
CVE-2000-0189(PUBLISHED):ColdFusion Server 4.x allows remote attackers to determine the real pathname of the server via an HTTP request to the application.cfm or onrequestend.cfm files.
CVE-2000-0190(PUBLISHED):AOL Instant Messenger (AIM) client allows remote attackers to cause a denial of service via a message with a malformed ASCII value.
CVE-2000-0191(PUBLISHED):Axis StorPoint CD allows remote attackers to access administrator URLs without authentication via a .. (dot dot) attack.
CVE-2000-0192(PUBLISHED):The default installation of Caldera OpenLinux 2.3 includes the CGI program rpm_query, which allows remote attackers to determine what packages are installed on the system.
CVE-2000-0193(PUBLISHED):The default configuration of Dosemu in Corel Linux 1.0 allows local users to execute the system.com program and gain privileges.
CVE-2000-0194(PUBLISHED):buildxconf in Corel Linux allows local users to modify or create arbitrary files via the -x or -f parameters.
CVE-2000-0195(PUBLISHED):setxconf in Corel Linux allows local users to gain root access via the -T parameter, which executes the user's .xserverrc file.
CVE-2000-0196(PUBLISHED):Buffer overflow in mhshow in the Linux nmh package allows remote attackers to execute commands via malformed MIME headers in an email message.
CVE-2000-0197(PUBLISHED):The Windows NT scheduler uses the drive mapping of the interactive user who is currently logged onto the system, which allows the local user to gain privileges by providing a Trojan horse batch file in place of the original batch file.
CVE-2000-0198(PUBLISHED):Buffer overflow in POP3 and IMAP servers in the MERCUR mail server suite allows remote attackers to cause a denial of service.
CVE-2000-0199(PUBLISHED):When a new SQL Server is registered in Enterprise Manager for Microsoft SQL Server 7.0 and the "Always prompt for login name and password" option is not set, then the Enterprise Manager uses weak encryption to store the login ID and password.
CVE-2000-0200(PUBLISHED):Buffer overflow in Microsoft Clip Art Gallery allows remote attackers to cause a denial of service or execute commands via a malformed CIL (clip art library) file, aka the "Clip Art Buffer Overrun" vulnerability.
CVE-2000-0201(PUBLISHED):The window.showHelp() method in Internet Explorer 5.x does not restrict HTML help files (.chm) to be executed from the local host, which allows remote attackers to execute arbitrary commands via Microsoft Networking.
CVE-2000-0202(PUBLISHED):Microsoft SQL Server 7.0 and Microsoft Data Engine (MSDE) 1.0 allow remote attackers to gain privileges via a malformed Select statement in an SQL query.
CVE-2000-0203(PUBLISHED):The Trend Micro OfficeScan client tmlisten.exe allows remote attackers to cause a denial of service via malformed data to port 12345.
CVE-2000-0204(PUBLISHED):The Trend Micro OfficeScan client allows remote attackers to cause a denial of service by making 5 connections to port 12345, which raises CPU utilization to 100%.
CVE-2000-0205(PUBLISHED):Trend Micro OfficeScan allows remote attackers to replay administrative commands and modify the configuration of OfficeScan clients.
CVE-2000-0206(PUBLISHED):The installation of Oracle 8.1.5.x on Linux follows symlinks and creates the orainstRoot.sh file with world-writeable permissions, which allows local users to gain privileges.
CVE-2000-0207(PUBLISHED):SGI InfoSearch CGI program infosrch.cgi allows remote attackers to execute commands via shell metacharacters.
CVE-2000-0208(PUBLISHED):The htdig (ht://Dig) CGI program htsearch allows remote attackers to read arbitrary files by enclosing the file name with backticks (`) in parameters to htsearch.
CVE-2000-0209(PUBLISHED):Buffer overflow in Lynx 2.x allows remote attackers to crash Lynx and possibly execute commands via a long URL in a malicious web page.
CVE-2000-0210(PUBLISHED):The lit program in Sun Flex License Manager (FlexLM) follows symlinks, which allows local users to modify arbitrary files.
CVE-2000-0211(PUBLISHED):The Windows Media server allows remote attackers to cause a denial of service via a series of client handshake packets that are sent in an improper sequence, aka the "Misordered Windows Media Services Handshake" vulnerability.
CVE-2000-0212(PUBLISHED):InterAccess TelnetD Server 4.0 allows remote attackers to conduct a denial of service via malformed terminal client configuration information.
CVE-2000-0213(PUBLISHED):The Sambar server includes batch files ECHO.BAT and HELLO.BAT in the CGI directory, which allow remote attackers to execute commands via shell metacharacters.
CVE-2000-0214(PUBLISHED):FTP Explorer uses weak encryption for storing the username, password, and profile of FTP sites.
CVE-2000-0215(PUBLISHED):Vulnerability in SCO cu program in UnixWare 7.x allows local users to gain privileges.
CVE-2000-0216(PUBLISHED):Microsoft email clients in Outlook, Exchange, and Windows Messaging automatically respond to Read Receipt and Delivery Receipt tags, which could allow an attacker to flood a mail system with responses by forging a Read Receipt request that is redirected to a large distribution list.
CVE-2000-0217(PUBLISHED):The default configuration of SSH allows X forwarding, which could allow a remote attacker to control a client's X sessions via a malicious xauth program.
CVE-2000-0218(PUBLISHED):Buffer overflow in Linux mount and umount allows local users to gain root privileges via a long relative pathname.
CVE-2000-0219(PUBLISHED):Red Hat 6.0 allows local users to gain root access by booting single user and hitting ^C at the password prompt.
CVE-2000-0220(PUBLISHED):ZoneAlarm sends sensitive system and network information in cleartext to the Zone Labs server if a user requests more information about an event.
CVE-2000-0221(PUBLISHED):The Nautica Marlin bridge allows remote attackers to cause a denial of service via a zero length UDP packet to the SNMP port.
CVE-2000-0222(PUBLISHED):The installation for Windows 2000 does not activate the Administrator password until the system has rebooted, which allows remote attackers to connect to the ADMIN$ share without a password until the reboot occurs.
CVE-2000-0223(PUBLISHED):Buffer overflow in the wmcdplay CD player program for the WindowMaker desktop allows local users to gain root privileges via a long parameter.
CVE-2000-0224(PUBLISHED):ARCserve agent in SCO UnixWare 7.x allows local attackers to gain root privileges via a symlink attack.
CVE-2000-0225(PUBLISHED):The Pocsag POC32 program does not properly prevent remote users from accessing its server port, even if the option has been disabled.
CVE-2000-0226(PUBLISHED):IIS 4.0 allows attackers to cause a denial of service by requesting a large buffer in a POST or PUT command which consumes memory, aka the "Chunked Transfer Encoding Buffer Overflow Vulnerability."
CVE-2000-0227(PUBLISHED):The Linux 2.2.x kernel does not restrict the number of Unix domain sockets as defined by the wmem_max parameter, which allows local users to cause a denial of service by requesting a large number of sockets.
CVE-2000-0228(PUBLISHED):Microsoft Windows Media License Manager allows remote attackers to cause a denial of service by sending a malformed request that causes the manager to halt, aka the "Malformed Media License Request" Vulnerability.
CVE-2000-0229(PUBLISHED):gpm-root in the gpm package does not properly drop privileges, which allows local users to gain privileges by starting a utility from gpm-root.
CVE-2000-0230(PUBLISHED):Buffer overflow in imwheel allows local users to gain root privileges via the imwheel-solo script and a long HOME environmental variable.
CVE-2000-0231(PUBLISHED):Linux kreatecd trusts a user-supplied path that is used to find the cdrecord program, allowing local users to gain root privileges.
CVE-2000-0232(PUBLISHED):Microsoft TCP/IP Printing Services, aka Print Services for Unix, allows an attacker to cause a denial of service via a malformed TCP/IP print request.
CVE-2000-0233(PUBLISHED):SuSE Linux IMAP server allows remote attackers to bypass IMAP authentication and gain privileges.
CVE-2000-0234(PUBLISHED):The default configuration of Cobalt RaQ2 and RaQ3 as specified in access.conf allows remote attackers to view sensitive contents of a .htaccess file.
CVE-2000-0235(PUBLISHED):Buffer overflow in the huh program in the orville-write package allows local users to gain root privileges.
CVE-2000-0236(PUBLISHED):Netscape Enterprise Server with Directory Indexing enabled allows remote attackers to list server directories via web publishing tags such as ?wp-ver-info and ?wp-cs-dump.
CVE-2000-0237(PUBLISHED):Netscape Enterprise Server with Web Publishing enabled allows remote attackers to list arbitrary directories via a GET request for the /publisher directory, which provides a Java applet that allows the attacker to browse the directories.
CVE-2000-0238(PUBLISHED):Buffer overflow in the web server for Norton AntiVirus for Internet Email Gateways allows remote attackers to cause a denial of service via a long URL.
CVE-2000-0239(PUBLISHED):Buffer overflow in the MERCUR WebView WebMail server allows remote attackers to cause a denial of service via a long mail_user parameter in the GET request.
CVE-2000-0240(PUBLISHED):vqSoft vqServer program allows remote attackers to read arbitrary files via a /........../ in the URL, a variation of a .. (dot dot) attack.
CVE-2000-0241(PUBLISHED):vqSoft vqServer stores sensitive information such as passwords in cleartext in the server.cfg file, which allows attackers to gain privileges.
CVE-2000-0242(PUBLISHED):WindMail allows remote attackers to read arbitrary files or execute commands via shell metacharacters.
CVE-2000-0243(PUBLISHED):AnalogX SimpleServer:WWW HTTP server 1.03 allows remote attackers to cause a denial of service via a short GET request to cgi-bin.
CVE-2000-0244(PUBLISHED):The Citrix ICA (Independent Computing Architecture) protocol uses weak encryption (XOR) for user authentication.
CVE-2000-0245(PUBLISHED):Vulnerability in SGI IRIX objectserver daemon allows remote attackers to create user accounts.
CVE-2000-0246(PUBLISHED):IIS 4.0 and 5.0 does not properly perform ISAPI extension processing if a virtual directory is mapped to a UNC share, which allows remote attackers to read the source code of ASP and other files, aka the "Virtualized UNC Share" vulnerability.
CVE-2000-0247(PUBLISHED):Unknown vulnerability in Generic-NQS (GNQS) allows local users to gain root privileges.
CVE-2000-0248(PUBLISHED):The web GUI for the Linux Virtual Server (LVS) software in the Red Hat Linux Piranha package has a backdoor password that allows remote attackers to execute arbitrary commands.
CVE-2000-0249(PUBLISHED):The AIX Fast Response Cache Accelerator (FRCA) allows local users to modify arbitrary files via the configuration capability in the frcactrl program.
CVE-2000-0250(PUBLISHED):The crypt function in QNX uses weak encryption, which allows local users to decrypt passwords.
CVE-2000-0251(PUBLISHED):HP-UX 11.04 VirtualVault (VVOS) sends data to unprivileged processes via an interface that has multiple aliased IP addresses.
CVE-2000-0252(PUBLISHED):The dansie shopping cart application cart.pl allows remote attackers to execute commands via a shell metacharacters in a form variable.
CVE-2000-0253(PUBLISHED):The dansie shopping cart application cart.pl allows remote attackers to modify sensitive purchase information via hidden form fields.
CVE-2000-0254(PUBLISHED):The dansie shopping cart application cart.pl allows remote attackers to obtain the shopping cart database and configuration information via a URL that references either the env, db, or vars form variables.
CVE-2000-0255(PUBLISHED):The Nbase-Xyplex EdgeBlaster router allows remote attackers to cause a denial of service via a scan for the FormMail CGI program.
CVE-2000-0256(PUBLISHED):Buffer overflows in htimage.exe and Imagemap.exe in FrontPage 97 and 98 Server Extensions allow a user to conduct activities that are not otherwise available through the web site, aka the "Server-Side Image Map Components" vulnerability.
CVE-2000-0257(PUBLISHED):Buffer overflow in the NetWare remote web administration utility allows remote attackers to cause a denial of service or execute commands via a long URL.
CVE-2000-0258(PUBLISHED):IIS 4.0 and 5.0 allows remote attackers to cause a denial of service by sending many URLs with a large number of escaped characters, aka the "Myriad Escaped Characters" Vulnerability.
CVE-2000-0259(PUBLISHED):The default permissions for the Cryptography\Offload registry key used by the OffloadModExpo in Windows NT 4.0 allows local users to obtain compromise the cryptographic keys of other users.
CVE-2000-0260(PUBLISHED):Buffer overflow in the dvwssr.dll DLL in Microsoft Visual Interdev 1.0 allows users to cause a denial of service or execute commands, aka the "Link View Server-Side Component" vulnerability.
CVE-2000-0261(PUBLISHED):The AVM KEN! web server allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0262(PUBLISHED):The AVM KEN! ISDN Proxy server allows remote attackers to cause a denial of service via a malformed request.
CVE-2000-0263(PUBLISHED):The X font server xfs in Red Hat Linux 6.x allows an attacker to cause a denial of service via a malformed request.
CVE-2000-0264(PUBLISHED):Panda Security 3.0 with registry editing disabled allows users to edit the registry and gain privileges by directly executing a .reg file or using other methods.
CVE-2000-0265(PUBLISHED):Panda Security 3.0 allows users to uninstall the Panda software via its Add/Remove Programs applet.
CVE-2000-0266(PUBLISHED):Internet Explorer 5.01 allows remote attackers to bypass the cross frame security policy via a malicious applet that interacts with the Java JSObject to modify the DOM properties to set the IFRAME to an arbitrary Javascript URL.
CVE-2000-0267(PUBLISHED):Cisco Catalyst 5.4.x allows a user to gain access to the "enable" mode without a password.
CVE-2000-0268(PUBLISHED):Cisco IOS 11.x and 12.x allows remote attackers to cause a denial of service by sending the ENVIRON option to the Telnet daemon before it is ready to accept it, which causes the system to reboot.
CVE-2000-0269(PUBLISHED):Emacs 20 does not properly set permissions for a slave PTY device when starting a new subprocess, which allows local users to read or modify communications between Emacs and the subprocess.
CVE-2000-0270(PUBLISHED):The make-temp-name Lisp function in Emacs 20 creates temporary files with predictable names, which allows attackers to conduct a symlink attack.
CVE-2000-0271(PUBLISHED):read-passwd and other Lisp functions in Emacs 20 do not properly clear the history of recently typed keys, which allows an attacker to read unencrypted passwords.
CVE-2000-0272(PUBLISHED):RealNetworks RealServer allows remote attackers to cause a denial of service by sending malformed input to the server at port 7070.
CVE-2000-0273(PUBLISHED):PCAnywhere allows remote attackers to cause a denial of service by terminating the connection before PCAnywhere provides a login prompt.
CVE-2000-0274(PUBLISHED):The Linux trustees kernel patch allows attackers to cause a denial of service by accessing a file or directory with a long name.
CVE-2000-0275(PUBLISHED):CRYPTOCard CryptoAdmin for PalmOS uses weak encryption to store a user's PIN number, which allows an attacker with access to the .PDB file to generate valid PT-1 tokens after cracking the PIN.
CVE-2000-0276(PUBLISHED):BeOS 4.5 and 5.0 allow local users to cause a denial of service via malformed direct system calls using interrupt 37.
CVE-2000-0277(PUBLISHED):Microsoft Excel 97 and 2000 does not warn the user when executing Excel Macro Language (XLM) macros in external text files, which could allow an attacker to execute a macro virus, aka the "XLM Text Macro" vulnerability.
CVE-2000-0278(PUBLISHED):The SalesLogix Eviewer allows remote attackers to cause a denial of service by accessing the URL for the slxweb.dll administration program, which does not authenticate the user.
CVE-2000-0279(PUBLISHED):BeOS allows remote attackers to cause a denial of service via malformed packets whose length field is less than the length of the headers.
CVE-2000-0280(PUBLISHED):Buffer overflow in the RealNetworks RealPlayer client versions 6 and 7 allows remote attackers to cause a denial of service via a long Location URL.
CVE-2000-0281(PUBLISHED):Buffer overflow in the Napster client beta 5 allows remote attackers to cause a denial of service via a long message.
CVE-2000-0282(PUBLISHED):TalentSoft webpsvr daemon in the Web+ shopping cart application allows remote attackers to read arbitrary files via a .. (dot dot) attack on the webplus CGI program.
CVE-2000-0283(PUBLISHED):The default installation of IRIX Performance Copilot allows remote attackers to access sensitive system information via the pmcd daemon.
CVE-2000-0284(PUBLISHED):Buffer overflow in University of Washington imapd version 4.7 allows users with a valid account to execute commands via LIST or other commands.
CVE-2000-0285(PUBLISHED):Buffer overflow in XFree86 3.3.x allows local users to execute arbitrary commands via a long -xkbmap parameter.
CVE-2000-0286(PUBLISHED):X fontserver xfs allows local users to cause a denial of service via malformed input to the server.
CVE-2000-0287(PUBLISHED):The BizDB CGI script bizdb-search.cgi allows remote attackers to execute arbitrary commands via shell metacharacters in the dbname parameter.
CVE-2000-0288(PUBLISHED):Infonautics getdoc.cgi allows remote attackers to bypass the payment phase for accessing documents via a modified form variable.
CVE-2000-0289(PUBLISHED):IP masquerading in Linux 2.2.x allows remote attackers to route UDP packets through the internal interface by modifying the external source IP address and port number to match those of an established connection.
CVE-2000-0290(PUBLISHED):Buffer overflow in Webstar HTTP server allows remote attackers to cause a denial of service via a long GET request.
CVE-2000-0291(PUBLISHED):Buffer overflow in Star Office 5.1 allows attackers to cause a denial of service by embedding a long URL within a document.
CVE-2000-0292(PUBLISHED):The Adtran MX2800 M13 Multiplexer allows remote attackers to cause a denial of service via a ping flood to the Ethernet interface, which causes the device to crash.
CVE-2000-0293(PUBLISHED):aaa_base in SuSE Linux 6.3, and cron.daily in earlier versions, allow local users to delete arbitrary files by creating files whose names include spaces, which are then incorrectly interpreted by aaa_base when it deletes expired files from the /tmp directory.
CVE-2000-0294(PUBLISHED):Buffer overflow in healthd for FreeBSD allows local users to gain root privileges.
CVE-2000-0295(PUBLISHED):Buffer overflow in LCDproc allows remote attackers to gain root privileges via the screen_add command.
CVE-2000-0296(PUBLISHED):fcheck allows local users to gain privileges by embedding shell metacharacters into file names that are processed by fcheck.
CVE-2000-0297(PUBLISHED):Allaire Forums 2.0.5 allows remote attackers to bypass access restrictions to secure conferences via the rightAccessAllForums or rightModerateAllForums variables.
CVE-2000-0298(PUBLISHED):The unattended installation of Windows 2000 with the OEMPreinstall option sets insecure permissions for the All Users and Default Users directories.
CVE-2000-0299(PUBLISHED):Buffer overflow in WebObjects.exe in the WebObjects Developer 4.5 package allows remote attackers to cause a denial of service via an HTTP request with long headers such as Accept.
CVE-2000-0300(PUBLISHED):The default encryption method of PcAnywhere 9.x uses weak encryption, which allows remote attackers to sniff and decrypt PcAnywhere or NT domain accounts.
CVE-2000-0301(PUBLISHED):Ipswitch IMAIL server 6.02 and earlier allows remote attackers to cause a denial of service via the AUTH CRAM-MD5 command.
CVE-2000-0302(PUBLISHED):Microsoft Index Server allows remote attackers to view the source code of ASP files by appending a %20 to the filename in the CiWebHitsFile argument to the null.htw URL.
CVE-2000-0303(PUBLISHED):Quake3 Arena allows malicious server operators to read or modify files on a client via a dot dot (..) attack.
CVE-2000-0304(PUBLISHED):Microsoft IIS 4.0 and 5.0 with the IISADMPWD virtual directory installed allows a remote attacker to cause a denial of service via a malformed request to the inetinfo.exe program, aka the "Undelimited .HTR Request" vulnerability.
CVE-2000-0305(PUBLISHED):Windows 95, Windows 98, Windows 2000, Windows NT 4.0, and Terminal Server systems allow a remote attacker to cause a denial of service by sending a large number of identical fragmented IP packets, aka jolt2 or the "IP Fragment Reassembly" vulnerability.
CVE-2000-0306(PUBLISHED):Buffer overflow in calserver in SCO OpenServer allows remote attackers to gain root access via a long message.
CVE-2000-0307(PUBLISHED):Vulnerability in xserver in SCO UnixWare 2.1.x and OpenServer 5.05 and earlier allows an attacker to cause a denial of service which prevents access to reserved port numbers below 1024.
CVE-2000-0308(PUBLISHED):Insecure file permissions for Netscape FastTrack Server 2.x, Enterprise Server 2.0, and Proxy Server 2.5 in SCO UnixWare 7.0.x and 2.1.3 allow an attacker to gain root privileges.
CVE-2000-0309(PUBLISHED):The i386 trace-trap handling in OpenBSD 2.4 with DDB enabled allows a local user to cause a denial of service.
CVE-2000-0310(PUBLISHED):IP fragment assembly in OpenBSD 2.4 allows a remote attacker to cause a denial of service by sending a large number of fragmented packets.
CVE-2000-0311(PUBLISHED):The Windows 2000 domain controller allows a malicious user to modify Active Directory information by modifying an unprotected attribute, aka the "Mixed Object Access" vulnerability.
CVE-2000-0312(PUBLISHED):cron in OpenBSD 2.5 allows local users to gain root privileges via an argv[] that is not NULL terminated, which is passed to cron's fake popen function.
CVE-2000-0313(PUBLISHED):Vulnerability in OpenBSD 2.6 allows a local user to change interface media configurations.
CVE-2000-0314(PUBLISHED):traceroute in NetBSD 1.3.3 and Linux systems allows local users to flood other systems by providing traceroute with a large waittime (-w) option, which is not parsed properly and sets the time delay for sending packets to zero.
CVE-2000-0315(PUBLISHED):traceroute in NetBSD 1.3.3 and Linux systems allows local unprivileged users to modify the source address of the packets, which could be used in spoofing attacks.
CVE-2000-0316(PUBLISHED):Buffer overflow in Solaris 7 lp allows local users to gain root privileges via a long -d option.
CVE-2000-0317(PUBLISHED):Buffer overflow in Solaris 7 lpset allows local users to gain root privileges via a long -r option.
CVE-2000-0318(PUBLISHED):Atrium Mercur Mail Server 3.2 allows local attackers to read other user's email and create arbitrary files via a dot dot (..) attack.
CVE-2000-0319(PUBLISHED):mail.local in Sendmail 8.10.x does not properly identify the .\n string which identifies the end of message text, which allows a remote attacker to cause a denial of service or corrupt mailboxes via a message line that is 2047 characters long and ends in .\n.
CVE-2000-0320(PUBLISHED):Qpopper 2.53 and 3.0 does not properly identify the \n string which identifies the end of message text, which allows a remote attacker to cause a denial of service or corrupt mailboxes via a message line that is 1023 characters long and ends in \n.
CVE-2000-0321(PUBLISHED):Buffer overflow in IC Radius package allows a remote attacker to cause a denial of service via a long user name.
CVE-2000-0322(PUBLISHED):The passwd.php3 CGI script in the Red Hat Piranha Virtual Server Package allows local users to execute arbitrary commands via shell metacharacters.
CVE-2000-0323(PUBLISHED):The Microsoft Jet database engine allows an attacker to modify text files via a database query, aka the "Text I-ISAM" vulnerability.
CVE-2000-0324(PUBLISHED):pcAnywhere 8.x and 9.0 allows remote attackers to cause a denial of service via a TCP SYN scan, e.g. by nmap.
CVE-2000-0325(PUBLISHED):The Microsoft Jet database engine allows an attacker to execute commands via a database query, aka the "VBA Shell" vulnerability.
CVE-2000-0326(PUBLISHED):Meeting Maker uses weak encryption (a polyalphabetic substitution cipher) for passwords, which allows remote attackers to sniff and decrypt passwords for Meeting Maker accounts.
CVE-2000-0327(PUBLISHED):Microsoft Virtual Machine (VM) allows remote attackers to escape the Java sandbox and execute commands via an applet containing an illegal cast operation, aka the "Virtual Machine Verifier" vulnerability.
CVE-2000-0328(PUBLISHED):Windows NT 4.0 generates predictable random TCP initial sequence numbers (ISN), which allows remote attackers to perform spoofing and session hijacking.
CVE-2000-0329(PUBLISHED):A Microsoft ActiveX control allows a remote attacker to execute a malicious cabinet file via an attachment and an embedded script in an HTML mail, aka the "Active Setup Control" vulnerability.
CVE-2000-0330(PUBLISHED):The networking software in Windows 95 and Windows 98 allows remote attackers to execute commands via a long file name string, aka the "File Access URL" vulnerability.
CVE-2000-0331(PUBLISHED):Buffer overflow in Microsoft command processor (CMD.EXE) for Windows NT and Windows 2000 allows a local user to cause a denial of service via a long environment variable, aka the "Malformed Environment Variable" vulnerability.
CVE-2000-0332(PUBLISHED):UltraBoard.pl or UltraBoard.cgi CGI scripts in UltraBoard 1.6 allows remote attackers to read arbitrary files via a pathname string that includes a dot dot (..) and ends with a null byte.
CVE-2000-0333(PUBLISHED):tcpdump, Ethereal, and other sniffer packages allow remote attackers to cause a denial of service via malformed DNS packets in which a jump offset refers to itself, which causes tcpdump to enter an infinite loop while decompressing the packet.
CVE-2000-0334(PUBLISHED):The Allaire Spectra container editor preview tool does not properly enforce object security, which allows an attacker to conduct unauthorized activities via an object-method that is added to the container object with a publishing rule.
CVE-2000-0335(PUBLISHED):The resolver in glibc 2.1.3 uses predictable IDs, which allows a local attacker to spoof DNS query results.
CVE-2000-0336(PUBLISHED):Linux OpenLDAP server allows local users to modify arbitrary files via a symlink attack.
CVE-2000-0337(PUBLISHED):Buffer overflow in Xsun X server in Solaris 7 allows local users to gain root privileges via a long -dev parameter.
CVE-2000-0338(PUBLISHED):Concurrent Versions Software (CVS) uses predictable temporary file names for locking, which allows local users to cause a denial of service by creating the lock directory before it is created for use by a legitimate CVS user.
CVE-2000-0339(PUBLISHED):ZoneAlarm 2.1.10 and earlier does not filter UDP packets with a source port of 67, which allows remote attackers to bypass the firewall rules.
CVE-2000-0340(PUBLISHED):Buffer overflow in Gnomelib in SuSE Linux 6.3 allows local users to execute arbitrary commands via the DISPLAY environmental variable.
CVE-2000-0341(PUBLISHED):ATRIUM Cassandra NNTP Server 1.10 allows remote attackers to cause a denial of service via a long login name.
CVE-2000-0342(PUBLISHED):Eudora 4.x allows remote attackers to bypass the user warning for executable attachments such as .exe, .com, and .bat by using a .lnk file that refers to the attachment, aka "Stealth Attachment."
CVE-2000-0343(PUBLISHED):Buffer overflow in Sniffit 0.3.x with the -L logging option enabled allows remote attackers to execute arbitrary commands via a long MAIL FROM mail header.
CVE-2000-0344(PUBLISHED):The knfsd NFS server in Linux kernel 2.2.x allows remote attackers to cause a denial of service via a negative size value.
CVE-2000-0345(PUBLISHED):The on-line help system options in Cisco routers allows non-privileged users without "enabled" access to obtain sensitive information via the show command.
CVE-2000-0346(PUBLISHED):AppleShare IP 6.1 and later allows a remote attacker to read potentially sensitive information via an invalid range request to the web server.
CVE-2000-0347(PUBLISHED):Windows 95 and Windows 98 allow a remote attacker to cause a denial of service via a NetBIOS session request packet with a NULL source name.
CVE-2000-0348(PUBLISHED):A vulnerability in the Sendmail configuration file sendmail.cf as installed in SCO UnixWare 7.1.0 and earlier allows an attacker to gain root privileges.
CVE-2000-0349(PUBLISHED):Vulnerability in the passthru driver in SCO UnixWare 7.1.0 allows an attacker to cause a denial of service.
CVE-2000-0350(PUBLISHED):A debugging feature in NetworkICE ICEcap 2.0.23 and earlier is enabled, which allows a remote attacker to bypass the weak authentication and post unencrypted events.
CVE-2000-0351(PUBLISHED):Some packaging commands in SCO UnixWare 7.1.0 have insecure privileges, which allows local users to add or remove software packages.
CVE-2000-0352(PUBLISHED):Pine before version 4.21 does not properly filter shell metacharacters from URLs, which allows remote attackers to execute arbitrary commands via a malformed URL.
CVE-2000-0353(PUBLISHED):Pine 4.x allows a remote attacker to execute arbitrary commands via an index.html file which executes lynx and obtains a uudecoded file from a malicious web server, which is then executed by Pine.
CVE-2000-0354(PUBLISHED):mirror 2.8.x in Linux systems allows remote attackers to create files one level above the local target directory.
CVE-2000-0355(PUBLISHED):pg and pb in SuSE pbpg 1.x package allows an attacker to read arbitrary files.
CVE-2000-0356(PUBLISHED):Pluggable Authentication Modules (PAM) in Red Hat Linux 6.1 does not properly lock access to disabled NIS accounts.
CVE-2000-0357(PUBLISHED):ORBit and esound in Red Hat Linux 6.1 do not use sufficiently random numbers, which allows local users to guess the authentication keys.
CVE-2000-0358(PUBLISHED):ORBit and gnome-session in Red Hat Linux 6.1 allows remote attackers to crash a program.
CVE-2000-0359(PUBLISHED):Buffer overflow in Trivial HTTP (THTTPd) allows remote attackers to cause a denial of service or execute arbitrary commands via a long If-Modified-Since header.
CVE-2000-0360(PUBLISHED):Buffer overflow in INN 2.2.1 and earlier allows remote attackers to cause a denial of service via a maliciously formatted article.
CVE-2000-0361(PUBLISHED):The PPP wvdial.lxdialog script in wvdial 1.4 and earlier creates a .config file with world readable permissions, which allows a local attacker in the dialout group to access login and password information.
CVE-2000-0362(PUBLISHED):Buffer overflows in Linux cdwtools 093 and earlier allows local users to gain root privileges.
CVE-2000-0363(PUBLISHED):Linux cdwtools 093 and earlier allows local users to gain root privileges via the /tmp directory.
CVE-2000-0364(PUBLISHED):screen and rxvt in Red Hat Linux 6.0 do not properly set the modes of tty devices, which allows local users to write to other ttys.
CVE-2000-0365(PUBLISHED):Red Hat Linux 6.0 installs the /dev/pts file system with insecure modes, which allows local users to write to other tty devices.
CVE-2000-0366(PUBLISHED):dump in Debian GNU/Linux 2.1 does not properly restore symlinks, which allows a local user to modify the ownership of arbitrary files.
CVE-2000-0367(PUBLISHED):Vulnerability in eterm 0.8.8 in Debian GNU/Linux allows an attacker to gain root privileges.
CVE-2000-0368(PUBLISHED):Classic Cisco IOS 9.1 and later allows attackers with access to the login prompt to obtain portions of the command history of previous users, which may allow the attacker to access sensitive data.
CVE-2000-0369(PUBLISHED):The IDENT server in Caldera Linux 2.3 creates multiple threads for each IDENT request, which allows remote attackers to cause a denial of service.
CVE-2000-0370(PUBLISHED):The debug option in Caldera Linux smail allows remote attackers to execute commands via shell metacharacters in the -D option for the rmail command.
CVE-2000-0371(PUBLISHED):The libmediatool library used for the KDE mediatool allows local users to create arbitrary files via a symlink attack.
CVE-2000-0372(PUBLISHED):Vulnerability in Caldera rmt command in the dump package 0.4b4 allows a local user to gain root privileges.
CVE-2000-0373(PUBLISHED):Vulnerabilities in the KDE kvt terminal program allow local users to gain root privileges.
CVE-2000-0374(PUBLISHED):The default configuration of kdm in Caldera and Mandrake Linux, and possibly other distributions, allows XDMCP connections from any host, which allows remote attackers to obtain sensitive information or bypass additional access restrictions.
CVE-2000-0375(PUBLISHED):The kernel in FreeBSD 3.2 follows symbolic links when it creates core dump files, which allows local attackers to modify arbitrary files.
CVE-2000-0376(PUBLISHED):Buffer overflow in the HTTP proxy server for the i-drive Filo software allows remote attackers to execute arbitrary commands via a long HTTP GET request.
CVE-2000-0377(PUBLISHED):The Remote Registry server in Windows NT 4.0 allows local authenticated users to cause a denial of service via a malformed request, which causes the winlogon process to fail, aka the "Remote Registry Access Authentication" vulnerability.
CVE-2000-0378(PUBLISHED):The pam_console PAM module in Linux systems performs a chown on various devices upon a user login, but an open file descriptor for those devices can be maintained after the user logs out, which allows that user to sniff activity on these devices when subsequent users log in.
CVE-2000-0379(PUBLISHED):The Netopia R9100 router does not prevent authenticated users from modifying SNMP tables, even if the administrator has configured it to do so.
CVE-2000-0380(PUBLISHED):The IOS HTTP service in Cisco routers and switches running IOS 11.1 through 12.1 allows remote attackers to cause a denial of service by requesting a URL that contains a %% string.
CVE-2000-0381(PUBLISHED):The Gossamer Threads DBMan db.cgi CGI script allows remote attackers to view environmental variables and setup information by referencing a non-existing database in the db parameter.
CVE-2000-0382(PUBLISHED):ColdFusion ClusterCATS appends stale query string arguments to a URL during HTML redirection, which may provide sensitive information to the redirected site.
CVE-2000-0383(PUBLISHED):The file transfer component of AOL Instant Messenger (AIM) reveals the physical path of the transferred file to the remote recipient.
CVE-2000-0384(PUBLISHED):NetStructure 7110 and 7180 have undocumented accounts (servnow, root, and wizard) whose passwords are easily guessable from the NetStructure's MAC address, which could allow remote attackers to gain root access.
CVE-2000-0385(PUBLISHED):FileMaker Pro 5 Web Companion allows remote attackers to bypass Field-Level database security restrictions via the XML publishing or email capabilities.
CVE-2000-0386(PUBLISHED):FileMaker Pro 5 Web Companion allows remote attackers to send anonymous or forged email.
CVE-2000-0387(PUBLISHED):The makelev program in the golddig game from the FreeBSD ports collection allows local users to overwrite arbitrary files.
CVE-2000-0388(PUBLISHED):Buffer overflow in FreeBSD libmytinfo library allows local users to execute commands via a long TERMCAP environmental variable.
CVE-2000-0389(PUBLISHED):Buffer overflow in krb_rd_req function in Kerberos 4 and 5 allows remote attackers to gain root privileges.
CVE-2000-0390(PUBLISHED):Buffer overflow in krb425_conv_principal function in Kerberos 5 allows remote attackers to gain root privileges.
CVE-2000-0391(PUBLISHED):Buffer overflow in krshd in Kerberos 5 allows remote attackers to gain root privileges.
CVE-2000-0392(PUBLISHED):Buffer overflow in ksu in Kerberos 5 allows local users to gain root privileges.
CVE-2000-0393(PUBLISHED):The KDE kscd program does not drop privileges when executing a program specified in a user's SHELL environmental variable, which allows the user to gain privileges by specifying an alternate program to execute.
CVE-2000-0394(PUBLISHED):NetProwler 3.0 allows remote attackers to cause a denial of service by sending malformed IP packets that trigger NetProwler's Man-in-the-Middle signature.
CVE-2000-0395(PUBLISHED):Buffer overflow in CProxy 3.3 allows remote users to cause a denial of service via a long HTTP request.
CVE-2000-0396(PUBLISHED):The add.exe program in the Carello shopping cart software allows remote attackers to duplicate files on the server, which could allow the attacker to read source code for web scripts such as .ASP files.
CVE-2000-0397(PUBLISHED):The EMURL web-based email account software encodes predictable identifiers in user session URLs, which allows a remote attacker to access a user's email account.
CVE-2000-0398(PUBLISHED):Buffer overflow in wconsole.dll in Rockliffe MailSite Management Agent allows remote attackers to execute arbitrary commands via a long query_string parameter in the HTTP GET request.
CVE-2000-0399(PUBLISHED):Buffer overflow in MDaemon POP server allows remote attackers to cause a denial of service via a long user name.
CVE-2000-0400(PUBLISHED):The Microsoft Active Movie ActiveX Control in Internet Explorer 5 does not restrict which file types can be downloaded, which allows an attacker to download any type of file to a user's system by encoding it within an email message or news post.
CVE-2000-0401(PUBLISHED):Buffer overflows in redirect.exe and changepw.exe in PDGSoft shopping cart allow remote attackers to execute arbitrary commands via a long query string.
CVE-2000-0402(PUBLISHED):The Mixed Mode authentication capability in Microsoft SQL Server 7.0 stores the System Administrator (sa) account in plaintext in a log file which is readable by any user, aka the "SQL Server 7.0 Service Pack Password" vulnerability.
CVE-2000-0403(PUBLISHED):The CIFS Computer Browser service on Windows NT 4.0 allows a remote attacker to cause a denial of service by sending a large number of host announcement requests to the master browse tables, aka the "HostAnnouncement Flooding" or "HostAnnouncement Frame" vulnerability.
CVE-2000-0404(PUBLISHED):The CIFS Computer Browser service allows remote attackers to cause a denial of service by sending a ResetBrowser frame to the Master Browser, aka the "ResetBrowser Frame" vulnerability.
CVE-2000-0405(PUBLISHED):Buffer overflow in L0pht AntiSniff allows remote attackers to execute arbitrary commands via a malformed DNS response packet.
CVE-2000-0406(PUBLISHED):Netscape Communicator before version 4.73 and Navigator 4.07 do not properly validate SSL certificates, which allows remote attackers to steal information by redirecting traffic from a legitimate web server to their own malicious server, aka the "Acros-Suencksen SSL" vulnerability.
CVE-2000-0407(PUBLISHED):Buffer overflow in Solaris netpr program allows local users to execute arbitrary commands via a long -p option.
CVE-2000-0408(PUBLISHED):IIS 4.05 and 5.0 allow remote attackers to cause a denial of service via a long, complex URL that appears to contain a large number of file extensions, aka the "Malformed Extension Data in URL" vulnerability.
CVE-2000-0409(PUBLISHED):Netscape 4.73 and earlier follows symlinks when it imports a new certificate, which allows local users to overwrite files of the user importing the certificate.
CVE-2000-0410(PUBLISHED):ColdFusion Server 4.5.1 allows remote attackers to cause a denial of service by making repeated requests to a CFCACHE tagged cache file that is not stored in memory.
CVE-2000-0411(PUBLISHED):Matt Wright's FormMail CGI script allows remote attackers to obtain environmental variables via the env_report parameter.
CVE-2000-0412(PUBLISHED):The gnapster and knapster clients for Napster do not properly restrict access only to MP3 files, which allows remote attackers to read arbitrary files from the client by specifying the full pathname for the file.
CVE-2000-0413(PUBLISHED):The shtml.exe program in the FrontPage extensions package of IIS 4.0 and 5.0 allows remote attackers to determine the physical path of HTML, HTM, ASP, and SHTML files by requesting a file that does not exist, which generates an error message that reveals the path.
CVE-2000-0414(PUBLISHED):Vulnerability in shutdown command for HP-UX 11.X and 10.X allows allows local users to gain privileges via malformed input variables.
CVE-2000-0415(PUBLISHED):Buffer overflow in Outlook Express 4.x allows attackers to cause a denial of service via a mail or news message that has a .jpg or .bmp attachment with a long file name.
CVE-2000-0416(PUBLISHED):NTMail 5.x allows network users to bypass the NTMail proxy restrictions by redirecting their requests to NTMail's web configuration server.
CVE-2000-0417(PUBLISHED):The HTTP administration interface to the Cayman 3220-H DSL router allows remote attackers to cause a denial of service via a long username or password.
CVE-2000-0418(PUBLISHED):The Cayman 3220-H DSL router allows remote attackers to cause a denial of service via oversized ICMP echo (ping) requests.
CVE-2000-0419(PUBLISHED):The Office 2000 UA ActiveX Control is marked as "safe for scripting," which allows remote attackers to conduct unauthorized activities via the "Show Me" function in Office Help, aka the "Office 2000 UA Control" vulnerability.
CVE-2000-0420(PUBLISHED):The default configuration of SYSKEY in Windows 2000 stores the startup key in the registry, which could allow an attacker tor ecover it and use it to decrypt Encrypted File System (EFS) data.
CVE-2000-0421(PUBLISHED):The process_bug.cgi script in Bugzilla allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2000-0422(PUBLISHED):Buffer overflow in Netwin DMailWeb CGI program allows remote attackers to execute arbitrary commands via a long utoken parameter.
CVE-2000-0423(PUBLISHED):Buffer overflow in Netwin DNEWSWEB CGI program allows remote attackers to execute arbitrary commands via long parameters such as group, cmd, and utag.
CVE-2000-0424(PUBLISHED):The CGI counter 4.0.7 by George Burgyan allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2000-0425(PUBLISHED):Buffer overflow in the Web Archives component of L-Soft LISTSERV 1.8 allows remote attackers to execute arbitrary commands.
CVE-2000-0426(PUBLISHED):UltraBoard 1.6 and other versions allow remote attackers to cause a denial of service by referencing UltraBoard in the Session parameter, which causes UltraBoard to fork copies of itself.
CVE-2000-0427(PUBLISHED):The Aladdin Knowledge Systems eToken device allows attackers with physical access to the device to obtain sensitive information without knowing the PIN of the owner by resetting the PIN in the EEPROM.
CVE-2000-0428(PUBLISHED):Buffer overflow in the SMTP gateway for InterScan Virus Wall 3.32 and earlier allows a remote attacker to execute arbitrary commands via a long filename for a uuencoded attachment.
CVE-2000-0429(PUBLISHED):A backdoor password in Cart32 3.0 and earlier allows remote attackers to execute arbitrary commands.
CVE-2000-0430(PUBLISHED):Cart32 allows remote attackers to access sensitive debugging information by appending /expdate to the URL request.
CVE-2000-0431(PUBLISHED):Cobalt RaQ2 and RaQ3 does not properly set the access permissions and ownership for files that are uploaded via FrontPage, which allows attackers to bypass cgiwrap and modify files.
CVE-2000-0432(PUBLISHED):The calender.pl and the calendar_admin.pl calendar scripts by Matt Kruse allow remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2000-0433(PUBLISHED):The SuSE aaa_base package installs some system accounts with home directories set to /tmp, which allows local users to gain privileges to those accounts by creating standard user startup scripts such as profiles.
CVE-2000-0434(PUBLISHED):The administrative password for the Allmanage web site administration software is stored in plaintext in a file which could be accessed by remote attackers.
CVE-2000-0435(PUBLISHED):The allmanageup.pl file upload CGI script in the Allmanage Website administration software 2.6 can be called directly by remote attackers, which allows them to modify user accounts or web pages.
CVE-2000-0436(PUBLISHED):MetaProducts Offline Explorer 1.2 and earlier allows remote attackers to access arbitrary files via a .. (dot dot) attack.
CVE-2000-0437(PUBLISHED):Buffer overflow in the CyberPatrol daemon "cyberdaemon" used in gauntlet and WebShield allows remote attackers to cause a denial of service or execute arbitrary commands.
CVE-2000-0438(PUBLISHED):Buffer overflow in fdmount on Linux systems allows local users in the "floppy" group to execute arbitrary commands via a long mountpoint parameter.
CVE-2000-0439(PUBLISHED):Internet Explorer 4.0 and 5.0 allows a malicious web site to obtain client cookies from another domain by including that domain name and escaped characters in a URL, aka the "Unauthorized Cookie Access" vulnerability.
CVE-2000-0440(PUBLISHED):NetBSD 1.4.2 and earlier allows remote attackers to cause a denial of service by sending a packet with an unaligned IP timestamp option.
CVE-2000-0441(PUBLISHED):Vulnerability in AIX 3.2.x and 4.x allows local users to gain write access to files on locally or remotely mounted AIX filesystems.
CVE-2000-0442(PUBLISHED):Qpopper 2.53 and earlier allows local users to gain privileges via a formatting string in the From: header, which is processed by the euidl command.
CVE-2000-0443(PUBLISHED):The web interface server in HP Web JetAdmin 5.6 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0444(PUBLISHED):HP Web JetAdmin 6.0 allows remote attackers to cause a denial of service via a malformed URL to port 8000.
CVE-2000-0445(PUBLISHED):The pgpk command in PGP 5.x on Unix systems uses an insufficiently random data source for non-interactive key pair generation, which may produce predictable keys.
CVE-2000-0446(PUBLISHED):Buffer overflow in MDBMS database server allows remote attackers to execute arbitrary commands via a long string.
CVE-2000-0447(PUBLISHED):Buffer overflow in WebShield SMTP 4.5.44 allows remote attackers to execute arbitrary commands via a long configuration parameter to the WebShield remote management service.
CVE-2000-0448(PUBLISHED):The WebShield SMTP Management Tool version 4.5.44 does not properly restrict access to the management port when an IP address does not resolve to a hostname, which allows remote attackers to access the configuration via the GET_CONFIG command.
CVE-2000-0449(PUBLISHED):Omnis Studio 2.4 uses weak encryption (trivial encoding) for encrypting database fields.
CVE-2000-0450(PUBLISHED):Vulnerability in bbd server in Big Brother System and Network Monitor allows an attacker to execute arbitrary commands.
CVE-2000-0451(PUBLISHED):The Intel express 8100 ISDN router allows remote attackers to cause a denial of service via oversized or fragmented ICMP packets.
CVE-2000-0452(PUBLISHED):Buffer overflow in the ESMTP service of Lotus Domino Server 5.0.1 allows remote attackers to cause a denial of service via a long MAIL FROM command.
CVE-2000-0453(PUBLISHED):XFree86 3.3.x and 4.0 allows a user to cause a denial of service via a negative counter value in a malformed TCP packet that is sent to port 6000.
CVE-2000-0454(PUBLISHED):Buffer overflow in Linux cdrecord allows local users to gain privileges via the dev parameter.
CVE-2000-0455(PUBLISHED):Buffer overflow in xlockmore xlock program version 4.16 and earlier allows local users to read sensitive data from memory via a long -mode option.
CVE-2000-0456(PUBLISHED):NetBSD 1.4.2 and earlier allows local users to cause a denial of service by repeatedly running certain system calls in the kernel which do not yield the CPU, aka "cpu-hog".
CVE-2000-0457(PUBLISHED):ISM.DLL in IIS 4.0 and 5.0 allows remote attackers to read file contents by requesting the file and appending a large number of encoded spaces (%20) and terminated with a .htr extension, aka the ".HTR File Fragment Reading" or "File Fragment Reading via .HTR" vulnerability.
CVE-2000-0458(PUBLISHED):The MSWordView application in IMP creates world-readable files in the /tmp directory, which allows other local users to read potentially sensitive information.
CVE-2000-0459(PUBLISHED):IMP does not remove files properly if the MSWordView application quits, which allows local users to cause a denial of service by filling up the disk space by requesting a large number of documents and prematurely stopping the request.
CVE-2000-0460(PUBLISHED):Buffer overflow in KDE kdesud on Linux allows local uses to gain privileges via a long DISPLAY environmental variable.
CVE-2000-0461(PUBLISHED):The undocumented semconfig system call in BSD freezes the state of semaphores, which allows local users to cause a denial of service of the semaphore system by using the semconfig call.
CVE-2000-0462(PUBLISHED):ftpd in NetBSD 1.4.2 does not properly parse entries in /etc/ftpchroot and does not chroot the specified users, which allows those users to access other files outside of their home directory.
CVE-2000-0463(PUBLISHED):BeOS 5.0 allows remote attackers to cause a denial of service via fragmented TCP packets.
CVE-2000-0464(PUBLISHED):Internet Explorer 4.x and 5.x allows remote attackers to execute arbitrary commands via a buffer overflow in the ActiveX parameter parsing capability, aka the "Malformed Component Attribute" vulnerability.
CVE-2000-0465(PUBLISHED):Internet Explorer 4.x and 5.x does not properly verify the domain of a  frame within a browser window, which allows a remote attacker to read client files via the frame, aka the "Frame Domain Verification" vulnerability.
CVE-2000-0466(PUBLISHED):AIX cdmount allows local users to gain root privileges via shell metacharacters.
CVE-2000-0467(PUBLISHED):Buffer overflow in Linux splitvt 1.6.3 and earlier allows local users to gain root privileges via a long password in the screen locking function.
CVE-2000-0468(PUBLISHED):man in HP-UX 10.20 and 11 allows local attackers to overwrite files via a symlink attack.
CVE-2000-0469(PUBLISHED):Selena Sol WebBanner 4.0 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0470(PUBLISHED):Allegro RomPager HTTP server allows remote attackers to cause a denial of service via a malformed authentication request.
CVE-2000-0471(PUBLISHED):Buffer overflow in ufsrestore in Solaris 8 and earlier allows local users to gain root privileges via a long pathname.
CVE-2000-0472(PUBLISHED):Buffer overflow in innd 2.2.2 allows remote attackers to execute arbitrary commands via a cancel request containing a long message ID.
CVE-2000-0473(PUBLISHED):Buffer overflow in AnalogX SimpleServer 1.05 allows a remote attacker to cause a denial of service via a long GET request for a program in the cgi-bin directory.
CVE-2000-0474(PUBLISHED):Real Networks RealServer 7.x allows remote attackers to cause a denial of service via a malformed request for a page in the viewsource directory.
CVE-2000-0475(PUBLISHED):Windows 2000 allows a local user process to access another user's desktop within the same windows station, aka the "Desktop Separation" vulnerability.
CVE-2000-0476(PUBLISHED):xterm, Eterm, and rxvt allow an attacker to cause a denial of service by embedding certain escape characters which force the window to be resized.
CVE-2000-0477(PUBLISHED):Buffer overflow in Norton Antivirus for Exchange (NavExchange) allows remote attackers to cause a denial of service via a .zip file that contains long file names.
CVE-2000-0478(PUBLISHED):In some cases, Norton Antivirus for Exchange (NavExchange) enters a "fail-open" state which allows viruses to pass through the server.
CVE-2000-0479(PUBLISHED):Dragon FTP server allows remote attackers to cause a denial of service via a long USER command.
CVE-2000-0480(PUBLISHED):Dragon telnet server allows remote attackers to cause a denial of service via a long username.
CVE-2000-0481(PUBLISHED):Buffer overflow in KDE Kmail allows a remote attacker to cause a denial of service via an attachment with a long file name.
CVE-2000-0482(PUBLISHED):Check Point Firewall-1 allows remote attackers to cause a denial of service by sending a large number of malformed fragmented IP packets.
CVE-2000-0483(PUBLISHED):The DocumentTemplate package in Zope 2.2 and earlier allows a remote attacker to modify DTMLDocuments or DTMLMethods without authorization.
CVE-2000-0484(PUBLISHED):Small HTTP Server ver 3.06 contains a memory corruption bug causing a memory overflow. The overflowed buffer crashes into a Structured Exception Handler resulting in a Denial of Service.
CVE-2000-0485(PUBLISHED):Microsoft SQL Server allows local users to obtain database passwords via the Data Transformation Service (DTS) package Properties dialog, aka the "DTS Password" vulnerability.
CVE-2000-0486(PUBLISHED):Buffer overflow in Cisco TACACS+ tac_plus server allows remote attackers to cause a denial of service via a malformed packet with a long length field.
CVE-2000-0487(PUBLISHED):The Protected Store in Windows 2000 does not properly select the strongest encryption when available, which causes it to use a default of 40-bit encryption instead of 56-bit DES encryption, aka the "Protected Store Key Length" vulnerability.
CVE-2000-0488(PUBLISHED):Buffer overflow in ITHouse mail server 1.04 allows remote attackers to execute arbitrary commands via a long RCPT TO mail command.
CVE-2000-0489(PUBLISHED):FreeBSD, NetBSD, and OpenBSD allow an attacker to cause a denial of service by creating a large number of socket pairs using the socketpair function, setting a large buffer size via setsockopt, then writing large buffers.
CVE-2000-0490(PUBLISHED):Buffer overflow in the NetWin DSMTP 2.7q in the NetWin dmail package allows remote attackers to execute arbitrary commands via a long ETRN request.
CVE-2000-0491(PUBLISHED):Buffer overflow in the XDMCP parsing code of GNOME gdm, KDE kdm, and wdm allows remote attackers to execute arbitrary commands or cause a denial of service via a long FORWARD_QUERY request.
CVE-2000-0492(PUBLISHED):PassWD 1.2 uses weak encryption (trivial encoding) to store passwords, which allows an attacker who can read the password file to easliy decrypt the passwords.
CVE-2000-0493(PUBLISHED):Buffer overflow in Simple Network Time Sync (SMTS) daemon allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long string.
CVE-2000-0494(PUBLISHED):Veritas Volume Manager creates a world writable .server_pids file, which allows local users to add arbitrary commands into the file, which is then executed by the vmsa_server script.
CVE-2000-0495(PUBLISHED):Microsoft Windows Media Encoder allows remote attackers to cause a denial of service via a malformed request, aka the "Malformed Windows Media Encoder Request" vulnerability.
CVE-2000-0497(PUBLISHED):IBM WebSphere server 3.0.2 allows a remote attacker to view source code of a JSP program by requesting a URL which provides the JSP extension in upper case.
CVE-2000-0498(PUBLISHED):Unify eWave ServletExec allows a remote attacker to view source code of a JSP program by requesting a URL which provides the JSP extension in upper case.
CVE-2000-0499(PUBLISHED):The default configuration of BEA WebLogic 3.1.8 through 4.5.1 allows a remote attacker to view source code of a JSP program by requesting a URL which provides the JSP extension in upper case.
CVE-2000-0500(PUBLISHED):The default configuration of BEA WebLogic 5.1.0 allows a remote attacker to view source code of programs by requesting a URL beginning with /file/, which causes the default servlet to display the file without further processing.
CVE-2000-0501(PUBLISHED):Race condition in MDaemon 2.8.5.0 POP server allows local users to cause a denial of service by entering a UIDL command and quickly exiting the server.
CVE-2000-0502(PUBLISHED):Mcafee VirusScan 4.03 does not properly restrict access to the alert text file before it is sent to the Central Alert Server, which allows local users to modify alerts in an arbitrary fashion.
CVE-2000-0503(PUBLISHED):The IFRAME of the WebBrowser control in Internet Explorer 5.01 allows a remote attacker to violate the cross frame security policy via the NavigateComplete2 event.
CVE-2000-0504(PUBLISHED):libICE in XFree86 allows remote attackers to cause a denial of service by specifying a large value which is not properly checked by the SKIP_STRING macro.
CVE-2000-0505(PUBLISHED):The Apache 1.3.x HTTP server for Windows platforms allows remote attackers to list directory contents by requesting a URL containing a large number of / characters.
CVE-2000-0506(PUBLISHED):The "capabilities" feature in Linux before 2.2.16 allows local users to cause a denial of service or gain privileges by setting the capabilities to prevent a setuid program from dropping privileges, aka the "Linux kernel setuid/setcap vulnerability."
CVE-2000-0507(PUBLISHED):Imate Webmail Server 2.5 allows remote attackers to cause a denial of service via a long HELO command.
CVE-2000-0508(PUBLISHED):rpc.lockd in Red Hat Linux 6.1 and 6.2 allows remote attackers to cause a denial of service via a malformed request.
CVE-2000-0509(PUBLISHED):Buffer overflows in the finger and whois demonstration scripts in Sambar Server 4.3 allow remote attackers to execute arbitrary commands via a long hostname.
CVE-2000-0510(PUBLISHED):CUPS (Common Unix Printing System) 1.04 and earlier allows remote attackers to cause a denial of service via a malformed IPP request.
CVE-2000-0511(PUBLISHED):CUPS (Common Unix Printing System) 1.04 and earlier allows remote attackers to cause a denial of service via a CGI POST request.
CVE-2000-0512(PUBLISHED):CUPS (Common Unix Printing System) 1.04 and earlier does not properly delete request files, which allows a remote attacker to cause a denial of service.
CVE-2000-0513(PUBLISHED):CUPS (Common Unix Printing System) 1.04 and earlier allows remote attackers to cause a denial of service by authenticating with a user name that does not exist or does not have a shadow password.
CVE-2000-0514(PUBLISHED):GSSFTP FTP daemon in Kerberos 5 1.1.x does not properly restrict access to some FTP commands, which allows remote attackers to cause a denial of service, and local users to gain root privileges.
CVE-2000-0515(PUBLISHED):The snmpd.conf configuration file for the SNMP daemon (snmpd) in HP-UX 11.0 is world writable, which allows local users to modify SNMP configuration or gain privileges.
CVE-2000-0516(PUBLISHED):When configured to store configuration information in an LDAP directory, Shiva Access Manager 5.0.0 stores the root DN (Distinguished Name) name and password in cleartext in a file that is world readable, which allows local users to compromise the LDAP server.
CVE-2000-0517(PUBLISHED):Netscape 4.73 and earlier does not properly warn users about a potentially invalid certificate if the user has previously accepted the certificate for a different web site, which could allow remote attackers to spoof a legitimate web site by compromising that site's DNS information.
CVE-2000-0518(PUBLISHED):Internet Explorer 4.x and 5.x does not properly verify all contents of an SSL certificate if a connection is made to the server via an image or a frame, aka one of two different "SSL Certificate Validation" vulnerabilities.
CVE-2000-0519(PUBLISHED):Internet Explorer 4.x and 5.x does not properly re-validate an SSL certificate if the user establishes a new SSL session with the same server during the same Internet Explorer session, aka one of two different "SSL Certificate Validation" vulnerabilities.
CVE-2000-0520(PUBLISHED):Buffer overflow in restore program 0.4b17 and earlier in dump package allows local users to execute arbitrary commands via a long tape name.
CVE-2000-0521(PUBLISHED):Savant web server allows remote attackers to read source code of CGI scripts via a GET request that does not include the HTTP version number.
CVE-2000-0522(PUBLISHED):RSA ACE/Server allows remote attackers to cause a denial of service by flooding the server's authentication request port with UDP packets, which causes the server to crash.
CVE-2000-0523(PUBLISHED):Buffer overflow in the logging feature of EServ 2.9.2 and earlier allows an attacker to execute arbitrary commands via a long MKD command.
CVE-2000-0524(PUBLISHED):Microsoft Outlook and Outlook Express allow remote attackers to cause a denial of service by sending email messages with blank fields such as BCC, Reply-To, Return-Path, or From.
CVE-2000-0525(PUBLISHED):OpenSSH does not properly drop privileges when the UseLogin option is enabled, which allows local users to execute arbitrary commands by providing the command to the ssh daemon.
CVE-2000-0526(PUBLISHED):mailview.cgi CGI program in MailStudio 2000 2.0 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0527(PUBLISHED):userreg.cgi CGI program in MailStudio 2000 2.0 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2000-0528(PUBLISHED):Net Tools PKI Server does not properly restrict access to remote attackers when the XUDA template files do not contain absolute pathnames for other files.
CVE-2000-0529(PUBLISHED):Net Tools PKI Server allows remote attackers to cause a denial of service via a long HTTP request.
CVE-2000-0530(PUBLISHED):The KApplication class in the KDE 1.1.2 configuration file management capability allows local users to overwrite arbitrary files.
CVE-2000-0531(PUBLISHED):Linux gpm program allows local users to cause a denial of service by flooding the /dev/gpmctl device with STREAM sockets.
CVE-2000-0532(PUBLISHED):A FreeBSD patch for SSH on 2000-01-14 configures ssh to listen on port 722 as well as port 22, which might allow remote attackers to access SSH through port 722 even if port 22 is otherwise filtered.
CVE-2000-0533(PUBLISHED):Vulnerability in cvconnect in SGI IRIX WorkShop allows local users to overwrite arbitrary files.
CVE-2000-0534(PUBLISHED):The apsfilter software in the FreeBSD ports package does not properly read user filter configurations, which allows local users to execute commands as the lpd user.
CVE-2000-0535(PUBLISHED):OpenSSL 0.9.4 and OpenSSH for FreeBSD do not properly check for the existence of the /dev/random or /dev/urandom devices, which are absent on FreeBSD Alpha systems, which causes them to produce weak keys which may be more easily broken.
CVE-2000-0536(PUBLISHED):xinetd 2.1.8.x does not properly restrict connections if hostnames are used for access control and the connecting host does not have a reverse DNS entry.
CVE-2000-0537(PUBLISHED):BRU backup software allows local users to append data to arbitrary files by specifying an alternate configuration file with the BRUEXECLOG environmental variable.
CVE-2000-0538(PUBLISHED):ColdFusion Administrator for ColdFusion 4.5.1 and earlier allows remote attackers to cause a denial of service via a long login password.
CVE-2000-0539(PUBLISHED):Servlet examples in Allaire JRun 2.3.x allow remote attackers to obtain sensitive information, e.g. listing HttpSession ID's via the SessionServlet servlet.
CVE-2000-0540(PUBLISHED):JSP sample files in Allaire JRun 2.3.x allow remote attackers to access arbitrary files (e.g. via viewsource.jsp) or obtain configuration information.
CVE-2000-0541(PUBLISHED):The Panda Antivirus console on port 2001 allows local users to execute arbitrary commands without authentication via the CMD command.
CVE-2000-0542(PUBLISHED):Tigris remote access server before ********* does not properly record Radius accounting information when a user fails the initial login authentication but subsequently succeeds.
CVE-2000-0543(PUBLISHED):The command port for PGP Certificate Server 2.5.0 and 2.5.1 allows remote attackers to cause a denial of service if their hostname does not have a reverse DNS entry and they connect to port 4000.
CVE-2000-0544(PUBLISHED):Windows NT and Windows 2000 hosts allow a remote attacker to cause a denial of service via malformed DCE/RPC SMBwriteX requests that contain an invalid data length.
CVE-2000-0545(PUBLISHED):Buffer overflow in mailx mail command (aka Mail) on Linux systems allows local users to gain privileges via a long -c (carbon copy) parameter.
CVE-2000-0546(PUBLISHED):Buffer overflow in Kerberos 4 KDC program allows remote attackers to cause a denial of service via the lastrealm variable in the set_tgtkey function.
CVE-2000-0547(PUBLISHED):Buffer overflow in Kerberos 4 KDC program allows remote attackers to cause a denial of service via the localrealm variable in the process_v4 function.
CVE-2000-0548(PUBLISHED):Buffer overflow in Kerberos 4 KDC program allows remote attackers to cause a denial of service via the e_msg variable in the kerb_err_reply function.
CVE-2000-0549(PUBLISHED):Kerberos 4 KDC program does not properly check for null termination of AUTH_MSG_KDC_REQUEST requests, which allows remote attackers to cause a denial of service via a malformed request.
CVE-2000-0550(PUBLISHED):Kerberos 4 KDC program improperly frees memory twice (aka "double-free"), which allows remote attackers to cause a denial of service.
CVE-2000-0551(PUBLISHED):The file transfer mechanism in Danware NetOp 6.0 does not provide authentication, which allows remote attackers to access and modify arbitrary files.
CVE-2000-0552(PUBLISHED):ICQwebmail client for ICQ 2000A creates a world readable temporary file during login and does not delete it, which allows local users to obtain sensitive information.
CVE-2000-0553(PUBLISHED):Race condition in IPFilter firewall 3.4.3 and earlier, when configured with overlapping "return-rst" and "keep state" rules, allows remote attackers to bypass access restrictions.
CVE-2000-0554(PUBLISHED):Ceilidh allows remote attackers to obtain the real path of the Ceilidh directory via the translated_path hidden form field.
CVE-2000-0555(PUBLISHED):Ceilidh allows remote attackers to cause a denial of service via a large number of POST requests.
CVE-2000-0556(PUBLISHED):Buffer overflow in the web interface for Cmail 2.4.7 allows remote attackers to cause a denial of service by sending a large user name to the user dialog running on port 8002.
CVE-2000-0557(PUBLISHED):Buffer overflow in the web interface for Cmail 2.4.7 allows remote attackers to execute arbitrary commands via a long GET request.
CVE-2000-0558(PUBLISHED):Buffer overflow in HP Openview Network Node Manager 6.1 allows remote attackers to execute arbitrary commands via the Alarm service (OVALARMSRV) on port 2345.
CVE-2000-0559(PUBLISHED):eTrust Intrusion Detection System (formerly SessionWall-3) uses weak encryption (XOR) to store administrative passwords in the registry, which allows local users to easily decrypt the passwords.
CVE-2000-0561(PUBLISHED):Buffer overflow in WebBBS 1.15 allows remote attackers to execute arbitrary commands via a long HTTP GET request.
CVE-2000-0562(PUBLISHED):BlackIce Defender 2.1 and earlier, and BlackIce Pro 2.0.23 and earlier, do not properly block Back Orifice traffic when the security setting is Nervous or lower.
CVE-2000-0563(PUBLISHED):The URLConnection function in MacOS Runtime Java (MRJ) 2.1 and earlier and the Microsoft virtual machine (VM) for MacOS allows a malicious web site operator to connect to arbitrary hosts using a HTTP redirection, in violation of the Java security model.
CVE-2000-0564(PUBLISHED):The guestbook CGI program in ICQ Web Front service for ICQ 2000a, 99b, and others allows remote attackers to cause a denial of service via a URL with a long name parameter.
CVE-2000-0565(PUBLISHED):SmartFTP Daemon 0.2 allows a local user to access arbitrary files by uploading and specifying an alternate user configuration file via a .. (dot dot) attack.
CVE-2000-0566(PUBLISHED):makewhatis in Linux man package allows local users to overwrite files via a symlink attack.
CVE-2000-0567(PUBLISHED):Buffer overflow in Microsoft Outlook and Outlook Express allows remote attackers to execute arbitrary commands via a long Date field in an email header, aka the "Malformed E-mail Header" vulnerability.
CVE-2000-0568(PUBLISHED):Sybergen Secure Desktop 2.1 does not properly protect against false router advertisements (ICMP type 9), which allows remote attackers to modify default routes.
CVE-2000-0569(PUBLISHED):Sybergen Sygate allows remote attackers to cause a denial of service by sending a malformed DNS UDP packet to its internal interface.
CVE-2000-0570(PUBLISHED):FirstClass Internet Services server 5.770, and other versions before 6.1, allows remote attackers to cause a denial of service by sending an email with a long To: mail header.
CVE-2000-0571(PUBLISHED):LocalWEB HTTP server 1.2.0 allows remote attackers to cause a denial of service via a long GET request.
CVE-2000-0572(PUBLISHED):The Razor configuration management tool uses weak encryption for its password file, which allows local users to gain privileges.
CVE-2000-0573(PUBLISHED):The lreply function in wu-ftpd 2.6.0 and earlier does not properly cleanse an untrusted format string, which allows remote attackers to execute arbitrary commands via the SITE EXEC command.
CVE-2000-0574(PUBLISHED):FTP servers such as OpenBSD ftpd, NetBSD ftpd, ProFTPd and Opieftpd do not properly cleanse untrusted format strings that are used in the setproctitle function (sometimes called by set_proc_title), which allows remote attackers to cause a denial of service or execute arbitrary commands.
CVE-2000-0575(PUBLISHED):SSH 1.2.27 with Kerberos authentication support stores Kerberos tickets in a file which is created in the current directory of the user who is logging in, which could allow remote attackers to sniff the ticket cache if the home directory is installed on NFS.
CVE-2000-0576(PUBLISHED):Oracle Web Listener for AIX versions *******.0 and *******.0 allows remote attackers to cause a denial of service via a malformed URL.
CVE-2000-0577(PUBLISHED):Netscape Professional Services FTP Server 1.3.6 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0578(PUBLISHED):SGI MIPSPro compilers C, C++, F77 and F90 generate temporary files in /tmp with predictable file names, which could allow local users to insert malicious contents into these files as they are being compiled by another user.
CVE-2000-0579(PUBLISHED):IRIX crontab creates temporary files with predictable file names and with the umask of the user, which could allow local users to modify another user's crontab file as it is being edited.
CVE-2000-0580(PUBLISHED):Windows 2000 Server allows remote attackers to cause a denial of service by sending a continuous stream of binary zeros to various TCP and UDP ports, which significantly increases the CPU utilization.
CVE-2000-0581(PUBLISHED):Windows 2000 Telnet Server allows remote attackers to cause a denial of service by sending a continuous stream of binary zeros, which causes the server to crash.
CVE-2000-0582(PUBLISHED):Check Point FireWall-1 4.0 and 4.1 allows remote attackers to cause a denial of service by sending a stream of invalid commands (such as binary zeros) to the SMTP Security Server proxy.
CVE-2000-0583(PUBLISHED):vchkpw program in vpopmail before version 4.8 does not properly cleanse an untrusted format string used in a call to syslog, which allows remote attackers to cause a denial of service via a USER or PASS command that contains arbitrary formatting directives.
CVE-2000-0584(PUBLISHED):Buffer overflow in Canna input system allows remote attackers to execute arbitrary commands via an SR_INIT command with a long user name or group name.
CVE-2000-0585(PUBLISHED):ISC DHCP client program dhclient allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2000-0586(PUBLISHED):Buffer overflow in Dalnet IRC server 4.6.5 allows remote attackers to cause a denial of service or execute arbitrary commands via the SUMMON command.
CVE-2000-0587(PUBLISHED):The privpath directive in glftpd 1.18 allows remote attackers to bypass access restrictions for directories by using the file name completion capability.
CVE-2000-0588(PUBLISHED):SawMill 5.0.21 CGI program allows remote attackers to read the first line of arbitrary files by listing the file in the rfcf parameter, whose contents SawMill attempts to parse as configuration commands.
CVE-2000-0589(PUBLISHED):SawMill 5.0.21 uses weak encryption to store passwords, which allows attackers to easily decrypt the password and modify the SawMill configuration.
CVE-2000-0590(PUBLISHED):Poll It 2.0 CGI script allows remote attackers to read arbitrary files by specifying the file name in the data_dir parameter.
CVE-2000-0591(PUBLISHED):Novell BorderManager 3.0 and 3.5 allows remote attackers to bypass URL filtering by encoding characters in the requested URL.
CVE-2000-0592(PUBLISHED):Buffer overflows in POP3 service in WinProxy 2.0 and 2.0.1 allow remote attackers to execute arbitrary commands via long USER, PASS, LIST, RETR, or DELE commands.
CVE-2000-0593(PUBLISHED):WinProxy 2.0 and 2.0.1 allows remote attackers to cause a denial of service by sending an HTTP GET request without listing an HTTP version number.
CVE-2000-0594(PUBLISHED):BitchX IRC client does not properly cleanse an untrusted format string, which allows remote attackers to cause a denial of service via an invite to a channel whose name includes special formatting characters.
CVE-2000-0595(PUBLISHED):libedit searches for the .editrc file in the current directory instead of the user's home directory, which may allow local users to execute arbitrary commands by installing a modified .editrc in another directory.
CVE-2000-0596(PUBLISHED):Internet Explorer 5.x does not warn a user before opening a Microsoft Access database file that is referenced within ActiveX OBJECT tags in an HTML document, which could allow remote attackers to execute arbitrary commands, aka the "IE Script" vulnerability.
CVE-2000-0597(PUBLISHED):Microsoft Office 2000 (Excel and PowerPoint) and PowerPoint 97 are marked as safe for scripting, which allows remote attackers to force Internet Explorer or some email clients to save files to arbitrary locations via the Visual Basic for Applications (VBA) SaveAs function, aka the "Office HTML Script" vulnerability.
CVE-2000-0598(PUBLISHED):Fortech Proxy+ allows remote attackers to bypass access restrictions for to the administration service by redirecting their connections through the telnet proxy.
CVE-2000-0599(PUBLISHED):Buffer overflow in iMesh 1.02 allows remote attackers to execute arbitrary commands via a long string to the iMesh port.
CVE-2000-0600(PUBLISHED):Netscape Enterprise Server in NetWare 5.1 allows remote attackers to cause a denial of service or execute arbitrary commands via a malformed URL.
CVE-2000-0601(PUBLISHED):LeafChat 1.7 IRC client allows a remote IRC server to cause a denial of service by rapidly sending a large amount of error messages.
CVE-2000-0602(PUBLISHED):Secure Locate (slocate) in Red Hat Linux allows local users to gain privileges via a malformed configuration file that is specified in the LOCATE_PATH environmental variable.
CVE-2000-0603(PUBLISHED):Microsoft SQL Server 7.0 allows a local user to bypass permissions for stored procedures by referencing them via a temporary stored procedure, aka the "Stored Procedure Permissions" vulnerability.
CVE-2000-0604(PUBLISHED):gkermit in Red Hat Linux is improperly installed with setgid uucp, which allows local users to modify files owned by uucp.
CVE-2000-0605(PUBLISHED):Blackboard CourseInfo 4.0 stores the local and SQL administrator user names and passwords in cleartext in a registry key whose access control allows users to access the passwords.
CVE-2000-0606(PUBLISHED):Buffer overflow in kon program in Kanji on Console (KON) package on Linux may allow local users to gain root privileges via a long -StartupMessage parameter.
CVE-2000-0607(PUBLISHED):Buffer overflow in fld program in Kanji on Console (KON) package on Linux may allow local users to gain root privileges via an input file containing long CHARSET_REGISTRY or CHARSET_ENCODING settings.
CVE-2000-0608(PUBLISHED):NetWin dMailWeb and cwMail 2.6i and earlier allows remote attackers to cause a denial of service via a long POP parameter (pophost).
CVE-2000-0609(PUBLISHED):NetWin dMailWeb and cwMail 2.6g and earlier allows remote attackers to cause a denial of service via a long username parameter.
CVE-2000-0610(PUBLISHED):NetWin dMailWeb and cwMail 2.6g and earlier allows remote attackers to bypass authentication and use the server for mail relay via a username that contains a carriage return.
CVE-2000-0611(PUBLISHED):The default configuration of NetWin dMailWeb and cwMail trusts all POP servers, which allows attackers to bypass normal authentication and cause a denial of service.
CVE-2000-0612(PUBLISHED):Windows 95 and Windows 98 do not properly process spoofed ARP packets, which allows remote attackers to overwrite static entries in the cache table.
CVE-2000-0613(PUBLISHED):Cisco Secure PIX Firewall does not properly identify forged TCP Reset (RST) packets, which allows remote attackers to force the firewall to close legitimate connections.
CVE-2000-0614(PUBLISHED):Tnef program in Linux systems allows remote attackers to overwrite arbitrary files via TNEF encoded compressed attachments which specify absolute path names for the decompressed output.
CVE-2000-0615(PUBLISHED):LPRng 3.6.x improperly installs lpd as setuid root, which can allow local users to append lpd trace and logging messages to files.
CVE-2000-0616(PUBLISHED):Vulnerability in HP TurboIMAGE DBUTIL allows local users to gain additional privileges via DBUTIL.PUB.SYS.
CVE-2000-0617(PUBLISHED):Buffer overflow in xconq and cconq game programs on Red Hat Linux allows local users to gain additional privileges via long USER environmental variable.
CVE-2000-0618(PUBLISHED):Buffer overflow in xconq and cconq game programs on Red Hat Linux allows local users to gain additional privileges via long DISPLAY environmental variable.
CVE-2000-0619(PUBLISHED):Top Layer AppSwitch 2500 allows remote attackers to cause a denial of service via malformed ICMP packets.
CVE-2000-0620(PUBLISHED):libX11 X library allows remote attackers to cause a denial of service via a resource mask of 0, which causes libX11 to go into an infinite loop.
CVE-2000-0621(PUBLISHED):Microsoft Outlook 98 and 2000, and Outlook Express 4.0x and 5.0x, allow remote attackers to read files on the client's system via a malformed HTML message that stores files outside of the cache, aka the "Cache Bypass" vulnerability.
CVE-2000-0622(PUBLISHED):Buffer overflow in Webfind CGI program in O'Reilly WebSite Professional web server 2.x allows remote attackers to execute arbitrary commands via a URL containing a long "keywords" parameter.
CVE-2000-0623(PUBLISHED):Buffer overflow in O'Reilly WebSite Professional web server 2.4 and earlier allows remote attackers to execute arbitrary commands via a long GET request or Referrer header.
CVE-2000-0624(PUBLISHED):Buffer overflow in Winamp 2.64 and earlier allows remote attackers to execute arbitrary commands via a long #EXTINF: extension in the M3U playlist.
CVE-2000-0625(PUBLISHED):NetZero 3.0 and earlier uses weak encryption for storing a user's login information, which allows a local user to decrypt the password.
CVE-2000-0626(PUBLISHED):Buffer overflow in Alibaba web server allows remote attackers to cause a denial of service via a long GET request.
CVE-2000-0627(PUBLISHED):BlackBoard CourseInfo 4.0 does not properly authenticate users, which allows local users to modify CourseInfo database information and gain privileges by directly calling the supporting CGI programs such as user_update_passwd.pl and user_update_admin.pl.
CVE-2000-0628(PUBLISHED):The source.asp example script in the Apache ASP module Apache::ASP 1.93 and earlier allows remote attackers to modify files.
CVE-2000-0629(PUBLISHED):The default configuration of the Sun Java web server 2.0 and earlier allows remote attackers to execute arbitrary commands by uploading Java code to the server via board.html, then directly calling the JSP compiler servlet.
CVE-2000-0630(PUBLISHED):IIS 4.0 and 5.0 allows remote attackers to obtain fragments of source code by appending a +.htr to the URL, a variant of the "File Fragment Reading via .HTR" vulnerability.
CVE-2000-0631(PUBLISHED):An administrative script from IIS 3.0, later included in IIS 4.0 and 5.0, allows remote attackers to cause a denial of service by accessing the script without a particular argument, aka the "Absent Directory Browser Argument" vulnerability.
CVE-2000-0632(PUBLISHED):Buffer overflow in the web archive component of L-Soft Listserv 1.8d and earlier allows remote attackers to execute arbitrary commands via a long query string.
CVE-2000-0633(PUBLISHED):Vulnerability in Mandrake Linux usermode package allows local users to to reboot or halt the system.
CVE-2000-0634(PUBLISHED):The web administration interface for CommuniGate Pro 3.2.5 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0635(PUBLISHED):The view_page.html sample page in the MiniVend shopping cart program allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2000-0636(PUBLISHED):HP JetDirect printers versions G.08.20 and H.08.20 and earlier allow remote attackers to cause a denial of service via a malformed FTP quote command.
CVE-2000-0637(PUBLISHED):Microsoft Excel 97 and 2000 allows an attacker to execute arbitrary commands by specifying a malicious .dll using the Register.ID function, aka the "Excel REGISTER.ID Function" vulnerability.
CVE-2000-0638(PUBLISHED):bb-hostsvc.sh in Big Brother 1.4h1 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack on the HOSTSVC parameter.
CVE-2000-0639(PUBLISHED):The default configuration of Big Brother 1.4h2 and earlier does not include proper access restrictions, which allows remote attackers to execute arbitrary commands by using bbd to upload a file whose extension will cause it to be executed as a CGI script by the web server.
CVE-2000-0640(PUBLISHED):Guild FTPd allows remote attackers to determine the existence of files outside the FTP root via a .. (dot dot) attack, which provides different error messages depending on whether the file exists or not.
CVE-2000-0641(PUBLISHED):Savant web server allows remote attackers to execute arbitrary commands via a long GET request.
CVE-2000-0642(PUBLISHED):The default configuration of WebActive HTTP Server 1.00 stores the web access log active.log in the document root, which allows remote attackers to view the logs by directly requesting the page.
CVE-2000-0643(PUBLISHED):Buffer overflow in WebActive HTTP Server 1.00 allows remote attackers to cause a denial of service via a long URL.
CVE-2000-0644(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows remote attackers to cause a denial of service by executing a STAT command while the LIST command is still executing.
CVE-2000-0645(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows remote attackers to cause a denial of service by using the RESTART (REST) command and writing beyond the end of a file, or writing to a file that does not exist, via commands such as STORE UNIQUE (STOU), STORE (STOR), or APPEND (APPE).
CVE-2000-0646(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows remote attackers to obtain the real pathname for a file by executing a STATUS (STAT) command while the file is being transferred.
CVE-2000-0647(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows remote attackers to cause a denial of service by executing an MLST command before logging into the server.
CVE-2000-0648(PUBLISHED):WFTPD and WFTPD Pro 2.41 allows local users to cause a denial of service by executing the RENAME TO (RNTO) command before a RENAME FROM (RNFR) command.
CVE-2000-0649(PUBLISHED):IIS 4.0 allows remote attackers to obtain the internal IP address of the server via an HTTP 1.0 request for a web page which is protected by basic authentication and has no realm defined.
CVE-2000-0650(PUBLISHED):The default installation of VirusScan 4.5 and NetShield 4.5 has insecure permissions for the registry key that identifies the AutoUpgrade directory, which allows local users to execute arbitrary commands by replacing SETUP.EXE in that directory with a Trojan Horse.
CVE-2000-0651(PUBLISHED):The ClientTrust program in Novell BorderManager does not properly verify the origin of authentication requests, which could allow remote attackers to impersonate another user by replaying the authentication requests and responses from port 3024 of the victim's machine.
CVE-2000-0652(PUBLISHED):IBM WebSphere allows remote attackers to read source code for executable web files by directly calling the default InvokerServlet using a URL which contains the "/servlet/file" string.
CVE-2000-0653(PUBLISHED):Microsoft Outlook Express allows remote attackers to monitor a user's email by creating a persistent browser link to the Outlook Express windows, aka the "Persistent Mail-Browser Link" vulnerability.
CVE-2000-0654(PUBLISHED):Microsoft Enterprise Manager allows local users to obtain database passwords via the Data Transformation Service (DTS) package Registered Servers Dialog dialog, aka a variant of the "DTS Password" vulnerability.
CVE-2000-0655(PUBLISHED):Netscape Communicator 4.73 and earlier allows remote attackers to cause a denial of service or execute arbitrary commands via a JPEG image containing a comment with an illegal field length of 1.
CVE-2000-0656(PUBLISHED):Buffer overflow in AnalogX proxy server 4.04 and earlier allows remote attackers to cause a denial of service via a long USER command in the FTP protocol.
CVE-2000-0657(PUBLISHED):Buffer overflow in AnalogX proxy server 4.04 and earlier allows remote attackers to cause a denial of service via a long HELO command in the SMTP protocol.
CVE-2000-0658(PUBLISHED):Buffer overflow in AnalogX proxy server 4.04 and earlier allows remote attackers to cause a denial of service via a long USER command in the POP3 protocol.
CVE-2000-0659(PUBLISHED):Buffer overflow in AnalogX proxy server 4.04 and earlier allows remote attackers to cause a denial of service via a long user ID in a SOCKS4 CONNECT request.
CVE-2000-0660(PUBLISHED):The WDaemon web server for WorldClient 2.1 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0661(PUBLISHED):WircSrv IRC Server 5.07s allows remote attackers to cause a denial of service via a long string to the server port.
CVE-2000-0662(PUBLISHED):Internet Explorer 5.x and Microsoft Outlook allows remote attackers to read arbitrary files by redirecting the contents of an IFRAME using the DHTML Edit Control (DHTMLED).
CVE-2000-0663(PUBLISHED):The registry entry for the Windows Shell executable (Explorer.exe) in Windows NT and Windows 2000 uses a relative path name, which allows local users to execute arbitrary commands by inserting a Trojan Horse named Explorer.exe into the %Systemdrive% directory, aka the "Relative Shell Path" vulnerability.
CVE-2000-0664(PUBLISHED):AnalogX SimpleServer:WWW 1.06 and earlier allows remote attackers to read arbitrary files via a modified .. (dot dot) attack that uses the %2E URL encoding for the dots.
CVE-2000-0665(PUBLISHED):GAMSoft TelSrv telnet server 1.5 and earlier allows remote attackers to cause a denial of service via a long username.
CVE-2000-0666(PUBLISHED):rpc.statd in the nfs-utils package in various Linux distributions does not properly cleanse untrusted format strings, which allows remote attackers to gain root privileges.
CVE-2000-0667(PUBLISHED):Vulnerability in gpm in Caldera Linux allows local users to delete arbitrary files or conduct a denial of service.
CVE-2000-0668(PUBLISHED):pam_console PAM module in Linux systems allows a user to access the system console and reboot the system when a display manager such as gdm or kdm has XDMCP enabled.
CVE-2000-0669(PUBLISHED):Novell NetWare 5.0 allows remote attackers to cause a denial of service by flooding port 40193 with random data.
CVE-2000-0670(PUBLISHED):The cvsweb CGI script in CVSWeb 1.80 allows remote attackers with write access to a CVS repository to execute arbitrary commands via shell metacharacters.
CVE-2000-0671(PUBLISHED):Roxen web server earlier than 2.0.69 allows allows remote attackers to bypass access restrictions, list directory contents, and read source code by inserting a null character (%00) to the URL.
CVE-2000-0672(PUBLISHED):The default configuration of Jakarta Tomcat does not restrict access to the /admin context, which allows remote attackers to read arbitrary files by directly calling the administrative servlets to add a context for the root directory.
CVE-2000-0673(PUBLISHED):The NetBIOS Name Server (NBNS) protocol does not perform authentication, which allows remote attackers to cause a denial of service by sending a spoofed Name Conflict or Name Release datagram, aka the "NetBIOS Name Server Protocol Spoofing" vulnerability.
CVE-2000-0674(PUBLISHED):ftp.pl CGI program for Virtual Visions FTP browser allows remote attackers to read directories outside of the document root via a .. (dot dot) attack.
CVE-2000-0675(PUBLISHED):Buffer overflow in Infopulse Gatekeeper 3.5 and earlier allows remote attackers to execute arbitrary commands via a long string.
CVE-2000-0676(PUBLISHED):Netscape Communicator and Navigator 4.04 through 4.74 allows remote attackers to read arbitrary files by using a Java applet to open a connection to a URL using the "file", "http", "https", and "ftp" protocols, as demonstrated by Brown Orifice.
CVE-2000-0677(PUBLISHED):Buffer overflow in IBM Net.Data db2www CGI program allows remote attackers to execute arbitrary commands via a long PATH_INFO environmental variable.
CVE-2000-0678(PUBLISHED):PGP 5.5.x through 6.5.3 does not properly check if an Additional Decryption Key (ADK) is stored in the signed portion of a public certificate, which allows an attacker who can modify a victim's public certificate to decrypt any data that has been encrypted with the modified certificate.
CVE-2000-0679(PUBLISHED):The CVS 1.10.8 client trusts pathnames that are provided by the CVS server, which allows the server to force the client to create arbitrary files.
CVE-2000-0680(PUBLISHED):The CVS 1.10.8 server does not properly restrict users from creating arbitrary Checkin.prog or Update.prog programs, which allows remote CVS committers to modify or create Trojan horse programs with the Checkin.prog or Update.prog names, then performing a CVS commit action.
CVE-2000-0681(PUBLISHED):Buffer overflow in BEA WebLogic server proxy plugin allows remote attackers to execute arbitrary commands via a long URL with a .JSP extension.
CVE-2000-0682(PUBLISHED):BEA WebLogic 5.1.x allows remote attackers to read source code for parsed pages by inserting /ConsoleHelp/ into the URL, which invokes the FileServlet.
CVE-2000-0683(PUBLISHED):BEA WebLogic 5.1.x allows remote attackers to read source code for parsed pages by inserting /*.shtml/ into the URL, which invokes the SSIServlet.
CVE-2000-0684(PUBLISHED):BEA WebLogic 5.1.x does not properly restrict access to the JSPServlet, which could allow remote attackers to compile and execute Java JSP code by directly invoking the servlet on any source file.
CVE-2000-0685(PUBLISHED):BEA WebLogic 5.1.x does not properly restrict access to the PageCompileServlet, which could allow remote attackers to compile and execute Java JHTML code by directly invoking the servlet on any source file.
CVE-2000-0686(PUBLISHED):Auction Weaver CGI script 1.03 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack in the fromfile parameter.
CVE-2000-0687(PUBLISHED):Auction Weaver CGI script 1.03 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack in the catdir parameter.
CVE-2000-0688(PUBLISHED):Subscribe Me LITE does not properly authenticate attempts to change the administrator password, which allows remote attackers to gain privileges for the Account Manager by directly calling the subscribe.pl script with the setpwd parameter.
CVE-2000-0689(PUBLISHED):Account Manager LITE does not properly authenticate attempts to change the administrator password, which allows remote attackers to gain privileges for the Account Manager by directly calling the amadmin.pl script with the setpasswd parameter.
CVE-2000-0690(PUBLISHED):Auction Weaver CGI script 1.02 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters in the fromfile parameter.
CVE-2000-0691(PUBLISHED):The faxrunq and faxrunqd in the mgetty package allows local users to create or modify arbitrary files via a symlink attack which creates a symlink in from /var/spool/fax/outgoing/.last_run to the target file.
CVE-2000-0692(PUBLISHED):ISS RealSecure 3.2.1 and 3.2.2 allows remote attackers to cause a denial of service via a flood of fragmented packets with the SYN flag set.
CVE-2000-0693(PUBLISHED):pgxconfig in the Raptor GFX configuration tool uses a relative path name for a system call to the "cp" program, which allows local users to execute arbitrary commands by modifying their path to point to an alternate "cp" program.
CVE-2000-0694(PUBLISHED):pgxconfig in the Raptor GFX configuration tool allows local users to gain privileges via a symlink attack.
CVE-2000-0695(PUBLISHED):Buffer overflows in pgxconfig in the Raptor GFX configuration tool allow local users to gain privileges via command line options.
CVE-2000-0696(PUBLISHED):The administration interface for the dwhttpd web server in Solaris AnswerBook2 does not properly authenticate requests to its supporting CGI scripts, which allows remote attackers to add user accounts to the interface by directly calling the admin CGI script.
CVE-2000-0697(PUBLISHED):The administration interface for the dwhttpd web server in Solaris AnswerBook2 allows interface users to remotely execute commands via shell metacharacters.
CVE-2000-0698(PUBLISHED):Minicom 1.82.1 and earlier on some Linux systems allows local users to create arbitrary files owned by the uucp user via a symlink attack.
CVE-2000-0699(PUBLISHED):Format string vulnerability in ftpd in HP-UX 10.20 allows remote attackers to cause a denial of service or execute arbitrary commands via format strings in the PASS command.
CVE-2000-0700(PUBLISHED):Cisco Gigabit Switch Routers (GSR) with Fast Ethernet / Gigabit Ethernet cards, from IOS versions 11.2(15)GS1A up to 11.2(19)GS0.2 and some versions of 12.0, do not properly handle line card failures, which allows remote attackers to bypass ACLs or force the interface to stop forwarding packets.
CVE-2000-0701(PUBLISHED):The wrapper program in mailman 2.0beta3 and 2.0beta4 does not properly cleanse untrusted format strings, which allows local users to gain privileges.
CVE-2000-0702(PUBLISHED):The net.init rc script in HP-UX 11.00 (S008net.init) allows local users to overwrite arbitrary files via a symlink attack that points from /tmp/stcp.conf to the targeted file.
CVE-2000-0703(PUBLISHED):suidperl (aka sperl) does not properly cleanse the escape sequence "~!" before calling /bin/mail to send an error report, which allows local users to gain privileges by setting the "interactive" environmental variable and calling suidperl with a filename that contains the escape sequence.
CVE-2000-0704(PUBLISHED):Buffer overflow in SGI Omron WorldView Wnn allows remote attackers to execute arbitrary commands via long JS_OPEN, JS_MKDIR, or JS_FILE_INFO commands.
CVE-2000-0705(PUBLISHED):ntop running in web mode allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0706(PUBLISHED):Buffer overflows in ntop running in web mode allows remote attackers to execute arbitrary commands.
CVE-2000-0707(PUBLISHED):PCCS MySQLDatabase Admin Tool Manager 1.2.4 and earlier installs the file dbconnect.inc within the web root, which allows remote attackers to obtain sensitive information such as the administrative password.
CVE-2000-0708(PUBLISHED):Buffer overflow in Pragma Systems TelnetServer 2000 version 4.0 allows remote attackers to cause a denial of service via a long series of null characters to the rexec port.
CVE-2000-0709(PUBLISHED):The shtml.exe component of Microsoft FrontPage 2000 Server Extensions 1.1 allows remote attackers to cause a denial of service in some components by requesting a URL whose name includes a standard DOS device name.
CVE-2000-0710(PUBLISHED):The shtml.exe component of Microsoft FrontPage 2000 Server Extensions 1.1 allows remote attackers to determine the physical path of the server components by requesting an invalid URL whose name includes a standard DOS device name.
CVE-2000-0711(PUBLISHED):Netscape Communicator does not properly prevent a ServerSocket object from being created by untrusted entities, which allows remote attackers to create a server on the victim's system via a malicious applet, as demonstrated by Brown Orifice.
CVE-2000-0712(PUBLISHED):Linux Intrusion Detection System (LIDS) 0.9.7 allows local users to gain root privileges when LIDS is disabled via the security=0 boot option.
CVE-2000-0713(PUBLISHED):Buffer overflow in Adobe Acrobat 4.05, Reader, Business Tools, and Fill In products that handle PDF files allows attackers to execute arbitrary commands via a long /Registry or /Ordering specifier.
CVE-2000-0714(PUBLISHED):umb-scheme 3.2-11 for Red Hat Linux is installed with world-writeable files.
CVE-2000-0715(PUBLISHED):DiskCheck script diskcheck.pl in Red Hat Linux 6.2 allows local users to create or overwrite arbitrary files via a symlink attack on a temporary file.
CVE-2000-0716(PUBLISHED):WorldClient email client in MDaemon 2.8 includes the session ID in the referer field of an HTTP request when the user clicks on a URL, which allows the visited web site to hijack the session ID and read the user's email.
CVE-2000-0717(PUBLISHED):GoodTech FTP server allows remote attackers to cause a denial of service via a large number of RNTO commands.
CVE-2000-0718(PUBLISHED):A race condition in MandrakeUpdate allows local users to modify RPM files while they are in the /tmp directory before they are installed.
CVE-2000-0719(PUBLISHED):VariCAD 7.0 is installed with world-writeable files, which allows local users to replace the VariCAD programs with a Trojan horse program.
CVE-2000-0720(PUBLISHED):news.cgi in GWScripts News Publisher does not properly authenticate requests to add an author to the author index, which allows remote attackers to add new authors by directly posting an HTTP request to the new.cgi program with an addAuthor parameter, and setting the Referer to the news.cgi program.
CVE-2000-0721(PUBLISHED):The FSserial, FlagShip_c, and FlagShip_p programs in the FlagShip package are installed world-writeable, which allows local users to replace them with Trojan horses.
CVE-2000-0722(PUBLISHED):Helix GNOME Updater helix-update 0.5 and earlier allows local users to install arbitrary RPM packages by creating the /tmp/helix-install installation directory before root has begun installing packages.
CVE-2000-0723(PUBLISHED):Helix GNOME Updater helix-update 0.5 and earlier does not properly create /tmp directories, which allows local users to create empty system configuration files such as /etc/config.d/bashrc, /etc/config.d/csh.cshrc, and /etc/rc.config.
CVE-2000-0724(PUBLISHED):The go-gnome Helix GNOME pre-installer allows local users to overwrite arbitrary files via a symlink attack on various files in /tmp, including uudecode, snarf, and some installer files.
CVE-2000-0725(PUBLISHED):Zope before 2.2.1 does not properly restrict access to the getRoles method, which allows users who can edit DTML to add or modify roles by modifying the roles list that is included in a request.
CVE-2000-0726(PUBLISHED):CGIMail.exe CGI program in Stalkerlab Mailers 1.1.2 allows remote attackers to read arbitrary files by specifying the file in the $Attach$ hidden form variable.
CVE-2000-0727(PUBLISHED):xpdf PDF viewer client earlier than 0.91 does not properly launch a web browser for embedded URL's, which allows an attacker to execute arbitrary commands via a URL that contains shell metacharacters.
CVE-2000-0728(PUBLISHED):xpdf PDF viewer client earlier than 0.91 allows local users to overwrite arbitrary files via a symlink attack.
CVE-2000-0729(PUBLISHED):FreeBSD 5.x, 4.x, and 3.x allows local users to cause a denial of service by executing a program with a malformed ELF image header.
CVE-2000-0730(PUBLISHED):Vulnerability in newgrp command in HP-UX 11.0 allows local users to gain privileges.
CVE-2000-0731(PUBLISHED):Directory traversal vulnerability in Worm HTTP server allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0732(PUBLISHED):Worm HTTP server allows remote attackers to cause a denial of service via a long URL.
CVE-2000-0733(PUBLISHED):Telnetd telnet server in IRIX 5.2 through 6.1 does not properly cleans user-injected format strings, which allows remote attackers to execute arbitrary commands via a long RLD variable in the IAC-SB-TELOPT_ENVIRON request.
CVE-2000-0734(PUBLISHED):eEye IRIS 1.01 beta allows remote attackers to cause a denial of service via a large number of UDP connections.
CVE-2000-0735(PUBLISHED):Buffer overflow in Becky! Internet Mail client 1.26.03 and earlier allows remote attackers to cause a denial of service via a long Content-type: MIME header when the user replies to a message.
CVE-2000-0736(PUBLISHED):Buffer overflow in Becky! Internet Mail client 1.26.04 and earlier allows remote attackers to cause a denial of service via a long Content-type: MIME header when the user forwards a message.
CVE-2000-0737(PUBLISHED):The Service Control Manager (SCM) in Windows 2000 creates predictable named pipes, which allows a local user with console access to gain administrator privileges, aka the "Service Control Manager Named Pipe Impersonation" vulnerability.
CVE-2000-0738(PUBLISHED):WebShield SMTP 4.5 allows remote attackers to cause a denial of service by sending e-mail with a From: address that has a . (period) at the end, which causes WebShield to continuously send itself copies of the e-mail.
CVE-2000-0739(PUBLISHED):Directory traversal vulnerability in strong.exe program in NAI Net Tools PKI server 1.0 before HotFix 3 allows remote attackers to read arbitrary files via a .. (dot dot) attack in an HTTPS request to the enrollment server.
CVE-2000-0740(PUBLISHED):Buffer overflow in strong.exe program in NAI Net Tools PKI server 1.0 before HotFix 3 allows remote attackers to execute arbitrary commands via a long URL in the HTTPS port.
CVE-2000-0741(PUBLISHED):Format string vulnerability in strong.exe program in NAI Net Tools PKI server 1.0 before HotFix 3 allows remote attackers to execute arbitrary code via format strings in a URL with a .XUDA extension.
CVE-2000-0742(PUBLISHED):The IPX protocol implementation in Microsoft Windows 95 and 98 allows remote attackers to cause a denial of service by sending a ping packet with a source IP address that is a broadcast address, aka the "Malformed IPX Ping Packet" vulnerability.
CVE-2000-0743(PUBLISHED):Buffer overflow in University of Minnesota (UMN) gopherd 2.x allows remote attackers to execute arbitrary commands via a DES key generation request (GDESkey) that contains a long ticket value.
CVE-2000-0745(PUBLISHED):admin.php3 in PHP-Nuke does not properly verify the PHP-Nuke administrator password, which allows remote attackers to gain privileges by requesting a URL that does not specify the aid or pwd parameter.
CVE-2000-0746(PUBLISHED):Vulnerabilities in IIS 4.0 and 5.0 do not properly protect against cross-site scripting (CSS) attacks.  They allow a malicious web site operator to embed scripts in a link to a trusted site, which are returned without quoting in an error message back to the client.  The client then executes those scripts in the same context as the trusted site, aka the "IIS Cross-Site Scripting" vulnerabilities.
CVE-2000-0747(PUBLISHED):The logrotate script for OpenLDAP before 1.2.11 in Conectiva Linux sends an improper signal to the kernel log daemon (klogd) and kills it.
CVE-2000-0748(PUBLISHED):OpenLDAP 1.2.11 and earlier improperly installs the ud binary with group write permissions, which could allow any user in that group to replace the binary with a Trojan horse.
CVE-2000-0749(PUBLISHED):Buffer overflow in the Linux binary compatibility module in FreeBSD 3.x through 5.x allows local users to gain root privileges via long filenames in the linux shadow file system.
CVE-2000-0750(PUBLISHED):Buffer overflow in mopd (Maintenance Operations Protocol loader daemon) allows remote attackers to execute arbitrary commands via a long file name.
CVE-2000-0751(PUBLISHED):mopd (Maintenance Operations Protocol loader daemon) does not properly cleanse user-injected format strings, which allows remote attackers to execute arbitrary commands.
CVE-2000-0752(PUBLISHED):Buffer overflows in brouted in FreeBSD and possibly other OSes allows local users to gain root privileges via long command line arguments.
CVE-2000-0753(PUBLISHED):The Microsoft Outlook mail client identifies the physical path of the sender's machine within a winmail.dat attachment to Rich Text Format (RTF) files.
CVE-2000-0754(PUBLISHED):Vulnerability in HP OpenView Network Node Manager (NMM) version 6.1 related to passwords.
CVE-2000-0755(PUBLISHED):Vulnerability in the newgrp command in HP-UX 11.00 allows local users to gain privileges.
CVE-2000-0756(PUBLISHED):Microsoft Outlook 2000 does not properly process long or malformed fields in vCard (.vcf) files, which allows attackers to cause a denial of service.
CVE-2000-0757(PUBLISHED):The sysgen service in Aptis Totalbill does not perform authentication, which allows remote attackers to gain root privileges by connecting to the service and specifying the commands to be executed.
CVE-2000-0758(PUBLISHED):The web interface for Lyris List Manager 3 and 4 allows list subscribers to obtain administrative access by modifying the value of the list_admin hidden form field.
CVE-2000-0759(PUBLISHED):Jakarta Tomcat 3.1 under Apache reveals physical path information when a remote attacker requests a URL that does not exist, which generates an error message that includes the physical path.
CVE-2000-0760(PUBLISHED):The Snoop servlet in Jakarta Tomcat 3.1 and 3.0 under Apache reveals sensitive system information when a remote attacker requests a nonexistent URL with a .snp extension.
CVE-2000-0761(PUBLISHED):OS2/Warp 4.5 FTP server allows remote attackers to cause a denial of service via a long username.
CVE-2000-0762(PUBLISHED):The default installation of eTrust Access Control (formerly SeOS) uses a default encryption key, which allows remote attackers to spoof the eTrust administrator and gain privileges.
CVE-2000-0763(PUBLISHED):xlockmore and xlockf do not properly cleanse user-injected format strings, which allows local users to gain root privileges via the -d option.
CVE-2000-0764(PUBLISHED):Intel Express 500 series switches allow a remote attacker to cause a denial of service via a malformed IP packet.
CVE-2000-0765(PUBLISHED):Buffer overflow in the HTML interpreter in Microsoft Office 2000 allows an attacker to execute arbitrary commands via a long embedded object tag, aka the "Microsoft Office HTML Object Tag" vulnerability.
CVE-2000-0766(PUBLISHED):Buffer overflow in vqSoft vqServer 1.4.49 allows remote attackers to cause a denial of service or possibly gain privileges via a long HTTP GET request.
CVE-2000-0767(PUBLISHED):The ActiveX control for invoking a scriptlet in Internet Explorer 4.x and 5.x renders arbitrary file types instead of HTML, which allows an attacker to read arbitrary files, aka the "Scriptlet Rendering" vulnerability.
CVE-2000-0768(PUBLISHED):A function in Internet Explorer 4.x and 5.x does not properly verify the domain of a frame within a browser window, which allows a remote attacker to read client files, aka a variant of the "Frame Domain Verification" vulnerability.
CVE-2000-0769(PUBLISHED):O'Reilly WebSite Pro 2.3.7 installs the uploader.exe program with execute permissions for all users, which allows remote attackers to create and execute arbitrary files by directly calling uploader.exe.
CVE-2000-0770(PUBLISHED):IIS 4.0 and 5.0 does not properly restrict access to certain types of files when their parent folders have less restrictive permissions, which could allow remote attackers to bypass access restrictions to some files, aka the "File Permission Canonicalization" vulnerability.
CVE-2000-0771(PUBLISHED):Microsoft Windows 2000 allows local users to cause a denial of service by corrupting the local security policy via malformed RPC traffic, aka the "Local Security Policy Corruption" vulnerability.
CVE-2000-0772(PUBLISHED):The installation of Tumbleweed Messaging Management System (MMS) 4.6 and earlier (formerly Worldtalk Worldsecure) creates a default account "sa" with no password.
CVE-2000-0773(PUBLISHED):Bajie HTTP web server 0.30a allows remote attackers to read arbitrary files via a URL that contains a "....", a variant of the dot dot directory traversal attack.
CVE-2000-0774(PUBLISHED):The sample Java servlet "test" in Bajie HTTP web server 0.30a reveals the real pathname of the web document root.
CVE-2000-0775(PUBLISHED):Buffer overflow in RobTex Viking server earlier than 1.06-370 allows remote attackers to cause a denial of service or execute arbitrary commands via a long HTTP GET request, or long Unless-Modified-Since, If-Range, or If-Modified-Since headers.
CVE-2000-0776(PUBLISHED):Mediahouse Statistics Server 5.02x allows remote attackers to execute arbitrary commands via a long HTTP GET request.
CVE-2000-0777(PUBLISHED):The password protection feature of Microsoft Money can store the password in plaintext, which allows attackers with physical access to the system to obtain the password, aka the "Money Password" vulnerability.
CVE-2000-0778(PUBLISHED):IIS 5.0 allows remote attackers to obtain source code for .ASP files and other scripts via an HTTP GET request with a "Translate: f" header, aka the "Specialized Header" vulnerability.
CVE-2000-0779(PUBLISHED):Checkpoint Firewall-1 with the RSH/REXEC setting enabled allows remote attackers to bypass access restrictions and connect to a RSH/REXEC client via malformed connection requests.
CVE-2000-0780(PUBLISHED):The web server in IPSWITCH IMail 6.04 and earlier allows remote attackers to read and delete arbitrary files via a .. (dot dot) attack.
CVE-2000-0781(PUBLISHED):uagentsetup in ARCServeIT Client Agent 6.62 does not properly check for the existence or ownership of a temporary file which is moved to the agent.cfg configuration file, which allows local users to execute arbitrary commands by modifying the temporary file before it is moved.
CVE-2000-0782(PUBLISHED):netauth.cgi program in Netwin Netauth 4.2e and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0783(PUBLISHED):Watchguard Firebox II allows remote attackers to cause a denial of service by sending a malformed URL to the authentication service on port 4100.
CVE-2000-0784(PUBLISHED):sshd program in the Rapidstream 2.1 Beta VPN appliance has a hard-coded "rsadmin" account with a null password, which allows remote attackers to execute arbitrary commands via ssh.
CVE-2000-0785(PUBLISHED):WircSrv IRC Server 5.07s allows IRC operators to read arbitrary files via the importmotd command, which sets the Message of the Day (MOTD) to the specified file.
CVE-2000-0786(PUBLISHED):GNU userv 1.0.0 and earlier does not properly perform file descriptor swapping, which can corrupt the USERV_GROUPS and USERV_GIDS environmental variables and allow local users to bypass some access restrictions.
CVE-2000-0787(PUBLISHED):IRC Xchat client versions 1.4.2 and earlier allows remote attackers to execute arbitrary commands by encoding shell metacharacters into a URL which XChat uses to launch a web browser.
CVE-2000-0788(PUBLISHED):The Mail Merge tool in Microsoft Word does not prompt the user before executing Visual Basic (VBA) scripts in an Access database, which could allow an attacker to execute arbitrary commands.
CVE-2000-0789(PUBLISHED):WinU 5.x and earlier uses weak encryption to store its configuration password, which allows local users to decrypt the password and gain privileges.
CVE-2000-0790(PUBLISHED):The web-based folder display capability in Microsoft Internet Explorer 5.5 on Windows 98 allows local users to insert Trojan horse programs by modifying the Folder.htt file and using the InvokeVerb method in the ShellDefView ActiveX control to specify a default execute option for the first file that is listed in the folder.
CVE-2000-0791(PUBLISHED):Trustix installs the httpsd program for Apache-SSL with world-writeable permissions, which allows local users to replace it with a Trojan horse.
CVE-2000-0792(PUBLISHED):Gnome Lokkit firewall package before 0.41 does not properly restrict access to some ports, even if a user does not make any services available.
CVE-2000-0793(PUBLISHED):Norton AntiVirus 5.00.01C with the Novell Netware client does not properly restart the auto-protection service after the first user has logged off of the system.
CVE-2000-0794(PUBLISHED):Buffer overflow in IRIX libgl.so library allows local users to gain root privileges via a long HOME variable to programs such as (1) gmemusage and (2) gr_osview.
CVE-2000-0795(PUBLISHED):Buffer overflow in lpstat in IRIX 6.2 and 6.3 allows local users to gain root privileges via a long -n option.
CVE-2000-0796(PUBLISHED):Buffer overflow in dmplay in IRIX 6.2 and 6.3 allows local users to gain root privileges via a long command line option.
CVE-2000-0797(PUBLISHED):Buffer overflow in gr_osview in IRIX 6.2 and 6.3 allows local users to gain privileges via a long -D option.
CVE-2000-0798(PUBLISHED):The truncate function in IRIX 6.x does not properly check for privileges when the file is in the xfs file system, which allows local users to delete the contents of arbitrary files.
CVE-2000-0799(PUBLISHED):inpview in InPerson in SGI IRIX 5.3 through IRIX 6.5.10 allows local users to gain privileges via a symlink attack on the .ilmpAAA temporary file.
CVE-2000-0800(PUBLISHED):String parsing error in rpc.kstatd in the linuxnfs or knfsd packages in SuSE and possibly other Linux systems allows remote attackers to gain root privileges.
CVE-2000-0801(PUBLISHED):Buffer overflow in bdf program in HP-UX 11.00 may allow local users to gain root privileges via a long -t option.
CVE-2000-0802(PUBLISHED):The BAIR program does not properly restrict access to the Internet Explorer Internet options menu, which allows local users to obtain access to the menu by modifying the registry key that starts BAIR.
CVE-2000-0803(PUBLISHED):GNU Groff uses the current working directory to find a device description file, which allows a local user to gain additional privileges by including a malicious postpro directive in the description file, which is executed when another user runs groff.
CVE-2000-0804(PUBLISHED):Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to bypass the directionality check via fragmented TCP connection requests or reopening closed TCP connection requests, aka "One-way Connection Enforcement Bypass."
CVE-2000-0805(PUBLISHED):Check Point VPN-1/FireWall-1 4.1 and earlier improperly retransmits encapsulated FWS packets, even if they do not come from a valid FWZ client, aka "Retransmission of Encapsulated Packets."
CVE-2000-0806(PUBLISHED):The inter-module authentication mechanism (fwa1) in Check Point VPN-1/FireWall-1 4.1 and earlier may allow remote attackers to conduct a denial of service, aka "Inter-module Communications Bypass."
CVE-2000-0807(PUBLISHED):The OPSEC communications authentication mechanism (fwn1) in Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to spoof connections, aka the "OPSEC Authentication Vulnerability."
CVE-2000-0808(PUBLISHED):The seed generation mechanism in the inter-module S/Key authentication mechanism in Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to bypass authentication via a brute force attack, aka "One-time (s/key) Password Authentication."
CVE-2000-0809(PUBLISHED):Buffer overflow in Getkey in the protocol checker in the inter-module communication mechanism in Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to cause a denial of service.
CVE-2000-0810(PUBLISHED):Auction Weaver 1.0 through 1.04 does not properly validate the names of form fields, which allows remote attackers to delete arbitrary files and directories via a .. (dot dot) attack.
CVE-2000-0811(PUBLISHED):Auction Weaver 1.0 through 1.04 allows remote attackers to read arbitrary files via a .. (dot dot) attack on the username or bidfile form fields.
CVE-2000-0812(PUBLISHED):The administration module in Sun Java web server allows remote attackers to execute arbitrary commands by uploading Java code to the module and invoke the com.sun.server.http.pagecompile.jsp92.JspServlet by requesting a URL that begins with a /servlet/ tag.
CVE-2000-0813(PUBLISHED):Check Point VPN-1/FireWall-1 4.1 and earlier allows remote attackers to redirect FTP connections to other servers ("FTP Bounce") via invalid FTP commands that are processed improperly by FireWall-1, aka "FTP Connection Enforcement Bypass."
CVE-2000-0816(PUBLISHED):Linux tmpwatch --fuser option allows local users to execute arbitrary commands by creating files whose names contain shell metacharacters.
CVE-2000-0817(PUBLISHED):Buffer overflow in the HTTP protocol parser for Microsoft Network Monitor (Netmon) allows remote attackers to execute arbitrary commands via malformed data, aka the "Netmon Protocol Parsing" vulnerability.
CVE-2000-0818(PUBLISHED):The default installation for the Oracle listener program 7.3.4, 8.0.6, and 8.1.6 allows an attacker to cause logging information to be appended to arbitrary files and execute commands via the SET TRC_FILE or SET LOG_FILE commands.
CVE-2000-0824(PUBLISHED):The unsetenv function in glibc 2.1.1 does not properly unset an environmental variable if the variable is provided twice to a program, which could allow local users to execute arbitrary commands in setuid programs by specifying their own duplicate environmental variables such as LD_PRELOAD or LD_LIBRARY_PATH.
CVE-2000-0825(PUBLISHED):Ipswitch Imail 6.0 allows remote attackers to cause a denial of service via a large number of connections in which a long Host: header is sent, which causes a thread to crash.
CVE-2000-0826(PUBLISHED):Buffer overflow in ddicgi.exe program in Mobius DocumentDirect for the Internet 1.2 allows remote attackers to execute arbitrary commands via a long GET request.
CVE-2000-0827(PUBLISHED):Buffer overflow in the web authorization form of Mobius DocumentDirect for the Internet 1.2 allows remote attackers to cause a denial of service or execute arbitrary commands via a long username.
CVE-2000-0828(PUBLISHED):Buffer overflow in ddicgi.exe in Mobius DocumentDirect for the Internet 1.2 allows remote attackers to execute arbitrary commands via a long User-Agent parameter.
CVE-2000-0829(PUBLISHED):The tmpwatch utility in Red Hat Linux forks a new process for each directory level, which allows local users to cause a denial of service by creating deeply nested directories in /tmp or /var/tmp/.
CVE-2000-0830(PUBLISHED):annclist.exe in webTV for Windows allows remote attackers to cause a denial of service by via a large, malformed UDP packet to ports 22701 through 22705.
CVE-2000-0831(PUBLISHED):Buffer overflow in Fastream FTP++ 2.0 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long username.
CVE-2000-0832(PUBLISHED):Htgrep CGI program allows remote attackers to read arbitrary files by specifying the full pathname in the hdr parameter.
CVE-2000-0833(PUBLISHED):Buffer overflow in WinSMTP 1.06f and 2.X allows remote attackers to cause a denial of service via a long (1) USER or (2) HELO command.
CVE-2000-0834(PUBLISHED):The Windows 2000 telnet client attempts to perform NTLM authentication by default, which allows remote attackers to capture and replay the NTLM challenge/response via a telnet:// URL that points to the malicious server, aka the "Windows 2000 Telnet Client NTLM Authentication" vulnerability.
CVE-2000-0835(PUBLISHED):search.dll Sambar ISAPI Search utility in Sambar Server 4.4 Beta 3 allows remote attackers to read arbitrary directories by specifying the directory in the query parameter.
CVE-2000-0836(PUBLISHED):Buffer overflow in CamShot WebCam Trial2.6 allows remote attackers to execute arbitrary commands via a long Authorization header.
CVE-2000-0837(PUBLISHED):FTP Serv-U 2.5e allows remote attackers to cause a denial of service by sending a large number of null bytes.
CVE-2000-0838(PUBLISHED):Fastream FUR HTTP server 1.0b allows remote attackers to cause a denial of service via a long GET request.
CVE-2000-0839(PUBLISHED):WinCOM LPD 1.00.90 allows remote attackers to cause a denial of service via a large number of LPD options to the LPD port (515).
CVE-2000-0840(PUBLISHED):Buffer overflow in XMail POP3 server before version 0.59 allows remote attackers to execute arbitrary commands via a long USER command.
CVE-2000-0841(PUBLISHED):Buffer overflow in XMail POP3 server before version 0.59 allows remote attackers to execute arbitrary commands via a long APOP command.
CVE-2000-0842(PUBLISHED):The search97cgi/vtopic" in the UnixWare 7 scohelphttp webserver allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0843(PUBLISHED):Buffer overflow in pam_smb and pam_ntdom pluggable authentication modules (PAM) allow remote attackers to execute arbitrary commands via a login with a long user name.
CVE-2000-0844(PUBLISHED):Some functions that implement the locale subsystem on Unix do not  properly cleanse user-injected format strings, which allows local attackers to execute arbitrary commands via functions such as gettext and catopen.
CVE-2000-0845(PUBLISHED):kdebug daemon (kdebugd) in Digital Unix 4.0F allows remote attackers to read arbitrary files by specifying the full file name in the initialization packet.
CVE-2000-0846(PUBLISHED):Buffer overflow in Darxite 0.4 and earlier allows a remote attacker to execute arbitrary commands via a long username or password.
CVE-2000-0847(PUBLISHED):Buffer overflow in University of Washington c-client library (used by pine and other programs) allows remote attackers to execute arbitrary commands via a long X-Keywords header.
CVE-2000-0848(PUBLISHED):Buffer overflow in IBM WebSphere web application server (WAS) allows remote attackers to execute arbitrary commands via a long Host:  request header.
CVE-2000-0849(PUBLISHED):Race condition in Microsoft Windows Media server allows remote attackers to cause a denial of service in the Windows Media Unicast Service via a malformed request, aka the "Unicast Service Race Condition" vulnerability.
CVE-2000-0850(PUBLISHED):Netegrity SiteMinder before 4.11 allows remote attackers to bypass its authentication mechanism by appending "$/FILENAME.ext" (where ext is .ccc, .class, or .jpg) to the requested URL.
CVE-2000-0851(PUBLISHED):Buffer overflow in the Still Image Service in Windows 2000 allows local users to gain additional privileges via a long WM_USER message, aka the "Still Image Service Privilege Escalation" vulnerability.
CVE-2000-0852(PUBLISHED):Multiple buffer overflows in eject on FreeBSD and possibly other OSes allows local users to gain root privileges.
CVE-2000-0853(PUBLISHED):YaBB Bulletin Board 9.1.2000 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0854(PUBLISHED):When a Microsoft Office 2000 document is launched, the directory of that document is first used to locate DLL's such as riched20.dll and msi.dll, which could allow an attacker to execute arbitrary commands by inserting a Trojan Horse DLL into the same directory as the document.
CVE-2000-0855(PUBLISHED):SunFTP build 9(1) allows remote attackers to cause a denial of service by connecting to the server and disconnecting before sending a newline.
CVE-2000-0856(PUBLISHED):Buffer overflow in SunFTP build 9(1) allows remote attackers to cause a denial of service or possibly execute arbitrary commands via a long GET request.
CVE-2000-0857(PUBLISHED):The logging capability in muh 2.05d IRC server does not properly cleanse user-injected format strings, which allows remote attackers to cause a denial of service or execute arbitrary commands via a malformed nickname.
CVE-2000-0858(PUBLISHED):Vulnerability in Microsoft Windows NT 4.0 allows remote attackers to cause a denial of service in IIS by sending it a series of malformed requests which cause INETINFO.EXE to fail, aka the "Invalid URL" vulnerability.
CVE-2000-0859(PUBLISHED):The web configuration server for NTMail V5 and V6 allows remote attackers to cause a denial of service via a series of partial HTTP requests.
CVE-2000-0860(PUBLISHED):The file upload capability in PHP versions 3 and 4 allows remote attackers to read arbitrary files by setting hidden form fields whose names match the names of internal PHP script variables.
CVE-2000-0861(PUBLISHED):Mailman 1.1 allows list administrators to execute arbitrary commands via shell metacharacters in the %(listname) macro expansion.
CVE-2000-0862(PUBLISHED):Vulnerability in an administrative interface utility for Allaire Spectra 1.0.1 allows remote attackers to read and modify sensitive configuration information.
CVE-2000-0863(PUBLISHED):Buffer overflow in listmanager earlier than 2.105.1 allows local users to gain additional privileges.
CVE-2000-0864(PUBLISHED):Race condition in the creation of a Unix domain socket in GNOME esound 0.2.19 and earlier allows a local user to change the permissions of arbitrary files and directories, and gain additional privileges, via a  symlink attack.
CVE-2000-0865(PUBLISHED):Buffer overflow in dvtermtype in Tridia Double Vision 3.07.00 allows local users to gain root privileges via a long terminal type argument.
CVE-2000-0866(PUBLISHED):Interbase 6 SuperServer for Linux allows an attacker to cause a denial of service via a query containing 0 bytes.
CVE-2000-0867(PUBLISHED):Kernel logging daemon (klogd) in Linux does not properly cleanse user-injected format strings, which allows local users to gain root privileges by triggering malformed kernel messages.
CVE-2000-0868(PUBLISHED):The default configuration of Apache 1.3.12 in SuSE Linux 6.4 allows remote attackers to read source code for CGI scripts by replacing the /cgi-bin/ in the requested URL with /cgi-bin-sdb/.
CVE-2000-0869(PUBLISHED):The default configuration of Apache 1.3.12 in SuSE Linux 6.4 enables WebDAV, which allows remote attackers to list arbitrary directories via the PROPFIND HTTP request method.
CVE-2000-0870(PUBLISHED):Buffer overflow in EFTP allows remote attackers to cause a denial of service via a long string.
CVE-2000-0871(PUBLISHED):Buffer overflow in EFTP allows remote attackers to cause a denial of service by sending a string that does not contain a newline, then disconnecting from the server.
CVE-2000-0872(PUBLISHED):explorer.php in PhotoAlbum 0.9.9 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0873(PUBLISHED):netstat in AIX 4.x.x does not properly restrict access to the -Zi option, which allows local users to clear network interface statistics and possibly hide evidence of unusual network activities.
CVE-2000-0874(PUBLISHED):Eudora mail client includes the absolute path of the sender's host within a virtual card (VCF).
CVE-2000-0875(PUBLISHED):WFTPD and WFTPD Pro 2.41 RC12 allows remote attackers to cause a denial of service by sending a long string of unprintable characters.
CVE-2000-0876(PUBLISHED):WFTPD and WFTPD Pro 2.41 RC12 allows remote attackers to obtain the  full pathname of the server via a "%C" command, which generates an error message that includes the pathname.
CVE-2000-0877(PUBLISHED):mailform.pl CGI script in MailForm 2.0 allows remote attackers to read arbitrary files by specifying the file name in the XX-attach_file parameter, which MailForm then sends to the attacker.
CVE-2000-0878(PUBLISHED):The mailto CGI script allows remote attacker to execute arbitrary commands via shell metacharacters in the emailadd form field.
CVE-2000-0879(PUBLISHED):LPPlus programs dccsched, dcclpdser, dccbkst, dccshut, dcclpdshut, and dccbkstshut are installed setuid root and world executable, which allows arbitrary local users to start and stop various LPD services.
CVE-2000-0880(PUBLISHED):LPPlus creates the lpdprocess file with world-writeable permissions, which allows local users to kill arbitrary processes by specifying an alternate process ID and using the setuid dcclpdshut program to kill the process that was specified in the lpdprocess file.
CVE-2000-0881(PUBLISHED):The dccscan setuid program in LPPlus does not properly check if the user has the permissions to print the file that is specified to dccscan, which allows local users to print arbitrary files.
CVE-2000-0882(PUBLISHED):Intel Express 500 series switches allow a remote attacker to cause a denial of service via a malformed ICMP packet, which causes the CPU to crash.
CVE-2000-0883(PUBLISHED):The default configuration of mod_perl for Apache as installed on Mandrake Linux 6.1 through 7.1 sets the /perl/ directory to be browseable, which allows remote attackers to list the contents of that directory.
CVE-2000-0884(PUBLISHED):IIS 4.0 and 5.0 allows remote attackers to read documents outside of the web root, and possibly execute arbitrary commands, via malformed URLs that contain UNICODE encoded characters, aka the "Web Server Folder Traversal" vulnerability.
CVE-2000-0885(PUBLISHED):Buffer overflows in Microsoft Network Monitor (Netmon) allow remote attackers to execute arbitrary commands via a long Browser Name in a CIFS Browse Frame, a long SNMP community name, or a long username or filename in an SMB session, aka the "Netmon Protocol Parsing" vulnerability.  NOTE: It is highly likely that this candidate will be split into multiple candidates.
CVE-2000-0886(PUBLISHED):IIS 5.0 allows remote attackers to execute arbitrary commands via a malformed request for an executable file whose name is appended with operating system commands, aka the "Web Server File Request Parsing" vulnerability.
CVE-2000-0887(PUBLISHED):named in BIND 8.2 through 8.2.2-P6 allows remote attackers to cause a denial of service by making a compressed zone transfer (ZXFR) request and performing a name service query on an authoritative record that is not cached, aka the "zxfr bug."
CVE-2000-0888(PUBLISHED):named in BIND 8.2 through 8.2.2-P6 allows remote attackers to cause a denial of service by sending an SRV record to the server, aka the "srv bug."
CVE-2000-0889(PUBLISHED):Two Sun security certificates have been compromised, which could allow attackers to insert malicious code such as applets and make it appear that it is signed by Sun.
CVE-2000-0890(PUBLISHED):periodic in FreeBSD 4.1.1 and earlier, and possibly other operating systems, allows local users to overwrite arbitrary files via a symlink attack.
CVE-2000-0891(PUBLISHED):A default ECL in Lotus Notes before 5.02 allows remote attackers to execute arbitrary commands by attaching a malicious program in an email message that is automatically executed when the user opens the email.
CVE-2000-0892(PUBLISHED):Some telnet clients allow remote telnet servers to request environment variables from the client that may contain sensitive information, or remote web servers to obtain the information via a telnet: URL.
CVE-2000-0893(PUBLISHED):The presence of the Distributed GL Daemon (dgld) service on port 5232 on SGI IRIX systems allows remote attackers to identify the target host as an SGI system.
CVE-2000-0894(PUBLISHED):HTTP server on the WatchGuard SOHO firewall does not properly restrict access to administrative functions such as password resets or rebooting, which allows attackers to cause a denial of service or conduct unauthorized activities.
CVE-2000-0895(PUBLISHED):Buffer overflow in HTTP server on the WatchGuard SOHO firewall allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long GET request.
CVE-2000-0896(PUBLISHED):WatchGuard SOHO firewall allows remote attackers to cause a denial of service via a flood of fragmented IP packets, which causes the firewall to drop connections and stop forwarding packets.
CVE-2000-0897(PUBLISHED):Small HTTP Server 2.03 and earlier allows remote attackers to cause a denial of service by repeatedly requesting a URL that references a directory that does not contain an index.html file, which consumes memory that is not released after the request is completed.
CVE-2000-0898(PUBLISHED):Small HTTP Server 2.01 does not properly process Server Side Includes (SSI) tags that contain null values, which allows local users, and possibly remote attackers, to cause the server to crash by inserting the SSI into an HTML file.
CVE-2000-0899(PUBLISHED):Small HTTP Server 2.01 allows remote attackers to cause a denial of service by connecting to the server and sending out multiple GET, HEAD, or POST requests and closing the connection before the server responds to the requests.
CVE-2000-0900(PUBLISHED):Directory traversal vulnerability in ssi CGI program in thttpd 2.19 and earlier allows remote attackers to read arbitrary files via a "%2e%2e" string, a variation of the .. (dot dot) attack.
CVE-2000-0901(PUBLISHED):Format string vulnerability in screen 3.9.5 and earlier allows local users to gain root privileges via format characters in the vbell_msg initialization variable.
CVE-2000-0902(PUBLISHED):getalbum.php in PhotoAlbum before 0.9.9 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0903(PUBLISHED):Directory traversal vulnerability in Voyager web server 2.01B in the demo disks for QNX 405 allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0904(PUBLISHED):Voyager web server 2.01B in the demo disks for QNX 405 stores sensitive web client information in the .photon directory in the web document root, which allows remote attackers to obtain that information.
CVE-2000-0905(PUBLISHED):QNX Embedded Resource Manager in Voyager web server 2.01B in the demo disks for QNX 405 allows remote attackers to read sensitive system statistics information via the embedded.html web page.
CVE-2000-0906(PUBLISHED):Directory traversal vulnerability in Moreover.com cached_feed.cgi script version 4.July.00 allows remote attackers to read arbitrary files via a .. (dot dot) attack on the category or format parameters.
CVE-2000-0907(PUBLISHED):EServ 2.92 Build 2982 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via long HELO and MAIL FROM commands.
CVE-2000-0908(PUBLISHED):BrowseGate 2.80 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via long Authorization or Referer MIME headers in the HTTP request.
CVE-2000-0909(PUBLISHED):Buffer overflow in the automatic mail checking component of Pine 4.21 and earlier allows remote attackers to execute arbitrary commands via a long From: header.
CVE-2000-0910(PUBLISHED):Horde library 1.02 allows attackers to execute arbitrary commands via shell metacharacters in the "from" address.
CVE-2000-0911(PUBLISHED):IMP 2.2 and earlier allows attackers to read and delete arbitrary files by modifying the attachment_name hidden form variable, which causes IMP to send the file to the attacker as an attachment.
CVE-2000-0912(PUBLISHED):MultiHTML CGI script allows remote attackers to read arbitrary files and possibly execute arbitrary commands by specifying the file name to the "multi" parameter.
CVE-2000-0913(PUBLISHED):mod_rewrite in Apache 1.3.12 and earlier allows remote attackers to read arbitrary files if a RewriteRule directive is expanded to include a filename whose name contains a regular expression.
CVE-2000-0914(PUBLISHED):OpenBSD 2.6 and earlier allows remote attackers to cause a denial of service by flooding the server with ARP requests.
CVE-2000-0915(PUBLISHED):fingerd in FreeBSD 4.1.1 allows remote attackers to read arbitrary files by specifying the target file name instead of a regular user name.
CVE-2000-0916(PUBLISHED):FreeBSD 4.1.1 and earlier, and possibly other BSD-based OSes, uses an insufficient random number generator to generate initial TCP sequence numbers (ISN), which allows remote attackers to spoof TCP connections.
CVE-2000-0917(PUBLISHED):Format string vulnerability in use_syslog() function in LPRng 3.6.24 allows remote attackers to execute arbitrary commands.
CVE-2000-0918(PUBLISHED):Format string vulnerability in kvt in KDE 1.1.2 may allow local users to execute arbitrary commands via a DISPLAY environmental variable that contains formatting characters.
CVE-2000-0919(PUBLISHED):Directory traversal vulnerability in PHPix Photo Album 1.0.2 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0920(PUBLISHED):Directory traversal vulnerability in BOA web server 0.94.8.2 and earlier allows remote attackers to read arbitrary files via a modified .. (dot dot) attack in the GET HTTP request that uses a "%2E" instead of a "."
CVE-2000-0921(PUBLISHED):Directory traversal vulnerability in Hassan Consulting shop.cgi shopping cart program allows remote attackers to read arbitrary files via a .. (dot dot) attack on the page parameter.
CVE-2000-0922(PUBLISHED):Directory traversal vulnerability in Bytes Interactive Web Shopper shopping cart program (shopper.cgi) 2.0 and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack on the newpage parameter.
CVE-2000-0923(PUBLISHED):authenticate.cgi CGI program in Aplio PRO allows remote attackers to execute arbitrary commands via shell metacharacters in the password parameter.
CVE-2000-0924(PUBLISHED):Directory traversal vulnerability in search.cgi CGI script in Armada Master Index allows remote attackers to read arbitrary files via a .. (dot dot) attack in the "catigory" parameter.
CVE-2000-0925(PUBLISHED):The default installation of SmartWin CyberOffice Shopping Cart 2 (aka CyberShop) installs the _private directory with world readable permissions, which allows remote attackers to obtain sensitive information.
CVE-2000-0926(PUBLISHED):SmartWin CyberOffice Shopping Cart 2 (aka CyberShop) allows remote attackers to modify price information by changing the "Price" hidden form variable.
CVE-2000-0927(PUBLISHED):WQuinn QuotaAdvisor 4.1 does not properly record file sizes if they are stored in alternative data streams, which allows users to bypass quota restrictions.
CVE-2000-0928(PUBLISHED):WQuinn QuotaAdvisor 4.1 allows users to list directories and files by running a report on the targeted shares.
CVE-2000-0929(PUBLISHED):Microsoft Windows Media Player 7 allows attackers to cause a denial of service in RTF-enabled email clients via an embedded OCX control that is not closed properly, aka the "OCX Attachment" vulnerability.
CVE-2000-0930(PUBLISHED):Pegasus Mail 3.12 allows remote attackers to read arbitrary files via an embedded URL that calls the mailto: protocol with a -F switch.
CVE-2000-0931(PUBLISHED):Buffer overflow in Pegasus Mail 3.11 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long email message containing binary data.
CVE-2000-0932(PUBLISHED):MAILsweeper for SMTP 3.x does not properly handle corrupt CDA documents in a ZIP file and hangs, which allows remote attackers to cause a denial of service.
CVE-2000-0933(PUBLISHED):The Input Method Editor (IME) in the Simplified Chinese version of Windows 2000 does not disable access to privileged functionality that should normally be restricted, which allows local users to gain privileges, aka the "Simplified Chinese IME State Recognition" vulnerability.
CVE-2000-0934(PUBLISHED):Glint in Red Hat Linux 5.2 allows local users to overwrite arbitrary files and cause a denial of service via a symlink attack.
CVE-2000-0935(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 allows local users to overwrite arbitrary files via a symlink attack on the cgi.log file.
CVE-2000-0936(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 installs the cgi.log logging file with world readable permissions, which allows local users to read sensitive information such as user names and passwords.
CVE-2000-0937(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 does not log login attempts in which the username is correct but the password is wrong, which allows remote attackers to conduct brute force password guessing attacks.
CVE-2000-0938(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 supplies a different error message when a valid username is provided versus an invalid name, which allows remote attackers to identify valid users on the server.
CVE-2000-0939(PUBLISHED):Samba Web Administration Tool (SWAT) in Samba 2.0.7 allows remote attackers to cause a denial of service by repeatedly submitting a nonstandard URL in the GET HTTP request and forcing it to restart.
CVE-2000-0940(PUBLISHED):Directory traversal vulnerability in Metertek pagelog.cgi allows remote attackers to read arbitrary files via a .. (dot dot) attack on the "name" or "display" parameter.
CVE-2000-0941(PUBLISHED):Kootenay Web KW Whois 1.0 CGI program allows remote attackers to execute arbitrary commands via shell metacharacters in the "whois" parameter.
CVE-2000-0942(PUBLISHED):The CiWebHitsFile component in Microsoft Indexing Services for Windows 2000 allows remote attackers to conduct a cross site scripting (CSS) attack via a CiRestriction parameter in a .htw request, aka the "Indexing Services Cross Site Scripting" vulnerability.
CVE-2000-0943(PUBLISHED):Buffer overflow in bftp daemon (bftpd) 1.0.11 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long USER command.
CVE-2000-0944(PUBLISHED):CGI Script Center News Update 1.1 does not properly validate the original news administration password during a password change operation, which allows remote attackers to modify the password without knowing the original password.
CVE-2000-0945(PUBLISHED):The web configuration interface for Catalyst 3500 XL switches allows remote attackers to execute arbitrary commands without authentication when the enable password is not set, via a URL containing the /exec/ directory.
CVE-2000-0946(PUBLISHED):Compaq Easy Access Keyboard software 1.3 does not properly disable access to custom buttons when the screen is locked, which could allow an attacker to gain privileges or execute programs without authorization.
CVE-2000-0947(PUBLISHED):Format string vulnerability in cfd daemon in GNU CFEngine before 1.6.0a11 allows attackers to execute arbitrary commands via format characters in the CAUTH command.
CVE-2000-0948(PUBLISHED):GnoRPM before 0.95 allows local users to modify arbitrary files via a symlink attack.
CVE-2000-0949(PUBLISHED):Heap overflow in savestr function in LBNL traceroute 1.4a5 and earlier allows a local user to execute arbitrary commands via the -g option.
CVE-2000-0950(PUBLISHED):Format string vulnerability in x-gw in TIS Firewall Toolkit (FWTK) allows local users to execute arbitrary commands via a malformed display name.
CVE-2000-0951(PUBLISHED):A misconfiguration in IIS 5.0 with Index Server enabled and the Index property set allows remote attackers to list directories in the web root via a Web Distributed Authoring and Versioning (WebDAV) search.
CVE-2000-0952(PUBLISHED):global.cgi CGI program in Global 3.55 and earlier on NetBSD allows remote attackers to execute arbitrary commands via shell metacharacters.
CVE-2000-0953(PUBLISHED):Shambala Server 4.5 allows remote attackers to cause a denial of service by opening then closing a connection.
CVE-2000-0954(PUBLISHED):Shambala Server 4.5 stores passwords in plaintext, which could allow local users to obtain the passwords and compromise the server.
CVE-2000-0955(PUBLISHED):Cisco Virtual Central Office 4000 (VCO/4K) uses weak encryption to store usernames and passwords in the SNMP MIB, which allows an attacker who knows the community name to crack the password and gain privileges.
CVE-2000-0956(PUBLISHED):cyrus-sasl before 1.5.24 in Red Hat Linux 7.0 does not properly verify the authorization for a local user, which could allow the users to bypass specified access restrictions.
CVE-2000-0957(PUBLISHED):The pluggable authentication module for mysql (pam_mysql) before 0.4.7 does not properly cleanse user input when constructing SQL statements, which allows attackers to obtain plaintext passwords or hashes.
CVE-2000-0958(PUBLISHED):HotJava Browser 3.0 allows remote attackers to access the DOM of a web page by opening a javascript: URL in a named window.
CVE-2000-0959(PUBLISHED):glibc2 does not properly clear the LD_DEBUG_OUTPUT and LD_DEBUG environmental variables when a program is spawned from a setuid program, which could allow local users to overwrite files via a symlink attack.
CVE-2000-0960(PUBLISHED):The POP3 server in Netscape Messaging Server 4.15p1 generates different error messages for incorrect user names versus incorrect passwords, which allows remote attackers to determine valid users on the system and harvest email addresses for spam abuse.
CVE-2000-0961(PUBLISHED):Buffer overflow in IMAP server in Netscape Messaging Server 4.15 Patch 2 allows local users to execute arbitrary commands via a long LIST command.
CVE-2000-0962(PUBLISHED):The IPSEC implementation in OpenBSD 2.7 does not properly handle empty AH/ESP packets, which allows remote attackers to cause a denial of service.
CVE-2000-0963(PUBLISHED):Buffer overflow in ncurses library allows local users to execute arbitrary commands via long environmental information such as TERM or TERMINFO_DIRS.
CVE-2000-0964(PUBLISHED):Buffer overflow in the web administration service for the HiNet LP5100 IP-phone allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long GET request.
CVE-2000-0965(PUBLISHED):The NSAPI plugins for TGA and the Java Servlet proxy in HP-UX VVOS 10.24 and 11.04 allows an attacker to cause a denial of service (high CPU utilization).
CVE-2000-0966(PUBLISHED):Buffer overflows in lpspooler in the fileset PrinterMgmt.LP-SPOOL of HP-UX 11.0 and earlier allows local users to gain privileges.
CVE-2000-0967(PUBLISHED):PHP 3 and 4 do not properly cleanse user-injected format strings, which allows remote attackers to execute arbitrary commands by triggering error messages that are improperly written to the error logs.
CVE-2000-0968(PUBLISHED):Buffer overflow in Half Life dedicated server before build 3104 allows remote attackers to execute arbitrary commands via a long rcon command.
CVE-2000-0969(PUBLISHED):Format string vulnerability in Half Life dedicated server build 3104 and earlier allows remote attackers to execute arbitrary commands by injecting format strings into the changelevel command, via the system console or rcon.
CVE-2000-0970(PUBLISHED):IIS 4.0 and 5.0 .ASP pages send the same Session ID cookie for secure and insecure web sessions, which could allow remote attackers to hijack the secure web session of the user if that user moves to an insecure session, aka the "Session ID Cookie Marking" vulnerability.
CVE-2000-0971(PUBLISHED):Avirt Mail 4.0 and 4.2 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long "RCPT TO" or "MAIL FROM" command.
CVE-2000-0972(PUBLISHED):HP-UX 11.00 crontab allows local users to read arbitrary files via the -e option by creating a symlink to the target file during the crontab session, quitting the session, and reading the error messages that crontab generates.
CVE-2000-0973(PUBLISHED):Buffer overflow in curl earlier than 6.0-1.1, and curl-ssl earlier than 6.0-1.2, allows remote attackers to execute arbitrary commands by forcing a long error message to be generated.
CVE-2000-0974(PUBLISHED):GnuPG (gpg) 1.0.3 does not properly check all signatures of a file containing multiple documents, which allows an attacker to modify contents of all documents but the first without detection.
CVE-2000-0975(PUBLISHED):Directory traversal vulnerability in apexec.pl in Anaconda Foundation Directory allows remote attackers to read arbitrary files via a .. (dot dot) attack.
CVE-2000-0976(PUBLISHED):Buffer overflow in xlib in XFree 3.3.x possibly allows local users to execute arbitrary commands via a long DISPLAY environment variable or a -display command line parameter.
CVE-2000-0977(PUBLISHED):mailfile.cgi CGI program in MailFile 1.10 allows remote attackers to read arbitrary files by specifying the target file name in the "filename" parameter in a POST request, which is then sent by email to the address specified in the "email" parameter.
CVE-2000-0978(PUBLISHED):bbd server in Big Brother System and Network Monitor before 1.5c2 allows remote attackers to execute arbitrary commands via the "&" shell metacharacter.
CVE-2000-0979(PUBLISHED):File and Print Sharing service in Windows 95, Windows 98, and Windows Me does not properly check the password for a file share, which allows remote attackers to bypass share access controls by sending a 1-byte password that matches the first character of the real password, aka the "Share Level Password" vulnerability.
CVE-2000-0980(PUBLISHED):NMPI (Name Management Protocol on IPX) listener in Microsoft NWLink does not properly filter packets from a broadcast address, which allows remote attackers to cause a broadcast storm and flood the network.
CVE-2000-0981(PUBLISHED):MySQL Database Engine uses a weak authentication method which leaks information that could be used by a remote attacker to recover the password.
CVE-2000-0982(PUBLISHED):Internet Explorer before 5.5 forwards cached user credentials for a secure web site to insecure pages on the same web site, which could allow remote attackers to obtain the credentials by monitoring connections to the web server, aka the "Cached Web Credentials" vulnerability.
CVE-2000-0983(PUBLISHED):Microsoft NetMeeting with Remote Desktop Sharing enabled allows remote attackers to cause a denial of service (CPU utilization) via a sequence of null bytes to the NetMeeting port, aka the "NetMeeting Desktop Sharing" vulnerability.
CVE-2000-0984(PUBLISHED):The HTTP server in Cisco IOS 12.0 through 12.1 allows local users to cause a denial of service (crash and reload) via a URL containing a "?/" string.
CVE-2000-0985(PUBLISHED):Buffer overflow in All-Mail 1.1 allows remote attackers to execute arbitrary commands via a long "MAIL FROM" or "RCPT TO" command.
CVE-2000-0986(PUBLISHED):Buffer overflow in Oracle 8.1.5 applications such as names, namesctl, onrsd, osslogin, tnslsnr, tnsping, trcasst, and trcroute possibly allow local users to gain privileges via a long ORACLE_HOME environmental variable.
CVE-2000-0987(PUBLISHED):Buffer overflow in oidldapd in Oracle 8.1.6 allow local users to gain privileges via a long "connect" command line parameter.
CVE-2000-0988(PUBLISHED):WinU 1.0 through 5.1 has a backdoor password that allows remote attackers to gain access to its administrative interface and modify configuration.
CVE-2000-0989(PUBLISHED):Buffer overflow in Intel InBusiness eMail Station 1.04.87 POP service allows remote attackers to cause a denial of service and possibly execute commands via a long username.
CVE-2000-0990(PUBLISHED):cmd5checkpw 0.21 and earlier allows remote attackers to cause a denial of service via an "SMTP AUTH" command with an unknown username.
CVE-2000-0991(PUBLISHED):Buffer overflow in Hilgraeve, Inc. HyperTerminal client on Windows 98, ME, and 2000 allows remote attackers to execute arbitrary commands via a long telnet URL, aka the "HyperTerminal Buffer Overflow" vulnerability.
CVE-2000-0992(PUBLISHED):Directory traversal vulnerability in scp in sshd 1.2.xx allows a remote malicious scp server to overwrite arbitrary files via a .. (dot dot) attack.
CVE-2000-0993(PUBLISHED):Format string vulnerability in pw_error function in BSD libutil library allows local users to gain root privileges via a malformed password in commands such as chpass or passwd.
CVE-2000-0994(PUBLISHED):Format string vulnerability in OpenBSD fstat program (and possibly other BSD-based operating systems) allows local users to gain root privileges via the PWD environmental variable.
CVE-2000-0995(PUBLISHED):Format string vulnerability in OpenBSD yp_passwd program (and possibly other BSD-based operating systems) allows attackers to gain root privileges a malformed name.
CVE-2000-0996(PUBLISHED):Format string vulnerability in OpenBSD su program (and possibly other BSD-based operating systems) allows local attackers to gain root privileges via a malformed shell.
CVE-2000-0997(PUBLISHED):Format string vulnerabilities in eeprom program in OpenBSD, NetBSD, and possibly other operating systems allows local attackers to gain root privileges.
CVE-2000-0998(PUBLISHED):Format string vulnerability in top program allows local attackers to gain root privileges via the "kill" or "renice" function.
CVE-2000-0999(PUBLISHED):Format string vulnerabilities in OpenBSD ssh program (and possibly other BSD-based operating systems) allow attackers to gain root privileges.
CVE-2000-1000(PUBLISHED):Format string vulnerability in AOL Instant Messenger (AIM) 4.1.2010 allows remote attackers to cause a denial of service and possibly execute arbitrary commands by transferring a file whose name includes format characters.
CVE-2000-1001(PUBLISHED):add_2_basket.asp in Element InstantShop allows remote attackers to modify price information via the "price" hidden form variable.
CVE-2000-1002(PUBLISHED):POP3 daemon in Stalker CommuniGate Pro 3.3.2 generates different error messages for invalid usernames versus invalid passwords, which allows remote attackers to determine valid email addresses on the server for SPAM attacks.
CVE-2000-1003(PUBLISHED):NETBIOS client in Windows 95 and Windows 98 allows a remote attacker to cause a denial of service by changing a file sharing service to return an unknown driver type, which causes the client to crash.
CVE-2000-1004(PUBLISHED):Format string vulnerability in OpenBSD photurisd allows local users to execute arbitrary commands via a configuration file directory name that contains formatting characters.
CVE-2000-1005(PUBLISHED):Directory traversal vulnerability in html_web_store.cgi and web_store.cgi CGI programs in eXtropia WebStore allows remote attackers to read arbitrary files via a .. (dot dot) attack on the page parameter.
CVE-2000-1006(PUBLISHED):Microsoft Exchange Server 5.5 does not properly handle a MIME header with a blank charset specified, which allows remote attackers to cause a denial of service via a charset="" command, aka the "Malformed MIME Header" vulnerability.
CVE-2000-1007(PUBLISHED):I-gear 3.5.7 and earlier does not properly process log entries in which a URL is longer than 255 characters, which allows an attacker to cause reporting errors.
CVE-2000-1008(PUBLISHED):PalmOS 3.5.2 and earlier uses weak encryption to store the user password, which allows attackers with physical access to the Palm device to decrypt the password and gain access to the device.
CVE-2000-1009(PUBLISHED):dump in Red Hat Linux 6.2 trusts the pathname specified by the RSH environmental variable, which allows local users to obtain root privileges by modifying the RSH variable to point to a Trojan horse program.
CVE-2000-1010(PUBLISHED):Format string vulnerability in talkd in OpenBSD and possibly other BSD-based OSes allows remote attackers to execute arbitrary commands via a user name that contains format characters.
CVE-2000-1011(PUBLISHED):Buffer overflow in catopen() function in FreeBSD 5.0 and earlier, and possibly other OSes, allows local users to gain root privileges via a long environmental variable.
CVE-2000-1012(PUBLISHED):The catopen function in FreeBSD 5.0 and earlier, and possibly other OSes, allows local users to read arbitrary files via the LANG environmental variable.
CVE-2000-1013(PUBLISHED):The setlocale function in FreeBSD 5.0 and earlier, and possibly other OSes, allows local users to read arbitrary files via the LANG environmental variable.
CVE-2000-1014(PUBLISHED):Format string vulnerability in the search97.cgi CGI script in SCO help http server for Unixware 7 allows remote attackers to execute arbitrary commands via format characters in the queryText parameter.
CVE-2000-1015(PUBLISHED):The default configuration of Slashcode before version 2.0 Alpha has a default administrative password, which allows remote attackers to gain Slashcode privileges and possibly execute arbitrary commands.
CVE-2000-1016(PUBLISHED):The default configuration of Apache (httpd.conf) on SuSE 6.4 includes an alias for the /usr/doc directory, which allows remote attackers to read package documentation and obtain system configuration information via an HTTP request for the /doc/packages URL.
CVE-2000-1017(PUBLISHED):Webteachers Webdata allows remote attackers with valid Webdata accounts to read arbitrary files by posting a request to import the file into the WebData database.
CVE-2000-1018(PUBLISHED):shred 1.0 file wiping utility does not properly open a file for overwriting or flush its buffers, which prevents shred from properly replacing the file's data and allows local users to recover the file.
CVE-2000-1019(PUBLISHED):Search engine in Ultraseek 3.1 and 3.1.10 (aka Inktomi Search) allows remote attackers to cause a denial of service via a malformed URL.
CVE-2000-1020(PUBLISHED):Heap overflow in Worldclient in Mdaemon 3.1.1 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long URL.
CVE-2000-1021(PUBLISHED):Heap overflow in WebConfig in Mdaemon 3.1.1 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long URL.
CVE-2000-1022(PUBLISHED):The mailguard feature in Cisco Secure PIX Firewall 5.2(2) and earlier does not properly restrict access to SMTP commands, which allows remote attackers to execute restricted commands by sending a DATA command before sending the restricted commands.
CVE-2000-1023(PUBLISHED):The Alabanza Control Panel does not require passwords to access administrative commands, which allows remote attackers to modify domain name information via the nsManager.cgi CGI program.
CVE-2000-1024(PUBLISHED):eWave ServletExec 3.0C and earlier does not restrict access to the UploadServlet Java/JSP servlet, which allows remote attackers to upload files and execute arbitrary commands.
CVE-2000-1025(PUBLISHED):eWave ServletExec JSP/Java servlet engine, versions 3.0C and earlier, allows remote attackers to cause a denial of service via a URL that contains the "/servlet/" string, which invokes the ServletExec servlet and causes an exception if the servlet is already running.
CVE-2000-1026(PUBLISHED):Multiple buffer overflows in LBNL tcpdump allow remote attackers to execute arbitrary commands.
CVE-2000-1027(PUBLISHED):Cisco Secure PIX Firewall 5.2(2) allows remote attackers to determine the real IP address of a target FTP server by flooding the server with PASV requests, which includes the real IP address in the response when passive mode is established.
CVE-2000-1028(PUBLISHED):Buffer overflow in cu program in HP-UX 11.0 may allow local users to gain privileges via a long -l command line argument.
CVE-2000-1029(PUBLISHED):Buffer overflow in host command allows a remote attacker to execute arbitrary commands via a long response to an AXFR query.
CVE-2000-1030(PUBLISHED):CS&T CorporateTime for the Web returns different error messages for invalid usernames and invalid passwords, which allows remote attackers to determine valid usernames on the server.
CVE-2000-1031(PUBLISHED):Buffer overflow in dtterm in HP-UX 11.0 and HP Tru64 UNIX 4.0f through 5.1a allows local users to execute arbitrary code via a long -tn option.
CVE-2000-1032(PUBLISHED):The client authentication interface for Check Point Firewall-1 4.0 and earlier generates different error messages for invalid usernames versus invalid passwords, which allows remote attackers to identify valid usernames on the firewall.
CVE-2000-1033(PUBLISHED):Serv-U FTP Server allows remote attackers to bypass its anti-hammering feature by first logging on as a valid user (possibly anonymous) and then attempting to guess the passwords of other users.
CVE-2000-1034(PUBLISHED):Buffer overflow in the System Monitor ActiveX control in Windows 2000 allows remote attackers to execute arbitrary commands via a long LogFileName parameter in HTML source code, aka the "ActiveX Parameter Validation" vulnerability.
CVE-2000-1035(PUBLISHED):Buffer overflows in TYPSoft FTP Server 0.78 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long USER, PASS, or CWD command.
CVE-2000-1036(PUBLISHED):Directory traversal vulnerability in Extent RBS ISP web server allows remote attackers to read sensitive information via a .. (dot dot) attack on the Image parameter.
CVE-2000-1037(PUBLISHED):Check Point Firewall-1 session agent 3.0 through 4.1 generates different error messages for invalid user names versus invalid passwords, which allows remote attackers to determine valid usernames and guess a password via a brute force attack.
CVE-2000-1038(PUBLISHED):The web administration interface for IBM AS/400 Firewall allows remote attackers to cause a denial of service via an empty GET request.
CVE-2000-1039(PUBLISHED):Various TCP/IP stacks and network applications allow remote attackers to cause a denial of service by flooding a target host with TCP connection attempts and completing the TCP/IP handshake without maintaining the connection state on the attacker host, aka the "NAPTHA" class of vulnerabilities.  NOTE: this candidate may change significantly as the security community discusses the technical nature of NAPTHA and learns more about the affected applications. This candidate is at a higher level of abstraction than is typical for CVE.
CVE-2000-1040(PUBLISHED):Format string vulnerability in logging function of ypbind 3.3, while running in debug mode, leaks file descriptors and allows an attacker to cause a denial of service.
CVE-2000-1041(PUBLISHED):Buffer overflow in ypbind 3.3 possibly allows an attacker to gain root privileges.
CVE-2000-1042(PUBLISHED):Buffer overflow in ypserv in Mandrake Linux 7.1 and earlier, and possibly other Linux operating systems, allows an attacker to gain root privileges when ypserv is built without a vsyslog() function.
CVE-2000-1043(PUBLISHED):Format string vulnerability in ypserv in Mandrake Linux 7.1 and earlier, and possibly other Linux operating systems, allows an attacker to gain root privileges when ypserv is built without a vsyslog() function.
CVE-2000-1044(PUBLISHED):Format string vulnerability in ypbind-mt in SuSE SuSE-6.2, and possibly other Linux operating systems, allows an attacker to gain root privileges.
CVE-2000-1045(PUBLISHED):nss_ldap earlier than 121, when run with nscd (name service caching daemon), allows remote attackers to cause a denial of service via a flood of LDAP requests.
CVE-2000-1046(PUBLISHED):Multiple buffer overflows in the ESMTP service of Lotus Domino 5.0.2c and earlier allow remote attackers to cause a denial of service and possibly execute arbitrary code via long (1) "RCPT TO," (2) "SAML FROM," or (3) "SOML FROM" commands.
CVE-2000-1047(PUBLISHED):Buffer overflow in SMTP service of Lotus Domino 5.0.4 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long ENVID keyword in the "MAIL FROM" command.
CVE-2000-1048(PUBLISHED):Directory traversal vulnerability in the logfile service of Wingate 4.1 Beta A and earlier allows remote attackers to read arbitrary files via a .. (dot dot) attack via an HTTP GET request that uses encoded characters in the URL.
CVE-2000-1049(PUBLISHED):Allaire JRun 3.0 http servlet server allows remote attackers to cause a denial of service via a URL that contains a long string of "." characters.
CVE-2000-1050(PUBLISHED):Allaire JRun 3.0 http servlet server allows remote attackers to directly access the WEB-INF directory via a URL request that contains an extra "/" in the beginning of the request (aka the "extra leading slash").
CVE-2000-1051(PUBLISHED):Directory traversal vulnerability in Allaire JRun 2.3 server allows remote attackers to read arbitrary files via the SSIFilter servlet.
CVE-2000-1052(PUBLISHED):Allaire JRun 2.3 server allows remote attackers to obtain source code for executable content by directly calling the SSIFilter servlet.
CVE-2000-1053(PUBLISHED):Allaire JRun 2.3.3 server allows remote attackers to compile and execute JSP code by inserting it via a cross-site scripting (CSS) attack and directly calling the com.livesoftware.jrun.plugins.JSP JSP servlet.
CVE-2000-1054(PUBLISHED):Buffer overflow in CSAdmin module in CiscoSecure ACS Server 2.4(2) and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a large packet.
CVE-2000-1055(PUBLISHED):Buffer overflow in CiscoSecure ACS Server 2.4(2) and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a large TACACS+ packet.
CVE-2000-1056(PUBLISHED):CiscoSecure ACS Server 2.4(2) and earlier allows remote attackers to bypass LDAP authentication on the server if the LDAP server allows null passwords.
CVE-2000-1057(PUBLISHED):Vulnerabilities in database configuration scripts in HP OpenView Network Node Manager (NNM) 6.1 and earlier allows local users to gain privileges, possibly via insecure permissions.
CVE-2000-1058(PUBLISHED):Buffer overflow in OverView5 CGI program in HP OpenView Network Node Manager (NNM) 6.1 and earlier allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, in the SNMP service (snmp.exe), aka the "Java SNMP MIB Browser Object ID parsing problem."
CVE-2000-1059(PUBLISHED):The default configuration of the Xsession file in Mandrake Linux 7.1 and 7.0 bypasses the Xauthority access control mechanism with an "xhost + localhost" command, which allows local users to sniff X Windows events and gain privileges.
CVE-2000-1060(PUBLISHED):The default configuration of XFCE 3.5.1 bypasses the Xauthority access control mechanism with an "xhost + localhost" command in the xinitrc program, which allows local users to sniff X Windows traffic and gain privileges.
CVE-2000-1061(PUBLISHED):Microsoft Virtual Machine (VM) in Internet Explorer 4.x and 5.x allows an unsigned applet to create and use ActiveX controls, which allows a remote attacker to bypass Internet Explorer's security settings and execute arbitrary commands via a malicious web page or email, aka the "Microsoft VM ActiveX Component" vulnerability.
CVE-2000-1062(PUBLISHED):Buffer overflow in the FTP service in HP JetDirect printer card Firmware x.08.20 and earlier allows remote attackers to cause a denial of service.
CVE-2000-1063(PUBLISHED):Buffer overflow in the Telnet service in HP JetDirect printer card Firmware x.08.20 and earlier allows remote attackers to cause a denial of service.
CVE-2000-1064(PUBLISHED):Buffer overflow in the LPD service in HP JetDirect printer card Firmware x.08.20 and earlier allows remote attackers to cause a denial of service.
CVE-2000-1065(PUBLISHED):Vulnerability in IP implementation of HP JetDirect printer card Firmware x.08.20 and earlier allows remote attackers to cause a denial of service (printer crash) via a malformed packet.
CVE-2000-1066(PUBLISHED):The getnameinfo function in FreeBSD 4.1.1 and earlier, and possibly other operating systems, allows a remote attacker to cause a denial of service via a long DNS hostname.
CVE-2000-1068(PUBLISHED):pollit.cgi in Poll It 2.0 allows remote attackers to execute arbitrary commands via shell metacharacters in the poll_options parameter.
CVE-2000-1069(PUBLISHED):pollit.cgi in Poll It 2.01 and earlier allows remote attackers to access administrative functions without knowing the real password by specifying the same value to the entered_password and admin_password parameters.
CVE-2000-1070(PUBLISHED):pollit.cgi in Poll It 2.01 and earlier uses data files that are located under the web document root, which allows remote attackers to access sensitive or private information.
CVE-2000-1071(PUBLISHED):The GUI installation for iCal 2.1 Patch 2 disables access control for the X server using an "xhost +" command, which allows remote attackers to monitor X Windows events and gain privileges.
CVE-2000-1072(PUBLISHED):iCal 2.1 Patch 2 installs many files with world-writeable permissions, which allows local users to modify the iCal configuration and execute arbitrary commands by replacing the iplncal.sh program with a Trojan horse.
CVE-2000-1073(PUBLISHED):csstart program in iCal 2.1 Patch 2 searches for the cshttpd program in the current working directory, which allows local users to gain root privileges by creating a Trojan Horse cshttpd program in a directory and calling csstart from that directory.
CVE-2000-1074(PUBLISHED):csstart program in iCal 2.1 Patch 2 uses relative pathnames to install the libsocket and libnsl libraries, which could allow the icsuser account to gain root privileges by creating a Trojan Horse library in the current or parent directory.
CVE-2000-1075(PUBLISHED):Directory traversal vulnerability in iPlanet Certificate Management System 4.2 and Directory Server 4.12 allows remote attackers to read arbitrary files via a .. (dot dot) attack in the Agent, End Entity, or Administrator services.
CVE-2000-1076(PUBLISHED):Netscape (iPlanet) Certificate Management System 4.2 and Directory Server 4.12 stores the administrative password in plaintext, which could allow local and possibly remote attackers to gain administrative privileges on the server.
CVE-2000-1077(PUBLISHED):Buffer overflow in the SHTML logging functionality of iPlanet Web Server 4.x allows remote attackers to execute arbitrary commands via a long filename with a .shtml extension.
CVE-2000-1078(PUBLISHED):ICQ Web Front HTTPd allows remote attackers to cause a denial of service by requesting a URL that contains a "?" character.
CVE-2000-1079(PUBLISHED):Interactions between the CIFS Browser Protocol and NetBIOS as implemented in Microsoft Windows 95, 98, NT, and 2000 allow remote attackers to modify dynamic NetBIOS name cache entries via a spoofed Browse Frame Request in a unicast or UDP broadcast datagram.
CVE-2000-1080(PUBLISHED):Quake 1 (quake1) and ProQuake 1.01 and earlier allow remote attackers to cause a denial of service via a malformed (empty) UDP packet.
CVE-2000-1081(PUBLISHED):The xp_displayparamstmt function in SQL Server and Microsoft SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the "Extended Stored Procedure Parameter Parsing" vulnerability.
CVE-2000-1082(PUBLISHED):The xp_enumresultset function in SQL Server and Microsoft SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the "Extended Stored Procedure Parameter Parsing" vulnerability.
CVE-2000-1083(PUBLISHED):The xp_showcolv function in SQL Server and Microsoft SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the "Extended Stored Procedure Parameter Parsing" vulnerability.
CVE-2000-1084(PUBLISHED):The xp_updatecolvbm function in SQL Server and Microsoft SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the "Extended Stored Procedure Parameter Parsing" vulnerability.
CVE-2000-1085(PUBLISHED):The xp_peekqueue function in Microsoft SQL Server 2000 and SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the "Extended Stored Procedure Parameter Parsing" vulnerability.
CVE-2000-1086(PUBLISHED):The xp_printstatements function in Microsoft SQL Server 2000 and SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the "Extended Stored Procedure Parameter Parsing" vulnerability.
CVE-2000-1087(PUBLISHED):The xp_proxiedmetadata function in Microsoft SQL Server 2000 and SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the "Extended Stored Procedure Parameter Parsing" vulnerability.
CVE-2000-1088(PUBLISHED):The xp_SetSQLSecurity function in Microsoft SQL Server 2000 and SQL Server Desktop Engine (MSDE) does not properly restrict the length of a buffer before calling the srv_paraminfo function in the SQL Server API for Extended Stored Procedures (XP), which allows an attacker to cause a denial of service or execute arbitrary commands, aka the "Extended Stored Procedure Parameter Parsing" vulnerability.
CVE-2000-1089(PUBLISHED):Buffer overflow in Microsoft Phone Book Service allows local users to execute arbitrary commands, aka the "Phone Book Service Buffer Overflow" vulnerability.
CVE-2000-1090(PUBLISHED):Microsoft IIS for Far East editions 4.0 and 5.0 allows remote attackers to read source code for parsed pages via a malformed URL that uses the lead-byte of a double-byte character.
CVE-2000-1092(PUBLISHED):loadpage.cgi CGI program in EZshopper 3.0 and 2.0 allows remote attackers to list and read files in the EZshopper data directory by inserting a "/" in front of the target filename in the "file" parameter.
CVE-2000-1093(PUBLISHED):Buffer overflow in AOL Instant Messenger before 4.3.2229 allows remote attackers to execute arbitrary commands via a long "goim" command.
CVE-2000-1094(PUBLISHED):Buffer overflow in AOL Instant Messenger (AIM) before 4.3.2229 allows remote attackers to execute arbitrary commands via a "buddyicon" command with a long "src" argument.
CVE-2000-1095(PUBLISHED):modprobe in the modutils 2.3.x package on Linux systems allows a local user to execute arbitrary commands via shell metacharacters.
CVE-2000-1096(PUBLISHED):crontab by Paul Vixie uses predictable file names for a temporary file and does not properly ensure that the file is owned by the user executing the crontab -e command, which allows local users with write access to the crontab spool directory to execute arbitrary commands by creating world-writeable temporary files and modifying them while the victim is editing the file.
CVE-2000-1097(PUBLISHED):The web server for the SonicWALL SOHO firewall allows remote attackers to cause a denial of service via a long username in the authentication page.
CVE-2000-1098(PUBLISHED):The web server for the SonicWALL SOHO firewall allows remote attackers to cause a denial of service via an empty GET or POST request.
CVE-2000-1099(PUBLISHED):Java Runtime Environment in Java Development Kit (JDK) 1.2.2_05 and earlier can allow an untrusted Java class to call into a disallowed class, which could allow an attacker to escape the Java sandbox and conduct unauthorized activities.
CVE-2000-1100(PUBLISHED):The default configuration for PostACI webmail system installs the /includes/global.inc configuration file within the web root, which allows remote attackers to read sensitive information such as database usernames and passwords via a direct HTTP GET request.
CVE-2000-1101(PUBLISHED):Directory traversal vulnerability in Winsock FTPd (WFTPD) 3.00 and 2.41 with the "Restrict to home directory" option enabled allows local users to escape the home directory via a "/../" string, a variation of the .. (dot dot) attack.
CVE-2000-1102(PUBLISHED):PTlink IRCD 3.5.3 and PTlink Services 1.8.1 allow remote attackers to cause a denial of service (server crash) via "mode +owgscfxeb" and "oper" commands.
CVE-2000-1103(PUBLISHED):rcvtty in BSD 3.0 and 4.0 does not properly drop privileges before executing a script, which allows local attackers to gain privileges by specifying an alternate Trojan horse script on the command line.
CVE-2000-1104(PUBLISHED):Variant of the "IIS Cross-Site Scripting" vulnerability as originally discussed in MS:MS00-060 (CVE-2000-0746) allows a malicious web site operator to embed scripts in a link to a trusted site, which are returned without quoting in an error message back to the client.  The client then executes those scripts in the same context as the trusted site.
CVE-2000-1105(PUBLISHED):The ixsso.query ActiveX Object is marked as safe for scripting, which allows malicious web site operators to embed a script that remotely determines the existence of files on visiting Windows 2000 systems that have Indexing Services enabled.
CVE-2000-1106(PUBLISHED):Trend Micro InterScan VirusWall creates an "Intscan" share to the "InterScan" directory with permissions that grant Full Control permissions to the Everyone group, which allows attackers to gain privileges by modifying the VirusWall programs.
CVE-2000-1107(PUBLISHED):in.identd ident server in SuSE Linux 6.x and 7.0 allows remote attackers to cause a denial of service via a long request, which causes the server to access a NULL pointer and crash.
CVE-2000-1108(PUBLISHED):cons.saver in Midnight Commander (mc) 4.5.42 and earlier does not properly verify if an output file descriptor is a TTY, which allows local users to corrupt files by creating a symbolic link to the target file, calling mc, and specifying that link as a TTY argument.
CVE-2000-1109(PUBLISHED):Midnight Commander (mc) 4.5.51 and earlier does not properly process malformed directory names when a user opens a directory, which allows other local users to gain privileges by creating directories that contain special characters followed by the commands to be executed.
CVE-2000-1110(PUBLISHED):document.d2w CGI program in the IBM Net.Data db2www package allows remote attackers to determine the physical path of the web server by sending a nonexistent command to the program.
CVE-2000-1111(PUBLISHED):Telnet Service for Windows 2000 Professional does not properly terminate incomplete connection attempts, which allows remote attackers to cause a denial of service by connecting to the server and not providing any input.
CVE-2000-1112(PUBLISHED):Microsoft Windows Media Player 7 executes scripts in custom skin (.WMS) files, which could allow remote attackers to gain privileges via a skin that contains a malicious script, aka the ".WMS Script Execution" vulnerability.
CVE-2000-1113(PUBLISHED):Buffer overflow in Microsoft Windows Media Player allows remote attackers to execute arbitrary commands via a malformed Active Stream Redirector (.ASX) file, aka the ".ASX Buffer Overrun" vulnerability.
CVE-2000-1114(PUBLISHED):Unify ServletExec AS v3.0C allows remote attackers to read source code for JSP pages via an HTTP request that ends with characters such as ".", or "+", or "%20".
CVE-2000-1115(PUBLISHED):Buffer overflow in remote web administration component (webprox.dll) of 602Pro LAN SUITE before 2000.0.1.33 allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long GET request.
CVE-2000-1116(PUBLISHED):Buffer overflow in TransSoft Broker FTP Server before ******* allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long command.
CVE-2000-1117(PUBLISHED):The Extended Control List (ECL) feature of the Java Virtual Machine (JVM) in Lotus Notes Client R5 allows malicious web site operators to determine the existence of files on the client by measuring delays in the execution of the getSystemResource method.
CVE-2000-1118(PUBLISHED):24Link 1.06 web server allows remote attackers to bypass access restrictions by prepending strings such as "/+/" or "/." to the HTTP GET request.
CVE-2000-1119(PUBLISHED):Buffer overflow in setsenv command in IBM AIX 4.3.x and earlier allows local users to execute arbitrary commands via a long "x=" argument.
CVE-2000-1120(PUBLISHED):Buffer overflow in digest command in IBM AIX 4.3.x and earlier allows local users to execute arbitrary commands.
CVE-2000-1121(PUBLISHED):Buffer overflow in enq command in IBM AIX 4.3.x and earlier may allow local users to execute arbitrary commands via a long -M argument.
CVE-2000-1122(PUBLISHED):Buffer overflow in setclock command in IBM AIX 4.3.x and earlier may allow local users to execute arbitrary commands via a long argument.
CVE-2000-1123(PUBLISHED):Buffer overflow in pioout command in IBM AIX 4.3.x and earlier may allow local users to execute arbitrary commands.
CVE-2000-1124(PUBLISHED):Buffer overflow in piobe command in IBM AIX 4.3.x allows local users to gain privileges via long environmental variables.
CVE-2000-1125(PUBLISHED):restore 0.4b15 and earlier in Red Hat Linux 6.2 trusts the pathname specified by the RSH environmental variable, which allows local users to obtain root privileges by modifying the RSH variable to point to a Trojan horse program.
CVE-2000-1126(PUBLISHED):Vulnerability in auto_parms and set_parms in HP-UX 11.00 and earlier allows remote attackers to execute arbitrary commands or cause a denial of service.
CVE-2000-1127(PUBLISHED):registrar in the HP resource monitor service allows local users to read and modify arbitrary files by renaming the original registrar.log log file and creating a symbolic link to the target file, to which registrar appends log information and sets the permissions to be world readable.
CVE-2000-1128(PUBLISHED):The default configuration of McAfee VirusScan 4.5 does not quote the ImagePath variable, which improperly sets the search path and allows local users to place a Trojan horse "common.exe" program in the C:\Program Files directory.
CVE-2000-1129(PUBLISHED):McAfee WebShield SMTP 4.5 allows remote attackers to cause a denial of service via a malformed recipient field.
CVE-2000-1130(PUBLISHED):McAfee WebShield SMTP 4.5 allows remote attackers to bypass email content filtering rules by including Extended ASCII characters in name of the attachment.
CVE-2000-1131(PUBLISHED):Bill Kendrick web site guestbook (GBook) allows remote attackers to execute arbitrary commands via shell metacharacters in the _MAILTO form variable.
CVE-2000-1132(PUBLISHED):DCForum cgforum.cgi CGI script allows remote attackers to read arbitrary files, and delete the program itself, via a malformed "forum" variable.
CVE-2000-1133(PUBLISHED):Authentix Authentix100 allows remote attackers to bypass authentication by inserting a . (dot) into the URL for a protected directory.
CVE-2000-1134(PUBLISHED):Multiple shell programs on various Unix systems, including (1) tcsh, (2) csh, (3) sh, and (4) bash, follow symlinks when processing << redirects (aka here-documents or in-here documents), which allows local users to overwrite files of other users via a symlink attack.
CVE-2000-1135(PUBLISHED):fshd (fsh daemon) in Debian GNU/Linux allows local users to overwrite files of other users via a symlink attack.
CVE-2000-1136(PUBLISHED):elvis-tiny before 1.4-10 in Debian GNU/Linux, and possibly other Linux operating systems, allows local users to overwrite files of other users via a symlink attack.
CVE-2000-1137(PUBLISHED):GNU ed before 0.2-18.1 allows local users to overwrite the files of other users via a symlink attack.
CVE-2000-1138(PUBLISHED):Lotus Notes R5 client R5.0.5 and earlier does not properly warn users when an S/MIME email message has been modified, which could allow an attacker to modify the email in transit without being detected.
CVE-2000-1139(PUBLISHED):The installation of Microsoft Exchange 2000 before Rev. A creates a user account with a known password, which could allow attackers to gain privileges, aka the "Exchange User Account" vulnerability.
CVE-2000-1140(PUBLISHED):Recourse ManTrap 1.6 does not properly hide processes from attackers, which could allow attackers to determine that they are in a honeypot system by comparing the results from kill commands with the process listing in the /proc filesystem.
CVE-2000-1141(PUBLISHED):Recourse ManTrap 1.6 modifies the kernel so that ".." does not appear in the /proc listing, which allows attackers to determine that they are in a honeypot system.
CVE-2000-1142(PUBLISHED):Recourse ManTrap 1.6 generates an error when an attacker cd's to /proc/self/cwd and executes the pwd command, which allows attackers to determine that they are in a honeypot system.
CVE-2000-1143(PUBLISHED):Recourse ManTrap 1.6 hides the first 4 processes that run on a Solaris system, which allows attackers to determine that they are in a honeypot system.
CVE-2000-1144(PUBLISHED):Recourse ManTrap 1.6 sets up a chroot environment to hide the fact that it is running, but the inode number for the resulting "/" file system is higher than normal, which allows attackers to determine that they are in a chroot environment.
CVE-2000-1145(PUBLISHED):Recourse ManTrap 1.6 allows attackers who have gained root access to use utilities such as crash or fsdb to read /dev/mem and raw disk devices to identify ManTrap processes or modify arbitrary data files.
CVE-2000-1146(PUBLISHED):Recourse ManTrap 1.6 allows attackers to cause a denial of service via a sequence of commands that navigate into and out of the /proc/self directory and executing various commands such as ls or pwd.
CVE-2000-1147(PUBLISHED):Buffer overflow in IIS ISAPI .ASP parsing mechanism allows attackers to execute arbitrary commands via a long string to the "LANGUAGE" argument in a script tag.
CVE-2000-1148(PUBLISHED):The installation of VolanoChatPro chat server sets world-readable permissions for its configuration file and stores the server administrator passwords in plaintext, which allows local users to gain privileges on the server.
CVE-2000-1149(PUBLISHED):Buffer overflow in RegAPI.DLL used by Windows NT 4.0 Terminal Server allows remote attackers to execute arbitrary commands via a long username, aka the "Terminal Server Login Buffer Overflow" vulnerability.
CVE-2000-1150(PUBLISHED):Felix IRC client in BeOS r5 pro and earlier allows remote attackers to conduct a denial of service via a message that contains a long URL.
CVE-2000-1151(PUBLISHED):Baxter IRC client in BeOS r5 pro and earlier allows remote attackers to conduct a denial of service via a message that contains a long URL.
CVE-2000-1152(PUBLISHED):Browser IRC client in BeOS r5 pro and earlier allows remote attackers to conduct a denial of service via a message that contains a long URL.
CVE-2000-1153(PUBLISHED):PostMaster 1.0 in BeOS r5 pro and earlier allows remote attackers to conduct a denial of service via a message that contains a long URL.
CVE-2000-1154(PUBLISHED):RHConsole in RobinHood 1.1 web server in BeOS r5 pro and earlier allows remote attackers to cause a denial of service via long HTTP request.
CVE-2000-1155(PUBLISHED):RHDaemon in RobinHood 1.1 web server in BeOS r5 pro and earlier allows remote attackers to cause a denial of service via long HTTP request.
CVE-2000-1156(PUBLISHED):StarOffice 5.2 follows symlinks and sets world-readable permissions for the /tmp/soffice.tmp directory, which allows a local user to read files of the user who is using StarOffice.
CVE-2000-1157(PUBLISHED):Buffer overflow in NAI Sniffer Agent allows remote attackers to execute arbitrary commands via a long SNMP community name.
CVE-2000-1158(PUBLISHED):NAI Sniffer Agent uses base64 encoding for authentication, which allows attackers to sniff the network and easily decrypt usernames and passwords.
CVE-2000-1159(PUBLISHED):NAI Sniffer Agent allows remote attackers to gain privileges on the agent by sniffing the initial UDP authentication packets and spoofing commands.
CVE-2000-1160(PUBLISHED):NAI Sniffer Agent allows remote attackers to cause a denial of service (crash) by sending a large number of login requests.
CVE-2000-1161(PUBLISHED):The installation of AdCycle banner management system leaves the build.cgi program in a web-accessible directory, which allows remote attackers to execute the program and view passwords or delete databases.
CVE-2000-1162(PUBLISHED):ghostscript before 5.10-16 allows local users to overwrite files of other users via a symlink attack.
CVE-2000-1163(PUBLISHED):ghostscript before 5.10-16 uses an empty LD_RUN_PATH environmental variable to find libraries in the current directory, which could allow local users to execute commands as other users by placing a Trojan horse library into a directory from which another user executes ghostscript.
CVE-2000-1164(PUBLISHED):WinVNC installs the WinVNC3 registry key with permissions that give Special Access (read and modify) to the Everybody group, which allows users to read and modify sensitive information such as passwords and gain access to the system.
CVE-2000-1165(PUBLISHED):Balabit syslog-ng allows remote attackers to cause a denial of service (application crash) via a malformed log message that does not have a closing > in the priority specifier.
CVE-2000-1166(PUBLISHED):Twig webmail system does not properly set the "vhosts" variable if it is not configured on the site, which allows remote attackers to insert arbitrary PHP (PHP3) code by specifying an alternate vhosts as an argument to the index.php3 program.
CVE-2000-1167(PUBLISHED):ppp utility in FreeBSD 4.1.1 and earlier does not properly restrict access as specified by the "nat deny_incoming" command, which allows remote attackers to connect to the target system.
CVE-2000-1168(PUBLISHED):IBM HTTP Server 1.3.6 (based on Apache) allows remote attackers to cause a denial of service and possibly execute arbitrary commands via a long GET request.
CVE-2000-1169(PUBLISHED):OpenSSH SSH client before 2.3.0 does not properly disable X11 or agent forwarding, which could allow a malicious SSH server to gain access to the X11 display and sniff X11 events, or gain access to the ssh-agent.
CVE-2000-1170(PUBLISHED):Buffer overflow in Netsnap webcam HTTP server before 1.2.9 allows remote attackers to execute arbitrary commands via a long GET request.
CVE-2000-1171(PUBLISHED):Directory traversal vulnerability in cgiforum.pl script in CGIForum 1.0 allows remote attackers to ready arbitrary files via a .. (dot dot) attack in the "thesection" parameter.
CVE-2000-1172(PUBLISHED):Buffer overflow in Gaim 0.10.3 and earlier using the OSCAR protocol allows remote attackers to conduct a denial of service and possibly execute arbitrary commands via a long HTML tag.
CVE-2000-1173(PUBLISHED):Microsys CyberPatrol uses weak encryption (trivial encoding) for credit card numbers and uses no encryption for the remainder of the information during registration, which could allow attackers to sniff network traffic and obtain this sensitive information.
CVE-2000-1174(PUBLISHED):Multiple buffer overflows in AFS ACL parser for Ethereal 0.8.13 and earlier allows remote attackers to execute arbitrary commands via a packet with a long username.
CVE-2000-1175(PUBLISHED):Buffer overflow in Koules 1.4 allows local users to execute arbitrary commands via a long command line argument.
CVE-2000-1176(PUBLISHED):Directory traversal vulnerability in YaBB search.pl CGI script allows remote attackers to read arbitrary files via a .. (dot dot) attack in the "catsearch" form field.
CVE-2000-1177(PUBLISHED):bb-hist.sh, bb-histlog.sh, bb-hostsvc.sh, bb-rep.sh, bb-replog.sh, and bb-ack.sh in Big Brother (BB) before 1.5d3 allows remote attackers to determine the existence of files and user ID's by specifying the target file in the HISTFILE parameter.
CVE-2000-1178(PUBLISHED):Joe text editor follows symbolic links when creating a rescue copy called DEADJOE during an abnormal exit, which allows local users to overwrite the files of other users whose joe session crashes.
CVE-2000-1179(PUBLISHED):Netopia ISDN Router 650-ST before 4.3.5 allows remote attackers to read system logs without authentication by directly connecting to the login screen and typing certain control characters.
CVE-2000-1180(PUBLISHED):Buffer overflow in cmctl program in Oracle 8.1.5 Connection Manager Control allows local users to gain privileges via a long command line argument.
CVE-2000-1181(PUBLISHED):Real Networks RealServer 7 and earlier allows remote attackers to obtain portions of RealServer's memory contents, possibly including sensitive information, by accessing the /admin/includes/ URL.
CVE-2000-1182(PUBLISHED):WatchGuard Firebox II allows remote attackers to cause a denial of service by flooding the Firebox with a large number of FTP or SMTP requests, which disables proxy handling.
CVE-2000-1183(PUBLISHED):Buffer overflow in socks5 server on Linux allows attackers to execute arbitrary commands via a long connection request.
CVE-2000-1184(PUBLISHED):telnetd in FreeBSD 4.2 and earlier, and possibly other operating systems, allows remote attackers to cause a denial of service by specifying an arbitrary large file in the TERMCAP environmental variable, which consumes resources as the server processes the file.
CVE-2000-1185(PUBLISHED):The telnet proxy in RideWay PN proxy server allows remote attackers to cause a denial of service via a flood of connections that contain malformed requests.
CVE-2000-1186(PUBLISHED):Buffer overflow in phf CGI program allows remote attackers to execute arbitrary commands by specifying a large number of arguments and including a long MIME header.
CVE-2000-1187(PUBLISHED):Buffer overflow in the HTML parser for Netscape 4.75 and earlier allows remote attackers to execute arbitrary commands via a long password value in a form field.
CVE-2000-1188(PUBLISHED):Directory traversal vulnerability in Quikstore shopping cart program allows remote attackers to read arbitrary files via a .. (dot dot) attack in the "page" parameter.
CVE-2000-1189(PUBLISHED):Buffer overflow in pam_localuser PAM module in Red Hat Linux 7.x and 6.x allows attackers to gain privileges.
CVE-2000-1190(PUBLISHED):imwheel-solo in imwheel package allows local users to modify arbitrary files via a symlink attack from the .imwheelrc file.
CVE-2000-1191(PUBLISHED):htsearch program in htDig 3.2 beta, 3.1.6, 3.1.5, and earlier allows remote attackers to determine the physical path of the server by requesting a non-existent configuration file using the config parameter, which generates an error message that includes the full path.
CVE-2000-1192(PUBLISHED):Buffer overflow in BTT Software SNMP Trap Watcher 1.16 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long string trap.
CVE-2000-1193(PUBLISHED):Performance Metrics Collector Daemon (PMCD) in Performance Copilot in IRIX 6.x allows remote attackers to cause a denial of service (resource exhaustion) via an extremely long string to the PMCD port.
CVE-2000-1194(PUBLISHED):Argosoft FRP server 1.0 allows remote attackers to cause a denial of service, and possibly execute arbitrary commands, via a long string to the (1) USER or (2) CWD commands.
CVE-2000-1195(PUBLISHED):telnet daemon (telnetd) from the Linux netkit package before netkit-telnet-0.16 allows remote attackers to bypass authentication when telnetd is running with the -L command line option.
CVE-2000-1196(PUBLISHED):PSCOErrPage.htm in Netscape PublishingXpert 2.5 before SP2 allows remote attackers to read arbitrary files by specifying the target file in the errPagePath parameter.
CVE-2000-1197(PUBLISHED):POP2 or POP3 server (pop3d) in imap-uw IMAP package on FreeBSD and other operating systems creates lock files with predictable names, which allows local users to cause a denial of service (lack of mail access) for other users by creating lock files for other mail boxes.
CVE-2000-1198(PUBLISHED):qpopper POP server creates lock files with predictable names, which allows local users to cause a denial of service for other users (lack of mail access) by creating lock files for other mail boxes.
CVE-2000-1199(PUBLISHED):PostgreSQL stores usernames and passwords in plaintext in (1) pg_shadow and (2) pg_pwd, which allows attackers with sufficient privileges to gain access to databases.
CVE-2000-1200(PUBLISHED):Windows NT allows remote attackers to list all users in a domain by obtaining the domain SID with the LsaQueryInformationPolicy policy function via a null session and using the SID to list the users.
CVE-2000-1201(PUBLISHED):Check Point FireWall-1 allows remote attackers to cause a denial of service (high CPU) via a flood of packets to port 264.
CVE-2000-1202(PUBLISHED):ikeyman in IBM IBMHSSSB 1.0 sets the CLASSPATH environmental variable to include the user's own CLASSPATH directories before the system's directories, which allows a malicious local user to execute arbitrary code as root via a Trojan horse Ikeyman class.
CVE-2000-1203(PUBLISHED):Lotus Domino SMTP server 4.63 through 5.08 allows remote attackers to cause a denial of service (CPU consumption) by forging an email message with the sender as bounce@[127.0.0.1] (localhost), which causes Domino to enter a mail loop.
CVE-2000-1204(PUBLISHED):Vulnerability in the mod_vhost_alias virtual hosting module for Apache 1.3.9, 1.3.11 and 1.3.12 allows remote attackers to obtain the source code for CGI programs if the cgi-bin directory is under the document root.
CVE-2000-1205(PUBLISHED):Cross site scripting vulnerabilities in Apache 1.3.0 through 1.3.11 allow remote attackers to execute script as other web site visitors via (1) the printenv CGI (printenv.pl), which does not encode its output, (2) pages generated by the ap_send_error_response function such as a default 404, which does not add an explicit charset, or (3) various messages that are generated by certain Apache modules or core code.  NOTE: the printenv issue might still exist for web browsers that can render text/plain content types as HTML, such as Internet Explorer, but CVE regards this as a design limitation of those browsers, not Apache.  The printenv.pl/acuparam vector, discloser on 20070724, is one such variant.
CVE-2000-1206(PUBLISHED):Vulnerability in Apache httpd before 1.3.11, when configured for mass virtual hosting using mod_rewrite, or mod_vhost_alias in Apache 1.3.9, allows remote attackers to retrieve arbitrary files.
CVE-2000-1207(PUBLISHED):userhelper in the usermode package on Red Hat Linux executes non-setuid programs as root, which does not activate the security measures in glibc and allows the programs to be exploited via format string vulnerabilities in glibc via the LANG or LC_ALL environment variables (CVE-2000-0844).
CVE-2000-1208(PUBLISHED):Format string vulnerability in startprinting() function of printjob.c in BSD-based lpr lpd package may allow local users to gain privileges via an improper syslog call that uses format strings from the checkremote() call.
CVE-2000-1209(PUBLISHED):The "sa" account is installed with a default null password on (1) Microsoft SQL Server 2000, (2) SQL Server 7.0, and (3) Data Engine (MSDE) 1.0, including third party packages that use these products such as (4) Tumbleweed Secure Mail (MMS) (5) Compaq Insight Manager, and (6) Visio 2000, which allows remote attackers to gain privileges, as exploited by worms such as Voyager Alpha Force and Spida.
CVE-2000-1210(PUBLISHED):Directory traversal vulnerability in source.jsp of Apache Tomcat before 3.1 allows remote attackers to read arbitrary files via a .. (dot dot) in the argument to source.jsp.
CVE-2000-1211(PUBLISHED):Zope 2.2.0 through 2.2.4 does not properly perform security registration for legacy names of object constructors such as DTML method objects, which could allow attackers to perform unauthorized activities.
CVE-2000-1212(PUBLISHED):Zope 2.2.0 through 2.2.4 does not properly protect a data updating method on Image and File objects, which allows attackers with DTML editing privileges to modify the raw data of these objects.
CVE-2000-1213(PUBLISHED):ping in iputils before 20001010, as distributed on Red Hat Linux 6.2 through 7J and other operating systems, does not drop privileges after acquiring a raw socket, which increases ping's exposure to bugs that otherwise would occur at lower privileges.
CVE-2000-1214(PUBLISHED):Buffer overflows in the (1) outpack or (2) buf variables of ping in iputils before 20001010, as distributed on Red Hat Linux 6.2 through 7J and other operating systems, may allow local users to gain privileges.
CVE-2000-1215(PUBLISHED):The default configuration of Lotus Domino server 5.0.8 includes system information (version, operating system, and build date) in the HTTP headers of replies, which allows remote attackers to obtain sensitive information.
CVE-2000-1216(PUBLISHED):Buffer overflow in portmir for AIX 4.3.0 allows local users to corrupt lock files and gain root privileges via the echo_error routine.
CVE-2000-1217(PUBLISHED):Microsoft Windows 2000 before Service Pack 2 (SP2), when running in a non-Windows 2000 domain and using NTLM authentication, and when credentials of an account are locally cached, allows local users to bypass account lockout policies and make an unlimited number of login attempts, aka the "Domain Account Lockout" vulnerability.
CVE-2000-1218(PUBLISHED):The default configuration for the domain name resolver for Microsoft Windows 98, NT 4.0, 2000, and XP sets the QueryIpMatching parameter to 0, which causes Windows to accept DNS updates from hosts that it did not query, which allows remote attackers to poison the DNS cache.
CVE-2000-1219(PUBLISHED):The -ftrapv compiler option in gcc and g++ 3.3.3 and earlier does not handle all types of integer overflows, which may leave applications vulnerable to vulnerabilities related to overflows.
CVE-2000-1220(PUBLISHED):The line printer daemon (lpd) in the lpr package in multiple Linux operating systems allows local users to gain root privileges by causing sendmail to execute with arbitrary command line arguments, as demonstrated using the -C option to specify a configuration file.
CVE-2000-1221(PUBLISHED):The line printer daemon (lpd) in the lpr package in multiple Linux operating systems authenticates by comparing the reverse-resolved hostname of the local machine to the hostname of the print server as returned by gethostname, which allows remote attackers to bypass intended access controls by modifying the DNS for the attacking IP.
CVE-2000-1222(PUBLISHED):AIX sysback before ******** uses a relative path to find and execute the hostname program, which allows local users to gain privileges by modifying the path to point to a malicious hostname program.
CVE-2000-1223(PUBLISHED):quikstore.cgi in Quikstore Shopping Cart allows remote attackers to execute arbitrary commands via shell metacharacters in the URL portion of an HTTP GET request.
CVE-2000-1224(PUBLISHED):Caucho Technology Resin 1.2 and possibly earlier allows remote attackers to view JSP source via an HTTP request to a .jsp file with certain characters appended to the file name, such as (1) "..", (2) "%2e..", (3) "%81", (4) "%82", and others.
CVE-2000-1225(PUBLISHED):Xitami 2.5b installs the testcgi.exe program by default in the cgi-bin directory, which allows remote attackers to gain sensitive configuration information about the web server by accessing the program.
CVE-2000-1226(PUBLISHED):Snort 1.6, when running in straight ASCII packet logging mode or IDS mode with straight decoded ASCII packet logging selected, allows remote attackers to cause a denial of service (crash) by sending non-IP protocols that Snort does not know about, as demonstrated by an nmap protocol scan.
CVE-2000-1227(PUBLISHED):Windows NT 4.0 and Windows 2000 hosts allow remote attackers to cause a denial of service (unavailable connections) by sending multiple SMB SMBnegprots requests but not reading the response that is sent back.
CVE-2000-1228(PUBLISHED):Phorum 3.0.7 allows remote attackers to change the administrator password without authentication via an HTTP request for admin.php3 that sets step, option, confirm and newPssword variables.
CVE-2000-1229(PUBLISHED):Directory traversal vulnerability in Phorum 3.0.7 allows remote Phorum administrators to read arbitrary files via ".." (dot dot) sequences in the default .langfile name field in the Master Settings administrative function, which causes the file to be displayed in admin.php3.
CVE-2000-1230(PUBLISHED):Backdoor in auth.php3 in Phorum 3.0.7 allows remote attackers to access restricted web pages via an HTTP request with the PHP_AUTH_USER parameter set to "boogieman".
CVE-2000-1231(PUBLISHED):code.php3 in Phorum 3.0.7 allows remote attackers to read arbitrary files in the phorum directory via the query string.
CVE-2000-1232(PUBLISHED):upgrade.php3 in Phorum 3.0.7 could allow remote attackers to modify certain Phorum database tables via an unknown method.
CVE-2000-1233(PUBLISHED):SQL injection vulnerability in read.php3 and other scripts in Phorum 3.0.7 allows remote attackers to execute arbitrary SQL queries via the sSQL parameter.
CVE-2000-1234(PUBLISHED):violation.php3 in Phorum 3.0.7 allows remote attackers to send e-mails to arbitrary addresses and possibly use Phorum as a "spam proxy" by setting the Mod and ForumName parameters.
CVE-2000-1235(PUBLISHED):The default configurations of (1) the port listener and (2) modplsql in Oracle Internet Application Server (IAS) 3.0.7 and earlier allow remote attackers to view privileged database information via HTTP requests for Database Access Descriptor (DAD) files.
CVE-2000-1236(PUBLISHED):SQL injection vulnerability in mod_sql in Oracle Internet Application Server (IAS) 3.0.7 and earlier allows remote attackers to execute arbitrary SQL commands via the query string of the URL.
CVE-2000-1237(PUBLISHED):The POP3 server in FTGate returns an -ERR code after receiving an invalid USER request, which makes it easier for remote attackers to determine valid usernames and conduct brute force password guessing.
CVE-2000-1238(PUBLISHED):BEA Systems WebLogic Express and WebLogic Server 5.1 SP1-SP6 allows remote attackers to bypass access controls for restricted JSP or servlet pages via a URL with multiple / (forward slash) characters before the restricted pages.
CVE-2000-1239(PUBLISHED):The HTTP interface of Tivoli Lightweight Client Framework (LCF) in IBM Tivoli Management Framework 3.7.1 sets http_disable to zero at install time, which allows remote authenticated users to bypass file permissions on Tivoli Endpoint Configuration data files via an unspecified manipulation of log files.
CVE-2000-1240(PUBLISHED):Unspecified vulnerability in siteman.php3 in AnyPortal(php) before 22 APR 00 allows remote attackers to obtain sensitive information via unknown attack vectors, which reveal the absolute path.  NOTE: the provenance of this information is unknown; the details are obtained from third party information.
CVE-2000-1241(PUBLISHED):Unspecified vulnerability in Haakon Nilsen simple, integrated publishing system (SIPS) before 0.2.4 has an unknown impact and attack vectors, related to a "grave security fault."
CVE-2000-1242(PUBLISHED):The HTTP service in American Power Conversion (APC) PowerChute uses a default username and password, which allows remote attackers to gain system access.
CVE-2000-1243(PUBLISHED):Privacy leak in Dansie Shopping Cart 3.04, and probably earlier versions, sends sensitive information such as user credentials to an e-mail address controlled by the product developers.
CVE-2000-1244(PUBLISHED):Computer Associates InoculateIT Agent for Exchange Server does not recognize an e-mail virus attachment if the SMTP header is missing the "From" field, which allows remote attackers to bypass virus protection.
CVE-2000-1245(PUBLISHED):Multiple unspecified vulnerabilities in NWFTPD.nlm before 5.01o in the FTP server in Novell NetWare 5.1 SP3 allow remote attackers to bypass intended restrictions on anonymous access via unknown vectors.
CVE-2000-1246(PUBLISHED):NWFTPD.nlm before 5.01o in the FTP server in Novell NetWare 5.1 SP3 allows remote authenticated users to cause a denial of service (abend) by sending an RNTO command after a failed RNFR command.
CVE-2000-1247(PUBLISHED):The default configuration of the jserv-status handler in jserv.conf in Apache JServ 1.1.2 includes an "allow from 127.0.0.1" line, which allows local users to discover JDBC passwords or other sensitive information via a direct request to the jserv/ URI.
CVE-2000-1254(PUBLISHED):crypto/rsa/rsa_gen.c in OpenSSL before 0.9.6 mishandles C bitwise-shift operations that exceed the size of an expression, which makes it easier for remote attackers to defeat cryptographic protection mechanisms by leveraging improper RSA key generation on 64-bit HP-UX platforms.
