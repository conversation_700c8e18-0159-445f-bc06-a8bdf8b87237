import os
import re
import urllib.parse


# 提取用户输入的内容
def identify_input_type(user_input):
    """
    识别用户输入的类型：URL、文件路径或普通文本
    
    Args:
        user_input (str): 用户输入内容
        
    Returns:
        dict: 包含以下键的字典：
            - input_type: 'url', 'file', 或 'text'
            - processed_input: 处理后的输入（如果是URL，则为验证后的URL）
            - is_valid: 布尔值，表示输入是否有效
    """
    result = {
        'input_type': 'text',  # 默认为文本
        'processed_input': user_input,
        'is_valid': True
    }
    
    # 确保输入是字符串
    if not isinstance(user_input, str):
        result['is_valid'] = False
        return result
    
    # 处理空输入
    if not user_input or user_input.strip() == "":
        result['is_valid'] = False
        return result
    
    # 检查是否是完整URL
    if user_input.startswith(("http://", "https://")):
        try:
            parsed = urllib.parse.urlparse(user_input)
            if parsed.scheme in ('http', 'https') and parsed.netloc:
                result['input_type'] = 'url'
                result['processed_input'] = user_input
                return result
        except:
            pass
    
    # 检查是否在文本中包含URL
    url_pattern = r'https?://[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)'
    url_matches = re.findall(url_pattern, user_input)
    
    if url_matches:
        # 验证并解析URL
        valid_urls = []
        for url in url_matches:
            try:
                # 移除URL末尾的标点符号
                if url[-1] in ['.', ',', ')', ';', ':']:
                    url = url[:-1]
                # 解析URL，如果格式无效会抛出异常
                parsed = urllib.parse.urlparse(url)
                if parsed.scheme in ('http', 'https') and parsed.netloc:
                    valid_urls.append(url)
            except:
                pass
                
        if valid_urls:
            # 选择最长的有效URL
            result['input_type'] = 'url'
            result['processed_input'] = max(valid_urls, key=len)
            return result
    
    # 检查是否为文件路径
    if os.path.exists(user_input):
        result['input_type'] = 'file'
        result['processed_input'] = user_input
        return result
    else:
        # 尝试在当前目录下寻找
        current_dir_path = os.path.join(os.getcwd(), user_input)
        if os.path.exists(current_dir_path):
            result['input_type'] = 'file'
            result['processed_input'] = current_dir_path
            return result
    
    # 如果不是URL也不是文件，则视为普通文本
    return result



# 获取每周威胁情报数据
def parse_analysis_for_intel(analysis_row):
    """从分析结果提取威胁情报相关信息"""
    try:
        analysis_content = analysis_row.get('analysis_content')
        
        # 如果分析结果为空，返回None
        if not analysis_content or analysis_content.strip() == "":
            return None
            
        # 构建情报项的基本信息
        intel_item = {
            'analysis_id': analysis_row['analysis_id'],
            'title': analysis_row['title'] or "未命名威胁报告",
            'date': analysis_row['report_time'] or analysis_row['analysis_time'],
            'source_url': analysis_row['source_url'],
            'description': get_intel_summary(analysis_content, analysis_row['extracted_text']),
            'severity': determine_severity(analysis_content),
            'threat_type': determine_threat_type(analysis_content, analysis_row['title']),
            'iocs': extract_iocs(analysis_content, analysis_row['extracted_text'])
        }
        
        return intel_item
    except Exception as e:
        print(f"解析威胁情报失败: {str(e)}")
        return None

def get_intel_summary(analysis_content, original_text):
    """提取威胁情报摘要"""
    try:
        # 首先尝试从分析内容中提取摘要或总结部分
        summary_patterns = [
            r'# 总体摘要\s*(.*?)(?=#|\Z)',
            r'# 摘要\s*(.*?)(?=#|\Z)',
            r'# 总结\s*(.*?)(?=#|\Z)',
            r'总体摘要[:：](.*?)(?=\n\n|\Z)',
            r'简介[:：](.*?)(?=\n\n|\Z)'
        ]
        
        for pattern in summary_patterns:
            match = re.search(pattern, analysis_content, re.DOTALL)
            if match:
                summary = match.group(1).strip()
                # 清理Markdown标记
                summary = re.sub(r'[#*_`]', '', summary)
                # 限制长度
                return summary[:300] + ('...' if len(summary) > 300 else '')
        
        # 如果没有找到明确的摘要，就取分析内容的前200个字符
        if analysis_content:
            clean_content = re.sub(r'[#*_`]', '', analysis_content)
            return clean_content[:200].strip() + '...'
            
        # 如果分析内容为空，则使用原文的前150个字符
        if original_text:
            return original_text[:150].strip() + '...'
            
        return "无可用摘要"
    except Exception as e:
        print(f"提取摘要失败: {str(e)}")
        return "摘要提取失败"

def determine_severity(analysis_content):
    """确定威胁严重程度"""
    try:
        # 在分析内容中寻找严重程度相关的关键词
        severity_patterns = {
            'high': [r'高危', r'严重', r'critical', r'high', r'重大', r'0day', r'零日'],
            'medium': [r'中危', r'中等', r'moderate', r'medium', r'一般'],
            'low': [r'低危', r'低', r'minor', r'low']
        }
        
        for severity, patterns in severity_patterns.items():
            for pattern in patterns:
                if re.search(pattern, analysis_content, re.IGNORECASE):
                    return severity
        
        # 默认为中等严重度
        return 'medium'
    except Exception as e:
        print(f"确定严重程度失败: {str(e)}")
        return 'medium'

def determine_threat_type(analysis_content, title=''):
    """确定威胁类型"""
    try:
        # 合并分析内容和标题以提高匹配准确性
        combined_text = f"{title} {analysis_content}"
        
        # 定义威胁类型及其关键词
        threat_type_patterns = {
            'malware': [r'恶意软件', r'木马', r'病毒', r'蠕虫', r'malware', r'trojan', r'virus', r'worm'],
            'vulnerability': [r'漏洞', r'CVE-\d{4}-\d+', r'安全缺陷', r'vulnerability', r'exploit'],
            'apt': [r'APT', r'高级持续性威胁', r'国家级', r'组织', r'黑客组织', r'advanced persistent threat'],
            'ransom': [r'勒索', r'加密', r'解密', r'赎金', r'ransom'],
            'phishing': [r'钓鱼', r'欺骗', r'仿冒', r'phishing', r'spoofing'],
            'backdoor': [r'后门', r'远控', r'远程访问', r'backdoor', r'RAT'],
            'exploit': [r'利用', r'攻击', r'exploit']
        }
        
        for threat_type, patterns in threat_type_patterns.items():
            for pattern in patterns:
                if re.search(pattern, combined_text, re.IGNORECASE):
                    return threat_type
        
        # 默认为恶意软件
        return 'malware'
    except Exception as e:
        print(f"确定威胁类型失败: {str(e)}")
        return 'malware'

def extract_iocs(analysis_content, original_text=''):
    """提取IOC指标"""
    try:
        iocs = []
        combined_text = f"{analysis_content} {original_text}"
        
        # IP地址模式
        ip_pattern = r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'
        ip_matches = re.findall(ip_pattern, combined_text)
        for ip in ip_matches:
            if is_valid_ip(ip):
                iocs.append({'type': 'ip', 'value': ip})
        
        # 域名模式
        domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
        domain_matches = re.findall(domain_pattern, combined_text)
        for domain in domain_matches:
            if not re.match(r'\d+\.\d+\.\d+\.\d+', domain) and not domain.startswith(('www.example', 'example.')):
                iocs.append({'type': 'domain', 'value': domain})
        
        # URL模式
        url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(/[-\w%/.]+)*'
        url_matches = re.findall(url_pattern, combined_text)
        for url in url_matches:
            iocs.append({'type': 'url', 'value': url})
        
        # 文件哈希模式
        hash_patterns = {
            'md5': r'\b[a-fA-F0-9]{32}\b',
            'sha1': r'\b[a-fA-F0-9]{40}\b',
            'sha256': r'\b[a-fA-F0-9]{64}\b'
        }
        
        for hash_type, pattern in hash_patterns.items():
            hash_matches = re.findall(pattern, combined_text)
            for hash_value in hash_matches:
                iocs.append({'type': 'hash', 'value': hash_value, 'hash_type': hash_type})
        
        # 去重
        seen = set()
        unique_iocs = []
        for ioc in iocs:
            key = (ioc['type'], ioc['value'])
            if key not in seen:
                seen.add(key)
                unique_iocs.append(ioc)
        
        return unique_iocs[:20]  # 限制返回数量，避免过多
    except Exception as e:
        print(f"提取IOC失败: {str(e)}")
        return []

def is_valid_ip(ip):
    """验证IP地址是否有效，排除私有IP和回环地址"""
    try:
        parts = ip.split('.')
        if len(parts) != 4:
            return False
        
        # 检查每个部分是否为0-255的整数
        for part in parts:
            if not (0 <= int(part) <= 255):
                return False
                
        # 排除私有IP和回环地址
        if ip.startswith('10.') or ip.startswith('172.') or ip.startswith('192.168.') or ip.startswith('127.'):
            return False
        return True
    except ValueError:
        return False

