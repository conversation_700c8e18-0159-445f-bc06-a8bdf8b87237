{"chunk_id": "line-1", "filename": "mitre_campaigns_v17.txt", "content": "2015 Ukraine Electric Power Attack was a Sandworm Team campaign during which they used BlackEnergy (specifically BlackEnergy3) and KillDisk to target and disrupt transmission and distribution substations within the Ukrainian power grid. This campaign was the first major public attack conducted against the Ukrainian power grid by Sandworm Team.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-2", "filename": "mitre_campaigns_v17.txt", "content": "2016 Ukraine Electric Power Attack was a Sandworm Team campaign during which they used Industroyer malware to target and disrupt distribution substations within the Ukrainian power grid. This campaign was the second major public attack conducted against Ukraine by Sandworm Team.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-3", "filename": "mitre_campaigns_v17.txt", "content": "The 2022 Ukraine Electric Power Attack was a Sandworm Team campaign that used a combination of GOGETTER, Neo-REGEORG, CaddyWiper, and living of the land (LotL) techniques to gain access to a Ukrainian electric utility to send unauthorized commands from their SCADA system.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-4", "filename": "mitre_campaigns_v17.txt", "content": "APT28 Nearest Neighbor Campaign was conducted by APT28 from early February 2022 to November 2024 against organizations and individuals with expertise on Ukraine. APT28 primarily leveraged living-off-the-land techniques, while leveraging the zero-day exploitation of CVE-2022-38028. Notably, APT28 leveraged Wi-Fi networks in close proximity to the intended target to gain initial access to the victim environment. By daisy-chaining multiple compromised organizations nearby the intended target, APT28 discovered dual-homed systems (with both a wired and wireless network connection) to enable Wi-Fi and use compromised credentials to connect to the victim network.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-5", "filename": "mitre_campaigns_v17.txt", "content": "APT41 DUST was conducted by APT41 from 2023 to July 2024 against entities in Europe, Asia, and the Middle East. APT41 DUST targeted sectors such as shipping, logistics, and media for information gathering purposes. APT41 used previously-observed malware such as DUSTPAN as well as newly observed tools such as DUSTTRAP in APT41 DUST.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-6", "filename": "mitre_campaigns_v17.txt", "content": "ArcaneDoor is a campaign targeting networking devices from Cisco and other vendors between July 2023 and April 2024, primarily focused on government and critical infrastructure networks. ArcaneDoor is associated with the deployment of the custom backdoors Line Runner and Line Dancer. ArcaneDoor is attributed to a group referred to as UAT4356 or STORM-1849, and is assessed to be a state-sponsored campaign.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-7", "filename": "mitre_campaigns_v17.txt", "content": "C0010 was a cyber espionage campaign conducted by UNC3890 that targeted Israeli shipping, government, aviation, energy, and healthcare organizations. Security researcher assess UNC3890 conducts operations in support of Iranian interests, and noted several limited technical connections to Iran, including PDB strings and Farsi language artifacts. C0010 began by at least late 2020, and was still ongoing as of mid-2022.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-8", "filename": "mitre_campaigns_v17.txt", "content": "C0011 was a suspected cyber espionage campaign conducted by Transparent Tribe that targeted students at universities and colleges in India. Security researchers noted this campaign against students was a significant shift from Transparent Tribe's historic targeting Indian government, military, and think tank personnel, and assessed it was still ongoing as of July 2022.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-9", "filename": "mitre_campaigns_v17.txt", "content": "C0015 was a ransomware intrusion during which the unidentified attackers used Bazar, Cobalt Strike, and Conti, along with other tools, over a 5 day period. Security researchers assessed the actors likely used the widely-circulated Conti ransomware playbook based on the observed pattern of activity and operator errors.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-10", "filename": "mitre_campaigns_v17.txt", "content": "C0017 was an APT41 campaign conducted between May 2021 and February 2022 that successfully compromised at least six U.S. state government networks through the exploitation of vulnerable Internet facing web applications. During C0017, APT41 was quick to adapt and use publicly-disclosed as well as zero-day vulnerabilities for initial access, and in at least two cases re-compromised victims following remediation efforts. The goals of C0017 are unknown, however APT41 was observed exfiltrating Personal Identifiable Information (PII).", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-11", "filename": "mitre_campaigns_v17.txt", "content": "C0018 was a month-long ransomware intrusion that successfully deployed AvosLocker onto a compromised network. The unidentified actors gained initial access to the victim network through an exposed server and used a variety of open-source tools prior to executing AvosLocker.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-12", "filename": "mitre_campaigns_v17.txt", "content": "C0021 was a spearphishing campaign conducted in November 2018 that targeted public sector institutions, non-governmental organizations (NGOs), educational institutions, and private-sector corporations in the oil and gas, chemical, and hospitality industries. The majority of targets were located in the US, particularly in and around Washington D.C., with other targets located in Europe, Hong Kong, India, and Canada. C0021's technical artifacts, tactics, techniques, and procedures (TTPs), and targeting overlap with previous suspected APT29 activity.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-13", "filename": "mitre_campaigns_v17.txt", "content": "C0026 was a campaign identified in September 2022 that included the selective distribution of KOPILUWAK and QUIETCANARY malware to previous ANDROMEDA malware victims in Ukraine through re-registered ANDROMEDA C2 domains. Several tools and tactics used during C0026 were consistent with historic Turla operations.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-14", "filename": "mitre_campaigns_v17.txt", "content": "C0027 was a financially-motivated campaign linked to Scattered Spider that targeted telecommunications and business process outsourcing (BPO) companies from at least June through December of 2022. During C0027 Scattered Spider used various forms of social engineering, performed SIM swapping, and attempted to leverage access from victim environments to mobile carrier networks.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-15", "filename": "mitre_campaigns_v17.txt", "content": "C0032 was an extended campaign suspected to involve the Triton adversaries with related capabilities and techniques focused on gaining a foothold within IT environments. This campaign occurred in 2019 and was distinctly different from the Triton Safety Instrumented System Attack.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-16", "filename": "mitre_campaigns_v17.txt", "content": "C0033 was a PROMETHIUM campaign during which they used StrongPity to target Android users. C0033 was the first publicly documented mobile campaign for PROMETHIUM, who previously used Windows-based techniques.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-17", "filename": "mitre_campaigns_v17.txt", "content": "CostaRicto was a suspected hacker-for-hire cyber espionage campaign that targeted multiple industries worldwide, with a large number being financial institutions. CostaRicto actors targeted organizations in Europe, the Americas, Asia, Australia, and Africa, with a large concentration in South Asia (especially India, Bangladesh, and Singapore), using custom malware, open source tools, and a complex network of proxies and SSH tunnels.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-18", "filename": "mitre_campaigns_v17.txt", "content": "Cutting Edge was a campaign conducted by suspected China-nexus espionage actors, variously identified as UNC5221/UTA0178 and UNC5325, that began as early as December 2023 with the exploitation of zero-day vulnerabilities in Ivanti Connect Secure (previously Pulse Secure) VPN appliances. Cutting Edge targeted the U.S. defense industrial base and multiple sectors globally including  telecommunications, financial, aerospace, and technology. Cutting Edge featured the use of defense evasion and living-off-the-land (LoTL) techniques along with the deployment of web shells and other custom malware.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-19", "filename": "mitre_campaigns_v17.txt", "content": "FLORAHOX Activity is conducted using a hybrid operational relay box (ORB) network, which combines two types of infrastructure: compromised devices and leased Virtual Private Servers (VPS). The compromised devices include end-of-life routers and IoT devices, while VPS space is commercially leased and managed by ORB network administrators. This hybrid ORB network allows adversaries to proxy and obscure malicious traffic, making the source of the traffic more difficult to trace.The FLORAHOX ORB network has been leveraged by multiple cyber threat actors, including China-nexus actors like ZIRCONIUM. These adversaries conduct espionage campaigns through FLORAHOX Activity, relying on the ORB network's ability to funnel traffic through Tor nodes, provisioned VPS servers, and compromised routers to obfuscate malicious traffic.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-20", "filename": "mitre_campaigns_v17.txt", "content": "Frankenstein was described by security researchers as a highly-targeted campaign conducted by moderately sophisticated and highly resourceful threat actors in early 2019. The unidentified actors primarily relied on open source tools, including Empire. The campaign name refers to the actors' ability to piece together several unrelated open-source tool components.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-21", "filename": "mitre_campaigns_v17.txt", "content": "FrostyGoop Incident took place in January 2024 against a municipal district heating company in Ukraine. Following initial access via likely exploitation of external facing services, FrostyGoop was used to manipulate ENCO control systems via legitimate Modbus commands to impact the delivery of heating services to Ukrainian civilians.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-22", "filename": "mitre_campaigns_v17.txt", "content": "FunnyDream was a suspected Chinese cyber espionage campaign that targeted government and foreign organizations in Malaysia, the Philippines, Taiwan, Vietnam, and other parts of Southeast Asia. Security researchers linked the FunnyDream campaign to possible Chinese-speaking threat actors through the use of the Chinoxy backdoor and noted infrastructure overlap with the TAG-16 threat group.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-23", "filename": "mitre_campaigns_v17.txt", "content": "HomeLand Justice was a disruptive campaign involving the use of ransomware, wiper malware, and sensitive information leaks conducted by Iranian state cyber actors against Albanian government networks in July and September 2022. Initial access for HomeLand Justice was established in May 2021 as threat actors subsequently moved laterally, exfiltrated sensitive information, and maintained persistence for approximately 14 months prior to the attacks. Responsibility was claimed by the \"HomeLand Justice\" front whose messaging indicated targeting of the Mujahedeen-e Khalq (MEK), an Iranian opposition group who maintain a refugee camp in Albania, and were formerly designated a terrorist organization by the US State Department. A second wave of attacks was launched in September 2022 using similar tactics after public attribution of the previous activity to Iran and the severing of diplomatic ties between Iran and Albania.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-24", "filename": "mitre_campaigns_v17.txt", "content": "Indian Critical Infrastructure Intrusions is a sequence of intrusions from 2021 through early 2022 linked to People’s Republic of China (PRC) threat actors, particularly RedEcho and Threat Activity Group 38 (TAG38). The intrusions appear focused on IT system breach in Indian electric utility entities and logistics firms, as well as potentially managed service providers operating within India. Although focused on OT-operating entities, there is no evidence this campaign was able to progress beyond IT breach and information gathering to OT environment access.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-25", "filename": "mitre_campaigns_v17.txt", "content": "The J-magic Campaign was active from mid-2023 to at least mid-2024 and featured the use of the J-magic backdoor, a custom cd00r variant tailored for use against Juniper routers. The J-magic Campaign targeted Junos OS routers serving as VPN gateways primarily in the semiconductor, energy, manufacturing, and IT sectors.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-26", "filename": "mitre_campaigns_v17.txt", "content": "Juicy Mix was a campaign conducted by OilRig throughout 2022 that targeted Israeli organizations with the Mango backdoor.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-27", "filename": "mitre_campaigns_v17.txt", "content": "KV Botnet Activity consisted of exploitation of primarily \"end-of-life\" small office-home office (SOHO) equipment from manufacturers such as Cisco, NETGEAR, and DrayTek. KV Botnet Activity was used by Volt Typhoon to obfuscate connectivity to victims in multiple critical infrastructure segments, including energy and telecommunication companies and entities based on the US territory of Guam. While the KV Botnet is the most prominent element of this campaign, it overlaps with another botnet cluster referred to as the JDY cluster. This botnet was disrupted by US law enforcement entities in early 2024 after periods of activity from October 2022 through January 2024.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-28", "filename": "mitre_campaigns_v17.txt", "content": "Leviathan Australian Intrusions consisted of at least two long-term intrusions against victims in Australia by Leviathan, relying on similar tradecraft such as external service exploitation followed by extensive credential capture and re-use to enable privilege escalation and lateral movement. Leviathan Australian Intrusions were focused on exfiltrating sensitive data including valid credentials for the victim organizations.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-29", "filename": "mitre_campaigns_v17.txt", "content": "Maroochy Water Breach was an incident in 2000 where an adversary leveraged the local government’s wastewater control system and stolen engineering equipment to disrupt and eventually release 800,000 liters of raw sewage into the local community.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-30", "filename": "mitre_campaigns_v17.txt", "content": "Night Dragon was a cyber espionage campaign that targeted oil, energy, and petrochemical companies, along with individuals and executives in Kazakhstan, Taiwan, Greece, and the United States. The unidentified threat actors searched for information related to oil and gas field production systems, financials, and collected data from SCADA systems. Based on the observed techniques, tools, and network activities, security researchers assessed the campaign involved a threat group based in China.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-31", "filename": "mitre_campaigns_v17.txt", "content": "Operation CuckooBees was a cyber espionage campaign targeting technology and manufacturing companies in East Asia, Western Europe, and North America since at least 2019. Security researchers noted the goal of Operation CuckooBees, which was still ongoing as of May 2022, was likely the theft of proprietary information, research and development documents, source code, and blueprints for various technologies. Researchers assessed Operation CuckooBees was conducted by actors affiliated with Winnti Group, APT41, and BARIUM.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-32", "filename": "mitre_campaigns_v17.txt", "content": "Operation Dream Job was a cyber espionage operation likely conducted by Lazarus Group that targeted the defense, aerospace, government, and other sectors in the United States, Israel, Australia, Russia, and India. In at least one case, the cyber actors tried to monetize their network access to conduct a business email compromise (BEC) operation. In 2020, security researchers noted overlapping TTPs, to include fake job lures and code similarities, between Operation Dream Job, Operation North Star, and Operation Interception; by 2022 security researchers described Operation Dream Job as an umbrella term covering both Operation Interception and Operation North Star.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-33", "filename": "mitre_campaigns_v17.txt", "content": "Operation Dust Storm was a long-standing persistent cyber espionage campaign that targeted multiple industries in Japan, South Korea, the United States, Europe, and several Southeast Asian countries. By 2015, the Operation Dust Storm threat actors shifted from government and defense-related intelligence targets to Japanese companies or Japanese subdivisions of larger foreign organizations supporting Japan's critical infrastructure, including electricity generation, oil and natural gas, finance, transportation, and construction.Operation Dust Storm threat actors also began to use Android backdoors in their operations by 2015, with all identified victims at the time residing in Japan or South Korea.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-34", "filename": "mitre_campaigns_v17.txt", "content": "Operation Ghost was an APT29 campaign starting in 2013 that included operations against ministries of foreign affairs in Europe and the Washington, D.C. embassy of a European Union country. During Operation Ghost, APT29 used new families of malware and leveraged web services, steganography, and unique C2 infrastructure for each victim.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-35", "filename": "mitre_campaigns_v17.txt", "content": "Operation Honeybee was a campaign that targeted humanitarian aid and inter-Korean affairs organizations from at least late 2017 through early 2018. Operation Honeybee initially targeted South Korea, but expanded to include Vietnam, Singapore, Japan, Indonesia, Argentina, and Canada. Security researchers assessed the threat actors were likely Korean speakers based on metadata used in both lure documents and executables, and named the campaign \"Honeybee\" after the author name discovered in malicious Word documents.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-36", "filename": "mitre_campaigns_v17.txt", "content": "Operation MidnightEclipse was a campaign conducted in March and April 2024 that involved initial exploit of zero-day vulnerability CVE-2024-3400, a critical command injection vulnerability in the GlobalProtect feature of Palo Alto Networks PAN-OS.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-37", "filename": "mitre_campaigns_v17.txt", "content": "Operation Sharpshooter was a global cyber espionage campaign that targeted nuclear, defense, government, energy, and financial companies, with many located in Germany, Turkey, the United Kingdom, and the United States. Security researchers noted the campaign shared many similarities with previous Lazarus Group operations, including fake job recruitment lures and shared malware code.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-38", "filename": "mitre_campaigns_v17.txt", "content": "Operation Spalax was a campaign that primarily targeted Colombian government organizations and private companies, particularly those associated with the energy and metallurgical industries. The Operation Spalax threat actors distributed commodity malware and tools using generic phishing topics related to COVID-19, banking, and law enforcement action. Security researchers noted indicators of compromise and some infrastructure overlaps with other campaigns dating back to April 2018, including at least one separately attributed to APT-C-36, however identified enough differences to report this as separate, unattributed activity.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-39", "filename": "mitre_campaigns_v17.txt", "content": "Operation Triangulation is a mobile campaign targeting iOS devices. The unidentified actors used zero-click exploits in iMessage attachments to gain Initial Access, then executed exploits and validators, such as Binary Validator before finally executing the TriangleDB implant.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-40", "filename": "mitre_campaigns_v17.txt", "content": "Operation Wocao was a cyber espionage campaign that targeted organizations around the world, including in Brazil, China, France, Germany, Italy, Mexico, Portugal, Spain, the United Kingdom, and the United States. The suspected China-based actors compromised government organizations and managed service providers, as well as aviation, construction, energy, finance, health care, insurance, offshore engineering, software development, and transportation companies.Security researchers assessed the Operation Wocao actors used similar TTPs and tools as APT20, suggesting a possible overlap. Operation Wocao was named after an observed command line entry by one of the threat actors, possibly out of frustration from losing webshell access.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-41", "filename": "mitre_campaigns_v17.txt", "content": "Outer Space was a campaign conducted by OilRig throughout 2021 that used the SampleCheck5000 downloader and Solar backdoor to target Israeli organizations.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-42", "filename": "mitre_campaigns_v17.txt", "content": "Pikabot was distributed in Pikabot Distribution February 2024 using malicious emails with embedded links leading to malicious ZIP archives requiring user interaction for follow-on infection. The version of Pikabot distributed featured significant changes over the 2023 variant, including reduced code complexity and simplified obfuscation mechanisms.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-43", "filename": "mitre_campaigns_v17.txt", "content": "RedDelta Modified PlugX Infection Chain Operations was executed by Mustang Panda from mid-2023 through the end of 2024 against multiple entities in East and Southeast Asia. RedDelta Modified PlugX Infection Chain Operations involved phishing to deliver malicious files or links to users prompting follow-on installer downloads to load PlugX on victim machines in a persistent state.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-44", "filename": "mitre_campaigns_v17.txt", "content": "ShadowRay was a campaign that began in late 2023 targeting the education, cryptocurrency, biopharma, and other sectors through a vulnerability (CVE-2023-48022) in the Ray AI framework named ShadowRay. According to security researchers ShadowRay was the first known instance of AI workloads being activley exploited in the wild through vulnerabilities in AI infrastructure. CVE-2023-48022, which allows access to compute resources and sensitive data for exposed instances, remains unpatched and has been disputed by the vendor as they maintain that Ray is not intended for use outside of a strictly controlled network environment.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-45", "filename": "mitre_campaigns_v17.txt", "content": "The SolarWinds Compromise was a sophisticated supply chain cyber operation conducted by APT29 that was discovered in mid-December 2020. APT29 used customized malware to inject malicious code into the SolarWinds Orion software build process that was later distributed through a normal software update; they also used password spraying, token theft, API abuse, spear phishing, and other supply chain attacks to compromise user accounts and leverage their associated access. Victims of this campaign included government, consulting, technology, telecom, and other organizations in North America, Europe, Asia, and the Middle East. This activity has been labled the StellarParticle campaign in industry reporting. Industry reporting also initially referred to the actors involved in this campaign as UNC2452, NOBELIUM, Dark Halo, and SolarStorm. In April 2021, the US and UK governments attributed the SolarWinds Compromise to Russia's Foreign Intelligence Service (SVR); public statements included citations to APT29, Cozy Bear, and The Dukes. The US government assessed that of the approximately 18,000 affected public and private sector customers of Solar Winds’ Orion product, a much smaller number were compromised by follow-on APT29 activity on their systems.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-46", "filename": "mitre_campaigns_v17.txt", "content": "SPACEHOP Activity is conducted through commercially leased Virtual Private Servers (VPS), otherwise known as provisioned Operational Relay Box (ORB) networks. The network leveraged for SPACEHOP Activity enabled China-nexus cyber threat actors – such as APT5 and Ke3chang – to perform network reconnaissance scanning and vulnerability exploitation. SPACEHOP Activity has historically targeted entities in North America, Europe, and the Middle East.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-47", "filename": "mitre_campaigns_v17.txt", "content": "Triton Safety Instrumented System Attack was a campaign employed by TEMP.Veles which leveraged the Triton malware framework against a petrochemical organization. The malware and techniques used within this campaign targeted specific Triconex Safety Controllers within the environment. The incident was eventually discovered due to a safety trip that occurred as a result of an issue in the malware.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-48", "filename": "mitre_campaigns_v17.txt", "content": "The Unitronics Defacement Campaign was a collection of intrusions across multiple sectors by the CyberAv3ngers, where threat actors engaged in a seemingly opportunistic and global targeting and defacement of Unitronics Vision Series Programmable Logic Controller (PLC) with Human-Machine Interface (HMI). The sectors that these PLCs can be commonly found in are water and wastewater, energy, food and beverage manufacturing, and healthcare. The most notable feature of this attack was the defacement of the PLCs' HMIs.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-49", "filename": "mitre_campaigns_v17.txt", "content": "Versa Director Zero Day Exploitation was conducted by Volt Typhoon from early June through August 2024 as zero-day exploitation of Versa Director servers controlling software-defined wide area network (SD-WAN) applications. Since tracked as CVE-2024-39717, exploitation focused on credential capture from compromised Versa Director servers at managed service providers (MSPs) and internet service providers (ISPs) to enable follow-on access to service provider clients. Versa Director Zero Day Exploitation was followed by the delivery of the VersaMem web shell for both credential theft and follow-on code execution.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
{"chunk_id": "line-50", "filename": "mitre_campaigns_v17.txt", "content": "Pikabot was distributed in Water Curupira Pikabot Distribution throughout 2023 by an entity linked to BlackBasta ransomware deployment via email attachments. This activity followed the take-down of QakBot, with several technical overlaps and similarities with QakBot, indicating a possible connection. The identified activity led to the deployment of tools such as Cobalt Strike, while coinciding with campaigns delivering DarkGate and IcedID en route to ransomware deployment.", "metadata": {"filename": "mitre_campaigns_v17.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\ATTCK\\mitre_campaigns_v17.txt"}}
