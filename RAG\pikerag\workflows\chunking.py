# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import importlib
import os
from typing import List, Tuple

import pickle
from tqdm import tqdm

from pikerag.document_loaders import get_loader
from pikerag.document_transformers import LLMPoweredRecursiveSplitter
from pikerag.llm_client import Base<PERSON>MClient
from pikerag.utils.config_loader import load_class
from pikerag.utils.logger import Logger
from pikerag.utils.walker import list_files_recursively


class ChunkingWorkflow:
    def __init__(self, yaml_config: dict) -> None:
        self._yaml_config: dict = yaml_config

        self._init_logger()
        self._init_splitter()

        self._init_file_infos()
        return

    def _init_logger(self) -> None:
        self._logger: Logger = Logger(
            name=self._yaml_config["experiment_name"],
            dump_folder=self._yaml_config["log_dir"],
        )

    def _init_llm_client(self) -> None:
        # Dynamically import the LLM client.
        self._client_logger = Logger(name="client", dump_mode="a", dump_folder=self._yaml_config["log_dir"])

        llm_client_config = self._yaml_config["llm_client"]
        cache_location = os.path.join(
            self._yaml_config["log_dir"],
            f"{llm_client_config['cache_config']['location_prefix']}.db",
        )

        client_module = importlib.import_module(llm_client_config["module_path"])
        client_class = getattr(client_module, llm_client_config["class_name"])
        assert issubclass(client_class, BaseLLMClient)
        self._client = client_class(
            location=cache_location,
            auto_dump=llm_client_config["cache_config"]["auto_dump"],
            logger=self._client_logger,
            llm_config=llm_client_config["llm_config"],
            **llm_client_config.get("args", {}),
        )
        return

    def _init_splitter(self) -> None:
        splitter_config: dict = self._yaml_config["splitter"]
        splitter_args: dict = splitter_config.get("args", {})

        splitter_class = load_class(
            module_path=splitter_config["module_path"],
            class_name=splitter_config["class_name"],
            base_class=None,
        )

        if issubclass(splitter_class, (LLMPoweredRecursiveSplitter)):
            # Initialize LLM client
            self._init_llm_client()

            # Update args
            splitter_args["llm_client"] = self._client
            splitter_args["llm_config"] = self._yaml_config["llm_client"]["llm_config"]

            splitter_args["logger"] = self._logger

        if issubclass(splitter_class, LLMPoweredRecursiveSplitter):
            # Load protocols
            protocol_configs = self._yaml_config["chunking_protocol"]
            protocol_module = importlib.import_module(protocol_configs["module_path"])
            chunk_summary_protocol = getattr(protocol_module, protocol_configs["chunk_summary"])
            chunk_summary_refinement_protocol = getattr(protocol_module, protocol_configs["chunk_summary_refinement"])
            chunk_resplit_protocol = getattr(protocol_module, protocol_configs["chunk_resplit"])

            # Update args
            splitter_args["first_chunk_summary_protocol"] = chunk_summary_protocol
            splitter_args["last_chunk_summary_protocol"] = chunk_summary_refinement_protocol
            splitter_args["chunk_resplit_protocol"] = chunk_resplit_protocol

        self._splitter = splitter_class(**splitter_args)
        return

    def _init_file_infos(self) -> None:
        input_setting: dict = self._yaml_config.get("input_doc_setting")
        output_setting: dict = self._yaml_config.get("output_doc_setting")
        assert input_setting is not None and output_setting is not None, (
            f"input_doc_setting and output_doc_setting should be provided!"
        )

        input_file_infos = list_files_recursively(
            directory=input_setting.get("doc_dir"),
            extensions=input_setting.get("extensions"),
        )

        output_dir = output_setting.get("doc_dir")
        output_suffix = output_setting.get("suffix", "pkl")
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        self._file_infos: List[Tuple[str, str, str]] = [
            (doc_name, doc_path, os.path.join(output_dir, f"{os.path.splitext(doc_name)[0]}.{output_suffix}"))
            for doc_name, doc_path in input_file_infos
        ]
        return
    
    # 为ChunkingWorkflow类添加一个专门处理TXT文件的方法
    def _split_txt_by_lines(self, input_path, doc_name):
        """对TXT文件进行按行分片处理"""
        self._logger.info(f"对TXT文件进行按行分片: {doc_name}")
        
        # 读取文件内容
        with open(input_path, 'r', encoding='utf-8', errors='replace') as file:
            lines = file.readlines()
        
        # 过滤空行
        lines = [line.strip() for line in lines if line.strip()]
        
        # 创建文档对象
        from langchain.schema import Document
        chunk_docs = []
        
        for i, line in enumerate(lines):
            # 跳过非常短的行(可选)
            if len(line) < 3:
                continue
                
            # 为每一行创建一个文档对象
            doc = Document(
                page_content=line,
                metadata={
                    "filename": doc_name,
                    "chunk_id": f"line-{i+1}",
                    "line_number": i+1,
                    "source": input_path
                }
            )
            chunk_docs.append(doc)
        
        self._logger.info(f"TXT文件按行分片完成，共生成 {len(chunk_docs)} 个分片")
        return chunk_docs

    def run(self) -> None:
        # 初始化记录实际处理文件的列表
        self._processed_files = []
        
        for doc_name, input_path, output_path in tqdm(self._file_infos, desc="Chunking file"):
            if os.path.exists(output_path) is True:
                self._logger.info(f"Skip file: {doc_name} due to output already exist!")
                continue

            self._logger.info(f"Loading file: {doc_name}")

            # 记录实际处理了的文件
            self._processed_files.append((doc_name, input_path, output_path))

            # 检查是否为TXT文件，如果是则使用专门的行分片方法
            if input_path.lower().endswith('.txt'):
                chunk_docs = self._split_txt_by_lines(input_path, doc_name)
            else:
                # 其他文件类型使用原有逻辑
                # Try get the file loader and load documents
                doc_loader = get_loader(file_path=input_path, file_type=None)
                if doc_loader is None:
                    self._logger.info(f"Skip file {doc_name} due to undefined Document Loader.")
                    continue
                docs = doc_loader.load()

                # Add metadata
                for index, doc in enumerate(docs, start=1):
                    doc.metadata.update({"filename": doc_name})
                    doc.metadata.update({"chunk_id": str(index)})

                # Document Splitting
                chunk_docs = self._splitter.transform_documents(docs)

                # 为分割后的文档重新分配唯一ID
                for index, chunk in enumerate(chunk_docs, start=1):
                    chunk.metadata["chunk_id"] = str(index)

            # 保存逻辑保持不变
            # 根据文件扩展名选择保存方式
            if output_path.endswith('.jsonl'):
                import jsonlines
                # 将文档转换为可序列化的字典
                serializable_docs = []
                for doc in chunk_docs:
                    doc_dict = {
                        "chunk_id": doc.metadata.get("chunk_id", ""),
                        "filename": doc.metadata.get("filename", ""),
                        "content": doc.page_content,
                        "metadata": doc.metadata
                    }
                    serializable_docs.append(doc_dict)
                
                # 保存为 jsonl 文件
                with jsonlines.open(output_path, 'w') as writer:
                    for doc_dict in serializable_docs:
                        writer.write(doc_dict)
                
                self._logger.info(f"Saved {len(serializable_docs)} chunks to {output_path} in JSONL format")
            else:
                # 原始的 pkl 保存方式
                with open(output_path, "wb") as fout:
                    pickle.dump(chunk_docs, fout)
                
                self._logger.info(f"Saved {len(chunk_docs)} chunks to {output_path} in PKL format")