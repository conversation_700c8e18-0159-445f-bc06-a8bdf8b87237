#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查ArchiveBox数据库结构
"""

import sqlite3
import os

# ArchiveBox数据库路径 - 使用正确的路径分隔符
ARCHIVEBOX_DATA_PATH = r'\\wsl.localhost\Ubuntu\home\qjq\docker_archivebox\Archive_box_data'
archivebox_db_path = os.path.join(ARCHIVEBOX_DATA_PATH, 'index.sqlite3')

def check_database_structure():
    """检查数据库结构"""
    print(f"🔍 检查ArchiveBox数据库: {archivebox_db_path}")
    
    if not os.path.exists(archivebox_db_path):
        print(f"❌ 数据库文件不存在: {archivebox_db_path}")
        return
    
    try:
        conn = sqlite3.connect(archivebox_db_path)
        cursor = conn.cursor()
        
        # 1. 查看所有表
        print("\n📋 数据库中的所有表:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  - {table[0]}")
        
        # 2. 查看快照相关的表结构
        snapshot_tables = [table[0] for table in tables if 'snapshot' in table[0].lower()]
        
        for table_name in snapshot_tables:
            print(f"\n📊 表 '{table_name}' 的结构:")
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            # 查看前几条记录
            print(f"\n📄 表 '{table_name}' 的前3条记录:")
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
            rows = cursor.fetchall()
            for i, row in enumerate(rows):
                print(f"  记录{i+1}: {row}")
        
        # 3. 搜索特定的快照ID
        snapshot_id = "1753296453.240292"
        print(f"\n🔍 搜索快照ID: {snapshot_id}")
        
        for table_name in snapshot_tables:
            try:
                # 尝试不同的字段名
                possible_fields = ['timestamp', 'id', 'snapshot_id', 'pk']
                for field in possible_fields:
                    try:
                        cursor.execute(f"SELECT * FROM {table_name} WHERE {field} = ?", (snapshot_id,))
                        result = cursor.fetchone()
                        if result:
                            print(f"✅ 在表 '{table_name}' 的字段 '{field}' 中找到: {result}")
                            break
                    except sqlite3.OperationalError:
                        continue
                else:
                    # 如果没有找到，尝试模糊搜索
                    cursor.execute(f"SELECT * FROM {table_name} WHERE CAST(timestamp AS TEXT) LIKE ?", (f"%{snapshot_id}%",))
                    result = cursor.fetchone()
                    if result:
                        print(f"🔍 在表 '{table_name}' 中模糊匹配到: {result}")
            except Exception as e:
                print(f"❌ 搜索表 '{table_name}' 时出错: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

if __name__ == '__main__':
    check_database_structure()
