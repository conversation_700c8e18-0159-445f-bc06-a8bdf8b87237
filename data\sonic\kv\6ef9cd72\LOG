2025/07/17-07:59:09.635321 14 RocksDB version: 8.10.0
2025/07/17-07:59:09.638225 14 Compile date 2023-12-15 13:01:14
2025/07/17-07:59:09.638232 14 DB SUMMARY
2025/07/17-07:59:09.638233 14 Host name (Env):  c9c3f71c2fbe
2025/07/17-07:59:09.638234 14 DB Session ID:  V1QKNZRZUFYF0HH41FBR
2025/07/17-07:59:09.638932 14 SST files in /var/lib/sonic/store/kv/6ef9cd72 dir, Total Num: 0, files: 
2025/07/17-07:59:09.638935 14 Write Ahead Log file in /var/lib/sonic/store/kv/6ef9cd72: 
2025/07/17-07:59:09.638936 14                         Options.error_if_exists: 0
2025/07/17-07:59:09.638938 14                       Options.create_if_missing: 1
2025/07/17-07:59:09.638938 14                         Options.paranoid_checks: 1
2025/07/17-07:59:09.638939 14             Options.flush_verify_memtable_count: 1
2025/07/17-07:59:09.638939 14          Options.compaction_verify_record_count: 1
2025/07/17-07:59:09.638940 14                               Options.track_and_verify_wals_in_manifest: 0
2025/07/17-07:59:09.638940 14        Options.verify_sst_unique_id_in_manifest: 1
2025/07/17-07:59:09.638941 14                                     Options.env: 0x7f86040018a0
2025/07/17-07:59:09.638942 14                                      Options.fs: PosixFileSystem
2025/07/17-07:59:09.638943 14                                Options.info_log: 0x7f8604009dc0
2025/07/17-07:59:09.638943 14                Options.max_file_opening_threads: 16
2025/07/17-07:59:09.638944 14                              Options.statistics: (nil)
2025/07/17-07:59:09.638945 14                               Options.use_fsync: 0
2025/07/17-07:59:09.638945 14                       Options.max_log_file_size: 0
2025/07/17-07:59:09.638946 14                  Options.max_manifest_file_size: 1073741824
2025/07/17-07:59:09.638946 14                   Options.log_file_time_to_roll: 0
2025/07/17-07:59:09.638947 14                       Options.keep_log_file_num: 1000
2025/07/17-07:59:09.638947 14                    Options.recycle_log_file_num: 0
2025/07/17-07:59:09.638948 14                         Options.allow_fallocate: 1
2025/07/17-07:59:09.638948 14                        Options.allow_mmap_reads: 0
2025/07/17-07:59:09.638949 14                       Options.allow_mmap_writes: 0
2025/07/17-07:59:09.638949 14                        Options.use_direct_reads: 0
2025/07/17-07:59:09.638950 14                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/17-07:59:09.638950 14          Options.create_missing_column_families: 0
2025/07/17-07:59:09.638951 14                              Options.db_log_dir: 
2025/07/17-07:59:09.638951 14                                 Options.wal_dir: 
2025/07/17-07:59:09.638952 14                Options.table_cache_numshardbits: 6
2025/07/17-07:59:09.638952 14                         Options.WAL_ttl_seconds: 0
2025/07/17-07:59:09.638953 14                       Options.WAL_size_limit_MB: 0
2025/07/17-07:59:09.638953 14                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/17-07:59:09.638954 14             Options.manifest_preallocation_size: 4194304
2025/07/17-07:59:09.638954 14                     Options.is_fd_close_on_exec: 1
2025/07/17-07:59:09.638955 14                   Options.advise_random_on_open: 1
2025/07/17-07:59:09.638955 14                    Options.db_write_buffer_size: 0
2025/07/17-07:59:09.638955 14                    Options.write_buffer_manager: 0x7f860400a760
2025/07/17-07:59:09.638956 14         Options.access_hint_on_compaction_start: 1
2025/07/17-07:59:09.638956 14           Options.random_access_max_buffer_size: 1048576
2025/07/17-07:59:09.638957 14                      Options.use_adaptive_mutex: 0
2025/07/17-07:59:09.638957 14                            Options.rate_limiter: (nil)
2025/07/17-07:59:09.638958 14     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/17-07:59:09.638959 14                       Options.wal_recovery_mode: 2
2025/07/17-07:59:09.638959 14                  Options.enable_thread_tracking: 0
2025/07/17-07:59:09.638960 14                  Options.enable_pipelined_write: 0
2025/07/17-07:59:09.638960 14                  Options.unordered_write: 0
2025/07/17-07:59:09.638962 14         Options.allow_concurrent_memtable_write: 1
2025/07/17-07:59:09.638963 14      Options.enable_write_thread_adaptive_yield: 1
2025/07/17-07:59:09.638963 14             Options.write_thread_max_yield_usec: 100
2025/07/17-07:59:09.638964 14            Options.write_thread_slow_yield_usec: 3
2025/07/17-07:59:09.638964 14                               Options.row_cache: None
2025/07/17-07:59:09.638965 14                              Options.wal_filter: None
2025/07/17-07:59:09.638965 14             Options.avoid_flush_during_recovery: 0
2025/07/17-07:59:09.638966 14             Options.allow_ingest_behind: 0
2025/07/17-07:59:09.638966 14             Options.two_write_queues: 0
2025/07/17-07:59:09.638967 14             Options.manual_wal_flush: 0
2025/07/17-07:59:09.638967 14             Options.wal_compression: 0
2025/07/17-07:59:09.638968 14             Options.atomic_flush: 0
2025/07/17-07:59:09.638968 14             Options.avoid_unnecessary_blocking_io: 0
2025/07/17-07:59:09.638969 14                 Options.persist_stats_to_disk: 0
2025/07/17-07:59:09.638969 14                 Options.write_dbid_to_manifest: 0
2025/07/17-07:59:09.638970 14                 Options.log_readahead_size: 0
2025/07/17-07:59:09.638970 14                 Options.file_checksum_gen_factory: Unknown
2025/07/17-07:59:09.638971 14                 Options.best_efforts_recovery: 0
2025/07/17-07:59:09.638971 14                Options.max_bgerror_resume_count: 2147483647
2025/07/17-07:59:09.638972 14            Options.bgerror_resume_retry_interval: 1000000
2025/07/17-07:59:09.638972 14             Options.allow_data_in_errors: 0
2025/07/17-07:59:09.638973 14             Options.db_host_id: __hostname__
2025/07/17-07:59:09.638973 14             Options.enforce_single_del_contracts: true
2025/07/17-07:59:09.638974 14             Options.max_background_jobs: 2
2025/07/17-07:59:09.638975 14             Options.max_background_compactions: -1
2025/07/17-07:59:09.638975 14             Options.max_subcompactions: 1
2025/07/17-07:59:09.638976 14             Options.avoid_flush_during_shutdown: 0
2025/07/17-07:59:09.638976 14           Options.writable_file_max_buffer_size: 1048576
2025/07/17-07:59:09.638977 14             Options.delayed_write_rate : 16777216
2025/07/17-07:59:09.638977 14             Options.max_total_wal_size: 0
2025/07/17-07:59:09.638978 14             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/17-07:59:09.638978 14                   Options.stats_dump_period_sec: 600
2025/07/17-07:59:09.638979 14                 Options.stats_persist_period_sec: 600
2025/07/17-07:59:09.638979 14                 Options.stats_history_buffer_size: 1048576
2025/07/17-07:59:09.638980 14                          Options.max_open_files: 100
2025/07/17-07:59:09.638980 14                          Options.bytes_per_sync: 0
2025/07/17-07:59:09.638981 14                      Options.wal_bytes_per_sync: 0
2025/07/17-07:59:09.638981 14                   Options.strict_bytes_per_sync: 0
2025/07/17-07:59:09.638982 14       Options.compaction_readahead_size: 2097152
2025/07/17-07:59:09.638982 14                  Options.max_background_flushes: -1
2025/07/17-07:59:09.638983 14 Options.daily_offpeak_time_utc: 
2025/07/17-07:59:09.638983 14 Compression algorithms supported:
2025/07/17-07:59:09.638985 14 	kZSTD supported: 1
2025/07/17-07:59:09.638985 14 	kXpressCompression supported: 0
2025/07/17-07:59:09.638986 14 	kBZip2Compression supported: 1
2025/07/17-07:59:09.638987 14 	kZSTDNotFinalCompression supported: 1
2025/07/17-07:59:09.638988 14 	kLZ4Compression supported: 1
2025/07/17-07:59:09.638988 14 	kZlibCompression supported: 1
2025/07/17-07:59:09.638989 14 	kLZ4HCCompression supported: 1
2025/07/17-07:59:09.638989 14 	kSnappyCompression supported: 1
2025/07/17-07:59:09.638991 14 Fast CRC32 supported: Not supported on x86
2025/07/17-07:59:09.638991 14 DMutex implementation: pthread_mutex_t
2025/07/17-07:59:09.646663 14 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/07/17-07:59:09.655765 14 [db/version_set.cc:5941] Recovering from manifest file: /var/lib/sonic/store/kv/6ef9cd72/MANIFEST-000001
2025/07/17-07:59:09.657277 14 [db/column_family.cc:616] --------------- Options for column family [default]:
2025/07/17-07:59:09.657281 14               Options.comparator: leveldb.BytewiseComparator
2025/07/17-07:59:09.657282 14           Options.merge_operator: None
2025/07/17-07:59:09.657283 14        Options.compaction_filter: None
2025/07/17-07:59:09.657284 14        Options.compaction_filter_factory: None
2025/07/17-07:59:09.657284 14  Options.sst_partitioner_factory: None
2025/07/17-07:59:09.657285 14         Options.memtable_factory: SkipListFactory
2025/07/17-07:59:09.657285 14            Options.table_factory: BlockBasedTable
2025/07/17-07:59:09.657308 14            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f8604000cd0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7f8604002c10
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/17-07:59:09.657308 14        Options.write_buffer_size: 16777216
2025/07/17-07:59:09.657309 14  Options.max_write_buffer_number: 2
2025/07/17-07:59:09.657310 14          Options.compression: ZSTD
2025/07/17-07:59:09.657311 14                  Options.bottommost_compression: Disabled
2025/07/17-07:59:09.657311 14       Options.prefix_extractor: nullptr
2025/07/17-07:59:09.657312 14   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/17-07:59:09.657312 14             Options.num_levels: 7
2025/07/17-07:59:09.657313 14        Options.min_write_buffer_number_to_merge: 1
2025/07/17-07:59:09.657313 14     Options.max_write_buffer_number_to_maintain: 0
2025/07/17-07:59:09.657314 14     Options.max_write_buffer_size_to_maintain: 0
2025/07/17-07:59:09.657314 14            Options.bottommost_compression_opts.window_bits: -14
2025/07/17-07:59:09.657315 14                  Options.bottommost_compression_opts.level: 32767
2025/07/17-07:59:09.657315 14               Options.bottommost_compression_opts.strategy: 0
2025/07/17-07:59:09.657316 14         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/17-07:59:09.657316 14         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/17-07:59:09.657317 14         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/17-07:59:09.657317 14                  Options.bottommost_compression_opts.enabled: false
2025/07/17-07:59:09.657318 14         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/17-07:59:09.657318 14         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/17-07:59:09.657319 14            Options.compression_opts.window_bits: -14
2025/07/17-07:59:09.657319 14                  Options.compression_opts.level: 32767
2025/07/17-07:59:09.657320 14               Options.compression_opts.strategy: 0
2025/07/17-07:59:09.657320 14         Options.compression_opts.max_dict_bytes: 0
2025/07/17-07:59:09.657321 14         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/17-07:59:09.657321 14         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/17-07:59:09.657323 14         Options.compression_opts.parallel_threads: 1
2025/07/17-07:59:09.657323 14                  Options.compression_opts.enabled: false
2025/07/17-07:59:09.657324 14         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/17-07:59:09.657324 14      Options.level0_file_num_compaction_trigger: 4
2025/07/17-07:59:09.657325 14          Options.level0_slowdown_writes_trigger: 20
2025/07/17-07:59:09.657325 14              Options.level0_stop_writes_trigger: 36
2025/07/17-07:59:09.657326 14                   Options.target_file_size_base: 67108864
2025/07/17-07:59:09.657326 14             Options.target_file_size_multiplier: 1
2025/07/17-07:59:09.657327 14                Options.max_bytes_for_level_base: 268435456
2025/07/17-07:59:09.657327 14 Options.level_compaction_dynamic_level_bytes: 1
2025/07/17-07:59:09.657328 14          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/17-07:59:09.657329 14 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/17-07:59:09.657330 14 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/17-07:59:09.657330 14 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/17-07:59:09.657331 14 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/17-07:59:09.657331 14 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/17-07:59:09.657332 14 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/17-07:59:09.657332 14 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/17-07:59:09.657333 14       Options.max_sequential_skip_in_iterations: 8
2025/07/17-07:59:09.657333 14                    Options.max_compaction_bytes: 1677721600
2025/07/17-07:59:09.657334 14   Options.ignore_max_compaction_bytes_for_input: true
2025/07/17-07:59:09.657334 14                        Options.arena_block_size: 1048576
2025/07/17-07:59:09.657335 14   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/17-07:59:09.657335 14   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/17-07:59:09.657336 14                Options.disable_auto_compactions: 0
2025/07/17-07:59:09.657338 14                        Options.compaction_style: kCompactionStyleLevel
2025/07/17-07:59:09.657339 14                          Options.compaction_pri: kMinOverlappingRatio
2025/07/17-07:59:09.657339 14 Options.compaction_options_universal.size_ratio: 1
2025/07/17-07:59:09.657340 14 Options.compaction_options_universal.min_merge_width: 2
2025/07/17-07:59:09.657340 14 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/17-07:59:09.657341 14 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/17-07:59:09.657341 14 Options.compaction_options_universal.compression_size_percent: -1
2025/07/17-07:59:09.657342 14 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/17-07:59:09.657342 14 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/17-07:59:09.657343 14 Options.compaction_options_fifo.allow_compaction: 0
2025/07/17-07:59:09.657349 14                   Options.table_properties_collectors: 
2025/07/17-07:59:09.657349 14                   Options.inplace_update_support: 0
2025/07/17-07:59:09.657350 14                 Options.inplace_update_num_locks: 10000
2025/07/17-07:59:09.657350 14               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/17-07:59:09.657351 14               Options.memtable_whole_key_filtering: 0
2025/07/17-07:59:09.657352 14   Options.memtable_huge_page_size: 0
2025/07/17-07:59:09.657352 14                           Options.bloom_locality: 0
2025/07/17-07:59:09.657353 14                    Options.max_successive_merges: 0
2025/07/17-07:59:09.657353 14                Options.optimize_filters_for_hits: 0
2025/07/17-07:59:09.657354 14                Options.paranoid_file_checks: 0
2025/07/17-07:59:09.657354 14                Options.force_consistency_checks: 1
2025/07/17-07:59:09.657355 14                Options.report_bg_io_stats: 0
2025/07/17-07:59:09.657355 14                               Options.ttl: 2592000
2025/07/17-07:59:09.657366 14          Options.periodic_compaction_seconds: 0
2025/07/17-07:59:09.657367 14                        Options.default_temperature: kUnknown
2025/07/17-07:59:09.657368 14  Options.preclude_last_level_data_seconds: 0
2025/07/17-07:59:09.657368 14    Options.preserve_internal_time_seconds: 0
2025/07/17-07:59:09.657368 14                       Options.enable_blob_files: false
2025/07/17-07:59:09.657369 14                           Options.min_blob_size: 0
2025/07/17-07:59:09.657369 14                          Options.blob_file_size: 268435456
2025/07/17-07:59:09.657370 14                   Options.blob_compression_type: NoCompression
2025/07/17-07:59:09.657370 14          Options.enable_blob_garbage_collection: false
2025/07/17-07:59:09.657371 14      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/17-07:59:09.657372 14 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/17-07:59:09.657372 14          Options.blob_compaction_readahead_size: 0
2025/07/17-07:59:09.657373 14                Options.blob_file_starting_level: 0
2025/07/17-07:59:09.657373 14         Options.experimental_mempurge_threshold: 0.000000
2025/07/17-07:59:09.657374 14            Options.memtable_max_range_deletions: 0
2025/07/17-07:59:09.659526 14 [db/version_set.cc:5984] Recovered from manifest file:/var/lib/sonic/store/kv/6ef9cd72/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/17-07:59:09.659529 14 [db/version_set.cc:5999] Column family [default] (ID 0), log number is 0
2025/07/17-07:59:09.660869 14 [db/db_impl/db_impl_open.cc:646] DB ID: 9874d48b-8de0-4bd5-a444-e005e6a4486a
2025/07/17-07:59:09.663106 14 [db/version_set.cc:5438] Creating manifest 5
2025/07/17-07:59:09.680561 14 [db/db_impl/db_impl_open.cc:2156] SstFileManager instance 0x7f860400ecf0
2025/07/17-07:59:09.681199 14 DB pointer 0x7f860400a840
2025/07/17-07:59:09.681500 33 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/07/17-07:59:09.681508 33 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x7f8604002c10#1 capacity: 32.00 MB seed: 1824344069 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 3.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/17-08:09:09.681812 33 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/07/17-08:09:09.715887 33 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 600.0 total, 600.0 interval
Cumulative writes: 675 writes, 675 keys, 675 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 675 writes, 0 syncs, 675.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 675 writes, 675 keys, 675 commit groups, 1.0 writes per commit group, ingest: 0.03 MB, 0.00 MB/s
Interval WAL: 675 writes, 0 syncs, 675.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x7f8604002c10#1 capacity: 32.00 MB seed: 1824344069 usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 5e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/17-08:14:18.194924 9 [db/db_impl/db_impl_compaction_flush.cc:1951] [default] Manual flush start.
2025/07/17-08:14:18.197652 9 [db/db_impl/db_impl_write.cc:2178] [default] New memtable created with log file: #8. Immutable memtables: 0.
2025/07/17-08:14:18.198830 17 (Original Log Time 2025/07/17-08:14:18.198635) [db/db_impl/db_impl_compaction_flush.cc:3217] Calling FlushMemTableToOutputFile with column family [default], flush slots available 1, compaction slots available 1, flush slots scheduled 1, compaction slots scheduled 0
2025/07/17-08:14:18.198834 17 [db/flush_job.cc:892] [default] [JOB 2] Flushing memtable with next log file: 8
2025/07/17-08:14:18.199036 17 EVENT_LOG_v1 {"time_micros": 1752740058199026, "job": 2, "event": "flush_started", "num_memtables": 1, "num_entries": 675, "num_deletes": 0, "total_data_size": 24812, "memory_usage": 34912, "num_range_deletes": 0, "flush_reason": "Manual Flush"}
2025/07/17-08:14:18.199120 17 [db/flush_job.cc:926] [default] [JOB 2] Level-0 flush table #9: started
2025/07/17-08:14:18.211524 17 EVENT_LOG_v1 {"time_micros": 1752740058211468, "cf_name": "default", "job": 2, "event": "table_file_creation", "file_number": 9, "file_size": 9403, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 2, "largest_seqno": 675, "table_properties": {"data_size": 8347, "index_size": 71, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 10421, "raw_average_key_size": 17, "raw_value_size": 5361, "raw_average_value_size": 8, "num_data_blocks": 4, "num_entries": 613, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "ZSTD", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1752739149, "oldest_key_time": 1752739149, "file_creation_time": 1752740058, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9874d48b-8de0-4bd5-a444-e005e6a4486a", "db_session_id": "V1QKNZRZUFYF0HH41FBR", "orig_file_number": 9, "seqno_to_time_mapping": "N/A"}}
2025/07/17-08:14:18.211844 17 [db/flush_job.cc:1062] [default] [JOB 2] Flush lasted 13015 microseconds, and 5932 cpu microseconds.
2025/07/17-08:14:18.214804 17 (Original Log Time 2025/07/17-08:14:18.211650) [db/flush_job.cc:1011] [default] [JOB 2] Level-0 flush table #9: 9403 bytes OK
2025/07/17-08:14:18.214807 17 (Original Log Time 2025/07/17-08:14:18.211866) [db/memtable_list.cc:555] [default] Level-0 commit table #9 started
2025/07/17-08:14:18.214808 17 (Original Log Time 2025/07/17-08:14:18.214271) [db/memtable_list.cc:754] [default] Level-0 commit table #9: memtable #1 done
2025/07/17-08:14:18.214809 17 (Original Log Time 2025/07/17-08:14:18.214288) EVENT_LOG_v1 {"time_micros": 1752740058214284, "job": 2, "event": "flush_finished", "output_compression": "ZSTD", "lsm_state": [1, 0, 0, 0, 0, 0, 0], "immutable_memtables": 0}
2025/07/17-08:14:18.214810 17 (Original Log Time 2025/07/17-08:14:18.214353) [db/db_impl/db_impl_compaction_flush.cc:354] [default] Level summary: files[1 0 0 0 0 0 0] max score 0.25
2025/07/17-08:14:18.214894 17 [db/db_impl/db_impl_files.cc:476] [JOB 2] Try to delete WAL files size 28187, prev total WAL file size 28187, number of live WAL files 2.
2025/07/17-08:14:18.216275 17 [file/delete_scheduler.cc:73] Deleted file /var/lib/sonic/store/kv/6ef9cd72/000004.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/17-08:14:18.216467 9 [db/db_impl/db_impl_compaction_flush.cc:1961] [default] Manual flush finished, status: OK
2025/07/17-08:19:09.716417 33 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/07/17-08:19:09.716950 33 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 1200.1 total, 600.0 interval
Cumulative writes: 846 writes, 846 keys, 846 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 846 writes, 0 syncs, 846.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 171 writes, 171 keys, 171 commit groups, 1.0 writes per commit group, ingest: 0.01 MB, 0.00 MB/s
Interval WAL: 171 writes, 0 syncs, 171.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    9.18 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.01              0.01         1    0.013       0      0       0.0       0.0
 Sum      1/0    9.18 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.01              0.01         1    0.013       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.01              0.01         1    0.013       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.01              0.01         1    0.013       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1200.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x7f8604002c10#1 capacity: 32.00 MB seed: 1824344069 usage: 12.24 KB table_size: 1024 occupancy: 4 collections: 3 last_copies: 0 last_secs: 8.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,11.90 KB,0.0363082%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/17-08:29:09.717267 33 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/07/17-08:29:09.719933 33 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 1800.1 total, 600.0 interval
Cumulative writes: 1214 writes, 1214 keys, 1214 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 1214 writes, 0 syncs, 1214.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 368 writes, 368 keys, 368 commit groups, 1.0 writes per commit group, ingest: 0.02 MB, 0.00 MB/s
Interval WAL: 368 writes, 0 syncs, 368.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    9.18 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.01              0.01         1    0.013       0      0       0.0       0.0
 Sum      1/0    9.18 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.01              0.01         1    0.013       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.01              0.01         1    0.013       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1800.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x7f8604002c10#1 capacity: 32.00 MB seed: 1824344069 usage: 12.24 KB table_size: 1024 occupancy: 4 collections: 4 last_copies: 0 last_secs: 4.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,11.90 KB,0.0363082%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/17-08:29:28.193193 9 [db/db_impl/db_impl_compaction_flush.cc:1951] [default] Manual flush start.
2025/07/17-08:29:28.202568 9 [db/db_impl/db_impl_write.cc:2178] [default] New memtable created with log file: #10. Immutable memtables: 0.
2025/07/17-08:29:28.206704 17 (Original Log Time 2025/07/17-08:29:28.205178) [db/db_impl/db_impl_compaction_flush.cc:3217] Calling FlushMemTableToOutputFile with column family [default], flush slots available 1, compaction slots available 1, flush slots scheduled 1, compaction slots scheduled 0
2025/07/17-08:29:28.206709 17 [db/flush_job.cc:892] [default] [JOB 3] Flushing memtable with next log file: 10
2025/07/17-08:29:28.207086 17 EVENT_LOG_v1 {"time_micros": 1752740968206916, "job": 3, "event": "flush_started", "num_memtables": 1, "num_entries": 539, "num_deletes": 0, "total_data_size": 19618, "memory_usage": 28648, "num_range_deletes": 0, "flush_reason": "Manual Flush"}
2025/07/17-08:29:28.207137 17 [db/flush_job.cc:926] [default] [JOB 3] Level-0 flush table #11: started
2025/07/17-08:29:28.229635 17 EVENT_LOG_v1 {"time_micros": 1752740968229509, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 11, "file_size": 8171, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 677, "largest_seqno": 1214, "table_properties": {"data_size": 7126, "index_size": 60, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 8517, "raw_average_key_size": 17, "raw_value_size": 4953, "raw_average_value_size": 9, "num_data_blocks": 3, "num_entries": 501, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "ZSTD", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1752740207, "oldest_key_time": 1752740207, "file_creation_time": 1752740968, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "9874d48b-8de0-4bd5-a444-e005e6a4486a", "db_session_id": "V1QKNZRZUFYF0HH41FBR", "orig_file_number": 11, "seqno_to_time_mapping": "N/A"}}
2025/07/17-08:29:28.230426 17 [db/flush_job.cc:1062] [default] [JOB 3] Flush lasted 23726 microseconds, and 16447 cpu microseconds.
2025/07/17-08:29:28.235771 17 (Original Log Time 2025/07/17-08:29:28.229847) [db/flush_job.cc:1011] [default] [JOB 3] Level-0 flush table #11: 8171 bytes OK
2025/07/17-08:29:28.235775 17 (Original Log Time 2025/07/17-08:29:28.230444) [db/memtable_list.cc:555] [default] Level-0 commit table #11 started
2025/07/17-08:29:28.235776 17 (Original Log Time 2025/07/17-08:29:28.235061) [db/memtable_list.cc:754] [default] Level-0 commit table #11: memtable #1 done
2025/07/17-08:29:28.235776 17 (Original Log Time 2025/07/17-08:29:28.235082) EVENT_LOG_v1 {"time_micros": 1752740968235078, "job": 3, "event": "flush_finished", "output_compression": "ZSTD", "lsm_state": [2, 0, 0, 0, 0, 0, 0], "immutable_memtables": 0}
2025/07/17-08:29:28.235777 17 (Original Log Time 2025/07/17-08:29:28.235120) [db/db_impl/db_impl_compaction_flush.cc:354] [default] Level summary: files[2 0 0 0 0 0 0] max score 0.50
2025/07/17-08:29:28.235860 17 [db/db_impl/db_impl_files.cc:476] [JOB 3] Try to delete WAL files size 22313, prev total WAL file size 22313, number of live WAL files 2.
2025/07/17-08:29:28.237692 17 [file/delete_scheduler.cc:73] Deleted file /var/lib/sonic/store/kv/6ef9cd72/000008.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/17-08:29:28.237881 9 [db/db_impl/db_impl_compaction_flush.cc:1961] [default] Manual flush finished, status: OK
2025/07/17-08:39:09.720334 33 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/07/17-08:39:09.723289 33 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 2400.1 total, 600.0 interval
Cumulative writes: 1214 writes, 1214 keys, 1214 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 1214 writes, 0 syncs, 1214.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   17.16 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.04              0.02         2    0.018       0      0       0.0       0.0
 Sum      2/0   17.16 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.04              0.02         2    0.018       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.3      0.02              0.02         1    0.024       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.5      0.04              0.02         2    0.018       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 2400.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x7f8604002c10#1 capacity: 32.00 MB seed: 1824344069 usage: 12.24 KB table_size: 1024 occupancy: 4 collections: 5 last_copies: 0 last_secs: 6.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,11.90 KB,0.0363082%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/17-08:44:38.231255 9 [db/db_impl/db_impl_compaction_flush.cc:1951] [default] Manual flush start.
2025/07/17-08:44:38.233190 9 [db/db_impl/db_impl_compaction_flush.cc:1961] [default] Manual flush finished, status: OK
2025/07/17-08:49:09.723985 33 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/07/17-08:49:09.726955 33 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 3000.1 total, 600.0 interval
Cumulative writes: 1214 writes, 1214 keys, 1214 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 1214 writes, 0 syncs, 1214.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   17.16 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.04              0.02         2    0.018       0      0       0.0       0.0
 Sum      2/0   17.16 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.04              0.02         2    0.018       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
High      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.5      0.04              0.02         2    0.018       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3000.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x7f8604002c10#1 capacity: 32.00 MB seed: 1824344069 usage: 12.24 KB table_size: 1024 occupancy: 4 collections: 6 last_copies: 0 last_secs: 0.000144 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(3,11.90 KB,0.0363082%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/17-08:57:08.216739 9 [db/db_impl/db_impl.cc:485] Shutdown: canceling all background work
2025/07/17-08:57:08.236660 9 [db/db_impl/db_impl.cc:667] Shutdown complete
