# 从SQLite转换到MySQL
import mysql.connector
from mysql.connector import Error
import pandas as pd
import os

# MySQL 数据库配置
DB_CONFIG = {
    'host': '************',
    'user': 'likai5',
    'password': 'likai5241203',
    'database': 'cyber_TI',
    'port': 3306,
    'charset': 'utf8mb4'
}

def check_table_exists(cursor, table_name):
    """检查表是否存在"""
    cursor.execute("""
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = %s
        AND table_name = %s
    """, (DB_CONFIG['database'], table_name))
    
    return cursor.fetchone()[0] > 0

def excel_to_mysql(excel_path: str) -> None:
    """将Excel数据导入到MySQL数据库"""

    # 创建数据库连接
    conn = None
    cursor = None
    
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查系统表是否已存在，如不存在则创建
        # crawled_data 表 - 存储爬取结果
        if not check_table_exists(cursor, 'crawled_data'):
            print("创建 crawled_data 表...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS crawled_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                query TEXT NOT NULL,
                title TEXT,
                link VARCHAR(1024) NOT NULL,
                snippet TEXT,
                html_content LONGTEXT,
                extracted_text LONGTEXT,
                crawl_status TINYINT DEFAULT 0,
                crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_link (link(768))
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')
            print("crawled_data 表创建成功")
        else:
            print("crawled_data 表已存在，跳过创建")

        # NewRetrieval 表 - 存储搜索语法
        if not check_table_exists(cursor, 'NewRetrieval'):
            print("创建 NewRetrieval 表...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS NewRetrieval (
                id INT AUTO_INCREMENT PRIMARY KEY,
                search_syntax TEXT,
                use_status TINYINT DEFAULT 0,
                UNIQUE KEY unique_syntax (search_syntax(768)),
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')
            print("NewRetrieval 表创建成功")
        else:
            print("NewRetrieval 表已存在，跳过创建")

        # analysis_results 表 - 存储分析结果
        if not check_table_exists(cursor, 'analysis_results'):
            print("创建 analysis_results 表...")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis_results (
                id INT AUTO_INCREMENT PRIMARY KEY,
                crawled_data_id INT NOT NULL,
                analysis_status TINYINT DEFAULT 0,
                report_summary TEXT,
                keywords TEXT,
                aliases TEXT,
                organization_type TEXT,
                
                -- 攻击模式信息
                attack_title TEXT,
                attack_date TEXT,
                attack_source TEXT,
                
                -- 时间线
                first_seen TEXT,
                last_seen TEXT,
                attack_pattern TEXT,
                
                -- TTPs (存储为JSON)
                ttps JSON,
                
                -- 基础设施
                domains JSON,
                urls JSON,
                hashes JSON,
                c2_protocols TEXT,
                fast_flux TINYINT,
                
                -- 攻击者信息
                actors JSON,
                
                -- 检测指标
                detection_files TEXT,
                detection_registry TEXT,
                detection_network TEXT,
                
                analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                FOREIGN KEY (crawled_data_id) REFERENCES crawled_data(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ''')
            print("analysis_results 表创建成功")
        else:
            print("analysis_results 表已存在，跳过创建")

        # 合并artificial和auto表
        combined_df = pd.DataFrame()
        for sheet in ['artificial', 'auto']:
            df = pd.read_excel(excel_path, sheet_name=sheet)
            combined_df = pd.concat([combined_df, df], ignore_index=True)

        # 按类型分组处理
        for keywords_type, group in combined_df.groupby('Keywords_type'):
            # 表名映射规则
            if keywords_type == "group":
                table_name = "gp"
            elif keywords_type == "Malicious Software":
                table_name = "ms"
            else:
                table_name = keywords_type.replace(' ', '_').replace('-', '_')
            
            # 检查表是否已存在
            if check_table_exists(cursor, table_name):
                # 检查表是否为空
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                row_count = cursor.fetchone()[0]
                
                if row_count > 0:
                    print(f"表 '{table_name}' 已存在且有 {row_count} 条数据，跳过数据导入")
                    continue
                else:
                    print(f"表 '{table_name}' 已存在但为空，将导入数据")
                    # 继续执行下面的导入代码
            else:
                # 表不存在，创建表
                print(f"创建表 '{table_name}'...")
                
                # 创建自定义表
                columns = []
                for col in group.columns:
                    if col == 'Describe':
                        col_type = "LONGTEXT"
                    else:
                        col_type = "TEXT"
                    columns.append(f"`{col}` {col_type}")
                
                create_table_sql = f'''
                CREATE TABLE IF NOT EXISTS `{table_name}` (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    {', '.join(columns)}
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                '''
                
                cursor.execute(create_table_sql)
            
            # 插入数据 (对于新创建的表或已存在但为空的表)
            print(f"向表 '{table_name}' 插入 {len(group)} 条数据...")
            for _, row in group.iterrows():
                # 处理NaN值
                values = []
                for val in row:
                    if pd.isna(val):
                        values.append(None)
                    else:
                        values.append(val)
                
                placeholders = ', '.join(['%s'] * len(group.columns))
                columns_escaped = ', '.join([f"`{col}`" for col in group.columns])
                insert_sql = f"INSERT INTO `{table_name}` ({columns_escaped}) VALUES ({placeholders})"
                
                cursor.execute(insert_sql, tuple(values))

        # 处理campaign表
        if not check_table_exists(cursor, 'ca'):
            print("创建 'ca' 表...")
            campaign_df = pd.read_excel(excel_path, sheet_name='campaign')
            
            # 首先获取Excel文件中的实际列名
            columns = []
            for col in campaign_df.columns:
                # 为所有列使用LONGTEXT类型，以避免长度问题
                col_type = "LONGTEXT"
                columns.append(f"`{col}` {col_type}")
            
            # 使用动态列定义创建表
            create_table_sql = f'''
            CREATE TABLE IF NOT EXISTS `ca` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                {', '.join(columns)}
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            '''
            
            cursor.execute(create_table_sql)
            
            # 插入campaign数据
            print(f"向表 'ca' 插入 {len(campaign_df)} 条数据...")
            for _, row in campaign_df.iterrows():
                values = []
                for val in row:
                    values.append(None if pd.isna(val) else val)
                
                columns_escaped = ', '.join([f"`{col}`" for col in campaign_df.columns])
                placeholders = ', '.join(['%s'] * len(campaign_df.columns))
                insert_sql = f"INSERT INTO `ca` ({columns_escaped}) VALUES ({placeholders})"
                
                cursor.execute(insert_sql, tuple(values))
        else:
            # 检查表是否为空
            cursor.execute("SELECT COUNT(*) FROM `ca`")
            row_count = cursor.fetchone()[0]
            
            if row_count > 0:
                print(f"表 'ca' 已存在且有 {row_count} 条数据，跳过数据导入")
            else:
                print("表 'ca' 已存在但为空，将导入数据")
                campaign_df = pd.read_excel(excel_path, sheet_name='campaign')
                
                # 首先获取表中的实际列名
                cursor.execute("SHOW COLUMNS FROM `ca`")
                table_columns = [column[0] for column in cursor.fetchall() if column[0] != 'id']
                
                # 获取Excel中的列名
                excel_columns = campaign_df.columns.tolist()
                
                # 检查列名是否匹配
                if set(table_columns) != set(excel_columns):
                    print(f"警告：表结构与Excel不匹配!")
                    print(f"表列名: {table_columns}")
                    print(f"Excel列名: {excel_columns}")
                    print("重新创建表结构...")
                    
                    # 删除旧表并重新创建
                    cursor.execute("DROP TABLE `ca`;")
                    
                    # 重新创建表
                    columns = []
                    for col in campaign_df.columns:
                        col_type = "LONGTEXT"
                        columns.append(f"`{col}` {col_type}")
                    
                    create_table_sql = f'''
                    CREATE TABLE IF NOT EXISTS `ca` (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        {', '.join(columns)}
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    '''
                    
                    cursor.execute(create_table_sql)
                
                # 导入数据
                print(f"向表 'ca' 插入 {len(campaign_df)} 条数据...")
                for _, row in campaign_df.iterrows():
                    values = []
                    for val in row:
                        values.append(None if pd.isna(val) else val)
                    
                    columns_escaped = ', '.join([f"`{col}`" for col in campaign_df.columns])
                    placeholders = ', '.join(['%s'] * len(campaign_df.columns))
                    insert_sql = f"INSERT INTO `ca` ({columns_escaped}) VALUES ({placeholders})"
                    
                    cursor.execute(insert_sql, tuple(values))

        # 同样修改link表的处理方式
        if not check_table_exists(cursor, 'li'):
            print("创建 'li' 表...")
            link_df = pd.read_excel(excel_path, sheet_name='link')
            
            # 获取Excel文件中的列名
            columns = []
            for col in link_df.columns:
                col_type = "TEXT"
                columns.append(f"`{col}` {col_type}")
            
            # 创建表
            create_table_sql = f'''
            CREATE TABLE IF NOT EXISTS `li` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                {', '.join(columns)}
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            '''
            
            cursor.execute(create_table_sql)
            
            # 插入数据
            print(f"向表 'li' 插入 {len(link_df)} 条数据...")
            for _, row in link_df.iterrows():
                values = []
                for val in row:
                    values.append(None if pd.isna(val) else val)
                
                placeholders = ', '.join(['%s'] * len(link_df.columns))
                columns_escaped = ', '.join([f"`{col}`" for col in link_df.columns])
                insert_sql = f"INSERT INTO `li` ({columns_escaped}) VALUES ({placeholders})"
                
                cursor.execute(insert_sql, tuple(values))
        else:
            # 检查表是否为空
            cursor.execute("SELECT COUNT(*) FROM `li`")
            row_count = cursor.fetchone()[0]
            
            if row_count > 0:
                print(f"表 'li' 已存在且有 {row_count} 条数据，跳过数据导入")
            else:
                print("表 'li' 已存在但为空，将导入数据")
                link_df = pd.read_excel(excel_path, sheet_name='link')

                # 首先获取表中的实际列名
                cursor.execute("SHOW COLUMNS FROM `li`")
                table_columns = [column[0] for column in cursor.fetchall() if column[0] != 'id']
                
                # 获取Excel中的列名
                excel_columns = link_df.columns.tolist()
                
                # 检查列名是否匹配
                if set(table_columns) != set(excel_columns):
                    print(f"警告：表结构与Excel不匹配!")
                    print(f"表列名: {table_columns}")
                    print(f"Excel列名: {excel_columns}")
                    print("重新创建表结构...")
                    
                    # 删除旧表并重新创建
                    cursor.execute("DROP TABLE `li`;")
                    
                    # 重新创建表
                    columns = []
                    for col in link_df.columns:
                        col_type = "TEXT"
                        columns.append(f"`{col}` {col_type}")
                    
                    create_table_sql = f'''
                    CREATE TABLE IF NOT EXISTS `li` (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        {', '.join(columns)}
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    '''
                    
                    cursor.execute(create_table_sql)
                
                # 导入数据的代码...

                print(f"向表 'li' 插入 {len(link_df)} 条数据...")
                for _, row in link_df.iterrows():
                    values = []
                    for val in row:
                        values.append(None if pd.isna(val) else val)
                    
                    placeholders = ', '.join(['%s'] * len(link_df.columns))
                    columns_escaped = ', '.join([f"`{col}`" for col in link_df.columns])
                    insert_sql = f"INSERT INTO `li` ({columns_escaped}) VALUES ({placeholders})"
                    
                    cursor.execute(insert_sql, tuple(values))

        # 提交事务
        conn.commit()
        print("数据操作完成！")

    except Error as e:
        print(f"MySQL错误: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"发生错误: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()
            print("MySQL连接已关闭")


def reset_retrieval_status(id_value=1):
    """重置特定ID的NewRetrieval记录状态为未使用(0)"""
    conn = None
    cursor = None
    
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 检查是否已存在唯一索引
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
            AND table_name = 'crawled_data' 
            AND index_name = 'unique_link'
        """)
        index_exists = cursor.fetchone()[0] > 0
        
        # 如果索引不存在，则添加
        if not index_exists:
            print("为 crawled_data 表的 link 字段添加唯一索引...")
            cursor.execute("""
                ALTER TABLE crawled_data ADD UNIQUE KEY unique_link (link(768));
            """)
            print("唯一索引添加成功")

        # 检查是否已存在唯一索引
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
            AND table_name = 'NewRetrieval' 
            AND index_name = 'unique_syntax'
        """)
        index_exists = cursor.fetchone()[0] > 0
        
        # 如果索引不存在，则添加
        if not index_exists:
            print("为 NewRetrieval 表的 search_syntax 字段添加唯一索引...")
            cursor.execute("""
                ALTER TABLE NewRetrieval ADD UNIQUE KEY unique_syntax (search_syntax(768));
            """)
            print("唯一索引添加成功")
    
        # 执行更新语句
        cursor.execute("""
            UPDATE NewRetrieval
            SET use_status = 0
            WHERE id = %s
        """, (id_value,))
        
        # 提交更改
        conn.commit()
        
        # 返回受影响的行数
        affected_rows = cursor.rowcount
        print(f"更新了 {affected_rows} 条记录，ID={id_value} 的检索状态已重置为未使用")
        
    except Error as e:
        print(f"MySQL错误: {e}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()
            print("MySQL连接已关闭")


if __name__ == '__main__':
    # current_dir = os.path.dirname(os.path.abspath(__file__))
    # excel_path = os.path.join(current_dir, '判断标准.xlsx')
    # excel_to_mysql(excel_path=excel_path)
    reset_retrieval_status(id_value=2)