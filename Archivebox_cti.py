import argparse
import csv
import json
from dotenv import load_dotenv
import mysql.connector
import os
import subprocess
import sys
import re
import time
import threading
import traceback
from urllib.parse import urlparse
from urllib.robotparser import RobotFileParser
from bs4 import BeautifulSoup
from htmldate import find_date
import mysql
from mysql.connector import Error
import requests
from tqdm import tqdm
import logging
from datetime import datetime, timedelta

import urllib

import os
import json
import logging
from datetime import datetime

# 在linux中
# try:
#     # 尝试导入并替换sqlite3
#     __import__('pysqlite3')
#     sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')
#     # 验证版本
#     import sqlite3
#     print(f"Using pysqlite3 version: {sqlite3.sqlite_version}")
# except ImportError:
#     print("无法导入pysqlite3，请先安装: pip install pysqlite3-binary")
#     print("继续使用系统sqlite3，但可能会出错")


from mian_data_pa import save_to_database
from RAG.utils import extract_wechat_article_date

dotenv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db.env')
load_dotenv(dotenv_path)


# $env:PYTHONPATH = "K:\CTI\Data_Excation;$env:PYTHONPATH"

DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

# ArchiveBox配置
ARCHIVEBOX_DATA_PATH = os.getenv('ARCHIVEBOX_DATA_PATH', '/home/<USER>/docker_archivebox/Archive_box_data')


# 日志
# 保存到output.txt
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logging.FileHandler('output.txt', mode='w', encoding='utf-8')

logging.info("正在初始化RAG分析器...")

# 全局变量声明
_analyzer_instance = None

# 初始化部分 - 移除第一个初始化代码块
try:
    from AI_copy import RAGAnalyzer
    from RAG.utils import web_analyze
    
    # 只有在不是主应用初始化时才初始化
    if 'RAG_INITIALIZING' not in os.environ:
        # 直接初始化分析器实例
        _analyzer_instance = RAGAnalyzer(db_config=DB_CONFIG)
        logging.info("RAG分析器初始化成功")
    else:
        print("pa_week: 检测到主应用正在初始化，跳过RAG初始化")
except ImportError as e:
    _analyzer_instance = None
    logging.error(f"无法导入 RAGAnalyzer 类，错误信息: {e}")
    logging.error(f"当前工作目录: {os.getcwd()}")
    logging.error(f"Python路径: {sys.path}")
    traceback.print_exc()
except Exception as e:
    _analyzer_instance = None
    logging.error(f"初始化RAG分析器失败: {e}")
    traceback.print_exc()

def get_analyzer_instance():
    """获取RAGAnalyzer实例"""
    global _analyzer_instance
    if _analyzer_instance is None:
        # 如果初始化失败，尝试重新初始化
        try:
            logging.info("尝试重新初始化RAG分析器...")
            _analyzer_instance = RAGAnalyzer(db_config=DB_CONFIG)
            logging.info("RAG分析器重新初始化成功")
        except Exception as e:
            logging.error(f"初始化RAG分析器失败: {e}")
            traceback.print_exc()
    return _analyzer_instance


class SnapshotManager:
    """管理ArchiveBox快照ID的类"""
    
    def __init__(self, storage_file="snapshot_registry.json"):
        self.storage_file = storage_file
        self.snapshots = self._load_snapshots()
        
    def _load_snapshots(self):
        """加载已有的快照记录"""
        if not os.path.exists(self.storage_file):
            return {"processed": [], "pending": [], "failed": []}
            
        try:
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 确保数据结构完整
                if "processed" not in data:
                    data["processed"] = []
                if "pending" not in data:
                    data["pending"] = []
                if "failed" not in data:
                    data["failed"] = []
                return data
        except Exception as e:
            logging.error(f"加载快照记录失败: {str(e)}")
            return {"processed": [], "pending": [], "failed": []}
    
    def save_snapshots(self):
        """保存快照记录到文件"""
        try:
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(self.snapshots, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logging.error(f"保存快照记录失败: {str(e)}")
            return False
    
    def add_snapshot(self, snapshot_id, url, status="pending"):
        """添加新的快照ID"""
        if status not in ["processed", "pending", "failed"]:
            logging.warning(f"无效的状态: {status}，使用默认状态 'pending'")
            status = "pending"
            
        # 创建快照记录
        snapshot_record = {
            "id": snapshot_id,
            "url": url,
            "added_time": datetime.now().isoformat(),
            "last_update": datetime.now().isoformat(),
            "attempts": 0
        }
        
        # 检查是否已存在
        existing = self.get_snapshot_by_id(snapshot_id)
        if existing:
            # 更新现有记录
            for item in self.snapshots[existing["status"]]:
                if item["id"] == snapshot_id:
                    item.update(snapshot_record)
                    item["status"] = status
                    break
            # 如果状态改变，需要移动到新列表
            if existing["status"] != status:
                self.snapshots[existing["status"]] = [
                    s for s in self.snapshots[existing["status"]] 
                    if s["id"] != snapshot_id
                ]
                self.snapshots[status].append(snapshot_record)
        else:
            # 添加新记录
            snapshot_record["status"] = status
            self.snapshots[status].append(snapshot_record)
            
        return self.save_snapshots()
    
    def mark_processed(self, snapshot_id, success=True):
        """将快照标记为已处理 - 基于数据库状态"""
        try:
            import sqlite3
            import mysql.connector
            import os

            # 1. 验证快照是否存在于ArchiveBox数据库中
            archivebox_db_path = os.path.join(ARCHIVEBOX_DATA_PATH, 'index.sqlite3')
            if not os.path.exists(archivebox_db_path):
                logging.warning(f"ArchiveBox数据库不存在: {archivebox_db_path}")
                return False

            archivebox_conn = sqlite3.connect(archivebox_db_path)
            archivebox_cursor = archivebox_conn.cursor()

            # 查询快照是否存在 - timestamp是varchar类型，需要字符串匹配
            archivebox_cursor.execute("""
                SELECT timestamp, url, title FROM core_snapshot WHERE timestamp = ?
            """, (str(snapshot_id),))

            snapshot_row = archivebox_cursor.fetchone()
            archivebox_cursor.close()
            archivebox_conn.close()

            if not snapshot_row:
                logging.warning(f"在ArchiveBox数据库中找不到快照ID: {snapshot_id}")
                return False

            timestamp, url, title = snapshot_row
            print(f"✓ 验证快照存在: {snapshot_id} - {url}")

            # 2. 检查MySQL数据库中是否已有分析记录
            mysql_conn = mysql.connector.connect(**DB_CONFIG)
            mysql_cursor = mysql_conn.cursor()

            mysql_cursor.execute("""
                SELECT ra.id FROM crawled_data cd
                INNER JOIN rag_analysis ra ON cd.id = ra.crawled_data_id
                WHERE cd.link = %s AND ra.analysis_status = 1
            """, (url,))

            analysis_exists = mysql_cursor.fetchone()
            mysql_cursor.close()
            mysql_conn.close()

            if analysis_exists:
                print(f"✓ 快照 {snapshot_id} 已成功分析并存储到数据库")
                return True
            else:
                if success:
                    logging.warning(f"快照 {snapshot_id} 标记为成功，但数据库中未找到分析记录")
                else:
                    print(f"✓ 快照 {snapshot_id} 标记为失败")
                return success

        except Exception as e:
            logging.error(f"标记快照 {snapshot_id} 状态时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_snapshot_by_id(self, snapshot_id):
        """根据ID获取快照记录"""
        for status in ["pending", "processed", "failed"]:
            for snapshot in self.snapshots[status]:
                if snapshot["id"] == snapshot_id:
                    snapshot["status"] = status  # 添加状态字段
                    return snapshot
        return None
    
    def get_pending_snapshots(self, limit=10):
        """获取等待处理的快照 - 直接查询ArchiveBox SQLite数据库"""
        try:
            import sqlite3
            import mysql.connector
            import os

            # 1. 直接查询ArchiveBox的SQLite数据库
            archivebox_db_path = os.path.join(ARCHIVEBOX_DATA_PATH, 'index.sqlite3')

            if not os.path.exists(archivebox_db_path):
                print(f"❌ ArchiveBox数据库不存在: {archivebox_db_path}")
                return self.snapshots["pending"][:limit]  # 回退到内存数据

            print(f"🔍 查询ArchiveBox SQLite数据库: {archivebox_db_path}")

            # 连接ArchiveBox SQLite数据库
            archivebox_conn = sqlite3.connect(archivebox_db_path)
            archivebox_cursor = archivebox_conn.cursor()

            # 查询所有快照
            archivebox_cursor.execute("""
                SELECT timestamp, url, title, updated
                FROM core_snapshot
                ORDER BY updated DESC
            """)

            archivebox_snapshots = []
            for row in archivebox_cursor.fetchall():
                timestamp, url, title, updated = row
                archivebox_snapshots.append({
                    'id': timestamp,
                    'url': url,
                    'title': title or '',
                    'timestamp': timestamp,
                    'updated': updated
                })

            archivebox_cursor.close()
            archivebox_conn.close()

            print(f"✓ ArchiveBox中共有 {len(archivebox_snapshots)} 个快照")

            # 2. 查询MySQL数据库，找出哪些URL已经分析过
            print(f"🔍 查询MySQL数据库中已分析的URL...")
            mysql_conn = mysql.connector.connect(**DB_CONFIG)
            mysql_cursor = mysql_conn.cursor()

            # 获取所有已分析的URL
            mysql_cursor.execute("""
                SELECT DISTINCT cd.link
                FROM crawled_data cd
                INNER JOIN rag_analysis ra ON cd.id = ra.crawled_data_id
                WHERE ra.analysis_status = 1
            """)

            analyzed_urls = set(row[0] for row in mysql_cursor.fetchall())
            mysql_cursor.close()
            mysql_conn.close()

            print(f"✓ MySQL数据库中已分析 {len(analyzed_urls)} 个URL")

            # 3. 找出未分析的快照
            pending_snapshots = []
            for snapshot in archivebox_snapshots:
                snapshot_url = snapshot.get('url', '')
                snapshot_id = snapshot.get('id', '')

                if not snapshot_url or not snapshot_id:
                    continue

                # 检查这个URL是否已经分析过
                if snapshot_url not in analyzed_urls:
                    pending_snapshots.append({
                        'id': snapshot_id,
                        'url': snapshot_url,
                        'title': snapshot.get('title', ''),
                        'timestamp': snapshot_id,
                        'status': 'pending'
                    })

            print(f"🔍 找到 {len(pending_snapshots)} 个未分析的快照")

            # 按时间戳排序，最旧的在前（优先分析已完成的快照）
            pending_snapshots.sort(key=lambda x: float(x['timestamp']), reverse=False)

            # 过滤出已完成基本提取的快照
            completed_snapshots = []
            for snapshot in pending_snapshots:
                if self.is_snapshot_ready_for_analysis(snapshot['id']):
                    completed_snapshots.append(snapshot)
                else:
                    print(f"⏳ 快照 {snapshot['id']} 还在处理中，跳过分析")

            print(f"🔍 过滤后可分析的快照: {len(completed_snapshots)} 个")
            return completed_snapshots[:limit]

        except Exception as e:
            print(f"❌ 查询待分析快照时出错: {e}")
            import traceback
            traceback.print_exc()
            # 回退到原来的逻辑
            print(f"🔄 回退到内存数据，返回 {len(self.snapshots['pending'])} 个内存中的快照")
            return self.snapshots["pending"][:limit]

    def is_snapshot_ready_for_analysis(self, snapshot_id):
        """检查快照是否已完成基本提取，可以进行分析"""
        try:
            import os
            import time

            snapshot_dir = os.path.join(ARCHIVEBOX_DATA_PATH, 'archive', snapshot_id)

            if not os.path.exists(snapshot_dir):
                return False

            # 检查快照目录的修改时间，如果太新（5分钟内），可能还在处理中
            snapshot_mtime = os.path.getmtime(snapshot_dir)
            current_time = time.time()

            # 如果快照创建时间少于5分钟，认为可能还在处理中
            if current_time - snapshot_mtime < 300:  # 5分钟 = 300秒
                return False

            # 检查是否有基本的提取文件
            required_files = [
                'index.json',  # 基本索引文件
            ]

            # 检查是否有任何内容提取文件（至少有一个）
            content_files = [
                'readability/content.txt',
                'singlefile.html',
                'mercury/content.html',
                'dom.html',
                'output.html'
            ]

            # 检查必需文件
            for file_path in required_files:
                full_path = os.path.join(snapshot_dir, file_path)
                if not os.path.exists(full_path):
                    return False

            # 检查是否至少有一个内容文件存在且不为空
            has_content = False
            for file_path in content_files:
                full_path = os.path.join(snapshot_dir, file_path)
                if os.path.exists(full_path) and os.path.getsize(full_path) > 100:  # 至少100字节
                    has_content = True
                    break

            return has_content

        except Exception as e:
            print(f"检查快照完整性时出错 {snapshot_id}: {e}")
            return False  # 出错时保守处理，认为未完成
    
    def get_stats(self):
        """获取快照统计信息"""
        return {
            "total": len(self.snapshots["pending"]) + 
                     len(self.snapshots["processed"]) + 
                     len(self.snapshots["failed"]),
            "pending": len(self.snapshots["pending"]),
            "processed": len(self.snapshots["processed"]),
            "failed": len(self.snapshots["failed"])
        }
    
    def __str__(self):
        stats = self.get_stats()
        return f"SnapshotManager: {stats['total']}个快照 (待处理: {stats['pending']}, 已处理: {stats['processed']}, 失败: {stats['failed']})"



# 先使用ArchiveBox获取网页内容
def add_url_to_archivebox(target_url, timeout=600):
    """将单个URL添加到ArchiveBox并实时显示输出，同时返回snapshot_id"""

    print(f"正在添加URL到ArchiveBox: {target_url}")
    print(f"设置超时时间: {timeout}秒")

    # 检查是否是微信链接
    if "mp.weixin.qq.com" in target_url:
        print("检测到微信公众号链接，将使用配置文件中的cookie")

    # 构建ArchiveBox命令（ArchiveBox会自动使用配置文件中的COOKIES_FILE）
    add_cmd = f"cd {ARCHIVEBOX_DATA_PATH} && archivebox add \"{target_url}\""

    try:
        # 使用Popen进行更细粒度的控制
        start_time = time.time()
        process = subprocess.Popen(
            add_cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace',  # 处理解码错误
            bufsize=1  # 行缓冲
        )
        
        # 实时读取并显示输出
        stdout_data = []
        stderr_data = []
        snapshot_id = None
        
        # 正则表达式用于从输出中匹配snapshot_id
        # 修改正则表达式，增加更多匹配模式
        snapshot_patterns = [
            re.compile(r'Saved (?:to|in|as) ([0-9]+\.[0-9]+)'),  # 原始模式
            re.compile(r'URL already exists in index: \S+/archive/([0-9]+\.[0-9]+)/index\.json'),  # 已存在URL模式
            re.compile(r'\./archive/([0-9]+\.[0-9]+)'),  # 新模式，匹配 ./archive/1751002879.392538
            re.compile(r'/archive/([0-9]+\.[0-9]+)'),  # 备用模式
            re.compile(r'> ([0-9]+\.[0-9]+)')  # 更宽松的匹配
        ]
        
        # 不使用select模块，改用轮询方式
        import io
        import threading
        
        def read_stream(stream, data_list, prefix=''):
            """线程函数：持续读取流并处理输出"""
            nonlocal snapshot_id
            try:
                for line in iter(stream.readline, ''):
                    if not line:
                        break
                    # 确保line是字符串且正确处理编码
                    if isinstance(line, bytes):
                        line = line.decode('utf-8', errors='replace')
                    print(f"{prefix}{line.rstrip()}")
                    data_list.append(line)
                    
                    # 检查snapshot_id，遍历所有正则表达式模式
                    if not snapshot_id:  # 只在尚未找到ID时尝试匹配
                        for pattern in snapshot_patterns:
                            match = pattern.search(line)
                            if match:
                                potential_id = match.group(1)
                                # 验证ID格式，要求包含小数点，且至少为8位
                                if '.' in potential_id and len(potential_id) >= 8:
                                    snapshot_id = potential_id
                                    print(f"从输出中提取到snapshot_id: {snapshot_id} (使用模式: {pattern.pattern})")
                                    break
            except UnicodeDecodeError as e:
                print(f"编码错误，使用UTF-8 replace模式: {e}")
            except Exception as e:
                print(f"读取流时出错: {e}")
        
        print("开始执行命令，等待输出...")
        
        # 启动读取线程
        stdout_thread = threading.Thread(target=read_stream, args=(process.stdout, stdout_data))
        stderr_thread = threading.Thread(target=read_stream, args=(process.stderr, stderr_data, 'ERROR: '))
        
        stdout_thread.daemon = True
        stderr_thread.daemon = True
        
        stdout_thread.start()
        stderr_thread.start()
        
        # 等待进程完成或超时
        while True:
            # 检查超时
            if time.time() - start_time > timeout:
                print(f"命令执行超时（{timeout}秒），终止进程")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                return False, None
            
            # 检查进程是否结束
            if process.poll() is not None:
                break
                
            # 定期显示进度
            elapsed = int(time.time() - start_time)
            if elapsed % 20 == 0 and elapsed > 0:
                print(f"命令仍在执行中... 已用时 {elapsed} 秒")
            
            time.sleep(0.5)  # 减少CPU使用率
        
        # 等待读取线程完成
        stdout_thread.join(timeout=5)
        stderr_thread.join(timeout=5)
        
        # 如果未找到snapshot_id，尝试从完整输出中再次查找
        if not snapshot_id:
            print("尝试从完整输出中查找snapshot_id...")
            full_output = ''.join(stdout_data)
            for pattern in snapshot_patterns:
                match = pattern.search(full_output)
                if match:
                    snapshot_id = match.group(1)
                    print(f"从完整输出中提取到snapshot_id: {snapshot_id} (使用模式: {pattern.pattern})")
                    break
            
        # 如果仍未找到snapshot_id，尝试自定义查询        
        if not snapshot_id:
            print("未从输出中找到snapshot_id，尝试自定义查询...")
            
            # 首先尝试直接列出最近的存档
            try:
                # 使用ls命令查找最新添加的目录
                list_cmd = f"ls -la {ARCHIVEBOX_DATA_PATH}/archive/ | grep -v index | tail -n 5"
                list_result = subprocess.run(list_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=30)
                
                print("最近5个存档目录:")
                print(list_result.stdout)
                
                # 尝试从输出中提取数字格式的ID
                for line in list_result.stdout.split('\n'):
                    # 查找形如 1751002879.392538 的ID
                    id_match = re.search(r'([0-9]+\.[0-9]+)', line)
                    if id_match:
                        potential_id = id_match.group(1)
                        # 验证这是我们要找的目录
                        verify_cmd = f"grep -l '{target_url}' {ARCHIVEBOX_DATA_PATH}/archive/{potential_id}/index.json 2>/dev/null"
                        verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=10)
                        if verify_result.stdout.strip():
                            snapshot_id = potential_id
                            print(f"通过目录列表找到匹配的snapshot_id: {snapshot_id}")
                            break
            except Exception as ls_error:
                print(f"列出最近存档目录失败: {str(ls_error)}")
            
            # 如果上面方法失败，尝试通过URL查询
            if not snapshot_id:
                print("尝试使用URL查找snapshot_id...")
                snapshot_id = find_snapshot_id_by_url(target_url)
        
        if process.returncode == 0 or snapshot_id:
            print(f"成功添加URL: {target_url}")
            if snapshot_id:
                print(f"最终获取的snapshot_id: {snapshot_id}")
                return True, snapshot_id
            else:
                print("警告: 添加成功但未能获取snapshot_id，尝试最后一次查询")
                # 最后一次尝试，直接用grep命令查找
                try:
                    grep_cmd = f"grep -l '{target_url}' {ARCHIVEBOX_DATA_PATH}/archive/*/index.json"
                    grep_result = subprocess.run(grep_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=30)
                    if grep_result.stdout:
                        # 从路径中提取ID
                        path_match = re.search(r'/archive/([0-9]+\.[0-9]+)/index\.json', grep_result.stdout)
                        if path_match:
                            final_id = path_match.group(1)
                            print(f"通过grep命令找到snapshot_id: {final_id}")
                            return True, final_id
                except Exception as grep_error:
                    print(f"grep查询失败: {str(grep_error)}")
                
                print("所有方法都未能获取snapshot_id")
                return True, None
        else:
            print(f"添加URL失败，退出代码: {process.returncode}")
            return False, None
            
    except Exception as e:
        print(f"执行命令时出错: {str(e)}")
        traceback.print_exc()
        return False, None


def find_snapshot_id_by_url(target_url, timeout=30):
    """根据URL查找snapshot_id"""
    print(f"尝试查找URL的快照ID: {target_url}")
    
    try:
        # 确保在正确的ArchiveBox数据目录下运行命令
        cmd = f"cd {ARCHIVEBOX_DATA_PATH} && archivebox list --json --filter-url='{target_url}' | head -n 1"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                              encoding='utf-8', errors='replace', timeout=timeout,
                              stdin=subprocess.DEVNULL)
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                entries = json.loads(result.stdout)
                if entries and len(entries) > 0:
                    for entry in entries:
                        if 'url' in entry and entry['url'] == target_url and 'timestamp' in entry:
                            snapshot_id = entry['timestamp']
                            print(f"找到URL对应的snapshot_id: {snapshot_id}")
                            return snapshot_id
            except json.JSONDecodeError:
                print("解析返回的JSON失败")
                
    except subprocess.TimeoutExpired:
        print(f"查询命令执行超时({timeout}秒)")
    except Exception as e:
        print(f"查找snapshot_id时出错: {str(e)}")
        
    return None


def get_all_archivebox_snapshots(timeout=60):
    """
    获取ArchiveBox中所有的快照信息，返回URL到snapshot_id的映射
    使用直接查询SQLite数据库的方式，比命令行方式更快

    Returns:
        dict: URL到快照信息的映射 {url: {'snapshot_id': str, 'snapshot_url': str}}
    """
    print("🔍 正在从ArchiveBox数据库获取快照信息...")
    snapshot_map = {}

    try:
        import sqlite3

        # 1. 连接ArchiveBox数据库
        archivebox_db_path = os.path.join(ARCHIVEBOX_DATA_PATH, 'index.sqlite3')
        if not os.path.exists(archivebox_db_path):
            print(f"ArchiveBox数据库不存在: {archivebox_db_path}")
            return {}

        print(f"连接ArchiveBox数据库: {archivebox_db_path}")
        conn = sqlite3.connect(archivebox_db_path)
        cursor = conn.cursor()

        # 2. 查询所有快照信息
        query = """
            SELECT timestamp, url, title
            FROM core_snapshot
            WHERE url IS NOT NULL AND timestamp IS NOT NULL
            ORDER BY timestamp DESC
        """

        cursor.execute(query)
        rows = cursor.fetchall()

        # 3. 构建快照映射
        for row in rows:
            timestamp, url, title = row
            if url and timestamp:
                snapshot_url = get_archivebox_snapshot_url(timestamp)

                snapshot_map[url] = {
                    'snapshot_id': timestamp,
                    'snapshot_url': snapshot_url
                }

        cursor.close()
        conn.close()

        print(f"成功从数据库获取 {len(snapshot_map)} 个快照信息")
        return snapshot_map

    except sqlite3.Error as e:
        print(f"SQLite数据库错误: {e}")
        return {}
    except Exception as e:
        print(f"获取快照列表时出错: {str(e)}")
        return {}


def batch_find_snapshots_by_urls(urls, timeout=60):
    """
    批量查找多个URL的快照信息
    
    Args:
        urls (list): URL列表
        timeout (int): 超时时间
        
    Returns:
        dict: URL到快照信息的映射
    """
    print(f"批量查找 {len(urls)} 个URL的快照信息...")
    
    # 先获取所有快照信息
    all_snapshots = get_all_archivebox_snapshots(timeout)
    
    # 筛选出请求的URL
    result = {}
    for url in urls:
        if url in all_snapshots:
            result[url] = all_snapshots[url]
        else:
            result[url] = {
                'snapshot_id': None,
                'snapshot_url': None
            }
    
    print(f"找到 {sum(1 for v in result.values() if v['snapshot_id'])} 个URL有快照")
    return result


def extract_content_from_alternative_sources(snapshot_id):
    """
    当readability目录缺失时，从其他文件源提取内容
    
    Args:
        snapshot_id (str): ArchiveBox快照ID
        
    Returns:
        str: 提取到的文本内容，如果全部失败则返回空字符串
    """
    print(f"开始从备用文件源提取内容: {snapshot_id}")
    alternative_content = ""
    
    # 备用文件源列表，按优先级排序
    file_sources = [
        # 1. SingleFile提取的内容
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/singlefile.html',
            'name': 'SingleFile HTML',
            'processor': 'html_to_text'
        },
        # 2. Mercury提取的内容
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/mercury/content.html',
            'name': 'Mercury HTML',
            'processor': 'html_to_text'
        },
        # 3. DOM快照内容
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/dom.html',
            'name': 'DOM HTML',
            'processor': 'html_to_text'
        },
        # 4. wget下载的HTML
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/index.html',
            'name': 'Index HTML',
            'processor': 'html_to_text'
        },
        # 5. htmltotext提取的纯文本
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/htmltotext.txt',
            'name': 'HTMLtoText',
            'processor': 'plain_text'
        },
        # 6. 备用HTML文件
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/output.html',
            'name': 'Output HTML',
            'processor': 'html_to_text'
        },
    ]
    
    for source in file_sources:
        try:
            file_path = source['path']
            source_name = source['name']
            processor = source['processor']
            
            # 检查文件是否存在且不为空
            check_cmd = f"test -f {file_path} && test -s {file_path} && echo 'exists' || echo 'missing'"
            check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
            
            if 'exists' not in check_result.stdout:
                print(f"  跳过 {source_name}: 文件不存在或为空")
                continue
            
            print(f"  尝试从 {source_name} 提取内容...")
            
            # 读取文件内容
            read_cmd = f"cat {file_path}"
            read_result = subprocess.run(read_cmd, shell=True, capture_output=True, text=False)
            
            if read_result.returncode != 0:
                print(f"  读取 {source_name} 失败")
                continue
            
            # 解码文件内容
            raw_data = read_result.stdout
            file_content = ""
            
            for encoding in ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']:
                try:
                    file_content = raw_data.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if not file_content:
                file_content = raw_data.decode('utf-8', errors='replace')
            
            if not file_content or len(file_content.strip()) < 50:
                print(f"  {source_name} 内容太短，跳过")
                continue
            
            # 根据处理器类型处理内容
            if processor == 'html_to_text':
                processed_content = html_to_readable_text(file_content)
            else:  # plain_text
                processed_content = file_content.strip()
            
            if processed_content and len(processed_content.strip()) > 100:
                print(f"  ✓ 成功从 {source_name} 提取到 {len(processed_content)} 个字符")
                alternative_content = processed_content
                break
            else:
                print(f"  {source_name} 处理后内容仍然太短")
                
        except Exception as e:
            print(f"  处理 {source['name']} 时出错: {str(e)}")
            continue
    
    if not alternative_content:
        print("  警告: 所有备用文件源都无法提取到有效内容")
        return ""
    
    print(f"  最终从备用源提取到 {len(alternative_content)} 个字符的内容")
    return alternative_content


def extract_html_from_alternative_sources(snapshot_id):
    """
    当readability HTML缺失时，从其他HTML文件源提取内容
    
    Args:
        snapshot_id (str): ArchiveBox快照ID
        
    Returns:
        str: 提取到的HTML内容，如果全部失败则返回空字符串
    """
    print(f"开始从备用HTML文件源提取内容: {snapshot_id}")
    alternative_html = ""
    
    # 备用HTML文件源列表，按优先级排序
    html_sources = [
        # 1. SingleFile提取的完整HTML
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/singlefile.html',
            'name': 'SingleFile HTML'
        },
        # 2. Mercury提取的HTML内容
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/mercury/content.html',
            'name': 'Mercury HTML'
        },
        # 3. DOM快照HTML
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/dom.html',
            'name': 'DOM HTML'
        },
        # 4. wget下载的原始HTML
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/index.html',
            'name': 'Index HTML'
        },
        # 5. 备用输出HTML
        {
            'path': f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/output.html',
            'name': 'Output HTML'
        },
    ]
    
    for source in html_sources:
        try:
            file_path = source['path']
            source_name = source['name']
            
            # 检查文件是否存在且不为空
            check_cmd = f"test -f {file_path} && test -s {file_path} && echo 'exists' || echo 'missing'"
            check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
            
            if 'exists' not in check_result.stdout:
                print(f"  跳过 {source_name}: 文件不存在或为空")
                continue
            
            print(f"  尝试从 {source_name} 提取HTML内容...")
            
            # 读取文件内容
            read_cmd = f"cat {file_path}"
            read_result = subprocess.run(read_cmd, shell=True, capture_output=True, text=False)
            
            if read_result.returncode != 0:
                print(f"  读取 {source_name} 失败")
                continue
            
            # 解码文件内容
            raw_data = read_result.stdout
            html_content = ""
            
            for encoding in ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']:
                try:
                    html_content = raw_data.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if not html_content:
                html_content = raw_data.decode('utf-8', errors='replace')
            
            if not html_content or len(html_content.strip()) < 100:
                print(f"  {source_name} HTML内容太短，跳过")
                continue
            
            # 简单验证这是有效的HTML内容
            if '<html' in html_content.lower() or '<body' in html_content.lower() or '<div' in html_content.lower():
                print(f"  ✓ 成功从 {source_name} 提取到 {len(html_content)} 个字符的HTML内容")
                alternative_html = html_content
                break
            else:
                print(f"  {source_name} 不包含有效的HTML标签")
                
        except Exception as e:
            print(f"  处理 {source['name']} 时出错: {str(e)}")
            continue
    
    if not alternative_html:
        print("  警告: 所有备用HTML文件源都无法提取到有效内容")
        return ""
    
    print(f"  最终从备用HTML源提取到 {len(alternative_html)} 个字符的内容")
    return alternative_html


def html_to_readable_text(html_content):
    """
    将HTML内容转换为可读的纯文本
    
    Args:
        html_content (str): HTML内容
        
    Returns:
        str: 提取的纯文本内容
    """
    try:
        from bs4 import BeautifulSoup
        
        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 移除脚本和样式标签
        for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
            script.decompose()
        
        # 移除注释
        from bs4 import Comment
        comments = soup.findAll(text=lambda text: isinstance(text, Comment))
        for comment in comments:
            comment.extract()
        
        # 提取文本
        text = soup.get_text()
        
        # 清理文本：移除多余的空白字符
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
        
    except ImportError:
        print("  警告: BeautifulSoup未安装，使用简单的HTML标签移除")
        # 简单的HTML标签移除
        import re
        text = re.sub(r'<[^>]+>', '', html_content)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
        
    except Exception as e:
        print(f"  HTML转文本时出错: {str(e)}")
        # 备用方案：简单移除HTML标签
        import re
        text = re.sub(r'<[^>]+>', '', html_content)
        text = re.sub(r'\s+', ' ', text).strip()
        return text


def extract_single_url_by_id(snapshot_id, url):
    """通过snapshot_id直接提取内容"""
    print(f"通过快照ID直接提取内容: {snapshot_id}")
    
    # 确认快照存在
    check_cmd = f"test -d {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id} && echo 'exists' || echo 'not exists'"
    check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
    
    if "exists" not in check_result.stdout:
        print(f"快照ID不存在: {snapshot_id}")
        return None, None, None, None
    
    # 检查readability目录是否存在
    readability_check_cmd = f"test -d {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/readability && echo 'readability_exists' || echo 'readability_missing'"
    readability_result = subprocess.run(readability_check_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
    
    if "readability_missing" in readability_result.stdout:
        print(f"警告: 快照 {snapshot_id} 缺少readability目录，可能是提取失败")
        print("尝试重新运行readability提取...")
        
        # 尝试重新运行readability提取
        try:
            readability_cmd = f"cd {ARCHIVEBOX_DATA_PATH} && archivebox extract --methods=readability {snapshot_id}"
            print(f"执行命令: {readability_cmd}")
            extract_result = subprocess.run(readability_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=120)
            
            if extract_result.returncode == 0:
                print("readability重新提取成功")
            else:
                print(f"readability重新提取失败: {extract_result.stderr}")
                print("readability重新提取失败，将尝试从其他文件提取内容")
        except subprocess.TimeoutExpired:
            print("readability重新提取超时，将尝试从其他文件提取内容")
        except Exception as e:
            print(f"readability重新提取出错: {str(e)}，将尝试从其他文件提取内容")
    
    # === 首先初始化所有变量 ===
    content = ''
    html_cont = ''
    publication_date = None
    title = "Unknown Title"
    
    # === 1. 提取标题 ===
    # 获取完整的index.json以正确提取标题
    index_cmd = f"cat {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/index.json 2>/dev/null"
    try:
        result = subprocess.run(index_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
        index_content = result.stdout.strip()
        
        # 解析JSON内容
        import json
        index_data = json.loads(index_content)
        
        # 检查title字段类型并根据不同结构提取标题
        if 'title' in index_data:
            title_field = index_data['title']
            
            # 情况1: title字段是字符串
            if isinstance(title_field, str):
                title = title_field
                print(f"直接从title字段提取到标题: '{title}'")
            
            # 情况2: title字段是列表
            elif isinstance(title_field, list) and title_field:
                for item in title_field:
                    # 情况2a: 列表项是包含output键的对象
                    if isinstance(item, dict) and 'output' in item and item['output']:
                        title = item['output']
                        print(f"从title列表的output提取到标题: '{title}'")
                        break
                    # 情况2b: 列表项是字符串
                    elif isinstance(item, str):
                        title = item
                        print(f"从title列表提取到标题: '{title}'")
                        break
                        
            # 情况3: title字段是对象
            elif isinstance(title_field, dict):
                if 'output' in title_field and title_field['output']:
                    title = title_field['output']
                    print(f"从title对象的output提取到标题: '{title}'")
        
        # 如果上述方法都提取不到标题，尝试使用不同的jq命令
        if not title or title == "Unknown Title":
            print("从索引JSON解析标题失败，尝试jq命令")
            
            # 尝试多种jq命令格式
            jq_commands = [
                f"jq -r '.title' {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/readability/article.json 2>/dev/null",
            ]
            
            for cmd in jq_commands:
                try:
                    jq_result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
                    jq_title = jq_result.stdout.strip()
                    if jq_title and jq_title != "null" and jq_title != "Unknown Title":
                        title = jq_title
                        print(f"使用jq命令提取到标题: '{title}'")
                        break
                except Exception as jq_e:
                    print(f"jq命令提取标题失败: {str(jq_e)}")
        
    except Exception as e:
        print(f"从index.json获取标题失败: {str(e)}")
        title = "Unknown Title"
    
    # === 2. 提取文本内容 ===
    cmd_txt = f"cat {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/readability/content.txt 2>/dev/null || echo ''"
    try:
        result_txt = subprocess.run(cmd_txt, shell=True, capture_output=True, text=False)
        raw_content = result_txt.stdout
        
        # 尝试多种编码方式
        for encoding in ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']:
            try:
                content = raw_content.decode(encoding)
                print(f"成功使用 {encoding} 从 readability/content.txt 提取了 {len(content)} 字符")
                break
            except UnicodeDecodeError:
                continue
                
        if not content and raw_content:
            content = raw_content.decode('utf-8', errors='replace')
            
        # 如果readability内容为空或很短，尝试从其他文件提取
        if not content or len(content.strip()) < 50:
            print("readability内容不足，尝试从其他文件提取内容...")
            content = extract_content_from_alternative_sources(snapshot_id)
            
    except Exception as e:
        print(f"提取文本内容失败: {str(e)}")
        # 如果提取失败，尝试从其他文件提取
        content = extract_content_from_alternative_sources(snapshot_id)
    
    # === 3. 提取HTML内容 ===
    cmd_html = f"cat {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/readability/content.html 2>/dev/null || echo ''"
    try:
        result_html = subprocess.run(cmd_html, shell=True, capture_output=True, text=False)
        raw_html = result_html.stdout
        
        # 尝试多种编码方式
        for encoding in ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']:
            try:
                html_cont = raw_html.decode(encoding)
                print(f"成功使用 {encoding} 从 readability/content.html 提取了 {len(html_cont)} 字符")
                break
            except UnicodeDecodeError:
                continue
                
        if not html_cont and raw_html:
            html_cont = raw_html.decode('utf-8', errors='replace')
            
        # 如果readability HTML内容为空或很短，尝试从其他文件提取
        if not html_cont or len(html_cont.strip()) < 50:
            print("readability HTML内容不足，尝试从其他HTML文件提取...")
            html_cont = extract_html_from_alternative_sources(snapshot_id)
            
    except Exception as e:
        print(f"提取HTML内容失败: {str(e)}")
        # 如果提取失败，尝试从其他文件提取
        html_cont = extract_html_from_alternative_sources(snapshot_id)
    
    # === 4. 提取发布日期 - 多种方法（按优先级排序） ===
    # 首先检查是否为微信公众号文章
    if 'mp.weixin.qq.com' in url:
        publication_date = extract_wechat_article_date(url)

    # 方法1: 使用htmldate库从URL和HTML内容提取日期（最准确）
    if not publication_date:
        try:
            print(f"从URL提取")
            url_date = find_date(url)
            print(f"从URL提取结果: {url_date}")
            if url_date:
                publication_date = url_date
                print(f"✓ 使用htmldate从URL提取到publication_date: {publication_date}")
            else:
                print(f"URL提取也失败")
        except Exception as e:
            print(f"使用htmldate提取日期时出错: {str(e)}")
    else:
        print(f"方法1: 跳过（publication_date已有值: {publication_date}）")

    # 方法2: 从article.json提取多种可能的日期字段，，byline字段中也可能，如
    # "byline": "Jun 30, 2025Ravie Lakshmanan",
    if not publication_date:
        print(f"方法2: 开始从article.json提取日期字段")
        try:
            # 尝试多种常见的日期字段名（按重要性排序）
            date_fields = ['byline','publishedTime', 'datePublished', 'published_time', 'date_published', 
                          'pubDate', 'publication_date', 'created', 'date', 'timestamp']
            
            for field in date_fields:
                jq_cmd = f"jq -r '.{field}' {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/readability/article.json 2>/dev/null"
                result = subprocess.run(jq_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
                extracted_date = result.stdout.strip()
                
                # "Jun 30, 2025Ravie Lakshmanan",只拿日期
                print(f"从字段'{field}'提取到原始值: {extracted_date}")
                if extracted_date and extracted_date != "null" and extracted_date != "":
                    # 对于byline字段或其他可能包含混合内容的字段，需要提取其中的日期部分
                    if field == 'byline' or any(keyword in field.lower() for keyword in ['author', 'by']):
                        # 使用正则表达式从混合内容中提取日期
                        cleaned_date = extract_date_from_mixed_text(extracted_date)
                        if cleaned_date:
                            publication_date = cleaned_date
                            print(f"✓ 从article.json的'{field}'字段提取并清理后得到publication_date: {publication_date}")
                            break
                        else:
                            print(f"从'{field}'字段无法提取有效日期: {extracted_date}")
                    else:
                        # 对于其他字段，直接使用提取的值
                        publication_date = extracted_date
                        print(f"✓ 从article.json的'{field}'字段提取到publication_date: {publication_date}")
                        break
                    
        except Exception as e:
            print(f"从article.json提取publication_date时出错: {str(e)}")

    # 方法3: 从HTML内容中提取日期元数据和语义化标签
    if not publication_date and html_cont:
        print(f"方法3: 开始从HTML内容提取日期元数据")
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_cont, 'html.parser')
            
            # 扩展的日期选择器列表（按优先级排序）
            date_selectors = [
                # Open Graph和标准meta标签
                'meta[property="article:published_time"]',
                'meta[property="og:published_time"]',
                'meta[name="article:published_time"]',
                'meta[name="date"]',
                'meta[name="publication-date"]',
                'meta[name="pubdate"]',
                'meta[name="publish-date"]',
                'meta[name="created"]',
                'meta[name="DC.date"]',
                'meta[name="dcterms.created"]',
                'meta[name="dcterms.date"]',
                # HTML5语义化标签
                'time[datetime]',
                'time[pubdate]',
                'time.published',
                'time.date',
                # 常见的CSS类选择器
                '.publish-time',
                '.publication-date',
                '.post-date',
                '.article-date',
                '.date-published',
                '.entry-date',
                '.post-meta-date'
            ]
            
            for selector in date_selectors:
                try:
                    meta_tag = soup.select_one(selector)
                    if meta_tag:
                        # 尝试多种属性获取日期值
                        date_value = (meta_tag.get('content') or 
                                    meta_tag.get('datetime') or 
                                    meta_tag.get_text().strip())
                        if date_value and len(date_value) > 4:  # 基本长度检查
                            publication_date = date_value
                            print(f"✓ 从HTML标签提取到publication_date: {publication_date} (选择器: {selector})")
                            break
                except Exception as selector_error:
                    print(f"选择器 {selector} 处理失败: {str(selector_error)}")
                    continue
                        
        except Exception as e:
            print(f"从HTML内容提取日期时出错: {str(e)}")
    
    # 方法4: 从htmltotext.txt文本中智能提取日期（标题附近优先）
    if not publication_date:
        print(f"方法4: 开始从htmltotext.txt文本中提取日期")
        try:
            # 读取更多文本内容以提高匹配成功率
            htmltotext_cmd = f"head -c 8000 {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/htmltotext.txt 2>/dev/null || echo ''"
            result = subprocess.run(htmltotext_cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
            text_content = result.stdout.strip()
            print(f"方法4: 从htmltotext.txt读取到 {len(text_content)} 个字符")
            
            # 如果htmltotext.txt文件为空或不存在，尝试从其他文件源提取文本内容
            if not text_content or len(text_content) < 50:
                print(f"方法4: htmltotext.txt文件为空或不存在，尝试从其他文件源提取文本内容")
                text_content = extract_content_from_alternative_sources(snapshot_id)
                if text_content:
                    print(f"方法4: 成功从备用文件源提取到 {len(text_content)} 个字符的文本内容")
                else:
                    print(f"方法4: 所有备用文件源都无法提取到有效的文本内容")
            
            if text_content:
                # 清理文本：去除URL和无用链接
                import re
                clean_text = re.sub(r'https?://\S+|www\.\S+', '', text_content)
                clean_text = re.sub(r'\s+', ' ', clean_text)  # 标准化空白字符
                
                lines = clean_text.splitlines()
                
                # 1. 优先在标题附近查找日期
                title_context = ""
                if title and title != 'Unknown Title':
                    # 查找标题所在行
                    title_keywords = title.strip()[:20]  # 取标题前20个字符作为关键词
                    for idx, line in enumerate(lines):
                        if title_keywords in line:
                            # 取标题前后各10行作为上下文
                            start = max(0, idx - 10)
                            end = min(len(lines), idx + 11)
                            title_context = '\n'.join(lines[start:end])
                            print(f"方法4: 在第{idx}行找到标题，提取上下文 {len(title_context)} 字符")
                            break
                
                # 如果没找到标题上下文，使用前30行
                if not title_context:
                    title_context = '\n'.join(lines[:30])
                    print(f"方法4: 未找到标题上下文，使用前30行文本")
                
                # 2. 改进的日期正则表达式（按优先级排序）
                date_patterns = [
                    # 标准日期格式
                    (r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}日?)', '标准日期格式'),
                    # 英文日期格式
                    (r'([A-Z][a-z]{2,8}\s+\d{1,2},?\s+\d{4})', '英文月份日期'),
                    (r'(\d{1,2}\s+[A-Z][a-z]{2,8}\s+\d{4})', '英文日期格式2'),
                    # 中文日期格式
                    (r'(\d{4}年\d{1,2}月\d{1,2}日)', '中文日期格式'),
                    # ISO日期格式
                    (r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})', 'ISO日期时间'),
                    (r'(\d{4}-\d{2}-\d{2})', 'ISO日期'),
                    # 其他常见格式
                    (r'(\d{1,2}/\d{1,2}/\d{4})', '美式日期格式'),
                    (r'(\d{1,2}\.\d{1,2}\.\d{4})', '欧式日期格式'),
                ]
                
                # 3. 在标题上下文中查找日期
                found_date = None
                for pattern, pattern_name in date_patterns:
                    matches = re.findall(pattern, title_context)
                    if matches:
                        # 选择第一个匹配项
                        found_date = matches[0]
                        print(f"方法4: 在标题上下文中使用{pattern_name}找到日期: {found_date}")
                        break
                    
                if found_date:
                    publication_date = found_date
                    print(f"✓ 从htmltotext.txt文本中提取到publication_date: {publication_date}")
                else:
                    print(f"方法4: 未在文本中找到匹配的日期格式")
            else:
                print(f"方法4: htmltotext.txt文件为空或不存在")
        except Exception as e:
            print(f"从htmltotext.txt文本提取日期时出错: {str(e)}")

    # 设置默认值
    if not publication_date:
        publication_date = "未知时间"
    
    print(f"最终确定的publication_date: {publication_date}")
    
    # === 5. 如果缺少标题，尝试从HTML内容提取 ===
    if (not title or title == 'Unknown Title' or title == 'null') and html_cont:
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_cont, 'html.parser')
            if soup.title:
                title = soup.title.text.strip()
                print(f"从HTML提取到标题: {title}")
        except:
            pass
    
    return content, html_cont, publication_date, title


def process_pending_snapshots(limit=10, analyze=True):
    """处理等待中的快照"""
    # 初始化快照管理器
    snapshot_manager = SnapshotManager()
    pending_snapshots = snapshot_manager.get_pending_snapshots(limit)
    
    if not pending_snapshots:
        logging.info("没有待处理的快照")
        return
    
    logging.info(f"开始处理 {len(pending_snapshots)} 个待处理快照...")
    
    # 处理结果统计
    results = {
        'total': len(pending_snapshots),
        'extracted': 0,
        'analyzed': 0,
        'saved': 0,
        'failed': 0
    }
    
    for i, snapshot in enumerate(pending_snapshots):
        snapshot_id = snapshot['id']
        url = snapshot['url']
        
        logging.info(f"\n处理快照 [{i+1}/{len(pending_snapshots)}]: {snapshot_id} (URL: {url})")
        
        try:
            # 提取内容
            content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id,url)
            
            if not content and not html_content:
                logging.warning(f"未能提取内容，标记为失败: {snapshot_id}")
                snapshot_manager.mark_processed(snapshot_id, success=False)
                results['failed'] += 1
                continue
            
            results['extracted'] += 1
            
            # 分析内容
            if analyze and content:
                analysis_result = analyze_archived_content(
                    content, html_content, url, title, publication_date,
                    analyze_after_crawl=True, snapshot_id=snapshot_id
                )
                
                if analysis_result == "success":
                    results['analyzed'] += 1
                    logging.info(f"成功分析文章: {url}")
                    snapshot_manager.mark_processed(snapshot_id, success=True)
                else:
                    logging.warning(f"分析失败: {url}")
                    snapshot_manager.mark_processed(snapshot_id, success=False)
                    results['failed'] += 1
            else:
                logging.info(f"跳过分析: {url}")
                snapshot_manager.mark_processed(snapshot_id, success=True)
        
        except Exception as e:
            logging.error(f"处理快照时出错: {snapshot_id}, 错误: {str(e)}")
            traceback.print_exc()
            snapshot_manager.mark_processed(snapshot_id, success=False)
            results['failed'] += 1
    
    # 显示最终结果
    logging.info("\n" + "="*50)
    logging.info("处理完成! 统计信息:")
    logging.info(f"总快照数量: {results['total']}")
    logging.info(f"成功提取内容: {results['extracted']}")
    logging.info(f"成功分析文章: {results['analyzed']}")
    logging.info(f"失败数量: {results['failed']}")
    logging.info("="*50)
    
    return results


def remove_archivebox_snapshot(snapshot_id, timeout=60):
    """
    删除ArchiveBox中的快照

    Returns:
        str: "success" - 删除成功
             "not_found" - 快照不存在
             "failed" - 删除失败
    """
    print(f"正在删除低价值快照: {snapshot_id}")

    try:

        # 使用SQLite数据库快速检查快照是否存在，避免命令行查询超时
        import sqlite3

        archivebox_db_path = os.path.join(ARCHIVEBOX_DATA_PATH, 'index.sqlite3')
        if not os.path.exists(archivebox_db_path):
            print(f"ArchiveBox数据库不存在: {archivebox_db_path}")
            return "not_found"

        # 连接ArchiveBox数据库
        conn = sqlite3.connect(archivebox_db_path)
        cursor = conn.cursor()

        # 查询快照是否存在 - timestamp是varchar类型，需要字符串匹配
        cursor.execute("SELECT timestamp, url, title FROM core_snapshot WHERE timestamp = ?", (str(snapshot_id),))
        snapshot_row = cursor.fetchone()

        cursor.close()
        conn.close()

        if not snapshot_row:
            print(f"快照不存在: {snapshot_id}")
            return "not_found"

        timestamp, url, title = snapshot_row
        print(f"✓ 确认快照存在: {snapshot_id} - {url}")

        # 直接从数据库删除快照记录（更可靠且更快）
        print(f"🗑️ 直接从数据库删除快照记录: {snapshot_id}")

        # 重新连接数据库进行删除操作
        conn = sqlite3.connect(archivebox_db_path)
        cursor = conn.cursor()

        try:
            # 删除相关的标签关联
            cursor.execute("DELETE FROM core_snapshot_tags WHERE snapshot_id = (SELECT id FROM core_snapshot WHERE timestamp = ?)", (str(snapshot_id),))
            tags_deleted = cursor.rowcount

            # 删除归档结果
            cursor.execute("DELETE FROM core_archiveresult WHERE snapshot_id = (SELECT id FROM core_snapshot WHERE timestamp = ?)", (str(snapshot_id),))
            results_deleted = cursor.rowcount

            # 删除快照记录
            cursor.execute("DELETE FROM core_snapshot WHERE timestamp = ?", (str(snapshot_id),))
            snapshot_deleted = cursor.rowcount

            # 提交事务
            conn.commit()

            if snapshot_deleted > 0:
                print(f"✅ 成功从数据库删除快照: {snapshot_id}")
                print(f"   - 删除快照记录: {snapshot_deleted}")
                print(f"   - 删除标签关联: {tags_deleted}")
                print(f"   - 删除归档结果: {results_deleted}")

                # 可选：删除文件系统中的快照目录
                try:
                    import shutil
                    snapshot_dir = os.path.join(ARCHIVEBOX_DATA_PATH, 'archive', str(snapshot_id))
                    if os.path.exists(snapshot_dir):
                        shutil.rmtree(snapshot_dir)
                        print(f"   - 删除快照目录: {snapshot_dir}")
                except Exception as e:
                    print(f"   - 删除快照目录失败: {e}")

                return "success"
            else:
                print(f"❌ 数据库中未找到要删除的快照: {snapshot_id}")
                return "not_found"

        except Exception as db_error:
            conn.rollback()
            print(f"❌ 数据库删除操作失败: {db_error}")
            return "failed"
        finally:
            cursor.close()
            conn.close()

    except subprocess.TimeoutExpired:
        print(f"删除快照超时({timeout}秒): {snapshot_id}")
        return "failed"
    except Exception as e:
        print(f"删除快照时出错: {e}")
        return "failed"


def analyze_archived_content(content, html_content, url, title=None, publication_date=None, analyze_after_crawl=True, snapshot_id=None):
    """分析归档的内容并存储到数据库"""
    print("\n分析文章内容...")

    def delete_invalid_snapshot(reason):
        """删除无效快照的辅助函数"""
        if snapshot_id:
            print(f"检测到无效内容，删除快照: {snapshot_id} - {reason}")
            try:
                result = remove_archivebox_snapshot(snapshot_id)
                if result == "success":
                    print(f"已删除无效快照: {snapshot_id}")
                elif result == "not_found":
                    print(f"ℹ快照不存在，可能已被删除: {snapshot_id}")
                else:
                    print(f"删除快照失败: {snapshot_id}")
            except Exception as e:
                print(f"删除快照时出错: {e}")

    analyzer = get_analyzer_instance()
    if not analyzer:
        print("无法初始化RAG分析器，只执行爬取任务")
        analyze_after_crawl = False
    else:
        analyze_after_crawl = True
        print("RAG分析器初始化成功，将在爬取后立即分析")
    success_count = 0
    failure_count = 0
    failure_reasons = {"storage_error": 0, "analysis_error": 0}
    analysis_id = None  # 初始化analysis_id

     # 检查内容编码
    print(f"内容编码检查 - 类型: {type(content)}, 长度: {len(content) if content else 0}")

    # === 内容质量检查 ===
    # 检查内容是否有效，过滤掉无意义的内容
    if not content or len(content.strip()) < 200:
        print(f"内容太短或为空（{len(content) if content else 0} 字符），跳过保存")
        delete_invalid_snapshot("内容太短或为空")
        return {"success": False, "error": "内容太短或为空", "analysis_id": None}

    # 检查标题是否有意义
    invalid_titles = [
        "Just a moment...",
        "Loading...",
        "Please wait...",
        "Access denied",
        "403 Forbidden",
        "404 Not Found",
        "500 Internal Server Error",
        "Not yet archived...",
        "Cloudflare",
        "Security check",
        "验证中...",
        "请稍候...",
        "加载中...",
        "访问被拒绝"
    ]

    if title and any(invalid_title.lower() in title.lower() for invalid_title in invalid_titles):
        print(f"检测到无效标题: '{title}'，跳过保存")
        delete_invalid_snapshot(f"无效标题: {title}")
        return {"success": False, "error": f"无效标题: {title}", "analysis_id": None}

    # 检查内容是否包含错误页面的特征
    error_indicators = [
        "just a moment",
        "cloudflare",
        "access denied",
        "403 forbidden",
        "404 not found",
        "security check",
        "please enable javascript",
        "browser check",
        "ddos protection"
    ]

    content_lower = content.lower()
    if any(indicator in content_lower for indicator in error_indicators):
        print(f"检测到错误页面内容，跳过保存")
        delete_invalid_snapshot("检测到错误页面内容")
        return {"success": False, "error": "检测到错误页面内容", "analysis_id": None}

    print("内容质量检查通过")

    if content and isinstance(content, str):
        # 检查是否包含可疑的乱码字符
        problematic_chars = sum(1 for c in content[:100] if ord(c) > 0xFFFF)
        if problematic_chars > 5:  # 如果前100个字符中有超过5个高码点字符，可能是编码问题
            print("警告：检测到可能的编码问题，尝试重新编码内容")
            # 重新编码内容
            content = content.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')

    # 同样检查HTML内容
    if html_content and isinstance(html_content, str):
        problematic_chars = sum(1 for c in html_content[:100] if ord(c) > 0xFFFF)
        if problematic_chars > 5:
            print("警告：检测到HTML内容可能存在编码问题，尝试重新编码")
            html_content = html_content.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')

    # 在保存之前检查是否已存在相同内容的分析
    try:
        print(f"检查是否已存在相同内容的分析...")

        # 规范化URL（移除参数部分）
        normalized_url = url.split('?')[0] if url else ""

        # 检查是否已存在相同标准化URL和标题的记录
        import mysql.connector
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)

        cursor.execute("""
            SELECT cd.id as crawled_data_id, ra.id as analysis_id, ra.analysis_status
            FROM crawled_data cd
            LEFT JOIN rag_analysis ra ON cd.id = ra.crawled_data_id
            WHERE
                SUBSTRING_INDEX(cd.link, '?', 1) = %s AND
                cd.title = %s
            ORDER BY cd.id DESC
            LIMIT 1
        """, (normalized_url, title))

        existing_record = cursor.fetchone()
        cursor.close()
        conn.close()

        if existing_record:
            if existing_record['analysis_id'] and existing_record['analysis_status'] == 1:
                print(f"✓ 发现已存在的成功分析记录: crawled_data_id={existing_record['crawled_data_id']}, analysis_id={existing_record['analysis_id']}")
                print(f"  URL: {url}")
                print(f"  标题: {title}")
                print("跳过重复分析")
                return {
                    "success": True,
                    "analysis_id": existing_record['analysis_id'],
                    "message": "已存在相同内容的分析，跳过重复分析"
                }
            else:
                print(f"发现已存在的记录但未分析或分析失败: crawled_data_id={existing_record['crawled_data_id']}")
                print("将重新分析此记录")

    except Exception as e:
        print(f"检查重复分析时出错: {e}")
        # 出错时继续执行，不影响正常流程

    # 保存到数据库中
    try:
        # 修改：保存函数返回完整记录而非仅ID
        print(f"准备存储文档，标题: '{title}'")

        # 确保数据库连接使用正确的字符集
        db_config = DB_CONFIG.copy()
        db_config['charset'] = 'utf8mb4'
        db_config['collation'] = 'utf8mb4_unicode_ci'

        inserted_count, new_records = save_to_database(
            db_config=db_config,
            all_results=[{
                'link': url,
                'title': title,
                'snippet': f"{url}",
                'query': f"discovered_from:{url}",
                'extracted_text': content,
                'html_content': html_content,
                'publication_date': publication_date,  # 添加发布日期
            }],
            return_records=True
        )
        
        if inserted_count == -1:
            print(f"数据库存储失败，继续处理下一条")
            failure_count += 1
            failure_reasons["storage_error"] += 1
        else:
            print(f"✓ 成功存储: {inserted_count} 条")
            success_count += 1
            
            # 处理分析逻辑
            if analyze_after_crawl:
                # 如果有新记录，分析新记录
                if new_records:
                    print(f"开始分析新爬取的数据...")
                    
                    # 直接使用存储返回的记录进行分析，避免二次查询
                    for record in new_records:
                        try:
                            # 使用记录中的ID（如果存在）
                            record_id = record.get('id', '未知ID')
                            print(f"分析文档 ID: {record_id}...")

                            # 在分析前检查是否已存在分析
                            crawled_data_id = record.get('id')
                            if crawled_data_id:
                                # 验证analyzer对象是否有check_existing_analysis方法
                                if hasattr(analyzer, 'check_existing_analysis'):
                                    existing_analysis = analyzer.check_existing_analysis(crawled_data_id, "all")
                                    if existing_analysis:
                                        print(f"跳过已分析的记录 ID: {crawled_data_id}")
                                        continue
                                else:
                                    # 动态添加check_existing_analysis方法到analyzer实例
                                    print(f"⚠️ analyzer对象缺少check_existing_analysis方法，尝试动态添加")
                                    if hasattr(analyzer, 'rag') and hasattr(analyzer.rag, 'check_existing_analysis'):
                                        # 动态绑定方法
                                        def check_existing_analysis_wrapper(crawled_data_id, analysis_type):
                                            return analyzer.rag.check_existing_analysis(crawled_data_id, analysis_type)

                                        # 将方法绑定到analyzer实例
                                        import types
                                        analyzer.check_existing_analysis = types.MethodType(
                                            lambda self, cid, at: self.rag.check_existing_analysis(cid, at),
                                            analyzer
                                        )

                                        print(f"✅ 成功动态添加check_existing_analysis方法")

                                        # 现在可以调用方法了
                                        existing_analysis = analyzer.check_existing_analysis(crawled_data_id, "all")
                                        if existing_analysis:
                                            print(f"跳过已分析的记录 ID: {crawled_data_id}")
                                            continue
                                    else:
                                        print(f"❌ 无法动态添加方法，analyzer.rag不可用或缺少方法")
                                        print(f"analyzer类型: {type(analyzer)}")
                                        print(f"analyzer.rag类型: {type(getattr(analyzer, 'rag', None))}")
                                        # 跳过重复检查，继续分析

                            # 直接使用记录数据进行调用
                            result = analyzer.analyze_from_crawled_data(
                                record_data=record,  # 传入完整记录
                                analysis_type="all"
                            )

                            # 拿取分析数据的id,返回的# 返回成功信息
                            current_analysis_id = result.get("analysis_id")
                            if current_analysis_id:
                                analysis_id = current_analysis_id  # 保存最后一个成功的analysis_id

                            if result.get("success"):
                                print(f"✓ 成功分析文档 {record_id}")
                            else:
                                error_msg = result.get('error', '未知错误')
                                print(f"分析文档 {record_id} 失败: {error_msg}")

                        except Exception as e:
                            print(f"分析文档时出错: {str(e)}")
                            traceback.print_exc()
                            
                elif inserted_count == 0:
                    # 重复链接情况，检查是否已有分析结果
                    print(f"检测到重复链接，检查是否已有分析结果...")
                    try:
                        with mysql.connector.connect(**DB_CONFIG) as conn:
                            cursor = conn.cursor(dictionary=True)
                            
                            # 查找该URL的最新分析结果
                            cursor.execute("""
                                SELECT ra.id as analysis_id, ra.analysis_status, cd.id as crawled_data_id
                                FROM crawled_data cd
                                LEFT JOIN rag_analysis ra ON cd.id = ra.crawled_data_id
                                WHERE cd.link = %s
                                ORDER BY ra.analysis_time DESC
                                LIMIT 1
                            """, (url,))
                            
                            existing_record = cursor.fetchone()
                            
                            if existing_record:
                                if existing_record['analysis_id'] and existing_record['analysis_status'] == 1:
                                    # 已有成功的分析结果
                                    analysis_id = existing_record['analysis_id']
                                    success_count += 1  # 标记为成功
                                    print(f"找到已有分析结果，analysis_id: {analysis_id}")
                                else:
                                    # 有记录但未分析或分析失败，重新分析
                                    crawled_data_id = existing_record['crawled_data_id']
                                    print(f"找到未分析记录，重新分析 ID: {crawled_data_id}")
                                    
                                    # 获取完整记录信息
                                    cursor.execute("""
                                        SELECT id, title, link, extracted_text, html_content
                                        FROM crawled_data WHERE id = %s
                                    """, (crawled_data_id,))
                                    
                                    record_data = cursor.fetchone()
                                    if record_data:
                                        # 添加 publication_date 到记录数据
                                        record_data['publication_date'] = publication_date
                                        
                                        result = analyzer.analyze_from_crawled_data(
                                            record_data=record_data,
                                            analysis_type="all"
                                        )
                                        
                                        current_analysis_id = result.get("analysis_id")
                                        if current_analysis_id:
                                            analysis_id = current_analysis_id
                                            success_count += 1  # 标记为成功
                                            print(f"✓ 成功重新分析文档 {crawled_data_id}")
                                        else:
                                            failure_count += 1  # 标记为失败
                                            print(f"重新分析失败: {result.get('error', '未知错误')}")
                            else:
                                print(f"警告: 未找到对应的数据库记录")
                                failure_count += 1  # 标记为失败
                                
                    except Exception as e:
                        print(f"检查重复链接分析结果时出错: {str(e)}")
                        traceback.print_exc()
                        failure_count += 1  # 标记为失败
                        
    except mysql.connector.Error as db_error:
        print(f"数据异常: {db_error}")
        failure_count += 1
        failure_reasons["storage_error"] += 1

    # 返回处理结果
    return {
        "success": success_count > 0,
        "analysis_id": analysis_id,
        "success_count": success_count,
        "failure_count": failure_count,
        "total_processed": success_count + failure_count
    }

def process_archive_box_content(urls=None, csv_file=None, batch_size=10, analyze=True):
    """处理和分析ArchiveBox中的内容"""
    # 初始化快照管理器
    snapshot_manager = SnapshotManager()
    
    # 获取URL列表
    if csv_file:
        logging.info(f"从CSV文件加载URL: {csv_file}")
        urls = load_urls_from_csv(csv_file)
    
    if not urls:
        logging.warning("没有提供URL，程序退出")
        return False
    
    logging.info(f"准备处理 {len(urls)} 个URL")
    
    # 添加URL到ArchiveBox，并获取快照ID
    successful_urls_with_ids = []
    for url in urls:
        # 首先检查是否已经存在该URL的快照
        existing_snapshot_id = find_snapshot_id_by_url(url)
        if existing_snapshot_id:
            logging.info(f"URL已存在于ArchiveBox中: {url}, 快照ID: {existing_snapshot_id}")
            successful_urls_with_ids.append((url, existing_snapshot_id))
            # 添加到快照管理器，状态为pending
            snapshot_manager.add_snapshot(existing_snapshot_id, url, "pending")
            continue
        
        # 如果不存在，添加新URL
        success, snapshot_id = add_url_to_archivebox(url, timeout=300)
        if success:
            if snapshot_id:
                logging.info(f"成功添加URL: {url}, 快照ID: {snapshot_id}")
                successful_urls_with_ids.append((url, snapshot_id))
                # 将快照ID添加到管理系统
                snapshot_manager.add_snapshot(snapshot_id, url, "pending")
            else:
                logging.warning(f"添加成功但未获得快照ID: {url}")
                # 尝试再次查找
                found_id = find_snapshot_id_by_url(url)
                if found_id:
                    successful_urls_with_ids.append((url, found_id))
                    snapshot_manager.add_snapshot(found_id, url, "pending")
                    logging.info(f"在二次查询中找到快照ID: {found_id}")
        else:
            logging.warning(f"添加URL失败: {url}")
    
    if not successful_urls_with_ids:
        logging.warning("没有成功添加的URL，程序退出")
        return False
    
    # 确保快照管理器保存状态
    snapshot_manager.save_snapshots()
    
    # 处理结果统计
    results = {
        'total': len(successful_urls_with_ids),
        'extracted': 0,
        'analyzed': 0,
        'saved': 0,
        'failed': 0
    }
    
    # 如果启用了立即分析，则处理每个URL
    if analyze:
        for i, (url, snapshot_id) in enumerate(successful_urls_with_ids):
            if not snapshot_id:
                logging.warning(f"缺少快照ID，跳过: {url}")
                continue
                
            logging.info(f"\n处理URL [{i+1}/{len(successful_urls_with_ids)}]: {url} (快照ID: {snapshot_id})")
            
            try:
                # 从ArchiveBox中提取内容
                content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id,url)
                
                if not content and not html_content:
                    logging.warning(f"未能提取内容，跳过: {url}")
                    snapshot_manager.mark_processed(snapshot_id, success=False)
                    results['failed'] += 1
                    continue
                    
                results['extracted'] += 1
                
                # 分析内容
                if content:
                    analysis_result = analyze_archived_content(
                        content, html_content, url, title, publication_date,
                        analyze_after_crawl=True, snapshot_id=snapshot_id
                    )
                    
                    if analysis_result.get("success", False):
                        results['analyzed'] += 1
                        logging.info(f"成功分析文章: {url}")
                        snapshot_manager.mark_processed(snapshot_id, success=True)
                    else:
                        logging.warning(f"分析失败: {url}")
                        snapshot_manager.mark_processed(snapshot_id, success=False)
                else:
                    logging.warning(f"保存到数据库失败: {url}")
                    snapshot_manager.mark_processed(snapshot_id, success=False)
            
            except Exception as e:
                logging.error(f"处理URL时出错: {url}, 错误: {str(e)}")
                traceback.print_exc()
                if snapshot_id:
                    snapshot_manager.mark_processed(snapshot_id, success=False)
                results['failed'] += 1
    else:
        logging.info("分析功能已禁用，快照将在后续处理")
        # 即使不立即分析，也已经将快照ID保存在了管理器中
    
    # 确保保存更新后的状态
    snapshot_manager.save_snapshots()
    
    # 显示最终结果
    logging.info("\n" + "="*50)
    logging.info("处理完成! 统计信息:")
    logging.info(f"总URL数量: {results['total']}")
    
    if analyze:
        logging.info(f"成功提取内容: {results['extracted']}")
        logging.info(f"成功分析文章: {results['analyzed']}")
        logging.info(f"失败数量: {results['failed']}")
    
    # 显示快照状态
    stats = snapshot_manager.get_stats()
    logging.info(f"快照状态: 总计 {stats['total']} (待处理: {stats['pending']}, 已处理: {stats['processed']}, 失败: {stats['failed']})")
    logging.info("="*50)
    
    return results

def load_urls_from_csv(file_path):
    """从CSV文件中加载URL"""
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return []
    
    urls = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if 'link' in row and row['link']:
                    urls.append(row['link'])
    except Exception as e:
        logging.error(f"读取CSV文件时出错: {str(e)}")
        traceback.print_exc()
    
    return urls


def validate_database_encoding():
    """确认数据库使用正确的字符编码"""
    try:
        with mysql.connector.connect(**DB_CONFIG) as conn:
            with conn.cursor() as cursor:
                # 检查数据库字符集
                cursor.execute("SHOW VARIABLES LIKE 'character_set_database'")
                db_charset = cursor.fetchone()
                
                cursor.execute("SHOW VARIABLES LIKE 'collation_database'")
                db_collation = cursor.fetchone()
                
                cursor.execute("SHOW VARIABLES LIKE 'character_set_connection'")
                conn_charset = cursor.fetchone()
                
                logging.info(f"数据库字符集: {db_charset}")
                logging.info(f"数据库排序规则: {db_collation}")
                logging.info(f"连接字符集: {conn_charset}")
                
                # 如果不是utf8mb4，打印警告
                if db_charset and db_charset[1] != 'utf8mb4':
                    logging.warning(f"数据库字符集不是utf8mb4，可能导致字符编码问题: {db_charset}")
                    logging.warning("建议设置数据库字符集为utf8mb4")
                    
        return True
    except Exception as e:
        logging.error(f"验证数据库编码时出错: {e}")
        return False


def inspect_snapshot(snapshot_path):
    """全面检查快照文件系统状态"""
    # 从路径中提取snapshot_id
    # 可能的格式: './archive/1750922791.820951/index.json' 或 '1750922791.820951'
    if '/' in snapshot_path:
        path_parts = snapshot_path.split('/')
        # 查找数字格式的部分作为ID
        for part in path_parts:
            if re.match(r'^\d+\.\d+$', part):
                snapshot_id = part
                break
        else:
            # 如果没有找到匹配的数字格式，使用倒数第二个部分
            snapshot_id = path_parts[-2] if len(path_parts) > 1 else snapshot_path
    else:
        snapshot_id = snapshot_path
    
    print(f"\n===== 开始检查快照 {snapshot_id} =====\n")
    
    # 1. 基本文件列表
    print("\n1. 基本文件列表")
    list_cmd = f"find {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id} -type f | sort"
    subprocess.run(list_cmd, shell=True)
    
    # 2. 详细文件状态
    print("\n2. 详细文件状态")
    ls_cmd = f"ls -la {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/"
    subprocess.run(ls_cmd, shell=True)
    
    # 3. 子目录状态
    print("\n3. 重要子目录状态")
    for subdir in ['readability', 'singlefile', 'dom', 'wget', 'mercury']:
        subdir_cmd = f"ls -la {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/{subdir}/ 2>/dev/null || echo '{subdir} 目录不存在'"
        print(f"\n--- {subdir} 目录 ---")
        subprocess.run(subdir_cmd, shell=True)
    
    # 4. 查看重要文件内容预览
    print("\n4. 重要文件内容预览")
    for file_path in [
        f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/index.json',
        f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/readability/content.txt',
        f'{ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/readability/article.json'
    ]:
        preview_cmd = f"head -20 {file_path} 2>/dev/null || echo '文件 {file_path} 不存在或为空'"
        print(f"\n--- {file_path} 预览 ---")
        subprocess.run(preview_cmd, shell=True)
    
    # 5. 查看空间使用情况
    print("\n5. 空间使用情况")
    du_cmd = f"du -sh {ARCHIVEBOX_DATA_PATH}/archive/{snapshot_id}/* 2>/dev/null"
    subprocess.run(du_cmd, shell=True)
    
    print(f"\n===== 快照 {snapshot_id} 检查完成 =====\n")

def main():
    parser = argparse.ArgumentParser(description='ArchiveBox内容提取与分析工具')
    parser.add_argument('--csv', type=str, help='包含URL列表的CSV文件路径')
    parser.add_argument('--url', type=str, help='单个URL进行处理')
    parser.add_argument('--batch-size', type=int, default=10, help='批处理大小，默认为10')
    parser.add_argument('--no-analyze', action='store_true', help='仅存档而不进行分析')
    parser.add_argument('--process-snapshots', action='store_true', help='处理待处理的快照')
    parser.add_argument('--limit', type=int, default=10, help='处理快照的数量限制，默认为10')
    parser.add_argument('--snapshot-info', type=str, help='显示指定快照ID的信息')
    parser.add_argument('--cleanup', action='store_true', help='清理失败的快照记录')
    parser.add_argument('--timeout', type=int, default=600, help='添加URL的超时时间(秒)，默认600秒')
    parser.add_argument('--retry', type=int, default=3, help='失败操作的重试次数，默认3次')
    
    args = parser.parse_args()
    
    # 验证数据库编码
    validate_database_encoding()
    
    # 清理失败的快照
    if args.cleanup:
        snapshot_manager = SnapshotManager()
        failed_count = len(snapshot_manager.snapshots["failed"])
        for snapshot in snapshot_manager.snapshots["failed"]:
            snapshot_manager.add_snapshot(snapshot["id"], snapshot["url"], "pending")
        print(f"已将 {failed_count} 个失败的快照重置为待处理状态")
        return
    
    # 处理快照信息查询
    if args.snapshot_info:
        snapshot_id = args.snapshot_info
        print(f"查询快照信息: {snapshot_id}")
        content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id,args.url)

        print(f"快照标题: {title}")
        print(f"发布日期: {publication_date}")
        print(f"内容长度: {len(content) if content else 0}字符")
        print(f"HTML长度: {len(html_content) if html_content else 0}字符")
        return
    
    # 处理待处理的快照
    if args.process_snapshots:
        process_pending_snapshots(limit=args.limit, analyze=not args.no_analyze)
        return
    
    # 处理URL添加
    if not args.csv and not args.url:
        logging.error("请提供CSV文件路径或单个URL，或使用--process-snapshots选项处理待处理快照")
        parser.print_help()
        return
    
    urls = []
    if args.url:
        urls.append(args.url)
    
    process_archive_box_content(
        urls=urls,
        csv_file=args.csv,
        batch_size=args.batch_size,
        analyze=not args.no_analyze
    )

if __name__ == "__main__":
    main()

# 添加url到ArchiveBox但不进行分析
# python Archivebox_cti.py --url "https://example.com/article" --no-analyze

# 处理未分析的记录
# python Archivebox_cti.py --process-snapshots --limit 20

# 查看链接信息
# python Archivebox_cti.py --snapshot-info "1750922791.820951"

def extract_date_from_mixed_text(mixed_text):
    """
    从包含日期和其他信息的混合文本中提取日期
    
    Args:
        mixed_text (str): 包含日期的混合文本，如 "Jun 30, 2025Ravie Lakshmanan"
        
    Returns:
        str: 提取到的日期字符串，如果未找到则返回None
    """
    if not mixed_text or not isinstance(mixed_text, str):
        return None
    
    # 定义多种日期格式的正则表达式模式（按精确度排序）
    date_patterns = [
        # 1. 完整的英文日期格式 (最精确)
        (r'([A-Z][a-z]{2,8}\s+\d{1,2},?\s+\d{4})', 'English month date format'),
        # 示例: "Jun 30, 2025", "June 30, 2025", "Jan 1, 2024"
        
        # 2. ISO标准日期格式
        (r'(\d{4}-\d{2}-\d{2})', 'ISO date format'),
        # 示例: "2025-06-30"
        
        # 3. 数字日期格式
        (r'(\d{1,2}/\d{1,2}/\d{4})', 'Numeric date format MM/DD/YYYY'),
        # 示例: "6/30/2025", "06/30/2025"
        
        # 4. 欧式日期格式
        (r'(\d{1,2}\.\d{1,2}\.\d{4})', 'European date format DD.MM.YYYY'),
        # 示例: "30.06.2025"
        
        # 5. 中文日期格式
        (r'(\d{4}年\d{1,2}月\d{1,2}日)', 'Chinese date format'),
        # 示例: "2025年6月30日"
        
        # 6. 带时间的ISO格式
        (r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})', 'ISO datetime format'),
        # 示例: "2025-06-30T12:00:00"
        
        # 7. 简化的年月日格式
        (r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', 'Simple date format YYYY-MM-DD or YYYY/MM/DD'),
        # 示例: "2025-6-30", "2025/6/30"
    ]
    
    print(f"尝试从混合文本中提取日期: '{mixed_text}'")
    
    # 按优先级尝试每个模式
    for pattern, pattern_name in date_patterns:
        try:
            matches = re.findall(pattern, mixed_text)
            if matches:
                # 取第一个匹配的日期
                extracted_date = matches[0].strip()
                
                # 验证提取的日期是否合理
                if is_valid_date_string(extracted_date):
                    print(f"✓ 使用模式'{pattern_name}'成功提取日期: '{extracted_date}'")
                    return extracted_date
                else:
                    print(f"模式'{pattern_name}'匹配到 '{extracted_date}' 但验证失败，继续尝试其他模式")
                    
        except Exception as e:
            print(f"使用模式'{pattern_name}'时出错: {str(e)}")
            continue
    
    print(f"所有日期提取模式都未能从文本中提取到有效日期: '{mixed_text}'")
    return None


def is_valid_date_string(date_str):
    """
    验证日期字符串是否为有效的日期格式
    
    Args:
        date_str (str): 待验证的日期字符串
        
    Returns:
        bool: 如果是有效日期格式返回True，否则返回False
    """
    if not date_str or len(date_str.strip()) < 4:
        return False
    
    # 基本格式检查：包含年份信息
    if not re.search(r'20\d{2}|19\d{2}', date_str):
        return False
    
    # 检查是否包含明显的非日期内容（如人名）
    # 如果包含连续的字母（可能是人名），则认为不是纯日期
    consecutive_letters = re.findall(r'[A-Za-z]{3,}', date_str)
    if len(consecutive_letters) > 1:
        # 如果有多个连续字母组合，可能包含人名等非日期信息
        # 但月份名称是允许的，所以需要检查是否为月份
        month_names = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec',
                      'january', 'february', 'march', 'april', 'may', 'june', 
                      'july', 'august', 'september', 'october', 'november', 'december']
        
        non_month_words = [word.lower() for word in consecutive_letters 
                          if word.lower() not in month_names]
        
        if len(non_month_words) > 0:
            print(f"日期字符串包含非月份的单词，可能不是纯日期: {non_month_words}")
            return False
    
    # 长度检查：合理的日期字符串长度
    if len(date_str.strip()) > 50:
        print(f"日期字符串过长，可能包含其他信息: {len(date_str)} 字符")
        return False
    
    return True



def get_existing_links_from_archivebox(timeout=60):
    """
    获取ArchiveBox中已存在的所有链接
    
    Returns:
        set: 包含所有已归档URL的集合
    """
    print("正在查询ArchiveBox中的所有已归档链接...")
    existing_links = set()
    
    try:
        import sqlite3
        import os

        # 直接查询ArchiveBox的SQLite数据库
        archivebox_db_path = os.path.join(ARCHIVEBOX_DATA_PATH, 'index.sqlite3')

        if not os.path.exists(archivebox_db_path):
            print(f"❌ ArchiveBox数据库不存在: {archivebox_db_path}")
            return existing_links

        print(f"🔍 直接查询ArchiveBox SQLite数据库: {archivebox_db_path}")

        # 连接ArchiveBox SQLite数据库
        conn = sqlite3.connect(archivebox_db_path)
        cursor = conn.cursor()

        # 查询所有已归档的URL
        cursor.execute("SELECT url FROM core_snapshot")

        for row in cursor.fetchall():
            url = row[0]
            if url:
                existing_links.add(url)

        cursor.close()
        conn.close()

        print(f"✓ 成功从ArchiveBox获取到 {len(existing_links)} 个已归档链接")
        return existing_links

    except Exception as e:
        print(f"❌ 查询ArchiveBox SQLite数据库失败: {str(e)}")

        # 如果SQLite查询失败，尝试备用方法
        try:
            print("🔄 尝试备用方法查询ArchiveBox链接...")
            # 使用文件系统直接读取，修正路径
            simple_cmd = f"find {ARCHIVEBOX_DATA_PATH}/archive -name 'index.json' -exec grep -l 'url' {{}} + | head -100"
            result = subprocess.run(simple_cmd, shell=True, capture_output=True, text=True,
                                  encoding='utf-8', errors='replace', timeout=timeout)

            if result.returncode == 0 and result.stdout:
                import json
                # 从找到的index.json文件中提取URL
                for index_file in result.stdout.strip().split('\n'):
                    if index_file.strip():
                        try:
                            cat_cmd = f"cat '{index_file}'"
                            cat_result = subprocess.run(cat_cmd, shell=True, capture_output=True, text=True,
                                                      encoding='utf-8', errors='replace', timeout=10)

                            if cat_result.returncode == 0:
                                entry_data = json.loads(cat_result.stdout)
                                if 'url' in entry_data:
                                    existing_links.add(entry_data['url'])
                        except Exception:
                            continue

                print(f"✓ 备用方法获取到 {len(existing_links)} 个已归档链接")
            else:
                print(f"⚠️ 备用方法未找到任何归档文件")

        except Exception as backup_e:
            print(f"❌ 备用方法也失败: {str(backup_e)}")
    
    return existing_links

def get_archivebox_snapshot_url(snapshot_id):
    """
    根据快照ID获取ArchiveBox查看URL

    Args:
        snapshot_id (str): ArchiveBox快照ID

    Returns:
        str: 完整的ArchiveBox查看URL，如果失败则返回None
    """
    try:
        # 默认ArchiveBox服务器地址（可以从环境变量配置）
        archivebox_base_url = os.getenv('ARCHIVEBOX_BASE_URL', 'http://localhost:8011')

        # 构建快照查看URL
        snapshot_url = f"{archivebox_base_url}/archive/{snapshot_id}/index.html"

        # print(f"[快照URL] 生成快照URL: {snapshot_url}")

        # 直接返回URL，不进行可访问性验证（避免每次都连接检查）
        # 可访问性检查会在 check_archivebox_service() 中统一进行
        return snapshot_url

    except Exception as e:
        print(f"[快照URL] 错误：生成快照URL时出错: {e}")
        return None


# 服务状态缓存
_service_status_cache = None
_service_check_time = None

def check_archivebox_service():
    """检查ArchiveBox服务是否运行 - 带缓存机制"""
    global _service_status_cache, _service_check_time

    # 如果缓存存在且未过期（5分钟），直接返回缓存结果
    if (_service_status_cache is not None and
        _service_check_time is not None and
        time.time() - _service_check_time < 300):
        return _service_status_cache

    try:
        archivebox_base_url = os.getenv('ARCHIVEBOX_BASE_URL', 'http://localhost:8011')
        print(f"[服务检查] 检查ArchiveBox服务: {archivebox_base_url}")
        response = requests.get(archivebox_base_url, timeout=3)
        service_running = response.status_code == 200
        print(f"[服务检查] 服务状态: {'运行中' if service_running else '未运行'}")

        # 更新缓存
        _service_status_cache = service_running
        _service_check_time = time.time()

        return service_running
    except requests.RequestException as e:
        print(f"[服务检查] 服务检查失败: {e}")
        # 更新缓存为False
        _service_status_cache = False
        _service_check_time = time.time()
        return False