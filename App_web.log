nohup: ignoring input
正在初始化任务管理器...
任务表已存在，检查表结构...
任务状态表创建/检查完成
任务管理器初始化成功
清理了 0 个超时任务
正在初始化威胁情报配置管理器...
INFO:config_manager:威胁情报网站配置表创建/检查完成
INFO:config_manager:威胁情报网站配置表已有 40 条数据，跳过初始化
INFO:config_manager:威胁情报配置数据库初始化完成
威胁情报配置管理器初始化成功
✓ 配置管理器工作正常，共有 40 个网站配置
正在初始化ArchiveBox快照缓存...正在初始化RAG分析器...

INFO:root:正在初始化RAG分析器...
创建RAG分析器单例pa_week: 检测到主应用正在初始化，跳过RAG初始化

初始化RAG分析器...
ChromaRetriever: 使用网络安全专用嵌入模型: sentence-transformer-models/ATTACK-BERT
正在加载网络安全嵌入模型: sentence-transformer-models/ATTACK-BERT
✓ 发现本地 ATT&CK BERT 模型: sentence-transformer-models/ATTACK-BERT
INFO:sentence_transformers.SentenceTransformer:Load pretrained SentenceTransformer: sentence-transformer-models/ATTACK-BERT
ArchiveBox快照缓存初始化完成，共缓存 51 个快照
✓ 成功加载本地 ATT&CK BERT 模型 (使用安全绕过)
正在加载向量数据库: data/test/chunks/threat_intel
正在从 data/test/chunks/threat_intel 加载向量数据库...
使用已初始化的嵌入模型...
INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
向量数据库加载完成! 耗时: 0.90秒，包含 67006 个文档
成功加载Chroma向量数据库: data/test/chunks/threat_intel
数据库连接和表结构初始化成功
RAG系统初始化完成
初始化数据库连接...
数据库连接初始化完成
RAG分析器初始化完成
✓ 已应用subprocess UTF-8编码修补
INFO:root:正在初始化RAG分析器...
pa_week: 检测到主应用正在初始化，跳过RAG初始化
应用启动 - 开始初始化ArchiveBox缓存...
ArchiveBox缓存初始化线程已启动应用启动 - ArchiveBox缓存初始化完成
[缓存] 后台开始刷新ArchiveBox快照缓存...

正在获取ArchiveBox中的所有快照信息... * Serving Flask app 'app'
 * Debug mode: off

INFO:werkzeug:[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5011
 * Running on http://************:5011
INFO:werkzeug:[33mPress CTRL+C to quit[0m
INFO:werkzeug:************ - - [23/Jul/2025 09:39:38] "[31m[1mGET /analysis/724?action=view HTTP/1.1[0m" 401 -
成功获取 51 个快照信息
[缓存] 后台成功刷新缓存，包含 51 个快照
INFO:werkzeug:************ - - [23/Jul/2025 09:39:55] "GET /analysis/724?action=view HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 09:39:55] "[31m[1mGET /api/analysis/724 HTTP/1.1[0m" 401 -
INFO:werkzeug:************ - - [23/Jul/2025 09:39:55] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 09:39:55] "GET /api/analysis/724 HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 09:39:56] "[31m[1mGET /api/snapshot_by_url?url=https://securelist.com/ru/ HTTP/1.1[0m" 401 -
[快照查询] 收到请求，URL: https://securelist.com/ru/
[快照查询] 从缓存中查找URL: https://securelist.com/ru/
[快照查询] 缓存查询结果: 1753156724.390249
[快照查询] 返回成功结果: {'success': True, 'has_snapshot': True, 'snapshot_id': '1753156724.390249', 'snapshot_url': 'http://************:8011/archive/1753156724.390249/index.html', 'message': '找到快照ID: 1753156724.390249', 'from_cache': True}
INFO:werkzeug:************ - - [23/Jul/2025 09:39:56] "GET /api/snapshot_by_url?url=https://securelist.com/ru/ HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 09:39:57] "[31m[1mGET / HTTP/1.1[0m" 401 -
INFO:werkzeug:************ - - [23/Jul/2025 09:39:57] "GET / HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 09:39:57] "GET /api/analysis_records HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 09:39:59] "GET /analysis/736?action=view HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 09:39:59] "GET /api/analysis/736 HTTP/1.1" 200 -
[快照查询] 收到请求，URL: http://mp.weixin.qq.com/s?__biz=MzI2MDc2MDA4OA==&mid=2247515391&idx=1&sn=c7106be4cceef2d65d657bd4ee313993&chksm=ea664b88dd11c29e7dc288a943bef5e75297324e04074748fed5e74090d0b300dda343ffca64#rd
[快照查询] 从缓存中查找URL: http://mp.weixin.qq.com/s?__biz=MzI2MDc2MDA4OA==&mid=2247515391&idx=1&sn=c7106be4cceef2d65d657bd4ee313993&chksm=ea664b88dd11c29e7dc288a943bef5e75297324e04074748fed5e74090d0b300dda343ffca64#rd
[快照查询] 缓存查询结果: 1753169403.225094
[快照查询] 返回成功结果: {'success': True, 'has_snapshot': True, 'snapshot_id': '1753169403.225094', 'snapshot_url': 'http://************:8011/archive/1753169403.225094/index.html', 'message': '找到快照ID: 1753169403.225094', 'from_cache': True}
INFO:werkzeug:************ - - [23/Jul/2025 09:39:59] "GET /api/snapshot_by_url?url=http://mp.weixin.qq.com/s?__biz%3DMzI2MDc2MDA4OA%3D%3D%26mid%3D2247515391%26idx%3D1%26sn%3Dc7106be4cceef2d65d657bd4ee313993%26chksm%3Dea664b88dd11c29e7dc288a943bef5e75297324e04074748fed5e74090d0b300dda343ffca64%23rd HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 09:40:04] "GET / HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 09:40:04] "GET /api/analysis_records HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 09:40:05] "GET /config_management HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 09:40:05] "GET /static/js/config_management.js HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 09:40:06] "GET /api/config/sites HTTP/1.1" 200 -
任务表已存在，检查表结构...
任务状态表创建/检查完成
INFO:werkzeug:************ - - [23/Jul/2025 09:40:06] "GET /api/config/sites HTTP/1.1" 200 -
INFO:__main__:获取到任务: {'id': 5, 'task_type': 'weekly_crawl', 'task_status': 'failed', 'total_count': 1, 'completed_count': 0, 'success_count': 0, 'error_count': 0, 'start_time': datetime.datetime(2025, 7, 22, 18, 3, 13), 'end_time': datetime.datetime(2025, 7, 22, 18, 20, 37), 'created_by': 'system', 'parent_task_id': None, 'is_follow_up_analysis': 0, 'progress': 0.0}
INFO:werkzeug:************ - - [23/Jul/2025 09:40:06] "GET /api/config/sites/weekly-crawl/status HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:23:58] "GET /analysis HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:23:58] "GET /static/js/task-manager.js?v=20250709-5 HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:23:58] "GET /static/js/enhanced-analysis.js?v=20250709-5 HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:00] "GET / HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 10:24:01] "GET /api/analysis_records HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:02] "GET /analysis/743?action=view HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 10:24:03] "GET /api/analysis/743 HTTP/1.1" 200 -
[快照查询] 收到请求，URL: https://securityaffairs.com/180182/hacking/sharepoint-zero-day-cve-2025-53770-actively-exploited-in-the-wild.html?amp
[快照查询] 从缓存中查找URL: https://securityaffairs.com/180182/hacking/sharepoint-zero-day-cve-2025-53770-actively-exploited-in-the-wild.html?amp
[快照查询] 缓存查询结果: None
[快照查询] 缓存中未找到URL: https://securityaffairs.com/180182/hacking/sharepoint-zero-day-cve-2025-53770-actively-exploited-in-the-wild.html?amp
[快照查询] 启动后台缓存刷新...
[快照查询] 返回未找到结果: {'success': True, 'has_snapshot': False, 'snapshot_id': None, 'snapshot_url': None, 'message': '该链接没有使用ArchiveBox存档'}
INFO:werkzeug:************ - - [23/Jul/2025 10:24:03] "GET /api/snapshot_by_url?url=https://securityaffairs.com/180182/hacking/sharepoint-zero-day-cve-2025-53770-actively-exploited-in-the-wild.html?amp HTTP/1.1" 200 -
[缓存] 后台开始刷新ArchiveBox快照缓存...
正在获取ArchiveBox中的所有快照信息...
INFO:werkzeug:************ - - [23/Jul/2025 10:24:05] "GET /analysis/742?action=view HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 10:24:06] "GET /api/analysis/742 HTTP/1.1" 200 -
[快照查询] 收到请求，URL: https://www.cyfirma.com/research/cyfirma-industry-report-materials-6/
[快照查询] 从缓存中查找URL: https://www.cyfirma.com/research/cyfirma-industry-report-materials-6/
[快照查询] 缓存查询结果: 1753175809.918718
[快照查询] 返回成功结果: {'success': True, 'has_snapshot': True, 'snapshot_id': '1753175809.918718', 'snapshot_url': 'http://************:8011/archive/1753175809.918718/index.html', 'message': '找到快照ID: 1753175809.918718', 'from_cache': True}
INFO:werkzeug:************ - - [23/Jul/2025 10:24:06] "GET /api/snapshot_by_url?url=https://www.cyfirma.com/research/cyfirma-industry-report-materials-6/ HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:17] "GET /analysis/724?action=view HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 10:24:18] "GET /api/analysis/724 HTTP/1.1" 200 -
[快照查询] 收到请求，URL: https://securelist.com/ru/
[快照查询] 从缓存中查找URL: https://securelist.com/ru/
[快照查询] 缓存查询结果: 1753156724.390249
[快照查询] 返回成功结果: {'success': True, 'has_snapshot': True, 'snapshot_id': '1753156724.390249', 'snapshot_url': 'http://************:8011/archive/1753156724.390249/index.html', 'message': '找到快照ID: 1753156724.390249', 'from_cache': True}
INFO:werkzeug:************ - - [23/Jul/2025 10:24:18] "GET /api/snapshot_by_url?url=https://securelist.com/ru/ HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:25] "GET /analysis/737?action=view HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 10:24:25] "GET /api/analysis/737 HTTP/1.1" 200 -
[快照查询] 收到请求，URL: http://mp.weixin.qq.com/s?__biz=MzUyMjk4NzExMA==&mid=2247507128&idx=1&sn=fc52ed41c425b97d96e8aa395b01cb16&chksm=f9c1efb1ceb666a767280237edff78a1beaf65e5c643caebfe6c9166b8c766033f8574f5ea7d#rd
[快照查询] 从缓存中查找URL: http://mp.weixin.qq.com/s?__biz=MzUyMjk4NzExMA==&mid=2247507128&idx=1&sn=fc52ed41c425b97d96e8aa395b01cb16&chksm=f9c1efb1ceb666a767280237edff78a1beaf65e5c643caebfe6c9166b8c766033f8574f5ea7d#rd
[快照查询] 缓存查询结果: 1753169111.806343
[快照查询] 返回成功结果: {'success': True, 'has_snapshot': True, 'snapshot_id': '1753169111.806343', 'snapshot_url': 'http://************:8011/archive/1753169111.806343/index.html', 'message': '找到快照ID: 1753169111.806343', 'from_cache': True}
INFO:werkzeug:************ - - [23/Jul/2025 10:24:25] "GET /api/snapshot_by_url?url=http://mp.weixin.qq.com/s?__biz%3DMzUyMjk4NzExMA%3D%3D%26mid%3D2247507128%26idx%3D1%26sn%3Dfc52ed41c425b97d96e8aa395b01cb16%26chksm%3Df9c1efb1ceb666a767280237edff78a1beaf65e5c643caebfe6c9166b8c766033f8574f5ea7d%23rd HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:34] "GET / HTTP/1.1" 200 -
数据库连接成功
INFO:werkzeug:************ - - [23/Jul/2025 10:24:34] "GET /api/analysis_records HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:35] "GET /config_management HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:35] "GET /static/js/config_management.js HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:35] "GET /api/config/sites HTTP/1.1" 200 -
任务表已存在，检查表结构...
任务状态表创建/检查完成
INFO:__main__:获取到任务: {'id': 5, 'task_type': 'weekly_crawl', 'task_status': 'failed', 'total_count': 1, 'completed_count': 0, 'success_count': 0, 'error_count': 0, 'start_time': datetime.datetime(2025, 7, 22, 18, 3, 13), 'end_time': datetime.datetime(2025, 7, 22, 18, 20, 37), 'created_by': 'system', 'parent_task_id': None, 'is_follow_up_analysis': 0, 'progress': 0.0}
INFO:werkzeug:************ - - [23/Jul/2025 10:24:35] "GET /api/config/sites/weekly-crawl/status HTTP/1.1" 200 -
INFO:werkzeug:************ - - [23/Jul/2025 10:24:35] "GET /api/config/sites HTTP/1.1" 200 -
任务表已存在，检查表结构...
任务状态表创建/检查完成
INFO:__main__:启动进程 PID: 60833, 脚本: /home/<USER>/docker_archivebox/Data_Excation/pa_week_ar_test.py
INFO:werkzeug:************ - - [23/Jul/2025 10:24:36] "POST /api/config/sites/weekly-crawl HTTP/1.1" 200 -
INFO:__main__:进程 60833 正在运行
任务表已存在，检查表结构...
任务状态表创建/检查完成
INFO:werkzeug:************ - - [23/Jul/2025 10:24:39] "GET /api/analysis/tasks HTTP/1.1" 200 -
成功获取 51 个快照信息
[缓存] 后台成功刷新缓存，包含 51 个快照
任务表已存在，检查表结构...
任务状态表创建/检查完成
INFO:werkzeug:************ - - [23/Jul/2025 10:24:42] "GET /api/analysis/tasks/6 HTTP/1.1" 200 -
