<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务状态测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="/static/js/task-manager.js"></script>
    <style>
        .task-status-container {
            background-color: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .task-status-card {
            transition: all 0.3s ease;
        }
        
        .task-status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .task-progress .progress {
            background-color: #e9ecef;
        }
        
        .task-actions {
            display: flex;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>任务状态管理测试</h1>
        
        <!-- 测试按钮 -->
        <div class="mb-4">
            <h3>测试功能</h3>
            <div class="btn-group" role="group">
                <button class="btn btn-primary" onclick="testBatchAnalysis()">测试批量分析</button>
                <button class="btn btn-secondary" onclick="testArchiveBoxAnalysis()">测试ArchiveBox分析</button>
                <button class="btn btn-info" onclick="testTaskHistory()">查看任务历史</button>
                <button class="btn btn-warning" onclick="testBatchUnanalyzed()">测试批量分析快照</button>
            </div>
        </div>
        
        <!-- 任务状态容器 -->
        <div id="task-status-container" class="task-status-container">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-list-task me-2"></i>当前任务状态
                    </h5>
                </div>
                <div class="card-body" id="task-list">
                    <p class="text-muted">暂无活跃任务</p>
                </div>
            </div>
        </div>
        
        <!-- 消息显示区域 -->
        <div id="message-area"></div>
        
        <!-- 任务历史模态框容器 -->
        <div id="modal-container"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 测试函数
        async function testBatchAnalysis() {
            try {
                const response = await fetch('/api/analysis_batch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        batch_size: 3,
                        analysis_type: 'all',
                        input_type: 'database'
                    })
                });
                
                const result = await response.json();
                console.log('批量分析响应:', result);
                
                if (result.task_id) {
                    handleTaskResponse(result);
                } else {
                    showMessage(result.message || '请求已提交', 'info');
                }
            } catch (error) {
                showMessage('测试批量分析失败: ' + error, 'danger');
            }
        }
        
        async function testArchiveBoxAnalysis() {
            try {
                const response = await fetch('/api/analysis_archivebox', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        urls: [
                            'https://example.com',
                            'https://test.com'
                        ],
                        analysis_type: 'all',
                        no_analyze: false
                    })
                });
                
                const result = await response.json();
                console.log('ArchiveBox分析响应:', result);
                
                if (result.task_id) {
                    handleTaskResponse(result);
                } else {
                    showMessage(result.message || '请求已提交', 'info');
                }
            } catch (error) {
                showMessage('测试ArchiveBox分析失败: ' + error, 'danger');
            }
        }
        
        async function testBatchUnanalyzed() {
            try {
                const response = await fetch('/api/batch_analyze_unanalyzed', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        limit: 10,
                        analysis_type: 'all'
                    })
                });
                
                const result = await response.json();
                console.log('批量分析快照响应:', result);
                
                if (result.task_id) {
                    handleTaskResponse(result);
                } else {
                    showMessage(result.message || '请求已提交', 'info');
                }
            } catch (error) {
                showMessage('测试批量分析快照失败: ' + error, 'danger');
            }
        }
        
        async function testTaskHistory() {
            try {
                const tasks = await taskManager.getTaskList();
                console.log('任务历史:', tasks);
                showMessage(`获取到 ${tasks.length} 个历史任务`, 'info');
                
                if (tasks.length > 0) {
                    // 显示最近的任务
                    const recent = tasks[0];
                    showMessage(`最近任务: ${taskManager.getTaskTypeText(recent.task_type)} (${taskManager.getStatusText(recent.task_status)})`, 'secondary');
                }
            } catch (error) {
                showMessage('获取任务历史失败: ' + error, 'danger');
            }
        }
        
        // 处理任务响应
        function handleTaskResponse(result) {
            const taskId = result.task_id;
            
            // 添加到本地存储
            taskManager.addTaskToStorage(taskId);
            
            // 显示任务开始提示
            const taskType = result.task_type || 'unknown';
            taskManager.showTaskStarted(taskId, taskType, result.stats?.total || result.total_count || 1);
            
            // 获取任务容器
            const taskContainer = document.getElementById('task-list');
            
            // 如果是第一个任务，清除占位文本
            if (taskContainer.querySelector('.text-muted')) {
                taskContainer.innerHTML = '';
            }
            
            // 开始轮询任务状态
            taskManager.startPolling(taskId,
                // 状态更新回调
                (task) => {
                    console.log(`任务 ${taskId} 状态更新:`, task);
                    taskManager.updateTaskStatusUI(task);
                },
                // 任务完成回调
                (task) => {
                    console.log(`任务 ${taskId} 完成:`, task);
                    taskManager.updateTaskStatusUI(task);
                    taskManager.removeTaskFromStorage(taskId);
                    
                    // 显示完成通知
                    if (task.task_status === 'completed') {
                        showMessage(`任务 ${taskId} 完成! 成功: ${task.success_count}, 失败: ${task.error_count}`, 'success');
                    } else if (task.task_status === 'failed') {
                        showMessage(`任务 ${taskId} 失败: ${task.error_message || '未知错误'}`, 'danger');
                    }
                }
            );
            
            // 立即创建任务状态UI
            taskManager.getTaskStatus(taskId).then(task => {
                if (task) {
                    taskManager.createTaskStatusUI(task, taskContainer);
                }
            }).catch(error => {
                console.error('获取任务状态失败:', error);
            });
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('message-area');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            messageArea.appendChild(alertDiv);
            
            // 自动清除消息
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // 页面加载完成后恢复任务状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始恢复任务状态');
            taskManager.restoreTasksFromStorage();
        });
    </script>
</body>
</html>
