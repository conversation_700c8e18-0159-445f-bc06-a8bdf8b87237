<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>威胁情报网站配置管理 - CTI 威胁情报分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 10px;
        }

        .config-header {
            background: white;
            color: #333;
            padding: 1.2rem 0;
            margin-bottom: 1.5rem;
            border-bottom: 1px solid #e3e6f0;
        }
        
        .site-card {
            background: white;
            border: 1px solid #e3e6f0;
            border-radius: 10px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .site-card:hover {
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
            transform: translateY(-3px);
            border-color: #4e73df;
        }
        
        .site-card.inactive {
            opacity: 0.6;
            background-color: #f8f9fa;
        }
        
        .status-badge {
            font-size: 0.8rem;
        }
        
        .priority-badge {
            font-size: 0.75rem;
            min-width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .category-tab {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-tab.active {
            background-color: #007bff;
            color: white;
        }
        
        .stats-card {
            background: white;
            color: #333;
            border: 1px solid #e3e6f0;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            border-color: #4e73df;
        }
        
        .btn-action {
            margin: 0 2px;
        }
        
        .modal-header {
            background: white;
            color: #333;
            border-bottom: 1px solid #e3e6f0;
        }
        .select-label-wrapper {
            margin-right: 20px; /* 可根据需要调整数值 */
        }

        /* 批量操作相关样式 */
        #batchOperations {
            animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .site-checkbox {
            transform: scale(1.1);
        }

        .site-checkbox:checked {
            background-color: #4e73df;
            border-color: #4e73df;
        }

        #selectedCount {
            font-size: 0.8rem;
            margin-top: 2px;
        }

        .form-check-label {
            cursor: pointer;
        }

        /* 批量操作按钮样式优化 */
        #batchOperations .btn {
            border-width: 1.5px;
            font-weight: 500;
            transition: all 0.2s ease;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            min-height: 48px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        #batchOperations .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        #batchOperations .btn.disabled {
            opacity: 0.6;
            transform: none;
            cursor: not-allowed;
        }

        /* 按钮颜色调整为浅色 */
        .btn-outline-success {
            color: #28a745;
            border-color: #28a745;
        }

        .btn-outline-success:hover {
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-outline-warning {
            color: #ffc107;
            border-color: #ffc107;
        }

        .btn-outline-warning:hover {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }

        .btn-outline-danger {
            color: #dc3545;
            border-color: #dc3545;
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        /* 导入导出添加按钮组样式 */
        .btn-group .btn-lg {
            padding: 0.5rem 1rem;
            font-size: 1rem;
            border-radius: 0.375rem;
        }

        .btn-group .btn-lg + .btn-lg {
            margin-left: 1px;
        }

        /* 统一按钮高度 */
        .btn-lg {
            min-height: 48px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* 每周爬取按钮样式 */
        #weeklyTaskBtn {
            min-width: 120px;
            transition: all 0.3s ease;
        }

        #weeklyTaskBtn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* 批量操作按钮2x2布局样式 */
        #batchOperationsRow .d-flex {
            min-width: 180px;
        }

        #batchOperationsRow .btn-group {
            width: 100%;
        }

        #batchOperationsRow .btn-sm {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            flex: 1;
        }

        /* 功能按钮组等宽样式 */
        .col-md-5 .d-flex {
            gap: 4px !important;
        }

        .col-md-5 .btn-lg {
            font-size: 0.9rem;
            padding: 0.5rem 0.75rem;
            white-space: nowrap;
            text-align: center;
        }

        .col-md-5 .btn-lg i {
            font-size: 0.85rem;
        }
        
        .form-label {
            font-weight: 600;
        }

        .col-md-2.pe-2 {
            padding-right: 0.5rem !important;
        }

        .col-md-5.ps-2 {
            margin-left: -4rem !important;
        }
        
        .tag-input {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.375rem 0.75rem;
            margin: 2px;
            display: inline-block;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .search-box {
            max-width: 300px;
        }
        
        .filter-section {
            background-color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e3e6f0;
        }
    </style>
</head>
<body>
    <!-- 添加导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <i class="bi bi-shield-lock me-2 text-primary"></i>
                <strong>CTI 威胁情报分析系统</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis"><i class="bi bi-search"></i> 分析工具</a>
                    </li>
                    <!-- 每周威胁情报 -->
                    <li class="nav-item">
                        <a class="nav-link" href="/weekly_intel"><i class="bi bi-calendar-week"></i> 每周威胁情报</a>
                    </li>
                    <!-- 爬取网站配置 -->
                    <li class="nav-item">
                        <a class="nav-link active" href="/config_management"><i class="bi bi-gear"></i> 爬取网站配置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- 间距 -->
    <div style="height: 60px;"></div>

    <!-- 页面头部 -->
    <div class="config-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-5">
                    <h1 class="mb-0">
                        <i class="bi bi-gear-fill me-2"></i>
                        威胁网站配置管理
                    </h1>
                    <p class="mb-0 mt-2">管理威胁情报爬虫的数据源配置</p>
                </div>
                <div class="col-md-2 text-end">
                    <!-- 批量操作按钮组 - 2x2布局 -->
                    <div id="batchOperationsRow" style="display: none;">
                        <div class="d-flex flex-column gap-1">
                            <div class="btn-group">
                                <button class="btn btn-outline-success btn-sm" id="batchEnableBtn" onclick="batchToggleStatus('active')" title="批量启用">
                                    <i class="bi bi-play-fill"></i> 启用
                                </button>
                                <button class="btn btn-outline-warning btn-sm" id="batchDisableBtn" onclick="batchToggleStatus('inactive')" title="批量停用">
                                    <i class="bi bi-pause-fill"></i> 停用
                                </button>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-outline-danger btn-sm" id="batchDeleteBtn" onclick="showBatchDeleteModal()" title="批量删除">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" id="batchCancelBtn" onclick="clearSelection()" title="取消选择">
                                    <i class="bi bi-x"></i> 取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-5 text-end">
                    <!-- 导入导出、添加和每周爬取按钮组 -->
                    <div class="d-flex justify-content-end gap-1">
                        <button class="btn btn-outline-info btn-lg flex-fill" onclick="exportToCSV()" title="导出CSV" style="max-width: 100px;">
                            <i class="bi bi-download me-1"></i>导出
                        </button>
                        <button class="btn btn-outline-primary btn-lg flex-fill" onclick="showImportModal()" title="批量导入" style="max-width: 100px;">
                            <i class="bi bi-upload me-1"></i>导入
                        </button>
                        <button class="btn btn-outline-success btn-lg flex-fill" onclick="showAddModal()" title="添加网站" style="max-width: 120px;">
                            <i class="bi bi-plus-circle me-1"></i>添加网站
                        </button>
                        <button class="btn btn-outline-warning btn-lg flex-fill" id="weeklyTaskBtn" onclick="startWeeklyCrawl()" title="启动每周爬取任务" style="max-width: 120px;">
                            <i class="bi bi-calendar-week me-1"></i>每周爬取
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card p-3">
                    <h5 class="mb-1">总网站数</h5>
                    <h3 class="mb-0" id="totalSites">-</h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card p-3">
                    <h5 class="mb-1">活跃网站</h5>
                    <h3 class="mb-0" id="activeSites">-</h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card p-3">
                    <h5 class="mb-1">国际网站</h5>
                    <h3 class="mb-0" id="internationalSites">-</h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card p-3">
                    <h5 class="mb-1">中文网站</h5>
                    <h3 class="mb-0" id="chineseSites">-</h3>
                </div>
            </div>
        </div>

        <!-- 过滤和搜索 -->
        <div class="filter-section">
            <div class="row align-items-center mb-3">
                <div class="col-md-2 pe-2">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        <label class="form-check-label" for="selectAll">
                            <strong>全选</strong>
                        </label>
                    </div>
                    <small class="text-muted" id="selectedCount">已选择: 0</small>
                </div>
                <div class="col-md-5 ps-2">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary category-tab active" data-category="">
                            全部
                        </button>
                        <button type="button" class="btn btn-outline-primary category-tab" data-category="international">
                            国际网站
                        </button>
                        <button type="button" class="btn btn-outline-primary category-tab" data-category="chinese">
                            中文网站
                        </button>
                        <button type="button" class="btn btn-outline-primary category-tab" data-category="crawl_url">
                            爬取URL
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">停用</option>
                        <option value="testing">测试</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="input-group search-box">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索网站...">
                        <button class="btn btn-outline-secondary" type="button" onclick="searchSites()">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 网站列表 -->
        <div id="sitesContainer">
            <div class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载网站配置...</p>
            </div>
        </div>


    </div>

    <!-- 添加/编辑网站模态框 -->
    <div class="modal fade" id="siteModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="siteModalTitle">添加网站</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="siteForm">
                        <input type="hidden" id="siteId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="siteName" class="form-label">网站名称 *</label>
                                    <input type="text" class="form-control" id="siteName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="siteDomain" class="form-label">域名 *</label>
                                    <input type="text" class="form-control" id="siteDomain" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="siteUrl" class="form-label">完整URL *</label>
                            <input type="url" class="form-control" id="siteUrl" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="siteCategory" class="form-label">分类</label>
                                    <select class="form-select" id="siteCategory">
                                        <option value="international">国际网站</option>
                                        <option value="chinese">中文网站</option>
                                        <option value="crawl_url">爬取URL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="siteLanguage" class="form-label">语言</label>
                                    <select class="form-select" id="siteLanguage">
                                        <option value="en">英文</option>
                                        <option value="zh">中文</option>
                                        <option value="mixed">混合</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="siteStatus" class="form-label">状态</label>
                                    <select class="form-select" id="siteStatus">
                                        <option value="active">活跃</option>
                                        <option value="inactive">停用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sitePriority" class="form-label">优先级 (1-10)</label>
                                    <input type="number" class="form-control" id="sitePriority" min="1" max="10" value="5">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="siteDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="siteDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSite()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除这个网站配置吗？此操作不可撤销。</p>
                    <p class="text-muted" id="deleteConfirmText"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量删除确认模态框 -->
    <div class="modal fade" id="batchDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量删除确认</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除选中的 <strong id="batchDeleteCount">0</strong> 个网站配置吗？此操作不可撤销。</p>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>警告：</strong>批量删除操作将永久删除选中的所有网站配置，请谨慎操作！
                    </div>
                    <div id="batchDeleteList" class="mt-3" style="max-height: 200px; overflow-y: auto;">
                        <!-- 将显示要删除的网站列表 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmBatchDelete()">
                        <i class="bi bi-trash"></i> 确认批量删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量导入网站配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>导入说明：</strong>
                        <ul class="mb-0 mt-2">
                            <li>支持CSV格式文件</li>
                            <li>必需字段：name（网站名称）、domain（域名）、url（URL地址）</li>
                            <li>可选字段：description（描述）</li>
                            <li>其他字段（分类、语言、状态等）将自动生成</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="csvFile" class="form-label">选择CSV文件</label>
                        <input type="file" class="form-control" id="csvFile" accept=".csv" onchange="previewCSV()">
                    </div>

                    <div id="csvPreview" style="display: none;">
                        <h6>文件预览：</h6>
                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-sm table-bordered">
                                <thead id="csvHeaders"></thead>
                                <tbody id="csvData"></tbody>
                            </table>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <span id="csvRowCount">0</span> 行数据，
                                <span id="csvValidCount">0</span> 行有效，
                                <span id="csvErrorCount">0</span> 行错误
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="downloadTemplate()">
                        <i class="bi bi-download"></i> 下载模板
                    </button>
                    <button type="button" class="btn btn-primary" id="importBtn" onclick="confirmImport()" disabled>
                        <i class="bi bi-upload"></i> 开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/config_management.js"></script>
</body>
</html>
