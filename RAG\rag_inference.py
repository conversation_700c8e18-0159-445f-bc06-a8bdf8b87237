from datetime import datetime
import os
import argparse
import re
import threading
import urllib
from sentence_transformers import SentenceTransformer
import yaml
from typing import Counter, List
import importlib
import time
import queue
import os
import re
import concurrent.futures
import glob
from tqdm import tqdm
import mysql.connector
from mysql.connector import Error

from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_core.documents import Document

from langchain_community.retrievers import BM25Retriever

from pikerag.utils.config_loader import load_class, load_protocol
from pikerag.knowledge_retrievers import BaseQaRetriever

from pikerag.utils.logger import Logger
from pikerag.workflows.common import GenerationQaData
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader, TextLoader, UnstructuredMarkdownLoader, Docx2txtLoader
from pikerag.utils.data_protocol_utils import load_chunks_from_jsonl
from utils import enrich_cve_data, extract_all_document_iocs, process_analysis_content, web_analyze, clean_extracted_text, remove_memory_dumps_and_registers




class CybersecurityEmbeddings:
    """网络安全专用嵌入模型包装器，支持ATT&CK BERT等专业模型"""
    
    def __init__(self, model_name="basel/ATTACK-BERT", device="cpu", normalize_embeddings=True):
        """
        初始化网络安全嵌入模型
        
        Args:
            model_name: 模型名称，支持:
                - basel/ATTACK-BERT (ATT&CK专用)
                - sentence-transformers/all-MiniLM-L6-v2 (通用)
                - BAAI/bge-base-en-v1.5 (通用多语言)
            device: 运行设备 (cpu/cuda)
            normalize_embeddings: 是否标准化嵌入向量
        """
        self.model_name = model_name
        self.device = device
        self.normalize_embeddings = normalize_embeddings
        
        print(f"正在加载网络安全嵌入模型: {model_name}")
        
        # 首先检查本地模型路径
        local_attack_bert_path = "sentence-transformer-models/ATTACK-BERT"
        local_minilm_path = "sentence-transformer-models/all-MiniLM-L6-v2"
        
        # 检查是否为ATT&CK BERT或其他sentence-transformers模型
        if "ATTACK-BERT" in model_name or "sentence-transformers" in model_name or "/" in model_name:
            # 使用SentenceTransformer直接加载
            try:
                # 如果请求的是 ATT&CK BERT，优先使用本地版本
                if "ATTACK-BERT" in model_name and os.path.exists(local_attack_bert_path):
                    print(f"✓ 发现本地 ATT&CK BERT 模型: {local_attack_bert_path}")
                    # 尝试使用信任本地文件的方式加载
                    try:
                        import torch
                        import torch.serialization
                        
                        # 临时修改 torch.load 行为以允许加载本地可信文件
                        original_load = torch.load
                        original_serialization_load = torch.serialization.load
                        
                        def safe_local_load(*args, **kwargs):
                            # 对本地文件，我们信任其安全性
                            if 'weights_only' in kwargs:
                                kwargs['weights_only'] = False
                            return original_load(*args, **kwargs)
                        
                        # 临时替换 torch.load
                        torch.load = safe_local_load
                        torch.serialization.load = safe_local_load
                        
                        try:
                            self.model = SentenceTransformer(local_attack_bert_path, device=device)
                            print(f"✓ 成功加载本地 ATT&CK BERT 模型 (使用安全绕过)")
                        finally:
                            # 恢复原始函数
                            torch.load = original_load
                            torch.serialization.load = original_serialization_load
                            
                    except Exception as trust_e:
                        print(f"本地安全绕过失败: {trust_e}")
                        # 尝试直接从 HuggingFace 下载最新版本
                        print("尝试从 HuggingFace 下载最新版本...")
                        self.model = SentenceTransformer("basel/ATTACK-BERT", device=device)
                        print(f"✓ 成功从 HuggingFace 下载 ATT&CK BERT 模型")
                        
                # 如果请求的是 MiniLM，优先使用本地版本
                elif "all-MiniLM-L6-v2" in model_name and os.path.exists(local_minilm_path):
                    print(f"✓ 发现本地 MiniLM 模型: {local_minilm_path}")
                    self.model = SentenceTransformer(local_minilm_path, device=device)
                    print(f"✓ 成功加载本地 MiniLM 模型")
                # 如果本地路径不存在，尝试从网络加载
                else:
                    print(f"本地模型未找到，尝试从网络加载: {model_name}")
                    self.model = SentenceTransformer(model_name, device=device)
                    print(f"✓ 成功从网络加载模型: {model_name}")
                
                self._model_type = "sentence_transformer"
            except Exception as e:
                print(f"✗ 加载专业模型失败，回退到本地通用模型: {e}")
                # 回退到本地通用模型
                try:
                    if os.path.exists(local_minilm_path):
                        print(f"使用本地 MiniLM 作为备用模型: {local_minilm_path}")
                        self.model = SentenceTransformer(local_minilm_path, device=device)
                    else:
                        print("使用网络 MiniLM 作为备用模型")
                        self.model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2", device=device)
                    self._model_type = "sentence_transformer"
                except Exception as fallback_e:
                    print(f"备用模型也加载失败: {fallback_e}")
                    raise
        else:
            # 使用HuggingFaceEmbeddings加载其他模型
            self.model = HuggingFaceEmbeddings(
                model_name=model_name,
                model_kwargs={'device': device},
                encode_kwargs={'normalize_embeddings': normalize_embeddings}
            )
            self._model_type = "huggingface"
    
    def embed_documents(self, texts):
        """嵌入文档列表"""
        if self._model_type == "sentence_transformer":
            embeddings = self.model.encode(
                texts, 
                normalize_embeddings=self.normalize_embeddings,
                convert_to_numpy=True
            )
            return embeddings.tolist()
        else:
            return self.model.embed_documents(texts)
    
    def embed_query(self, text):
        """嵌入单个查询"""
        if self._model_type == "sentence_transformer":
            embedding = self.model.encode(
                [text], 
                normalize_embeddings=self.normalize_embeddings,
                convert_to_numpy=True
            )
            return embedding[0].tolist()
        else:
            return self.model.embed_query(text)
    
    def similarity(self, text1, text2):
        """计算两个文本的语义相似度"""
        if self._model_type == "sentence_transformer":
            embeddings = self.model.encode([text1, text2], normalize_embeddings=True)
            from sklearn.metrics.pairwise import cosine_similarity
            return cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        else:
            emb1 = self.embed_query(text1)
            emb2 = self.embed_query(text2)
            import numpy as np
            return np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))


# MySQL 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

# 优化文本预处理和关键词提取功能
def preprocess_text(text):
    """优化版文本预处理，专注于威胁情报关键术语提取"""
    # 转换为小写并处理标点符号
    text = text.lower()
    text = re.sub(r'[^\w\s]', ' ', text)
    
    # 扩展的英文停用词列表
    stop_words = {
        'a', 'an', 'the', 'and', 'or', 'but', 'if', 'than', 'as', 'at', 'by', 'for', 'with', 'about', 
        'to', 'in', 'on', 'of', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 
        'had', 'do', 'does', 'did', 'this', 'that', 'these', 'those', 'from', 'during', 'using',
        'use', 'used', 'uses', 'which', 'who', 'whom', 'whose', 'when', 'where', 'why', 'how',
        'all', 'any', 'both', 'each', 'few', 'more', 'most', 'some', 'such', 'no', 'nor', 'not',
        'only', 'own', 'same', 'so', 'than', 'too', 'very', 'can', 'will', 'just', 'should',
        'now', 'also', 'may', 'would', 'could', 'many', 'much', 'since', 'into'
    }
    
    # 移除停用词
    return ' '.join([word for word in text.split() if word not in stop_words and len(word) > 2])

def extract_keywords(text, top_n=10):
    """增强版关键词提取，优先考虑威胁情报术语、CVE编号、MITRE ATT&CK ID和IOC指标"""
    # 预定义的威胁情报高价值术语词表
    cti_terms = {
        'malware', 'ransomware', 'backdoor', 'trojan', 'worm', 'botnet', 'rootkit', 'spyware',
        'adware', 'keylogger', 'rat', 'payload', 'exploit', 'vulnerability', 'cve', 'apt',
        'attack', 'campaign', 'threat', 'actor', 'group', 'hacker', 'phishing', 'spear', 'ddos',
        'injection', 'xss', 'sql', 'mitm', 'zeroday', 'zero', 'day', 'backdoor', 'c2', 'command',
        'control', 'exfiltration', 'lateral', 'movement', 'persistence', 'privilege', 
        'encryption', 'decryption', 'obfuscation', 'stealer', 'infostealer', 'dropper',
        'loader', 'downloader', 'fileless', 'memory', 'resident', 'sandbox', 'evasion',
        'anti', 'analysis', 'debug', 'forensic', 'domain', 'ip', 'url', 'hash', 'ioc', 'ttp',
        'indicator', 'compromise', 'tactic', 'technique', 'procedure', 'mitre', 'attck',
        'framework', 'killchain', 'kill', 'chain', 'credential', 'dumping', 'mimikatz',
        'beacon', 'cobalt', 'strike', 'metasploit', 'empire', 'lazarus', 'apt29', 'apt28',
        'fancy', 'bear', 'cozy', 'conti', 'ryuk', 'revil', 'darkside', 'fin7', 'emotet',
        'trickbot', 'qakbot', 'fakebat', 'batloader', 'icsid', 'pikabot', 'vidar', 'lumma',
        'redline', 'cobaltstrike', 'sliver', 'netsupport', 'msix', 'msi', 'powershell'
    }
    
    # 定义正则表达式模式
    # 1. CVE编号 (如CVE-2021-44228)
    cve_pattern = r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})'
    
    # 2. MITRE ATT&CK技术ID (如T1566, TA0001)
    mitre_pattern = r'\b(T[0-9]{4}(\.[0-9]{3}){0,2})\b'
    
    # 3. IP地址
    ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
    
    # 4. 域名
    domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
    
    # 5. URL
    url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+'
    
    # 6. 文件哈希 (MD5, SHA1, SHA256)
    md5_pattern = r'\b[a-fA-F0-9]{32}\b'
    sha1_pattern = r'\b[a-fA-F0-9]{40}\b'
    sha256_pattern = r'\b[a-fA-F0-9]{64}\b'
    
    # 处理文本
    processed_text = preprocess_text(text)
    words = processed_text.split()
    
    # 计算词频
    word_counts = Counter(words)
    
    # 首先提取特殊格式的关键词
    special_keywords = []
    
    # 提取CVE编号
    cve_matches = re.findall(cve_pattern, text, re.IGNORECASE)
    special_keywords.extend([f"CVE-{year}-{id_num}" for year, id_num in cve_matches])
    
    # 提取MITRE ATT&CK ID
    mitre_matches = re.findall(mitre_pattern, text)
    special_keywords.extend([match.upper() for match in mitre_matches])
    
    # 提取IOC - IP地址
    ip_matches = re.findall(ip_pattern, text)
    special_keywords.extend(ip_matches[:5])  # 限制IP地址数量
    
    # 提取IOC - 域名
    domain_matches = re.findall(domain_pattern, text)
    # 过滤掉常见的非恶意域名
    common_domains = {'com', 'org', 'net', 'edu', 'gov', 'microsoft.com', 'google.com', 'apple.com'}
    filtered_domains = [d for d in domain_matches if not any(d.endswith(f'.{c}') for c in common_domains)]
    special_keywords.extend(filtered_domains[:5])  # 限制域名数量
    
    # 提取IOC - URL
    url_matches = re.findall(url_pattern, text)
    special_keywords.extend(url_matches[:3])  # 限制URL数量
    
    # 提取IOC - 哈希值
    hash_matches = []
    hash_matches.extend(re.findall(md5_pattern, text))
    hash_matches.extend(re.findall(sha1_pattern, text))
    hash_matches.extend(re.findall(sha256_pattern, text))
    special_keywords.extend(hash_matches[:5])  # 限制哈希值数量
    
    # 去重
    special_keywords = list(dict.fromkeys(special_keywords))
    
    # 如果特殊关键词足够多，直接使用它们
    if len(special_keywords) >= top_n:
        return special_keywords[:top_n]
    
    # 然后提取与威胁情报相关的关键词
    cti_keywords = []
    for word, count in word_counts.most_common():
        if word.lower() in cti_terms and word not in cti_keywords and word not in special_keywords:
            cti_keywords.append(word)
            if len(special_keywords) + len(cti_keywords) >= top_n:
                break
    
    # 合并特殊关键词和CTI关键词
    combined_keywords = special_keywords + cti_keywords
    
    # 如果关键词还是不足，则加入普通高频词
    if len(combined_keywords) < top_n:
        remaining_top_words = [
            word for word, _ in word_counts.most_common(top_n * 2)
            if word not in combined_keywords and len(word) > 3
        ][:top_n - len(combined_keywords)]
        combined_keywords.extend(remaining_top_words)
    
    return combined_keywords[:top_n]

def load_document(file_path):
    """根据文件类型加载文档"""
    
    file_extension = os.path.splitext(file_path)[1].lower()
    file_name = os.path.basename(file_path)  # 获取文件名，不包含路径
    
    # 处理过长或复杂的文件名
    if len(file_name) > 30:
        # 检查是否包含时间戳模式 (常见于自动下载或爬取的文件)
        timestamp_match = re.search(r'_\d{14}\.', file_name)
        if timestamp_match:
            # 提取时间戳之前的部分
            simplified_name = file_name[:timestamp_match.start()]
            if len(simplified_name) > 20:
                simplified_name = simplified_name[:20]
            # 添加文件扩展名
            simplified_name += file_extension
        else:
            # 如果没有时间戳模式，直接截断
            simplified_name = file_name[:20] + "..." + file_extension
    else:
        simplified_name = file_name
    
    try:
        if file_extension == ".pdf":
            loader = PyPDFLoader(file_path)
        elif file_extension == ".txt":
            loader = TextLoader(file_path, encoding="utf-8")
        elif file_extension in [".md", ".markdown"]:
            loader = UnstructuredMarkdownLoader(file_path)
        elif file_extension in [".docx", ".doc"]:
            loader = Docx2txtLoader(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {file_extension}")
            
        documents = loader.load()
        
        # 为文档添加元数据，使用简化后的文件名
        for doc in documents:
            doc.metadata.update({
                "source": simplified_name,
                "title": simplified_name
            })
                
        print(f"从 {file_path} 加载了 {len(documents)} 个文档部分")
        return documents
    
    except Exception as e:
        print(f"加载文档 {file_path} 时出错: {e}")
        return []

def load_html(html_url):
    """使用自己实现的爬取函数，同时返回报告日期"""
    from langchain_core.documents import Document

    # 获取网页内容和报告日期
    extracted_content, report_date, html_content = web_analyze(html_url, timeout=5)

    if not extracted_content:
        print("没有提取任何信息！！！")
        return [], None
    
    # 创建Document对象
    if isinstance(extracted_content, str):
        # 如果返回的是字符串，创建一个Document对象
        document = Document(
            page_content=extracted_content,
            metadata={
                "source": html_url,
                "title": os.path.basename(html_url),
                "report_date": report_date  # 将报告日期添加到元数据
            }
        )
        return [document], report_date
    elif isinstance(extracted_content, list):
        # 如果已经是Document列表，确保每个对象都有正确的元数据
        for doc in extracted_content:
            if hasattr(doc, 'metadata'):
                doc.metadata.update({
                    "source": html_url,
                    "title": os.path.basename(html_url),
                    "report_date": report_date  # 将报告日期添加到元数据
                })
        return extracted_content, report_date
    else:
        # 处理其他类型的返回值
        document = Document(
            page_content=str(extracted_content),
            metadata={
                "source": html_url,
                "title": os.path.basename(html_url),
                "report_date": report_date  # 将报告日期添加到元数据
            }
        )
        return [document], report_date


def load_vector_store(vector_db_path, embedding_model_name, existing_embeddings=None):
    """从向量存储路径加载 Chroma 向量数据库 - 支持ATT&CK BERT等专业模型
    
    Args:
        vector_db_path: 向量数据库路径
        embedding_model_name: 嵌入模型名称
        existing_embeddings: 已存在的嵌入模型实例（可选，避免重复创建）
    """
    print(f"正在从 {vector_db_path} 加载向量数据库...")

    # 如果提供了现有的嵌入模型，直接使用
    if existing_embeddings is not None:
        print("使用传入的嵌入模型实例...")
        embeddings = existing_embeddings
    else:
        print("初始化嵌入模型,较慢...")
        
        # 检查是否为专业网络安全模型
        cybersecurity_models = [
            "basel/ATTACK-BERT",
            "sentence-transformers/all-MiniLM-L6-v2", 
            "BAAI/bge-base-en-v1.5"
        ]
        
        # 本地模型路径映射
        local_model_mapping = {
            "basel/ATTACK-BERT": "sentence-transformer-models/ATTACK-BERT",
            "sentence-transformers/all-MiniLM-L6-v2": "sentence-transformer-models/all-MiniLM-L6-v2"
        }
        
        local_model_path = embedding_model_name

        # 优先尝试使用专业网络安全模型包装器
        if any(model in embedding_model_name for model in cybersecurity_models) or "/" in embedding_model_name:
            print(f"使用网络安全专用嵌入模型: {embedding_model_name}")
            
            # 检查是否有对应的本地模型
            local_path = local_model_mapping.get(embedding_model_name)
            if local_path and os.path.exists(local_path):
                print(f"✓ 发现本地模型: {local_path}")
                embeddings = CybersecurityEmbeddings(
                    model_name=local_path,
                    device='cpu',
                    normalize_embeddings=True
                )
            else:
                embeddings = CybersecurityEmbeddings(
                    model_name=embedding_model_name,
                    device='cpu',
                    normalize_embeddings=True
                )
        # 检查本地模型路径是否存在
        elif os.path.exists(local_model_path):
            print(f"使用本地模型: {local_model_path}")
            embeddings = HuggingFaceEmbeddings(
                model_name=local_model_path,
                model_kwargs={'device': 'cpu'},
                encode_kwargs={'normalize_embeddings': True}
            )
        else:
            print(f"本地模型未找到，从Hugging Face下载: {embedding_model_name}")
            embeddings = HuggingFaceEmbeddings(
                model_name=embedding_model_name,
                model_kwargs={'device': 'cpu'},
                encode_kwargs={'normalize_embeddings': True}
            )

    # 检查向量存储路径是否存在
    if not os.path.exists(vector_db_path):
        raise FileNotFoundError(f"向量数据库路径不存在: {vector_db_path}")
    
    # 加载向量存储
    start_time = time.time()
    vector_store = Chroma(
        persist_directory=vector_db_path,
        embedding_function=embeddings
    )
    load_time = time.time() - start_time
    
    doc_count = vector_store._collection.count()
    print(f"向量数据库加载完成! 耗时: {load_time:.2f}秒，包含 {doc_count} 个文档")
    
    return vector_store


def load_chunks_with_ids(jsonl_chunk_path: str):
    """适配器函数，加载文档并生成ID"""
    documents = load_chunks_from_jsonl(jsonl_chunk_path)
    ids = [doc.metadata.get("chunk_id", str(i)) for i, doc in enumerate(documents)]
    return ids, documents

# 估算当前提示词的token数量(粗略估计)
def estimate_tokens(text):
    # GPT模型大约将4个字符视为1个token
    return len(text) / 4

# 动态调整要包含的chunk数量
def select_chunks_within_token_limit(chunks, base_query, token_limit=8000):
    selected_chunks = []
    current_tokens = estimate_tokens(base_query)
    
    # 确保chunks不为空
    if not chunks:
        return []
    
    # 如果文档块数量少，直接全部使用
    threshold_count = 20  # 可以调整这个阈值
    if len(chunks) <= threshold_count:
        print(f"文档总共只有 {len(chunks)} 个块 (不超过 {threshold_count})，将全部使用")
        selected_indices = []
        for i, chunk in enumerate(chunks):
            chunk_tokens = estimate_tokens(chunk.page_content)
            if current_tokens + chunk_tokens < token_limit:
                selected_chunks.append((i, chunk))
                current_tokens += chunk_tokens
                selected_indices.append(i)
            else:
                print(f"达到token限制，无法添加更多块，已添加 {len(selected_chunks)} 个块")
                break
        
        if len(selected_chunks) == len(chunks):
            print(f"已全部添加 {len(chunks)} 个文档块")
        return selected_chunks
    
    # 1. 首先扫描整个文档，找出包含CVE编号的块
    cve_chunks = []
    for i, chunk in enumerate(chunks):
        # 专门检查是否包含CVE编号
        if re.search(r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})\b', 
                    chunk.page_content, re.IGNORECASE):
            cve_chunks.append((i, chunk))
            if len(cve_chunks) >= 3:  # 限制最多3个CVE块，可以调整
                break
    
    # 2. 添加包含CVE的高价值块
    for i, chunk in cve_chunks:
        chunk_tokens = estimate_tokens(chunk.page_content)
        if current_tokens + chunk_tokens < token_limit:
            selected_chunks.append((i, chunk))
            current_tokens += chunk_tokens
        else:
            break
    
    # 3. 添加开头的chunks (如果还有空间)
    head_count = min(3, len(chunks))
    added_indices = {i for i, _ in selected_chunks}  # 追踪已添加的块索引
    
    for i in range(head_count):
        if i not in added_indices:  # 避免重复添加
            chunk_tokens = estimate_tokens(chunks[i].page_content)
            if current_tokens + chunk_tokens < token_limit:
                selected_chunks.append((i, chunks[i]))
                added_indices.add(i)
                current_tokens += chunk_tokens
            else:
                break
    
    # 4. 添加末尾的chunks
    if len(chunks) > head_count:
        tail_chunks = []
        for i in range(min(3, len(chunks) - head_count)):
            idx = len(chunks) - i - 1
            if idx not in added_indices:  # 避免重复添加
                chunk_tokens = estimate_tokens(chunks[idx].page_content)
                if current_tokens + chunk_tokens < token_limit:
                    tail_chunks.insert(0, (idx, chunks[idx]))
                    added_indices.add(idx)
                    current_tokens += chunk_tokens
                else:
                    break
        selected_chunks.extend(tail_chunks)
    
    # 5. 添加中间部分的高价值chunks(包含IOC或其他关键词)
    middle_chunks = []
    for i in range(head_count, len(chunks)):
        if i not in added_indices:
            if contains_ioc_or_keywords(chunks[i].page_content):
                chunk_tokens = estimate_tokens(chunks[i].page_content)
                if current_tokens + chunk_tokens < token_limit:
                    middle_chunks.append((i, chunks[i]))
                    current_tokens += chunk_tokens
    
    # 按原始顺序排序所有选定的chunks
    all_selected = selected_chunks + middle_chunks
    all_selected.sort(key=lambda x: x[0])
    
    print(f"选择了 {len(all_selected)} 个文档块，包含 {len(cve_chunks)} 个CVE块")
    return all_selected

# 检查文本是否包含IOC或关键词
def contains_ioc_or_keywords(text):
    # IOC模式
    ioc_patterns = [
        r'\b(?:\d{1,3}\.){3}\d{1,3}\b',  # IP
        r'\b[a-fA-F0-9]{32,64}\b',        # 哈希
        r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b',  # 域名
        r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})\b'  # CVE编号 - 新增这一行
    ]
    
    # 检查IOC模式
    for pattern in ioc_patterns:
        if re.search(pattern, text, re.IGNORECASE if "cve" in pattern.lower() else 0):
            return True
    
    # 检查关键威胁情报术语
    keywords = ['malware', 'ransomware', 'backdoor', 'trojan', 'exploit', 
                'vulnerability', 'attack', 'threat', 'campaign', 'apt', 
                'command', 'control', 'c2', 'beacon', 'payload',
                'cve', 'vulnerability', 'vuln']  # 添加这些关键词
    
    for kw in keywords:
        if kw.lower() in text.lower():
            return True
    
    return False


class AnalysisDB:
    """数据库连接和操作类"""
    def __init__(self, db_config=DB_CONFIG):
        self.conn = mysql.connector.connect(**db_config)
        self.cursor = self.conn.cursor(dictionary=True)
        
    def commit(self):
        self.conn.commit()
        
    def close(self):
        if self.conn.is_connected():
            self.cursor.close()
            self.conn.close()

    def ensure_tables_exist(self):
        """确保必要的数据库表存在"""
        try:
            # 检查crawled_data表是否存在
            self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS crawled_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                query TEXT NOT NULL,
                title TEXT,
                link VARCHAR(1024) NOT NULL,
                snippet TEXT,
                html_content LONGTEXT,
                extracted_text LONGTEXT,
                crawl_status TINYINT DEFAULT 0,
                crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_link (link(768))
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            # 检查rag_analysis表是否存在
            self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS rag_analysis (
                id INT AUTO_INCREMENT PRIMARY KEY,
                crawled_data_id INT NOT NULL,
                analysis_type VARCHAR(50) NOT NULL,
                analysis_content TEXT,
                references_text TEXT,
                rationale TEXT,
                analysis_status TINYINT DEFAULT 0,
                analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                report_time TIMESTAMP NULL,
                input_source VARCHAR(1024) NULL,
                input_type ENUM('url', 'file', 'text', 'database') NULL,
                INDEX (crawled_data_id),
                INDEX (analysis_type),
                UNIQUE INDEX (crawled_data_id, analysis_type),
                INDEX (input_type),
                INDEX (analysis_time)
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            """)
            
            self.conn.commit()
            return True
        except Error as e:
            print(f"创建表结构时出错: {e}")
            return False


class ChromaRetriever(BaseQaRetriever):
    """使用Chroma向量数据库进行检索的检索器 - 支持ATT&CK BERT等专业模型"""
    
    def __init__(self, retriever_config, log_dir="outputs", main_logger=None):
        super().__init__(retriever_config, log_dir, main_logger)
        self.vector_db_path = retriever_config.get("vector_db_path")
        self.top_k = retriever_config.get("top_k", 3)
        self.embedding_model = retriever_config.get("embedding_model", "basel/ATTACK-BERT")
        
        # 确保有日志记录器
        if not hasattr(self, 'logger'):
            self.logger = main_logger or Logger(name="chroma_retriever", dump_folder=log_dir)

        # 检查是否为专业网络安全模型
        cybersecurity_models = [
            "basel/ATTACK-BERT",
            "sentence-transformers/all-MiniLM-L6-v2", 
            "BAAI/bge-base-en-v1.5"
        ]
        
        # 本地模型路径映射
        local_model_mapping = {
            "basel/ATTACK-BERT": "sentence-transformer-models/ATTACK-BERT",
            "sentence-transformers/all-MiniLM-L6-v2": "sentence-transformer-models/all-MiniLM-L6-v2"
        }
        
        local_model_path = self.embedding_model

        # 优先使用专业网络安全模型
        if any(model in self.embedding_model for model in cybersecurity_models) or "/" in self.embedding_model:
            print(f"ChromaRetriever: 使用网络安全专用嵌入模型: {self.embedding_model}")
            
            # 检查是否有对应的本地模型
            local_path = local_model_mapping.get(self.embedding_model)
            if local_path and os.path.exists(local_path):
                print(f"ChromaRetriever: ✓ 发现本地模型: {local_path}")
                self.embeddings = CybersecurityEmbeddings(
                    model_name=local_path,
                    device='cpu',
                    normalize_embeddings=True
                )
            else:
                self.embeddings = CybersecurityEmbeddings(
                    model_name=self.embedding_model,
                    device='cpu',
                    normalize_embeddings=True
                )
        elif os.path.exists(local_model_path):
            print(f"ChromaRetriever: 使用本地模型: {local_model_path}")
            self.embeddings = HuggingFaceEmbeddings(
                model_name=local_model_path,
                model_kwargs={'device': 'cpu', 'local_files_only': True},
                encode_kwargs={'normalize_embeddings': True}
            )
        else:
            print(f"ChromaRetriever: 本地模型未找到，尝试从Hugging Face下载: {self.embedding_model}")
            self.embeddings = HuggingFaceEmbeddings(
                model_name=self.embedding_model, 
                model_kwargs={'device': 'cpu'},
                encode_kwargs={'normalize_embeddings': True}
            )
        
        print(f"正在加载向量数据库: {self.vector_db_path}")
        self._vector_store = self._load_vector_store_with_existing_embeddings()
        self.logger.info(f"成功加载Chroma向量数据库: {self.vector_db_path}")

    def _load_vector_store_with_existing_embeddings(self):
        """使用已有的嵌入模型加载向量存储，避免重复初始化"""
        vector_db_path = self.vector_db_path
        
        print(f"正在从 {vector_db_path} 加载向量数据库...")
        print("使用已初始化的嵌入模型...")

        # 检查向量存储路径是否存在
        if not os.path.exists(vector_db_path):
            raise FileNotFoundError(f"向量数据库路径不存在: {vector_db_path}")
        
        # 直接使用已经初始化的嵌入模型加载向量存储
        start_time = time.time()
        vector_store = Chroma(
            persist_directory=vector_db_path,
            embedding_function=self.embeddings  # 使用已有的嵌入模型
        )
        load_time = time.time() - start_time
        
        doc_count = vector_store._collection.count()
        print(f"向量数据库加载完成! 耗时: {load_time:.2f}秒，包含 {doc_count} 个文档")
        
        return vector_store

    def retrieve_contents_by_query(self, query: str, retrieve_id: str="", **kwargs) -> List[str]:
        """从向量数据库检索内容"""
        if not query or not query.strip():
            self.logger.warning(f"收到空查询")
            return []
            
        if not hasattr(self, "_vector_store") or not self._vector_store:
            self.logger.error("向量存储未初始化")
            return []
        
        try:
            # 设置检索参数
            top_k = kwargs.get("top_k", self.top_k)
            score_threshold = kwargs.get("score_threshold", 0.2)  # 使用较低的阈值
            
            self.logger.debug(f"检索查询: '{query[:50]}...', top_k={top_k}, threshold={score_threshold}")
            
            # 使用MMR方法检索，增加多样性
            search_kwargs = {"k": top_k, "score_threshold": score_threshold}
            results = self._vector_store.max_marginal_relevance_search(
                query, **search_kwargs
            )
            
            # 提取内容
            contents = []
            for doc in results:
                if hasattr(doc, "page_content") and doc.page_content:
                    contents.append(doc.page_content)
            
            # 记录检索结果
            self.logger.debug(f"检索到 {len(contents)} 个结果")
            
            if len(contents) == 0:
                self.logger.info("尝试直接相似度搜索，不使用MMR...")
                direct_results = self._vector_store.similarity_search(
                    query, k=top_k, score_threshold=score_threshold
                )
                direct_contents = [doc.page_content for doc in direct_results if hasattr(doc, "page_content")]
                self.logger.info(f"直接搜索返回 {len(direct_contents)} 个结果")
                contents = direct_contents
            
            return contents
            
        except Exception as e:
            self.logger.error(f"检索过程中发生错误: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return []
    
    @property
    def vector_store(self):
        """返回已加载的向量存储"""
        return self._vector_store


class SimpleRagInference:
    def __init__(self, config_path):
        # 加载配置
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
            
        # 确保输出目录存在
        os.makedirs(self.config.get("output_dir", "outputs"), exist_ok=True)
            
        # 初始化组件
        self._init_retriever()
        self._init_protocol()
        self._init_llm_client()

         # 添加数据库连接
        try:
            self.db = AnalysisDB(DB_CONFIG)
            self.db.ensure_tables_exist()
            print("数据库连接和表结构初始化成功")
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            self.db = None

    def _reconnect_to_database(self):
        """重新连接到数据库"""
        if self.db is None:
            try:
                self.db = AnalysisDB(DB_CONFIG)
                self.db.ensure_tables_exist()
                print("数据库重新连接成功")
            except Exception as e:
                print(f"数据库重新连接失败: {e}")

    def _init_retriever(self):
        """初始化检索器"""
        retriever_config = self.config["retriever"]
        
        # 检查是否使用我们定义的 ChromaRetriever
        if (retriever_config.get("class_name") == "ChromaRetriever" and 
            retriever_config.get("module_path").endswith("rag_inference")):
            self.retriever = ChromaRetriever(
                retriever_config=retriever_config["args"],
                log_dir=self.config.get("output_dir", "outputs"),
                main_logger=Logger(name="retriever", dump_folder=self.config.get("output_dir", "outputs"))
            )
        else:
            # 使用配置中指定的其他检索器
            retriever_class = load_class(
                module_path=retriever_config["module_path"],
                class_name=retriever_config["class_name"],
                base_class=BaseQaRetriever
            )
            self.retriever = retriever_class(
                retriever_config=retriever_config["args"],
                log_dir=self.config.get("output_dir", "outputs"),
                main_logger=Logger(name="retriever", dump_folder=self.config.get("output_dir", "outputs"))
            )
        
    def _init_protocol(self):
        """初始化提示词协议"""
        protocol_config = self.config["prompt"]
        self.protocol = load_protocol(
            module_path=protocol_config["module_path"],
            protocol_name=protocol_config["attr_name"],
            partial_values=protocol_config.get("template_partial", {}),
        )
        
    def _init_llm_client(self):
        """初始化LLM客户端"""
        llm_config = self.config["llm_client"]
        client_module = importlib.import_module(llm_config["module_path"])
        client_class = getattr(client_module, llm_config["class_name"])
        
        
        args = llm_config.get("args", {})
        
        self.llm = client_class(
            location=None,
            auto_dump=False,
            logger=Logger(name="llm", dump_folder=self.config.get("output_dir", "outputs")),
            llm_config=llm_config["llm_config"],
            **args,  # 同时传递其他参数
        )
        self.llm_config = llm_config["llm_config"]


    # 根据重要性优先级选择和限制引用条目
    def prioritize_references(self, filtered_external_info, document_text=None, chunks=None, max_total=20, max_cve=5, max_ioc=3):
        """
        根据重要性优先级选择和限制引用条目，优先考虑文章中出现的CVE和IOC
        
        参数:
            filtered_external_info: 已过滤的外部知识列表
            document_text: 原始文档内容，用于提取IOC
            chunks: 文档分块，用于利用现有的_retrieve_using_iocs函数
            max_total: 最大总引用数量，默认15
            max_cve: CVE引用的最大数量，默认5
            max_ioc: IOC引用的最大数量，默认3
        
        返回:
            优先级排序后的引用列表
        """
        if not filtered_external_info:
            print("没有可用的外部知识引用")
            return []
        
        # 从文档中提取所有IOC (CVE编号和其他IOC)
        doc_cves = []
        doc_iocs = []
        
        
        # 如果提供了文档块，使用_retrieve_using_iocs函数提取IOC
        if chunks:
            all_iocs = []
            # 调用已有的IOC提取函数，但不进行检索，只提取识别信息
            for chunk in chunks:
                # 从每个文档块中提取IOC
                for ioc_type, pattern in {
                    'ip': r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
                    'hash': r'\b[a-fA-F0-9]{32}\b|\b[a-fA-F0-9]{40}\b|\b[a-fA-F0-9]{64}\b',
                    'domain': r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}\b',
                    'cve': r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})\b'
                }.items():
                    if ioc_type == 'cve':
                        # 特殊处理CVE
                        matches = re.findall(pattern, chunk.page_content, re.IGNORECASE)
                        for year, id_num in matches:
                            normalized_cve = f"CVE-{year}-{id_num}".upper()
                            if normalized_cve not in doc_cves:
                                doc_cves.append(normalized_cve)
                    else:
                        # 处理其他IOC
                        matches = re.findall(pattern, chunk.page_content)
                        for match in matches:
                            # 排除常见域名和私有IP地址
                            if ioc_type == 'domain' and any(common in match.lower() for common in 
                                ['google.com', 'microsoft.com', 'apple.com', 'amazon.com']):
                                continue
                            if ioc_type == 'ip' and (match.startswith('127.') or match.startswith('192.168.') 
                                                or match.startswith('10.')):
                                continue
                            
                            if match not in doc_iocs:
                                doc_iocs.append(match)
        # 如果只提供了文档文本，直接使用正则表达式提取
        elif document_text:
            # 提取文档中的CVE编号
            cve_matches = re.findall(r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})', document_text, re.IGNORECASE)
            doc_cves = [f"CVE-{year}-{id_num}".upper() for year, id_num in cve_matches]
            
            # 提取文档中的IP地址
            ip_matches = re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', document_text)
            doc_iocs.extend(ip_matches)
            
            # 提取文档中的哈希值
            hash_matches = re.findall(r'\b[a-fA-F0-9]{32,64}\b', document_text)
            doc_iocs.extend(hash_matches)
            
            # 提取文档中的域名
            domain_matches = re.findall(r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b', document_text)
            doc_iocs.extend(domain_matches)
        
        # 打印找到的IOC信息
        if doc_cves or doc_iocs:
            print(f"从文档中提取了 {len(doc_cves)} 个CVE编号和 {len(doc_iocs)} 个其他IOC")
            print(f"文档CVE列表: {doc_cves}")  # 打印所有CVE ID以便调试
        
        
        # 分类引用
        cve_in_doc = []    # 文档中出现的CVE相关引用
        cve_not_in_doc = [] # 文档中未出现的CVE相关引用
        ioc_in_doc = []     # 文档中出现的IOC相关引用
        ioc_not_in_doc = [] # 文档中未出现的IOC相关引用
        other_info = []     # 其他一般引用
        
        # 1. 将引用分类到不同类别
        for i, info in enumerate(filtered_external_info):
            
            # 检测CVE
            cve_id, found_match = self._detect_cve_in_references(info, doc_cves)
            
            if cve_id:  # 如果检测到CVE
                if found_match:
                    # 匹配到文档中的CVE
                    cve_in_doc.append((info, cve_id))

                else:
                    # 检测到CVE但未匹配到文档中的
                    if doc_cves:
                        # print(f"  × 未匹配的CVE: {cve_id}，文档中有: {doc_cves}")
                        cve_not_in_doc.append(info)
                    # print(f"    - 添加到其他CVE引用")
                continue

                    
            # IOC引用检查
            is_ioc = False
            matched_ioc = None
            
            # 检查IP地址
            ip_matches = re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', info)
            if ip_matches:
                is_ioc = True
                matched_ioc = ip_matches[0]
            
            # 检查哈希值
            if not is_ioc:
                hash_matches = re.findall(r'\b[a-fA-F0-9]{32,64}\b', info)
                if hash_matches:
                    is_ioc = True
                    matched_ioc = hash_matches[0]
            
            # 检查域名
            if not is_ioc:
                domain_matches = re.findall(r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b', info)
                if domain_matches:
                    is_ioc = True
                    matched_ioc = domain_matches[0]
            
            if is_ioc:
                # 检查这个IOC是否出现在原始文档中
                if doc_iocs and matched_ioc in doc_iocs:
                    ioc_in_doc.append((info, matched_ioc))
                else:
                    ioc_not_in_doc.append(info)
            else:
                # 其他一般引用
                other_info.append(info)
        
        # 2. 对文档中出现的CVE和IOC进行内部排序，确保最重要的在前面
        if cve_in_doc:
            # 按CVE ID排序，确保最新的CVE优先（通常年份和编号越大越新）
            cve_in_doc.sort(key=lambda x: x[1], reverse=True)
            cve_in_doc = [item[0] for item in cve_in_doc]  # 仅保留引用内容
        
        if ioc_in_doc:
            # 这里可以添加更复杂的IOC重要性排序逻辑
            # 目前简单地保留原始顺序
            ioc_in_doc = [item[0] for item in ioc_in_doc]  # 仅保留引用内容
        
        # 3. 动态分配配额，确保文档中出现的CVE和IOC有最高优先级
        # 首先确保分配给文档中出现的CVE
        doc_cve_count = len(cve_in_doc)
        actual_max_doc_cve = min(doc_cve_count, max_cve)
        
        # 然后分配给文档外的CVE
        remaining_cve_slots = max_cve - actual_max_doc_cve
        actual_max_other_cve = min(len(cve_not_in_doc), remaining_cve_slots)
        
        # 分配给文档中出现的IOC
        remaining_slots_after_cve = max_total - (actual_max_doc_cve + actual_max_other_cve)
        doc_ioc_count = len(ioc_in_doc)
        actual_max_doc_ioc = min(doc_ioc_count, max_ioc, remaining_slots_after_cve)
        
        # 分配给文档外的IOC
        remaining_ioc_slots = min(max_ioc - actual_max_doc_ioc, 
                                remaining_slots_after_cve - actual_max_doc_ioc)
        actual_max_other_ioc = min(len(ioc_not_in_doc), remaining_ioc_slots)
        
        # 剩余空间用于其他引用
        remaining_slots = max_total - (actual_max_doc_cve + actual_max_other_cve + 
                                    actual_max_doc_ioc + actual_max_other_ioc)
        actual_max_other = min(len(other_info), remaining_slots)
        
        # 4. 合并最终引用列表，按优先级排列
        final_references = (cve_in_doc[:actual_max_doc_cve] +  # 文档中的CVE最高优先级
                        cve_not_in_doc[:actual_max_other_cve] +  # 其他CVE次高优先级
                        ioc_in_doc[:actual_max_doc_ioc] +  # 文档中的IOC第三优先级
                        ioc_not_in_doc[:actual_max_other_ioc] +  # 其他IOC第四优先级
                        other_info[:actual_max_other])  # 一般引用最低优先级
        
        # 5. 记录详细日志
        reference_stats = (
            f"引用优先级: "
            f"{actual_max_doc_cve}个文档中的CVE, "
            f"{actual_max_other_cve}个其他CVE, "
            f"{actual_max_doc_ioc}个文档中的IOC, "
            f"{actual_max_other_ioc}个其他IOC, "
            f"{actual_max_other}个一般引用"
        )
        print(reference_stats)
        
        return final_references

    def answer_question(self, question, custom_references=None, Iocs=None):
        """回答单个问题"""
        try:
            # 创建一个简单的问题对象
            qa_data = GenerationQaData(question=question)
            
            # 使用自定义参考资料或检索相关内容
            if custom_references is not None:
                reference_chunks = custom_references
            else:
                reference_chunks = self.retriever.retrieve_contents(qa_data)
            
            # 构建参数 - 注意：确保使用正确的参数名称
            if not reference_chunks:
                instructions = "我没有找到与此问题直接相关的参考资料。请用中文基于你的专业知识回答。"
                references_str = "" 
            else:
                instructions = """请基于提供的参考资料，用中文回答有关网络威胁情报的问题。

**分析要求：**
- 只基于参考资料中明确提及的内容进行分析
- 如果参考资料中没有相关信息，明确说明"参考资料中没有提供这方面的信息"
- 不要基于一般知识补充或推测未在参考资料中明确提及的信息

**参考资料可能包含：**
- 威胁行为者和APT组织的详细信息和归因
- 恶意软件、漏洞和攻击向量的技术细节
- 攻击战术、技术和程序(TTPs)
- 被攻击的行业和组织
- 威胁指标(IOCs)

**重要提醒：**
- IOC部分只包含技术指标（IP、域名、URL、文件哈希等），CVE编号归入"相关漏洞"
- 遇到"Contact <EMAIL>"等联系方式，不要作为IOC处理
"""
                references_str = "\n\n".join([f"参考 {i+1}:\n{ref}" for i, ref in enumerate(reference_chunks)])
            
            # 如果提供了IOC信息，添加IOC完善指令
            if Iocs and len(Iocs) > 0:
                total_iocs = sum(len(ioc_list) for ioc_list in Iocs.values())
                ioc_instruction = f"\n\n**重要：IOC完善任务**\n基于文档内容和上下文，请完善以下通过正则表达式提取的 {total_iocs} 个IOC指标。对于每个IOC，请结合文档信息提供准确的描述（如文件类型、用途、关联恶意软件等）：\n"
                
                # 分类显示已提取的IOC
                for ioc_type, ioc_list in Iocs.items():
                    if ioc_list:
                        ioc_instruction += f"\n**{ioc_type}类型 ({len(ioc_list)}个):**\n"
                        for i, ioc in enumerate(ioc_list[:10], 1):  # 限制每种类型最多10个
                            ioc_instruction += f"{i}. {ioc}\n"
                        if len(ioc_list) > 10:
                            ioc_instruction += f"... 还有 {len(ioc_list) - 10} 个同类型IOC\n"
                
                ioc_instruction += "\n**威胁指标(IOCs)输出要求：**\n"
                ioc_instruction += "1. 必须包含完整的'# 相关威胁指标(IOCs)'部分\n"
                ioc_instruction += "2. 对每个IOC提供准确描述，严格按以下格式：\n"
                ioc_instruction += "   - **IP地址**：[IP地址] - [具体用途，如C2服务器/恶意下载源/攻击目标等]\n"
                ioc_instruction += "   - **域名**：[域名] - [功能描述，如C2域名/钓鱼站点/恶意载荷托管等]\n"
                ioc_instruction += "   - **URL**：[完整URL] - [用途说明，如恶意载荷下载链接/C2通信端点等]\n"
                ioc_instruction += "   - **文件路径**：[完整路径] - [文件性质，如恶意可执行文件/持久化脚本/配置文件等]\n"
                ioc_instruction += "   - **文件哈希**：[哈希类型]:[哈希值] - [文件详情，如'恶意ISO镜像'/'后门DLL'/'加密载荷'等]\n"
                ioc_instruction += "   - **注册表项**：[注册表路径] - [作用说明，如持久化机制/配置存储等]\n"
                ioc_instruction += "   - **进程名**：[进程名称] - [恶意行为描述]\n"
                ioc_instruction += "3. **补充要求**：\n"
                ioc_instruction += "   - 主动识别文档中未被自动提取的IOC信息并补充\n"
                ioc_instruction += "   - 每个IOC描述必须基于文档内容，避免推测\n"
                ioc_instruction += "   - 如遇到\"Contact <EMAIL> for more details\"等提示，说明不是文章中的IOC,是分析员的联系方式，不要放到IOC部分\n"
                ioc_instruction += "4. **严格分类**：\n"
                ioc_instruction += "   - IOC部分只包含：IP、域名、URL、文件哈希、文件路径、注册表、进程等技术指标\n"
                ioc_instruction += "   - CVE编号必须放在'# 相关漏洞'部分，不得混入IOC部分\n"
                ioc_instruction += "   - 攻击技术和战术放在相应的ATT&CK部分\n"
                instructions += ioc_instruction
                
            # 检查模板可能需要的参数
            template_params = {}
            
            # 根据模板所需参数添加
            if hasattr(self.protocol, "template") and hasattr(self.protocol.template, "input_variables"):
                required_vars = self.protocol.template.input_variables
                template_params = {
                    "content": question,
                    "references_str": references_str if "references_str" in required_vars else None,
                    "instructions": instructions if "instructions" in required_vars else None
                }
                # 移除None值
                template_params = {k: v for k, v in template_params.items() if v is not None}
            else:
                # 传递所有可能的参数
                template_params = {
                    "content": question,
                    "references_str": references_str,
                    "instructions": instructions
                }
                
                
            # 生成提示词
            messages = self.protocol.process_input(**template_params)
            
            # 尝试不同的调用方式
            try:
                if hasattr(self.llm, "generate_content_with_messages"):
                    response = self.llm.generate_content_with_messages(messages, **self.llm_config)
                elif hasattr(self.llm, "chat"):
                    response = self.llm.chat(messages, **self.llm_config)
                elif hasattr(self.llm, "generate"):
                    response = self.llm.generate(messages, **self.llm_config)
                else:
                    response = self.llm(messages, **self.llm_config)
                
            except Exception as e:
                print(f"LLM调用出错: {e}")
                raise
            
            # 解析输出
            try:
                output = self.protocol.parse_output(response)
            except Exception as e:
                print(f"解析输出出错: {e}")
                output = {"answer": f"解析模型响应时出错: {e}"}
            
            # 获取答案并根据检索结果添加前缀
            answer = output.get("answer", "")
            if not answer:
                # 如果没有answer字段，尝试直接使用原始响应
                answer = str(response)
            
            # 保存真正使用的引用列表
            actual_references = custom_references if custom_references else reference_chunks
            
            return {
                "question": question,
                "answer": answer,
                "references": actual_references if actual_references else [],
                "raw_response": response
            }
        except Exception as e:
            print(f"处理问题时出错: {e}")
            return {
                "question": question,
                "answer": f"处理您的问题时遇到了错误，请稍后再试。详细错误: {e}",
                "references": [],
                "cve_details": [],
                "raw_response": ""
            }
        

    def optimized_keyword_retrieval(self, keywords, doc_summary, max_pairs=3, max_keywords=4, timeout=10):
        """优化的关键词检索函数 - 使用并行处理和超时机制"""
        external_info = []
        start_time = time.time()
        
        # 1. 限制关键词数量
        if len(keywords) > max_keywords:
            keywords = keywords[:max_keywords]
        
        # 2. 创建关键词对 (最多max_pairs个)
        keyword_pairs = []
        if len(keywords) >= 2:
            for i in range(len(keywords) - 1):
                for j in range(i + 1, len(keywords)):
                    keyword_pairs.append(f"{keywords[i]} {keywords[j]}")
                    if len(keyword_pairs) >= max_pairs:
                        break
                if len(keyword_pairs) >= max_pairs:
                    break
        
        # 3. 准备所有检索查询 - 包括关键词对和单个关键词
        all_queries = keyword_pairs.copy()
        all_queries.extend([k for k in keywords if len(k) >= 2])
        
        # 4. 打印检索计划
        print(f"执行关键词检索: {len(all_queries)} 个查询 (并行处理)")
        
        # 5. 并行执行所有检索
        results_dict = {}
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            # 提交所有检索任务
            future_to_query = {
                executor.submit(self._retrieval_with_timeout, query, 
                            2 if query in keyword_pairs else 1, 
                            timeout): query
                for query in all_queries
            }
            
            # 使用tqdm显示进度
            for future in tqdm(concurrent.futures.as_completed(future_to_query), 
                            total=len(future_to_query), 
                            desc="关键词检索"):
                query = future_to_query[future]
                try:
                    results = future.result()
                    if results:
                        results_dict[query] = results
                        external_info.extend(results)
                except Exception as e:
                    print(f"检索 '{query}' 失败: {e}")
        
        # 去重
        external_info = list(set(external_info))
        
        # 打印结果统计
        elapsed = time.time() - start_time
        print(f"关键词检索完成，耗时: {elapsed:.2f}秒，获取 {len(external_info)} 个结果")
        
        return external_info


    def _detect_cve_in_references(self, info, doc_cves):
        """检测参考信息中的CVE编号并与文档中的CVE进行匹配"""
        
        # 简化调试：打印信息开头
        info_preview = info[:50] + "..." if len(info) > 50 else info
        
        # 1. 尝试精确匹配 - 首先看引用是否包含文档中的CVE
        for doc_cve in doc_cves:
            if doc_cve in info:
                print(f"  直接匹配成功! 文档CVE {doc_cve} 在引用中")
                return doc_cve, True
        
        # 2. 尝试匹配 CVE-YYYY-NNNN(PUBLISHED): 格式
        published_format = re.search(r'(CVE-\d{4}-\d{1,7})\(PUBLISHED\):', info)
        if published_format:
            cve_id = published_format.group(1).upper()
            print(f"  检测到特殊格式CVE: {cve_id}(PUBLISHED):")
            return self._compare_cve_with_doc(cve_id, doc_cves)
        
        # 3. 尝试匹配标准 CVE 格式
        standard_format = re.search(r'CVE-\d{4}-\d{1,7}', info)
        if standard_format:
            cve_id = standard_format.group().upper()
            print(f"  检测到标准格式CVE: {cve_id}")
            return self._compare_cve_with_doc(cve_id, doc_cves)
        
        # 4. 尝试匹配任何包含年份和CVE ID的格式
        cve_loose = re.search(r'CVE[\-\s]*(\d{4})[\-\s]*(\d{1,7})', info, re.IGNORECASE)
        if cve_loose:
            year, id_num = cve_loose.groups()
            normalized_cve = f"CVE-{year}-{id_num}".upper()
            print(f"  检测到宽松格式CVE: {normalized_cve}")
            return self._compare_cve_with_doc(normalized_cve, doc_cves)
        
        return None, False

    def _compare_cve_with_doc(self, cve_id, doc_cves):
        """比较CVE ID与文档中的CVE"""
        if not cve_id or not doc_cves:
            return cve_id, False
        
        # 首先尝试直接完全匹配
        if cve_id in doc_cves:
            print(f"CVE完全匹配: {cve_id} 在文档中")
            return cve_id, True
        
        # 提取CVE的核心部分 - 年份和编号
        cve_parts = cve_id.split("-")
        if len(cve_parts) != 3:
            return cve_id, False
        
        cve_year, cve_num = cve_parts[1], cve_parts[2]
        
        # 与文档中的CVE进行比较
        for doc_cve in doc_cves:
            # 如果是完全相同的CVE ID
            if doc_cve == cve_id:
                print(f"CVE完全匹配: {doc_cve} 与 {cve_id}")
                return cve_id, True
                
            doc_parts = doc_cve.split("-")
            if len(doc_parts) == 3:
                doc_year, doc_num = doc_parts[1], doc_parts[2]
                # 比较年份和编号是否一致，忽略前缀零
                if doc_year.lstrip("0") == cve_year.lstrip("0") and doc_num.lstrip("0") == cve_num.lstrip("0"):
                    print(f"CVE核心匹配成功: {doc_cve} 与 {cve_id}")
                    return cve_id, True
        
        return cve_id, False

    def _retrieval_with_timeout(self, query, top_k=2, timeout=10):
        """带超时机制的检索函数"""
        import threading
        
        result_queue = queue.Queue()
        
        def perform_retrieval():
            try:
                # 在嵌套函数内部先声明results变量
                # CVE 特殊处理 - 生成多种检索变体并增强语义
                if query.startswith("CVE-"):
                    print(f"检索CVE: {query}")
                    
                    # 尝试多种变体进行检索
                    variants = [query]  # 原始格式 CVE-2022-34962
                    
                    # 提取年份和编号
                    parts = query.split("-")
                    if len(parts) == 3:
                        year, id_num = parts[1], parts[2]
                        
                        # 变体1-4: 不同格式的CVE表示
                        variants.append(f"CVE{year}{id_num}")
                        variants.append(f"CVE {year} {id_num}")
                        variants.append(f"{year}-{id_num}")
                        variants.append(f"{year}{id_num}")
                        
                        # 增强语义查询 - 添加更多相关上下文
                        variants.append(f"漏洞 {query}")
                        variants.append(f"vulnerability {query}")
                        variants.append(f"security issue {query}")
                        variants.append(f"CVE {year}年 {id_num}号漏洞")
                    
                    print(f"为CVE {query} 生成 {len(variants)} 个检索变体")
                    
                    # 结合精确检索和向量检索
                    # 1. 先尝试精确关键词匹配
                    exact_results = []
                    for variant in variants[:5]:  # 使用前5个基本变体进行精确匹配
                        # 使用精确匹配模式
                        exact_query = f'"{variant}"'  # 添加引号强制精确匹配
                        variant_results = self.retriever.retrieve_contents(
                            GenerationQaData(question=exact_query), 
                            top_k=1
                        )
                        if variant_results:  # 确保结果不为None
                            exact_results.extend(variant_results)
                    
                    # 2. 然后使用语义增强的向量检索
                    semantic_results = []
                    for variant in variants[5:]:  # 使用后面的语义增强变体
                        variant_results = self.retriever.retrieve_contents(
                            GenerationQaData(question=variant), 
                            top_k=max(1, top_k // 2)
                        )
                        if variant_results:  # 确保结果不为None
                            semantic_results.extend(variant_results)
                    
                    # 合并结果，优先保留精确匹配结果
                    results = exact_results + semantic_results
                    
                    # 去重
                    seen = set()
                    unique_results = []
                    for item in results:
                        if item and item not in seen:
                            seen.add(item)
                            unique_results.append(item)
                    
                    results = unique_results
                    
                    # 打印找到的结果数
                    print(f"CVE检索找到 {len(results)} 条结果")
                    
                    # 在结果中查找是否包含原始CVE编号 - 使用更灵活的匹配方式
                    found_exact = False
                    for idx, result in enumerate(results):
                        if result:  # 确保结果不为None或空字符串
                            # 1. 检查包含在文本中的CVE (允许连接的情况，如CVE-2020-14144(PUBLISHED))
                            if re.search(r'CVE\-\d{4}\-\d{1,7}(?!\-)', result):  # 确保不是连字符后跟着数字的情况
                                # 从结果中提取所有CVE编号
                                cve_in_result = re.findall(r'CVE\-\d{4}\-\d{1,7}', result)
                                # 提取查询中的CVE编号
                                query_cve = re.search(r'(CVE\-\d{4}\-\d{1,7})', query)
                                if query_cve and any(query_cve.group(1) in cve for cve in cve_in_result):
                                    found_exact = True
                                    print(f"找到精确匹配CVE (格式1): {query}")
                                    print(f"匹配结果片段: {result[:100]}...")  # 显示匹配到的前100个字符
                                    break
                            
                            # 2. 规范化比较 - 提取年份和ID进行比较
                            query_parts = query.split("-")
                            if len(query_parts) == 3 and query_parts[0].upper() == "CVE":
                                query_year, query_id = query_parts[1], query_parts[2]
                                # 查找结果中的任何CVE年份-ID组合
                                cve_pattern = re.compile(r'CVE[\-\s]*?(\d{4})[\-\s]*?(\d{1,7})', re.IGNORECASE)
                                for match in cve_pattern.finditer(result):
                                    year, id_num = match.groups()
                                    if year == query_year and id_num == query_id:
                                        found_exact = True
                                        print(f"找到精确匹配CVE (年份-ID匹配): {query} 与 CVE-{year}-{id_num}")
                                        print(f"匹配结果片段: {result[match.start():match.start()+100]}...")  # 显示匹配到的前100个字符
                                        break
                    
                    if not found_exact:
                        print(f"警告: 未找到精确匹配的CVE: {query}")
                        # 显示得分最高的结果的前100个字符，帮助调试
                        if results:
                            print(f"最佳结果片段: {results[0][:100]}...")
                else:
                    # 普通查询
                    results = self.retriever.retrieve_contents(
                        GenerationQaData(question=query), 
                        top_k=top_k
                    )
                
                # 确保结果不为None
                if results is None:
                    results = []
                    
                result_queue.put(results)
            except Exception as e:
                print(f"检索执行错误: {e}")
                import traceback
                traceback.print_exc()
                result_queue.put([])  # 确保放入空列表而不是None
        
        # 启动线程
        retrieval_thread = threading.Thread(target=perform_retrieval)
        retrieval_thread.daemon = True
        retrieval_thread.start()
        
        # 等待线程完成或超时
        retrieval_thread.join(timeout=timeout)
        
        # 检查是否超时
        if retrieval_thread.is_alive():
            print(f"检索 '{query[:30]}...' 超时")
            return []  # 超时返回空列表
        
        # 获取结果
        try:
            if not result_queue.empty():
                results = result_queue.get()
                return results if results is not None else []
            return []
        except Exception as e:
            print(f"获取检索结果时出错: {e}")
            return []  # 出错也返回空列表


    def add_anti_hallucination_instructions(self, query):
        """为查询添加反幻觉指令"""
        anti_hallucination = """
        【重要提示：防止幻觉】
        
        请遵循以下防止幻觉的严格指南：
        ... (现有部分保持不变)

        【输出格式规范】
        请使用纯文本格式（不要使用JSON结构），按照以下标题组织输出：

        ## 总体摘要
        （提供文档概述）

        ## 威胁行为者
        （列出文档中提到的威胁行为者）

        ## 相关漏洞
        （在此列出文档中提到的漏洞，一个漏洞一行，格式为：CVE编号 - 描述）

        ## 攻击技术(TTPs)
        （列出文档中提到的攻击技术）

        ## 相关威胁指标(IOCs)
        （列出文档中提到的IOC）

        ## 分析理由
        （在此部分，你必须详细说明分析过程中如何使用了不同来源的信息。请记住：当引用中包含对文档中提到的CVE、恶意软件或APT组织的额外信息时，你应该主动利用这些信息来丰富分析，而不是简单地重复文档内容。如果未使用任何外部知识库信息，请明确说明原因。）

        重要提示：不要使用代码块标记。如需在文本中使用引号，请使用单引号'示例'代替双引号，避免格式冲突。

        记住：宁可答案不够完整，也不要包含文档中不存在的信息。

        在回答中，如果你看到类似以下形式的信息：
        - "CVE-2025-49113(PUBLISHED): Roundcube Webmail before 1.5.10 and 1.6.x before 1.6.11 allows remote code execution..."
        - "CVE-20XX-XXXXX(PUBLISHED): [任何描述]..."

        这些都是典型的来自外部知识库的信息，而不是原始文档的一部分。当你使用这些信息提供诸如CVSS评分、漏洞详情、发布日期等细节时，你必须在"分析理由"部分确认你使用了这些外部知识。

        当你反思外部知识使用情况时，请考虑以下问题：
        - 外部知识是否提供了文档中没有的CVSS评分？
        - 外部知识是否提供了文档中没有的漏洞利用细节？
        - 外部知识是否提供了文档中没有的漏洞编号格式（如CVE-XXXX-XXXXX）？
        - 外部知识是否提供了文档中没有的发布日期？

        如果你对以上任一问题回答"是"，你就应该在分析理由中明确说明使用了外部知识。
        """
        return query + "\n\n" + anti_hallucination

    def analyze_document(self, analysis_text, analysis_type="summary", save_to_db=True):
        """优化版文档分析方法 - 使用文档摘要进行向量检索和词袋模型检索"""
        start_time = time.time()
        raw_text = ""
        html_content = ""
        report_date = None  # 初始化报告日期
        filtered_external_info = []
        doc_cves = []  # 初始化 doc_cves 变量
        all_iocs = []  

        try:
            # 1. 确定输入类型
            is_url = analysis_text.startswith(("http://", "https://"))
            input_type = "url" if is_url else "file"

            # 2. 加载文档
            if is_url:
                documents, report_date = load_html(analysis_text)
                # 获取原始HTML内容和提取文本
                for doc in documents:
                    raw_text += doc.page_content + "\n\n"
            else:
                documents = load_document(analysis_text)
                # 获取原始文本
                for doc in documents:
                    raw_text += doc.page_content + "\n\n"
                
            if not documents:
                return {"error": f"无法加载文档 {analysis_text}"}


             # 显式保存IOC检索结果
            all_iocs = extract_all_document_iocs(raw_text)
            print(f"从文档中提取了 {sum(len(iocs) for iocs in all_iocs.values())} 个IOC指标")
            
            # 3. 获取文档内容摘要并优化提取质量
            doc_summary = self._get_optimized_document_summary(documents)
            print("已创建文档摘要用于检索")

            # 4. 对文档进行分片 - 使用优化的分片策略
            chunks = self._split_documents_with_metadata(documents)
            print(f"文档已分片为 {len(chunks)} 个部分")
            
            # 5. 从外部知识库检索相关信息 - 使用混合检索策略
            print("从外部知识库检索相关信息...")
            external_info = self._get_external_knowledge(doc_summary, chunks)
            
            #  从文档中提取所有CVE编号 
            for chunk in chunks:
                cve_matches = re.findall(r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})', 
                                    chunk.page_content, re.IGNORECASE)
                for year, id_num in cve_matches:
                    normalized_cve = f"CVE-{year}-{id_num}".upper()
                    if normalized_cve not in doc_cves:
                        doc_cves.append(normalized_cve)
            
            if doc_cves:
                print(f"从文档中提取了 {len(doc_cves)} 个CVE编号")

            
            # 6. 构建分析提示
            analysis_query = self._build_analysis_prompt(analysis_text, doc_summary, analysis_type)
            
            # 7. 添加文档内容部分，使用明确的分隔标记
            analysis_query += "\n\n===================== 文档内容（这是你需要分析的主体内容）=====================\n\n"

            # 选择文档块，并确保不超出令牌限制
            selected_chunks = select_chunks_within_token_limit(chunks, analysis_query)
            for idx, (i, chunk) in enumerate(selected_chunks):
                # 清理块内容，移除格式问题
                cleaned_content = self._clean_chunk_content(chunk.page_content)
                analysis_query += f"\n--- 文档部分 {idx+1} (原始部分 {i+1}) ---\n{cleaned_content}"
                
            analysis_query += "\n\n===================== 文档内容结束 =====================\n\n"

            # 8. 生成分析报告
            print("\n正在生成分析报告，这可能需要一些时间...")
            if doc_cves:
                raw_text_for_cve = raw_text  # 使用原始文档文本
                
                # 获取embedding_model配置
                embedding_model = None
                if "database_setting" in self.config and "embedding_model" in self.config["database_setting"]:
                    embedding_model = self.config["database_setting"].get("embedding_model")
                
                # 调用增强的enrich_cve_data函数
                cve_details = enrich_cve_data(
                    doc_text=raw_text_for_cve
                )
                
                if cve_details:
                    # 将获取到的CVE信息添加到分析查询中
                    analysis_query += "\n\n=====================辅助背景知识--最新CVE详细信息 =====================\n\n"
                    for cve_id, cve_detail in cve_details.items():
                        analysis_query += f"{cve_detail}\n\n"
                    analysis_query += "=====================辅助背景知识--CVE详细信息结束 =====================\n\n"
                if all_iocs:
                    analysis_query += "\n\n===================== 用正则表达式提取的文章中的--IOC信息 =====================\n\n"
                    for ioc_type, ioc_list in all_iocs.items():
                        if ioc_list:
                            analysis_query += f"{ioc_type}:\n"
                            for ioc in ioc_list:
                                analysis_query += f" - {ioc}\n"
                    analysis_query += "===================== 用正则表达式提取的文章中的--IOC信息结束 =====================\n\n"

            # 准备引用 - 使用一个变量统一管理所有引用
            final_references = []
            unique_final_references = []
            
            # 添加外部知识库的相关信息，并明确标记这些是辅助背景信息
            if external_info:
                analysis_query += "\n\n===================== 辅助背景知识（仅供参考，不是待分析的文档内容）=====================\n\n"
                # 过滤和清理外部知识
                filtered_external_info = self._filter_and_clean_external_info(external_info)
                
                # 使用优先级函数选择最终引用 - 传入文档块
                final_references = self.prioritize_references(
                    filtered_external_info, 
                    document_text=raw_text,
                    chunks=chunks  # 传入文档块供IOC提取
                )
                
                # 确保关键的CVE信息被优先包含，即使它们不在优先级排序前列
                if doc_cves:
                    # 针对每个CVE进行强制检索
                    forced_cve_refs = []
                    for doc_cve in doc_cves:
                        cve_results = self._retrieve_cve_with_format(doc_cve, top_k=1)
                        if cve_results:
                            # 确保没有重复添加
                            if cve_results[0] not in final_references:
                                forced_cve_refs.append(cve_results[0])
                    
                    # 将强制检索的CVE添加到引用的开头
                    if forced_cve_refs:
                        final_references = forced_cve_refs + final_references
                    # 对最终引用进行去重
                    seen_refs = set()
                    for ref in final_references:
                        # 使用前100个字符作为唯一标识
                        ref_id = ref[:100]
                        if ref_id not in seen_refs:
                            seen_refs.add(ref_id)
                            unique_final_references.append(ref)

                    final_references = unique_final_references
                                    
                # 限制最多包含15个引用
                max_references = 15
                if len(final_references) > max_references:
                    print(f"引用数量 ({len(final_references)}) 超过最大限制，截取为 {max_references} 个")
                    final_references = final_references[:max_references]
                
                # 添加到分析查询 - 这里确保推理和展示使用同一个引用列表
                for i, ref in enumerate(final_references):
                    analysis_query += f"\n辅助背景知识 {i+1}:\n{ref}\n"
                
                analysis_query += "\n\n===================== 辅助背景知识结束 =====================\n\n"
            else:
                print("没有检索到相关的外部知识")
                filtered_external_info = []
                final_references = []

            # 清洗过滤最后的引用
            cleaned_final_references = []
            for ref in final_references:
                # 移除内存转储和寄存器内容
                cleaned_ref = remove_memory_dumps_and_registers(ref)
                # 进一步清理文本
                cleaned_ref = clean_extracted_text(cleaned_ref)
                # 只保留有意义的引用（至少50个字符）
                if len(cleaned_ref.strip()) >= 50:
                    cleaned_final_references.append(cleaned_ref)
            
            final_references = cleaned_final_references
            
            # 生成回答 - 使用统一的引用列表
            print(f"准备传递 {sum(len(iocs) for iocs in all_iocs.values())} 个IOC指标给LLM进行完善")
            result = self.answer_question(analysis_query, custom_references=final_references,Iocs=all_iocs)
            
            answer_content = result.get("answer", "")
            analysis_content, rationale = process_analysis_content(answer_content)
            print(f"分析内容长度: {len(analysis_content)} 字符")
            print(f"分析理由长度: {len(rationale)} 字符")

            # 9. 添加处理元数据
            process_time = time.time() - start_time
            result["document"] = analysis_text
            result["rationale"] = rationale
            result["analysis_type"] = analysis_type
            result["process_time"] = f"{process_time:.2f} 秒"
            result["knowledge_sources"] = {
                "document_chunks": len(chunks),
                "chunks_used": len(selected_chunks),
                "chunk_indices": [idx for idx, _ in selected_chunks],
                "external_entries": len(external_info),
                "filtered_external": len(filtered_external_info),
                "keywords_used": self.last_used_keywords if hasattr(self, 'last_used_keywords') else []
            }
            
            # 之前重连数据库
            self._reconnect_to_database()


            # 10. 保存到数据库
            if save_to_db and self.db:
                try:
                    self._save_analysis_to_database(result, analysis_text, is_url, input_type, 
                                                documents, raw_text, html_content, doc_summary, 
                                                final_references, report_date, analysis_type, analysis_content, rationale)
                except Exception as e:
                    print(f"保存到数据库失败: {e}")
                    result["db_save_status"] = f"失败: {e}"
            
            return result

        except Exception as e:
            import traceback
            traceback.print_exc()
            return {
                "error": f"分析文档时出错: {e}",
                "document": analysis_text
            }

    def _get_optimized_document_summary(self, documents):
        """创建优化的文档摘要，重点提取关键内容"""
        # 优先使用文档开头部分
        summary_parts = []
        
        # 从第一个文档提取标题和开头
        if documents and len(documents) > 0:
            first_doc = documents[0].page_content
            # 尝试提取标题或第一段
            first_lines = first_doc.split('\n\n')[0].strip()
            if first_lines:
                summary_parts.append(first_lines)
        
        # 从所有文档中提取内容片段
        for i, doc in enumerate(documents[:2]):  # 只使用前两个文档
            content = doc.page_content
            
            # 提取内容片段，优先考虑包含关键字的部分
            key_patterns = ["summary", "overview", "introduction", "background", 
                            "threat", "attack", "malware", "vulnerability"]
            
            for pattern in key_patterns:
                # 查找包含关键字的段落
                paragraphs = content.split('\n\n')
                for para in paragraphs:
                    if pattern in para.lower() and len(para) > 50:
                        summary_parts.append(para[:300])  # 限制长度
                        break
            
            # 如果没有找到关键段落，就使用文档开头
            if len(summary_parts) <= i:
                # 分割成段落
                paragraphs = content.split('\n\n')
                # 选择非空且有意义的段落
                for para in paragraphs:
                    if len(para.strip()) > 50:  # 忽略太短的段落
                        summary_parts.append(para[:300])  # 限制长度
                        break
                
                # 如果还是没有合适的段落，使用前500个字符
                if len(summary_parts) <= i:
                    summary_parts.append(content[:500])
        
        # 组合摘要，限制总长度
        combined_summary = " ".join(summary_parts)
        if len(combined_summary) > 1000:
            combined_summary = combined_summary[:1000]
            
        return combined_summary

    def _split_documents_with_metadata(self, documents):
        """使用优化的分片策略处理文档"""
        # 使用递归分割器进行分片
        splitter = RecursiveCharacterTextSplitter(
            chunk_size=1200,
            chunk_overlap=200,
            separators=["\n\n", "\n", "。", ".", "!", "?", ";", "；", "！", "？", ""]
        )
        
        # 分片处理
        chunks = []
        for i, doc in enumerate(documents):
            doc_id = f"doc-{i}"
            doc_chunks = splitter.split_documents([doc])
            
            # 为每个分片添加和保留元数据
            for j, chunk in enumerate(doc_chunks):
                chunk.metadata["chunk_id"] = f"{doc_id}-chunk-{j}"
                chunk.metadata["doc_index"] = i
                chunk.metadata["chunk_index"] = j
                
                # 保留原始文档元数据
                for key in doc.metadata:
                    if key not in chunk.metadata:
                        chunk.metadata[key] = doc.metadata[key]
            
            chunks.extend(doc_chunks)
        
        return chunks


    def _get_external_knowledge(self, doc_summary, chunks):
        """混合检索策略：结合BM25和向量检索获取外部知识"""
        # 1. 提取关键词
        keywords = extract_keywords(doc_summary, top_n=5)
        self.last_used_keywords = keywords  # 保存以便后续使用
        
        # 2. 初始化结果存储
        vector_external_info = []
        bm25_external_info = []
        ioc_external_info = []

        
        # 3. 使用向量检索 - 基于文档摘要
        print("执行向量检索 (top_k=5)...")
        try:
            vector_external_info = self._retrieval_with_timeout(
                doc_summary[:1000], top_k=5, timeout=20
            )
            if vector_external_info is None:
                print("向量检索返回为空，可能是超时或发生错误")
                vector_external_info = []
            print(f"向量检索返回 {len(vector_external_info)} 个结果")
        except Exception as e:
            print(f"向量检索过程中出现错误: {e}")
            vector_external_info = []
        
        # 4. 创建BM25检索器 - 从知识库加载文档
        try:
            print("初始化BM25检索器...")
            # 获取知识库文档路径
            knowledge_dir = "知识库"
            knowledge_files = []
            
            # 查找所有TXT文件
            for root, _, files in os.walk(knowledge_dir):
                for file in files:
                    if file.endswith('.txt'):
                        knowledge_files.append(os.path.join(root, file))
            
            if not knowledge_files:
                print("警告: 未找到知识库文档")
                knowledge_docs = []
            else:
                print(f"从 {len(knowledge_files)} 个知识库文件加载内容...")
                knowledge_docs = []
                
                # 加载文件内容
                for file_path in knowledge_files[:50]:  # 限制文件数量，避免内存问题
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            # 按行处理，每行作为一个文档
                            for i, line in enumerate(f):
                                line = line.strip()
                                if line and len(line) > 30:  # 忽略太短的行
                                    knowledge_docs.append(Document(
                                        page_content=line,
                                        metadata={"source": file_path, "line": i}
                                    ))
                    except Exception as e:
                        print(f"读取文件 {file_path} 出错: {e}")
            
            # 从文档创建BM25检索器(如果有文档的话)
            if knowledge_docs:
                bm25_retriever = BM25Retriever.from_documents(knowledge_docs, k=5)
                print(f"BM25检索器初始化完成，包含 {len(knowledge_docs)} 个知识库文档")
                
                # 过滤掉CVE关键词，让它们通过专门的CVE检索处理
                non_cve_keywords = [kw for kw in keywords if not kw.upper().startswith('CVE-')]
                
                # 初始化bm25_results列表
                bm25_results = []  # 在这里添加初始化
                
                if non_cve_keywords:
                    # 合并关键词为查询字符串
                    combined_query = " ".join(non_cve_keywords[:3])  # 使用前3个非CVE关键词
                    print(f"BM25检索查询: '{combined_query}'")
                    
                    # 执行检索
                    docs = bm25_retriever.invoke(combined_query)
                    for doc in docs:
                        bm25_results.append(doc.page_content)
                        
                    print(f"BM25检索返回 {len(bm25_results)} 个结果")
                else:
                    print("没有找到合适的非CVE关键词用于BM25检索")
                    bm25_results = []
                    
                bm25_external_info = bm25_results
            
        except Exception as e:
            print(f"BM25检索过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            bm25_external_info = []
        
        # 5. 使用特殊IOC检索 - 从文档块中提取IOC进行搜索
        try:
            ioc_external_info = self._retrieve_using_iocs(chunks)
            if ioc_external_info is None:
                ioc_external_info = []
            print(f"IOC检索返回 {len(ioc_external_info)} 个结果")
        except Exception as e:
            print(f"IOC检索过程中出现错误: {e}")
            ioc_external_info = []
        
        # 6. 合并去重结果
        combined_results = vector_external_info + bm25_external_info + ioc_external_info
        unique_results = []
        seen_content = set()
        
        for item in combined_results:
            # 使用内容的前100个字符作为唯一标识符
            content_id = item[:100] if item else ""
            if content_id and content_id not in seen_content:
                seen_content.add(content_id)
                unique_results.append(item)
        
        print(f"从外部知识库共检索到 {len(unique_results)} 个相关条目")
        return unique_results


    def _retrieve_using_iocs(self, chunks, max_iocs=5):
        """从文档块中提取IOC并使用它们进行检索"""
        # 提取所有可能的IOC
        all_iocs = []
        
        try:
            # 扩展IOC模式，添加多种CVE编号支持格式
            ioc_patterns = {
                'ip': r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
                'hash': r'\b[a-fA-F0-9]{32}\b|\b[a-fA-F0-9]{40}\b|\b[a-fA-F0-9]{64}\b',
                'domain': r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}\b',
                'cve': r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})\b'
            }
            
            # 检查chunks是否为None或空
            if not chunks:
                print("警告: 未提供文档块，无法提取IOC")
                return []
            
            # 从所有块中提取IOC
            for _, chunk in enumerate(chunks):
                text = chunk.page_content if hasattr(chunk, 'page_content') else str(chunk)
                
                for ioc_type, pattern in ioc_patterns.items():
                    if ioc_type == 'cve':
                        # 特殊处理CVE编号的提取
                        matches = re.findall(pattern, text, re.IGNORECASE)
                        for year, id_num in matches:
                            # 统一格式化CVE编号: CVE-YYYY-NNNNN
                            normalized_cve = f"CVE-{year}-{id_num}"
                            all_iocs.append((normalized_cve, ioc_type))
                    else:
                        matches = re.findall(pattern, text)
                        for match in matches:
                            # 排除常见域名和私有IP地址
                            if ioc_type == 'domain' and any(common in match.lower() for common in 
                                ['google.com', 'microsoft.com', 'apple.com', 'amazon.com']):
                                continue
                            if ioc_type == 'ip' and (match.startswith('127.') or match.startswith('192.168.') 
                                                or match.startswith('10.')):
                                continue
                            
                            all_iocs.append((match, ioc_type))
            
            # 去重，保留类型信息
            unique_iocs_dict = {}
            for ioc, ioc_type in all_iocs:
                if ioc not in unique_iocs_dict:
                    unique_iocs_dict[ioc] = ioc_type
            
            # 转换回列表，但保留优先级排序：CVE > hash > domain > ip
            priority_order = {'cve': 0, 'hash': 1, 'domain': 2, 'ip': 3}
            unique_iocs = sorted(unique_iocs_dict.items(), key=lambda x: priority_order.get(x[1], 999))
            
            # 限制数量，但确保按照优先级选择
            iocs_to_use = [ioc for ioc, _ in unique_iocs[:max_iocs]]
            
            # 如果找到了IOC，使用它们进行检索
            results = []
            if iocs_to_use:
                # 打印找到的IOC类型统计
                ioc_type_counts = {}
                for ioc, ioc_type in unique_iocs[:max_iocs]:
                    ioc_type_counts[ioc_type] = ioc_type_counts.get(ioc_type, 0) + 1
                
                ioc_summary = ", ".join([f"{count} 个{ioc_type}" for ioc_type, count in ioc_type_counts.items()])
                print(f"使用 {len(iocs_to_use)} 个IOC进行额外检索 ({ioc_summary})...")
                
                for ioc in iocs_to_use:
                    if ioc.startswith("CVE-"):
                        print(f"检索CVE: {ioc}")
                        
                        # 使用专门针对向量库中CVE格式的查询方法
                        cve_results = self._retrieve_cve_with_format(ioc, top_k=3)
                        if cve_results:
                            results.extend(cve_results)
                    else:
                        # 其他IOC类型的检索保持不变
                        ioc_results = self._retrieval_with_timeout(ioc, top_k=2, timeout=5)
                        if ioc_results:
                            results.extend(ioc_results)
            
            return results
        
        except Exception as e:
            print(f"IOC检索过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return []


    def _exact_keyword_search(self, keyword):
        """并行处理版本的精确关键词搜索方法"""
        
        # 检查单个文件中的CVE
        def check_file(file_path, target_cve):
            try:
                matches = []
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        if target_cve in line:
                            # 优先返回完全匹配的格式
                            if line.startswith(f"{target_cve}(PUBLISHED):"):
                                return [line.strip()]
                            matches.append(line.strip())
                return matches
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")
                return []
        
        # 开始实际搜索
        try:
            # 只对CVE编号进行特殊处理
            if not keyword.startswith("CVE-"):
                return []
                
            # 知识库目录配置
            knowledge_dir = "知识库"
            cve_dir = os.path.join(knowledge_dir, "cve")
            
            # 确保目录存在
            if not os.path.exists(cve_dir):
                print(f"CVE目录不存在: {cve_dir}")
                return []
            
            # 提取CVE年份
            parts = keyword.split("-")
            if len(parts) < 3:
                return []
                
            year = parts[1]
            
            # 创建文件列表
            potential_files = []
            
            # 1. 年份文件优先级最高
            year_file = os.path.join(cve_dir, f"{year}.txt")
            if os.path.exists(year_file):
                potential_files.append(year_file)
            
            # 2. 查找其他可能包含该CVE的文件
            for pattern in [f"{year}*.txt", "all*.txt", "cve*.txt"]:
                matching_files = glob.glob(os.path.join(cve_dir, pattern))
                potential_files.extend(matching_files)
            
            # 去重并限制文件数量
            potential_files = list(set(potential_files))[:10]  # 最多检查10个文件
            
            if not potential_files:
                print(f"未找到可能包含 {keyword} 的文件")
                return []
                
            print(f"搜索 {keyword} 在 {len(potential_files)} 个文件中")
            
            # 并行搜索所有文件
            all_results = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                future_to_file = {
                    executor.submit(check_file, file_path, keyword): file_path
                    for file_path in potential_files
                }
                
                for future in concurrent.futures.as_completed(future_to_file):
                    file_results = future.result()
                    if file_results:
                        all_results.extend(file_results)
                        # 如果找到了精确格式的匹配项，可以提前返回
                        if any(r.startswith(f"{keyword}(PUBLISHED):") for r in file_results):
                            matching = [r for r in file_results if r.startswith(f"{keyword}(PUBLISHED):")]
                            return matching[:1]  # 只返回第一条精确匹配
            
            return all_results
            
        except Exception as e:
            print(f"精确关键词搜索过程中出错: {e}")
            return []



    def _retrieve_cve_with_format(self, cve_id, top_k=5):  # 增加top_k提高召回率
        """优化CVE特殊格式检索方法"""
        # 方案1：无符号精确匹配 (核心修复)
        results = []
    
        # 1. 优先使用精确关键词匹配（不依赖向量数据库）
        exact_results = self._exact_keyword_search(cve_id)
        if exact_results:
            print(f"精确关键词匹配成功: 找到{len(exact_results)}条记录")
            return exact_results[:top_k]
        
        # 2. 向量检索 + 优化的过滤逻辑
        exact_target = f"{cve_id}(PUBLISHED):"
        try:
            # 使用精确的CVE编号作为查询（不添加额外格式）
            results = self.retriever.retrieve_contents(
                GenerationQaData(question=f'"{cve_id}"'),  # 使用引号确保精确匹配CVE编号
                top_k=max(top_k, 10)
            )
            
            # 详细调试输出
            print(f"向量检索返回 {len(results) if results else 0} 条结果")
            if results and len(results) > 0:
                for i, result in enumerate(results[:2]):  # 只显示前两条
                    print(f"  结果 #{i+1} 前100字符: {result[:100]}")
                    print(f"  是否包含目标CVE: {cve_id in result}")
            
            # 使用更灵活的匹配逻辑：只要包含CVE编号即可
            matched = []
            if results:
                for result in results:
                    if cve_id in result:  # 只需包含CVE编号
                        matched.append(result)
                        print(f"  找到匹配: {result[:100]}...")
            
            if matched:
                print(f"基础CVE匹配成功: 找到{len(matched)}条记录")
                return matched[:top_k]
        except Exception as e:
            print(f"精确查询异常: {e}")
        
        # 方案3：模糊匹配（最后防线）
        try:
            # 加入上下文关键词强化语义
            semantic_results = self.retriever.retrieve_contents(
                GenerationQaData(question=f"{cve_id} 漏洞详情 PUBLISHED"), 
                top_k=top_k*3
            )
            # 宽松内容过滤
            semantic_match = [r for r in semantic_results if cve_id in r]
            if semantic_match:
                print(f"语义检索匹配: {len(semantic_match)}条")
                return semantic_match[:top_k]
        except Exception as e:
            print(f"语义检索异常: {e}")
        
        print(f"警告: 所有检索模式未找到 {cve_id}")
        return []
    

    def _clean_chunk_content(self, text):
        """清理文档块内容，移除格式问题"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除页眉页脚和URL
        text = re.sub(r'Page \d+ of \d+', '', text)
        text = re.sub(r'https?://\S+', '', text)
        
        # 移除特殊字符和格式标记
        text = text.replace('\u2022', '•')  # 替换特殊项目符号
        text = text.replace('\xa0', ' ')    # 替换不间断空格
        
        return text.strip()

    def _filter_and_clean_external_info(self, external_info):
        """过滤和清理外部知识"""
        if not external_info:
            return []
        
        # 过滤太短或无意义的条目
        filtered_info = [info for info in external_info if info and len(info.strip()) > 50]
        
        # 过滤掉重复内容和元数据
        unique_info = []
        seen_content = set()
        
        for item in filtered_info:
            # 清理项目
            cleaned_item = self._clean_external_knowledge_item(item)
            
            # 使用内容的开头作为唯一标识
            content_id = cleaned_item[:100]
            if content_id and content_id not in seen_content:
                seen_content.add(content_id)
                unique_info.append(cleaned_item)
        
        return unique_info

    def _clean_external_knowledge_item(self, text):
        """清理单个外部知识条目"""
        if not text:
            return ""
        
        # 移除URL、页眉页脚
        text = re.sub(r'https?://\S+', '', text)
        text = re.sub(r'Page \d+ of \d+', '', text)
        text = re.sub(r'www\.[a-zA-Z0-9-]+\.[a-zA-Z]{2,}', '', text)
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 如果文本太长，截断它
        if len(text) > 500:
            text = text[:500] + "..."
        
        return text.strip()

    def _build_analysis_prompt(self, analysis_text, doc_summary, analysis_type):
        """构建分析提示"""
        # 设置任务说明
        if analysis_type == "summary":
            task_instruction = "提供一份文档的综合摘要，包括主要内容、关键观点和结论。不少于150字。"
        elif analysis_type == "threats":
            task_instruction = "分析文档中提到的所有网络威胁，包括威胁行为者、攻击技术和战术。"
        elif analysis_type == "vulns":
            task_instruction = "识别并分析文档中提到的所有漏洞，包括CVE编号、严重程度和可能的利用方式。编号一样的CVE描述只需一条即可。"
        elif analysis_type == "iocs":
            task_instruction = ("提取文档中的所有威胁指标(IOCs)，包括IP地址、域名、URL、文件哈希等。"
                                "请务必包含我提供的'用正则表达式提取的文章中的IOC信息'部分中列出的所有IOC，"
                                "并确保输出中的IOC列表完整、不遗漏。对每个IOC尽可能提供简短说明，说明其在文档中的上下文或关联威胁。"
                                "如果文档内容不足以解释某个IOC的作用，可以标注为'上下文不明'。")
        elif analysis_type == "all":
            task_instruction = "对文档进行全面分析，包括：1)总体摘要；2)提到的威胁行为者；3)相关漏洞；4)攻击技术(TTPs)；5)相关威胁指标(IOCs)。"
        else:
            task_instruction = f"分析文档并提供有关{analysis_type}的详细信息。"
        
        # 构建基础查询
        analysis_query = (
            f"请用中文分析以下网络威胁情报文档内容：{task_instruction}\n\n"
            f"文档标题：{analysis_text}\n\n"
            f"文档概述：\n{doc_summary[:500]}\n\n"
        )
        
        # 添加反幻觉指令
        analysis_query = self.add_anti_hallucination_instructions(analysis_query)
        
        # 添加信息类型说明
        analysis_query += "\n\n【重要说明：下面我将提供两类信息 - 1) 待分析的文档内容和 2) 辅助参考的外部知识。这两者是完全不同的来源，你的主要任务是分析文档内容，而外部知识仅作为背景信息和辅助理解。注意：分析理由从这两部分说明，没有使用外部知识库就说无】\n\n"
        
        return analysis_query

    def check_existing_analysis(self, crawled_data_id, analysis_type):
        """检查是否已存在相同分析，使用规范化URL和内容相似度"""
        # 1. 首先获取当前数据的URL和标题
        self.db.cursor.execute(
            "SELECT link, title FROM crawled_data WHERE id = %s",
            (crawled_data_id,)
        )
        current_data = self.db.cursor.fetchone()
        
        if not current_data:
            return None
            
        current_link, current_title = current_data
        normalized_url = current_link.split('?')[0]  # 移除URL参数部分
        
        # 2. 检查精确匹配
        self.db.cursor.execute(
            "SELECT id FROM rag_analysis WHERE crawled_data_id = %s AND analysis_type = %s",
            (crawled_data_id, analysis_type)
        )
        existing_row = self.db.cursor.fetchone()
        
        if existing_row:
            return existing_row
        
        # 3. 检查是否有相同标准化URL和相似标题的分析
        self.db.cursor.execute("""
            SELECT ra.id
            FROM rag_analysis ra
            JOIN crawled_data cd ON ra.crawled_data_id = cd.id
            WHERE 
                SUBSTRING_INDEX(cd.link, '?', 1) = %s AND
                ra.analysis_type = %s AND
                cd.title = %s
            LIMIT 1
        """, (normalized_url, analysis_type, current_title))
        
        similar_row = self.db.cursor.fetchone()
        return similar_row


    def _save_analysis_to_database(self, result, analysis_text, is_url, input_type, 
                           documents, raw_text, html_content, doc_summary, 
                           references, report_date, analysis_type, analysis_content, rationale):
        """将分析结果保存到数据库"""
        try:
            # 1. 检查是否有数据库连接
            if not self.db or not hasattr(self.db, 'cursor'):
                print("数据库连接不可用")
                return False

            
            # 2. 准备要保存的数据
            crawled_data_id = None
            
            # 为文档类型和URL类型添加标题提取
            title = os.path.basename(analysis_text) if not is_url else analysis_text
            
            # 尝试从文档内容中提取真正的标题（适用于文件和URL）
            if len(documents) > 0:
                first_chunk = documents[0].page_content.strip()
                
                # 对于URL类型，尝试从原始HTML中提取标题
                if is_url and html_content:
                    try:
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(html_content, 'html.parser')
                        
                        # 优先级1: HTML title标签
                        title_tag = soup.find('title')
                        if title_tag and title_tag.get_text().strip():
                            potential_title = title_tag.get_text().strip()
                            # 清理常见的网站名称后缀
                            potential_title = re.sub(r'\s*[-|–]\s*[^-|–]+$', '', potential_title)  # 移除 "- 网站名" 格式
                            potential_title = re.sub(r'\s*\|\s*[^|]+$', '', potential_title)  # 移除 "| 网站名" 格式
                            if len(potential_title) > 10 and len(potential_title) < 200:
                                title = potential_title
                                print(f"从HTML title标签提取标题: {title}")
                        
                        # 优先级2: 主标题h1标签（如果title标签不可用或太短）
                        if title == analysis_text or len(title) < 10:
                            h1_tag = soup.find('h1')
                            if h1_tag and h1_tag.get_text().strip():
                                potential_title = h1_tag.get_text().strip()
                                if len(potential_title) > 10 and len(potential_title) < 200:
                                    title = potential_title
                                    print(f"从HTML h1标签提取标题: {title}")
                        
                    except Exception as e:
                        print(f"从HTML提取标题失败: {e}")
                
                # 对于所有类型，尝试从提取的文本内容中获取标题
                if title == analysis_text or len(title) < 10:
                    first_lines = first_chunk.split('\n')
                    for line in first_lines[:5]:  # 检查前5行
                        line = line.strip()
                        # 更严格的标题验证条件
                        if (line and 
                            len(line) > 10 and len(line) < 150 and  # 长度限制更严格
                            not re.match(r'^https?://', line) and 
                            line.count('http') < 2 and
                            not line.endswith('.') and  # 标题通常不以句号结尾
                            line.count('。') == 0):  # 中文标题通常不包含句号
                            
                            # 进一步验证：不应包含过多的特殊字符
                            special_char_ratio = len(re.findall(r'[^\w\s\u4e00-\u9fff]', line)) / len(line)
                            if special_char_ratio < 0.2:  # 特殊字符比例小于20%
                                title = line
                                print(f"从文本内容提取标题: {title}")
                                break
                
            # 如果是URL但仍然没有从内容中提取到合适的标题，尝试从URL路径中提取
            if is_url and title == analysis_text:
                try:
                    from urllib.parse import urlparse, unquote
                    parsed_url = urlparse(analysis_text)
                    path_parts = [part for part in parsed_url.path.split('/') if part]
                    if path_parts:
                        # 使用最后一个路径部分，去掉扩展名
                        last_part = unquote(path_parts[-1])
                        if '.' in last_part:
                            last_part = last_part.rsplit('.', 1)[0]
                        if len(last_part) > 3:  # 确保不是太短的路径
                            title = last_part.replace('-', ' ').replace('_', ' ')
                            print(f"从URL路径中提取标题: {title}")
                except Exception as e:
                    print(f"从URL提取标题失败: {e}")
                    # 保持原来的URL作为标题
            
            # 如果是URL，检查是否已存在于crawled_data表中
            if is_url:
                self.db.cursor.execute(
                    "SELECT id FROM crawled_data WHERE link = %s", 
                    (analysis_text,)
                )
                row = self.db.cursor.fetchone()
                
                if row:
                    # 如果已存在，使用现有ID
                    crawled_data_id = row['id']
                    # 更新记录
                    self.db.cursor.execute(
                        """UPDATE crawled_data SET 
                        html_content = %s, 
                        extracted_text = %s, 
                        crawl_status = 1, 
                        crawl_time = CURRENT_TIMESTAMP
                        WHERE id = %s""",
                        (html_content, raw_text, crawled_data_id)
                    )
                else:
                    # 如果不存在，创建新记录
                    self.db.cursor.execute(
                        """INSERT INTO crawled_data 
                        (query, title, link, snippet, html_content, extracted_text, crawl_status) 
                        VALUES (%s, %s, %s, %s, %s, %s, %s)""",
                        (
                            "RAG分析", 
                            title[:255] if title else "", 
                            analysis_text, 
                            doc_summary[:500], 
                            html_content, 
                            raw_text, 
                            1
                        )
                    )
                    self.db.conn.commit()
                    crawled_data_id = self.db.cursor.lastrowid
            else:
                # 如果不是URL，则创建新记录
                self.db.cursor.execute(
                    """INSERT INTO crawled_data 
                    (query, title, link, snippet, extracted_text, crawl_status) 
                    VALUES (%s, %s, %s, %s, %s, %s)""",
                    (
                        "文件分析", 
                        title[:255] if title else "", 
                        analysis_text, 
                        doc_summary[:500], 
                        raw_text, 
                        1
                    )
                )
                self.db.conn.commit()
                crawled_data_id = self.db.cursor.lastrowid
            
            # 准备报告时间
            report_time_str = None
            if report_date:
                # 如果是datetime对象
                if isinstance(report_date, datetime):
                    report_time_str = report_date.strftime('%Y-%m-%d %H:%M:%S')
                # 如果是字符串
                elif isinstance(report_date, str):
                    report_time_str = report_date
                else:
                    report_time_str = datetime.now().strftime('%Y-%m-%d')
            else:
                report_time_str = datetime.now().strftime('%Y-%m-%d')
            
            # 转换引用列表为字符串
            references_text = "\n\n---\n\n".join(references) if references else ""
            
            # 检查是否已存在相同分析
            existing_row = self.check_existing_analysis(crawled_data_id, analysis_type)

            # If check_existing_analysis returns None, proceed with creating a new analysis
            if existing_row:
                print(f"找到现有分析: ID {existing_row['id']}")


            if existing_row:
                # 更新现有记录
                self.db.cursor.execute(
                    """UPDATE rag_analysis SET 
                    analysis_content = %s, references_text = %s, rationale = %s, 
                    analysis_status = %s, analysis_time = NOW(), report_time = %s, 
                    input_source = %s, input_type = %s 
                    WHERE id = %s""",
                    (
                        analysis_content,
                        references_text,
                        rationale,
                        1,
                        report_time_str,
                        analysis_text,
                        input_type,
                        existing_row['id']
                    )
                )
                analysis_id = existing_row['id']
            else:
                # 插入新记录
                self.db.cursor.execute(
                    """INSERT INTO rag_analysis 
                    (crawled_data_id, analysis_type, analysis_content, references_text, 
                    rationale, analysis_status, report_time, input_source, input_type) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                    (
                        crawled_data_id,
                        analysis_type,
                        analysis_content,
                        references_text,
                        rationale,
                        1,
                        report_time_str,
                        analysis_text,
                        input_type
                    )
                )
                analysis_id = self.db.cursor.lastrowid
            
            # 提交事务
            self.db.conn.commit()
            print(f"分析结果已成功保存到数据库，ID: {analysis_id}")
            
            # 返回结果ID信息
            return {
                "crawled_data_id": crawled_data_id,
                "analysis_id": analysis_id
            }
        
        except Exception as e:
            import traceback
            print(f"保存到数据库时出错: {e}")
            traceback.print_exc()
            return False


    def close(self):
        """关闭资源连接"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("数据库连接已关闭")

    def smart_interactive(self):
        """智能交互式界面 - 自动识别输入类型并处理"""
        print("\n🤖 CTI 智能分析助手 🤖")
        print("- 输入问题直接获取回答")
        print("- 输入文件路径以分析本地文档 (支持 PDF, DOCX, TXT, MD)")
        print("- 输入网址(http://或https://)以分析在线内容")
        print("- 输入 'exit' 或 'q' 退出程序\n")

        while True:
            user_input = input("\n请输入问题、文件路径或网址: ").strip()
            
            # 检查是否退出
            if user_input.lower() in ["exit", "quit", "q"]:
                print("感谢使用，再见！")
                break
                
            # 检查输入是否为空
            if not user_input:
                print("输入不能为空，请重新输入。")
                continue
                
            # 初始化变量
            is_url = False
            is_file = False
            url_to_analyze = None
                
            # 处理测试文档快捷方式
            if user_input.lower() == "test1":
                # 尝试多种可能的格式
                potential_paths = [
                    os.path.join("test_docs", "test1.pdf"),
                    os.path.join("test_docs", "test1.docx"),
                    os.path.join("test_docs", "test1.txt")
                ]
                
                # 检查文件是否存在
                found_test_doc = False
                for path in potential_paths:
                    if os.path.exists(path):
                        user_input = path
                        print(f"使用测试文档: {user_input}")
                        found_test_doc = True
                        break
                
                if not found_test_doc:
                    print("未找到测试文档，请确保在test_docs目录中存在test1.pdf、test1.docx或test1.txt")
                    continue
            
            # 检查和提取URL - 放在测试文档处理之后，这样可以处理所有类型的输入
            url_pattern = r'https?://[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)'
            url_matches = re.findall(url_pattern, user_input)

            # 检查是否包含URL
            if url_matches:
                # 验证并解析URL
                valid_urls = []
                for url in url_matches:
                    try:
                        # 移除URL末尾的标点符号
                        if url[-1] in ['.', ',', ')', ';', ':']:
                            url = url[:-1]
                        # 解析URL，如果格式无效会抛出异常
                        result = urllib.parse.urlparse(url)
                        if result.scheme in ('http', 'https') and result.netloc:
                            valid_urls.append(url)
                    except:
                        pass
                        
                if valid_urls:
                    is_url = True
                    # 选择最长的有效URL
                    url_to_analyze = max(valid_urls, key=len)
                    print(f"检测到URL: {url_to_analyze}")

            # 如果整个输入就是一个URL
            if user_input.startswith(("http://", "https://")) and not is_url:
                try:
                    result = urllib.parse.urlparse(user_input)
                    if result.scheme in ('http', 'https') and result.netloc:
                        is_url = True
                        url_to_analyze = user_input
                except:
                    pass
                    
            if not is_url:  # 如果不是URL，检查是否为文件路径
                # 检查文件是否存在
                if os.path.exists(user_input):
                    is_file = True
                else:
                    # 尝试在当前目录下寻找
                    current_dir_path = os.path.join(os.getcwd(), user_input)
                    if os.path.exists(current_dir_path):
                        user_input = current_dir_path
                        is_file = True
            
            # 基于输入类型调用相应功能
            if is_url:  # 网页分析
                print(f"\n🔍 检测到网页链接, 开始分析...")
                
                # 自动选择分析类型
                if "摘要" in user_input:
                    analysis_type = "summary"
                elif "威胁" in user_input:
                    analysis_type = "threats"
                elif "漏洞" in user_input:
                    analysis_type = "vulns"
                elif "IOC" in user_input or "ioc" in user_input.lower():
                    analysis_type = "iocs"
                else:
                    analysis_type = "all"

                print(f"\n开始{analysis_type}分析...")

                result = self.analyze_document(url_to_analyze, analysis_type)

                if "error" in result:
                    print(f"\n 分析错误: {result['error']}")
                    continue
                
                print("\n 分析结果:")
                print(result['answer'])

                print("\n 知识库信息:")
                if result['references']:
                    for i, ref in enumerate(result['references'], 1):
                        if len(ref) > 150:
                            ref = ref[:150] + "..."
                        print(f"{i}. {ref}")
                else:
                    print("(未检索到相关参考资料)")
                
                print(f"\n处理时间: {result.get('process_time', '未知')}")
                
            elif is_file:  # 文档分析
                # 自动选择分析类型
                print(f"\n🔍 检测到文档, 开始分析...")
                
                # 自动选择分析类型
                if "摘要" in user_input:
                    analysis_type = "summary"
                elif "威胁" in user_input:
                    analysis_type = "threats"
                elif "漏洞" in user_input:
                    analysis_type = "vulns"
                elif "IOC" in user_input or "ioc" in user_input.lower():
                    analysis_type = "iocs"
                else:
                    analysis_type = "all"

                print(f"\n开始{analysis_type}分析...")
                
                result = self.analyze_document(user_input, analysis_type)
                
                if "error" in result:
                    print(f"\n 分析错误: {result['error']}")
                    continue
                
                print("\n 分析结果:")
                print(result['answer'])

                print("\n 参考资料:")
                if result['references']:
                    for i, ref in enumerate(result['references'], 1):
                        if len(ref) > 150:
                            ref = ref[:150] + "..."
                        print(f"{i}. {ref}")
                else:
                    print("(未检索到相关参考资料)")
                
                print(f"\n处理时间: {result.get('process_time', '未知')}")
                
            else:  # 问答模式
                print("\n💬 处理问题中...")
                result = self.answer_question(user_input)
                
                print("\n🔍 回答:")
                print(result['answer'])
                
                print("\n 参考资料:")
                if result['references']:
                    for i, ref in enumerate(result['references'], 1):
                        if len(ref) > 150:
                            ref = ref[:150] + "..."
                        print(f"{i}. {ref}")
                else:
                    print("(未检索到相关参考资料)")
                    
            print("\n" + "-"*50)



def main():
    parser = argparse.ArgumentParser(description="PIKE-RAG 交互式问答与文档分析")
    parser.add_argument("--config", default="configs/rag_inference.yml", help="配置文件路径")
    parser.add_argument("--mode", choices=["qa", "document"], default="qa", 
                       help="运行模式: qa(问答) 或 document(文档分析)")
    parser.add_argument("--doc", help="要分析的文档路径")
    parser.add_argument("--analysis", choices=["summary", "threats", "vulns", "iocs", "all"], 
                       default="summary", help="文档分析类型")
    parser.add_argument("--question", "-q", help="直接提问模式")
    args = parser.parse_args()
    
    rag = SimpleRagInference(args.config)


    # 判断运行模式
    if args.question:
        # 直接回答问题
        result = rag.answer_question(args.question)
        print("\n 回答:")
        print(result['answer'])
        
        print("\n 参考资料:")
        if result['references']:
            for i, ref in enumerate(result['references'], 1):
                if len(ref) > 150:
                    ref = ref[:150] + "..."
                print(f"{i}. {ref}")
        else:
            print("(未检索到相关参考资料)")
    elif args.doc:
        # 直接分析指定的文档
        print(f"\n 开始分析: {args.doc}")
        result = rag.analyze_document(args.doc, "all")
        
        if "error" in result:
            print(f"\n 分析错误: {result['error']}")
        else:
            print("\n 分析结果:")
            print(result['answer'])
            print(f"\n处理时间: {result.get('process_time', '未知')}")
    else:
        # 启动智能交互式界面
        rag.smart_interactive()
    
    rag.close()

if __name__ == "__main__":
    
    main()

