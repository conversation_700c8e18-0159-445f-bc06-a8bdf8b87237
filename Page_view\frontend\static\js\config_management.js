/**
 * 威胁情报网站配置管理前端脚本
 */

// 全局变量
let currentSites = [];
let currentCategory = '';
let currentStatus = '';
let deleteTargetId = null;
let selectedSites = new Set(); // 存储选中的网站ID

// API调用函数 - 安全的认证处理
async function apiCall(url, options = {}) {
    // 从页面的meta标签或全局变量获取认证信息
    // 这样避免在JavaScript中硬编码密码
    const authHeader = getAuthHeader();

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...authHeader
        }
    };

    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    return fetch(url, finalOptions);
}

// 获取认证头 - 使用session认证
function getAuthHeader() {
    // 由于页面已经通过Flask-HTTPAuth认证，浏览器会自动处理认证
    // 我们不需要在JavaScript中手动添加认证头
    // 浏览器会自动重用已建立的认证session
    return {};
}

// 确保URL有正确的协议前缀
function ensureHttpProtocol(url) {
    if (!url) return '';

    // 如果已经有协议前缀，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 默认添加https://前缀
    return 'https://' + url;
}

// 通知函数
function showNotification(message, type = 'info', duration = 4000) {
    // 优先使用系统的通知函数
    if (typeof showTopRightNotification === 'function') {
        const icon = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info';
        showTopRightNotification(`${icon} ${message}`, type, duration);
    } else if (typeof showMessage === 'function') {
        showMessage(message, type === 'error');
    } else {
        // 创建简单的右上角通知
        createSimpleNotification(message, type, duration);
    }
}

function createSimpleNotification(message, type, duration) {
    // 创建通知容器（如果不存在）
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }

    // 创建通知元素
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : type === 'warning' ? '#fff3cd' : '#d1ecf1';
    const textColor = type === 'success' ? '#155724' : type === 'error' ? '#721c24' : type === 'warning' ? '#856404' : '#0c5460';
    const borderColor = type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : type === 'warning' ? '#ffeaa7' : '#bee5eb';

    notification.style.cssText = `
        background-color: ${bgColor};
        color: ${textColor};
        border: 1px solid ${borderColor};
        border-radius: 8px;
        padding: 12px 16px;
        margin-bottom: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        font-size: 14px;
        line-height: 1.4;
    `;

    notification.textContent = message;
    container.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSites();
    initEventListeners();
});

// 初始化事件监听器
function initEventListeners() {
    // 分类标签点击事件
    document.querySelectorAll('.category-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // 更新活跃状态
            document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // 更新当前分类并重新加载
            currentCategory = this.dataset.category;
            loadSites();
        });
    });
    
    // 状态过滤器变化事件
    document.getElementById('statusFilter').addEventListener('change', function() {
        currentStatus = this.value;
        loadSites();
    });
    
    // 搜索输入框回车事件
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchSites();
        }
    });
}

// 加载网站列表
async function loadSites() {
    try {
        showLoading();
        
        const params = new URLSearchParams();
        if (currentCategory) params.append('category', currentCategory);
        if (currentStatus) params.append('status', currentStatus);
        
        const response = await apiCall(`/api/config/sites?${params}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            currentSites = data.data;
            renderSites(currentSites);
            updateStatistics(currentSites);
        } else {
            throw new Error(data.error || '加载失败');
        }
        
    } catch (error) {
        console.error('加载网站列表失败:', error);
        showError('加载网站列表失败: ' + error.message);
    }
}

// 渲染网站列表
function renderSites(sites) {
    const container = document.getElementById('sitesContainer');
    
    if (sites.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="bi bi-inbox" style="font-size: 3rem; color: #dee2e6;"></i>
                <h4 class="mt-3">暂无网站配置</h4>
                <p>点击上方"添加网站"按钮开始添加威胁情报数据源</p>
            </div>
        `;
        return;
    }
    
    const sitesHtml = sites.map(site => `
        <div class="site-card ${site.status !== 'active' ? 'inactive' : ''}" data-site-id="${site.id}">
            <div class="card-body p-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="me-2">
                                <input class="form-check-input site-checkbox" type="checkbox" value="${site.id}"
                                       onchange="toggleSiteSelection(${site.id})" id="site-${site.id}">
                            </div>
                            <div class="me-3">
                                <span class="priority-badge badge bg-${getPriorityColor(site.priority)}">${site.priority}</span>
                            </div>
                            <div>
                                <h6 class="mb-1">${escapeHtml(site.name)}</h6>
                                <small class="text-muted">${escapeHtml(site.domain)}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex flex-wrap gap-1">
                            <span class="badge bg-${getCategoryColor(site.category)}">${getCategoryText(site.category)}</span>
                            <span class="badge bg-${getStatusColor(site.status)} status-badge">${getStatusText(site.status)}</span>
                            <span class="badge bg-secondary">${getLanguageText(site.language)}</span>
                        </div>
                    </div>
                    <div class="col-md-3 text-end">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary btn-action" onclick="editSite(${site.id})" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-${site.status === 'active' ? 'warning' : 'success'} btn-action" 
                                    onclick="toggleSiteStatus(${site.id})" title="${site.status === 'active' ? '停用' : '启用'}">
                                <i class="bi bi-${site.status === 'active' ? 'pause' : 'play'}"></i>
                            </button>
                            <button class="btn btn-outline-info btn-action" onclick="viewSiteDetails(${site.id})" title="查看详情">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-action" onclick="deleteSite(${site.id}, '${escapeHtml(site.name)}')" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                ${site.description ? `
                <div class="row mt-2">
                    <div class="col-12">
                        <small class="text-muted">${escapeHtml(site.description)}</small>
                    </div>
                </div>
                ` : ''}
                
                ${site.tags && site.tags.length > 0 ? `
                <div class="row mt-2">
                    <div class="col-12">
                        ${site.tags.map(tag => `<span class="tag-input">${escapeHtml(tag)}</span>`).join('')}
                    </div>
                </div>
                ` : ''}
                
                <div class="row mt-2">
                    <div class="col-12">
                        <small class="text-muted">
                            <i class="bi bi-link-45deg me-1"></i>
                            <a href="${ensureHttpProtocol(site.url)}" target="_blank" class="text-decoration-none">${escapeHtml(site.url)}</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = sitesHtml;
}

// 更新统计信息
function updateStatistics(sites) {
    const total = sites.length;
    const active = sites.filter(s => s.status === 'active').length;
    const international = sites.filter(s => s.category === 'international').length;
    const chinese = sites.filter(s => s.category === 'chinese').length;
    
    document.getElementById('totalSites').textContent = total;
    document.getElementById('activeSites').textContent = active;
    document.getElementById('internationalSites').textContent = international;
    document.getElementById('chineseSites').textContent = chinese;
}

// 显示加载状态
function showLoading() {
    document.getElementById('sitesContainer').innerHTML = `
        <div class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载网站配置...</p>
        </div>
    `;
}

// 显示错误信息
function showError(message) {
    document.getElementById('sitesContainer').innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${escapeHtml(message)}
        </div>
    `;
}

// 搜索网站
function searchSites() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    
    if (!searchTerm) {
        renderSites(currentSites);
        return;
    }
    
    const filteredSites = currentSites.filter(site => 
        site.name.toLowerCase().includes(searchTerm) ||
        site.domain.toLowerCase().includes(searchTerm) ||
        site.url.toLowerCase().includes(searchTerm) ||
        (site.description && site.description.toLowerCase().includes(searchTerm)) ||
        (site.tags && site.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
    );
    
    renderSites(filteredSites);
}

// 显示添加网站模态框
function showAddModal() {
    document.getElementById('siteModalTitle').textContent = '添加网站';
    document.getElementById('siteForm').reset();
    document.getElementById('siteId').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('siteModal'));
    modal.show();
}

// 编辑网站
async function editSite(siteId) {
    try {
        const response = await apiCall(`/api/config/sites/${siteId}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            const site = data.data;
            
            // 填充表单
            document.getElementById('siteModalTitle').textContent = '编辑网站';
            document.getElementById('siteId').value = site.id;
            document.getElementById('siteName').value = site.name;
            document.getElementById('siteDomain').value = site.domain;
            document.getElementById('siteUrl').value = site.url;
            document.getElementById('siteCategory').value = site.category;
            document.getElementById('siteLanguage').value = site.language;
            document.getElementById('siteStatus').value = site.status;
            document.getElementById('sitePriority').value = site.priority;
            document.getElementById('siteDescription').value = site.description || '';
            
            const modal = new bootstrap.Modal(document.getElementById('siteModal'));
            modal.show();
        } else {
            throw new Error(data.error || '获取网站信息失败');
        }
        
    } catch (error) {
        console.error('编辑网站失败:', error);
        showNotification('编辑网站失败: ' + error.message, 'error');
    }
}

// 保存网站
async function saveSite() {
    try {
        const siteId = document.getElementById('siteId').value;
        const isEdit = !!siteId;
        
        // 收集表单数据
        const formData = {
            name: document.getElementById('siteName').value.trim(),
            domain: document.getElementById('siteDomain').value.trim(),
            url: document.getElementById('siteUrl').value.trim(),
            category: document.getElementById('siteCategory').value,
            language: document.getElementById('siteLanguage').value,
            status: document.getElementById('siteStatus').value,
            priority: parseInt(document.getElementById('sitePriority').value),
            description: document.getElementById('siteDescription').value.trim()
        };
        
        // 验证必需字段
        if (!formData.name || !formData.domain || !formData.url) {
            showNotification('请填写所有必需字段', 'warning');
            return;
        }
        
        const url = isEdit ? `/api/config/sites/${siteId}` : '/api/config/sites';
        const method = isEdit ? 'PUT' : 'POST';
        
        const response = await apiCall(url, {
            method: method,
            body: JSON.stringify(formData)
        });

        const data = await response.json();

        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('siteModal'));
            modal.hide();

            // 重新加载列表
            await loadSites();

            showNotification(isEdit ? '网站更新成功' : '网站添加成功', 'success');
        } else {
            // 根据响应状态码显示不同的错误信息
            if (response.status === 400) {
                // 400错误通常是重复检查失败，显示服务器返回的具体错误信息
                showNotification(data.error || '保存失败', 'error');
            } else {
                // 其他错误
                showNotification(`保存失败: ${data.error || '未知错误'}`, 'error');
            }
            return;
        }
        
    } catch (error) {
        console.error('保存网站失败:', error);
        // 网络错误或其他异常
        showNotification('网络错误，请检查连接后重试', 'error');
    }
}

// 切换网站状态
async function toggleSiteStatus(siteId) {
    try {
        const response = await apiCall(`/api/config/sites/${siteId}/toggle`, {
            method: 'POST'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            await loadSites();
            showNotification('网站状态切换成功', 'success', 3000);
        } else {
            throw new Error(data.error || '状态切换失败');
        }

    } catch (error) {
        console.error('切换网站状态失败:', error);
        showNotification('切换网站状态失败: ' + error.message, 'error');
    }
}

// 查看网站详情
function viewSiteDetails(siteId) {
    const site = currentSites.find(s => s.id === siteId);
    if (!site) return;

    const detailsHtml = `
        <div class="table-responsive">
            <table class="table table-borderless">
                <tr><td><strong>名称:</strong></td><td>${escapeHtml(site.name)}</td></tr>
                <tr><td><strong>域名:</strong></td><td>${escapeHtml(site.domain)}</td></tr>
                <tr><td><strong>URL:</strong></td><td><a href="${ensureHttpProtocol(site.url)}" target="_blank" class="text-break">${escapeHtml(site.url)}</a></td></tr>
                <tr><td><strong>分类:</strong></td><td><span class="badge bg-${getCategoryColor(site.category)}">${getCategoryText(site.category)}</span></td></tr>
                <tr><td><strong>语言:</strong></td><td>${getLanguageText(site.language)}</td></tr>
                <tr><td><strong>状态:</strong></td><td><span class="badge bg-${getStatusColor(site.status)}">${getStatusText(site.status)}</span></td></tr>
                <tr><td><strong>优先级:</strong></td><td>${site.priority}</td></tr>
                ${site.description ? `<tr><td><strong>描述:</strong></td><td>${escapeHtml(site.description)}</td></tr>` : ''}
            </table>
        </div>
    `;

    // 创建临时模态框显示详情
    const modalHtml = `
        <div class="modal fade" id="detailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">网站详情 - ${escapeHtml(site.name)}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${detailsHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的详情模态框
    const existingModal = document.getElementById('detailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新的模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
    modal.show();

    // 模态框关闭后移除DOM元素
    document.getElementById('detailsModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 删除网站
function deleteSite(siteId, siteName) {
    deleteTargetId = siteId;
    document.getElementById('deleteConfirmText').textContent = `网站: ${siteName}`;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// 确认删除
async function confirmDelete() {
    if (!deleteTargetId) return;

    try {
        const response = await apiCall(`/api/config/sites/${deleteTargetId}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();

            // 重新加载列表
            await loadSites();

            showNotification('网站删除成功', 'success');
        } else {
            throw new Error(data.error || '删除失败');
        }

    } catch (error) {
        console.error('删除网站失败:', error);
        showNotification('删除网站失败: ' + error.message, 'error');
    } finally {
        deleteTargetId = null;
    }
}



// 工具函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function getPriorityColor(priority) {
    if (priority <= 2) return 'danger';
    if (priority <= 4) return 'warning';
    if (priority <= 6) return 'primary';
    return 'secondary';
}

function getCategoryColor(category) {
    const colors = {
        'international': 'primary',
        'chinese': 'success',
        'crawl_url': 'info'
    };
    return colors[category] || 'secondary';
}

function getCategoryText(category) {
    const texts = {
        'international': '国际网站',
        'chinese': '中文网站',
        'crawl_url': '爬取URL'
    };
    return texts[category] || category;
}

function getStatusColor(status) {
    const colors = {
        'active': 'success',
        'inactive': 'secondary'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'active': '活跃',
        'inactive': '停用'
    };
    return texts[status] || status;
}

function getLanguageText(language) {
    const texts = {
        'en': '英文',
        'zh': '中文',
        'mixed': '混合'
    };
    return texts[language] || language;
}

function getTypeText(type) {
    const texts = {
        'domain': '域名',
        'crawl_url': '爬取URL',
        'rss_feed': 'RSS源'
    };
    return texts[type] || type;
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// ==================== 批量操作相关函数 ====================

// 切换单个网站的选择状态
function toggleSiteSelection(siteId) {
    const checkbox = document.getElementById(`site-${siteId}`);
    if (checkbox.checked) {
        selectedSites.add(siteId);
    } else {
        selectedSites.delete(siteId);
    }
    updateSelectionUI();
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const siteCheckboxes = document.querySelectorAll('.site-checkbox');

    if (selectAllCheckbox.checked) {
        // 全选
        siteCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedSites.add(parseInt(checkbox.value));
        });
    } else {
        // 取消全选
        siteCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectedSites.clear();
    }
    updateSelectionUI();
}

// 清除选择
function clearSelection() {
    selectedSites.clear();
    document.getElementById('selectAll').checked = false;
    document.querySelectorAll('.site-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectionUI();
}

// 更新选择相关的UI
function updateSelectionUI() {
    const selectedCount = selectedSites.size;
    const totalVisible = document.querySelectorAll('.site-checkbox').length;

    // 更新选择计数
    document.getElementById('selectedCount').textContent = `已选择: ${selectedCount}`;

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectedCount === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (selectedCount === totalVisible) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }

    // 显示/隐藏批量操作按钮
    const batchOperationsRow = document.getElementById('batchOperationsRow');
    if (selectedCount > 0) {
        batchOperationsRow.style.display = 'block';
    } else {
        batchOperationsRow.style.display = 'none';
    }
}

// 批量切换状态
async function batchToggleStatus(newStatus) {
    if (selectedSites.size === 0) {
        showNotification('请先选择要操作的网站', 'warning');
        return;
    }

    const statusText = newStatus === 'active' ? '启用' : '停用';
    const selectedArray = Array.from(selectedSites);

    // 立即显示开始提示并禁用按钮
    showNotification(`开始批量${statusText} ${selectedArray.length} 个网站...`, 'info', 3000);
    setBatchButtonsDisabled(true);

    try {
        let successCount = 0;
        let failCount = 0;

        for (const siteId of selectedArray) {
            try {
                const response = await apiCall(`/api/config/sites/${siteId}/toggle`, {
                    method: 'POST'
                });

                const data = await response.json();
                if (data.success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (error) {
                failCount++;
                console.error(`切换网站${siteId}状态失败:`, error);
            }
        }

        // 重新加载列表
        await loadSites();

        // 清除选择
        clearSelection();

        // 显示最终结果
        if (failCount === 0) {
            showNotification(`成功${statusText} ${successCount} 个网站`, 'success');
        } else {
            showNotification(`${statusText}完成：成功 ${successCount} 个，失败 ${failCount} 个`, 'warning');
        }

    } catch (error) {
        console.error('批量切换状态失败:', error);
        showNotification('批量操作失败，请重试', 'error');
    } finally {
        // 重新启用按钮
        setBatchButtonsDisabled(false);
    }
}

// 显示批量删除确认对话框
function showBatchDeleteModal() {
    if (selectedSites.size === 0) {
        showNotification('请先选择要删除的网站', 'warning');
        return;
    }

    // 禁用批量删除按钮，防止重复点击
    setBatchButtonsDisabled(true);

    const selectedArray = Array.from(selectedSites);
    const selectedSiteData = currentSites.filter(site => selectedArray.includes(site.id));

    // 更新模态框内容
    document.getElementById('batchDeleteCount').textContent = selectedSites.size;

    const listHtml = selectedSiteData.map(site => `
        <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
            <div>
                <strong>${escapeHtml(site.name)}</strong>
                <br>
                <small class="text-muted">${escapeHtml(site.domain)}</small>
            </div>
            <span class="badge bg-${getCategoryColor(site.category)}">${getCategoryText(site.category)}</span>
        </div>
    `).join('');

    document.getElementById('batchDeleteList').innerHTML = listHtml;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
    modal.show();

    // 监听模态框关闭事件，重新启用按钮
    modal._element.addEventListener('hidden.bs.modal', function() {
        setBatchButtonsDisabled(false);
    }, { once: true });
}

// 确认批量删除
async function confirmBatchDelete() {
    const selectedArray = Array.from(selectedSites);

    // 立即显示开始提示并禁用确认按钮
    showNotification(`🗑️ 开始批量删除 ${selectedArray.length} 个网站...`, 'info', 3000);

    // 禁用确认按钮
    const confirmBtn = document.querySelector('#batchDeleteModal .btn-danger');
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';

    try {
        let successCount = 0;
        let failCount = 0;

        for (const siteId of selectedArray) {
            try {
                const response = await apiCall(`/api/config/sites/${siteId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();
                if (data.success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (error) {
                failCount++;
                console.error(`删除网站${siteId}失败:`, error);
            }
        }

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal'));
        modal.hide();

        // 重新加载列表
        await loadSites();

        // 清除选择
        clearSelection();

        // 显示最终结果
        if (failCount === 0) {
            showNotification(`成功删除 ${successCount} 个网站`, 'success');
        } else {
            showNotification(`删除完成：成功 ${successCount} 个，失败 ${failCount} 个`, 'warning');
        }

    } catch (error) {
        console.error('批量删除失败:', error);
        showNotification('批量删除失败，请重试', 'error');
    } finally {
        // 恢复确认按钮
        const confirmBtn = document.querySelector('#batchDeleteModal .btn-danger');
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = '<i class="bi bi-trash"></i> 确认批量删除';
    }
}

// 设置批量操作按钮的禁用状态
function setBatchButtonsDisabled(disabled) {
    const buttons = [
        'batchEnableBtn',
        'batchDisableBtn',
        'batchDeleteBtn',
        'batchCancelBtn'
    ];

    buttons.forEach(btnId => {
        const btn = document.getElementById(btnId);
        if (btn) {
            btn.disabled = disabled;
            if (disabled) {
                btn.classList.add('disabled');
                // 保存原始内容
                if (!btn.dataset.originalHtml) {
                    btn.dataset.originalHtml = btn.innerHTML;
                }
                // 显示加载状态
                if (btnId === 'batchEnableBtn') {
                    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 启用中...';
                } else if (btnId === 'batchDisableBtn') {
                    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 停用中...';
                } else if (btnId === 'batchDeleteBtn') {
                    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';
                } else if (btnId === 'batchCancelBtn') {
                    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
                }
            } else {
                btn.classList.remove('disabled');
                // 恢复原始内容
                if (btn.dataset.originalHtml) {
                    btn.innerHTML = btn.dataset.originalHtml;
                }
            }
        }
    });
}

// ==================== 导入导出功能 ====================

// 导出CSV
async function exportToCSV() {
    try {
        showNotification('📥 开始导出网站配置...', 'info', 3000);

        const response = await apiCall('/api/config/sites/export');

        if (response.ok) {
            // 创建下载链接
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'threat_intel_sites.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showNotification('网站配置导出成功', 'success');
        } else {
            throw new Error('导出失败');
        }
    } catch (error) {
        console.error('导出失败:', error);
        showNotification('导出失败，请重试', 'error');
    }
}

// 显示导入模态框
function showImportModal() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();

    // 重置表单
    document.getElementById('csvFile').value = '';
    document.getElementById('csvPreview').style.display = 'none';
    document.getElementById('importBtn').disabled = true;
}

// 预览CSV文件
function previewCSV() {
    const fileInput = document.getElementById('csvFile');
    const file = fileInput.files[0];

    if (!file) {
        document.getElementById('csvPreview').style.display = 'none';
        document.getElementById('importBtn').disabled = true;
        return;
    }

    if (!file.name.endsWith('.csv')) {
        showNotification('请选择CSV格式文件', 'warning');
        fileInput.value = '';
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        const lines = csv.split('\n');

        if (lines.length < 2) {
            showNotification('CSV文件格式错误：至少需要表头和一行数据', 'warning');
            return;
        }

        // 解析CSV
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const requiredHeaders = ['name', 'domain', 'url'];
        const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

        if (missingHeaders.length > 0) {
            showNotification(`CSV文件缺少必需列：${missingHeaders.join(', ')}`, 'warning');
            return;
        }

        // 显示预览
        let validCount = 0;
        let errorCount = 0;

        const headerHtml = '<tr>' + headers.map(h => `<th>${escapeHtml(h)}</th>`).join('') + '</tr>';
        let dataHtml = '';

        for (let i = 1; i < Math.min(lines.length, 11); i++) { // 最多显示10行数据
            if (lines[i].trim()) {
                const cells = lines[i].split(',').map(c => c.trim().replace(/"/g, ''));
                const isValid = cells[headers.indexOf('name')] &&
                               cells[headers.indexOf('domain')] &&
                               cells[headers.indexOf('url')];

                if (isValid) {
                    validCount++;
                } else {
                    errorCount++;
                }

                const rowClass = isValid ? '' : 'table-warning';
                dataHtml += `<tr class="${rowClass}">` +
                           cells.map(c => `<td>${escapeHtml(c || '')}</td>`).join('') +
                           '</tr>';
            }
        }

        document.getElementById('csvHeaders').innerHTML = headerHtml;
        document.getElementById('csvData').innerHTML = dataHtml;
        document.getElementById('csvRowCount').textContent = lines.length - 1;
        document.getElementById('csvValidCount').textContent = validCount;
        document.getElementById('csvErrorCount').textContent = errorCount;

        document.getElementById('csvPreview').style.display = 'block';
        document.getElementById('importBtn').disabled = validCount === 0;
    };

    reader.readAsText(file);
}

// 下载模板
function downloadTemplate() {
    const csvContent = 'name,domain,url,description\n' +
                      'Microsoft Security Blog,www.microsoft.com,https://www.microsoft.com/en-us/security/blog/topic/threat-intelligence/,微软安全博客威胁情报栏目\n' +
                      'Unit 42,unit42.paloaltonetworks.com,https://unit42.paloaltonetworks.com/,Palo Alto Networks威胁研究团队\n';

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'threat_intel_sites_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    showNotification('📄 模板文件下载成功', 'success');
}

// 确认导入
async function confirmImport() {
    const fileInput = document.getElementById('csvFile');
    const file = fileInput.files[0];

    if (!file) {
        showNotification('请选择要导入的文件', 'warning');
        return;
    }

    // 立即反馈并禁用按钮
    showNotification('开始批量导入网站配置...', 'info', 3000);
    const importBtn = document.getElementById('importBtn');
    importBtn.disabled = true;
    importBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导入中...';

    try {
        const formData = new FormData();
        formData.append('file', file);

        // 文件上传需要直接使用fetch，不能通过apiCall
        const response = await fetch('/api/config/sites/import', {
            method: 'POST',
            body: formData
            // 不设置Content-Type，让浏览器自动设置multipart/form-data
        });

        const data = await response.json();

        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
            modal.hide();

            // 重新加载列表
            await loadSites();

            // 显示结果
            if (data.data.error_count === 0) {
                showNotification(`导入成功：共导入 ${data.data.success_count} 个网站配置`, 'success');
            } else {
                showNotification(`导入完成：成功 ${data.data.success_count} 个，失败 ${data.data.error_count} 个`, 'warning', 8000);

                // 显示错误详情
                if (data.data.errors && data.data.errors.length > 0) {
                    console.log('导入错误详情:', data.data.errors);
                }
            }
        } else {
            showNotification(`导入失败：${data.error}`, 'error');
        }

    } catch (error) {
        console.error('导入失败:', error);
        showNotification('导入失败，请检查文件格式后重试', 'error');
    } finally {
        // 恢复按钮
        importBtn.disabled = false;
        importBtn.innerHTML = '<i class="bi bi-upload"></i> 开始导入';
    }
}

// ==================== 每周爬取任务功能 ====================

// 启动每周爬取任务
async function startWeeklyCrawl() {
    try {
        // 立即反馈并禁用按钮
        showNotification('正在启动每周爬取任务...', 'info', 3000);
        const weeklyBtn = document.getElementById('weeklyTaskBtn');
        weeklyBtn.disabled = true;
        weeklyBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>启动中...';

        const response = await apiCall('/api/config/sites/weekly-crawl', {
            method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
            showNotification('每周爬取任务已启动，请查看任务历史', 'success', 5000);

            // 清理任务历史缓存
            clearTaskHistoryCache();

            // 更新按钮为查看任务状态
            weeklyBtn.onclick = () => viewTaskHistory();
            weeklyBtn.innerHTML = '<i class="bi bi-list-task me-2"></i>查看任务';
            weeklyBtn.disabled = false;
            weeklyBtn.classList.remove('btn-outline-warning');
            weeklyBtn.classList.add('btn-outline-info');
            weeklyBtn.title = '查看任务历史';

            // 延迟强制刷新状态，确保按钮状态正确
            setTimeout(() => {
                checkWeeklyCrawlStatus(true); // 强制刷新
            }, 1000);

        } else {
            showNotification(`启动失败：${data.error}`, 'error');
            // 恢复按钮
            weeklyBtn.disabled = false;
            weeklyBtn.innerHTML = '<i class="bi bi-calendar-week me-2"></i>每周爬取';
        }

    } catch (error) {
        console.error('启动每周爬取任务失败:', error);
        showNotification('启动失败，请重试', 'error');

        // 恢复按钮
        const weeklyBtn = document.getElementById('weeklyTaskBtn');
        weeklyBtn.disabled = false;
        weeklyBtn.innerHTML = '<i class="bi bi-calendar-week me-2"></i>每周爬取';
    }
}

// 查看任务历史
async function viewTaskHistory() {
    try {
        // 直接在当前页面弹出任务历史模态框
        await showTaskHistoryModal();
    } catch (error) {
        console.error('显示任务历史失败:', error);
        // 如果弹窗失败，则跳转到分析页面
        window.location.href = '/analysis?show=tasks#task-history';
    }
}

// 任务历史缓存
let taskHistoryCache = null;
let taskHistoryCacheTime = 0;
const taskHistoryCacheTTL = 30000; // 30秒缓存

// 清理任务历史缓存
function clearTaskHistoryCache() {
    taskHistoryCache = null;
    taskHistoryCacheTime = 0;
    console.log('已清理任务历史缓存');
}

// 刷新当前任务历史模态框
async function refreshCurrentTaskHistory() {
    try {
        console.log('刷新当前任务历史数据');

        // 清理缓存，强制重新获取数据
        clearTaskHistoryCache();

        // 获取最新数据
        const response = await apiCall('/api/analysis/tasks?limit=20');
        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || '获取任务历史失败');
        }

        // 更新缓存
        taskHistoryCache = data;
        taskHistoryCacheTime = Date.now();

        // 更新当前模态框的内容
        const currentModal = document.querySelector('#taskHistoryModal');
        if (currentModal) {
            // 更新统计卡片
            const stats = {
                total: data.data.length,
                running: data.data.filter(t => t.task_status === 'running').length,
                pending: data.data.filter(t => t.task_status === 'pending').length,
                completed: data.data.filter(t => t.task_status === 'completed').length,
                failed: data.data.filter(t => t.task_status === 'failed').length
            };

            // 更新统计数字
            const statCards = currentModal.querySelectorAll('.card-body .fw-bold');
            if (statCards.length >= 5) {
                statCards[0].textContent = stats.total;
                statCards[1].textContent = stats.running;
                statCards[2].textContent = stats.pending;
                statCards[3].textContent = stats.completed;
                statCards[4].textContent = stats.failed;
            }

            // 更新表格内容
            const tbody = currentModal.querySelector('#taskTableBody');
            if (tbody) {
                tbody.innerHTML = createConfigTaskRows(data.data);
            }

            // 更新标题中的任务数量
            const titleElement = currentModal.querySelector('.modal-title + small');
            if (titleElement) {
                titleElement.textContent = `共 ${stats.total} 个任务`;
            }

            console.log(`任务历史已刷新，共 ${data.data.length} 条记录`);
            showNotification('任务历史已刷新', 'success', 2000);
        }

    } catch (error) {
        console.error('刷新任务历史失败:', error);
        showNotification('刷新失败: ' + error.message, 'error', 3000);
    }
}



// 显示任务历史模态框
async function showTaskHistoryModal() {
    try {
        let data;
        const currentTime = Date.now();

        // 检查缓存
        if (taskHistoryCache && (currentTime - taskHistoryCacheTime) < taskHistoryCacheTTL) {
            console.log('使用缓存的任务历史数据');
            data = taskHistoryCache;
        } else {
            // 获取任务历史数据（直接使用API，不依赖taskManager）
            const response = await apiCall('/api/analysis/tasks?limit=20');
            data = await response.json();

            if (!data.success) {
                throw new Error(data.error || '获取任务历史失败');
            }

            // 更新缓存
            taskHistoryCache = data;
            taskHistoryCacheTime = currentTime;
            console.log('更新任务历史缓存');
        }

        // 创建增强的任务历史模态框
        const modal = createEnhancedTaskHistoryModal(data.data);
        document.body.appendChild(modal);

        // 显示模态框
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // 模态框关闭时移除DOM元素
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });

    } catch (error) {
        console.error('显示任务历史模态框失败:', error);
        // 使用右上角通知而不是弹窗
        showNotification('获取任务历史失败: ' + error.message, 'error');
        throw error;
    }
}

// 创建增强的任务历史模态框
function createEnhancedTaskHistoryModal(tasks) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'taskHistoryModal';
    modal.tabIndex = -1;

    // 按状态分组统计
    const stats = {
        total: tasks.length,
        running: tasks.filter(t => t.task_status === 'running').length,
        pending: tasks.filter(t => t.task_status === 'pending').length,
        completed: tasks.filter(t => t.task_status === 'completed').length,
        failed: tasks.filter(t => t.task_status === 'failed').length
    };



    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-light">
                    <div>
                        <h5 class="modal-title mb-1">
                            <i class="bi bi-clock-history text-primary"></i> 任务历史
                        </h5>
                        <small class="text-muted">共 ${stats.total} 个任务</small>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <!-- 统计卡片 -->
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-primary fw-bold">${stats.total}</div>
                                    <small class="text-muted">总计</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-info fw-bold">${stats.running}</div>
                                    <small class="text-muted">运行中</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-warning fw-bold">${stats.pending}</div>
                                    <small class="text-muted">等待中</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-success fw-bold">${stats.completed}</div>
                                    <small class="text-muted">已完成</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-danger fw-bold">${stats.failed}</div>
                                    <small class="text-muted">失败</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <button class="btn btn-sm btn-outline-primary w-100" onclick="refreshCurrentTaskHistory();">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选器 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label small text-muted">按状态筛选</label>
                            <select class="form-select form-select-sm" id="taskStatusFilter" onchange="filterConfigTasks()">
                                <option value="">所有状态</option>
                                <option value="running">运行中</option>
                                <option value="pending">等待中</option>
                                <option value="completed">已完成</option>
                                <option value="failed">失败</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted">按类型筛选</label>
                            <select class="form-select form-select-sm" id="taskTypeFilter" onchange="filterConfigTasks()">
                                <option value="">所有类型</option>
                                <option value="batch_analysis">批量分析</option>
                                <option value="single_analysis">单个分析</option>
                                <option value="weekly_crawl">每周爬取</option>
                                <option value="batch_unanalyzed">批量未分析</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted">搜索任务</label>
                            <input type="text" class="form-control form-control-sm" id="taskSearchInput"
                                   placeholder="搜索任务ID或类型..." onkeyup="filterConfigTasks()">
                        </div>
                    </div>

                    <!-- 任务表格 -->
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-hover table-sm">
                            <thead class="table-light sticky-top">
                                <tr>
                                    <th style="width: 60px;">ID</th>
                                    <th style="width: 120px;">类型</th>
                                    <th style="width: 100px;">状态</th>
                                    <th style="width: 150px;">进度</th>
                                    <th style="width: 80px;">总数</th>
                                    <th style="width: 80px;">成功</th>
                                    <th style="width: 80px;">失败</th>
                                    <th style="width: 140px;">开始时间</th>
                                    <th style="width: 140px;">结束时间</th>
                                    <th style="width: 100px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="taskTableBody">
                                ${createConfigTaskRows(tasks)}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="modal-footer bg-light">
                    <small class="text-muted me-auto">
                        <i class="bi bi-info-circle"></i> 数据每30秒自动刷新
                    </small>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    return modal;
}

// 创建配置管理页面的任务行
function createConfigTaskRows(tasks) {
    return tasks.map(task => `
        <tr class="task-row" data-status="${task.task_status}" data-type="${task.task_type}" data-id="${task.id}">
            <td><span class="badge bg-light text-dark">${task.id}</span></td>
            <td><small>${getTaskTypeText(task.task_type)}</small></td>
            <td>${getTaskStatusBadge(task.task_status)}</td>
            <td>${getEnhancedProgressBar(task)}</td>
            <td><small class="text-muted">${task.total_count || 0}</small></td>
            <td><small class="text-success">${task.success_count || 0}</small></td>
            <td><small class="text-danger">${task.error_count || 0}</small></td>
            <td><small class="text-muted">${formatDateTime(task.start_time)}</small></td>
            <td><small class="text-muted">${task.end_time ? formatDateTime(task.end_time) : '-'}</small></td>
            <td>
                <button class="btn btn-sm btn-outline-info" onclick="viewTaskDetails(${task.id})" title="查看详情">
                    <i class="bi bi-eye"></i>
                </button>
                ${task.task_status === 'running' || task.task_status === 'pending' ? `
                <button class="btn btn-sm btn-outline-danger ms-1" onclick="stopTask(${task.id})" title="停止任务">
                    <i class="bi bi-stop"></i>
                </button>
                ` : ''}
            </td>
        </tr>
    `).join('');
}

// 筛选配置管理页面的任务
function filterConfigTasks() {
    // 使用任务历史模态框中的筛选器ID
    const statusFilter = document.getElementById('taskStatusFilter')?.value || '';
    const typeFilter = document.getElementById('taskTypeFilter')?.value || '';
    const searchInput = document.getElementById('taskSearchInput')?.value.toLowerCase() || '';

    console.log('筛选条件:', { statusFilter, typeFilter, searchInput });

    const rows = document.querySelectorAll('.task-row');
    console.log('找到任务行数:', rows.length);

    let visibleCount = 0;
    rows.forEach(row => {
        const status = row.dataset.status;
        const type = row.dataset.type;
        const id = row.dataset.id;
        const text = row.textContent.toLowerCase();

        const statusMatch = !statusFilter || status === statusFilter;
        const typeMatch = !typeFilter || type === typeFilter;
        const searchMatch = !searchInput || text.includes(searchInput) || id.includes(searchInput);

        if (statusMatch && typeMatch && searchMatch) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    console.log('筛选后可见任务数:', visibleCount);
}

// 保持原有的简单模态框作为备用
function createTaskHistoryModal(tasks) {
    return createEnhancedTaskHistoryModal(tasks);
}

// 获取任务类型文本
function getTaskTypeText(taskType) {
    const typeMap = {
        'batch_analysis': '批量分析',
        'single_analysis': '单个分析',
        'weekly_crawl': '每周爬取',
        'batch_unanalyzed': '批量未分析',
        'archivebox_analysis': 'ArchiveBox分析',
        'archivebox_archive_only': 'ArchiveBox归档',
        'test_task': '测试任务',
        'follow_up_analysis': '跟进分析'
    };
    return typeMap[taskType] || taskType;
}

// 获取增强的进度条
function getEnhancedProgressBar(task) {
    const progress = task.progress || 0;
    const completed = task.completed_count || 0;
    const total = task.total_count || 0;

    let progressClass = 'bg-primary';
    if (task.task_status === 'completed') progressClass = 'bg-success';
    else if (task.task_status === 'failed') progressClass = 'bg-danger';
    else if (task.task_status === 'pending') progressClass = 'bg-warning';

    return `
        <div class="progress" style="height: 20px;">
            <div class="progress-bar ${progressClass}" role="progressbar"
                 style="width: ${progress}%" aria-valuenow="${progress}"
                 aria-valuemin="0" aria-valuemax="100">
                <small>${completed}/${total}</small>
            </div>
        </div>
    `;
}

// 辅助函数
function getTaskStatusBadge(status) {
    const statusMap = {
        'pending': '<span class="badge bg-warning">等待中</span>',
        'running': '<span class="badge bg-primary">运行中</span>',
        'completed': '<span class="badge bg-success">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>',
        'cancelled': '<span class="badge bg-secondary">已取消</span>'
    };
    return statusMap[status] || `<span class="badge bg-light text-dark">${status}</span>`;
}

function getTaskTypeText(taskType) {
    const typeMap = {
        'batch_analysis': '批量分析',
        'single_analysis': '单个分析',
        'archivebox_analysis': 'ArchiveBox分析',
        'archivebox_archive_only': 'ArchiveBox归档',
        'batch_unanalyzed': '批量未分析',
        'test_task': '测试任务',
        'follow_up_analysis': '跟进分析',
        'weekly_crawl': '每周爬取'
    };
    return typeMap[taskType] || taskType;
}

function createProgressBar(progress) {
    const percentage = Math.round(progress);
    return `
        <div class="progress" style="height: 20px;">
            <div class="progress-bar" role="progressbar" style="width: ${percentage}%"
                 aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
                ${percentage}%
            </div>
        </div>
    `;
}

function formatDateTime(dateTime) {
    if (!dateTime) return '-';
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

async function viewTaskDetails(taskId) {
    try {
        // 先关闭任务历史模态框
        const taskHistoryModal = document.getElementById('taskHistoryModal');
        if (taskHistoryModal) {
            const historyBootstrapModal = bootstrap.Modal.getInstance(taskHistoryModal);
            if (historyBootstrapModal) {
                historyBootstrapModal.hide();
            }
        }

        // 获取任务详情
        const response = await apiCall(`/api/analysis/tasks/${taskId}`);
        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || '获取任务详情失败');
        }

        // 等待任务历史模态框完全关闭后再显示详情模态框
        setTimeout(() => {
            // 创建任务详情模态框
            const modal = createTaskDetailsModal(data.data);
            document.body.appendChild(modal);

            // 显示模态框
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // 模态框关闭时移除DOM元素
            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }, 300); // 等待300ms确保前一个模态框完全关闭

    } catch (error) {
        console.error('显示任务详情失败:', error);
        showNotification('获取任务详情失败: ' + error.message, 'error');
    }
}

// 创建任务详情模态框
function createTaskDetailsModal(task) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'taskDetailsModal';
    modal.tabIndex = -1;

    const statusBadge = getTaskStatusBadge(task.task_status);
    const progressBar = createProgressBar(task.progress || 0);

    // 格式化任务参数
    let taskParams = '';
    if (task.task_params) {
        try {
            const params = typeof task.task_params === 'string' ? JSON.parse(task.task_params) : task.task_params;
            taskParams = Object.entries(params).map(([key, value]) =>
                `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`
            ).join('');
        } catch (e) {
            taskParams = `<tr><td colspan="2">${task.task_params}</td></tr>`;
        }
    }

    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-info-circle me-2"></i>任务详情 - ID: ${task.id}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>任务ID:</strong></td>
                                    <td>${task.id}</td>
                                </tr>
                                <tr>
                                    <td><strong>任务类型:</strong></td>
                                    <td>${getTaskTypeText(task.task_type)}</td>
                                </tr>
                                <tr>
                                    <td><strong>状态:</strong></td>
                                    <td>${statusBadge}</td>
                                </tr>
                                <tr>
                                    <td><strong>创建者:</strong></td>
                                    <td>${task.created_by || '-'}</td>
                                </tr>
                                <tr>
                                    <td><strong>开始时间:</strong></td>
                                    <td>${formatDateTime(task.start_time)}</td>
                                </tr>
                                <tr>
                                    <td><strong>结束时间:</strong></td>
                                    <td>${formatDateTime(task.end_time)}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>总数量:</strong></td>
                                    <td>${task.total_count || 0}</td>
                                </tr>
                                <tr>
                                    <td><strong>已完成:</strong></td>
                                    <td>${task.completed_count || 0}</td>
                                </tr>
                                <tr>
                                    <td><strong>成功数:</strong></td>
                                    <td>${task.success_count || 0}</td>
                                </tr>
                                <tr>
                                    <td><strong>失败数:</strong></td>
                                    <td>${task.error_count || 0}</td>
                                </tr>
                                <tr>
                                    <td><strong>进度:</strong></td>
                                    <td>${progressBar}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    ${taskParams ? `
                    <div class="mt-3">
                        <h6><i class="bi bi-gear me-2"></i>任务参数</h6>
                        <table class="table table-sm table-striped">
                            ${taskParams}
                        </table>
                    </div>
                    ` : ''}

                    ${task.error_message ? `
                    <div class="mt-3">
                        <h6><i class="bi bi-exclamation-triangle me-2 text-danger"></i>错误信息</h6>
                        <div class="alert alert-danger">
                            ${task.error_message}
                        </div>
                    </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" onclick="backToTaskHistory()">
                        <i class="bi bi-arrow-left me-1"></i>返回任务历史
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    ${(task.task_status === 'running' || task.task_status === 'pending') ? `
                    <button type="button" class="btn btn-danger" onclick="stopTask(${task.id})">
                        <i class="bi bi-stop-fill me-1"></i>停止任务
                    </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    return modal;
}

// 从任务详情返回任务历史
function backToTaskHistory() {
    // 关闭任务详情模态框
    const taskDetailsModal = document.getElementById('taskDetailsModal');
    if (taskDetailsModal) {
        const detailsBootstrapModal = bootstrap.Modal.getInstance(taskDetailsModal);
        if (detailsBootstrapModal) {
            detailsBootstrapModal.hide();
        }
    }

    // 等待详情模态框关闭后重新打开任务历史
    setTimeout(() => {
        viewTaskHistory();
    }, 300);
}

// 停止单个任务
async function stopTask(taskId) {
    if (!confirm(`确定要停止任务 ${taskId} 吗？`)) {
        return;
    }

    try {
        const response = await apiCall(`/api/analysis/tasks/${taskId}/stop`, {
            method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
            showNotification('任务已停止', 'success');

            // 清理任务历史缓存
            clearTaskHistoryCache();

            // 关闭任务详情模态框
            const modal = document.getElementById('taskDetailsModal');
            if (modal) {
                const bootstrapModal = bootstrap.Modal.getInstance(modal);
                if (bootstrapModal) {
                    bootstrapModal.hide();
                }
            }

            // 刷新任务状态
            setTimeout(() => {
                checkWeeklyCrawlStatus();
                // 重新打开任务历史显示更新后的状态
                viewTaskHistory();
            }, 1000);
        } else {
            throw new Error(data.error || '停止任务失败');
        }

    } catch (error) {
        console.error('停止任务失败:', error);
        showNotification('停止任务失败: ' + error.message, 'error');
    }
}



// 检查每周爬取任务状态
async function checkWeeklyCrawlStatus(forceRefresh = false) {
    try {
        // 如果需要强制刷新，添加时间戳参数绕过缓存
        const url = forceRefresh
            ? `/api/config/sites/weekly-crawl/status?_t=${Date.now()}`
            : '/api/config/sites/weekly-crawl/status';

        const response = await apiCall(url);
        const data = await response.json();

        console.log('每周爬取状态检查结果:', data);

        const weeklyBtn = document.getElementById('weeklyTaskBtn');

        if (data.success && data.data.has_running_task) {
            // 如果有运行中或等待中的任务，更新按钮状态
            console.log(`检测到任务状态: ${data.data.status}, has_running_task: ${data.data.has_running_task}, 显示查看任务按钮`);
            weeklyBtn.onclick = () => viewTaskHistory();
            weeklyBtn.innerHTML = '<i class="bi bi-list-task me-2"></i>查看任务';
            weeklyBtn.classList.remove('btn-outline-warning');
            weeklyBtn.classList.add('btn-outline-info');
            weeklyBtn.title = `查看任务历史 (当前状态: ${data.data.status})`;
        } else {
            // 没有运行中的任务，显示启动按钮
            console.log(`没有运行中的任务，显示每周爬取按钮。has_running_task: ${data.data?.has_running_task}, status: ${data.data?.status}`);
            weeklyBtn.onclick = () => startWeeklyCrawl();
            weeklyBtn.innerHTML = '<i class="bi bi-calendar-week me-2"></i>每周爬取';
            weeklyBtn.classList.remove('btn-outline-info');
            weeklyBtn.classList.add('btn-outline-warning');
            weeklyBtn.title = '启动每周爬取任务';
        }
    } catch (error) {
        console.error('检查每周爬取任务状态失败:', error);
        // 出错时默认显示启动按钮
        const weeklyBtn = document.getElementById('weeklyTaskBtn');
        weeklyBtn.onclick = () => startWeeklyCrawl();
        weeklyBtn.innerHTML = '<i class="bi bi-calendar-week me-2"></i>每周爬取';
        weeklyBtn.classList.remove('btn-outline-info');
        weeklyBtn.classList.add('btn-outline-warning');
        weeklyBtn.title = '启动每周爬取任务';
    }
}

// ==================== 页面初始化 ====================

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSites();

    // 检查每周爬取任务状态
    checkWeeklyCrawlStatus();

    // 绑定分类标签点击事件
    document.querySelectorAll('.category-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有active类
            document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
            // 添加active类到当前标签
            this.classList.add('active');

            // 设置当前分类并重新加载
            currentCategory = this.dataset.category;
            loadSites();

            // 清除选择状态
            clearSelection();
        });
    });

    // 绑定状态筛选变化事件
    document.getElementById('statusFilter').addEventListener('change', function() {
        currentStatus = this.value;
        loadSites();

        // 清除选择状态
        clearSelection();
    });

    // 绑定搜索事件
    document.getElementById('searchInput').addEventListener('input', function() {
        // 清除选择状态
        clearSelection();
    });
});
