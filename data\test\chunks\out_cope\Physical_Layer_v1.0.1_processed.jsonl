{"chunk_id": "line-1", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 4", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-2", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "INTRODUCTION", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-3", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "This Knowledge Area is a review of the most relevant topics in wireless physical layer security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-4", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The physical phenomenon utilized by the techniques presented in this Knowledge Area is the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-5", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "radiation of electromagnetic waves. The frequencies considered hereinafter consist of the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-6", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "entire spectrum that ranges from a few Hertz to frequencies beyond those of visible light", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-7", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(optical spectrum). This Knowledge Area covers concepts and techniques that exploit the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-8", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "way these signals propagate through the air and other transmission media. It is organised", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-9", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "into sections that describe security mechanisms for wireless communication methods as", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-10", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "well as some implications of unintended radio frequency emanations.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-11", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Since most frequencies used for wireless communication reside in the radio frequency spec-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-12", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "trum and follow the well-understood laws of radio propagation theory, the majority of this", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-13", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Knowledge Area is dedicated to security concepts based on physical aspects of radio fre-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-14", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "quency transmission. The chapter therefore starts with an explanation of the fundamental", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-15", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "concepts and main techniques that were developed to make use of the wireless communi-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-16", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cation layer for conﬁdentiality, integrity, access control and covert communication. These", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-17", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "techniques mainly use properties of physical layer modulations and signal propagation to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-18", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "enhance the security of systems.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-19", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "After having presented schemes to secure the wireless channel, the Knowledge Area continues", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-20", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "with a review of security issues related to the wireless physical layer, focusing on those aspects", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-21", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "that make wireless communication systems different from wired systems. Most notably, signal", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-22", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "jamming, signal annihilation and jamming resilience. The section on jamming is followed", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-23", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by a review of techniques capable of performing physical device identiﬁcation (i.e., device", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-24", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ﬁngerprinting) by extracting unique characteristics from the device’s (analogue) circuitry.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-25", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Following this, the chapter continues to present approaches for performing secure distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-26", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "measurements and secure positioning based on electromagnetic waves. Protocols for dis-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-27", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tance measurements and positioning are designed in order to thwart threats on the physical", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-28", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "layer as well as the logical layer. Those attack vectors are covered in detail, together with", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-29", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "defense strategies and the requirements for secure position veriﬁcation.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-30", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Then, the Knowledge Area covers unintentional wireless emanations from devices such as", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-31", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "from computer displays and summarises wireless side-channel attacks studied in literature.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-32", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "This is followed by a review on spooﬁng of analogue sensors. Unintentional emissions are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-33", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in their nature different from wireless communication systems, especially because these", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-34", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "interactions are not structured. They are not designed to carry information, however, they also", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-35", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "make use of—or can be affected by—electromagnetic waves.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-36", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Finally, after having treated the fundamental concepts of wireless physical security, this", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-37", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Knowledge Area presents a selection of existing communication technologies and discusses", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-38", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "their security mechanisms. It explains design choices and highlights potential shortcomings", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-39", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "while referring to the principles described in the earlier sections. Included are examples from", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-40", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "near-ﬁeld communication and wireless communication in the aviation industry, followed by", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-41", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the security considerations of cellular networks. Security of global navigation systems and of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-42", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "terrestrial positioning systems is covered last since the security goals of such systems are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-43", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "different from communication systems and are mainly related to position spooﬁng resilience.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-44", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-45", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 3", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-46", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 5", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-47", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "CONTENT", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-49", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "PHYSICAL LAYER SCHEMES FOR CONFIDENTIALITY,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-50", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "INTEGRITY AND ACCESS CONTROL", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-51", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[1, 2, 3, 4, 5, 6]", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-52", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Securing wireless networks is challenging due to the shared broadcast medium which makes", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-53", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "it easy for remote adversaries to eavesdrop, modify and block the communication between", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-54", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "devices. However, wireless communication also offers some unique opportunities. Radio", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-55", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals are affected by reﬂection, diffraction, and scattering, all of which contribute to a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-56", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "complex multi-path behaviour of communicated signals. The channel response, as measured", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-57", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "at the receiver, can therefore be modelled as having frequency and position dependent random", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-58", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "components. In addition, within the short time span and in the absence of interference,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-59", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communicating parties will measure highly correlated channel responses. These responses", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-60", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "can therefore be used as shared randomness, unavailable to the adversary, and form a basis", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-61", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of secure communication.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-62", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "It should be noted that modern-day cryptography provides many different protocols to assure", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-63", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the conﬁdentiality, integrity and authenticity of data transmitted using radio signals. If the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-64", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communicating parties are associated with each other or share a mutual secret, cryptographic", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-65", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "protocols can effectively establish secure communication by making use of cryptographic", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-66", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "keying material. However, if mere information exchange is not the only goal of a wireless", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-67", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "system (e.g., in a positioning system), or if no pre-shared secrets are available, cryptographic", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-68", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "protocols operating at higher layers of the protocol stack are not sufﬁcient and physical-layer", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-69", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "constructs can be viable solutions. The main physical layer schemes are presented in the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-70", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "following sections.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-71", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "1.1", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-72", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Key Establishment based on Channel Reciprocity", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-73", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The physical-layer randomness of a wireless channel can be used to derive a shared secret.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-74", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "One of the main security assumptions of physical-layer key establishment schemes is that", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-75", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the attacker is located at least half a wavelength away from the communicating parties.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-76", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "According to wireless communication theory, it can be assumed that the attacker’s channel", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-77", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "measurements will be de-correlated from those computed by the communicating parties if", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-78", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "they are at least half a wavelength apart. The attacker will therefore likely not have access", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-79", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to the measured secret randomness. If the attacker injects signals during the key genera-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-80", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tion, the signal that it transmits will, due to channel distortions, be measured differently at", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-81", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communicating parties, resulting in key disagreement.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-82", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Physical layer key establishment schemes operate as follows. The communicating parties", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-83", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(Alice and Bob) ﬁrst exchange pre-agreed, non-secret, data packets. Each party then measures", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-84", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the channel response over the received packets. The key agreement is then typically executed", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-85", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in three phases.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-86", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Quantisation Phase: Alice and Bob create a time series of channel properties that are measured", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-87", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "over the received packets. Example properties include RSSI and the CIR. Any property that", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-88", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "is believed to be non-observable by the attacker can be used. The measured time series are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-89", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "then quantised by both parties independently. This quantisation is typically based on ﬁxed or", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-90", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-91", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 4", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-92", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 6", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-93", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "dynamic thresholds.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-94", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Information reconciliation phase: Since the quantisation phase is likely to result in disagreeing", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-95", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sequences at Alice and Bob, they need to reconcile their sequences to correct for any errors.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-96", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "This is typically done leveraging error correcting codes and privacy ampliﬁcation techniques.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-97", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Most schemes use simple level-crossing algorithms for quantisation and do not use coding", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-98", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "techniques. However, if the key derivation uses methods based on channel states whose", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-99", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distributions are not necessarily symmetric, more sophisticated quantisation methods, such", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-100", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as approximating the channel fading phenomena as a Gaussian source, or (multi-level) coding", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-101", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "is needed [2].", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-102", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Key Veriﬁcation Phase: In this last phase, communicating parties conﬁrm that they established", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-103", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "a shared secret key. If this step fails, the parties need to restart key establishment.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-104", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Most of the research in physical-layer techniques has been concerned with the choice of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-105", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "channel properties and of the quantisation technique. Even if physical-layer key establishment", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-106", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "techniques seem attractive, many of them have been shown to be vulnerable to active, physi-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-107", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cally distributed and multi-antenna adversaries. However, in a number of scenarios where the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-108", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "devices are mobile, and where the attacker is restricted, they can be a valuable replacement", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-109", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "or enhancement to traditional public-key key establishment techniques.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-110", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "1.2", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-111", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "MIMO-supported Approaches: Orthogonal Blinding, Zero-Forcing", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-112", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Initially, physical-layer key establishment techniques were proposed in the context of single-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-113", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "antenna devices. However, with the emergence of MIMO devices and beam-forming, re-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-114", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "searchers have proposed to leverage these new capabilities to further secure communication.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-115", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Two basic techniques that were proposed in this context are orthogonal blinding and zero", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-116", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "forcing. Both of these techniques aim to enable the transmitter to wirelessly send conﬁdential", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-117", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "data to the intended receiver, while preventing the co-located attacker from receiving this", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-118", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "data. Although this might seem infeasible, since as well as the intended receiver, the attacker", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-119", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "can receive all transmitted packets. However, MIMO systems allow transmitters to ’steer’", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-120", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the signal towards the intended receiver. For beam-forming to be effective, the transmitter", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-121", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "needs to know some channel information for the channels from its antennas to the antennas", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-122", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of the receiver. As described in [5], these channels are considered to be secret from the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-123", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker. In Zero-Forcing, the transmitter knows the channels to the intended receiver as", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-124", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "well as to the attacker. This allows the transmitter to encode the data such that it can be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-125", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "measured at the receiver, whereas the attacker measures nothing related to the data. In", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-126", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "many scenarios, assuming the knowledge of the channel to the attackers is unrealistic. In", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-127", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Orthogonal Blinding, the transmitter doesn’t know the channel to the attacker, but knows the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-128", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "channels to the receiver. The transmitter then encodes the data in the way that the receiver", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-129", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "can decode the data, whereas the attacker will receive data mixed with random noise. The", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-130", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker therefore cannot decode the data. In order to communicate securely, the transmitter", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-131", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and the receiver do not need to share any secrets. Instead, the transmitter only needs to know", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-132", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(or measure) the channels to the intended receivers. Like physical-layer key establishment", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-133", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "techniques, these techniques have been show to be vulnerable to multi-antenna and physically", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-134", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distributed attackers. They were further shown to be vulnerable to known-plaintext attacks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-135", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-136", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 5", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-137", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 7", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-138", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "1.3", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-139", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Secrecy Capacity", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-140", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Secrecy capacity is an information-theoretical concept that attempts to determine the maximal", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-141", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "rate at which a wireless channel can be used to transmit conﬁdential information without", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-142", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "relying on higher-layer encryption, even if there is an eavesdropper present. A famous result", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-143", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by Shannon [7] says that, for an adversary with unbounded computing power, unconditionally", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-144", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "secure transmission can only be achieved if a one-time-pad cipher is used to encrypt the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-145", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "transmitted information. However, Wyner later showed that if the attacker’s channel slightly", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-146", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "degrades the information, that is, the channel is noisy, the secrecy capacity can indeed be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-147", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "positive under certain conditions [8]. This means it is possible to convey a secret message", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-148", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "without leaking any information to an eavesdropper. Csiszár and Korner extended Wyner’s", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-149", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "result by showing that the secrecy capacity is non-zero, unless the adversary’s channel (wiretap", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-150", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "channel) is less noisy than the channel that carries the message from the legitimate transmitter", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-151", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to the receiver [9]. These theoretical results have been reﬁned for concrete channel models by", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-152", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "assuming a certain type of noise (e.g., Gaussian) and channel layout (e.g., SIMO and MIMO).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-153", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Researchers have managed to derive explicit mathematical expressions and bounds even", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-154", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "when taking into account complex phenomena such as fading which is present in wireless", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-155", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "channels [10].", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-156", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "A practical implementation of the concept of secrecy capacity can mainly be achieved using", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-157", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the two methods described above. Either the communicating parties establish a secret key by", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-158", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "extracting features from the wireless channel (see 1.1) or they communicate with each other", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-159", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "using intelligent coding and transmission strategies possibly relying on multiple antennas", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-160", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(see 1.2). Therefore, the study of secrecy capacity can be understood as the information-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-161", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "theoretical framework for key establishment and MIMO-supported security mechanisms in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-162", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the context of wireless communication.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-163", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "1.4", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-164", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Friendly Jamming", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-165", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Similar to Orthogonal Blinding, Friendly Jamming schemes use signal interference generated", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-166", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by collaborating devices to either prevent an attacker from communicating with the protected", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-167", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "device, or to prevent the attacker from eavesdropping on messages sent by protected devices.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-168", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Friendly Jamming can therefore be used for both conﬁdentiality and access control. Unlike", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-169", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Orthogonal Blinding, Friendly Jamming doesn’t leverage the knowledge of the channel to the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-170", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver. If a collaborating device (i.e., the friendly jammer) wants to prevent unauthorised", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-171", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communication with the protected device it will jam the receiver of the protected device. If it", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-172", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "wants to prevent eavesdropping, it will transmit jamming signals in the vicinity of the protected", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-173", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "device. Preventing communication with a protected device requires no special assumptions", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-174", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on the location of the collaborating devices. However, protecting against eavesdropping", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-175", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "requires that the eavesdropper is unable to separate the signals from the protected device", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-176", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "from those originating at the collaborating device. For this to hold, the channel from the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-177", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "protected device to the attacker should not be correlated to the channel from the collaborating", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-178", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "device to the attacker. To ensure this, the protected device and the collaborating device need", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-179", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to be typically placed less than half a carrier wavelength apart. This assumption is based on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-180", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the fact that, in theory, an attacker with multiple antennas who tries to tell apart the jamming", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-181", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal from the target signal requires the two transmitters to be separated by more than half a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-182", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "wavelength. However, signal deterioration is gradual and it has been shown that under some", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-183", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "conditions, a multi-antenna attacker will be able to separate these signals and recover the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-184", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "transmitted messages.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-185", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-186", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 6", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-187", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 8", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-188", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Friendly jamming was originally proposed for the protection of those medical implants (e.g.,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-189", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "already implanted pacemakers) that have no abilities to perform cryptographic operations.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-190", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The main idea was that the collaborating device (i.e. ’the shield’) would be placed around the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-191", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "user’s neck, close to the pacemaker. This device would then simultaneously receive and jam", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-192", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "all communication from the implant. The shield would then be able to forward the received", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-193", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "messages to any other authorised device using standard cryptographic techniques.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-194", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "1.5", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-195", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Using Physical Layer to Protect Data Integrity", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-196", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Research into the use of physical layer for security is not only limited to the protection of data", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-197", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "conﬁdentiality. Physical layer can also be leveraged to protect data integrity. This is illustrated", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-198", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by the following scenario. Assuming that two entities (Alice and Bob) share a common radio", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-199", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communication channel, but do not share any secrets or authentication material (e.g., shared", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-200", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "keys or authenticated public keys), how can the messages exchanged between these entities", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-201", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "be authenticated and how can their integrity be preserved in the presence of an attacker? Here,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-202", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by message integrity, we mean that the message must be protected against any malicious", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-203", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "modiﬁcation, and by message authentication we mean that it should be clear who is the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-204", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sender of the message.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-205", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "One basic technique that was proposed in this context is integrity codes, a modulation scheme", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-206", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "that provides a method of ensuring the integrity (and a basis for authentication) of a message", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-207", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "transmitted over a public channel. Integrity codes rely on the observation that, in a mobile", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-208", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "setting and in a multi-path rich environment, it is hard for the attacker to annihilate randomly", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-209", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "chosen signals.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-210", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Integrity codes assume a synchronised transmission between the transmitter and a receiver,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-211", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as well as the receiver being aware that it is in the range of the transmitter. To transmit", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-212", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "a message, the sender encodes the binary message using a unidirectional code (e.g., a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-213", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Manchester code), resulting in a known ration of 1s and 0s within an encoded message (for", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-214", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Manchester code, the number of 1s and 0s will be equal). This encoded message is then", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-215", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "transmitted using on-off keying, such that each 0 is transmitted as an absence of signal and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-216", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "each 1 as a random signal. To decode the message and check its integrity, the receiver simply", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-217", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "measures the energy of the signal. If the energy in a time slot is above a ﬁxed threshold, the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-218", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "bit is interpreted as a 1 and if it is below a threshold, it is interpreted as a 0. If the ratio of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-219", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "bits 1 and 0 corresponds to the encoding scheme, the integrity of the message is validated.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-220", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Integrity codes assume that the receiver knows when the transmitter is transmitting. This", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-221", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "means that their communication needs to be scheduled or the transmitter needs to always be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-222", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "transmitting.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-223", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "1.6", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-224", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Low probability of intercept and Covert Communication", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-225", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "LPI signals are such signals that are difﬁcult to detect for the unintended recipient. The", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-226", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "simplest form of LPI is communication at a reduced power and with high directionality. Since", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-227", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "such communication limits the range and the direction of communication, more sophisticated", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-228", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "techniques were developed: Frequency Hopping, Direct Sequence Spread Spectrum and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-229", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Chirping. In Frequency Hopping the sender and the receiver hop between different frequency", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-230", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "channels thus trying to avoid detection. In Direct Sequence Spread Spectrum the information", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-231", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal is modulated with a high rate (and thus high bandwidth) digital signal, thus spreading", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-232", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "across a wide frequency band. Finally, Chirps are high speed frequency sweeps that carry", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-233", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "information. The hopping sequence or chirp sequence constitute a secret shared between", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-234", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-235", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 7", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-236", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 9", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-237", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver and transmitter. This allows the legitimate receiver to recombine the signal while an", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-238", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "eavesdropper is unable to do so.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-239", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Covert communication is parasitic and leverages legitimate and expected transmissions", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-240", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to enable unobservable communication. Typically, such communication hides within the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-241", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "expected and tolerated deviations of the signal from its nominal form. One prominent example", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-242", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "is embedding of communicated bits within the modulation errors.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-244", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "JAMMING AND JAMMING-RESILIENT COMMUNICATION", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-245", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[11, 12]", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-246", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Communication jamming is an interference that prevents the intended receiver(s) from suc-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-247", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cessfully recognising and decoding the transmitted message. It happens when the jammer", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-248", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "injects a signal which, when combined with the legitimate transmission, prevents the re-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-249", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ceiver from extracting the information contained in the legitimate transmission. Jamming", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-250", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "can be surgical and affect only the message preamble thus preventing decoding, or can be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-251", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "comprehensive and aim to affect every symbol in the transmission.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-252", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Depending on their behaviour, jammers can be classiﬁed as constant or reactive. Constant", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-253", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "jammers transmit permanently, irrespective of the legitimate transmission. Reactive jammers", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-254", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "are most agile as they sense for transmission and then jam. This allows them to save energy", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-255", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as well as to stay undetected. Jammer strength is typically expressed in terms of their output", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-256", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "power and their effectiveness as the jamming-to-signal ratio at the receiver. Beyond a certain", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-257", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "jamming-to-signal ratio, the receiver will not be able to decode the information contained in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-258", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the signal. This ratio is speciﬁc to particular receivers and communication schemes. The main", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-259", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "parameters that inﬂuence the success of jamming are transmission power of the jammer", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-260", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and benign transmitter, their antenna gains, communication frequency, and their respective", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-261", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distances to the benign receiver. These parameters will determine the jamming-to-signal ratio.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-262", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Countermeasures against jamming involve concealing from the adversary which frequencies", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-263", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "are used for communication at which time. This uncertainty forces the adversary to jam a wider", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-264", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "portion of the spectrum and therefore weakens their impact on the legitimate transmission,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-265", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "effectively reducing the jamming-to-signal ratio. Most common techniques include Chirp,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-266", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "FHSS and DSSS. Typically, these techniques rely on pre-shared secret keys, in which case we", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-267", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "call the communication ’coordinated’. Recently, to enable jamming resilience in scenarios in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-268", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "which keys cannot be pre-shared (e.g., broadcast), uncoordinated FHSS and DSSS schemes", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-269", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "were also proposed.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-270", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2.1", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-271", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Coordinated Spread Spectrum Techniques", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-272", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Coordinated Spread Spectrum techniques are prevalent jamming countermeasures in a num-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-273", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ber of civilian and military applications. They are used not only to increase resilience to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-274", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "jamming, but also to cope with interference from neighboring devices. Spreading is used in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-275", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "practically all wireless communication technologies, in e.g.,802.11, cellular, Bluetooth, global", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-276", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "satellite positioning systems.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-277", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Spread spectrum techniques are typically effective against jammers that cannot cover the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-278", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "entire communication spectrum at all times. These techniques make a sender spread a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-279", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal over the entire available band of radio frequencies, which might require a considerable", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-280", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-281", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 8", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-282", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 10", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-283", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "M := m, sig(m), …", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-284", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "M1 M2", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-294", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Figure 1: In UFH, the fragment linking protect against message insertion attack.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-295", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "amount of energy. The attacker’s ability to impact the transmission is limited by the achieved", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-296", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "processing gain of the spread-spectrum communication. This gain is the ratio by which", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-297", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "interference can be suppressed relative to the original signal, and is computed as a ratio of the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-298", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "spread signal radio frequency bandwidth to the un-spread information (baseband) bandwidth.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-299", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Spread-spectrum techniques use randomly generated sequences to spread information sig-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-300", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "nals over a wider band of frequencies. The resulting signal is transmitted and then de-spread", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-301", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "at the receivers by correlating it with the spreading sequence. For this to work, it is essential", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-302", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "that the transmitter and receiver share the same secret spreading sequence. In FHSS, this", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-303", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sequence is the set of central frequencies and the order in which the transmitter and receiver", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-304", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "switch between them in synchrony. In DSSS, the data signal is modulated with the spreading", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-305", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sequence; this process effectively mixes the carrier signal with the spreading sequence, thus", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-306", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "increasing the frequency bandwidth of the transmitted signal. This process allows for both", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-307", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "narrow band and wide band jamming to be suppressed at the receiver. Unless the jammer", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-308", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "can guess the spreading code, its jamming signal will be spread at the receiver, whereas", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-309", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the legitimate transmission will be de-spread, allowing for its detection. The secrecy of the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-310", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "spreading codes is therefore crucial for the jamming resilience of spread spectrum systems.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-311", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "This is why a number of civilian systems that use spreading with public spreading codes, such", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-312", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as the GPS and 802.11b, remain vulnerable to jamming.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-313", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2.2", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-314", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Uncoordinated Spread Spectrum Techniques", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-315", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "In broadcast applications and in applications in which communication cannot be anticipated", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-316", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as scheduled, there is still a need to protect such communication from jamming.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-317", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "To address such scenarios, uncoordinated spread spectrum techniques were proposed: UFH", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-318", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and UDSSS. These techniques enable anti-jamming broadcast communication without pre-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-319", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "shared secrets. uncoordinated frequency hopping relies on the fact that even if the sender", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-320", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "hops in a manner that is not coordinated with the receiver, the throughput of this channel will", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-321", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "be non-zero. In fact, if the receiver is broadband, it can recover all the messages transmitted by", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-322", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the sender. UFH however, introduces new challenges. Given that the sender and the receiver", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-323", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "are not synchronised, and short message fragments transmitted within each hop are not", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-324", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "authenticated, the attacker can inject fragments that make the reassembly of the packets", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-325", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "infeasible. To prevent this, UFH includes fragment linking schemes that make this reassembly", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-326", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "possible even under poisoning.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-327", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "UDSSS follows the principle of DSSS in terms of spreading the data using spreading sequences.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-328", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "However, in contrast to anti-jamming DSSS where the spreading sequence is secret and shared", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-329", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "exclusively by the communication partners, in UDSSS, a public set of spreading sequences is", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-330", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "used by the sender and the receivers. To transmit a message, the sender repeatedly selects a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-331", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-332", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 9", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-333", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 11", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-334", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "fresh, randomly selected spreading sequence from the public set and spreads the message", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-335", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "with this sequence. Hence, UDSSS neither requires message fragmentation at the sender", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-336", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "nor message reassembly at the receivers. The receivers record the signal on the channel", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-337", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and despread the message by applying sequences from the public set, using a trial-and-error", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-338", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "approach. The receivers are not synchronised to the beginning of the sender’s message", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-339", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and thus record for (at least) twice the message transmission time. After the sampling, the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-340", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver tries to decode the data in the buffer by using code sequences from the set and by", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-341", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "applying a sliding-window protocol.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-342", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2.3", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-343", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Signal Annihilation and Overshadowing", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-344", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Unlike jamming where the primary goal of the attacker is to prevent information from being", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-345", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "decoded at the receiver, signal annihilation suppresses the signal at the receiver by introducing", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-346", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "destructive interference. The attacker’s goal is to insert a signal which cancels out the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-347", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "legitimate transmitter’s signal at the antenna of the receiver. This typically means that the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-348", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker will generate a signal identical to the legitimate transmission only with a different", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-349", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "polarity. Jamming attacks typically increase the energy on the channel and thus are more", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-350", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "easily detected than signal annihilation which reduces the energy typically below the threshold", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-351", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of signal detection.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-352", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The goal of overshadowing is similar to jamming and signal annihilation in the sense that the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-353", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker aims to prevent the receiver from decoding a legitimate signal. However, instead", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-354", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of interfering with the signal by adding excessive noise to the channel or cancelling out the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-355", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal (i.e., signal annihilation), the attacker emits their own signal at the same time and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-356", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "overshadows the legitimate signal. As a result, the receiver only registers the adversarial", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-357", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal which is often orders of magnitude higher in amplitude than the legitimate signal.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-358", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Practical overshadowing attacks were shown to be effective against QPSK modulation [13]", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-359", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and more recently against cellular LTE systems [14].", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-360", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Malicious signal overshadowing can not only deceive the receiver into decoding different data", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-361", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "than intended, it can also be used to alter any physical properties the receiver may extract", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-362", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "during signal reception, such as angle of arrival or time of arrival. Overshadowing attacks have", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-363", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "been shown to be particularly effective against systems that rely on physical layer properties", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-364", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "including positioning and ranging systems.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-366", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "PHYSICAL-LAYER IDENTIFICATION", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-367", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[15]", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-368", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Physical-Layer Identiﬁcation techniques enable the identiﬁcation of wireless devices by unique", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-369", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "characteristics of their analogue (radio) circuitry; this type of identiﬁcation is also referred to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-370", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as Radio Fingerprinting. More precisely, physical-layer device identiﬁcation is the process of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-371", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ﬁngerprinting the analogue circuitry of a device by analysing the device’s communication at", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-372", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the physical layer for the purpose of identifying a device or a class of devices. This type of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-373", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "identiﬁcation is possible due to hardware imperfections in the analogue circuitry introduced at", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-374", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the manufacturing process. These imperfections are remotely measurable as they appear in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-375", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the transmitted signals. While more precise manufacturing and quality control could minimise", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-376", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "such artefacts, it is often impractical due to signiﬁcantly higher production costs.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-377", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-378", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 10", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-379", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 12", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-380", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Physical-layer device identiﬁcation systems aim at identifying (or verifying the identity of)", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-381", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "devices or their afﬁliation classes, such as their manufacturer. Such systems can be viewed", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-382", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as pattern recognition systems typically composed of: an acquisition setup to acquire signals", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-383", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "from devices under identiﬁcation, also referred to as identiﬁcation signals, a feature extraction", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-384", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "module to obtain identiﬁcation-relevant information from the acquired signals, also referred", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-385", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to as ﬁngerprints, and a ﬁngerprint matcher for comparing ﬁngerprints and notifying the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-386", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "application system requesting the identiﬁcation of the comparison results. Typically, there are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-387", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "two modules in an identiﬁcation system: one for enrollment and one for identiﬁcation. During", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-388", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "enrollment, signals are captured either from each device or each (set of) class-representative", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-389", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "device(s) considered by the application system. Fingerprints obtained from the feature", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-390", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "extraction module are then stored in a database (each ﬁngerprint may be linked with some form", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-391", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of unique ID representing the associated device or class). During identiﬁcation, ﬁngerprints", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-392", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "obtained from the devices under identiﬁcation are compared with reference ﬁngerprints stored", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-393", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "during enrollment. The task of the identiﬁcation module can be twofold: either recognise", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-394", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(identify) a device or its afﬁliation class from among many enrolled devices or classes (1:N", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-395", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "comparisons), or verify that a device identity or class matches a claimed identity or class (1:1", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-396", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "comparison).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-397", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The identiﬁcation module uses statistical methods to perform the matching of the ﬁngerprints.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-398", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "These methods are classiﬁers trained with Machine Learning techniques during the enrollment", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-399", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "phase. If the module has to verify a 1:1 comparison, the classiﬁer is referred to as binary. It", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-400", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tries to verify a newly acquired signal against a stored reference pattern established during", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-401", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "enrollment. If the classiﬁer performs a 1:N comparison, on the other hand, it attempts to ﬁnd", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-402", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the reference pattern in a data base which best matches with the acquired signal. Often, these", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-403", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "classiﬁers are designed to return a list of candidates ranked according to a similarity metric", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-404", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "or likelihood that denotes the conﬁdence for a match.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-405", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "3.1", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-406", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Device under Identiﬁcation", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-407", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Physical-layer device identiﬁcation is based on ﬁngerprinting the analogue circuitry of devices", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-408", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by observing their radio communication. Consequently, any device that uses radio communi-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-409", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cation may be subject to physical-layer identiﬁcation. So far, it has been shown that a number", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-410", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of devices (or classes of devices) can be identiﬁed using physical-layer identiﬁcation. These", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-411", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "include analogue VHF, Bluetooth, WiFi, RFID and other radio transmitters.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-412", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Although what enables a device or a class of devices to be uniquely identiﬁed among other", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-413", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "devices or classes of devices is known to be due to imperfections introduced at the manufac-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-414", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "turing phase of the analogue circuitry, the actual device’s components causing these have", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-415", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "not always been clearly identiﬁed in all systems. For example, VHF identiﬁcation systems are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-416", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "based on the uniqueness of transmitters’ frequency synthesisers (local oscillators), while in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-417", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "RFID systems some studies only suggested that the proposed identiﬁcation system may rely", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-418", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on imperfections caused by the RFID device’s antennas and charge pumps. Identifying the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-419", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "exact components may become more difﬁcult when considering relatively-complex devices. In", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-420", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "these cases, it is common to identify in the whole analogue circuitry, or in a speciﬁc sub-circuit,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-421", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the cause of imperfections. For example, IEEE 802.11 transceivers were identiﬁed considering", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-422", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "modulation-related features; the cause of hardware artefacts can be then located in the mod-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-423", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ulator subcircuit of the transceivers. Knowing the components that make devices uniquely", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-424", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "identiﬁable may have relevant implications for both attacks and applications, which makes", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-425", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the investigation of such components an important open problem and research direction.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-426", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-427", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 11", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-428", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 14", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-429", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "5. Robustness. Fingerprints should not be subject, or at least, they should be evaluated with", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-430", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "respect to external environmental aspects that directly inﬂuence the collected signal like", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-430", "line_number": 430, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-431", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "radio interference due to other radio signals, surrounding materials, signal reﬂections,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-432", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "absorption, etc., as well as positioning aspects like the distance and orientation between", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-433", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the devices under identiﬁcation and the identiﬁcation system. Furthermore, ﬁngerprints", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-434", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "should be robust to device-related aspects like temperature, voltage level, and power", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-435", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "level. Many types of robustness can be acceptable for a practical identiﬁcation system.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-436", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Generally, obtaining robust features helps in building more reliable identiﬁcation systems.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-437", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "6. Data-Dependency. Fingerprints can be obtained from features extracted from a speciﬁc", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-438", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "bit pattern (data-related part of the identiﬁcation signal) transmitted by a device under", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-439", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "identiﬁcation (e.g., the claimed ID sent in a packet frame). This dependency has partic-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-440", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ularly interesting implications if the ﬁngerprints can be associated with both devices", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-441", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and data transmitted by those devices. This might strengthen authentication and help", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-442", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "prevent replay attacks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-443", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "3.4", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-444", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Attacks on Physical Layer Identiﬁcation", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-445", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The large majority of research works have focused on exploring feature extraction and match-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-446", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ing techniques for physical-layer device identiﬁcation. Only recently the security of these", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-447", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "techniques started being addressed. Different studies showed that their identiﬁcation system", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-448", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "may be vulnerable to hill-climbing attacks if the set of signals used for building the device", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-449", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ﬁngerprint is not carefully chosen. This attack consists of repeatedly sending signals to the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-450", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "device identiﬁcation system with modiﬁcations that gradually improve the similarity score be-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-451", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tween these signals and a target genuine signal. They also demonstrated that transient-based", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-452", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "approaches could easily be disabled by jamming the transient part of the signal while still", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-453", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "enabling reliable communication. Furthermore, impersonation attacks on modulation-based", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-454", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "identiﬁcation techniques were developed and showed that low-cost software-deﬁned radios", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-455", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as well as high end signal generators could be used to reproduce modulation features and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-456", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "impersonate a target device with a success rate of 50-75%. Modulation-based techniques are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-457", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "vulnerable to impersonation with high accuracy, while transient-based techniques are likely to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-458", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "be compromised only from the location of the target device. The authors pointed out that this", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-459", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "is mostly due to presence of wireless channel effects in the considered device ﬁngerprints;", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-460", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "therefore, the channel needed to be taken into consideration for successful impersonation.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-461", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Generally, these attacks can be divided into two groups: signal re(P)lay and feature replay", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-462", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacks. In a signal replay attack, the attacker’s goal is to observe analogue identiﬁcation", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-463", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals of a target device, capture them in a digital form (digital sampling), and then transmit", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-464", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(replay) these signals towards the identiﬁcation system by some appropriate means. The", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-465", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker does not modify the captured identiﬁcation signals, that is, the analogue signal and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-466", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the data payload are preserved. This attack is similar to message replay in the Dolev-Yao", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-467", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "model in which an attacker can observe and manipulate information currently in the air at", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-468", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "will. Unlike in signal replay attacks, where the goal of the attack is to reproduce the captured", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-469", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "identiﬁcation signals in their entirety, feature replay attack creates, modiﬁes or composes", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-470", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "identiﬁcation signals that reproduce only the features considered by the identiﬁcation system.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-471", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The analogue representation of the forged signals may be different, but the features should", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-472", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "be the same (or at the least very similar).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-473", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-474", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 13", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-475", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 16", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-476", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "4.2", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-477", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Distance Measurement Techniques", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-478", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Establishing proximity requires estimating the physical distance between two or more wire-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-479", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "less entities. Typically, the distance is estimated either by observing the changes in the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-480", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal’s physical properties (e.g., amplitude, phase) that occur as the signal propagates or by", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-481", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "estimating the time taken for the signal to travel between the entities.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-482", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "A radio signal experiences a loss in its signal strength as it travels through the medium. The", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-483", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "amount of loss or attenuation in the signal’s strength is proportional to the square of the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-484", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distance travelled. The distance between the transmitter and the receiver can therefore be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-485", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "calculated based on the free space path loss equation. In reality, the signal experiences", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-486", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "additional losses due to its interaction with the objects in the environment which are difﬁcult", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-487", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to account for accurately. This directly affects the accuracy of the computed distance and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-488", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "therefore advanced models such as the Rayleigh fading and log-distance path loss models", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-489", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "are typically used to improve the distance estimation accuracy. Bluetooth-based proximity", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-490", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sensing tags (e.g., Apple iBeacon and passive keyless entry and Start Systems) use the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-491", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "strength of the received Bluetooth signal also referred to as the Received Signal Strength", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-492", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Indicator (RSSI) value as a measure of proximity.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-493", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Alternatively, the devices can measure the distance between them by estimating the phase", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-494", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "difference between a received continuous wave signal and a local reference signal. The", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-495", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "need for keeping track of the number of whole cycles elapsed is eliminated by using signals", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-496", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of different frequencies typically referred to as multi-carrier phase-based ranging. Due to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-497", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "their low complexity and low power consumption, phase based ranging is used in several", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-498", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "commercial products.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-499", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Finally, the time taken for the radio waves to travel from one point to another can be used", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-500", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to measure the distance between the devices. In RF-based RTT based distance estimation", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-501", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the distance d between two entities is given by d = (trx −ttx) × c, where c is the speed of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-502", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "light, ttx and trx represent the time of transmission and reception respectively. The mea-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-503", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sured time-of-ﬂight can either be one way time-of-ﬂight or a round-trip time-of-ﬂight. One way", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-504", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "time-of-ﬂight measurement requires the clocks of the measuring entities to be tightly synchro-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-505", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "nised. The errors due to mismatched clocks are compensated in the round-trip time-of-ﬂight", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-506", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "measurement.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-507", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The precise distance measurement largely depends on the system’s ability to estimate the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-508", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "time of arrival and the physical characteristics of the radio frequency signal itself. The ranging", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-509", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "precision is roughly proportional to the bandwidth of the ranging signal. Depending on the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-510", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "required level of accuracy, time-of-ﬂight based distance measurement systems use either", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-511", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Impulse-Radio Ultra Wideband (IR-UWB) or Chirp-Spread Spectrum (CSS) signals. IR-UWB", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-512", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "systems provide centimeter-level precision while the precision of CSS systems is of the order", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-513", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of 1–2m. There are a number of commercially available wireless systems that use chirp and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-514", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "UWB round-trip time-of-ﬂight for distance measurement today.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-515", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-516", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 15", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-517", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 17", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-518", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "4.3", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-519", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Physical Layer Attacks on Secure Distance Measurement", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-520", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "With the increasing availability of low-cost software-deﬁned radio systems, an attacker can", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-521", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "eavesdrop, modify, compose, and (re)play radio signals with ease. This means that the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-522", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker has full control of the wireless communication channel and therefore is capable of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-523", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "manipulating all messages transmitted between the two entities. In RSSI-based distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-524", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "estimation, an attacker can manipulate the measured distance by manipulating the received", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-525", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal strength at the veriﬁer. The attacker can simply amplify the signal transmitted by the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-526", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "prover before relaying it to the veriﬁer. This will result in an incorrect distance estimation at", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-527", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the veriﬁer. Commercially available solutions claim to secure against relay attacks by simply", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-528", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "reducing or attenuating the power of the transmitted signal. However, an attacker can trivially", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-529", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "circumvent such countermeasures by using higher gain ampliﬁers and receiving antennas.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-530", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Similarly, an attacker can also manipulate the estimated distance between the veriﬁer and the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-531", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "prover in systems that use the phase or frequency property of the radio signal. For instance, the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-532", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker can exploit the maximum measurable property of phase or frequency-based distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-533", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "measurement systems and execute distance reduction attacks. The maximum measurable", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-534", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distance, i.e., the largest value of distance dmax that can be estimated using a phase-based", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-535", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "proximity system, directly depends on the maximum measurable phase. Given that the phase", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-536", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "value ranges from 0 to 2π and then rolls over, the maximum measurable distance also rolls", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-537", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "over after a certain value. An attacker can leverage this maximum measurable distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-538", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "property of the system in order to execute the distance decreasing relay attack. During the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-539", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attack, the attacker simply relays (ampliﬁes and forwards) the veriﬁer’s interrogating signal", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-540", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to the prover. The prover determines the phase of the interrogating signal and re-transmits", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-541", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "a response signal that is phase-locked with the veriﬁer’s interrogating signal. The attacker", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-542", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "then receives the prover’s response signal and forwards it to the veriﬁer, however with a time", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-543", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "delay. The attacker chooses the time delay such that the measured phase differences reaches", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-544", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "its maximum value of 2 and rolls over. In other words, the attacker was able to prove to the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-545", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "veriﬁer that the prover is in close proximity (e.g., 1m away) even though the prover was far", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-546", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "from the veriﬁer.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-547", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "In Time of Flight (ToF) based ranging systems, the distance is estimated based on the time", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-548", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "elapsed between the veriﬁer transmitting a ranging packet and receiving an acknowledgement", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-549", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "back from the prover. In order to reduce the distance measured, an attacker must decrease", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-550", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the signal’s round trip time of ﬂight. Based on the implementation, an attacker can reduce the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-551", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "estimated distance in a time-of-ﬂight based ranging system in more than one way. Given that", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-552", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the radio signals travel at a speed of light, a 1 ns decrease in the time estimate can result in a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-553", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distance reduction of 30cm.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-554", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The ﬁrst type of attack on time-of-ﬂight ranging leverages the predictable nature of the data", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-555", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "contained in the ranging and the acknowledgement packets. A number of time-of-ﬂight ranging", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-556", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "systems use pre-deﬁned data packets for ranging, making it trivial for an attacker to predict", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-557", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and generate their own ranging or acknowledgment signal. An attacker can transmit the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-558", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "acknowledgment packet even before receiving the challenge ranging packet. Several works", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-559", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "have shown that the de-facto standard for IR-UWB, IEEE 802.15.4a does not automatically", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-560", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "provide security against distance decreasing attacks. In [25] it was shown that an attacker", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-561", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "can potentially decrease the measured distance by as much as 140 meters by predicting the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-562", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "preamble and payload data with more than 99% accuracy even before receiving the entire", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-563", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "symbol. In a ’Cicada’ attack, the attacker continuously transmits a pulse with a power greater", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-564", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "than that of the prover. This degrades the performance of energy detection based receivers,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-565", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "resulting in reduction of the distance measurements. In order to prevent such attacks it is", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-566", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-567", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 16", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-568", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 18", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-569", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "important to avoid predeﬁned or ﬁxed data during the time critical phase of the distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-570", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "estimation scheme.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-571", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "In addition to having the response packet dependent on the challenge signal, the way in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-572", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "which these challenge and response data are encoded in the radio signals affects the security", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-573", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "guarantees provided by the ranging or localisation system. An attacker can predict the bit", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-574", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(early detect) even before receiving the symbol completely. Furthermore, the attacker can", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-575", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "leverage the robustness property of modern receivers and transmit arbitrary signal until the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-576", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "correct symbol is predicted. Once the bit is predicted (e.g., early-detection), the attacker stops", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-577", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "transmitting the arbitrary signal and switches to transmitting the bit corresponding to the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-578", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "predicted symbol, i.e., the attacker ’commits’ to the predicted symbol, commonly known as", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-579", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "late commit. In such a scenario, the attacker needn’t wait for the entire series of pulses to be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-580", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "received before detecting the data being transmitted. After just a time period, the attacker", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-581", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "would be able to correctly predict the symbol.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-582", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "As described previously, round-trip time-of-ﬂight systems are implemented either using chirp or", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-583", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "impulse radio ultrawideband signals. Due to their long symbol lengths, both implementations", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-584", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "have been shown to be vulnerable to early-detect and late-commit attacks. In the case of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-585", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "chirp-based systems, an attacker can decrease the distance by more than 160 m and in some", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-586", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "scenarios even up to 700 m. Although IR-UWB pulses are of short duration (typically 2–3 ns", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-587", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "long), data symbols are typically composed of a series of UWB pulses. Furthermore, IEEE", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-588", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "802.15.4a IR-UWB standard allows long symbol lengths ranging from 32 ns to as large as 8µs.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-589", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Therefore, even the smallest symbol length of 32 ns allows an attacker to reduce the distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-590", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by as much as 10 m by performing early-detect and late-commit attacks. Thus, it is clear that", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-591", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in order to guarantee proximity and secure a wireless proximity system against early detect", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-592", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and late-commit attacks, it is necessary to keep the symbol length as short as possible.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-593", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Design of a physical layer for secure distance measurement remains an open topic. However,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-594", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "research so far has yielded some guiding principles for its design. Only radio RTT with single-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-595", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pulse or multi-pulse UWB modulation has been shown to be secure against physical layer", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-596", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacks. As a result, the IEEE 802.15.4z working group started the standardization of a new", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-597", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "physical layer for UWB secure distance measurement.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-598", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The ﬁrst attempt at formalizing the requirements for secure distance measurement based on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-599", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the Time of Arrival (ToA) of transmitted messages can be found in [23]. Said work presents", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-600", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "a formal deﬁnition of Message Time of Arrival Codes (MTACs), the core primitive in the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-601", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "construction of systems for secure ToA measurement. If implemented correctly, MTACs", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-602", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "provide the ability to withstand reduction and enlargement attacks on distance measurements.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-603", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "It is shown that systems based on UWB modulation can be implemented such that the stated", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-604", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "security requirements are met and therefore constitute examples of MTAC schemes.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-605", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "4.4", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-606", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Secure Positioning", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-607", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Secure positioning systems allow positioning anchors (also called veriﬁers) to compute the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-608", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "correct position of a node (also called the prover) or allow the prover to determine its own", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-609", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "position correctly despite manipulations by the attacker. This means that the attacker cannot", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-610", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "convince the veriﬁers or the prover that the prover is at a position that is different from its", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-611", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "true position. This is also called spooﬁng-resilience. A related property is the one of secure", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-612", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "position veriﬁcation which means that the veriﬁers can verify the position of an untrusted", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-613", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "prover. It is generally assumed that the veriﬁers are trusted. No restrictions are posed on the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-614", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker as it fully controls the communication channel between the provers and the veriﬁers.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-615", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-616", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 17", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-617", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 19", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-625", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Figure 2: If the computed location of the prover is in the veriﬁcation triangle, the veriﬁers", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-626", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "conclude that this is a correct location. To spoof the position of prover inside the triangle, the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-627", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker would need to reduce at least one of the distance bounds.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-628", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The analysis of broadcast positioning techniques, such as GNSS has shown that such tech-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-629", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "niques are vulnerable to spooﬁng if the attacker controls the signals at the antenna of the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-630", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "GNSS receiver.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-631", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "These type of approaches have been proposed to address this issue: Veriﬁable Multilateration", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-632", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and Secure Positioning based on Hidden Stations.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-633", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Veriﬁable Multilateration relies on secure distance measurement / distance bounding. It", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-634", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "consists of distance bound measurements to the prover from at least three veriﬁers (in 2D)", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-635", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and four veriﬁers (in 3D) and of subsequent computations performed by the veriﬁers or", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-636", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by a central system. Veriﬁable Multilateration has been proposed to address both secure", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-637", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "positioning and position veriﬁcation. In the case of secure positioning, the prover is trusted", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-638", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and maﬁa-fraud-resilient distance bounding is run between the prover and each of the veriﬁers.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-639", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The veriﬁers form veriﬁcation triangles / triangular pyramids (in 3D) and verify the position of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-640", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the prover within the triangle / pyramid. For the attacker to spoof a prover from position P to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-641", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "P’ within a triangle/pyramid, the attacker would need to reduce at least one of the distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-642", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "bounds that are measured to P. This follows from the geometry of the triangle/pyramid. Since", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-643", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Distance bounding prevents distance reduction attacks, Veriﬁable Multilateration prevents", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-644", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "spooﬁng attacks within the triangle/pyramid. The attacker can only spoof P to P’ that is", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-645", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "outside of the triangle/pyramid, causing the prover and the veriﬁers to reject the computed", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-646", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "position. Namely, the veriﬁers and the prover only accept the positions that are within the area", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-647", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of coverage, deﬁned as the area covered by the veriﬁcation triangles/pyramids. Given this,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-648", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "when the prover is trusted, Veriﬁable Multilateration is resilient to all forms of spooﬁng by the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-649", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker. Additional care needs to be given to the management of errors and the computation", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-650", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of the position when distance measurement errors are taken into account.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-651", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "When used for position veriﬁcation, Veriﬁable Multilateration is run with an untrusted prover.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-652", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Each veriﬁer runs a distance-fraud resilient distance bounding protocol with the prover. Based", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-653", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on the obtained distance bounds, the veriﬁers compute the provers’ position. If this position", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-654", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(within some distance and position error bounds) falls within the veriﬁcation triangle/pyramid,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-655", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the veriﬁers accept it as valid. Given that the prover is untrusted, it can enlarge any of the mea-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-656", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sured distances, but cannot reduce them since this is prevented by the use of distance bound-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-657", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ing protocols. Like in the case of secure positioning, the geometry of the triangle/pyramid then", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-658", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "prevents the prover from claiming a false position. Unlike in the case of secure positioning,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-659", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "position veriﬁcation is vulnerable to cloning attacks, in which the prover shares its key to its", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-660", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "clones. These clones can then be strategically placed to the veriﬁers and fake any position", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-661", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by enlarging distances to each individual veriﬁer. This attack can be possibly addressed by", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-662", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tamper resistant hardware or device ﬁngerprinting.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-663", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Another approach to secure positioning and position veriﬁcation is to prevent the attacker", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-664", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-665", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 18", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-666", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 21", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-667", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "reﬂections from different objects in the vicinity of computer screens, such as spoons, bottles", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-668", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and user’s retina were used to infer information show on a display.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-669", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The increasing availability of phones that integrate high quality sensors, such as cameras,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-670", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "microphones and accelerometers makes it easier to mount successful attacks since no", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-671", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "dedicated sensor equipment needs to be covertly put in place.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-671", "line_number": 671, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-672", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "To avoid unwanted signal emissions, devices can be held at a distance, can be shielded and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-673", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals that are transmitted should be ﬁltered in order to remove high-frequency components", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-673", "line_number": 673, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-674", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "that might reﬂect switching activity in the circuitry. Moreover, it is generally advised to place a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-675", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "return wire close to the transmission wire in order to avoid exploitation of the return current.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-676", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "In general, wires and communication systems bearing conﬁdential information should be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-677", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "separated (air-gapped) from non-conﬁdential systems.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-677", "line_number": 677, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-678", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "5.2", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-679", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Sensor Compromise", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-680", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Analogue sensors have been shown to be particularly vulnerable to spooﬁng attacks. Similar to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-681", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "compromising emanations, sensor spooﬁng depends on the type of the physical phenomena", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-681", "line_number": 681, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-682", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the sensor captures. It can be acoustic, optical, thermal, mechanic or electromagnetic.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-683", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Nowadays, many electronic devices, including self-driving cars, medical devices and closed-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-684", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "loop control systems, feature analogue sensors that help observe the environment and make", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-685", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "decisions in a fully autonomous way. These systems are equipped with sophisticated pro-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-686", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tection mechanisms to prevent unauthorised access or compromise via the device’s com-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-687", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "munication interfaces, such as encryption, authentication and access control. Unfortunately,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-688", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "when it comes to data gathered by sensors, the same level of protection is often not available", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-689", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "or difﬁcult to achieve since adversarial interactions with a sensor can be hard to model and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-690", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "predict. As a result, unintentional and especially intentional EMI targeted at analogue sensors", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-691", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "can pose a realistic threat to any system that relies on readings obtained from an affected", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-692", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sensor.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-692", "line_number": 692, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-693", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "EMI has been used to manipulate the output of medical devices as well as to compromise", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-694", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ultrasonic ranging systems. Research has shown that consumer electronic devices equipped", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-695", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "with microphones are especially vulnerable to the injection of fabricated audio signals [31].", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-696", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Ultrasonic signals were used to inject silent voice commands, and acoustic waves were used", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-697", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to affect the output of MEMS accelerometers. Accelerometers and intertial systems based", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-698", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on MEMS are, for instance, used extensively in (consumer-grade) drones and multi-copters.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-699", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Undoubtedly, sensor spooﬁng attacks have gained a lot of attention and will likely impact", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-700", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "many future cyber-physical devices. System designers therefore have to take great care", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-701", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and protect analogue sensors from adversarial input as an attacker might trigger a critical", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-702", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "decision on the application layer of such a device by exposing it to intentional EMI. Potential", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-703", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "defence strategies include, for example, (analogue) shielding of the devices, measuring signal", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-704", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "contamination using various metrics, or accommodating dedicated EMI monitors to detect", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-705", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and ﬂag suspicious sensor readings.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-706", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "A promising strategy that follows the approach of quantifying signal contamination to detect", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-707", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "EMI sensor spooﬁng is presented in [34]. The sensor output can be turned on and off according", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-708", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to a pattern unknown to the attacker. Adversarial EMI in the wires between sensor and the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-708", "line_number": 708, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-709", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "circuitry converting the reading to a digital value, i.e., the ADC, can be detected during the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-709", "line_number": 709, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-710", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "times the sensor is off since the sensor output should be at a known level. In case there", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-710", "line_number": 710, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-711", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "are ﬂuctuations in the readings, an attack is detected. Such an approach is thought to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-711", "line_number": 711, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-712", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-712", "line_number": 712, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-713", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 20", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-713", "line_number": 713, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-714", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 22", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-715", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "be especially effective when used to protect powered or non-powered passive sensors. It", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-716", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "has been demonstrated to successfully thwart EMI attacks against a microphone and a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-717", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "temperature sensor system. The only modiﬁcation required is the addition of an electronic", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-717", "line_number": 717, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-718", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "switch that can be operated by the control unit or microcontroller to turn the sensor on and off.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-718", "line_number": 718, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-719", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "A similar sensor spooﬁng detection scheme can be implemented for active sensors, such as", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-719", "line_number": 719, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-720", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ultrasonic and infrared sensors, by incorporating a challenge-response like mechanism into", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-720", "line_number": 720, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-721", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the measurement acquisition process [36]. An active sensor often has an emitting element", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-721", "line_number": 721, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-722", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and a receiving element. The emitter releases a signal that is reﬂected and captured by the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-722", "line_number": 722, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-723", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver. Based on the properties of the received signal, the sensor can infer information about", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-723", "line_number": 723, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-724", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the entity or the object that reﬂected the signal. The emitter can be turned off randomly and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-725", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "during that time the receiver should not be able to register any incoming signal. Otherwise, an", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-726", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attack is detected and the sensor reading is discarded.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-726", "line_number": 726, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-728", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "PHYSICAL LAYER SECURITY OF SELECTED", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-729", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "COMMUNICATION TECHNOLOGIES", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-729", "line_number": 729, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-730", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[37, 38, 39, 40]", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-731", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "This section presents security mechanisms of a selection of existing wireless communication", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-732", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "techniques that are in use today. The main focus is on physical-layer security constructs", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-732", "line_number": 732, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-733", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as well as any lack thereof. The communication techniques that are discussed in detail are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-733", "line_number": 733, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-734", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "near-ﬁeld communication, air trafﬁc communication networks, cellular networks and global", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-734", "line_number": 734, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-735", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "navigation satellite systems.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-735", "line_number": 735, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-736", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "6.1", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-736", "line_number": 736, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-737", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Near-ﬁeld communication (NFC)", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-738", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Near-ﬁeld communication commonly refers to wireless communication protocols between", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-738", "line_number": 738, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-739", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "two small (portable) electronic devices. The standard is used for contact-less payment", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-739", "line_number": 739, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-740", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and mobile payment systems in general. NFC-enabled devices can also exchange identity", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-740", "line_number": 740, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-741", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "information, such as keycards, for access control, and negotiate parameters to establish a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-741", "line_number": 741, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-742", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "subsequent high-bandwidth wireless connection using more capable protocols.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-743", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "NFC is designed to only transmit and receive data to a distance of up to a few centimeters.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-744", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Even if higher-layer cryptographic protocols are used, vanilla NFC protocols do not offer secure", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-745", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communication and can not guarantee that two communicating devices are indeed only a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-746", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "short distance apart. NFC is vulnerable to eavesdropping, man-in-the-middle attacks and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-747", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "message relay attacks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-748", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Even nowadays, standard NFC is deployed in security-critical contexts due to the assumption", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-749", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "that communicating devices are in close proximity. Research has shown, however, that this", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-750", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "assumption can not be veriﬁed reliably using NFC protocols. The distance can be made", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-750", "line_number": 750, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-751", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "almost arbitrarily large by relaying messages between NFC-enabled devices. The attack works", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-752", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as follows: The benign NFC devices are made to believe that they are communicating with", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-753", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "each other, but they are actually exchanging data with a modiﬁed smartphone. An adversary", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-754", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "can strategically place a smartphone next to each benign NFC device while the smartphones", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-755", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "themselves use a communication method that can cover long distances, such as WiFi. They", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-756", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "simply forward the messages the benign devices are sending to each other. Such an attack is", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-756", "line_number": 756, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-757", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "also referred to as a wormhole attack where communicating parties are tricked into assuming", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-757", "line_number": 757, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-758", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-759", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 21", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-760", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 23", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-760", "line_number": 760, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-761", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "that they are closer than they actually are. This is a problem that cannot be solved using", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-761", "line_number": 761, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-762", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "techniques on the logical layer or on the data layer.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-762", "line_number": 762, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-763", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Obviously, most of the described attacks can be mitigated by shielding the NFC devices or", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-763", "line_number": 763, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-764", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "enhance the protocol with two-factor authentication, for example. Such mechanisms unfortu-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-765", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "nately transfer security-relevant decisions to the user of an NFC system. Countermeasures", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-766", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "that do not impose user burden can roughly be categorised into physical layer methods and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-767", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the augmentation with context- or device-speciﬁc identiﬁers [37].", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-768", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Protocol augmentation entails context-aware NFC devices that incorporate location infor-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-769", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "mation into the NFC system to verify proximity. The location sensing can be implemented", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-770", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "with the help of a variety of different services, each with its own accuracy and granularity.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-771", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Conceivable are, for instance, GNSS/GPS based proximity veriﬁcation or leveraging the cell-ID", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-772", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of the base station to which the NFC device is currently closest in order to infer a notion of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-773", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "proximity.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-774", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Physical layer methods that have been suggested in research literature are timing restrictions", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-775", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and distance bounding. Enforcing strict timing restraints on the protocol messages can be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-776", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "understood as a crude form of distance bounding. As discussed in Section 4.1, distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-777", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "bounding determines an upper bound on the physical distance between two communicating", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-778", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "devices. While distance bounding is considered the most effective approach, it still remains to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-779", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "be shown if secure distance bounding can be implemented in practice for small NFC-enabled", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-780", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "devices.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-781", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "6.2", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-782", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Air Trafﬁc Communication Networks", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-783", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Throughout different ﬂight phases commercial and non-commercial aviation uses several", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-784", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "wireless communication technologies to exchange information with aviation authorities on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-785", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the ground as well as between airborne vehicles. Often legacy systems are still in use and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-786", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "security has never been part of the design of such systems.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-787", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "While new proposals suggest to overhaul these systems and to tightly integrate security", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-788", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "measures into the data layer, such as encryption and message authentication, air trafﬁc", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-789", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communication networks are not only used for information transmission, but also to extract", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-790", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "physical layer features from the signal in order to perform aircraft location positioning.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-791", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "A prominent example is ADS-B. An ADS-B transponder periodically (or when requested) broad-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-792", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "casts the aircraft’s position information, such as coordinates, that have been obtained through", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-793", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "an on-board GNSS receiver. Most versions of ADS-B only support unauthenticated messages", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-794", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and therefore, this technology suffers from active and passive attacks, i.e., eavesdropping,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-795", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "modifying, injecting and jamming messages. It is, for instance, possible to prevent an aircraft’s", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-796", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "location from being tracked by Air Trafﬁc Control (ATC) by simply jamming the respective mes-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-796", "line_number": 796, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-797", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sages. Similarly, an adversary could create ghost planes by emitting fabricated transponder", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-798", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "messages. A sophisticated attacker could even fully distort the view ATC has on its airspace.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-799", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Multilateration (MLAT) can be seen as a technology that mitigates some of the shortcomings", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-800", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of unauthenticated ADS-B and is therefore usually deployed in conjunction with ADS-B. MLAT", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-801", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "does not rely on the transmitted information encapsulated in the message, but makes use of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-802", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the physical and geometrical constellation between the transmitter (i.e., transponder of the air-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-803", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "craft) and several receivers. MLAT systems extract physical layer properties from the received", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-804", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "messages. The time of arrival of a message is recorded at different co-located receivers and,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-805", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "using the propagation speed of the signal, the location of the aircraft’s transponder can be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-806", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-807", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 22", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-808", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 24", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-809", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "estimated. Multilateration techniques infer the aircraft’s location even if the contents of the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-810", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ADS-B messages are incorrect and thus MLAT provides a means to crosscheck the location", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-811", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "information disseminated by the aircraft’s transponder.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-812", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Although MLAT offers additional security based on physical layer properties, a distributed", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-813", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "adversary can still manipulate ADS-B messages. In addition to altering the location information,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-813", "line_number": 813, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-814", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "an attacker can modify or inject signals that affect the time-of-arrival measurement at the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-814", "line_number": 814, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-815", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receivers. If the attacker has access to multiple distributed antennas and is able to coordinate", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-816", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "adversarial signal emission precisely, attacks similar to those on standard ADS-B are feasible.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-817", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "However, the more receivers used to record the signals, the more difﬁcult such attacks", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-817", "line_number": 817, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-818", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "become. Unfortunately, MLAT is not always an effective solution in aviation as strategic", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-818", "line_number": 818, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-819", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver placement is crucial and time of arrival calculations can be susceptible to multi-path", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-820", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "interference [38].", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-821", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "6.3", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-822", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Cellular Networks", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-823", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Cellular networks provide voice, data and messaging communication through a network of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-824", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "base stations, each covering one or more cells. The security provisions of these networks are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-825", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "mainly governed by the standards that were adopted in the GSM Association and later in the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-826", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Third Generation Partnership Plan (3GPP).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-827", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Second Generation (2G) ‘GSM’ networks were introduced during the 1990s, and restricted", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-828", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "their services to voice and text messaging. 2G networks were capable of carrying data via a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-829", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Circuit-Switched Data Service (CSD) which operated in a manner similar to the dial-up modems,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-830", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "just over cellular networks. Further development of email and web services resulted in a need", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-831", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "for enhanced speeds and services", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-832", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "3GPP improved 2G GSM standard with packet switched data service, resulting in the general", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-833", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "packet radio service (GPRS). Like GSM, GPRS made use of the Home Location Register", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-834", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(HLR), a component that was responsible for subscriber key management and authentication.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-835", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "However, GPRS enhanced GSM by adding the Serving GPRS Support Node (SGSN) for data", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-836", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "trafﬁc routing and mobility management for better data trafﬁc delivery. Third Generation (3G)", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-837", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of cellular networks, also known as Universal Mobile Telecommunications Systems (UMTS),", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-838", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "introduced a number of improvements over 2G networks, including security enhancements,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-839", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as well as increased uplink and downlink speeds and capacities. Fourth Generation (4G)", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-840", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cellular networks, also known as Long Term Evolution (LTE) introduced further increase in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-841", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "transmission speeds and capacities.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-842", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "One of the main security properties that cellular networks aim to protect is the conﬁdentiality", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-843", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of the communication of the link between the mobile station, and the base station and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-844", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "correct billing. The security of cellular networks has evolved with network generations, but all", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-845", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "generations have the same overarching concept. Subscribers are identiﬁed via their (Universal)", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-845", "line_number": 845, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-846", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "subscriber identity modules their International Mobile Subscriber Identity (IMSI) number and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-846", "line_number": 846, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-847", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "its related secret key. IMSI and the keys are used to authenticate subscribers as well as to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-847", "line_number": 847, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-848", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "generate the necessary shared secrets to protect the communication to the cellular network.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-849", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2G security focused on the conﬁdentiality of the wireless link between the mobile station", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-850", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and the base station. This was achieved through the authentication via a challenge-response", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-851", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "protocol, 2G Authentication and Key Agreement (AKA). This protocol is executed each time", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-852", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "when a mobile station initiates a billable operation. 2G AKA achieved authentication based on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-853", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "a long term key Ki shared between the subscriber SIM card and the network. This key is used", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-854", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-855", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 23", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-856", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 25", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-857", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "by the network to authenticate the subscriber and to derive a session key Kc. This is done", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-858", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "within in a challenge response protocol, executed between the SGSN and the mobile station.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-859", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Before the execution of the protocol, SGSN receives from the HLR the Kc, a random value", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-860", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "RAND and an expected response XRES. Both Kc and XRES are generated within the HLR", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-861", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "based on RAND and Ki. When the mobile station attempts to authenticate to the network it", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-862", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "is sent RAND. To authenticate, the mobile station combines its long term key Ki (stored on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-863", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "its SIM card) with the received RAND to generate RES and Kc. The mobile station sends", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-864", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "RES to the SGSN which compares it to XRES. If the two values match, the mobile station is", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-865", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "authenticated to the network. The SGSN then sends the Kc to the base station to which the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-866", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "mobile station is connected in order to protect the mobile to base station wireless link.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-867", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2G AKA offered very limited protection. It used inadequate key size (56-64 bits), and weak", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-868", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "authentication and key generation algorithms (A3,A5 and A8) which were, once released,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-869", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "broken, allowing for eavesdropping and message forgery. Furthermore, AKA was designed to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-870", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "provide only one-way authentication of the mobile station to the network. Since the network", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-871", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "did not authenticate to the mobile stations this enabled attacks by fake base stations violating", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-872", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "users location privacy and conﬁdentiality of their communication.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-873", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "In order to address the 2G security shortcomings, 3G networks introduced new 3G Authenti-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-874", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cation and Key Agreement (3G AKA) procedures. 3G AKA replaced the weak cryptographic", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-875", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "algorithms that were used in 2G and provided mutual authentication between the network", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-876", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and the mobile stations. Like in 2G, the goal of the protocol is the authentication (now mu-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-877", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tual) of the network and the mobile station. The input into the protocol is a secret key K", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-877", "line_number": 877, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-878", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "shared between the HLR and the subscriber. The outcome of the protocol are two keys,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-878", "line_number": 878, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-879", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the encryption/conﬁdentiality key CK and the integrity key IK. The generation of two keys", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-879", "line_number": 879, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-880", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "allows the network and the mobile station to protect the integrity and conﬁdentiality of their", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-880", "line_number": 880, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-881", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communication using two different keys, in line with common security practices. CK and IK", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-881", "line_number": 881, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-882", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "are each 128 bits long which is considered adequate.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-882", "line_number": 882, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-883", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The authentication and key derivation is performed as follows. The HLR ﬁrst generates the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-883", "line_number": 883, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-884", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "random challenge RAND, from it the expected response XRES, the keys CK and IK and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-884", "line_number": 884, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-885", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the authentication token AUTN. It then sends these values to the SGSN. The SGSN sends the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-885", "line_number": 885, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-886", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "RAND as well as the AUTN to the mobile station (also denoted as User Equipment (UE)),", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-886", "line_number": 886, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-887", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "which will then use its long term key K to generate the response RES and to verify if AUTN", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-887", "line_number": 887, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-888", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "was generated by the HLR. The AUTN is from the shared key and the counter maintained by", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-888", "line_number": 888, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-889", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "both the HLR and the mobile station. Upon receiving the RES from the mobile station, SGSN", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-889", "line_number": 889, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-890", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "will compare it with the XRES and if they match, will forward the CK and IK to the base", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-890", "line_number": 890, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-891", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "station. The base and mobile station can now use these keys to protect their communication.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-891", "line_number": 891, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-892", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "3G, however, still didn’t resolve the vulnerabilities within the operator’s networks. CK and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-892", "line_number": 892, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-893", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IK are transmitted between different entities in the network. They are transmitted between", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-893", "line_number": 893, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-894", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "SGSN and the associated base station as well as between different base stations during", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-894", "line_number": 894, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-895", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "mobility. This allows network attackers to record these keys and therefore eavesdrop on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-895", "line_number": 895, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-896", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "wireless connections.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-896", "line_number": 896, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-897", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "4G (LTE) security architecture preserved many of the core elements of 2G and 3G networks,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-897", "line_number": 897, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-898", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "but aimed to address the shortcomings of 3G in terms of the protection of the in-network", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-898", "line_number": 898, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-899", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "trafﬁc through the protection of network links and redistribution of different roles. For example,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-899", "line_number": 899, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-900", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the long term key storage was moved from the HLR to the Home Subscriber Server (HSS).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-900", "line_number": 900, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-901", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Mobility management was moved from the SGSN to the Mobility Management Engine (MME).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-901", "line_number": 901, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-902", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "5G security architecture evolves 4G but follows a similar set of principles and entities. 5G", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-902", "line_number": 902, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-903", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-903", "line_number": 903, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-904", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 24", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-904", "line_number": 904, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-905", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 26", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-905", "line_number": 905, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-906", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "introduces a new versions of Authentication and Key Agreement (AKA) protocols that was", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-906", "line_number": 906, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-907", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "designed to ﬁx the issues found in 4G, however with mixed success [41].", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-907", "line_number": 907, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-908", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "6.4", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-908", "line_number": 908, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-909", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "GNSS Security and Spooﬁng Attacks", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-909", "line_number": 909, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-910", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "GNSS such as GPS and Galileo provide global navigation service through satellites that are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-910", "line_number": 910, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-911", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "orbiting the earth approximately 20,000km above the ground. Satellites are equipped with", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-911", "line_number": 911, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-912", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "high-precision atomic clocks which allows the satellites to remain synchronised. Satellites", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-912", "line_number": 912, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-913", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "transmit navigation messages at central frequencies of 1575.42MHz (L1) and 1227.60MHz", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-913", "line_number": 913, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-914", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(L2). direct sequence spreading is used to enable acquisition and to protect the signals", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-914", "line_number": 914, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-915", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "carrying those messages from spooﬁng and jamming attacks. Civilian codes are public", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-915", "line_number": 915, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-916", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and therefore do not offer such protection, whereas military and special interest codes are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-916", "line_number": 916, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-917", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "kept conﬁdential. Navigation messages carry data including satellite clock information, the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-917", "line_number": 917, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-918", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ephemeris (information related to the satellite orbit) and the almanac (the satellite orbital and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-918", "line_number": 918, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-919", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "clock information). Satellite messages are broadcasted and the reception of messages from", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-919", "line_number": 919, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-920", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "four of more satellites will allow a receiver to calculate its position. This position calculation", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-920", "line_number": 920, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-921", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "is based on trilateration. The receiver measures the times of arrival of the satellite signals,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-921", "line_number": 921, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-922", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "converts them into distances (pseudoranges), and then calculates its position as well as its", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-922", "line_number": 922, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-923", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "clock offset with respect to the satellite clocks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-923", "line_number": 923, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-924", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "A GPS signal spooﬁng attack is a physical-layer attack in which an attacker transmits specially", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-924", "line_number": 924, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-925", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "crafted radio signals that are identical to authentic satellite signals. Civilian GPS is easily", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-925", "line_number": 925, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-926", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "vulnerable to signal spooﬁng attacks. This is due to the lack of any signal authentication", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-926", "line_number": 926, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-927", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and the publicly known spreading codes for each satellite, modulation schemes, and data", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-927", "line_number": 927, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-928", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "structure. In a signal spooﬁng attack, the objective of an attacker may be to force a target", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-928", "line_number": 928, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-929", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver to (i) compute an incorrect position, (ii) compute an incorrect time or (iii) disrupt the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-929", "line_number": 929, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-930", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver. Due to the low power of the legitimate satellite signal at the receiver, the attacker’s", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-930", "line_number": 930, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-931", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "spooﬁng signals can trivially overshadow the authentic signals. In a spooﬁng attack, the GPS", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-931", "line_number": 931, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-932", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver typically locks (acquires and tracks) onto the stronger, attacker’s signal, thus ignoring", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-932", "line_number": 932, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-933", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the satellite signals.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-933", "line_number": 933, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-934", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "An attacker can inﬂuence the receiver’s position and time estimate in two ways: (i) by manip-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-934", "line_number": 934, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-935", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ulating the contents of the navigation messages (e.g., the location of satellites, navigation", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-935", "line_number": 935, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-936", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "message transmission time) and/or (ii) by modifying the arrival time of the navigation mes-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-936", "line_number": 936, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-937", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sages. The attacker can manipulate the receiver time of arrival by temporally shifting the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-937", "line_number": 937, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-938", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "navigation message signals while transmitting the spooﬁng signals. We can classify spooﬁng", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-938", "line_number": 938, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-939", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacks based on how synchronous (in time) and consistent (with respect to the contents", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-939", "line_number": 939, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-940", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of the navigation messages) the spooﬁng signals are in comparison to the legitimate GPS", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-940", "line_number": 940, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-941", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals currently being received at the receiver’s true location.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-941", "line_number": 941, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-942", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Non-Coherent and Modiﬁed Message Contents: In this type of attack, the attacker’s signals", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-942", "line_number": 942, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-943", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "are both unsynchronised and contain different navigation message data in comparison to the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-943", "line_number": 943, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-944", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "authentic signals. Attackers who use GPS signal generators to execute the spooﬁng attack", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-944", "line_number": 944, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-945", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "typically fall under this category. An attacker with a little know-how can execute a spooﬁng", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-945", "line_number": 945, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-946", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attack using these simulators due to their low complexity, portability and ease of use. Some", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-946", "line_number": 946, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-947", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "advanced GPS signal generators are even capable of recording and replaying signals, however", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-947", "line_number": 947, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-948", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "not in real-time. In other words, the attacker uses the simulator to record at one particular time", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-948", "line_number": 948, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-949", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in a given location and later replays it. Since they are replayed at a later time, the attacker’s", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-949", "line_number": 949, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-950", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals are not coherent and contain different navigation message data than the legitimate", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-950", "line_number": 950, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-951", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals currently being received.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-951", "line_number": 951, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-952", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-952", "line_number": 952, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-953", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 25", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-953", "line_number": 953, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-954", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 27", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-954", "line_number": 954, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-955", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Figure 3: Seamless takeover attack on GPS. The spooﬁng aligns its signal with the legitimate", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-955", "line_number": 955, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-956", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal and slowly increase the transmit power. Once receiver locks on to attacker’s signal, he", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-956", "line_number": 956, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-957", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "starts to manipulate it.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-957", "line_number": 957, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-958", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Non-Coherent but Unmodiﬁed Message Contents: In this type of attack, the navigation mes-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-958", "line_number": 958, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-959", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sage contents of the transmitted spooﬁng signals are identical to the legitimate GPS signals", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-959", "line_number": 959, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-960", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "currently being received. However, the attacker temporally shifts the spooﬁng signal thereby", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-960", "line_number": 960, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-961", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "manipulating the spooﬁng signal time of arrival at the target receiver. For example, attackers", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-961", "line_number": 961, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-962", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "capable of real-time recording and replaying of GPS signals fall under this category as they", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-962", "line_number": 962, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-963", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "will have the same navigation contents as that of the legitimate GPS signals, however shifted", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-963", "line_number": 963, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-964", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in time. The location or time offset caused by such an attack on the target receiver depends", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-964", "line_number": 964, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-965", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on the time delay introduced both by the attacker and due to the propagation time of the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-965", "line_number": 965, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-966", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "relayed signal. The attacker can precompute these delays and successfully spoof a receiver", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-966", "line_number": 966, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-967", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to a desired location.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-967", "line_number": 967, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-968", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Coherent but Modiﬁed Message Contents: The attacker generates spooﬁng signals that are", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-968", "line_number": 968, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-969", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "synchronised to the authentic GPS signals. However, the contents of the navigation messages", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-969", "line_number": 969, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-970", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "are not the same as that of the currently seen authentic signals. For instance, phase-coherent", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-970", "line_number": 970, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-971", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal synthesisers are capable of generating spooﬁng signals with the same code phase", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-971", "line_number": 971, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-972", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as the legitimate GPS signal that the target receiver is currently locked on to. Additionally,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-972", "line_number": 972, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-973", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the attacker modiﬁes the contents of the navigation message in real-time (and with minimal", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-973", "line_number": 973, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-974", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "delay) and replays it to the target receiver. A variety of commercial GPS receivers were shown", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-974", "line_number": 974, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-975", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to be vulnerable to this attack and in some cases, it even caused permanent damage to the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-975", "line_number": 975, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-976", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receivers.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-976", "line_number": 976, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-977", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Coherent and Unmodiﬁed Message Contents: Here, the attacker does not modify the contents", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-977", "line_number": 977, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-978", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of the navigation message and is completely synchronised to the authentic GPS signals. Even", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-978", "line_number": 978, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-979", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "though the receiver locks on to the attacker’s spooﬁng signals (due to the higher power), there", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-979", "line_number": 979, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-980", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "is no change in the location or time computed by the target receiver. Therefore, this is not an", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-980", "line_number": 980, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-981", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attack in itself but is an important ﬁrst step in executing the seamless takeover attack.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-981", "line_number": 981, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-982", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The seamless takeover attack is considered one of the strongest attacks in literature. In a", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-982", "line_number": 982, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-983", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "majority of applications, the target receiver is already locked on to the legitimate GPS satellite", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-983", "line_number": 983, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-984", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals. The main steps are highlighted in Figure 3. The goal of an attacker is to force the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-984", "line_number": 984, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-985", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receiver to stop tracking the authentic GPS signals and lock onto the spooﬁng signals without", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-985", "line_number": 985, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-986", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "causing any signal disruption or data loss. This is because the target receiver can potentially", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-986", "line_number": 986, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-987", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "detect the attack based on the abrupt loss of GPS signal. In a seamless takeover attack, ﬁrst,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-987", "line_number": 987, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-988", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the attacker transmits spooﬁng signals that are synchronised with the legitimate satellite", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-988", "line_number": 988, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-989", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals and are at a power level lower than the received satellite signals. The receiver is still", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-989", "line_number": 989, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-990", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "locked on to the legitimate satellite signals due to the higher power and hence there is no", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-990", "line_number": 990, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-991", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "change in the ships route. The attacker then gradually increases the power of the spooﬁng", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-991", "line_number": 991, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-992", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals until the target receiver stops tracking the authentic signal and locks on to the spooﬁng", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-992", "line_number": 992, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-993", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-993", "line_number": 993, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-994", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 26", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-994", "line_number": 994, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-995", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 28", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-995", "line_number": 995, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-996", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals. Note that during this takeover, the receiver does not see any loss of lock, in other", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-996", "line_number": 996, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-997", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "words, the takeover was seamless. Even though the target receiver is now locked on to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-997", "line_number": 997, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-998", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the attacker, there is still no change in the route as the spooﬁng signals are both coherent", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-998", "line_number": 998, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-999", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "with the legitimate satellite signals as well as there is no modiﬁcation to the contents of the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-999", "line_number": 999, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1000", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "navigation message itself. Now, the attacker begins to manipulate the spooﬁng signal such", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1000", "line_number": 1000, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1001", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "that the receiver computes a false location and begins to alter its course. The attacker can", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1001", "line_number": 1001, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1002", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "either slowly introduce a temporal shift from the legitimate signals or directly manipulate the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1002", "line_number": 1002, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1003", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "navigation message contents to slowly deviate the course of the ship to a hostile destination.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1003", "line_number": 1003, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1004", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "If an attacker controls all the signals that arrive at the receiver’s antenna(s) the receiver", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1004", "line_number": 1004, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1005", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cannot detect spooﬁng. However, if the attack is remote, and the attacker cannot fully control", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1005", "line_number": 1005, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1006", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the signals at the receiver, anomaly detection techniques can be used to detect spooﬁng.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1006", "line_number": 1006, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1007", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "In particular, Automatic Gain Control (AGC) values, Received Signal Strength (RSS) from", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1007", "line_number": 1007, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1008", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "individual satellites, carrier phase values, estimated noise ﬂoor levels, number of visible", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1008", "line_number": 1008, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1009", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "satellites all can be used to detect spooﬁng. Particularly interesting are techniques based", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1009", "line_number": 1009, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1010", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on tracking and analysis of autocorrelation peaks that are used for the detection of GNSS", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1010", "line_number": 1010, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1011", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals. Distortion, the number and the behaviour over time of these peaks can be used to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1011", "line_number": 1011, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1012", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "detect even the most sophisticated seamless takeover attacks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1012", "line_number": 1012, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1013", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "The detection of GNSS spooﬁng can be improved if spooﬁng signals are simultaneously", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1013", "line_number": 1013, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1014", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "received by several receivers. This can be used for the detection of spooﬁng as well as for", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1014", "line_number": 1014, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1015", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "spoofer localisation. If the receivers know their mutual distances (e.g., are placed at ﬁxed", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1015", "line_number": 1015, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1016", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distances), the spoofer needs to preserve those distances when performing the spooﬁng", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1016", "line_number": 1016, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1017", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attack. When a single spoofer broadcasts its signals, it will result in all receivers being spoofed", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1017", "line_number": 1017, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1018", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "to the same position, therefore enabling detection. This basic detection technique can be", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1018", "line_number": 1018, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1019", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "generalised to several receivers, allowing even the detection of distributed spoofers.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1019", "line_number": 1019, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1020", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Finally, GNSS spooﬁng can be made harder through the authentication and hiding of GNSS", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1020", "line_number": 1020, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1021", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signals. Although currently civilian GNSS systems do not support authentication, digital", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1021", "line_number": 1021, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1022", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signatures as well as hash-based signatures such as TESLA can be added to prevent the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1022", "line_number": 1022, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1023", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacker from generating GNSS signals. This would, however, not prevent all spooﬁng attacks", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1023", "line_number": 1023, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1024", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "since the attacker can still selectively delay navigation messages and therefore modify the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1024", "line_number": 1024, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1025", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "computed position. This attack can be prevented by the use of spreading with delayed key", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1025", "line_number": 1025, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1026", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "disclosure. Even this approach still does not fully prevent against spooﬁng by broadband", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1026", "line_number": 1026, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1027", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "receivers that are able to relay full GNSS frequency band between locations.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1027", "line_number": 1027, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1028", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Military GPS signals are authenticated, and try to achieve low-probability of intercept as well", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1028", "line_number": 1028, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1029", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "as jamming resilience via the use of secret spreading codes. This approach prevents some", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1029", "line_number": 1029, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1030", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "of the spooﬁng attacks, but still fails to fully prevent record-and-relay attacks. In addition,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1030", "line_number": 1030, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1031", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "this approach does not scale well since secret spreading codes need to be distributed to all", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1031", "line_number": 1031, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1032", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "intended receivers, increasing the likelihood of their leakage and reducing usability.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1032", "line_number": 1032, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1033", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "In conclusion, although newly proposed and deployed countermeasures make it more difﬁcult", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1033", "line_number": 1033, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1034", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "for the attacker to spoof GNS systems like GPS, currently no measure fully prevents spooﬁng", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1034", "line_number": 1034, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1035", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "under strong attacker models. This is an area of active research.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1035", "line_number": 1035, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1036", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1036", "line_number": 1036, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1037", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 27", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1037", "line_number": 1037, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1038", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 31", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1038", "line_number": 1038, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1039", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "REFERENCES", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1039", "line_number": 1039, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1040", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[1] R. Liu and W. Trappe, Securing Wireless Communications at the Physical Layer, 1st ed.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1040", "line_number": 1040, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1041", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer Publishing Company, Incorporated, 2009.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1041", "line_number": 1041, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1042", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[2] C. Ye, S. Mathur, A. Reznik, Y. Shah, W. Trappe, and N. B. Mandayam, “Information-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1042", "line_number": 1042, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1043", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "theoretically secret key generation for fading wireless channels,” IEEE Transactions on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1043", "line_number": 1043, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1044", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Information Forensics and Security, vol. 5, no. 2, pp. 240–254, 2010.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1044", "line_number": 1044, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1045", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[3] S. Eberz, M. Strohmeier, M. Wilhelm, and I. Martinovic, “A practical man-in-the-middle", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1045", "line_number": 1045, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1046", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attack on signal-based key generation protocols,” in Computer Security – ESORICS 2012,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1046", "line_number": 1046, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1047", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "S. Foresti, M. Yung, and F. Martinelli, Eds.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1047", "line_number": 1047, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1048", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Berlin, Heidelberg: Springer Berlin Heidelberg,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1048", "line_number": 1048, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1049", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2012, pp. 235–252.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1049", "line_number": 1049, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1050", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[4] S. Gollakota, H. Hassanieh, B. Ransford, D. Katabi, and K. Fu, “They can hear your", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1050", "line_number": 1050, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1051", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "heartbeats: Non-invasive security for implantable medical devices,” in Proceedings of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1051", "line_number": 1051, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1052", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the ACM SIGCOMM 2011 Conference, ser. SIGCOMM ’11.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1052", "line_number": 1052, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1053", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "New York, NY, USA: ACM, 2011,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1053", "line_number": 1053, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1054", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pp. 2–13.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1054", "line_number": 1054, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1055", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[5] N. Anand, S.-J. Lee, and E. W. Knightly, “Strobe: Actively securing wireless communi-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1055", "line_number": 1055, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1056", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cations using zero-forcing beamforming,” in 2012 Proceedings IEEE INFOCOM, March", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1056", "line_number": 1056, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1057", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2012, pp. 720–728.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1057", "line_number": 1057, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1058", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[6] S. Čapkun, M. Čagalj, R. Rengaswamy, I. Tsigkogiannis, J.-P. Hubaux, and M. Srivastava,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1058", "line_number": 1058, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1059", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "“Integrity codes: Message integrity protection and authentication over insecure channels,”", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1059", "line_number": 1059, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1060", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE Transactions on Dependable and Secure Computing, vol. 5, no. 4, pp. 208–223, Oct", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1060", "line_number": 1060, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1061", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2008.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1061", "line_number": 1061, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1062", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[7] C. E. Shannon, “Communication theory of secrecy systems,” Bell system technical jour-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1062", "line_number": 1062, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1063", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "nal, vol. 28, no. 4, pp. 656–715, 1949.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1063", "line_number": 1063, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1064", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[8] A. D. Wyner, “The wire-tap channel,” Bell system technical journal, vol. 54, no. 8, pp.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1064", "line_number": 1064, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1065", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "1355–1387, 1975.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1065", "line_number": 1065, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1066", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[9] I. Csiszár and J. Korner, “Broadcast channels with conﬁdential messages,” IEEE transac-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1066", "line_number": 1066, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1067", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tions on information theory, vol. 24, no. 3, pp. 339–348, 1978.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1067", "line_number": 1067, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1068", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[10] M. Bloch, J. Barros, M. R. Rodrigues, and S. W. McLaughlin, “Wireless information-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1068", "line_number": 1068, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1069", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "theoretic security,” IEEE Transactions on Information Theory, vol. 54, no. 6, pp. 2515–", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1069", "line_number": 1069, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1070", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2534, 2008.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1070", "line_number": 1070, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1071", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[11] D. Adamy, EW 101: a ﬁrst course in electronic warfare.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1071", "line_number": 1071, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1072", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Artech House, 2001.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1072", "line_number": 1072, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1073", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[12] C. Popper, “On secure wireless communication under adversarial interference,” PhD", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1073", "line_number": 1073, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1074", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "thesis, ETH Zurich, 2011.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1074", "line_number": 1074, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1075", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[13] C. Pöpper, N. O. Tippenhauer, B. Danev, and S. Čapkun, “Investigation of signal and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1075", "line_number": 1075, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1076", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "message manipulations on the wireless channel,” in Proceedings of the European Sym-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1076", "line_number": 1076, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1077", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "posium on Research in Computer Security, 2011.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1077", "line_number": 1077, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1078", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[14] H. Yang, S. Bae, M. Son, H. Kim, S. M. Kim, and Y. Kim, “Hiding in plain signal:", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1078", "line_number": 1078, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1079", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Physical signal overshadowing attack on LTE,” in 28th USENIX Security Symposium", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1079", "line_number": 1079, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1080", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(USENIX Security 19).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1080", "line_number": 1080, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1081", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Santa Clara, CA: USENIX Association, Aug. 2019, pp. 55–72.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1081", "line_number": 1081, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1082", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[Online]. Available: https://www.usenix.org/conference/usenixsecurity19/presentation/", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1082", "line_number": 1082, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1083", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "yang-hojoon", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1083", "line_number": 1083, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1084", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[15] B. Danev, D. Zanetti, and S. Capkun, “On physical-layer identiﬁcation of wireless devices,”", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1084", "line_number": 1084, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1085", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM Comput. Surv., vol. 45, no. 1, pp. 6:1–6:29, Dec. 2012.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1085", "line_number": 1085, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1086", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[16] G. Avoine, M. A. Bingöl, I. Boureanu, S. čapkun, G. Hancke, S. Kardaş, C. H. Kim, C. Lau-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1086", "line_number": 1086, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1087", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "radoux, B. Martin, J. Munilla, A. Peinado, K. B. Rasmussen, D. Singelée, A. Tchamkerten,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1087", "line_number": 1087, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1088", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "R. Trujillo-Rasua, and S. Vaudenay, “Security of distance-bounding: A survey,” ACM", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1088", "line_number": 1088, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1089", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Comput. Surv., vol. 51, no. 5, pp. 94:1–94:33, Sep. 2018.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1089", "line_number": 1089, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1090", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[17] T. Beth and Y. Desmedt, “Identiﬁcation tokens—or: Solving the chess grandmaster", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1090", "line_number": 1090, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1091", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1091", "line_number": 1091, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1092", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 30", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1092", "line_number": 1092, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1093", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 32", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1093", "line_number": 1093, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1094", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "problem,” in Conference on the Theory and Application of Cryptography.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1094", "line_number": 1094, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1095", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 1990,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1095", "line_number": 1095, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1096", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pp. 169–176.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1096", "line_number": 1096, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1097", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[18] S. Brands and D. Chaum, “Distance-bounding protocols,” in Workshop on the Theory and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1097", "line_number": 1097, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1098", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Application of of Cryptographic Techniques.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1098", "line_number": 1098, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1099", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 1993, pp. 344–359.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1099", "line_number": 1099, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1100", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[19] J. Clulow, G. P. Hancke, M. G. Kuhn, and T. Moore, “So near and yet so far: Distance-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1100", "line_number": 1100, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1101", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "bounding attacks in wireless networks,” in Security and Privacy in Ad-Hoc and Sensor", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1101", "line_number": 1101, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1102", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Networks, L. Buttyán, V. D. Gligor, and D. Westhoff, Eds.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1102", "line_number": 1102, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1103", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Berlin, Heidelberg: Springer", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1103", "line_number": 1103, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1104", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Berlin Heidelberg, 2006, pp. 83–97.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1104", "line_number": 1104, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1105", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[20] A. Ranganathan and S. Capkun, “Are we really close? Verifying proximity in wireless", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1105", "line_number": 1105, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1106", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "systems,” IEEE Security & Privacy, vol. 15, no. 3, pp. 52–58, 2017.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1106", "line_number": 1106, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1107", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[21] M. Singh, P. Leu, and S. Capkun, “UWB with pulse reordering: Securing ranging against", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1107", "line_number": 1107, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1108", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "relay and physical layer attacks.” IACR Cryptology ePrint Archive, vol. 2017, p. 1240, 2017.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1108", "line_number": 1108, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1109", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[22] S. Capkun and J.-P. Hubaux, “Secure positioning in wireless networks,” IEEE Journal on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1109", "line_number": 1109, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1110", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Selected Areas in Communications, vol. 24, no. 2, pp. 221–232, Feb 2006.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1110", "line_number": 1110, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1111", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[23] P. Leu, M. Singh, M. Roeschlin, K. G. Paterson, and S. Capkun, “Message time of arrival", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1111", "line_number": 1111, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1112", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "codes: A fundamental primitive for secure distance measurement,” IEEE Symposium on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1112", "line_number": 1112, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1113", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Security and Privacy, 2020.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1113", "line_number": 1113, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1114", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[24] G. P. Hancke and M. G. Kuhn, “An RFID distance bounding protocol,” in First International", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1114", "line_number": 1114, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1115", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Conference on Security and Privacy for Emerging Areas in Communications Networks", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1115", "line_number": 1115, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1116", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(SECURECOMM’05).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1116", "line_number": 1116, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1117", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2005, pp. 67–73.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1117", "line_number": 1117, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1118", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[25] M. Poturalski, M. Flury, P. Papadimitratos, J.-P. Hubaux, and J.-Y. Le Boudec, “Distance", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1118", "line_number": 1118, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1119", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "bounding with IEEE 802.15. 4a: Attacks and countermeasures,” IEEE Transactions on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1119", "line_number": 1119, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1120", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Wireless Communications, vol. 10, no. 4, pp. 1334–1344, 2011.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1120", "line_number": 1120, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1121", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[26] M. G. Kuhn and C. M. G. Kuhn, “Compromising emanations: Eavesdropping risks of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1121", "line_number": 1121, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1122", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "computer displays,” 2003.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1122", "line_number": 1122, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1123", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[27] M. G. Kuhn, “Electromagnetic eavesdropping risks of ﬂat-panel displays,” in International", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1123", "line_number": 1123, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1124", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Workshop on Privacy Enhancing Technologies.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1124", "line_number": 1124, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1125", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 2004, pp. 88–107.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1125", "line_number": 1125, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1126", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[28] M. Backes, T. Chen, M. Duermuth, H. P. A. Lensch, and M. Welk, “Tempest in a teapot:", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1126", "line_number": 1126, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1127", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Compromising reﬂections revisited,” in 2009 30th IEEE Symposium on Security and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1127", "line_number": 1127, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1128", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Privacy, May 2009, pp. 315–327.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1128", "line_number": 1128, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1129", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[29] D. Genkin, A. Shamir, and E. Tromer, “RSA key extraction via low-bandwidth acoustic", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1129", "line_number": 1129, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1130", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "cryptanalysis,” in Advances in Cryptology – CRYPTO 2014, J. A. Garay and R. Gennaro,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1130", "line_number": 1130, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1131", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Eds.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1131", "line_number": 1131, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1132", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Berlin, Heidelberg: Springer Berlin Heidelberg, 2014, pp. 444–461.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1132", "line_number": 1132, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1133", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[30] P. Marquardt, A. Verma, H. Carter, and P. Traynor, “(sp)iPhone: decoding vibrations from", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1133", "line_number": 1133, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1134", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "nearby keyboards using mobile phone accelerometers,” in Proceedings of the 18th ACM", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1134", "line_number": 1134, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1135", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "conference on Computer and communications security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1135", "line_number": 1135, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1136", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2011, pp. 551–562.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1136", "line_number": 1136, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1137", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[31] D. F. Kune, J. Backes, S. S. Clark, D. Kramer, M. Reynolds, K. Fu, Y. Kim, and W. Xu,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1137", "line_number": 1137, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1138", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "“Ghost talk: Mitigating emi signal injection attacks against analog sensors,” in 2013 IEEE", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1138", "line_number": 1138, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1139", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Symposium on Security and Privacy, May 2013, pp. 145–159.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1139", "line_number": 1139, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1140", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[32] G. Zhang, C. Yan, X. Ji, T. Zhang, T. Zhang, and W. Xu, “DolphinAttack: Inaudible voice", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1140", "line_number": 1140, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1141", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "commands,” in Proceedings of the 2017 ACM SIGSAC Conference on Computer and Com-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1141", "line_number": 1141, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1142", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "munications Security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1142", "line_number": 1142, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1143", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2017, pp. 103–117.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1143", "line_number": 1143, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1144", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[33] T. Trippel, O. Weisse, W. Xu, P. Honeyman, and K. Fu, “WALNUT: Waging doubt on the", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1144", "line_number": 1144, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1145", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "integrity of MEMS accelerometers with acoustic injection attacks,” in 2017 IEEE European", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1145", "line_number": 1145, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1146", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Symposium on Security and Privacy (EuroS&P), April 2017, pp. 3–18.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1146", "line_number": 1146, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1147", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[34] Y. Zhang and K. Rasmussen, “Detection of electromagnetic interference attacks on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1147", "line_number": 1147, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1148", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sensor systems,” in IEEE Symposium on Security and Privacy (S&P), May 2020.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1148", "line_number": 1148, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1149", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[35] W. van Eck, “Electromagnetic radiation from video display units: An eavesdropping", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1149", "line_number": 1149, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1150", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "risk?” Computers & Security, vol. 4, no. 4, pp. 269 – 286, 1985. [Online]. Available:", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1150", "line_number": 1150, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1151", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1151", "line_number": 1151, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1152", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 31", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1152", "line_number": 1152, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1153", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 33", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1153", "line_number": 1153, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1154", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "http://www.sciencedirect.com/science/article/pii/016740488590046X", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1154", "line_number": 1154, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1155", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[36] Y. Shoukry, P. Martin, Y. Yona, S. Diggavi, and M. Srivastava, “Pycra: Physical challenge-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1155", "line_number": 1155, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1156", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "response authentication for active sensors under spooﬁng attacks,” in Proceedings of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1156", "line_number": 1156, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1157", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the 22nd ACM SIGSAC Conference on Computer and Communications Security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1157", "line_number": 1157, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1158", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1158", "line_number": 1158, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1159", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2015, pp. 1004–1015.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1159", "line_number": 1159, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1160", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[37] L. Francis, G. P. Hancke, K. Mayes, and K. Markantonakis, “Practical relay attack on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1160", "line_number": 1160, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1161", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "contactless transactions by using NFC mobile phones.” IACR Cryptology ePrint Archive,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1161", "line_number": 1161, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1162", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "vol. 2011, p. 618, 2011.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1162", "line_number": 1162, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1163", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[38] M. Strohmeier, “Security in next generation air trafﬁc communication networks,” Ph.D.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1163", "line_number": 1163, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1164", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "dissertation, University of Oxford, 2016.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1164", "line_number": 1164, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1165", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[39] D. Forsberg, G. Horn, W.-D. Moeller, and V. Niemi, LTE Security, 2nd ed.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1165", "line_number": 1165, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1166", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Wiley Publishing,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1166", "line_number": 1166, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1167", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2012.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1167", "line_number": 1167, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1168", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[40] A. Ranganathan, “Physical-layer techniques for secure proximity veriﬁcation and local-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1168", "line_number": 1168, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1169", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ization,” PhD thesis, ETH Zurich, 2016.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1169", "line_number": 1169, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1170", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[41] D. Basin, J. Dreier, L. Hirschi, S. Radomirovic, R. Sasse, and V. Stettler, “A formal", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1170", "line_number": 1170, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1171", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "analysis of 5G authentication,” in Proceedings of the 2018 ACM SIGSAC Conference on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1171", "line_number": 1171, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1172", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Computer and Communications Security, ser. CCS ’18.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1172", "line_number": 1172, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1173", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "New York, NY, USA: ACM, 2018,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1173", "line_number": 1173, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1174", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pp. 1383–1396. [Online]. Available: http://doi.acm.org/10.1145/3243734.3243846", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1174", "line_number": 1174, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1175", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[42] S. N. Premnath, S. Jana, J. Croft, P. L. Gowda, M. Clark, S. K. Kasera, N. Patwari, and S. V.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1175", "line_number": 1175, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1176", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Krishnamurthy, “Secret key extraction from wireless signal strength in real environments,”", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1176", "line_number": 1176, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1177", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE Transactions on mobile Computing, vol. 12, no. 5, pp. 917–930, 2012.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1177", "line_number": 1177, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1178", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[43] S. Mathur, R. Miller, A. Varshavsky, W. Trappe, and N. Mandayam, “Proximate: proximity-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1178", "line_number": 1178, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1179", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "based secure pairing using ambient wireless signals,” in Proceedings of the 9th inter-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1179", "line_number": 1179, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1180", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "national conference on Mobile systems, applications, and services.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1180", "line_number": 1180, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1181", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2011, pp.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1181", "line_number": 1181, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1182", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "211–224.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1182", "line_number": 1182, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1183", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[44] J. Zhang, T. Q. Duong, A. Marshall, and R. Woods, “Key generation from wireless channels:", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1183", "line_number": 1183, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1184", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "A review,” Ieee access, vol. 4, pp. 614–626, 2016.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1184", "line_number": 1184, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1185", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[45] J. Zhang, A. Marshall, R. Woods, and T. Q. Duong, “Efﬁcient key generation by exploiting", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1185", "line_number": 1185, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1186", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "randomness from channel responses of individual OFDM subcarriers,” IEEE Transactions", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1186", "line_number": 1186, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1187", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on Communications, vol. 64, no. 6, pp. 2578–2588, 2016.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1187", "line_number": 1187, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1188", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[46] B. Azimi-Sadjadi, A. Kiayias, A. Mercado, and B. Yener, “Robust key generation from", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1188", "line_number": 1188, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1189", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "signal envelopes in wireless networks,” in Proceedings of the 14th ACM conference on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1189", "line_number": 1189, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1190", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Computer and communications security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1190", "line_number": 1190, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1191", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2007, pp. 401–410.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1191", "line_number": 1191, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1192", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[47] M. Strasser, C. Popper, S. Capkun, and M. Cagalj, “Jamming-resistant key establish-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1192", "line_number": 1192, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1193", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ment using uncoordinated frequency hopping,” in 2008 IEEE Symposium on Security", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1193", "line_number": 1193, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1194", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and Privacy (sp 2008).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1194", "line_number": 1194, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1195", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2008, pp. 64–78.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1195", "line_number": 1195, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1196", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[48] D. W. K. Ng, E. S. Lo, and R. Schober, “Robust beamforming for secure communication in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1196", "line_number": 1196, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1197", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "systems with wireless information and power transfer,” IEEE Transactions on Wireless", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1197", "line_number": 1197, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1198", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Communications, vol. 13, no. 8, pp. 4599–4615, 2014.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1198", "line_number": 1198, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1199", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[49] Y. Zheng, M. Schulz, W. Lou, Y. T. Hou, and M. Hollick, “Proﬁling the strength of physical-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1199", "line_number": 1199, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1200", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "layer security: A study in orthogonal blinding,” in Proceedings of the 9th ACM Conference", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1200", "line_number": 1200, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1201", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on Security & Privacy in Wireless and Mobile Networks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1201", "line_number": 1201, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1202", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2016, pp. 21–30.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1202", "line_number": 1202, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1203", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[50] M. Schulz, A. Loch, and M. Hollick, “Practical known-plaintext attacks against physical", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1203", "line_number": 1203, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1204", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "layer security in wireless mimo systems.” in The Network and Distributed System Security", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1204", "line_number": 1204, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1205", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Symposium (NDSS), 2014.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1205", "line_number": 1205, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1206", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[51] P. Robyns, P. Quax, and W. Lamotte, “PHY-layer security is no alternative to cryptography,”", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1206", "line_number": 1206, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1207", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in Proceedings of the 10th ACM Conference on Security and Privacy in Wireless and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1207", "line_number": 1207, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1208", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Mobile Networks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1208", "line_number": 1208, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1209", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2017, pp. 160–162.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1209", "line_number": 1209, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1210", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[52] H. Mahdavifar and A. Vardy, “Achieving the secrecy capacity of wiretap channels using", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1210", "line_number": 1210, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1211", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1211", "line_number": 1211, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1212", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 32", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1212", "line_number": 1212, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1213", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 34", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1213", "line_number": 1213, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1214", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "polar codes,” arXiv preprint arXiv:1007.3568, 2010.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1214", "line_number": 1214, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1215", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[53] P. Parada and R. Blahut, “Secrecy capacity of SIMO and slow fading channels,” in Pro-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1215", "line_number": 1215, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1216", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ceedings. International Symposium on Information Theory, 2005. ISIT 2005. IEEE, 2005,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1216", "line_number": 1216, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1217", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pp. 2152–2155.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1217", "line_number": 1217, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1218", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[54] A. Mukherjee, S. A. A. Fakoorian, J. Huang, and A. L. Swindlehurst, “Principles of physical", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1218", "line_number": 1218, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1219", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "layer security in multiuser wireless networks: A survey,” IEEE Communications Surveys", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1219", "line_number": 1219, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1220", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "& Tutorials, vol. 16, no. 3, pp. 1550–1573, 2014.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1220", "line_number": 1220, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1221", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[55] P. K. Gopala, L. Lai, and H. El Gamal, “On the secrecy capacity of fading channels,” in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1221", "line_number": 1221, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1222", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2007 IEEE International Symposium on Information Theory.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1222", "line_number": 1222, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1223", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2007, pp. 1306–1310.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1223", "line_number": 1223, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1224", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[56] W. Shen, P. Ning, X. He, and H. Dai, “Ally friendly jamming: How to jam your enemy and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1224", "line_number": 1224, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1225", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "maintain your own wireless connectivity at the same time,” in 2013 IEEE Symposium on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1225", "line_number": 1225, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1226", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Security and Privacy.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1226", "line_number": 1226, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1227", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2013, pp. 174–188.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1227", "line_number": 1227, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1228", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[57] D. S. Berger, F. Gringoli, N. Facchi, I. Martinovic, and J. Schmitt, “Gaining insight on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1228", "line_number": 1228, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1229", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "friendly jamming in a real-world IEEE 802.11 network,” in Proceedings of the 2014 ACM", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1229", "line_number": 1229, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1230", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "conference on Security and privacy in wireless & mobile networks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1230", "line_number": 1230, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1231", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2014, pp.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1231", "line_number": 1231, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1232", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "105–116.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1232", "line_number": 1232, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1233", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[58] J. P. Vilela, M. Bloch, J. Barros, and S. W. McLaughlin, “Wireless secrecy regions with", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1233", "line_number": 1233, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1234", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "friendly jamming,” IEEE Transactions on Information Forensics and Security, vol. 6, no. 2,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1234", "line_number": 1234, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1235", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pp. 256–266, 2011.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1235", "line_number": 1235, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1236", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[59] N. O. Tippenhauer, L. Malisa, A. Ranganathan, and S. Capkun, “On limitations of friendly", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1236", "line_number": 1236, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1237", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "jamming for conﬁdentiality,” in 2013 IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1237", "line_number": 1237, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1238", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1238", "line_number": 1238, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1239", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2013, pp. 160–173.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1239", "line_number": 1239, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1240", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[60] S. Capkun, M. Cagalj, G. Karame, and N. O. Tippenhauer, “Integrity regions: Authentication", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1240", "line_number": 1240, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1241", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "through presence in wireless networks,” IEEE Transactions on Mobile Computing, vol. 9,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1241", "line_number": 1241, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1242", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "no. 11, pp. 1608–1621, 2010.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1242", "line_number": 1242, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1243", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[61] A. Polydoros and K. Woo, “LPI detection of frequency-hopping signals using autocor-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1243", "line_number": 1243, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1244", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "relation techniques,” IEEE journal on selected areas in communications, vol. 3, no. 5, pp.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1244", "line_number": 1244, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1245", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "714–726, 1985.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1245", "line_number": 1245, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1246", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[62] R. F. Mills and G. E. Prescott, “Waveform design and analysis of frequency hopping LPI", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1246", "line_number": 1246, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1247", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "networks,” in Proceedings of MILCOM’95, vol. 2.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1247", "line_number": 1247, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1248", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 1995, pp. 778–782.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1248", "line_number": 1248, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1249", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[63] L. Frikha, Z. Trabelsi, and W. El-Hajj, “Implementation of a covert channel in the 802.11", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1249", "line_number": 1249, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1250", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "header,” in 2008 International Wireless Communications and Mobile Computing Confer-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1250", "line_number": 1250, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1251", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ence.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1251", "line_number": 1251, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1252", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2008, pp. 594–599.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1252", "line_number": 1252, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1253", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[64] C. Popper, M. Strasser, and S. Capkun, “Anti-jamming broadcast communication using", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1253", "line_number": 1253, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1254", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "uncoordinated spread spectrum techniques,” IEEE Journal on Selected Areas in Com-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1254", "line_number": 1254, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1255", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "munications, vol. 28, no. 5, pp. 703–715, June 2010.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1255", "line_number": 1255, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1256", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[65] W. Xu, W. Trappe, and Y. Zhang, “Anti-jamming timing channels for wireless networks,” in", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1256", "line_number": 1256, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1257", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Proceedings of the First ACM Conference on Wireless Network Security, ser. WiSec ’08.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1257", "line_number": 1257, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1258", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "New York, NY, USA: ACM, 2008, pp. 203–213.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1258", "line_number": 1258, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1259", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[66] M. Strasser, C. Pöpper, and S. Čapkun, “Efﬁcient uncoordinated FHSS anti-jamming", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1259", "line_number": 1259, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1260", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "communication,” in Proceedings of the Tenth ACM International Symposium on Mobile", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1260", "line_number": 1260, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1261", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Ad Hoc Networking and Computing, ser. MobiHoc ’09.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1261", "line_number": 1261, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1262", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "New York, NY, USA: ACM, 2009,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1262", "line_number": 1262, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1263", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pp. 207–218.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1263", "line_number": 1263, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1264", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[67] K. Grover, A. Lim, and Q. Yang, “Jamming and anti-jamming techniques in wireless", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1264", "line_number": 1264, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1265", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "networks: a survey,” International Journal of Ad Hoc and Ubiquitous Computing, vol. 17,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1265", "line_number": 1265, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1266", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "no. 4, pp. 197–215, 2014.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1266", "line_number": 1266, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1267", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[68] W. Wang, Z. Sun, S. Piao, B. Zhu, and K. Ren, “Wireless physical-layer identiﬁcation: Mod-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1267", "line_number": 1267, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1268", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "eling and validation,” IEEE Transactions on Information Forensics and Security, vol. 11,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1268", "line_number": 1268, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1269", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "no. 9, pp. 2091–2106, 2016.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1269", "line_number": 1269, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1270", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1270", "line_number": 1270, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1271", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 33", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1271", "line_number": 1271, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1272", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 35", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1272", "line_number": 1272, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1273", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[69] T. J. Bihl, K. W. Bauer, and M. A. Temple, “Feature selection for RF ﬁngerprinting with", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1273", "line_number": 1273, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1274", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "multiple discriminant analysis and using zigbee device emissions,” IEEE Transactions", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1274", "line_number": 1274, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1275", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on Information Forensics and Security, vol. 11, no. 8, pp. 1862–1874, 2016.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1275", "line_number": 1275, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1276", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[70] T. D. Vo-Huu, T. D. Vo-Huu, and G. Noubir, “Fingerprinting Wi-Fi devices using software de-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1276", "line_number": 1276, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1277", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ﬁned radios,” in Proceedings of the 9th ACM Conference on Security & Privacy in Wireless", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1277", "line_number": 1277, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1278", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and Mobile Networks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1278", "line_number": 1278, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1279", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2016, pp. 3–14.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1279", "line_number": 1279, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1280", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[71] S. Capkun, K. El Defrawy, and G. Tsudik, “Group distance bounding protocols,” in Interna-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1280", "line_number": 1280, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1281", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "tional Conference on Trust and Trustworthy Computing.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1281", "line_number": 1281, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1282", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 2011, pp. 302–312.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1282", "line_number": 1282, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1283", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[72] N. O. Tippenhauer and S. Čapkun, “Id-based secure distance bounding and localization,”", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1283", "line_number": 1283, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1284", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in European Symposium on Research in Computer Security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1284", "line_number": 1284, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1285", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 2009, pp. 621–", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1285", "line_number": 1285, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1286", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "636.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1286", "line_number": 1286, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1287", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[73] M. Kuhn, H. Luecken, and N. O. Tippenhauer, “UWB impulse radio based distance bound-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1287", "line_number": 1287, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1288", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ing,” in 2010 7th Workshop on Positioning, Navigation and Communication.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1288", "line_number": 1288, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1289", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2010,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1289", "line_number": 1289, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1290", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pp. 28–37.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1290", "line_number": 1290, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1291", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[74] L. Bussard and W. Bagga, “Distance-bounding proof of knowledge to avoid real-time", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1291", "line_number": 1291, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1292", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacks,” in IFIP International Information Security Conference.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1292", "line_number": 1292, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1293", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 2005, pp.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1293", "line_number": 1293, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1294", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "223–238.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1294", "line_number": 1294, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1295", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[75] D. Singelée and B. Preneel, “Distance bounding in noisy environments,” in European", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1295", "line_number": 1295, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1296", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Workshop on Security in Ad-hoc and Sensor Networks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1296", "line_number": 1296, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1297", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 2007, pp. 101–115.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1297", "line_number": 1297, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1298", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[76] K. B. Rasmussen and S. Capkun, “Realization of RF distance bounding.” in USENIX", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1298", "line_number": 1298, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1299", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Security Symposium, 2010, pp. 389–402.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1299", "line_number": 1299, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1300", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[77] A. Ranganathan, N. O. Tippenhauer, B. Škorić, D. Singelée, and S. Čapkun, “Design and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1300", "line_number": 1300, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1301", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "implementation of a terrorist fraud resilient distance bounding system,” in European", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1301", "line_number": 1301, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1302", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Symposium on Research in Computer Security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1302", "line_number": 1302, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1303", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 2012, pp. 415–432.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1303", "line_number": 1303, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1304", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[78] N. O. Tippenhauer, H. Luecken, M. Kuhn, and S. Capkun, “UWB rapid-bit-exchange system", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1304", "line_number": 1304, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1305", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "for distance bounding,” in Proceedings of the 8th ACM Conference on Security & Privacy", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1305", "line_number": 1305, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1306", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in Wireless and Mobile Networks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1306", "line_number": 1306, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1307", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2015, p. 2.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1307", "line_number": 1307, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1308", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[79] S. Drimer, S. J. Murdoch et al., “Keep your enemies close: Distance bounding against", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1308", "line_number": 1308, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1309", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "smartcard relay attacks.” in USENIX security symposium, vol. 312, 2007.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1309", "line_number": 1309, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1310", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[80] C. Cremers, K. B. Rasmussen, B. Schmidt, and S. Capkun, “Distance hijacking attacks on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1310", "line_number": 1310, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1311", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distance bounding protocols,” in 2012 IEEE Symposium on Security and Privacy.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1311", "line_number": 1311, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1312", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1312", "line_number": 1312, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1313", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "2012, pp. 113–127.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1313", "line_number": 1313, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1314", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[81] G. P. Hancke and M. G. Kuhn, “Attacks on time-of-ﬂight distance bounding channels,”", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1314", "line_number": 1314, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1315", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in Proceedings of the ﬁrst ACM conference on Wireless network security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1315", "line_number": 1315, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1316", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2008,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1316", "line_number": 1316, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1317", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "pp. 194–202.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1317", "line_number": 1317, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1318", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[82] K. B. Rasmussen and S. Čapkun, “Location privacy of distance bounding protocols,”", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1318", "line_number": 1318, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1319", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "in Proceedings of the 15th ACM conference on Computer and communications security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1319", "line_number": 1319, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1320", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2008, pp. 149–160.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1320", "line_number": 1320, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1321", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[83] M. Flury, M. Poturalski, P. Papadimitratos, J.-P. Hubaux, and J.-Y. Le Boudec, “Effective-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1321", "line_number": 1321, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1322", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ness of distance-decreasing attacks against impulse radio ranging,” in Proceedings of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1322", "line_number": 1322, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1323", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the third ACM conference on Wireless network security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1323", "line_number": 1323, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1324", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2010, pp. 117–128.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1324", "line_number": 1324, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1325", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[84] R. Shokri, M. Poturalski, G. Ravot, P. Papadimitratos, and J.-P. Hubaux, “A practical", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1325", "line_number": 1325, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1326", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "secure neighbor veriﬁcation protocol for wireless sensor networks,” in Proceedings of", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1326", "line_number": 1326, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1327", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "the second ACM conference on Wireless network security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1327", "line_number": 1327, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1328", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2009, pp. 193–200.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1328", "line_number": 1328, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1329", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[85] S. Čapkun and J.-P. Hubaux, “Secure positioning of wireless devices with application to", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1329", "line_number": 1329, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1330", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sensor networks,” in IEEE infocom, no. CONF, 2005.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1330", "line_number": 1330, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1331", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[86] J. T. Chiang, J. J. Haas, and Y.-C. Hu, “Secure and precise location veriﬁcation using", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1331", "line_number": 1331, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1332", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "distance bounding and simultaneous multilateration,” in Proceedings of the second ACM", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1332", "line_number": 1332, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1333", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "conference on Wireless network security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1333", "line_number": 1333, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1334", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2009, pp. 181–192.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1334", "line_number": 1334, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1335", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1335", "line_number": 1335, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1336", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 34", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1336", "line_number": 1336, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1337", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "## Page 36", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1337", "line_number": 1337, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1338", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[87] N. Basilico, N. Gatti, M. Monga, and S. Sicari, “Security games for node localization", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1338", "line_number": 1338, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1339", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "through veriﬁable multilateration,” IEEE Transactions on Dependable and Secure Com-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1339", "line_number": 1339, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1340", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "puting, vol. 11, no. 1, pp. 72–85, 2013.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1340", "line_number": 1340, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1341", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[88] L. Lazos, R. Poovendran, and S. Čapkun, “Rope: robust position estimation in wireless", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1341", "line_number": 1341, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1342", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "sensor networks,” in Proceedings of the 4th international symposium on Information", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1342", "line_number": 1342, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1343", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "processing in sensor networks.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1343", "line_number": 1343, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1344", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE Press, 2005, p. 43.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1344", "line_number": 1344, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1345", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[89] M. Backes, M. Dürmuth, S. Gerling, M. Pinkal, and C. Sporleder, “Acoustic side-channel", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1345", "line_number": 1345, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1346", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "attacks on printers.” in USENIX Security symposium, 2010, pp. 307–322.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1346", "line_number": 1346, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1347", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[90] D. Balzarotti, M. Cova, and G. Vigna, “Clearshot: Eavesdropping on keyboard input from", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1347", "line_number": 1347, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1348", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "video,” in 2008 IEEE Symposium on Security and Privacy (sp 2008).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1348", "line_number": 1348, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1349", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2008, pp.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1349", "line_number": 1349, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1350", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "170–183.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1350", "line_number": 1350, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1351", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[91] M. Backes, M. Dürmuth, and D. Unruh, “Compromising reﬂections-or-how to read lcd", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1351", "line_number": 1351, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1352", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "monitors around the corner,” in 2008 IEEE Symposium on Security and Privacy (sp 2008).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1352", "line_number": 1352, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1353", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2008, pp. 158–169.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1353", "line_number": 1353, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1354", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[92] R. Raguram, A. M. White, D. Goswami, F. Monrose, and J.-M. Frahm, “iSpy: automatic", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1354", "line_number": 1354, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1355", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "reconstruction of typed input from compromising reﬂections,” in Proceedings of the 18th", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1355", "line_number": 1355, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1356", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM conference on Computer and communications security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1356", "line_number": 1356, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1357", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2011, pp. 527–536.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1357", "line_number": 1357, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1358", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[93] X. Liu, Z. Zhou, W. Diao, Z. Li, and K. Zhang, “When good becomes evil: Keystroke", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1358", "line_number": 1358, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1359", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "inference with smartwatch,” in Proceedings of the 22nd ACM SIGSAC Conference on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1359", "line_number": 1359, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1360", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Computer and Communications Security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1360", "line_number": 1360, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1361", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2015, pp. 1273–1285.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1361", "line_number": 1361, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1362", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[94] C. Kasmi and J. L. Esteves, “IEMI threats for information security: Remote command", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1362", "line_number": 1362, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1363", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "injection on modern smartphones,” IEEE Transactions on Electromagnetic Compatibility,", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1363", "line_number": 1363, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1364", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "vol. 57, no. 6, pp. 1752–1755, 2015.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1364", "line_number": 1364, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1365", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[95] Y. Park, Y. Son, H. Shin, D. Kim, and Y. Kim, “This ain’t your dose: Sensor spooﬁng attack", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1365", "line_number": 1365, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1366", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "on medical infusion pump,” in 10th USENIX Workshop on Offensive Technologies (WOOT", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1366", "line_number": 1366, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1367", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "16), 2016.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1367", "line_number": 1367, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1368", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[96] K. B. Rasmussen, C. Castelluccia, T. S. Heydt-Benjamin, and S. Capkun, “Proximity-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1368", "line_number": 1368, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1369", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "based access control for implantable medical devices,” in Proceedings of the 16th ACM", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1369", "line_number": 1369, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1370", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "conference on Computer and communications security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1370", "line_number": 1370, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1371", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2009, pp. 410–419.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1371", "line_number": 1371, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1372", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[97] J. Selvaraj, G. Y. Dayanıklı, N. P. Gaunkar, D. Ware, R. M. Gerdes, M. Mina et al., “Electro-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1372", "line_number": 1372, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1373", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "magnetic induction attacks against embedded systems,” in Proceedings of the 2018 on", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1373", "line_number": 1373, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1374", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Asia Conference on Computer and Communications Security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1374", "line_number": 1374, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1375", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "ACM, 2018, pp. 499–510.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1375", "line_number": 1375, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1376", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[98] Y. Son, H. Shin, D. Kim, Y. Park, J. Noh, K. Choi, J. Choi, and Y. Kim, “Rocking drones with", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1376", "line_number": 1376, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1377", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "intentional sound noise on gyroscopic sensors,” in 24th USENIX Security Symposium", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1377", "line_number": 1377, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1378", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "(USENIX Security 15), 2015, pp. 881–896.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1378", "line_number": 1378, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1379", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[99] G. Madlmayr, J. Langer, C. Kantner, and J. Scharinger, “NFC devices: Security and", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1379", "line_number": 1379, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1380", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "privacy,” in 2008 Third International Conference on Availability, Reliability and Security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1380", "line_number": 1380, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1381", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2008, pp. 642–647.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1381", "line_number": 1381, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1382", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[100] S. Burkard, “Near ﬁeld communication in smartphones,” Dep. of Telecommunication", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1382", "line_number": 1382, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1383", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Systems, Service-centric Networking, Berlin Institute of Technology, Germany, 2012.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1383", "line_number": 1383, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1384", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[101] N. Alexiou, S. Basagiannis, and S. Petridou, “Security analysis of NFC relay attacks", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1384", "line_number": 1384, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1385", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "using probabilistic model checking,” in 2014 International Wireless Communications", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1385", "line_number": 1385, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1386", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and Mobile Computing Conference (IWCMC).", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1386", "line_number": 1386, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1387", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "IEEE, 2014, pp. 524–529.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1387", "line_number": 1387, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1388", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[102] A. Costin and A. Francillon, “Ghost in the air (trafﬁc): On insecurity of ADS-B protocol", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1388", "line_number": 1388, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1389", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and practical attacks on ADS-B devices,” Black Hat USA, pp. 1–12, 2012.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1389", "line_number": 1389, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1390", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[103] M. Schäfer, V. Lenders, and I. Martinovic, “Experimental analysis of attacks on next gen-", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1390", "line_number": 1390, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1391", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "eration air trafﬁc communication,” in International Conference on Applied Cryptography", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1391", "line_number": 1391, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1392", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "and Network Security.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1392", "line_number": 1392, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1393", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Springer, 2013, pp. 253–271.", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1393", "line_number": 1393, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1394", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "[104] M. Smith, D. Moser, M. Strohmeier, V. Lenders, and I. Martinovic, “Economy class crypto:", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1394", "line_number": 1394, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1395", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "KA Physical Layer & Telecommunications Security | July 2021", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1395", "line_number": 1395, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
{"chunk_id": "line-1396", "filename": "Physical_Layer_v1.0.1_processed.txt", "content": "Page 35", "metadata": {"filename": "Physical_Layer_v1.0.1_processed.txt", "chunk_id": "line-1396", "line_number": 1396, "source": "知识库\\output\\Physical_Layer_v1.0.1_processed.txt"}}
