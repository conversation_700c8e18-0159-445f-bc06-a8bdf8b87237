# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import traceback
from typing import Dict, List, Tuple

from bs4 import BeautifulSoup

from pikerag.prompts import BaseContentParser, CommunicationProtocol, MessageTemplate
from pikerag.prompts.qa.generation import GenerationQaParser
from pikerag.utils.lxml_parser import get_soup_from_content


multiple_choice_qa_template = MessageTemplate(
    template=[
        ("system", "You are a helpful assistant good at {knowledge_domain} knowledge that can help people answer {knowledge_domain} questions."),
        ("user", """
# Task
Your task is to think step by step and then choose the correct option from the given options, the chosen option should be correct and the most suitable one to answer the given question. If you don't have sufficient data to determine, randomly choose one option from the given options.

# Output format
The output should strictly follow the format below, do not add any redundant information.

<result>
  <thinking>Your thinking for the given question.</thinking>
  <answer>
    <mask>The chosen option mask. Please note that only one single mask is allowable.</mask>
    <option>The option detail corresponds to the chosen option mask.</option>
  </answer>
</result>

# Question
{content}

# Options
{options_str}

# Thinking and Answer
""".strip()),
    ],
    input_variables=["knowledge_domain", "content", "options_str"],
)

# 修改提示词模板  
cti_generation_qa_template = MessageTemplate(
    template=[
        ("system", "你是一个网络安全威胁情报专家，擅长分析APT组织、攻击手法和恶意软件。你具备深厚的威胁情报背景知识，理解不同攻击者的特征、工具、战术和程序(TTPs)。"),
        ("user", """
# 任务
请回答有关网络威胁情报的问题。
{instructions}
         
# 重要规则
- 只回答参考资料中明确提及的内容
- 如果参考资料中没有相关信息，明确说明"参考资料中没有提供这方面的信息"
- 不要基于一般知识补充或推测未在参考资料中明确提及的信息
- IOC部分只包含技术指标（IP、域名、URL、文件哈希等），CVE编号归入"相关漏洞"
- 遇到"Contact <EMAIL>"等联系方式，不要作为IOC处理

# 回答格式
请用以下纯文本格式（不要使用JSON）提供分析结果：

## 总体摘要
[在此提供文档总体内容的概述]

## 威胁行为者
[在此列出文档中提到的威胁行为者]

## 相关漏洞
[在此列出文档中提到的漏洞，一个漏洞一行，格式为：CVE编号 - 描述]

## 攻击技术(TTPs)
[在此列出文档中提到的攻击技术,及其对应的TTPs_ID,如有必要]

## 相关威胁指标(IOCs)
[请按以下格式列出文档中的威胁指标，每类一行：
- **IP地址**：[IP地址] - [用途描述]
- **域名**：[域名] - [功能描述]
- **URL**：[完整URL] - [用途说明]
- **文件哈希**：[哈希类型]:[哈希值] - [文件详情]
- **文件路径**：[完整路径] - [文件性质]
- **注册表项**：[注册表路径] - [作用说明]
- **进程名**：[进程名称] - [恶意行为描述]
注意：只包含技术指标，CVE编号放在"相关漏洞"部分]

## 分析理由
[请说明信息来源，按以下格式：

**文档内容分析**：
- 列出直接来自主文档的关键信息
- 引用具体段落或部分

**外部知识使用**：
- 明确说明使用了哪些辅助背景知识
- 例如：CVE详情、CVSS评分、攻击技术细节等
- 如未使用外部知识，说明"仅基于文档内容分析"

**重要**：如果使用了外部知识库的任何信息（CVE详情、评分、技术说明等），必须明确承认]


# 问题
{content}

{references_str}
"""),
    ],
    input_variables=["content", "instructions", "references_str"],
    partial_variables={
        "system_prompt": "你是一个网络安全威胁情报专家，擅长分析APT组织、攻击手法和恶意软件。你具备深厚的威胁情报背景知识，理解不同攻击者的特征、工具、战术和程序(TTPs)。",
    },
)



class MultipleChoiceQaParser(BaseContentParser):
    def __init__(self) -> None:
        self.option_masks: List[str] = []
        self.options: Dict[str, str] = {}

    def encode(self, content: str, options: Dict[str, str], answer_mask_labels: List[str], **kwargs) -> Tuple[str, dict]:
        self.option_masks = sorted(list(options.keys()))
        self.options = options.copy()

        # NOTE: could enable re-ordering method in the future, do remember to check the answer mask as well.
        options_str = "\n".join([f"{key}: {self.options[key]}" for key in self.option_masks])

        for mask_label in answer_mask_labels:
            assert mask_label in self.option_masks, (
                f"Given answer mask label {mask_label}, but no corresponding option provided: {self.option_masks}"
            )

        return content, {"options_str": options_str}

    # TODO: update the decode interface to be Tuple[answer, dict]
    def decode(self, content: str, options: Dict[str, str], **kwargs) -> dict:
        if content is None or content == "":
            return {}

        try:
            result_soup: BeautifulSoup = get_soup_from_content(content, tag="result")
            if result_soup is not None:
                thinking_soup = result_soup.find("thinking")
                answer_soup = result_soup.find("answer")
            else:
                thinking_soup = get_soup_from_content(content, tag="thinking")
                answer_soup = get_soup_from_content(content, "answer")

            if thinking_soup is not None:
                thinking = thinking_soup.text
            else:
                thinking = ""

            if answer_soup is not None:
                mask_soup = answer_soup.find("mask")
                mask = mask_soup.text.strip() if mask_soup is not None else ""
                option_soup = answer_soup.find("option")
                option = option_soup.text.strip() if option_soup is not None else ""
            else:
                mask = ""
                option = ""

            if len(mask) == 1:
                assert mask in self.option_masks, f"choose {mask} from {self.option_masks}\n{content}"
                if option != self.options[mask]:
                    print()
                    print(f"Answer option: [{option}]")
                    print(f"But the Given: [{self.options[mask]}]")
            elif len(mask) == 0:
                print("No mask extracted")
            else:
                print(f"Multiple options chosen: {mask}")

        except Exception as e:
            print("Content:")
            print(content)
            print("Exception")
            print(e)
            traceback.print_exc()
            exit(0)

        return {
            "thinking": thinking,
            "answer": mask,
            "chosen_option": option,
        }


class MultipleChoiceQaWithReferenceParser(MultipleChoiceQaParser):
    def encode(self, content: str, options: Dict[str, str], answer_mask_labels: List[str], **kwargs) -> Tuple[str, Dict]:
        content, supplementary = super().encode(content, options, answer_mask_labels[0], **kwargs)

        references = kwargs.get("references", [])
        supplementary["references_str"] = "\n".join([reference.strip() for reference in references])

        return content, supplementary


multiple_choice_qa_protocol = CommunicationProtocol(
    template=multiple_choice_qa_template,
    parser=MultipleChoiceQaParser(),
)


cti_multiple_choice_qa_with_reference_protocol = CommunicationProtocol(
    template=cti_generation_qa_template,
     parser=GenerationQaParser(),
)

