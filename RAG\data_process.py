# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import argparse
import os
import shutil
import mysql.connector
from mysql.connector import <PERSON>rror
import yaml
from tqdm import tqdm
import json
import time
import glob

from langchain_core.documents import Document
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import HuggingFaceEmbeddings

from pikerag.utils.config_loader import load_dot_env
from pikerag.workflows.chunking import ChunkingWorkflow


# python .\RAG\data_process.py .\configs\chunking.yml

db_config = {
    'host': '127.0.0.1',
    'user': 'CTI',
    'password': 'cti0cd23',
    'database': 'CTI_DB',
    'port': 3306,
    'charset': 'utf8mb4'
}

def load_yaml_config(config_path: str, args: argparse.Namespace) -> dict:
    with open(config_path, "r", encoding="utf-8") as fin:
        yaml_config: dict = yaml.safe_load(fin)

    # Create logging dir if not exists
    experiment_name = yaml_config["experiment_name"]
    log_dir = os.path.join(yaml_config["log_root_dir"], experiment_name)
    yaml_config["log_dir"] = log_dir
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    shutil.copy(config_path, log_dir)

    # LLM cache config
    if "llm_client" in yaml_config:
        if yaml_config["llm_client"]["cache_config"]["location_prefix"] is None:
            yaml_config["llm_client"]["cache_config"]["location_prefix"] = experiment_name

    # 处理输入源类型
    if yaml_config.get("input_source_type") == "database":
        # 数据库配置检查
        assert "database_setting" in yaml_config, "数据库设置缺失！"
        assert "db_path" in yaml_config["database_setting"], "数据库路径缺失！"
    else:
        # 文件输入配置检查
        input_doc_dir = yaml_config["input_doc_setting"]["doc_dir"]
        assert os.path.exists(input_doc_dir), f"输入文档目录 {input_doc_dir} 不存在！"
        if "extensions" not in yaml_config["input_doc_setting"]:
            yaml_config["input_doc_setting"]["extensions"] = None
        elif isinstance(yaml_config["input_doc_setting"]["extensions"], str):
            yaml_config["input_doc_setting"]["extensions"] = [yaml_config["input_doc_setting"]["extensions"]]

    # 输出目录
    output_dir: str = yaml_config["output_doc_setting"]["doc_dir"]
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    else:
        # 允许向非空目录写入，只检查是否为文件
        if os.path.isfile(output_dir):
            raise ValueError(f"输出路径 {output_dir} 是一个文件，无法作为输出目录！")
        
        # 如果需要覆盖已有数据，可以通过配置控制
        if yaml_config["output_doc_setting"].get("overwrite_existing", False):
            print(f"警告：将覆盖输出目录 {output_dir} 中的现有数据")
        else:
            print(f"注意：将向输出目录 {output_dir} 中添加数据")

    return yaml_config


def fetch_threat_intel_data_from_mysql(db_config, limit=None, offset=0, resume=False, progress_file="data/processing_progress.json"):
    """从MySQL数据库获取威胁情报数据，支持续传功能"""
    print(f"正在从MySQL数据库获取威胁情报数据: {db_config['database']}@{db_config['host']}")
    
    # 如果需要续传，尝试读取上次处理的进度
    if resume and os.path.exists(progress_file):
        try:
            with open(progress_file, 'r') as f:
                progress_data = json.load(f)
                if progress_data.get("db_host") == db_config['host'] and progress_data.get("db_name") == db_config['database']:
                    offset = progress_data.get("next_offset", 0)
                    print(f"续传模式: 从偏移量 {offset} 开始处理")
        except Exception as e:
            print(f"读取进度文件失败: {e}，将从偏移量 {offset} 开始")
    
    conn = None
    try:
        # 连接到MySQL数据库
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)
        
        # 获取各个表的数据
        all_records = []
        
        # 1. 从group表获取数据
        print("获取group表数据...")
        group_query = """
            SELECT 
                group_id as id, 
                group_name as title,
                group_description as extracted_text,
                'group' as source_table
            FROM `group` 
            ORDER BY group_id
        """
        if limit:
            group_query += f" LIMIT {limit}"
        if offset > 0:
            group_query += f" OFFSET {offset}"
            
        cursor.execute(group_query)
        group_records = cursor.fetchall()
        print(f"从group表获取了 {len(group_records)} 条记录")
        all_records.extend(group_records)
        
        # 2. 从malware表获取数据 - 首先检查表结构
        print("获取malware表结构...")
        cursor.execute("SHOW COLUMNS FROM malware")
        columns = [col['Field'] for col in cursor.fetchall()]
        print(f"malware表有以下列: {columns}")
        
        # 构建动态查询，只使用实际存在的列
        select_parts = []
        concat_parts = []
        
        # 始终包括malware_id
        select_parts.append("malware_id as id")
        
        # 检查并添加可能的列
        if 'md5' in columns:
            concat_parts.append("IFNULL(md5, '')")
        if 'sha1' in columns:
            concat_parts.append("IFNULL(sha1, '')")
        if 'sha256' in columns:
            concat_parts.append("IFNULL(sha256, '')")
        if 'reference' in columns:
            concat_parts.append("IFNULL(reference, '')")
        
        # 构建标题和内容
        if concat_parts:
            # 构建标题
            select_parts.append(f"CONCAT({', ' .join(concat_parts)}) as title")
            
            # 构建提取文本
            extracted_parts = []
            if 'md5' in columns:
                extracted_parts.append("'MD5: ', IFNULL(md5, 'N/A')")
            if 'sha1' in columns:
                extracted_parts.append("'SHA1: ', IFNULL(sha1, 'N/A')")
            if 'sha256' in columns:
                extracted_parts.append("'SHA256: ', IFNULL(sha256, 'N/A')")
            if 'reference' in columns:
                extracted_parts.append("'Reference: ', IFNULL(reference, 'N/A')")
            
            if extracted_parts:
                extracted_text = "CONCAT({}) as extracted_text".format(", '\\n', ".join(extracted_parts))
                select_parts.append(extracted_text)
            else:
                select_parts.append("'' as extracted_text")
        else:
            # 如果没有任何列可用，使用ID作为标题
            select_parts.append("malware_id as title")
            select_parts.append("'' as extracted_text")
            
        # 添加来源表
        select_parts.append("'malware' as source_table")
        
        # 构建完整的malware查询
        malware_query = f"""
            SELECT 
                {', '.join(select_parts)}
            FROM malware
            ORDER BY malware_id
        """
        
        print("获取malware表数据...")
        if limit:
            malware_query += f" LIMIT {limit}"
        if offset > 0:
            malware_query += f" OFFSET {offset}"
            
        cursor.execute(malware_query)
        malware_records = cursor.fetchall()
        print(f"从malware表获取了 {len(malware_records)} 条记录")
        all_records.extend(malware_records)
        
        # 3. 从ttps表获取数据
        # 首先检查表是否存在以及结构
        print("检查ttps表...")
        cursor.execute("SHOW TABLES LIKE 'ttps'")
        if cursor.fetchone():
            # 检查表结构
            cursor.execute("SHOW COLUMNS FROM ttps")
            ttp_columns = {col['Field'] for col in cursor.fetchall()}
            
            # 构建查询
            if 'ttp_id' in ttp_columns and 'id_number' in ttp_columns and 'ttp_name' in ttp_columns:
                ttps_query = """
                    SELECT 
                        ttp_id as id,
                        CONCAT(id_number, ': ', ttp_name) as title,
                """
                if 'ttp_description' in ttp_columns:
                    ttps_query += "ttp_description as extracted_text,"
                else:
                    ttps_query += "'' as extracted_text,"
                    
                ttps_query += """
                        'ttps' as source_table
                    FROM ttps
                    ORDER BY ttp_id
                """
                
                if limit:
                    ttps_query += f" LIMIT {limit}"
                if offset > 0:
                    ttps_query += f" OFFSET {offset}"
                
                print("获取ttps表数据...")
                cursor.execute(ttps_query)
                ttps_records = cursor.fetchall()
                print(f"从ttps表获取了 {len(ttps_records)} 条记录")
                all_records.extend(ttps_records)
            else:
                print("ttps表结构不符合要求，跳过")
        else:
            print("ttps表不存在，跳过")
        
        # 4. 从event表获取数据
        # 首先检查表是否存在以及结构
        print("检查event表...")
        cursor.execute("SHOW TABLES LIKE 'event'")
        if cursor.fetchone():
            # 检查表结构
            cursor.execute("SHOW COLUMNS FROM event")
            event_columns = {col['Field'] for col in cursor.fetchall()}
            
            # 构建查询
            if 'event_id' in event_columns:
                event_query = """
                    SELECT 
                        event_id as id,
                """
                
                if 'report_name' in event_columns:
                    event_query += "report_name as title,"
                else:
                    event_query += "CONCAT('Event-', event_id) as title,"
                
                if 'report_description' in event_columns:
                    event_query += "report_description as extracted_text,"
                else:
                    event_query += "'' as extracted_text,"
                    
                event_query += """
                        'event' as source_table
                    FROM event
                    ORDER BY event_id
                """
                
                if limit:
                    event_query += f" LIMIT {limit}"
                if offset > 0:
                    event_query += f" OFFSET {offset}"
                
                print("获取event表数据...")
                cursor.execute(event_query)
                event_records = cursor.fetchall()
                print(f"从event表获取了 {len(event_records)} 条记录")
                all_records.extend(event_records)
            else:
                print("event表结构不符合要求，跳过")
        else:
            print("event表不存在，跳过")
        
        # 获取总记录数
        total_count = len(all_records)
        print(f"总共获取了 {total_count} 条记录")
        
        # 保存下一次的起始偏移量
        if resume:
            next_offset = offset + total_count
            progress_data = {
                "db_host": db_config['host'],
                "db_name": db_config['database'],
                "last_processed_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "records_processed": total_count,
                "next_offset": next_offset,
                "total_processed": next_offset
            }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(progress_file), exist_ok=True)
            
            with open(progress_file, 'w') as f:
                json.dump(progress_data, f, indent=2)
            print(f"已记录处理进度，下次将从偏移量 {next_offset} 开始")
        
        return all_records
    except Error as e:
        print(f"MySQL数据库操作失败: {e}")
        return []
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()
            print("数据库连接已关闭")

        
def prepare_documents_from_db(records):
    """将数据库记录转换为LangChain Document对象"""
    documents = []
    
    for record in tqdm(records, desc="准备文档"):
        # 创建文档标题
        doc_id = record["id"]
        title = record["title"] or f"Document-{doc_id}"
        source_table = record.get("source_table", "unknown")
        
        # 组合文档内容 - 标题和正文
        content = f"# {title}\n\n"
        
        # 提取正文内容
        extracted_text = record.get("extracted_text", "")
        if extracted_text:
            content += extracted_text
        
        # 创建Document对象
        doc = Document(
            page_content=content,
            metadata={
                "id": doc_id,
                "title": title,
                "source": f"mysql_{source_table}_{doc_id}",
                "file_path": f"database/{source_table}/{doc_id}",  # 虚拟路径
                "source_type": "mysql",
                "source_table": source_table
            }
        )
        
        documents.append(doc)
    
    print(f"准备了 {len(documents)} 个文档对象")
    return documents


def create_vector_db_from_chunks(chunks, output_dir, collection_name, embedding_model_name):
    """从分片创建或更新向量数据库 - 支持BGE-M3混合向量模型"""
    print("创建向量存储...")
    
    # 检查是否使用BGE-M3模型
    use_bge_m3 = "bge-m3" in embedding_model_name.lower()
    
    if use_bge_m3:
        # 使用BGE-M3高级向量化
        return create_m3_vector_store(chunks, output_dir, collection_name)
    else:
        # 使用传统向量化方法
        return create_traditional_vector_store(chunks, output_dir, collection_name, embedding_model_name)


def create_m3_vector_store(documents, output_dir, collection_name):
    """使用BGE-M3创建混合向量存储"""
    try:
        from FlagEmbedding import BGEM3FlagModel
    except ImportError:
        print("未安装FlagEmbedding库，请运行 'pip install -U FlagEmbedding'")
        print("回退到传统向量存储方法...")
        return create_traditional_vector_store(documents, output_dir, collection_name, 
                                              "sentence-transformers/all-MiniLM-L6-v2")
    
    try:    
        # 加载BGE-M3模型
        print("加载BGE-M3模型...")
        model = BGEM3FlagModel('BAAI/bge-m3', use_fp16=True, device="cpu")

        # 提取文档文本
        texts = [doc.page_content for doc in documents]
        metadatas = [doc.metadata for doc in documents]
        
        # 生成嵌入 - 使用批处理避免内存问题
        batch_size = 8  # 可调整批次大小
        print(f"使用BGE-M3编码 {len(texts)} 个文档...")
        
        # 分批处理大量文档
        all_dense_embeddings = []
        all_sparse_embeddings = []
        
        for i in tqdm(range(0, len(texts), batch_size), desc="生成BGE-M3嵌入"):
            batch_texts = texts[i:i+batch_size]
            embeddings_result = model.encode(
                batch_texts, 
                batch_size=batch_size, 
                max_length=8192  # BGE-M3支持长文本
            )
            
            # 提取向量
            dense_vecs = embeddings_result['dense_vecs']
            all_dense_embeddings.extend(dense_vecs)
            
            # 可选: 提取稀疏向量用于混合检索
            if 'sparse_vecs' in embeddings_result:
                all_sparse_embeddings.extend(embeddings_result['sparse_vecs'])
        
        print(f"成功生成 {len(all_dense_embeddings)} 个嵌入向量")
        
        # 创建或更新Chroma数据库
        import chromadb
        from chromadb.utils import embedding_functions
        
        # 创建自定义嵌入函数
        class CustomEmbeddingFunction(embedding_functions.EmbeddingFunction):
            def __call__(self, texts):
                # 这个函数不会被调用，因为我们直接提供了预计算的嵌入
                pass
        
        # 创建客户端和集合
        chroma_db_path = os.path.join(output_dir, collection_name)
        os.makedirs(chroma_db_path, exist_ok=True)
        client = chromadb.PersistentClient(path=chroma_db_path)
        
        # 检查集合是否存在
        try:
            collection = client.get_collection(name=collection_name)
            print(f"更新现有集合: {collection_name}")
        except:
            collection = client.create_collection(
                name=collection_name,
                embedding_function=CustomEmbeddingFunction()
            )
            print(f"创建新集合: {collection_name}")
        
        # 生成ID
        ids = [f"doc_{i}" for i in range(len(texts))]
        
        # 分批添加文档和嵌入
        batch_size = 500  # 每批添加的文档数量
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in tqdm(range(0, len(texts), batch_size), desc="添加到向量数据库", total=total_batches):
            end_idx = min(i + batch_size, len(texts))
            batch_ids = ids[i:end_idx]
            batch_texts = texts[i:end_idx]
            batch_metadatas = metadatas[i:end_idx]
            batch_embeddings = [emb.tolist() for emb in all_dense_embeddings[i:end_idx]]
            
            # 添加文档和嵌入
            collection.add(
                embeddings=batch_embeddings,
                documents=batch_texts,
                metadatas=batch_metadatas,
                ids=batch_ids
            )
        
        print(f"向量数据库更新完成! 总文档数: {collection.count()}")
        
        # 保存向量库元数据
        metadata_path = os.path.join(output_dir, f"{collection_name}_metadata.json")
        metadata = {
            "model": "BAAI/bge-m3",
            "document_count": len(texts),
            "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
            "embedding_dimensions": len(all_dense_embeddings[0]) if all_dense_embeddings else 0,
            "hybrid_search_enabled": len(all_sparse_embeddings) > 0
        }
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
            
        return chroma_db_path
        
    except Exception as e:
        print(f"使用BGE-M3创建向量存储时出错: {e}")
        import traceback
        traceback.print_exc()
        print("回退到传统向量存储方法...")
        return create_traditional_vector_store(documents, output_dir, collection_name, 
                                             "sentence-transformers/all-MiniLM-L6-v2")


def create_traditional_vector_store(chunks, output_dir, collection_name, embedding_model_name):
    """使用传统方法从分片创建或更新向量数据库 - 支持ATT&CK BERT等专业模型"""
    print(f"使用嵌入模型: {embedding_model_name}")
    
    # 检查是否为专业网络安全模型，优先使用本地版本
    local_model_mapping = {
        "basel/ATTACK-BERT": "sentence-transformer-models/ATTACK-BERT",
        "sentence-transformers/all-MiniLM-L6-v2": "sentence-transformer-models/all-MiniLM-L6-v2"
    }
    
    # 如果有本地模型，优先使用
    local_path = local_model_mapping.get(embedding_model_name)
    if local_path and os.path.exists(local_path):
        print(f"✓ 使用本地网络安全嵌入模型: {local_path}")
        actual_model_name = local_path
    else:
        actual_model_name = embedding_model_name
    
    # 检查是否为专业网络安全模型
    cybersecurity_models = ["ATTACK-BERT", "basel/ATTACK-BERT", "sentence-transformer-models/ATTACK-BERT"]
    
    if any(model in actual_model_name for model in cybersecurity_models):
        print("使用网络安全专用嵌入模型配置")
        # 尝试导入我们的专用包装器
        try:
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from RAG.rag_inference import CybersecurityEmbeddings
            
            embeddings = CybersecurityEmbeddings(
                model_name=actual_model_name,
                device='cpu',
                normalize_embeddings=True
            )
        except ImportError:
            print("回退到标准 HuggingFace 嵌入")
            from langchain_huggingface import HuggingFaceEmbeddings
            embeddings = HuggingFaceEmbeddings(
                model_name=actual_model_name,
                model_kwargs={'device': 'cpu'},
                encode_kwargs={'normalize_embeddings': True}
            )
    else:
        # 使用更新的导入方式消除警告
        try:
            from langchain_huggingface import HuggingFaceEmbeddings
        except ImportError:
            from langchain_community.embeddings import HuggingFaceEmbeddings
        
        embeddings = HuggingFaceEmbeddings(
            model_name=actual_model_name,
            model_kwargs={'device': 'cpu'},
            encode_kwargs={'normalize_embeddings': True}
        )
    
    # 创建Chroma存储路径
    chroma_db_path = os.path.join(output_dir, collection_name)
    os.makedirs(chroma_db_path, exist_ok=True)
    
    # 检查是否已存在向量数据库
    existing_db = os.path.exists(os.path.join(chroma_db_path, "chroma.sqlite3"))
    
    if existing_db:
        print(f"发现现有向量数据库，将添加新数据到: {chroma_db_path}")
        # 加载现有数据库
        vector_store = Chroma(
            persist_directory=chroma_db_path,
            embedding_function=embeddings
        )
        
        # 添加新文档 - 使用进度条分批处理
        print("开始添加文档到向量数据库...")
        batch_size = 100  # 每批处理的文档数
        total_batches = (len(chunks) + batch_size - 1) // batch_size
        
        for i in tqdm(range(0, len(chunks), batch_size), desc="添加文档到向量存储", total=total_batches):
            batch = chunks[i:i+batch_size]
            vector_store.add_documents(batch)
            # Chroma 0.4.x+ 自动持久化，无需手动调用 persist()
    else:
        # 创建新的向量数据库
        print(f"创建新的向量数据库到: {chroma_db_path}")
        
        # 使用进度条分批处理文档向量化
        batch_size = 100  # 每批处理的文档数
        total_batches = (len(chunks) + batch_size - 1) // batch_size
        
        # 首先处理第一批创建数据库
        first_batch = chunks[:min(batch_size, len(chunks))]
        print(f"初始化向量数据库 (批次 1/{total_batches})...")
        vector_store = Chroma.from_documents(
            documents=first_batch,
            embedding=embeddings,
            persist_directory=chroma_db_path
        )
        # Chroma 0.4.x+ 自动持久化，无需手动调用 persist()
        
        # 如果有更多批次，继续添加
        if len(chunks) > batch_size:
            print("继续添加剩余文档...")
            for i in tqdm(range(batch_size, len(chunks), batch_size), desc="添加文档到向量存储", total=total_batches-1):
                batch = chunks[i:i+batch_size]
                vector_store.add_documents(batch)
                # Chroma 0.4.x+ 自动持久化
    
    # 最终确认向量存储已完成
    print("向量数据库创建/更新完成...")
    # Chroma 0.4.x+ 自动持久化，无需手动调用 persist()
    
    # 保存或更新索引元数据
    vector_index_path = os.path.join(output_dir, f"{collection_name}_metadata.json")
    
    # 尝试加载现有元数据
    metadata = {}
    if os.path.exists(vector_index_path):
        try:
            with open(vector_index_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
                print("加载现有元数据以进行更新")
        except:
            print("无法加载现有元数据，将创建新的元数据文件")
    
    # 更新元数据
    print("更新元数据...")
    chunk_ids = metadata.get("chunk_ids", [])
    
    # 使用进度条添加新的chunk_ids
    for chunk in tqdm(chunks, desc="处理元数据"):
        chunk_ids.append(chunk.metadata.get("chunk_id", ""))
    
    # 记录最新的元数据
    metadata.update({
        "embedding_model": embedding_model_name,
        "document_count": len(chunk_ids),  # 总文档计数
        "last_update_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "newly_added_count": len(chunks),
        "chunk_ids": chunk_ids
    })
    
    with open(vector_index_path, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    if existing_db:
        print(f"向量存储更新完成! 新增 {len(chunks)} 个文档片段，总计 {len(chunk_ids)} 个文档片段")
    else:
        print(f"向量存储创建完成! {len(chunks)} 个文档片段已索引")
    
    return vector_store


class IntegratedChunkingWorkflow:
    def __init__(self, config):
        self.config = config
        self.input_type = config.get("input_source_type", "files")
        
        # 如果是传统的文档处理，使用原始的ChunkingWorkflow
        if self.input_type == "files":
            self.workflow = ChunkingWorkflow(config)
        else:
            # 我们自己处理数据库输入情况
            self.db_config = config.get("database_setting", {})
            self.output_config = config.get("output_doc_setting", {})
            
            # 获取MySQL数据库配置
            self.mysql_config = {
                'host': self.db_config.get('host', '127.0.0.1'),
                'user': self.db_config.get('user', 'CTI'),
                'password': self.db_config.get('password', 'cti0cd23'),
                'database': self.db_config.get('database', 'CTI_DB'),
                'port': self.db_config.get('port', 3306),
                'charset': self.db_config.get('charset', 'utf8mb4')
            }
            
            self.limit = self.db_config.get("limit")
            self.collection_name = self.output_config.get("collection_name", "threat_intel")
            
            # 使用默认的嵌入模型
            self.embedding_model = self.db_config.get(
                "embedding_model", 
                "sentence-transformers/all-MiniLM-L6-v2"
            )

            # 确认是否使用BGE-M3
            self.use_bge_m3 = "bge-m3" in self.embedding_model.lower()
            if self.use_bge_m3:
                try:
                    from FlagEmbedding import BGEM3FlagModel
                    print("已检测到BGE-M3配置，将使用增强的混合向量能力")
                except ImportError:
                    print("将回退到传统向量模型")
                    self.use_bge_m3 = False
                    self.embedding_model = "sentence-transformers/all-MiniLM-L6-v2"


    def _add_processed_file_detection(self, output_dir, output_suffix):
        """添加已处理文件检测逻辑到ChunkingWorkflow"""
        # 保存原始的_init_file_infos方法
        original_init_file_infos = self.workflow._init_file_infos
        
        # 创建已处理文件列表
        processed_files = set()
        
        # 搜索已经生成的分片文件
        chunk_files = glob.glob(os.path.join(output_dir, f"*.{output_suffix}"))
        
        print(f"正在检查已处理的文件 ({len(chunk_files)} 个可能已处理的文件)")
        
        # 从分片文件名中提取原始文件名
        for chunk_file in chunk_files:
            base_name = os.path.basename(chunk_file)
            # 去掉后缀，得到原始文件名
            original_name = base_name.replace(f".{output_suffix}", "")
            processed_files.add(original_name)
        
        # 重写_init_file_infos方法来过滤已处理的文件
        def new_init_file_infos(self_workflow):
            # 调用原始方法
            original_init_file_infos()
            
            original_file_count = len(self_workflow._file_infos)
            
            # 过滤掉已处理的文件
            filtered_file_infos = []
            for file_info in self_workflow._file_infos:
                doc_name = file_info[0]  # 文件名通常是第一个元素
                if doc_name in processed_files:
                    continue
                filtered_file_infos.append(file_info)
            
            # 更新文件列表
            self_workflow._file_infos = filtered_file_infos
            
            # 报告跳过的文件数量
            skipped_count = original_file_count - len(filtered_file_infos)
            if skipped_count > 0:
                print(f"跳过了 {skipped_count} 个已处理过的文件，只处理 {len(filtered_file_infos)} 个新文件")
        
        # 替换原始方法
        self.workflow._init_file_infos = new_init_file_infos.__get__(self.workflow)

    def _vectorize_new_jsonl_chunks(self):
        """只向量化新生成的分片文件"""
        start_time = time.time()
        print("\n=== 开始向量化处理（仅新文件）===")
        
        # 获取向量化配置
        vectorization = self.config.get("vectorization", {})
        output_dir = vectorization.get("output_dir", "data/vector_db")
        collection_name = vectorization.get("collection_name", "knowledge_base")
        embedding_model = vectorization.get("embedding_model", "sentence-transformers/all-MiniLM-L6-v2")
        
        # 只处理本次run实际处理的文件
        if not hasattr(self.workflow, "_processed_files") or not self.workflow._processed_files:
            print("没有新处理的文件，跳过向量化")
            return
        
        processed_files = self.workflow._processed_files
        print(f"本次处理了 {len(processed_files)} 个新文件，开始加载这些文件的分片...")
        
        # 直接使用已经记录的输出路径，而不是尝试构造
        all_chunks = []
        for doc_name, input_path, output_path in processed_files:
            # 直接使用output_path而不是构造新路径
            if os.path.exists(output_path):
                chunks = self._load_chunks_from_jsonl(output_path)
                all_chunks.extend(chunks)
            else:
                print(f"警告: 无法找到文件 {output_path}")
        
        # 添加过滤步骤
        print("过滤短文档片段中...")
        min_tokens = self.config.get("filtering", {}).get("min_tokens", 20)
        min_words = self.config.get("filtering", {}).get("min_words", 5)
        all_chunks = self.filter_short_chunks(all_chunks, min_tokens, min_words)
        
        # 创建向量数据库
        if all_chunks:
            print(f"\n开始更新向量数据库，共 {len(all_chunks)} 个新文档分片...")
            vector_store = create_vector_db_from_chunks(
                all_chunks, 
                output_dir, 
                collection_name, 
                embedding_model
            )
            
            # 计时
            process_time = time.time() - start_time
            print(f"\n向量化完成! 总处理时间: {process_time:.2f} 秒")
        else:
            print("没有加载到任何新的文档分片，向量化终止")

    def run(self):
        """运行工作流，添加跳过已处理文件的逻辑"""
        if self.input_type == "files":
            # 获取输出目录配置，用于检查文件是否已处理
            output_dir = self.config["output_doc_setting"]["doc_dir"]
            output_suffix = self.config["output_doc_setting"].get("suffix", "jsonl")
            
            # 创建文件跳过检查功能
            self._add_processed_file_detection(output_dir, output_suffix)
            
            # 使用原始工作流处理文档
            self.workflow.run()
            
            # 检查是否有新处理的文件 - 使用正确的属性
            if not hasattr(self.workflow, "_processed_files") or not self.workflow._processed_files:
                print("\n没有新文件需要处理，跳过向量化阶段")
                return

            # 检查配置中是否启用了向量化
            vectorization_enabled = self.config.get("vectorization", {}).get("enabled", False)
            
            if vectorization_enabled:
                # 在文档分片完成后进行向量化，但只处理新生成的分片
                self._vectorize_new_jsonl_chunks()
            else:
                print("\n文档分片完成。如需创建向量数据库，请设置 vectorization.enabled=true 或手动运行向量化脚本。")
        else:
            # 处理数据库输入
            self._process_database_input()


    def _vectorize_jsonl_chunks(self):
        """从JSONL分片文件创建向量数据库"""
        start_time = time.time()
        print("\n=== 开始向量化处理 ===")
        
        # 获取向量化配置
        vectorization = self.config.get("vectorization", {})
        output_dir = vectorization.get("output_dir", "data/vector_db")
        collection_name = vectorization.get("collection_name", "knowledge_base")
        embedding_model = vectorization.get("embedding_model", "sentence-transformers/all-MiniLM-L6-v2")
        
        # 获取分片文件目录
        chunks_dir = self.config["output_doc_setting"]["doc_dir"]
        
        # 获取所有JSONL文件
        import glob
        jsonl_files = glob.glob(os.path.join(chunks_dir, "*.jsonl"))
        
        if not jsonl_files:
            print(f"在 {chunks_dir} 中未找到JSONL文件，无法向量化")
            return
        
        print(f"找到 {len(jsonl_files)} 个JSONL文件，开始加载...")
        
        # 加载所有分片
        all_chunks = []
        for jsonl_file in tqdm(jsonl_files, desc="加载JSONL文件"):
            chunks = self._load_chunks_from_jsonl(jsonl_file)
            all_chunks.extend(chunks)

        # 添加过滤步骤
        print("过滤短文档片段中...")
        min_tokens = self.config.get("filtering", {}).get("min_tokens", 20)
        min_words = self.config.get("filtering", {}).get("min_words", 5)
        all_chunks = self.filter_short_chunks(all_chunks, min_tokens, min_words)
        
        # 创建向量数据库
        if all_chunks:
            print(f"\n开始创建向量数据库，共 {len(all_chunks)} 个文档分片...")
            vector_store = create_vector_db_from_chunks(
                all_chunks, 
                output_dir, 
                collection_name, 
                embedding_model
            )
            
            # 计时
            process_time = time.time() - start_time
            print(f"\n向量化完成! 总处理时间: {process_time:.2f} 秒")
            print(f"\n下一步: 使用 rag_inference.py 加载向量数据库进行问答")
        else:
            print("没有加载到任何文档分片，向量化终止")

    def _load_chunks_from_jsonl(self, jsonl_path):
        """从JSONL文件加载文档分片"""
        documents = []
        
        try:
            with open(jsonl_path, 'r', encoding='utf-8') as file:
                for line in file:
                    chunk_data = json.loads(line)
                    
                    # 创建Document对象
                    doc = Document(
                        page_content=chunk_data.get('content', ''),
                        metadata={
                            "chunk_id": chunk_data.get('chunk_id', ''),
                            "title": chunk_data.get('title', ''),
                            "source": chunk_data.get('source', ''),
                            **chunk_data.get('metadata', {})
                        }
                    )
                    documents.append(doc)
                    
            return documents
        except Exception as e:
            print(f"加载 {jsonl_path} 时出错: {e}")
            return []
            
    def _process_database_input(self):
        """处理数据库输入流程 - 支持续传功能"""
        start_time = time.time()
        
        try:
            print("=== 开始处理MySQL数据库输入 ===")
            
            # 1. 获取完整的数据库配置
            mysql_config = self.mysql_config
            limit = self.limit
            offset = self.db_config.get("offset", 0)
            resume = self.db_config.get("resume", False)
            progress_file = self.db_config.get("progress_file", "data/processing_progress.json")
            
            print(f"数据库: {mysql_config['database']}@{mysql_config['host']}, 限制: {limit}, 偏移量: {offset}, 续传: {resume}")
            
            # 2. 从MySQL数据库获取数据（支持续传）
            records = fetch_threat_intel_data_from_mysql(
                mysql_config, 
                limit=limit, 
                offset=offset, 
                resume=resume, 
                progress_file=progress_file
            )
            
            if not records:
                print("未找到可处理的数据，退出")
                return
            
            print(f"成功加载 {len(records)} 条记录")
            
            # 3. 准备文档对象
            documents = prepare_documents_from_db(records)
            
            # 4. 分片处理
            try:
                # 获取配置
                splitter_config = self.config.get("splitter", {})
                splitter_class_name = splitter_config.get("class_name", "RecursiveSentenceSplitter")
                splitter_module_path = splitter_config.get("module_path", "pikerag.document_transformers.splitter.recursive_sentence_splitter")
                
                print(f"使用 {splitter_class_name} 从 {splitter_module_path} 分片文档...")
                chunks = self.split_db_documents(documents, splitter_module_path, splitter_class_name, splitter_config.get("args", {}))
                
            except ImportError as e:
                print(f"导入分片器模块失败: {e}")
                print("使用备用分片方法...")
                chunks = self.split_documents_with_langchain(documents)

            # 添加过滤步骤 - 过滤掉过短或无意义的片段
            print("过滤短文档片段中...")
            min_tokens = self.config.get("filtering", {}).get("min_tokens", 20)
            min_words = self.config.get("filtering", {}).get("min_words", 5)
            chunks = self.filter_short_chunks(chunks, min_tokens, min_words)
            
            # 5. 创建向量数据库
            output_dir = self.output_config.get("doc_dir", "data/vector_db")
            vector_store = create_vector_db_from_chunks(
                chunks, 
                output_dir, 
                self.collection_name, 
                self.embedding_model
            )
            
            # 计时
            process_time = time.time() - start_time
            print(f"\n处理完成! 共处理 {len(records)} 条记录，生成 {len(chunks)} 个文档片段")
            print(f"总处理时间: {process_time:.2f} 秒")
            
            # 6. 显示处理状态和进度
            if resume:
                # 尝试读取进度信息
                try:
                    with open(progress_file, 'r') as f:
                        progress_data = json.load(f)
                        next_offset = progress_data.get("next_offset", offset + len(records))
                        total_processed = progress_data.get("total_processed", 0)
                        
                        print(f"\n处理进度: 已累计处理 {total_processed} 条记录")
                        print(f"下次续传将从偏移量 {next_offset} 开始")
                except Exception as e:
                    print(f"读取进度文件失败: {e}")
            
            print(f"\n下一步: 使用 rag_inference.py 加载向量数据库进行问答")
            
        except Exception as e:
            print(f"错误: {e}")
            import traceback
            traceback.print_exc()

    def filter_short_chunks(self, chunks, min_tokens=5, min_words=2):
        """过滤掉太短的文档片段"""
        original_count = len(chunks)
        
        # 定义基本的过滤条件
        def is_chunk_valid(chunk):
            content = chunk.page_content.strip()
            
            # 检查字数
            word_count = len(content.split())
            if word_count < min_words:
                return False
                
            # 简单估算token数量 (通常4个字符≈1个token)
            token_count = len(content) / 4
            if token_count < min_tokens:
                return False
                
            # 检查是否只有数字、符号等无意义内容
            import re
            text_content = re.sub(r'[^\w\s]', '', content)
            text_content = text_content.strip()
            if not text_content:
                return False
                
            return True
        
        # 过滤chunks列表
        filtered_chunks = [chunk for chunk in chunks if is_chunk_valid(chunk)]
        
        # 打印过滤结果
        removed_count = original_count - len(filtered_chunks)
        if removed_count > 0:
            print(f"过滤结果: 移除了 {removed_count} 个过短或无意义的文档片段")
        
        return filtered_chunks

    def split_db_documents(self, documents, splitter_module_path, splitter_class_name, args=None):
        """使用指定的分片器处理数据库文档"""
        args = args or {}
        all_chunks = []
        
        try:
            # 导入分片器
            module = __import__(splitter_module_path, fromlist=[splitter_class_name])
            splitter_class = getattr(module, splitter_class_name)
            
            # 创建分片器实例
            splitter = splitter_class(**args)
            
            print(f"使用 {splitter_class_name} 处理 {len(documents)} 个文档...")
            
            # 检查可用的方法
            if hasattr(splitter, 'create_documents'):
                # 如果有create_documents方法，可能是直接接受文本而非Document对象
                for i, doc in enumerate(tqdm(documents, desc="分片文档")):
                    # 提取文档ID和元数据
                    doc_id = doc.metadata.get("id", f"doc-{i}")
                    title = doc.metadata.get("title", "")
                    
                    # 调用create_documents方法创建分片
                    doc_chunks = splitter.create_documents([doc.page_content])
                    
                    # 为每个分片添加元数据
                    for j, chunk in enumerate(doc_chunks):
                        chunk.metadata.update({
                            "chunk_id": f"{doc_id}-chunk-{j}",
                            "title": title,
                            "source_id": doc_id,
                            "source": doc.metadata.get("source", ""),
                            "source_type": "database"
                        })
                    
                    all_chunks.extend(doc_chunks)
            elif hasattr(splitter, 'split_text'):
                # 如果有split_text方法，先分割文本再创建Document
                for i, doc in enumerate(tqdm(documents, desc="分片文档")):
                    # 提取文档ID和元数据
                    doc_id = doc.metadata.get("id", f"doc-{i}")
                    title = doc.metadata.get("title", "")
                    
                    # 分割文本
                    text_chunks = splitter.split_text(doc.page_content)
                    
                    # 创建Document对象
                    for j, chunk_text in enumerate(text_chunks):
                        chunk = Document(
                            page_content=chunk_text,
                            metadata={
                                "chunk_id": f"{doc_id}-chunk-{j}",
                                "title": title,
                                "source_id": doc_id,
                                "source": doc.metadata.get("source", ""),
                                "source_type": "database"
                            }
                        )
                        all_chunks.append(chunk)
            else:
                raise AttributeError(f"分片器 {splitter_class_name} 没有可用的分片方法")
            
            print(f"生成了 {len(all_chunks)} 个文档片段")
            return all_chunks
            
        except Exception as e:
            print(f"分片过程出错: {e}")
            # 回退到默认的分片方法
            return self.split_documents_with_langchain(documents)

    def split_documents_with_langchain(self, documents,chunk_sent_size=12, chunk_overlap=4):
        """使用LangChain的RecursiveCharacterTextSplitter分片文档"""
        from langchain_text_splitters import RecursiveSentenceSplitter

        print("使用LangChain RecursiveSentenceSplitter分片...")
        splitter = RecursiveSentenceSplitter(
            chunk_size=chunk_sent_size,     # 每个分片的句子数
            chunk_overlap=chunk_overlap,   # 分片间重叠的句子数
            num_parallel=4     # 并行处理线程数
        )
        
        all_chunks = []
        for i, doc in enumerate(tqdm(documents, desc="LangChain分片")):
            # 生成唯一ID
            doc_id = doc.metadata.get("id", f"doc-{i}")
            
            # 分片文档
            doc_chunks = splitter.split_documents([doc])
            
            # 为每个分片添加元数据
            for j, chunk in enumerate(doc_chunks):
                chunk.metadata["chunk_id"] = f"{doc_id}-chunk-{j}"
                # 确保保留原始文档的关键元数据
                for key in ["title", "source", "source_type"]:
                    if key in doc.metadata and key not in chunk.metadata:
                        chunk.metadata[key] = doc.metadata[key]
            
            all_chunks.extend(doc_chunks)
        
        print(f"生成了 {len(all_chunks)} 个文档片段")
        return all_chunks
    

# 在 if __name__ == "__main__" 部分
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("config", type=str, help="YAML配置文件路径")
    parser.add_argument("--db", help="SQLite数据库路径（可选，覆盖配置文件）")
    parser.add_argument("--limit", type=int, help="限制处理记录数量（可选）")
    parser.add_argument("--offset", type=int, help="起始偏移量（可选）")
    parser.add_argument("--resume", action="store_true", help="从上次位置继续处理")
    parser.add_argument("--no-resume", action="store_true", help="不从上次位置继续（覆盖配置）")
    args = parser.parse_args()

    # 加载YAML配置
    yaml_config: dict = load_yaml_config(args.config, args)

    # 加载环境变量
    load_dot_env(env_path=yaml_config["dotenv_path"])

    # 命令行参数覆盖配置文件
    if "database_setting" in yaml_config:
        if args.db:
            yaml_config["database_setting"]["db_path"] = args.db
            
        if args.limit is not None:
            yaml_config["database_setting"]["limit"] = args.limit
            
        if args.offset is not None:
            yaml_config["database_setting"]["offset"] = args.offset
            
        # 处理续传标志
        if args.resume:
            yaml_config["database_setting"]["resume"] = True
        elif args.no_resume:
            yaml_config["database_setting"]["resume"] = False

    # 创建并运行集成工作流
    workflow = IntegratedChunkingWorkflow(yaml_config)
    workflow.run()


  