import hashlib
from io import BytesIO
import os
import re
import json
import traceback
from turtle import pd
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
import time
from urllib.parse import urlparse

import PyPDF2
from bs4 import BeautifulSoup
from htmldate import find_date
from readability import Document as ReadabilityDocument
import requests
import urllib
import urllib3
import json
import concurrent.futures
import requests
from datetime import datetime
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_core.documents import Document
# Disable SSL warnings
urllib3.disable_warnings()

def extract_page_pattern(url):
    """提取URL中的分页模式"""
    # 检查常见分页模式
    patterns = [
        r'/page/\d+/?',
        r'/p/\d+/?',
        r'[?&]page=\d+',
        r'[?&]p=\d+',
        r'[?&]pg=\d+'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            # 返回不含具体数字的模式
            return re.sub(r'\d+', 'X', match.group(0))
    
    return None

def normalize_url(url):
    """
    增强版URL标准化，处理页码和路径变体
    
    Args:
        url: 需要标准化的URL
    
    Returns:
        tuple: (标准化URL, 替代形式URL)
    """
    try:
        # 确保URL是字符串
        if not isinstance(url, str):
            return url, url
        
        # 移除URL末尾的空白符
        url = url.strip().lower()
        
        # 解析URL
        parsed = urllib.parse.urlparse(url)
        
        # 获取URL路径
        path = parsed.path
        
        # 移除尾部斜杠
        if path.endswith('/'):
            path = path[:-1]
            
        # 特殊处理 - 移除页码部分，提取基本路径
        # 例如：/blog/page/3/ -> /blog
        path_normalized = re.sub(r'(/page/\d+|/p/\d+)$', '', path)
        
        # 重建标准化URL (不含页码)
        normalized = f"{parsed.scheme}://{parsed.netloc}{path_normalized}"
        
        # 处理www子域名变体
        netloc = parsed.netloc
        alt_netloc = netloc
        
        if netloc.startswith('www.'):
            alt_netloc = netloc[4:]
        else:
            alt_netloc = f"www.{netloc}"
            
        # 创建替代形式 (不含页码)
        alt_form = f"{parsed.scheme}://{alt_netloc}{path_normalized}"
        
        return normalized, alt_form
    
    except Exception as e:
        print(f"URL标准化失败: {url}, 错误: {e}")
        return url, url

# 规范化URL并提取域名和路径核心部分
# 修改normalize_url_for_comparison函数，增加域名提取功能
def normalize_url_for_domain_comparison(url):
    """提取URL的域名部分用于比较"""
    if not url:
        return ""
    
    try:
        # 解析URL获取域名
        parsed = urllib.parse.urlparse(url)
        domain = parsed.netloc
        
        # 移除www前缀
        if domain.startswith('www.'):
            domain = domain[4:]
            
        # 提取域名与主路径的前两级，以便更精确地匹配
        path_parts = parsed.path.strip('/').split('/')
        path_prefix = '/'.join(path_parts[:2]) if len(path_parts) > 1 else (path_parts[0] if path_parts else "")
        
        # 返回域名+路径前缀作为比较依据
        return f"{domain}/{path_prefix}"
    except:
        return url
    
def parse_date_to_datetime(date_value):
    """
    将各种格式的日期转换为datetime对象
    
    Args:
        date_value: 可以是字符串、datetime对象或其他日期格式
        
    Returns:
        datetime对象或None（如果转换失败）
    """
    try:
        from datetime import datetime as dt
        
        if isinstance(date_value, datetime):
            # 已经是datetime对象
            return date_value
            
        elif isinstance(date_value, str):
            # 如果是ISO格式的日期字符串
            if 'T' in date_value:
                date_value = date_value.split('T')[0]
                
            # 尝试解析不同的日期格式
            for fmt in ('%Y-%m-%d', '%Y/%m/%d', '%d-%m-%Y', '%d/%m/%Y', '%m/%d/%Y'):
                try:
                    return dt.strptime(date_value, fmt)
                except ValueError:
                    continue
                    
        # 无法解析的情况
        print(f"无法解析日期值: {date_value} (类型: {type(date_value)})")
        return None
        
    except Exception as e:
        print(f"日期解析错误: {str(e)}")
        return None

class HttpManager:
    """Custom HTTP client with proxy support and retry logic"""
    def __init__(self, use_proxy=False, timeout=30):
        # Configure proxy settings
        self.proxy_url = "http://127.0.0.1:7890" if use_proxy else None
        
        # Configure retry policy - 减少重试次数以避免过长等待
        retries = urllib3.Retry(
            total=2,
            backoff_factor=0.3,
            status_forcelist=[500, 502, 503, 504],
            redirect=2
        )
        
        # 默认超时配置 - 连接超时减少到3秒
        self.default_timeout = urllib3.Timeout(connect=3.0, read=timeout)
        
        # Create connection pool (with or without proxy)
        if use_proxy:
            self.http = urllib3.ProxyManager(
                self.proxy_url,
                cert_reqs='CERT_NONE',  # Disable certificate verification
                retries=retries,
                timeout=self.default_timeout
            )
        else:
            self.http = urllib3.PoolManager(
                cert_reqs='CERT_NONE',
                retries=retries,
                timeout=self.default_timeout
            )

    def request(self, method, url, headers=None, timeout=None):
        """Send HTTP request with optional custom timeout"""
        try:
            # 使用自定义超时或默认超时
            custom_timeout = timeout if timeout else self.default_timeout
            
            # 临时设置timeout，不改变默认值
            return self.http.request(
                method, 
                url, 
                headers=headers,
                timeout=custom_timeout,
                # 添加静默选项
                retries=False  # 禁用urllib3内部的重试，让我们自己处理
            )
        except (urllib3.exceptions.TimeoutError, 
                urllib3.exceptions.MaxRetryError, 
                urllib3.exceptions.ConnectTimeoutError) as e:
            # 静默处理超时错误，返回None
            return None

def sanitize_filename(filename):
    """Clean illegal filename characters"""
    return re.sub(r'[\\/*?:"<>|]', '_', filename)

def is_detail_page(url, html_content=None):
    """
    增强版详情页检测函数，通过URL特征和内容分析来判断是否为文章详情页
    
    Args:
        url (str): 网页URL
        html_content (str): 网页HTML内容，可选参数
        
    Returns:
        bool: 是否为详情页
    """
    # 1. 检查文件类型 - 排除特定文件类型
    if url.lower().endswith(('.xml', '.rss', '.atom', '.json', '.txt')):
        return False
    
    # 2. 检查URL模式 - 排除明显的非详情页URL
    exclude_patterns = [
        # 通用分类/列表页模式
        '/tag/', '/category/', '/author/', '/topics/', 
        '/search/', '/page/', '/feed/', '/index/', 
        '/archive', '/archives/', '/list/', '/lists/',
        '/section/', '/sections/', '/tags/', '/user/',
        '/comment', '/comments/', '/replies/', '/help/',
        '/faq/', '/support/', '/forum/', '/forums/',
        '/en/', '/zh/', '/fr/', '/de/', '/es/', '/it/',
        
        # 特定列表形式
        '/page=', '?page=', '&page=', 'page/', 
        'p=', '/p/', '?p=', '&p=',
        'paged=', '?paged=', '&paged=',
        # 其他分页形式
        '/page/', '/list/', '/archive/',
        # 文件和特殊页面
        'sitemap.xml', 'robots.txt', 'rss.xml', 'feed.xml',
        '/login', '/signup', '/register', '/contact', '/about',
        '/privacy', '/terms', '/disclaimer', '/policy',
        
        # 安全博客特有的列表页
        '/blog/', '/blogs/', '/news/', '/latest/',

        # 标签页 - 更完善的标签页过滤规则
        # 类似 https://www.secrss.com/articles?tag=%E5%8B%92%E7%B4%A2%E8%BD%AF%E4%BB%B6
        '/tag/', '/tags/',
        '?tag=', '&tag=', '/tag=',
        '?tags=', '&tags=', '/tags=',
        # 其他常见的标签参数
        '?label=', '&label=', '/label=',
        '?keyword=', '&keyword=', '/keyword=',
        '?topic=', '&topic=', '/topic=',
    ]
    
    # 简单字符串包含检查
    for pattern in exclude_patterns:
        if pattern in url.lower():
            return False

    # 2.5. 使用正则表达式进行更精确的标签页过滤
    tag_patterns = [
        r'[?&]tag=',           # ?tag=xxx 或 &tag=xxx
        r'[?&]tags=',          # ?tags=xxx 或 &tags=xxx
        r'[?&]label=',         # ?label=xxx 或 &label=xxx
        r'[?&]keyword=',       # ?keyword=xxx 或 &keyword=xxx
        r'[?&]topic=',         # ?topic=xxx 或 &topic=xxx
        r'[?&]category=',      # ?category=xxx 或 &category=xxx
        r'/tag/[^/]+/?$',      # /tag/something/ 结尾
        r'/tags/[^/]+/?$',     # /tags/something/ 结尾
        r'/category/[^/]+/?$', # /category/something/ 结尾
    ]

    for pattern in tag_patterns:
        if re.search(pattern, url.lower()):
            # 调试信息：记录被过滤的标签页URL
            print(f"[过滤] 标签页URL被过滤: {url}")
            return False

    # 3. 检查URL结构特征 - 文章详情页通常有这些特征
    detail_indicators = [
        # 常见的文章URL模式
        r'/\d{4}/\d{2}/\d{2}/',  # 日期格式: /2023/05/28/
        r'/\d{4}-\d{2}-\d{2}/',  # 日期格式: /2023-05-28/
        r'/article/\d+',         # 文章ID: /article/12345
        r'/post/\d+',            # 文章ID: /post/12345
        r'/story/\d+',           # 文章ID: /story/12345
        r'/[a-z0-9-]{20,}/?$',   # 长slug，通常是详情页
        r'/[^/]+/$',             # 短路径，可能是详情页
        r'\d+',                  # 链接中有数字的：/177878/
        r'/apt',
        r'/malware',
        r'/news',
        
    ]
    
    # 如果URL匹配任何详情页指标，则可能是详情页
    for pattern in detail_indicators:
        if re.search(pattern, url):
            return True
    
    # 4. 内容分析 - 如果提供了HTML内容
    if html_content:
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 检查文章特有元素
            article_tags = soup.find_all(['article', 'main', 'section'])
            if article_tags:
                return True
            
            # 检查标题和内容区
            title_tag = soup.find('h1')
            if title_tag and len(title_tag.get_text()) > 12:  # 详情页标题通常较长
                # 检查是否有长段落
                paragraphs = soup.find_all('p')
                total_text = sum(len(p.get_text()) for p in paragraphs)
                if total_text > 300:  # 详情页通常有大量文本
                    return True
            
            # # 检查元数据 - 文章通常有作者、日期等元数据
            # meta_tags = soup.find_all('meta', property=['og:type', 'article:published_time'])
            # for meta in meta_tags:
            #     content = meta.get('content', '')
            #     if content and ('article' in content or 'post' in content):
            #         return True
            
            # 检查URL - 有些详情页URL没有特殊格式，但页面内容与URL匹配度高
            # 例如从URL提取关键词，检查是否在正文中出现
            path_parts = urlparse(url).path.strip('/').split('/')
            if path_parts:
                last_part = path_parts[-1].replace('-', ' ').replace('_', ' ')
                if len(last_part) > 8:
                    # 检查标题或正文是否包含URL中的关键词
                    page_text = ' '.join([t.get_text() for t in soup.find_all(['h1', 'h2', 'p'])[:5]])
                    if last_part in page_text.lower():
                        return True
            
        except Exception as e:
            print(f"内容分析错误: {str(e)}")
    

    # 无法确定的情况默认为非详情页
    return False

def remove_memory_dumps_and_registers(text):
    """
    专门用于移除内存转储、寄存器内容和系统调试信息
    """
    if not text:
        return text
    
    # 移除十六进制内存地址模式
    patterns_to_remove = [
        # 16位和8位十六进制数字
        r'\b[0-9a-fA-F]{16}\b',
        r'\b[0-9a-fA-F]{8}\b',
        
        # 寄存器内容 (R10:, R11:, RBP:, etc.)
        r'\b[Rr]\d+:\s*[0-9a-fA-F]+\b',
        r'\b[A-Z]{2,4}:\s*[0-9a-fA-F]+\b',
        
        # ARM寄存器模式 (s5, s6, s7, etc.)
        r'\bs\d+\s+[0-9a-fA-F]+\b',
        
        # 内核日志时间戳
        r'\[\s*\d+\.\d+\]\s*[A-Z]+:',
        r'\[\s*\d+\.\d+\].*?[0-9a-fA-F]{8,}',
        
        # 内存地址行 (如: 9ea0: 01067f50 f1439ee3 00000000)
        r'^[0-9a-fA-F]+:\s+[0-9a-fA-F\s]+$',
        
        # Stack trace相关
        r'ffffffff[0-9a-fA-F]+',
        r'ffff[0-9a-fA-F]{12,}',
        
        # 连续的十六进制字符块
        r'[0-9a-fA-F]{32,}',
    ]
    
    # 应用所有模式
    for pattern in patterns_to_remove:
        text = re.sub(pattern, '', text, flags=re.MULTILINE | re.IGNORECASE)
    
    # 清理空行和多余空格
    text = re.sub(r'\n\s*\n', '\n', text)
    text = re.sub(r' +', ' ', text)
    
    return text.strip()

def clean_extracted_text(text):
    """清理提取的文本"""
    try:
        # 首先移除内存转储和寄存器内容
        text = remove_memory_dumps_and_registers(text)
        
        # 删除多余的空格和空行
        text = re.sub(r'\n{3,}', '\n\n', text)  # 替换连续多个换行为两个换行
        text = re.sub(r' {3,}', ' ', text)      # 替换连续多个空格为一个空格
        
        # 修复常见问题
        text = re.sub(r'([.!?])\s*\n', r'\1\n\n', text)  # 确保句子后有适当的换行
        
        # 只删除明显的控制字符和特殊符号，保留中文、英文、数字和常用标点符号
        # 删除零宽度字符和一些明显的垃圾字符
        text = re.sub(r'[\u200b\u200c\u200d\ufeff\x00-\x08\x0e-\x1f\x7f]', '', text)  # 删除控制字符
        # 保留绝大多数有用字符，只删除一些明显的垃圾字符
        # text = re.sub(r'[^\w\s.,;:!?()[\]{}\-–—\'\"&@#$%*+=/\\<>~^`|）（【】《》""''；：，。、？！…\u4e00-\u9fff\u3400-\u4dbf\u3000-\u303f\uff00-\uffef]', '', text)
        
        return text.strip()
    except Exception as e:
        print(f"清理文本时出错: {e}")
        return text if text else ""

def should_filter_text(text):
    """判断文本是否应该被过滤掉"""
    # 如果文本过短，直接过滤
    if len(text) < 50:
        return True
    
    # 检查是否主要包含内存转储或寄存器内容
    hex_pattern_count = len(re.findall(r'\b[0-9a-fA-F]{8,16}\b', text))
    register_pattern_count = len(re.findall(r'\b[Rr]\d+:\s*[0-9a-fA-F]+\b', text))
    
    # 如果文本中十六进制模式过多，可能是内存转储
    if hex_pattern_count > 5 or register_pattern_count > 3:
        return True
    
    # 检查是否包含大量连续的十六进制字符
    hex_blocks = re.findall(r'[0-9a-fA-F\s]{50,}', text)
    if len(hex_blocks) > 2:
        return True
        
    # 广告和无关文本模式
    filter_patterns = [
        r'^\s*广告\s*$',
        r'^\s*advertisement\s*$',
        r'^\s*cookies?\s*$',
        r'^\s*newsletter\s*$',
        r'^\s*subscribe\s*$',
        r'^\s*sign\s+up\s*$',
        r'^comments$',
        r'^related\s+posts?$',
        r'^share\s+this\s+article$',
        r'^tags?:',
        r'^categories?:'
    ]
    
    # 检查是否匹配过滤模式
    for pattern in filter_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            return True
            
    # 检查是否为常见的页脚签名内容
    common_footers = [
        "all rights reserved",
        "copyright",
        "privacy policy",
        "terms of use",
        "terms of service",
        "contact us",
        "about us",
        "follow us"
    ]
    
    text_lower = text.lower()
    if any(footer in text_lower for footer in common_footers) and len(text) < 200:
        return True
    
    # 检查是否内容意义不大
    low_value_indicators = [
        "loading...",
        "please wait",
        "click here",
        "read more",
        "continue reading",
        "skip to content"
    ]
    
    if any(indicator in text_lower for indicator in low_value_indicators):
        return True
        
    # 如果没有被过滤掉，就保留
    return False


def download_pdf(url, timeout=30):
    """Download and extract PDF text"""
    try:
        # Set up HTTP session with proper headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...'
        }
        
        response = requests.get(url, headers=headers, verify=False, timeout=timeout)
        response.raise_for_status()
        
        # Process with PyPDF2
        pdf_file = BytesIO(response.content)
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        
        text = ""
        for page_num in range(len(pdf_reader.pages)):
            text += pdf_reader.pages[page_num].extract_text()
            
        return text
    except Exception as e:
        print(f"PDF processing failed: {str(e)}")
        return None

def process_pdf_content(pdf_text):
    """Structured processing of PDF text content"""
    # Split into lines
    lines = pdf_text.split('\n')
    sections = []
    current_paragraph = []
    previous_is_heading = False
    
    # Identify titles and paragraphs
    for line in lines:
        line = line.strip()
        if not line:
            # Empty line indicates end of paragraph
            if current_paragraph:
                paragraph_text = ' '.join(current_paragraph)
                if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
                    sections.append(paragraph_text)
                current_paragraph = []
                previous_is_heading = False
            continue
            
        # Possible heading characteristics: short, high proportion of uppercase, no punctuation at end
        is_heading = (len(line) < 80 and 
                     sum(1 for c in line if c.isupper()) / len(line) > 0.3 and
                     not line[-1] in '.,:;?!')
        
        if is_heading:
            # Add accumulated paragraph content to sections
            if current_paragraph:
                paragraph_text = ' '.join(current_paragraph)
                if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
                    sections.append(paragraph_text)
                current_paragraph = []
            
            # Add separator before heading (except first heading)
            if sections and not previous_is_heading:
                sections.append('-'*40 + '\n')
                
            # Add heading
            if not should_filter_text(line):
                sections.append('\n' + line + '\n')
                previous_is_heading = True
        else:
            # Accumulate paragraph content
            current_paragraph.append(line)
    
    # Process final paragraph
    if current_paragraph:
        paragraph_text = ' '.join(current_paragraph)
        if len(paragraph_text) >= 50 and not should_filter_text(paragraph_text):
            sections.append(paragraph_text)
    
    return clean_extracted_text('\n'.join(sections))

def crawl_url(url: str, query: str = "direct_input", use_proxy: bool = False, timeout: int = 30):
    """
    爬取单个URL并返回其内容
    
    Args:
        url: 需爬取的URL
        query: 搜索查询（可选）
        use_proxy: 是否使用代理
        timeout: 请求超时时间（秒）
        
    Returns: (content, content_type) 或 (None, None)
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Sec-Ch-Ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
        }

        
        # 配置代理（如果启用）
        proxies = None
        if use_proxy:
            proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
        
        # 发送请求
        response = requests.get(
            url, 
            headers=headers, 
            proxies=proxies, 
            timeout=timeout,
            verify=False  # 禁用SSL验证
        )
        
        # 检查状态码
        response.raise_for_status()
        
        # 检查是否是PDF
        content_type = response.headers.get('Content-Type', '')
        if 'application/pdf' in content_type or url.lower().endswith('.pdf'):
            # 处理PDF文件
            pdf_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pdf_files")
            os.makedirs(pdf_dir, exist_ok=True)
            
            file_name = os.path.basename(urlparse(url).path) or f"{hashlib.md5(url.encode()).hexdigest()}.pdf"
            if not file_name.endswith('.pdf'):
                file_name += '.pdf'
                
            pdf_path = os.path.join(pdf_dir, sanitize_filename(file_name))
            with open(pdf_path, 'wb') as f:
                f.write(response.content)
            
            print(f"已保存PDF文件: {pdf_path}")
            return pdf_path, "application/pdf"
        
        # 使用chardet库检测并处理编码
        html_content = None
        
        try:
            # 首先尝试使用chardet库检测编码
            import chardet
            detected = chardet.detect(response.content)
            encoding = detected.get('encoding')
            confidence = detected.get('confidence', 0)
            print(f"chardet检测到编码: {encoding}, 置信度: {confidence}")
            
            # 检查encoding是否为None
            if not encoding:
                raise ValueError("chardet未能检测出编码")
                
            # 将检测到的编码设置到response对象
            response.encoding = encoding
            html_content = response.text
            
            # 验证内容是否包含明显的乱码
            if '�' in html_content or html_content.count('?') > len(html_content) * 0.1:
                print("检测到可能的乱码，尝试其他编码方式...")
                # 如果检测到乱码，尝试常见编码
                encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'big5', 'latin1']
                for enc in encodings_to_try:
                    if encoding.lower() != enc:  # 避免重复尝试相同的编码
                        try:
                            temp_content = response.content.decode(enc, errors='strict')
                            # 如果解码成功并且乱码较少
                            if '�' not in temp_content and temp_content.count('?') < len(temp_content) * 0.05:
                                # 额外检查HTML完整性
                                if ('<html' in temp_content.lower() and '</html>' in temp_content.lower()) or \
                                   ('<body' in temp_content.lower() and '</body>' in temp_content.lower()):
                                    html_content = temp_content
                                    print(f"使用备选编码成功: {enc} (HTML结构完整)")
                                    break
                                else:
                                    html_content = temp_content  # 保存但继续尝试
                                    print(f"使用备选编码: {enc} (HTML可能不完整)")
                                    # 不立即退出循环，继续寻找更好的编码
                        except (UnicodeDecodeError, LookupError):
                            continue
        except (ImportError, KeyError, AttributeError, ValueError) as e:
            print(f"chardet检测编码失败: {e}，使用备用方法")
            
            # 针对特定网站的处理
            domain = urlparse(url).netloc
            if "blog.sekoia.io" in domain or "welivesecurity.com" in domain or "bleepingcomputer.com" in domain:
                print(f"检测到特殊网站: {domain}，使用特殊处理")
                
                # 尝试特殊处理方式
                special_encodings = ['utf-8-sig', 'windows-1252', 'latin1', 'iso-8859-1']
                for special_enc in special_encodings:
                    try:
                        test_content = response.content.decode(special_enc, errors='strict')
                        # 检查解码质量
                        if '�' not in test_content and test_content.count('?') < len(test_content) * 0.05:
                            print(f"使用特殊编码成功: {special_enc}")
                            html_content = test_content
                            break
                    except (UnicodeDecodeError, LookupError):
                        continue
                        
                # 如果特殊处理失败，尝试使用headers中声明的编码
                if html_content is None and response.headers.get('Content-Type'):
                    content_type = response.headers['Content-Type'].lower()
                    charset_match = re.search(r'charset=([^;]+)', content_type)
                    if charset_match:
                        try:
                            charset = charset_match.group(1).strip()
                            html_content = response.content.decode(charset, errors='replace')
                            print(f"使用Content-Type中指定的编码: {charset}")
                        except (UnicodeDecodeError, LookupError):
                            pass
                
                # 最后的备选：强制使用latin1
                if html_content is None:
                    html_content = response.content.decode('latin1', errors='replace')
                    print("使用latin1编码（替换模式）")
            else:
                # 备用方法1: 使用response的apparent_encoding
                encoding = response.encoding
                if not encoding or encoding.lower() == 'iso-8859-1':
                    encoding = response.apparent_encoding or 'utf-8'
                
                try:
                    html_content = response.content.decode(encoding, errors='strict')
                    print(f"使用response.apparent_encoding成功: {encoding}")
                except (UnicodeDecodeError, LookupError):
                    # 备用方法2: 尝试常用编码
                    encodings_to_try = [
                        'utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'big5', 
                        'latin1', 'windows-1252', 'iso-8859-1', 'ascii'
                    ]
                    
                    best_encoding = None
                    best_quality = 0
                    best_content = None
                    
                    # 尝试所有编码并选择质量最好的
                    for enc in encodings_to_try:
                        try:
                            temp_content = response.content.decode(enc, errors='strict')
                            # 计算内容质量分数
                            quality_score = 100
                            # 减分项：乱码字符
                            quality_score -= temp_content.count('�') * 10
                            quality_score -= temp_content.count('?') * 0.5
                            # 加分项：HTML标记完整
                            if '<html' in temp_content.lower() and '</html>' in temp_content.lower():
                                quality_score += 20
                            if '<body' in temp_content.lower() and '</body>' in temp_content.lower():
                                quality_score += 10
                                
                            print(f"编码 {enc} 质量分数: {quality_score}")
                            
                            # 如果是最佳质量，保存结果
                            if quality_score > best_quality:
                                best_quality = quality_score
                                best_encoding = enc
                                best_content = temp_content
                        except UnicodeDecodeError:
                            continue
                    
                    # 使用质量最好的编码结果
                    if best_content:
                        html_content = best_content
                        print(f"使用最佳质量编码成功: {best_encoding} (分数: {best_quality})")
                    else:
                        # 如果strict模式全部失败，尝试replace模式
                        for enc in encodings_to_try:
                            try:
                                temp_content = response.content.decode(enc, errors='replace')
                                html_content = temp_content
                                print(f"使用备用编码(replace模式)成功: {enc}")
                                break
                            except LookupError:
                                continue
                    
                    # 如果所有编码尝试都失败，最后使用utf-8并替换错误字符
                    if html_content is None:
                        html_content = response.content.decode('utf-8', errors='replace')
                        print("使用UTF-8编码并替换错误字符")

        if is_detail_page(url, html_content):
            print(f"成功获取详情页内容，长度: {len(html_content)} 字符")
        else:
            print(f"成功获取非详情页内容，长度: {len(html_content)} 字符")

        return html_content, "text/html"
        
    except requests.exceptions.Timeout:
        print(f"请求超时: {url}")
        return None, None
    except requests.exceptions.ConnectionError as e:
        print(f"连接错误: {url}, 错误: {e}")
        return None, None
    except requests.exceptions.HTTPError as e:
        print(f"HTTP错误: {url}, 状态码: {e.response.status_code}")
        return None, None
    except Exception as e:
        print(f"爬取过程中出现未知错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def normalize_date(date_str):
    """
    将各种格式的日期标准化为YYYY-MM-DD格式
    
    Args:
        date_str: 待标准化的日期字符串
        
    Returns:
        str: 标准化后的日期，格式为YYYY-MM-DD，如果转换失败返回原字符串
    """
    if not date_str:
        return None
        
    try:
        # 如果已经是ISO格式，检查是否有时间部分
        if 'T' in date_str:
            date_str = date_str.split('T')[0]
            
        # 检查是否已经是YYYY-MM-DD格式
        if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
            return date_str
            
        # 处理YYYY年MM月DD日格式
        match = re.match(r'(\d{4})[年](\d{1,2})[月](\d{1,2})[日]?', date_str)
        if match:
            year, month, day = match.groups()
            return f"{year}-{int(month):02d}-{int(day):02d}"
            
        # 处理MM-DD-YYYY或MM/DD/YYYY格式
        match = re.match(r'(\d{1,2})[-/月](\d{1,2})[-/日]?\s*[,]?\s*(\d{4})', date_str)
        if match:
            month, day, year = match.groups()
            return f"{year}-{int(month):02d}-{int(day):02d}"
            
        # 处理英文月份格式: August 19, 2021 或 August 19 2021
        match = re.search(r'([A-Za-z]+)\s+(\d{1,2})(?:st|nd|rd|th)?,?\s+(\d{4})', date_str)
        if match:
            month_str, day, year = match.groups()
            month_dict = {
                'january': 1, 'february': 2, 'march': 3, 'april': 4, 'may': 5, 'june': 6, 
                'july': 7, 'august': 8, 'september': 9, 'october': 10, 'november': 11, 'december': 12
            }
            month_num = month_dict.get(month_str.lower())
            if month_num:
                return f"{year}-{month_num:02d}-{int(day):02d}"
                
        # 处理英文格式: 19th August, 2021
        match = re.search(r'(\d{1,2})(?:st|nd|rd|th)?\s+([A-Za-z]+),?\s+(\d{4})', date_str)
        if match:
            day, month_str, year = match.groups()
            month_dict = {
                'january': 1, 'february': 2, 'march': 3, 'april': 4, 'may': 5, 'june': 6, 
                'july': 7, 'august': 8, 'september': 9, 'october': 10, 'november': 11, 'december': 12
            }
            month_num = month_dict.get(month_str.lower())
            if month_num:
                return f"{year}-{month_num:02d}-{int(day):02d}"
                
        # 尝试使用datetime解析
        from dateutil import parser
        parsed_date = parser.parse(date_str, fuzzy=True)
        return parsed_date.strftime('%Y-%m-%d')
        
    except Exception as e:
        print(f"日期格式转换失败: {e} - 原始日期: {date_str}")
        return date_str


def process_html_content(html_content: str, text_length: int = 300, url: str = None) -> Tuple[Optional[str], Optional[str]]:
    """
    Process and extract main content from HTML, along with the report date
    
    Args:
        html_content: The HTML content to process
        text_length: Minimum length for valid extracted content
        url: Original URL (for fallback date extraction)
        
    Returns:
        Tuple[Optional[str], Optional[str]]: (extracted content, report date)
    """
    try:
        # 检查内容是否包含乱码
        if '�' in html_content:
            print("警告: HTML内容包含乱码字符，可能存在编码问题")
            # 尝试清理乱码字符
            html_content = html_content.replace('�', '')
        
        # 验证HTML内容的有效性
        if len(html_content.strip()) < 100:
            print("警告: HTML内容过短，可能不是有效内容")
            return None, None
            
        # Extract main content using readability
        doc = ReadabilityDocument(html_content)
        main_content = doc.summary()
        
        report_date = None

        # 对主内容进行处理
        soup = BeautifulSoup(main_content, 'html.parser')
        
        # 使用更稳健的元素删除方法
        for tag_name in ['aside', 'figure', 'svg', 'footer', 'header', 'script', 'style', 'nav', 'iframe', 'noscript', 'form']:
            try:
                for tag in soup.find_all(tag_name):
                    try:
                        tag.decompose()
                    except Exception as e:
                        print(f"无法删除 {tag_name} 元素: {e}")
                        # 备用策略：替换为空字符串
                        tag.replace_with("")
            except Exception as e:
                print(f"处理 {tag_name} 标签时出错: {e}")
        
        # 也移除特定的类和ID，这些通常是广告和弹窗
        selectors = [
            '.advertisement', '.ad-', '.popup', '.modal', '.sidebar', 
            '#advertisement', '#ad-', '#popup', '#modal', '#sidebar',
            '[class*="cookie"]', '[class*="newsletter"]', '[class*="subscription"]',
            '[id*="cookie"]', '[id*="newsletter"]', '[id*="subscription"]'
        ]
        
        for selector in selectors:
            try:
                for element in soup.select(selector):
                    try:
                        element.decompose()
                    except:
                        element.replace_with("")
            except Exception as e:
                print(f"处理选择器 {selector} 时出错: {e}")
                                

        # 统一使用htmldate库
        if not report_date and url:
            try:
                report_date = find_date(url)
                print(f"Date extracted using htmldate fallback: {report_date}")
            except Exception as e:
                print(f"htmldate extraction failed: {e}")

        # 对日期进行标准化处理
        if report_date:
            report_date = normalize_date(report_date)

        # Structured extraction for content
        sections = []
        current_heading = None
        
        # 尝试识别主要内容区域，通常包含文章实际内容的div
        main_article_div = None
        potential_article_divs = soup.find_all(['article', 'div'], class_=lambda c: c and any(term in str(c).lower() for term in ['article', 'content', 'post', 'entry', 'blog', 'story', 'news']))
        
        if potential_article_divs:
            # 找出最可能包含文章的div (通常包含更多段落和更多文本)
            main_article_div = max(potential_article_divs, key=lambda div: len(div.find_all('p')))
            
            # 如果找到了主要文章div，从它中提取内容
            for element in main_article_div.find_all(['h1', 'h2', 'h3', 'h4', 'p', 'pre', 'ul', 'ol']):
                if element.name.startswith('h'):
                    current_heading = element.get_text(strip=True)
                    sections.append(f"\n# {current_heading}\n")
                elif element.name in ['ul', 'ol']:
                    # 对于列表，逐项添加
                    for li in element.find_all('li'):
                        li_text = li.get_text(separator=' ', strip=True)
                        if li_text and len(li_text) > 20:  # 过滤掉太短的项目
                            sections.append(f"- {li_text}")
                else:
                    text = element.get_text(separator=' ', strip=True)
                    if text and len(text) > 50 and not should_filter_text(text):
                        sections.append(text)
        else:
            # 如果没有找到明显的文章div，使用常规提取
            for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'p', 'pre', 'ul', 'ol']):
                if element.name.startswith('h'):
                    current_heading = element.get_text(strip=True)
                    sections.append(f"\n# {current_heading}\n")
                elif element.name in ['ul', 'ol']:
                    # 对于列表，逐项添加
                    for li in element.find_all('li'):
                        li_text = li.get_text(separator=' ', strip=True)
                        if li_text and len(li_text) > 20:
                            sections.append(f"- {li_text}")
                else:
                    text = element.get_text(separator=' ', strip=True)
                    if text and len(text) > 50 and not should_filter_text(text):
                        sections.append(text)
        
        cleaned_text = clean_extracted_text('\n'.join(sections))
        
        # 检查提取内容是否足够
        if not cleaned_text:
            print(f"警告: 提取内容为空，尝试备用提取方法")
            # 备用提取方法：直接获取所有p标签内容
            all_paragraphs = soup.find_all('p')
            cleaned_text = '\n\n'.join([p.get_text(separator=' ', strip=True) 
                                        for p in all_paragraphs 
                                        if len(p.get_text(strip=True)) > 50])
            
        if not cleaned_text or (isinstance(text_length, int) and len(cleaned_text) <= text_length):
            print(f"警告: 提取内容太短 ({len(cleaned_text) if cleaned_text else 0} 字符)")
            content_result = None
        else:
            content_result = cleaned_text
        
        return content_result, report_date
    
    except Exception as e:
        print(f"内容提取失败: {str(e)}")
        traceback.print_exc()  
        return None, None



def extract_date_fallback(url, html_content=None, text_content=None):
    """
    当htmldate库失败时的备用日期提取方法
    
    Args:
        url: 网页URL
        html_content: HTML内容(可选)
        text_content: 已提取的文本内容(可选)
        
    Returns:
        str: 找到的日期(YYYY-MM-DD格式)或None
    """
    import re
    from datetime import datetime
    from dateutil import parser
    
    # 首先检查是否为微信公众号文章
    if 'mp.weixin.qq.com' in url:
        return extract_wechat_article_date(url)
    
    # 首先尝试从URL中提取日期
    url_date_patterns = [
        r'(\d{4})/(\d{1,2})/(\d{1,2})',  # 2019/05/28
        r'(\d{4})-(\d{1,2})-(\d{1,2})',  # 2019-05-28
        r'/(\d{4})-_-_/',  # /2019_05_28/
    ]
    
    for pattern in url_date_patterns:
        match = re.search(pattern, url)
        if match:
            try:
                year, month, day = map(int, match.groups())
                if 1990 <= year <= datetime.now().year and 1 <= month <= 12 and 1 <= day <= 31:
                    return f"{year:04d}-{month:02d}-{day:02d}"
            except:
                pass
    
    # 从URL中提取年份作为部分信息
    year_match = re.search(r'/(\d{4})/', url)
    extracted_year = None
    if year_match:
        try:
            year = int(year_match.group(1))
            if 1990 <= year <= datetime.now().year:
                extracted_year = year
        except:
            pass
    
    # 如果有HTML内容，从meta标签提取
    if html_content:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查meta标签
        meta_patterns = [
            {'property': ['article:published_time', 'og:published_time', 'published_time', 'pubdate', 'publication_date']},
            {'name': ['date', 'pubdate', 'publishdate', 'timestamp', 'dc.date', 'dc.date.issued', 'date.issued']},
            {'itemprop': ['datePublished', 'dateCreated', 'date']}
        ]
        
        for pattern in meta_patterns:
            for attr, values in pattern.items():
                for val in values:
                    meta_tag = soup.find('meta', {attr: val})
                    if meta_tag and meta_tag.get('content'):
                        try:
                            # 解析日期并标准化为YYYY-MM-DD
                            date_obj = parser.parse(meta_tag['content'], fuzzy=True)
                            return date_obj.strftime('%Y-%m-%d')
                        except:
                            pass
        
        # 检查时间标签
        time_tags = soup.find_all('time')
        for time_tag in time_tags:
            if time_tag.get('datetime'):
                try:
                    date_obj = parser.parse(time_tag['datetime'], fuzzy=True)
                    return date_obj.strftime('%Y-%m-%d')
                except:
                    pass
            elif time_tag.string:
                try:
                    date_obj = parser.parse(time_tag.string, fuzzy=True)
                    return date_obj.strftime('%Y-%m-%d')
                except:
                    pass
        
        # 查找页面中的常见日期块
        date_containers = soup.select('.date, .post-date, .entry-date, .published, .timestamp, .time, [itemprop="datePublished"]')
        for container in date_containers:
            try:
                date_text = container.get_text(strip=True)
                date_obj = parser.parse(date_text, fuzzy=True)
                return date_obj.strftime('%Y-%m-%d')
            except:
                pass
    
    # 从提取的文本内容中查找日期
    if text_content:
        # 常见的日期模式
        text_date_patterns = [
            r'(?:published|posted|date)[: ]+(\w+ \d{1,2},? \d{4})',  # Published: January 15, 2020
            r'(\d{1,2} \w+ \d{4})',  # 15 January 2020
            r'(\w+ \d{1,2},? \d{4})'  # January 15, 2020
        ]
        
        for pattern in text_date_patterns:
            date_matches = re.findall(pattern, text_content, re.IGNORECASE)
            for date_text in date_matches:
                try:
                    date_obj = parser.parse(date_text, fuzzy=True)
                    # 检查提取的日期是否合理
                    if 1990 <= date_obj.year <= datetime.now().year:
                        return date_obj.strftime('%Y-%m-%d')
                except:
                    pass
    
    # 不设置任何默认日期，即使从URL中提取到了年份
    # 找不到日期
    return None


import re
import time
import requests

def extract_wechat_article_date(url: str) -> str:
    """
    给定微信公众号文章 URL，下载 HTML 并从 JS 中提取 `var ct = "时间戳"` 的发布时间。

    参数:
        url (str): 微信公众号文章链接

    返回:
        str | None: 格式化后的发布时间（如 2025-07-11 10:47:10）或 None
    """
    try:
        print(f"[微信ct提取] 正在请求页面: {url}")
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36"
        }
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code != 200:
            print(f"[微信ct提取] 请求失败，状态码: {response.status_code}")
            return None

        html = response.text

        # 提取 var ct = "时间戳"
        pattern = r'var\s+ct\s*=\s*"(\d{10})"'
        match = re.search(pattern, html)
        if match:
            timestamp = int(match.group(1))
            pub_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))
            print(f"[微信ct提取] 提取成功: {pub_time}")
            return pub_time
        else:
            print("[微信ct提取] 未找到时间戳 var ct")
            return None

    except Exception as e:
        print(f"[微信ct提取] 异常: {e}")
        return None





def web_analyze(url: str, timeout: int = 60):
    """
    分析指定URL的网页内容
    
    Args:
        url: 要分析的网页URL
        timeout: 请求超时时间（秒）
    
    Returns:
        extracted_text, report_date, content 的元组
    """
    try:
        print(f"开始分析URL: {url}")
        
        # 2. 爬取URL - 传递timeout参数
        content, content_type = crawl_url(url, use_proxy=False, timeout=timeout)

        # 检查请求是否失败
        if content is None:
            print("爬取URl超时或内容为空")
            return None, None, None
        
        print(f"成功获取内容，类型: {content_type}, 长度: {len(content) if content else 0}")
        
        # 3. 提取内容
        extracted_text = None
        report_date = None
        
        try:
            if content_type == "text/html":
                print("开始处理HTML内容...")
                extracted_text, report_date = process_html_content(content, url=url)
                
                if extracted_text:
                    print(f"成功提取文本内容，长度: {len(extracted_text)}")
                else:
                    print("警告: 未能提取到有效文本内容")
                
                # 如果htmldate提取失败，使用备用方法
                if report_date is None:
                    print("使用备用方法提取日期...")
                    report_date = extract_date_fallback(url, html_content=content, text_content=extracted_text)
                    if report_date:
                        print(f"备用方法提取到日期: {report_date}")
                    else:
                        print("备用方法也未能提取到日期，保持日期为空")
                        # 不设置任何默认值，保持report_date为None
                        
            elif content_type == "application/pdf":
                print("开始处理PDF内容...")
                pdf_text = download_pdf(url)
                if pdf_text:
                    extracted_text = process_pdf_content(pdf_text)
                    print(f"成功提取PDF文本，长度: {len(extracted_text) if extracted_text else 0}")
                    
        except Exception as e:
            # 只显示简短的错误信息
            print(f"内容提取失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None, None, content

        return extracted_text, report_date, content

    except Exception as e:
        # 完全抑制所有错误的详细信息
        print(f"URL分析失败: {url}, 错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None, None   


def extract_all_document_iocs(text):
    """从原始文本中提取所有IOC指标"""
    ioc_data = {
        "cve": [],
        "ip": [],
        "domain": [],
        "url": [],
        "hash": [],
        "email": []
    }
    
    try:
        # 提取CVE
        cve_matches = re.findall(r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})', text, re.IGNORECASE)
        for year, id_num in cve_matches:
            normalized_cve = f"CVE-{year}-{id_num}".upper()
            if normalized_cve not in ioc_data["cve"]:
                ioc_data["cve"].append(normalized_cve)
        
        # 提取IP地址
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        ip_matches = re.findall(ip_pattern, text)
        for ip in ip_matches:
            # 排除私有IP
            if not (ip.startswith('127.') or ip.startswith('192.168.') or ip.startswith('10.')):
                if ip not in ioc_data["ip"]:
                    ioc_data["ip"].append(ip)
        
        # 提取域名
        domain_pattern = r'\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b'
        domain_matches = re.findall(domain_pattern, text)
        for domain in domain_matches:
            # 排除常见域名
            if not any(common in domain.lower() for common in ['google.com', 'microsoft.com', 'apple.com']):
                if domain not in ioc_data["domain"]:
                    ioc_data["domain"].append(domain)
        
        # 提取URL
        url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(/[-\w%/.]+)*'
        url_matches = re.findall(url_pattern, text)
        for url in url_matches:
            if url not in ioc_data["url"]:
                ioc_data["url"].append(url)
        
        # 提取哈希值
        hash_patterns = {
            'md5': r'\b[a-fA-F0-9]{32}\b',
            'sha1': r'\b[a-fA-F0-9]{40}\b',
            'sha256': r'\b[a-fA-F0-9]{64}\b'
        }
        
        for hash_type, pattern in hash_patterns.items():
            hash_matches = re.findall(pattern, text)
            for hash_value in hash_matches:
                if hash_value not in ioc_data["hash"]:
                    ioc_data["hash"].append(hash_value)
        
        # 提取邮箱地址
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        email_matches = re.findall(email_pattern, text)
        for email in email_matches:
            if email not in ioc_data["email"]:
                ioc_data["email"].append(email)
        
        return ioc_data
    except Exception as e:
        print(f"提取IOC时出错: {e}")
        return ioc_data


def process_vulnerabilities(content_section):
    """
    处理相关漏洞部分，确保每个CVE编号只列出一次
    
    Args:
        content_section: 提取的漏洞部分内容
        
    Returns:
        str: 去重后的漏洞内容
    """
    try:
        
        # 如果内容为空，直接返回
        if not content_section:
            return content_section
            
        # 提取所有CVE行并进行标准化处理
        lines = content_section.strip().split('\n')
        cve_dict = {}  # 使用字典确保CVE唯一性
        
        for line in lines:
            # 使用更精确的正则表达式来提取CVE编号
            cve_match = re.search(r'(CVE-\d{4}-\d+)', line, re.IGNORECASE)
            if cve_match:
                # 获取规范化的CVE编号
                cve_id = cve_match.group(1).upper()
                
                # 如果这个CVE编号已经存在，选择更长的描述（通常更详细）
                if cve_id in cve_dict:
                    # 现有描述和新描述
                    existing_desc = cve_dict[cve_id]
                    
                    # 如果新行更长或者新行包含现有行中没有的信息，则使用新行
                    if len(line) > len(existing_desc) or any(info not in existing_desc for info in line.split('-') if len(info) > 10):
                        if line.strip().startswith('- ') or line.strip().startswith('* '):
                            cve_dict[cve_id] = line.strip()
                        else:
                            cve_dict[cve_id] = f"- {line.strip()}"
                else:
                    # 首次添加这个CVE，确保格式一致
                    if line.strip().startswith('- ') or line.strip().startswith('* '):
                        cve_dict[cve_id] = line.strip()
                    else:
                        cve_dict[cve_id] = f"- {line.strip()}"
        
        # 如果提取成功，返回去重后的结果
        if cve_dict:
            # 按CVE编号排序，使得输出更一致
            sorted_cves = sorted(cve_dict.keys())
            result = '\n'.join(cve_dict[cve_id] for cve_id in sorted_cves)
            return result
        
        return content_section
        
    except Exception as e:
        print(f"处理漏洞去重时出错: {e}")
        import traceback
        traceback.print_exc()
        return content_section
    

def process_analysis_content(content):
    """
    处理分析结果，提取内容并格式化
    
    Returns:
        tuple: (cleaned_analysis, rationale) 分析内容和推理过程
    """
    try:
        # 默认返回值
        cleaned_content = content
        rationale_content = ""
        
        # 首先尝试常规的JSON解析方式
        if "```json" in content or ("```" in content and "{" in content):
            # 从代码块中提取JSON
            json_block_match = re.search(r'```(?:json)?\s*(.*?)\s*```', content, re.DOTALL)
            
            if json_block_match:
                json_str = json_block_match.group(1)
                # 尝试解析JSON
                try:
                    json_str = fix_invalid_json(json_str)
                    data = json.loads(json_str)
                    
                    if 'response' in data and 'rationale' in data:
                        response = data['response']
                        rationale = data['rationale']
                        
                        # 处理response
                        if isinstance(response, str):
                            cleaned_content = process_string_response(response)
                        else:
                            cleaned_content = format_structured_response(response)
                        
                        # 处理rationale
                        rationale_content = rationale if isinstance(rationale, str) else json.dumps(rationale, ensure_ascii=False)
                        print(f"使用JSON格式提取。。。")
                        return cleaned_content, rationale_content
                except Exception as e:
                    # JSON解析失败，继续尝试其他方法
                    print(f"JSON解析失败: {e}")
                    print("尝试使用正则表达式提取内容...")

        
        # 如果JSON解析失败或没有JSON，尝试提取标准的markdown标题节
        sections = {}
        current_section = None
        
        # 模式1：使用 ## 标题 格式
        # 拿取每一项的内容
        section_matches = re.finditer(r'##\s+.*?(?=\n##|\Z)', content, re.DOTALL)

        for match in section_matches:
            title_content = match.group(0)
            title_match = re.match(r'##\s+(.*?)(?:\n|$)', title_content)
            
            if title_match:
                section_title = title_match.group(1).strip()
                section_content = title_content[title_match.end():].strip()
                sections[section_title] = section_content
        
        # 如果找到了分析理由部分
        if '分析理由' in sections:
            rationale_content = sections.pop('分析理由')
        
        # 对漏洞部分进行特殊处理 - 去重CVE
        if '相关漏洞' in sections:
            sections['相关漏洞'] = process_vulnerabilities(sections['相关漏洞'])
        
        # 构建清洁的输出
        if sections:
            cleaned_parts = []
            for title, content in sections.items():
                cleaned_parts.append(f"# {title}\n\n{content}\n")
            cleaned_content = "\n".join(cleaned_parts)
            
            print(f"使用MD格式提取。。。")
            return cleaned_content, rationale_content
        
        # 如果仍然没有找到分析理由，尝试备用的extract_content_sections函数
        if not rationale_content:  
            print("使用备用的extract_content_sections函数提取内容")
            extracted_sections = extract_content_sections(content)
            
            if extracted_sections:
                print(f"成功通过正则表达式提取到 {len(extracted_sections)} 个字段")
                formatted_parts = []
                
                # 处理分析理由特殊部分 - 确保提取分析理由
                if '分析理由' in extracted_sections:
                    rationale_content = extracted_sections.pop('分析理由')
                    print(f"从extract_content_sections提取到分析理由，长度: {len(rationale_content)}")
                
                # 处理漏洞部分 - 确保CVE不重复
                if '相关漏洞' in extracted_sections:
                    extracted_sections['相关漏洞'] = process_vulnerabilities(extracted_sections['相关漏洞'])

                
                # 格式化其他部分
                for title, section_content in extracted_sections.items():
                    if section_content:  # 只添加非空部分
                        formatted_parts.append(f"# {title}\n\n{section_content}\n")
                
                if formatted_parts:
                    cleaned_content = "\n".join(formatted_parts)
        
        # 最后检查 - 尝试单独查找分析理由如果还是为空
        if not rationale_content:
            # 尝试更多的模式匹配来查找分析理由
            rationale_patterns = [
                r'(?:## |# )分析理由\s*(.*?)(?=\Z)',  # 一直匹配到文本末尾
                r'分析理由[:：]\s*(.*?)(?=\Z)',
                r'## 分析理由\s*(.*)',  # 修改: 简化模式，捕获一切到末尾
                r'# 分析理由\s*(.*)',   # 修改: 简化模式，捕获一切到末尾
            ]
            
            for pattern in rationale_patterns:
                match = re.search(pattern, content, re.DOTALL)
                if match:
                    rationale_content = match.group(1).strip()
                    print(f"通过额外模式匹配找到分析理由，长度: {len(rationale_content)}")
                    break
        
        print(f"最终处理结果 - 内容长度: {len(cleaned_content)}, 分析理由长度: {len(rationale_content)}")
        return cleaned_content, rationale_content

    except Exception as e:
        print(f"处理分析内容时出错: {e}")
        traceback.print_exc()
        return content, ""


def extract_content_sections(text):
    """从纯文本格式中提取结构化内容"""
    result = {}
    
    # 定义要查找的部分
    sections = [
        ('总体摘要', [r'## 总体摘要', r'# 总体摘要']),
        ('威胁行为者', [r'## 威胁行为者', r'# 威胁行为者']),
        ('相关漏洞', [r'## 相关漏洞', r'# 相关漏洞']),
        ('攻击技术', [r'## 攻击技术\(TTPs\)', r'# 攻击技术\(TTPs\)']),
        ('所有相关威胁指标\\(IOCs\\)', [r'## 所有相关威胁指标\(IOCs\)', r'# 所有相关威胁指标\(IOCs\)']),
        ('分析理由', [r'## 分析理由', r'# 分析理由'])
    ]
    
    # 修改：使用不同的方法来查找每个部分，特别是针对分析理由这种通常在最后的部分
    for i, (section_name, patterns) in enumerate(sections):
        for pattern in patterns:
            # 对于分析理由（最后一部分），使用一种特殊处理方式，查找到文档末尾
            if section_name == '分析理由':
                match = re.search(f'{pattern}(.*?)$', text, re.DOTALL)
                if match:
                    content = match.group(1).strip()
                    result[section_name] = content
                    break
            else:
                # 尝试找到当前部分和下一部分之间的内容
                next_patterns = []
                if i < len(sections) - 1:
                    for next_pattern in sections[i+1][1]:
                        next_patterns.append(next_pattern)
                
                if next_patterns:
                    pattern_str = f'{pattern}(.*?)(?:{"|".join(next_patterns)})'
                    match = re.search(pattern_str, text, re.DOTALL)
                    if match:
                        content = match.group(1).strip()
                        result[section_name] = content
                        break
                else:
                    # 如果是最后一部分，直接找到文档末尾
                    match = re.search(f'{pattern}(.*?)$', text, re.DOTALL)
                    if match:
                        content = match.group(1).strip()
                        result[section_name] = content
                        break
    
    return result


def fix_invalid_json(json_str):
    """修复常见的JSON格式问题，处理嵌套对象和数组"""
    
    # 1. 基本清理
    # 替换单引号为双引号
    json_str = json_str.replace("'", "\"")
    # 处理转义问题
    json_str = json_str.replace("\\'", "'")
    json_str = json_str.replace('\\\"', '\"')
    
    # 2. 修复结构问题
    # 处理对象内部缺少逗号的情况
    json_str = re.sub(r'(["\d\w])\s*\n\s*"', r'\1,\n"', json_str)
    json_str = re.sub(r'(["\d\w])\s*\n\s*\{', r'\1,\n{', json_str)
    
    # 3. 处理数组内对象之间缺少逗号的情况（特别针对嵌套数组）
    json_str = re.sub(r'(\})\s*\n\s*\{', r'\1,\n{', json_str)
    
    # 4. 处理JSON数组中的逗号问题
    json_str = re.sub(r'(\])\s*\n\s*\[', r'\1,\n[', json_str)
    
    # 5. 修复对象内的键值对格式问题
    json_str = re.sub(r'"([^"]+)"\s*:\s*(\{[^{}]*\})\s*([^\s,}])', r'"\1": \2,\3', json_str)
    
    # 6. 移除末尾多余的逗号
    json_str = re.sub(r',\s*\}', '}', json_str)
    json_str = re.sub(r',\s*\]', ']', json_str)
    
    # 7. 处理特定格式问题
    # 修复对象内的属性间缺少逗号的情况
    json_str = re.sub(r'"([^"]+)"\s*:\s*"([^"]*)"([^\s,}])', r'"\1": "\2",\3', json_str)
    json_str = re.sub(r'"([^"]+)"\s*:\s*\[([^\[\]]*)\]([^\s,}])', r'"\1": [\2],\3', json_str)
    
    # 8. 嵌套对象处理
    # 在深度嵌套的对象数组中补充缺少的逗号
    # 处理花括号后缺少逗号的情况
    json_str = re.sub(r'(\})\s+(".*?":\s)', r'\1,\n\2', json_str)
    
    # 9. 处理特殊语法结构
    json_str = re.sub(r'("战术":[^,]+)("技术")', r'\1,\2', json_str)
    json_str = re.sub(r'("技术":[^,]+)("描述")', r'\1,\2', json_str)
    
    # 10. 处理IOC数组中的特殊格式问题 - 新增部分
    json_str = re.sub(r'"(文件哈希[^"]*)":\s*"([^"]+)"([^\s,}\]])', r'"\1": "\2",\3', json_str)
    json_str = re.sub(r'"(IP地址[^"]*)":\s*"([^"]+)"([^\s,}\]])', r'"\1": "\2",\3', json_str)
    json_str = re.sub(r'"(域名[^"]*)":\s*"([^"]+)"([^\s,}\]])', r'"\1": "\2",\3', json_str)
    json_str = re.sub(r'"(URL[^"]*)":\s*"([^"]+)"([^\s,}\]])', r'"\1": "\2",\3', json_str)
    
    # 11. 修复数组中字符串元素间缺少逗号的问题
    json_str = re.sub(r'("[^"]+")(\s*")', r'\1,\2', json_str)
    
    # 12. 特殊处理：修复错误的哈希值格式
    json_str = re.sub(r'(sha256:")([^"]+)(")', r'\1\2\3', json_str)
    
    # 尝试验证修复是否成功
    try:
        json.loads(json_str)
        print("JSON修复成功，解析正常")
    except json.JSONDecodeError as e:
        print(f"JSON仍有格式问题: {e}")
        # 尝试定位错误位置并输出上下文
        error_pos = int(str(e).split("char ")[1].split(")")[0])
        start = max(0, error_pos - 75)
        end = min(len(json_str), error_pos + 75)
        print(f"错误位置上下文: ...{json_str[start:end]}...")
        
        # 尝试替代方案：直接修复指定位置的错误
        if "文件哈希" in json_str[start:end]:
            patched_str = json_str[:error_pos] + "," + json_str[error_pos:]
            try:
                json.loads(patched_str)
                print("JSON通过直接插入逗号修复成功")
                return patched_str
            except:
                print("直接修复失败，返回原修复结果")
    
    return json_str


def process_string_response(response_str):
    """处理字符串形式的response"""
    output = []
    
    # 检查是否有标题数字格式"1) 总体摘要："
    sections = {}
    current_section = None
    
    # 分割行
    lines = response_str.split('\n')
    for line in lines:
        # 检查标题行
        match = re.match(r'^(\d+)\)\s+(.*?)[:：]', line)
        if match:
            current_section = match.group(2).strip()
            sections[current_section] = []
            continue
            
        # 检查【标题】格式
        match = re.match(r'^【(.*?)】$', line)
        if match:
            current_section = match.group(1).strip()
            sections[current_section] = []
            continue
            
        # 添加内容到当前部分
        if current_section and line.strip():
            sections[current_section].append(line.strip())
    
    # 如果找不到结构化的章节，尝试正则表达式提取关键信息
    if not sections:
        # 提取总体摘要
        summary_match = re.search(r'总体摘要[:：](.*?)(?=\n\n|$)', response_str, re.DOTALL)
        if summary_match:
            output.append(f"# 总体摘要\n\n{summary_match.group(1).strip()}\n")
        
        # 提取威胁行为者
        actors_match = re.search(r'(?:威胁行为者|提到的威胁行为者)[:：](.*?)(?=\n\n|$)', response_str, re.DOTALL)
        if actors_match:
            output.append(f"# 威胁行为者\n\n{actors_match.group(1).strip()}\n")
            
        # 提取漏洞信息
        vulns_match = re.search(r'(?:相关漏洞|漏洞信息)[:：](.*?)(?=\n\n|$)', response_str, re.DOTALL)
        if vulns_match:
            output.append(f"# 相关漏洞\n\n{vulns_match.group(1).strip()}\n")
        
        # 提取攻击技术
        ttps_match = re.search(r'(?:攻击技术|TTPs)[:：](.*?)(?=\n\n|$)', response_str, re.DOTALL)
        if ttps_match:
            output.append(f"# 攻击技术(TTPs)\n\n{ttps_match.group(1).strip()}\n")
        
        # 提取IOC指标
        iocs_match = re.search(r'(?:相关威胁指标|IOCs|IOC指标)[:：](.*?)(?=\n\n|$)', response_str, re.DOTALL)
        if iocs_match:
            output.append(f"# 所有相关威胁指标(IOCs)\n\n{iocs_match.group(1).strip()}\n")
    else:
        # 处理已识别的章节
        for section, content_lines in sections.items():
            if '摘要' in section or '总体' in section:
                output.append(f"# 总体摘要\n\n{' '.join(content_lines)}\n")
            elif '威胁' in section and '行为者' in section:
                output.append(f"# 威胁行为者\n\n")
                for line in content_lines:
                    if line.startswith('-'):
                        output.append(line)
                    else:
                        output.append(f"- {line}")
            elif '漏洞' in section:
                output.append(f"# 相关漏洞\n\n{' '.join(content_lines)}\n")
            elif '攻击' in section or 'TTPs' in section.upper():
                output.append(f"# 攻击技术(TTPs)\n\n")
                for line in content_lines:
                    if line.startswith('-'):
                        output.append(line)
                    else:
                        items = re.split(r'[,，、]', line)
                        for item in items:
                            if item.strip():
                                output.append(f"- {item.strip()}")
            elif 'IOC' in section.upper() or '指标' in section:
                output.append(f"# 所有相关威胁指标(IOCs)\n\n")
                for line in content_lines:
                    if line.startswith('-'):
                        output.append(line)
                    else:
                        output.append(f"- {line}")
    
    return "\n".join(output)


def format_structured_response(response):
    """格式化结构化的响应"""
    output = []
    
    # 添加总体摘要
    if '总体摘要' in response:
        output.append(f"# 总体摘要\n\n{response['总体摘要']}\n")
    
    # 添加威胁行为者信息
    if '提到的威胁行为者' in response:
        output.append("# 威胁行为者")
        actors = response['提到的威胁行为者']
        if isinstance(actors, list):
            for actor in actors:
                if isinstance(actor, dict):
                    output.append(f"\n## {actor.get('名称', '未命名行为者')}")
                    # 添加别名
                    if '别名' in actor and actor['别名']:
                        aliases = ", ".join(actor['别名']) if isinstance(actor['别名'], list) else actor['别名']
                        output.append(f"- 别名: {aliases}")
                    # 添加其他属性
                    for key, value in actor.items():
                        if key != '名称' and key != '别名' and value:
                            output.append(f"- {key}: {value}")
                else:
                    output.append(f"\n- {actor}")
        else:
            output.append(f"\n{actors}")
    
    # 添加漏洞信息
    if '相关漏洞' in response:
        output.append(f"\n# 相关漏洞\n\n{response['相关漏洞']}")
    
    # 添加攻击技术
    if '攻击技术(TTPs)' in response:
        output.append("\n# 攻击技术(TTPs)")
        ttps = response['攻击技术(TTPs)']
        if isinstance(ttps, list):
            for technique in ttps:
                output.append(f"- {technique}")
        else:
            output.append(f"\n{ttps}")
    
    # 添加IOC信息
    if '所有相关威胁指标(IOCs)' in response:
        output.append("\n# 所有相关威胁指标(IOCs)")
        iocs = response['所有相关威胁指标(IOCs)']
        
        # 处理IOC列表
        if isinstance(iocs, list):
            for ioc in iocs:
                if isinstance(ioc, dict):
                    for key, value in ioc.items():
                        output.append(f"- {key}: {value}")
                else:
                    output.append(f"- {ioc}")
        # 处理IOC字典
        elif isinstance(iocs, dict):
            for key, value in iocs.items():
                if value and value != "未提及具体IP地址" and value != "未提及具体域名" and value != "未提及具体URL" and value != "未提及具体哈希值":
                    output.append(f"- {key}: {value}")
        else:
            output.append(f"\n{iocs}")
    
    # 合并输出
    return "\n".join(output)


def enrich_cve_data(doc_text):
    """
    从文档中提取CVE编号，获取对应的漏洞数据，并可选地将获取的数据添加到向量库中
    
    参数:
        doc_text (str): 文档文本内容

    返回:
        dict: 包含CVE标识符和对应漏洞数据的字典
    """

    # 1. 从文档中提取所有CVE编号
    cve_pattern = r'(?:CVE[-\s]?|cve[-\s]?)(\d{4})[-\s]?(\d{1,7})\b'
    cve_matches = re.findall(cve_pattern, doc_text, re.IGNORECASE)
    
    if not cve_matches:
        print("未在文档中发现CVE编号")
        return {}
    
    # 2. 规范化CVE标识符
    cve_ids = []
    for year, id_num in cve_matches:
        normalized_cve = f"CVE-{year}-{id_num}".upper()
        if normalized_cve not in cve_ids:
            cve_ids.append(normalized_cve)
    
    print(f"从文档中提取了 {len(cve_ids)} 个独特的CVE编号")
    
    # 3. 定义数据获取函数 - 从本地知识库获取
    def get_cve_from_local_db(cve_id):
        """从本地知识库尝试获取CVE信息"""
        try:
            # 知识库目录配置
            knowledge_dir = "知识库"
            cve_dir = os.path.join(knowledge_dir, "cve")
            
            # 确保目录存在
            if not os.path.exists(cve_dir):
                return None
                
            # 提取CVE年份
            parts = cve_id.split("-")
            if len(parts) < 3:
                return None
                
            year = parts[1]
            
            # 优先检查年份文件
            year_file = os.path.join(cve_dir, f"{year}.txt")
            if os.path.exists(year_file):
                with open(year_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if cve_id in line:
                            # 如果找到精确的格式匹配，则返回
                            if line.startswith(f"{cve_id}(PUBLISHED):"):
                                return line.strip()
                            # 否则返回包含CVE的整行
                            return line.strip()
            
            # 如果没找到，搜索其他可能包含该CVE的文件
            search_patterns = [f"{year}*.txt", "all*.txt", "cve*.txt"]
            matching_files = []
            for pattern in search_patterns:
                import glob
                matching_files.extend(glob.glob(os.path.join(cve_dir, pattern)))
            
            # 去重
            matching_files = list(set(matching_files))
            
            # 搜索所有匹配的文件
            for file_path in matching_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if cve_id in line:
                                # 如果找到精确的格式匹配，则返回
                                if line.startswith(f"{cve_id}(PUBLISHED):"):
                                    return line.strip()
                                # 否则返回包含CVE的整行
                                return line.strip()
                except Exception:
                    continue
            
            # 没找到
            return None
        except Exception as e:
            print(f"从本地获取CVE信息时出错: {e}")
            return None
    
    # 4. 备用获取函数 - 从NVD API获取
    def get_cve_from_nvd_api(cve_id):
        """从NVD API获取CVE信息并格式化为单行文本"""
        try:
            # NVD API的请求URL
            api_url = f"https://services.nvd.nist.gov/rest/json/cves/2.0?cveId={cve_id}"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'application/json',  # 显式请求JSON格式
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
            }
            
            # 配置代理，如果需要
            proxies = None
            # proxies = {
            #    'http': 'http://127.0.0.1:7890',
            #    'https': 'http://127.0.0.1:7890'
            # }
            
            # 发送请求
            response = requests.get(
                api_url, 
                headers=headers, 
                proxies=proxies,
                verify=False,  # 禁用SSL验证，解决可能的SSL问题
                timeout=15      # 增加超时时间
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查是否有漏洞记录
                if data.get('vulnerabilities') and len(data['vulnerabilities']) > 0:
                    vuln = data['vulnerabilities'][0]['cve']
                    
                    # 提取基础信息
                    description = ""
                    if 'descriptions' in vuln:
                        for desc in vuln['descriptions']:
                            if desc.get('lang') == 'en':
                                description = desc.get('value', '')
                                # 清理描述文本 - 移除多余空格和特殊字符
                                description = re.sub(r'\s+', ' ', description).strip()
                                break
                    
                    # 提取CVSS分数
                    cvss_v3 = None
                    cvss_v2 = None
                    severity = None
                    
                    if 'metrics' in vuln:
                        metrics = vuln['metrics']
                        if 'cvssMetricV31' in metrics and metrics['cvssMetricV31']:
                            cvss_data = metrics['cvssMetricV31'][0]['cvssData']
                            cvss_v3 = cvss_data.get('baseScore')
                            severity = cvss_data.get('baseSeverity', '')
                        elif 'cvssMetricV30' in metrics and metrics['cvssMetricV30']:
                            cvss_data = metrics['cvssMetricV30'][0]['cvssData']
                            cvss_v3 = cvss_data.get('baseScore')
                            severity = cvss_data.get('baseSeverity', '')
                        
                        if 'cvssMetricV2' in metrics and metrics['cvssMetricV2']:
                            cvss_v2 = metrics['cvssMetricV2'][0]['cvssData'].get('baseScore')
                    
                    # 格式化结果为单行文本
                    # 格式: CVE-XXXX-XXXX(PUBLISHED): 描述 | CVSS: 分数(严重性) | 发布日期
                    published_date = vuln.get('published', '')
                    if published_date:
                        # 提取日期部分，移除时间
                        published_date = published_date.split('T')[0] if 'T' in published_date else published_date
                    
                    # 构建单行格式的CVE信息
                    result = f"{cve_id}(PUBLISHED):{description}"
                    
                    # 添加CVSS信息，优先使用V3
                    if cvss_v3 and severity:
                        result += f" | CVSS v3: {cvss_v3} ({severity})"
                    elif cvss_v2:
                        result += f" | CVSS v2: {cvss_v2}"
                    
                    # 添加发布日期
                    if published_date:
                        result += f" | Published: {published_date}"
                    
                    # 移除可能导致向量库索引问题的特殊字符
                    result = re.sub(r'[^\w\s.,;:!?()[\]{}\-–—\'\"&@#$%*+=/\\<>~^`|\(\)]', '', result)
                    
                    return result.strip()
            
            # 如果获取失败，返回None
            return None
        except Exception as e:
            print(f"从NVD API获取 {cve_id} 失败: {e}")
            return None

    # 5. 并行获取CVE信息
    print("开始获取CVE数据...")
    cve_data = {}
    new_entries = []
    new_cvs_obtained = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        # 首先尝试从本地知识库获取
        local_futures = {executor.submit(get_cve_from_local_db, cve_id): cve_id for cve_id in cve_ids}
        
        for future in concurrent.futures.as_completed(local_futures):
            cve_id = local_futures[future]
            try:
                result = future.result()
                if result:
                    cve_data[cve_id] = result
                    
            except Exception as e:
                print(f"处理 {cve_id} 时出错: {e}")
        
        # 对于未能从本地获取的CVE，尝试从NVD API获取
        missing_cves = [cve_id for cve_id in cve_ids if cve_id not in cve_data]
        if missing_cves:
            print(f"{len(missing_cves)} 个CVE未在本地找到，尝试从NVD API获取")
            
            # 限制API调用频率，避免触发速率限制
            api_futures = {}
            for i, cve_id in enumerate(missing_cves):
                # 每个CVE提交之间等待一小段时间，避免请求过于集中
                if i > 0 and i % 3 == 0:  # 每3个请求等待一下
                    time.sleep(0.5)
                api_futures[executor.submit(get_cve_from_nvd_api, cve_id)] = cve_id
            
            for future in concurrent.futures.as_completed(api_futures):
                cve_id = api_futures[future]
                try:
                    result = future.result()
                    if result:
                        cve_data[cve_id] = result

                        new_cvs_obtained.append(result)
                        
                        # 双重保存: 
                        # 1. 保存到对应年份文件
                        try:
                            # 提取年份并确定保存路径
                            parts = cve_id.split("-")
                            if len(parts) >= 3:
                                year = parts[1]
                                
                                # 确保知识库目录存在
                                knowledge_dir = "知识库"
                                cve_dir = os.path.join(knowledge_dir, "cve")
                                os.makedirs(cve_dir, exist_ok=True)
                                
                                # 保存到对应年份的文件
                                year_file = os.path.join(cve_dir, f"{year}.txt")
                                with open(year_file, 'a', encoding='utf-8') as f:
                                    f.write(f"{result}\n")
                                print(f"已将 {cve_id} 保存到年份知识库: {year_file}")
                        except Exception as e:
                            print(f"保存 {cve_id} 到年份知识库时出错: {e}")
                except Exception as e:
                    print(f"从NVD API处理 {cve_id} 时出错: {e}")

            # 2. 同时保存到新的统一文件
            if new_cvs_obtained:
                try:
                    # 确保知识库目录存在
                    knowledge_dir = "知识库"
                    cve_dir = os.path.join(knowledge_dir, "cve")
                    os.makedirs(cve_dir, exist_ok=True)
                    
                    # 创建带有日期的文件名
                    current_date = datetime.now().strftime("%Y%m%d")
                    new_file = os.path.join(knowledge_dir, f"new_cve.txt")
                    
                    # 追加模式写入新文件
                    with open(new_file, 'a', encoding='utf-8') as f:
                        for cve_info in new_cvs_obtained:
                            f.write(f"{cve_info}\n")
                    
                    print(f"已将 {len(new_cvs_obtained)} 个新获取的CVE保存到统一文件: {new_file}")
                except Exception as e:
                    print(f"保存到统一新CVE文件时出错: {e}")

    # 7. 返回获取到的CVE数据
    return cve_data


if __name__ == "__main__":
    # 测试 Web 分析
    print("=== 测试 Web 分析 ===")
    
    url ="https://securityonline.info/critical-kubernetes-image-builder-flaw-default-credentials-grant-root-access-to-windows-nodes/"
    extracted_content, report_date, raw_content = web_analyze(url)

    if extracted_content:
        print(f"提取内容: {extracted_content[:200]}...")

    if report_date:
        print(f"报告日期: {report_date}")
        
    # print("\n=== 测试 CVE 数据拿取 ===")
    # # 创建包含一些 CVE 的测试文本
    # test_text = """
    # 在此次安全事件中，攻击者利用了多个漏洞:
    # 1. CVE-2020-14144 - Gitea 中的权限提升漏洞
    # 2. CVE-2025-5068 - Blink渲染引擎中的释放后使用问题。
    # 3. CVE-2025-2783 - Windows版Chrome浏览器中Mojo IPC库的错误句柄问题。

    # 研究人员在深入分析后也确认了系统中存在 CVE-2025-5054 漏洞的影响。
    # """
    
    # # 调用 enrich_cve_data 函数
    # cve_results = enrich_cve_data(
    #     doc_text=test_text
    # )
    
    # # 打印结果
    # if cve_results:
    #     print(f"找到 {len(cve_results)} 个 CVE 数据:")
    #     for cve_id, info in cve_results.items():
    #         print(f"- {cve_id}: {info[:100]}..." if len(info) > 100 else info)
    # else:
    #     print("未找到 CVE 数据")

    # url="https://mp.weixin.qq.com/s?__biz=MzI2MDc2MDA4OA==&mid=2247514665&idx=1&sn=37751d5f4cdb6b4d9786010ddd25e751&chksm=ea664d5edd11c4489b2f2744b6fae637ebf692c2cb95c73929fd9c0d80bfa62ae3913d795dd6&cur_album_id=1539799351089283075&scene=189#wechat_redirect"
    # extract_wechat_article_date(url)