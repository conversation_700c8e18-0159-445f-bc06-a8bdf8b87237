import os
import re
import nltk
import pymupdf as fitz  
import docx  # python-docx for Word files
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import Porter<PERSON><PERSON><PERSON>, WordNetLemmatizer
from nltk.corpus import stopwords
import pdfplumber

# 下载必要的NLTK资源
try:
    nltk.data.find('tokenizers/punkt')
    nltk.data.find('corpora/stopwords')
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('punkt')
    nltk.download('stopwords')
    nltk.download('wordnet')

class DocumentPreprocessor:
    def __init__(self, remove_stopwords=True, max_tokens_per_chunk=512, chunk_overlap=50):
        self.stemmer = PorterStemmer()
        self.lemmatizer = WordNetLemmatizer()
        self.remove_stopwords = remove_stopwords
        self.max_tokens_per_chunk = max_tokens_per_chunk
        self.chunk_overlap = chunk_overlap
        if remove_stopwords:
            self.stop_words = set(stopwords.words('english'))
        
    def preprocess_text(self, text):
        """预处理英文文本，包括分词、词干提取和词形还原"""
        # 将文本转为小写
        text = text.lower()
        
        # 使用NLTK进行分词
        tokens = word_tokenize(text)
        
        # 初始化词干提取器和词形还原器
        stemmer = PorterStemmer()
        lemmatizer = WordNetLemmatizer()
        
        # 去除停用词（可选）
        if self.remove_stopwords:
            tokens = [token for token in tokens if token not in self.stop_words]
        
        # 处理分词结果
        processed_tokens = []
        for token in tokens:
            # 忽略纯数字和太短的词
            if token.isalpha() and len(token) > 2:
                # 词干提取
                stemmed = stemmer.stem(token)
                # 词形还原
                lemmatized = lemmatizer.lemmatize(token)
                
                # 如果词干提取和词形还原结果不同，则同时保留
                if stemmed != lemmatized:
                    processed_tokens.append(stemmed)
                    processed_tokens.append(lemmatized)
                else:
                    processed_tokens.append(stemmed)
            else:
                # 保留原始token（如标点、数字等）
                processed_tokens.append(token)
        
        return processed_tokens
    
    def split_into_paragraphs(self, text):
        """将文本分成页面和段落，保留页面结构"""
        # 先按页面分割
        pages = re.split(r'(## Page \d+)', text)
        
        # 重组页面和内容
        page_contents = []
        i = 1  # 跳过文档标题
        
        while i < len(pages):
            if i + 1 < len(pages) and pages[i].startswith('## Page '):
                page_header = pages[i]
                page_content = pages[i+1]
                
                # 将页面内容分割成段落
                paragraphs = re.split(r'\n\s*\n', page_content)
                # 过滤空段落并保留页面标题
                paragraphs = [p.strip() for p in paragraphs if p.strip()]
                
                if paragraphs:  # 只添加非空页面
                    page_contents.append(page_header + "\n\n" + "\n\n".join(paragraphs))
                
                i += 2  # 跳过已处理的页面标题和内容
            else:
                i += 1
        
        # 如果找不到页面，则使用传统的段落分割
        if not page_contents:
            paragraphs = re.split(r'\n\s*\n|\r\n\s*\r\n', text)
            # 过滤掉空段落
            paragraphs = [p.strip() for p in paragraphs if p.strip()]
            return paragraphs
        
        return page_contents
    
    def extract_text_from_pdf(self, pdf_path):
        """从PDF文件中提取文本，使用PyMuPDF将PDF转换为Markdown格式，并过滤页眉页脚和短内容"""
        text = ""
        try:
            # 方法1: PyMuPDF - 转换为Markdown格式
            doc = fitz.open(pdf_path)
            
            # 用于识别重复页眉页脚的计数器
            header_footer_candidates = {}
            all_lines = []
            
            for page_num in range(len(doc)):
                try:
                    page = doc[page_num]
                    # 使用"markdown"格式提取文本
                    page_text = page.get_text("markdown")
                    
                    # 收集页面中的行用于识别页眉页脚
                    lines = [line.strip() for line in page_text.split('\n')]
                    all_lines.extend([(line, page_num) for line in lines])
                    
                    # 统计潜在的页眉页脚
                    for line in lines:
                        if line.strip():
                            if line not in header_footer_candidates:
                                header_footer_candidates[line] = 1
                            else:
                                header_footer_candidates[line] += 1
                    
                    if page_num > 0:
                        text += "\n\n## Page " + str(page_num + 1) + "\n\n"
                    else:
                        text += "# " + os.path.basename(pdf_path) + "\n\n"
                        text += "## Page 1\n\n"
                    
                    text += page_text
                except Exception as e:
                    print(f"Warning: Error extracting markdown from page {page_num} in {pdf_path}: {e}")
                    # 尝试备选提取方法
                    try:
                        page_text = page.get_text("text")
                        text += "\n\n```\n" + page_text + "\n```\n\n"
                    except:
                        print(f"Failed to extract text from page {page_num} using alternative method")
            
            doc.close()
            
            # 过滤处理：识别并移除页眉页脚和短内容
            # 1. 识别出现在多个页面的短行（可能是页眉页脚）
            potential_headers_footers = []
            page_count = len(doc)
            for text_line, count in header_footer_candidates.items():
                # 如果是短行且出现在多个页面中（超过总页数的30%）
                if len(text_line.strip()) < 50 and count > max(3, page_count * 0.3):
                    potential_headers_footers.append(text_line)
            
            # 2. 清理文本
            cleaned_text = []
            for line in text.split('\n'):
                line_text = line.strip()
                
                # 跳过识别出的页眉页脚
                if line_text in potential_headers_footers:
                    continue
                    
                # 跳过非常短的行（通常是页码或噪声），但保留Markdown标记
                if line_text and (len(line_text) > 5 or line_text.startswith('#')):
                    # 跳过可能的页码
                    if not re.match(r'^[0-9]+$', line_text):
                        cleaned_text.append(line)
            
            # 重建文本
            text = '\n'.join(cleaned_text)
            
            # 如果PyMuPDF提取的文本太少，可能存在问题
            if len(text.strip().split()) < 100:
                print(f"Warning: PyMuPDF extracted limited text from {pdf_path}, trying alternate methods")
                text = ""  # 重置文本
                raise Exception("Text too short, try alternative method")
                    
        except Exception as e:
            # 备选方法代码保持不变...
            print(f"Error extracting text from PDF {pdf_path} using PyMuPDF Markdown: {e}")
            
        # 更完善的句子断行修复
        # 1. 先整体清理文本，删除多余空行
        text = re.sub(r'\n{3,}', '\n\n', text)

        # 2. 合并断行的句子 - 加强版正则表达式
        # 这个规则处理多种情况：
        # - 句子在非句尾标点处断行
        # - 段落分散在多行
        # - 学术文献中的引用断行
        text = re.sub(r'([^\.\!\?\"\'\:\n])\n([a-z\[\(])', r'\1 \2', text)

        # 3. 修复学术引用的断行问题：如 [1][2][3, 4][5, 6, 7]
        text = re.sub(r'(\[\d+(?:,\s*\d+)*\])\n(\[\d+(?:,\s*\d+)*\])', r'\1 \2', text)

        # 4. 修复断开的单词（行尾是连字符，下一行首字符是小写字母）
        text = re.sub(r'(\w+)-\n(\w+)', r'\1\2', text)

        # 5. 保留有意义的断行 - 确保以下情况仍然保持断行：
        # - Markdown 标题
        # - 列表项
        # - 代码块
        text = re.sub(r'(#+\s.*)\n(?!#+\s)', r'\1\n\n', text)  # 标题后加空行
        text = re.sub(r'([-*]\s.*)\n(?![-*]\s)', r'\1\n\n', text)  # 列表项后加空行

        # 6. 清理多余空行（再次）
        text = re.sub(r'\n{3,}', '\n\n', text)

        # 7. 段落重组 - 将明显属于同一段落但被分到不同行的内容合并
        paragraphs = []
        current_para = []

        for line in text.split('\n'):
            line = line.strip()
            
            # 如果是空行，保存当前段落并重置
            if not line:
                if current_para:
                    paragraphs.append(' '.join(current_para))
                    current_para = []
                paragraphs.append('')  # 保留空行
                continue
            
            # 如果是标题、列表项或代码块，作为单独段落
            if re.match(r'^#+\s|^[-*]\s|^```', line):
                if current_para:
                    paragraphs.append(' '.join(current_para))
                    current_para = []
                paragraphs.append(line)
                continue
            
            # 如果以大写字母开头且前面有内容，可能是新段落开始
            if current_para and re.match(r'^[A-Z]', line) and current_para[-1].endswith(('.', '!', '?', '"', "'", ':', ')')):
                paragraphs.append(' '.join(current_para))
                current_para = [line]
            else:
                current_para.append(line)

        # 添加最后一个段落
        if current_para:
            paragraphs.append(' '.join(current_para))

        # 重建文本
        text = '\n\n'.join(p for p in paragraphs if p)

        # 4. 删除非实质性短句段落（如单行页码等）
        paragraphs = re.split(r'\n\n+', text)
        filtered_paragraphs = []
        
        for para in paragraphs:
            # 保留Markdown标题
            if re.match(r'^#+\s', para.strip()):
                filtered_paragraphs.append(para)
            # 保留足够长度的段落
            elif len(para.strip().split()) > 5:
                filtered_paragraphs.append(para)
        
        text = '\n\n'.join(filtered_paragraphs)
        
        # 最后再次执行断行修复
        text = re.sub(r'([^.!?"\'])\n(?![\s\t\d\[A-Z#\*\-])', r'\1 ', text)
        
        with open(pdf_path.replace('.pdf', '_processed.txt'), 'w', encoding='utf-8') as f:
            f.write(text)

        return text
        
    def extract_text_from_docx(self, docx_path):
        """从Word文件中提取文本"""
        text = ""
        try:
            doc = docx.Document(docx_path)
            for para in doc.paragraphs:
                text += para.text + "\n\n"
        except Exception as e:
            print(f"Error extracting text from Word document {docx_path}: {e}")
        return text
    
    def extract_text_from_txt(self, txt_path):
        """从文本文件中提取文本"""
        with open(txt_path, 'r', encoding='utf-8', errors='ignore') as file:
            return file.read()
    
    def chunk_text(self, text):
        """将文本分割成以页面为单位的块，过滤掉内容较少的页面"""
        # 检查输入是否是页面格式
        page_match = re.match(r'Page (\d+)\ntext:```(.*)', text, re.DOTALL)
        if page_match:
            page_num = page_match.group(1)
            page_content = page_match.group(2).strip()
            
            # 计算页面内容的实际词数
            # 除去常见页眉如 "The Cyber Security Body Of Knowledge" 和 "www.cybok.org"
            filtered_content = re.sub(r'The Cyber Security Body Of Knowledge\s*www\.cybok\.org', '', page_content)
            word_count = len(filtered_content.split())

            # 如果页面内容太少（少于300词），则跳过
            if word_count < 300:
                print(f"Skipping Page {page_num} as it contains only {word_count} words")
                return []
                
            # 加入如果word_count页面中出现太多的数字，就丢弃这个页面，因为是参考文献，还有统计了，超过30个数字
            if len(re.findall(r'\b\d+\b', filtered_content)) > 15 and word_count < 500:
                print(f"Skipping Page {page_num} as it contains too many numbers and only {word_count} words")
                return []
            
            # 创建带页面标题的块
            page_header = f"## Page {page_num}"
            
            # 分句处理长页面内容
            if word_count > self.max_tokens_per_chunk:
                sentences = sent_tokenize(filtered_content)
                chunks = []
                current_chunk = [page_header]  # 每个chunk都从页面标题开始
                current_chunk_tokens = len(word_tokenize(page_header))
                
                for sentence in sentences:
                    sentence_tokens = word_tokenize(sentence)
                    sentence_token_count = len(sentence_tokens)
                    
                    # 如果添加这个句子会超过最大token数
                    if current_chunk_tokens + sentence_token_count > self.max_tokens_per_chunk and len(current_chunk) > 1:
                        # 保存当前块
                        chunks.append("\n\n".join(current_chunk))
                        # 从重叠处开始新块，保留页面标题
                        if len(current_chunk) > self.chunk_overlap + 1:
                            # 保留页面标题和一些重叠内容
                            current_chunk = [current_chunk[0]] + current_chunk[-self.chunk_overlap:]
                            current_chunk_tokens = sum(len(word_tokenize(s)) for s in current_chunk)
                        else:
                            current_chunk = [page_header]
                            current_chunk_tokens = len(word_tokenize(page_header))
                    
                    current_chunk.append(sentence)
                    current_chunk_tokens += sentence_token_count
                
                # 添加最后一个块
                if len(current_chunk) > 1:
                    chunks.append("\n\n".join(current_chunk))
                
                return chunks
            else:
                # 对于较短的页面，直接作为一个块返回
                return [f"{page_header}\n\n{filtered_content}"]
        else:
            # 如果不是页面格式，尝试识别并提取页面内容（兼容不同格式）
            page_sections = re.findall(r'(## Page \d+.*?)(?=## Page \d+|$)', text, re.DOTALL)
            if page_sections:
                chunks = []
                for section in page_sections:
                    # 计算有效内容长度
                    clean_section = re.sub(r'The Cyber Security Body Of Knowledge\s*www\.cybok\.org', '', section)
                    word_count = len(clean_section.split())
                    
                    if word_count >= 100:  # 只保留长度足够的页面
                        chunks.append(section.strip())
                return chunks
            
            # 如果没有识别出页面格式，使用默认的句子分块逻辑
            sentences = sent_tokenize(text)
            chunks = []
            current_chunk = []
            current_chunk_tokens = 0
            
            for sentence in sentences:
                sentence_tokens = word_tokenize(sentence)
                sentence_token_count = len(sentence_tokens)
                
                if current_chunk_tokens + sentence_token_count > self.max_tokens_per_chunk and current_chunk:
                    chunks.append(" ".join(current_chunk))
                    if len(current_chunk) > self.chunk_overlap:
                        current_chunk = current_chunk[-self.chunk_overlap:]
                        current_chunk_tokens = sum(len(word_tokenize(s)) for s in current_chunk)
                    else:
                        current_chunk = []
                        current_chunk_tokens = 0
                
                current_chunk.append(sentence)
                current_chunk_tokens += sentence_token_count
            
            if current_chunk:
                chunks.append(" ".join(current_chunk))
            
            return chunks
    
    def process_file(self, file_path):
        """处理单个文件，按页面分块并过滤短页面"""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        # 根据文件类型提取文本
        if file_extension == '.pdf':
            # 使用PyMuPDF提取文本，按页面分割
            processed_chunks = []
            
            try:
                doc = fitz.open(file_path)
                
                # 用于识别和过滤页眉页脚的计数器
                header_footer_candidates = {}
                
                # 第一阶段：收集所有页面内容并识别页眉页脚
                for page_num in range(len(doc)):
                    try:
                        page = doc[page_num]
                        page_text = page.get_text("text")
                        
                        # 统计潜在页眉页脚
                        lines = [line.strip() for line in page_text.split('\n')]
                        for line in lines:
                            if line.strip():
                                if line not in header_footer_candidates:
                                    header_footer_candidates[line] = 1
                                else:
                                    header_footer_candidates[line] += 1
                                
                    except Exception as e:
                        print(f"Warning: Error extracting text from page {page_num+1}: {e}")
                
                # 识别页眉页脚
                potential_headers_footers = []
                page_count = len(doc)
                for text_line, count in header_footer_candidates.items():
                    if len(text_line.strip()) < 50 and count > max(3, page_count * 0.3):
                        potential_headers_footers.append(text_line)
                
                # 第二阶段：处理每个页面并创建块
                for page_num in range(len(doc)):
                    try:
                        page = doc[page_num]
                        page_text = page.get_text("text")
                        
                        # 清理页眉页脚
                        cleaned_lines = []
                        for line in page_text.split('\n'):
                            if line.strip() and line.strip() not in potential_headers_footers:
                                cleaned_lines.append(line)
                        
                        cleaned_page_text = '\n'.join(cleaned_lines)
                        
                        # 创建页面格式的文本块
                        page_block = f"Page {page_num+1}\ntext:```\n{cleaned_page_text}"
                        
                        # 使用chunk_text处理这个页面
                        page_chunks = self.chunk_text(page_block)
                        processed_chunks.extend(page_chunks)
                        
                    except Exception as e:
                        print(f"Warning: Error processing page {page_num+1}: {e}")
                
                doc.close()
                
                # 如果处理后没有任何块，可能是提取失败，尝试备选方法
                if not processed_chunks:
                    print("Warning: Primary extraction method failed, trying alternative...")
                    text = self.extract_text_from_pdf(file_path)
                    paragraphs = self.split_into_paragraphs(text)
                    
                    for para in paragraphs:
                        if para.strip():
                            chunks = self.chunk_text(para)
                            for chunk in chunks:
                                tokens = self.preprocess_text(chunk)
                                if tokens:
                                    processed_chunks.append(' '.join(tokens))
                
            except Exception as e:
                print(f"Error processing PDF {file_path}: {e}")
                # 回退到标准处理方法
                text = self.extract_text_from_pdf(file_path)
                paragraphs = self.split_into_paragraphs(text)
                
                for para in paragraphs:
                    if para.strip():
                        chunks = self.chunk_text(para)
                        for chunk in chunks:
                            tokens = self.preprocess_text(chunk)
                            if tokens:
                                processed_chunks.append(' '.join(tokens))
            
            return processed_chunks
                    
        elif file_extension == '.docx':
            text = self.extract_text_from_docx(file_path)
        elif file_extension == '.txt':
            text = self.extract_text_from_txt(file_path)
        else:
            print(f"Unsupported file format: {file_extension}")
            return []
        
        # 非PDF文件继续使用原来的处理逻辑
        if file_extension != '.pdf':
            paragraphs = self.split_into_paragraphs(text)
            processed_chunks = []
            
            for para in paragraphs:
                if para.strip():
                    chunks = self.chunk_text(para)
                    for chunk in chunks:
                        tokens = self.preprocess_text(chunk)
                        if tokens:
                            processed_chunks.append(' '.join(tokens))
        
        return processed_chunks
    
    def process_directory(self, input_dir, output_dir):
        """处理目录下的所有文件"""
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 遍历目录下所有文件
        for root, _, files in os.walk(input_dir):
            for file in files:
                file_extension = os.path.splitext(file)[1].lower()
                if file_extension in ['.pdf', '.docx', '.txt']:
                    input_path = os.path.join(root, file)
                    output_name = os.path.splitext(file)[0] + '_processed.txt'
                    output_path = os.path.join(output_dir, output_name)
                    
                    print(f"Processing {input_path}...")
                    processed_chunks = self.process_file(input_path)
                    
                    # 将处理后的块写入输出文件，一行一块
                    with open(output_path, 'w', encoding='utf-8') as out_file:
                        for chunk in processed_chunks:
                            out_file.write(chunk + '\n')
                    
                    print(f"Finished processing. Output saved to {output_path}")
                    print(f"Total chunks created: {len(processed_chunks)}")

if __name__ == "__main__":
    # 使用示例
    print("Document Preprocessing Tool for Vector Knowledge Base")
    print("----------------------------------------------------")
    

    remove_stopwords=True # 是否移除停用词
    max_tokens = 2048  # 每个块的最大token数
    overlap = 50  # 块之间的重叠token数

    
    # 初始化处理器
    preprocessor = DocumentPreprocessor(
        remove_stopwords=remove_stopwords,
        max_tokens_per_chunk=max_tokens,
        chunk_overlap=overlap
    )
    
    # 定义输入和输出目录
    input_directory = input("Enter input directory path: ")
    output_directory = input("Enter output directory path: ")
    
    # 处理目录下的所有文件
    preprocessor.process_directory(input_directory, output_directory)

    # file_name='知识库/cybok/Adversarial_Behaviours_v1.0.1.pdf'

    # preprocessor.process_file(file_name)