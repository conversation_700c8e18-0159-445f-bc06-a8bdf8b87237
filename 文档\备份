# 1、拿取关键字列
def process_keywords_data(excel_path):
    """处理带有关键字类型和数据描述的数据流程"""
    try:
        # 读取原始数据
        df_artificial = pd.read_excel(excel_path, sheet_name='artificial')
        df_auto = pd.read_excel(excel_path, sheet_name='auto')

        # 列名验证（新增Describe列）
        required_columns = ['Keywords_name', 'Keywords_type', 'Describe']
        for df in [df_artificial, df_auto]:
            missing_cols = set(required_columns) - set(df.columns)
            if missing_cols:
                raise ValueError(f"缺少必要列: {missing_cols}")

        # 合并数据（新增Describe列）
        combined = pd.concat([
            df_artificial[required_columns],
            df_auto[required_columns]
        ], ignore_index=True)

        # 增强版数据清洗管道
        processed = (
            combined
            .assign(
                # 基础清洗
                Keywords_name=lambda x: x.Keywords_name.str.strip(),
                Keywords_type=lambda x: x.Keywords_type.str.strip().str.lower(),
                # 新增描述清洗
                Describe=lambda x: x.Describe.fillna('').str.strip(),
                # 生成联合搜索字段
                Combined_Text=lambda x: x.Keywords_name + " " + x.Describe
            )
            # 过滤核心字段空值
            .dropna(subset=['Keywords_name', 'Keywords_type'])
            # 去重处理（基于关键字和类型）
            .drop_duplicates(subset=['Keywords_name', 'Keywords_type'])
        )

        # 结果验证
        if processed.empty:
            raise ValueError("清洗后数据为空，请检查输入数据")

        return processed[['Keywords_name', 'Keywords_type', 'Describe', 'Combined_Text']]

    except Exception as e:
        print(f"数据处理失败: {str(e)}")
        raise




def process_website_data(excel_path):
    """处理网站数据"""
    try:
        # 读取原始数据
        df_link = pd.read_excel(excel_path, sheet_name='link')

        # 列名验证（只检查website列）
        required_columns = ['website', 'quality']
        for df in [df_link]:
            if not set(required_columns).issubset(df.columns):
                missing = set(required_columns) - set(df.columns)
                raise ValueError(f"缺少必要列: {missing}")

        # 数据清洗管道
        processed = (
            df_link
            # 清洗website列
            .assign(
                website=lambda x: x.website.str.strip().str.lower(),  # ✅ 正确列名
                quality=lambda x: x.quality.str.strip().str.lower()
            )
            [['website',"quality"]]  # 仅保留website列
            # 过滤空值
            .dropna(subset=required_columns)
            # 去重处理
            .drop_duplicates(subset=required_columns)
        )

        # 过滤可用域名
        valid_websites = filter_available_websites(processed)

        # 结果验证
        if valid_websites.empty:
            raise ValueError("清洗后数据为空，请检查输入数据")
        return valid_websites

    except Exception as e:
        print(f"网站数据处理失败: {str(e)}")
        raise




def main():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    excel_path = os.path.join(current_dir, '判断标准.xlsx')
    # 1、拿到数据 
    try:

         result_df = process_keywords_data(excel_path)
         website_df = process_website_data(excel_path) 


        print(f"\n成功处理 {len(result_df)} 条有效记录")
        print("数据类型分布:")
        print(result_df.Keywords_type.value_counts().to_string())


        print(website_df.head(3).to_string(index=False))
        print(f"共获取 {len(website_df)} 个有效域名")

        # 2、进行谷歌检索语法的拼接

        # 新增搜索模式选择
        print("\n请选择搜索模式：")
        print("1. 全部关键字检索")
        print("2. 单个关键字检索")
        search_mode = input("请输入选项数字 (1/2): ").strip()

        if search_mode == '1':
            # 全量模式
            print("\n🔍 您选择了【全部关键字】检索模式")
            search_syntax = generate_google_dorks(result_df, website_df)
            print(f"系统将执行全量检索，共生成 {len(search_syntax)} 条搜索语法")
            
        elif search_mode == '2':
            # 单个关键字模式
            print("\n🔍 您选择了【单个关键字】检索模式")
            print(f"当前可用类型: {result_df.Keywords_type.unique().tolist()}")
            
            user_input = input("请输入搜索内容（格式：类型:关键字，如 group:APT34）:").strip()
            matched_series = fuzzy_match_with_type(user_input, result_df)
            
            if matched_series is None:
                print(f"🚨 未找到匹配项: {user_input}")
                return

            # 转换为标准DataFrame格式
            matched_df = pd.DataFrame([matched_series])
            print(f"✅ 匹配到关键字 - 类型: {matched_series.Keywords_type}, 名称: {matched_series.Keywords_name}")
            
            search_syntax = generate_google_dorks(matched_df, website_df)
            print(f"生成 {len(search_syntax)} 条相关搜索语法")
            
        else:
            print("🚫 无效的选项，程序终止")
            return
    
    except FileNotFoundError:
        print(f"错误：文件路径不存在 - {excel_path}")

    # 3、使用searchapi
    try:
        # 初始化API客户端
        searcher = GoogleSearchAPI(api_key="kqTUhhc6JJV1htYv8QyBUenY")  # 建议改用环境变量

        # 获取日期范围
        start_date, end_date = get_date_range()

        # 测试
        # search_syntax = [
        #     '"Lazarus" site:blog.sekoia.io',
        #     '"Lazarus" site:decoded.avast.io'
        # ]
        
        # 执行搜索并保存结果
        all_results = []
        for idx, query in enumerate(search_syntax[:5]):  # 示例仅处理前1111条
            print(f"正在搜索 ({idx+1}/{len(search_syntax)})：{query}")
            
            results = searcher.safe_search(
                query,
                start_date=start_date,
                end_date=end_date)
            all_results.extend([{'query': query, **r} for r in results])
            
            time.sleep(1)  # 基础速率限制

        # 保存结构化结果
        output_path = os.path.join(current_dir, 'search_results.json')
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n成功获取 {len(all_results)} 条搜索结果，已保存至 {output_path}")
        print(f"数据已保存至 {output_path}")

    except Exception as e:
        print(f"API处理失败: {str(e)}")

    # 4、在获取威胁情报的链接后，对每个链接进行爬取，将整个网页保存成HTML的，标题是该链接的域名+关键字。
    # 转到data_cope.py处理
    # 数据处理脚本
    processing_data_name = 'data_cope.py'
    try:
        print(f"🕷️ 开始保存HTML {processing_data_name}，请等待......")
        # 修改编码处理方式
        proc = subprocess.Popen(
            [sys.executable, processing_data_name, output_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT
        )

        output = []
        while True:
            line_bytes = proc.stdout.readline()
            if not line_bytes:
                if proc.poll() is not None:
                    break
                continue  # 防止空数据时提前退出
            
            # 兼容性解码
            try:
                line = line_bytes.decode('utf-8', errors='replace').rstrip()
            except UnicodeDecodeError:
                line = line_bytes.decode('gbk', errors='replace').rstrip()
            
            print(f"[DATA_COPE] {line}")
            output.append(line)

        exit_code = proc.wait()

        if exit_code != 0:
            raise subprocess.CalledProcessError(
                exit_code, 
                proc.args, 
                '\n'.join(output)
            )
        
        # joined_output = '\n'.join(output)
        print(f"{processing_data_name} 执行成功\n")
        # print(f"{processing_data_name} 输出日志\n{joined_output}")

        return True

    except subprocess.CalledProcessError as e:
        print(f"HTML保存失败：{e.output}")
        return False
    except Exception as e:
        print(f"未知处理错误：{str(e)}")
        return False

if __name__ == "__main__":
    main()

    


    
爬虫

""""
处理关键词+域名语法检索到的网页链接，保存成html,以供后续处理

"""
import os
import io
import json
import time
import re
import hashlib
import sys
import urllib3
from urllib.parse import urlparse
from bs4 import BeautifulSoup

# 修改这个，从数据库中拿取数据进行爬取，如果爬取到得html，保存到数据库中得对应得字段中去

# 强制标准流使用UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

# 禁用SSL警告
urllib3.disable_warnings()

def sanitize_filename(filename):
    """清理非法文件名字符"""
    return re.sub(r'[\\/*?:"<>|]', '_', filename)

def extract_keyword(query):
    """从搜索语句中提取主关键词"""
    match = re.search(r'"([^"]+)"', query)
    return match.group(1) if match else "unknown"

class HttpManager:
    """自定义HTTP客户端"""
    def __init__(self):
        # 配置代理
        self.proxy_url = "http://127.0.0.1:7890"
        
        # 配置重试策略
        retries = urllib3.Retry(
            total=3,
            backoff_factor=0.1,
            status_forcelist=[500, 502, 503, 504],
            redirect=3  # 处理重定向
        )
        
        # 创建代理连接池
        self.http = urllib3.ProxyManager(
            self.proxy_url,
            cert_reqs='CERT_NONE',  # 禁用证书验证
            retries=retries,
            timeout=urllib3.Timeout(connect=5.0, read=10.0)
        )

    def request(self, method, url, headers=None):
        """发送HTTP请求"""
        return self.http.request(method, url, headers=headers)

def generate_filename(url, query):
    """生成包含完整URL信息的文件名"""
    parsed = urlparse(url)
    domain = parsed.netloc.replace('www.', '')
    
    # 生成URL的哈希标识（8位）
    url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
    
    # 提取关键词
    keyword = extract_keyword(query)
    
    # 构建文件名结构
    return f"{domain}_{sanitize_filename(keyword)}_{url_hash}.html"


def is_detail_page(url, html_content):
    """判断是否为详情页面的复合策略"""
    # 策略1：URL模式过滤
    exclude_patterns = [
        r'/tag/', r'/category/', r'/author/',
        r'/search/', r'/page/\d+', r'/feed'
    ]
    if any(re.search(p, url) for p in exclude_patterns):
        return False
    
    # 策略2：OG类型检测
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        og_type = soup.find('meta', property='og:type')
        if og_type and og_type['content'].lower() == 'article':
            return True
    except Exception as e:
        print(f"OG检测异常: {str(e)}")
    
    # 策略3：正文内容长度分析
    content_selectors = [
        {'name': 'article', 'attrs': {'class': 'post-content'}},  # 常见CMS结构
        {'name': 'div', 'attrs': {'class': 'main-content'}},
        {'name': 'article'}  # 通用标签
    ]
    
    for selector in content_selectors:
        article = soup.find(**selector)
        if article:
            text_length = len(article.get_text(strip=True))
            if text_length > 1500:  # 根据实际阈值调整
                return True
            break
    
    # 策略4：发布时间检测
    time_selectors = [
        {'name': 'time', 'attrs': {'class': 'post-date'}},
        {'name': 'span', 'attrs': {'class': 'date'}},
        {'name': 'meta', 'attrs': {'itemprop': 'datePublished'}}
    ]
    
    for selector in time_selectors:
        if soup.find(**selector):
            return True
    
    return False

def save_webpage(url, query, output_dir="threat_intel_html"):
    """
    使用urllib3保存网页内容
    """
    try:
        # 初始化HTTP客户端
        client = HttpManager()
        
        # 配置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://blog.sekoia.io/'
        }

        # 发送请求
        print(f"\n[urllib3] 请求开始: {url}")
        start_time = time.time()
        
        resp = client.request('GET', url, headers=headers)
        
        elapsed = time.time() - start_time
        print(f"[urllib3] 响应状态: {resp.status} 耗时: {elapsed:.2f}s")

        # 解析域名和关键词
        domain = urlparse(url).netloc.replace('www.', '')
        keyword = extract_keyword(query)
        
        # 创建保存目录
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件名
        filename = f"{domain}_{sanitize_filename(keyword)}.html"
        filepath = os.path.join(output_dir, filename)

        # 检测编码
        content_type = resp.headers.get('Content-Type', '')
        encoding = 'utf-8'
        if 'charset=' in content_type:
            encoding = content_type.split('charset=')[-1]
        else:
            try:
                import chardet
                encoding = chardet.detect(resp.data)['encoding'] or 'utf-8'
            except ImportError:
                pass

        # 在保存前进行过滤判断
        if not is_detail_page(url, resp.data.decode(errors='ignore')):
            print(f"跳过非详情页: {url}")
            return None

        # 保存文件
        filename = generate_filename(url, query)
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'wb') as f:
            f.write(resp.data)
        
        print(f"成功保存: {filepath}")
        return filepath

    except urllib3.exceptions.ProxyError as pe:
        print(f"[Critical] 代理连接失败: {str(pe)}")
        print("解决方案: 检查Clash是否运行在127.0.0.1:7890")
        return None
        
    except urllib3.exceptions.HTTPError as he:
        print(f"[Error] HTTP异常: {str(he)}")
        return None
        
    except Exception as e:
        print(f"[Error] 未知异常: {str(e)}")
        return None

def crawl_search_results(results_path, output_dir="html_archives"):
    """
    完整的爬取流程
    """
    # 创建保存目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载搜索结果
    with open(results_path, 'r', encoding='utf-8') as f:
        search_results = json.load(f)
    
    success_count = 0
    error_count = 0
    crawled_urls = set()

    print(f"\n开始处理 {len(search_results)} 条搜索结果...")
    
    for result in search_results:
        url = result.get('link', '')
        query = result.get('query', '')
        
        # 跳过无效条目
        if not url or not query:
            error_count += 1
            continue
        
        # 去重处理
        if url in crawled_urls:
            print(f"跳过重复链接: {url}")
            continue
        crawled_urls.add(url)
        
        # 执行爬取
        try:
            saved_path = save_webpage(url, query, output_dir)
            if saved_path:
                success_count += 1
            else:
                error_count += 1
        except Exception as e:
            print(f"处理异常 [{url}]: {str(e)}")
            error_count += 1
        
        # 保持请求间隔
        time.sleep(1)
    
    print(f"\n爬取完成: 成功 {success_count} 条 | 跳过非正文 {error_count} 条")
    print(f"文件保存目录: {os.path.abspath(output_dir)}")





if __name__ == "__main__":

    crawl_search_results()




提取正文
""""
处理HTML文件，并使用DeepSeek进行分析
"""
from bs4 import BeautifulSoup
from readability import Document
import os
import re
import json
import logging
import time
from pathlib import Path
import requests
from requests.exceptions import RequestException



def should_filter_text(text):
    """增强版过滤规则"""
    filter_patterns = [
        r'Log\s*in', 
        r'Reset(\s*\w*)*',
        r'Search\s*the\s*site',
        r'All\s*categories',
        r'Sign\s*up',
        r'Table\s*of\s*contents',
        r'Share\s*this\s*post',
        r'Comments?\s*are\s*closed',
        r'Read\s*more',
        r'Follow\s*us',
        r'(value|respect|protect)[s]?[\s\w]*privacy',  
        r'cookie[s]?[\s\w]*use',
        r'by\s*(continuing|using|accessing)',
        r'terms\s*of\s*(use|service)',
        r'privacy\s*(policy|notice|statement)',
        r'同意使用cookies?',
        r'使用Cookie',
    ]
    return any(re.search(pattern, text, re.IGNORECASE) for pattern in filter_patterns)

def clean_extracted_text(text):
    """优化后的文本清理"""
    # 合并重复段落
    text = re.sub(r'(.+?)\n\s*\1', r'\1', text, flags=re.DOTALL)
    # 标准化换行符
    text = re.sub(r'\n{3,}', '\n\n', text)
    # 移除短行噪音
    text = re.sub(r'^\W{0,5}$', '', text, flags=re.MULTILINE)
    return text.strip()

def extract_main_content(html):
    """使用readability-lxml提取正文"""
    doc = Document(html)
    return doc.summary()

def process_html_content(html_path):
    """完整的处理流程"""
    with open(html_path, 'r', encoding='utf-8') as f:
        html = f.read()
    
    # 第一阶段：使用readability提取主体内容
    main_content = extract_main_content(html)
    
    # 第二阶段：用BeautifulSoup精细处理
    soup = BeautifulSoup(main_content, 'html.parser')
    
    # 移除残留的干扰元素
    for element in soup(['aside', 'figure', 'svg', 'footer', 'header']):
        element.decompose()
    
    # 结构化提取文本
    sections = []
    previous_is_heading = False
    for element in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'blockquote']):
        if element.name in ['h1', 'h2', 'h3', 'h4']:
            # 在标题前添加分隔线（第一个标题除外）
            if sections and not previous_is_heading:
                sections.append('-'*40 + '\n')

            text = '\n' + element.get_text(separator=' ', strip=True) + '\n'
            # 新增标题过滤
            if should_filter_text(text):
                continue  # 跳过隐私相关标题
            sections.append(text)
            previous_is_heading = True
        else:
            text = element.get_text(separator=' ', strip=True)
            if text and not should_filter_text(text) and len(text) >= 50:
                sections.append(text)
                previous_is_heading = False
        
    
    return clean_extracted_text('\n'.join(sections))

def batch_process_html_folder(input_folder, output_folder):
    """增强版批量处理"""
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    skipped_files = []
    for filename in os.listdir(input_folder):
        if filename.lower().endswith(('.html', '.htm')):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, f"{os.path.splitext(filename)[0]}.txt")
            
            try:
                cleaned_text = process_html_content(input_path)
                # 新增有效性检查
                if cleaned_text.strip() == '':
                    skipped_files.append( (filename, "内容为空") )
                    continue
                    
                if len(cleaned_text) < 200:
                    skipped_files.append( (filename, f"内容过短（{len(cleaned_text)}字符）") )
                    continue

                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(cleaned_text)
                print(f"成功处理: {filename} → {output_path}")
            except Exception as e:
                print(f"处理失败 {filename}: {str(e)}")

            # 打印跳过文件统计
    if skipped_files:
        print("\n跳过以下无效文件:")
        for fname, reason in skipped_files:
            print(f" - {fname}: {reason}")

# # 使用示例-----可以了，下一步进行deepseek分析,,,提示词再进行改进
batch_process_html_folder('html_archives', 'cleaned_texts')





"""
-------关键词模糊匹配-----

"""


def fuzzy_match_with_type(user_input, keywords_df):
    """类型感知模糊匹配（使用Combined_Text增强匹配）"""
    # 输入解析
    if ':' in user_input:
        input_type, input_keyword = map(str.strip, user_input.split(':', 1))
    else:
        input_type, input_keyword = None, user_input.strip()

    # 构建增强候选列表
    candidates = []
    for _, row in keywords_df.iterrows():
        candidates.append({
            'Keywords_type': row.Keywords_type,
            'Keywords_name': row.Keywords_name,
            # 使用组合文本构建匹配字符串（类型:组合文本）
            'match_str': f"{row.Keywords_type.lower()}:{row.Combined_Text.lower()}"
        })

    # 执行模糊匹配（输入也转为小写）
    normalized_input = f"{input_type or ''}:{input_keyword}".lower().strip(':')
    matches = get_close_matches(
        normalized_input,
        [c['match_str'] for c in candidates],
        n=5,
        cutoff=0.5
    )

    # 类型优先匹配（匹配类型前缀和组合文本）
    if input_type:
        type_matched = [
            c for c in candidates 
            if c['Keywords_type'].lower().startswith(input_type.lower()) 
            and normalized_input.split(':', 1)[-1] in c['match_str']
        ]
        if type_matched:
            best_match = type_matched[0]
            return pd.Series({
                'Keywords_type': best_match['Keywords_type'],
                'Keywords_name': best_match['Keywords_name']
            })

    # 全局最佳匹配（从所有候选中选择）
    if matches:
        best_match = next(c for c in candidates if c['match_str'] == matches[0])
        return pd.Series({
            'Keywords_type': best_match['Keywords_type'],
            'Keywords_name': best_match['Keywords_name']
        })
    
    return None

