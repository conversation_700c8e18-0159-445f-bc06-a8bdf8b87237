import os
import re
from typing import List, Dict, Any, Tuple, Optional
import numpy as np
import torch
from tqdm import tqdm

from langchain_community.vectorstores import Chroma
from pikerag.knowledge_retrievers import BaseQaRetriever
from pikerag.utils.logger import Logger
from pikerag.workflows.common import GenerationQaData

class HybridRetriever(BaseQaRetriever):
    """
    混合检索器 - 结合密集向量、稀疏检索和重排序
    支持BGE-M3混合检索模型
    """
    
    def __init__(self, retriever_config, log_dir="outputs", main_logger=None):
        super().__init__(retriever_config, log_dir, main_logger)
        self.vector_db_path = retriever_config.get("vector_db_path")
        self.top_k = retriever_config.get("top_k", 5)
        self.use_hybrid = retriever_config.get("use_hybrid", True)
        self.use_reranker = retriever_config.get("use_reranker", True)
        self.sparse_weight = retriever_config.get("sparse_weight", 0.1)  # 混合搜索中稀疏检索的权重
        
        # 选择模型配置
        self.model_name = retriever_config.get("model", "BAAI/bge-m3")
        self.reranker_model = retriever_config.get("reranker", "BAAI/bge-reranker-v2")
        
        # 确保有日志记录器
        if not hasattr(self, 'logger'):
            self.logger = main_logger or Logger(name="hybrid_retriever", dump_folder=log_dir)
        
        # 加载向量数据库
        self._init_vector_store()
        
        # 加载检索和重排序模型
        self._init_models()
    
    def _init_vector_store(self):
        """初始化向量存储"""
        try:
            from langchain_community.vectorstores import Chroma
            from langchain_huggingface import HuggingFaceEmbeddings
            
            # 先使用基本嵌入模型初始化 - 后续会被替换
            self.embeddings = HuggingFaceEmbeddings(
                model_name="sentence-transformers/all-MiniLM-L6-v2",
                model_kwargs={'device': 'cpu'},
                encode_kwargs={'normalize_embeddings': True}
            )
            
            # 检查向量存储路径是否存在
            if not os.path.exists(self.vector_db_path):
                raise FileNotFoundError(f"向量数据库路径不存在: {self.vector_db_path}")
            
            # 加载向量存储
            self.logger.info(f"正在加载向量数据库: {self.vector_db_path}")
            self._vector_store = Chroma(
                persist_directory=self.vector_db_path,
                embedding_function=self.embeddings
            )
            
            self.collection_size = self._vector_store._collection.count()
            self.logger.info(f"成功加载向量数据库，包含 {self.collection_size} 个文档")
            
        except Exception as e:
            self.logger.error(f"初始化向量存储时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            self._vector_store = None
    
    def _init_models(self):
        """初始化BGE-M3和重排序模型"""
        try:
            # 检查是否安装了必要的库
            missing_libraries = []
            try:
                import FlagEmbedding
            except ImportError:
                missing_libraries.append("FlagEmbedding")
            
            if missing_libraries:
                self.logger.warning(f"缺少必要的库: {', '.join(missing_libraries)}，将使用基本检索")
                self.logger.warning("请运行 'pip install -U FlagEmbedding' 以启用高级检索功能")
                self.use_hybrid = False
                self.use_reranker = False
                return
            
            # 加载BGE-M3模型用于混合检索
            if self.use_hybrid:
                self.logger.info(f"加载BGE-M3混合检索模型: {self.model_name}")
                from FlagEmbedding import BGEM3FlagModel
                
                self.m3_model = BGEM3FlagModel(
                    self.model_name,
                    use_fp16=True  # 使用半精度加速
                )
                self.logger.info("BGE-M3模型加载成功")
            
            # 加载重排序模型
            if self.use_reranker:
                self.logger.info(f"加载重排序模型: {self.reranker_model}")
                from FlagEmbedding import FlagReranker
                
                self.reranker = FlagReranker(
                    self.reranker_model,
                    use_fp16=True
                )
                self.logger.info("重排序模型加载成功")
            
        except Exception as e:
            self.logger.error(f"初始化模型时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            self.use_hybrid = False
            self.use_reranker = False
    
    def query_expansion(self, query: str) -> List[str]:
        """查询扩展 - 生成多个查询变体"""
        # 基础变体
        variants = [query]
        
        # 添加领域特定前缀
        domain_prefixes = [
            "cybersecurity", "malware", "threat actor", 
            "vulnerability", "attack", "APT"
        ]
        
        # 检查是否已有领域术语
        has_domain_term = any(term in query.lower() for term in domain_prefixes)
        
        # 只有在查询中没有领域术语时才添加
        if not has_domain_term:
            for prefix in domain_prefixes[:2]:  # 限制变体数量
                if len(query.split()) < 10:  # 只对短查询进行扩展
                    variants.append(f"{prefix} {query}")
        
        return variants
    
    def retrieve_contents(self, qa_data: GenerationQaData, **kwargs) -> List[str]:
        """检索内容 - 实现接口方法"""
        query = qa_data.question
        return self.retrieve_contents_by_query(query, **kwargs)
    
    def retrieve_contents_by_query(self, query: str, retrieve_id: str = "", **kwargs) -> List[str]:
        """从向量数据库检索内容 - 支持混合检索"""
        if not query or not query.strip():
            self.logger.warning(f"收到空查询")
            return []
            
        if not hasattr(self, "_vector_store") or not self._vector_store:
            self.logger.error("向量存储未初始化")
            return []
        
        try:
            # 设置检索参数
            top_k = kwargs.get("top_k", self.top_k)
            score_threshold = kwargs.get("score_threshold", 0.2)
            
            self.logger.info(f"检索查询: '{query[:50]}...', top_k={top_k}")
            
            # 1. 使用高级混合检索 (如果启用)
            if self.use_hybrid and hasattr(self, "m3_model"):
                return self._perform_hybrid_search(query, top_k)
            
            # 2. 回退到基本向量检索
            expanded_queries = self.query_expansion(query)
            all_results = []
            
            for expanded_query in expanded_queries:
                # 使用MMR方法检索
                search_kwargs = {"k": top_k, "score_threshold": score_threshold}
                try:
                    results = self._vector_store.max_marginal_relevance_search(
                        expanded_query, **search_kwargs
                    )
                    
                    # 提取内容
                    for doc in results:
                        if hasattr(doc, "page_content") and doc.page_content:
                            all_results.append((doc.page_content, 1.0))  # 假设分数为1.0
                except:
                    # 如果MMR失败，尝试普通的相似度搜索
                    results = self._vector_store.similarity_search(
                        expanded_query, k=top_k, score_threshold=score_threshold
                    )
                    
                    for doc in results:
                        if hasattr(doc, "page_content") and doc.page_content:
                            all_results.append((doc.page_content, 1.0))
            
            # 去重
            seen_contents = set()
            unique_results = []
            
            for content, score in all_results:
                if content not in seen_contents:
                    seen_contents.add(content)
                    unique_results.append((content, score))
            
            # 排序并返回内容
            unique_results.sort(key=lambda x: x[1], reverse=True)
            contents = [content for content, _ in unique_results[:top_k]]
            
            self.logger.info(f"检索到 {len(contents)}/{top_k} 个结果")
            return contents
            
        except Exception as e:
            self.logger.error(f"检索过程中发生错误: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return []
    
    def _perform_hybrid_search(self, query: str, top_k: int) -> List[str]:
        """执行BGE-M3混合检索"""
        try:
            # 1. 使用BGE-M3编码查询 - 获取密集向量和稀疏向量
            query_embeddings = self.m3_model.encode(query)
            dense_vec = query_embeddings['dense_vecs'][0]  # 密集向量
            
            # 2. 从Chroma中获取所有文档向量和ID
            # 注意：这里有性能问题 - 实际应用中应该直接将BGE-M3的向量存入矢量数据库
            # 这里只是临时解决方案，展示混合检索的概念
            all_docs = self._vector_store.similarity_search_with_score(
                query=query,
                k=min(self.collection_size, 100)  # 限制检索数量
            )
            
            # 3. 收集文档向量和内容
            candidate_docs = []
            for doc, dense_score in all_docs:
                if hasattr(doc, "page_content") and doc.page_content:
                    candidate_docs.append((doc.page_content, dense_score))
            
            # 4. 如果启用了重排序，使用重排序模型
            if self.use_reranker and hasattr(self, "reranker") and len(candidate_docs) > 0:
                docs_to_rerank = [doc for doc, _ in candidate_docs]
                
                # 使用重排序模型计算得分
                rerank_scores = self.reranker.compute_score(
                    query, docs_to_rerank
                )
                
                # 结合重排序得分
                reranked_results = list(zip(docs_to_rerank, rerank_scores))
                reranked_results.sort(key=lambda x: x[1], reverse=True)
                
                # 返回排名最高的结果
                top_results = reranked_results[:top_k]
                self.logger.info(f"重排序后返回 {len(top_results)} 个结果")
                return [doc for doc, _ in top_results]
            else:
                # 如果不使用重排序，直接返回密集向量检索结果
                candidate_docs.sort(key=lambda x: x[1])  # 注意Chroma的分数是距离，越小越好
                return [doc for doc, _ in candidate_docs[:top_k]]
                
        except Exception as e:
            self.logger.error(f"执行混合检索时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            
            # 出错时回退到基本检索
            basic_results = self._vector_store.similarity_search(
                query, k=top_k
            )
            return [doc.page_content for doc in basic_results if hasattr(doc, "page_content")]