
"""
MCP Web Analyzer - 主启动模块

用于启动和执行 MCP Web 内容爬取、处理和分析功能的脚本
"""
import os
import sys
import time
import argparse
from datetime import datetime
from typing import List, Dict, Optional

# 导入 cope_mcp.py 中的类和函数
from cope_mcp import (
    HttpManager, CrawlerDB, ContentAnalyzer,
    crawl_url, process_html_content, process_pdf_content, download_pdf
)


class MCPRunner:
    """MCP Web 分析器的主运行类"""
    def __init__(self, db_path: str = "mcp_crawler.db", use_proxy: bool = False):
        """初始化 MCP 运行器"""
        self.db = CrawlerDB(db_path)
        self.analyzer = ContentAnalyzer()
        self.use_proxy = use_proxy
        print(f"MCP Web Analyzer 已初始化. 数据库: {db_path}, 使用代理: {self.use_proxy}")
        
    def add_url(self, url: str, query: str = "direct_input") -> bool:
        """添加 URL 到待爬取队列"""
        return self.db.add_url(url, query)
        
    def crawl_pending_urls(self, limit: int = 10) -> int:
        """爬取待处理的 URL"""
        pending_urls = self.db.get_pending_urls(limit)
        if not pending_urls:
            print("没有待爬取的 URL")
            return 0
            
        print(f"开始爬取 {len(pending_urls)} 个 URL...")
        processed = 0
        
        for item in pending_urls:
            url = item['link']
            query = item['query']
            print(f"正在爬取: {url}")
            
            # 爬取 URL 内容
            content, status, content_type = crawl_url(url, query, self.use_proxy)
            
            # 更新爬取结果到数据库
            self.db.update_crawl_result(url, content, status, content_type)
            
            # 如果爬取成功，提取和处理内容
            if status == 1:
                if content_type == "text/html":
                    # 处理 HTML 内容
                    extracted_text = process_html_content(content)
                    if extracted_text:
                        self.db.update_extracted_text(url, extracted_text)
                        print(f"已提取 HTML 内容: {len(extracted_text)} 字符")
                    else:
                        print(f"无法提取有效内容")
                        
                elif content_type == "application/pdf":
                    # 处理 PDF 内容 (content 实际上是 PDF 文件路径)
                    pdf_text = download_pdf(url) if url.lower().endswith('.pdf') else None
                    if not pdf_text and os.path.exists(content):
                        # 如果下载失败但文件已保存，尝试直接处理文件
                        try:
                            import PyPDF2
                            with open(content, 'rb') as f:
                                pdf_reader = PyPDF2.PdfReader(f)
                                pdf_text = ""
                                for page_num in range(len(pdf_reader.pages)):
                                    pdf_text += pdf_reader.pages[page_num].extract_text()
                        except Exception as e:
                            print(f"PDF 处理失败: {str(e)}")
                            pdf_text = None
                    
                    if pdf_text:
                        extracted_text = process_pdf_content(pdf_text)
                        self.db.update_extracted_text(url, extracted_text)
                        print(f"已提取 PDF 内容: {len(extracted_text)} 字符")
                    else:
                        print(f"无法提取 PDF 内容")
            else:
                print(f"爬取失败，状态码: {status}")
                
            processed += 1
            time.sleep(1)  # 避免请求过快
            
        return processed
        
    def analyze_pending_content(self, limit: int = 10) -> int:
        """分析待处理的内容"""
        pending_items = self.db.get_pending_analysis(limit)
        if not pending_items:
            print("没有待分析的内容")
            return 0
            
        print(f"开始分析 {len(pending_items)} 个文档...")
        analyzed = 0
        
        for item in pending_items:
            item_id = item['id']
            url = item['link']
            text = item['text']
            
            print(f"正在分析: {url}")
            
            # 执行内容分析
            analysis_result = self.analyzer.analyze_content(text, url)
            
            # 更新分析结果
            self.db.update_analysis_result(item_id, analysis_result)
            print(f"分析完成: {analysis_result.get('main_topic', '未知主题')}")
            
            analyzed += 1
            
        return analyzed
        
    def get_analysis_result(self, url: str) -> Optional[Dict]:
        """获取指定 URL 的分析结果"""
        return self.db.get_result_by_url(url)
        
    def close(self):
        """关闭资源"""
        self.db.close()


def display_analysis_result(result: Dict):
    """格式化显示分析结果"""
    if not result:
        print("未找到分析结果")
        return
        
    print("\n" + "=" * 50)
    print(f"URL: {result.get('link')}")
    print(f"标题: {result.get('title', '未知')}")
    print("-" * 50)
    
    # 分析状态
    analysis_status = result.get('analysis_status')
    if analysis_status != 1:
        print(f"分析状态: {'待分析' if analysis_status == 0 else '分析失败'}")
        return
        
    # 显示基本信息
    print(f"主题: {result.get('main_topic', '未知')}")
    print(f"情感得分: {result.get('sentiment_score', 0):.2f} (-1 负面到 +1 正面)")
    print(f"内容类型: {result.get('content_type', '未知')}")
    print(f"分析时间: {result.get('analysis_time', '未知')}")
    
    # 显示摘要
    print("\n摘要:")
    print(result.get('summary', '无摘要'))
    
    # 显示关键实体
    key_entities = result.get('key_entities', [])
    if key_entities:
        print("\n关键实体:")
        for entity in key_entities:
            print(f"- {entity.get('name', '')} ({entity.get('type', '未知')})")
    
    # 显示详细分析报告
    report = result.get('analysis_report', {})
    if report and isinstance(report, dict):
        print("\n详细分析:")
        
        # 文本统计
        if 'text_stats' in report:
            stats = report['text_stats']
            print(f"文字数量: {stats.get('word_count', 0)}")
            print(f"句子数量: {stats.get('sentence_count', 0)}")
            print(f"平均句长: {stats.get('avg_sentence_length', 0)}")
        
        # 关键词
        if 'top_keywords' in report and report['top_keywords']:
            print("\n关键词:")
            for word, freq in report['top_keywords'].items():
                print(f"- {word}: {freq}")
        
        # 可读性
        if 'readability' in report:
            read = report['readability']
            print(f"\n可读性: {read.get('level', '未知')} (得分: {read.get('score', 0)})")
        
        # 来源信息
        if 'source_info' in report:
            source = report['source_info']
            print(f"\n来源: {source.get('domain', '未知')}")
            print(f"可信度评分: {source.get('credibility_score', 0)}/10")
    
    print("=" * 50)


def main():
    """主函数入口"""
    parser = argparse.ArgumentParser(description="MCP Web 内容分析工具")
    
    # 主命令参数
    parser.add_argument("--db", type=str, default="mcp_crawler.db", help="数据库文件路径")
    parser.add_argument("--proxy", action="store_true", help="使用代理 (http://127.0.0.1:7890)")
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 添加 URL 命令
    add_parser = subparsers.add_parser("add", help="添加 URL 到爬取队列")
    add_parser.add_argument("url", type=str, help="要爬取的 URL")
    add_parser.add_argument("--query", type=str, default="direct_input", help="查询关键词")
    
    # 爬取命令
    crawl_parser = subparsers.add_parser("crawl", help="爬取待处理的 URL")
    crawl_parser.add_argument("--limit", type=int, default=5, help="最多爬取数量")
    
    # 分析命令
    analyze_parser = subparsers.add_parser("analyze", help="分析已爬取的内容")
    analyze_parser.add_argument("--limit", type=int, default=5, help="最多分析数量")
    
    # 获取分析结果命令
    result_parser = subparsers.add_parser("result", help="获取分析结果")
    result_parser.add_argument("url", type=str, help="要查询的 URL")
    
    # 一键处理命令
    process_parser = subparsers.add_parser("process", help="一键添加、爬取和分析")
    process_parser.add_argument("url", type=str, help="要处理的 URL")
    
    args = parser.parse_args()
    
    # 初始化 MCP 运行器
    runner = MCPRunner(db_path=args.db, use_proxy=args.proxy)
    
    try:
        if args.command == "add":
            # 添加 URL
            if runner.add_url(args.url, args.query):
                print(f"成功添加 URL: {args.url}")
            else:
                print(f"添加 URL 失败")
                
        elif args.command == "crawl":
            # 爬取 URL
            count = runner.crawl_pending_urls(args.limit)
            print(f"已处理 {count} 个 URL")
            
        elif args.command == "analyze":
            # 分析内容
            count = runner.analyze_pending_content(args.limit)
            print(f"已分析 {count} 个文档")
            
        elif args.command == "result":
            # 获取分析结果
            result = runner.get_analysis_result(args.url)
            display_analysis_result(result)
            
        elif args.command == "process":
            # 一键处理
            print(f"开始处理 URL: {args.url}")
            
            # 添加
            if runner.add_url(args.url):
                print(f"已添加 URL 到队列")
                
                # 爬取
                count = runner.crawl_pending_urls(1)
                if count > 0:
                    print(f"已爬取内容")
                    
                    # 分析
                    count = runner.analyze_pending_content(1)
                    if count > 0:
                        print(f"已完成分析")
                        
                        # 显示结果
                        result = runner.get_analysis_result(args.url)
                        display_analysis_result(result)
                    else:
                        print("分析失败")
                else:
                    print("爬取失败")
            else:
                print("添加 URL 失败")
        else:
            # 无命令时显示帮助
            parser.print_help()
            
    finally:
        # 关闭资源
        runner.close()


if __name__ == "__main__":
    main()


# 如果您确实需要一个MCP服务器版本，我可以为您创建一个兼容MCP协议的版本。MCP是一种让模型（如Claude）能够执行代码并获取结果的协议。
# 但是请注意，这个版本将需要一个MCP服务器来运行，并且需要一些额外的配置。
# 这个版本的代码是一个独立的命令行工具，可以直接在本地运行。
# 你可以使用这个版本来测试和调试你的代码。
# 你可以在终端中运行这个脚本，使用不同的命令来添加URL、爬取内容、分析内容和获取分析结果。
