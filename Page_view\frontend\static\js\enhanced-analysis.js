/**
 * 增强的分析功能，集成任务状态管理
 */

// 显示右上角弹窗通知
function showTopRightNotification(message, type = 'info', duration = 3000) {
    // 创建通知容器（如果不存在）
    let notificationContainer = document.getElementById('top-right-notifications');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'top-right-notifications';
        notificationContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 350px;
            pointer-events: none;
        `;
        document.body.appendChild(notificationContainer);
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.style.cssText = `
        background-color: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        padding: 12px 16px;
        margin-bottom: 10px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        font-size: 14px;
        pointer-events: auto;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease-in-out;
        word-wrap: break-word;
    `;
    
    // 添加图标
    const icon = type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ';
    notification.innerHTML = `
        <div style="display: flex; align-items: center;">
            <span style="margin-right: 8px; font-weight: bold;">${icon}</span>
            <span>${message}</span>
        </div>
    `;
    
    // 添加到容器
    notificationContainer.appendChild(notification);
    
    // 触发显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // 自动移除
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// 扩展原有的analyzeContent函数，添加任务状态支持
async function enhancedAnalyzeContent() {
    // 隐藏结果区域
    document.getElementById('resultsArea').style.display = 'none';
    document.getElementById('batchResultsArea').style.display = 'none';
    
    // 显示加载器
    document.getElementById('loader').style.display = 'block';
    document.getElementById('analyzeBtn').disabled = true;
    
    try {
        const inputType = document.getElementById('inputType').value;
        const analysisType = document.getElementById('analysisType').value;
        
        let result = null;
        let taskId = null;
        
        // ArchiveBox 分析处理
        if (inputType === 'archivebox') {
            result = await handleArchiveBoxAnalysis(analysisType);
            taskId = result?.task_id;
        } else {
            // 普通分析处理
            result = await handleNormalAnalysis(inputType, analysisType);
            taskId = result?.task_id;
        }
        
        // 如果返回了task_id，说明是异步任务
        if (taskId) {
            handleAsyncTask(taskId, result);
        } else {
            // 同步任务，直接显示结果
            if (result && result.success) {
                showTopRightNotification(' 分析完成! 结果已生成，可查看分析详情。', 'success', 4000);
                displayAnalysisResult(result);
            } else {
                showTopRightNotification('分析失败: ' + (result?.error || '未知错误'), 'error', 5000);
            }
        }
        
    } catch (error) {
        showTopRightNotification('分析过程出错: ' + error, 'error', 5000);
    } finally {
        // 隐藏加载器并启用按钮
        document.getElementById('loader').style.display = 'none';
        const analyzeBtn = document.getElementById('analyzeBtn');
        if (analyzeBtn) {
            analyzeBtn.disabled = false;
            analyzeBtn.innerHTML = '<i class="bi bi-lightning-charge"></i> 开始分析';
        }
    }
}

// 处理ArchiveBox分析
async function handleArchiveBoxAnalysis(analysisType) {
    const archiveboxInput = document.getElementById('archiveboxInput').value.trim();
    if (!archiveboxInput) {
        showTopRightNotification(' 请输入要分析的URL', 'warning', 3000);
        return null;
    }
    
    // 检查是否为多个URL
    const isBatch = archiveboxInput.includes('\n') || archiveboxInput.includes(';');
    
    // 解析URL
    let urls = [];
    if (isBatch) {
        urls = archiveboxInput
            .split(/[\n;]/)
            .map(url => url.trim())
            .filter(url => url);
    } else {
        urls = [archiveboxInput];
    }
    
    const finalIsBatch = urls.length > 1;
    
    // 构建请求数据
    const requestData = {
        analysis_type: analysisType,
        no_analyze: document.getElementById('noAnalyzeCheck').checked
    };
    
    if (finalIsBatch) {
        requestData.urls = urls;
    } else {
        requestData.url = archiveboxInput;
    }
    
    // 发送请求
    const response = await fetch('/api/analysis_archivebox', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    });
    
    const result = await response.json();
    
    // 处理结果显示
    if (result.success) {
        if (finalIsBatch) {
            // 批量处理结果
            const totalProcessed = result.stats?.total || urls.length;
            showTopRightNotification(`📦 批量处理完成! 已处理 ${totalProcessed} 个URL。`, 'success', 6000);
            displayBatchResults(result);
        } else {
            // 单个URL结果
            const noAnalyze = document.getElementById('noAnalyzeCheck').checked;
            if (noAnalyze && result.status === 'archived') {
                showTopRightNotification('📁 存档完成! URL已成功存储到ArchiveBox。', 'success', 4000);
                displayArchiveResult(result);
            } else {
                showTopRightNotification(' 分析完成! 结果已生成，可查看分析详情。', 'success', 4000);
                displayAnalysisResult(result);
            }
        }
    } else {
        showTopRightNotification('处理失败: ' + (result.error || '未知错误'), 'error', 5000);
    }
    
    return result;
}

// 处理普通分析
async function handleNormalAnalysis(inputType, analysisType) {
    let formData = new FormData();
    formData.append('analysis_type', analysisType);
    
    // 根据输入类型设置不同的请求参数
    if (inputType === 'file') {
        const fileInput = document.getElementById('fileInput');
        if (!fileInput.files || fileInput.files.length === 0) {
            showTopRightNotification(' 请选择要分析的文件', 'warning', 3000);
            return null;
        }
        formData.append('file', fileInput.files[0]);
    } else if (inputType === 'url') {
        const urlInput = document.getElementById('urlInput').value.trim();
        if (!urlInput) {
            showTopRightNotification(' 请输入要分析的URL', 'warning', 3000);
            return null;
        }
        formData.append('content', urlInput);
    } else if (inputType === 'text') {
        const textInput = document.getElementById('textInput').value.trim();
        if (!textInput) {
            showTopRightNotification(' 请输入要分析的文本内容', 'warning', 3000);
            return null;
        }
        formData.append('content', textInput);
    }
    
    // 发送请求到服务器
    const response = await fetch('/api/analysis', {
        method: 'POST',
        body: formData
    });
    
    const result = await response.json();
    return result;
}

// 处理异步任务
function handleAsyncTask(taskId, initialResult) {
    console.log(`开始处理异步任务 ${taskId}`);
    
    // 获取taskManager实例
    const taskManager = window.taskManager || window.globalTaskManager || (window.getTaskManager && window.getTaskManager());
    
    // 检查taskManager是否可用
    if (!taskManager) {
        console.error('taskManager未定义，等待初始化...');
        // 延迟处理，等待taskManager初始化
        setTimeout(() => {
            const retryTaskManager = window.taskManager || window.globalTaskManager || (window.getTaskManager && window.getTaskManager());
            if (retryTaskManager) {
                handleAsyncTask(taskId, initialResult);
            } else {
                console.error('taskManager初始化失败，使用备用方案');
                // 备用方案：直接显示简单提示
                showTopRightNotification(` 任务 ${taskId} 已启动，请等待处理完成...`, 'info', 4000);
            }
        }, 1000);
        return;
    }
    
    // 添加到本地存储
    taskManager.addTaskToStorage(taskId);
    
    // 显示任务开始提示
    const taskType = getTaskTypeFromContext();
    taskManager.showTaskStarted(taskId, taskType, initialResult.stats?.total || 1);
    
    // 开始轮询任务状态（仅后台轮询，不显示UI）
    taskManager.startPolling(taskId, 
        // 状态更新回调
        (task) => {
            console.log(`任务 ${taskId} 状态更新:`, task);
            // 不更新UI，仅记录状态
        },
        // 任务完成回调
        (task) => {
            console.log(`任务 ${taskId} 完成:`, task);
            taskManager.removeTaskFromStorage(taskId);
            
            // 显示完成通知
            if (task.task_status === 'completed') {
                showCompletionNotification(task);
                // 刷新相关任务状态
                if (taskManager.refreshRelatedTasks) {
                    taskManager.refreshRelatedTasks(task.task_type);
                }
                // 刷新任务历史以更新状态
                refreshTaskHistoryIfVisible();
            } else if (task.task_status === 'failed') {
                // 显示任务失败通知
                const errorMsg = `任务失败: ${task.error_message || '未知错误'}`;
                showTopRightNotification(errorMsg, 'error', 5000);
                // 刷新任务历史以更新状态
                refreshTaskHistoryIfVisible();
            }
        }
    );
}

// 根据当前上下文确定任务类型
function getTaskTypeFromContext() {
    const inputType = document.getElementById('inputType')?.value;
    
    if (inputType === 'archivebox') {
        const archiveboxInput = document.getElementById('archiveboxInput')?.value || '';
        const isBatch = archiveboxInput.includes('\n') || archiveboxInput.includes(';');
        return isBatch ? 'archivebox_analysis' : 'archivebox_analysis';
    } else {
        return 'single_analysis';
    }
}

// 显示完成通知
function showCompletionNotification(task) {
    const successCount = task.success_count || 0;
    const errorCount = task.error_count || 0;
    const totalCount = task.total_count || 0;
    
    // 使用增强的右上角通知
    const taskTypeName = window.taskManager ? window.taskManager.getTaskTypeText(task.task_type) : task.task_type;
    
    if (totalCount > 1) {
        showTopRightNotification(` ${taskTypeName}完成！成功处理${successCount}项${errorCount > 0 ? `，失败${errorCount}项` : ''}`, 'success', 8000);
    } else {
        showTopRightNotification(` ${taskTypeName}完成！${successCount > 0 ? '分析成功' : '处理完成'}`, 'success', 5000);
    }
    
    // 如果有成功的结果，可以显示查看按钮
    if (successCount > 0 && task.results) {
        console.log('任务结果:', task.results);
    }
}

// 辅助函数：将分析类型代码转换为中文描述
function getAnalysisTypeText(analysisType) {
    const typeMap = {
        'all': '全面分析',
        'summary': '摘要',
        'threats': '威胁',
        'vulns': '漏洞',
        'iocs': 'IOCs'
    };
    return typeMap[analysisType] || analysisType;
}

// 批量分析功能
async function startBatchAnalysis() {
    const batchSize = parseInt(document.getElementById('batchSize').value) || 5;
    const analysisType = document.getElementById('batchAnalysisType').value || 'all';
    
    // 禁用按钮并显示开始提示
    const button = document.getElementById('startBatchAnalysisBtn');
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> 启动中...';
    }
    
    // 显示开始提示
    showTopRightNotification(` 开始批量分析数据库记录 (批量大小: ${batchSize}, 分析类型: ${getAnalysisTypeText(analysisType)})，可从任务历史中查看进度`, 'info', 5000);
    
    try {
        const response = await fetch('/api/analysis_batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                batch_size: batchSize,
                analysis_type: analysisType,
                input_type: 'database'
            })
        });
        
        const result = await response.json();
        
        if (result.success && result.task_id) {
            // 异步任务
            showTopRightNotification(` 批量分析任务已启动 (任务ID: ${result.task_id})，可从任务历史中查看详细进度`, 'success', 6000);
            handleAsyncTask(result.task_id, result);
        } else if (result.success) {
            // 同步任务或无任务
            showTopRightNotification(result.message || '批量分析完成', 'success', 4000);
        } else {
            showTopRightNotification('批量分析失败: ' + (result.error || '未知错误'), 'error', 6000);
        }
        
    } catch (error) {
        showTopRightNotification('批量分析过程出错: ' + error, 'error', 6000);
    } finally {
        // 恢复按钮状态
        if (button) {
            button.disabled = false;
            button.innerHTML = '<i class="bi bi-play-circle"></i> 开始批量分析';
        }
    }
}

// 批量分析未分析快照
async function batchAnalyzeUnanalyzed() {
    const limit = parseInt(document.getElementById('unanalyzedLimit').value) || 50;
    const analysisType = document.getElementById('unanalyzedAnalysisType').value || 'all';
    
    // 禁用按钮并显示开始提示
    const button = document.getElementById('analyzeBatchUnanalyzedBtn');
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> 启动中...';
    }
    
    // 显示开始提示
    showTopRightNotification(`📦 开始批量分析未分析快照 (处理数量限制: ${limit}, 分析类型: ${getAnalysisTypeText(analysisType)})，可从任务历史中查看进度`, 'info', 5000);
    
    try {
        const response = await fetch('/api/batch_analyze_unanalyzed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                limit: limit,
                analysis_type: analysisType
            })
        });
        
        const result = await response.json();
        
        if (result.success && result.task_id) {
            // 异步任务
            showTopRightNotification(` 批量分析快照任务已启动 (任务ID: ${result.task_id})，可从任务历史中查看详细进度`, 'success', 6000);
            handleAsyncTask(result.task_id, result);
        } else if (result.success) {
            // 同步任务或无任务（无待分析快照）
            if (result.message && result.message.includes('没有找到')) {
                showTopRightNotification(`ℹ️ ${result.message}`, 'info', 4000);
            } else {
                showTopRightNotification(result.message || '批量分析快照完成', 'success', 4000);
            }
        } else {
            showTopRightNotification('批量分析快照失败: ' + (result.error || '未知错误'), 'error', 6000);
        }
        
    } catch (error) {
        showTopRightNotification('批量分析快照过程出错: ' + error, 'error', 6000);
    } finally {
        // 恢复按钮状态
        if (button) {
            button.disabled = false;
            button.innerHTML = '<i class="bi bi-archive"></i> 分析快照';
        }
    }
}

// 单个记录分析
async function analyzeSingleRecord(crawledDataId) {
    try {
        const response = await fetch(`/api/analysis_content/${crawledDataId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success && result.task_id) {
            // 异步任务
            handleAsyncTask(result.task_id, result);
        } else if (result.success) {
            // 同步任务
            showTopRightNotification('单个记录分析完成', 'success', 4000);
            if (result.analysis_content) {
                displayAnalysisResult(result);
            }
        } else {
            showTopRightNotification('单个记录分析失败: ' + (result.error || '未知错误'), 'error', 5000);
        }
        
    } catch (error) {
        showTopRightNotification('单个记录分析过程出错: ' + error, 'error', 5000);
    }
}

// 任务历史缓存
let taskHistoryCache = null;
let taskHistoryCacheTime = 0;
const taskHistoryCacheTTL = 30000; // 30秒缓存

// 清理任务历史缓存
function clearTaskHistoryCache() {
    taskHistoryCache = null;
    taskHistoryCacheTime = 0;
    console.log('已清理任务历史缓存');
}

// 简化的全局函数定义（确保在全局作用域中）
function showTaskHistory() {
    console.log('showTaskHistory函数被调用');
    if (window.showTaskHistory && window.showTaskHistory !== showTaskHistory) {
        return window.showTaskHistory();
    } else {
        return showTaskHistoryImpl();
    }
}

// 实际的实现函数
async function showTaskHistoryImpl() {
    try {
        console.log('showTaskHistoryImpl开始执行');
        let data;
        const currentTime = Date.now();

        // 检查缓存
        if (taskHistoryCache && (currentTime - taskHistoryCacheTime) < taskHistoryCacheTTL) {
            console.log('使用缓存的任务历史数据');
            data = { success: true, data: taskHistoryCache };
        } else {
            // 直接使用API获取任务历史，不依赖taskManager
            console.log('从API获取任务历史数据');
            const response = await fetch('/api/analysis/tasks?limit=20');

            data = await response.json();
            console.log('API返回数据:', data);

            if (!data.success) {
                throw new Error(data.error || '获取任务历史失败');
            }

            // 更新缓存
            taskHistoryCache = data.data;
            taskHistoryCacheTime = currentTime;
            console.log('更新任务历史缓存，任务数量:', data.data.length);
        }

        if (!data.data || data.data.length === 0) {
            showTopRightNotification('暂无任务历史记录', 'info', 3000);
            return;
        }

        console.log('创建任务历史模态框，任务数量:', data.data.length);
        // 创建增强的任务历史模态框
        const modal = createEnhancedTaskHistoryModal(data.data);
        document.body.appendChild(modal);

        // 显示模态框
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // 模态框关闭后清理
        modal.addEventListener('hidden.bs.modal', () => {
            stopTaskHistoryAutoRefresh();
            modal.remove();
        });

        // 启动任务历史自动刷新
        startTaskHistoryAutoRefresh();

    } catch (error) {
        console.error('获取任务历史失败:', error);
        // 使用右上角通知而不是弹窗
        showTopRightNotification('获取任务历史失败: ' + error.message, 'error', 5000);
    }
}

// 刷新当前任务历史模态框（分析页面专用）
async function refreshCurrentTaskHistoryAnalysis() {
    try {
        console.log('刷新当前任务历史数据（分析页面）');

        // 清理缓存，强制重新获取数据
        clearTaskHistoryCache();

        // 获取最新数据
        const response = await fetch('/api/analysis/tasks?limit=20');
        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || '获取任务历史失败');
        }

        // 更新缓存
        taskHistoryCache = data.data;
        taskHistoryCacheTime = Date.now();

        // 更新当前模态框的内容
        const currentModal = document.querySelector('#enhancedTaskHistoryModal');
        if (currentModal) {
            // 更新统计卡片
            const stats = {
                total: data.data.length,
                running: data.data.filter(t => t.task_status === 'running').length,
                pending: data.data.filter(t => t.task_status === 'pending').length,
                completed: data.data.filter(t => t.task_status === 'completed').length,
                failed: data.data.filter(t => t.task_status === 'failed').length
            };

            // 更新统计数字
            const statCards = currentModal.querySelectorAll('.card-body .fw-bold');
            if (statCards.length >= 5) {
                statCards[0].textContent = stats.total;
                statCards[1].textContent = stats.running;
                statCards[2].textContent = stats.pending;
                statCards[3].textContent = stats.completed;
                statCards[4].textContent = stats.failed;
            }

            // 更新表格内容
            const tbody = currentModal.querySelector('#taskTableBody');
            if (tbody) {
                tbody.innerHTML = createTaskRows(data.data);
            }

            // 更新标题中的任务数量
            const titleElement = currentModal.querySelector('.modal-title + small');
            if (titleElement) {
                titleElement.textContent = `共 ${stats.total} 个任务`;
            }

            console.log(`任务历史已刷新，共 ${data.data.length} 条记录`);
            showTopRightNotification('任务历史已刷新', 'success', 2000);
        }

    } catch (error) {
        console.error('刷新任务历史失败:', error);
        showTopRightNotification('刷新失败: ' + error.message, 'error', 3000);
    }
}



// 创建任务历史模态框
function createTaskHistoryModal(tasks) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">任务历史</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>进度</th>
                                    <th>开始时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${tasks.map(task => `
                                    <tr>
                                        <td>${task.id}</td>
                                        <td>${taskManager.getTaskTypeText(task.task_type)}</td>
                                        <td>
                                            <span class="badge bg-${taskManager.getStatusClass(task.task_status)}">
                                                ${taskManager.getStatusText(task.task_status)}
                                            </span>
                                        </td>
                                        <td>
                                            ${task.completed_count || 0} / ${task.total_count || 0}
                                            ${task.progress ? `(${Math.round(task.progress)}%)` : ''}
                                        </td>
                                        <td>${new Date(task.start_time).toLocaleString()}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    onclick="viewTaskDetails(${task.id})">
                                                详情
                                            </button>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return modal;
}

// 创建增强的任务历史模态框
function createEnhancedTaskHistoryModal(tasks) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.id = 'enhancedTaskHistoryModal';

    // 按状态分组统计
    const stats = {
        total: tasks.length,
        running: tasks.filter(t => t.task_status === 'running').length,
        pending: tasks.filter(t => t.task_status === 'pending').length,
        completed: tasks.filter(t => t.task_status === 'completed').length,
        failed: tasks.filter(t => t.task_status === 'failed').length
    };

    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-light">
                    <div>
                        <h5 class="modal-title mb-1">
                            <i class="bi bi-clock-history text-primary"></i> 任务历史
                        </h5>
                        <small class="text-muted">共 ${stats.total} 个任务</small>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <!-- 统计卡片 -->
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-primary fw-bold">${stats.total}</div>
                                    <small class="text-muted">总计</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-info fw-bold">${stats.running}</div>
                                    <small class="text-muted">运行中</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-warning fw-bold">${stats.pending}</div>
                                    <small class="text-muted">等待中</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-success fw-bold">${stats.completed}</div>
                                    <small class="text-muted">已完成</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <div class="text-danger fw-bold">${stats.failed}</div>
                                    <small class="text-muted">失败</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-6 mb-2">
                            <div class="card text-center h-100">
                                <div class="card-body py-2">
                                    <button class="btn btn-sm btn-outline-primary w-100" onclick="refreshCurrentTaskHistoryAnalysis();">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选器 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select form-select-sm" id="statusFilter" onchange="filterTasks()">
                                <option value="">所有状态</option>
                                <option value="running">运行中</option>
                                <option value="pending">等待中</option>
                                <option value="completed">已完成</option>
                                <option value="failed">失败</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select form-select-sm" id="typeFilter" onchange="filterTasks()">
                                <option value="">所有类型</option>
                                <option value="batch_analysis">批量分析</option>
                                <option value="single_analysis">单个分析</option>
                                <option value="weekly_crawl">每周爬取</option>
                                <option value="batch_unanalyzed">批量未分析</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control form-control-sm" id="searchInput"
                                   placeholder="搜索任务ID或类型..." onkeyup="filterTasks()">
                        </div>
                    </div>

                    <!-- 任务表格 -->
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-hover table-sm">
                            <thead class="table-light sticky-top">
                                <tr>
                                    <th style="width: 60px;">ID</th>
                                    <th style="width: 120px;">类型</th>
                                    <th style="width: 100px;">状态</th>
                                    <th style="width: 150px;">进度</th>
                                    <th style="width: 80px;">总数</th>
                                    <th style="width: 80px;">成功</th>
                                    <th style="width: 80px;">失败</th>
                                    <th style="width: 140px;">开始时间</th>
                                    <th style="width: 140px;">结束时间</th>
                                    <th style="width: 100px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="taskTableBody">
                                ${createTaskRows(tasks)}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="modal-footer bg-light">
                    <small class="text-muted me-auto">
                        <i class="bi bi-info-circle"></i> 数据每30秒自动刷新
                    </small>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;
    return modal;
}

// 创建任务行
function createTaskRows(tasks) {
    return tasks.map(task => `
        <tr class="task-row" data-status="${task.task_status}" data-type="${task.task_type}" data-id="${task.id}">
            <td><span class="badge bg-light text-dark">${task.id}</span></td>
            <td><small>${getTaskTypeText(task.task_type)}</small></td>
            <td>${getTaskStatusBadge(task.task_status)}</td>
            <td>${getEnhancedProgressBar(task)}</td>
            <td><small class="text-muted">${task.total_count || 0}</small></td>
            <td><small class="text-success">${task.success_count || 0}</small></td>
            <td><small class="text-danger">${task.error_count || 0}</small></td>
            <td><small class="text-muted">${formatDateTime(task.start_time)}</small></td>
            <td><small class="text-muted">${task.end_time ? formatDateTime(task.end_time) : '-'}</small></td>
            <td>
                <button class="btn btn-sm btn-outline-info" onclick="showEnhancedTaskDetails(${task.id})" title="查看详情">
                    <i class="bi bi-eye"></i>
                </button>
                ${task.task_status === 'running' || task.task_status === 'pending' ? `
                <button class="btn btn-sm btn-outline-danger ms-1" onclick="stopTask(${task.id})" title="停止任务">
                    <i class="bi bi-stop"></i>
                </button>
                ` : ''}
            </td>
        </tr>
    `).join('');
}

// 获取任务类型文本
function getTaskTypeText(taskType) {
    const typeMap = {
        'batch_analysis': '批量分析',
        'single_analysis': '单个分析',
        'weekly_crawl': '每周爬取',
        'batch_unanalyzed': '批量未分析',
        'archivebox_analysis': 'ArchiveBox分析',
        'archivebox_archive_only': 'ArchiveBox归档',
        'test_task': '测试任务',
        'follow_up_analysis': '跟进分析'
    };
    return typeMap[taskType] || taskType;
}

// 获取任务状态徽章
function getTaskStatusBadge(status) {
    const statusMap = {
        'pending': '<span class="badge bg-warning">等待中</span>',
        'running': '<span class="badge bg-primary">运行中</span>',
        'completed': '<span class="badge bg-success">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>',
        'cancelled': '<span class="badge bg-secondary">已取消</span>'
    };
    return statusMap[status] || `<span class="badge bg-light text-dark">${status}</span>`;
}

// 获取增强的进度条
function getEnhancedProgressBar(task) {
    const progress = task.progress || 0;
    const completed = task.completed_count || 0;
    const total = task.total_count || 0;

    let progressClass = 'bg-primary';
    if (task.task_status === 'completed') progressClass = 'bg-success';
    else if (task.task_status === 'failed') progressClass = 'bg-danger';
    else if (task.task_status === 'pending') progressClass = 'bg-warning';

    return `
        <div class="progress" style="height: 20px;">
            <div class="progress-bar ${progressClass}" role="progressbar"
                 style="width: ${progress}%" aria-valuenow="${progress}"
                 aria-valuemin="0" aria-valuemax="100">
                <small>${completed}/${total}</small>
            </div>
        </div>
    `;
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 筛选任务
function filterTasks() {
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const typeFilter = document.getElementById('typeFilter')?.value || '';
    const searchInput = document.getElementById('searchInput')?.value.toLowerCase() || '';

    const rows = document.querySelectorAll('.task-row');

    rows.forEach(row => {
        const status = row.dataset.status;
        const type = row.dataset.type;
        const id = row.dataset.id;
        const text = row.textContent.toLowerCase();

        const statusMatch = !statusFilter || status === statusFilter;
        const typeMatch = !typeFilter || type === typeFilter;
        const searchMatch = !searchInput || text.includes(searchInput) || id.includes(searchInput);

        if (statusMatch && typeMatch && searchMatch) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 显示增强的任务详情
async function showEnhancedTaskDetails(taskId) {
    try {
        // 先关闭任务历史模态框
        const taskHistoryModal = document.getElementById('enhancedTaskHistoryModal');
        if (taskHistoryModal) {
            const historyBootstrapModal = bootstrap.Modal.getInstance(taskHistoryModal);
            if (historyBootstrapModal) {
                historyBootstrapModal.hide();
            }
        }

        // 获取任务详情
        const response = await fetch(`/api/analysis/tasks/${taskId}`);

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || '获取任务详情失败');
        }

        const task = data.data;

        // 等待任务历史模态框完全关闭后再显示详情模态框
        setTimeout(() => {
            // 创建增强的任务详情模态框
            const modal = createEnhancedTaskDetailsModal(task);
            document.body.appendChild(modal);

            // 显示模态框
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // 模态框关闭时移除DOM元素
            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }, 300); // 等待300ms确保前一个模态框完全关闭

    } catch (error) {
        console.error('获取任务详情失败:', error);
        showTopRightNotification('获取任务详情失败: ' + error.message, 'error');
    }
}

// 创建增强的任务详情模态框
function createEnhancedTaskDetailsModal(task) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.id = 'enhancedTaskDetailsModal';

    // 计算运行时间
    const startTime = new Date(task.start_time);
    const endTime = task.end_time ? new Date(task.end_time) : new Date();
    const duration = Math.round((endTime - startTime) / 1000); // 秒

    // 计算成功率
    const successRate = task.total_count > 0 ?
        Math.round((task.success_count / task.total_count) * 100) : 0;

    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-light">
                    <div>
                        <h5 class="modal-title mb-1">
                            <i class="bi bi-info-circle text-primary"></i> 任务详情
                        </h5>
                        <small class="text-muted">任务ID: ${task.id}</small>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <!-- 基本信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><i class="bi bi-info-circle"></i> 基本信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="text-muted" style="width: 80px;">任务ID:</td>
                                            <td><span class="badge bg-light text-dark">${task.id}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">类型:</td>
                                            <td>${getTaskTypeText(task.task_type)}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">状态:</td>
                                            <td>${getTaskStatusBadge(task.task_status)}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">创建者:</td>
                                            <td>${task.created_by || 'system'}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">运行时间:</td>
                                            <td>${formatDuration(duration)}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><i class="bi bi-bar-chart"></i> 执行统计</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="text-muted" style="width: 80px;">总数:</td>
                                            <td><span class="badge bg-primary">${task.total_count || 0}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">已完成:</td>
                                            <td><span class="badge bg-info">${task.completed_count || 0}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">成功:</td>
                                            <td><span class="badge bg-success">${task.success_count || 0}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">失败:</td>
                                            <td><span class="badge bg-danger">${task.error_count || 0}</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">成功率:</td>
                                            <td><span class="badge bg-${successRate >= 80 ? 'success' : successRate >= 50 ? 'warning' : 'danger'}">${successRate}%</span></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 进度条 -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="bi bi-speedometer2"></i> 执行进度</h6>
                            </div>
                            <div class="card-body">
                                ${getDetailedProgressBar(task)}
                            </div>
                        </div>
                    </div>

                    <!-- 时间信息 -->
                    <div class="mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="bi bi-clock"></i> 时间信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">开始时间:</small><br>
                                        <span>${formatDateTime(task.start_time)}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">结束时间:</small><br>
                                        <span>${task.end_time ? formatDateTime(task.end_time) : '运行中...'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer bg-light">
                    ${task.task_status === 'running' || task.task_status === 'pending' ? `
                    <button type="button" class="btn btn-danger" onclick="stopTask(${task.id})">
                        <i class="bi bi-stop"></i> 停止任务
                    </button>
                    ` : ''}
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    return modal;
}

// 获取详细进度条
function getDetailedProgressBar(task) {
    const progress = task.progress || 0;
    const completed = task.completed_count || 0;
    const total = task.total_count || 0;
    const success = task.success_count || 0;
    const error = task.error_count || 0;

    let progressClass = 'bg-primary';
    if (task.task_status === 'completed') progressClass = 'bg-success';
    else if (task.task_status === 'failed') progressClass = 'bg-danger';
    else if (task.task_status === 'pending') progressClass = 'bg-warning';

    return `
        <div class="mb-2">
            <div class="d-flex justify-content-between align-items-center mb-1">
                <small class="text-muted">总体进度</small>
                <small class="text-muted">${completed}/${total} (${Math.round(progress)}%)</small>
            </div>
            <div class="progress" style="height: 25px;">
                <div class="progress-bar ${progressClass}" role="progressbar"
                     style="width: ${progress}%" aria-valuenow="${progress}"
                     aria-valuemin="0" aria-valuemax="100">
                    ${Math.round(progress)}%
                </div>
            </div>
        </div>

        ${total > 0 ? `
        <div class="row mt-3">
            <div class="col-6">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <small class="text-success">成功</small>
                    <small class="text-success">${success}/${total}</small>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar bg-success" style="width: ${total > 0 ? (success/total)*100 : 0}%"></div>
                </div>
            </div>
            <div class="col-6">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <small class="text-danger">失败</small>
                    <small class="text-danger">${error}/${total}</small>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar bg-danger" style="width: ${total > 0 ? (error/total)*100 : 0}%"></div>
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

// 格式化持续时间
function formatDuration(seconds) {
    if (seconds < 60) {
        return `${seconds}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小时${minutes}分`;
    }
}

// 停止任务
async function stopTask(taskId) {
    if (!confirm('确定要停止这个任务吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/analysis/tasks/${taskId}/stop`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            showTopRightNotification('任务已停止', 'success');

            // 清理缓存并刷新
            clearTaskHistoryCache();

            // 关闭详情模态框
            const detailsModal = document.getElementById('enhancedTaskDetailsModal');
            if (detailsModal) {
                const bootstrapModal = bootstrap.Modal.getInstance(detailsModal);
                if (bootstrapModal) {
                    bootstrapModal.hide();
                }
            }

            // 刷新任务历史
            setTimeout(() => {
                showTaskHistory();
            }, 1000);

        } else {
            throw new Error(data.error || '停止任务失败');
        }

    } catch (error) {
        console.error('停止任务失败:', error);
        showTopRightNotification('停止任务失败: ' + error.message, 'error');
    }
}

// 查看任务详情（保持兼容性）
async function viewTaskDetails(taskId) {
    return showEnhancedTaskDetails(taskId);
}

// 创建任务详情模态框
function createTaskDetailsModal(task) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">任务详情 - ${task.id}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>任务ID:</strong></td><td>${task.id}</td></tr>
                                <tr><td><strong>类型:</strong></td><td>${taskManager.getTaskTypeText(task.task_type)}</td></tr>
                                <tr>
                                    <td><strong>状态:</strong></td>
                                    <td>
                                        <span class="badge bg-${taskManager.getStatusClass(task.task_status)}">
                                            ${taskManager.getStatusText(task.task_status)}
                                        </span>
                                    </td>
                                </tr>
                                <tr><td><strong>创建者:</strong></td><td>${task.created_by}</td></tr>
                                ${task.parent_task_id ? `<tr><td><strong>父任务:</strong></td><td><a href="javascript:void(0)" onclick="viewTaskDetails(${task.parent_task_id})" class="text-primary">#${task.parent_task_id}</a></td></tr>` : ''}
                                ${task.child_task_ids && task.child_task_ids.length > 0 ? `<tr><td><strong>子任务:</strong></td><td>${task.child_task_ids.map(id => `<a href="javascript:void(0)" onclick="viewTaskDetails(${id})" class="text-primary me-1">#${id}</a>`).join('')}</td></tr>` : ''}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>执行情况</h6>
                            <table class="table table-sm">
                                <tr><td><strong>总数:</strong></td><td>${task.total_count || 0}</td></tr>
                                <tr><td><strong>已完成:</strong></td><td>${task.completed_count || 0}</td></tr>
                                <tr><td><strong>成功:</strong></td><td>${task.success_count || 0}</td></tr>
                                <tr><td><strong>失败:</strong></td><td>${task.error_count || 0}</td></tr>
                                <tr><td><strong>进度:</strong></td><td>${Math.round(task.progress || 0)}%</td></tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>时间信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>开始时间:</strong></td>
                                    <td>${new Date(task.start_time).toLocaleString()}</td>
                                </tr>
                                ${task.end_time ? `
                                    <tr>
                                        <td><strong>结束时间:</strong></td>
                                        <td>${new Date(task.end_time).toLocaleString()}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>执行时长:</strong></td>
                                        <td>${Math.round((new Date(task.end_time) - new Date(task.start_time)) / 1000)} 秒</td>
                                    </tr>
                                ` : ''}
                            </table>
                        </div>
                    </div>
                    
                    ${task.error_message ? `
                        <div class="row mt-3">

                            <div class="col-12">
                                <h6>错误信息</h6>
                                <div class="alert alert-danger">${task.error_message}</div>
                            </div>
                        </div>
                    ` : ''}
                    
                    ${task.results && task.results.length > 0 ? `
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>执行结果 (前10项)</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>项目</th>
                                                <th>状态</th>
                                                <th>详情</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${task.results.slice(0, 10).map((result, index) => `
                                                <tr>
                                                    <td>${index + 1}</td>
                                                    <td>
                                                        <span class="badge ${result.status === 'success' || result.status === 'archived' ? 'bg-success' : (result.status === 'analyzed' || (result.analysis_id && result.status !== 'error')) ? 'bg-info' : 'bg-danger'}">
                                                            ${result.status === 'archived' ? '已成功存档，未进行分析' : 
                                                              result.status === 'analyzed' ? '已分析' : 
                                                              result.status === 'success' ? (result.analysis_id ? '已分析' : (task.task_type === 'archivebox_archive_only' ? '已存档，未分析' : '成功')) : 
                                                              result.status}
                                                        </span>
                                                    </td>
                                                    <td>
                                        ${(() => {
                                            // 获取显示内容和链接信息
                                            const url = result.url || result.source_url;
                                            const snapshotId = result.snapshot_id || result.id;
                                            const title = result.title || result.reason || result.message;
                                            
                                            // 生成显示标题：优先使用实际标题，否则使用截断的URL
                                            let displayTitle;
                                            if (title && title !== url && title !== result.id && !title.match(/^\d+$/)) {
                                                // 有真实标题且不是URL或数字ID
                                                displayTitle = title.length > 60 ? title.substring(0, 60) + '...' : title;
                                            } else if (url) {
                                                // 使用URL作为标题
                                                displayTitle = url.length > 60 ? url.substring(0, 60) + '...' : url;
                                            } else {
                                                // 备用显示
                                                displayTitle = result.reason || result.message || result.error || '未知项目';
                                            }
                                            
                                            // 根据任务类型和结果状态决定点击行为
                                            if (task.task_type === 'batch_analysis') {
                                                // 批量分析数据库记录：点击跳转到原URL
                                                if (url) {
                                                    return `
                                                        <a href="${url}" target="_blank" class="text-decoration-none" title="点击查看原文链接: ${url}">
                                                            <i class="bi bi-link-45deg me-1"></i>${displayTitle}
                                                        </a>
                                                    `;
                                                } else {
                                                    return `<span class="text-muted">${displayTitle}</span>`;
                                                }
                                            } else if (task.task_type === 'batch_unanalyzed' || task.task_type === 'archivebox_analysis' || task.task_type === 'archivebox_archive_only') {
                                                // 快照相关任务：点击查看快照
                                                if (snapshotId) {
                                                    return `
                                                        <a href="javascript:void(0)" onclick="viewSnapshot('${snapshotId}')" class="text-primary text-decoration-none" title="点击查看快照存档: ${url}">
                                                            <i class="bi bi-archive me-1"></i>${displayTitle}
                                                        </a>
                                                    `;
                                                } else if (url) {
                                                    return `
                                                        <a href="${url}" target="_blank" class="text-decoration-none" title="点击查看原文链接: ${url}">
                                                            <i class="bi bi-link-45deg me-1"></i>${displayTitle}
                                                        </a>
                                                    `;
                                                } else {
                                                    return `<span class="text-muted">${displayTitle}</span>`;
                                                }
                                            } else {
                                                // 其他任务类型：默认行为
                                                if (url) {
                                                    return `
                                                        <a href="${url}" target="_blank" class="text-decoration-none" title="点击查看链接: ${url}">
                                                            <i class="bi bi-link-45deg me-1"></i>${displayTitle}
                                                        </a>
                                                    `;
                                                } else {
                                                    return `<span class="text-muted">${displayTitle}</span>`;
                                                }
                                            }
                                        })()}
                                    </td>
                                    <td>
                                        ${(() => {
                                            // 根据任务类型和结果状态显示合适的操作按钮
                                            const snapshotId = result.snapshot_id || result.id;
                                            const analysisId = result.analysis_id;
                                            const url = result.url || result.source_url;
                                            
                                            if (task.task_type === 'batch_analysis' || task.task_type === 'single_analysis') {
                                                // 批量分析数据库记录：显示分析结果按钮组
                                                if ((result.status === 'analyzed' || result.status === 'success') && analysisId) {
                                                    return `
                                                        <button class="btn btn-sm btn-outline-secondary" onclick="openAnalysisResultInNewTab('${analysisId}')" title="在新标签页中打开分析结果">
                                                            <i class="bi bi-box-arrow-up-right"></i> 查看分析
                                                        </button>
                                                    `;
                                                } else {
                                                    return '<span class="text-muted">无操作</span>';
                                                }
                                            } else if (task.task_type === 'batch_unanalyzed' || 
                                                      task.task_type === 'archivebox_analysis' || 
                                                      task.task_type === 'archivebox_archive_only' || 
                                                      task.task_type === 'follow_up_analysis') {
                                                // 快照相关任务：显示查看快照和分析结果按钮
                                                if (result.status === 'analyzed') {
                                                    // 已分析状态：只显示查看分析按钮
                                                    return `
                                                        <div class="btn-group" role="group">
                                                            ${analysisId ? `
                                                                <button class="btn btn-sm btn-outline-secondary" onclick="openAnalysisResultInNewTab('${analysisId}')" title="在新标签页中打开分析结果">
                                                                    <i class="bi bi-box-arrow-up-right"></i> 查看分析
                                                                </button>
                                                            ` : ''}
                                                        </div>
                                                    `;
                                                } else if (result.status === 'success' && analysisId) {
                                                    // 成功状态且有分析ID：显示查看分析按钮
                                                    return `
                                                        <div class="btn-group" role="group">
                                                            ${analysisId ? `
                                                                <button class="btn btn-sm btn-outline-secondary" onclick="openAnalysisResultInNewTab('${analysisId}')" title="在新标签页中打开分析结果">
                                                                    <i class="bi bi-box-arrow-up-right"></i> 查看分析
                                                                </button>
                                                            ` : ''}
                                                        </div>
                                                    `;
                                                } else if ((result.status === 'archived' || 
                                                           result.status === 'success' || 
                                                           (task.task_type === 'archivebox_archive_only' && !analysisId)) && 
                                                          snapshotId) {
                                                    return `
                                                        <button class="btn btn-sm btn-outline-success" onclick="analyzeArchivedContent('${snapshotId}', '${url}', '${task.id}')" title="分析快照内容">
                                                            <i class="bi bi-lightning-charge"></i> 分析
                                                        </button>
                                                    `;
                                                } else if (snapshotId) {
                                                    return `
                                                        <button class="btn btn-sm btn-outline-primary" onclick="viewSnapshot('${snapshotId}')" title="查看快照">
                                                            <i class="bi bi-eye"></i> 查看
                                                        </button>
                                                    `;
                                                } else {
                                                    return '<span class="text-muted">无操作</span>';
                                                }
                                            } else {
                                                // 其他任务类型：默认逻辑
                                                if (analysisId) {
                                                    return `
                                                            <button class="btn btn-sm btn-outline-secondary" onclick="openAnalysisResultInNewTab('${analysisId}')" title="在新标签页中打开分析结果">
                                                                <i class="bi bi-box-arrow-up-right"></i> 查看分析
                                                            </button>
                                                        </div>
                                                    `;
                                                } else {
                                                    return '<span class="text-muted">无操作</span>';
                                                }
                                            }
                                        })()}
                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                                ${task.results.length > 10 ? `<small class="text-muted">显示前10项，共${task.results.length}项</small>` : ''}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
    
    return modal;
}

// 创建任务详情模态框内容（用于刷新显示）
function createTaskDetailsModalContent(task) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td><strong>任务ID:</strong></td><td>${task.id}</td></tr>
                    <tr><td><strong>类型:</strong></td><td>${taskManager.getTaskTypeText(task.task_type)}</td></tr>
                    <tr>
                        <td><strong>状态:</strong></td>
                        <td>
                            <span class="badge bg-${taskManager.getStatusClass(task.task_status)}">
                                ${taskManager.getStatusText(task.task_status)}
                            </span>
                        </td>
                    </tr>
                    <tr><td><strong>创建者:</strong></td><td>${task.created_by}</td></tr>
                    ${task.parent_task_id ? `<tr><td><strong>父任务:</strong></td><td><a href="javascript:void(0)" onclick="viewTaskDetails(${task.parent_task_id})" class="text-primary">#${task.parent_task_id}</a></td></tr>` : ''}
                    ${task.child_task_ids && task.child_task_ids.length > 0 ? `<tr><td><strong>子任务:</strong></td><td>${task.child_task_ids.map(id => `<a href="javascript:void(0)" onclick="viewTaskDetails(${id})" class="text-primary me-1">#${id}</a>`).join('')}</td></tr>` : ''}
                </table>
            </div>
            <div class="col-md-6">
                <h6>执行情况</h6>
                <table class="table table-sm">
                    <tr><td><strong>总数:</strong></td><td>${task.total_count || 0}</td></tr>
                    <tr><td><strong>已完成:</strong></td><td>${task.completed_count || 0}</td></tr>
                    <tr><td><strong>成功:</strong></td><td>${task.success_count || 0}</td></tr>
                    <tr><td><strong>失败:</strong></td><td>${task.error_count || 0}</td></tr>
                    <tr><td><strong>进度:</strong></td><td>${Math.round(task.progress || 0)}%</td></tr>
                </table>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>开始时间:</strong></td>
                        <td>${new Date(task.start_time).toLocaleString()}</td>
                    </tr>
                    ${task.end_time ? `
                        <tr>
                            <td><strong>结束时间:</strong></td>
                            <td>${new Date(task.end_time).toLocaleString()}</td>
                        </tr>
                        <tr>
                            <td><strong>执行时长:</strong></td>
                            <td>${Math.round((new Date(task.end_time) - new Date(task.start_time)) / 1000)} 秒</td>
                        </tr>
                    ` : ''}
                </table>
            </div>
        </div>
        
        ${task.error_message ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>错误信息</h6>
                    <div class="alert alert-danger">${task.error_message}</div>
                </div>
            </div>
        ` : ''}
        
        ${task.results && task.results.length > 0 ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>执行结果 (前10项)</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>项目</th>
                                    <th>状态</th>
                                    <th>详情</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${task.results.slice(0, 10).map((result, index) => `
                                    <tr>
                                        <td>${index + 1}</td>
                                        <td>
                                            <span class="badge ${result.status === 'success' || result.status === 'archived' ? 'bg-success' : (result.status === 'analyzed' || (result.analysis_id && result.status !== 'error')) ? 'bg-info' : 'bg-danger'}">
                                                ${result.status === 'archived' ? '已成功存档，未进行分析' : 
                                                  result.status === 'analyzed' ? '已分析' : 
                                                  result.status === 'success' ? (result.analysis_id ? '已分析' : (task.task_type === 'archivebox_archive_only' ? '已存档，未分析' : '成功')) : 
                                                  result.status}
                                            </span>
                                        </td>
                                        <td>
                            ${(() => {
                                // 获取显示内容和链接信息
                                const url = result.url || result.source_url;
                                const snapshotId = result.snapshot_id || result.id;
                                const title = result.title || result.reason || result.message;
                                
                                // 生成显示标题：优先使用实际标题，否则使用截断的URL
                                let displayTitle;
                                if (title && title !== url && title !== result.id && !title.match(/^\d+$/)) {
                                    // 有真实标题且不是URL或数字ID
                                    displayTitle = title.length > 60 ? title.substring(0, 60) + '...' : title;
                                } else if (url) {
                                    // 使用URL作为标题
                                    displayTitle = url.length > 60 ? url.substring(0, 60) + '...' : url;
                                } else {
                                    // 备用显示
                                    displayTitle = result.reason || result.message || result.error || '未知项目';
                                }
                                
                                // 根据任务类型和结果状态决定点击行为
                                if (task.task_type === 'batch_analysis') {
                                    // 批量分析数据库记录：点击跳转到原URL
                                    if (url) {
                                        return `
                                            <a href="${url}" target="_blank" class="text-decoration-none" title="点击查看原文链接: ${url}">
                                                <i class="bi bi-link-45deg me-1"></i>${displayTitle}
                                            </a>
                                        `;
                                    } else {
                                        return `<span class="text-muted">${displayTitle}</span>`;
                                    }
                                } else if (task.task_type === 'batch_unanalyzed' || task.task_type === 'archivebox_analysis' || task.task_type === 'archivebox_archive_only') {
                                    // 快照相关任务：点击查看快照
                                    if (snapshotId) {
                                        return `
                                            <a href="javascript:void(0)" onclick="viewSnapshot('${snapshotId}')" class="text-primary text-decoration-none" title="点击查看快照存档: ${url}">
                                                <i class="bi bi-archive me-1"></i>${displayTitle}
                                            </a>
                                        `;
                                    } else if (url) {
                                        return `
                                            <a href="${url}" target="_blank" class="text-decoration-none" title="点击查看原文链接: ${url}">
                                                <i class="bi bi-link-45deg me-1"></i>${displayTitle}
                                            </a>
                                        `;
                                    } else {
                                        return `<span class="text-muted">${displayTitle}</span>`;
                                    }
                                } else {
                                    // 其他任务类型：默认行为
                                    if (url) {
                                        return `
                                            <a href="${url}" target="_blank" class="text-decoration-none" title="点击查看链接: ${url}">
                                                <i class="bi bi-link-45deg me-1"></i>${displayTitle}
                                            </a>
                                        `;
                                    } else {
                                        return `<span class="text-muted">${displayTitle}</span>`;
                                    }
                                }
                            })()}
                        </td>
                        <td>
                            ${(() => {
                                // 根据任务类型和结果状态显示合适的操作按钮
                                const snapshotId = result.snapshot_id || result.id;
                                const analysisId = result.analysis_id;
                                const url = result.url || result.source_url;
                                
                                if (task.task_type === 'batch_analysis' || task.task_type === 'single_analysis') {
                                    // 批量分析数据库记录：显示分析结果按钮组
                                    if ((result.status === 'analyzed' || result.status === 'success') && analysisId) {
                                        return `
                                            <button class="btn btn-sm btn-outline-secondary" onclick="openAnalysisResultInNewTab('${analysisId}')" title="在新标签页中打开分析结果">
                                                <i class="bi bi-box-arrow-up-right"></i> 查看分析
                                            </button>
                                        `;
                                    } else {
                                        return '<span class="text-muted">无操作</span>';
                                    }
                                } else if (task.task_type === 'batch_unanalyzed' || 
                                          task.task_type === 'archivebox_analysis' || 
                                          task.task_type === 'archivebox_archive_only' || 
                                          task.task_type === 'follow_up_analysis') {
                                    // 快照相关任务：显示查看快照和分析结果按钮
                                    if (result.status === 'analyzed') {
                                        // 已分析状态：显示查看分析按钮或已分析状态
                                        if (analysisId) {
                                            return `
                                                <button class="btn btn-sm btn-outline-secondary" onclick="openAnalysisResultInNewTab('${analysisId}')" title="在新标签页中打开分析结果">
                                                    <i class="bi bi-box-arrow-up-right"></i> 查看分析
                                                </button>
                                            `;
                                        } else {
                                            return `
                                                <button class="btn btn-sm btn-success" disabled title="内容已分析完成">
                                                    <i class="bi bi-check-circle-fill"></i> 已分析
                                                </button>
                                            `;
                                        }
                                    } else if (result.status === 'success' && analysisId) {
                                        // 成功状态且有分析ID：显示查看分析按钮
                                        return `
                                            <div class="btn-group" role="group">
                                                ${analysisId ? `
                                                    <button class="btn btn-sm btn-outline-secondary" onclick="openAnalysisResultInNewTab('${analysisId}')" title="在新标签页中打开分析结果">
                                                        <i class="bi bi-box-arrow-up-right"></i> 查看分析
                                                    </button>
                                                ` : ''}
                                            </div>
                                        `;
                                    } else if ((result.status === 'archived' || 
                                               result.status === 'success' || 
                                               (task.task_type === 'archivebox_archive_only' && !analysisId)) && 
                                              snapshotId) {
                                                    return `
                                                        <button class="btn btn-sm btn-outline-success" onclick="analyzeArchivedContent('${snapshotId}', '${url}', '${task.id}')" title="分析快照内容">
                                                            <i class="bi bi-lightning-charge"></i> 分析
                                                        </button>
                                                    `;
                                                } else if (snapshotId) {
                                                    return `
                                                        <button class="btn btn-sm btn-outline-primary" onclick="viewSnapshot('${snapshotId}')" title="查看快照">
                                                            <i class="bi bi-eye"></i> 查看
                                                        </button>
                                                    `;
                                                } else {
                                                    return '<span class="text-muted">无操作</span>';
                                                }
                                            } else {
                                                // 其他任务类型：默认逻辑
                                                if (analysisId) {
                                                    return `
                                                            <button class="btn btn-sm btn-outline-secondary" onclick="openAnalysisResultInNewTab('${analysisId}')" title="在新标签页中打开分析结果">
                                                                <i class="bi bi-box-arrow-up-right"></i> 查看分析
                                                            </button>
                                                        </div>
                                                    `;
                                                } else {
                                                    return '<span class="text-muted">无操作</span>';
                                                }
                                            }
                                        })()}
                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                                ${task.results.length > 10 ? `<small class="text-muted">显示前10项，共${task.results.length}项</small>` : ''}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// 将函数暴露到全局作用域，以便analysis.html中使用
window.createTaskDetailsModalContent = createTaskDetailsModalContent;

// 替换原有的analyzeContent函数
if (typeof analyzeContent !== 'undefined') {
    // 保存原函数作为备份
    window.originalAnalyzeContent = analyzeContent;
}

// 设置新的analyzeContent函数
window.analyzeContent = enhancedAnalyzeContent;

// 处理分析按钮点击事件，提供立即反馈
async function handleAnalyzeClick() {
    try {
        // 立即提供视觉反馈
        const analyzeBtn = document.getElementById('analyzeBtn');
        if (analyzeBtn) {
            analyzeBtn.disabled = true;
            analyzeBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
        }
        
        // 显示开始分析的通知
        showTopRightNotification('正在启动分析任务...', 'info', 2000);
        
        // 调用增强版分析函数
        await enhancedAnalyzeContent();
        
    } catch (error) {
        console.error('分析启动失败:', error);
        showTopRightNotification('分析启动失败: ' + error.message, 'error', 5000);
        
        // 恢复按钮状态
        const analyzeBtn = document.getElementById('analyzeBtn');
        if (analyzeBtn) {
            analyzeBtn.disabled = false;
            analyzeBtn.innerHTML = '<i class="bi bi-lightning-charge"></i> 开始分析';
        }
    }
}

// 将函数添加到全局范围以供HTML调用
window.handleAnalyzeClick = handleAnalyzeClick;

// 安全包装函数，防止未定义错误
function safeStartBatchAnalysis() {
    if (typeof startBatchAnalysis === 'function') {
        startBatchAnalysis();
    } else {
        console.error('startBatchAnalysis函数未定义');
        showTopRightNotification(' 批量分析功能未加载，请刷新页面重试', 'warning', 4000);
    }
}

function safeBatchAnalyzeUnanalyzed() {
    if (typeof batchAnalyzeUnanalyzed === 'function') {
        batchAnalyzeUnanalyzed();
    } else {
        console.error('batchAnalyzeUnanalyzed函数未定义');
        showTopRightNotification(' 批量分析快照功能未加载，请刷新页面重试', 'warning', 4000);
    }
}

// 查看分析结果（支持任务历史详情页按钮）
async function viewAnalysisResult(analysisId) {
    if (!analysisId) {
        showTopRightNotification(' 未提供分析ID', 'warning', 3000);
        return;
    }
    
    // 检查是否在主页面（威胁情报分析工具界面）
    const isInAnalysisPage = window.location.pathname === '/analysis' || window.location.pathname.includes('analysis');
    
    if (isInAnalysisPage) {
        // 在分析页面：关闭所有模态框，在当前页面展示结果
        await viewAnalysisResultInCurrentPage(analysisId);
    } else {
        // 在其他页面：跳转到分析页面查看结果
        window.open(`/analysis/${analysisId}`, '_blank');
    }
}

// 在当前页面展示分析结果
async function viewAnalysisResultInCurrentPage(analysisId) {
    try {
        // 关闭所有打开的模态框
        closeAllModals();
        
        // 显示加载器
        if (document.getElementById('loader')) {
            document.getElementById('loader').style.display = 'block';
        }
        
        // 获取分析详情 - 使用正确的API端点
        const resp = await fetch(`/api/analysis/${encodeURIComponent(analysisId)}`);
        const result = await resp.json();
        
        if (result.success) {
            // 显示成功消息
            showTopRightNotification(` 已加载分析结果 (ID: ${analysisId})`, 'success', 4000);
            
            // 使用现有的displayAnalysisResult函数展示结果
            if (typeof displayAnalysisResult === 'function') {
                displayAnalysisResult(result);
            } else {
                // 备用展示方法
                displayAnalysisResultFallback(result, analysisId);
            }
        } else {
            showTopRightNotification('未找到分析结果: ' + (result.error || analysisId), 'error', 5000);
        }
    } catch (e) {
        showTopRightNotification('获取分析结果失败: ' + e, 'error', 5000);
    } finally {
        if (document.getElementById('loader')) {
            document.getElementById('loader').style.display = 'none';
        }
    }
}

// 页面加载时检查URL是否包含分析ID
function checkUrlForAnalysisId() {
    const path = window.location.pathname;
    const analysisIdMatch = path.match(/\/analysis\/(\d+)$/);
    
    if (analysisIdMatch) {
        const analysisId = analysisIdMatch[1];
        console.log(`从URL中检测到分析ID: ${analysisId}`);
        
        // 延迟加载分析结果，确保页面完全加载
        setTimeout(() => {
            viewAnalysisResultInCurrentPage(analysisId);
        }, 1000);
        
        return true;
    }
    
    return false;
}

// 为新标签页打开添加支持函数
function openAnalysisResultInNewTab(analysisId) {
    const url = `/analysis/${analysisId}`;
    window.open(url, '_blank');
}

// 关闭所有模态框
function closeAllModals() {
    // 查找所有打开的Bootstrap模态框
    const openModals = document.querySelectorAll('.modal.show');
    openModals.forEach(modal => {
        try {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            } else {
                // 手动关闭
                modal.classList.remove('show');
                modal.style.display = 'none';
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        } catch (error) {
            console.error('关闭模态框失败:', error);
        }
    });
    
    console.log('已关闭所有模态框');
}

// 备用的分析结果展示方法
function displayAnalysisResultFallback(result, analysisId) {
    // 显示结果区域
    const resultsArea = document.getElementById('resultsArea');
    if (resultsArea) {
        resultsArea.style.display = 'block';
        resultsArea.style.visibility = 'visible';
        resultsArea.style.opacity = '1';
        
        // 填充分析内容
        const analysisContent = document.getElementById('analysisContent');
        if (analysisContent) {
            const content = result.analysis_content || result.answer || result.content || result.analysis_result || result.analysis || result.data;
            if (content) {
                // 添加分析ID信息
                const analysisHeader = `
                    <div class="alert alert-info mb-3">
                        <i class="bi bi-info-circle"></i> 
                        <strong>分析结果 ID:</strong> ${analysisId}
                        ${result.analysis_type ? `<br><strong>分析类型:</strong> ${result.analysis_type}` : ''}
                        ${result.created_at ? `<br><strong>创建时间:</strong> ${new Date(result.created_at).toLocaleString()}` : ''}
                    </div>
                `;
                
                // 解析并显示内容
                let parsedContent = '';
                try {
                    if (typeof marked !== 'undefined') {
                        parsedContent = marked.parse(content);
                    } else {
                        parsedContent = content.replace(/\n/g, '<br>');
                    }
                } catch (error) {
                    parsedContent = `<pre>${content}</pre>`;
                }
                
                // 为IOC部分添加源链接
                parsedContent = addSourceLinkToIOCs(parsedContent, result.source_url);
                
                analysisContent.innerHTML = analysisHeader + parsedContent;
            } else {
                analysisContent.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i> 
                        未找到分析内容。<br>
                        <small>原始数据: ${JSON.stringify(result, null, 2)}</small>
                    </div>
                `;
            }
        }
        
        // 处理引用信息
        const referencesContent = document.getElementById('referencesContent');
        if (referencesContent) {
            const references = result.references || result.reference || result.refs;
            if (references) {
                let referencesHtml = '';
                if (typeof references === 'string') {
                    referencesHtml = references.replace(/\n/g, '<br>');
                } else if (Array.isArray(references)) {
                    referencesHtml = references.map(ref => `<li>${ref}</li>`).join('');
                    referencesHtml = `<ul>${referencesHtml}</ul>`;
                } else {
                    referencesHtml = JSON.stringify(references, null, 2);
                }
                referencesContent.innerHTML = referencesHtml;
            } else {
                referencesContent.innerHTML = '<p class="text-muted">无引用信息</p>';
            }
        }
        
        // 处理分析理由
        const rationaleContent = document.getElementById('rationaleContent');
        if (rationaleContent) {
            const rationale = result.rationale;
            if (rationale) {
                let rationaleHtml = '';
                if (typeof rationale === 'string') {
                    rationaleHtml = rationale.replace(/\n/g, '<br>');
                } else {
                    rationaleHtml = JSON.stringify(rationale, null, 2);
                }
                rationaleContent.innerHTML = `<pre>${rationaleHtml}</pre>`;
            } else {
                rationaleContent.innerHTML = '<p class="text-muted">无分析理由</p>';
            }
        }
        
        // 滚动到结果区域
        resultsArea.scrollIntoView({ behavior: 'smooth' });
        
        // 激活分析标签页
        setTimeout(() => {
            const analysisTab = document.getElementById('analysisTab');
            if (analysisTab) {
                analysisTab.classList.add('active');
                analysisTab.style.display = 'block';
            }
            
            // 重置标签按钮状态
            const tabButtons = document.getElementsByClassName('tab-button');
            for (let i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove('active');
            }
            if (tabButtons[0]) {
                tabButtons[0].classList.add('active');
            }
        }, 100);
    }
}
window.viewAnalysisResult = viewAnalysisResult;

// 确保函数在全局范围内可用
window.safeStartBatchAnalysis = safeStartBatchAnalysis;
window.safeBatchAnalyzeUnanalyzed = safeBatchAnalyzeUnanalyzed;
window.viewAnalysisResult = viewAnalysisResult;
window.viewAnalysisResultInCurrentPage = viewAnalysisResultInCurrentPage;
window.openAnalysisResultInNewTab = openAnalysisResultInNewTab;
window.viewTaskDetails = viewTaskDetails;
window.viewSnapshot = viewSnapshot;
window.analyzeArchivedContent = analyzeArchivedContent;
window.refreshTaskHistoryIfVisible = refreshTaskHistoryIfVisible;
window.closeAllModals = closeAllModals;
window.checkUrlForAnalysisId = checkUrlForAnalysisId;
window.showTopRightNotification = showTopRightNotification;

// 刷新任务历史（如果任务历史模态框当前可见）
function refreshTaskHistoryIfVisible() {
    // 检查是否有任务历史模态框正在显示
    const taskHistoryModal = document.querySelector('.modal.show .modal-title');
    if (taskHistoryModal && taskHistoryModal.textContent === '任务历史') {
        // 延迟刷新，给后端时间更新状态
        setTimeout(() => {
            console.log('刷新任务历史显示');
            refreshTaskHistoryModal();
        }, 1000);
    }
}

// 刷新任务历史模态框内容
async function refreshTaskHistoryModal() {
    try {
        // 直接使用API刷新，不依赖taskManager
        const response = await fetch('/api/analysis/tasks?limit=20');

        const data = await response.json();

        if (!data.success) {
            console.log('刷新任务历史失败:', data.error);
            return;
        }

        const tasks = data.data;

        // 更新缓存
        taskHistoryCache = tasks;
        taskHistoryCacheTime = Date.now();

        // 查找当前显示的任务历史模态框
        const currentModal = document.querySelector('.modal.show');
        if (!currentModal) return;

        const modalTitle = currentModal.querySelector('.modal-title');
        if (!modalTitle || !modalTitle.textContent.includes('任务历史')) return;

        // 更新表格内容
        const tbody = currentModal.querySelector('#taskTableBody');
        if (tbody) {
            tbody.innerHTML = createTaskRows(tasks);
        }

        console.log('任务历史已刷新');
    } catch (error) {
        console.error('刷新任务历史失败:', error);
    }
}

// 自动刷新任务历史的定时器
let taskHistoryRefreshTimer = null;

// 开始任务历史自动刷新
function startTaskHistoryAutoRefresh() {
    // 如果已经有定时器在运行，先清除它
    if (taskHistoryRefreshTimer) {
        clearInterval(taskHistoryRefreshTimer);
    }
    
    // 每5秒刷新一次任务历史
    taskHistoryRefreshTimer = setInterval(() => {
        const taskHistoryModal = document.querySelector('.modal.show .modal-title');
        if (taskHistoryModal && taskHistoryModal.textContent === '任务历史') {
            refreshTaskHistoryModal();
        }
    }, 5000);
    
    console.log('任务历史自动刷新已启动 (每5秒)');
}

// 停止任务历史自动刷新
function stopTaskHistoryAutoRefresh() {
    if (taskHistoryRefreshTimer) {
        clearInterval(taskHistoryRefreshTimer);
        taskHistoryRefreshTimer = null;
        console.log('任务历史自动刷新已停止');
    }
}

// 分析存档内容（用于任务详情页的分析按钮）
async function analyzeArchivedContent(snapshotId, url, parentTaskId) {
    console.log('analyzeArchivedContent被调用，参数:', { snapshotId, url, parentTaskId });
    
    if (!snapshotId) {
        console.warn('缺少快照ID，函数返回');
        showTopRightNotification('⚠️ 缺少快照ID', 'warning', 3000);
        return;
    }
    
    try {
        // 显示右上角弹窗提示
        console.log('显示开始分析提示');
        showTopRightNotification('🔄 开始分析快照内容...', 'info', 2000);
        
        // 构建请求数据
        const requestData = {
            url: url || `snapshot:${snapshotId}`,
            analysis_type: 'all',
            no_analyze: false,
            snapshot_id: snapshotId
        };
        
        // 如果有父任务ID，添加到请求中
        if (parentTaskId && parentTaskId !== '' && parentTaskId !== 'undefined') {
            requestData.parent_task_id = parentTaskId;
        }
        
        console.log('发送请求数据:', requestData);
        
        // 发送分析请求
        const response = await fetch('/api/analysis_archivebox', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        const result = await response.json();
        console.log('收到响应:', result);
        
        if (result.success) {
            console.log('分析成功，显示成功提示');
            showTopRightNotification('✅ 快照分析完成！', 'success', 4000);
            
            // 如果有分析内容，可以显示
            if (result.analysis_content) {
                // displayAnalysisResult(result); // 可选：在主页面显示结果
            }
            
            // 刷新当前打开的任务详情模态框（如果存在）
            await refreshCurrentTaskDetailsModal(parentTaskId);
            
            // 刷新任务历史列表
            if (window.taskManager && typeof window.taskManager.loadTasks === 'function') {
                window.taskManager.loadTasks();
            }
            
        } else {
            console.log('分析失败，显示失败提示');
            showTopRightNotification('❌ 分析快照失败: ' + (result.error || '未知错误'), 'error', 5000);
            
            // 即使失败也刷新模态框，显示最新状态
            await refreshCurrentTaskDetailsModal(parentTaskId);
        }
    } catch (error) {
        console.error('分析快照时出错:', error);
        showTopRightNotification('❌ 分析快照时出错: ' + error, 'error', 5000);
        
        // 即使出错也刷新模态框
        await refreshCurrentTaskDetailsModal(parentTaskId);
    }
}

// 刷新当前打开的任务详情模态框
async function refreshCurrentTaskDetailsModal(taskId) {
    // 查找当前打开的任务详情模态框
    const openModal = document.querySelector('.modal.show .modal-content');
    if (!openModal) {
        return; // 没有打开的模态框
    }
    
    // 从模态框标题中提取任务ID
    const titleElement = openModal.querySelector('.modal-title');
    if (!titleElement) {
        return;
    }
    
    const titleText = titleElement.textContent;
    const taskIdMatch = titleText.match(/任务详情\s*-\s*(\d+)/);
    if (!taskIdMatch) {
        return;
    }
    
    const modalTaskId = parseInt(taskIdMatch[1]);
    
    // 如果提供了taskId参数，检查是否匹配
    if (taskId && modalTaskId !== parseInt(taskId)) {
        return; // 任务ID不匹配
    }
    
    try {
        // 重新获取任务详情
        const task = await taskManager.getTaskStatus(modalTaskId, true);
        
        // 重新生成模态框内容
        const newModal = createTaskDetailsModal(task);
        const newContent = newModal.querySelector('.modal-content');
        
        // 替换当前模态框的内容
        const currentModal = openModal.closest('.modal');
        const currentContent = currentModal.querySelector('.modal-content');
        currentContent.innerHTML = newContent.innerHTML;
        
        console.log(`任务详情模态框已刷新 (任务ID: ${modalTaskId})`);
        
    } catch (error) {
        console.error('刷新任务详情模态框失败:', error);
    }
}

// 查看快照（用于任务详情页的查看按钮）
async function viewSnapshot(snapshotId) {
    if (!snapshotId) {
        showTopRightNotification(' 缺少快照ID', 'warning', 3000);
        return;
    }
    
    try {
        // 在新标签页中打开快照详情
        const url = `/snapshot/${snapshotId}`;
        window.open(url, '_blank');
    } catch (error) {
        console.error('打开快照失败:', error);
        showTopRightNotification('打开快照失败: ' + error, 'error', 5000);
    }
}

// 为IOC部分添加源链接
function addSourceLinkToIOCs(content, sourceUrl) {
    if (!sourceUrl || !content) return content;
    
    // 匹配IOC相关的标题或段落
    const iocPatterns = [
        /(<h[1-6][^>]*>.*?IOC.*?<\/h[1-6]>)/gi,
        /(<h[1-6][^>]*>.*?威胁.*?指标.*?<\/h[1-6]>)/gi,
        /(<h[1-6][^>]*>.*?恶意.*?指标.*?<\/h[1-6]>)/gi,
        /(<h[1-6][^>]*>.*?指标.*?<\/h[1-6]>)/gi,
        /(<p><strong>.*?IOC.*?<\/strong><\/p>)/gi,
        /(<p><strong>.*?威胁.*?指标.*?<\/strong><\/p>)/gi,
        /(<p><strong>.*?恶意.*?指标.*?<\/strong><\/p>)/gi,
        /(<p><strong>.*?指标.*?<\/strong><\/p>)/gi
    ];
    
    let modifiedContent = content;
    
    // 为每个匹配的IOC标题检测（但不添加单独链接）
    iocPatterns.forEach(pattern => {
        modifiedContent = modifiedContent.replace(pattern, (match) => {
            // 检测到IOC标题，但不添加单独的链接，保持原样
            return match;
        });
    });
    
    // 检查是否有IOC内容（无论是否有标题）
    let hasIOCs = false;
    
    // 首先检查是否有IOC标题，并且标题后有真实的IOC内容
    for (const pattern of iocPatterns) {
        const matches = content.match(pattern);
        if (matches) {
            // 找到IOC标题，检查标题后的内容
            const titleMatch = matches[0];
            const titleIndex = content.indexOf(titleMatch);
            const contentAfterTitle = content.substring(titleIndex + titleMatch.length, titleIndex + titleMatch.length + 500);
            
            // 检查标题后是否包含"没有提供"、"无相关信息"等否定词汇
            const hasNegativeIndicators = /没有提供|无相关信息|参考资料中没有|参考资料中没有提供具体的|未找到|不包含|无此信息|暂无|无法获取/gi.test(contentAfterTitle);
            
            if (!hasNegativeIndicators) {
                // 没有否定词汇，检查是否有实际的IOC内容
                const hasActualIOCs = /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b|[a-fA-F0-9]{32}|[a-fA-F0-9]{40}|[a-fA-F0-9]{64}|https?:\/\/|MD5:|SHA1:|SHA256:|IP地址：|域名：|文件哈希：/gi.test(contentAfterTitle);
                
                if (hasActualIOCs) {
                    hasIOCs = true;
                    break;
                }
            }
        }
    }
    
    // 如果没有IOC标题或标题后没有实际内容，再检查是否有IOC列表内容
    if (!hasIOCs) {
        const iocListPatterns = [
            /(IP地址：.*?)(\n|<br>|$)/gi,
            /(域名：.*?)(\n|<br>|$)/gi,
            /(文件哈希：.*?)(\n|<br>|$)/gi,
            /(URL：.*?)(\n|<br>|$)/gi,
            /MD5:\s*[a-fA-F0-9]{32}/gi,
            /SHA1:\s*[a-fA-F0-9]{40}/gi,
            /SHA256:\s*[a-fA-F0-9]{64}/gi,
            /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/gi, // IP地址
            /\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b/gi // 域名
        ];
        
        for (const pattern of iocListPatterns) {
            if (pattern.test(modifiedContent)) {
                hasIOCs = true;
                break;
            }
        }
    }
    
    // 如果检测到任何IOC内容，在文档末尾添加链接
    if (hasIOCs) {
        const sourceLinkHtml = `<small class="text-muted mt-2 d-block">
            <i class="bi bi-info-circle"></i> 以上是报告中的IOC，详请查看 <a href="${sourceUrl}" target="_blank" class="text-primary">原网页</a>
        </small>`;
        
        // 在文档末尾添加链接
        modifiedContent += sourceLinkHtml;
    }
    
    return modifiedContent;
}

// 清理引用文本（去除引用信息）
function cleanReferencesText(text) {
    if (!text) return '';
    
    // 移除引用模式
    let cleaned = text
        // 移除 [数字] 格式的引用
        .replace(/\[\d+\]/g, '')
        // 移除 (数字) 格式的引用
        .replace(/\(\d+\)/g, '')
        // 移除引用链接格式 [文本](链接)
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
        // 移除多余的空格和换行
        .replace(/\s+/g, ' ')
        .trim();
    
    return cleaned;
}

// 清理引用数组
function cleanReferencesArray(references) {
    if (!Array.isArray(references)) return references;
    
    return references.map(ref => {
        if (typeof ref === 'string') {
            return cleanReferencesText(ref);
        }
        return ref;
    }).filter(ref => ref && ref.toString().trim() !== '');
}
