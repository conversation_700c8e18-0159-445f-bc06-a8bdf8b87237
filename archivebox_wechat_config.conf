# ArchiveBox配置文件 - 针对微信公众号优化
# 将此文件复制到 /home/<USER>/docker_archivebox/Archive_box_data/ArchiveBox.conf

[SERVER_CONFIG]
SECRET_KEY = "DuJ5Gg0OGUQBO23wrMob0AlYDervu21eTcFWvnBNXPaL0oKjNn"
ALLOWED_HOSTS = "*"
DEBUG = False


[DEPENDENCY_CONFIG]
# Chrome配置 - 使用系统Chrome而不是Playwright
CHROME_BINARY = /usr/bin/google-chrome
# 如果系统Chrome不存在，尝试以下路径：
# CHROME_BINARY = /usr/bin/chromium-browser
# CHROME_BINARY = /home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome

# 统一User-Agent - 使用较新但不是最新的版本避免检测
WGET_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
CURL_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
CHROME_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# 增加超时时间 - 微信页面加载较慢
CURL_TIMEOUT = 300
WGET_TIMEOUT = 300
CHROME_TIMEOUT = 300
SINGLEFILE_TIMEOUT = 300
READABILITY_TIMEOUT = 300
MERCURY_TIMEOUT = 300

[ARCHIVE_METHODS_CONFIG]
# 启用主要的归档方法
SAVE_TITLE = True
SAVE_FAVICON = True
SAVE_WGET = True
SAVE_SINGLEFILE = True
SAVE_READABILITY = True
SAVE_MERCURY = True
SAVE_PDF = True
SAVE_SCREENSHOT = True
SAVE_DOM = True
SAVE_HEADERS = True

# 禁用可能导致问题的方法
SAVE_ARCHIVE_DOT_ORG = False
SAVE_YOUTUBEDL = False
SAVE_GIT = False

[GENERAL_CONFIG]
# 基本配置
TIMEOUT = 300
MEDIA_MAX_SIZE = 1024m
CHECK_SSL_VALIDITY = False
SAVE_ALLOWLIST_PTN = ".*"
SAVE_DENYLIST_PTN = ""

# 并发配置
MAX_CONCURRENT_DOWNLOADS = 3

# 微信特定配置 - 使用Cookie文件
COOKIES_FILE = "/home/<USER>/docker_archivebox/Data_Excation/WX_Cookie.txt"
CHROME_EXTRA_ARGS = "--no-sandbox --disable-dev-shm-usage --disable-gpu --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI --disable-ipc-flooding-protection --no-first-run --no-default-browser-check"

[LOGGING_CONFIG]
LOG_LEVEL = INFO
USE_COLOR = True

# 微信公众号特殊处理说明：
# 1. 增加了超时时间到300秒
# 2. 禁用了Archive.org保存（避免额外请求）
# 3. 统一了User-Agent避免检测
# 4. 添加了Chrome额外参数提高稳定性
# 5. 禁用SSL验证避免证书问题
