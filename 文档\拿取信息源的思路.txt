
构建谷歌检索语法  site: 域名 关键字

1、维护一个关键字list------ATT&CK的组织名，国内平台上的组织名

2、源的list ----人工筛选出高质量的文章源，进行爬取

3、依据前两步的列表，自动构建 检索语法的list

4、通过语法检索的list进行通用的爬取，使用（https://www.searchapi.io/）进行爬虫的编写。保存成html页面，供后续使用

   --Google 搜索 API 使用/api/v1/search?engine=googleAPI 端点来抓取实时结果。
   import requests

    url = "https://www.searchapi.io/api/v1/search"
    params = {
      "engine": "google",
      "q": "chatgpt",
      "api_key": "kqTUhhc6JJV1htYv8QyBUenY"
    }

    response = requests.get(url, params=params)
    print(response.text)


5、存入数据库中的信息
原始数据的list      ---id,标题，HTML的编号等等------crawled_data表 (crawl_status= 0表示未处理，1表示成功，2表示失败，3表示跳过，4表示空白)

存取语法检索的list  -----id,语法检索的内容，时间戳等------NewRetrieval表  (use_status=0表示未检索，1表示已检索且有数据，2表示已检索但无数据)


处理后的数据的list    -----通过bs4,解析html界面，拿取信息----------analysis_results表


最终动态更新上述的list   ------最后实现自动化的脚本