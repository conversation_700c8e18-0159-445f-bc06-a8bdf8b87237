<PERSON><PERSON><PERSON>ITE PAPERNew Warp Malware
Chat ID

Launch Command-************* New.launchP. 07 Then it fetches the command from the chat using the “telegram.GetChat” function with the chat ID. After verifying the return value, it downloads additional payloads using the “telegram.DownloadFile” function. The random calls and requests are performed again before downloading.The “telegram.SendMessage” user function sends a message containing the hostname and username to its telegram C2 bot. It utilizes “telegram.GetBase” to decrypt strings to be used in the URL:Telegram C2 Bot Initial Message

URL for Telegram API

Private Bot Token Get command Get the ﬁle to be downloaded Download path/sendMessage?&parse_mode=HTML&chat_id=%s&text=%s https://api.telegram.org/bot%s

**********************************************

/getChat?chat_id=%s /getFile?ﬁle_id=%s C:\ProgramData\warp Fig. 6 – Initial contact with Telegram C2 Bot Fig. 7 – Spam calls before downloading payload P. 08Though the C2 bot was not alive during our analysis, we could ﬁnd that it was downloading a ﬁle named wd.exe in the ProgramData directory. We observed a GO binary being dropped in the same directory is, in fact, the Warp Dropper. After downloading, the spam functions are triggered again before executing the payload using Cmd.Run(). Fig. 8 – Loader downloading the dropper Fig. 9 – Spam calls before executing a payloadP. 09 Fig. 10 – Dropper functions Fig. 11 – Dropper flowThe dropper component ultimately downloads and runs a stealer. It performs privilege escalation and kills the antivirus solution installed on the victim’s machine. The dropper utilizes the same telegram functionalities for C2. After using GoReSym, the functions are renamed as follows:Warp Dropper Though the stealer is downloaded and run, both the binaries required for getting privileges and killing AV are embedded in the dropper itself. P. 10 It checks if the running process is elevated via the current user’s UID and, if failed, self-restarts by dropping an embedded binary for UAC bypass to escalate privileges. The binary is decrypted in a similar fashion seen in the loader component and executed from the ‘Program Data\warp\uac.exe’ directory. The executable used to elevate privileges is PE64 with compiler-stamp May 06, 2023 and the PDB path leads us to a known UAC bypass trick. It uses RPC requests (RAiLaunchAdminProcess) via ALPC (Advanced Local Procedure Calls) kernel feature. C:\Users\<USER>\Desktop\PR0CESS-main\UACBypassJF_RpcALPC\src\x64\Release\tyranid_app Info_alpc.pdb The non-elevated process created is ‘winver.exe’ to initial the debug object by setting the necessary flag. The auto-elevated process designed is ‘computerdefaults.exe,’ which gets assigning the existing debug object.UAC Bypass Fig. 12 – Dropping and executing UAC bypass binaryP. 11The handle of this elevated process is duplicated to retrieve a higher privileged handle by capturing the debug object retrieved from the debug event.Fig. 13 – Creating non-elevated and auto-elevated processes Fig. 14 – Duplicating process handleP. 12 Fig. 15 – Sending a message to C2 with privilege info Fig. 16 – Dropping driver file and executing it as a service To kill the antivirus solution, an embedded driver ﬁle is dropped, which is a vulnerable Avast’s Anti-Rootkit driver ﬁle that can terminate a given process. It is installed as a kernel service with the following command: This disabling technique was ﬁrst found in 2022 and was used by AvosLocker and Cuba Ransomware groups to terminate EDR solutions. Meanwhile, a thread function uses CreateToolhelp32Snapshot winAPI  to fetch the process list and kill process PID using DeviceIoControl API.sc.exe create aswSP_ArPots binPath=C:\ProgramData\warp\av.sys type=kernelDisabling AV P. 13Fig. 17 – Killing process via PID using DeviceIoControl Fig. 18 – Creating a scheduled task for persistenceIt moves itself (dropper) into the ProgramData directory and creates a scheduled task. This is done to persist it to execute daily at a speciﬁc time via cmd.exe. The task name used here, “MicrososftSecureUpdateTaskMachineUA,” can be easily confused with the legitimate update schedule of Microsoft Edge. P. 14Fig. 19 – Task Scheduled for persistence Fig. 20 – Function DiffThis modiﬁed infostealer belongs to the malware family known as Stealerium, an open-source C# project present on a GitHub repository. It has stealer, clipper, and keylogger features. This year, various modiﬁed versions of this malware, like Enigma Stealer, have been discovered that targeted individuals in the crypto industry. After analyzing the modiﬁed .NET sample using BinDiff, we have found changes in a few modules present in this new Warp Stealer, with both being 83% similar.Finally, the stealer is downloaded into the same directory as ‘wst.exe’ and executed. After the initial stealer report is sent to the C2, the stealer is deleted as the dropper component persists through a system reboot and keeps it from getting detected. Signiﬁcant changes are the removal of Discord Web-hooks used for ex-ﬁltrating information stolen and string occurrences “Stealerium.”Warp Stealer P. 15 For sending data, the threat actor has added the same Telegram bot conﬁguration used in the loader/dropper component. Some modules have been disabled in this modiﬁed version 2.0, like Clipper, Keylogger, and AutoRun. The grabber module has added new ﬁles and folders that interest the threat actor. Rust-based source code and maFile databases have also been added, whereas image ﬁles have been removed completely. Files and folders added:Fig. 22 – Stealer Configuration Changes Fig. 21 – Removed Stealerium details .env .gitignoredocker-compose.yml docker-compose.yamlrs maFile.git .sshDockerﬁle

README.md

P. 16 Other additions include fetching network cookies and local storage for the Chromium browser. Multiple changes in Discord Webhook and Helper functions are also found.Fig. 23 – Modifications in Grabber module P. 17The ﬁnal Warp Stealer report sent to the Telegram C2 is shown below. Compared to the original Stealerium report, this sends less data as some modules are disabled. Fig. 25 – Report of Warp Stealer Fig. 24 - Additions in fetching Chromium browser dataP. 18 Fig. 26 – Report of Stealerium P. 19Execution Immediately after the execution, it creates a hidden directory in AppData/Local folder. The name of the directory is by combining Hash+system information (username, computer name, CPU name, GPU name, and system language)The remaining features of Stealerium are described below: Fig. 27.1 – Hidden directory creation Fig. 27.2 – Naming the hidden directoryP. 20Clipper Gets clipboard information and will store it as clipboardText. If clipboard text matches any of the wallet addresses, it will replace it with the attacker’s crypto wallet address. Keylogger It monitors the victim’s keyboard and saves keys in a log ﬁle in the keylogger directory with the date and time. Fig. 28 – Clipper module Fig. 29 – Keylogger module P. 21Persistence It sets a RUNKEY for persistence at the location HKCU\Software\Microsoft\Windows\CurrentVersion\Run\ Defense Evasion It delays the execution and sleeps for 10000 milliseconds to postpone its execution in sandbox systems.Delay Execution Fig. 30 – Persistence mechanism used by the stealer Fig. 31 – Delay execution module P. 22It delays the execution and sleeps for 10000 milliseconds to postpone its execution in sandbox systems. If any checks pass it generates a fake error message and calls a self-destruction process. Anti- Analysis techniques Anti-Debugging Anti-Virtual Box Anti-Emulator Anti- sandbox Analysis toolsCheckRemoteDebuggerPresent() API Checks with the keyword VMware, VirtualBox Compares the system’s date and time Checks for SbieDll, SxIn, snxhk,cmdvrt32 Checks for Processhacker, netstat, netmon, tcpview, wireshark, ﬁlemon, regmon, cain Fig. 32 – Anti-analysis techniques used Fig. 33 – Generating fake error message P. 23 Fig. 34 – Self-destruction process Fig. 35 – Collecting saved Wi-Fi password from the victim’s systemCredential Access It collects data from the browsers like Chrome, Firefox, and internet explorer •From Chromium browsers, it collects information like saved passwords, card details, cookies, auto-ﬁll ﬁeld information, and bookmarks. •From Firefox browsers, it collects information like bookmarks, browser history, db ﬁles, and cookies. •From internet explorer/edge, it collects auto-ﬁlls, bookmarks, credit card details, and saved passwords. •From the system, it collects the username and passwords of WiFi networks and performs scans to get information about the devices around. P. 24Fig. 36 – Sensitive data collection from different browsers P. 25Collection It will check for the below strings. It will take screenshots and record keys when it matches any of the below strings. Financial details from It collects data from the below crypto servicesSensitive information Fig. 37 – Data collection from these social media accounts Fig. 38 – Data collection from these financial services P. 26Fig. 39 – Data collection from these crypto services P. 27Gets system information It tries to get system information from the victim’s machine like In addition to the above information, it takes desktop screenshots and saves them as

DESKTOP.jpg
PublicIP
CPU name
GPUname

Battery detailsDeafaultGateway Systemversion RAM details Process list Fig. 40 – Taking Desktop screenshot Fig. 41 – System information collection from the victim’s system P. 28 Porn detection It will check if the system has adult content and takes a screenshot and shot from the webcam, which will be stored in logs. Fig. 42 – Porn detection module P. 29Conclusion IOCWarp malware combines a loader, a dropper, and a stealer. Multi-functional malware targets users’ sensitive information from all sources, including system information. At ﬁrst, the attacker creates a telegram Bot account and inserts that token into the malware. Later, the sample is sent as an attachment to the victim’s machine, luring the victim to open it. Then immediately after opening, it starts its execution and downloads a stealer, which is responsible for collecting all user data related to ﬁnancial and personal, including web camera shots. And later, all this collected information is stored as logs which will be sent to the attacker through C2. To mitigate these types of attacks, it is essential to maintain robust security practices, including using up-to-date antivirus software, regularly updating systems and applications, exercising caution while clicking on links or downloading ﬁles, and practicing good password hygiene to safeguard our personal information.

URLs

hxxps://api.telegram[.]org/bot**********************************************/send Message?&parse_mode=HTML&chat_id=-*************&text= hxxps://api.telegram[.]org/bot**********************************************/getChat? chat_id=-************* hxxps://api.telegram[.]org/bot**********************************************/send Document?chat_id=-************* hxxps://api.telegram[.]org/bot**********************************************/send Message?parse_mode=Markdown&chat_id=-*************&text= hxxps://api.telegram[.]org/bot**********************************************/getFile? ﬁle_id=-************* hxxps://softstock[.]shop/download/Adobe%20Acrobat%20Update.exe MD5 Description Detection Warp Loader Warp Dropper Warp Stealer (Stealerium)

UAC Bypass

Avast Anti-Rootkit DriverTrojan.WarpLoader Trojan.WarpDropper

Trojan.YakbeexMSIL.ZZ4

Exploit.UACBypass (legitimate)ac941919c2bffaf6aa6077322a48f09f fe08102907a8202581766631b1e31915 e1f6f92526dabe5365b7c3137c385cd2 b400973f489df968022756822ca4d76a 0a0bdd679d44b77d2e6464e9fac6244cAll Intellectual Property Right(s) including trademark(s), logo(s) and copyright(s) are properties of their respective owners. Copyright © 2023 Quick Heal Technologies Ltd. All rights reserved.Marvel Edge, Ofﬁce No. 7010 C & D, 7th Floor, Viman Nagar, Pune - 411014, India. www.seqrite.com