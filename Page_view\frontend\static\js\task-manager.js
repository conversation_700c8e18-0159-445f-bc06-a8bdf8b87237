/**
 * 任务状态管理器
 * 用于处理分析任务的持久化状态管理
 */
class TaskManager {
    constructor() {
        this.pollingInterval = 2000; // 2秒轮询间隔
        this.activePolls = new Map(); // 活跃的轮询任务
        this.taskStatusCache = new Map(); // 任务状态缓存
        this.onTaskUpdateCallbacks = new Map(); // 任务更新回调
        
        // 页面卸载时清理轮询
        window.addEventListener('beforeunload', () => {
            this.stopAllPolling();
        });
    }

    /**
     * 开始轮询任务状态
     * @param {number} taskId - 任务ID
     * @param {Function} onUpdate - 状态更新回调函数
     * @param {Function} onComplete - 任务完成回调函数
     */
    startPolling(taskId, onUpdate, onComplete) {
        console.log(`开始轮询任务 ${taskId} 的状态`);
        
        // 如果已经在轮询该任务，先停止
        if (this.activePolls.has(taskId)) {
            this.stopPolling(taskId);
        }
        
        // 保存回调函数
        this.onTaskUpdateCallbacks.set(taskId, { onUpdate, onComplete });
        
        // 开始轮询
        const pollFunction = async () => {
            try {
                const response = await fetch(`/api/tasks/${taskId}`);
                const result = await response.json();
                
                if (result.success && result.task) {
                    const task = result.task;
                    
                    // 更新缓存
                    this.taskStatusCache.set(taskId, task);
                    
                    // 调用更新回调
                    if (onUpdate) {
                        onUpdate(task);
                    }
                    
                    // 检查任务是否完成
                    if (['completed', 'failed', 'cancelled'].includes(task.task_status)) {
                        console.log(`任务 ${taskId} 已完成，状态: ${task.task_status}`);
                        
                        // 调用完成回调
                        if (onComplete) {
                            onComplete(task);
                        }
                        
                        // 停止轮询
                        this.stopPolling(taskId);
                        return;
                    }
                    
                    // 继续轮询
                    const timeoutId = setTimeout(pollFunction, this.pollingInterval);
                    this.activePolls.set(taskId, timeoutId);
                } else {
                    console.error(`获取任务 ${taskId} 状态失败:`, result.error);
                    this.stopPolling(taskId);
                }
            } catch (error) {
                console.error(`轮询任务 ${taskId} 状态时出错:`, error);
                this.stopPolling(taskId);
            }
        };
        
        // 立即执行一次
        pollFunction();
    }

    /**
     * 停止轮询特定任务
     * @param {number} taskId - 任务ID
     */
    stopPolling(taskId) {
        if (this.activePolls.has(taskId)) {
            clearTimeout(this.activePolls.get(taskId));
            this.activePolls.delete(taskId);
            console.log(`停止轮询任务 ${taskId}`);
        }
        
        // 清理回调
        this.onTaskUpdateCallbacks.delete(taskId);
    }

    /**
     * 停止所有轮询
     */
    stopAllPolling() {
        for (const [taskId, timeoutId] of this.activePolls) {
            clearTimeout(timeoutId);
            console.log(`停止轮询任务 ${taskId}`);
        }
        this.activePolls.clear();
        this.onTaskUpdateCallbacks.clear();
    }

    /**
     * 获取任务状态（从缓存或服务器）
     * @param {number} taskId - 任务ID
     * @param {boolean} forceRefresh - 是否强制从服务器刷新
     */
    async getTaskStatus(taskId, forceRefresh = false) {
        if (!forceRefresh && this.taskStatusCache.has(taskId)) {
            return this.taskStatusCache.get(taskId);
        }
        
        try {
            const response = await fetch(`/api/tasks/${taskId}`);
            const result = await response.json();
            
            if (result.success && result.task) {
                this.taskStatusCache.set(taskId, result.task);
                return result.task;
            } else {
                throw new Error(result.error || '获取任务状态失败');
            }
        } catch (error) {
            console.error(`获取任务 ${taskId} 状态失败:`, error);
            throw error;
        }
    }

    /**
     * 获取任务列表
     * @param {string} taskType - 任务类型（可选）
     * @param {number} limit - 限制数量
     */
    async getTaskList(taskType = null, limit = 20) {
        try {
            const params = new URLSearchParams({ limit: limit.toString() });
            if (taskType) {
                params.append('task_type', taskType);
            }
            
            const response = await fetch(`/api/tasks?${params.toString()}`);
            const result = await response.json();
            
            if (result.success) {
                return result.tasks;
            } else {
                throw new Error(result.error || '获取任务列表失败');
            }
        } catch (error) {
            console.error('获取任务列表失败:', error);
            throw error;
        }
    }

    /**
     * 取消任务
     * @param {number} taskId - 任务ID
     */
    async cancelTask(taskId) {
        try {
            const response = await fetch(`/api/tasks/${taskId}/cancel`, {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                // 停止轮询
                this.stopPolling(taskId);
                return true;
            } else {
                throw new Error(result.error || '取消任务失败');
            }
        } catch (error) {
            console.error(`取消任务 ${taskId} 失败:`, error);
            throw error;
        }
    }

    /**
     * 创建任务状态显示UI
     * @param {Object} task - 任务对象
     * @param {Element} container - 容器元素
     */
    createTaskStatusUI(task, container) {
        const taskElement = document.createElement('div');
        taskElement.className = 'task-status-card mb-3';
        taskElement.id = `task-${task.id}`;
        
        const statusClass = this.getStatusClass(task.task_status);
        const statusText = this.getStatusText(task.task_status);
        
        taskElement.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">
                                <span class="badge bg-${statusClass} me-2">${statusText}</span>
                                ${this.getTaskTypeText(task.task_type)}
                            </h6>
                            <div class="task-progress mb-2">
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-${statusClass}" 
                                         role="progressbar" 
                                         style="width: ${task.progress || 0}%"
                                         aria-valuenow="${task.progress || 0}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">
                                    ${task.completed_count || 0} / ${task.total_count || 0} 
                                    (成功: ${task.success_count || 0}, 失败: ${task.error_count || 0})
                                </small>
                            </div>
                            <small class="text-muted">
                                开始时间: ${new Date(task.start_time).toLocaleString()}
                                ${task.end_time ? ` | 结束时间: ${new Date(task.end_time).toLocaleString()}` : ''}
                            </small>
                        </div>
                        <div class="task-actions">
                            ${task.task_status === 'running' ? 
                                `<button class="btn btn-sm btn-outline-danger" onclick="taskManager.cancelTask(${task.id})">取消</button>` : 
                                `<button class="btn btn-sm btn-outline-secondary" onclick="taskManager.clearTaskFromUI(${task.id})">清除</button>`
                            }
                        </div>
                    </div>
                    ${task.error_message ? `
                        <div class="alert alert-danger alert-sm mt-2 mb-0">
                            <small>${task.error_message}</small>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        container.appendChild(taskElement);
        return taskElement;
    }

    /**
     * 更新任务状态UI
     * @param {Object} task - 任务对象
     */
    updateTaskStatusUI(task) {
        const taskElement = document.getElementById(`task-${task.id}`);
        if (!taskElement) return;
        
        const statusClass = this.getStatusClass(task.task_status);
        const statusText = this.getStatusText(task.task_status);
        
        // 更新状态标签
        const statusBadge = taskElement.querySelector('.badge');
        if (statusBadge) {
            statusBadge.className = `badge bg-${statusClass} me-2`;
            statusBadge.textContent = statusText;
        }
        
        // 更新进度条
        const progressBar = taskElement.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.className = `progress-bar bg-${statusClass}`;
            progressBar.style.width = `${task.progress || 0}%`;
            progressBar.setAttribute('aria-valuenow', task.progress || 0);
        }
        
        // 更新进度文本
        const progressText = taskElement.querySelector('.text-muted');
        if (progressText) {
            progressText.innerHTML = `
                ${task.completed_count || 0} / ${task.total_count || 0} 
                (成功: ${task.success_count || 0}, 失败: ${task.error_count || 0})
            `;
        }
        
        // 更新操作按钮
        const actionBtn = taskElement.querySelector('.task-actions button');
        if (actionBtn) {
            if (task.task_status === 'running') {
                actionBtn.className = 'btn btn-sm btn-outline-danger';
                actionBtn.textContent = '取消';
                actionBtn.onclick = () => this.cancelTask(task.id);
            } else {
                actionBtn.className = 'btn btn-sm btn-outline-secondary';
                actionBtn.textContent = '清除';
                actionBtn.onclick = () => this.clearTaskFromUI(task.id);
            }
        }
        
        // 添加错误信息
        if (task.error_message) {
            let errorDiv = taskElement.querySelector('.alert-danger');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger alert-sm mt-2 mb-0';
                taskElement.querySelector('.card-body').appendChild(errorDiv);
            }
            errorDiv.innerHTML = `<small>${task.error_message}</small>`;
        }
    }

    /**
     * 从UI中清除任务
     * @param {number} taskId - 任务ID
     */
    clearTaskFromUI(taskId) {
        const taskElement = document.getElementById(`task-${taskId}`);
        if (taskElement) {
            taskElement.remove();
        }
        
        // 停止轮询
        this.stopPolling(taskId);
        
        // 清除缓存
        this.taskStatusCache.delete(taskId);
    }

    /**
     * 获取状态对应的Bootstrap类
     * @param {string} status - 任务状态
     */
    getStatusClass(status) {
        const statusMap = {
            'pending': 'secondary',
            'running': 'primary',
            'completed': 'success',
            'failed': 'danger',
            'cancelled': 'warning',
            'partially_completed': 'info'
        };
        return statusMap[status] || 'secondary';
    }

    /**
     * 获取状态的中文文本
     * @param {string} status - 任务状态
     */
    getStatusText(status) {
        const statusMap = {
            'pending': '等待中',
            'running': '运行中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消',
            'partially_completed': '部分完成'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取任务类型的中文文本
     * @param {string} taskType - 任务类型
     */
    getTaskTypeText(taskType) {
        const typeMap = {
            'batch_analysis': '批量RAG分析',
            'single_analysis': '单个RAG分析',
            'archivebox_analysis': 'ArchiveBox存档+分析',
            'archivebox_archive_only': 'ArchiveBox仅存档',
            'batch_unanalyzed': '批量分析未分析快照',
            'test_task': '测试任务',
            'follow_up_analysis': '后续分析任务',
            'weekly_crawl': '每周爬取任务'
        };
        return typeMap[taskType] || taskType;
    }

    /**
     * 显示任务开始提示
     * @param {number} taskId - 任务ID
     * @param {string} taskType - 任务类型
     * @param {number} totalCount - 总数量
     */
    showTaskStarted(taskId, taskType, totalCount = 0) {
        const message = `
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <strong>任务已启动!</strong> 
                ${this.getTaskTypeText(taskType)} 任务 (ID: ${taskId}) 
                ${totalCount > 0 ? `，共 ${totalCount} 项` : ''}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 显示在页面顶部
        this.showMessage(message);
        
        // 3秒后自动隐藏
        setTimeout(() => {
            const alert = document.querySelector('.alert-info');
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        // 查找或创建消息容器
        let messageContainer = document.getElementById('message-container');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'message-container';
            messageContainer.className = 'position-fixed top-0 start-50 translate-middle-x';
            messageContainer.style.zIndex = '1055';
            messageContainer.style.marginTop = '20px';
            document.body.appendChild(messageContainer);
        }
        
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.innerHTML = message;
        messageContainer.appendChild(messageElement);
        
        // 确保Bootstrap的alert功能正常工作
        if (window.bootstrap && bootstrap.Alert) {
            const alerts = messageElement.querySelectorAll('.alert');
            alerts.forEach(alert => new bootstrap.Alert(alert));
        }
    }

    /**
     * 恢复页面刷新前的任务状态（仅后台轮询，不显示UI）
     */
    async restoreTasksFromStorage() {
        try {
            const storedTasks = localStorage.getItem('activeTasks');
            if (!storedTasks) {
                console.log('没有存储的活跃任务');
                return;
            }
            
            const taskIds = JSON.parse(storedTasks);
            console.log('恢复活跃任务:', taskIds);
            
            // 恢复每个任务的状态（仅后台轮询，不显示UI）
            for (const taskId of taskIds) {
                try {
                    const task = await this.getTaskStatus(taskId, true);
                    if (task && !['completed', 'failed', 'cancelled'].includes(task.task_status)) {
                        console.log(`恢复任务 ${taskId} 的后台轮询`);
                        
                        // 开始后台轮询，但不创建UI
                        this.startPolling(taskId,
                            (updatedTask) => {
                                // 任务更新时只更新缓存，不更新UI
                                console.log(`任务 ${taskId} 状态更新:`, updatedTask.task_status);
                            },
                            (completedTask) => {
                                // 任务完成时清理并显示通知
                                console.log(`任务 ${taskId} 已完成`);
                                this.removeTaskFromStorage(taskId);
                                if (completedTask.task_status === 'completed') {
                                    this.showCompletionNotification(completedTask);
                                }
                            }
                        );
                    } else {
                        // 任务已完成，从存储中移除
                        this.removeTaskFromStorage(taskId);
                    }
                } catch (error) {
                    console.error(`恢复任务 ${taskId} 失败:`, error);
                    this.removeTaskFromStorage(taskId);
                }
            }
        } catch (error) {
            console.error('恢复任务状态失败:', error);
        }
    }
    
    /**
     * 添加任务到本地存储
     * @param {number} taskId - 任务ID
     */
    addTaskToStorage(taskId) {
        try {
            const storedTasks = localStorage.getItem('activeTasks');
            let taskIds = storedTasks ? JSON.parse(storedTasks) : [];
            
            if (!taskIds.includes(taskId)) {
                taskIds.push(taskId);
                localStorage.setItem('activeTasks', JSON.stringify(taskIds));
                console.log(`任务 ${taskId} 已添加到本地存储`);
            }
        } catch (error) {
            console.error('添加任务到本地存储失败:', error);
        }
    }
    
    /**
     * 从本地存储中移除任务
     * @param {number} taskId - 任务ID
     */
    removeTaskFromStorage(taskId) {
        try {
            const storedTasks = localStorage.getItem('activeTasks');
            if (storedTasks) {
                let taskIds = JSON.parse(storedTasks);
                taskIds = taskIds.filter(id => id !== taskId);
                localStorage.setItem('activeTasks', JSON.stringify(taskIds));
                console.log(`任务 ${taskId} 已从本地存储中移除`);
            }
        } catch (error) {
            console.error('从本地存储移除任务失败:', error);
        }
    }

    /**
     * 创建任务状态容器（不再自动显示在页面上）
     */
    createTaskContainer() {
        // 不再自动创建可见的任务状态容器
        // 只在需要时通过任务历史弹窗显示
        console.log('任务状态容器创建被跳过，只在任务历史中显示');
        return null;
    }

    /**
     * 显示完成通知
     * @param {Object} task - 任务对象
     */
    showCompletionNotification(task) {
        const successCount = task.success_count || 0;
        const errorCount = task.error_count || 0;
        const totalCount = task.total_count || 0;
        
        let message = '<strong>任务完成!</strong> ';
        
        if (totalCount > 1) {
            message += `共处理 ${totalCount} 项，成功 ${successCount} 项`;
            if (errorCount > 0) {
                message += `，失败 ${errorCount} 项`;
            }
            message += '。';
        } else {
            message += successCount > 0 ? '分析成功。' : '处理完成。';
        }
        
        // 显示成功消息
        this.showMessage(`
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        // 如果有成功的结果，可以显示查看按钮
        if (successCount > 0 && task.results) {
            console.log('任务结果:', task.results);
        }
    }

    /**
     * 刷新相关任务状态（用于处理任务间的依赖更新）
     * @param {string} completedTaskType - 已完成的任务类型
     */
    async refreshRelatedTasks(completedTaskType) {
        try {
            // 根据完成的任务类型，决定是否需要刷新其他任务
            if (completedTaskType === 'batch_analysis' || 
                completedTaskType === 'batch_unanalyzed' || 
                completedTaskType === 'archivebox_analysis') {
                
                console.log(`任务类型 ${completedTaskType} 完成，检查是否需要刷新相关任务状态`);
                
                // 清除缓存，强制重新获取任务状态
                this.taskStatusCache.clear();
                
                // 触发任务历史刷新（如果界面正在显示）
                if (typeof window.refreshTaskHistoryIfVisible === 'function') {
                    window.refreshTaskHistoryIfVisible();
                }
            }
        } catch (error) {
            console.error('刷新相关任务状态失败:', error);
        }
    }
}

// 创建全局任务管理器实例
const taskManager = new TaskManager();

// 将taskManager添加到全局窗口对象
window.taskManager = taskManager;
window.globalTaskManager = taskManager;
window.getTaskManager = () => taskManager;

// 页面加载完成后恢复任务状态
document.addEventListener('DOMContentLoaded', function() {
    taskManager.restoreTasksFromStorage();
});
