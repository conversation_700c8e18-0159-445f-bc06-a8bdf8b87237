"""
SQLite 到 MySQL 数据迁移工具
从 SQLite 数据库 Grammar.db 迁移指定表到 MySQL 数据库 cyber_TI
只迁移: crawled_data、NewRetrieval 和 analysis_results 表
"""
import os
import sys
import sqlite3
import mysql.connector
from mysql.connector import Error
import pandas as pd
from typing import List, Dict, Any, Tuple
import re
import datetime
import json

# 数据库配置
SQLITE_DB_PATH = 'Grammar.db'
MYSQL_CONFIG = {
    'host': '************',
    'user': 'likai5',
    'password': 'likai5241203',
    'database': 'cyber_TI',
    'port': 3306,
    'charset': 'utf8mb4'
}

# 指定要迁移的表
TARGET_TABLES = ['crawled_data']

# 表结构定义 - 使用 MySQL 语法
TABLE_SCHEMAS = {
    'crawled_data': """
        CREATE TABLE IF NOT EXISTS `crawled_data` (
            id INT AUTO_INCREMENT PRIMARY KEY,
            query TEXT NOT NULL,
            title TEXT,
            link VARCHAR(1024) NOT NULL,
            snippet TEXT,
            html_content LONGTEXT,
            extracted_text LONGTEXT,
            crawl_status TINYINT DEFAULT 0,
            crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """,
    'NewRetrieval': """
    CREATE TABLE IF NOT EXISTS `NewRetrieval` (
        id INT AUTO_INCREMENT PRIMARY KEY,
        search_syntax TEXT,
        use_status TINYINT DEFAULT 0
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    """,
    'analysis_results': """
    CREATE TABLE IF NOT EXISTS `analysis_results` (
        id INT AUTO_INCREMENT PRIMARY KEY,
        crawled_data_id INT NOT NULL,
        analysis_status TINYINT DEFAULT 0,
        report_summary TEXT,
        keywords TEXT,
        aliases TEXT,
        organization_type TEXT,
        
        -- 攻击模式信息
        attack_title TEXT,
        attack_date TEXT,
        attack_source TEXT,
        
        -- 时间线
        first_seen TEXT,
        last_seen TEXT,
        attack_pattern TEXT,
        
        -- TTPs (存储为JSON数组)
        ttps JSON,
        
        -- 基础设施
        domains JSON,
        urls JSON,
        hashes JSON,
        c2_protocols TEXT,
        fast_flux TINYINT,
        
        -- 攻击者信息
        actors JSON,
        
        -- 检测指标
        detection_files TEXT,
        detection_registry TEXT,
        detection_network TEXT,
        
        analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (crawled_data_id) REFERENCES crawled_data(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    """
}

def create_mysql_tables():
    """在MySQL中创建必要的表"""
    try:
        # 连接到MySQL
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        mysql_cursor = mysql_conn.cursor()
        
        # 先禁用外键检查
        mysql_cursor.execute("SET FOREIGN_KEY_CHECKS=0;")
        
        # 创建每个表
        for table_name, schema in TABLE_SCHEMAS.items():
            try:
                print(f"创建表: {table_name}")
                mysql_cursor.execute(schema)
                print(f"表 {table_name} 创建成功")
            except Error as e:
                print(f"创建表 {table_name} 时出错: {e}")
        
        # 重新启用外键检查
        mysql_cursor.execute("SET FOREIGN_KEY_CHECKS=1;")
        mysql_conn.commit()
        
        mysql_cursor.close()
        mysql_conn.close()
        print("所有表创建完成")
        return True
    except Error as e:
        print(f"创建MySQL表时出错: {e}")
        return False

def get_sqlite_data(table: str) -> List[Tuple]:
    """从SQLite数据库获取表数据"""
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        cursor = conn.cursor()
        
        # 获取表中所有数据
        cursor.execute(f"SELECT * FROM {table}")
        rows = cursor.fetchall()
        
        # 获取列名
        cursor.execute(f"PRAGMA table_info({table})")
        columns = [col[1] for col in cursor.fetchall()]
        
        conn.close()
        return columns, rows
    except sqlite3.Error as e:
        print(f"从SQLite获取数据时出错: {e}")
        return [], []

def insert_data_to_mysql(table: str, columns: List[str], rows: List[Tuple]) -> bool:
    """将数据插入到MySQL表中"""
    try:
        # 连接到MySQL
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        mysql_cursor = mysql_conn.cursor()
        
        if not rows:
            print(f"表 {table} 没有数据需要迁移")
            return True
        
        # 准备插入语句 - 使用参数化查询避免SQL注入
        placeholders = ', '.join(['%s'] * len(columns))
        columns_str = ', '.join([f"`{col}`" for col in columns])
        insert_sql = f"INSERT INTO `{table}` ({columns_str}) VALUES ({placeholders})"
        
        # 处理JSON字段和HTML内容
        json_fields = ['ttps', 'domains', 'urls', 'hashes', 'actors']
        html_fields = ['html_content', 'extracted_text']
        
        # 批量插入数据
        batch_size = 100  # 减小批量大小以降低错误风险
        success_count = 0
        
        for i in range(0, len(rows), batch_size):
            batch = rows[i:i+batch_size]
            processed_batch = []
            
            for row in batch:
                # 处理可能的特殊字段
                processed_row = list(row)
                for j, col_name in enumerate(columns):
                    # 跳过NULL值
                    if processed_row[j] is None:
                        continue
                        
                    # HTML内容处理
                    if col_name in html_fields and isinstance(processed_row[j], str):
                        # 移除特殊的非打印字符
                        processed_row[j] = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', processed_row[j])
                        # 如果内容特别长，可以考虑截断
                        if len(processed_row[j]) > 16777215:  # MEDIUMTEXT最大长度
                            processed_row[j] = processed_row[j][:16777215]
                    
                    # 确保所有字段都是安全的字符串
                    if isinstance(processed_row[j], str):
                        # MySQL连接器会自动处理字符转义，不需要手动处理
                        pass
                
                processed_batch.append(tuple(processed_row))
            
            try:
                # 执行批量插入
                mysql_cursor.executemany(insert_sql, processed_batch)
                mysql_conn.commit()
                success_count += len(batch)
                print(f"表 {table} - 已迁移 {success_count} / {len(rows)} 行")
            except Error as e:
                print(f"批量插入数据时出错: {e}")
                print("尝试逐行插入...")
                
                # 如果批量插入失败，尝试逐行插入
                for row_idx, row in enumerate(processed_batch):
                    try:
                        mysql_cursor.execute(insert_sql, row)
                        mysql_conn.commit()
                        success_count += 1
                        if row_idx % 10 == 0:  # 每10行显示一次进度
                            print(f"已插入 {success_count} 行")
                    except Error as row_e:
                        print(f"跳过一行数据: {row_e}")
                        # 打印截断的错误行以便调试
                        error_row_sample = str(row)[:100] + "..." if len(str(row)) > 100 else str(row)
                        print(f"问题数据预览: {error_row_sample}")
        
        print(f"表 {table} 数据迁移完成，成功插入 {success_count}/{len(rows)} 行")
        mysql_cursor.close()
        mysql_conn.close()
        return True
    except Error as e:
        print(f"插入数据到MySQL时出错: {e}")
        return False
def main():
    """主函数"""
    print(f"准备迁移以下表到MySQL: {', '.join(TARGET_TABLES)}")
    
    # 确认是否继续
    confirm = input(f"是否继续迁移这些表? (y/n): ")
    if confirm.lower() != 'y':
        print("迁移已取消")
        return
    
    # 先创建表结构
    print("第一步: 创建MySQL表结构")
    if not create_mysql_tables():
        print("创建表结构失败，迁移中止")
        return
    
    # 统计结果
    success = 0
    failed = 0
    
    # 处理每个表 (按依赖顺序处理，确保外键约束不会出错)
    for table in ['crawled_data', 'NewRetrieval', 'analysis_results']:
        if table not in TARGET_TABLES:
            continue
            
        print(f"\n正在处理表: {table}")
        
        # 询问是否清空表数据
        truncate = input(f"是否清空表 {table} 中的现有数据? (y/n): ")
        if truncate.lower() == 'y':
            try:
                # 连接到MySQL
                mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
                mysql_cursor = mysql_conn.cursor()
                
                # 先禁用外键检查
                mysql_cursor.execute("SET FOREIGN_KEY_CHECKS=0;")
                
                # 清空表
                mysql_cursor.execute(f"TRUNCATE TABLE `{table}`")
                mysql_conn.commit()
                
                # 重新启用外键检查
                mysql_cursor.execute("SET FOREIGN_KEY_CHECKS=1;")
                mysql_conn.commit()
                
                mysql_cursor.close()
                mysql_conn.close()
                print(f"表 {table} 已清空")
            except Error as e:
                print(f"清空表时出错: {e}")
        
        # 从SQLite获取数据
        print(f"从SQLite读取表 {table} 数据...")
        columns, rows = get_sqlite_data(table)
        
        if not columns or not rows:
            print(f"表 {table} 无数据或读取失败，跳过")
            failed += 1
            continue
        
        print(f"找到 {len(rows)} 行数据，开始迁移...")
        
        # 插入数据到MySQL
        if insert_data_to_mysql(table, columns, rows):
            success += 1
        else:
            failed += 1
    
    print(f"\n迁移完成: {success} 个表成功, {failed} 个表失败")

if __name__ == "__main__":
    main()