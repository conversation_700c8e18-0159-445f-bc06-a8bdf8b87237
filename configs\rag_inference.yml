
output_dir: "outputs/rag_test"

# 使用我们的内置 ChromaRetriever
retriever:
  module_path: "k:\\CTI\\Data_Excation\\RAG\\rag_inference"
  class_name: "ChromaRetriever"
  
  args:
    vector_db_path: "data/test/chunks/threat_intel"  
    top_k: 3  # 返回前k个最相关文档
    embedding_model: "sentence-transformer-models/ATTACK-BERT"
    # sentence-transformer-models/all-MiniLM-L6-v2



# # 混合检索器配置
# retriever:
#   module_path: "RAG.BGE"
#   class_name: "HybridRetriever"
#   args:
#     vector_db_path: "data/test/chunks/threat_intel"
#     top_k: 5
#     use_hybrid: true
#     use_reranker: true
#     sparse_weight: 0.1
#     model: "BAAI/bge-m3"
#     reranker: "BAAI/bge-m3"


# LLM客户端配置
llm_client:
  module_path: pikerag.llm_client
  class_name: DeepSeekV3Client
  args:
    api_key: "***********************************"
  llm_config:
    model: deepseek-chat
    temperature: 0

# # 检索器配置
# retriever:
#   module_path: pikerag.knowledge_retrievers
#   class_name: QaChunkRetriever
#   args:
#     retrieve_k: 5
#     retrieve_score_threshold: 0.2
    
#     # 查询处理
#     retrieval_query:
#       module_path: pikerag.knowledge_retrievers.query_parsers
#       func_name: question_as_query
    
#     # 向量存储
#     vector_store:
#       collection_name: cti_collection
      
#       id_document_loading:
#         module_path: __main__
#         func_name: load_chunks_with_ids
#         args:
#           jsonl_chunk_path: data/test/tagging/output.jsonl
      
#       embedding_setting:
#         args:
#           model_name: "sentence-transformers/all-MiniLM-L6-v2"
#           model_kwargs:
#             device: "cpu"
#           encode_kwargs:
#             normalize_embeddings: True

# 提示词配置
prompt:
  module_path: pikerag.prompts.qa
  attr_name: cti_multiple_choice_qa_with_reference_protocol
  template_partial:
    knowledge_domain: CTI