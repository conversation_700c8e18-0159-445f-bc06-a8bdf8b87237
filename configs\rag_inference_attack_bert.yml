# RAG 推理配置文件 - ATT&CK BERT 专用版本
################################################################################

# Environment Variable Setting
################################################################################
dotenv_path: configs\.env

# 输出目录设置
output_dir: outputs/rag_test_attack_bert

# Logging Setting
################################################################################
log_root_dir: logs/rag_inference

# experiment_name: would be used to create log_dir = log_root_dir/experiment_name/
experiment_name: attack_bert_rag

# Retriever Setting
################################################################################
retriever:
  module_path: RAG.rag_inference  # 指向我们的自定义检索器
  class_name: ChromaRetriever
  args:
    vector_db_path: data/test/chunks  # 向量数据库路径
    top_k: 5  # 检索的最大结果数
    embedding_model: basel/ATTACK-BERT  # 使用 ATT&CK BERT 模型
    use_reranking: false  # 是否启用重排序
    similarity_threshold: 0.3  # 相似度阈值

# Prompt Protocol Setting  
################################################################################
prompt:
  module_path: pikerag.prompts.qa_generation
  attr_name: qa_generation_protocol
  template_partial:
    # 可以在这里添加默认的部分模板参数

# LLM Client Setting
################################################################################
llm_client:
  module_path: pikerag.llm_client
  class_name: DeepSeekV3Client
  args: 
    api_key: "***********************************"

  llm_config:
    model: deepseek-chat
    temperature: 0.1  # 降低随机性，提高一致性
    max_tokens: 2048
    # enable max_new_tokens when using llama model, response seems truncated without it
    # max_new_tokens: 1024

  cache_config:
    # location: will be joined with log_dir to generate the full path;
    #   if set to null, the experiment_name would be used
    location_prefix: null
    auto_dump: True

# 网络安全特定配置
################################################################################
cybersecurity_config:
  # ATT&CK BERT 特定配置
  attack_bert:
    model_name: basel/ATTACK-BERT
    device: cpu  # 或 cuda 如果有GPU
    normalize_embeddings: true
    
  # 威胁情报增强
  threat_intel_enhancement:
    enabled: true
    cve_enrichment: true  # 启用CVE信息增强
    ioc_extraction: true  # 启用IOC提取
    apt_mapping: true     # 启用APT组织映射
    
  # 语义搜索优化
  semantic_search:
    cybersecurity_weight: 0.7  # 网络安全语义权重
    general_weight: 0.3        # 通用语义权重
    
  # 专业术语处理
  terminology:
    mitre_attack_mapping: true   # 启用MITRE ATT&CK映射
    cve_normalization: true      # CVE标准化
    ioc_standardization: true    # IOC标准化
