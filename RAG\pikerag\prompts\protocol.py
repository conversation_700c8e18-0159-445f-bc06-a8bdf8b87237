# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

from dataclasses import dataclass
from typing import Any, Dict, List

from pikerag.prompts.base_parser import BaseContentParser
from pikerag.prompts.message_template import MessageTemplate


@dataclass
class CommunicationProtocol:
    template: MessageTemplate
    parser: BaseContentParser

    def template_partial(self, **kwargs) -> List[str]:
        """Partially fill in the template placeholders to update the template.

        Args:
            **kwargs: the key, value pairs for the partially fill in variables.

        Returns:
            List[str]: the remaining input variables needed to fill in for the updated template.
        """
        self.template = self.template.partial(**kwargs)
        return self.template.input_variables

    def process_input(self, content: str, **kwargs) -> List[Dict[str, str]]:
        """Fill in the placeholders in the message template to form an input message list.

        Args:
            content (str): the main content for encoding.
            kwargs (dict): the optional key-value pairs that may be used for encoding.

        Returns:
            List[Dict[str, str]]: the formatted message list for LLM chat.
        """
        encoded_content, encoded_dict = self.parser.encode(content, **kwargs)
        return self.template.format(content=encoded_content, **kwargs, **encoded_dict)

    # 增强Parser解析能力
    def parse_output(self, response):
        """解析LLM输出"""
        try:
            # 尝试识别JSON部分
            content = response
            
            # 1. 如果是字符串，检查是否包含JSON代码块
            if isinstance(content, str):
                if "```json" in content and "```" in content:
                    # 提取JSON内容
                    start_idx = content.find("```json") + 7
                    end_idx = content.rfind("```")
                    
                    if start_idx > 7 and end_idx > start_idx:
                        content = content[start_idx:end_idx].strip()
                
                # 2. 尝试直接解析JSON
                import json
                try:
                    parsed = json.loads(content)
                    
                    # 3. 检查是否包含必要字段
                    if "response" in parsed:
                        return {"answer": parsed["response"], "rationale": parsed.get("rationale", "")}
                    else:
                        # 没有标准字段时，返回整个JSON
                        return {"answer": content, "rationale": "Parsed as raw JSON"}
                except:
                    # JSON解析失败，返回原始内容
                    return {"answer": content, "rationale": "Failed to parse JSON"}
            
            # 如果是其他类型的响应对象，尝试提取文本
            try:
                if hasattr(response, "text"):
                    return {"answer": response.text, "rationale": ""}
                elif hasattr(response, "choices") and len(response.choices) > 0:
                    return {"answer": response.choices[0].message.content, "rationale": ""}
                else:
                    return {"answer": str(response), "rationale": ""}
            except:
                return {"answer": str(response), "rationale": ""}
        except Exception as e:
            print(f"[GenerationQaParser] Error: {e}")
            return {"answer": f"parsing error", "rationale": "parsing error"}
