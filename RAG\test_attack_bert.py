#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ATT&CK BERT 测试脚本
测试 ATT&CK BERT 在网络安全领域的语义理解能力
"""


from rag_inference import CybersecurityEmbeddings
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity


def test_attack_bert_vs_general_model():
    """比较 ATT&CK BERT 与通用模型在网络安全语义理解上的差异"""
    
    print("=" * 60)
    print("ATT&CK BERT vs 通用模型语义理解对比测试")
    print("=" * 60)
    
    # 网络安全相关的测试句子对
    cybersecurity_test_cases = [
        # 攻击行为描述
        ("Attacker takes a screenshot", "Attacker captures the screen"),
        ("Malware establishes persistence", "Trojan creates startup registry entries"), 
        ("Lateral movement through SMB", "Network propagation via Server Message Block"),
        ("Command and control communication", "C2 channel establishment"),
        ("Data exfiltration via DNS", "Information theft through DNS tunneling"),
        
        # APT 和威胁组织
        ("APT29 campaigns", "Cozy Bear operations"),
        ("Lazarus Group activities", "Hidden Cobra malware campaigns"),
        
        # 恶意软件家族
        ("Emotet banking trojan", "Emotet financial malware"),
        ("Ryuk ransomware deployment", "Ryuk encryption attack"),
        
        # 攻击技术 (MITRE ATT&CK)
        ("T1055 Process Injection", "Code injection into running process"),
        ("T1083 File Discovery", "Filesystem enumeration technique"),
        ("T1003 Credential Dumping", "Password hash extraction"),
    ]
    
    # 非安全相关的对照组
    general_test_cases = [
        ("I love pizza", "Pizza is my favorite food"),
        ("The weather is nice", "It's a beautiful day"),
        ("Programming is fun", "Coding brings joy"),
    ]
    
    try:
        # 初始化模型
        print("正在加载模型...")
        attack_bert = CybersecurityEmbeddings("basel/ATTACK-BERT")
        general_model = CybersecurityEmbeddings("sentence-transformers/all-MiniLM-L6-v2")
        print("✓ 模型加载完成\n")
        
        print("网络安全语义测试:")
        print("-" * 40)
        
        attack_bert_scores = []
        general_scores = []
        
        for i, (sent1, sent2) in enumerate(cybersecurity_test_cases, 1):
            # ATT&CK BERT 相似度
            attack_sim = attack_bert.similarity(sent1, sent2)
            # 通用模型相似度  
            general_sim = general_model.similarity(sent1, sent2)
            
            attack_bert_scores.append(attack_sim)
            general_scores.append(general_sim)
            
            print(f"{i:2d}. '{sent1}' <-> '{sent2}'")
            print(f"    ATT&CK BERT: {attack_sim:.4f}")
            print(f"    通用模型:    {general_sim:.4f}")
            print(f"    差异:        {attack_sim - general_sim:+.4f}")
            print()
        
        print("非安全领域对照测试:")
        print("-" * 40)
        
        for i, (sent1, sent2) in enumerate(general_test_cases, 1):
            attack_sim = attack_bert.similarity(sent1, sent2)
            general_sim = general_model.similarity(sent1, sent2)
            
            print(f"{i}. '{sent1}' <-> '{sent2}'")
            print(f"   ATT&CK BERT: {attack_sim:.4f}")
            print(f"   通用模型:    {general_sim:.4f}")
            print(f"   差异:        {attack_sim - general_sim:+.4f}")
            print()
        
        # 统计分析
        avg_attack_bert = np.mean(attack_bert_scores)
        avg_general = np.mean(general_scores)
        
        print("=" * 60)
        print("统计结果:")
        print(f"网络安全语义平均相似度:")
        print(f"  ATT&CK BERT: {avg_attack_bert:.4f}")
        print(f"  通用模型:    {avg_general:.4f}")
        print(f"  平均提升:    {avg_attack_bert - avg_general:+.4f}")
        
        if avg_attack_bert > avg_general:
            print("✓ ATT&CK BERT 在网络安全语义理解上表现更优!")
        else:
            print("⚠ 通用模型在此测试中表现更好")
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def test_cybersecurity_embedding_wrapper():
    """测试网络安全嵌入包装器的功能"""
    
    print("\n" + "=" * 60)
    print("CybersecurityEmbeddings 包装器功能测试")
    print("=" * 60)
    
    test_sentences = [
        "APT29 uses PowerShell for lateral movement",
        "Emotet spreads through malicious email attachments",
        "The attacker performed credential dumping using Mimikatz"
    ]
    
    try:
        # 测试 ATT&CK BERT
        print("测试 ATT&CK BERT 嵌入:")
        attack_bert = CybersecurityEmbeddings("basel/ATTACK-BERT")
        
        # 测试单个查询嵌入
        query_embedding = attack_bert.embed_query(test_sentences[0])
        print(f"✓ 单个查询嵌入维度: {len(query_embedding)}")
        
        # 测试批量文档嵌入
        doc_embeddings = attack_bert.embed_documents(test_sentences)
        print(f"✓ 批量文档嵌入: {len(doc_embeddings)} 个文档, 每个维度 {len(doc_embeddings[0])}")
        
        # 测试相似度计算
        similarity = attack_bert.similarity(test_sentences[0], test_sentences[1])
        print(f"✓ 语义相似度计算: {similarity:.4f}")
        
        print("✓ 所有功能测试通过!")
        
    except Exception as e:
        print(f"✗ 包装器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_threat_intel_keywords():
    """测试威胁情报关键词的语义聚类效果"""
    
    print("\n" + "=" * 60) 
    print("威胁情报关键词语义聚类测试")
    print("=" * 60)
    
    # 威胁情报关键词分组
    keyword_groups = {
        "APT组织": [
            "APT1", "APT29", "Lazarus Group", "Cozy Bear", "Fancy Bear",
            "Hidden Cobra", "Equation Group", "Dark Halo"
        ],
        "恶意软件": [
            "Emotet", "TrickBot", "Ryuk", "Maze", "Conti", 
            "Cobalt Strike", "Mimikatz", "PowerShell Empire"
        ],
        "攻击技术": [
            "spear phishing", "credential dumping", "lateral movement",
            "persistence", "privilege escalation", "data exfiltration"
        ],
        "IOC类型": [
            "IP address", "domain name", "file hash", "URL", 
            "registry key", "file path", "mutex"
        ]
    }
    
    try:
        print("正在加载 ATT&CK BERT...")
        model = CybersecurityEmbeddings("basel/ATTACK-BERT")
        
        print("计算关键词组内平均相似度:")
        print("-" * 40)
        
        for group_name, keywords in keyword_groups.items():
            similarities = []
            
            # 计算组内所有关键词对的相似度
            for i in range(len(keywords)):
                for j in range(i + 1, len(keywords)):
                    sim = model.similarity(keywords[i], keywords[j])
                    similarities.append(sim)
            
            avg_similarity = np.mean(similarities)
            print(f"{group_name:12s}: {avg_similarity:.4f} (基于 {len(similarities)} 个词对)")
        
        print("\n组间代表性关键词相似度对比:")
        print("-" * 40)
        
        # 选择每组的代表性关键词进行组间比较
        representatives = {
            "APT组织": "APT29",
            "恶意软件": "Emotet", 
            "攻击技术": "lateral movement",
            "IOC类型": "IP address"
        }
        
        group_names = list(representatives.keys())
        for i in range(len(group_names)):
            for j in range(i + 1, len(group_names)):
                group1, group2 = group_names[i], group_names[j]
                word1, word2 = representatives[group1], representatives[group2]
                sim = model.similarity(word1, word2)
                print(f"{group1} <-> {group2}: {sim:.4f} ('{word1}' <-> '{word2}')")
        
    except Exception as e:
        print(f"✗ 聚类测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("开始 ATT&CK BERT 模型测试...\n")
    
    # 运行所有测试
    test_cybersecurity_embedding_wrapper()
    test_attack_bert_vs_general_model() 
    test_threat_intel_keywords()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)
