<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTI 威胁情报分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 10px;
        }
        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .status-waiting {
            background-color: #ffc107;
            color: #212529;
        }
        .status-analyzed {
            background-color: #198754;
            color: white;
        }
        .status-failed {
            background-color: #dc3545;
            color: white;
        }
        .search-bar {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 30px;
        }
        .pagination {
            justify-content: center;
            margin-top: 30px;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin: 50px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .empty-state {
            text-align: center;
            padding: 50px 0;
            color: #6c757d;
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 15px;
        }
        .section-title {
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 30px;
            color: #343a40;
            font-weight: 600;
        }
        .table {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .table thead th {
            border-bottom: none;
            font-weight: 600;
            color: #495057;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .table td, .table th {
            vertical-align: middle;
            padding: 12px 15px;
        }

        .table .badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .nav-tabs {
            border-bottom: none;
        }
        .hidden-file-input {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 添加导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <i class="bi bi-shield-lock me-2 text-primary"></i>
                <strong>CTI 威胁情报分析系统</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/analysis"><i class="bi bi-search"></i> 分析工具</a>
                    </li>
                    <!-- 每周威胁情报 -->
                    <li class="nav-item">
                        <a class="nav-link" href="/weekly_intel"><i class="bi bi-calendar-week"></i> 每周威胁情报</a>
                    </li>
                    <!-- 爬取网站配置 -->
                    <li class="nav-item">
                        <a class="nav-link" href="/config_management"><i class="bi bi-gear"></i> 爬取网站配置</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- 间距 -->
    <div style="height: 60px;"></div>
    <div class="container">
        <!-- 搜索和过滤区域 -->
        <div class="search-bar">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" id="search-input" class="form-control" placeholder="搜索标题、内容或域名...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select id="status-filter" class="form-select">
                        <option value="all">所有状态</option>
                        <option value="0">待分析</option>
                        <option value="1">已分析</option>
                        <option value="2">分析失败</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select id="type-filter" class="form-select">
                        <option value="all">所有分析类型</option>
                        <option value="all">全面分析</option>
                        <option value="summary">内容摘要</option>
                        <option value="threats">威胁行为者</option>
                        <option value="vulns">漏洞分析</option>
                        <option value="iocs">威胁指标(IOCs)</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button id="search-btn" class="btn btn-primary w-100">
                        查找
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="loader" class="loader"></div>

        <!-- 结果区域 -->
        <div id="results-area" style="display:none;">
            <div id="reports-list">
                <!-- 报告卡片将在这里动态生成 -->
            </div>

            <!-- 分页控件 -->
            <nav aria-label="分页导航">
                <ul class="pagination" id="pagination">
                    <!-- 分页链接将在这里动态生成 -->
                </ul>
            </nav>
        </div>

        <!-- 无结果状态 -->
        <div id="empty-state" class="empty-state" style="display:none;">
            <i class="bi bi-exclamation-circle"></i>
            <h3>没有找到匹配的报告</h3>
            <p>尝试不同的搜索条件或清除过滤器</p>
            <button id="clear-filters" class="btn btn-outline-secondary mt-3">
                清除所有过滤器
            </button>
        </div>
    </div>

    <script>
        // 全局变量
        let allReports = [];
        let currentPage = 1;
        const reportsPerPage = 12;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            fetchReports();
            
            // 绑定搜索按钮点击事件
            document.getElementById('search-btn').addEventListener('click', function() {
                currentPage = 1;
                filterReports();
            });
            


            // 绑定回车键搜索
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    currentPage = 1;
                    filterReports();
                }
            });

        });

        // 获取报告数据
        function fetchReports() {
            document.getElementById('loader').style.display = 'block';
            document.getElementById('results-area').style.display = 'none';
            document.getElementById('empty-state').style.display = 'none';

            // 构建查询参数
            const searchTerm = document.getElementById('search-input').value.trim();
            const statusFilter = document.getElementById('status-filter').value;
            const typeFilter = document.getElementById('type-filter').value;

            const params = new URLSearchParams();
            if (searchTerm) {
                params.append('search', searchTerm);
                params.append('search_type', 'all');  // 始终搜索全部（标题、内容、域名）
            }
            if (statusFilter !== 'all') {
                params.append('status', statusFilter);
            }
            if (typeFilter !== 'all') {
                params.append('analysis_type', typeFilter);
            }

            // 添加分页参数
            params.append('page', currentPage);
            params.append('per_page', reportsPerPage);

            const url = '/api/analysis_records' + (params.toString() ? '?' + params.toString() : '');

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应异常');
                    }
                    return response.json();
                })
                .then(response => {
                    // 处理新的分页响应格式
                    if (response.data) {
                        allReports = response.data;
                        displayReports(response.data, response.pagination);
                    } else {
                        // 兼容旧格式
                        allReports = response;
                        displayReports(response);
                    }
                })
                .catch(error => {
                    console.error('获取报告错误:', error);
                    document.getElementById('loader').style.display = 'none';
                    document.getElementById('empty-state').style.display = 'block';
                    document.getElementById('empty-state').innerHTML = `
                        <i class="bi bi-exclamation-triangle"></i>
                        <h3>数据加载失败</h3>
                        <p>${error.message}</p>
                        <button onclick="fetchReports()" class="btn btn-primary mt-3">重试</button>
                    `;
                });
        }

        // 过滤报告 - 现在通过后端API进行搜索
        function filterReports() {
            // 重置页码并重新获取数据
            currentPage = 1;
            fetchReports();
        }

        // 显示报告列表
        function displayReports(reports, pagination = null) {
            document.getElementById('loader').style.display = 'none';

            if (reports.length === 0) {
                document.getElementById('results-area').style.display = 'none';
                document.getElementById('empty-state').style.display = 'block';
                return;
            }

            // 如果有分页信息，使用后端分页
            if (pagination) {
                // 后端已经排序和分页，直接渲染
                renderReports(reports);
                renderPagination(pagination.pages);
            } else {
                // 兼容旧的前端分页逻辑
                reports.sort((a, b) => {
                    if (!a.report_time) return 1;
                    if (!b.report_time) return -1;
                    const dateA = new Date(a.report_time);
                    const dateB = new Date(b.report_time);
                    return dateB - dateA;
                });

                const totalPages = Math.ceil(reports.length / reportsPerPage);

                if (currentPage > totalPages && totalPages > 0) {
                    currentPage = totalPages;
                }

                const startIndex = (currentPage - 1) * reportsPerPage;
                const endIndex = Math.min(startIndex + reportsPerPage, reports.length);
                const currentReports = reports.slice(startIndex, endIndex);

                renderReports(currentReports);
                renderPagination(totalPages);
            }

            document.getElementById('results-area').style.display = 'block';
            document.getElementById('empty-state').style.display = 'none';
        }

        // 渲染报告列表
        function renderReports(reports) {
            const reportsContainer = document.getElementById('reports-list');
            reportsContainer.innerHTML = '';
            
            // 创建表格
            const tableElement = document.createElement('table');
            tableElement.className = 'table table-hover';
            
            // 创建表头
            const tableHead = document.createElement('thead');
            tableHead.className = 'table-light';
            tableHead.innerHTML = `
                <tr>
                    <th scope="col" style="width: 8%">状态</th>
                    <th scope="col" style="width: 10%">日期</th>
                    <th scope="col" style="width: 40%">标题</th>
                    <th scope="col" style="width: 15%">来源</th>
                    <th scope="col" style="width: 12%">分析类型</th>
                    <th scope="col" style="width: 15%">操作</th>
                </tr>
            `;
            tableElement.appendChild(tableHead);
            
            // 创建表格主体
            const tableBody = document.createElement('tbody');
            
            reports.forEach((report, index) => {
                const statusClass = getStatusClass(report.analysis_status);
                const statusText = report.analysis_status_text || getStatusText(report.analysis_status);
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><span class="badge ${statusClass}">${statusText}</span></td>
                    <td>${formatDate(report.report_time)}</td>
                    <td title="${report.article_title}">${truncateText(report.article_title, 80)}</td>
                    <td>
                        <a href="${report.source_url}" target="_blank" title="${report.source_url}">
                            ${truncateText(getDomain(report.source_url), 15)}
                        </a>
                    </td>
                    <td>${report.analysis_type || "未分析"}</td>
                    <td>
                        <a href="/analysis/${report.id || report.crawled_data_id}?action=${report.id ? 'view' : 'analyze'}" 
                        class="btn btn-sm ${report.id ? 'btn-primary' : 'btn-outline-secondary'}">
                        ${report.id ? '查看分析' : '分析内容'}
                        </a>
                    </td>
                `;
                tableBody.appendChild(row);
            });
            
            tableElement.appendChild(tableBody);
            reportsContainer.appendChild(tableElement);
        }

        // 渲染分页控件
        function renderPagination(totalPages) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            if (totalPages <= 1) {
                return;
            }
            
            // 上一页按钮
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `
                <a class="page-link" href="#" aria-label="Previous" 
                   ${currentPage !== 1 ? 'onclick="changePage(' + (currentPage - 1) + '); return false;"' : ''}>
                    <span aria-hidden="true">&laquo;</span>
                </a>
            `;
            pagination.appendChild(prevLi);
            
            // 页码按钮
            const maxVisible = 5; // 最大显示页码数
            let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let endPage = Math.min(totalPages, startPage + maxVisible - 1);
            
            // 调整startPage以确保显示maxVisible个页码
            if (endPage - startPage + 1 < maxVisible && startPage > 1) {
                startPage = Math.max(1, endPage - maxVisible + 1);
            }
            
            // 第一页
            if (startPage > 1) {
                const firstLi = document.createElement('li');
                firstLi.className = 'page-item';
                firstLi.innerHTML = `
                    <a class="page-link" href="#" onclick="changePage(1); return false;">1</a>
                `;
                pagination.appendChild(firstLi);
                
                // 省略号
                if (startPage > 2) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'page-item disabled';
                    ellipsisLi.innerHTML = '<span class="page-link">...</span>';
                    pagination.appendChild(ellipsisLi);
                }
            }
            
            // 页码
            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
                pageLi.innerHTML = `
                    <a class="page-link" href="#" 
                       onclick="changePage(${i}); return false;">${i}</a>
                `;
                pagination.appendChild(pageLi);
            }
            
            // 最后页
            if (endPage < totalPages) {
                // 省略号
                if (endPage < totalPages - 1) {
                    const ellipsisLi = document.createElement('li');
                    ellipsisLi.className = 'page-item disabled';
                    ellipsisLi.innerHTML = '<span class="page-link">...</span>';
                    pagination.appendChild(ellipsisLi);
                }
                
                const lastLi = document.createElement('li');
                lastLi.className = 'page-item';
                lastLi.innerHTML = `
                    <a class="page-link" href="#" 
                       onclick="changePage(${totalPages}); return false;">${totalPages}</a>
                `;
                pagination.appendChild(lastLi);
            }
            
            // 下一页按钮
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `
                <a class="page-link" href="#" aria-label="Next" 
                   ${currentPage !== totalPages ? 'onclick="changePage(' + (currentPage + 1) + '); return false;"' : ''}>
                    <span aria-hidden="true">&raquo;</span>
                </a>
            `;
            pagination.appendChild(nextLi);
        }

        // 切换页码
        function changePage(page) {
            currentPage = page;
            fetchReports();  // 使用fetchReports获取指定页的数据
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 辅助函数: 获取状态类
        function getStatusClass(status) {
            switch (parseInt(status)) {
                case 0: return 'status-badge status-waiting';
                case 1: return 'status-badge status-analyzed';
                case 2: return 'status-badge status-failed';
                default: return 'status-badge bg-secondary';
            }
        }

        // 辅助函数: 获取状态文本
        function getStatusText(status) {
            switch (parseInt(status)) {
                case 0: return '待分析';
                case 1: return '已分析';
                case 2: return '分析失败';
                default: return '未知状态';
            }
        }

        // 辅助函数: 格式化日期
        function formatDate(dateString) {
            if (!dateString || dateString === '未知' || dateString === '未分析') {
                return dateString;
            }
            
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return dateString;
            }
            
            return date.toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit'
            }).replace(/\//g, '-');
        }

        // 辅助函数: 截断文本
        function truncateText(text, maxLength) {
            if (!text) return '未知';
            
            // 检查是否是文件路径
            const isFilePath = /^(file:\/\/|[a-zA-Z]:\\|\/\w+\/)/.test(text) || 
                            (text.includes('/') && !text.includes('://') && !text.includes('.'));
                            
            if (isFilePath) {
                // 对文件路径也进行截断处理
                const fileTitle = "file analysis: " + text;
                return fileTitle.length > maxLength ? 
                    fileTitle.substring(0, maxLength) + '...' : fileTitle;
            }
            
            // 正常文本截断
            return text.length > maxLength ? 
                text.substring(0, maxLength) + '...' : text;
        }

        // 辅助函数: 从URL获取域名
        function getDomain(url) {
            if (!url) return '未知';
            
            // 检查是否是本地文件路径
            const isFilePath = /^(file:\/\/|[a-zA-Z]:\\|\/\w+\/)/.test(url) || 
                            !url.includes('://') && url.includes('/') && !url.includes('.');
                            
            if (isFilePath) return 'file analysis';
            
            try {
                const urlObject = new URL(url);
                return urlObject.hostname;
            } catch (e) {
                // 处理无效URL
                if (url.includes('.') && !url.startsWith('/')) {
                    // 可能是缺少协议的URL
                    try {
                        const urlWithProtocol = 'http://' + url;
                        const hostname = new URL(urlWithProtocol).hostname;
                        return hostname;
                    } catch {
                        return url;
                    }
                }
                return url;
            }
        }
    </script>
</body>
</html>