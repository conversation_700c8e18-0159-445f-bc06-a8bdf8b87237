{"chunk_id": "line-1", "filename": "mitre_software_v17.txt", "content": "3PARA RAT is a remote access tool (RAT) programmed in C++ that has been used by Putter Panda.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-2", "filename": "mitre_software_v17.txt", "content": "4H RAT is malware that has been used by Putter Panda since at least 2007.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-3", "filename": "mitre_software_v17.txt", "content": "AADInternals is a PowerShell-based framework for administering, enumerating, and exploiting Azure Active Directory. The tool is publicly available on GitHub.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-4", "filename": "mitre_software_v17.txt", "content": "ABK is a downloader that has been used by BRONZE BUTLER since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-5", "filename": "mitre_software_v17.txt", "content": "AbstractEmu is mobile malware that was first seen in Google Play and other third-party stores in October 2021. It was discovered in 19 Android applications, of which at least 7 abused known Android exploits for obtaining root permissions. AbstractEmu was observed primarily impacting users in the United States, however victims are believed to be across a total of 17 countries.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-6", "filename": "mitre_software_v17.txt", "content": "ACAD/Medre.A is a worm that steals operational information. The worm collects AutoCAD files with drawings. ACAD/Medre.A has the capability to be used for industrial espionage.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-7", "filename": "mitre_software_v17.txt", "content": "AcidPour is a variant of AcidRain designed to impact a wider range of x86 architecture Linux devices. AcidPour is an x86 ELF binary that expands on the targeted devices and locations in AcidRain by including items such as Unsorted Block Image (UBI), Deice Mapper (DM), and various flash memory references. Based on this expanded targeting, AcidPour can impact a variety of device types including IoT, networking, and ICS embedded device types. AcidPour is a wiping payload associated with the Sandworm Team threat actor, and potentially linked to attacks against Ukrainian internet service providers (ISPs) in 2023.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-8", "filename": "mitre_software_v17.txt", "content": "AcidRain is an ELF binary targeting modems and routers using MIPS architecture. AcidRain is associated with the ViaSat KA-SAT communication outage that took place during the initial phases of the 2022 full-scale invasion of Ukraine. Analysis indicates overlap with another network device-targeting malware, VPNFilter, associated with Sandworm Team. US and European government sources linked AcidRain to Russian government entities, while Ukrainian government sources linked AcidRain specifically to Sandworm Team.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-9", "filename": "mitre_software_v17.txt", "content": "Action RAT is a  remote access tool written in Delphi that has been used by SideCopy since at least December 2021 against Indian and Afghani government personnel.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-10", "filename": "mitre_software_v17.txt", "content": "adbupd is a backdoor used by PLATINUM that is similar to Dipsind.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-11", "filename": "mitre_software_v17.txt", "content": "AdFind is a free command-line query tool that can be used for gathering information from Active Directory.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-12", "filename": "mitre_software_v17.txt", "content": "Adups is software that was pre-installed onto Android devices, including those made by BLU Products. The software was reportedly designed to help a Chinese phone manufacturer monitor user behavior, transferring sensitive data to a Chinese server.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-13", "filename": "mitre_software_v17.txt", "content": "ADVSTORESHELL is a spying backdoor that has been used by APT28 from at least 2012 to 2016. It is generally used for long-term espionage and is deployed on targets deemed interesting after a reconnaissance phase.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-14", "filename": "mitre_software_v17.txt", "content": "Agent Smith is mobile malware that generates financial gain by replacing legitimate applications on devices with malicious versions that include fraudulent ads. As of July 2019 Agent Smith had infected around 25 million devices, primarily targeting India though effects had been observed in other Asian countries as well as Saudi Arabia, the United Kingdom, and the United States.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-15", "filename": "mitre_software_v17.txt", "content": "Agent Tesla is a spyware Trojan written for the .NET framework that has been observed since at least 2014.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-16", "filename": "mitre_software_v17.txt", "content": "Agent.btz is a worm that primarily spreads itself via removable devices such as USB drives. It reportedly infected U.S. military networks in 2008.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-17", "filename": "mitre_software_v17.txt", "content": "AhRat is an Android remote access tool based on the open-source AhMyth remote access tool. AhRat initially spread in August 2022 on the Google Play Store via an update containing malicious code to the previously benign application, \"iRecorder – Screen Recorder,\" which itself was released in September 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-18", "filename": "mitre_software_v17.txt", "content": "Akira ransomware, written in C++, is most prominently (but not exclusively) associated with the ransomware-as-a-service entity Akira. Akira ransomware has been used in attacks across North America, Europe, and Australia, with a focus on critical infrastructure sectors including manufacturing, education, and IT services. Akira ransomware employs hybrid encryption and threading to increase the speed and efficiency of encryption and runtime arguments for tailored attacks. Notable variants include Rust-based Megazord for targeting Windows and Akira _v2 for targeting VMware ESXi servers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-19", "filename": "mitre_software_v17.txt", "content": "Akira _v2 is a Rust-based variant of Akira ransomware that has been in use since at least 2024. Akira _v2 is designed to target VMware ESXi servers and includes a new command-line argument set and other expanded capabilities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-20", "filename": "mitre_software_v17.txt", "content": "Allwinner is a company that supplies processors used in Android tablets and other devices. A Linux kernel distributed by Allwinner for use on these devices reportedly contained a backdoor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-21", "filename": "mitre_software_v17.txt", "content": "Amadey is a Trojan bot that has been used since at least October 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-22", "filename": "mitre_software_v17.txt", "content": "Anchor is one of a family of backdoor malware that has been used in conjunction with TrickBot on selected high profile targets since at least 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-23", "filename": "mitre_software_v17.txt", "content": "Android/AdDisplay.Ashas is a variant of adware that has been distributed through multiple apps in the Google Play Store.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-24", "filename": "mitre_software_v17.txt", "content": "Android/Chuli.A is Android malware that was delivered to activist groups via a spearphishing email with an attachment.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-25", "filename": "mitre_software_v17.txt", "content": "Android/SpyAgent is a variant of spyware in the MoqHao phishing campaign primarily targeting Korean and Japanese users. Fake security applications were used to target Japanese users, while fake police applications were used to target Korean users. Both fake applications have common C2 commands and share the same crash report key on a cloud service.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-26", "filename": "mitre_software_v17.txt", "content": "AndroidOS/MalLocker.B is a variant of a ransomware family targeting Android devices. It prevents the user from interacting with the UI by displaying a screen containing a ransom note over all other windows.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-27", "filename": "mitre_software_v17.txt", "content": "ANDROIDOS_ANSERVER.A is Android malware that is unique because it uses encrypted content within a blog site for command and control.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-28", "filename": "mitre_software_v17.txt", "content": "ANDROMEDA is commodity malware that was widespread in the early 2010's and continues to be observed in infections across a wide variety of industries. During the 2022 C0026 campaign, threat actors re-registered expired ANDROMEDA C2 domains to spread malware to select targets in Ukraine.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-29", "filename": "mitre_software_v17.txt", "content": "AndroRAT is an open-source remote access tool for Android devices. AndroRAT is capable of collecting data, such as device location, call logs, etc., and is capable of executing actions, such as sending SMS messages and taking pictures. It is originally available through the The404Hacking Github repository.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-30", "filename": "mitre_software_v17.txt", "content": "Anubis is Android malware that was originally used for cyber espionage, and has been retooled as a banking trojan.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-31", "filename": "mitre_software_v17.txt", "content": "Apostle is malware that has functioned as both a wiper and, in more recent versions, as ransomware.  Apostle is written in .NET and shares various programming and functional overlaps with IPsec Helper.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-32", "filename": "mitre_software_v17.txt", "content": "AppleJeus is a family of downloaders initially discovered in 2018 embedded within trojanized cryptocurrency applications. AppleJeus has been used by Lazarus Group, targeting companies in the energy, finance, government, industry, technology, and telecommunications sectors, and several countries including the United States, United Kingdom, South Korea, Australia, Brazil, New Zealand, and Russia. AppleJeus has been used to distribute the FALLCHILL RAT.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-33", "filename": "mitre_software_v17.txt", "content": "AppleSeed is a backdoor that has been used by Kimsuky to target South Korean government, academic, and commercial  targets since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-34", "filename": "mitre_software_v17.txt", "content": "Aria-body is a custom backdoor that has been used by Naikon since approximately 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-35", "filename": "mitre_software_v17.txt", "content": "Arp displays and modifies information about a system's Address Resolution Protocol (ARP) cache.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-36", "filename": "mitre_software_v17.txt", "content": "Asacub is a banking trojan that attempts to steal money from victims’ bank accounts. It attempts to do this by initiating a wire transfer via SMS message from compromised devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-37", "filename": "mitre_software_v17.txt", "content": "ASPXSpy is a Web shell. It has been modified by Threat Group-3390 actors to create the ASPXTool version.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-38", "filename": "mitre_software_v17.txt", "content": "Astaroth is a Trojan and information stealer known to affect companies in Europe, Brazil, and throughout Latin America. It has been known publicly since at least late 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-39", "filename": "mitre_software_v17.txt", "content": "AsyncRAT is an open-source remote access tool originally available through the NYANxCAT Github repository that has been used in malicious campaigns.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-40", "filename": "mitre_software_v17.txt", "content": "at is used to schedule tasks on a system to run at a specified date or time.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-41", "filename": "mitre_software_v17.txt", "content": "Attor is a Windows-based espionage platform that has been seen in use since 2013. Attor has a loadable plugin architecture to customize functionality for specific targets.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-42", "filename": "mitre_software_v17.txt", "content": "attrib is a Windows utility used to display, set or remove attributes assigned to files or directories.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-43", "filename": "mitre_software_v17.txt", "content": "AuditCred is a malicious DLL that has been used by Lazarus Group during their 2018 attacks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-44", "filename": "mitre_software_v17.txt", "content": "AuTo Stealer is malware written in C++ has been used by SideCopy since at least December 2021 to target government agencies and personnel in India and Afghanistan.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-45", "filename": "mitre_software_v17.txt", "content": "AutoIt backdoor is malware that has been used by the actors responsible for the MONSOON campaign. The actors frequently used it in weaponized .pps files exploiting CVE-2014-6352.  This malware makes use of the legitimate scripting language for Windows GUI automation with the same name.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-46", "filename": "mitre_software_v17.txt", "content": "Avaddon is ransomware written in C++ that has been offered as Ransomware-as-a-Service (RaaS) since at least June 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-47", "filename": "mitre_software_v17.txt", "content": "Avenger is a downloader that has been used by BRONZE BUTLER since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-48", "filename": "mitre_software_v17.txt", "content": "AvosLocker is ransomware written in C++ that has been offered via the Ransomware-as-a-Service (RaaS) model. It was first observed in June 2021 and has been used against financial services, critical manufacturing, government facilities, and other critical infrastructure sectors in the United States. As of March 2022, AvosLocker had also been used against organizations in Belgium, Canada, China, Germany, Saudi Arabia, Spain, Syria, Taiwan, Turkey, the United Arab Emirates, and the United Kingdom.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-49", "filename": "mitre_software_v17.txt", "content": "Azorult is a commercial Trojan that is used to steal information from compromised hosts. Azorult has been observed in the wild as early as 2016.In July 2018, Azorult was seen used in a spearphishing campaign against targets in North America. Azorult has been seen used for cryptocurrency theft.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-50", "filename": "mitre_software_v17.txt", "content": "Babuk is a Ransomware-as-a-service (RaaS) malware that has been used since at least 2021. The operators of Babuk employ a \"Big Game Hunting\" approach to targeting major enterprises and operate a leak site to post stolen data as part of their extortion scheme.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-51", "filename": "mitre_software_v17.txt", "content": "BabyShark is a Microsoft Visual Basic (VB) script-based malware family that is believed to be associated with several North Korean campaigns.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-52", "filename": "mitre_software_v17.txt", "content": "BackConfig is a custom Trojan with a flexible plugin architecture that has been used by Patchwork.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-53", "filename": "mitre_software_v17.txt", "content": "Backdoor.Oldrea is a modular backdoor that used by Dragonfly against energy companies since at least 2013. Backdoor.Oldrea was distributed via supply chain compromise, and included specialized modules to enumerate and map ICS-specific systems, processes, and protocols.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-54", "filename": "mitre_software_v17.txt", "content": "BACKSPACE is a backdoor used by APT30 that dates back to at least 2005.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-55", "filename": "mitre_software_v17.txt", "content": "Bad Rabbit is a self-propagating ransomware that affected the Ukrainian transportation sector in 2017. Bad Rabbit has also targeted organizations and consumers in Russia.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-56", "filename": "mitre_software_v17.txt", "content": "BADCALL is a Trojan malware variant used by the group Lazarus Group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-57", "filename": "mitre_software_v17.txt", "content": "BADFLICK is a backdoor used by Leviathan in spearphishing campaigns first reported in 2018 that targeted the U.S. engineering and maritime industries.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-58", "filename": "mitre_software_v17.txt", "content": "BADHATCH is a backdoor that has been utilized by FIN8 since at least 2019. BADHATCH has been used to target the insurance, retail, technology, and chemical industries in the United States, Canada, South Africa, Panama, and Italy.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-59", "filename": "mitre_software_v17.txt", "content": "BADNEWS is malware that has been used by the actors responsible for the Patchwork campaign. Its name was given due to its use of RSS feeds, forums, and blogs for command and control.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-60", "filename": "mitre_software_v17.txt", "content": "BadPatch is a Windows Trojan that was used in a Gaza Hackers-linked campaign.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-61", "filename": "mitre_software_v17.txt", "content": "Bandook is a commercially available RAT, written in Delphi and C++, that has been available since at least 2007. It has been used against government, financial, energy, healthcare, education, IT, and legal organizations in the US, South America, Europe, and Southeast Asia. Bandook has been used by Dark Caracal, as well as in a separate campaign referred to as \"Operation Manul\".", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-62", "filename": "mitre_software_v17.txt", "content": "Bankshot is a remote access tool (RAT) that was first reported by the Department of Homeland Security in December of 2017. In 2018, Lazarus Group used the Bankshot implant in attacks against the Turkish financial sector.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-63", "filename": "mitre_software_v17.txt", "content": "Bazar is a downloader and backdoor that has been used since at least April 2020, with infections primarily against professional services, healthcare, manufacturing, IT, logistics and travel companies across the US and Europe. Bazar reportedly has ties to TrickBot campaigns and can be used to deploy additional malware, including ransomware, and to steal sensitive data.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-64", "filename": "mitre_software_v17.txt", "content": "BBK is a downloader that has been used by BRONZE BUTLER since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-65", "filename": "mitre_software_v17.txt", "content": "BBSRAT is malware with remote access tool functionality that has been used in targeted compromises.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-66", "filename": "mitre_software_v17.txt", "content": "BendyBear is an x64 shellcode for a stage-zero implant designed to download malware from a C2 server. First discovered in August 2020, BendyBear shares a variety of features with Waterbear, malware previously attributed to the Chinese cyber espionage group BlackTech.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-67", "filename": "mitre_software_v17.txt", "content": "BFG Agonizer is a wiper related to the open-source project CRYLINE-v.5.0. The malware is associated with wiping operations conducted by the Agrius threat actor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-68", "filename": "mitre_software_v17.txt", "content": "Binary Validator is a Mach-O binary file used during Operation Triangulation. Binary Validator first collects information about the device, such as the device's phone number and a list of installed applications, before the deployment of the TriangleDB implant.  After the actions are completed and the data is collected, Binary Validator encrypts and sends the data to the C2 server, and in turn, the C2 server sends the TriangleDB implant.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-69", "filename": "mitre_software_v17.txt", "content": "BISCUIT is a backdoor that has been used by APT1 since as early as 2007.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-70", "filename": "mitre_software_v17.txt", "content": "Bisonal is a remote access tool (RAT) that has been used by Tonto Team against public and private sector organizations in Russia, South Korea, and Japan since at least December 2010.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-71", "filename": "mitre_software_v17.txt", "content": "BitPaymer is a ransomware variant first observed in August 2017 targeting hospitals in the U.K. BitPaymer uses a unique encryption key, ransom note, and contact information for each operation. BitPaymer has several indicators suggesting overlap with the Dridex malware and is often delivered via Dridex.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-72", "filename": "mitre_software_v17.txt", "content": "BITSAdmin is a command line tool used to create and manage BITS Jobs.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-73", "filename": "mitre_software_v17.txt", "content": "Black Basta is ransomware written in C++ that has been offered within the ransomware-as-a-service (RaaS) model since at least April 2022; there are variants that target Windows and VMWare ESXi servers. Black Basta operations have included the double extortion technique where in addition to demanding ransom for decrypting the files of targeted organizations the cyber actors also threaten to post sensitive information to a leak site if the ransom is not paid. Black Basta affiliates have targeted multiple high-value organizations, with the largest number of victims based in the U.S. Based on similarities in TTPs, leak sites, payment sites, and negotiation tactics, security researchers assess the Black Basta RaaS operators could include current or former members of the Conti group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-74", "filename": "mitre_software_v17.txt", "content": "BlackByte 2.0 Ransomware is a replacement for BlackByte Ransomware. Unlike BlackByte Ransomware, BlackByte 2.0 Ransomware does not have a common key for victim decryption. BlackByte 2.0 Ransomware remains uniquely associated with BlackByte operations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-75", "filename": "mitre_software_v17.txt", "content": "BlackByte Ransomware is uniquely associated with BlackByte operations. BlackByte Ransomware used a common key for infections, allowing for the creation of a universal decryptor. BlackByte Ransomware was replaced in BlackByte operations by BlackByte 2.0 Ransomware by 2023.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-76", "filename": "mitre_software_v17.txt", "content": "BlackCat is ransomware written in Rust that has been offered via the Ransomware-as-a-Service (RaaS) model. First observed November 2021, BlackCat has been used to target multiple sectors and organizations in various countries and regions in Africa, the Americas, Asia, Australia, and Europe.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-77", "filename": "mitre_software_v17.txt", "content": "BLACKCOFFEE is malware that has been used by several Chinese groups since at least 2013.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-78", "filename": "mitre_software_v17.txt", "content": "BlackEnergy is a malware toolkit that has been used by both criminal and APT actors. It dates back to at least 2007 and was originally designed to create botnets for use in conducting Distributed Denial of Service (DDoS) attacks, but its use has evolved to support various plug-ins. It is well known for being used during the confrontation between Georgia and Russia in 2008, as well as in targeting Ukrainian institutions. Variants include BlackEnergy 2 and BlackEnergy 3.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-79", "filename": "mitre_software_v17.txt", "content": "BlackMould is a web shell based on China Chopper for servers running Microsoft IIS. First reported in December 2019, it has been used in malicious campaigns by GALLIUM against telecommunication providers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-80", "filename": "mitre_software_v17.txt", "content": "BLINDINGCAN is a remote access Trojan that has been used by the North Korean government since at least early 2020 in cyber operations against defense, engineering, and government organizations in Western Europe and the US.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-81", "filename": "mitre_software_v17.txt", "content": "BloodHound is an Active Directory (AD) reconnaissance tool that can reveal hidden relationships and identify attack paths within an AD environment.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-82", "filename": "mitre_software_v17.txt", "content": "BLUELIGHT is a remote access Trojan used by APT37 that was first observed in early 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-83", "filename": "mitre_software_v17.txt", "content": "BOLDMOVE is a type of backdoor malware written in C linked to People’s Republic of China operations from 2022 through 2023. BOLDMOVE includes both Windows and Linux variants, with some Linux variants specifically designed for FortiGate Firewall devices. BOLDMOVE is linked to zero-day exploitation of CVE-2022-42475 in FortiOSS SSL-VPNs. The record for BOLDMOVE only covers known Linux variants.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-84", "filename": "mitre_software_v17.txt", "content": "Bonadan is a malicious version of OpenSSH which acts as a custom backdoor. Bonadan has been active since at least 2018 and combines a new cryptocurrency-mining module with the same credential-stealing module used by the Onderon family of backdoors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-85", "filename": "mitre_software_v17.txt", "content": "BONDUPDATER is a PowerShell backdoor used by OilRig. It was first observed in November 2017 during targeting of a Middle Eastern government organization, and an updated version was observed in August 2018 being used to target a government organization with spearphishing emails.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-86", "filename": "mitre_software_v17.txt", "content": "BoomBox is a downloader responsible for executing next stage components that has been used by APT29 since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-87", "filename": "mitre_software_v17.txt", "content": "BOOSTWRITE is a loader crafted to be launched via abuse of the DLL search order of applications used by FIN7.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-88", "filename": "mitre_software_v17.txt", "content": "BOOTRASH is a Bootkit that targets Windows operating systems. It has been used by threat actors that target the financial sector.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-89", "filename": "mitre_software_v17.txt", "content": "BOULDSPY is an Android malware, detected in early 2023, with surveillance and remote-control capabilities. Analysis of exfiltrated C2 data suggests that BOULDSPY primarily targeted minority groups in Iran.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-90", "filename": "mitre_software_v17.txt", "content": "BoxCaon is a Windows backdoor that was used by IndigoZebra in a 2021 spearphishing campaign against Afghan government officials. BoxCaon's name stems from similarities shared with the malware family xCaon.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-91", "filename": "mitre_software_v17.txt", "content": "BPFDoor is a Linux based passive long-term backdoor used by China-based threat actors. First seen in 2021, BPFDoor is named after its usage of Berkley Packet Filter (BPF) to execute single task instructions. BPFDoor supports multiple protocols for communicating with a C2 including TCP, UDP, and ICMP and can start local or reverse shells that bypass firewalls using iptables.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-92", "filename": "mitre_software_v17.txt", "content": "BrainTest is a family of Android malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-93", "filename": "mitre_software_v17.txt", "content": "BRATA (Brazilian Remote Access Tool, Android), is an evolving Android malware strain, detected in late 2018 and again in late 2021. Originating in Brazil, BRATA was later also found in the UK, Poland, Italy, Spain, and USA, where it is believed to have targeted financial institutions such as banks. There are currently three known variants of BRATA.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-94", "filename": "mitre_software_v17.txt", "content": "Brave Prince is a Korean-language implant that was first observed in the wild in December 2017. It contains similar code and behavior to Gold Dragon, and was seen along with Gold Dragon and RunningRAT in operations surrounding the 2018 Pyeongchang Winter Olympics.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-95", "filename": "mitre_software_v17.txt", "content": "Bread was a large-scale billing fraud malware family known for employing many different cloaking and obfuscation techniques in an attempt to continuously evade Google Play Store’s malware detection. 1,700 unique Bread apps were detected and removed from the Google Play Store before being downloaded by users.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-96", "filename": "mitre_software_v17.txt", "content": "Briba is a trojan used by Elderwood to open a backdoor and download files on to compromised hosts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-97", "filename": "mitre_software_v17.txt", "content": "Brute Ratel C4 is a commercial red-teaming and adversarial attack simulation tool that first appeared in December 2020. Brute Ratel C4 was specifically designed to avoid detection by endpoint detection and response (EDR) and antivirus (AV) capabilities, and deploys agents called badgers to enable arbitrary command execution for lateral movement, privilege escalation, and persistence. In September 2022, a cracked version of Brute Ratel C4 was leaked in the cybercriminal underground, leading to its use by threat actors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-98", "filename": "mitre_software_v17.txt", "content": "BS2005 is malware that was used by Ke3chang in spearphishing campaigns since at least 2011.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-99", "filename": "mitre_software_v17.txt", "content": "BUBBLEWRAP is a full-featured, second-stage backdoor used by the admin@338 group. It is set to run when the system boots and includes functionality to check, upload, and register plug-ins that can further enhance its capabilities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-100", "filename": "mitre_software_v17.txt", "content": "build_downer is a downloader that has been used by BRONZE BUTLER since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-101", "filename": "mitre_software_v17.txt", "content": "Bumblebee is a custom loader written in C++ that has been used by multiple threat actors, including possible initial access brokers, to download and execute additional payloads since at least March 2022. Bumblebee has been linked to ransomware operations including Conti, Quantum, and Mountlocker and derived its name from the appearance of \"bumblebee\" in the user-agent.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-102", "filename": "mitre_software_v17.txt", "content": "Bundlore is adware written for macOS that has been in use since at least 2015. Though categorized as adware, Bundlore has many features associated with more traditional backdoors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-103", "filename": "mitre_software_v17.txt", "content": "BUSHWALK is a web shell written in Perl that was inserted into the legitimate querymanifest.cgi file on compromised Ivanti Connect Secure VPNs during Cutting Edge.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-104", "filename": "mitre_software_v17.txt", "content": "BusyGasper is Android spyware that has been in use since May 2016. There have been less than 10 victims, all who appear to be located in Russia, that were all infected via physical access to the device.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-105", "filename": "mitre_software_v17.txt", "content": "Cachedump is a publicly-available tool that program extracts cached password hashes from a system’s registry.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-106", "filename": "mitre_software_v17.txt", "content": "CaddyWiper is a destructive data wiper that has been used in attacks against organizations in Ukraine since at least March 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-107", "filename": "mitre_software_v17.txt", "content": "Cadelspy is a backdoor that has been used by APT39.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-108", "filename": "mitre_software_v17.txt", "content": "CALENDAR is malware used by APT1 that mimics legitimate Gmail Calendar traffic.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-109", "filename": "mitre_software_v17.txt", "content": "Calisto is a macOS Trojan that opens a backdoor on the compromised machine. Calisto is believed to have first been developed in 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-110", "filename": "mitre_software_v17.txt", "content": "CallMe is a Trojan designed to run on Apple OSX. It is based on a publicly available tool called Tiny SHell.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-111", "filename": "mitre_software_v17.txt", "content": "Cannon is a Trojan with variants written in C# and Delphi. It was first observed in April 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-112", "filename": "mitre_software_v17.txt", "content": "Carbanak is a full-featured, remote backdoor used by a group of the same name (Carbanak). It is intended for espionage, data exfiltration, and providing remote access to infected machines.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-113", "filename": "mitre_software_v17.txt", "content": "Carberp is a credential and information stealing malware that has been active since at least 2009. Carberp's source code was leaked online in 2013, and subsequently used as the foundation for the Carbanak backdoor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-114", "filename": "mitre_software_v17.txt", "content": "Carbon is a sophisticated, second-stage backdoor and framework that can be used to steal sensitive information from victims. Carbon has been selectively used by Turla to target government and foreign affairs-related organizations in Central Asia.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-115", "filename": "mitre_software_v17.txt", "content": "CarbonSteal is one of a family of four surveillanceware tools that share a common C2 infrastructure. CarbonSteal primarily deals with audio surveillance.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-116", "filename": "mitre_software_v17.txt", "content": "Cardinal RAT is a potentially low volume remote access trojan (RAT) observed since December 2015. Cardinal RAT is notable for its unique utilization of uncompiled C# source code and the Microsoft Windows built-in csc.exe compiler.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-117", "filename": "mitre_software_v17.txt", "content": "CARROTBALL is an FTP downloader utility that has been in use since at least 2019. CARROTBALL has been used as a downloader to install SYSCON.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-118", "filename": "mitre_software_v17.txt", "content": "CARROTBAT is a customized dropper that has been in use since at least 2017. CARROTBAT has been used to install SYSCON and has infrastructure overlap with KONNI.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-119", "filename": "mitre_software_v17.txt", "content": "Catchamas is a Windows Trojan that steals information from compromised systems.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-120", "filename": "mitre_software_v17.txt", "content": "Caterpillar WebShell is a self-developed Web Shell tool created by the group Volatile Cedar.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-121", "filename": "mitre_software_v17.txt", "content": "CCBkdr is malware that was injected into a signed version of CCleaner and distributed from CCleaner's distribution website.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-122", "filename": "mitre_software_v17.txt", "content": "ccf32 is data collection malware that has been used since at least February 2019, most notably during the FunnyDream campaign; there is also a similar x64 version.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-123", "filename": "mitre_software_v17.txt", "content": "cd00r is an open-source backdoor for UNIX and UNIX-variant operating systems that was orginally released in 2000. cd00r source code is primarily based on a packet-capturing program as it utilizes a sniffer to listen for specific sequences of network traffic or \"secret knock\" before executing the attacker's code.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-124", "filename": "mitre_software_v17.txt", "content": "Cerberus is a banking trojan whose usage can be rented on underground forums and marketplaces. Prior to being available to rent, the authors of Cerberus claim was used in private operations for two years.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-125", "filename": "mitre_software_v17.txt", "content": "certutil is a command-line utility that can be used to obtain certificate authority information and configure Certificate Services.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-126", "filename": "mitre_software_v17.txt", "content": "Chaes is a multistage information stealer written in several programming languages that collects login credentials, credit card numbers, and other financial information. Chaes was first observed in 2020, and appears to primarily target victims in Brazil as well as other e-commerce customers in Latin America.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-127", "filename": "mitre_software_v17.txt", "content": "Chameleon is an Android banking trojan that can leverage Android’s Accessibility Services to perform malicious activities. Believed to have been first active in January 2023, Chameleon has been observed targeting users in Australia and Poland by masquerading as official apps.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-128", "filename": "mitre_software_v17.txt", "content": "Chaos is Linux malware that compromises systems by brute force attacks against SSH services. Once installed, it provides a reverse shell to its controllers, triggered by unsolicited packets.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-129", "filename": "mitre_software_v17.txt", "content": "Charger is Android malware that steals steals contacts and SMS messages from the user's device. It can also lock the device and demand ransom payment if it receives admin permissions.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-130", "filename": "mitre_software_v17.txt", "content": "CharmPower is a PowerShell-based, modular backdoor that has been used by Magic Hound since at least 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-131", "filename": "mitre_software_v17.txt", "content": "ChChes is a Trojan that appears to be used exclusively by menuPass. It was used to target Japanese organizations in 2016. Its lack of persistence methods suggests it may be intended as a first-stage tool.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-132", "filename": "mitre_software_v17.txt", "content": "Cheerscrypt is a ransomware that was developed by Cinnamon Tempest and has been used in attacks against ESXi and Windows environments since at least 2022. Cheerscrypt was derived from the leaked Babuk source code and has infrastructure overlaps with deployments of Night Sky ransomware, which was also derived from Babuk.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-133", "filename": "mitre_software_v17.txt", "content": "CHEMISTGAMES is a modular backdoor that has been deployed by Sandworm Team.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-134", "filename": "mitre_software_v17.txt", "content": "Cherry Picker is a point of sale (PoS) memory scraper.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-135", "filename": "mitre_software_v17.txt", "content": "CHIMNEYSWEEP is a backdoor malware that was deployed during HomeLand Justice along with ROADSWEEP ransomware, and has been used to target Farsi and Arabic speakers since at least 2012.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-136", "filename": "mitre_software_v17.txt", "content": "China Chopper is a Web Shell hosted on Web servers to provide access back into an enterprise network that does not rely on an infected system calling back to a remote command and control server. It has been used by several threat groups.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-137", "filename": "mitre_software_v17.txt", "content": "Chinoxy is a backdoor that has been used since at least November 2018, during the FunnyDream campaign, to gain persistence and drop additional payloads. According to security researchers, Chinoxy has been used by Chinese-speaking threat actors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-138", "filename": "mitre_software_v17.txt", "content": "CHOPSTICK is a malware family of modular backdoors used by APT28. It has been used since at least 2012 and is usually dropped on victims as second-stage malware, though it has been used as first-stage malware in several cases. It has both Windows and Linux variants.     It is tracked separately from the X-Agent for Android.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-139", "filename": "mitre_software_v17.txt", "content": "Chrommme is a backdoor tool written using the Microsoft Foundation Class (MFC) framework that was first reported in June 2021; security researchers noted infrastructure overlaps with Gelsemium malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-140", "filename": "mitre_software_v17.txt", "content": "cipher.exe is a native Microsoft utility that manages encryption of directories and files on NTFS (New Technology File System) partitions by using the Encrypting File System (EFS).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-141", "filename": "mitre_software_v17.txt", "content": "Circles reportedly takes advantage of Signaling System 7 (SS7) weaknesses, the protocol suite used to route phone calls, to both track the location of mobile devices and intercept voice calls and SMS messages. It can be connected to a telecommunications company’s infrastructure or purchased as a cloud service. Circles has reportedly been linked to the NSO Group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-142", "filename": "mitre_software_v17.txt", "content": "Clambling is a modular backdoor written in C++ that has been used by Threat Group-3390 since at least 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-143", "filename": "mitre_software_v17.txt", "content": "Clop is a ransomware family that was first observed in February 2019 and has been used against retail, transportation and logistics, education, manufacturing, engineering, automotive, energy, financial, aerospace, telecommunications, professional and legal services, healthcare, and high tech industries. Clop is a variant of the CryptoMix ransomware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-144", "filename": "mitre_software_v17.txt", "content": "CloudDuke is malware that was used by APT29 in 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-145", "filename": "mitre_software_v17.txt", "content": "cmd is the Windows command-line interpreter that can be used to interact with systems and execute other processes and utilities. Cmd.exe contains native functionality to perform many operations to interact with the system, including listing files in a directory (e.g., dir ), deleting files (e.g., del ), and copying files (e.g., copy ).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-146", "filename": "mitre_software_v17.txt", "content": "COATHANGER is a remote access tool (RAT) targeting FortiGate networking appliances. First used in 2023 in targeted intrusions against military and government entities in the Netherlands along with other victims, COATHANGER was disclosed in early 2024, with a high confidence assessment linking this malware to a state-sponsored entity in the People's Republic of China. COATHANGER is delivered after gaining access to a FortiGate device, with in-the-wild observations linked to exploitation of CVE-2022-42475. The name COATHANGER is based on a unique string in the malware used to encrypt configuration files on disk: \"She took his coat and hung it up\".", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-147", "filename": "mitre_software_v17.txt", "content": "Cobalt Strike is a commercial, full-featured, remote access tool that bills itself as \"adversary simulation software designed to execute targeted attacks and emulate the post-exploitation actions of advanced threat actors\". Cobalt Strike’s interactive post-exploit capabilities cover the full range of ATT&CK tactics, all executed within a single, integrated system.In addition to its own capabilities, Cobalt Strike leverages the capabilities of other well-known tools such as Metasploit and Mimikatz.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-148", "filename": "mitre_software_v17.txt", "content": "Cobian RAT is a backdoor, remote access tool that has been observed since 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-149", "filename": "mitre_software_v17.txt", "content": "CoinTicker is a malicious application that poses as a cryptocurrency price ticker and installs components of the open source backdoors EvilOSX and EggShell.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-150", "filename": "mitre_software_v17.txt", "content": "Comnie is a remote backdoor which has been used in attacks in East Asia.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-151", "filename": "mitre_software_v17.txt", "content": "ComRAT is a second stage implant suspected of being a descendant of Agent.btz and used by Turla. The first version of ComRAT was identified in 2007, but the tool has undergone substantial development for many years since.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-152", "filename": "mitre_software_v17.txt", "content": "Concipit1248 is iOS spyware that was discovered using the same name as the developer of the Android spyware Corona Updates. Further investigation revealed that the two pieces of software contained the same C2 URL and similar functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-153", "filename": "mitre_software_v17.txt", "content": "Conficker is a computer worm first detected in October 2008 that targeted Microsoft Windows using the MS08-067 Windows vulnerability to spread. In 2016, a variant of Conficker made its way on computers and removable disk drives belonging to a nuclear power plant.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-154", "filename": "mitre_software_v17.txt", "content": "ConnectWise is a legitimate remote administration tool that has been used since at least 2016 by threat actors including MuddyWater and GOLD SOUTHFIELD to connect to and conduct lateral movement in target environments.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-155", "filename": "mitre_software_v17.txt", "content": "Conti is a Ransomware-as-a-Service (RaaS) that was first observed in December 2019. Conti has been deployed via TrickBot and used against major corporations and government agencies, particularly those in North America. As with other ransomware families, actors using Conti steal sensitive files and information from compromised networks, and threaten to publish this data unless the ransom is paid.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-156", "filename": "mitre_software_v17.txt", "content": "CookieMiner is mac-based malware that targets information associated with cryptocurrency exchanges as well as enabling cryptocurrency mining on the victim system itself. It was first discovered in the wild in 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-157", "filename": "mitre_software_v17.txt", "content": "CORALDECK is an exfiltration tool used by APT37.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-158", "filename": "mitre_software_v17.txt", "content": "CORESHELL is a downloader used by APT28. The older versions of this malware are known as SOURFACE and newer versions as CORESHELL.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-159", "filename": "mitre_software_v17.txt", "content": "Corona Updates is Android spyware that took advantage of the Coronavirus pandemic. The campaign distributing this spyware is tracked as Project Spy. Multiple variants of this spyware have been discovered to have been hosted on the Google Play Store.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-160", "filename": "mitre_software_v17.txt", "content": "CosmicDuke is malware that was used by APT29 from 2010 to 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-161", "filename": "mitre_software_v17.txt", "content": "CostaBricks is a loader that was used to deploy 32-bit backdoors in the CostaRicto campaign.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-162", "filename": "mitre_software_v17.txt", "content": "Covenant is a multi-platform command and control framework written in .NET. While designed for penetration testing and security research, the tool has also been used by threat actors such as HAFNIUM during operations. Covenant functions through a central listener managing multiple deployed \"Grunts\" that communicate back to the controller.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-163", "filename": "mitre_software_v17.txt", "content": "CozyCar is malware that was used by APT29 from 2010 to 2015. It is a modular malware platform, and its backdoor component can be instructed to download and execute a variety of modules with different functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-164", "filename": "mitre_software_v17.txt", "content": "CrackMapExec, or CME, is a post-exploitation tool developed in Python and designed for penetration testing against networks. CrackMapExec collects Active Directory information to conduct lateral movement through targeted networks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-165", "filename": "mitre_software_v17.txt", "content": "CreepyDrive is a custom implant has been used by POLONIUM since at least early 2022 for C2 with and exfiltration to actor-controlled OneDrive accounts.POLONIUM has used a similar implant called CreepyBox that relies on actor-controlled DropBox accounts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-166", "filename": "mitre_software_v17.txt", "content": "CreepySnail is a custom PowerShell implant that has been used by POLONIUM since at least 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-167", "filename": "mitre_software_v17.txt", "content": "Crimson is a remote access Trojan that has been used by Transparent Tribe since at least 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-168", "filename": "mitre_software_v17.txt", "content": "CrossRAT is a cross platform RAT.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-169", "filename": "mitre_software_v17.txt", "content": "Crutch is a backdoor designed for document theft that has been used by Turla since at least 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-170", "filename": "mitre_software_v17.txt", "content": "Cryptoistic is a backdoor, written in Swift, that has been used by Lazarus Group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-171", "filename": "mitre_software_v17.txt", "content": "CSPY Downloader is a tool designed to evade analysis and download additional payloads used by Kimsuky.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-172", "filename": "mitre_software_v17.txt", "content": "Cuba is a Windows-based ransomware family that has been used against financial institutions, technology, and logistics organizations in North and South America as well as Europe since at least December 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-173", "filename": "mitre_software_v17.txt", "content": "Cuckoo Stealer is a macOS malware with characteristics of spyware and an infostealer that has been in use since at least 2024. Cuckoo Stealer is a universal Mach-O binary that can run on Intel or ARM-based Macs and has been spread through trojanized versions of various potentially unwanted programs or PUP's such as converters, cleaners, and uninstallers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-174", "filename": "mitre_software_v17.txt", "content": "Cyclops Blink is a modular malware that has been used in widespread campaigns by Sandworm Team since at least 2019 to target Small/Home Office (SOHO) network devices, including WatchGuard and Asus. Cyclops Blink is assessed to be a replacement for VPNFilter, a similar platform targeting network devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-175", "filename": "mitre_software_v17.txt", "content": "Dacls is a multi-platform remote access tool used by Lazarus Group since at least December 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-176", "filename": "mitre_software_v17.txt", "content": "DanBot is a first-stage remote access Trojan written in C# that has been used by HEXANE since at least 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-177", "filename": "mitre_software_v17.txt", "content": "DarkComet is a Windows remote administration tool and backdoor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-178", "filename": "mitre_software_v17.txt", "content": "DarkGate first emerged in 2018 and has evolved into an initial access and data gathering tool associated with various criminal cyber operations. Written in Delphi and named \"DarkGate\" by its author, DarkGate is associated with credential theft, cryptomining, cryptotheft, and pre-ransomware actions. DarkGate use increased significantly starting in 2022 and is under active development by its author, who provides it as a Malware-as-a-Service offering.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-179", "filename": "mitre_software_v17.txt", "content": "DarkTortilla is a highly configurable .NET-based crypter that has been possibly active since at least August 2015. DarkTortilla has been used to deliver popular information stealers, RATs, and payloads such as Agent Tesla, AsyncRat, NanoCore, RedLine, Cobalt Strike, and Metasploit.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-180", "filename": "mitre_software_v17.txt", "content": "DarkWatchman is a lightweight JavaScript-based remote access tool (RAT) that avoids file operations; it was first observed in November 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-181", "filename": "mitre_software_v17.txt", "content": "Daserf is a backdoor that has been used to spy on and steal from Japanese, South Korean, Russian, Singaporean, and Chinese victims. Researchers have identified versions written in both Visual C and Delphi.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-182", "filename": "mitre_software_v17.txt", "content": "DCSrv is destructive malware that has been used by Moses Staff since at least  September 2021. Though DCSrv has ransomware-like capabilities, Moses Staff does not demand ransom or offer a decryption key.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-183", "filename": "mitre_software_v17.txt", "content": "DDKONG is a malware sample that was part of a campaign by Rancor. DDKONG was first seen used in February 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-184", "filename": "mitre_software_v17.txt", "content": "DEADEYE is a malware launcher that has been used by APT41 since at least May 2021. DEADEYE has variants that can either embed a payload inside a compiled binary (DEADEYE.EMBED) or append it to the end of a file (DEADEYE.APPEND).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-185", "filename": "mitre_software_v17.txt", "content": "DEADWOOD is wiper malware written in C++ using Boost libraries. DEADWOOD was first observed in an unattributed wiping event in Saudi Arabia in 2019, and has since been incorporated into Agrius operations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-186", "filename": "mitre_software_v17.txt", "content": "DealersChoice is a Flash exploitation framework used by APT28.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-187", "filename": "mitre_software_v17.txt", "content": "DEATHRANSOM is ransomware written in C that has been used since at least 2020, and has potential overlap with FIVEHANDS and HELLOKITTY.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-188", "filename": "mitre_software_v17.txt", "content": "DEFENSOR ID is a banking trojan capable of clearing a victim’s bank account or cryptocurrency wallet and taking over email or social media accounts. DEFENSOR ID performs the majority of its malicious functionality by abusing Android’s accessibility service.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-189", "filename": "mitre_software_v17.txt", "content": "Dendroid is an Android remote access tool (RAT) primarily targeting Western countries. The RAT was available for purchase for $300 and came bundled with a utility to inject the RAT into legitimate applications.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-190", "filename": "mitre_software_v17.txt", "content": "Denis is a Windows backdoor and Trojan used by APT32. Denis shares several similarities to the SOUNDBITE backdoor and has been used in conjunction with the Goopy backdoor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-191", "filename": "mitre_software_v17.txt", "content": "Derusbi is malware used by multiple Chinese APT groups. Both Windows and Linux variants have been observed.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-192", "filename": "mitre_software_v17.txt", "content": "Desert Scorpion is surveillanceware that has targeted the Middle East, specifically individuals located in Palestine. Desert Scorpion is suspected to have been operated by the threat actor APT-C-23. There are multiple close variants of Desert Scorpion, such as VAMP, GnatSpy, FrozenCell and SpyC23, which add some additional functionality but are not significantly different from the original malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-193", "filename": "mitre_software_v17.txt", "content": "Diavol is a ransomware variant first observed in June 2021 that is capable of prioritizing file types to encrypt based on a pre-configured list of extensions defined by the attacker.  The Diavol Ransomware-as-a Service (RaaS) program is managed by Wizard Spider and it has been observed being deployed by Bazar.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-194", "filename": "mitre_software_v17.txt", "content": "Dipsind is a malware family of backdoors that appear to be used exclusively by PLATINUM.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-195", "filename": "mitre_software_v17.txt", "content": "Disco is a custom implant that has been used by MoustachedBouncer since at least 2020 including in campaigns using targeted malicious content injection for initial access and command and control.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-196", "filename": "mitre_software_v17.txt", "content": "DnsSystem is a .NET based DNS backdoor, which is a customized version of the open source tool DIG.net, that has been used by HEXANE since at least June 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-197", "filename": "mitre_software_v17.txt", "content": "DOGCALL is a backdoor used by APT37 that has been used to target South Korean government and military organizations in 2017. It is typically dropped using a Hangul Word Processor (HWP) exploit.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-198", "filename": "mitre_software_v17.txt", "content": "Dok is a Trojan application disguised as a .zip file that is able to collect user credentials and install a malicious proxy server to redirect a user's network traffic (i.e. Adversary-in-the-Middle).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-199", "filename": "mitre_software_v17.txt", "content": "Doki is a backdoor that uses a unique Dogecoin-based Domain Generation Algorithm and was first observed in July 2020. Doki was used in conjunction with the ngrok Mining Botnet in a campaign that targeted Docker servers in cloud platforms.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-200", "filename": "mitre_software_v17.txt", "content": "Donut is an open source framework used to generate position-independent shellcode. Donut generated code has been used by multiple threat actors to inject and load malicious payloads into memory.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-201", "filename": "mitre_software_v17.txt", "content": "DoubleAgent is a family of RAT malware dating back to 2013, known to target groups with contentious relationships with the Chinese government.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-202", "filename": "mitre_software_v17.txt", "content": "down_new is a downloader that has been used by BRONZE BUTLER since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-203", "filename": "mitre_software_v17.txt", "content": "Downdelph is a first-stage downloader written in Delphi that has been used by APT28 in rare instances between 2013 and 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-204", "filename": "mitre_software_v17.txt", "content": "DownPaper is a backdoor Trojan; its main functionality is to download and run second stage malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-205", "filename": "mitre_software_v17.txt", "content": "DRATzarus is a remote access tool (RAT) that has been used by Lazarus Group to target the defense and aerospace organizations globally since at least summer 2020. DRATzarus shares similarities with Bankshot, which was used by Lazarus Group in 2017 to target the Turkish financial sector.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-206", "filename": "mitre_software_v17.txt", "content": "DressCode is an Android malware family.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-207", "filename": "mitre_software_v17.txt", "content": "Dridex is a prolific banking Trojan that first appeared in 2014. By December 2019, the US Treasury estimated Dridex had infected computers in hundreds of banks and financial institutions in over 40 countries, leading to more than $100 million in theft. Dridex was created from the source code of the Bugat banking Trojan (also known as Cridex).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-208", "filename": "mitre_software_v17.txt", "content": "Drinik is an evolving Android banking trojan that was observed targeting customers of around 27 banks in India in August 2021. Initially seen as an SMS stealer in 2016, Drinik resurfaced as a banking trojan with more advanced capabilities included in subsequent versions between September 2021 and August 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-209", "filename": "mitre_software_v17.txt", "content": "DroidJack is an Android remote access tool that has been observed posing as legitimate applications including the Super Mario Run and Pokemon GO games.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-210", "filename": "mitre_software_v17.txt", "content": "DropBook is a Python-based backdoor compiled with PyInstaller.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-211", "filename": "mitre_software_v17.txt", "content": "Drovorub is a Linux malware toolset comprised of an agent, client, server, and kernel modules, that has been used by APT28.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-212", "filename": "mitre_software_v17.txt", "content": "dsquery is a command-line utility that can be used to query Active Directory for information from a system within a domain.  It is typically installed only on Windows Server versions but can be installed on non-server variants through the Microsoft-provided Remote Server Administration Tools bundle.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-213", "filename": "mitre_software_v17.txt", "content": "Dtrack is spyware that was discovered in 2019 and has been used against Indian financial institutions, research facilities, and the Kudankulam Nuclear Power Plant. Dtrack shares similarities with the DarkSeoul campaign, which was attributed to Lazarus Group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-214", "filename": "mitre_software_v17.txt", "content": "DualToy is Windows malware that installs malicious applications onto Android and iOS devices connected over USB.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-215", "filename": "mitre_software_v17.txt", "content": "Duqu is a malware platform that uses a modular approach to extend functionality after deployment within a target network.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-216", "filename": "mitre_software_v17.txt", "content": "DUSTPAN is an in-memory dropper written in C/C++ used by APT41 since 2021 that decrypts and executes an embedded payload.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-217", "filename": "mitre_software_v17.txt", "content": "DUSTTRAP is a multi-stage plugin framework associated with APT41 operations with multiple components.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-218", "filename": "mitre_software_v17.txt", "content": "DustySky is multi-stage malware written in .NET that has been used by Molerats since May 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-219", "filename": "mitre_software_v17.txt", "content": "Dvmap is rooting malware that injects malicious code into system runtime libraries. It is credited with being the first malware that performs this type of code injection.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-220", "filename": "mitre_software_v17.txt", "content": "Dyre is a banking Trojan that has been used for financial gain.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-221", "filename": "mitre_software_v17.txt", "content": "Ebury is an OpenSSH backdoor and credential stealer targeting Linux servers and container hosts developed by Windigo. Ebury is primarily installed through modifying shared libraries (.so files) executed by the legitimate OpenSSH program. First seen in 2009, Ebury has been used to maintain a botnet of servers, deploy additional malware, and steal cryptocurrency wallets, credentials, and credit card details.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-222", "filename": "mitre_software_v17.txt", "content": "ECCENTRICBANDWAGON is a remote access Trojan (RAT) used by North Korean cyber actors that was first identified in August 2020. It is a reconnaissance tool--with keylogging and screen capture functionality--used for information gathering on compromised systems.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-223", "filename": "mitre_software_v17.txt", "content": "Ecipekac is a multi-layer loader that has been used by menuPass since at least 2019 including use as a loader for P8RAT, SodaMaster, and FYAnti.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-224", "filename": "mitre_software_v17.txt", "content": "Egregor is a Ransomware-as-a-Service (RaaS) tool that was first observed in September 2020. Researchers have noted code similarities between Egregor and Sekhmet ransomware, as well as Maze ransomware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-225", "filename": "mitre_software_v17.txt", "content": "EKANS is ransomware variant written in Golang that first appeared in mid-December 2019 and has been used against multiple sectors, including energy, healthcare, and automotive manufacturing, which in some cases resulted in significant operational disruptions. EKANS has used a hard-coded kill-list of processes, including some associated with common ICS software platforms (e.g., GE Proficy, Honeywell HMIWeb, etc), similar to those defined in MegaCortex.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-226", "filename": "mitre_software_v17.txt", "content": "Elise is a custom backdoor Trojan that appears to be used exclusively by Lotus Blossom. It is part of a larger group of tools referred to as LStudio, ST Group, and APT0LSTU.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-227", "filename": "mitre_software_v17.txt", "content": "ELMER is a non-persistent, proxy-aware HTTP backdoor written in Delphi that has been used by APT16.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-228", "filename": "mitre_software_v17.txt", "content": "Emissary is a Trojan that has been used by Lotus Blossom. It shares code with Elise, with both Trojans being part of a malware group referred to as LStudio.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-229", "filename": "mitre_software_v17.txt", "content": "Emotet is a modular malware variant which is primarily used as a downloader for other malware variants such as TrickBot and IcedID. Emotet first emerged in June 2014, initially targeting the financial sector, and has expanded to multiple verticals over time.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-230", "filename": "mitre_software_v17.txt", "content": "Empire is an open source, cross-platform remote administration and post-exploitation framework that is publicly available on GitHub. While the tool itself is primarily written in Python, the post-exploitation agents are written in pure PowerShell for Windows and Python for Linux/macOS. Empire was one of five tools singled out by a joint report on public hacking tools being widely used by adversaries.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-231", "filename": "mitre_software_v17.txt", "content": "EnvyScout is a dropper that has been used by APT29 since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-232", "filename": "mitre_software_v17.txt", "content": "Epic is a backdoor that has been used by Turla.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-233", "filename": "mitre_software_v17.txt", "content": "Escobar is an Android banking trojan, first detected in March 2021, believed to be a new variant of AbereBot.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-234", "filename": "mitre_software_v17.txt", "content": "esentutl is a command-line tool that provides database utilities for the Windows Extensible Storage Engine.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-235", "filename": "mitre_software_v17.txt", "content": "eSurv is mobile surveillanceware designed for the lawful intercept market that was developed over the course of many years.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-236", "filename": "mitre_software_v17.txt", "content": "EventBot is an Android banking trojan and information stealer that abuses Android’s accessibility service to steal data from various applications. EventBot was designed to target over 200 different banking and financial applications, the majority of which are European bank and cryptocurrency exchange applications.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-237", "filename": "mitre_software_v17.txt", "content": "EvilBunny is a C++ malware sample observed since 2011 that was designed to be a execution platform for Lua scripts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-238", "filename": "mitre_software_v17.txt", "content": "EvilGrab is a malware family with common reconnaissance capabilities. It has been deployed by menuPass via malicious Microsoft Office documents as part of spearphishing campaigns.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-239", "filename": "mitre_software_v17.txt", "content": "EVILNUM is fully capable backdoor that was first identified in 2018. EVILNUM is used by the APT group Evilnum which has the same name.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-240", "filename": "mitre_software_v17.txt", "content": "Exaramel for Linux is a backdoor written in the Go Programming Language and compiled as a 64-bit ELF binary. The Windows version is tracked separately under Exaramel for Windows.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-241", "filename": "mitre_software_v17.txt", "content": "Exaramel for Windows is a backdoor used for targeting Windows systems. The Linux version is tracked separately under Exaramel for Linux.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-242", "filename": "mitre_software_v17.txt", "content": "Exbyte is an exfiltration tool written in Go that is uniquely associated with BlackByte operations. Observed since 2022, Exbyte transfers collected files to online file sharing and hosting services.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-243", "filename": "mitre_software_v17.txt", "content": "Exobot is Android banking malware, primarily targeting financial institutions in Germany, Austria, and France.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-244", "filename": "mitre_software_v17.txt", "content": "Exodus is Android spyware deployed in two distinct stages named Exodus One (dropper) and Exodus Two (payload).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-245", "filename": "mitre_software_v17.txt", "content": "Expand is a Windows utility used to expand one or more compressed CAB files. It has been used by BBSRAT to decompress a CAB file into executable content.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-246", "filename": "mitre_software_v17.txt", "content": "Explosive is a custom-made remote access tool used by the group Volatile Cedar. It was first identified in the wild in 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-247", "filename": "mitre_software_v17.txt", "content": "Fakecalls is an Android trojan, first detected in January 2021, that masquerades as South Korean banking apps. It has capabilities to intercept calls to banking institutions and even maintain realistic dialogues with the victim using pre-recorded audio snippets.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-248", "filename": "mitre_software_v17.txt", "content": "FakeM is a shellcode-based Windows backdoor that has been used by Scarlet Mimic.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-249", "filename": "mitre_software_v17.txt", "content": "FakeSpy is Android spyware that has been operated by the Chinese threat actor behind the Roaming Mantis campaigns.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-250", "filename": "mitre_software_v17.txt", "content": "FALLCHILL is a RAT that has been used by Lazarus Group since at least 2016 to target the aerospace, telecommunications, and finance industries. It is usually dropped by other Lazarus Group malware or delivered when a victim unknowingly visits a compromised website.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-251", "filename": "mitre_software_v17.txt", "content": "FatDuke is a backdoor used by APT29 since at least 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-252", "filename": "mitre_software_v17.txt", "content": "Felismus is a modular backdoor that has been used by Sowbug.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-253", "filename": "mitre_software_v17.txt", "content": "FELIXROOT is a backdoor that has been used to target Ukrainian victims.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-254", "filename": "mitre_software_v17.txt", "content": "Ferocious is a first stage implant composed of VBS and PowerShell scripts that has been used by WIRTE since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-255", "filename": "mitre_software_v17.txt", "content": "Fgdump is a Windows password hash dumper.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-256", "filename": "mitre_software_v17.txt", "content": "Final1stspy is a dropper family that has been used to deliver DOGCALL.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-257", "filename": "mitre_software_v17.txt", "content": "FinFisher is a government-grade commercial surveillance spyware reportedly sold exclusively to government agencies for use in targeted and lawful criminal investigations. It is heavily obfuscated and uses multiple anti-analysis techniques. It has other variants including Wingbird.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-258", "filename": "mitre_software_v17.txt", "content": "FIVEHANDS is a customized version of DEATHRANSOM ransomware written in C++. FIVEHANDS has been used since at least 2021, including in Ransomware-as-a-Service (RaaS) campaigns, sometimes along with SombRAT.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-259", "filename": "mitre_software_v17.txt", "content": "FjordPhantom is a malicious Android application first discovered in September 2024 with targets in Southeast Asia, specifically Indonesia, Thailand, and Vietnam. FjordPhantom was distributed through email and messaging applications. Once installed, the application launches a virtualization solution to steal important information, such as bank accounts, and to manipulate the user interface. The malicious activity from the virtualization solution runs alongside legitimate banking applications.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-260", "filename": "mitre_software_v17.txt", "content": "Flagpro is a Windows-based, first-stage downloader that has been used by BlackTech since at least October 2020. It has primarily been used against defense, media, and communications companies in Japan.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-261", "filename": "mitre_software_v17.txt", "content": "Flame is a sophisticated toolkit that has been used to collect information since at least 2010, largely targeting Middle East countries.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-262", "filename": "mitre_software_v17.txt", "content": "FLASHFLOOD is malware developed by APT30 that allows propagation and exfiltration of data over removable devices. APT30 may use this capability to exfiltrate data across air-gaps.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-263", "filename": "mitre_software_v17.txt", "content": "FlawedAmmyy is a remote access tool (RAT) that was first seen in early 2016. The code for FlawedAmmyy was based on leaked source code for a version of Ammyy Admin, a remote access software.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-264", "filename": "mitre_software_v17.txt", "content": "FlawedGrace is a fully featured remote access tool (RAT) written in C++ that was first observed in late 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-265", "filename": "mitre_software_v17.txt", "content": "FlexiSpy is sophisticated surveillanceware for iOS and Android. Publicly-available, comprehensive analysis has only been found for the Android version.FlexiSpy markets itself as a parental control and employee monitoring application.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-266", "filename": "mitre_software_v17.txt", "content": "FLIPSIDE is a simple tool similar to Plink that is used by FIN5 to maintain access to victims.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-267", "filename": "mitre_software_v17.txt", "content": "FlixOnline is an Android malware, first detected in early 2021, believed to target users of WhatsApp. FlixOnline primarily spreads via automatic replies to a device’s incoming WhatsApp messages.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-268", "filename": "mitre_software_v17.txt", "content": "FluBot is a multi-purpose mobile banking malware that was first observed in Spain in late 2020. It primarily spread through European countries using a variety of SMS phishing messages in multiple languages. An international law enforcement operation of 11 countries eventually disrupted the spread of FluBot.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-269", "filename": "mitre_software_v17.txt", "content": "FlyTrap is an Android trojan, first detected in March 2021, that uses social engineering tactics to compromise Facebook accounts. FlyTrap was initially detected through infected apps on the Google Play store, and is believed to have impacted over 10,000 victims across at least 140 countries.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-270", "filename": "mitre_software_v17.txt", "content": "FoggyWeb is a passive and highly-targeted backdoor capable of remotely exfiltrating sensitive information from a compromised Active Directory Federated Services (AD FS) server. It has been used by APT29 since at least early April 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-271", "filename": "mitre_software_v17.txt", "content": "Forfiles is a Windows utility commonly used in batch jobs to execute commands on one or more selected files or directories (ex: list all directories in a drive, read the first line of all files created yesterday, etc.). Forfiles can be executed from either the command line, Run window, or batch files/scripts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-272", "filename": "mitre_software_v17.txt", "content": "FRAMESTING is a Python web shell that was used during Cutting Edge to embed into an Ivanti Connect Secure Python package for command execution.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-273", "filename": "mitre_software_v17.txt", "content": "FrameworkPOS is a point of sale (POS) malware used by FIN6 to steal payment card data from sytems that run physical POS devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-274", "filename": "mitre_software_v17.txt", "content": "FrostyGoop is a Windows-based binary written in Golang that allows for interaction with industrial control system (ICS) equipment via Modbus TCP over port 502. FrostyGoop allows for reading and writing data to holding registers on targeted devices, manipulating the operation of systems for malicious purposes. FrostyGoop is associated with the FrostyGoop Incident in Ukraine.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-275", "filename": "mitre_software_v17.txt", "content": "FrozenCell is the mobile component of a family of surveillanceware, with a corresponding desktop component known as KasperAgent and Micropsia. There are multiple close variants of FrozenCell, such as VAMP, GnatSpy, Desert Scorpion and SpyC23, which add some additional functionality but are not significantly different from the original malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-276", "filename": "mitre_software_v17.txt", "content": "FRP, which stands for Fast Reverse Proxy, is an openly available tool that is capable of exposing a server located behind a firewall or Network Address Translation (NAT) to the Internet. FRP can support multiple protocols including TCP, UDP, and HTTP(S) and has been abused by threat actors to proxy command and control communications.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-277", "filename": "mitre_software_v17.txt", "content": "FruitFly is designed to spy on mac users  .", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-278", "filename": "mitre_software_v17.txt", "content": "ftp is a utility commonly available with operating systems to transfer information over the File Transfer Protocol (FTP). Adversaries can use it to transfer other tools onto a system or to exfiltrate data.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-279", "filename": "mitre_software_v17.txt", "content": "FunnyDream is a backdoor with multiple components that was used during the FunnyDream campaign since at least 2019, primarily for execution and exfiltration.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-280", "filename": "mitre_software_v17.txt", "content": "Fuxnet is malware designed to impact the industrial network infrastructure managing control system sensors for utility operations in Moscow. Fuxnet is linked to an entity referred to as the Blackjack hacking group, which is assessed to be linked to Ukrainian intelligence services.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-281", "filename": "mitre_software_v17.txt", "content": "FYAnti is a loader that has been used by menuPass since at least 2020, including to deploy QuasarRAT.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-282", "filename": "mitre_software_v17.txt", "content": "Fysbis is a Linux-based backdoor used by APT28 that dates back to at least 2014.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-283", "filename": "mitre_software_v17.txt", "content": "Gazer is a backdoor used by Turla since at least 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-284", "filename": "mitre_software_v17.txt", "content": "Gelsemium is a modular malware comprised of a dropper (Gelsemine), a loader (Gelsenicine), and main (Gelsevirine) plug-ins written using the Microsoft Foundation Class (MFC) framework. Gelsemium has been used by the Gelsemium group since at least 2014.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-285", "filename": "mitre_software_v17.txt", "content": "GeminiDuke is malware that was used by APT29 from 2009 to 2012.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-286", "filename": "mitre_software_v17.txt", "content": "Get2 is a downloader written in C++ that has been used by TA505 to deliver FlawedGrace, FlawedAmmyy, Snatch and SDBbot.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-287", "filename": "mitre_software_v17.txt", "content": "gh0st RAT is a remote access tool (RAT). The source code is public and it has been used by multiple groups.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-288", "filename": "mitre_software_v17.txt", "content": "Ginp is an Android banking trojan that has been used to target Spanish banks. Some of the code was taken directly from Anubis.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-289", "filename": "mitre_software_v17.txt", "content": "GLASSTOKEN is a custom web shell used by threat actors during Cutting Edge to execute commands on compromised Ivanti Secure Connect VPNs.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-290", "filename": "mitre_software_v17.txt", "content": "GLOOXMAIL is malware used by APT1 that mimics legitimate Jabber/XMPP traffic.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-291", "filename": "mitre_software_v17.txt", "content": "GoBear is a Go-based backdoor that abuses legitimate, stolen certificates for defense evasion purposes. GoBear is exclusively linked to Kimsuky operations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-292", "filename": "mitre_software_v17.txt", "content": "Gold Dragon is a Korean-language, data gathering implant that was first observed in the wild in South Korea in July 2017. Gold Dragon was used along with Brave Prince and RunningRAT in operations targeting organizations associated with the 2018 Pyeongchang Winter Olympics.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-293", "filename": "mitre_software_v17.txt", "content": "Golden Cup is Android spyware that has been used to target World Cup fans.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-294", "filename": "mitre_software_v17.txt", "content": "GoldenEagle is a piece of Android malware that has been used in targeting of Uyghurs, Muslims, Tibetans, individuals in Turkey, and individuals in China. Samples have been found as early as 2012.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-295", "filename": "mitre_software_v17.txt", "content": "GoldenSpy is a backdoor malware which has been packaged with legitimate tax preparation software. GoldenSpy was discovered targeting organizations in China, being delivered with the \"Intelligent Tax\" software suite which is produced by the Golden Tax Department of Aisino Credit Information Co. and required to pay local taxes.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-296", "filename": "mitre_software_v17.txt", "content": "GoldFinder is a custom HTTP tracer tool written in Go that logs the route a packet takes between a compromised network and a C2 server. It can be used to inform  threat actors of potential points of discovery or logging of their actions, including C2 related to other malware. GoldFinder was discovered in early 2021 during an investigation into the SolarWinds Compromise by APT29.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-297", "filename": "mitre_software_v17.txt", "content": "GoldMax is a second-stage C2 backdoor written in Go with Windows and Linux variants that are nearly identical in functionality. GoldMax was discovered in early 2021 during the investigation into the SolarWinds Compromise, and has likely been used by APT29 since at least mid-2019. GoldMax uses multiple defense evasion techniques, including avoiding virtualization execution and masking malicious traffic.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-298", "filename": "mitre_software_v17.txt", "content": "GolfSpy is Android spyware deployed by the group Bouncing Golf.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-299", "filename": "mitre_software_v17.txt", "content": "Gomir is a Linux backdoor variant of the Go-based malware GoBear, uniquely assoicated with Kimsuky operations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-300", "filename": "mitre_software_v17.txt", "content": "Gooligan is a malware family that runs privilege escalation exploits on Android devices and then uses its escalated privileges to steal authentication tokens that can be used to access data from many Google applications. Gooligan has been described as part of the Ghost Push Android malware family.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-301", "filename": "mitre_software_v17.txt", "content": "Goopy is a Windows backdoor and Trojan used by APT32 and shares several similarities to another backdoor used by the group (Denis). Goopy is named for its impersonation of the legitimate Google Updater executable.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-302", "filename": "mitre_software_v17.txt", "content": "Gootloader is a Javascript-based infection framework that has been used since at least 2020 as a delivery method for the Gootkit banking trojan, Cobalt Strike, REvil, and others. Gootloader operates on an \"Initial Access as a Service\" model and has leveraged SEO Poisoning to provide access to entities in multiple sectors worldwide including financial, military, automotive, pharmaceutical, and energy.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-303", "filename": "mitre_software_v17.txt", "content": "GPlayed is an Android trojan with a broad range of capabilities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-304", "filename": "mitre_software_v17.txt", "content": "Grandoreiro is a banking trojan written in Delphi that was first observed in 2016 and uses a Malware-as-a-Service (MaaS) business model. Grandoreiro has confirmed victims in Brazil, Mexico, Portugal, and Spain.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-305", "filename": "mitre_software_v17.txt", "content": "GravityRAT is a remote access tool (RAT) and has been in ongoing development since 2016. The actor behind the tool remains unknown, but two usernames have been recovered that link to the author, which are \"TheMartian\" and \"The Invincible.\" According to the National Computer Emergency Response Team (CERT) of India, the malware has been identified in attacks against organization and entities in India.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-306", "filename": "mitre_software_v17.txt", "content": "Green Lambert is a modular backdoor that security researchers assess has been used by an advanced threat group referred to as Longhorn and The Lamberts. First reported in 2017, the Windows variant of Green Lambert may have been used as early as 2008; a macOS version was uploaded to a multiscanner service in September 2014.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-307", "filename": "mitre_software_v17.txt", "content": "GreyEnergy is a backdoor written in C and compiled in Visual Studio. GreyEnergy shares similarities with the BlackEnergy malware and is thought to be the successor of it.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-308", "filename": "mitre_software_v17.txt", "content": "GRIFFON is a JavaScript backdoor used by FIN7.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-309", "filename": "mitre_software_v17.txt", "content": "GrimAgent is a backdoor that has been used before the deployment of Ryuk ransomware since at least 2020; it is likely used by FIN6 and Wizard Spider.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-310", "filename": "mitre_software_v17.txt", "content": "gsecdump is a publicly-available credential dumper used to obtain password hashes and LSA secrets from Windows operating systems.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-311", "filename": "mitre_software_v17.txt", "content": "GuLoader is a file downloader that has been used since at least December 2019 to distribute a variety of remote administration tool (RAT) malware, including NETWIRE, Agent Tesla, NanoCore, FormBook, and Parallax RAT.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-312", "filename": "mitre_software_v17.txt", "content": "Gustuff is mobile malware designed to steal users' banking and virtual currency credentials.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-313", "filename": "mitre_software_v17.txt", "content": "H1N1 is a malware variant that has been distributed via a campaign using VBA macros to infect victims. Although it initially had only loader capabilities, it has evolved to include information-stealing functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-314", "filename": "mitre_software_v17.txt", "content": "Hacking Team UEFI Rootkit is a rootkit developed by the company Hacking Team as a method of persistence for remote access software.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-315", "filename": "mitre_software_v17.txt", "content": "HALFBAKED is a malware family consisting of multiple components intended to establish persistence in victim networks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-316", "filename": "mitre_software_v17.txt", "content": "HAMMERTOSS is a backdoor that was used by APT29 in 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-317", "filename": "mitre_software_v17.txt", "content": "Hancitor is a downloader that has been used by Pony and other information stealing malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-318", "filename": "mitre_software_v17.txt", "content": "Hannotog is a type of backdoor malware uniquely assoicated with Lotus Blossom operations since at least 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-319", "filename": "mitre_software_v17.txt", "content": "HAPPYWORK is a downloader used by APT37 to target South Korean government and financial victims in November 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-320", "filename": "mitre_software_v17.txt", "content": "HARDRAIN is a Trojan malware variant reportedly used by the North Korean government.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-321", "filename": "mitre_software_v17.txt", "content": "Havij is an automatic SQL Injection tool distributed by the Iranian ITSecTeam security company. Havij has been used by penetration testers and adversaries.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-322", "filename": "mitre_software_v17.txt", "content": "HAWKBALL is a backdoor that was observed in targeting of the government sector in Central Asia.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-323", "filename": "mitre_software_v17.txt", "content": "hcdLoader is a remote access tool (RAT) that has been used by APT18.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-324", "filename": "mitre_software_v17.txt", "content": "HDoor is malware that has been customized and used by the Naikon group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-325", "filename": "mitre_software_v17.txt", "content": "HELLOKITTY is a ransomware written in C++  that shares similar code structure and functionality with DEATHRANSOM and FIVEHANDS. HELLOKITTY has been used since at least 2020, targets have included a Polish video game developer and a Brazilian electric power company.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-326", "filename": "mitre_software_v17.txt", "content": "Helminth is a backdoor that has at least two variants - one written in VBScript and PowerShell that is delivered via a macros in Excel spreadsheets, and one that is a standalone Windows executable.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-327", "filename": "mitre_software_v17.txt", "content": "HenBox is Android malware that attempts to only execute on Xiaomi devices running the MIUI operating system. HenBox has primarily been used to target Uyghurs, a minority Turkic ethnic group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-328", "filename": "mitre_software_v17.txt", "content": "HermeticWiper is a data wiper that has been used since at least early 2022, primarily against Ukraine with additional activity observed in Latvia and Lithuania. Some sectors targeted include government, financial, defense, aviation, and IT services.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-329", "filename": "mitre_software_v17.txt", "content": "HermeticWizard is a worm that has been used to spread HermeticWiper in attacks against organizations in Ukraine since at least 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-330", "filename": "mitre_software_v17.txt", "content": "Heyoka Backdoor is a custom backdoor--based on the Heyoka open source exfiltration tool--that  has been used by Aoqin Dragon since at least 2013.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-331", "filename": "mitre_software_v17.txt", "content": "Hi-Zor is a remote access tool (RAT) that has characteristics similar to Sakula. It was used in a campaign named INOCNATION.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-332", "filename": "mitre_software_v17.txt", "content": "HiddenWasp is a Linux-based Trojan used to target systems for remote control. It comes in the form of a statically linked ELF binary with stdlibc++.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-333", "filename": "mitre_software_v17.txt", "content": "HIDEDRV is a rootkit used by APT28. It has been deployed along with Downdelph to execute and hide that malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-334", "filename": "mitre_software_v17.txt", "content": "Hikit is malware that has been used by Axiom for late-stage persistence and exfiltration after the initial compromise.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-335", "filename": "mitre_software_v17.txt", "content": "HilalRAT is a remote access-capable Android malware, developed and used by UNC788. HilalRAT is capable of collecting data, such as device location, call logs, etc., and is capable of executing actions, such as activating a device's camera and microphone.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-336", "filename": "mitre_software_v17.txt", "content": "Hildegard is malware that targets misconfigured kubelets for initial access and runs cryptocurrency miner operations. The malware was first observed in January 2021. The TeamTNT activity group is believed to be behind Hildegard.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-337", "filename": "mitre_software_v17.txt", "content": "HOMEFRY is a 64-bit Windows password dumper/cracker that has previously been used in conjunction with other Leviathan backdoors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-338", "filename": "mitre_software_v17.txt", "content": "HOPLIGHT is a backdoor Trojan that has reportedly been used by the North Korean government.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-339", "filename": "mitre_software_v17.txt", "content": "Hornbill is one of two mobile malware families known to be used by the APT Confucius. Analysis suggests that Hornbill was first active in early 2018. While Hornbill and Sunbird overlap in core capabilities, Hornbill has tools and behaviors suggesting more passive reconnaissance.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-340", "filename": "mitre_software_v17.txt", "content": "HotCroissant is a remote access trojan (RAT) attributed by U.S. government entities to malicious North Korean government cyber activity, tracked collectively as HIDDEN COBRA. HotCroissant shares numerous code similarities with Rifdoor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-341", "filename": "mitre_software_v17.txt", "content": "HTRAN is a tool that proxies connections through intermediate hops and aids users in disguising their true geographical location. It can be used by adversaries to hide their location when interacting with the victim networks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-342", "filename": "mitre_software_v17.txt", "content": "HTTPBrowser is malware that has been used by several threat groups.   It is believed to be of Chinese origin.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-343", "filename": "mitre_software_v17.txt", "content": "httpclient is malware used by Putter Panda. It is a simple tool that provides a limited range of functionality, suggesting it is likely used as a second-stage or supplementary/backup tool.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-344", "filename": "mitre_software_v17.txt", "content": "HUI Loader is a custom DLL loader that has been used since at least 2015 by China-based threat groups including Cinnamon Tempest and menuPass to deploy malware on compromised hosts. HUI Loader has been observed in campaigns loading SodaMaster, PlugX, Cobalt Strike, Komplex, and several strains of ransomware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-345", "filename": "mitre_software_v17.txt", "content": "HummingBad is a family of Android malware that generates fraudulent advertising revenue and has the ability to obtain root access on older, vulnerable versions of Android.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-346", "filename": "mitre_software_v17.txt", "content": "HummingWhale is an Android malware family that performs ad fraud.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-347", "filename": "mitre_software_v17.txt", "content": "Hydraq is a data-theft trojan first used by Elderwood in the 2009 Google intrusion known as Operation Aurora, though variations of this trojan have been used in more recent campaigns by other Chinese actors, possibly including APT17.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-348", "filename": "mitre_software_v17.txt", "content": "HyperBro is a custom in-memory backdoor used by Threat Group-3390.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-349", "filename": "mitre_software_v17.txt", "content": "HyperStack is a RPC-based backdoor used by Turla since at least 2018. HyperStack has similarities to other backdoors used by Turla including Carbon.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-350", "filename": "mitre_software_v17.txt", "content": "IceApple is a modular Internet Information Services (IIS) post-exploitation framework, that has been used since at least 2021 against the technology, academic, and government sectors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-351", "filename": "mitre_software_v17.txt", "content": "IcedID is a modular banking malware designed to steal financial information that has been observed in the wild since at least 2017.  IcedID  has been downloaded by Emotet in multiple campaigns.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-352", "filename": "mitre_software_v17.txt", "content": "ifconfig is a Unix-based utility used to gather information about and interact with the TCP/IP settings on a system.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-353", "filename": "mitre_software_v17.txt", "content": "iKitten is a macOS exfiltration agent  .", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-354", "filename": "mitre_software_v17.txt", "content": "IMAPLoader is a .NET-based loader malware exclusively associated with CURIUM operations since at least 2022. IMAPLoader leverages email protocols for command and control and payload delivery.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-355", "filename": "mitre_software_v17.txt", "content": "Imminent Monitor was a commodity remote access tool (RAT) offered for sale from 2012 until 2019, when an operation was conducted to take down the Imminent Monitor infrastructure. Various cracked versions and variations of this RAT are still in circulation.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-356", "filename": "mitre_software_v17.txt", "content": "Impacket is an open source collection of modules written in Python for programmatically constructing and manipulating network protocols. Impacket contains several tools for remote service execution, Kerberos manipulation, Windows credential dumping, packet sniffing, and relay attacks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-357", "filename": "mitre_software_v17.txt", "content": "INC Ransomware is a ransomware strain that has been used by the INC Ransom group since at least 2023 against multiple industry sectors worldwide. INC Ransomware can employ partial encryption combined with multi-threading to speed encryption.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-358", "filename": "mitre_software_v17.txt", "content": "INCONTROLLER is custom malware that includes multiple modules tailored towards ICS devices and technologies, including Schneider Electric and Omron PLCs as well as OPC UA, Modbus, and CODESYS protocols. INCONTROLLER has the ability to discover specific devices, download logic on the devices, and exploit platform-specific vulnerabilities. As of September 2022, some security researchers assessed INCONTROLLER was developed by CHERNOVITE.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-359", "filename": "mitre_software_v17.txt", "content": "Industroyer is a sophisticated malware framework designed to cause an impact to the working processes of Industrial Control Systems (ICS), specifically components used in electrical substations. Industroyer was used in the attacks on the Ukrainian power grid in December 2016. This is the first publicly known malware specifically designed to target and impact operations in the electric grid.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-360", "filename": "mitre_software_v17.txt", "content": "Industroyer2 is a compiled and static piece of malware that has the ability to communicate over the IEC-104 protocol. It is similar to the IEC-104 module found in Industroyer. Security researchers assess that Industroyer2 was designed to cause impact to high-voltage electrical substations. The initial Industroyer2 sample was compiled on 03/23/2022 and scheduled to execute on 04/08/2022, however it was discovered before deploying, resulting in no impact.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-361", "filename": "mitre_software_v17.txt", "content": "InnaputRAT is a remote access tool that can exfiltrate files from a victim’s machine. InnaputRAT has been seen out in the wild since 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-362", "filename": "mitre_software_v17.txt", "content": "INSOMNIA is spyware that has been used by the group Evil Eye.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-363", "filename": "mitre_software_v17.txt", "content": "InvisiMole is a modular spyware program that has been used by the InvisiMole Group since at least 2013. InvisiMole has two backdoor modules called RC2FM and RC2CL that are used to perform post-exploitation activities. It has been discovered on compromised victims in the Ukraine and Russia. Gamaredon Group infrastructure has been used to download and execute InvisiMole against a small number of victims.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-364", "filename": "mitre_software_v17.txt", "content": "Invoke-PSImage takes a PowerShell script and embeds the bytes of the script into the pixels of a PNG image. It generates a one liner for executing either from a file of from the web. Example of usage is embedding the PowerShell code from the Invoke-Mimikatz module and embed it into an image file. By calling the image file from a macro for example, the macro will download the picture and execute the PowerShell code, which in this case will dump the passwords.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-365", "filename": "mitre_software_v17.txt", "content": "ipconfig is a Windows utility that can be used to find information about a system's TCP/IP, DNS, DHCP, and adapter configuration.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-366", "filename": "mitre_software_v17.txt", "content": "IPsec Helper is a post-exploitation remote access tool linked to Agrius operations. This malware shares significant programming and functional overlaps with Apostle ransomware, also linked to Agrius. IPsec Helper provides basic remote access tool functionality such as uploading files from victim systems, running commands, and deploying additional payloads.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-367", "filename": "mitre_software_v17.txt", "content": "IronNetInjector is a Turla toolchain that utilizes scripts from the open-source IronPython implementation of Python with a .NET injector to drop one or more payloads including ComRAT.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-368", "filename": "mitre_software_v17.txt", "content": "ISMInjector is a Trojan used to install another OilRig backdoor, ISMAgent.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-369", "filename": "mitre_software_v17.txt", "content": "Ixeshe is a malware family that has been used since at least 2009 against targets in East Asia.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-370", "filename": "mitre_software_v17.txt", "content": "J-magic is a custom variant of the cd00r backdoor tailored to target Juniper routers that was first observed during the J-magic Campaign in mid-2023. J-magic monitors TCP traffic for five predefined parameters or \"magic packets\" to be sent by the attackers before activating on compromised devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-371", "filename": "mitre_software_v17.txt", "content": "Janicab is an OS X trojan that relied on a valid developer ID and oblivious users to install it.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-372", "filename": "mitre_software_v17.txt", "content": "Javali is a banking trojan that has targeted Portuguese and Spanish-speaking countries since 2017, primarily focusing on customers of financial institutions in Brazil and Mexico.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-373", "filename": "mitre_software_v17.txt", "content": "JCry is ransomware written in Go. It was identified as apart of the #OpJerusalem 2019 campaign.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-374", "filename": "mitre_software_v17.txt", "content": "JHUHUGIT is malware used by APT28. It is based on Carberp source code and serves as reconnaissance malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-375", "filename": "mitre_software_v17.txt", "content": "JPIN is a custom-built backdoor family used by PLATINUM. Evidence suggests developers of JPIN and Dipsind code bases were related in some way.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-376", "filename": "mitre_software_v17.txt", "content": "jRAT is a cross-platform, Java-based backdoor originally available for purchase in 2012. Variants of jRAT have been distributed via a software-as-a-service platform, similar to an online subscription model.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-377", "filename": "mitre_software_v17.txt", "content": "JSS Loader is Remote Access Trojan (RAT) with .NET and C++ variants that has been used by FIN7 since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-378", "filename": "mitre_software_v17.txt", "content": "Judy is auto-clicking adware that was distributed through multiple apps in the Google Play Store.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-379", "filename": "mitre_software_v17.txt", "content": "JumbledPath is a custom-built utility written in GO that has been used by Salt Typhoon since at least 2024 for packet capture on remote Cisco devices. JumbledPath is compiled as an ELF binary using x86-64 architecture which makes it potentially useable across Linux operating systems and network devices from multiple vendors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-380", "filename": "mitre_software_v17.txt", "content": "Kapeka is a backdoor written in C++ used against victims in Eastern Europe since at least mid-2022. Kapeka has technical overlaps with Exaramel for Windows and Prestige malware variants, both of which are linked to Sandworm Team. Kapeka may have been used in advance of Prestige deployment in late 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-381", "filename": "mitre_software_v17.txt", "content": "KARAE is a backdoor typically used by APT37 as first-stage malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-382", "filename": "mitre_software_v17.txt", "content": "Kasidet is a backdoor that has been dropped by using malicious VBA macros.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-383", "filename": "mitre_software_v17.txt", "content": "Kazuar is a fully featured, multi-platform backdoor Trojan written using the Microsoft .NET framework.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-384", "filename": "mitre_software_v17.txt", "content": "Kerrdown is a custom downloader that has been used by APT32 since at least 2018 to install spyware from a server on the victim's network.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-385", "filename": "mitre_software_v17.txt", "content": "Kessel is an advanced version of OpenSSH which acts as a custom backdoor, mainly acting to steal credentials and function as a bot. Kessel has been active since its C2 domain began resolving in August 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-386", "filename": "mitre_software_v17.txt", "content": "Kevin is a backdoor implant written in C++ that has been used by HEXANE since at least June 2020, including in operations against organizations in Tunisia.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-387", "filename": "mitre_software_v17.txt", "content": "KeyBoy is malware that has been used in targeted campaigns against members of the Tibetan Parliament in 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-388", "filename": "mitre_software_v17.txt", "content": "This piece of malware steals the content of the user's keychain while maintaining a permanent backdoor  .", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-389", "filename": "mitre_software_v17.txt", "content": "KEYMARBLE is a Trojan that has reportedly been used by the North Korean government.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-390", "filename": "mitre_software_v17.txt", "content": "KEYPLUG is a modular backdoor written in C++, with Windows and Linux variants, that has been used by APT41 since at least June 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-391", "filename": "mitre_software_v17.txt", "content": "KeyRaider is malware that steals Apple account credentials and other data from jailbroken iOS devices. It also has ransomware functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-392", "filename": "mitre_software_v17.txt", "content": "KGH_SPY is a modular suite of tools used by Kimsuky for reconnaissance, information stealing, and backdoor capabilities. KGH_SPY derived its name from PDB paths and internal names found in samples containing \"KGH\".", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-393", "filename": "mitre_software_v17.txt", "content": "KillDisk is a disk-wiping tool designed to overwrite files with random data to render the OS unbootable. It was first observed as a component of BlackEnergy malware during cyber attacks against Ukraine in 2015. KillDisk has since evolved into stand-alone malware used by a variety of threat actors against additional targets in Europe and Latin America; in 2016 a ransomware component was also incorporated into some KillDisk variants.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-394", "filename": "mitre_software_v17.txt", "content": "Kinsing is Golang-based malware that runs a cryptocurrency miner and attempts to spread itself to other hosts in the victim environment.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-395", "filename": "mitre_software_v17.txt", "content": "Kivars is a modular remote access tool (RAT), derived from the Bifrost RAT, that was used by BlackTech in a 2010 campaign.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-396", "filename": "mitre_software_v17.txt", "content": "Koadic is a Windows post-exploitation framework and penetration testing tool that is publicly available on GitHub. Koadic has several options for staging payloads and creating implants, and performs most of its operations using Windows Script Host.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-397", "filename": "mitre_software_v17.txt", "content": "Kobalos is a multi-platform backdoor that can be used against Linux, FreeBSD, and Solaris. Kobalos has been deployed against high profile targets, including high-performance computers, academic servers, an endpoint security vendor, and a large internet service provider; it has been found in Europe, North America, and Asia. Kobalos was first identified in late 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-398", "filename": "mitre_software_v17.txt", "content": "KOCTOPUS's batch variant is loader used by LazyScripter since 2018 to launch Octopus and Koadic and, in some cases, QuasarRAT. KOCTOPUS also has a VBA variant that has the same functionality as the batch version.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-399", "filename": "mitre_software_v17.txt", "content": "Komplex is a backdoor that has been used by APT28 on OS X and appears to be developed in a similar manner to XAgentOSX  .", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-400", "filename": "mitre_software_v17.txt", "content": "KOMPROGO is a signature backdoor used by APT32 that is capable of process, file, and registry management.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-401", "filename": "mitre_software_v17.txt", "content": "KONNI is a remote access tool that security researchers assess has been used by North Korean cyber actors since at least 2014. KONNI has significant code overlap with the NOKKI malware family, and has been linked to several suspected North Korean campaigns targeting political organizations in Russia, East Asia, Europe and the Middle East; there is some evidence potentially linking KONNI to APT37.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-402", "filename": "mitre_software_v17.txt", "content": "KOPILUWAK is a JavaScript-based reconnaissance tool that has been used for victim profiling and C2 since at least 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-403", "filename": "mitre_software_v17.txt", "content": "Kwampirs is a backdoor Trojan used by Orangeworm. Kwampirs has been found on machines which had software installed for the use and control of high-tech imaging devices such as X-Ray and MRI machines. Kwampirs has multiple technical overlaps with Shamoon based on reverse engineering analysis.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-404", "filename": "mitre_software_v17.txt", "content": "Latrodectus is a Windows malware downloader that has been used since at least 2023 to download and execute additional payloads and modules. Latrodectus has most often been distributed through email campaigns, primarily by TA577 and TA578, and has infrastructure overlaps with historic IcedID operations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-405", "filename": "mitre_software_v17.txt", "content": "LaZagne is a post-exploitation, open-source tool used to recover stored passwords on a system. It has modules for Windows, Linux, and OSX, but is mainly focused on Windows systems. LaZagne is publicly available on GitHub.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-406", "filename": "mitre_software_v17.txt", "content": "LightNeuron is a sophisticated backdoor that has targeted Microsoft Exchange servers since at least 2014. LightNeuron has been used by Turla to target diplomatic and foreign affairs-related organizations. The presence of certain strings in the malware suggests a Linux variant of LightNeuron exists.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-407", "filename": "mitre_software_v17.txt", "content": "First observed in 2018, LightSpy is a modular malware family that initially targeted iOS devices in Southern Asia before expanding to Android and macOS platforms. It consists of a downloader, a main executable that manages network communications, and functionality-specific modules, typically implemented as .dylib files (iOS, macOS) or .apk files (Android). LightSpy can collect VoIP call recordings, SMS messages, and credential stores, which are then exfiltrated to a command and control (C2) server.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-408", "filename": "mitre_software_v17.txt", "content": "LIGHTWIRE is a web shell written in Perl that was used during Cutting Edge to maintain access and enable command execution by imbedding into the legitimate compcheckresult.cgi component of Ivanti Secure Connect VPNs.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-409", "filename": "mitre_software_v17.txt", "content": "Line Dancer is a memory-only Lua-based shellcode loader associated with the ArcaneDoor campaign. Line Dancer allows an adversary to upload and execute arbitrary shellcode on victim devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-410", "filename": "mitre_software_v17.txt", "content": "Line Runner is a persistent backdoor and web shell allowing threat actors to upload and execute arbitrary Lua scripts. Line Runner is associated with the ArcaneDoor campaign.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-411", "filename": "mitre_software_v17.txt", "content": "Linfo is a rootkit trojan used by Elderwood to open a backdoor on compromised hosts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-412", "filename": "mitre_software_v17.txt", "content": "Linux Rabbit is malware that targeted Linux servers and IoT devices in a campaign lasting from August to October 2018. It shares code with another strain of malware known as Rabbot. The goal of the campaign was to install cryptocurrency miners onto the targeted servers and devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-413", "filename": "mitre_software_v17.txt", "content": "LiteDuke is a third stage backdoor that was used by APT29, primarily in 2014-2015. LiteDuke used the same dropper as PolyglotDuke, and was found on machines also compromised by MiniDuke.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-414", "filename": "mitre_software_v17.txt", "content": "LitePower is a downloader and second stage malware that has been used by WIRTE since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-415", "filename": "mitre_software_v17.txt", "content": "LITTLELAMB.WOOLTEA is a backdoor that was used by UNC5325 during Cutting Edge to deploy malware on targeted Ivanti Connect Secure VPNs and to establish persistence across system upgrades and patches.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-416", "filename": "mitre_software_v17.txt", "content": "Lizar is a modular remote access tool written using the .NET Framework that shares structural similarities to Carbanak. It has likely been used by FIN7 since at least February 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-417", "filename": "mitre_software_v17.txt", "content": "LockBit 2.0 is an affiliate-based Ransomware-as-a-Service (RaaS) that has been in use since at least June 2021 as the successor to LockBit Ransomware. LockBit 2.0 has versions capable of infecting Windows and VMware ESXi virtual machines, and has been observed targeting multiple industry verticals globally.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-418", "filename": "mitre_software_v17.txt", "content": "LockBit 3.0 is an evolution of the LockBit Ransomware-as-a-Service (RaaS) offering with similarities to BlackMatter and BlackCat ransomware. LockBit 3.0 has been in use since at least June 2022 and features enhanced defense evasion and exfiltration tactics, robust encryption methods for Windows and VMware ESXi systems, and a more refined RaaS structure over its predecessors such as LockBit 2.0.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-419", "filename": "mitre_software_v17.txt", "content": "LockerGoga is ransomware that was first reported in January 2019, and has been tied to various attacks on European companies, including industrial and manufacturing firms.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-420", "filename": "mitre_software_v17.txt", "content": "LoFiSe has been used by ToddyCat since at least 2023 to identify and collect files of interest on targeted systems.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-421", "filename": "mitre_software_v17.txt", "content": "LoJax is a UEFI rootkit used by APT28 to persist remote access software on targeted systems.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-422", "filename": "mitre_software_v17.txt", "content": "Lokibot is a widely distributed information stealer that was first reported in 2015. It is designed to steal sensitive information such as usernames, passwords, cryptocurrency wallets, and other credentials. Lokibot can also create a backdoor into infected systems to allow an attacker to install additional payloads.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-423", "filename": "mitre_software_v17.txt", "content": "LookBack is a remote access trojan written in C++ that was used against at least three US utility companies in July 2019. The TALONITE activity group has been observed using LookBack.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-424", "filename": "mitre_software_v17.txt", "content": "LoudMiner is a cryptocurrency miner which uses virtualization software to siphon system resources. The miner has been bundled with pirated copies of Virtual Studio Technology (VST) for Windows and macOS.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-425", "filename": "mitre_software_v17.txt", "content": "LOWBALL is malware used by admin@338. It was used in August 2015 in email messages targeting Hong Kong-based media organizations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-426", "filename": "mitre_software_v17.txt", "content": "Lslsass is a publicly-available tool that can dump active logon session password hashes from the lsass process.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-427", "filename": "mitre_software_v17.txt", "content": "Lucifer is a crypto miner and DDoS hybrid malware that leverages well-known exploits to spread laterally on Windows platforms.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-428", "filename": "mitre_software_v17.txt", "content": "Lumma Stealer is an information stealer malware family in use since at least 2022. Lumma Stealer is a Malware as a Service (MaaS) where captured data has been sold in criminal markets to Initial Access Brokers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-429", "filename": "mitre_software_v17.txt", "content": "LunarLoader is the loader component for the LunarWeb and LunarMail backdoors that has been used by Turla since at least 2020 including against a European ministry of foreign affairs (MFA). LunarLoader has been observed as a standalone and as a part of trojanized open-source software such as AdmPwd.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-430", "filename": "mitre_software_v17.txt", "content": "LunarMail is a backdoor that has been used by Turla since at least 2020 including in a compromise of a European ministry of foreign affairs (MFA) in conjunction with LunarLoader and LunarWeb. LunarMail is designed to be deployed on workstations and can use email messages and Steganography in command and control.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-430", "line_number": 430, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-431", "filename": "mitre_software_v17.txt", "content": "LunarWeb is a backdoor that has been used by Turla since at least 2020 including in a compromise of a European ministry of foreign affairs (MFA) together with LunarLoader and LunarMail. LunarWeb has only been observed deployed against servers and can use Steganography to obfuscate command and control.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-432", "filename": "mitre_software_v17.txt", "content": "Lurid is a malware family that has been used by several groups, including PittyTiger, in targeted attacks as far back as 2006.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-433", "filename": "mitre_software_v17.txt", "content": "Machete is a cyber espionage toolset used by Machete. It is a Python-based backdoor targeting Windows machines that was first observed in 2010.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-434", "filename": "mitre_software_v17.txt", "content": "MacMa is a macOS-based backdoor with a large set of functionalities to control and exfiltrate files from a compromised computer. MacMa has been observed in the wild since November 2021. MacMa shares command and control and unique libraries with MgBot and Nightdoor, indicating a relationship with the Daggerfly threat actor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-435", "filename": "mitre_software_v17.txt", "content": "macOS.OSAMiner is a Monero mining trojan that was first observed in 2018; security researchers assessed macOS.OSAMiner may have been circulating since at least 2015. macOS.OSAMiner is known for embedding one run-only AppleScript into another, which helped the malware evade full analysis for five years due to a lack of Apple event (AEVT) analysis tools.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-436", "filename": "mitre_software_v17.txt", "content": "MacSpy is a malware-as-a-service offered on the darkweb  .", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-437", "filename": "mitre_software_v17.txt", "content": "Mafalda is a flexible interactive implant that has been used by Metador. Security researchers assess the Mafalda name may be inspired by an Argentinian cartoon character that has been popular as a means of political commentary since the 1960s.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-438", "filename": "mitre_software_v17.txt", "content": "MagicRAT is a remote access tool developed in C++ and exclusively used by the Lazarus Group threat actor in operations. MagicRAT allows for arbitrary command execution on victim machines and provides basic remote access functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-439", "filename": "mitre_software_v17.txt", "content": "MailSniper is a penetration testing tool for searching through email in a Microsoft Exchange environment for specific terms (passwords, insider intel, network architecture information, etc.). It can be used by a non-administrative user to search their own email, or by an Exchange administrator to search the mailboxes of every user in a domain.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-440", "filename": "mitre_software_v17.txt", "content": "Mandrake is a sophisticated Android espionage platform that has been active in the wild since at least 2016. Mandrake is very actively maintained, with sophisticated features and attacks that are executed with surgical precision.Mandrake has gone undetected for several years by providing legitimate, ad-free applications with social media and real reviews to back the apps. The malware is only activated when the operators issue a specific command.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-441", "filename": "mitre_software_v17.txt", "content": "Mango is a first-stage backdoor written in C#/.NET that was used by OilRig during the Juicy Mix campaign. Mango is the successor to Solar and includes additional exfiltration capabilities, the use of native APIs, and added detection evasion code.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-442", "filename": "mitre_software_v17.txt", "content": "Manjusaka is a Chinese-language intrusion framework, similar to Sliver and Cobalt Strike, with an ELF binary written in GoLang as the controller for Windows and Linux implants written in Rust. First identified in 2022, Manjusaka consists of multiple components, only one of which (a command and control module) is freely available.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-443", "filename": "mitre_software_v17.txt", "content": "MarkiRAT is a remote access Trojan (RAT) compiled with Visual Studio that has been used by Ferocious Kitten since at least 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-444", "filename": "mitre_software_v17.txt", "content": "Matryoshka is a malware framework used by CopyKittens that consists of a dropper, loader, and RAT. It has multiple versions; v1 was seen in the wild from July 2016 until January 2017. v2 has fewer commands and other minor differences.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-445", "filename": "mitre_software_v17.txt", "content": "MazarBOT is Android malware that was distributed via SMS in Denmark in 2016.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-446", "filename": "mitre_software_v17.txt", "content": "Maze ransomware, previously known as \"ChaCha\", was discovered in May 2019. In addition to encrypting files on victim machines for impact, Maze operators conduct information stealing campaigns prior to encryption and post the information online to extort affected companies.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-447", "filename": "mitre_software_v17.txt", "content": "MCMD is a remote access tool that provides remote command shell capability used by Dragonfly 2.0.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-448", "filename": "mitre_software_v17.txt", "content": "MechaFlounder is a python-based remote access tool (RAT) that has been used by APT39. The payload uses a combination of actor developed code and code snippets freely available online in development communities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-449", "filename": "mitre_software_v17.txt", "content": "meek is an open-source Tor plugin that tunnels Tor traffic through HTTPS connections.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-450", "filename": "mitre_software_v17.txt", "content": "MegaCortex is ransomware that first appeared in May 2019.  MegaCortex has mainly targeted industrial organizations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-451", "filename": "mitre_software_v17.txt", "content": "Megazord is a Rust-based variant of Akira ransomware that has been in use since at least August 2023 to target Windows environments. Megazord has been attributed to the Akira group based on overlapping infrastructure though is possibly not exclusive to the group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-452", "filename": "mitre_software_v17.txt", "content": "Melcoz is a banking trojan family built from the open source tool Remote Access PC. Melcoz was first observed in attacks in Brazil and since 2018 has spread to Chile, Mexico, Spain, and Portugal.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-453", "filename": "mitre_software_v17.txt", "content": "MESSAGETAP is a data mining malware family deployed by APT41 into telecommunications networks to monitor and save SMS traffic from specific phone numbers, IMSI numbers, or that contain specific keywords.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-454", "filename": "mitre_software_v17.txt", "content": "metaMain is a backdoor used by Metador to maintain long-term access to compromised machines; it has also been used to decrypt Mafalda into memory.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-455", "filename": "mitre_software_v17.txt", "content": "Metamorfo is a Latin-American banking trojan operated by a Brazilian cybercrime group that has been active since at least April 2018. The group focuses on targeting banks and cryptocurrency services in Brazil and Mexico.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-456", "filename": "mitre_software_v17.txt", "content": "Meteor is a wiper that was used against Iranian government organizations, including Iranian Railways, the Ministry of Roads, and Urban Development systems, in July 2021. Meteor is likely a newer version of similar wipers called Stardust and Comet that were reportedly used by a group called \"Indra\" since at least 2019 against private companies in Syria.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-457", "filename": "mitre_software_v17.txt", "content": "MgBot is a modular malware framework exclusively associated with Daggerfly operations since at least 2012. MgBot was developed in C++ and features a module design with multiple available plugins that have been under active development through 2024.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-458", "filename": "mitre_software_v17.txt", "content": "Micropsia is a remote access tool written in Delphi.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-459", "filename": "mitre_software_v17.txt", "content": "Milan is a backdoor implant based on DanBot that was written in Visual C++ and .NET. Milan has been used by HEXANE since at least June 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-460", "filename": "mitre_software_v17.txt", "content": "Mimikatz is a credential dumper capable of obtaining plaintext Windows account logins and passwords, along with many other features that make it useful for testing the security of networks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-461", "filename": "mitre_software_v17.txt", "content": "MimiPenguin is a credential dumper, similar to Mimikatz, designed specifically for Linux platforms.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-462", "filename": "mitre_software_v17.txt", "content": "Miner-C is malware that mines victims for the Monero cryptocurrency. It has targeted FTP servers and Network Attached Storage (NAS) devices to spread.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-463", "filename": "mitre_software_v17.txt", "content": "MiniDuke is malware that was used by APT29 from 2010 to 2015. The MiniDuke toolset consists of multiple downloader and backdoor components. The loader has been used with other MiniDuke components as well as in conjunction with CosmicDuke and PinchDuke.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-464", "filename": "mitre_software_v17.txt", "content": "MirageFox is a remote access tool used against Windows systems. It appears to be an upgraded version of a tool known as Mirage, which is a RAT believed to originate in 2012.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-465", "filename": "mitre_software_v17.txt", "content": "Mis-Type is a backdoor hybrid that was used in Operation Dust Storm by 2012.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-466", "filename": "mitre_software_v17.txt", "content": "Misdat is a backdoor that was used in Operation Dust Storm from 2010 to 2011.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-467", "filename": "mitre_software_v17.txt", "content": "Mispadu is a banking trojan written in Delphi that was first observed in 2019 and uses a Malware-as-a-Service (MaaS) business model. This malware is operated, managed, and sold by the Malteiro cybercriminal group. Mispadu has mainly been used to target victims in Brazil and Mexico, and has also had confirmed operations throughout Latin America and Europe.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-468", "filename": "mitre_software_v17.txt", "content": "Mivast is a backdoor that has been used by Deep Panda. It was reportedly used in the Anthem breach.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-469", "filename": "mitre_software_v17.txt", "content": "MobileOrder is a Trojan intended to compromise Android mobile devices. It has been used by Scarlet Mimic.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-470", "filename": "mitre_software_v17.txt", "content": "MoleNet is a downloader tool with backdoor capabilities that has been observed in use since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-471", "filename": "mitre_software_v17.txt", "content": "Moneybird is a ransomware variant written in C++ associated with Agrius operations. The name \"Moneybird\" is contained in the malware's ransom note and as strings in the executable.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-472", "filename": "mitre_software_v17.txt", "content": "Mongall is a backdoor that has been used since at least 2013, including by Aoqin Dragon.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-473", "filename": "mitre_software_v17.txt", "content": "Monokle is targeted, sophisticated mobile surveillanceware. It is developed for Android, but there are some code artifacts that suggests an iOS version may be in development.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-474", "filename": "mitre_software_v17.txt", "content": "MoonWind is a remote access tool (RAT) that was used in 2016 to target organizations in Thailand.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-475", "filename": "mitre_software_v17.txt", "content": "More_eggs is a JScript backdoor used by Cobalt Group and FIN6. Its name was given based on the variable \"More_eggs\" being present in its code. There are at least two different versions of the backdoor being used, version 2.0 and version 4.4.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-476", "filename": "mitre_software_v17.txt", "content": "Mori is a backdoor that has been used by MuddyWater since at least January 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-477", "filename": "mitre_software_v17.txt", "content": "Mosquito is a Win32 backdoor that has been used by Turla. Mosquito is made up of three parts: the installer, the launcher, and the backdoor. The main backdoor is called CommanderDLL and is launched by the loader program.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-478", "filename": "mitre_software_v17.txt", "content": "MultiLayer Wiper is wiper malware written in .NET associated with Agrius operations. Observed samples of MultiLayer Wiper have an anomalous, future compilation date suggesting possible metadata manipulation.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-479", "filename": "mitre_software_v17.txt", "content": "MURKYTOP is a reconnaissance tool used by Leviathan.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-480", "filename": "mitre_software_v17.txt", "content": "Mythic is an open source, cross-platform post-exploitation/command and control platform. Mythic is designed to \"plug-n-play\" with various agents and communication channels. Deployed Mythic C2 servers have been observed as part of potentially malicious infrastructure.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-481", "filename": "mitre_software_v17.txt", "content": "Naid is a trojan used by Elderwood to open a backdoor on compromised hosts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-482", "filename": "mitre_software_v17.txt", "content": "NanHaiShu is a remote access tool and JScript backdoor used by Leviathan. NanHaiShu has been used to target government and private-sector organizations that have relations to the South China Sea dispute.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-483", "filename": "mitre_software_v17.txt", "content": "NanoCore is a modular remote access tool developed in .NET that can be used to spy on victims and steal information. It has been used by threat actors since 2013.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-484", "filename": "mitre_software_v17.txt", "content": "NativeZone is the name given collectively to disposable custom Cobalt Strike loaders used by APT29 since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-485", "filename": "mitre_software_v17.txt", "content": "NavRAT is a remote access tool designed to upload, download, and execute files. It has been observed in attacks targeting South Korea.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-486", "filename": "mitre_software_v17.txt", "content": "NBTscan is an open source tool that has been used by state groups to conduct internal reconnaissance within a compromised network.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-487", "filename": "mitre_software_v17.txt", "content": "nbtstat is a utility used to troubleshoot NetBIOS name resolution.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-488", "filename": "mitre_software_v17.txt", "content": "NDiskMonitor is a custom backdoor written in .NET that appears to be unique to Patchwork.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-489", "filename": "mitre_software_v17.txt", "content": "Nebulae Is a backdoor that has been used by Naikon  since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-490", "filename": "mitre_software_v17.txt", "content": "Neo-reGeorg is an open-source web shell designed as a restructuring of reGeorg with improved usability, security, and fixes for exising reGeorg bugs.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-491", "filename": "mitre_software_v17.txt", "content": "Neoichor is C2 malware used by Ke3chang since at least 2019; similar malware families used by the group include Leeson and Numbldea.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-492", "filename": "mitre_software_v17.txt", "content": "Nerex is a Trojan used by Elderwood to open a backdoor on compromised hosts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-493", "filename": "mitre_software_v17.txt", "content": "The Net utility is a component of the Windows operating system. It is used in command-line operations for control of users, groups, services, and network connections. Net has a great deal of functionality,  much of which is useful for an adversary, such as gathering system and network information for Discovery, moving laterally through SMB/Windows Admin Shares using net use commands, and interacting with services. The net1.exe utility is executed for certain functionality when net.exe is run and can be used directly in commands such as net1 user.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-494", "filename": "mitre_software_v17.txt", "content": "Net Crawler is an intranet worm capable of extracting credentials using credential dumpers and spreading to systems on a network over SMB by brute forcing accounts with recovered passwords and using PsExec to execute a copy of Net Crawler.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-495", "filename": "mitre_software_v17.txt", "content": "NETEAGLE is a backdoor developed by APT30 with compile dates as early as 2008. It has two main variants known as \"Scout\" and \"Norton.\"", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-496", "filename": "mitre_software_v17.txt", "content": "netsh is a scripting utility used to interact with networking components on local or remote systems.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-497", "filename": "mitre_software_v17.txt", "content": "netstat is an operating system utility that displays active TCP connections, listening ports, and network statistics.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-498", "filename": "mitre_software_v17.txt", "content": "NetTraveler is malware that has been used in multiple cyber espionage campaigns for basic surveillance of victims. The earliest known samples have timestamps back to 2005, and the largest number of observed samples were created between 2010 and 2013.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-499", "filename": "mitre_software_v17.txt", "content": "Netwalker is fileless ransomware written in PowerShell and executed directly in memory.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-500", "filename": "mitre_software_v17.txt", "content": "NETWIRE is a publicly available, multiplatform remote administration tool (RAT) that has been used by criminal and APT groups since at least 2012.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-501", "filename": "mitre_software_v17.txt", "content": "NGLite is a backdoor Trojan that is only capable of running commands received through its C2 channel. While the capabilities are standard for a backdoor, NGLite uses a novel C2 channel that leverages a decentralized network based on the legitimate NKN to communicate between the backdoor and the actors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-502", "filename": "mitre_software_v17.txt", "content": "ngrok is a legitimate reverse proxy tool that can create a secure tunnel to servers located behind firewalls or on local machines that do not have a public IP. ngrok has been leveraged by threat actors in several campaigns including use for lateral movement and data exfiltration.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-503", "filename": "mitre_software_v17.txt", "content": "NICECURL is a VBScript-based backdoor used by APT42 to download additional modules.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-504", "filename": "mitre_software_v17.txt", "content": "Nidiran is a custom backdoor developed and used by Suckfly. It has been delivered via strategic web compromise.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-505", "filename": "mitre_software_v17.txt", "content": "NightClub is a modular implant written in C++ that has been used by MoustachedBouncer since at least 2014.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-506", "filename": "mitre_software_v17.txt", "content": "Nightdoor is a backdoor exclusively associated with Daggerfly operations. Nightdoor uses common libraries with MgBot and MacMa, linking these malware families together.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-507", "filename": "mitre_software_v17.txt", "content": "Ninja is a malware developed in C++ that has been used by ToddyCat to penetrate networks and control remote systems since at least 2020.  Ninja is possibly part of a post exploitation toolkit exclusively used by ToddyCat and allows multiple operators to work simultaneously on the same machine. Ninja has been used against government and military entities in Europe and Asia and observed in specific infection chains being deployed by Samurai.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-508", "filename": "mitre_software_v17.txt", "content": "njRAT is a remote access tool (RAT) that was first observed in 2012. It has been used by threat actors in the Middle East.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-509", "filename": "mitre_software_v17.txt", "content": "NKAbuse is a Go-based, multi-platform malware abusing NKN (New Kind of Network) technology for data exchange between peers, functioning as a potent implant, and equipped with both flooder and backdoor capabilities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-510", "filename": "mitre_software_v17.txt", "content": "Nltest is a Windows command-line utility used to list domain controllers and enumerate domain trusts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-511", "filename": "mitre_software_v17.txt", "content": "NOKKI is a modular remote access tool. The earliest observed attack using NOKKI was in January 2018. NOKKI has significant code overlap with the KONNI malware family. There is some evidence potentially linking NOKKI to APT37.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-512", "filename": "mitre_software_v17.txt", "content": "NotCompatible is an Android malware family that was used between at least 2014 and 2016. It has multiple variants that have become more sophisticated over time.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-513", "filename": "mitre_software_v17.txt", "content": "NotPetya is malware that was used by Sandworm Team in a worldwide attack starting on June 27, 2017. While NotPetya appears as a form of ransomware, its main purpose was to destroy data and disk structures on compromised systems; the attackers never intended to make the encrypted data recoverable. As such, NotPetya may be more appropriately thought of as a form of wiper malware. NotPetya contains worm-like features to spread itself across a computer network using the SMBv1 exploits EternalBlue and EternalRomance.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-514", "filename": "mitre_software_v17.txt", "content": "NPPSPY is an implementation of a theoretical mechanism first presented in 2004 for capturing credentials submitted to a Windows system via a rogue Network Provider API item. NPPSPY captures credentials following submission and writes them to a file on the victim system for follow-on exfiltration.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-515", "filename": "mitre_software_v17.txt", "content": "OBAD is an Android malware family.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-516", "filename": "mitre_software_v17.txt", "content": "ObliqueRAT is a remote access trojan, similar to Crimson, that has been in use by Transparent Tribe since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-517", "filename": "mitre_software_v17.txt", "content": "OceanSalt is a Trojan that was used in a campaign targeting victims in South Korea, United States, and Canada. OceanSalt shares code similarity with SpyNote RAT, which has been linked to APT1.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-518", "filename": "mitre_software_v17.txt", "content": "Octopus is a Windows Trojan written in the Delphi programming language that has been used by Nomadic Octopus to target government organizations in Central Asia since at least 2014.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-519", "filename": "mitre_software_v17.txt", "content": "ODAgent is a C#/.NET downloader that has been used by OilRig since at least 2022 including against target organizations in Israel to download and execute payloads and to exfiltrate staged files.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-520", "filename": "mitre_software_v17.txt", "content": "OilBooster is a downloader written in Microsoft Visual C/C++ that has been used by OilRig since at least 2022 including against target organizations in Israel to download and execute files and for exfiltration.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-521", "filename": "mitre_software_v17.txt", "content": "OilCheck is a C#/.NET downloader that has been used by OilRig since at least 2022 including against targets in Israel. OilCheck uses draft messages created in a shared email account for C2 communication.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-522", "filename": "mitre_software_v17.txt", "content": "Okrum is a Windows backdoor that has been seen in use since December 2016 with strong links to Ke3chang.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-523", "filename": "mitre_software_v17.txt", "content": "OLDBAIT is a credential harvester used by APT28.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-524", "filename": "mitre_software_v17.txt", "content": "OldBoot is an Android malware family.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-525", "filename": "mitre_software_v17.txt", "content": "Olympic Destroyer is malware that was used by Sandworm Team against the 2018 Winter Olympics, held in Pyeongchang, South Korea. The main purpose of the malware was to render infected computer systems inoperable. The malware leverages various native Windows utilities and API calls to carry out its destructive tasks. Olympic Destroyer has worm-like features to spread itself across a computer network in order to maximize its destructive impact.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-526", "filename": "mitre_software_v17.txt", "content": "OnionDuke is malware that was used by APT29 from 2013 to 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-527", "filename": "mitre_software_v17.txt", "content": "OopsIE is a Trojan used by OilRig to remotely execute commands as well as upload/download files to/from victims.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-528", "filename": "mitre_software_v17.txt", "content": "Orz is a custom JavaScript backdoor used by Leviathan. It was observed being used in 2014 as well as in August 2017 when it was dropped by Microsoft Publisher files.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-529", "filename": "mitre_software_v17.txt", "content": "OSInfo is a custom tool used by APT3 to do internal discovery on a victim's computer and network.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-530", "filename": "mitre_software_v17.txt", "content": "OSX/Shlayer is a Trojan designed to install adware on macOS that was first discovered in 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-531", "filename": "mitre_software_v17.txt", "content": "OSX_OCEANLOTUS.D is a macOS backdoor used by APT32. First discovered in 2015, APT32 has continued to make improvements using a plugin architecture to extend capabilities, specifically using .dylib files. OSX_OCEANLOTUS.D can also determine it's permission level and execute according to access type (root or user).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-532", "filename": "mitre_software_v17.txt", "content": "Out1 is a remote access tool written in python and used by MuddyWater since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-533", "filename": "mitre_software_v17.txt", "content": "OutSteel is a file uploader and document stealer developed with the scripting language AutoIT that has been used by Saint Bear since at least March 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-534", "filename": "mitre_software_v17.txt", "content": "OwaAuth is a Web shell and credential stealer deployed to Microsoft Exchange servers that appears to be exclusively used by Threat Group-3390.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-535", "filename": "mitre_software_v17.txt", "content": "P.A.S. Webshell is a publicly available multifunctional PHP webshell in use since at least 2016 that provides remote access and execution on target web servers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-536", "filename": "mitre_software_v17.txt", "content": "P2P ZeuS is a closed-source fork of the leaked version of the ZeuS botnet. It presents improvements over the leaked version, including a peer-to-peer architecture.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-537", "filename": "mitre_software_v17.txt", "content": "P8RAT is a fileless malware used by menuPass to download and execute payloads since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-538", "filename": "mitre_software_v17.txt", "content": "PACEMAKER is a credential stealer that was used by APT5 as early as 2020 including activity against US Defense Industrial Base (DIB) companies.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-539", "filename": "mitre_software_v17.txt", "content": "Pacu is an open-source AWS exploitation framework. The tool is written in Python and publicly available on GitHub.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-540", "filename": "mitre_software_v17.txt", "content": "Pallas is mobile surveillanceware that was custom-developed by Dark Caracal.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-541", "filename": "mitre_software_v17.txt", "content": "Pandora is a multistage kernel rootkit with backdoor functionality that has been in use by Threat Group-3390 since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-542", "filename": "mitre_software_v17.txt", "content": "Pasam is a trojan used by Elderwood to open a backdoor on compromised hosts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-543", "filename": "mitre_software_v17.txt", "content": "Pass-The-Hash Toolkit is a toolkit that allows an adversary to \"pass\" a password hash (without knowing the original password) to log in to systems.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-544", "filename": "mitre_software_v17.txt", "content": "Pay2Key is a ransomware written in C++ that has been used by Fox Kitten since at least July 2020 including campaigns against Israeli companies. Pay2Key has been incorporated with a leak site to display stolen sensitive information to further pressure victims into payment.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-545", "filename": "mitre_software_v17.txt", "content": "Pcexter is an uploader that has been used by ToddyCat since at least 2023 to exfiltrate stolen files.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-546", "filename": "mitre_software_v17.txt", "content": "PcShare is an open source remote access tool that has been modified and used by Chinese threat actors, most notably during the FunnyDream campaign since late 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-547", "filename": "mitre_software_v17.txt", "content": "Pegasus for Android is the Android version of malware that has reportedly been linked to the NSO Group.   The iOS version is tracked separately under Pegasus for iOS.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-548", "filename": "mitre_software_v17.txt", "content": "Pegasus for iOS is the iOS version of malware that has reportedly been linked to the NSO Group. It has been advertised and sold to target high-value victims. The Android version is tracked separately under Pegasus for Android.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-549", "filename": "mitre_software_v17.txt", "content": "Peirates is a post-exploitation Kubernetes exploitation framework with a focus on gathering service account tokens for lateral movement and privilege escalation. The tool is written in GoLang and publicly available on GitHub.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-550", "filename": "mitre_software_v17.txt", "content": "Penquin is a remote access trojan (RAT) with multiple versions used by Turla to target Linux systems since at least 2014.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-551", "filename": "mitre_software_v17.txt", "content": "Peppy is a Python-based remote access Trojan, active since at least 2012, with similarities to Crimson.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-552", "filename": "mitre_software_v17.txt", "content": "Phenakite is a mobile malware that is used by APT-C-23 to target iOS devices. According to several reports, Phenakite was developed to fill a tooling gap and to target those who owned iPhones instead of Windows desktops or Android phones.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-553", "filename": "mitre_software_v17.txt", "content": "PHOREAL is a signature backdoor used by APT32.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-554", "filename": "mitre_software_v17.txt", "content": "Pikabot is a backdoor used for initial access and follow-on tool deployment active since early 2023. Pikabot is notable for extensive use of multiple encoding, encryption, and defense evasion mechanisms to evade defenses and avoid analysis. Pikabot has some overlaps with QakBot, but insufficient evidence exists to definitively link these two malware families. Pikabot is frequently used to deploy follow on tools such as Cobalt Strike or ransomware variants.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-555", "filename": "mitre_software_v17.txt", "content": "Pillowmint is a point-of-sale malware used by FIN7 designed to capture credit card information.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-556", "filename": "mitre_software_v17.txt", "content": "PinchDuke is malware that was used by APT29 from 2008 to 2010.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-557", "filename": "mitre_software_v17.txt", "content": "Ping is an operating system utility commonly used to troubleshoot and verify network connections.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-558", "filename": "mitre_software_v17.txt", "content": "PingPull is a remote access Trojan (RAT) written in Visual C++ that has been used by GALLIUM since at least June 2022. PingPull has been used to target telecommunications companies, financial institutions, and government entities in Afghanistan, Australia, Belgium, Cambodia, Malaysia, Mozambique, the Philippines, Russia, and Vietnam.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-559", "filename": "mitre_software_v17.txt", "content": "PipeMon is a multi-stage modular backdoor used by Winnti Group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-560", "filename": "mitre_software_v17.txt", "content": "Pisloader is a malware family that is notable due to its use of DNS as a C2 protocol as well as its use of anti-analysis tactics. It has been used by APT18 and is similar to another malware family, HTTPBrowser, that has been used by the group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-561", "filename": "mitre_software_v17.txt", "content": "PITSTOP is a backdoor that was deployed on compromised Ivanti Connect Secure VPNs during Cutting Edge to enable command execution and file read/write.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-562", "filename": "mitre_software_v17.txt", "content": "PJApps is an Android malware family.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-563", "filename": "mitre_software_v17.txt", "content": "PLAINTEE is a malware sample that has been used by Rancor in targeted attacks in Singapore and Cambodia.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-564", "filename": "mitre_software_v17.txt", "content": "Playcrypt is a ransomware that has been used by Play since at least 2022 in attacks against against the business, government, critical infrastructure, healthcare, and media sectors in North America, South America, and Europe. Playcrypt derives its name from adding the .play extension to encrypted files and has overlap with tactics and tools associated with Hive and Nokoyawa ransomware and infrastructure associated with Quantum ransomware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-565", "filename": "mitre_software_v17.txt", "content": "PLC-Blaster is a piece of proof-of-concept malware that runs on Siemens S7 PLCs. This worm locates other Siemens S7 PLCs on the network and attempts to infect them.  Once this worm has infected its target and attempted to infect other devices on the network, the worm can then run one of many modules.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-566", "filename": "mitre_software_v17.txt", "content": "PLEAD is a remote access tool (RAT) and downloader used by BlackTech in targeted attacks in East Asia including Taiwan, Japan, and Hong Kong. PLEAD has also been referred to as TSCookie, though more recent reporting indicates likely separation between the two. PLEAD was observed in use as early as March 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-567", "filename": "mitre_software_v17.txt", "content": "PlugX is a remote access tool (RAT) with modular plugins that has been used by multiple threat groups.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-568", "filename": "mitre_software_v17.txt", "content": "pngdowner is malware used by Putter Panda. It is a simple tool with limited functionality and no persistence mechanism, suggesting it is used only as a simple \"download-and-execute\" utility.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-569", "filename": "mitre_software_v17.txt", "content": "PoetRAT is a remote access trojan (RAT) that was first identified in April 2020. PoetRAT has been used in multiple campaigns against the private and public sectors in Azerbaijan, including ICS and SCADA systems in the energy sector. The STIBNITE activity group has been observed using the malware. PoetRAT derived its name from references in the code to poet William Shakespeare.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-570", "filename": "mitre_software_v17.txt", "content": "PoisonIvy is a popular remote access tool (RAT) that has been used by many groups.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-571", "filename": "mitre_software_v17.txt", "content": "PolyglotDuke is a downloader that has been used by APT29 since at least 2013. PolyglotDuke has been used to drop MiniDuke.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-572", "filename": "mitre_software_v17.txt", "content": "Pony is a credential stealing malware, though has also been used among adversaries for its downloader capabilities. The source code for Pony Loader 1.0 and 2.0 were leaked online, leading to their use by various threat actors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-573", "filename": "mitre_software_v17.txt", "content": "POORAIM is a backdoor used by APT37 in campaigns since at least 2014.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-574", "filename": "mitre_software_v17.txt", "content": "PoshC2 is an open source remote administration and post-exploitation framework that is publicly available on GitHub. The server-side components of the tool are primarily written in Python, while the implants are written in PowerShell. Although PoshC2 is primarily focused on Windows implantation, it does contain a basic Python dropper for Linux/macOS.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-575", "filename": "mitre_software_v17.txt", "content": "POSHSPY is a backdoor that has been used by APT29 since at least 2015. It appears to be used as a secondary backdoor used if the actors lost access to their primary backdoors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-576", "filename": "mitre_software_v17.txt", "content": "Power Loader is modular code sold in the cybercrime market used as a downloader in malware families such as Carberp, Redyms and Gapz.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-577", "filename": "mitre_software_v17.txt", "content": "PowerDuke is a backdoor that was used by APT29 in 2016. It has primarily been delivered through Microsoft Word or Excel attachments containing malicious macros.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-578", "filename": "mitre_software_v17.txt", "content": "PowerExchange is a PowerShell backdoor that has been used by OilRig since at least 2023 including against government targets in the Middle East.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-579", "filename": "mitre_software_v17.txt", "content": "PowerLess is a PowerShell-based modular backdoor that has been used by Magic Hound since at least 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-580", "filename": "mitre_software_v17.txt", "content": "PowerPunch is a lightweight downloader that has been used by Gamaredon Group since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-581", "filename": "mitre_software_v17.txt", "content": "PowerShower is a PowerShell backdoor used by Inception for initial reconnaissance and to download and execute second stage payloads.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-582", "filename": "mitre_software_v17.txt", "content": "POWERSOURCE is a PowerShell backdoor that is a heavily obfuscated and modified version of the publicly available tool DNS_TXT_Pwnage. It was observed in February 2017 in spearphishing campaigns against personnel involved with United States Securities and Exchange Commission (SEC) filings at various organizations. The malware was delivered when macros were enabled by the victim and a VBS script was dropped.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-583", "filename": "mitre_software_v17.txt", "content": "PowerSploit is an open source, offensive security framework comprised of PowerShell modules and scripts that perform a wide range of tasks related to penetration testing such as code execution, persistence, bypassing anti-virus, recon, and exfiltration.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-584", "filename": "mitre_software_v17.txt", "content": "PowerStallion is a lightweight PowerShell backdoor used by Turla, possibly as a recovery access tool to install other backdoors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-585", "filename": "mitre_software_v17.txt", "content": "POWERSTATS is a PowerShell-based first stage backdoor used by MuddyWater.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-586", "filename": "mitre_software_v17.txt", "content": "POWERTON is a custom PowerShell backdoor first observed in 2018. It has typically been deployed as a late-stage backdoor by APT33. At least two variants of the backdoor have been identified, with the later version containing improved functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-587", "filename": "mitre_software_v17.txt", "content": "PowGoop is a loader that consists of a DLL loader and a PowerShell-based downloader; it has been used by MuddyWater as their main loader.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-588", "filename": "mitre_software_v17.txt", "content": "POWRUNER is a PowerShell script that sends and receives commands to and from the C2 server.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-589", "filename": "mitre_software_v17.txt", "content": "Prestige ransomware has been used by Sandworm Team since at least March 2022, including against transportation and related logistics industries in Ukraine and Poland in October 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-590", "filename": "mitre_software_v17.txt", "content": "Prikormka is a malware family used in a campaign known as Operation Groundbait. It has predominantly been observed in Ukraine and was used as early as 2008.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-591", "filename": "mitre_software_v17.txt", "content": "ProLock is a ransomware strain that has been used in Big Game Hunting (BGH) operations since at least 2020, often obtaining initial access with QakBot. ProLock is the successor to PwndLocker ransomware which was found to contain a bug allowing decryption without ransom payment in 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-592", "filename": "mitre_software_v17.txt", "content": "Proton is a macOS backdoor focusing on data theft and credential access  .", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-593", "filename": "mitre_software_v17.txt", "content": "Proxysvc is a malicious DLL used by Lazarus Group in a campaign known as Operation GhostSecret. It has appeared to be operating undetected since 2017 and was mostly observed in higher education organizations. The goal of Proxysvc is to deliver additional payloads to the target and to maintain control for the attacker. It is in the form of a DLL that can also be executed as a standalone process.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-594", "filename": "mitre_software_v17.txt", "content": "PS1 is a loader that was used to deploy 64-bit backdoors in the CostaRicto campaign.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-595", "filename": "mitre_software_v17.txt", "content": "PsExec is a free Microsoft tool that can be used to execute a program on another computer. It is used by IT administrators and attackers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-596", "filename": "mitre_software_v17.txt", "content": "Psylo is a shellcode-based Trojan that has been used by Scarlet Mimic. It has similar characteristics as FakeM.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-597", "filename": "mitre_software_v17.txt", "content": "Pteranodon is a custom backdoor used by Gamaredon Group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-598", "filename": "mitre_software_v17.txt", "content": "PULSECHECK is a web shell written in Perl that was used by APT5 as early as 2020 including against Pulse Secure VPNs at US Defense Industrial Base (DIB) companies.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-599", "filename": "mitre_software_v17.txt", "content": "PUNCHBUGGY is a backdoor malware used by FIN8 that has been observed targeting POS networks in the hospitality industry.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-600", "filename": "mitre_software_v17.txt", "content": "PUNCHTRACK is non-persistent point of sale (POS) system malware utilized by FIN8 to scrape payment card data.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-601", "filename": "mitre_software_v17.txt", "content": "Pupy is an open source, cross-platform (Windows, Linux, OSX, Android) remote administration and post-exploitation tool.  It is written in Python and can be generated as a payload in several different ways (Windows exe, Python file, PowerShell oneliner/file, Linux elf, APK, Rubber Ducky, etc.).  Pupy is publicly available on GitHub.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-602", "filename": "mitre_software_v17.txt", "content": "pwdump is a credential dumper.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-603", "filename": "mitre_software_v17.txt", "content": "PyDCrypt is malware written in Python designed to deliver DCSrv. It has been used by Moses Staff since at least September 2021, with each sample tailored for its intended victim organization.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-604", "filename": "mitre_software_v17.txt", "content": "Pysa is a ransomware that was first used in October 2018 and has been seen to target particularly high-value finance, government and healthcare organizations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-605", "filename": "mitre_software_v17.txt", "content": "QakBot is a modular banking trojan that has been used primarily by financially-motivated actors since at least 2007. QakBot is continuously maintained and developed and has evolved from an information stealer into a delivery agent for ransomware, most notably ProLock and Egregor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-606", "filename": "mitre_software_v17.txt", "content": "QUADAGENT is a PowerShell backdoor used by OilRig.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-607", "filename": "mitre_software_v17.txt", "content": "QuasarRAT is an open-source, remote access tool that has been publicly available on GitHub since at least 2014. QuasarRAT is developed in the C# language.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-608", "filename": "mitre_software_v17.txt", "content": "Quick Assist is a remote assistance tool primarily for Microsoft Windows, although a macOS version also exists. Quick Assist allows for remote screen sharing and, with end user approval, remote control and command execution on the enabling device.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-609", "filename": "mitre_software_v17.txt", "content": "QUIETCANARY is a backdoor tool written in .NET that has been used since at least 2022 to gather and exfiltrate data from victim networks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-610", "filename": "mitre_software_v17.txt", "content": "QUIETEXIT is a novel backdoor, based on the open-source Dropbear SSH client-server software, that has been used by APT29 since at least 2021. APT29 has deployed QUIETEXIT on opaque network appliances that typically don't support antivirus or endpoint detection and response tools within a victim environment.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-611", "filename": "mitre_software_v17.txt", "content": "QuietSieve is an information stealer that has been used by Gamaredon Group since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-612", "filename": "mitre_software_v17.txt", "content": "Raccoon Stealer is an information stealer malware family active since at least 2019 as a malware-as-a-service offering sold in underground forums. Raccoon Stealer has experienced two periods of activity across two variants, from 2019 to March 2022, then resurfacing in a revised version in June 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-613", "filename": "mitre_software_v17.txt", "content": "Ragnar Locker is a ransomware that has been in use since at least December 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-614", "filename": "mitre_software_v17.txt", "content": "Raindrop is a loader used by APT29 that was discovered on some victim machines during investigations related to the SolarWinds Compromise. It was discovered in January 2021 and was likely used since at least May 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-615", "filename": "mitre_software_v17.txt", "content": "RainyDay is a backdoor tool that has been used by Naikon since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-616", "filename": "mitre_software_v17.txt", "content": "Ramsay is an information stealing malware framework designed to collect and exfiltrate sensitive documents, including from air-gapped systems. Researchers have identified overlaps between Ramsay and the Darkhotel-associated Retro malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-617", "filename": "mitre_software_v17.txt", "content": "RansomHub is a ransomware-as-a-service (RaaS) offering with Windows, ESXi, Linux, and FreeBSD versions that has been in use since at least 2024 to target organizations in multiple sectors globally. RansomHub operators may have purchased and rebranded resources from Knight  (formerly Cyclops) Ransomware which shares infrastructure, feature, and code overlaps with RansomHub.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-618", "filename": "mitre_software_v17.txt", "content": "RAPIDPULSE is a web shell that exists as a modification to a legitimate Pulse Secure file that has been used by APT5 since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-619", "filename": "mitre_software_v17.txt", "content": "RARSTONE is malware used by the Naikon group that has some characteristics similar to PlugX.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-620", "filename": "mitre_software_v17.txt", "content": "Raspberry Robin is initial access malware first identified in September 2021, and active through early 2024. The malware is notable for spreading via infected USB devices containing a malicious LNK object that, on execution, retrieves remote hosted payloads for installation. Raspberry Robin has been widely used against various industries and geographies, and as a precursor to information stealer, ransomware, and other payloads such as SocGholish, Cobalt Strike, IcedID, and Bumblebee. The DLL componenet in the Raspberry Robin infection chain is also referred to as \"Roshtyak.\" The name \"Raspberry Robin\" is used to refer to both the malware as well as the threat actor associated with its use, although the Raspberry Robin operators are also tracked as Storm-0856 by some vendors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-621", "filename": "mitre_software_v17.txt", "content": "RATANKBA is a remote controller tool used by Lazarus Group. RATANKBA has been used in attacks targeting financial institutions in Poland, Mexico, Uruguay, the United Kingdom, and Chile. It was also seen used against organizations related to telecommunications, management consulting, information technology, insurance, aviation, and education. RATANKBA has a graphical user interface to allow the attacker to issue jobs to perform on the infected machines.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-622", "filename": "mitre_software_v17.txt", "content": "RawDisk is a legitimate commercial driver from the EldoS Corporation that is used for interacting with files, disks, and partitions. The driver allows for direct modification of data on a local computer's hard drive. In some cases, the tool can enact these raw disk modifications from user-mode processes, circumventing Windows operating system security features.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-623", "filename": "mitre_software_v17.txt", "content": "RawPOS is a point-of-sale (POS) malware family that searches for cardholder data on victims. It has been in use since at least 2008.    FireEye divides RawPOS into three components: FIENDCRY, DUEBREW, and DRIFTWOOD.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-624", "filename": "mitre_software_v17.txt", "content": "Rclone is a command line program for syncing files with cloud storage services such as Dropbox, Google Drive, Amazon S3, and MEGA. Rclone has been used in a number of ransomware campaigns, including those associated with the Conti and DarkSide Ransomware-as-a-Service operations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-625", "filename": "mitre_software_v17.txt", "content": "RCSAndroid is Android malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-626", "filename": "mitre_software_v17.txt", "content": "RCSession is a backdoor written in C++ that has been in use since at least 2018 by Mustang Panda and by Threat Group-3390 (Type II Backdoor).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-627", "filename": "mitre_software_v17.txt", "content": "RDAT is a backdoor used by the suspected Iranian threat group OilRig. RDAT was originally identified in 2017 and targeted companies in the telecommunications sector.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-628", "filename": "mitre_software_v17.txt", "content": "RDFSNIFFER is a module loaded by BOOSTWRITE which allows an attacker to monitor and tamper with legitimate connections made via an application designed to provide visibility and system management capabilities to remote IT techs.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-629", "filename": "mitre_software_v17.txt", "content": "Reaver is a malware family that has been in the wild since at least late 2016. Reporting indicates victims have primarily been associated with the \"Five Poisons,\" which are movements the Chinese government considers dangerous. The type of malware is rare due to its final payload being in the form of Control Panel items.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-630", "filename": "mitre_software_v17.txt", "content": "Red Alert 2.0 is a banking trojan that masquerades as a VPN client.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-631", "filename": "mitre_software_v17.txt", "content": "RedDrop is an Android malware family that exfiltrates sensitive data from devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-632", "filename": "mitre_software_v17.txt", "content": "RedLeaves is a malware family used by menuPass. The code overlaps with PlugX and may be based upon the open source tool Trochilus.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-633", "filename": "mitre_software_v17.txt", "content": "Reg is a Windows utility used to interact with the Windows Registry. It can be used at the command-line interface to query, add, modify, and remove information. Utilities such as Reg are known to be used by persistent threats.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-634", "filename": "mitre_software_v17.txt", "content": "RegDuke is a first stage implant written in .NET and used by APT29 since at least 2017. RegDuke has been used to control a compromised machine when control of other implants on the machine was lost.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-635", "filename": "mitre_software_v17.txt", "content": "reGeorg is an open-source web shell written in Python that can be used as a proxy to bypass firewall rules and tunnel data in and out of targeted networks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-636", "filename": "mitre_software_v17.txt", "content": "Regin is a malware platform that has targeted victims in a range of industries, including telecom, government, and financial institutions. Some Regin timestamps date back to 2003.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-637", "filename": "mitre_software_v17.txt", "content": "Remcos is a closed-source tool that is marketed as a remote control and surveillance software by a company called Breaking Security. Remcos has been observed being used in malware campaigns.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-638", "filename": "mitre_software_v17.txt", "content": "Remexi is a Windows-based Trojan that was developed in the C programming language.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-639", "filename": "mitre_software_v17.txt", "content": "RemoteCMD is a custom tool used by APT3 to execute commands on a remote system similar to SysInternal's PSEXEC functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-640", "filename": "mitre_software_v17.txt", "content": "RemoteUtilities is a legitimate remote administration tool that has been used by MuddyWater since at least 2021 for execution on target machines.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-641", "filename": "mitre_software_v17.txt", "content": "Remsec is a modular backdoor that has been used by Strider and appears to have been designed primarily for espionage purposes. Many of its modules are written in Lua.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-642", "filename": "mitre_software_v17.txt", "content": "Responder is an open source tool used for LLMNR, NBT-NS and MDNS poisoning, with built-in HTTP/SMB/MSSQL/FTP/LDAP rogue authentication server supporting NTLMv1/NTLMv2/LMv2, Extended Security NTLMSSP and Basic HTTP authentication.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-643", "filename": "mitre_software_v17.txt", "content": "Revenge RAT is a freely available remote access tool written in .NET (C#).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-644", "filename": "mitre_software_v17.txt", "content": "REvil is a ransomware family that has been linked to the GOLD SOUTHFIELD group and operated as ransomware-as-a-service (RaaS) since at least April 2019. REvil, which as been used against organizations in the manufacturing, transportation, and electric sectors, is highly configurable and shares code similarities with the GandCrab RaaS.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-645", "filename": "mitre_software_v17.txt", "content": "RGDoor is a malicious Internet Information Services (IIS) backdoor developed in the C++ language. RGDoor has been seen deployed on webservers belonging to the Middle East government organizations. RGDoor provides backdoor access to compromised IIS servers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-646", "filename": "mitre_software_v17.txt", "content": "Rifdoor is a remote access trojan (RAT) that shares numerous code similarities with HotCroissant.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-647", "filename": "mitre_software_v17.txt", "content": "Riltok is banking malware that uses phishing popups to collect user credentials.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-648", "filename": "mitre_software_v17.txt", "content": "RIPTIDE is a proxy-aware backdoor used by APT12.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-649", "filename": "mitre_software_v17.txt", "content": "Rising Sun is a modular backdoor that was used extensively in Operation Sharpshooter between 2017 and 2019. Rising Sun infected at least 87 organizations around the world, including nuclear, defense, energy, and financial service companies. Security researchers assessed Rising Sun included some source code from Lazarus Group's Trojan Duuzer.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-650", "filename": "mitre_software_v17.txt", "content": "ROADSWEEP is a ransomware that was deployed against Albanian government networks during HomeLand Justice along with the CHIMNEYSWEEP backdoor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-651", "filename": "mitre_software_v17.txt", "content": "ROADTools is a framework for enumerating Azure Active Directory environments. The tool is written in Python and publicly available on GitHub.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-652", "filename": "mitre_software_v17.txt", "content": "RobbinHood is ransomware that was first observed being used in an attack against the Baltimore city government's computer network.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-653", "filename": "mitre_software_v17.txt", "content": "ROCKBOOT is a Bootkit that has been used by an unidentified, suspected China-based group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-654", "filename": "mitre_software_v17.txt", "content": "RogueRobin is a payload used by DarkHydrus that has been developed in PowerShell and C#.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-655", "filename": "mitre_software_v17.txt", "content": "ROKRAT is a cloud-based remote access tool (RAT) used by APT37 to target victims in South Korea. APT37 has used ROKRAT during several campaigns from 2016 through 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-656", "filename": "mitre_software_v17.txt", "content": "RotaJakiro is a 64-bit Linux backdoor used by APT32. First seen in 2018, it uses a plugin architecture to extend capabilities. RotaJakiro can determine it's permission level and execute according to access type (root or user).", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-657", "filename": "mitre_software_v17.txt", "content": "Rotexy is an Android banking malware that has evolved over several years. It was originally an SMS spyware Trojan first spotted in October 2014, and since then has evolved to contain more features, including ransomware functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-658", "filename": "mitre_software_v17.txt", "content": "route can be used to find or change information within the local system IP routing table.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-659", "filename": "mitre_software_v17.txt", "content": "Rover is malware suspected of being used for espionage purposes. It was used in 2015 in a targeted email sent to an Indian Ambassador to Afghanistan.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-660", "filename": "mitre_software_v17.txt", "content": "Royal is ransomware that first appeared in early 2022;  a version that also targets ESXi servers was later observed in February 2023. Royal employs partial encryption and multiple threads to evade detection and speed encryption. Royal has been used in attacks against multiple industries worldwide--including critical infrastructure. Security researchers have identified similarities in the encryption routines and TTPs used in Royal and Conti attacks and noted a possible connection between their operators.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-661", "filename": "mitre_software_v17.txt", "content": "RTM is custom malware written in Delphi. It is used by the group of the same name (RTM). Newer versions of the malware have been reported publicly as Redaman.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-662", "filename": "mitre_software_v17.txt", "content": "Rubeus is a C# toolset designed for raw Kerberos interaction that has been used since at least 2020, including in ransomware operations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-663", "filename": "mitre_software_v17.txt", "content": "Ruler is a tool to abuse Microsoft Exchange services. It is publicly available on GitHub and the tool is executed via the command line. The creators of Ruler have also released a defensive tool, NotRuler, to detect its usage.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-664", "filename": "mitre_software_v17.txt", "content": "RuMMS is an Android malware family.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-665", "filename": "mitre_software_v17.txt", "content": "RunningRAT is a remote access tool that appeared in operations surrounding the 2018 Pyeongchang Winter Olympics along with Gold Dragon and Brave Prince.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-666", "filename": "mitre_software_v17.txt", "content": "Ryuk is a ransomware designed to target enterprise environments that has been used in attacks since at least 2018. Ryuk shares code similarities with Hermes ransomware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-667", "filename": "mitre_software_v17.txt", "content": "S-Type is a backdoor that was used in Operation Dust Storm since at least 2013.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-668", "filename": "mitre_software_v17.txt", "content": "S.O.V.A. is an Android banking trojan that was first identified in August 2021 and has subsequently been found in a variety of applications, including banking, cryptocurrency wallet/exchange, and shopping apps. S.O.V.A., which is Russian for \"owl\", contains features not commonly found in Android malware, such as session cookie theft.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-669", "filename": "mitre_software_v17.txt", "content": "Sagerunex is a malware family exclusively associated with Lotus Blossom operations, with variants existing since at least 2016. Variations of Sagerunex leverage non-traditional command and control mechanisms such as various web services.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-670", "filename": "mitre_software_v17.txt", "content": "Saint Bot is a .NET downloader that has been used by Saint Bear since at least March 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-671", "filename": "mitre_software_v17.txt", "content": "Sakula is a remote access tool (RAT) that first surfaced in 2012 and was used in intrusions throughout 2015.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-671", "line_number": 671, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-672", "filename": "mitre_software_v17.txt", "content": "SampleCheck5000 is a downloader with multiple variants that was used by OilRig including during the Outer Space campaign to download and execute additional payloads.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-673", "filename": "mitre_software_v17.txt", "content": "SamSam is ransomware that appeared in early 2016. Unlike some ransomware, its variants have required operators to manually interact with the malware to execute some of its core components.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-673", "line_number": 673, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-674", "filename": "mitre_software_v17.txt", "content": "Samurai is a passive backdoor that has been used by ToddyCat since at least 2020. Samurai allows arbitrary C# code execution and is used with multiple modules for remote administration and lateral movement.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-675", "filename": "mitre_software_v17.txt", "content": "Sardonic is a backdoor written in C and C++ that is known to be used by FIN8, as early as August 2021 to target a financial institution in the United States. Sardonic has a plugin system that can load specially made DLLs and execute their functions.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-676", "filename": "mitre_software_v17.txt", "content": "schtasks is used to schedule execution of programs or scripts on a Windows system to run at a specific date and time.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-677", "filename": "mitre_software_v17.txt", "content": "SDBbot is a backdoor with installer and loader components that has been used by TA505 since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-677", "line_number": 677, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-678", "filename": "mitre_software_v17.txt", "content": "SDelete is an application that securely deletes data in a way that makes it unrecoverable. It is part of the Microsoft Sysinternals suite of tools.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-679", "filename": "mitre_software_v17.txt", "content": "SeaDuke is malware that was used by APT29 from 2014 to 2015. It was used primarily as a secondary backdoor for victims that were already compromised with CozyCar.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-680", "filename": "mitre_software_v17.txt", "content": "Seasalt is malware that has been linked to APT1's 2010 operations. It shares some code similarities with OceanSalt.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-681", "filename": "mitre_software_v17.txt", "content": "SEASHARPEE is a Web shell that has been used by OilRig.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-681", "line_number": 681, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-682", "filename": "mitre_software_v17.txt", "content": "ServHelper is a backdoor first observed in late 2018. The backdoor is written in Delphi and is typically delivered as a DLL file.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-683", "filename": "mitre_software_v17.txt", "content": "Seth-Locker is a ransomware with some remote control capabilities that has been in use since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-684", "filename": "mitre_software_v17.txt", "content": "ShadowPad is a modular backdoor that was first identified in a supply chain compromise of the NetSarang software in mid-July 2017. The malware was originally thought to be exclusively used by APT41, but has since been observed to be used by various Chinese threat activity groups.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-685", "filename": "mitre_software_v17.txt", "content": "Shamoon is wiper malware that was first used by an Iranian group known as the \"Cutting Sword of Justice\" in 2012. Other versions known as Shamoon 2 and Shamoon 3 were observed in 2016 and 2018. Shamoon has also been seen leveraging RawDisk and Filerase to carry out data wiping tasks. Analysis has linked Shamoon with Kwampirs based on multiple shared artifacts and coding patterns. The term Shamoon is sometimes used to refer to the group using the malware as well as the malware itself.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-686", "filename": "mitre_software_v17.txt", "content": "Shark is a backdoor malware written in C# and .NET that is an updated version of Milan; it has been used by HEXANE since at least July 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-687", "filename": "mitre_software_v17.txt", "content": "SharkBot is a banking malware, first discovered in October 2021, that tries to initiate money transfers directly from compromised devices by abusing Accessibility Services.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-688", "filename": "mitre_software_v17.txt", "content": "SharpDisco is a dropper developed in C# that has been used by MoustachedBouncer since at least 2020 to load malicious plugins.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-689", "filename": "mitre_software_v17.txt", "content": "SharpStage is a .NET malware with backdoor capabilities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-690", "filename": "mitre_software_v17.txt", "content": "SHARPSTATS is a .NET backdoor used by MuddyWater since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-691", "filename": "mitre_software_v17.txt", "content": "ShiftyBug is an auto-rooting adware family of malware for Android. The family is very similar to the other Android families known as Shedun, Shuanet, Kemoge, though it is not believed all the families were created by the same group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-692", "filename": "mitre_software_v17.txt", "content": "ShimRat has been used by the suspected China-based adversary Mofang in campaigns targeting multiple countries and sectors including government, military, critical infrastructure, automobile, and weapons development. The name \"ShimRat\" comes from the malware's extensive use of Windows Application Shimming to maintain persistence.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-692", "line_number": 692, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-693", "filename": "mitre_software_v17.txt", "content": "ShimRatReporter is a tool used by suspected Chinese adversary Mofang to automatically conduct initial discovery. The details from this discovery are used to customize follow-on payloads (such as ShimRat) as well as set up faux infrastructure which mimics the adversary's targets. ShimRatReporter has been used in campaigns targeting multiple countries and sectors including government, military, critical infrastructure, automobile, and weapons development.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-694", "filename": "mitre_software_v17.txt", "content": "SHIPSHAPE is malware developed by APT30 that allows propagation and exfiltration of data over removable devices. APT30 may use this capability to exfiltrate data across air-gaps.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-695", "filename": "mitre_software_v17.txt", "content": "SHOTPUT is a custom backdoor used by APT3.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-696", "filename": "mitre_software_v17.txt", "content": "ShrinkLocker is a VBS-based malicious script that leverages the legitimate Bitlocker application to encrypt files on victim systems for ransom. ShrinkLocker functions by using Bitlocker to encrypt files, then renames impacted drives to the adversary’s contact email address to facilitate communication for the ransom payment.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-697", "filename": "mitre_software_v17.txt", "content": "SHUTTERSPEED is a backdoor used by APT37.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-698", "filename": "mitre_software_v17.txt", "content": "Sibot is dual-purpose malware written in VBScript designed to achieve persistence on a compromised system as well as download and execute additional payloads. Microsoft discovered three Sibot variants in early 2021 during its investigation of APT29 and the SolarWinds Compromise.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-699", "filename": "mitre_software_v17.txt", "content": "SideTwist is a C-based backdoor that has been used by OilRig since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-700", "filename": "mitre_software_v17.txt", "content": "SILENTTRINITY is an open source remote administration and post-exploitation framework primarily written in Python that includes stagers written in Powershell, C, and Boo. SILENTTRINITY was used in a 2019 campaign against Croatian government agencies by unidentified cyber actors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-701", "filename": "mitre_software_v17.txt", "content": "SilkBean is a piece of Android surveillanceware containing comprehensive remote access tool (RAT) functionality that has been used in targeting of the Uyghur ethnic group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-702", "filename": "mitre_software_v17.txt", "content": "Siloscape is malware that targets Kubernetes clusters through Windows containers. Siloscape was first observed in March 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-703", "filename": "mitre_software_v17.txt", "content": "SimBad was a strain of adware on the Google Play Store, distributed through the RXDroider Software Development Kit. The name \"SimBad\" was derived from the fact that most of the infected applications were simulator games. The adware was controlled using an instance of the open source framework Parse Server.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-704", "filename": "mitre_software_v17.txt", "content": "Skeleton Key is malware used to inject false credentials into domain controllers with the intent of creating a backdoor password.  Functionality similar to Skeleton Key is included as a module in Mimikatz.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-705", "filename": "mitre_software_v17.txt", "content": "Skidmap is a kernel-mode rootkit used for cryptocurrency mining.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-706", "filename": "mitre_software_v17.txt", "content": "Skygofree is Android spyware that is believed to have been developed in 2014 and used through at least 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-707", "filename": "mitre_software_v17.txt", "content": "SLIGHTPULSE is a web shell that was used by APT5 as early as 2020 including against Pulse Secure VPNs at US Defense Industrial Base (DIB) entities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-708", "filename": "mitre_software_v17.txt", "content": "Sliver is an open source, cross-platform, red team command and control (C2) framework written in Golang. Sliver includes its own package manager, \"armory,\" for staging and downloading additional tools and payloads to the primary C2 framework.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-708", "line_number": 708, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-709", "filename": "mitre_software_v17.txt", "content": "SLOTHFULMEDIA is a remote access Trojan written in C++ that has been used by an unidentified \"sophisticated cyber actor\" since at least January 2017. It has been used to target government organizations, defense contractors, universities, and energy companies in Russia, India, Kazakhstan, Kyrgyzstan, Malaysia, Ukraine, and Eastern Europe. In October 2020, Kaspersky Labs assessed SLOTHFULMEDIA is part of an activity cluster it refers to as \"IAmTheKing\". ESET also noted code similarity between SLOTHFULMEDIA and droppers used by a group it refers to as \"PowerPool\".", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-709", "line_number": 709, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-710", "filename": "mitre_software_v17.txt", "content": "SLOWDRIFT is a backdoor used by APT37 against academic and strategic victims in South Korea.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-710", "line_number": 710, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-711", "filename": "mitre_software_v17.txt", "content": "SLOWPULSE is a malware that was used by APT5 as early as 2020 including against U.S. Defense Industrial Base (DIB) companies. SLOWPULSE has several variants and can modify legitimate Pulse Secure VPN files in order to log credentials and bypass single and two-factor authentication flows.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-711", "line_number": 711, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-712", "filename": "mitre_software_v17.txt", "content": "Small Sieve is a Telegram Bot API-based Python backdoor that has been distributed using a Nullsoft Scriptable Install System (NSIS) Installer; it has been used by MuddyWater since at least January 2022.Security researchers have also noted Small Sieve's use by UNC3313, which may be associated with MuddyWater.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-712", "line_number": 712, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-713", "filename": "mitre_software_v17.txt", "content": "Smoke Loader is a malicious bot application that can be used to load other malware.Smoke Loader has been seen in the wild since at least 2011 and has included a number of different payloads. It is notorious for its use of deception and self-protection. It also comes with several plug-ins.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-713", "line_number": 713, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-714", "filename": "mitre_software_v17.txt", "content": "SMOKEDHAM is a Powershell-based .NET backdoor that was first reported in May 2021; it has been used by at least one ransomware-as-a-service affiliate.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-715", "filename": "mitre_software_v17.txt", "content": "SnappyTCP is a web shell used by Sea Turtle between 2021 and 2023 against multiple victims. SnappyTCP appears to be based on a public GitHub project that has since been removed from the code-sharing site. SnappyTCP includes a simple reverse TCP shell for Linux and Unix environments with basic command and control capabilities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-716", "filename": "mitre_software_v17.txt", "content": "Snip3 is a sophisticated crypter-as-a-service that has been used since at least 2021 to obfuscate and load numerous strains of malware including AsyncRAT, Revenge RAT, Agent Tesla, and NETWIRE.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-717", "filename": "mitre_software_v17.txt", "content": "SNUGRIDE is a backdoor that has been used by menuPass as first stage malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-717", "line_number": 717, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-718", "filename": "mitre_software_v17.txt", "content": "SocGholish is a JavaScript-based loader malware that has been used since at least 2017. It has been observed in use against multiple sectors globally for initial access, primarily through drive-by-downloads masquerading as software updates. SocGholish is operated by Mustard Tempest and its access has been sold to groups including Indrik Spider for downloading secondary RAT and ransomware payloads.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-718", "line_number": 718, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-719", "filename": "mitre_software_v17.txt", "content": "Socksbot is a backdoor that  abuses Socket Secure (SOCKS) proxies.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-719", "line_number": 719, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-720", "filename": "mitre_software_v17.txt", "content": "SodaMaster is a fileless malware used by menuPass to download and execute payloads since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-720", "line_number": 720, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-721", "filename": "mitre_software_v17.txt", "content": "Solar is a C#/.NET backdoor that was used by OilRig during the Outer Space campaign to download, execute, and exfiltrate files.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-721", "line_number": 721, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-722", "filename": "mitre_software_v17.txt", "content": "SombRAT is a modular backdoor written in C++ that has been used since at least 2019 to download and execute malicious payloads, including FIVEHANDS ransomware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-722", "line_number": 722, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-723", "filename": "mitre_software_v17.txt", "content": "SoreFang is first stage downloader used by APT29 for exfiltration and to load other malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-723", "line_number": 723, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-724", "filename": "mitre_software_v17.txt", "content": "SOUNDBITE is a signature backdoor used by APT32.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-725", "filename": "mitre_software_v17.txt", "content": "SPACESHIP is malware developed by APT30 that allows propagation and exfiltration of data over removable devices. APT30 may use this capability to exfiltrate data across air-gaps.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-726", "filename": "mitre_software_v17.txt", "content": "Spark is a Windows backdoor and has been in use since as early as 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-726", "line_number": 726, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-727", "filename": "mitre_software_v17.txt", "content": "SpeakUp is a Trojan backdoor that targets both Linux and OSX devices. It was first observed in January 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-727", "line_number": 727, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-728", "filename": "mitre_software_v17.txt", "content": "Spica is a custom backdoor written in Rust that has been used by Star Blizzard since at least 2023.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-729", "filename": "mitre_software_v17.txt", "content": "SpicyOmelette is a JavaScript based remote access tool that has been used by Cobalt Group since at least 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-729", "line_number": 729, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-730", "filename": "mitre_software_v17.txt", "content": "spwebmember is a Microsoft SharePoint enumeration and data dumping tool written in .NET.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-731", "filename": "mitre_software_v17.txt", "content": "SpyC23 is a mobile malware that has been used by APT-C-23 since at least 2017. SpyC23 has been observed primarily targeting Android devices in the Middle East. There are multiple close variants of SpyC23, such as VAMP, GnatSpy, Desert Scorpion and FrozenCell, which add some additional functionality but are not significantly different from the original malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-732", "filename": "mitre_software_v17.txt", "content": "SpyDealer is Android malware that exfiltrates sensitive data from Android devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-732", "line_number": 732, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-733", "filename": "mitre_software_v17.txt", "content": "SpyNote RAT (Remote Access Trojan) is a family of malicious Android apps. The SpyNote RAT builder tool can be used to develop malicious apps with the malware's functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-733", "line_number": 733, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-734", "filename": "mitre_software_v17.txt", "content": "sqlmap is an open source penetration testing tool that can be used to automate the process of detecting and exploiting SQL injection flaws.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-734", "line_number": 734, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-735", "filename": "mitre_software_v17.txt", "content": "SQLRat is malware that executes SQL scripts to avoid leaving traditional host artifacts. FIN7 has been observed using it.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-735", "line_number": 735, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-736", "filename": "mitre_software_v17.txt", "content": "Squirrelwaffle is a loader that was first seen in September 2021. It has been used in spam email campaigns to deliver additional malware such as Cobalt Strike and the QakBot banking trojan.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-736", "line_number": 736, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-737", "filename": "mitre_software_v17.txt", "content": "SslMM is a full-featured backdoor used by Naikon that has multiple variants.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-738", "filename": "mitre_software_v17.txt", "content": "Starloader is a loader component that has been observed loading Felismus and associated tools.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-738", "line_number": 738, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-739", "filename": "mitre_software_v17.txt", "content": "STARWHALE is Windows Script File (WSF) backdoor that has been used by MuddyWater, possibly since at least November 2021; there is also a STARWHALE variant written in Golang with similar capabilities. Security researchers have also noted the use of STARWHALE by UNC3313, which may be associated with MuddyWater.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-739", "line_number": 739, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-740", "filename": "mitre_software_v17.txt", "content": "STEADYPULSE is a web shell that infects targeted Pulse Secure VPN servers through modification of a legitimate Perl script that was used as early as 2020 including in activity against US Defense Industrial Base (DIB) entities.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-740", "line_number": 740, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-741", "filename": "mitre_software_v17.txt", "content": "StealBit is a data exfiltration tool that is developed and maintained by the operators of the the LockBit Ransomware-as-a-Service (RaaS) and offered to affiliates to exfiltrate data from compromised systems for double extortion purposes.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-741", "line_number": 741, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-742", "filename": "mitre_software_v17.txt", "content": "Stealth Mango is Android malware that has reportedly been used to successfully compromise the mobile devices of government officials, members of the military, medical professionals, and civilians. The iOS malware known as Tangelo is believed to be from the same developer.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-743", "filename": "mitre_software_v17.txt", "content": "StoneDrill is wiper malware discovered in destructive campaigns against both Middle Eastern and European targets in association with APT33.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-744", "filename": "mitre_software_v17.txt", "content": "StreamEx is a malware family that has been used by Deep Panda since at least 2015. In 2016, it was distributed via legitimate compromised Korean websites.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-745", "filename": "mitre_software_v17.txt", "content": "StrelaStealer is an information stealer malware variant first identified in November 2022 and active through late 2024. StrelaStealer focuses on the automated identification, collection, and exfiltration of email credentials from email clients such as Outlook and Thunderbird.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-746", "filename": "mitre_software_v17.txt", "content": "StrifeWater is a remote-access tool that has been used by Moses Staff in the initial stages of their attacks since at least November 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-747", "filename": "mitre_software_v17.txt", "content": "StrongPity is an information stealing malware used by PROMETHIUM.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-748", "filename": "mitre_software_v17.txt", "content": "Stuxnet was the first publicly reported piece of malware to specifically target industrial control systems devices. Stuxnet is a large and complex piece of malware that utilized multiple different behaviors including multiple zero-day vulnerabilities, a sophisticated Windows rootkit, and network infection routines. Stuxnet was discovered in 2010, with some components being used as early as November 2008.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-749", "filename": "mitre_software_v17.txt", "content": "SUGARDUMP is a proprietary browser credential harvesting tool that was used by UNC3890 during the C0010 campaign. The first known SUGARDUMP version was used since at least early 2021, a second SMTP C2 version was used from late 2021-early 2022, and a third HTTP C2 variant was used since at least April 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-750", "filename": "mitre_software_v17.txt", "content": "SUGARUSH is a small custom backdoor that can establish a reverse shell over TCP to a hard coded C2 address. SUGARUSH was first identified during analysis of UNC3890's C0010 campaign targeting Israeli companies, which began in late 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-750", "line_number": 750, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-751", "filename": "mitre_software_v17.txt", "content": "Sunbird is one of two mobile malware families known to be used by the APT Confucius.  Analysis suggests that Sunbird was first active in early 2017. While Sunbird and Hornbill overlap in core capabilities, Sunbird has a more extensive set of malicious features.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-752", "filename": "mitre_software_v17.txt", "content": "SUNBURST is a trojanized DLL designed to fit within the SolarWinds Orion software update framework. It was used by APT29 since at least February 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-753", "filename": "mitre_software_v17.txt", "content": "SUNSPOT is an implant that injected the SUNBURST backdoor into the SolarWinds Orion software update framework. It was used by APT29 since at least February 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-754", "filename": "mitre_software_v17.txt", "content": "SUPERNOVA is an in-memory web shell written in .NET C#. It was discovered in November 2020 during the investigation of APT29's SolarWinds cyber operation but determined to be unrelated. Subsequent analysis suggests SUPERNOVA may have been used by the China-based threat group SPIRAL.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-755", "filename": "mitre_software_v17.txt", "content": "SVCReady is a loader that has been used since at least April 2022 in malicious spam campaigns. Security researchers have noted overlaps between TA551 activity and SVCReady distribution, including similarities in file names, lure images, and identical grammatical errors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-756", "filename": "mitre_software_v17.txt", "content": "Sykipot is malware that has been used in spearphishing campaigns since approximately 2007 against victims primarily in the US. One variant of Sykipot hijacks smart cards on victims.  The group using this malware has also been referred to as Sykipot.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-756", "line_number": 756, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-757", "filename": "mitre_software_v17.txt", "content": "SynAck is variant of Trojan ransomware targeting mainly English-speaking users since at least fall 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-757", "line_number": 757, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-758", "filename": "mitre_software_v17.txt", "content": "SYNful Knock is a stealthy modification of the operating system of network devices that can be used to maintain persistence within a victim's network and provide new capabilities to the adversary.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-759", "filename": "mitre_software_v17.txt", "content": "Sys10 is a backdoor that was used throughout 2013 by Naikon.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-760", "filename": "mitre_software_v17.txt", "content": "SYSCON is a backdoor that has been in use since at least 2017 and has been associated with campaigns involving North Korean themes. SYSCON has been delivered by the CARROTBALL and CARROTBAT droppers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-760", "line_number": 760, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-761", "filename": "mitre_software_v17.txt", "content": "Systeminfo is a Windows utility that can be used to gather detailed information about a computer.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-761", "line_number": 761, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-762", "filename": "mitre_software_v17.txt", "content": "SysUpdate is a backdoor written in C++ that has been used by Threat Group-3390 since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-762", "line_number": 762, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-763", "filename": "mitre_software_v17.txt", "content": "T9000 is a backdoor that is a newer variant of the T5000 malware family, also known as Plat1. Its primary function is to gather information about the victim. It has been used in multiple targeted attacks against U.S.-based organizations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-763", "line_number": 763, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-764", "filename": "mitre_software_v17.txt", "content": "Taidoor is a remote access trojan (RAT) that has been used by Chinese government cyber actors to maintain access on victim networks. Taidoor has primarily been used against Taiwanese government organizations since at least 2010.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-765", "filename": "mitre_software_v17.txt", "content": "TAINTEDSCRIBE is a fully-featured beaconing implant integrated with command modules used by Lazarus Group. It was first reported in May 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-766", "filename": "mitre_software_v17.txt", "content": "TajMahal is a multifunctional spying framework that has been in use since at least 2014. TajMahal is comprised of two separate packages, named Tokyo and Yokohama, and can deploy up to 80 plugins.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-767", "filename": "mitre_software_v17.txt", "content": "TAMECAT is a malware that is used by APT42 to execute PowerShell or C# content.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-768", "filename": "mitre_software_v17.txt", "content": "Tangelo is iOS malware that is believed to be from the same developers as the Stealth Mango Android malware. It is not a mobile application, but rather a Debian package that can only run on jailbroken iOS devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-769", "filename": "mitre_software_v17.txt", "content": "TangleBot is SMS malware that was initially observed in September 2021, primarily targeting mobile users in the United States and Canada. TangleBot has used SMS text message lures about COVID-19 regulations and vaccines to trick mobile users into downloading the malware, similar to FluBot Android malware campaigns.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-770", "filename": "mitre_software_v17.txt", "content": "Tarrask is malware that has been used by HAFNIUM since at least August 2021. Tarrask was designed to evade digital defenses and maintain persistence by generating concealed scheduled tasks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-771", "filename": "mitre_software_v17.txt", "content": "The Tasklist utility displays a list of applications and services with their Process IDs (PID) for all tasks running on either a local or a remote computer. It is packaged with Windows operating systems and can be executed from the command-line interface.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-772", "filename": "mitre_software_v17.txt", "content": "TDTESS is a 64-bit .NET binary backdoor used by CopyKittens.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-773", "filename": "mitre_software_v17.txt", "content": "TEARDROP is a memory-only dropper that was discovered on some victim machines during investigations related to the SolarWinds Compromise. It was likely used by APT29 since at least May 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-774", "filename": "mitre_software_v17.txt", "content": "TERRACOTTA is an ad fraud botnet that has been capable of generating over 2 billion fraudulent requests per week.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-775", "filename": "mitre_software_v17.txt", "content": "TEXTMATE is a second-stage PowerShell backdoor that is memory-resident. It was observed being used along with POWERSOURCE in February 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-776", "filename": "mitre_software_v17.txt", "content": "ThiefQuest is a virus, data stealer, and wiper that presents itself as ransomware targeting macOS systems. ThiefQuest was first seen in 2020 distributed via trojanized pirated versions of popular macOS software on Russian forums sharing torrent links. Even though ThiefQuest presents itself as ransomware, since the dynamically generated encryption key is never sent to the attacker it may be more appropriately thought of as a form of wiper malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-777", "filename": "mitre_software_v17.txt", "content": "ThreatNeedle is a backdoor that has been used by Lazarus Group since at least 2019 to target cryptocurrency, defense, and mobile gaming organizations.  It is considered to be an advanced cluster of Lazarus Group's Manuscrypt (a.k.a. NukeSped) malware family.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-778", "filename": "mitre_software_v17.txt", "content": "TianySpy is a mobile malware primarily spread by SMS phishing between September 30 and October 12, 2021. TianySpy is believed to have targeted credentials associated with membership websites of major Japanese telecommunication services.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-779", "filename": "mitre_software_v17.txt", "content": "Tiktok Pro is spyware that has been masquerading as the TikTok application.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-780", "filename": "mitre_software_v17.txt", "content": "TinyTurla is a backdoor that has been used by Turla against targets in the US, Germany, and Afghanistan since at least 2020.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-781", "filename": "mitre_software_v17.txt", "content": "TINYTYPHON is a backdoor  that has been used by the actors responsible for the MONSOON campaign. The majority of its code was reportedly taken from the MyDoom worm.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-782", "filename": "mitre_software_v17.txt", "content": "TinyZBot is a bot written in C# that was developed by Cleaver.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-783", "filename": "mitre_software_v17.txt", "content": "Tomiris is a backdoor written in Go that continuously queries its C2 server for executables to download and execute on a victim system. It was first reported in September 2021 during an investigation of a successful DNS hijacking campaign against a Commonwealth of Independent States (CIS) member. Security researchers assess there are similarities between Tomiris and GoldMax.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-784", "filename": "mitre_software_v17.txt", "content": "Tor is a software suite and network that provides increased anonymity on the Internet. It creates a multi-hop proxy network and utilizes multilayer encryption to protect both the message and routing information. Tor utilizes \"Onion Routing,\" in which messages are encrypted with multiple layers of encryption; at each step in the proxy network, the topmost layer is decrypted and the contents forwarded on to the next node until it reaches its destination.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-785", "filename": "mitre_software_v17.txt", "content": "Torisma is a second stage implant designed for specialized monitoring that has been used by Lazarus Group. Torisma was discovered during an investigation into the 2020 Operation North Star campaign that targeted the defense sector.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-786", "filename": "mitre_software_v17.txt", "content": "TrailBlazer is a modular malware that has been used by APT29 since at least 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-787", "filename": "mitre_software_v17.txt", "content": "TRANSLATEXT is malware that is believed to be used by Kimsuky. TRANSLATEXT masqueraded as a Google Translate extension for Google Chrome, but is actually a collection of four malicious Javascript files that perform defense evasion, information collection and exfiltration.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-788", "filename": "mitre_software_v17.txt", "content": "Triada was first reported in 2016 as a second stage malware. Later versions in 2019 appeared with new techniques and as an initial downloader of other Trojan apps.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-789", "filename": "mitre_software_v17.txt", "content": "TriangleDB is an Objective-C written implant deployed after Binary Validator and after root privileges are obtained during Operation Triangulation’s infection chain. Upon execution, TriangleDB communicates with the C2 server, relaying information about the victim device.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-790", "filename": "mitre_software_v17.txt", "content": "TrickBot is a Trojan spyware program written in C++ that first emerged in September 2016 as a possible successor to Dyre. TrickBot was developed and initially used by Wizard Spider for targeting banking sites in North America, Australia, and throughout Europe; it has since been used against all sectors worldwide as part of \"big game hunting\" ransomware campaigns.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-791", "filename": "mitre_software_v17.txt", "content": "TrickMo a 2FA bypass mobile banking trojan, most likely being distributed by TrickBot. TrickMo has been primarily targeting users located in Germany.TrickMo is designed to steal transaction authorization numbers (TANs), which are typically used as one-time passwords.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-792", "filename": "mitre_software_v17.txt", "content": "Triton is an attack framework built to interact with Triconex Safety Instrumented System (SIS) controllers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-793", "filename": "mitre_software_v17.txt", "content": "Trojan-SMS.AndroidOS.Agent.ao is Android malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-794", "filename": "mitre_software_v17.txt", "content": "Trojan-SMS.AndroidOS.FakeInst.a is Android malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-795", "filename": "mitre_software_v17.txt", "content": "Trojan-SMS.AndroidOS.OpFake.a is Android malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-796", "filename": "mitre_software_v17.txt", "content": "Trojan.Karagany is a modular remote access tool used for recon and linked to Dragonfly. The source code for Trojan.Karagany originated from Dream Loader malware which was leaked in 2010 and sold on underground forums.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-796", "line_number": 796, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-797", "filename": "mitre_software_v17.txt", "content": "Trojan.Mebromi is BIOS-level malware that takes control of the victim before MBR.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-798", "filename": "mitre_software_v17.txt", "content": "Troll Stealer is an information stealer written in Go associated with Kimsuky operations. Troll Stealer has typically been delivered through a dropper disguised as a legitimate security program installation file. Troll Stealer features code similar to AppleSeed, also uniquely associated with Kimsuky operations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-799", "filename": "mitre_software_v17.txt", "content": "Truvasys is first-stage malware that has been used by PROMETHIUM. It is a collection of modules written in the Delphi programming language.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-800", "filename": "mitre_software_v17.txt", "content": "TSCookie is a remote access tool (RAT) that has been used by BlackTech in campaigns against Japanese targets.. TSCookie has been referred to as PLEAD though more recent reporting indicates a separation between the two.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-801", "filename": "mitre_software_v17.txt", "content": "Turian is a backdoor that has been used by BackdoorDiplomacy to target Ministries of Foreign Affairs, telecommunication companies, and charities in Africa, Europe, the Middle East, and Asia. First reported in 2021, Turian is likely related to Quarian, an older backdoor that was last observed being used in 2013 against diplomatic targets in Syria and the United States.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-802", "filename": "mitre_software_v17.txt", "content": "TURNEDUP is a non-public backdoor. It has been dropped by APT33's StoneDrill malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-803", "filename": "mitre_software_v17.txt", "content": "Twitoor is a dropper application capable of receiving commands from social media.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-804", "filename": "mitre_software_v17.txt", "content": "TYPEFRAME is a remote access tool that has been used by Lazarus Group.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-805", "filename": "mitre_software_v17.txt", "content": "UACMe is an open source assessment tool that contains many methods for bypassing Windows User Account Control on multiple versions of the operating system.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-806", "filename": "mitre_software_v17.txt", "content": "UBoatRAT is a remote access tool that was identified in May 2017.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-807", "filename": "mitre_software_v17.txt", "content": "A Linux rootkit that provides backdoor access and hides from defenders.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-808", "filename": "mitre_software_v17.txt", "content": "Unknown Logger is a publicly released, free backdoor. Version 1.5 of the backdoor has been used by the actors responsible for the MONSOON campaign.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-809", "filename": "mitre_software_v17.txt", "content": "UPPERCUT is a backdoor that has been used by menuPass.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-810", "filename": "mitre_software_v17.txt", "content": "UPSTYLE is a Python-based backdoor associated with exploitation of Palo Alto firewalls using CVE-2024-3400 in early 2024. UPSTYLE has only been observed in relation to this exploitation activity, which involved attempted install on compromised devices by the threat actor UTA0218.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-811", "filename": "mitre_software_v17.txt", "content": "Uroburos is a sophisticated cyber espionage tool written in C that has been used by units within Russia's Federal Security Service (FSB) associated with the Turla toolset to collect intelligence on sensitive targets worldwide. Uroburos has several variants and has undergone nearly constant upgrade since its initial development in 2003 to keep it viable after public disclosures. Uroburos is typically deployed to external-facing nodes on a targeted network and has the ability to leverage additional tools and TTPs to further exploit an internal network. Uroburos has interoperable implants for Windows, Linux, and macOS, employs a high level of stealth in communications and architecture, and can easily incorporate new or replacement components.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-812", "filename": "mitre_software_v17.txt", "content": "Ursnif is a banking trojan and variant of the Gozi malware observed being spread through various automated exploit kits, Spearphishing Attachments, and malicious links. Ursnif is associated primarily with data theft, but variants also include components (backdoors, spyware, file injectors, etc.) capable of a wide variety of behaviors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-813", "filename": "mitre_software_v17.txt", "content": "USBferry is an information stealing malware and has been used by Tropic Trooper in targeted attacks against Taiwanese and Philippine air-gapped military environments. USBferry shares an overlapping codebase with YAHOYAH, though it has several features which makes it a distinct piece of malware.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-813", "line_number": 813, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-814", "filename": "mitre_software_v17.txt", "content": "USBStealer is malware that has been used by APT28 since at least 2005 to extract information from air-gapped networks. It does not have the capability to communicate over the Internet and has been used in conjunction with ADVSTORESHELL.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-814", "line_number": 814, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-815", "filename": "mitre_software_v17.txt", "content": "Valak is a multi-stage modular malware that can function as a standalone information stealer or downloader, first observed in 2019 targeting enterprises in the US and Germany.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-816", "filename": "mitre_software_v17.txt", "content": "VaporRage is a shellcode downloader that has been used by APT29 since at least 2021.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-817", "filename": "mitre_software_v17.txt", "content": "Vasport is a trojan used by Elderwood to open a backdoor on compromised hosts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-817", "line_number": 817, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-818", "filename": "mitre_software_v17.txt", "content": "VBShower is a backdoor that has been used by Inception since at least 2019. VBShower has been used as a downloader for second stage payloads, including PowerShower.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-818", "line_number": 818, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-819", "filename": "mitre_software_v17.txt", "content": "VERMIN is a remote access tool written in the Microsoft .NET framework. It is mostly composed of original code, but also has some open source code.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-820", "filename": "mitre_software_v17.txt", "content": "VersaMem is a web shell designed for deployment to Versa Director servers following exploitation. Discovered in August 2024, VersaMem was used during Versa Director Zero Day Exploitation by Volt Typhoon to target ISPs and MSPs.  VersaMem is deployed as a Java Archive (JAR) and allows for credential capture for Versa Director logon activity as well as follow-on execution of arbitrary Java payloads.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-821", "filename": "mitre_software_v17.txt", "content": "ViceLeaker is a spyware framework, capable of extensive surveillance and data exfiltration operations, primarily targeting devices belonging to Israeli citizens.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-822", "filename": "mitre_software_v17.txt", "content": "ViperRAT is sophisticated surveillanceware that has been in operation since at least 2015 and was used to target the Israeli Defense Force.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-823", "filename": "mitre_software_v17.txt", "content": "Volgmer is a backdoor Trojan designed to provide covert access to a compromised system. It has been used since at least 2013 to target the government, financial, automotive, and media industries. Its primary delivery mechanism is suspected to be spearphishing.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-824", "filename": "mitre_software_v17.txt", "content": "VPNFilter is a multi-stage, modular platform with versatile capabilities to support both intelligence-collection and destructive cyber attack operations. VPNFilter modules such as its packet sniffer ('ps') can collect traffic that passes through an infected device, allowing the theft of website credentials and monitoring of Modbus SCADA protocols.   VPNFilter was assessed to be replaced by Sandworm Team with Cyclops Blink starting in 2019.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-825", "filename": "mitre_software_v17.txt", "content": "WannaCry is ransomware that was first seen in a global attack during May 2017, which affected more than 150 countries. It contains worm-like features to spread itself across a computer network using the SMBv1 exploit EternalBlue.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-826", "filename": "mitre_software_v17.txt", "content": "WARPWIRE is a Javascript credential stealer that targets plaintext passwords and usernames for exfiltration that was used during Cutting Edge to target Ivanti Connect Secure VPNs.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-827", "filename": "mitre_software_v17.txt", "content": "WarzoneRAT is a malware-as-a-service remote access tool (RAT) written in C++ that has been publicly available for purchase since at least late 2018.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-828", "filename": "mitre_software_v17.txt", "content": "WastedLocker is a ransomware family attributed to Indrik Spider that has been used since at least May 2020. WastedLocker has been used against a broad variety of sectors, including manufacturing, information technology, and media.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-829", "filename": "mitre_software_v17.txt", "content": "Waterbear is modular malware attributed to BlackTech that has been used primarily for lateral movement, decrypting, and triggering payloads and is capable of hiding network behaviors.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-830", "filename": "mitre_software_v17.txt", "content": "WEBC2 is a family of backdoor malware used by APT1 as early as July 2006. WEBC2 backdoors are designed to retrieve a webpage, with commands hidden in HTML comments or special tags, from a predetermined C2 server.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-831", "filename": "mitre_software_v17.txt", "content": "WellMail is a lightweight malware written in Golang used by APT29, similar in design and structure to WellMess.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-832", "filename": "mitre_software_v17.txt", "content": "WellMess is lightweight malware family with variants written in .NET and Golang that has been in use since at least 2018 by APT29.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-833", "filename": "mitre_software_v17.txt", "content": "Wevtutil is a Windows command-line utility that enables administrators to retrieve information about event logs and publishers.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-834", "filename": "mitre_software_v17.txt", "content": "WhisperGate is a multi-stage wiper designed to look like ransomware that has been used against multiple government, non-profit, and information technology organizations in Ukraine since at least January 2022.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-835", "filename": "mitre_software_v17.txt", "content": "Wiarp is a trojan used by Elderwood to open a backdoor on compromised hosts.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-836", "filename": "mitre_software_v17.txt", "content": "Windows Credential Editor is a password dumping tool.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-837", "filename": "mitre_software_v17.txt", "content": "WINDSHIELD is a signature backdoor used by APT32.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-838", "filename": "mitre_software_v17.txt", "content": "WindTail is a macOS surveillance implant used by Windshift. WindTail shares code similarities with Hack Back aka KitM OSX.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-839", "filename": "mitre_software_v17.txt", "content": "WINERACK is a backdoor used by APT37.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-840", "filename": "mitre_software_v17.txt", "content": "Winexe is a lightweight, open source tool similar to PsExec designed to allow system administrators to execute commands on remote servers.  Winexe is unique in that it is a GNU/Linux based client.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-841", "filename": "mitre_software_v17.txt", "content": "Wingbird is a backdoor that appears to be a version of commercial software FinFisher. It is reportedly used to attack individual computers instead of networks. It was used by NEODYMIUM in a May 2016 campaign.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-842", "filename": "mitre_software_v17.txt", "content": "WinMM is a full-featured, simple backdoor used by Naikon.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-843", "filename": "mitre_software_v17.txt", "content": "Winnti for Linux is a trojan, seen since at least 2015, designed specifically for targeting Linux systems. Reporting indicates the winnti malware family is shared across a number of actors including Winnti Group. The Windows variant is tracked separately under Winnti for Windows.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-844", "filename": "mitre_software_v17.txt", "content": "Winnti for Windows is a modular remote access Trojan (RAT) that has been used likely by multiple groups to carry out intrusions in various regions since at least 2010, including by one group referred to as the same name, Winnti Group.. The Linux variant is tracked separately under Winnti for Linux.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-845", "filename": "mitre_software_v17.txt", "content": "Wiper is a family of destructive malware used in March 2013 during breaches of South Korean banks and media companies.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-845", "line_number": 845, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-846", "filename": "mitre_software_v17.txt", "content": "WIREFIRE is a web shell written in Python that exists as trojanized logic to the visits.py component of Ivanti Connect Secure VPN appliances. WIREFIRE was used during Cutting Edge for downloading files and command execution.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-846", "line_number": 846, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-847", "filename": "mitre_software_v17.txt", "content": "WireLurker is a family of macOS malware that targets iOS devices connected over USB.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-847", "line_number": 847, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-848", "filename": "mitre_software_v17.txt", "content": "WolfRAT is malware based on a leaked version of Dendroid that has primarily targeted Thai users. WolfRAT has most likely been operated by the now defunct organization Wolf Research.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-849", "filename": "mitre_software_v17.txt", "content": "Woody RAT is a remote access trojan (RAT) that has been used since at least August 2021 against Russian organizations.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-850", "filename": "mitre_software_v17.txt", "content": "X-Agent for Android is Android malware that was placed in a repackaged version of a Ukrainian artillery targeting application. The malware reportedly retrieved general location data on where the victim device was used, and therefore could likely indicate the potential location of Ukrainian artillery.  Is it tracked separately from the CHOPSTICK.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-851", "filename": "mitre_software_v17.txt", "content": "XAgentOSX is a trojan that has been used by APT28  on OS X and appears to be a port of their standard CHOPSTICK or XAgent trojan.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-852", "filename": "mitre_software_v17.txt", "content": "Xbash is a malware family that has targeted Linux and Microsoft Windows servers. The malware has been tied to the Iron Group, a threat actor group known for previous ransomware attacks. Xbash was developed in Python and then converted into a self-contained Linux ELF executable by using PyInstaller.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-853", "filename": "mitre_software_v17.txt", "content": "Xbot is an Android malware family that was observed in 2016 primarily targeting Android users in Russia and Australia.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-854", "filename": "mitre_software_v17.txt", "content": "xCaon is an HTTP variant of the BoxCaon malware family that has used by IndigoZebra since at least 2014. xCaon has been used to target political entities in Central Asia, including Kyrgyzstan and Uzbekistan.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-855", "filename": "mitre_software_v17.txt", "content": "xCmd is an open source tool that is similar to PsExec and allows the user to execute applications on remote systems.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-856", "filename": "mitre_software_v17.txt", "content": "XcodeGhost is iOS malware that infected at least 39 iOS apps in 2015 and potentially affected millions of users.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-857", "filename": "mitre_software_v17.txt", "content": "XCSSET is a modular macOS malware family delivered through infected Xcode projects and executed when the project is compiled. Active since August 2020, it has been observed installing backdoors, spoofed browsers, collecting data, and encrypting user files. It is composed of SHC-compiled shell scripts and run-only AppleScripts, often hiding in apps that mimic system tools (such as Xcode, Mail, or Notes) or use familiar icons (like Launchpad) to avoid detection.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-858", "filename": "mitre_software_v17.txt", "content": "XLoader is an infostealer malware in use since at least 2016. Previously known and sometimes still referred to as Formbook, XLoader is a Malware as a Service (MaaS) known for stealing data from web browsers, email clients and File Transfer Protocol (FTP) applications.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-859", "filename": "mitre_software_v17.txt", "content": "XLoader for Android is a malicious Android app first observed targeting Japan, Korea, China, Taiwan, and Hong Kong in 2018. It has more recently been observed targeting South Korean users as a pornography application. It is tracked separately from the XLoader for iOS.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-860", "filename": "mitre_software_v17.txt", "content": "XLoader for iOS is a malicious iOS application that is capable of gathering system information. It is tracked separately from the XLoader for Android.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-861", "filename": "mitre_software_v17.txt", "content": "XTunnel a VPN-like network proxy tool that can relay traffic between a C2 server and a victim. It was first seen in May 2013 and reportedly used by APT28 during the compromise of the Democratic National Committee.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-862", "filename": "mitre_software_v17.txt", "content": "YAHOYAH is a Trojan used by Tropic Trooper as a second-stage backdoor.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-863", "filename": "mitre_software_v17.txt", "content": "YiSpecter is a family of iOS and Android malware, first detected in November 2014, targeting users in mainland China and Taiwan. YiSpecter abuses private APIs in iOS to infect both jailbroken and non-jailbroken devices.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-864", "filename": "mitre_software_v17.txt", "content": "yty is a modular, plugin-based malware framework. The components of the framework are written in a variety of programming languages.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-865", "filename": "mitre_software_v17.txt", "content": "Zebrocy is a Trojan that has been used by APT28 since at least November 2015. The malware comes in several programming language variants, including C++, Delphi, AutoIt, C#, VB.NET, and Golang.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-866", "filename": "mitre_software_v17.txt", "content": "Zen is Android malware that was first seen in 2013.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-867", "filename": "mitre_software_v17.txt", "content": "ZergHelper is iOS riskware that was unique due to its apparent evasion of Apple's App Store review process. No malicious functionality was identified in the app, but it presents security risks.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-868", "filename": "mitre_software_v17.txt", "content": "Zeroaccess is a kernel-mode Rootkit that attempts to add victims to the ZeroAccess botnet, often for monetary gain.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-869", "filename": "mitre_software_v17.txt", "content": "ZeroCleare is a wiper malware that has been used in conjunction with the RawDisk driver since at least 2019 by suspected Iran-nexus threat actors including activity targeting the energy and industrial sectors in the Middle East and political targets in Albania.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-870", "filename": "mitre_software_v17.txt", "content": "ZeroT is a Trojan used by TA459, often in conjunction with PlugX.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-871", "filename": "mitre_software_v17.txt", "content": "Zeus Panda is a Trojan designed to steal banking information and other sensitive credentials for exfiltration. Zeus Panda’s original source code was leaked in 2011, allowing threat actors to use its source code as a basis for new malware variants. It is mainly used to target Windows operating systems ranging from Windows XP through Windows 10.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-872", "filename": "mitre_software_v17.txt", "content": "ZIPLINE is a passive backdoor that was used during Cutting Edge on compromised Secure Connect VPNs for reverse shell and proxy functionality.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-873", "filename": "mitre_software_v17.txt", "content": "ZLib is a full-featured backdoor that was used as a second-stage implant during Operation Dust Storm since at least 2014. ZLib is malware and should not be confused with the legitimate compression library from which its name is derived.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-874", "filename": "mitre_software_v17.txt", "content": "Zox is a remote access tool that has been used by Axiom since at least 2008.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-875", "filename": "mitre_software_v17.txt", "content": "zwShell is a remote access tool (RAT) written in Delphi that has been seen in the wild since the spring of 2010 and used by threat actors during Night Dragon.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-876", "filename": "mitre_software_v17.txt", "content": "ZxShell is a remote administration tool and backdoor that can be downloaded from the Internet, particularly from Chinese hacker websites. It has been used since at least 2004.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
{"chunk_id": "line-877", "filename": "mitre_software_v17.txt", "content": "ZxxZ is a trojan written in Visual C++ that has been used by BITTER since at least August 2021, including against Bangladeshi government personnel.", "metadata": {"filename": "mitre_software_v17.txt", "chunk_id": "line-877", "line_number": 877, "source": "知识库\\ATTCK\\mitre_software_v17.txt"}}
