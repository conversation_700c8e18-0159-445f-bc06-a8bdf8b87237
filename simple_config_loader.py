"""
简化的威胁情报配置加载器
从数据库动态加载威胁情报网站配置，替代硬编码的配置文件
只需要修改两个地方：threat_intel_config_simplified.py 和 pa_week_ar_test.py
"""

import os
import mysql.connector
from mysql.connector import Error
from typing import List
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('db.env')

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def load_international_domains() -> List[str]:
    """
    从数据库加载国际威胁情报域名列表
    用于替代 threat_intel_config_simplified.py 中的 INTERNATIONAL_DOMAINS
    
    Returns:
        国际域名列表
    """
    connection = get_db_connection()
    if not connection:
        # 返回备用硬编码列表
        return get_fallback_international_domains()
    
    try:
        cursor = connection.cursor()
        
        # 查询国际域名，按优先级排序
        query = """
            SELECT url FROM threat_intel_sites 
            WHERE category = 'international' 
            AND status = 'active' 
            ORDER BY priority ASC, created_at DESC
        """
        
        cursor.execute(query)
        domains = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"从数据库加载了 {len(domains)} 个国际威胁情报域名")
        return domains
        
    except Error as e:
        logger.error(f"加载国际域名失败: {e}")
        return get_fallback_international_domains()
    finally:
        if connection and connection.is_connected():
            connection.close()

def load_chinese_domains() -> List[str]:
    """
    从数据库加载中文威胁情报域名列表
    用于替代 threat_intel_config_simplified.py 中的 CHINESE_DOMAINS
    
    Returns:
        中文域名列表
    """
    connection = get_db_connection()
    if not connection:
        # 返回备用硬编码列表
        return get_fallback_chinese_domains()
    
    try:
        cursor = connection.cursor()
        
        # 查询中文域名，按优先级排序
        query = """
            SELECT url FROM threat_intel_sites 
            WHERE category = 'chinese' 
            AND status = 'active' 
            ORDER BY priority ASC, created_at DESC
        """
        
        cursor.execute(query)
        domains = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"从数据库加载了 {len(domains)} 个中文威胁情报域名")
        return domains
        
    except Error as e:
        logger.error(f"加载中文域名失败: {e}")
        return get_fallback_chinese_domains()
    finally:
        if connection and connection.is_connected():
            connection.close()

def load_crawl_urls() -> List[str]:
    """
    从数据库加载爬取URL列表
    用于替代 pa_week_ar_test.py 中的 new_urls
    
    Returns:
        爬取URL列表
    """
    connection = get_db_connection()
    if not connection:
        # 返回备用硬编码列表
        return get_fallback_crawl_urls()
    
    try:
        cursor = connection.cursor()
        
        # 查询爬取URL，按优先级排序
        query = """
            SELECT url FROM threat_intel_sites 
            WHERE category = 'crawl_url' 
            AND status = 'active' 
            ORDER BY priority ASC, created_at DESC
        """
        
        cursor.execute(query)
        urls = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"从数据库加载了 {len(urls)} 个爬取URL")
        return urls
        
    except Error as e:
        logger.error(f"加载爬取URL失败: {e}")
        return get_fallback_crawl_urls()
    finally:
        if connection and connection.is_connected():
            connection.close()

def get_fallback_international_domains() -> List[str]:
    """获取备用国际域名列表（硬编码）"""
    logger.warning("使用备用国际域名列表")
    return [
        "www.microsoft.com/en-us/security/blog/topic/threat-intelligence",
        "www.microsoft.com/en-us/security/blog",
        "www.trellix.com/blogs/research",
        "www.security.com/threat-intelligence",
        "asec.ahnlab.com/en",
        "www.fortinet.com/blog/threat-research",
        "blogs.infoblox.com/category/threat-intelligence",
        "securelist.com",
        "securelist.com/threat-category/spam-and-phishing",
        "securelist.com/threat-category/vulnerabilities-and-exploits",
        "securelist.com/threat-category/apt-targeted-attacks",
        "www.proofpoint.com/us/blog/threat-insight",
        "www.welivesecurity.com/en/about-eset-research",
        "www.trendmicro.com/en_us/research.html",
        "cofense.com/blog",
        "www.sentinelone.com/blog/category/from-the-front-lines",
        "thehackernews.com",
        "blog.polyswarm.io",
        "cyfirma.com/research/",
        "unit42.paloaltonetworks.com/category/threat-research/",
        "trustwave.com/en-us/resources/blogs/spiderlabs-blog",
        "darktrace.com/blog"
    ]

def get_fallback_chinese_domains() -> List[str]:
    """获取备用中文域名列表（硬编码）"""
    logger.warning("使用备用中文域名列表")
    return [
        "ti.qianxin.com/blog",
        "ti.dbappsecurity.com.cn/info",
        "www.secrss.com/articles",
        "blog.nsfocus.net"
    ]

def get_fallback_crawl_urls() -> List[str]:
    """获取备用爬取URL列表（硬编码）"""
    logger.warning("使用备用爬取URL列表")
    return [
        "https://www.microsoft.com/en-us/security/blog/topic/threat-intelligence/",
        "https://blog.knowbe4.com/page/1",
        "https://www.recordedfuture.com/blog",
        "https://threatconnect.com/blog/",
        "https://www.varonis.com/blog/page/1#blog-listing",
        "https://www.clearskysec.com/blog/page/4/",
        "https://www.trellix.com/blogs/platform/"
    ]

def test_database_connection():
    """测试数据库连接"""
    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM threat_intel_sites")
            count = cursor.fetchone()[0]
            logger.info(f"数据库连接成功，共有 {count} 条网站配置记录")
            return True
        except Error as e:
            logger.error(f"数据库查询失败: {e}")
            return False
        finally:
            connection.close()
    else:
        logger.error("数据库连接失败")
        return False

if __name__ == "__main__":
    # 测试配置加载器
    print("=== 测试简化配置加载器 ===")
    
    # 测试数据库连接
    if test_database_connection():
        print("✓ 数据库连接正常")
    else:
        print("✗ 数据库连接失败，将使用备用配置")
    
    # 测试加载国际域名
    international_domains = load_international_domains()
    print(f"\n国际域名 ({len(international_domains)} 个):")
    for i, domain in enumerate(international_domains[:5], 1):
        print(f"  {i}. {domain}")
    if len(international_domains) > 5:
        print(f"  ... 还有 {len(international_domains) - 5} 个")
    
    # 测试加载中文域名
    chinese_domains = load_chinese_domains()
    print(f"\n中文域名 ({len(chinese_domains)} 个):")
    for i, domain in enumerate(chinese_domains, 1):
        print(f"  {i}. {domain}")
    
    # 测试加载爬取URL
    crawl_urls = load_crawl_urls()
    print(f"\n爬取URL ({len(crawl_urls)} 个):")
    for i, url in enumerate(crawl_urls, 1):
        print(f"  {i}. {url}")
    
    print("\n=== 测试完成 ===")
