<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTI 威胁情报分析</title>
    <!-- 添加Bootstrap和图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- 添加任务管理相关的JavaScript - 添加版本参数避免缓存 -->
    <script src="/static/js/task-manager.js?v=20250709-9"></script>
    <script src="/static/js/enhanced-analysis.js?v=20250709-9"></script>
    <script>
        // 检查函数是否加载
        window.addEventListener('load', function() {
            console.log('页面完全加载完成');
            console.log('window.showTaskHistory存在:', typeof window.showTaskHistory);
            console.log('showTaskHistory存在:', typeof showTaskHistory);
            console.log('safeShowTaskHistory存在:', typeof safeShowTaskHistory);

            // 确保函数在全局作用域中可用
            if (typeof showTaskHistory === 'function') {
                window.showTaskHistory = showTaskHistory;
                console.log('已将showTaskHistory挂载到window对象');
            }
        });
    </script>
    <script>
        // 全局taskManager实例
        window.taskManager = null;
        
        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化...');

            // 检查URL参数，如果有show=tasks，自动显示任务历史
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('show') === 'tasks') {
                // 延迟执行，确保页面完全加载
                setTimeout(() => {
                    console.log('自动显示任务历史');
                    safeShowTaskHistory();
                }, 1000);
            }
            
            // 清理可能存在的任务状态容器（防止显示在导航栏等错误位置）
            const existingTaskContainer = document.getElementById('task-status-container');
            if (existingTaskContainer) {
                console.log('清理已存在的任务状态容器');
                existingTaskContainer.remove();
            }
            
            // 清理可能存在的其他位置的任务状态容器
            const allTaskContainers = document.querySelectorAll('[id*="task"], .task-status-container');
            allTaskContainers.forEach(container => {
                if (container.id !== 'task-history-modal' && container.className.includes('task-status')) {
                    console.log('清理额外的任务状态容器:', container.id || container.className);
                    container.remove();
                }
            });
            
            // 检查库加载状态
            console.log('marked 库状态:', typeof marked !== 'undefined' ? '已加载' : '未加载');
            if (typeof marked !== 'undefined') {
                console.log('marked 版本:', marked.getDefaults ? 'v4+' : '旧版本');
            }
            
            // 初始化任务管理器
            if (typeof TaskManager !== 'undefined') {
                try {
                    window.taskManager = new TaskManager();
                    console.log('任务管理器初始化成功');
                    
                    // 恢复页面刷新前的任务状态
                    window.taskManager.restoreTasksFromStorage();
                    
                    // 设置全局变量以便其他脚本使用
                    window.globalTaskManager = window.taskManager;
                    
                    // 检查URL是否包含分析ID并自动加载
                    setTimeout(() => {
                        if (typeof checkUrlForAnalysisId === 'function') {
                            checkUrlForAnalysisId();
                        } else {
                            console.log('checkUrlForAnalysisId函数尚未加载，稍后重试...');
                            setTimeout(() => {
                                if (typeof checkUrlForAnalysisId === 'function') {
                                    checkUrlForAnalysisId();
                                }
                            }, 2000);
                        }
                    }, 500);
                } catch (error) {
                    console.error('任务管理器初始化失败:', error);
                }
            } else {
                console.error('TaskManager类未定义');
            }
        });
        
        // 为了向后兼容，提供一个获取taskManager的函数
        function getTaskManager() {
            return window.taskManager || window.globalTaskManager;
        }
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 40px;
        }
        .container, .container-fluid {
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            padding: 30px; /* 增加内边距 */
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #495057;
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            transform: translateY(-2px);
        }
        .btn-primary:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .results {
            margin-top: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 0;
            overflow: hidden;
        }
        /* 自定义 Markdown 样式 */
        .markdown-body {
            color: #24292e;
        }

        .markdown-body h1 {
            font-size: 1.8rem;
            margin-top: 30px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eaecef;
        }

        .markdown-body h2 {
            font-size: 1.5rem;
            margin-top: 24px;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eaecef;
        }

        .markdown-body h3 {
            font-size: 1.3rem;
            margin-top: 20px;
            margin-bottom: 10px;
        }

        .markdown-body h4 {
            font-size: 1.2rem;
            margin-top: 18px;
            margin-bottom: 8px;
        }

        .markdown-body p {
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .markdown-body ul, .markdown-body ol {
            padding-left: 2em;
            margin-bottom: 16px;
        }

        .markdown-body li {
            margin-bottom: 4px;
        }

        .markdown-body blockquote {
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
            margin: 0 0 16px 0;
        }

        .markdown-body pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
            margin-bottom: 16px;
        }

        .markdown-body code {
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(27, 31, 35, 0.05);
            border-radius: 3px;
            font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
        }

        .markdown-body table {
            width: 100%;
            margin-bottom: 16px;
            border-collapse: collapse;
        }

        .markdown-body table th, 
        .markdown-body table td {
            padding: 8px 13px;
            border: 1px solid #dfe2e5;
        }

        .markdown-body table th {
            background-color: #f6f8fa;
            font-weight: 600;
        }

        /* IOC 标签样式 */
        .ioc-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 90%;
            font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
            margin-right: 5px;
        }

        .ioc-ip {
            background-color: #fff3cd;
            color: #856404;
        }

        .ioc-hash {
            background-color: #f8d7da;
            color: #721c24;
        }

        .ioc-url {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        
        /* 消息容器样式 */
        #message-container {
            max-width: 500px;
        }
        
        .alert-sm {
            padding: 8px 12px;
            font-size: 0.875rem;
        }
        
        /* 批量操作面板样式 */
        .batch-operations {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 25px; /* 增加内边距 */
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        
        .batch-operations .form-row {
            display: flex;
            gap: 20px; /* 增加间距 */
            align-items: end;
            margin-bottom: 20px; /* 增加底部间距 */
        }
        
        .batch-operations .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container-fluid {
                max-width: 100% !important;
                padding: 15px;
            }
            
            .batch-operations .form-row {
                flex-direction: column;
                gap: 15px;
            }
            
            .batch-operations .form-group {
                width: 100%;
            }
            
            .page-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .page-header .d-flex {
                justify-content: center;
            }
        }
        
        @media (min-width: 1600px) {
            .container-fluid {
                max-width: 1600px !important;
            }
        }
        .tab-content {
            display: none;
            padding: 20px;
            border-top: none;
        }
        .tab-content.active {
            display: block;
        }
        .tabs {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0;
        }
        .tabs button {
            padding: 12px 20px;
            border: none;
            background-color: transparent;
            cursor: pointer;
            font-weight: 500;
            color: #495057;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }
        .tabs button:hover {
            background-color: #e9ecef;
            color: #0d6efd;
        }
        .tabs button.active {
            color: #0d6efd;
            border-bottom: 2px solid #0d6efd;
            background-color: #fff;
        }
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #0d6efd;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1.2s linear infinite;
            margin: 30px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        textarea {
            width: 100%;
            height: 180px;
            padding: 12px;
            box-sizing: border-box;
            border: 1px solid #ced4da;
            border-radius: 5px;
            resize: vertical;
            font-family: inherit;
            font-size: 0.9rem;
        }
        .page-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 15px;
        }
        .form-select, .form-control {
            border-radius: 5px;
            padding: 10px 12px;
            border: 1px solid #ced4da;
        }
        #analysisContent, #referencesContent, #rationaleContent {
            line-height: 1.6;
        }
        .card-file-input {
            border: 2px dashed #ced4da;
            padding: 30px;
            text-align: center;
            border-radius: 5px;
            transition: all 0.3s;
            cursor: pointer;
        }
        .card-file-input:hover {
            border-color: #0d6efd;
            background-color: rgba(13, 110, 253, 0.05);
        }
        .nav-tabs {
            border-bottom: none;
        }
        .hidden-file-input {
            display: none;
        }
        .url-example {
            color: #6c757d;
            font-size: 0.85rem;
            margin-top: 8px;
            font-style: italic;
        }
        .examples-title {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 15px;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .example-card {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px 15px;
            margin-bottom: 10px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        .example-card:hover {
            background-color: #e9ecef;
            border-color: #dee2e6;
        }
    </style>
</head>
<body>
    <!-- 添加导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <div class="container-fluid"> <!-- 使用 container-fluid 让导航栏占满宽度 -->
            <a class="navbar-brand d-flex align-items-center" href="/">
                <i class="bi bi-shield-lock me-2 text-primary"></i>
                <strong>CTI 威胁情报分析系统</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="bi bi-house-door"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/analysis"><i class="bi bi-search"></i> 分析工具</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区域，添加顶部间距 -->
    <div class="container-fluid" style="margin-top: 100px; max-width: 1400px;"> <!-- 使用 container-fluid 并设置最大宽度 -->
        <div class="page-header">
            <h2><i class="bi bi-lightning-charge text-primary"></i> 威胁情报分析工具</h2>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-info btn-sm" onclick="safeShowTaskHistory()">
                    <i class="bi bi-clock-history"></i> 任务历史
                </button>
                <a href="/" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> 返回首页
                </a>
            </div>
        </div>
        
        <div id="messageArea"></div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="inputType" class="form-label">选择分析方式:</label>
                    <select id="inputType" class="form-select" onchange="toggleInputMethod()">
                        <option value="file">上传文件</option>
                        <option value="url">输入URL</option>
                        <option value="text">输入文本</option>
                        <option value="archivebox">ArchiveBox爬取分析</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="analysisType" class="form-label">分析类型:</label>
                    <select id="analysisType" class="form-select">
                        <option value="all">全面分析</option>
                        <option value="summary">内容摘要</option>
                        <option value="threats">威胁行为者</option>
                        <option value="vulns">漏洞分析</option>
                        <option value="iocs">威胁指标(IOCs)</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" id="analyzeBtn" onclick="handleAnalyzeClick()">
                        <i class="bi bi-lightning-charge"></i> 开始分析
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 修改输入区域样式 -->
        <div id="fileInputArea" class="form-group mt-3">
            <label for="fileInput" class="form-label">选择文件:</label>
            <div class="card-file-input" onclick="document.getElementById('fileInput').click()">
                <i class="bi bi-file-earmark-text display-4 text-primary mb-2"></i>
                <p class="mb-1">点击或拖拽文件到此处</p>
                <small class="text-muted">支持 .txt, .pdf, .docx, .doc, .md, .html, .json 格式</small>
                <input type="file" id="fileInput" class="hidden-file-input" accept=".txt,.pdf,.docx,.doc,.md,.html,.json">
            </div>
            <div id="selectedFileName" class="mt-2 text-success"></div>
        </div>
        
        <div id="urlInputArea" class="form-group mt-3" style="display:none;">
            <label for="urlInput" class="form-label">输入URL:</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-link-45deg"></i></span>
                <input type="text" id="urlInput" class="form-control" placeholder="https://example.com/threat-report.html">
            </div>
            <small class="text-muted mt-1 d-block">输入包含威胁情报信息的文章或报告URL</small>
        </div>
        
        <div id="textInputArea" class="form-group mt-3" style="display:none;">
            <label for="textInput" class="form-label">输入文本内容:</label>
            <textarea id="textInput" class="form-control" placeholder="在此粘贴需要分析的文本内容..."></textarea>
            <small class="text-muted mt-1 d-block">直接粘贴威胁情报相关的文本内容进行分析</small>
        </div>
        
        <!-- 新增 ArchiveBox 输入区域 -->
        <div id="archiveboxInputArea" class="form-group mt-3" style="display:none;">
            <label for="archiveboxInput" class="form-label">输入URL (多个URL请换行或使用分号分隔):</label>
            <textarea id="archiveboxInput" class="form-control" placeholder="https://example.com/article1&#10;https://example.com/article2&#10;https://example.com/article3"></textarea>
            <small class="text-muted mt-1 d-block">输入单个URL或多个URL，使用ArchiveBox存档并分析威胁情报。</small>
            
            <div class="form-check mt-3" style="display: none;">
                <input class="form-check-input" type="checkbox" id="noAnalyzeCheck">
                <label class="form-check-label" for="noAnalyzeCheck">
                    仅存档，不分析内容，用于批量处理
                </label>
            </div>
        </div>
        
        <!-- 加载器 - 放在输入区域和结果区域之间 -->
        <div class="text-center my-4">
            <div class="loader" id="loader" style="display:none;"></div>
        </div>
        
        <!-- 优化结果展示区域 -->
        <div id="resultsArea" class="results" style="display:none;">
            <ul class="nav nav-tabs tabs">
                <li class="nav-item">
                    <button class="tab-button active" onclick="openTab(event, 'analysisTab')">
                        <i class="bi bi-file-text"></i> 分析结果
                    </button>
                </li>
                <li class="nav-item">
                    <button class="tab-button" onclick="openTab(event, 'referencesTab')">
                        <i class="bi bi-journals"></i> 引用信息
                    </button>
                </li>
                <li class="nav-item">
                    <button class="tab-button" onclick="openTab(event, 'rationaleTab')">
                        <i class="bi bi-lightbulb"></i> 分析理由
                    </button>
                </li>
            </ul>
            
            <!-- 标签页内容结构 -->
            <div id="analysisTab" class="tab-content active">
                <div class="card">
                    <div class="card-body markdown-body" id="analysisContent">
                        <!-- 分析内容将在这里动态插入 -->
                    </div>
                </div>
            </div>
            
            <div id="referencesTab" class="tab-content">
                <div class="card">
                    <div class="card-body markdown-body" id="referencesContent">
                        <!-- 参考信息将在这里动态插入 -->
                    </div>
                </div>
            </div>
            
            <div id="rationaleTab" class="tab-content">
                <div class="card">
                    <div class="card-body markdown-body" id="rationaleContent">
                        <!-- 分析理由将在这里动态插入 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 批量处理结果展示区域 -->
        <div id="batchResultsArea" class="results mt-4" style="display:none;">
            <h4 class="mb-3">批量处理结果</h4>
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>处理统计</span>
                    </div>
                </div>
                <div class="card-body">
                    <div id="batchStats" class="mb-3"></div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>URL</th>
                                    <th>状态</th>
                                    <th>快照ID</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="batchResultsTable">
                                <!-- 表格内容将在这里动态插入 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    <!-- 批量操作面板 -->
    <div class="batch-operations">
        <h5><i class="bi bi-gear-fill me-2"></i>批量操作
            <button class="btn btn-outline-secondary btn-sm float-end" onclick="safeShowTaskHistory()">
                <i class="bi bi-clock-history"></i> 任务历史
            </button>
        </h5>
        
        <!-- 批量分析数据库记录 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <h6 class="text-muted">批量分析数据库记录</h6>
                <div class="row g-2">
                    <div class="col-md-3">
                        <label class="form-label">批量大小:</label>
                        <input type="number" class="form-control form-control-sm" id="batchSize" value="5" min="1" max="50">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">分析类型:</label>
                        <select class="form-select form-select-sm" id="batchAnalysisType">
                            <option value="all">全面分析</option>
                            <option value="summary">摘要</option>
                            <option value="threats">威胁</option>
                            <option value="vulns">漏洞</option>
                            <option value="iocs">IOCs</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button class="btn btn-primary btn-sm" id="startBatchAnalysisBtn" onclick="safeStartBatchAnalysis()">
                            <i class="bi bi-play-circle"></i> 开始批量分析
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 批量分析未分析快照 -->
        <div class="row">
            <div class="col-md-12">
                <h6 class="text-muted">批量分析未分析快照</h6>
                <div class="row g-2">
                    <div class="col-md-3">
                        <label class="form-label">处理数量限制:</label>
                        <input type="number" class="form-control form-control-sm" id="unanalyzedLimit" value="5" min="1" max="200">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">分析类型:</label>
                        <select class="form-select form-select-sm" id="unanalyzedAnalysisType">
                            <option value="all">全面分析</option>
                            <option value="summary">摘要</option>
                            <option value="threats">威胁</option>
                            <option value="vulns">漏洞</option>
                            <option value="iocs">IOCs</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button class="btn btn-warning btn-sm" id="analyzeBatchUnanalyzedBtn" onclick="safeBatchAnalyzeUnanalyzed()">
                            <i class="bi bi-archive"></i> 分析快照
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 安全的包装函数，直接调用showTaskHistory（不再依赖taskManager）
        function safeShowTaskHistory() {
            console.log('safeShowTaskHistory被调用');

            // 检查window.showTaskHistory是否存在
            if (typeof window.showTaskHistory === 'function') {
                console.log('调用window.showTaskHistory函数');
                window.showTaskHistory();
            } else if (typeof showTaskHistory === 'function') {
                console.log('调用全局showTaskHistory函数');
                showTaskHistory();
            } else {
                console.error('showTaskHistory函数未定义');
                if (typeof showTopRightNotification === 'function') {
                    showTopRightNotification('任务历史功能未加载，请刷新页面重试', 'error', 5000);
                } else {
                    alert('任务历史功能未加载，请刷新页面重试');
                }
            }
        }

        
        function safeStartBatchAnalysis() {
            const taskManager = window.taskManager || window.globalTaskManager || (window.getTaskManager && window.getTaskManager());
            if (taskManager && typeof startBatchAnalysis === 'function') {
                startBatchAnalysis();
            } else {
                console.error('任务管理器未初始化或startBatchAnalysis函数未定义');
                if (typeof showTopRightNotification === 'function') {
                    showTopRightNotification('任务管理器未初始化，请刷新页面重试', 'warning', 4000);
                } else {
                    alert('任务管理器未初始化，请刷新页面重试');
                }
            }
        }
        
        function safeBatchAnalyzeUnanalyzed() {
            const taskManager = window.taskManager || window.globalTaskManager || (window.getTaskManager && window.getTaskManager());
            if (taskManager && typeof batchAnalyzeUnanalyzed === 'function') {
                batchAnalyzeUnanalyzed();
            } else {
                console.error('任务管理器未初始化或batchAnalyzeUnanalyzed函数未定义');
                if (typeof showTopRightNotification === 'function') {
                    showTopRightNotification(' 任务管理器未初始化，请刷新页面重试', 'warning', 4000);
                } else {
                    alert('任务管理器未初始化，请刷新页面重试');
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 添加文件选择监听器来显示选择的文件名
            document.getElementById('fileInput').addEventListener('change', function(e) {
                const fileName = e.target.files[0]?.name || '';
                if (fileName) {
                    document.getElementById('selectedFileName').innerHTML = 
                        `<i class="bi bi-check-circle"></i> 已选择: <strong>${fileName}</strong>`;
                } else {
                    document.getElementById('selectedFileName').innerHTML = '';
                }
            });
            
            // 添加ArchiveBox输入框监听器，根据URL数量动态显示/隐藏"仅存档"选项
            const archiveboxInput = document.getElementById('archiveboxInput');
            const noAnalyzeContainer = document.querySelector('.form-check');
            
            function updateArchiveOnlyOption() {
                const input = archiveboxInput.value.trim();
                if (!input) {
                    // 没有输入时隐藏选项
                    noAnalyzeContainer.style.display = 'none';
                    return;
                }
                
                // 解析URL数量
                let urls = [];
                if (input.includes('\n') || input.includes(';')) {
                    urls = input
                        .split(/[\n;]/)
                        .map(url => url.trim())
                        .filter(url => url);
                } else {
                    urls = [input];
                }
                
                // 只有多个URL时才显示"仅存档"选项
                if (urls.length > 1) {
                    noAnalyzeContainer.style.display = 'block';
                } else {
                    noAnalyzeContainer.style.display = 'none';
                    // 单个URL时自动取消"仅存档"选项
                    document.getElementById('noAnalyzeCheck').checked = false;
                }
            }
            
            // 监听输入变化
            archiveboxInput.addEventListener('input', updateArchiveOnlyOption);
            archiveboxInput.addEventListener('paste', function() {
                // 粘贴后稍微延迟执行，确保内容已更新
                setTimeout(updateArchiveOnlyOption, 100);
            });
            
            // 初始化状态
            updateArchiveOnlyOption();
            
            // 添加支持拖放文件
            const dropZone = document.querySelector('.card-file-input');
            
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('border-primary');
            });
            
            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('border-primary');
            });
            
            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('border-primary');
                
                if (e.dataTransfer.files.length) {
                    document.getElementById('fileInput').files = e.dataTransfer.files;
                    const fileName = e.dataTransfer.files[0]?.name || '';
                    if (fileName) {
                        document.getElementById('selectedFileName').innerHTML = 
                            `<i class="bi bi-check-circle"></i> 已选择: <strong>${fileName}</strong>`;
                    }
                }
            });
        });

        // 填充示例URL
        function fillExampleUrl(type) {
            const inputElement = document.getElementById('archiveboxInput');
            
            switch(type) {
                case 'single':
                    inputElement.value = "https://www.secrss.com/articles/60193";
                    break;
                case 'multiple':
                    inputElement.value = "https://www.secrss.com/articles/60193\nhttps://www.freebuf.com/news/368429.html\nhttps://www.anquanke.com/post/id/291156";
                    break;
                case 'semicolon':
                    inputElement.value = "https://www.secrss.com/articles/60193;https://www.freebuf.com/news/368429.html;https://www.anquanke.com/post/id/291156";
                    break;
            }
            
            // 触发输入事件，更新"仅存档"选项的显示状态
            inputElement.dispatchEvent(new Event('input'));
        }

        function toggleInputMethod() {
            const inputType = document.getElementById('inputType').value;
            
            document.getElementById('fileInputArea').style.display = 'none';
            document.getElementById('urlInputArea').style.display = 'none';
            document.getElementById('textInputArea').style.display = 'none';
            document.getElementById('archiveboxInputArea').style.display = 'none';
            
            document.getElementById(inputType + 'InputArea').style.display = 'block';
            
            // 如果切换到ArchiveBox模式，检查并更新"仅存档"选项状态
            if (inputType === 'archivebox') {
                // 获取当前输入内容并更新选项显示状态
                const archiveboxInput = document.getElementById('archiveboxInput');
                if (archiveboxInput) {
                    // 触发输入事件来更新状态
                    archiveboxInput.dispatchEvent(new Event('input'));
                }
            }
        }
        
        function showMessage(message, isError = false) {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `
                <div class="alert ${isError ? 'alert-danger' : 'alert-success'} d-flex align-items-center">
                    <i class="bi ${isError ? 'bi-exclamation-triangle' : 'bi-check-circle'} me-2"></i>
                    ${message}
                </div>`;
            
            // 5秒后自动清除消息
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
            
            // 滚动到消息区域
            messageArea.scrollIntoView({ behavior: 'smooth' });
        }
        
        function showError(message) {
            showMessage(message, true);
        }
        
        function openTab(evt, tabName) {
            console.log('切换到标签页:', tabName); // 调试日志
            
            // 隐藏所有标签内容
            const tabContents = document.getElementsByClassName('tab-content');
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove('active');
                tabContents[i].style.display = 'none'; // 强制隐藏
            }
            
            // 移除所有标签按钮的活动状态
            const tabButtons = document.getElementsByClassName('tab-button');
            for (let i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove('active');
            }
            
            // 显示当前标签内容并设置按钮为活动状态
            const targetTab = document.getElementById(tabName);
            if (targetTab) {
                targetTab.classList.add('active');
                targetTab.style.display = 'block'; // 强制显示
                console.log('成功激活标签页:', tabName);
            } else {
                console.error('找不到标签页:', tabName);
            }
            
            if (evt && evt.currentTarget) {
                evt.currentTarget.classList.add('active');
                console.log('成功激活标签按钮');
            } else {
                console.error('事件目标无效');
            }
        }
        
        async function analyzeContent() {
            // 隐藏结果区域
            document.getElementById('resultsArea').style.display = 'none';
            document.getElementById('batchResultsArea').style.display = 'none';
            
            // 显示加载器
            document.getElementById('loader').style.display = 'block';
            document.getElementById('analyzeBtn').disabled = true;
            
            try {
                const inputType = document.getElementById('inputType').value;
                const analysisType = document.getElementById('analysisType').value;
                
                // ArchiveBox 分析处理
                if (inputType === 'archivebox') {
                    const archiveboxInput = document.getElementById('archiveboxInput').value.trim();
                if (!archiveboxInput) {
                    if (typeof showTopRightNotification === 'function') {
                        showTopRightNotification(' 请输入要分析的URL', 'warning', 3000);
                    } else {
                        showError('请输入要分析的URL');
                    }
                    return;
                }                    // 检查是否为多个URL (包含换行符或分号)
                    const isBatch = archiveboxInput.includes('\n') || archiveboxInput.includes(';');
                    console.log('输入内容:', archiveboxInput);
                    console.log('是否为批量处理:', isBatch);
                    console.log('输入内容长度:', archiveboxInput.length);
                    console.log('包含换行符:', archiveboxInput.includes('\n'));
                    console.log('包含分号:', archiveboxInput.includes(';'));
                    
                    // 解析URL
                    let urls = [];
                    if (isBatch) {
                        // 处理多行输入和分号分隔
                        urls = archiveboxInput
                            .split(/[\n;]/)
                            .map(url => url.trim())
                            .filter(url => url);
                    } else {
                        urls = [archiveboxInput];
                    }
                    
                    console.log('解析后的URLs:', urls);
                    console.log('URL数量:', urls.length);
                    
                    // 重新检查：如果解析后有多个URL，强制设为批量处理
                    const finalIsBatch = urls.length > 1;
                    console.log('最终是否为批量处理:', finalIsBatch);
                    
                    // 构建请求数据
                    const requestData = {
                        analysis_type: analysisType,
                        no_analyze: document.getElementById('noAnalyzeCheck').checked
                    };
                    
                    // 根据单个还是批量URL设置不同参数
                    if (finalIsBatch) {
                        requestData.urls = urls;
                        console.log('发送批量请求，URLs:', requestData.urls);
                    } else {
                        requestData.url = archiveboxInput;
                        console.log('发送单个请求，URL:', requestData.url);
                    }
                    
                    // 发送请求
                    const response = await fetch('/api/analysis_archivebox', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    });
                    
                    const result = await response.json();
                    console.log('服务器返回结果:', result);
                    console.log('返回结果类型:', typeof result);
                    console.log('是否成功:', result.success);
                    console.log('当前isBatch状态:', isBatch);
                    
                    if (result.success) {
                        if (finalIsBatch) {
                            console.log('进入批量处理分支');
                            // 显示批量处理结果
                            if (typeof showTopRightNotification === 'function') {
                                showTopRightNotification(' 批量处理完成! 已处理 ' + (result.stats?.total || result.total_processed || urls.length || '未知') + ' 个URL', 'success', 6000);
                            } else {
                                showMessage('<strong>批量处理完成!</strong> 已处理 ' + (result.stats?.total || result.total_processed || urls.length || '未知') + ' 个URL。');
                            }
                            displayBatchResults(result);
                        } else {
                            console.log('进入单个URL处理分支');
                            // 单个URL结果展示
                            console.log('ArchiveBox单个URL结果:', result);
                            
                            // 检查是否仅存档模式
                            const noAnalyze = document.getElementById('noAnalyzeCheck').checked;
                            if (noAnalyze && result.status === 'archived') {
                                // 仅存档模式 - 显示存档成功信息
                                if (typeof showTopRightNotification === 'function') {
                                    showTopRightNotification('📦 存档完成! URL已成功存储到ArchiveBox', 'success', 5000);
                                } else {
                                    showMessage('<strong>存档完成!</strong> URL已成功存储到ArchiveBox。');
                                }
                                displayArchiveResult(result);
                            } else {
                                // 分析模式 - 显示分析结果
                                if (typeof showTopRightNotification === 'function') {
                                    showTopRightNotification(' 分析完成! 结果已生成，可查看分析详情', 'success', 5000);
                                } else {
                                    showMessage('<strong>分析完成!</strong> 结果已生成，可查看分析详情。');
                                }
                                displayAnalysisResult(result);
                            }
                        }
                    } else {
                        if (typeof showTopRightNotification === 'function') {
                            showTopRightNotification('处理失败: ' + (result.error || '未知错误'), 'error', 5000);
                        } else {
                            showError('处理失败: ' + (result.error || '未知错误'));
                        }
                    }
                    
                    // 隐藏加载器并启用按钮
                    document.getElementById('loader').style.display = 'none';
                    document.getElementById('analyzeBtn').disabled = false;
                    return;
                }
                
                // 原有的分析处理逻辑
                let formData = new FormData();
                formData.append('analysis_type', analysisType);
                
                // 根据输入类型设置不同的请求参数
                if (inputType === 'file') {
                    const fileInput = document.getElementById('fileInput');
                    if (!fileInput.files || fileInput.files.length === 0) {
                        if (typeof showTopRightNotification === 'function') {
                            showTopRightNotification(' 请选择要分析的文件', 'warning', 3000);
                        } else {
                            showError('请选择要分析的文件');
                        }
                        return;
                    }
                    formData.append('file', fileInput.files[0]);
                } else if (inputType === 'url') {
                    const urlInput = document.getElementById('urlInput').value.trim();
                    if (!urlInput) {
                        if (typeof showTopRightNotification === 'function') {
                            showTopRightNotification(' 请输入要分析的URL', 'warning', 3000);
                        } else {
                            showError('请输入要分析的URL');
                        }
                        return;
                    }
                    formData.append('content', urlInput);
                } else if (inputType === 'text') {
                    const textInput = document.getElementById('textInput').value.trim();
                    if (!textInput) {
                        if (typeof showTopRightNotification === 'function') {
                            showTopRightNotification(' 请输入要分析的文本内容', 'warning', 3000);
                        } else {
                            showError('请输入要分析的文本内容');
                        }
                        return;
                    }
                    formData.append('content', textInput);
                }
                
                // 发送请求到服务器
                const response = await fetch('/api/analysis', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    console.log('普通URL/文件分析结果:', result); // 添加调试日志
                    if (typeof showTopRightNotification === 'function') {
                        showTopRightNotification(' 分析完成! 结果已生成，可查看分析详情', 'success', 5000);
                    } else {
                        showMessage('<strong>分析完成!</strong> 结果已生成，可查看分析详情。');
                    }
                    displayAnalysisResult(result);
                } else {
                    if (typeof showTopRightNotification === 'function') {
                        showTopRightNotification('分析失败: ' + (result.error || '未知错误'), 'error', 5000);
                    } else {
                        showError('分析失败: ' + (result.error || '未知错误'));
                    }
                }
            } catch (error) {
                if (typeof showTopRightNotification === 'function') {
                    showTopRightNotification('分析过程出错: ' + error, 'error', 5000);
                } else {
                    showError('分析过程出错: ' + error);
                }
            } finally {
                // 隐藏加载器并启用按钮
                document.getElementById('loader').style.display = 'none';
                document.getElementById('analyzeBtn').disabled = false;
            }
        }
        
        // 使用增强版分析函数，由enhanced-analysis.js提供
        // 通过enhanced-analysis.js的handleAnalyzeClick获得更好的任务管理支持
        
        function displayAnalysisResult(result) {
            console.log('displayAnalysisResult收到的数据:', result); // 添加调试日志
            
            // 显示结果区域
            const resultsArea = document.getElementById('resultsArea');
            resultsArea.style.display = 'block';
            resultsArea.style.visibility = 'visible';
            resultsArea.style.opacity = '1';
            console.log('强制设置结果区域可见性');
            
            // 填充分析内容 - 使用 marked 解析 Markdown
            const analysisContent = document.getElementById('analysisContent');
            console.log('分析内容容器元素:', analysisContent); // 调试容器元素
            
            // 尝试多种可能的字段名称
            const content = result.analysis_content || result.answer || result.content || result.analysis_result || result.analysis || result.data;
            console.log('分析内容字段:', content); // 调试日志
            console.log('可用的结果字段:', Object.keys(result)); // 显示所有可用字段
            
            if (analysisContent) {
                if (content) {
                    try {
                        let parsedContent = safeMarkdownParse(content);
                        console.log('解析后的内容长度:', parsedContent.length);
                        console.log('解析后的内容预览:', parsedContent.substring(0, 200) + '...');
                        
                        // 添加原网页链接到IOC内容
                        const sourceUrl = result.source_url || result.url || result.link;
                        if (sourceUrl) {
                            parsedContent = addSourceLinkToIOCs(parsedContent, sourceUrl);
                        }
                        
                        analysisContent.innerHTML = parsedContent;
                        console.log('成功显示分析内容，长度:', content.length);
                        
                        // 强制检查内容是否真的插入了
                        setTimeout(() => {
                            console.log('延迟检查 - 分析内容区域HTML:', analysisContent.innerHTML.substring(0, 200) + '...');
                            console.log('延迟检查 - 分析内容区域是否可见:', analysisContent.offsetParent !== null);
                            console.log('延迟检查 - 结果区域是否可见:', document.getElementById('resultsArea').style.display);
                        }, 500);
                    } catch (error) {
                        console.error('处理分析内容时出错:', error);
                        analysisContent.innerHTML = `<pre>${content}</pre>`;
                    }
                } else {
                    console.log('未找到分析内容，尝试显示整个结果对象');
                    // 如果没有找到预期字段，显示整个结果对象以便调试
                    analysisContent.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                }
            } else {
                console.error('找不到分析内容容器元素 analysisContent');
            }
            
            // 填充引用信息
            const referencesContent = document.getElementById('referencesContent');
            console.log('引用信息容器元素:', referencesContent); // 调试容器元素
            
            const references = result.references || result.reference || result.refs || result.citation || result.citations;
            console.log('引用信息:', references); // 调试日志
            console.log('引用信息类型:', typeof references); // 调试类型
            
            if (referencesContent) {
                if (references) {
                    try {
                        let referencesMarkdown = '';
                        
                        // 检查引用信息的类型并相应处理
                        if (Array.isArray(references)) {
                            // 如果是数组，先过滤清洗，再转换为列表
                            const cleanedRefs = cleanReferencesArray(references);
                            referencesMarkdown = cleanedRefs.map(ref => `- ${ref}`).join('\n');
                            console.log('处理数组类型引用信息，原始数量:', references.length, '清洗后数量:', cleanedRefs.length);
                        } else if (typeof references === 'string') {
                            // 如果是字符串，先清洗再处理
                            const cleanedText = cleanReferencesText(references);
                            
                            if (cleanedText.includes('---')) {
                                // 使用 --- 分隔的格式，转换为列表
                                const refArray = cleanedText.split('---').map(ref => ref.trim()).filter(ref => ref);
                                const cleanedRefs = cleanReferencesArray(refArray);
                                referencesMarkdown = cleanedRefs.map(ref => `- ${ref}`).join('\n\n');
                                console.log('处理 --- 分隔的字符串引用信息，原始数量:', refArray.length, '清洗后数量:', cleanedRefs.length);
                            } else if (cleanedText.includes('\n-')) {
                                // 已经是列表格式的字符串
                                referencesMarkdown = cleanedText;
                                console.log('处理列表格式的字符串引用信息');
                            } else {
                                // 普通字符串，直接使用
                                referencesMarkdown = cleanedText;
                                console.log('处理普通字符串引用信息');
                            }
                        } else if (typeof references === 'object') {
                            // 如果是对象，转换为键值对列表
                            const cleanedEntries = Object.entries(references)
                                .map(([key, value]) => `- **${key}**: ${value}`)
                                .filter(entry => entry.length >= 50); // 过滤过短的条目
                            referencesMarkdown = cleanedEntries.join('\n');
                            console.log('处理对象类型引用信息，原始键数量:', Object.keys(references).length, '清洗后数量:', cleanedEntries.length);
                        } else {
                            // 其他类型，转换为字符串后清洗
                            const cleanedText = cleanReferencesText(String(references));
                            referencesMarkdown = cleanedText;
                            console.log('处理其他类型引用信息:', typeof references);
                        }
                        
                        if (referencesMarkdown.trim()) {
                            referencesContent.innerHTML = safeMarkdownParse(referencesMarkdown);
                            console.log('成功显示引用信息');
                        } else {
                            referencesContent.innerHTML = '<p class="text-muted">无有效的参考信息</p>';
                            console.log('所有不可读引用信息已被过滤，显示空状态');
                        }
                    } catch (error) {
                        console.error('处理引用信息时出错:', error);
                        // 出错时显示原始内容
                        referencesContent.innerHTML = `<pre class="text-muted">${String(references)}</pre>`;
                    }
                } else {
                    referencesContent.innerHTML = '<p class="text-muted">无引用信息</p>';
                }
            } else {
                console.error('找不到引用信息容器元素 referencesContent');
            }
            
            // 填充分析理由 - 处理不同格式的结果
            const rationaleContent = document.getElementById('rationaleContent');
            console.log('分析理由容器元素:', rationaleContent); // 调试容器元素
            console.log('分析理由数据:', result.rationale); // 调试数据
            
            if (rationaleContent) {
                if (result.rationale) {
                    try {
                        // 检查是否是JSON格式字符串
                        let rationaleText = result.rationale;
                        if (typeof rationaleText === 'string' && (rationaleText.trim().startsWith('{') || rationaleText.trim().startsWith('['))) {
                            try {
                                // 尝试将JSON字符串解析为对象
                                const jsonData = JSON.parse(rationaleText);
                                
                                // 如果是老格式的JSON对象
                                if (jsonData.hasOwnProperty('给出的文档') || jsonData.hasOwnProperty('外部知识库')) {
                                    let formattedRationale = '';
                                    if (jsonData['给出的文档']) {
                                        formattedRationale += `### 文档分析\n${jsonData['给出的文档']}\n\n`;
                                    }
                                    if (jsonData['外部知识库']) {
                                        formattedRationale += `### 外部知识\n${jsonData['外部知识库']}`;
                                    }
                                    rationaleContent.innerHTML = safeMarkdownParse(formattedRationale);
                                } 
                                // 如果是列表形式的分析点
                                else if (Array.isArray(jsonData)) {
                                    const listContent = jsonData.map(point => `- ${point}`).join('\n');
                                    rationaleContent.innerHTML = safeMarkdownParse(listContent);
                                } 
                                // 其他JSON格式，直接展示
                                else {
                                    // 为JSON对象的每个键创建一个小节
                                    let formattedContent = '';
                                    for (const [key, value] of Object.entries(jsonData)) {
                                        formattedContent += `### ${key}\n${value}\n\n`;
                                    }
                                    rationaleContent.innerHTML = safeMarkdownParse(formattedContent);
                                }
                            } catch (e) {
                                // JSON解析失败，作为普通文本处理
                                console.warn('JSON解析失败，作为普通文本处理:', e);
                                rationaleContent.innerHTML = safeMarkdownParse(rationaleText);
                            }
                        } else {
                            // 非JSON字符串，直接使用marked解析
                            rationaleContent.innerHTML = safeMarkdownParse(rationaleText);
                        }
                        console.log('成功显示分析理由');
                    } catch (error) {
                        console.error("处理分析理由时出错:", error);
                        rationaleContent.innerHTML = `<pre>${result.rationale}</pre>`;
                    }
                } else {
                    rationaleContent.innerHTML = '<p class="text-muted">无分析理由</p>';
                }
            } else {
                console.error('找不到分析理由容器元素 rationaleContent');
            }
            
            // 优化表格显示
            enhanceMarkdownTables();
            
            // 高亮 IOCs
            highlightIOCs();
            
            // 滚动到结果区域
            document.getElementById('resultsArea').scrollIntoView({behavior: 'smooth'});
            
            // 确保分析标签页激活并可见
            setTimeout(() => {
                const analysisTab = document.getElementById('analysisTab');
                const referencesTab = document.getElementById('referencesTab');
                const rationaleTab = document.getElementById('rationaleTab');
                
                // 确保所有标签页都存在并正确设置
                if (analysisTab) {
                    analysisTab.classList.add('active');
                    analysisTab.style.display = 'block';
                }
                if (referencesTab) {
                    referencesTab.classList.remove('active');
                    referencesTab.style.display = 'none';
                }
                if (rationaleTab) {
                    rationaleTab.classList.remove('active');
                    rationaleTab.style.display = 'none';
                }
                
                // 确保分析结果按钮是激活状态
                const tabButtons = document.getElementsByClassName('tab-button');
                for (let i = 0; i < tabButtons.length; i++) {
                    tabButtons[i].classList.remove('active');
                }
                if (tabButtons[0]) {
                    tabButtons[0].classList.add('active');
                }
                
                console.log('强制激活分析标签页并重置所有标签状态');
            }, 100);
            
            // 强制检查结果区域的可见性
            setTimeout(() => {
                const resultsArea = document.getElementById('resultsArea');
                const analysisTab = document.getElementById('analysisTab');
                console.log('最终检查 - 结果区域显示状态:', resultsArea.style.display);
                console.log('最终检查 - 结果区域可见性:', resultsArea.offsetParent !== null);
                console.log('最终检查 - 分析标签页是否激活:', analysisTab.classList.contains('active'));
                console.log('最终检查 - 分析内容区域内容:', document.getElementById('analysisContent').innerHTML.length > 0 ? '有内容' : '无内容');
            }, 1000);
        }

        // 将displayAnalysisResult函数暴露到全局，供enhanced-analysis.js调用
        window.displayAnalysisResult = displayAnalysisResult;

        // 显示存档结果（仅存档，不分析）
        function displayArchiveResult(result) {
            console.log('displayArchiveResult收到的数据:', result);
            
            // 显示结果区域，但内容不同
            const resultsArea = document.getElementById('resultsArea');
            resultsArea.style.display = 'block';
            resultsArea.style.visibility = 'visible';
            resultsArea.style.opacity = '1';
            
            // 创建存档结果的HTML内容
            const archiveInfo = `
                <div class="card border-success mb-3">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-archive"></i> 存档完成</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">URL已成功存储到ArchiveBox</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>源URL:</strong><br>
                                <a href="${result.source_url || '未知'}" target="_blank" class="text-break">${result.source_url || '未知'}</a></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>快照ID:</strong><br>
                                <code>${result.snapshot_id || '未知'}</code></p>
                            </div>
                        </div>
                        ${result.title ? `<p><strong>标题:</strong> ${result.title}</p>` : ''}
                        ${result.publication_date ? `<p><strong>发布日期:</strong> ${result.publication_date}</p>` : ''}
                        <p><strong>处理时间:</strong> ${result.process_time || '未知'}</p>
                        
                        <div class="d-flex gap-2 mt-3">
                            ${result.snapshot_id ? `
                                <button class="btn btn-primary" onclick="viewSnapshot('${result.snapshot_id}')">
                                    <i class="bi bi-eye"></i> 查看快照
                                </button>
                                <button class="btn btn-success" onclick="analyzeArchivedContent('${result.snapshot_id}', '${result.source_url}')">
                                    <i class="bi bi-lightning-charge"></i> 现在分析
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>说明:</strong> 该URL已成功存档到ArchiveBox，但未进行内容分析。您可以稍后通过"现在分析"按钮对存档内容进行威胁情报分析。
                </div>
            `;
            
            // 将存档信息显示在分析内容区域
            const analysisContent = document.getElementById('analysisContent');
            if (analysisContent) {
                analysisContent.innerHTML = archiveInfo;
            }
            
            // 清空引用信息和分析理由（因为没有分析）
            const referencesContent = document.getElementById('referencesContent');
            const rationaleContent = document.getElementById('rationaleContent');
            
            if (referencesContent) {
                referencesContent.innerHTML = '<div class="alert alert-secondary">该内容仅存档，未进行分析，因此无引用信息。</div>';
            }
            
            if (rationaleContent) {
                rationaleContent.innerHTML = '<div class="alert alert-secondary">该内容仅存档，未进行分析，因此无分析理由。</div>';
            }
            
            // 滚动到结果区域
            resultsArea.scrollIntoView({behavior: 'smooth'});
            
            // 确保分析标签页激活并可见
            setTimeout(() => {
                const analysisTab = document.getElementById('analysisTab');
                const referencesTab = document.getElementById('referencesTab');
                const rationaleTab = document.getElementById('rationaleTab');
                
                // 确保所有标签页都存在并正确设置
                if (analysisTab) {
                    analysisTab.classList.add('active');
                    analysisTab.style.display = 'block';
                }
                
                if (referencesTab) {
                    referencesTab.classList.remove('active');
                }
                
                if (rationaleTab) {
                    rationaleTab.classList.remove('active');
                }
                
                // 确保分析结果按钮是激活状态
                const tabButtons = document.getElementsByClassName('tab-button');
                for (let i = 0; i < tabButtons.length; i++) {
                    tabButtons[i].classList.remove('active');
                }
                if (tabButtons[0]) {
                    tabButtons[0].classList.add('active');
                }
            }, 100);
        }

        // 分析已存档的内容
        function analyzeArchivedContent(snapshotId, sourceUrl, parentTaskId) {
            // 立即更新按钮状态为"分析中"，防止重复点击
            const clickedButton = event.target;
            if (clickedButton) {
                console.log('立即更新按钮状态为分析中');
                clickedButton.disabled = true;
                clickedButton.className = 'btn btn-warning btn-sm';
                clickedButton.innerHTML = '<i class="bi bi-hourglass-split text-white me-1"></i>分析中...';
            }
            
            // 显示加载器
            document.getElementById('loader').style.display = 'block';
            
            // 构建请求数据
            const requestData = {
                snapshot_id: snapshotId,
                url: sourceUrl,
                analysis_type: document.getElementById('analysisType').value,
                no_analyze: false  // 明确要求分析
            };
            
            // 如果有父任务ID，添加到请求中
            if (parentTaskId && parentTaskId !== '' && parentTaskId !== 'undefined') {
                requestData.parent_task_id = parentTaskId;
            }
            
            // 发送分析请求
            fetch('/api/analysis_archivebox', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(result => {
                document.getElementById('loader').style.display = 'none';
                if (result.success) {
                    if (typeof showTopRightNotification === 'function') {
                        showTopRightNotification('✅ 分析完成! 存档内容已成功分析', 'success', 5000);
                    } else {
                        showMessage('<strong>分析完成!</strong> 存档内容已成功分析。');
                    }
                    
                    // 立即更新按钮状态为"已分析"
                    if (clickedButton) {
                        clickedButton.disabled = true;
                        clickedButton.className = 'btn btn-success btn-sm';
                        clickedButton.innerHTML = '<i class="bi bi-check-circle-fill text-white me-1"></i>已分析';
                        clickedButton.removeAttribute('onclick');
                    }
                    
                    // 向后端发送状态更新请求，确保下次刷新时显示"已分析"
                    const updateData = {
                        snapshot_id: snapshotId,
                        status: 'analyzed'
                    };
                    
                    // 如果分析结果包含analysis_id，也要更新
                    if (result.analysis_id) {
                        updateData.analysis_id = result.analysis_id;
                    }
                    
                    fetch('/api/update_task_status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(updateData)
                    })
                    .then(response => response.json())
                    .then(updateResult => {
                        console.log('任务状态更新结果:', updateResult);
                        
                        // 清除任务管理器缓存，确保下次查看时获取最新数据
                        const taskManager = window.taskManager || window.globalTaskManager;
                        if (taskManager && taskManager.taskStatusCache) {
                            // 如果更新了多个任务，清除所有任务缓存
                            if (updateResult.updated_tasks && updateResult.updated_tasks > 1) {
                                console.log(`已更新 ${updateResult.updated_tasks} 个任务，清除所有任务缓存`);
                                taskManager.taskStatusCache.clear();
                            } else if (parentTaskId) {
                                // 只更新了一个任务，只清除特定任务缓存
                                taskManager.taskStatusCache.delete(parseInt(parentTaskId));
                                console.log('已清除任务缓存，确保下次查看时获取最新数据');
                            }
                        }
                        
                        // 如果当前正在显示任务详情模态框，刷新任务数据
                        const taskModal = document.querySelector('.modal.show');
                        if (taskModal && parentTaskId) {
                            console.log('检测到任务详情模态框打开，准备刷新任务数据');
                            // 延迟刷新，确保数据库更新完成
                            setTimeout(async () => {
                                try {
                                    const taskManager = window.taskManager || window.globalTaskManager;
                                    if (taskManager) {
                                        console.log('开始刷新任务详情数据，任务ID:', parentTaskId);
                                        const refreshedTask = await taskManager.getTaskStatus(parentTaskId, true); // 强制刷新
                                        
                                        // 重新创建任务详情模态框内容
                                        const modalBody = taskModal.querySelector('.modal-body');
                                        if (modalBody && refreshedTask) {
                                            console.log('更新任务详情模态框内容');
                                            const newModalContent = createTaskDetailsModalContent(refreshedTask);
                                            modalBody.innerHTML = newModalContent;
                                        }
                                    }
                                } catch (error) {
                                    console.error('刷新任务详情失败:', error);
                                }
                            }, 1000); // 1秒延迟，确保数据库更新完成
                        }
                    })
                    .catch(error => {
                        console.error('更新任务状态失败:', error);
                    });
                    
                    // 显示分析结果
                    displayAnalysisResult(result);
                    
                } else {
                    if (typeof showTopRightNotification === 'function') {
                        showTopRightNotification('分析失败: ' + (result.error || '未知错误'), 'error', 5000);
                    } else {
                        showError('分析失败: ' + (result.error || '未知错误'));
                    }
                    
                    // 分析失败，恢复按钮状态
                    if (clickedButton) {
                        clickedButton.disabled = false;
                        clickedButton.className = 'btn btn-outline-success btn-sm';
                        clickedButton.innerHTML = '<i class="bi bi-lightning-charge"></i> 分析';
                    }
                }
            })
            .catch(error => {
                document.getElementById('loader').style.display = 'none';
                if (typeof showTopRightNotification === 'function') {
                    showTopRightNotification('分析过程出错: ' + error, 'error', 5000);
                } else {
                    showError('分析过程出错: ' + error);
                }
                
                // 出错，恢复按钮状态
                if (clickedButton) {
                    clickedButton.disabled = false;
                    clickedButton.className = 'btn btn-outline-success btn-sm';
                    clickedButton.innerHTML = '<i class="bi bi-lightning-charge"></i> 分析';
                }
            });
            
            console.log('分析请求已发送，快照ID:', snapshotId);
        }

        // 查看快照详情
        function viewSnapshot(snapshotId) {
            // 显示加载器
            document.getElementById('loader').style.display = 'block';
            
            // 发送请求获取快照详情
            fetch(`/api/analysis_archivebox?snapshot_id=${snapshotId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loader').style.display = 'none';
                    if (data.success && data.snapshot_url) {
                        // 在新窗口中打开ArchiveBox快照
                        window.open(data.snapshot_url, '_blank', 'width=1200,height=800');
                        if (typeof showTopRightNotification === 'function') {
                            showTopRightNotification(`已打开快照 ${snapshotId}`, 'info', 3000);
                        } else {
                            showMessage(`已打开快照 ${snapshotId}`, false);
                        }
                    } else {
                        if (typeof showTopRightNotification === 'function') {
                            showTopRightNotification('获取快照链接失败: ' + (data.error || '未知错误'), 'error', 5000);
                        } else {
                            showError('获取快照链接失败: ' + (data.error || '未知错误'));
                        }
                    }
                })
                .catch(error => {
                    document.getElementById('loader').style.display = 'none';
                    if (typeof showTopRightNotification === 'function') {
                        showTopRightNotification('获取快照详情时出错: ' + error, 'error', 5000);
                    } else {
                        showError('获取快照详情时出错: ' + error);
                    }
                });
        }

        // 简单的 Markdown 解析备用函数
        function simpleMarkdownParse(text) {
            if (!text) return '';
            
            let html = text
                // 标题
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                // 粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 斜体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                // 列表项
                .replace(/^- (.*$)/gim, '<li>$1</li>')
                // 代码块
                .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                // 行内代码
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                // 链接
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
                // 换行
                .replace(/\n/g, '<br>');
            
            // 处理列表
            html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');
            
            return html;
        }

        // 安全的 Markdown 解析函数
        function safeMarkdownParse(text) {
            try {
                if (typeof marked !== 'undefined') {
                    // 检查 marked 版本
                    if (marked.parse) {
                        // 新版本的 marked (v4+)
                        return marked.parse(text, {
                            breaks: true,
                            gfm: true,
                            headerIds: true,
                            mangle: false
                        });
                    } else if (marked) {
                        // 旧版本的 marked (v3 及以下)
                        return marked(text, {
                            breaks: true,
                            gfm: true,
                            headerIds: true,
                            mangle: false,
                            sanitize: false
                        });
                    }
                } else {
                    console.warn('marked 库不可用，使用简单解析器');
                    return simpleMarkdownParse(text);
                }
            } catch (error) {
                console.error('Markdown 解析出错:', error);
                return simpleMarkdownParse(text);
            }
        }

        // 增强 Markdown 表格显示
        function enhanceMarkdownTables() {
            // 为所有表格添加 Bootstrap 类
            const tables = document.querySelectorAll('.markdown-body table');
            tables.forEach(table => {
                table.classList.add('table', 'table-bordered', 'table-striped', 'table-hover', 'mb-4');
                // 确保表格响应式
                const wrapper = document.createElement('div');
                wrapper.classList.add('table-responsive');
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            });
        }

        // 高亮显示威胁指标 (IOCs)
        function highlightIOCs() {
            // 查找可能包含 IOC 的段落和列表项
            const elements = document.querySelectorAll('.markdown-body p, .markdown-body li');
            
            elements.forEach(el => {
                // 高亮 IP 地址
                el.innerHTML = el.innerHTML.replace(
                    /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, 
                    '<span class="bg-warning px-1 rounded">$&</span>'
                );
                
                // 高亮哈希值 (MD5, SHA1, SHA256)
                el.innerHTML = el.innerHTML.replace(
                    /\b([a-f0-9]{32}|[a-f0-9]{40}|[a-f0-9]{64})\b/gi,
                    '<span class="bg-light text-danger px-1 rounded">$&</span>'
                );
                
                // 高亮 URL 和域名
                el.innerHTML = el.innerHTML.replace(
                    /(https?:\/\/[^\s<]+|www\.[^\s<]+\.[^\s<]+)/g,
                    '<span class="bg-info text-white px-1 rounded">$&</span>'
                );
            });
        }

        // 显示批量处理结果
        function displayBatchResults(result) {
            console.log('displayBatchResults收到的数据:', result);
            console.log('数据类型和结构:', typeof result, Object.keys(result));
            
            // 显示批量结果区域
            const batchResultsArea = document.getElementById('batchResultsArea');
            batchResultsArea.style.display = 'block';
            
            // 隐藏普通结果区域
            document.getElementById('resultsArea').style.display = 'none';
            
            // 填充统计信息 - 适配多种可能的数据格式
            const batchStats = document.getElementById('batchStats');
            const stats = {
                total: result.total_processed || result.stats?.total || result.total || 0,
                success: result.success_count || result.stats?.success || result.success || 0,
                failed: result.failure_count || result.stats?.failed || result.failed || 0,
                archived: 0,
                analyzed: 0
            };
            
            // 计算已存档和已分析的数量
            if (result.results && Array.isArray(result.results)) {
                stats.archived = result.results.filter(r => r.status === 'archived' || r.status === 'analyzed').length;
                stats.analyzed = result.results.filter(r => r.status === 'analyzed').length;
            } else if (result.stats) {
                // 如果有 stats 对象，直接使用
                stats.archived = result.stats.archived || 0;
                stats.analyzed = result.stats.analyzed || 0;
            }
            
            console.log('处理后的统计信息:', stats);
            
            batchStats.innerHTML = `
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">${stats.total}</h5>
                                <p class="card-text">总计</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">${stats.success}</h5>
                                <p class="card-text">成功</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">${stats.archived}</h5>
                                <p class="card-text">已存档</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <h5 class="card-title">${stats.analyzed}</h5>
                                <p class="card-text">已分析</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h5 class="card-title">${stats.failed}</h5>
                                <p class="card-text">失败</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 填充结果表格
            const batchResultsTable = document.getElementById('batchResultsTable');
            let tableHTML = '';
            
            console.log('开始填充表格，结果数据:', result.results);
            
            // 获取任务ID用于父子关系
            const taskId = result.task_id;
            
            if (result.results && Array.isArray(result.results)) {
                console.log('处理结果数组，长度:', result.results.length);
                result.results.forEach((item, index) => {
                    console.log(`处理项目 ${index + 1}:`, item);
                    const statusBadge = getStatusBadge(item.status, item.success);
                    const snapshotId = item.snapshot_id || item.id || '未知';
                    const url = item.url || item.source_url || '未知';
                    
                    tableHTML += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>
                                <a href="${url}" target="_blank" class="text-decoration-none">
                                    ${url.length > 50 ? url.substring(0, 50) + '...' : url}
                                </a>
                            </td>
                            <td>${statusBadge}</td>
                            <td><code>${snapshotId}</code></td>
                            <td>
                                ${getActionButtons(item, taskId)}
                            </td>
                        </tr>
                    `;
                });
            } else if (result.url || result.source_url) {
                // 处理单个URL但被当作批量处理的情况
                console.log('处理单个URL的批量结果');
                const statusBadge = getStatusBadge(result.status, result.success);
                const snapshotId = result.snapshot_id || result.id || '未知';
                const url = result.url || result.source_url || '未知';
                
                tableHTML = `
                    <tr>
                        <td>1</td>
                        <td>
                            <a href="${url}" target="_blank" class="text-decoration-none">
                                ${url.length > 50 ? url.substring(0, 50) + '...' : url}
                            </a>
                        </td>
                        <td>${statusBadge}</td>
                        <td><code>${snapshotId}</code></td>
                        <td>
                            ${getActionButtons(result, taskId)}
                        </td>
                    </tr>
                `;
            } else {
                // 如果没有详细结果数组，创建基于统计的简单显示
                console.log('没有详细结果，显示统计信息');
                tableHTML = `
                    <tr>
                        <td colspan="5" class="text-center text-muted">
                            <i class="bi bi-info-circle"></i>
                            批量处理完成，但没有详细的结果列表。<br>
                            总计处理: ${stats.total} 个URL，成功: ${stats.success} 个，失败: ${stats.failed} 个<br>
                            <small class="text-muted">数据结构: ${JSON.stringify(result, null, 2)}</small>
                        </td>
                    </tr>
                `;
            }
            
            batchResultsTable.innerHTML = tableHTML;
            
            // 滚动到批量结果区域
            batchResultsArea.scrollIntoView({behavior: 'smooth'});
        }
        
        // 获取状态标签
        function getStatusBadge(status, success) {
            if (!success) {
                return '<span class="badge bg-danger">失败</span>';
            }
            
            switch(status) {
                case 'analyzed':
                    return '<span class="badge bg-success">已分析</span>';
                case 'archived':
                    return '<span class="badge bg-info">已存档</span>';
                case 'failed':
                    return '<span class="badge bg-danger">失败</span>';
                default:
                    return '<span class="badge bg-secondary">未知</span>';
            }
        }
        
        // 获取操作按钮
        function getActionButtons(item, parentTaskId) {
            if (!item.success) {
                return '<span class="text-muted">无操作</span>';
            }
            
            const snapshotId = item.snapshot_id || item.id;
            const url = item.url || item.source_url;
            
            // 根据状态显示不同按钮
            switch(item.status) {
                case 'analyzed':
                    // 分析成功 - 显示查看分析结果按钮
                    return `
                        <button class="btn btn-sm btn-outline-info" onclick="viewAnalysisResult('${item.analysis_id || snapshotId}')">
                            <i class="bi bi-file-text"></i> 查看分析
                        </button>
                    `;
                    
                case 'archived':
                    // 仅存档 - 显示分析按钮
                    return `
                        <button class="btn btn-sm btn-outline-success" onclick="analyzeArchivedContent('${snapshotId}', '${url}', '${parentTaskId || ''}')">
                            <i class="bi bi-lightning-charge"></i> 分析
                        </button>
                    `;
                    
                case 'analysis_failed':
                case 'content_extraction_failed':
                case 'analysis_error':
                    // 分析失败 - 显示查看快照按钮
                    return `
                        <button class="btn btn-sm btn-outline-warning" onclick="viewSnapshot('${snapshotId}')">
                            <i class="bi bi-eye"></i> 查看快照
                        </button>
                    `;
                    
                default:
                    return '<span class="text-muted">无操作</span>';
            }
        }
        
        // 查看分析结果
        function viewAnalysisResult(analysisId) {
            // 这里可以实现跳转到分析结果详情页面的逻辑
            console.log('查看分析结果:', analysisId);
            if (typeof showTopRightNotification === 'function') {
                showTopRightNotification('🚧 功能开发中：查看分析结果 ID ' + analysisId, 'info', 4000);
            } else {
                showMessage('功能开发中：查看分析结果 ID ' + analysisId);
            }
        }

        // 批量分析已存档未分析的快照
        async function batchAnalyzeUnanalyzed() {
            // 隐藏结果区域
            document.getElementById('resultsArea').style.display = 'none';
            document.getElementById('batchResultsArea').style.display = 'none';
            
            // 显示加载器
            document.getElementById('loader').style.display = 'block';
            document.getElementById('analyzeBatchUnanalyzedBtn').disabled = true;
            
            try {
                console.log('开始批量分析已存档未分析的快照...');
                
                const response = await fetch('/api/batch_analyze_unanalyzed', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        analysis_type: document.getElementById('analysisType').value || 'all'
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => null);
                    throw new Error(errorData?.message || `HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('批量分析响应:', result);
                
                if (result.success) {
                    if (result.pending_count === 0) {
                        if (typeof showTopRightNotification === 'function') {
                            showTopRightNotification('ℹ️ 没有找到待分析的快照，所有快照都已经分析过了', 'info', 4000);
                        } else {
                            showMessage('没有找到待分析的快照，所有快照都已经分析过了。', false);
                        }
                    } else {
                        if (typeof showTopRightNotification === 'function') {
                            showTopRightNotification(`批量分析已启动! 找到 ${result.pending_count} 个待分析快照，任务ID: ${result.task_id}`, 'success', 6000);
                        } else {
                            showMessage(`开始批量分析 ${result.pending_count} 个待分析快照。这可能需要一些时间...`, false);
                        }
                        
                        // 显示批量处理结果
                        displayBatchAnalysisResult(result);
                    }
                } else {
                    if (typeof showTopRightNotification === 'function') {
                        showTopRightNotification('批量分析启动失败: ' + (result.message || '未知错误'), 'error', 5000);
                    } else {
                        showError(result.message || '批量分析启动失败');
                    }
                }
                
            } catch (error) {
                console.error('批量分析错误:', error);
                if (typeof showTopRightNotification === 'function') {
                    showTopRightNotification('批量分析失败: ' + error.message, 'error', 5000);
                } else {
                    showError('批量分析失败: ' + error.message);
                }
            } finally {
                // 隐藏加载器
                document.getElementById('loader').style.display = 'none';
                document.getElementById('analyzeBatchUnanalyzedBtn').disabled = false;
            }
        }

        // 显示批量分析结果
        function displayBatchAnalysisResult(result) {
            console.log('显示批量分析结果:', result);
            
            // 确保有批量结果区域
            let batchResultsArea = document.getElementById('batchResultsArea');
            if (!batchResultsArea) {
                // 如果没有batchResultsArea，创建一个
                batchResultsArea = document.createElement('div');
                batchResultsArea.id = 'batchResultsArea';
                batchResultsArea.className = 'results mt-4';
                document.querySelector('.container').appendChild(batchResultsArea);
            }
            
            let html = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-arrow-repeat"></i> 批量分析结果</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> 
                            找到 ${result.pending_count} 个待分析快照，已启动批量分析进程。
                        </div>
            `;
            
            if (result.started_snapshots && result.started_snapshots.length > 0) {
                html += `
                    <h6>已启动分析的快照:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>快照ID</th>
                                    <th>URL</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                result.started_snapshots.forEach(snapshot => {
                    html += `
                        <tr>
                            <td><code>${snapshot.id}</code></td>
                            <td><a href="${snapshot.url}" target="_blank" class="text-truncate" style="max-width: 300px;">${snapshot.url}</a></td>
                            <td><span class="badge bg-warning">分析中</span></td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
            
            html += `
                        <div class="mt-3">
                            <small class="text-muted">
                                分析进程在后台运行，您可以稍后刷新页面查看结果。
                            </small>
                        </div>
                    </div>
                </div>
            `;
            
            batchResultsArea.innerHTML = html;
            batchResultsArea.style.display = 'block';
        }
        
        // 引用信息清洗函数 - 清洗文本内容
        function cleanReferencesText(text) {
            if (!text || typeof text !== 'string') {
                return '';
            }
            
            // 移除内存转储和寄存器内容的模式
            let cleaned = text
                // 移除十六进制内存转储 (如: 0x1234abcd: 48 65 6c 6c...)
                .replace(/0x[0-9a-fA-F]+:\s*([0-9a-fA-F]{2}\s*)+/g, '')
                // 移除寄存器内容 (如: RAX: 0x123456, RBX: 0x789abc...)
                .replace(/\b[A-Z]{2,4}:\s*0x[0-9a-fA-F]+/g, '')
                // 移除文件路径模式 (如: C:\Users\<USER>\\[^\s]+|\/[a-zA-Z0-9/_.-]+/g, '')
                // 移除时间戳模式 (如: 2023-01-01 12:34:56)
                .replace(/\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}/g, '')
                // 移除IP地址后跟端口的模式 (如: ***********:8080)
                .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d+\b/g, '')
                // 移除大量连续的十六进制字符 (超过20个字符)
                .replace(/\b[0-9a-fA-F]{20,}\b/g, '')
                // 移除多余的空白字符
                .replace(/\s+/g, ' ')
                .trim();
            
            return cleaned;
        }
        
        // 引用信息清洗函数 - 清洗数组
        function cleanReferencesArray(references) {
            if (!Array.isArray(references)) {
                return [];
            }
            
            return references
                .map(ref => {
                    // 如果是对象，提取有用信息
                    if (typeof ref === 'object' && ref !== null) {
                        const parts = [];
                        if (ref.title) parts.push(ref.title);
                        if (ref.source) parts.push(`来源: ${ref.source}`);
                        if (ref.url) parts.push(`链接: ${ref.url}`);
                        if (ref.description) parts.push(ref.description);
                        return parts.join(' - ');
                    }
                    return String(ref);
                })
                .map(ref => cleanReferencesText(ref)) // 应用文本清洗
                .filter(ref => {
                    // 过滤条件：
                    // 1. 长度至少50个字符
                    // 2. 不能全是数字和符号
                    // 3. 不能是纯粹的技术数据
                    if (ref.length < 50) return false;
                    
                    // 检查是否包含有意义的词汇
                    const meaningfulWords = /[一-龥]|[a-zA-Z]{3,}/;
                    if (!meaningfulWords.test(ref)) return false;
                    
                    // 排除主要由十六进制、数字构成的引用
                    const hexOrNumberRatio = (ref.match(/[0-9a-fA-F\s]/g) || []).length / ref.length;
                    if (hexOrNumberRatio > 0.7) return false;
                    
                    return true;
                })
                .slice(0, 20); // 限制最多显示20条引用，避免页面过长
        }
        
        // 在IOC相关内容后添加"原网页"链接
        function addSourceLinkToIOCs(htmlContent, sourceUrl) {
            if (!sourceUrl || sourceUrl === '#') {
                return htmlContent;
            }
            
            // 匹配IOC相关的标题和内容
            const iocPatterns = [
                /(<h[1-6][^>]*>.*?所有相关威胁指标.*?<\/h[1-6]>)/gi,
                /(<h[1-6][^>]*>.*?威胁指标.*?<\/h[1-6]>)/gi,
                /(<h[1-6][^>]*>.*?IOC.*?<\/h[1-6]>)/gi,
                /(<p[^>]*>.*?所有相关威胁指标.*?<\/p>)/gi,
                /(<strong>.*?相关威胁指标.*?<\/strong>)/gi
            ];
            
            let modifiedContent = htmlContent;
            
            // 遍历所有可能的IOC标题模式
            for (const pattern of iocPatterns) {
                modifiedContent = modifiedContent.replace(pattern, (match) => {
                    // 检测到IOC标题，但不添加单独的链接，保持原样
                    return match;
                });
            }
            
            // 检查是否有IOC内容（无论是否有标题）
            let hasIOCs = false;
            
            // 首先检查是否有IOC标题，并且标题后有真实的IOC内容
            for (const pattern of iocPatterns) {
                const matches = htmlContent.match(pattern);
                if (matches) {
                    // 找到IOC标题，检查标题后的内容
                    const titleMatch = matches[0];
                    const titleIndex = htmlContent.indexOf(titleMatch);
                    const contentAfterTitle = htmlContent.substring(titleIndex + titleMatch.length, titleIndex + titleMatch.length + 500);
                    
                    // 检查标题后是否包含"没有提供"、"无相关信息"等否定词汇
                    const hasNegativeIndicators = /没有提供|无相关信息|参考资料中没有|参考资料中没有提供具体的|未找到|不包含|无此信息|暂无|无法获取/gi.test(contentAfterTitle);
                    
                    if (!hasNegativeIndicators) {
                        // 没有否定词汇，检查是否有实际的IOC内容
                        const hasActualIOCs = /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b|[a-fA-F0-9]{32}|[a-fA-F0-9]{40}|[a-fA-F0-9]{64}|https?:\/\/|MD5:|SHA1:|SHA256:|IP地址：|域名：|文件哈希：/gi.test(contentAfterTitle);
                        
                        if (hasActualIOCs) {
                            hasIOCs = true;
                            break;
                        }
                    }
                }
            }
            
            // 如果没有IOC标题或标题后没有实际内容，再检查是否有IOC列表内容
            if (!hasIOCs) {
                const iocListPatterns = [
                    /(IP地址：.*?)(\n|<br>|$)/gi,
                    /(域名：.*?)(\n|<br>|$)/gi,
                    /(文件哈希：.*?)(\n|<br>|$)/gi,
                    /(URL：.*?)(\n|<br>|$)/gi,
                    /MD5:\s*[a-fA-F0-9]{32}/gi,
                    /SHA1:\s*[a-fA-F0-9]{40}/gi,
                    /SHA256:\s*[a-fA-F0-9]{64}/gi,
                    /\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/gi, // IP地址
                    /\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b/gi // 域名
                ];
                
                for (const pattern of iocListPatterns) {
                    if (pattern.test(modifiedContent)) {
                        hasIOCs = true;
                        break;
                    }
                }
            }
            
            // 如果检测到任何IOC内容，在文档末尾添加链接
            if (hasIOCs) {
                const sourceLinkHtml = `<small class="text-muted mt-2 d-block">
                    <i class="bi bi-info-circle"></i> 以上是报告中的IOC，详请查看 <a href="${sourceUrl}" target="_blank" class="text-primary">原网页</a>
                </small>`;
                
                // 在文档末尾添加链接
                modifiedContent += sourceLinkHtml;
            }
            
            return modifiedContent;
        }
    </script>
    
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>