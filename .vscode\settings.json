{"sqltools.connections": [{"mysqlOptions": {"authProtocol": "default", "enableSsl": "Disabled"}, "previewLimit": 50, "server": "************", "port": 3306, "driver": "MySQL", "name": "likai5", "database": "cyber_TI", "username": "likai5", "password": "likai5241203"}, {"mysqlOptions": {"authProtocol": "default", "enableSsl": "Disabled"}, "previewLimit": 50, "server": "localhost", "port": 3306, "driver": "MySQL", "name": "CTI", "username": "CTI", "database": "CTI_DB", "password": "cti0cd23"}], "python.testing.pytestArgs": ["MC<PERSON>_cope"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "python.analysis.extraPaths": ["./RAG"]}