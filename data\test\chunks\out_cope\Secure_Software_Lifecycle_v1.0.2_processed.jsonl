{"chunk_id": "line-1", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 5", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-2", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the mean time to identify that a breach had occurred was 197 days, and the mean time", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-3", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "to ﬁnd and ﬁx a vulnerability once the breach was detected was an additional 69 days.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-4", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Patches can introduce new vulnerabilities or other problems. Vulnerability patches are", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-5", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "considered urgent and can be rushed out, potentially introducing new problems to a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-6", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "system. For example, Microsoft’s early patches for the Meltdown1 chip ﬂaw introduced", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-7", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "an even more serious vulnerability in Windows 72. The new vulnerability allowed attackers", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-8", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "to read kernel memory much faster and to write their own memory, and could allow an", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-9", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "attacker to access every user-level computing process running on a machine.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-10", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Patches often go unapplied by customers. Users and system administrators may be", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-11", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "reluctant to apply security patches. For example, the highly-publicised Heartbleed3", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-12", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "vulnerability in OpenSSL allows attackers to easily and quietly exploit vulnerable systems,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-13", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "stealing passwords, cookies, private crypto-keys, and much more. The vulnerability was", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-14", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "reported in April 2014; but in January 2017 a scan revealed 200,000 Internet-accessible", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-15", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "devices remained unpatched [7]. Once a vulnerability is publicly reported, attackers", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-16", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "formulate a new mechanism to exploit the vulnerability with the knowledge that many", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-17", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "organisations will not adopt the ﬁx.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-18", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "In 1998, McGraw [5] advocated moving beyond the penetrate and patch approach based upon", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-19", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "his work on a DARPA-funded research effort investigating the application of software engi-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-20", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "neering to the assessment of software vulnerabilities. He contended that proactive rigorous", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-21", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "software analysis should play an increasingly important role in assessing and preventing", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-22", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "vulnerabilities in applications based upon the well-known fact that security violations occur", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-23", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "because of errors in software design and coding. In 2002, Viega and McGraw published", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-24", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the ﬁrst book on developing secure programs, Building Secure Software [6], with a focus on", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-25", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "preventing the injection of vulnerabilities and reducing security risk through an integration of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-26", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "security into a software development process.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-27", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "In the early 2000s, attackers became more aggressive, and Microsoft was a focus of this", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-28", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "aggression with exposure of security weaknesses in their products, particularly the Internet", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-29", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Information Services (IIS). Gartner, a leading research and advisory company who seldom", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-30", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "advises its clients to steer clear of speciﬁc software, advised companies to stop using IIS. In", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-31", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "response to customer concerns and mounting bad press, the then Microsoft CEO, Bill Gates,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-32", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "sent the Trustworthy Computing memo [2] to all employees on January 15, 2002. The memo", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-33", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "was also widely circulated on the Internet. An excerpt of the memo deﬁnes Trustworthy", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-34", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Computing:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-35", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "‘Trustworthy Computing is the highest priority for all the work we are doing. We", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-36", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "must lead the industry to a whole new level of Trustworthiness in computing ...", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-37", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Trustworthy Computing is computing that is as available, reliable and secure as", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-38", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "electricity, water services and telephony’.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-39", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The Trustworthy Computing memo caused a shift in the company. Two weeks later, Microsoft", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-40", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "announced the delay of the release of Windows .NET Server [8] to ensure a proper security", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-41", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "review (referred to as the Windows Security Push), as mandated by Microsoft’s Trustworthy", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-42", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Computing initiative outlined in this memo. In 2003, Microsoft employees Howard and Le", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-43", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Blanc [9] publicly published a second edition of a book on writing secure code to prevent", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-44", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "1https://meltdownattack.com/ Meltdown lets hackers get around a barrier between applications and computer", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-45", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "memory to steal sensitive data.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-46", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2https://www.cyberscoop.com/microsoft-meltdown-patches-windows-7-memory-management/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-47", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3http://heartbleed.com/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-48", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 4", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-49", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 9", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-50", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "5. Establish Design Requirements. Design requirements guide the implementation of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-51", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "’secure features’ (i.e., features that are well engineered with respect to security). Addi-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-52", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "tionally, the architecture and design must be resistant to known threats in the intended", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-53", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "operational environment.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-54", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The design of secure features involves abiding by the timeless security principles set", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-55", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "forth by Saltzer and Schroeder [16] in 1975 and restated by Viega and McGraw [6] in", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-56", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2002. The eight Saltzer and Schroeder principles are:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-57", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Economy of mechanism. Keep the design of the system as simple and small as", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-58", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "possible.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-59", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Fail-safe defaults. Base access decisions on permissions rather than exclusion; the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-60", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "default condition is lack of access and the protection scheme identiﬁes conditions", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-61", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "under which access is permitted. Design a security mechanism so that a failure", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-62", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "will follow the same execution path as disallowing the operation.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-63", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Complete mediation. Every access to every object must be checked for authorisa-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-64", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "tion.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-65", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Open design. The design should not depend upon the ignorance of attackers but", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-66", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "rather on the possession of keys or passwords.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-67", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Separation of privilege. A protection mechanism that requires two keys to unlock", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-68", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "is more robust than one that requires a single key when two or more decisions", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-69", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "must be made before access should be granted.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-70", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Least privilege. Every program and every user should operate using the least set", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-71", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of privileges necessary to complete the job.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-72", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Least common mechanism. Minimise the amount of mechanisms common to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-73", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "more than one user and depended on by all users.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-74", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Psychological acceptability. The human interface should be designed for ease of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-75", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "use so that users routinely and automatically apply the mechanisms correctly and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-76", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "securely.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-77", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Two other important secure design principles include the following:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-78", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Defense in depth. Provide multiple layers of security controls to provide redundancy", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-79", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "in the event a security breach.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-80", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Design for updating. The software security must be designed for change, such as", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-81", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "for security patches and security property changes.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-82", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Design requirements also involve the selection of security features, such as cryptography,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-83", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "authentication and logging to reduce the risks identiﬁed through threat modelling. Teams", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-84", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "also take actions to reduce the attack surface of their system design. The attack surface,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-85", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "a concept introduced by Howard [15] in 2003, can be thought of as the sum of the points", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-86", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "where attackers can try to enter data to or extract data from a system [24, 23].", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-87", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "In 2014, the IEEE Center for Secure Design [17] enumerated the top ten security design", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-88", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ﬂaws and provided guidelines on techniques for avoiding them. These guidelines are as", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-89", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "follows:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-90", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) Earn or give, but never assume, trust.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-91", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 8", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-92", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 10", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-93", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Use an authentication mechanism that cannot be bypassed or tampered with.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-94", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) Authorise after you authenticate.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-95", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(d) Strictly separate data and control instructions, and never process control instruc-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-96", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "tions received from untrusted sources.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-97", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(e) Deﬁne an approach that ensures all data are explicitly validated.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-98", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(f) Use cryptography correctly.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-99", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(g) Identify sensitive data and how they should be handled.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-100", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(h) Always consider the users.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-101", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(i) Understand how integrating external components changes your attack surface.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-102", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(j) Be ﬂexible when considering future changes to objects and actors.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-103", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "6. Deﬁne and Use Cryptography Standards. The use of cryptography is an important", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-104", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "design feature for a system to ensure security- and privacy-sensitive data is protected", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-105", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "from unintended disclosure or alteration when it is transmitted or stored. However, an", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-106", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "incorrect choice in the use of cryptography can render the intended protection weak or", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-107", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ineffective. Experts should be consulted in the use of clear encryption standards that", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-108", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "provide speciﬁcs on every element of the encryption implementation and on the use", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-109", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of only properly vetted encryption libraries. Systems should be designed to allow the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-110", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "encryption libraries to be easily replaced, if needed, in the event the library is broken by", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-111", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "an attacker, such as was done to the Data Encryption Standard (DES) through ’Deep", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-112", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Crack’9, a brute force search of every possible key as designed by Paul Kocher, president", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-113", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of Cryptography Research.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-114", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "7. Manage the Security Risk of Using Third-Party Components. The vast majority of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-115", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "software projects are built using proprietary and open-source third-party components.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-116", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The Black Duck On-Demand audit services group [18] conducted open-source audits", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-117", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "on over 1,100 commercial applications and found open-source components in 95%", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-118", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of the applications with an average 257 components per application. Each of these", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-119", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "components can have vulnerabilities upon adoption or in the future. An organisation", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-120", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "should have an accurate inventory of third-party components [32], continuously use", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-121", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "a tool to scan for vulnerabilities in its components, and have a plan to respond when", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-122", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "new vulnerabilities are discovered. Freely available and proprietary tools can be used to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-123", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "identify project component dependencies and to check if there are any known, publicly", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-124", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "disclosed, vulnerabilities in these components.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-125", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "8. Use Approved Tools. An organisation should publish a list of approved tools and their", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-126", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "associated security checks and settings such as compiler/linker options and warn-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-127", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ings. Engineers should use the latest version of these tools, such as compiler versions,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-128", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and take advantage of new security analysis functionality and protections. Often, the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-129", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "resultant software must be backward compatible with previous versions.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-130", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "9. Perform Static Analysis Security Testing (SAST). SAST tools can be used for an auto-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-131", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "mated security code review to ﬁnd instances of insecure coding patterns and to help", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-132", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ensure that secure coding policies are being followed. SAST can be integrated into the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-133", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "commit and deployment pipeline as a check-in gate to identify vulnerabilities each time", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-134", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the software is built or packaged. For increased efﬁciency, SAST tools can integrate into", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-135", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "9https://w2.eff.org/Privacy/Crypto/Crypto misc/DESCracker/HTML/19980716 eff des faq.html", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-136", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 9", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-137", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 11", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-138", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the developer environment and be run by the developer during coding. Some SAST tools", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-139", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "spot certain implementation bugs, such as the existence of unsafe or other banned", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-140", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "functions and automatically replace with (or suggest) safer alternatives as the developer", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-141", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "is actively coding. See also the Software Security CyBOK Knowledge Area [1].", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-142", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "10. Perform Dynamic Analysis Security Testing (DAST). DAST performs run-time veriﬁca-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-143", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "tion of compiled or packaged software to check functionality that is only apparent when", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-144", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "all components are integrated and running. DAST often involves the use of a suite", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-145", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of pre-built attacks and malformed strings that can detect memory corruption, user", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-146", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "privilege issues, injection attacks, and other critical security problems. DAST tools may", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-147", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "employ fuzzing, an automated technique of inputting known invalid and unexpected test", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-148", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "cases at an application, often in large volume. Similar to SAST, DAST can be run by the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-149", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "developer and/or integrated into the build and deployment pipeline as a check-in gate.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-150", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "DAST can be considered to be automated penetration testing. See also the Software", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-151", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Security CyBOK Knowledge Area [1].", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-152", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "11. Perform Penetration Testing. Manual penetration testing is black box testing of a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-153", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "running system to simulate the actions of an attacker. Penetration testing is often", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-154", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "performed by skilled security professionals, who can be internal to an organisation or", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-155", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "consultants, opportunistically simulating the actions of a hacker. The objective of a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-156", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "penetration test is to uncover any form of vulnerability - from small implementation", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-157", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "bugs to major design ﬂaws resulting from coding errors, system conﬁguration faults,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-158", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "design ﬂaws or other operational deployment weaknesses. Tests should attempt both", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-159", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "unauthorised misuse of and access to target assets and violations of the assumptions.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-160", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "A widely-referenced resource for structuring penetration tests is the OWASP Top 10", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-161", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Most Critical Web Application Security Risks10. As such, penetration testing can ﬁnd the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-162", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "broadest variety of vulnerabilities, although usually less efﬁciently compared with SAST", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-163", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and DAST [19]. Penetration testers can be referred to as white hat hackers or ethical", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-164", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "hackers. In the penetration and patch model, penetration testing was the only line of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-165", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "security analysis prior to deploying a system.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-166", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "12. Establish a Standard Incident Response Process. Despite a secure software lifecycle,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-167", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "organisations must be prepared for inevitable attacks. Organisations should proactively", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-168", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "prepare an Incident Response Plan (IRP). The plan should include who to contact in", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-169", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "case of a security emergency, establish the protocol for efﬁcient vulnerability mitigation,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-170", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "for customer response and communication, and for the rapid deployment of a ﬁx. The", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-171", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "IRP should include plans for code inherited from other groups within the organisation", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-172", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and for third-party code. The IRP should be tested before it is needed. Lessons learned", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-173", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "through responses to actual attack should be factored back into the SDL.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-174", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "10https://www.owasp.org/index.php/Category:OWASP Top Ten Project", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-175", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 10", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-176", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 12", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-177", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2.1.2", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-178", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Touchpoints", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-179", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "International software security consultant, Gary McGraw, provided seven Software Security", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-180", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Touchpoints [10] by codifying extensive industrial experience with building secure products.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-181", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "McGraw uses the term touchpoint to refer to software security best practices which can be", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-182", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "incorporated into a secure software lifecycle. McGraw differentiates vulnerabilities that are", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-183", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "implementation bugs and those that are design ﬂaws [17]. Implementation bugs are localized", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-184", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "errors, such as buffer overﬂow and input validation errors, in a single piece of code, making", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-185", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "spotting and comprehension easier. Design ﬂaws are systemic problems at the design level", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-186", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of the code, such as error-handling and recovery systems that fail in an insecure fashion or", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-187", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "object-sharing systems that mistakenly include transitive trust issues [10]. Kuhn et al. [32]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-188", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "analysed the 2008 - 2016 vulnerability data from the US National Vulnerability Database (NVD)11", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-189", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and found that 67% of the vulnerabilities were implementation bugs. The seven touchpoints", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-190", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "help to prevent and detect both bugs and ﬂaws.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-191", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "These seven touchpoints are described below and are provided in order of effectiveness", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-192", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "based upon McGraw’s experience with the utility of each practice over many years, hence", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-193", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "prescriptive:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-194", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "1. Code Review (Tools).", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-195", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Code review is used to detect implementation bugs. Manual code review may be used,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-196", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "but requires that the auditors are knowledgeable about security vulnerabilities before", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-197", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "they can rigorously examine the code. ’Code review with a tool’ (a.k.a. the use of static", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-198", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "analysis tools or SAST) has been shown to be effective and can be used by engineers", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-199", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "that do not have expert security knowledge. For further discussion on static analysis,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-200", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "see Section 2.1.1 bullet 9.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-201", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2. Architectural Risk Analysis.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-202", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Architectural Risk Analysis, which can also be referred to as threat modelling (see Section", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-203", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2.1.1 bullet 4), is used to prevent and detect design ﬂaws. Designers and architects", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-204", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "provide a high-level view of the target system and documentation for assumptions, and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-205", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "identify possible attacks. Through architectural risk analysis, security analysts uncover", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-206", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and rank architectural and design ﬂaws so mitigation can begin. For example, risk", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-207", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "analysis may identify a possible attack type, such as the ability for data to be intercepted", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-208", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and read. This identiﬁcation would prompt the designers to look at all their code’s", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-209", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "trafﬁcs ﬂows to see if interception was a worry, and whether adequate protection (i.e.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-210", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "encryption) was in place. That review that the analysis prompted is what uncovers", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-211", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "design ﬂaws, such as sensitive data is transported in the clear.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-212", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "No system can be perfectly secure, so risk analysis must be used to prioritise security", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-213", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "efforts and to link system-level concerns to probability and impact measures that matter", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-214", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "to the business building the software. Risk exposure is computed by multiplying the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-215", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "probability of occurrence of an adverse event by the cost associated with that event [33].", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-216", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "McGraw proposes three basic steps for architectural risk analysis:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-217", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• Attack resistance analysis. Attack resistance analysis uses a checklist/systematic", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-218", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "approach of considering each system component relative to known threats, as is", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-219", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "done in Microsoft threat modelling discussed in Section 2.1.1 bullet 4. Information", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-220", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "about known attacks and attack patterns are used during the analysis, identifying", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-221", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "11http://nvd.nist.gov", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-222", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 11", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-223", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 16", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-224", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) Software Engineering Institute (SEI) CERT Secure Coding Standards 18", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-225", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Special care must also be given to handling unanticipated errors in a controlled and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-226", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "graceful way through generic error handlers or exception handlers that log the events.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-227", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "If the generic handlers are invoked, the application should be considered to be in an", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-228", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "unsafe state such that further execution is no longer considered trusted.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-229", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "4. Manage Security Risk Inherent in the Use of Third-Party Components. See Section", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-230", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2.1.1 bullet 7.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-231", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "5. Testing and Validation. See Section 2.1.1 bullets 9-11 and Section 2.1.2 bullets 1, 3 and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-233", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "6. Manage Security Findings. The ﬁrst ﬁve practices produce artifacts that contain or", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-234", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "generate ﬁndings related to the security of the product (or lack thereof). The ﬁndings in", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-235", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "these artifacts should be tracked and actions should be taken to remediate vulnerabilities,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-236", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "such as is laid out in the Common Criteria (see Section 4.3) ﬂaw remediation procedure", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-237", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[36]. Alternatively, the team may consciously accept the security risk when the risk is", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-238", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "determined to be acceptable. Acceptance of risk must be tracked, including a severity", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-239", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "rating; a remediation plan, an expiration or a re-review deadline; and the area for re-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-240", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "review/validation.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-241", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Clear deﬁnitions of severity are important to ensure that all participants have and com-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-242", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "municate with a consistent understanding of a security issue and its potential impact.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-243", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "A possible starting point is mapping to the severity levels, attributes, and thresholds", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-244", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "used by the Common Vulnerability Scoring System (CVSS)19 such as 10–8.5 is critical,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-245", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "8.4–7.0 is high, etc. The severity levels are used to prioritise mitigations based upon", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-246", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "their complexity of exploitation and impact on the properties of a system.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-247", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "7. Vulnerability Response and Disclosure. Even with following a secure software lifecycle,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-248", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "no product can be ’perfectly secure’ because of the constantly changing threat land-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-249", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "scapes. Vulnerabilities will be exploited and the software will eventually be compromised.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-250", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "An organisation must develop a vulnerability response and disclosure process to help", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-251", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "drive the resolution of externally discovered vulnerabilities and to keep all stakeholders", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-252", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "informed of progress. ISO provides industry-proven standards20 for vulnerability dis-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-253", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "closure and handling. To prevent vulnerabilities from re-occurring in new or updated", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-254", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "products, the team should perform a root cause analysis and feed the lessons learned", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-255", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "into the secure software lifecycle practices. For further discussion, see Sections 2.1.1", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-256", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "bullet 12 and 2.1.2 bullet 7.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-257", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "8. Planning the Implementation and Deployment of Secure Development. A healthy and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-258", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "mature secure development lifecycle includes the above seven practices but also an", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-259", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "integration of these practices into the business process and the entire organisation,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-260", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "including program management, stakeholder management, deployment planning, met-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-261", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "rics and indicators, and a plan for continuous improvement. The culture, expertise", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-262", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and skill level of the organisation needs to be considered when planning to deploy a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-263", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "secure software lifecycle. Based upon past history, the organisation may respond better", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-264", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "to a corporate mandate, to a bottom-up groundswell approach or to a series of pilot", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-265", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "programs. Training will be needed (see Section 2.1.1 bullet 1). The speciﬁcation of the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-266", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "organisation’s secure software lifecycle including the roles and responsibilities should", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-267", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "18https://wiki.sei.cmu.edu/conﬂuence/display/seccode/SEI+CERT+Coding+Standards", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-268", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "19https://www.ﬁrst.org/cvss/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-269", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "20https://www.iso.org/standard/45170.html and https://www.iso.org/standard/53231.html", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-270", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 15", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-271", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 21", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-272", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3.2", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-273", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Mobile", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-274", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Security concerns for mobile apps differ from traditional desktop software in some important", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-275", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ways, including local data storage, inter-app communication, proper usage of cryptographic", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-276", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "APIs and secure network communication. The OWASP Mobile Security Project [40] is a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-277", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "resource for developers and security teams to build and maintain secure mobile applications;", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-278", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "see also the Web & Mobile Security CyBOK Knowledge Area [47].", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-279", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Four resources are provided to aid in the secure software lifecycle of mobile applications:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-280", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "1. OWASP Mobile Application Security Veriﬁcation Standard (MASVS) Security Require-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-281", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ments and Veriﬁcation. The MASVS deﬁnes a mobile app security model and lists", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-282", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "generic security requirements for mobile apps. The MASVS can be used by architects,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-283", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "developers, testers, security professionals, and consumers to deﬁne and understand", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-284", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the qualities of a secure mobile app.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-285", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2. Mobile Security Testing Guide (MSTG). The guide25 is a comprehensive manual for", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-286", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "mobile application security testing and reverse engineering for iOS and Android mobile", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-287", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "security testers. The guide provides the following content:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-288", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) A general mobile application testing guide that contains a mobile app security test-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-289", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ing methodology and general vulnerability analysis techniques as they apply to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-290", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "mobile app security. The guide also contains additional technical test cases that are", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-291", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "operating system independent, such as authentication and session management,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-292", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "network communications, and cryptography.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-293", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Operating system-dependent testing guides for mobile security testing on the An-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-294", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "droid and iOS platforms, including security basics; security test cases; reverse", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-295", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "engineering techniques and prevention; and tampering techniques and prevention.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-296", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) Detailed test cases that map to the requirements in the MASVS.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-297", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3. Mobile App Security Checklist. The checklist26 is used for security assessments and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-298", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "contains links to the MSTG test case for each requirement.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-299", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "4. Mobile Threat Model. The threat model [41] provides a checklist of items that should", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-300", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "be documented, reviewed and discussed when developing a mobile application. Five", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-301", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "areas are considered in the threat model:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-302", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) Mobile Application Architecture. The mobile application architecture describes", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-303", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "device-speciﬁc features used by the application, wireless transmission protocols,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-304", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "data transmission medium, interaction with hardware components and other appli-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-305", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "cations. The attack surface can be assessed through a mapping to the architecture.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-306", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Mobile Data. This section of the threat model deﬁnes the data the application", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-307", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "stores, transmits and receives. The data ﬂow diagrams should be reviewed to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-308", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "determine exactly how data are handled and managed by the application.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-309", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) Threat Agent Identiﬁcation. The threat agents are enumerated, including humans", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-310", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and automated programs.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-311", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(d) Methods of Attack. The most common attacks utilised by threat agents are deﬁned", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-312", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "so that controls can be developed to mitigate attacks.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-313", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "25https://www.owasp.org/index.php/OWASP Mobile Security Testing Guide", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-314", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "26https://github.com/OWASP/owasp-mstg/tree/master/Checklists", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-315", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 20", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-316", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 22", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-317", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(e) Controls. The controls to mitigate attacks are deﬁned.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-318", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3.3", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-319", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Cloud Computing", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-320", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The emergence of cloud computing bring unique security risks and challenges. In conjunction", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-321", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "with the Cloud Security Alliance (CSA)27, SAFECode has provided a ’Practices for Secure", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-322", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Development of Cloud Applications’ [42] guideline as a supplement to the ’Fundamental", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-323", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Practices for Secure Software Development’ guideline [34] discussed in Section 2.1.3 - see", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-324", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "also the Distributed Systems Security CyBOK Knowledge Area [48]. The Cloud guideline", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-325", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "provides additional secure development recommendations to address six threats unique to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-326", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "cloud computing and to identify speciﬁc security design and implementation practices in the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-327", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "context of these threats. These threats and associated practices are provided:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-328", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "1. Threat: Multitenancy. Multitenancy allows multiple consumers or tenants to maintain", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-329", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "a presence in a cloud service provider’s environment, but in a manner where the compu-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-330", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "tations, processes, and data (both at rest and in transit) of one tenant are isolated from", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-331", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and inaccessible to another tenant. Practices:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-332", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) Model the application’s interfaces in threat models. Ensure that the multitenancy", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-333", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "threats, such as information disclosure and privilege elevation are modeled for each", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-334", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of these interfaces, and ensure that these threats are mitigated in the application", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-335", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "code and/or conﬁguration settings.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-336", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Use a ’separate schema’ database design and tables for each tenant when building", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-337", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "multitenant applications rather than relying on a ’TenantID’ column in each table.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-338", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) When developing applications that leverage a cloud service provider’s Platform as", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-339", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "a Service (PaaS) services, ensure common services are designed and deployed in", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-340", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "a way that ensures that the tenant segregation is maintained.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-341", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2. Tokenisation of Sensitive Data. An organisation may not wish to generate and store", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-342", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "intellectual property in a cloud environment not under its control. Tokenisation is a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-343", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "method of removing sensitive data from systems where they do not need to exist or", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-344", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "disassociating the data from the context or the identity that makes them sensitive.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-345", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The sensitive data are replaced with a token for those data. The token is later used to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-346", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "rejoin the sensitive data with other data in the cloud system. The sensitive data are", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-347", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "encrypted and secured within an organisation’s central system which can be protected", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-348", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "with multiple layers of protection and appropriate redundancy for disaster recovery and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-349", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "business continuity. Practices:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-350", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) When designing a cloud application, determine if the application needs to process", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-351", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "sensitive data and if so, identify any organisational, government, or industry regu-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-352", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "lations that pertain to that type of sensitive data and assess their impact on the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-353", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "application design.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-354", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Consider implementing tokenisation to reduce or eliminate the amount of sensitive", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-355", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "data that need to be processed and or stored in cloud environments.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-356", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) Consider data masking, an approach that can be used in pre-production test and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-357", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "debug systems in which a representative data set is used, but does not need to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-358", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "27https://cloudsecurityalliance.org/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-359", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 21", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-360", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 23", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-361", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "have access to actual sensitive data. This approach allows the test and debug", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-362", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "systems to be exempt from sensitive data protection requirements.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-363", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3. Trusted Compute Pools. Trusted Compute Pools are either physical or logical groupings", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-364", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of compute resources/systems in a data centre that share a security posture. These", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-365", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "systems provide measured veriﬁcation of the boot and runtime infrastructure for mea-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-366", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "sured launch and trust veriﬁcation. The measurements are stored in a trusted location", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-367", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "on the system (referred to as a Trusted Platform Module (TPM)) and veriﬁcation occurs", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-368", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "when an agent, service or application requests the trust quote from the TPM. Practices:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-369", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) Ensure the platform for developing cloud applications provides trust measurement", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-370", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "capabilities and the APIs and services necessary for your applications to both", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-371", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "request and verify the measurements of the infrastructure they are running on.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-372", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Verify the trust measurements as either part of the initialisation of your application", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-373", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "or as a separate function prior to launching the application.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-374", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) Audit the trust of the environments your applications run on using attestation", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-375", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "services or native attestation features from your infrastructure provider.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-376", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "4. Data Encryption and Key Management. Encryption is the most pervasive means of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-377", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "protecting sensitive data both at rest and in transit. When encryption is used, both", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-378", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "providers and tenants must ensure that the associated cryptographic key materials are", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-379", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "properly generated, managed and stored. Practices:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-380", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) When developing an application for the cloud, determine if cryptographic and key", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-381", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "management capabilities need to be directly implemented in the application or", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-382", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "if the application can leverage cryptographic and key management capabilities", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-383", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "provided by the PaaS environment.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-384", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Make sure that appropriate key management capabilities are integrated into the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-385", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "application to ensure continued access to data encryption keys, particularly as the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-386", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "data move across cloud boundaries, such as enterprise to cloud or public to private", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-387", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "cloud.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-388", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "5. Authentication and Identity Management. As an authentication consumer, the appli-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-389", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "cation may need to authenticate itself to the PaaS to access interfaces and services", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-390", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "provided by the PaaS. As an authentication provider, the application may need to authen-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-391", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ticate the users of the application itself. Practices:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-392", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) Cloud application developers should implement the authentication methods and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-393", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "credentials required for accessing PaaS interfaces and services.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-394", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Cloud application developers need to implement appropriate authentication meth-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-395", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ods for their environments (private, hybrid or public).", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-396", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) When developing cloud applications to be used by enterprise users, developers", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-397", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "should consider supporting Single Sign On (SSO) solutions.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-398", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "6. Shared-Domain Issues. Several cloud providers offer domains that developers can use", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-399", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "to store user content, or for staging and testing their cloud applications. As such, these", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-400", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "domains, which may be used by multiple vendors, are considered ’shared domains’ when", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-401", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "running client-side script (such as JavaScript) and from reading data. Practices:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-402", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) Ensure that your cloud applications are using custom domains whenever the cloud", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-403", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "provider’s architecture allows you to do so.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-404", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 22", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-405", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 24", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-406", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Review your source code for any references to shared domains.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-407", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The European Union Agency for Cybersecurity (ENISA) [44] conducted an in-depth and indepen-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-408", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "dent analysis of the information security beneﬁts and key security risks of cloud computing.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-409", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The analysis reports that the massive concentrations of resources and data in the cloud", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-410", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "present a more attractive target to attackers, but cloud-based defences can be more robust,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-411", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "scalable and cost-effective.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-412", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3.4", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-413", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Internet of Things (IoT)", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-414", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The Internet of Things (IoT) is utilised in almost every aspect of our daily life, including the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-415", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "extension into industrial sectors and applications (i.e. Industrial IOT (IIoT)). IoT and IIoT", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-416", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "constitute an area of rapid growth that presents unique security challenges. [From this point", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-417", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "forth we include IIoT when we use IoT.] Some of these are considered in the Cyber-Physical", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-418", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Systems Security CyBOK Knowledge Area [49], but we consider speciﬁcally software lifecycle", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-419", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "issues here. Devices must be securely provisioned, connectivity between these devices and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-420", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the cloud must be secure, and data in storage and in transit must be protected. However, the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-421", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "devices are small, cheap, resource-constrained. Building security into each device may not be", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-422", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "considered to be cost effective by its manufacturer, depending upon the value of the device", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-423", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and the importance of the data it collects. An IoT-based solution often has a large number of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-424", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "geographically-distributed devices. As a result of these technical challenges, trust concerns", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-425", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "exist with the IoT, most of which currently have no resolution and are in need of research.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-426", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "However, the US National Institute of Standards and Technology (NIST) [43] recommends", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-427", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "four practices for the development of secure IoT-based systems.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-428", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "1. Use of Radio-Frequency Identiﬁcation (RFID) tags. Sensors and their data may be tam-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-429", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "pered with, deleted, dropped, or transmitted insecurely. Counterfeit ’things’ exist in the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-430", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "marketplace. Unique identiﬁers can mitigate this problem by attaching Radio-Frequency", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-430", "line_number": 430, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-431", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Identiﬁcation (RFID) tags to devices. Readers activate a tag, causing the device to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-432", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "broadcast radio waves within a bandwidth reserved for RFID usage by governments", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-433", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "internationally. The radio waves transmit identiﬁers or codes that reference unique", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-434", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "information associated with the device.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-435", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2. Not using or allowing the use of default passwords or credentials. IoT devices are often", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-436", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "not developed to require users and administrators to change default passwords during", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-437", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "system set up. Additionally, devices often lack intuitive user interfaces for changing", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-438", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "credentials. Recommended practices are to require passwords to be changed or to", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-439", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "design in intuitive interfaces. Alternatively, manufacturers can randomise passwords", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-440", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "per device rather than having a small number of default passwords.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-441", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3. Use of the Manufacturer Usage Description (MUD) speciﬁcation. The Manufacturer", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-442", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Usage Description (MUD)28 speciﬁcation allows manufacturers to specify authorised", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-443", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and expected user trafﬁc patterns to reduce the threat surface of an IoT device by", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-444", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "restricting communications to/from the device to sources and destinations intended by", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-445", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the manufacturer.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-446", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "4. Development of a Secure Upgrade Process. In non-IoT systems, updates are usually", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-447", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "delivered via a secure process in which the computer can authenticate the source", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-448", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "pushing the patches and feature and conﬁguration updates. IoT manufacturers have,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-449", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "generally, not established such a secure upgrade process, which enables attackers", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-450", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "28https://tools.ietf.org/id/draft-ietf-opsawg-mud-22.html", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-451", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 23", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-452", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 30", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-453", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3. Domain: Secure software development lifecycle touchpoints", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-454", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) Architecture analysis", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-455", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Code review", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-456", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) Security testing", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-457", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "4. Domain: Deployment", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-458", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(a) Penetration testing", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-459", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(b) Software environment", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-460", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(c) Conﬁguration management and vulnerability management", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-461", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "BSIMM assessments are conducted through in-person interviews by software security profes-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-462", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "sionals at Cigital (now Synopsys) with security leaders in a ﬁrm. Via the interviews, the ﬁrm", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-463", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "obtains a scorecard on which of the 113 software security activities the ﬁrm uses. After the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-464", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ﬁrm completes the interviews, they are provided information comparing themselves with the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-465", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "other organisations that have been assessed. BSIMM assessments have been conducted", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-466", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "since 2008. Annually, the overall results of the assessments from all ﬁrms are published,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-467", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "resulting in the BSIMM1 through BSIMM9 reports. Since the BSIMM study began in 2008,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-468", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "167 ﬁrms have participated in BSIMM assessment, sometimes multiple times, comprising", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-469", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "389 distinct measurements. To ensure the continued relevance of the data reported, the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-470", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "BSIMM9 report excluded measurements older than 42 months and reported on 320 distinct", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-471", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "measurements collected from 120 ﬁrms.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-472", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "4.3", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-473", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The Common Criteria", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-474", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The purpose of this Common Criteria (CC)41 is to provide a vehicle for international recog-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-475", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "nition of a secure information technology (IT) product (where the SAMM and BSIMM were", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-476", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "assessments of a development process). The objective of the CC is for IT products that", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-477", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "have earned a CC certiﬁcate from an authorised Certiﬁcation/Validation Body (CB) to be", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-478", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "procured or used with no need for further evaluation. The Common Criteria seek to provide", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-479", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "grounds for conﬁdence in the reliability of the judgments on which the original certiﬁcate", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-480", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "was based by requiring that a CB issuing Common Criteria certiﬁcates should meet high and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-481", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "consistent standards. A developer of a new product range may provide guidelines for the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-482", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "secure development and conﬁguration of that product. This guideline can be submitted as a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-483", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Protection Proﬁle (the pattern for similar products that follow on). Any other developer can", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-484", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "add to or change this guideline. Products that earn certiﬁcation in this product range use the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-485", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "protection proﬁle as the delta against which they build.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-486", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Based upon the assessment of the CB, a product receives an Evaluation Assurance Level", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-487", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(EAL). A product or system must meet speciﬁc assurance requirements to achieve a particular", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-488", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "EAL. Requirements involve design documentation, analysis and functional or penetration", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-489", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "testing. The highest level provides the highest guarantee that the system’s principal security", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-490", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "features are reliably applied. The EAL indicates to what extent the product or system was", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-491", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "tested:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-492", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "• EAL 1: Functionally tested. Applies when security threats are not viewed as serious.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-493", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The evaluation provides evidence that the system functions in a manner consistent with", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-494", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "41https://www.commoncriteriaportal.org/ccra/index.cfm", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-495", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 29", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-496", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 32", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-497", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "DISCUSSION", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-498", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[53]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-499", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "This chapter has provided an overview of of three prominent and prescriptive secure software", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-500", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "lifecycle processes and six adaptations of these processes that can be applied in a speciﬁed", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-501", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "domain. However, the cybersecurity landscape in terms of threats, vulnerabilities, tools, and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-502", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "practices is ever evolving. For example, a practice has has not be been mentioned in any of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-503", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "these nine processes is the use of a bug bounty program for the identiﬁcation and resolution", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-504", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of vulnerabilities. With a bug bounty program, organisations compensate individuals and/or", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-505", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "researchers for ﬁnding and reporting vulnerabilities. These individuals are external to the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-506", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "organisation producing the software and may work independently or through a bug bounty", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-507", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "organisation, such as HackerOne42.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-508", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "While the majority of this knowledge area focuses on technical practices, the successful", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-509", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "adoption of these practices involves organisational and cultural changes in an organisation.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-510", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The organisation, starting from executive leadership, must support the extra training, resources,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-511", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and steps needed to use a secure development lifecycle. Additionally, every developer must", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-512", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "uphold his or her responsibility to take part in such a process.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-513", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "A team and an organisation need to choose the appropriate software security practices to de-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-514", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "velop a customised secure software lifecycle based upon team and technology characteristics", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-515", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and upon the security risk of the product.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-516", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "While this chapter has provided practices for developing secure products, information in-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-517", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "security is often due to economic disincentives [53] which drives software organizations", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-518", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "to choose the rapid deployment and release of functionality over the production of secure", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-519", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "products. As a result, increasingly governments and industry groups are imposing cyber", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-520", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "security standards on organisations as a matter of legal compliance or as a condition for", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-521", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "being considered as a vendor. Compliance requirements may lead to faster adoption of a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-522", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "secure development lifecycle. However, this compliance-driven adoption may divert efforts", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-523", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "away from the real security issues by driving an over-focus on compliance requirements rather", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-524", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "than on the pragmatic prevention and detection of the most risky security concerns.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-525", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "42https://www.hackerone.com", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-526", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 31", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-527", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 34", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-528", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Software Security: Building Security In [10]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-529", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "This book discusses seven software securing best practices, called touchpoints. It also", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-530", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "provides information on software security fundamentals and contexts for a software security", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-531", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "program in an enterprise.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-532", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The Security Development Lifecycle (Original Book) [3]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-533", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "This seminal book provides the foundation for the other processes laid out in this knowledge", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-534", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "area, and was customised over the years by other organisations, such as Cisco 43. The book", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-535", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "lays out 13 stages for integrating practices into a software development lifecycle such that", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-536", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the product is more secure. This book is out of print, but is avaialble as a free download44.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-537", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The Security Development Lifecycle (Current Microsoft Resources) [11]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-538", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "The Microsoft SDL are practices that are used internally to build secure products and services,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-539", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and address security compliance requirements by introducing security practices throughout", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-540", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "every phase of the development process. This webpage is a continuously-updated version of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-541", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the seminal book [3] based on Microsoft’s growing experience with new scenarios such as", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-542", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "the cloud, the Internet of Things (IoT) and Artiﬁcial Intelligence (AI).", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-543", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Software Security Engineering: A Guide for Project Managers [26]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-544", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "This book is a management guide for selecting from among sound software development", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-545", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "practices that have been shown to increase the security and dependability of a software", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-546", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "product, both during development and subsequently during its operation. Additionally, this", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-547", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "book discusses governance and the need for a dynamic risk management approach for", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-548", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "identifying priorities throughout the product lifecycle.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-549", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Cyber Security Engineering: A Practical Approach for Systems and Software", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-550", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Assurance [54]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-551", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "This book provides a tutorial on the best practices for building software systems that exhibit", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-552", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "superior operational security, and for considering security throughout your full system de-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-553", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "velopment and acquisition lifecycles. This book provides seven core principles of software", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-554", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "assurance, and shows how to apply them coherently and systematically. This book addresses", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-555", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "important topics, including the use of standards, engineering security requirements for acquir-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-556", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ing COTS software, applying DevOps, analysing malware to anticipate future vulnerabilities,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-557", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and planning ongoing improvements.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-558", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "43https://www.cisco.com/c/en/us/about/trust-center/technology-built-in-security.html#∼stickynav=2", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-559", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "44https://blogs.msdn.microsoft.com/microsoft press/2016/04/19/free-ebook-the-security-development-lifecycle/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-560", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 33", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-561", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 35", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-562", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "SAFECode’s Fundamental Practices for Secure Software Development: Es-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-563", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "sential Elements of a Secure Development Lifecycle Program, Third Edition", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-564", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[34]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-565", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Eight practices for secure development are provided based upon the experiences of member", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-566", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "companies of the SAFECode organisation.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-567", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "OWASP’s Secure Software Development Lifecycle Project (S-SDLC) [12]", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-568", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Based upon a committee of industry participants, the Secure-Software Development Lifecycle", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-569", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Project (S-SDLC) deﬁnes a standard Secure Software Development Life Cycle and provides", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-570", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "resources to help developers know what should be considered or best practices at each", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-571", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "phase of a development lifecycle (e.g., Design Phase/Coding Phase/Maintain Phase/etc.) The", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-572", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "committee of industry participants are members of the Open Web Application Security Project", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-573", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(OWASP)45, an international not-for-proﬁt organisation focused on improving the security of", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-574", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "web application software. The earliest secure software lifecycle contributions from OWASP", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-575", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "were referred to as the Comprehensive, Lightweight Application Security Process (CLASP).", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-576", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Security controls", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-577", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Government and standards organizations have provided security controls to be integrated in", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-578", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "a secure software or systems lifecyle:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-579", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "1. The Trustworthy Software Foundation 46 provides the the Trustworthy Software Frame-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-580", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "work (TSFr) 47 a collection of good practice, existing guidance and relevant standards", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-581", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "across the ﬁve main facets of trustworthiness: Safety; Reliability; Availability; Resilience;", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-582", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "and Security. The purpose of the TSFr is to provide a minimum set of controls such that,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-583", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "when applied, all software (irrespective of implementation constraints) can be speciﬁed,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-584", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "realised and used in a trustworthy manner.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-585", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2. The US National Institute of Standards and Technology (NIST) has authored the Systems", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-586", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Security Engineering Cyber Resiliency Considerations for the Engineering [55] framework", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-587", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "(NIST SP 800-160). This Framework provides resources on cybersecurity Knowledge,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-588", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Skills and Abilitiess (KSAs), and tasks for a number of work roles for achieving the", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-589", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "identiﬁed cyber resiliency outcomes based on a systems engineering perspective on", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-590", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "system life cycle processes.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-591", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "3. The Software Engineering Institute (SEI) has collaborated with professional organisa-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-592", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "tions, industry partners and institutions of higher learning to develop freely-available", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-593", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "curricula and educational materials. Included in these materials are resources for a", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-594", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "software assurance program48 to train professionals to build security and correct func-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-595", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "tionality into software and systems.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-596", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "4. The UK National Cyber Security Centre (NCSC)49 provide resources for secure software", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-597", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "development:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-598", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "45https://www.owasp.org/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-599", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "46https://tsfdn.org", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-600", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "47https://tsfdn.org/ts-framework/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-601", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "48https://www.sei.cmu.edu/education-outreach/curricula/software-assurance/index.cfm", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-602", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "49https://www.ncsc.gov.uk/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-603", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 34", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-604", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "## Page 38", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-605", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[20] P. Hope, G. McGraw, and A. I. Anton, “Misuse and abuse cases: getting past the positive,”", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-606", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "IEEE Security and Privacy, vol. 2, no. 3, pp. 90–92, May 2004.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-607", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[21] G. Sindre and A. L. Opdahl, “Eliciting security requirements by misuse cases,” in Pro-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-608", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ceedings 37th International Conference on Technology of Object-Oriented Languages and", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-609", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Systems. TOOLS-Paciﬁc 2000, Nov 2000, pp. 120–131.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-610", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[22] K. Tuma, G. Calikli, and R. Scandariato, “Threat analysis of software systems: A systematic", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-611", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "literature review,” Journal of Systems and Software, vol. 144, 06 2018.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-612", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[23] P. K. Manadhata and J. M. Wing, “An attack surface metric,” IEEE Trans. Softw. Eng., vol. 37,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-613", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "no. 3, pp. 371–386, May 2011. [Online]. Available: http://dx.doi.org/10.1109/TSE.2010.60", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-614", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[24] C. Theisen, N. Munaiah, M. Al-Zyoud, J. C. Carver, A. Meneely, and L. Williams, “Attack", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-615", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "surface deﬁnitions: A systematic literature review,” Information and Software Technology,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-616", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "vol. 104, pp. 94 – 103, 2018. [Online]. Available: http://www.sciencedirect.com/science/", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-617", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "article/pii/S0950584918301514", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-618", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[25] N. M. Mohammed, M. Niazi, M. Alshayeb, and S. Mahmood, “Exploring software", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-619", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "security approaches in software development lifecycle: A systematic mapping study,”", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-620", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Comput. Stand. Interfaces, vol. 50, no. C, pp. 107–115, Feb. 2017. [Online]. Available:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-621", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "https://doi.org/10.1016/j.csi.2016.10.001", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-622", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[26] J. H. Allen, S. Barnum, R. J. Ellison, G. McGraw, and N. R. Mead, Software Security En-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-623", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "gineering: A Guide for Project Managers (The SEI Series in Software Engineering), 1st ed.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-624", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Addison-Wesley Professional, 2008.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-625", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[27] A. van Lamsweerde, “Elaborating security requirements by construction of intentional anti-", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-626", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "models,” in Proceedings of the 26th International Conference on Software Engineering,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-627", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ser. ICSE ’04.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-628", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Washington, DC, USA: IEEE Computer Society, 2004, pp. 148–157. [Online].", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-629", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Available: http://dl.acm.org.prox.lib.ncsu.edu/citation.cfm?id=998675.999421", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-630", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[28] G. Elahi and E. Yu, “A goal oriented approach for modeling and analyzing security", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-631", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "trade-offs,” in Proceedings of the 26th International Conference on Conceptual Modeling,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-632", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "ser. ER’07.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-633", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Berlin, Heidelberg: Springer-Verlag, 2007, pp. 375–390. [Online]. Available:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-634", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "http://dl.acm.org.prox.lib.ncsu.edu/citation.cfm?id=1784489.1784524", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-635", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[29] V. Saini, Q. Duan, and V. Paruchuri, “Threat modeling using attack trees,” J.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-636", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Comput. Sci. Coll., vol. 23, no. 4, pp. 124–131, Apr. 2008. [Online]. Available:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-637", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "http://dl.acm.org.prox.lib.ncsu.edu/citation.cfm?id=1352079.1352100", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-638", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[30] L. Williams, A. Meneely, and G. Shipley, “Protection poker: The new software security", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-639", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "”game”;,” IEEE Security Privacy, vol. 8, no. 3, pp. 14–20, May 2010.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-640", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[31] G. McGraw, “The new killer app for security: Software inventory,” Computer, vol. 51, no. 2,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-641", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "pp. 60–62, February 2018.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-642", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[32] R. Kuhn, M. Raunak, and R. Kacker, “What proportion of vulnerabilities can", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-643", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "be attributed to ordinary coding errors?:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-644", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Poster,”", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-645", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "in Proceedings of the 5th", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-646", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Annual Symposium and Bootcamp on Hot Topics in the Science of Security, ser.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-647", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "HoTSoS ’18.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-648", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "New York, NY, USA: ACM, 2018, pp. 30:1–30:1. [Online]. Available:", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-649", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "http://doi.acm.org/10.1145/3190619.3191686", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-650", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[33] NIST Computer Security, “Guide for conducting risk assessments,” National Institute", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-651", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of Standards and Technology, Tech. Rep. Special Publication 800-30 Revision 1, 2012.", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-652", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[Online]. Available: https://csrc.nist.gov/publications/detail/sp/800-30/rev-1/ﬁnal", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-653", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[34] SAFECode, “Fundamental practices for secure software development: Essential elements", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-654", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "of a secure development lifecycle program,” SAFECode, Tech. Rep. Third Edition, March", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-655", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "2018. [Online]. Available: https://safecode.org/wp-content/uploads/2018/03/SAFECode", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-656", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Fundamental Practices for Secure Software Development March 2018.pdf", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-657", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "[35] Joint Task Force Transformation Initiative, “Security and privacy controls for federal", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-658", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "information systems and organizations,” National Institute of Standards and Technology,", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
{"chunk_id": "line-659", "filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "content": "Page 37", "metadata": {"filename": "Secure_Software_Lifecycle_v1.0.2_processed.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\output\\Secure_Software_Lifecycle_v1.0.2_processed.txt"}}
