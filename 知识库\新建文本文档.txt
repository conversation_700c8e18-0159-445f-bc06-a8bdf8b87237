每天这种  APT  Botnet  恶意软件文章 每天不多


 1. 每天手动收集  质量高高的文章  然后记录下 URL
     （1）  国内 各大公众号  绿盟：绿盟威胁情报公众号发 、blog发
			奇安信：公众号
			360:360威胁情报中心
			
	有人做好了 RSS  github  有已经做好的数据源  FOLO  app


360网络实验室博客 - 360	https://blog.netlab.360.com	https://blog.netlab.360.com/rss



https://ti.qianxin.com/blog/        奇安信


https://www.secrss.com/articles?tag=威胁态势
  


https://blog.nsfocus.net/category/threat-alert/



HackerNews

https://hackernews.cc/             	http://hackernews.cc/feed   -rss





公众号APT组织分析文章

360威胁情报中心    ------  更新威胁组织活动信息
腾讯安全威胁情报中心  -----更新威胁组织活动信息（全是长图片，去掉）
奇安信威胁情报中心      ----------每周威胁情报解读
奇安网情局                 ----------国外网络工作的报告解读
绿盟科技威胁情报        ----------更新威胁组织活动信息
安天AVL威胁情报中心    --------------每周的情报盘点，一月的移动设备威胁盘点
白泽安全实验室     --------------威胁组织信息，每周情报
卡巴斯基威胁情报    --------------更新周期太长

收集定期更新高质量的微信公众号

爬虫了。需要定期切换微信公众号的cookie------------------------------------------------------------------------


     （2）国外： 知名网络安全厂商的Blog

可以使用archivebox进行爬取，自定义的爬取有编码问题
1--------------
https://cloud.google.com/blog/topics/threat-intelligence    谷歌威胁情报

https://feeds.feedburner.com/threatintelligence/pvexyqv7v0v   ----rss谷歌威胁情报

---------- Check Point
https://research.checkpoint.com/category/threat-research/feed/    -rss
https://research.checkpoint.com/category/threat-research/   ----网站 （日期提取不到）


2-----------------------------trellix

https://www.trellix.com/blogs/research/


3-------------------------------darkreading
https://www.darkreading.com/threat-intelligence    （报告时间获取不到，需要改进）


4-------------------------------微软
https://www.microsoft.com/en-us/security/blog/topic/threat-intelligence/




5------------------------Symantec Enterprise Blogs Threat Intelligence

https://www.security.com/threat-intelligence


6----------------------ASEC,ahnlab
https://asec.ahnlab.com/en/



7----------------
https://cofense.com/blog


8------------------- fortinet
https://www.fortinet.com/blog/threat-research


9-------------------Infoblox 威胁情报博客
https://blogs.infoblox.com/category/threat-intelligence/
 


10-----------------卡巴斯基
https://securelist.com/


11--------------------proofpoint
https://www.proofpoint.com/us/blog/threat-insight



12----------------趋势科技，分类较多
https://www.trendmicro.com/en_us/research.html?


13

https://www.fortinet.com/blog/threat-research



已实现

1. 每天手动收集  质量高高的文章  然后记录下 URL


2. URL 存档在Archive box中    linux  账户 sudo  


3. 自动读取 Archive Box中的存档分析，批量存，批量分析


4. 手动收集  质量高高的文章  域名记下来  加到自动爬虫的脚本中  


5. 不用爬虫爬数据  search api 拿到文章的URL  提供给Archive box  存档 循环读取


单独的一套运行流程了


search api 搞一个用临时邮箱自动注册search api 的工具，执行前调用拿到api，半自动已实现，


后面改成利用临时邮箱全自动获取 search api


存在的问题  

问题：先存档，再分析的任务，分析完成后，之前的存档任务中的



 site:cloud.google.com ("APT" OR "malware" OR "ransomware" OR "vulnerability" OR "threat intelligence" OR "zero-day" OR "phishing" OR "botnet" OR "backdoor" OR "exploit")
"backdoor" OR "exploit")       





 site:security.com/threat-intelligence ("APT" OR "malware" OR "ransomware" OR "vulnerability" OR "threat intelligence" OR "zero-day" OR "phishing" OR "botnet" OR "backdoor" OR "exploit")
"backdoor" OR "exploit")       

检索出来的

语法检索的关键词需要再细化，应该是site:"域名"  "APT" or "malware" or "threat intelligence"
就是这个域名下的关键词， 





Archive box  

[1] 2557947