# Environment Variable Settin  embedding_model: sentence-transformer-models/ATTACK-BERT  # 使用本地 ATT&CK BERT 专业网络安全模型
################################################################################
dotenv_path: configs\.env


# Logging Setting
################################################################################
log_root_dir: logs/test

# experiment_name: would be used to create log_dir = log_root_dir/experiment_name/
experiment_name: chunking

# 输入源类型: "files" 或 "database"
input_source_type: files


# 数据过滤设置
filtering:
  enabled: true
  min_tokens: 5  # 最小token数量
  min_words: 2    # 最小单词数量
  remove_duplicates: true  # 是否移除重复内容

database_setting:
  db_path: SQL_cope/Grammar.db
  limit: 10000  # 每次处理的记录数量
  offset: 0   # 开始位置的偏移量，初始为0，后续会自动更新
  resume: False  # 设置为true表示从上次处理的位置继续
  progress_file: "data/processing_progress.json"  # 记录处理进度的文件
  embedding_model: sentence-transformer-models/ATTACK-BERT  # 使用ATT&CK BERT专业网络安全模型

  # 其他可选模型:
  # sentence-transformers/all-MiniLM-L6-v2  # 通用轻量级模型
  # BAAI/bge-base-en-v1.5  # 通用多语言模型


# Input Document & Output Dir Setting
################################################################################
# 知识库\cve
# 知识库\ATTCK
# 知识库\cybok

input_doc_setting:
  doc_dir: 知识库\output

output_doc_setting:
  doc_dir: data/test/chunks/out_cope
  suffix: jsonl
  overwrite_existing: true  # 设置为false表示追加数据，true表示覆盖
  collection_name: threat_intel  # 向量数据库集合名称


# LLM Setting
################################################################################
llm_client:
  module_path: pikerag.llm_client
  # available class_name: AzureMetaLlamaClient, AzureOpenAIClient, HFMetaLlamaClient
  class_name: DeepSeekV3Client
  args: 
    api_key: "***********************************"

  llm_config:
  
    model: deepseek-chat
    temperature: 0
    # enable max_new_tokens when using llama model, response seems truncated without it
    # max_new_tokens: 1024

  cache_config:
    # location: will be joined with log_dir to generate the full path;
    #   if set to null, the experiment_name would be used
    location_prefix: null
    auto_dump: True


# Splitter Setting
################################################################################
chunking_protocol:
  module_path: pikerag.prompts.chunking
  chunk_summary: chunk_summary_protocol
  chunk_summary_refinement: chunk_summary_refinement_protocol
  chunk_resplit: chunk_resplit_protocol


# 添加向量化配置部分
vectorization:
  enabled: true  # 设
  output_dir: "data/test/chunks"
  collection_name: "threat_intel"
  embedding_model: "sentence-transformer-models/ATTACK-BERT"  # 使用本地 ATT&CK BERT 专业网络安全模型
  use_hybrid_search: true  # 启用混合检索



# RecursiveSentenceSplitter  是使用Scapy进行分割的

# splitter:
#   module_path: pikerag.document_transformers
#   class_name: LLMPoweredRecursiveSplitter
#   args:
#     separators:
#       - "\n"
#     is_separator_regex: False
#     chunk_size: 512
#     chunk_overlap: 0

# 还需要下载这个模型进行分割句子
# python -m spacy download en_core_web_lg
splitter:
  module_path: pikerag.document_transformers.splitter.recursive_sentence_splitter
  class_name: RecursiveSentenceSplitter
  args:
    lang: "en"  # 可以设置成"en"或"zh"，取决于您的文档主要语言
    chunk_size: 12  # 每个分片的句子数
    chunk_overlap: 4  # 分片间重叠的句子数
    num_parallel: 4  # 并行处理线程数，可根据CPU核心数调整
