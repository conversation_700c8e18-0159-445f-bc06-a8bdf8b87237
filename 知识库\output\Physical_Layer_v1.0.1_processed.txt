## Page 4

INTRODUCTION
This Knowledge Area is a review of the most relevant topics in wireless physical layer security.
The physical phenomenon utilized by the techniques presented in this Knowledge Area is the
radiation of electromagnetic waves. The frequencies considered hereinafter consist of the
entire spectrum that ranges from a few Hertz to frequencies beyond those of visible light
(optical spectrum). This Knowledge Area covers concepts and techniques that exploit the
way these signals propagate through the air and other transmission media. It is organised
into sections that describe security mechanisms for wireless communication methods as
well as some implications of unintended radio frequency emanations.
Since most frequencies used for wireless communication reside in the radio frequency spec-
trum and follow the well-understood laws of radio propagation theory, the majority of this
Knowledge Area is dedicated to security concepts based on physical aspects of radio fre-
quency transmission. The chapter therefore starts with an explanation of the fundamental
concepts and main techniques that were developed to make use of the wireless communi-
cation layer for conﬁdentiality, integrity, access control and covert communication. These
techniques mainly use properties of physical layer modulations and signal propagation to
enhance the security of systems.
After having presented schemes to secure the wireless channel, the Knowledge Area continues
with a review of security issues related to the wireless physical layer, focusing on those aspects
that make wireless communication systems different from wired systems. Most notably, signal
jamming, signal annihilation and jamming resilience. The section on jamming is followed
by a review of techniques capable of performing physical device identiﬁcation (i.e., device
ﬁngerprinting) by extracting unique characteristics from the device’s (analogue) circuitry.
Following this, the chapter continues to present approaches for performing secure distance
measurements and secure positioning based on electromagnetic waves. Protocols for dis-
tance measurements and positioning are designed in order to thwart threats on the physical
layer as well as the logical layer. Those attack vectors are covered in detail, together with
defense strategies and the requirements for secure position veriﬁcation.
Then, the Knowledge Area covers unintentional wireless emanations from devices such as
from computer displays and summarises wireless side-channel attacks studied in literature.
This is followed by a review on spooﬁng of analogue sensors. Unintentional emissions are
in their nature different from wireless communication systems, especially because these
interactions are not structured. They are not designed to carry information, however, they also
make use of—or can be affected by—electromagnetic waves.
Finally, after having treated the fundamental concepts of wireless physical security, this
Knowledge Area presents a selection of existing communication technologies and discusses
their security mechanisms. It explains design choices and highlights potential shortcomings
while referring to the principles described in the earlier sections. Included are examples from
near-ﬁeld communication and wireless communication in the aviation industry, followed by
the security considerations of cellular networks. Security of global navigation systems and of
terrestrial positioning systems is covered last since the security goals of such systems are
different from communication systems and are mainly related to position spooﬁng resilience.
KA Physical Layer & Telecommunications Security | July 2021
Page 3
## Page 5

CONTENT
1
PHYSICAL LAYER SCHEMES FOR CONFIDENTIALITY,
INTEGRITY AND ACCESS CONTROL
[1, 2, 3, 4, 5, 6]
Securing wireless networks is challenging due to the shared broadcast medium which makes
it easy for remote adversaries to eavesdrop, modify and block the communication between
devices. However, wireless communication also offers some unique opportunities. Radio
signals are affected by reﬂection, diffraction, and scattering, all of which contribute to a
complex multi-path behaviour of communicated signals. The channel response, as measured
at the receiver, can therefore be modelled as having frequency and position dependent random
components. In addition, within the short time span and in the absence of interference,
communicating parties will measure highly correlated channel responses. These responses
can therefore be used as shared randomness, unavailable to the adversary, and form a basis
of secure communication.
It should be noted that modern-day cryptography provides many different protocols to assure
the conﬁdentiality, integrity and authenticity of data transmitted using radio signals. If the
communicating parties are associated with each other or share a mutual secret, cryptographic
protocols can effectively establish secure communication by making use of cryptographic
keying material. However, if mere information exchange is not the only goal of a wireless
system (e.g., in a positioning system), or if no pre-shared secrets are available, cryptographic
protocols operating at higher layers of the protocol stack are not sufﬁcient and physical-layer
constructs can be viable solutions. The main physical layer schemes are presented in the
following sections.
1.1
Key Establishment based on Channel Reciprocity
The physical-layer randomness of a wireless channel can be used to derive a shared secret.
One of the main security assumptions of physical-layer key establishment schemes is that
the attacker is located at least half a wavelength away from the communicating parties.
According to wireless communication theory, it can be assumed that the attacker’s channel
measurements will be de-correlated from those computed by the communicating parties if
they are at least half a wavelength apart. The attacker will therefore likely not have access
to the measured secret randomness. If the attacker injects signals during the key genera-
tion, the signal that it transmits will, due to channel distortions, be measured differently at
communicating parties, resulting in key disagreement.
Physical layer key establishment schemes operate as follows. The communicating parties
(Alice and Bob) ﬁrst exchange pre-agreed, non-secret, data packets. Each party then measures
the channel response over the received packets. The key agreement is then typically executed
in three phases.
Quantisation Phase: Alice and Bob create a time series of channel properties that are measured
over the received packets. Example properties include RSSI and the CIR. Any property that
is believed to be non-observable by the attacker can be used. The measured time series are
then quantised by both parties independently. This quantisation is typically based on ﬁxed or
KA Physical Layer & Telecommunications Security | July 2021
Page 4
## Page 6

dynamic thresholds.
Information reconciliation phase: Since the quantisation phase is likely to result in disagreeing
sequences at Alice and Bob, they need to reconcile their sequences to correct for any errors.
This is typically done leveraging error correcting codes and privacy ampliﬁcation techniques.
Most schemes use simple level-crossing algorithms for quantisation and do not use coding
techniques. However, if the key derivation uses methods based on channel states whose
distributions are not necessarily symmetric, more sophisticated quantisation methods, such
as approximating the channel fading phenomena as a Gaussian source, or (multi-level) coding
is needed [2].
Key Veriﬁcation Phase: In this last phase, communicating parties conﬁrm that they established
a shared secret key. If this step fails, the parties need to restart key establishment.
Most of the research in physical-layer techniques has been concerned with the choice of
channel properties and of the quantisation technique. Even if physical-layer key establishment
techniques seem attractive, many of them have been shown to be vulnerable to active, physi-
cally distributed and multi-antenna adversaries. However, in a number of scenarios where the
devices are mobile, and where the attacker is restricted, they can be a valuable replacement
or enhancement to traditional public-key key establishment techniques.
1.2
MIMO-supported Approaches: Orthogonal Blinding, Zero-Forcing
Initially, physical-layer key establishment techniques were proposed in the context of single-
antenna devices. However, with the emergence of MIMO devices and beam-forming, re-
searchers have proposed to leverage these new capabilities to further secure communication.
Two basic techniques that were proposed in this context are orthogonal blinding and zero
forcing. Both of these techniques aim to enable the transmitter to wirelessly send conﬁdential
data to the intended receiver, while preventing the co-located attacker from receiving this
data. Although this might seem infeasible, since as well as the intended receiver, the attacker
can receive all transmitted packets. However, MIMO systems allow transmitters to ’steer’
the signal towards the intended receiver. For beam-forming to be effective, the transmitter
needs to know some channel information for the channels from its antennas to the antennas
of the receiver. As described in [5], these channels are considered to be secret from the
attacker. In Zero-Forcing, the transmitter knows the channels to the intended receiver as
well as to the attacker. This allows the transmitter to encode the data such that it can be
measured at the receiver, whereas the attacker measures nothing related to the data. In
many scenarios, assuming the knowledge of the channel to the attackers is unrealistic. In
Orthogonal Blinding, the transmitter doesn’t know the channel to the attacker, but knows the
channels to the receiver. The transmitter then encodes the data in the way that the receiver
can decode the data, whereas the attacker will receive data mixed with random noise. The
attacker therefore cannot decode the data. In order to communicate securely, the transmitter
and the receiver do not need to share any secrets. Instead, the transmitter only needs to know
(or measure) the channels to the intended receivers. Like physical-layer key establishment
techniques, these techniques have been show to be vulnerable to multi-antenna and physically
distributed attackers. They were further shown to be vulnerable to known-plaintext attacks.
KA Physical Layer & Telecommunications Security | July 2021
Page 5
## Page 7

1.3
Secrecy Capacity
Secrecy capacity is an information-theoretical concept that attempts to determine the maximal
rate at which a wireless channel can be used to transmit conﬁdential information without
relying on higher-layer encryption, even if there is an eavesdropper present. A famous result
by Shannon [7] says that, for an adversary with unbounded computing power, unconditionally
secure transmission can only be achieved if a one-time-pad cipher is used to encrypt the
transmitted information. However, Wyner later showed that if the attacker’s channel slightly
degrades the information, that is, the channel is noisy, the secrecy capacity can indeed be
positive under certain conditions [8]. This means it is possible to convey a secret message
without leaking any information to an eavesdropper. Csiszár and Korner extended Wyner’s
result by showing that the secrecy capacity is non-zero, unless the adversary’s channel (wiretap
channel) is less noisy than the channel that carries the message from the legitimate transmitter
to the receiver [9]. These theoretical results have been reﬁned for concrete channel models by
assuming a certain type of noise (e.g., Gaussian) and channel layout (e.g., SIMO and MIMO).
Researchers have managed to derive explicit mathematical expressions and bounds even
when taking into account complex phenomena such as fading which is present in wireless
channels [10].
A practical implementation of the concept of secrecy capacity can mainly be achieved using
the two methods described above. Either the communicating parties establish a secret key by
extracting features from the wireless channel (see 1.1) or they communicate with each other
using intelligent coding and transmission strategies possibly relying on multiple antennas
(see 1.2). Therefore, the study of secrecy capacity can be understood as the information-
theoretical framework for key establishment and MIMO-supported security mechanisms in
the context of wireless communication.
1.4
Friendly Jamming
Similar to Orthogonal Blinding, Friendly Jamming schemes use signal interference generated
by collaborating devices to either prevent an attacker from communicating with the protected
device, or to prevent the attacker from eavesdropping on messages sent by protected devices.
Friendly Jamming can therefore be used for both conﬁdentiality and access control. Unlike
Orthogonal Blinding, Friendly Jamming doesn’t leverage the knowledge of the channel to the
receiver. If a collaborating device (i.e., the friendly jammer) wants to prevent unauthorised
communication with the protected device it will jam the receiver of the protected device. If it
wants to prevent eavesdropping, it will transmit jamming signals in the vicinity of the protected
device. Preventing communication with a protected device requires no special assumptions
on the location of the collaborating devices. However, protecting against eavesdropping
requires that the eavesdropper is unable to separate the signals from the protected device
from those originating at the collaborating device. For this to hold, the channel from the
protected device to the attacker should not be correlated to the channel from the collaborating
device to the attacker. To ensure this, the protected device and the collaborating device need
to be typically placed less than half a carrier wavelength apart. This assumption is based on
the fact that, in theory, an attacker with multiple antennas who tries to tell apart the jamming
signal from the target signal requires the two transmitters to be separated by more than half a
wavelength. However, signal deterioration is gradual and it has been shown that under some
conditions, a multi-antenna attacker will be able to separate these signals and recover the
transmitted messages.
KA Physical Layer & Telecommunications Security | July 2021
Page 6
## Page 8

Friendly jamming was originally proposed for the protection of those medical implants (e.g.,
already implanted pacemakers) that have no abilities to perform cryptographic operations.
The main idea was that the collaborating device (i.e. ’the shield’) would be placed around the
user’s neck, close to the pacemaker. This device would then simultaneously receive and jam
all communication from the implant. The shield would then be able to forward the received
messages to any other authorised device using standard cryptographic techniques.
1.5
Using Physical Layer to Protect Data Integrity
Research into the use of physical layer for security is not only limited to the protection of data
conﬁdentiality. Physical layer can also be leveraged to protect data integrity. This is illustrated
by the following scenario. Assuming that two entities (Alice and Bob) share a common radio
communication channel, but do not share any secrets or authentication material (e.g., shared
keys or authenticated public keys), how can the messages exchanged between these entities
be authenticated and how can their integrity be preserved in the presence of an attacker? Here,
by message integrity, we mean that the message must be protected against any malicious
modiﬁcation, and by message authentication we mean that it should be clear who is the
sender of the message.
One basic technique that was proposed in this context is integrity codes, a modulation scheme
that provides a method of ensuring the integrity (and a basis for authentication) of a message
transmitted over a public channel. Integrity codes rely on the observation that, in a mobile
setting and in a multi-path rich environment, it is hard for the attacker to annihilate randomly
chosen signals.
Integrity codes assume a synchronised transmission between the transmitter and a receiver,
as well as the receiver being aware that it is in the range of the transmitter. To transmit
a message, the sender encodes the binary message using a unidirectional code (e.g., a
Manchester code), resulting in a known ration of 1s and 0s within an encoded message (for
Manchester code, the number of 1s and 0s will be equal). This encoded message is then
transmitted using on-off keying, such that each 0 is transmitted as an absence of signal and
each 1 as a random signal. To decode the message and check its integrity, the receiver simply
measures the energy of the signal. If the energy in a time slot is above a ﬁxed threshold, the
bit is interpreted as a 1 and if it is below a threshold, it is interpreted as a 0. If the ratio of
bits 1 and 0 corresponds to the encoding scheme, the integrity of the message is validated.
Integrity codes assume that the receiver knows when the transmitter is transmitting. This
means that their communication needs to be scheduled or the transmitter needs to always be
transmitting.
1.6
Low probability of intercept and Covert Communication
LPI signals are such signals that are difﬁcult to detect for the unintended recipient. The
simplest form of LPI is communication at a reduced power and with high directionality. Since
such communication limits the range and the direction of communication, more sophisticated
techniques were developed: Frequency Hopping, Direct Sequence Spread Spectrum and
Chirping. In Frequency Hopping the sender and the receiver hop between different frequency
channels thus trying to avoid detection. In Direct Sequence Spread Spectrum the information
signal is modulated with a high rate (and thus high bandwidth) digital signal, thus spreading
across a wide frequency band. Finally, Chirps are high speed frequency sweeps that carry
information. The hopping sequence or chirp sequence constitute a secret shared between
KA Physical Layer & Telecommunications Security | July 2021
Page 7
## Page 9

receiver and transmitter. This allows the legitimate receiver to recombine the signal while an
eavesdropper is unable to do so.
Covert communication is parasitic and leverages legitimate and expected transmissions
to enable unobservable communication. Typically, such communication hides within the
expected and tolerated deviations of the signal from its nominal form. One prominent example
is embedding of communicated bits within the modulation errors.
2
JAMMING AND JAMMING-RESILIENT COMMUNICATION
[11, 12]
Communication jamming is an interference that prevents the intended receiver(s) from suc-
cessfully recognising and decoding the transmitted message. It happens when the jammer
injects a signal which, when combined with the legitimate transmission, prevents the re-
ceiver from extracting the information contained in the legitimate transmission. Jamming
can be surgical and affect only the message preamble thus preventing decoding, or can be
comprehensive and aim to affect every symbol in the transmission.
Depending on their behaviour, jammers can be classiﬁed as constant or reactive. Constant
jammers transmit permanently, irrespective of the legitimate transmission. Reactive jammers
are most agile as they sense for transmission and then jam. This allows them to save energy
as well as to stay undetected. Jammer strength is typically expressed in terms of their output
power and their effectiveness as the jamming-to-signal ratio at the receiver. Beyond a certain
jamming-to-signal ratio, the receiver will not be able to decode the information contained in
the signal. This ratio is speciﬁc to particular receivers and communication schemes. The main
parameters that inﬂuence the success of jamming are transmission power of the jammer
and benign transmitter, their antenna gains, communication frequency, and their respective
distances to the benign receiver. These parameters will determine the jamming-to-signal ratio.
Countermeasures against jamming involve concealing from the adversary which frequencies
are used for communication at which time. This uncertainty forces the adversary to jam a wider
portion of the spectrum and therefore weakens their impact on the legitimate transmission,
effectively reducing the jamming-to-signal ratio. Most common techniques include Chirp,
FHSS and DSSS. Typically, these techniques rely on pre-shared secret keys, in which case we
call the communication ’coordinated’. Recently, to enable jamming resilience in scenarios in
which keys cannot be pre-shared (e.g., broadcast), uncoordinated FHSS and DSSS schemes
were also proposed.
2.1
Coordinated Spread Spectrum Techniques
Coordinated Spread Spectrum techniques are prevalent jamming countermeasures in a num-
ber of civilian and military applications. They are used not only to increase resilience to
jamming, but also to cope with interference from neighboring devices. Spreading is used in
practically all wireless communication technologies, in e.g.,802.11, cellular, Bluetooth, global
satellite positioning systems.
Spread spectrum techniques are typically effective against jammers that cannot cover the
entire communication spectrum at all times. These techniques make a sender spread a
signal over the entire available band of radio frequencies, which might require a considerable
KA Physical Layer & Telecommunications Security | July 2021
Page 8
## Page 10

M := m, sig(m), …
M1 M2
Ml
…
M3
M1
M2
Ml
m1
m2
ml
Figure 1: In UFH, the fragment linking protect against message insertion attack.
amount of energy. The attacker’s ability to impact the transmission is limited by the achieved
processing gain of the spread-spectrum communication. This gain is the ratio by which
interference can be suppressed relative to the original signal, and is computed as a ratio of the
spread signal radio frequency bandwidth to the un-spread information (baseband) bandwidth.
Spread-spectrum techniques use randomly generated sequences to spread information sig-
nals over a wider band of frequencies. The resulting signal is transmitted and then de-spread
at the receivers by correlating it with the spreading sequence. For this to work, it is essential
that the transmitter and receiver share the same secret spreading sequence. In FHSS, this
sequence is the set of central frequencies and the order in which the transmitter and receiver
switch between them in synchrony. In DSSS, the data signal is modulated with the spreading
sequence; this process effectively mixes the carrier signal with the spreading sequence, thus
increasing the frequency bandwidth of the transmitted signal. This process allows for both
narrow band and wide band jamming to be suppressed at the receiver. Unless the jammer
can guess the spreading code, its jamming signal will be spread at the receiver, whereas
the legitimate transmission will be de-spread, allowing for its detection. The secrecy of the
spreading codes is therefore crucial for the jamming resilience of spread spectrum systems.
This is why a number of civilian systems that use spreading with public spreading codes, such
as the GPS and 802.11b, remain vulnerable to jamming.
2.2
Uncoordinated Spread Spectrum Techniques
In broadcast applications and in applications in which communication cannot be anticipated
as scheduled, there is still a need to protect such communication from jamming.
To address such scenarios, uncoordinated spread spectrum techniques were proposed: UFH
and UDSSS. These techniques enable anti-jamming broadcast communication without pre-
shared secrets. uncoordinated frequency hopping relies on the fact that even if the sender
hops in a manner that is not coordinated with the receiver, the throughput of this channel will
be non-zero. In fact, if the receiver is broadband, it can recover all the messages transmitted by
the sender. UFH however, introduces new challenges. Given that the sender and the receiver
are not synchronised, and short message fragments transmitted within each hop are not
authenticated, the attacker can inject fragments that make the reassembly of the packets
infeasible. To prevent this, UFH includes fragment linking schemes that make this reassembly
possible even under poisoning.
UDSSS follows the principle of DSSS in terms of spreading the data using spreading sequences.
However, in contrast to anti-jamming DSSS where the spreading sequence is secret and shared
exclusively by the communication partners, in UDSSS, a public set of spreading sequences is
used by the sender and the receivers. To transmit a message, the sender repeatedly selects a
KA Physical Layer & Telecommunications Security | July 2021
Page 9
## Page 11

fresh, randomly selected spreading sequence from the public set and spreads the message
with this sequence. Hence, UDSSS neither requires message fragmentation at the sender
nor message reassembly at the receivers. The receivers record the signal on the channel
and despread the message by applying sequences from the public set, using a trial-and-error
approach. The receivers are not synchronised to the beginning of the sender’s message
and thus record for (at least) twice the message transmission time. After the sampling, the
receiver tries to decode the data in the buffer by using code sequences from the set and by
applying a sliding-window protocol.
2.3
Signal Annihilation and Overshadowing
Unlike jamming where the primary goal of the attacker is to prevent information from being
decoded at the receiver, signal annihilation suppresses the signal at the receiver by introducing
destructive interference. The attacker’s goal is to insert a signal which cancels out the
legitimate transmitter’s signal at the antenna of the receiver. This typically means that the
attacker will generate a signal identical to the legitimate transmission only with a different
polarity. Jamming attacks typically increase the energy on the channel and thus are more
easily detected than signal annihilation which reduces the energy typically below the threshold
of signal detection.
The goal of overshadowing is similar to jamming and signal annihilation in the sense that the
attacker aims to prevent the receiver from decoding a legitimate signal. However, instead
of interfering with the signal by adding excessive noise to the channel or cancelling out the
signal (i.e., signal annihilation), the attacker emits their own signal at the same time and
overshadows the legitimate signal. As a result, the receiver only registers the adversarial
signal which is often orders of magnitude higher in amplitude than the legitimate signal.
Practical overshadowing attacks were shown to be effective against QPSK modulation [13]
and more recently against cellular LTE systems [14].
Malicious signal overshadowing can not only deceive the receiver into decoding different data
than intended, it can also be used to alter any physical properties the receiver may extract
during signal reception, such as angle of arrival or time of arrival. Overshadowing attacks have
been shown to be particularly effective against systems that rely on physical layer properties
including positioning and ranging systems.
3
PHYSICAL-LAYER IDENTIFICATION
[15]
Physical-Layer Identiﬁcation techniques enable the identiﬁcation of wireless devices by unique
characteristics of their analogue (radio) circuitry; this type of identiﬁcation is also referred to
as Radio Fingerprinting. More precisely, physical-layer device identiﬁcation is the process of
ﬁngerprinting the analogue circuitry of a device by analysing the device’s communication at
the physical layer for the purpose of identifying a device or a class of devices. This type of
identiﬁcation is possible due to hardware imperfections in the analogue circuitry introduced at
the manufacturing process. These imperfections are remotely measurable as they appear in
the transmitted signals. While more precise manufacturing and quality control could minimise
such artefacts, it is often impractical due to signiﬁcantly higher production costs.
KA Physical Layer & Telecommunications Security | July 2021
Page 10
## Page 12

Physical-layer device identiﬁcation systems aim at identifying (or verifying the identity of)
devices or their afﬁliation classes, such as their manufacturer. Such systems can be viewed
as pattern recognition systems typically composed of: an acquisition setup to acquire signals
from devices under identiﬁcation, also referred to as identiﬁcation signals, a feature extraction
module to obtain identiﬁcation-relevant information from the acquired signals, also referred
to as ﬁngerprints, and a ﬁngerprint matcher for comparing ﬁngerprints and notifying the
application system requesting the identiﬁcation of the comparison results. Typically, there are
two modules in an identiﬁcation system: one for enrollment and one for identiﬁcation. During
enrollment, signals are captured either from each device or each (set of) class-representative
device(s) considered by the application system. Fingerprints obtained from the feature
extraction module are then stored in a database (each ﬁngerprint may be linked with some form
of unique ID representing the associated device or class). During identiﬁcation, ﬁngerprints
obtained from the devices under identiﬁcation are compared with reference ﬁngerprints stored
during enrollment. The task of the identiﬁcation module can be twofold: either recognise
(identify) a device or its afﬁliation class from among many enrolled devices or classes (1:N
comparisons), or verify that a device identity or class matches a claimed identity or class (1:1
comparison).
The identiﬁcation module uses statistical methods to perform the matching of the ﬁngerprints.
These methods are classiﬁers trained with Machine Learning techniques during the enrollment
phase. If the module has to verify a 1:1 comparison, the classiﬁer is referred to as binary. It
tries to verify a newly acquired signal against a stored reference pattern established during
enrollment. If the classiﬁer performs a 1:N comparison, on the other hand, it attempts to ﬁnd
the reference pattern in a data base which best matches with the acquired signal. Often, these
classiﬁers are designed to return a list of candidates ranked according to a similarity metric
or likelihood that denotes the conﬁdence for a match.
3.1
Device under Identiﬁcation
Physical-layer device identiﬁcation is based on ﬁngerprinting the analogue circuitry of devices
by observing their radio communication. Consequently, any device that uses radio communi-
cation may be subject to physical-layer identiﬁcation. So far, it has been shown that a number
of devices (or classes of devices) can be identiﬁed using physical-layer identiﬁcation. These
include analogue VHF, Bluetooth, WiFi, RFID and other radio transmitters.
Although what enables a device or a class of devices to be uniquely identiﬁed among other
devices or classes of devices is known to be due to imperfections introduced at the manufac-
turing phase of the analogue circuitry, the actual device’s components causing these have
not always been clearly identiﬁed in all systems. For example, VHF identiﬁcation systems are
based on the uniqueness of transmitters’ frequency synthesisers (local oscillators), while in
RFID systems some studies only suggested that the proposed identiﬁcation system may rely
on imperfections caused by the RFID device’s antennas and charge pumps. Identifying the
exact components may become more difﬁcult when considering relatively-complex devices. In
these cases, it is common to identify in the whole analogue circuitry, or in a speciﬁc sub-circuit,
the cause of imperfections. For example, IEEE 802.11 transceivers were identiﬁed considering
modulation-related features; the cause of hardware artefacts can be then located in the mod-
ulator subcircuit of the transceivers. Knowing the components that make devices uniquely
identiﬁable may have relevant implications for both attacks and applications, which makes
the investigation of such components an important open problem and research direction.
KA Physical Layer & Telecommunications Security | July 2021
Page 11
## Page 14

5. Robustness. Fingerprints should not be subject, or at least, they should be evaluated with
respect to external environmental aspects that directly inﬂuence the collected signal like
radio interference due to other radio signals, surrounding materials, signal reﬂections,
absorption, etc., as well as positioning aspects like the distance and orientation between
the devices under identiﬁcation and the identiﬁcation system. Furthermore, ﬁngerprints
should be robust to device-related aspects like temperature, voltage level, and power
level. Many types of robustness can be acceptable for a practical identiﬁcation system.
Generally, obtaining robust features helps in building more reliable identiﬁcation systems.
6. Data-Dependency. Fingerprints can be obtained from features extracted from a speciﬁc
bit pattern (data-related part of the identiﬁcation signal) transmitted by a device under
identiﬁcation (e.g., the claimed ID sent in a packet frame). This dependency has partic-
ularly interesting implications if the ﬁngerprints can be associated with both devices
and data transmitted by those devices. This might strengthen authentication and help
prevent replay attacks.
3.4
Attacks on Physical Layer Identiﬁcation
The large majority of research works have focused on exploring feature extraction and match-
ing techniques for physical-layer device identiﬁcation. Only recently the security of these
techniques started being addressed. Different studies showed that their identiﬁcation system
may be vulnerable to hill-climbing attacks if the set of signals used for building the device
ﬁngerprint is not carefully chosen. This attack consists of repeatedly sending signals to the
device identiﬁcation system with modiﬁcations that gradually improve the similarity score be-
tween these signals and a target genuine signal. They also demonstrated that transient-based
approaches could easily be disabled by jamming the transient part of the signal while still
enabling reliable communication. Furthermore, impersonation attacks on modulation-based
identiﬁcation techniques were developed and showed that low-cost software-deﬁned radios
as well as high end signal generators could be used to reproduce modulation features and
impersonate a target device with a success rate of 50-75%. Modulation-based techniques are
vulnerable to impersonation with high accuracy, while transient-based techniques are likely to
be compromised only from the location of the target device. The authors pointed out that this
is mostly due to presence of wireless channel effects in the considered device ﬁngerprints;
therefore, the channel needed to be taken into consideration for successful impersonation.
Generally, these attacks can be divided into two groups: signal re(P)lay and feature replay
attacks. In a signal replay attack, the attacker’s goal is to observe analogue identiﬁcation
signals of a target device, capture them in a digital form (digital sampling), and then transmit
(replay) these signals towards the identiﬁcation system by some appropriate means. The
attacker does not modify the captured identiﬁcation signals, that is, the analogue signal and
the data payload are preserved. This attack is similar to message replay in the Dolev-Yao
model in which an attacker can observe and manipulate information currently in the air at
will. Unlike in signal replay attacks, where the goal of the attack is to reproduce the captured
identiﬁcation signals in their entirety, feature replay attack creates, modiﬁes or composes
identiﬁcation signals that reproduce only the features considered by the identiﬁcation system.
The analogue representation of the forged signals may be different, but the features should
be the same (or at the least very similar).
KA Physical Layer & Telecommunications Security | July 2021
Page 13
## Page 16

4.2
Distance Measurement Techniques
Establishing proximity requires estimating the physical distance between two or more wire-
less entities. Typically, the distance is estimated either by observing the changes in the
signal’s physical properties (e.g., amplitude, phase) that occur as the signal propagates or by
estimating the time taken for the signal to travel between the entities.
A radio signal experiences a loss in its signal strength as it travels through the medium. The
amount of loss or attenuation in the signal’s strength is proportional to the square of the
distance travelled. The distance between the transmitter and the receiver can therefore be
calculated based on the free space path loss equation. In reality, the signal experiences
additional losses due to its interaction with the objects in the environment which are difﬁcult
to account for accurately. This directly affects the accuracy of the computed distance and
therefore advanced models such as the Rayleigh fading and log-distance path loss models
are typically used to improve the distance estimation accuracy. Bluetooth-based proximity
sensing tags (e.g., Apple iBeacon and passive keyless entry and Start Systems) use the
strength of the received Bluetooth signal also referred to as the Received Signal Strength
Indicator (RSSI) value as a measure of proximity.
Alternatively, the devices can measure the distance between them by estimating the phase
difference between a received continuous wave signal and a local reference signal. The
need for keeping track of the number of whole cycles elapsed is eliminated by using signals
of different frequencies typically referred to as multi-carrier phase-based ranging. Due to
their low complexity and low power consumption, phase based ranging is used in several
commercial products.
Finally, the time taken for the radio waves to travel from one point to another can be used
to measure the distance between the devices. In RF-based RTT based distance estimation
the distance d between two entities is given by d = (trx −ttx) × c, where c is the speed of
light, ttx and trx represent the time of transmission and reception respectively. The mea-
sured time-of-ﬂight can either be one way time-of-ﬂight or a round-trip time-of-ﬂight. One way
time-of-ﬂight measurement requires the clocks of the measuring entities to be tightly synchro-
nised. The errors due to mismatched clocks are compensated in the round-trip time-of-ﬂight
measurement.
The precise distance measurement largely depends on the system’s ability to estimate the
time of arrival and the physical characteristics of the radio frequency signal itself. The ranging
precision is roughly proportional to the bandwidth of the ranging signal. Depending on the
required level of accuracy, time-of-ﬂight based distance measurement systems use either
Impulse-Radio Ultra Wideband (IR-UWB) or Chirp-Spread Spectrum (CSS) signals. IR-UWB
systems provide centimeter-level precision while the precision of CSS systems is of the order
of 1–2m. There are a number of commercially available wireless systems that use chirp and
UWB round-trip time-of-ﬂight for distance measurement today.
KA Physical Layer & Telecommunications Security | July 2021
Page 15
## Page 17

4.3
Physical Layer Attacks on Secure Distance Measurement
With the increasing availability of low-cost software-deﬁned radio systems, an attacker can
eavesdrop, modify, compose, and (re)play radio signals with ease. This means that the
attacker has full control of the wireless communication channel and therefore is capable of
manipulating all messages transmitted between the two entities. In RSSI-based distance
estimation, an attacker can manipulate the measured distance by manipulating the received
signal strength at the veriﬁer. The attacker can simply amplify the signal transmitted by the
prover before relaying it to the veriﬁer. This will result in an incorrect distance estimation at
the veriﬁer. Commercially available solutions claim to secure against relay attacks by simply
reducing or attenuating the power of the transmitted signal. However, an attacker can trivially
circumvent such countermeasures by using higher gain ampliﬁers and receiving antennas.
Similarly, an attacker can also manipulate the estimated distance between the veriﬁer and the
prover in systems that use the phase or frequency property of the radio signal. For instance, the
attacker can exploit the maximum measurable property of phase or frequency-based distance
measurement systems and execute distance reduction attacks. The maximum measurable
distance, i.e., the largest value of distance dmax that can be estimated using a phase-based
proximity system, directly depends on the maximum measurable phase. Given that the phase
value ranges from 0 to 2π and then rolls over, the maximum measurable distance also rolls
over after a certain value. An attacker can leverage this maximum measurable distance
property of the system in order to execute the distance decreasing relay attack. During the
attack, the attacker simply relays (ampliﬁes and forwards) the veriﬁer’s interrogating signal
to the prover. The prover determines the phase of the interrogating signal and re-transmits
a response signal that is phase-locked with the veriﬁer’s interrogating signal. The attacker
then receives the prover’s response signal and forwards it to the veriﬁer, however with a time
delay. The attacker chooses the time delay such that the measured phase differences reaches
its maximum value of 2 and rolls over. In other words, the attacker was able to prove to the
veriﬁer that the prover is in close proximity (e.g., 1m away) even though the prover was far
from the veriﬁer.
In Time of Flight (ToF) based ranging systems, the distance is estimated based on the time
elapsed between the veriﬁer transmitting a ranging packet and receiving an acknowledgement
back from the prover. In order to reduce the distance measured, an attacker must decrease
the signal’s round trip time of ﬂight. Based on the implementation, an attacker can reduce the
estimated distance in a time-of-ﬂight based ranging system in more than one way. Given that
the radio signals travel at a speed of light, a 1 ns decrease in the time estimate can result in a
distance reduction of 30cm.
The ﬁrst type of attack on time-of-ﬂight ranging leverages the predictable nature of the data
contained in the ranging and the acknowledgement packets. A number of time-of-ﬂight ranging
systems use pre-deﬁned data packets for ranging, making it trivial for an attacker to predict
and generate their own ranging or acknowledgment signal. An attacker can transmit the
acknowledgment packet even before receiving the challenge ranging packet. Several works
have shown that the de-facto standard for IR-UWB, IEEE 802.15.4a does not automatically
provide security against distance decreasing attacks. In [25] it was shown that an attacker
can potentially decrease the measured distance by as much as 140 meters by predicting the
preamble and payload data with more than 99% accuracy even before receiving the entire
symbol. In a ’Cicada’ attack, the attacker continuously transmits a pulse with a power greater
than that of the prover. This degrades the performance of energy detection based receivers,
resulting in reduction of the distance measurements. In order to prevent such attacks it is
KA Physical Layer & Telecommunications Security | July 2021
Page 16
## Page 18

important to avoid predeﬁned or ﬁxed data during the time critical phase of the distance
estimation scheme.
In addition to having the response packet dependent on the challenge signal, the way in
which these challenge and response data are encoded in the radio signals affects the security
guarantees provided by the ranging or localisation system. An attacker can predict the bit
(early detect) even before receiving the symbol completely. Furthermore, the attacker can
leverage the robustness property of modern receivers and transmit arbitrary signal until the
correct symbol is predicted. Once the bit is predicted (e.g., early-detection), the attacker stops
transmitting the arbitrary signal and switches to transmitting the bit corresponding to the
predicted symbol, i.e., the attacker ’commits’ to the predicted symbol, commonly known as
late commit. In such a scenario, the attacker needn’t wait for the entire series of pulses to be
received before detecting the data being transmitted. After just a time period, the attacker
would be able to correctly predict the symbol.
As described previously, round-trip time-of-ﬂight systems are implemented either using chirp or
impulse radio ultrawideband signals. Due to their long symbol lengths, both implementations
have been shown to be vulnerable to early-detect and late-commit attacks. In the case of
chirp-based systems, an attacker can decrease the distance by more than 160 m and in some
scenarios even up to 700 m. Although IR-UWB pulses are of short duration (typically 2–3 ns
long), data symbols are typically composed of a series of UWB pulses. Furthermore, IEEE
802.15.4a IR-UWB standard allows long symbol lengths ranging from 32 ns to as large as 8µs.
Therefore, even the smallest symbol length of 32 ns allows an attacker to reduce the distance
by as much as 10 m by performing early-detect and late-commit attacks. Thus, it is clear that
in order to guarantee proximity and secure a wireless proximity system against early detect
and late-commit attacks, it is necessary to keep the symbol length as short as possible.
Design of a physical layer for secure distance measurement remains an open topic. However,
research so far has yielded some guiding principles for its design. Only radio RTT with single-
pulse or multi-pulse UWB modulation has been shown to be secure against physical layer
attacks. As a result, the IEEE 802.15.4z working group started the standardization of a new
physical layer for UWB secure distance measurement.
The ﬁrst attempt at formalizing the requirements for secure distance measurement based on
the Time of Arrival (ToA) of transmitted messages can be found in [23]. Said work presents
a formal deﬁnition of Message Time of Arrival Codes (MTACs), the core primitive in the
construction of systems for secure ToA measurement. If implemented correctly, MTACs
provide the ability to withstand reduction and enlargement attacks on distance measurements.
It is shown that systems based on UWB modulation can be implemented such that the stated
security requirements are met and therefore constitute examples of MTAC schemes.
4.4
Secure Positioning
Secure positioning systems allow positioning anchors (also called veriﬁers) to compute the
correct position of a node (also called the prover) or allow the prover to determine its own
position correctly despite manipulations by the attacker. This means that the attacker cannot
convince the veriﬁers or the prover that the prover is at a position that is different from its
true position. This is also called spooﬁng-resilience. A related property is the one of secure
position veriﬁcation which means that the veriﬁers can verify the position of an untrusted
prover. It is generally assumed that the veriﬁers are trusted. No restrictions are posed on the
attacker as it fully controls the communication channel between the provers and the veriﬁers.
KA Physical Layer & Telecommunications Security | July 2021
Page 17
## Page 19

V3
V1
V2
x
y
P
P
Figure 2: If the computed location of the prover is in the veriﬁcation triangle, the veriﬁers
conclude that this is a correct location. To spoof the position of prover inside the triangle, the
attacker would need to reduce at least one of the distance bounds.
The analysis of broadcast positioning techniques, such as GNSS has shown that such tech-
niques are vulnerable to spooﬁng if the attacker controls the signals at the antenna of the
GNSS receiver.
These type of approaches have been proposed to address this issue: Veriﬁable Multilateration
and Secure Positioning based on Hidden Stations.
Veriﬁable Multilateration relies on secure distance measurement / distance bounding. It
consists of distance bound measurements to the prover from at least three veriﬁers (in 2D)
and four veriﬁers (in 3D) and of subsequent computations performed by the veriﬁers or
by a central system. Veriﬁable Multilateration has been proposed to address both secure
positioning and position veriﬁcation. In the case of secure positioning, the prover is trusted
and maﬁa-fraud-resilient distance bounding is run between the prover and each of the veriﬁers.
The veriﬁers form veriﬁcation triangles / triangular pyramids (in 3D) and verify the position of
the prover within the triangle / pyramid. For the attacker to spoof a prover from position P to
P’ within a triangle/pyramid, the attacker would need to reduce at least one of the distance
bounds that are measured to P. This follows from the geometry of the triangle/pyramid. Since
Distance bounding prevents distance reduction attacks, Veriﬁable Multilateration prevents
spooﬁng attacks within the triangle/pyramid. The attacker can only spoof P to P’ that is
outside of the triangle/pyramid, causing the prover and the veriﬁers to reject the computed
position. Namely, the veriﬁers and the prover only accept the positions that are within the area
of coverage, deﬁned as the area covered by the veriﬁcation triangles/pyramids. Given this,
when the prover is trusted, Veriﬁable Multilateration is resilient to all forms of spooﬁng by the
attacker. Additional care needs to be given to the management of errors and the computation
of the position when distance measurement errors are taken into account.
When used for position veriﬁcation, Veriﬁable Multilateration is run with an untrusted prover.
Each veriﬁer runs a distance-fraud resilient distance bounding protocol with the prover. Based
on the obtained distance bounds, the veriﬁers compute the provers’ position. If this position
(within some distance and position error bounds) falls within the veriﬁcation triangle/pyramid,
the veriﬁers accept it as valid. Given that the prover is untrusted, it can enlarge any of the mea-
sured distances, but cannot reduce them since this is prevented by the use of distance bound-
ing protocols. Like in the case of secure positioning, the geometry of the triangle/pyramid then
prevents the prover from claiming a false position. Unlike in the case of secure positioning,
position veriﬁcation is vulnerable to cloning attacks, in which the prover shares its key to its
clones. These clones can then be strategically placed to the veriﬁers and fake any position
by enlarging distances to each individual veriﬁer. This attack can be possibly addressed by
tamper resistant hardware or device ﬁngerprinting.
Another approach to secure positioning and position veriﬁcation is to prevent the attacker
KA Physical Layer & Telecommunications Security | July 2021
Page 18
## Page 21

reﬂections from different objects in the vicinity of computer screens, such as spoons, bottles
and user’s retina were used to infer information show on a display.
The increasing availability of phones that integrate high quality sensors, such as cameras,
microphones and accelerometers makes it easier to mount successful attacks since no
dedicated sensor equipment needs to be covertly put in place.
To avoid unwanted signal emissions, devices can be held at a distance, can be shielded and
signals that are transmitted should be ﬁltered in order to remove high-frequency components
that might reﬂect switching activity in the circuitry. Moreover, it is generally advised to place a
return wire close to the transmission wire in order to avoid exploitation of the return current.
In general, wires and communication systems bearing conﬁdential information should be
separated (air-gapped) from non-conﬁdential systems.
5.2
Sensor Compromise
Analogue sensors have been shown to be particularly vulnerable to spooﬁng attacks. Similar to
compromising emanations, sensor spooﬁng depends on the type of the physical phenomena
the sensor captures. It can be acoustic, optical, thermal, mechanic or electromagnetic.
Nowadays, many electronic devices, including self-driving cars, medical devices and closed-
loop control systems, feature analogue sensors that help observe the environment and make
decisions in a fully autonomous way. These systems are equipped with sophisticated pro-
tection mechanisms to prevent unauthorised access or compromise via the device’s com-
munication interfaces, such as encryption, authentication and access control. Unfortunately,
when it comes to data gathered by sensors, the same level of protection is often not available
or difﬁcult to achieve since adversarial interactions with a sensor can be hard to model and
predict. As a result, unintentional and especially intentional EMI targeted at analogue sensors
can pose a realistic threat to any system that relies on readings obtained from an affected
sensor.
EMI has been used to manipulate the output of medical devices as well as to compromise
ultrasonic ranging systems. Research has shown that consumer electronic devices equipped
with microphones are especially vulnerable to the injection of fabricated audio signals [31].
Ultrasonic signals were used to inject silent voice commands, and acoustic waves were used
to affect the output of MEMS accelerometers. Accelerometers and intertial systems based
on MEMS are, for instance, used extensively in (consumer-grade) drones and multi-copters.
Undoubtedly, sensor spooﬁng attacks have gained a lot of attention and will likely impact
many future cyber-physical devices. System designers therefore have to take great care
and protect analogue sensors from adversarial input as an attacker might trigger a critical
decision on the application layer of such a device by exposing it to intentional EMI. Potential
defence strategies include, for example, (analogue) shielding of the devices, measuring signal
contamination using various metrics, or accommodating dedicated EMI monitors to detect
and ﬂag suspicious sensor readings.
A promising strategy that follows the approach of quantifying signal contamination to detect
EMI sensor spooﬁng is presented in [34]. The sensor output can be turned on and off according
to a pattern unknown to the attacker. Adversarial EMI in the wires between sensor and the
circuitry converting the reading to a digital value, i.e., the ADC, can be detected during the
times the sensor is off since the sensor output should be at a known level. In case there
are ﬂuctuations in the readings, an attack is detected. Such an approach is thought to
KA Physical Layer & Telecommunications Security | July 2021
Page 20
## Page 22

be especially effective when used to protect powered or non-powered passive sensors. It
has been demonstrated to successfully thwart EMI attacks against a microphone and a
temperature sensor system. The only modiﬁcation required is the addition of an electronic
switch that can be operated by the control unit or microcontroller to turn the sensor on and off.
A similar sensor spooﬁng detection scheme can be implemented for active sensors, such as
ultrasonic and infrared sensors, by incorporating a challenge-response like mechanism into
the measurement acquisition process [36]. An active sensor often has an emitting element
and a receiving element. The emitter releases a signal that is reﬂected and captured by the
receiver. Based on the properties of the received signal, the sensor can infer information about
the entity or the object that reﬂected the signal. The emitter can be turned off randomly and
during that time the receiver should not be able to register any incoming signal. Otherwise, an
attack is detected and the sensor reading is discarded.
6
PHYSICAL LAYER SECURITY OF SELECTED
COMMUNICATION TECHNOLOGIES
[37, 38, 39, 40]
This section presents security mechanisms of a selection of existing wireless communication
techniques that are in use today. The main focus is on physical-layer security constructs
as well as any lack thereof. The communication techniques that are discussed in detail are
near-ﬁeld communication, air trafﬁc communication networks, cellular networks and global
navigation satellite systems.
6.1
Near-ﬁeld communication (NFC)
Near-ﬁeld communication commonly refers to wireless communication protocols between
two small (portable) electronic devices. The standard is used for contact-less payment
and mobile payment systems in general. NFC-enabled devices can also exchange identity
information, such as keycards, for access control, and negotiate parameters to establish a
subsequent high-bandwidth wireless connection using more capable protocols.
NFC is designed to only transmit and receive data to a distance of up to a few centimeters.
Even if higher-layer cryptographic protocols are used, vanilla NFC protocols do not offer secure
communication and can not guarantee that two communicating devices are indeed only a
short distance apart. NFC is vulnerable to eavesdropping, man-in-the-middle attacks and
message relay attacks.
Even nowadays, standard NFC is deployed in security-critical contexts due to the assumption
that communicating devices are in close proximity. Research has shown, however, that this
assumption can not be veriﬁed reliably using NFC protocols. The distance can be made
almost arbitrarily large by relaying messages between NFC-enabled devices. The attack works
as follows: The benign NFC devices are made to believe that they are communicating with
each other, but they are actually exchanging data with a modiﬁed smartphone. An adversary
can strategically place a smartphone next to each benign NFC device while the smartphones
themselves use a communication method that can cover long distances, such as WiFi. They
simply forward the messages the benign devices are sending to each other. Such an attack is
also referred to as a wormhole attack where communicating parties are tricked into assuming
KA Physical Layer & Telecommunications Security | July 2021
Page 21
## Page 23

that they are closer than they actually are. This is a problem that cannot be solved using
techniques on the logical layer or on the data layer.
Obviously, most of the described attacks can be mitigated by shielding the NFC devices or
enhance the protocol with two-factor authentication, for example. Such mechanisms unfortu-
nately transfer security-relevant decisions to the user of an NFC system. Countermeasures
that do not impose user burden can roughly be categorised into physical layer methods and
the augmentation with context- or device-speciﬁc identiﬁers [37].
Protocol augmentation entails context-aware NFC devices that incorporate location infor-
mation into the NFC system to verify proximity. The location sensing can be implemented
with the help of a variety of different services, each with its own accuracy and granularity.
Conceivable are, for instance, GNSS/GPS based proximity veriﬁcation or leveraging the cell-ID
of the base station to which the NFC device is currently closest in order to infer a notion of
proximity.
Physical layer methods that have been suggested in research literature are timing restrictions
and distance bounding. Enforcing strict timing restraints on the protocol messages can be
understood as a crude form of distance bounding. As discussed in Section 4.1, distance
bounding determines an upper bound on the physical distance between two communicating
devices. While distance bounding is considered the most effective approach, it still remains to
be shown if secure distance bounding can be implemented in practice for small NFC-enabled
devices.
6.2
Air Trafﬁc Communication Networks
Throughout different ﬂight phases commercial and non-commercial aviation uses several
wireless communication technologies to exchange information with aviation authorities on
the ground as well as between airborne vehicles. Often legacy systems are still in use and
security has never been part of the design of such systems.
While new proposals suggest to overhaul these systems and to tightly integrate security
measures into the data layer, such as encryption and message authentication, air trafﬁc
communication networks are not only used for information transmission, but also to extract
physical layer features from the signal in order to perform aircraft location positioning.
A prominent example is ADS-B. An ADS-B transponder periodically (or when requested) broad-
casts the aircraft’s position information, such as coordinates, that have been obtained through
an on-board GNSS receiver. Most versions of ADS-B only support unauthenticated messages
and therefore, this technology suffers from active and passive attacks, i.e., eavesdropping,
modifying, injecting and jamming messages. It is, for instance, possible to prevent an aircraft’s
location from being tracked by Air Trafﬁc Control (ATC) by simply jamming the respective mes-
sages. Similarly, an adversary could create ghost planes by emitting fabricated transponder
messages. A sophisticated attacker could even fully distort the view ATC has on its airspace.
Multilateration (MLAT) can be seen as a technology that mitigates some of the shortcomings
of unauthenticated ADS-B and is therefore usually deployed in conjunction with ADS-B. MLAT
does not rely on the transmitted information encapsulated in the message, but makes use of
the physical and geometrical constellation between the transmitter (i.e., transponder of the air-
craft) and several receivers. MLAT systems extract physical layer properties from the received
messages. The time of arrival of a message is recorded at different co-located receivers and,
using the propagation speed of the signal, the location of the aircraft’s transponder can be
KA Physical Layer & Telecommunications Security | July 2021
Page 22
## Page 24

estimated. Multilateration techniques infer the aircraft’s location even if the contents of the
ADS-B messages are incorrect and thus MLAT provides a means to crosscheck the location
information disseminated by the aircraft’s transponder.
Although MLAT offers additional security based on physical layer properties, a distributed
adversary can still manipulate ADS-B messages. In addition to altering the location information,
an attacker can modify or inject signals that affect the time-of-arrival measurement at the
receivers. If the attacker has access to multiple distributed antennas and is able to coordinate
adversarial signal emission precisely, attacks similar to those on standard ADS-B are feasible.
However, the more receivers used to record the signals, the more difﬁcult such attacks
become. Unfortunately, MLAT is not always an effective solution in aviation as strategic
receiver placement is crucial and time of arrival calculations can be susceptible to multi-path
interference [38].
6.3
Cellular Networks
Cellular networks provide voice, data and messaging communication through a network of
base stations, each covering one or more cells. The security provisions of these networks are
mainly governed by the standards that were adopted in the GSM Association and later in the
Third Generation Partnership Plan (3GPP).
Second Generation (2G) ‘GSM’ networks were introduced during the 1990s, and restricted
their services to voice and text messaging. 2G networks were capable of carrying data via a
Circuit-Switched Data Service (CSD) which operated in a manner similar to the dial-up modems,
just over cellular networks. Further development of email and web services resulted in a need
for enhanced speeds and services
3GPP improved 2G GSM standard with packet switched data service, resulting in the general
packet radio service (GPRS). Like GSM, GPRS made use of the Home Location Register
(HLR), a component that was responsible for subscriber key management and authentication.
However, GPRS enhanced GSM by adding the Serving GPRS Support Node (SGSN) for data
trafﬁc routing and mobility management for better data trafﬁc delivery. Third Generation (3G)
of cellular networks, also known as Universal Mobile Telecommunications Systems (UMTS),
introduced a number of improvements over 2G networks, including security enhancements,
as well as increased uplink and downlink speeds and capacities. Fourth Generation (4G)
cellular networks, also known as Long Term Evolution (LTE) introduced further increase in
transmission speeds and capacities.
One of the main security properties that cellular networks aim to protect is the conﬁdentiality
of the communication of the link between the mobile station, and the base station and
correct billing. The security of cellular networks has evolved with network generations, but all
generations have the same overarching concept. Subscribers are identiﬁed via their (Universal)
subscriber identity modules their International Mobile Subscriber Identity (IMSI) number and
its related secret key. IMSI and the keys are used to authenticate subscribers as well as to
generate the necessary shared secrets to protect the communication to the cellular network.
2G security focused on the conﬁdentiality of the wireless link between the mobile station
and the base station. This was achieved through the authentication via a challenge-response
protocol, 2G Authentication and Key Agreement (AKA). This protocol is executed each time
when a mobile station initiates a billable operation. 2G AKA achieved authentication based on
a long term key Ki shared between the subscriber SIM card and the network. This key is used
KA Physical Layer & Telecommunications Security | July 2021
Page 23
## Page 25

by the network to authenticate the subscriber and to derive a session key Kc. This is done
within in a challenge response protocol, executed between the SGSN and the mobile station.
Before the execution of the protocol, SGSN receives from the HLR the Kc, a random value
RAND and an expected response XRES. Both Kc and XRES are generated within the HLR
based on RAND and Ki. When the mobile station attempts to authenticate to the network it
is sent RAND. To authenticate, the mobile station combines its long term key Ki (stored on
its SIM card) with the received RAND to generate RES and Kc. The mobile station sends
RES to the SGSN which compares it to XRES. If the two values match, the mobile station is
authenticated to the network. The SGSN then sends the Kc to the base station to which the
mobile station is connected in order to protect the mobile to base station wireless link.
2G AKA offered very limited protection. It used inadequate key size (56-64 bits), and weak
authentication and key generation algorithms (A3,A5 and A8) which were, once released,
broken, allowing for eavesdropping and message forgery. Furthermore, AKA was designed to
provide only one-way authentication of the mobile station to the network. Since the network
did not authenticate to the mobile stations this enabled attacks by fake base stations violating
users location privacy and conﬁdentiality of their communication.
In order to address the 2G security shortcomings, 3G networks introduced new 3G Authenti-
cation and Key Agreement (3G AKA) procedures. 3G AKA replaced the weak cryptographic
algorithms that were used in 2G and provided mutual authentication between the network
and the mobile stations. Like in 2G, the goal of the protocol is the authentication (now mu-
tual) of the network and the mobile station. The input into the protocol is a secret key K
shared between the HLR and the subscriber. The outcome of the protocol are two keys,
the encryption/conﬁdentiality key CK and the integrity key IK. The generation of two keys
allows the network and the mobile station to protect the integrity and conﬁdentiality of their
communication using two different keys, in line with common security practices. CK and IK
are each 128 bits long which is considered adequate.
The authentication and key derivation is performed as follows. The HLR ﬁrst generates the
random challenge RAND, from it the expected response XRES, the keys CK and IK and
the authentication token AUTN. It then sends these values to the SGSN. The SGSN sends the
RAND as well as the AUTN to the mobile station (also denoted as User Equipment (UE)),
which will then use its long term key K to generate the response RES and to verify if AUTN
was generated by the HLR. The AUTN is from the shared key and the counter maintained by
both the HLR and the mobile station. Upon receiving the RES from the mobile station, SGSN
will compare it with the XRES and if they match, will forward the CK and IK to the base
station. The base and mobile station can now use these keys to protect their communication.
3G, however, still didn’t resolve the vulnerabilities within the operator’s networks. CK and
IK are transmitted between different entities in the network. They are transmitted between
SGSN and the associated base station as well as between different base stations during
mobility. This allows network attackers to record these keys and therefore eavesdrop on
wireless connections.
4G (LTE) security architecture preserved many of the core elements of 2G and 3G networks,
but aimed to address the shortcomings of 3G in terms of the protection of the in-network
trafﬁc through the protection of network links and redistribution of different roles. For example,
the long term key storage was moved from the HLR to the Home Subscriber Server (HSS).
Mobility management was moved from the SGSN to the Mobility Management Engine (MME).
5G security architecture evolves 4G but follows a similar set of principles and entities. 5G
KA Physical Layer & Telecommunications Security | July 2021
Page 24
## Page 26

introduces a new versions of Authentication and Key Agreement (AKA) protocols that was
designed to ﬁx the issues found in 4G, however with mixed success [41].
6.4
GNSS Security and Spooﬁng Attacks
GNSS such as GPS and Galileo provide global navigation service through satellites that are
orbiting the earth approximately 20,000km above the ground. Satellites are equipped with
high-precision atomic clocks which allows the satellites to remain synchronised. Satellites
transmit navigation messages at central frequencies of 1575.42MHz (L1) and 1227.60MHz
(L2). direct sequence spreading is used to enable acquisition and to protect the signals
carrying those messages from spooﬁng and jamming attacks. Civilian codes are public
and therefore do not offer such protection, whereas military and special interest codes are
kept conﬁdential. Navigation messages carry data including satellite clock information, the
ephemeris (information related to the satellite orbit) and the almanac (the satellite orbital and
clock information). Satellite messages are broadcasted and the reception of messages from
four of more satellites will allow a receiver to calculate its position. This position calculation
is based on trilateration. The receiver measures the times of arrival of the satellite signals,
converts them into distances (pseudoranges), and then calculates its position as well as its
clock offset with respect to the satellite clocks.
A GPS signal spooﬁng attack is a physical-layer attack in which an attacker transmits specially
crafted radio signals that are identical to authentic satellite signals. Civilian GPS is easily
vulnerable to signal spooﬁng attacks. This is due to the lack of any signal authentication
and the publicly known spreading codes for each satellite, modulation schemes, and data
structure. In a signal spooﬁng attack, the objective of an attacker may be to force a target
receiver to (i) compute an incorrect position, (ii) compute an incorrect time or (iii) disrupt the
receiver. Due to the low power of the legitimate satellite signal at the receiver, the attacker’s
spooﬁng signals can trivially overshadow the authentic signals. In a spooﬁng attack, the GPS
receiver typically locks (acquires and tracks) onto the stronger, attacker’s signal, thus ignoring
the satellite signals.
An attacker can inﬂuence the receiver’s position and time estimate in two ways: (i) by manip-
ulating the contents of the navigation messages (e.g., the location of satellites, navigation
message transmission time) and/or (ii) by modifying the arrival time of the navigation mes-
sages. The attacker can manipulate the receiver time of arrival by temporally shifting the
navigation message signals while transmitting the spooﬁng signals. We can classify spooﬁng
attacks based on how synchronous (in time) and consistent (with respect to the contents
of the navigation messages) the spooﬁng signals are in comparison to the legitimate GPS
signals currently being received at the receiver’s true location.
Non-Coherent and Modiﬁed Message Contents: In this type of attack, the attacker’s signals
are both unsynchronised and contain different navigation message data in comparison to the
authentic signals. Attackers who use GPS signal generators to execute the spooﬁng attack
typically fall under this category. An attacker with a little know-how can execute a spooﬁng
attack using these simulators due to their low complexity, portability and ease of use. Some
advanced GPS signal generators are even capable of recording and replaying signals, however
not in real-time. In other words, the attacker uses the simulator to record at one particular time
in a given location and later replays it. Since they are replayed at a later time, the attacker’s
signals are not coherent and contain different navigation message data than the legitimate
signals currently being received.
KA Physical Layer & Telecommunications Security | July 2021
Page 25
## Page 27

Figure 3: Seamless takeover attack on GPS. The spooﬁng aligns its signal with the legitimate
signal and slowly increase the transmit power. Once receiver locks on to attacker’s signal, he
starts to manipulate it.
Non-Coherent but Unmodiﬁed Message Contents: In this type of attack, the navigation mes-
sage contents of the transmitted spooﬁng signals are identical to the legitimate GPS signals
currently being received. However, the attacker temporally shifts the spooﬁng signal thereby
manipulating the spooﬁng signal time of arrival at the target receiver. For example, attackers
capable of real-time recording and replaying of GPS signals fall under this category as they
will have the same navigation contents as that of the legitimate GPS signals, however shifted
in time. The location or time offset caused by such an attack on the target receiver depends
on the time delay introduced both by the attacker and due to the propagation time of the
relayed signal. The attacker can precompute these delays and successfully spoof a receiver
to a desired location.
Coherent but Modiﬁed Message Contents: The attacker generates spooﬁng signals that are
synchronised to the authentic GPS signals. However, the contents of the navigation messages
are not the same as that of the currently seen authentic signals. For instance, phase-coherent
signal synthesisers are capable of generating spooﬁng signals with the same code phase
as the legitimate GPS signal that the target receiver is currently locked on to. Additionally,
the attacker modiﬁes the contents of the navigation message in real-time (and with minimal
delay) and replays it to the target receiver. A variety of commercial GPS receivers were shown
to be vulnerable to this attack and in some cases, it even caused permanent damage to the
receivers.
Coherent and Unmodiﬁed Message Contents: Here, the attacker does not modify the contents
of the navigation message and is completely synchronised to the authentic GPS signals. Even
though the receiver locks on to the attacker’s spooﬁng signals (due to the higher power), there
is no change in the location or time computed by the target receiver. Therefore, this is not an
attack in itself but is an important ﬁrst step in executing the seamless takeover attack.
The seamless takeover attack is considered one of the strongest attacks in literature. In a
majority of applications, the target receiver is already locked on to the legitimate GPS satellite
signals. The main steps are highlighted in Figure 3. The goal of an attacker is to force the
receiver to stop tracking the authentic GPS signals and lock onto the spooﬁng signals without
causing any signal disruption or data loss. This is because the target receiver can potentially
detect the attack based on the abrupt loss of GPS signal. In a seamless takeover attack, ﬁrst,
the attacker transmits spooﬁng signals that are synchronised with the legitimate satellite
signals and are at a power level lower than the received satellite signals. The receiver is still
locked on to the legitimate satellite signals due to the higher power and hence there is no
change in the ships route. The attacker then gradually increases the power of the spooﬁng
signals until the target receiver stops tracking the authentic signal and locks on to the spooﬁng
KA Physical Layer & Telecommunications Security | July 2021
Page 26
## Page 28

signals. Note that during this takeover, the receiver does not see any loss of lock, in other
words, the takeover was seamless. Even though the target receiver is now locked on to
the attacker, there is still no change in the route as the spooﬁng signals are both coherent
with the legitimate satellite signals as well as there is no modiﬁcation to the contents of the
navigation message itself. Now, the attacker begins to manipulate the spooﬁng signal such
that the receiver computes a false location and begins to alter its course. The attacker can
either slowly introduce a temporal shift from the legitimate signals or directly manipulate the
navigation message contents to slowly deviate the course of the ship to a hostile destination.
If an attacker controls all the signals that arrive at the receiver’s antenna(s) the receiver
cannot detect spooﬁng. However, if the attack is remote, and the attacker cannot fully control
the signals at the receiver, anomaly detection techniques can be used to detect spooﬁng.
In particular, Automatic Gain Control (AGC) values, Received Signal Strength (RSS) from
individual satellites, carrier phase values, estimated noise ﬂoor levels, number of visible
satellites all can be used to detect spooﬁng. Particularly interesting are techniques based
on tracking and analysis of autocorrelation peaks that are used for the detection of GNSS
signals. Distortion, the number and the behaviour over time of these peaks can be used to
detect even the most sophisticated seamless takeover attacks.
The detection of GNSS spooﬁng can be improved if spooﬁng signals are simultaneously
received by several receivers. This can be used for the detection of spooﬁng as well as for
spoofer localisation. If the receivers know their mutual distances (e.g., are placed at ﬁxed
distances), the spoofer needs to preserve those distances when performing the spooﬁng
attack. When a single spoofer broadcasts its signals, it will result in all receivers being spoofed
to the same position, therefore enabling detection. This basic detection technique can be
generalised to several receivers, allowing even the detection of distributed spoofers.
Finally, GNSS spooﬁng can be made harder through the authentication and hiding of GNSS
signals. Although currently civilian GNSS systems do not support authentication, digital
signatures as well as hash-based signatures such as TESLA can be added to prevent the
attacker from generating GNSS signals. This would, however, not prevent all spooﬁng attacks
since the attacker can still selectively delay navigation messages and therefore modify the
computed position. This attack can be prevented by the use of spreading with delayed key
disclosure. Even this approach still does not fully prevent against spooﬁng by broadband
receivers that are able to relay full GNSS frequency band between locations.
Military GPS signals are authenticated, and try to achieve low-probability of intercept as well
as jamming resilience via the use of secret spreading codes. This approach prevents some
of the spooﬁng attacks, but still fails to fully prevent record-and-relay attacks. In addition,
this approach does not scale well since secret spreading codes need to be distributed to all
intended receivers, increasing the likelihood of their leakage and reducing usability.
In conclusion, although newly proposed and deployed countermeasures make it more difﬁcult
for the attacker to spoof GNS systems like GPS, currently no measure fully prevents spooﬁng
under strong attacker models. This is an area of active research.
KA Physical Layer & Telecommunications Security | July 2021
Page 27
## Page 31

REFERENCES
[1] R. Liu and W. Trappe, Securing Wireless Communications at the Physical Layer, 1st ed.
Springer Publishing Company, Incorporated, 2009.
[2] C. Ye, S. Mathur, A. Reznik, Y. Shah, W. Trappe, and N. B. Mandayam, “Information-
theoretically secret key generation for fading wireless channels,” IEEE Transactions on
Information Forensics and Security, vol. 5, no. 2, pp. 240–254, 2010.
[3] S. Eberz, M. Strohmeier, M. Wilhelm, and I. Martinovic, “A practical man-in-the-middle
attack on signal-based key generation protocols,” in Computer Security – ESORICS 2012,
S. Foresti, M. Yung, and F. Martinelli, Eds.
Berlin, Heidelberg: Springer Berlin Heidelberg,
2012, pp. 235–252.
[4] S. Gollakota, H. Hassanieh, B. Ransford, D. Katabi, and K. Fu, “They can hear your
heartbeats: Non-invasive security for implantable medical devices,” in Proceedings of
the ACM SIGCOMM 2011 Conference, ser. SIGCOMM ’11.
New York, NY, USA: ACM, 2011,
pp. 2–13.
[5] N. Anand, S.-J. Lee, and E. W. Knightly, “Strobe: Actively securing wireless communi-
cations using zero-forcing beamforming,” in 2012 Proceedings IEEE INFOCOM, March
2012, pp. 720–728.
[6] S. Čapkun, M. Čagalj, R. Rengaswamy, I. Tsigkogiannis, J.-P. Hubaux, and M. Srivastava,
“Integrity codes: Message integrity protection and authentication over insecure channels,”
IEEE Transactions on Dependable and Secure Computing, vol. 5, no. 4, pp. 208–223, Oct
2008.
[7] C. E. Shannon, “Communication theory of secrecy systems,” Bell system technical jour-
nal, vol. 28, no. 4, pp. 656–715, 1949.
[8] A. D. Wyner, “The wire-tap channel,” Bell system technical journal, vol. 54, no. 8, pp.
1355–1387, 1975.
[9] I. Csiszár and J. Korner, “Broadcast channels with conﬁdential messages,” IEEE transac-
tions on information theory, vol. 24, no. 3, pp. 339–348, 1978.
[10] M. Bloch, J. Barros, M. R. Rodrigues, and S. W. McLaughlin, “Wireless information-
theoretic security,” IEEE Transactions on Information Theory, vol. 54, no. 6, pp. 2515–
2534, 2008.
[11] D. Adamy, EW 101: a ﬁrst course in electronic warfare.
Artech House, 2001.
[12] C. Popper, “On secure wireless communication under adversarial interference,” PhD
thesis, ETH Zurich, 2011.
[13] C. Pöpper, N. O. Tippenhauer, B. Danev, and S. Čapkun, “Investigation of signal and
message manipulations on the wireless channel,” in Proceedings of the European Sym-
posium on Research in Computer Security, 2011.
[14] H. Yang, S. Bae, M. Son, H. Kim, S. M. Kim, and Y. Kim, “Hiding in plain signal:
Physical signal overshadowing attack on LTE,” in 28th USENIX Security Symposium
(USENIX Security 19).
Santa Clara, CA: USENIX Association, Aug. 2019, pp. 55–72.
[Online]. Available: https://www.usenix.org/conference/usenixsecurity19/presentation/
yang-hojoon
[15] B. Danev, D. Zanetti, and S. Capkun, “On physical-layer identiﬁcation of wireless devices,”
ACM Comput. Surv., vol. 45, no. 1, pp. 6:1–6:29, Dec. 2012.
[16] G. Avoine, M. A. Bingöl, I. Boureanu, S. čapkun, G. Hancke, S. Kardaş, C. H. Kim, C. Lau-
radoux, B. Martin, J. Munilla, A. Peinado, K. B. Rasmussen, D. Singelée, A. Tchamkerten,
R. Trujillo-Rasua, and S. Vaudenay, “Security of distance-bounding: A survey,” ACM
Comput. Surv., vol. 51, no. 5, pp. 94:1–94:33, Sep. 2018.
[17] T. Beth and Y. Desmedt, “Identiﬁcation tokens—or: Solving the chess grandmaster
KA Physical Layer & Telecommunications Security | July 2021
Page 30
## Page 32

problem,” in Conference on the Theory and Application of Cryptography.
Springer, 1990,
pp. 169–176.
[18] S. Brands and D. Chaum, “Distance-bounding protocols,” in Workshop on the Theory and
Application of of Cryptographic Techniques.
Springer, 1993, pp. 344–359.
[19] J. Clulow, G. P. Hancke, M. G. Kuhn, and T. Moore, “So near and yet so far: Distance-
bounding attacks in wireless networks,” in Security and Privacy in Ad-Hoc and Sensor
Networks, L. Buttyán, V. D. Gligor, and D. Westhoff, Eds.
Berlin, Heidelberg: Springer
Berlin Heidelberg, 2006, pp. 83–97.
[20] A. Ranganathan and S. Capkun, “Are we really close? Verifying proximity in wireless
systems,” IEEE Security & Privacy, vol. 15, no. 3, pp. 52–58, 2017.
[21] M. Singh, P. Leu, and S. Capkun, “UWB with pulse reordering: Securing ranging against
relay and physical layer attacks.” IACR Cryptology ePrint Archive, vol. 2017, p. 1240, 2017.
[22] S. Capkun and J.-P. Hubaux, “Secure positioning in wireless networks,” IEEE Journal on
Selected Areas in Communications, vol. 24, no. 2, pp. 221–232, Feb 2006.
[23] P. Leu, M. Singh, M. Roeschlin, K. G. Paterson, and S. Capkun, “Message time of arrival
codes: A fundamental primitive for secure distance measurement,” IEEE Symposium on
Security and Privacy, 2020.
[24] G. P. Hancke and M. G. Kuhn, “An RFID distance bounding protocol,” in First International
Conference on Security and Privacy for Emerging Areas in Communications Networks
(SECURECOMM’05).
IEEE, 2005, pp. 67–73.
[25] M. Poturalski, M. Flury, P. Papadimitratos, J.-P. Hubaux, and J.-Y. Le Boudec, “Distance
bounding with IEEE 802.15. 4a: Attacks and countermeasures,” IEEE Transactions on
Wireless Communications, vol. 10, no. 4, pp. 1334–1344, 2011.
[26] M. G. Kuhn and C. M. G. Kuhn, “Compromising emanations: Eavesdropping risks of
computer displays,” 2003.
[27] M. G. Kuhn, “Electromagnetic eavesdropping risks of ﬂat-panel displays,” in International
Workshop on Privacy Enhancing Technologies.
Springer, 2004, pp. 88–107.
[28] M. Backes, T. Chen, M. Duermuth, H. P. A. Lensch, and M. Welk, “Tempest in a teapot:
Compromising reﬂections revisited,” in 2009 30th IEEE Symposium on Security and
Privacy, May 2009, pp. 315–327.
[29] D. Genkin, A. Shamir, and E. Tromer, “RSA key extraction via low-bandwidth acoustic
cryptanalysis,” in Advances in Cryptology – CRYPTO 2014, J. A. Garay and R. Gennaro,
Eds.
Berlin, Heidelberg: Springer Berlin Heidelberg, 2014, pp. 444–461.
[30] P. Marquardt, A. Verma, H. Carter, and P. Traynor, “(sp)iPhone: decoding vibrations from
nearby keyboards using mobile phone accelerometers,” in Proceedings of the 18th ACM
conference on Computer and communications security.
ACM, 2011, pp. 551–562.
[31] D. F. Kune, J. Backes, S. S. Clark, D. Kramer, M. Reynolds, K. Fu, Y. Kim, and W. Xu,
“Ghost talk: Mitigating emi signal injection attacks against analog sensors,” in 2013 IEEE
Symposium on Security and Privacy, May 2013, pp. 145–159.
[32] G. Zhang, C. Yan, X. Ji, T. Zhang, T. Zhang, and W. Xu, “DolphinAttack: Inaudible voice
commands,” in Proceedings of the 2017 ACM SIGSAC Conference on Computer and Com-
munications Security.
ACM, 2017, pp. 103–117.
[33] T. Trippel, O. Weisse, W. Xu, P. Honeyman, and K. Fu, “WALNUT: Waging doubt on the
integrity of MEMS accelerometers with acoustic injection attacks,” in 2017 IEEE European
Symposium on Security and Privacy (EuroS&P), April 2017, pp. 3–18.
[34] Y. Zhang and K. Rasmussen, “Detection of electromagnetic interference attacks on
sensor systems,” in IEEE Symposium on Security and Privacy (S&P), May 2020.
[35] W. van Eck, “Electromagnetic radiation from video display units: An eavesdropping
risk?” Computers & Security, vol. 4, no. 4, pp. 269 – 286, 1985. [Online]. Available:
KA Physical Layer & Telecommunications Security | July 2021
Page 31
## Page 33

http://www.sciencedirect.com/science/article/pii/016740488590046X
[36] Y. Shoukry, P. Martin, Y. Yona, S. Diggavi, and M. Srivastava, “Pycra: Physical challenge-
response authentication for active sensors under spooﬁng attacks,” in Proceedings of
the 22nd ACM SIGSAC Conference on Computer and Communications Security.
ACM,
2015, pp. 1004–1015.
[37] L. Francis, G. P. Hancke, K. Mayes, and K. Markantonakis, “Practical relay attack on
contactless transactions by using NFC mobile phones.” IACR Cryptology ePrint Archive,
vol. 2011, p. 618, 2011.
[38] M. Strohmeier, “Security in next generation air trafﬁc communication networks,” Ph.D.
dissertation, University of Oxford, 2016.
[39] D. Forsberg, G. Horn, W.-D. Moeller, and V. Niemi, LTE Security, 2nd ed.
Wiley Publishing,
2012.
[40] A. Ranganathan, “Physical-layer techniques for secure proximity veriﬁcation and local-
ization,” PhD thesis, ETH Zurich, 2016.
[41] D. Basin, J. Dreier, L. Hirschi, S. Radomirovic, R. Sasse, and V. Stettler, “A formal
analysis of 5G authentication,” in Proceedings of the 2018 ACM SIGSAC Conference on
Computer and Communications Security, ser. CCS ’18.
New York, NY, USA: ACM, 2018,
pp. 1383–1396. [Online]. Available: http://doi.acm.org/10.1145/3243734.3243846
[42] S. N. Premnath, S. Jana, J. Croft, P. L. Gowda, M. Clark, S. K. Kasera, N. Patwari, and S. V.
Krishnamurthy, “Secret key extraction from wireless signal strength in real environments,”
IEEE Transactions on mobile Computing, vol. 12, no. 5, pp. 917–930, 2012.
[43] S. Mathur, R. Miller, A. Varshavsky, W. Trappe, and N. Mandayam, “Proximate: proximity-
based secure pairing using ambient wireless signals,” in Proceedings of the 9th inter-
national conference on Mobile systems, applications, and services.
ACM, 2011, pp.
211–224.
[44] J. Zhang, T. Q. Duong, A. Marshall, and R. Woods, “Key generation from wireless channels:
A review,” Ieee access, vol. 4, pp. 614–626, 2016.
[45] J. Zhang, A. Marshall, R. Woods, and T. Q. Duong, “Efﬁcient key generation by exploiting
randomness from channel responses of individual OFDM subcarriers,” IEEE Transactions
on Communications, vol. 64, no. 6, pp. 2578–2588, 2016.
[46] B. Azimi-Sadjadi, A. Kiayias, A. Mercado, and B. Yener, “Robust key generation from
signal envelopes in wireless networks,” in Proceedings of the 14th ACM conference on
Computer and communications security.
ACM, 2007, pp. 401–410.
[47] M. Strasser, C. Popper, S. Capkun, and M. Cagalj, “Jamming-resistant key establish-
ment using uncoordinated frequency hopping,” in 2008 IEEE Symposium on Security
and Privacy (sp 2008).
IEEE, 2008, pp. 64–78.
[48] D. W. K. Ng, E. S. Lo, and R. Schober, “Robust beamforming for secure communication in
systems with wireless information and power transfer,” IEEE Transactions on Wireless
Communications, vol. 13, no. 8, pp. 4599–4615, 2014.
[49] Y. Zheng, M. Schulz, W. Lou, Y. T. Hou, and M. Hollick, “Proﬁling the strength of physical-
layer security: A study in orthogonal blinding,” in Proceedings of the 9th ACM Conference
on Security & Privacy in Wireless and Mobile Networks.
ACM, 2016, pp. 21–30.
[50] M. Schulz, A. Loch, and M. Hollick, “Practical known-plaintext attacks against physical
layer security in wireless mimo systems.” in The Network and Distributed System Security
Symposium (NDSS), 2014.
[51] P. Robyns, P. Quax, and W. Lamotte, “PHY-layer security is no alternative to cryptography,”
in Proceedings of the 10th ACM Conference on Security and Privacy in Wireless and
Mobile Networks.
ACM, 2017, pp. 160–162.
[52] H. Mahdavifar and A. Vardy, “Achieving the secrecy capacity of wiretap channels using
KA Physical Layer & Telecommunications Security | July 2021
Page 32
## Page 34

polar codes,” arXiv preprint arXiv:1007.3568, 2010.
[53] P. Parada and R. Blahut, “Secrecy capacity of SIMO and slow fading channels,” in Pro-
ceedings. International Symposium on Information Theory, 2005. ISIT 2005. IEEE, 2005,
pp. 2152–2155.
[54] A. Mukherjee, S. A. A. Fakoorian, J. Huang, and A. L. Swindlehurst, “Principles of physical
layer security in multiuser wireless networks: A survey,” IEEE Communications Surveys
& Tutorials, vol. 16, no. 3, pp. 1550–1573, 2014.
[55] P. K. Gopala, L. Lai, and H. El Gamal, “On the secrecy capacity of fading channels,” in
2007 IEEE International Symposium on Information Theory.
IEEE, 2007, pp. 1306–1310.
[56] W. Shen, P. Ning, X. He, and H. Dai, “Ally friendly jamming: How to jam your enemy and
maintain your own wireless connectivity at the same time,” in 2013 IEEE Symposium on
Security and Privacy.
IEEE, 2013, pp. 174–188.
[57] D. S. Berger, F. Gringoli, N. Facchi, I. Martinovic, and J. Schmitt, “Gaining insight on
friendly jamming in a real-world IEEE 802.11 network,” in Proceedings of the 2014 ACM
conference on Security and privacy in wireless & mobile networks.
ACM, 2014, pp.
105–116.
[58] J. P. Vilela, M. Bloch, J. Barros, and S. W. McLaughlin, “Wireless secrecy regions with
friendly jamming,” IEEE Transactions on Information Forensics and Security, vol. 6, no. 2,
pp. 256–266, 2011.
[59] N. O. Tippenhauer, L. Malisa, A. Ranganathan, and S. Capkun, “On limitations of friendly
jamming for conﬁdentiality,” in 2013 IEEE Symposium on Security and Privacy.
IEEE,
2013, pp. 160–173.
[60] S. Capkun, M. Cagalj, G. Karame, and N. O. Tippenhauer, “Integrity regions: Authentication
through presence in wireless networks,” IEEE Transactions on Mobile Computing, vol. 9,
no. 11, pp. 1608–1621, 2010.
[61] A. Polydoros and K. Woo, “LPI detection of frequency-hopping signals using autocor-
relation techniques,” IEEE journal on selected areas in communications, vol. 3, no. 5, pp.
714–726, 1985.
[62] R. F. Mills and G. E. Prescott, “Waveform design and analysis of frequency hopping LPI
networks,” in Proceedings of MILCOM’95, vol. 2.
IEEE, 1995, pp. 778–782.
[63] L. Frikha, Z. Trabelsi, and W. El-Hajj, “Implementation of a covert channel in the 802.11
header,” in 2008 International Wireless Communications and Mobile Computing Confer-
ence.
IEEE, 2008, pp. 594–599.
[64] C. Popper, M. Strasser, and S. Capkun, “Anti-jamming broadcast communication using
uncoordinated spread spectrum techniques,” IEEE Journal on Selected Areas in Com-
munications, vol. 28, no. 5, pp. 703–715, June 2010.
[65] W. Xu, W. Trappe, and Y. Zhang, “Anti-jamming timing channels for wireless networks,” in
Proceedings of the First ACM Conference on Wireless Network Security, ser. WiSec ’08.
New York, NY, USA: ACM, 2008, pp. 203–213.
[66] M. Strasser, C. Pöpper, and S. Čapkun, “Efﬁcient uncoordinated FHSS anti-jamming
communication,” in Proceedings of the Tenth ACM International Symposium on Mobile
Ad Hoc Networking and Computing, ser. MobiHoc ’09.
New York, NY, USA: ACM, 2009,
pp. 207–218.
[67] K. Grover, A. Lim, and Q. Yang, “Jamming and anti-jamming techniques in wireless
networks: a survey,” International Journal of Ad Hoc and Ubiquitous Computing, vol. 17,
no. 4, pp. 197–215, 2014.
[68] W. Wang, Z. Sun, S. Piao, B. Zhu, and K. Ren, “Wireless physical-layer identiﬁcation: Mod-
eling and validation,” IEEE Transactions on Information Forensics and Security, vol. 11,
no. 9, pp. 2091–2106, 2016.
KA Physical Layer & Telecommunications Security | July 2021
Page 33
## Page 35

[69] T. J. Bihl, K. W. Bauer, and M. A. Temple, “Feature selection for RF ﬁngerprinting with
multiple discriminant analysis and using zigbee device emissions,” IEEE Transactions
on Information Forensics and Security, vol. 11, no. 8, pp. 1862–1874, 2016.
[70] T. D. Vo-Huu, T. D. Vo-Huu, and G. Noubir, “Fingerprinting Wi-Fi devices using software de-
ﬁned radios,” in Proceedings of the 9th ACM Conference on Security & Privacy in Wireless
and Mobile Networks.
ACM, 2016, pp. 3–14.
[71] S. Capkun, K. El Defrawy, and G. Tsudik, “Group distance bounding protocols,” in Interna-
tional Conference on Trust and Trustworthy Computing.
Springer, 2011, pp. 302–312.
[72] N. O. Tippenhauer and S. Čapkun, “Id-based secure distance bounding and localization,”
in European Symposium on Research in Computer Security.
Springer, 2009, pp. 621–
636.
[73] M. Kuhn, H. Luecken, and N. O. Tippenhauer, “UWB impulse radio based distance bound-
ing,” in 2010 7th Workshop on Positioning, Navigation and Communication.
IEEE, 2010,
pp. 28–37.
[74] L. Bussard and W. Bagga, “Distance-bounding proof of knowledge to avoid real-time
attacks,” in IFIP International Information Security Conference.
Springer, 2005, pp.
223–238.
[75] D. Singelée and B. Preneel, “Distance bounding in noisy environments,” in European
Workshop on Security in Ad-hoc and Sensor Networks.
Springer, 2007, pp. 101–115.
[76] K. B. Rasmussen and S. Capkun, “Realization of RF distance bounding.” in USENIX
Security Symposium, 2010, pp. 389–402.
[77] A. Ranganathan, N. O. Tippenhauer, B. Škorić, D. Singelée, and S. Čapkun, “Design and
implementation of a terrorist fraud resilient distance bounding system,” in European
Symposium on Research in Computer Security.
Springer, 2012, pp. 415–432.
[78] N. O. Tippenhauer, H. Luecken, M. Kuhn, and S. Capkun, “UWB rapid-bit-exchange system
for distance bounding,” in Proceedings of the 8th ACM Conference on Security & Privacy
in Wireless and Mobile Networks.
ACM, 2015, p. 2.
[79] S. Drimer, S. J. Murdoch et al., “Keep your enemies close: Distance bounding against
smartcard relay attacks.” in USENIX security symposium, vol. 312, 2007.
[80] C. Cremers, K. B. Rasmussen, B. Schmidt, and S. Capkun, “Distance hijacking attacks on
distance bounding protocols,” in 2012 IEEE Symposium on Security and Privacy.
IEEE,
2012, pp. 113–127.
[81] G. P. Hancke and M. G. Kuhn, “Attacks on time-of-ﬂight distance bounding channels,”
in Proceedings of the ﬁrst ACM conference on Wireless network security.
ACM, 2008,
pp. 194–202.
[82] K. B. Rasmussen and S. Čapkun, “Location privacy of distance bounding protocols,”
in Proceedings of the 15th ACM conference on Computer and communications security.
ACM, 2008, pp. 149–160.
[83] M. Flury, M. Poturalski, P. Papadimitratos, J.-P. Hubaux, and J.-Y. Le Boudec, “Effective-
ness of distance-decreasing attacks against impulse radio ranging,” in Proceedings of
the third ACM conference on Wireless network security.
ACM, 2010, pp. 117–128.
[84] R. Shokri, M. Poturalski, G. Ravot, P. Papadimitratos, and J.-P. Hubaux, “A practical
secure neighbor veriﬁcation protocol for wireless sensor networks,” in Proceedings of
the second ACM conference on Wireless network security.
ACM, 2009, pp. 193–200.
[85] S. Čapkun and J.-P. Hubaux, “Secure positioning of wireless devices with application to
sensor networks,” in IEEE infocom, no. CONF, 2005.
[86] J. T. Chiang, J. J. Haas, and Y.-C. Hu, “Secure and precise location veriﬁcation using
distance bounding and simultaneous multilateration,” in Proceedings of the second ACM
conference on Wireless network security.
ACM, 2009, pp. 181–192.
KA Physical Layer & Telecommunications Security | July 2021
Page 34
## Page 36

[87] N. Basilico, N. Gatti, M. Monga, and S. Sicari, “Security games for node localization
through veriﬁable multilateration,” IEEE Transactions on Dependable and Secure Com-
puting, vol. 11, no. 1, pp. 72–85, 2013.
[88] L. Lazos, R. Poovendran, and S. Čapkun, “Rope: robust position estimation in wireless
sensor networks,” in Proceedings of the 4th international symposium on Information
processing in sensor networks.
IEEE Press, 2005, p. 43.
[89] M. Backes, M. Dürmuth, S. Gerling, M. Pinkal, and C. Sporleder, “Acoustic side-channel
attacks on printers.” in USENIX Security symposium, 2010, pp. 307–322.
[90] D. Balzarotti, M. Cova, and G. Vigna, “Clearshot: Eavesdropping on keyboard input from
video,” in 2008 IEEE Symposium on Security and Privacy (sp 2008).
IEEE, 2008, pp.
170–183.
[91] M. Backes, M. Dürmuth, and D. Unruh, “Compromising reﬂections-or-how to read lcd
monitors around the corner,” in 2008 IEEE Symposium on Security and Privacy (sp 2008).
IEEE, 2008, pp. 158–169.
[92] R. Raguram, A. M. White, D. Goswami, F. Monrose, and J.-M. Frahm, “iSpy: automatic
reconstruction of typed input from compromising reﬂections,” in Proceedings of the 18th
ACM conference on Computer and communications security.
ACM, 2011, pp. 527–536.
[93] X. Liu, Z. Zhou, W. Diao, Z. Li, and K. Zhang, “When good becomes evil: Keystroke
inference with smartwatch,” in Proceedings of the 22nd ACM SIGSAC Conference on
Computer and Communications Security.
ACM, 2015, pp. 1273–1285.
[94] C. Kasmi and J. L. Esteves, “IEMI threats for information security: Remote command
injection on modern smartphones,” IEEE Transactions on Electromagnetic Compatibility,
vol. 57, no. 6, pp. 1752–1755, 2015.
[95] Y. Park, Y. Son, H. Shin, D. Kim, and Y. Kim, “This ain’t your dose: Sensor spooﬁng attack
on medical infusion pump,” in 10th USENIX Workshop on Offensive Technologies (WOOT
16), 2016.
[96] K. B. Rasmussen, C. Castelluccia, T. S. Heydt-Benjamin, and S. Capkun, “Proximity-
based access control for implantable medical devices,” in Proceedings of the 16th ACM
conference on Computer and communications security.
ACM, 2009, pp. 410–419.
[97] J. Selvaraj, G. Y. Dayanıklı, N. P. Gaunkar, D. Ware, R. M. Gerdes, M. Mina et al., “Electro-
magnetic induction attacks against embedded systems,” in Proceedings of the 2018 on
Asia Conference on Computer and Communications Security.
ACM, 2018, pp. 499–510.
[98] Y. Son, H. Shin, D. Kim, Y. Park, J. Noh, K. Choi, J. Choi, and Y. Kim, “Rocking drones with
intentional sound noise on gyroscopic sensors,” in 24th USENIX Security Symposium
(USENIX Security 15), 2015, pp. 881–896.
[99] G. Madlmayr, J. Langer, C. Kantner, and J. Scharinger, “NFC devices: Security and
privacy,” in 2008 Third International Conference on Availability, Reliability and Security.
IEEE, 2008, pp. 642–647.
[100] S. Burkard, “Near ﬁeld communication in smartphones,” Dep. of Telecommunication
Systems, Service-centric Networking, Berlin Institute of Technology, Germany, 2012.
[101] N. Alexiou, S. Basagiannis, and S. Petridou, “Security analysis of NFC relay attacks
using probabilistic model checking,” in 2014 International Wireless Communications
and Mobile Computing Conference (IWCMC).
IEEE, 2014, pp. 524–529.
[102] A. Costin and A. Francillon, “Ghost in the air (trafﬁc): On insecurity of ADS-B protocol
and practical attacks on ADS-B devices,” Black Hat USA, pp. 1–12, 2012.
[103] M. Schäfer, V. Lenders, and I. Martinovic, “Experimental analysis of attacks on next gen-
eration air trafﬁc communication,” in International Conference on Applied Cryptography
and Network Security.
Springer, 2013, pp. 253–271.
[104] M. Smith, D. Moser, M. Strohmeier, V. Lenders, and I. Martinovic, “Economy class crypto:
KA Physical Layer & Telecommunications Security | July 2021
Page 35
