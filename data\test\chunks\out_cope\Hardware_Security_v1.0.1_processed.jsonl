{"chunk_id": "line-1", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 4", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-2", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "INTRODUCTION", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-3", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Hardware security covers a broad range of topics from trusted computing to Trojan circuits.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-4", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "To classify these topics we follow the different hardware abstraction layers as introduced", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-5", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "by the Y-chart of Gajski & Kuhn. The different layers of the hardware design process will be", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-6", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "introduced in section 1. It is linked with the important concept of a root of trust and associated", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-7", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "threat models in the context of hardware security. Next follows section 2 on measuring", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-8", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and evaluating hardware security. The next sections gradually reduce the abstraction level.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-9", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Section 3 describes secure platforms, i.e. a complete system or system-on-chip as trusted", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-10", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "computing base. Next section 4 covers hardware support for software security: what features", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-11", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "should a programmable processor include to support software security. This section is closely", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-12", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "related to the Software Security CyBOK Knowledge Area [1]. Register transfer level is the next", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-13", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "abstraction level down, covered in section 5. Focus at this level is typically the efﬁcient and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-14", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "secure implementation of cryptographic algorithms so that they can be mapped on ASIC", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-15", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "or FPGA. This section is closely related to the Cryptography CyBOK Knowledge Area [2]. All", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-16", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "implementations also need protection against physical attacks, most importantly against side-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-17", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "channel and fault attacks. Physical attacks and countermeasures are described in section 6.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-18", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Section 7 describes entropy sources at the lowest abstraction level, close to CMOS technology.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-19", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "It includes the design of random numbers generators and physically unclonable functions.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-20", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The last technical section describes aspects related to the hardware design process itself.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-21", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "This chapter ends with the conclusion and an outlook on hardware security.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-23", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "HARDWARE DESIGN CYCLE AND ITS LINK TO", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-24", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "HARDWARE SECURITY", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-25", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Hardware security is a very broad topic and many topics fall under its umbrella. In this section,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-26", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "these seemingly unrelated topics are grouped and ordered according to the design levels of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-27", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "abstraction as introduced by the Y-chart of Gajski & Kuhn [3]. While Gajski & Kuhn propose a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-28", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "general approach to hardware design, in this chapter it is applied to the security aspects of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-29", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "hardware design and it is linked to threat models and the associated root of trust.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-30", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "1.1", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-31", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Short background on the hardware design process", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-32", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Design abstraction layers are introduced in hardware design to reduce the complexity of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-33", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "design. As indicated in 1, the lowest abstraction level a designer considers are individual", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-34", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "transistors at the center of the ﬁgure. These transistors are composed together to form", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-35", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "basic logic gates, such as NAND, NOR gates or ﬂip-ﬂops, called the logic level. Going one", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-36", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "abstraction layer up, at register transfer level gates are grouped together to form modules,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-37", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "registers, ALU’s, etc, and their operation is synchronized by a clock. These modules are then", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-38", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "composed to form processors, speciﬁed by instruction sets, upon which applications and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-39", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "algorithms can be implemented.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-40", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "By going up in the abstraction layers, details of underlying layers are hidden. This reduces", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-41", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "design complexity at higher abstraction layers. The abstraction layers are represented by", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-42", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "concentric circles in ﬁgure 1. Upon these circles, the Y-chart of Gajski & Kuhn introduces 3", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-43", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 3", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-44", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 5", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-45", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Behavioural Domain", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-46", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Structural Domain", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-47", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Physical Domain", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-48", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Systems", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-49", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Algorithms", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-50", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Register transfers", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-51", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Logic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-52", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Current, voltage", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-53", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Processors", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-54", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ALUs, RAM, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-55", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Gates, ﬂip-ﬂops, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-56", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Transistors", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-57", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Physical partitions", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-58", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Floorplans", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-59", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Module layout", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-60", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Cell layout", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-61", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Transistor layout", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-62", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Figure 1: Gajski-Kuhn Y-chart", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-63", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "design activities, represented by three axes: a behavioral axis, describing the behavior or what", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-64", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "needs to be implemented (aka speciﬁcations), a structural axis describing how something is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-65", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "implemented and a physical axis, how the layouts are composed together at gate, module,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-66", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "chip, board level. An actual design activity is a ‘walk’ through this design space. Typically", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-67", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "one starts with the speciﬁcations at the top of the behavioral domain. These speciﬁcations", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-68", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "(=what) are decomposed in components at the same level of abstraction (=how) moving from", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-69", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the behavioral axis to the structural axis. A structural component at one abstraction level", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-70", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "becomes a behavioral component at one level down.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-71", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "As an example of a walk through the design space: Assume a hardware designer is requested", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-72", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to implement a light-weight, low power security protocol for an Internet of Things (IoT) device.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-73", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "This designer will only receive speciﬁcations on what needs to be designed: a security protocol", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-74", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "aims at providing conﬁdentiality and integrity (= what) and a set of cryptographic algorithms", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-75", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "(= components) to support the protocol. The crypto-algorithms are provided as a behavioral", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-76", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "speciﬁcation to the hardware designer, who has the choice of implementing it as a dedicated", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-77", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "co-processor, as an assembly program, or support it with a set of custom instructions. De-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-78", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "pending on costs and volumes, a choice of a target CMOS technology or an FPGA platform", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-79", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is made. This behavioral level will be translated into a more detailed register-transfer level", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-80", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "description (e.g. VHDL or Verilog). At the Register Transfer Level (RTL), decisions need to be", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-81", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "made if this will be a parallel or sequential version, a dedicated or programmable design, with", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-82", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "or without countermeasures against side-channel and fault attacks, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-83", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Essential for the division in design abstraction layers, is the creation of models on how", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-84", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "components behave. E.g. to simulate the throughput or energy consumption of a arithmetic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-85", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "unit, quality models of the underlying gates need to be available. Similarly, the Instruction Set", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-86", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Architecture is a model of a processor available to the programmer.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-87", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 4", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-88", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 6", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-89", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "1.2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-90", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Root of trust", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-91", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In the context of security, a root of trust is a model of an underlying component for the purpose", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-92", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of security evaluation. According to Anderson [4]: ”A root of trust is a component used to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-93", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "realize a security function, upon which a designer relies but of which the trustworthiness", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-94", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can not be explicitly veriﬁed.” The designer uses one or multiple components to construct a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-95", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "security function, which then deﬁnes the trusted computing base. It is deﬁned by the trusted", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-96", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "computing group as follows: “An entity can be trusted if it always behaves in the expected", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-97", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "manner for the intended purpose.” [5].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-98", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "E.g. for an application developer, a Trusted Platform Module (TPM) or a Subscriber Identi-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-99", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ﬁcation Module (SIM) are a root of trust which the developer uses to construct a security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-100", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "application. For the TPM designer, the TPM is composition of smaller components which", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-101", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are composed together to provide security functionality. At the lowest hardware abstraction", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-102", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "layers, basic roots of trust are the secure storage of the key in memory or the quality of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-103", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "True Random Number Generator.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-104", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Hardware security is used as an enabler for software and system security. For this reason,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-105", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "hardware provides basic security services such as secure storage, isolation or attestation.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-106", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The software or system considers the hardware as the trusted computing base. And thus", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-107", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "from a systems or application view point, hardware has to behave as a trusted component.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-108", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "However, the hardware implementation can violate the trust assumption. E.g. Trojan circuits", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-109", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "or side-channel attacks could leak the key or other sensitive data to an attacker. Hence,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-110", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "hardware itself also needs security. Moreover hardware needs security at all abstraction", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-111", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "layers. Therefore, at every abstraction layer, a threat model and associated trust assumptions", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-112", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "need to be made. An alternative deﬁnition for a root of trust in the context of design abstraction", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-113", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "layers is therefore: “A root of trust is a component at a lower abstraction layer, upon which", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-114", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the system relies for its security. Its trustworthiness can either not be veriﬁed, or it is veriﬁed", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-115", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "at a lower hardware design abstraction layer. ”", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-116", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "1.3", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-117", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Threat model", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-118", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "A threat model is associated with each root of trust. When using a root of trust, it is assumed", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-119", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that the threat model is not violated. This means that the threat model is also linked to the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-120", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "hardware abstraction layers. If we consider a root of trust at a particular abstraction layer,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-121", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "then all components that constitute this root of trust, are also considered trusted.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-122", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Example 1: security protocols assume that the secret key is securely stored and not accessible", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-123", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to the attacker. The root of trust, upon which the protocol relies, is the availability of secure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-124", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memory to guard this key. For the protocol designer, this secure memory is a black box. The", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-125", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "hardware designer has to decompose this requirement for a secure memory into a set of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-126", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "requirements at a lower abstraction layer. What type of memory will be used? On which", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-127", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "busses will the key travel? Which other hardware components or software have access to the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-128", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "storage? Can there be side-channel leaks?", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-129", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Example 2: It is during this translation of higher abstraction layer requirements from protocol", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-130", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "or security application developers into lower abstraction layers for the hardware designers", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-131", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that many security vulnerabilities occur. Implementations of cryptographic algorithms used", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-132", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to be considered black boxes to the attacker: only inputs/outputs at the algorithm level are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-133", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "available to mount mostly mathematical cryptanalysis attacks. However, with the appearance", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-134", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of side-channel attacks (see section 6) this black box assumption no longer holds. Taking", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-135", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 5", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-136", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 7", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-137", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "side-channel leakage into account the attacker has the algorithm level information as well as", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-138", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the extra timing, power, electro-magnetic information as observable from the outside of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-139", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "chip. Thus the attacker model moves from black box to gray box. It is still assumed that the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-140", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attacker does not know the details of the internals, e.g. the contents of the key registers.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-141", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Example 3: for programmable processors, the model between hardware and software is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-142", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "traditionally considered the Instruction Set Architecture (ISA). The ISA is what is visible to the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-143", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "software programmer and the implementation of the ISA is left to the hardware designer. The", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-144", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ISA used to be considered the trust boundary for the software designer. Yet, with the discovery", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-145", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of micro-architectural side-channel attacks, such as Spectre, Meltdown, Foreshadow, this", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-146", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ISA model is no longer a black box, as also micro-architectural information and leakage are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-147", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "available to the attacker [6].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-148", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "1.4", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-149", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Root of trust, threat model and hardware design abstraction layers", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-150", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The decomposition in abstraction layers, in combination with Electronic Design Automation", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-151", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "(EDA) tools, is one of the main reasons that the exponential growth of Moore’s law was", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-152", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "sustainable in the past decades and it still is. This approach works well when optimizing for", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-153", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "performance, area, energy or power consumption. Yet for hardware security, no such general", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-154", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "decomposition exists.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-155", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In this chapter, we propose to organise the different hardware security topics, their associated", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-156", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "threat models and root of trust according to the hardware design abstraction layers, as there is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-157", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "no known other general body of knowledge available to organize the topics. This organization", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-158", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "has the advantage that it can be used to identify the state of the art on different subtopics of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-159", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "hardware security. As an example, in the speciﬁc context of hardware implementations of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-160", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cryptographic algorithms, the state of the art is well advanced and robust countermeasures", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-161", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "exist to protect cryptographic implementations against a wide range of side-channel attacks,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-162", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "as shown in detail in section 5. Yet in the context of general processor security, e.g. to isolate", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-163", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "process related data or to provide secure execution, new security hazards continue to be", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-164", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "discovered on a regular basis.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-165", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In an attempt to order the topics, table 1 summarizes this organization. The different ab-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-166", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "straction layers are identiﬁed (ﬁrst column) from a hardware perspective. The highest level", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-167", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "(system and software) sits on top of the hardware platform. E.g. a system designer assumes", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-168", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that a secure platform is available. Thus the secure platform is the root of trust, providing", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-169", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "security functionality. The second column describes the functionality provided by the root of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-170", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "trust. The third column describes how this functionality might be implemented. E.g. at the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-171", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "highest abstraction layer this might be by providing a Trusted Execution Module or a secure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-172", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "element, etc. The fourth column describes the threat models and attack categories at that", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-173", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "abstraction layer. E.g. at system level, the system designer assumes that they will receive a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-174", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "module that provides isolation, integrity, attestation, etc. The last column describes typical", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-175", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "design activities at this particular design abstraction layer.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-176", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "This exercise is repeated for each abstraction layer and described in detail in each of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-177", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "following sections.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-178", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "At the processor level, one can distinguish general purpose programmable processors and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-179", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "domain speciﬁc processors. General purpose processors should support a wide range of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-180", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "applications, which unfortunately typically include software vulnerabilities. Hardware features", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-181", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are added to address these software vulnerabilities, such as a shadow stack or measures", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-182", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 6", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-183", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 8", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-184", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Abstraction level", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-185", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Root of trust -", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-186", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "functionality", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-187", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Structural (how) -", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-188", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "examples", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-189", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Example Threats", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-190", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Typical HW design", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-191", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "activities", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-192", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "System and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-193", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "application", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-194", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Secure platforms", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-195", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "e.g. Trusted", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-196", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Execution", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-197", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "(Trustzone, SGX,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-198", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "TEE), HSM, Secure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-199", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Element", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-200", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to support", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-201", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "isolation, integrity,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-202", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attestation, ...", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-203", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-204", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "application", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-205", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "development", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-206", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Processor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-207", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "general purpose", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-208", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "e.g. shadow stack", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-209", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "SW vulnerabilities", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-210", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ISA, HW/SW", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-211", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "co-design", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-212", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Processor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-213", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "domain speciﬁc", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-214", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Crypto speciﬁc", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-215", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "RTL", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-216", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Timing attacks", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-217", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Constant number", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-218", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of clock cycles", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-219", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Register Transfer", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-220", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Crypto speciﬁc", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-221", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Building blocks,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-222", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Side Channel", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-223", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Attack,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-224", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Logic synthesis", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-225", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Logic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-226", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Resistance to SCA,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-227", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Power, EM, fault", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-228", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Masking, Circuit", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-229", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "styles", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-230", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Side Channel", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-231", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attack, fault", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-232", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "FPGA tools,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-233", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "standard cell", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-234", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "design", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-235", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Circuit and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-236", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "technology", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-237", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Source of entropy", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-238", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "TRNG, PUF, Secure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-239", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "SRAM", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-240", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Temperature,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-241", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "glitches", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-242", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "SPICE simulations", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-243", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Physical", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-244", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Tamper", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-245", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Resistance", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-246", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Shields, sensors", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-247", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Probing, heating", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-248", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Layout activities", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-249", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Table 1: Design abstraction layers linked to threat models, root of trust and design activities", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-250", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to support hardware control ﬂow integrity. Domain speciﬁc processors typically focus on", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-251", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a limited functionality. They are typically developed as co-processors in larger systems-on-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-252", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "chip. Typical examples are co-processors to support public key or secret key cryptographic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-253", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "algorithms. Time at the processor level is typically measured in instruction cycles.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-254", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Both general purpose and domain speciﬁc processors are composed together from compu-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-255", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tational units, multipliers and ALU’s, memory and interconnect. These modules are typically", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-256", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "described at the register transfer level: constant-time and resistance against side-channel", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-257", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attacks become the focus. Time at this level is typically measured in clock cycles.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-258", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Multipliers, ALU’s, memories, interconnect and bus infrastructure are created from gates", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-259", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and ﬂip-ﬂops at the logic level. At this design abstraction level, focus is on leakage through", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-260", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "physical side-channels, power, electro-magnetic, and fault attacks. Time is typically measured", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-261", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "in absolute time (nsec) based on the available standard cell libraries or FPGA platforms.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-262", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The design of entropy sources requires knowledge and insights into the behavior of transistors", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-263", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and the underlying Complementary Metal-Oxide-Semiconductor (CMOS) technology.The de-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-264", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "sign of these hardware security primitives is therefore positioned at the circuit and transistor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-265", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "level. Similarly the design of sensors and shields against physical tampering require insight", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-266", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "into the technology. At the circuit and technology level it is measured in absolute time, e.g.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-267", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "nsec delay or GHz clock frequency.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-268", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The table 1 does not aim to be complete. The idea is to illustrate each abstraction layer with", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-269", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "an example. In the next sections, the hardware security goals and their associated threat", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-270", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "models will be discussed in detail in relation to and relevance for each abstraction layer.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-271", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 7", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-272", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 9", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-274", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "MEASURING HARDWARE SECURITY", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-275", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Depending on the commercial application domain, several industrial and government orga-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-276", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "nizations have issued standards or evaluation procedures. The most well known ones are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-277", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the FIPS 140-2 (and the older FIPS 140-1), the Common Criteria (CC) evaluation and in the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-278", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ﬁnancial world the EMVCO. FIPS 140-2 mostly focuses on the implementation security of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-279", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cryptographic algorithms. Common Criteria are applicable to IT security in general.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-280", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "2.1", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-281", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "FIPS140-2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-282", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "FIPS140-2 is a US NIST standard used for the evaluation of cryptographic modules. FIPS140-2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-283", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "deﬁnes security levels from 1 to 4 (1 being the lowest). The following gives a description of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-284", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "four levels from a physical hardware security point of view. Next to the physical requirements,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-285", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "there are also roles, services and authentication requirements (for more details see [7] and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-286", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "other KAs).", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-287", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Security level 1 only requires than an approved cryptographic algorithm be used, e.g. AES or", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-288", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "SHA-3, but does not impose physical security requirements. Hence a software implementation", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-289", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "could meet level 1. Level 2 requires a ﬁrst level of tamper evidence. Level 3 also requires the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-290", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tamper evidence, but on top requires tamper resistance.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-291", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "NIST deﬁnes tampering as an intentional but unauthorized act resulting in the modiﬁcation of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-292", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a system, components of systems, its intended behavior, or data, [8].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-293", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Tamper evidence means that there is a proof or testimony that tampering with a hardware", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-294", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "module has happened. E.g. a broken seal indicates that a device was opened. A light sensor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-295", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "might observe that the lid of a chip package was lifted.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-296", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Tamper resistance means that on top of tamper evidence, protection mechanisms are added", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-297", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to the device. E.g. by extra coating or dense metal layers, it is difﬁcult to probe the key registers.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-298", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Level 4 increases the requirements such that the cryptographic module can operate in phys-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-299", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ically unprotected environments. In this context, the physical side-channel attacks pose", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-300", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "an important threat. If any of these physical components depend on sensitive data being", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-301", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "processed, information is leaked. Since the device is under normal operation, a classic tamper", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-302", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "evidence mechanism will not realize that the device is under attack. See later in section 6.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-303", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "2.2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-304", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Common criteria and EMVCo", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-305", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "“Common Criteria for information technology security evaluation” is an international standard", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-306", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "for IT product security (ISO/IEC 15408), in short known as Common Criteria (CC). CC is a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-307", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "very generic procedure applicable to the security evaluation of IT products. Several parties", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-308", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are involved in this procedure. The customer will deﬁne a set of security speciﬁcations for", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-309", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "its product. The manufacturer will design a product according to these speciﬁcations. An", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-310", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "independent evaluation lab will verify if the product fulﬁlls the claims made in the security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-311", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "requirements. Certiﬁcation bodies will issue a certiﬁcation that the procedure was correctly", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-312", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "followed and that the evaluation lab indeed conﬁrmed the claims made. The set of security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-313", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "speciﬁcations are collected in a so-called protection proﬁle.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-314", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Depending on the amount of effort put into the security evaluation, the CC deﬁnes different", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-315", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Evaluation Assurance Levels (EALs). It ranges from basic functional testing, corresponding", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-316", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 8", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-317", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 10", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-318", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to EAL1, to formally veriﬁed design and tested, corresponding to the highest level EAL7. CC", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-319", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "further subdivides the process of evaluation into several classes, where most of the classes", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-320", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "verify the conformity of the device under test. The 5th class (AVA) deals with the actual", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-321", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "vulnerability assessment. It is the most important class from a hardware security viewpoint", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-322", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "as it searches for vulnerabilities and associated tests. It will assign a rating on the difﬁculty", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-323", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to execute the test, called the identiﬁcation, and the possible beneﬁt an attacker can gain", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-324", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "from the penetration, called the exploitation. The difﬁculty is a function of the time required", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-325", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to perform the attack, the expertise of the attacker from layman to multiple experts, how", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-326", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "much knowledge of the device is required from simple public information to detailed hardware", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-327", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "source code, the number of samples required, and the cost and availability of equipment to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-328", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "perform the attack, etc. A high difﬁculty level will result in a high score and a high level of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-329", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "AVA class. The highest score one can obtain is an AVA level of 5, which is required to obtain a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-330", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "top EAL score.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-331", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Its usage is well established in the ﬁeld of smartcards and secure elements as they are used", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-332", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "in telecom, ﬁnancial, government ID’s applications. It is also used in the ﬁeld of Hardware", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-333", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Security Modules, Trusted Platform Modules and some more [9]. For certain classes of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-334", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "applications minimum sets of requirements are deﬁned into protection proﬁles. There exists", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-335", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "protection proﬁles for Trusted Platform Module (TPM), Javacards, Biometric passports, SIM", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-336", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cards, secure elements, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-337", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Since certiﬁcation comes from one body, there exist agreements between countries so that", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-338", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the certiﬁcations in one country are recognized in other countries. As an exception EMVCo", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-339", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is a private organization to set the speciﬁcations for worldwide interoperability of payment", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-340", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "transactions. It has its own certiﬁcation procedure similar to CC.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-341", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Please note that the main purpose of a common criteria evaluation is to verify that an IT product", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-342", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "delivers the claims promised in the proﬁle. It does not mean that there are no vulnerabilities", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-343", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "left. A good introduction to the topic can be found in [10] and a list of certiﬁed products on [9].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-344", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "2.3", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-345", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "SESIP: Security Evaluation Standard for IoT Platforms", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-346", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In the context of IoT security evaluation, a recent initiative is the SESIP Security Evaluation", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-347", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "scheme [11], currently at version 1.2. IoT devices are typically small, light-weight ’things’, with", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-348", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "limited accessibility via internet. Several levels of threat model for IoT are possible: from", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-349", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "only remote internet access, over various remote software attack options, to also physical", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-350", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attack resistance. A comprehensive set of security functional requirements are deﬁned:", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-351", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "identiﬁcation and attestation, product lifecycle, secure communication, software and physical", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-352", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attack resistance, cryptographic functionality including random number generation, and some", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-353", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "compliance functionality to e.g. provide secure encrypted storage or provide reliable time.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-354", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Similar to Common Criteria, SESIP provides several levels of assurance. Level 1 is the lowest", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-355", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "level and consists of a self-assessment. The highest level of SESIP consists of a full CC", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-356", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "evaluation similar to smart cards or secure elements. The levels in between cover from a black", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-357", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "box penetration testing over white box penetration testing with or without time limitations.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-358", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 9", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-359", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 11", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-361", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "SECURE PLATFORMS", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-362", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "This section describes the goals and the state-of-the-art in secure platforms. At this high level", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-363", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of abstraction the system designer receives a complete chip or board as trusted computing", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-364", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "base. The system designers assume that the trusted root delivers a set of cryptographic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-365", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "functions, protected by the hardware and software inside the physical enclosure. Common", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-366", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to these platforms is that they are stand-alone pieces of silicon with a strict access policy.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-367", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Depending on the provided functionality, the hardware tamper resistance and protection levels,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-368", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and the communication interface, these secure platforms are used in different application", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-369", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ﬁelds (automotive, ﬁnancial, telecom). Three important platforms are the Hardware Security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-370", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Module (HSM), the Subscriber Identiﬁcation Module or SIM and the Trusted Platform Module", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-371", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "(TPM). These are brieﬂy described next.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-372", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "3.1", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-373", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "HSM Hardware Security Module", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-374", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "A HSM module will typically provide cryptographic operations, e.g. a set of public key and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-375", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "secret key algorithms, together with secure key management including secure generation,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-376", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "storage and deletion of keys. Essential to HSM’s is that these operations occur in a hardened", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-377", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and tamper resistant environment. A TRNG and a notion of a real-time clock are usually also", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-378", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "included. HSM’s are mostly used in server back-end systems to manage keys or payment", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-379", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "systems, e.g. in banking systems.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-380", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "A HSM is used as a co-processor, attached to a host system. Its architecture typically includes", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-381", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a micro-processor/micro-controller, a set of crypto co-processors, secure volatile and non-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-382", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "volatile memory, TRNG, real-time clock, and I/O. The operations occur typically inside a tamper", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-383", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "resistant casing. In previous generations, inside the casing multiple components reside on", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-384", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "one board.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-385", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Recently, in some application domains, such as automotive, HSM functionality is no longer", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-386", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "provided as a stand-alone module but is now integrated as a secure co-processor in a larger", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-387", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "System on Chip (SoC). Indeed Moore’s law enables higher integration into one SoC. What", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-388", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "exactly is covered under HSM functionality depends on the application domain. Therefore,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-389", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "compliance with security levels is also evaluated by specialized independent evaluation labs", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-390", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "according to speciﬁc protection proﬁles.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-391", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "3.2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-392", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Secure Element and Smartcard", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-393", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Similar to an HSM, a Secure Element and a smart card provide a set of cryptographic algo-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-394", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "rithms, public key, secret key, HMAC, etc. together with secure key storage, generation and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-395", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "deletion. The main difference with an HSM are cost, size, and form factor. They are typically", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-396", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "implemented as one single integrated circuit and have a much smaller form factor from around", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-397", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "50 cm2 to less than 1 cm2. The main difference between a smart card and a secure element", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-398", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "sits in the form factor and the different markets they address. Secure elements are a more", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-399", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "generic term, while smart cards have the very speciﬁc form factor of a banking card. They", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-400", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are produced in large volumes and need to be very cheap as they are used for SIM cards in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-401", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cell phones and smart phones. They are also used in banking cards, pay-TV systems access", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-402", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cards, national identity cards and passports, and recently in IOT devices, vehicular systems", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-403", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and so on. Tamper resistance and physical protection are essential to secure elements. They", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-404", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are a clear instance of what in a computer architecture domain are called ’domain speciﬁc", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-405", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 10", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-406", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 12", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-407", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "processors’. Speciﬁc protection proﬁles exist depending the application domain: ﬁnancial,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-408", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "automotive, pay-TV, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-409", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "A typical embedded secure element is one integrated circuit with no external components. It", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-410", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "consists of a small micro-controller with cryptographic co-processors, secure volatile and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-411", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "non-volatile storage, TRNG, etc. I/O is usually limited, through a speciﬁc set of pins, or through", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-412", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a NFC wireless connection. Building a secure element is a challenge for a hardware designer,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-413", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "as one needs to combine security with non-security requirements of embedded circuits: small", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-414", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "form factor (no external memory), low power and/or low energy consumption in combination", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-415", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "with tamper resistance and resistance against physical attacks, such as side-channel and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-416", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "fault attacks (see section 6).", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-417", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "3.3", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-418", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Trusted Platform Module (TPM)", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-419", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The TPM module has been deﬁned by the Trusted Computing Group (TCG), an industry asso-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-420", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ciation, to provide speciﬁc security functions to the Personal Computer (PC) platform. More", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-421", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "speciﬁcally, the TPM is a root of trust embedded on the PC platform, so that PC+TPM platform", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-422", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can identify itself and its current conﬁguration and running software [5]. The TPM provides", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-423", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "three speciﬁc roots of trust: the Root of Trust for Measurement (RTM), the Root of Trust for", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-424", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Storage (RTS), the Root of Trust for Reporting (RTR). Besides these three basic functions,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-425", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "other functionality of TPMs is being used: access to speciﬁc cryptographic functions, secure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-426", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "key storage, support for secure login, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-427", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The TPM is implemented as a separate security module, much like a secure element but with", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-428", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a speciﬁc bus interface to a PC platform, e.g. through the LPC or I2C bus interface. Its archi-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-429", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tecture at minimum consists of an embedded micro-controller, several crypto coprocessors,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-430", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "secure volatile and non-volatile storage for root keys and a high quality true random number", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-430", "line_number": 430, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-431", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "generator. It includes hardware engines for hash functions (SHA1 and SHA256), public key", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-432", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "(RSA and ECC), secret key (AES) and HMAC calculations. Since a TPM is a separate module,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-433", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "physical protection and tamper resistance is essential for security. Next to its main scope of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-434", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "integrity protection, TPM also has applications in disk encryption, digital rights management,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-435", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-436", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The most recent TPM2.0 version broadens the application scope from PC oriented to also", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-437", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "supporting networking, embedded, automotive, IoT, and so on. It also provides a more ﬂexible", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-438", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "approach in the functionality included. Four types of TPM are identiﬁed: the dedicated", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-439", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "integrated circuit ‘discrete element’ TPM provides the highest security level. One step lower in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-440", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "protection level is the ‘integrated TPM’ as an IP module in a larger SoC. The lowest levels of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-441", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "protection are provided by the ﬁrmware and software TPM.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-442", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The adoption of TPMs has evolved differently from what was originally the focus of the TCG.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-443", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Originally, the main focus was the support of a secure boot and the associated software stack,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-444", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "so that a complete measurement of the software installed could be made. The problem is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-445", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that the complexity of this complete software base grows too quickly, making it too difﬁcult to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-446", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "measure completely all variations in valid conﬁgurations. Thus TPMs are less used to protect", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-447", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a complete software stack up to the higher layers of software. Still most new PCs now have", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-448", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "TPMs but they are used to protect the encryption keys, avoid ﬁrmware roll-back, and assist", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-449", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the boot process in general.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-450", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Starting from the original TPM, the Trusted Computing Group has broadened its scope and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-451", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "now has working groups on many different application, such as cloud, embedded systems,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-452", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 11", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-453", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 13", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-454", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "IoT, mobile, network equipment, and so on, [12].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-456", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "HARDWARE SUPPORT FOR SOFTWARE SECURITY AT", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-457", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ARCHITECTURE LEVEL", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-458", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "At the secure platform level, the complete module, i.e. hardware and its enclosed embedded", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-459", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "software, are part of the trusted computing base. One level down on the abstraction layers,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-460", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "we make the assumption that all hardware is trusted, while software is no longer trusted.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-461", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Indeed, software vulnerabilities are a major source of security weaknesses (see the Software", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-462", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Security CyBOK Knowledge Area [1]). To prevent the exploitation or to mitigate the effects of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-463", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "software vulnerabilities, a large variety of hardware modiﬁcations/additions to the processor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-464", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "architecture have been proposed in literature and have been included in commercial proces-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-465", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "sors. We call this abstraction layer the hardware/software boundary: hardware forms the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-466", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "trust boundary, while software is no longer trusted. These security additions to the hardware", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-467", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "typically have a cost in extra area and loss in performance.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-468", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The most important security objectives at this design abstraction level are to support pro-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-469", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tection, isolation and attestation for the software running on a processor platform [13], [14],", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-470", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "[15].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-471", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Protection: ”A set of mechanisms for ensuring that multiple processes sharing the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-472", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "processor, memory, or I/O devices cannot interfere, intentionally or unintentionally, with", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-473", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "one another by reading or writing each others’ data. These mechanisms also isolate", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-474", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the operating system from the user process” [13]. In a traditional computer architecture,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-475", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "usually the OS kernel is part of the Trusted Computing Base (TCB), but the rest of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-476", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "software is not.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-477", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• With isolation, a hardware mechanism is added that controls access to pieces of soft-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-478", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ware and associated data. Isolation separates two parties: a software module might", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-479", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "need protection from the surrounding software is one case. So, a Protected Model Archi-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-480", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tecture (PMA) provides a hardware guarantee that a piece of software runs unhindered", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-481", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "from unwanted outside inﬂuences. The opposite case, if we want to limit the effects", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-482", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of possibly tainted software to its environment, it will be sandboxed or be put into a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-483", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "‘compartment.’ Protected Module Architectures are a hardware only solution: the OS is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-484", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "not part of the TCB. More details are described in section 4.4", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-485", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• With attestation, there is hardware support to demonstrate to a third party that the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-486", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "system, e.g. the code installed and/or running on a processor, is in a particular state.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-487", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Attestation can be local or remote. Local attestation means that one software module", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-488", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can attest its state to another one on the same compute platform. Remote attestation", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-489", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "means that a third party, outside the compute platform can get some guarantee about", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-490", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the state of a processor.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-491", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In the context of general purpose computing, Virtual Machines (VMs) and Hypervisors have", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-492", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "been introduced to support multiple operating systems on one physical processor. This", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-493", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "sharing of resources improves efﬁciency and reuse. It can however only be realized by a secure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-494", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and efﬁcient sharing of physical memory: virtual machines should only be allowed to use the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-495", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "portions of physical memory assigned to it. The organization and details of virtual memory are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-496", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "out of scope of hardware security and part of the Operating Systems & Virtualisation CyBOK", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-497", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Knowledge Area [16]. The hardware supports protection by providing privileged instructions,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-498", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 12", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-499", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 14", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-500", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "control and status registers and sometimes support for multiple parallel threads.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-501", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In the context of embedded micro-controllers, with no operating system, and only one applica-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-502", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tion, the hardware support could be limited to only machine level support. Memory protection", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-503", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "could be added as an optional hardware module to the processor.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-504", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Other more advanced security objectives to support software security might include:", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-505", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Sealed storage is the process of wrapping code and/or data with certain conﬁguration,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-506", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "process or status values. Only under the correct conﬁguration (e.g. program counter", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-507", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "value, nonce, secret key, etc.) can the data be unsealed. Dynamic root of trust in combi-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-508", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "nation with a late launch guarantees that even if the processor starts from an unknown", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-509", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "state, it can enter a ﬁxed known piece of code and known state. This typically requires", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-510", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "special instructions to enter and exit the protected partition.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-511", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Memory protection refers to the protection of data when it travels between the processor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-512", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "unit and the on-chip or off-chip memory. It protects against bus snooping or side-channel", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-513", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attacks or more active fault injection attacks.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-514", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Control ﬂow integrity is a security mechanism to prevent malware attacks from redirect-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-515", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ing the ﬂow of execution of a program. In hardware, the control ﬂow of the program is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-516", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "compared on-the-ﬂy at runtime with the expected control ﬂow of the program.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-517", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Information ﬂow analysis is a security mechanism to follow the ﬂow of sensitive data", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-518", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "while it travels through the different components, from memory to cache over multiple", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-519", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "busses into register ﬁles and processing units and back. This is important in the context", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-520", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of micro-architectural and physical side-channel attacks.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-521", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In the next subsections a representative set of hardware approaches to address the above", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-522", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "software security challenges are presented. Some hardware techniques address multiple se-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-523", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "curity objectives. Some are large complex approaches, others are simple dedicated hardware", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-524", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "features.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-525", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "As a side note: a large body of knowledge on software-only approaches is available in literature.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-526", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Mostly, they offer a weaker level of security as they are not rooted in a hardware root of trust.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-527", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "E.g. for control ﬂow integrity, software-only approaches might instruct the software code", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-528", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to check branches or jumps, while hardware support might calculate MACs on the ﬂy and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-529", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "compare these to stored associated MACs.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-530", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "4.1", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-531", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Trusted Execution Environment (TEE)", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-532", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "TEE was originally an initiative of Global Platform, a consortium of companies, to standardize a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-533", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "part of the processor as a trusted secure part. TEE has since evolved and covers in general the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-534", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "hardware modiﬁcations made to processors to provide isolation and attestation to software", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-535", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "applications. There is a large body of knowledge both from the industrial side as well as from", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-536", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the academic side.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-537", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "TEE is a concept that provides a secure area of the main processor “to provide end-to-end", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-538", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "security by protecting the execution of authenticated code, conﬁdentiality, authenticity, privacy,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-539", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "system integrity and data access rights” [17]. It is important that the TEE is isolated from the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-540", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "so-called Rich Execution Environment (REE), which includes the untrusted OS. The reasoning", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-541", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "behind this split is that it is impossible to guarantee secure execution and to avoid malware in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-542", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the normal world due to the complexity of the OS and all other applications running there. The", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-543", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 13", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-544", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 15", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-545", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "rich resources are accessible from the TEE, while the opposite is not possible. Global Platform", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-546", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "does not specify the speciﬁcs on how these security properties should be implemented. Three", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-547", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "main hardware options are suggested. Option 1 assumes that every processor component on", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-548", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the IC can be split into a trusted and a rich part, i.e. the processor core, the crypto accelerators,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-549", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the volatile and non-volatile memory are all split. Option 2 assumes that there is a separate", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-550", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "secure co-processor area on the SoC with a well-deﬁned hardware interface to the rest of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-551", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "SoC. Option 3 assumes a dedicated off-chip secure co-processor, much like a secure element.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-552", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Global Platform deﬁnes also a Common Criteria based protection proﬁle (see section 2.2)", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-553", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "for the TEE. It assumes that the package of the integrated circuit is a black box [17] and thus", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-554", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "secure storage is assumed by the fact that the secure asset remains inside the SoC. It follows", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-555", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the procedures of common criteria assurance package EAL2 with some extra features. It", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-556", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "pays extra attention to the evaluation of the random number generator and the concept of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-557", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "monotonic increasing time.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-558", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "4.2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-559", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "IBM 4758 Secure coprocessor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-560", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "An early example, even before the appearance of the TEE of Global Platform is the IBM 4758", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-561", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "secure processor. Physical hardware security was essential for this processor: it contained a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-562", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "board with a general purpose processor, DRAM, separate battery backed-DRAM, Flash ROM,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-563", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "crypto accelerator (for DES), a random number generator and more. All of these components", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-564", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "were enclosed in a box with tamper resistant and tamper evidence measures. It was certiﬁed", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-565", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to FIPS 140-1, level 4 at that time [18].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-566", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "4.3", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-567", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ARM Trustzone", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-568", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ARM Trustzone is one well known instantiation of a TEE. It is part of a system of ARM", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-569", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "processors integrated into System on Chips (SoCs) mostly used for smartphones. The TEE", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-570", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is the secure part of the processor and it runs a smaller trusted OS. It is isolated from the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-571", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "non-secure world, called the Rich Execution Environment, which runs the untrusted rich OS.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-572", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The main hardware feature to support this split is the Non-Secure (NS) bit. The AXI bus", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-573", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "transactions are enhanced with a NS bit so that it can block the access of secure world", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-574", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "resources by non-secure resources. Each AXI transaction comes with this bit set or reset.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-575", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "When the processor runs in the secure mode, then the transaction comes with the NS bit set", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-576", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to zero, which gives it access to both secure and non-secure resources. When the processor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-577", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "runs in normal mode, it can only access resources from the normal world. This concept is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-578", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "extended to the level 1 and level 2 cache. These caches store an extra information bit to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-579", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "indicate if the code can be accessed by a secure or non-secure master. Special procedures", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-580", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are foreseen to jump from secure to non-secure and vice-versa. This is supported by a special", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-581", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "monitor mode which exists in the secure world.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-582", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The split applied by ARM Trustzone is however a binary split. Applications from different ven-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-583", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "dors could co-exist together in the secure world and so if one trusted component violates the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-584", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "system’s security, the security can no longer be guaranteed. To address this issue, protected", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-585", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "module architectures are introduced.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-586", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Trusted Execution Environments are also being created in open-source context, more speciﬁ-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-587", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cally in the context of the RISC-V architecture.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-588", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 14", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-589", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 16", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-590", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "4.4", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-591", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Protected Module Architectures and HWSW co-design solutions", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-592", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "If multiple software applications want to run on the same platform isolated from each other,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-593", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "then hardware needs to isolate them from each other at a more ﬁne granularity. This can", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-594", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "be done by so-called protected module architectures. The basic idea is that small software", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-595", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "modules can run protected from all other software running on the processor. And because", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-596", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "they are small, their properties and behavior can be veriﬁed more thoroughly. The protection", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-597", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is provided by extra features added to the hardware in combination with an extremely small", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-598", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "trusted software base if needed. In the Flicker project, the software TCB relies on only 250", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-599", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "lines of codes but requires a dedicated TPM chip [19]. Table 12 of the review work of [18],", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-600", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "provides an in-depth comparison of several general purpose secure processor projects with", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-601", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "their hardware and software TCB. The hardware TCB distinguishes between the complete", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-602", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "mother board as TCB, e.g. for TPM usage, to CPU package only for SGX and other projects. The", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-603", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "software TCB varies from a complete secure world as is the case for TrustZone to privileged", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-604", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "containers in the case of SGX or a trusted hypervisor, OS or security monitor.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-605", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Even more advanced are solutions with a zero trusted software base: only the hardware is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-606", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "trusted. This is the case for the Sancus project [20]. It implements a program counter based", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-607", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memory access control system. Extra hardware is provided to compare the current program", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-608", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "counter with stored boundaries of the protected module. Access to data is only possible if", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-609", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the program counter is in the correct range of the code section. Progress of the program in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-610", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the code section is also controlled by the hardware so that correct entry, progress and exit of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-611", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the module can be guaranteed.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-612", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Intel’s Software Guard Extension (SGX) are also a protection mechanism at small granularity.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-613", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Software modules of an application are placed in memory enclaves. Enclaves are deﬁned in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-614", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the address space of a process, but access to enclaves is restricted. Enclaves are created,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-615", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "initialized, and cleared by possibly untrusted system software, but operating in the enclave", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-616", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can only be done by the application software. Minimizing the extra hardware to support SGX,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-617", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and especially avoiding performance degradation is an important goal. The details of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-618", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "hardware micro-architecture have not been disclosed: yet its most important parts are a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-619", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memory encryption unit, a series of hardware enforced memory access checks and secure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-620", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memory range registers [18].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-621", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "4.5", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-622", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Light-weight and individual solutions", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-623", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The above listed solutions are mostly suited for general purpose computing, i.e. for platforms", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-624", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "on which a complex software stack will run. In literature, more solutions are proposed to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-625", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "provide extremely light weight solutions to support speciﬁc security requests. SMART is one", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-626", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "early example: it includes a small immutable piece of bootROM, considered the root of trust,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-627", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to support remote attestation [21].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-628", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "To protect against speciﬁc software attacks, more individual hardware countermeasures", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-629", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "have been introduced. One example is a hardware shadow stack: to avoid buffer overﬂow", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-630", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attacks and to protect control ﬂow integrity, return addresses are put on both the stack and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-631", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the shadow stack. When a function loads a return address, the hardware will compare the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-632", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "return address of the stack to that of the shadow stack. They should agree for a correct return.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-633", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Another example is the protection of jump and return addresses to avoid buffer overﬂow", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-634", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attacks and other abuses of pointers. A simple but restrictive option is to use read-only", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-635", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memory, which ﬁxes the pointer. A novel recent technique is the use of pointer authentication.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-636", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 15", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-637", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 17", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-638", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The authentication code relies on cryptographic primitives. A challenge for these algorithms", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-639", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is that they should create the authentication tag with very low latency to ﬁt into the critical", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-640", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "path of a microprocessor. The ARMV8-A architectures uses therefore a dedicated low-latency", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-641", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "crypto algorithm Qarma [22]. In this approach the unused bits in a 64-bit pointer are used", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-642", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to store a tag. This tag is calculated based on a key and on the program state, i.e. current", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-643", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "address and function. These tags are calculated and veriﬁed on the ﬂy.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-644", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Address Space Layout Randomization or Stack canaries area general software technique: its", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-645", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "aim is to make it hard to predict the destination address of the jump. A detailed description", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-646", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can be found in the Software Security CyBOK Knowledge Area [1].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-648", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "HARDWARE DESIGN FOR CRYPTOGRAPHIC", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-649", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ALGORITHMS AT RTL LEVEL", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-650", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The hardware features discussed so far are added to general purpose compute platforms,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-651", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "i.e. to a programmable micro-processor or micro-controller. General purpose means that a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-652", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "platform is created of which the hardware designer does not know the future applications", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-653", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that will run on it. Flexibility, reﬂected in the instruction set, is then of importance. A second", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-654", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "class of processors are domain-speciﬁc processors: they have limited or no programmability", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-655", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and designed for one or a small class of applications.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-656", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "5.1", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-657", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Design process from RTL to ASIC or FPGA", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-658", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "When a dedicated processor is built for one or a class of cryptographic algorithms, this gives", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-659", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a lot of freedom to the hardware designer. Typically, the hardware designer will, starting", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-660", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "from the cryptographic algorithm description, come up with hardware architectures at the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-661", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Register Transfer Level (RTL) taking into account a set of constraints. Area is measured", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-662", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "by gate count at RTL level. Throughput is measured by bits/sec. Power consumption is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-663", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "important for cooling purposes and measured in Watt. Energy, measured in Joule, is important", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-664", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "for battery operated devices. It is often expressed in the amount of operations or amount", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-665", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of bits that can be processed per unit energy. Hence the design goal is to maximize the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-666", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "operations/Joule or bits/Joule. The resistance to side channel attacks is measured by the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-667", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "number of measurements or samples required to disclose the key or other sensitive material.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-668", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Flexibility and programmability are difﬁcult to measure and are typically imposed by the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-669", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "application or class of applications that need to be supported: will the hardware support", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-670", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "only one or a few algorithms, encryption and/or decryption, modes of operation, initialization,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-671", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "requirements for key storage, and so on.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-671", "line_number": 671, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-672", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "A hardware architecture is typically described in a Hardware Description Language such as", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-673", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Verilog of VHDL. Starting from this description the two most important hardware platforms", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-673", "line_number": 673, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-674", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "available to a hardware designer are ASIC and FPGA. An Application Speciﬁc Integrated Circuit", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-675", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "(ASIC) is a dedicated circuit fabricated in silicon. Once fabricated (baked) it cannot be modiﬁed", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-676", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "anymore. A Field Programmable Gate Array (FPGA) is a special type of programmable device:", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-677", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "it consists of regular arrays of 1-bit cells, that can programmed by means of a bitstream. This", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-677", "line_number": 677, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-678", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "special bitstream programs each cell to a speciﬁc function, e.g. a one bit addition, a register,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-679", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a multiplexer, and so on. By changing the bit-stream the functionality of the FPGA changes.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-680", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "From the viewpoint of the Register Transfer Level (RTL) the actual design process for either", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-681", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "FPGA or ASIC doesn’t differ that much. Similar design options are available: the designer", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-681", "line_number": 681, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-682", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 16", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-683", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 18", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-684", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can decide to go for serial or parallel architectures, making use of multiple design tricks to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-685", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "match the design with the requirements. The most well-known tricks are to use pipelining to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-686", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "increase throughput, or unrolling to reduce latency, time multiplexing to reduce area, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-687", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "From implementation viewpoint, at this register transfer abstraction level, a large body of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-688", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "knowledge and a large set of Electronic Design Automation (EDA) tools exist to map an appli-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-689", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cation onto a FPGA or ASIC platform [3]. Implementation results should be compared not only", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-690", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "on the number of operations, but also on memory requirements (program memory and data", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-691", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memory), throughput and latency requirements, energy and power requirements, bandwidth", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-692", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "requirements and the ease with which side-channel and fault attack countermeasures can", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-692", "line_number": 692, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-693", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "be added. Please note that this large body of knowledge exists for implementations that", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-694", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "focus on efﬁciency. However, when combining efﬁciency with security requirements, such as", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-695", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "constant time execution or other countermeasures, there is a huge lack of supporting EDA", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-696", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tools (see section 8).", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-697", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "5.2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-698", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Cryptographic algorithms at RTL level", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-699", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Cryptographic implementations are subdivided in several categories, enumerated below. The", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-700", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "details of the cryptographic algorithms themselves are discussed in the Cryptography CyBOK", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-701", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Knowledge Area [2]. Here only remarks related to the RTL implementation are made. In this", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-702", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "section only notes speciﬁc to the hardware implementations are made.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-703", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Secret key algorithms: both block ciphers and stream ciphers result usually in compact", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-704", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and fast implementations. Feistel ciphers are chosen for very area constrained designs", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-705", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "as the encryption and decryption hardware is the same. This is e.g. not the case for the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-706", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "AES algorithm for which encryption and decryption require different units.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-707", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Secret key: light-weight algorithms. For embedded devices, over the years, many light-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-708", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "weight algorithms have been developed and implemented, e.g. Present, Prince, Rectan-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-708", "line_number": 708, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-709", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "gle, Simon or Speck cipher. Focus in these cases is mostly on area cost. However, lately", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-709", "line_number": 709, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-710", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "light-weight has been extended to include also low power, low energy and especially", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-710", "line_number": 710, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-711", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "low-latency. Latency is deﬁned as the time difference between input clear text and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-711", "line_number": 711, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-712", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "corresponding encrypted output or MAC. Having a short latency is important in real-time", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-712", "line_number": 712, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-713", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "control systems, automotive, industrial IoT but also in memory encryption, control ﬂow", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-713", "line_number": 713, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-714", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "integrity applications etc. More knowledge will follow from the recent NIST call on", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-715", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "light-weight crypto [23].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-716", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Secret key: block ciphers by themselves are not directly applicable in security applica-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-717", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tion. They need to be combined with modes of operation to provide conﬁdentiality or", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-717", "line_number": 717, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-718", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "integrity, etc. (see the Cryptography CyBOK Knowledge Area [2]). In this context efﬁcient", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-718", "line_number": 718, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-719", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "implementations of authenticated encryption schemes are required: this is the topic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-719", "line_number": 719, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-720", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of the CAESAR competition [24]. From an implementation viewpoint, the sequential", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-720", "line_number": 720, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-721", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "nature of the authenticated encryption schemes makes it very difﬁcult to obtain high", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-721", "line_number": 721, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-722", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "throughputs as pipelining cannot directly be applied.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-722", "line_number": 722, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-723", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Hash algorithms require typically a much larger area compared to secret key algorithms.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-723", "line_number": 723, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-724", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Especially the SHA3 algorithm and its different versions are large in area and slow in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-725", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "execution. Therefore, light-weight hash algorithms are a topic of active research.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-726", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• One important hardware application of hash functions is the mining of cryptocurrencies,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-726", "line_number": 726, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-727", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "such as Bitcoin, Etherium, Litecoin and others, based on SHA2, SHA256, SHA3, etc. To", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-727", "line_number": 727, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-728", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "obtain the required high throughputs, massive parallelism and pipelining is applied. This", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-729", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 17", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-729", "line_number": 729, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-730", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 19", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-731", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is however limited as hash algorithms are recursive algorithms and thus there is an", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-732", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "upper bound on the amount of pipelining that can be applied [25]. Cryptocurrencies", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-732", "line_number": 732, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-733", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "form part of the more general technology of distributed ledgers, which is discussed in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-733", "line_number": 733, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-734", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the Distributed Systems Security CyBOK Knowledge Area [26].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-734", "line_number": 734, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-735", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• The computational complexity of public key algorithms is typically 2 or 3 orders of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-735", "line_number": 735, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-736", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "magnitude higher than secret key and thus its implementation 2 to 3 orders slower or", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-736", "line_number": 736, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-737", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "larger. Especially for RSA and Elliptic curve implementations, a large body of knowledge", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-738", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is available, ranging from compact [27] to fast, for classic and newer curves [28].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-738", "line_number": 738, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-739", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Algorithms resistant to attacks of quantum computers, aka post-quantum secure algo-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-739", "line_number": 739, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-740", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "rithms, are the next generation algorithms requiring implementation in existing CMOS", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-740", "line_number": 740, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-741", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ASIC and FPGA technology. Computational bottle-necks are the large multiplier struc-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-741", "line_number": 741, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-742", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tures, with/without the Number Theoretic Transform, the large memory requirements", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-743", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and the requirements on random numbers that follow speciﬁc distributions. Currently,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-744", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "NIST is holding a competition on post-quantum cryptography [29]. Thus it is expected", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-745", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that after the algorithms are decided, implementations in hardware will follow.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-746", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "• Currently, the most demanding implementations for cryptographic algorithms are those", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-747", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "used in homomorphic encryption schemes: the computational complexity, the size of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-748", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the multipliers and especially the large memory requirements are the challenges to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-749", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "address [30].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-751", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "SIDE-CHANNEL ATTACKS, FAULT ATTACKS AND", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-752", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "COUNTERMEASURES", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-753", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "This section ﬁrst provides an overview of physical attacks on implementations of cryptographic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-754", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "algorithms. The second part discusses a wide range of countermeasures and some open", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-755", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "research problems. Physical attacks, mostly side-channel and fault attacks, were originally of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-756", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "great concern to the developers of small devices that are in the hands of attackers, especially", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-756", "line_number": 756, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-757", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "smart-cards and pay-TV systems. The importance of these attacks and countermeasures is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-757", "line_number": 757, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-758", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "growing as more electronic devices are easily accessible in the context of the IoT.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-759", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "6.1", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-760", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Attacks", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-760", "line_number": 760, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-761", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "At the current state of knowledge, cryptographic algorithms have become very secure against", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-761", "line_number": 761, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-762", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "mathematical and cryptanalytical attacks: this is certainly the case for algorithms that are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-762", "line_number": 762, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-763", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "standardized or that have received an extensive review in the open research literature. Cur-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-763", "line_number": 763, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-764", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "rently, the weak link is mostly the implementation of algorithms in hardware and software.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-765", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Information leaks from the hardware implementation through side-channel and fault attacks.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-766", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "A distinction is made between passive or side-channel attacks versus active or fault attacks.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-767", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "A second distinction can be made based on the distance of the attacker to the device: attacks", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-768", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can occur remotely, close to the device still non-invasive to actual invasive attacks. More", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-769", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "details on several classes of attacks are below.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-770", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Passive Side Channel Attacks General side-channel attacks are passive observations of a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-771", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "compute platform. Through data dependent variations of execution time, power consumption", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-772", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "or electro-magnetic radiation of the device, the attacker can deduce information of secret", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-773", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "internals. Variations of execution time, power consumption or electro-magnetic radiations", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-774", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 18", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-775", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 20", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-776", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are typically picked up in close proximity of the device, while it is operated under normal", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-777", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "conditions. It is important to note that the normal operation of the device is not disturbed.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-778", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Thus the device is not aware that it is being attacked, which makes this attack quite powerful", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-779", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "[31].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-780", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Side channel attacks based on variations on power consumption have been extensively", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-781", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "studied. They are performed close to the device with access to the power supply or the power", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-782", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "pins. One makes a distinction between Simple Power Analysis (SPA), Differential and Higher", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-783", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Order Power Analysis (DPA), and template attacks. In SPA, the idea is to ﬁrst study the target", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-784", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "for features that depend on the key. E.g. a typical target in timing and power attacks are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-785", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "if-then-else branches that are dependent on key bits. In public key algorithm implementations,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-786", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "such as RSA or ECC, the algorithm runs sequentially through all key bits. When the if-branch", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-787", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "takes more or less computation time than the else-branch this can be observed from outside", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-788", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the chip. SPA attacks are not limited to public key algorithms, they have also been applied to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-789", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "secret key algorithms, or algorithms to generate prime numbers (in case they need to remain", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-790", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "secret). So with knowledge of the internal operation of the device, SPA only requires to collect", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-791", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "one or a few traces for analysis.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-792", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "With DPA, the attacker collects multiple traces, ranging from a few tens for unprotected", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-793", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "implementations to millions in case of protected hardware implementations. In this situation,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-794", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the attacker exploits the fact that the instantaneous power consumption depends on the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-795", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "data that is processed. The same operation, depending on the same unknown sub-key, will", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-796", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "result in different power consumption proﬁles if the data is different. The attacker will also", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-796", "line_number": 796, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-797", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "built a statistical model of the device to estimate the power consumption as a function of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-798", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the data and the different values of the subkey. Statistical analysis on these traces based on", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-799", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "correlation analysis, mutual information and other statistical tests are applied to correlate the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-800", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "measured values to the statistical model.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-801", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Side channel attacks based on Electro-Magnetic radiations have been recognized early-on", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-802", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "in the context of military communication and radio equipment. As a reaction, NATO and the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-803", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "governments of many countries have issued TEMPEST [32]. It consists of speciﬁcations on", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-804", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the protection of equipment against unintentional electro-magnetic radiation but also against", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-805", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "leakage of information through vibrations or sound. Electro-Magnetic radiation attacks can", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-806", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "be mounted from a distance, as explained above, but also at close proximity to the integrated", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-807", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "circuit. Electro-Magnetic probing on top of an integrated circuit can release very localized", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-808", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "information of speciﬁc parts of an IC by using a 2D stepper and ﬁne electro-magnetic probers.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-809", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Thus electro-magnetic evaluation has the possibility to provide more ﬁne grained leakage", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-810", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "information compared to power measurements.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-811", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Timing attacks are another subclass of side-channel attacks [33]. When the execution time of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-812", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a cryptographic calculation or a program handling sensitive data, varies as a function of the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-813", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "sensitive data, then this time difference can be picked up by the attacker. A timing attack can", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-813", "line_number": 813, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-814", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "be as simple as a key dependent different execution time of an if-branch versus an else-branch", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-814", "line_number": 814, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-815", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "in a ﬁnite state machine. Cache attacks, which abuse the time difference between a cache hit", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-816", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and a cache miss are an important class of timing attacks [34], [35], .", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-817", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "With a template attack, the attacker will ﬁrst create a copy or template of the target device", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-817", "line_number": 817, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-818", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "[36]. This template is used to study the behavior of the device for all or a large set of inputs", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-818", "line_number": 818, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-819", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and secret data values. One or a few samples of the target device are then compared to the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-820", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "templates in the database to deduce secret information from the device. Template attacks", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-821", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are typically used when the original device has countermeasures against multiple executions.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-822", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "E.g. it might have an internal counter to log the number of failed attempts. Templates can be", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-823", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 19", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-824", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 21", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-825", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "made based on timing, power or electro-magnetic information. As machine learning and AI", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-826", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "techniques become more powerful, so will the attack possibility with template attacks.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-827", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Micro-architectural Side-channels Processor architectures are very vulnerable to timing at-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-828", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tacks. The problem of information leaks and the difﬁculty of conﬁnement between programs", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-829", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "was already identiﬁed early on in [37]. Later timing variations in cache hits and misses became", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-830", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "an important class of timing attacks [38]. Recently gaining a lot of attention are the micro-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-831", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "architectural side-channel attacks, such as Spectre, Meltdown, Foreshadow. They are also", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-832", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "based on the observation of timing differences [6][38]. The strength of the attacks sits in the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-833", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "fact that they can be mounted remotely from software. Modern processors include multiple", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-834", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "optimization techniques to boost performance not only with caches, but also speculative", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-835", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "execution, out-of-order execution, branch predictors, etc. When multiple processes run on the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-836", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "same hardware platform, virtualization and other software techniques isolates the data of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-837", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the different parties in separate memory locations. Yet, through the out-of-order execution", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-838", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "or speculative execution (or many other variants) the hardware of the processor will access", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-839", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memory locations not intended for the process by means of so-called transient instructions.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-840", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "These instructions are executed but never committed. They have however touched memory", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-841", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "locations, which might create side channel effects, such as variations in access time, and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-842", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "thus leak information.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-843", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Active fault attacks Fault attacks are active manipulations of hardware compute platforms", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-844", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "[39]. The result is that the computation itself or the program control ﬂow is disturbed. Faulty or", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-845", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "no outputs are released. Even if no output is released or the device resets itself, this decision", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-845", "line_number": 845, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-846", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "might leak sensitive information. One famous example is published in [40]: it describes an", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-846", "line_number": 846, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-847", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "RSA signature implementation which makes use of the Chinese Remainder Theorem (CRT).", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-847", "line_number": 847, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-848", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "With one faulty and one correct result signature, and some simple mathematical calculations,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-849", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the secret signing key can be derived. Physical fault-attacks could be a simple clock glitching,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-850", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "power glitching, heating up or cooling down a device. These require close proximity to the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-851", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "device but are non-invasive.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-852", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "With scaling of memories, more attack surfaces appear. A very speciﬁc attack on DRAM", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-853", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memories, is the RowHammer attack [41, 42]. By repeating reading speciﬁc locations in DRAM", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-854", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "memory, neighboring locations will loose their values. Thus by hammering certain locations,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-855", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "bit ﬂips will occur in nearby locations.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-856", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "With more expensive equipment, and with opening the lid of the integrated circuit or etching", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-857", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the silicon down, even more detailed information of the circuit can be obtained. Equipment", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-858", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that has been used include optical fault [43], laser attacks [44], Focused Ion Beam (FIB), a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-859", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Scanning Electron Microscope (SEM) and other. The latter are typically equipment that has", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-860", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "been designed for chip reliability and failure analysis. This equipment can also be used or", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-861", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "misused for reverse engineering.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-862", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 20", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-863", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 22", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-864", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "6.2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-865", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Countermeasures", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-866", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "There are no generic countermeasures that resist all classes of side-channel attacks. De-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-867", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "pending on the threat model (remote/local access, passive/active, etc.) and the assumptions", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-868", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "made on the trusted computing base (i.e. what is and what is not included in the root of trust),", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-869", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "countermeasures have been proposed at several levels of abstraction. The most important", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-870", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "categories are summarized below.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-871", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "To resist timing attacks, the ﬁrst objective is to provide hardware that executes the application", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-872", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "or program in constant time independent of secret inputs, keys and internal state. Depending", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-873", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "on the time granularity of the measurement equipment of the attacker, constant time counter-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-874", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "measures also need to be more ﬁne grained. At the processor architecture level, constant", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-875", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "time means a constant number of instructions. At the RTL level, constant time means a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-876", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "constant number of clock cycles. At logic and circuit level, constant time means a constant", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-877", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "logic depth or critical path independent of the input data. At instruction level, constant time", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-877", "line_number": 877, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-878", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can be obtained by balancing execution paths and adding dummy instructions. Sharing of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-878", "line_number": 878, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-879", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "resources, e.g. through caches, make constant time implementations extremely difﬁcult to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-879", "line_number": 879, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-880", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "obtain.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-880", "line_number": 880, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-881", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "At RTL level, we need to make sure that all instructions run in the same number of clock", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-881", "line_number": 881, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-882", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cycles. dummy operations or dummy gates, depending on the granularity level. Providing", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-882", "line_number": 882, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-883", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "constant time RTL level and gate level descriptions is however a challenge as design tools,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-883", "line_number": 883, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-884", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "both hardware and software compilers, will for performance reasons synthesize away the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-884", "line_number": 884, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-885", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "dummy operations or logic which were added to balance the computations.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-885", "line_number": 885, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-886", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "As many side-channel attacks rely on a large number of observations or samples, randomisa-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-886", "line_number": 886, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-887", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tion is a popular countermeasure. It is used to protect against power, electro-magnetic and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-887", "line_number": 887, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-888", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "timing side-channel attacks. Randomisation is a technique that can be applied at algorithm", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-888", "line_number": 888, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-889", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "level: it is especially popular for public key algorithms, which apply techniques such as scalar", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-889", "line_number": 889, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-890", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "blinding, or message blinding [45]. Randomisation applied at register transfer and gate level", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-890", "line_number": 890, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-891", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is called masking. Masking schemes randomise intermediate values in the calculations so", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-891", "line_number": 891, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-892", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that their power consumption can no longer be linked with the internal secrets. A large set of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-892", "line_number": 892, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-893", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "papers on gate level masking schemes is available, ranging from simple Boolean masking to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-893", "line_number": 893, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-894", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "threshold implementations that are provable secure under certain leakage models [46]. Ran-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-894", "line_number": 894, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-895", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "domisation has been effective in practice especially as a public key implementation protection", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-895", "line_number": 895, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-896", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "measure. The protection of secret key algorithms by masking is more challenging. Some", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-896", "line_number": 896, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-897", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "masking schemes require a huge amount of random numbers, others assume leakage models", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-897", "line_number": 897, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-898", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that do not always correspond to reality. In this context, novel cryptographic techniques", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-898", "line_number": 898, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-899", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "summarized under the label leakage resilient cryptography, are developed that are inherently", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-899", "line_number": 899, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-900", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "resistant against side-channel attacks [47, 48]. At this stage, there is still a gap between theory", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-900", "line_number": 900, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-901", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and practice.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-901", "line_number": 901, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-902", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Hiding is another major class of countermeasures. The idea is to reduce the signal to noise", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-902", "line_number": 902, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-903", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ratio by reducing the signal strength. Shielding in the context of TEMPEST is one such example.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-903", "line_number": 903, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-904", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Similarly, at gate level, reducing the power signature or electro-magnetic signature of standard", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-904", "line_number": 904, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-905", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cells or logic modules, will increase the resistance against power or electro-magnetic attacks.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-905", "line_number": 905, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-906", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Simple techniques such as using a jittery or drifting clock, and large decoupling capacitances", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-906", "line_number": 906, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-907", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "will also reduce the signal to noise ratio.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-907", "line_number": 907, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-908", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Sometimes solutions for leaking at one abstraction level, e.g. power side channels, can be", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-908", "line_number": 908, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-909", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "addressed at a different abstraction level. Therefore, if there is a risk that an encryption key", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-909", "line_number": 909, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-910", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "leaks from an embedded device, a cryptographic protocol that changes the key at a sufﬁciently", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-910", "line_number": 910, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-911", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 21", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-911", "line_number": 911, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-912", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 23", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-912", "line_number": 912, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-913", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "high frequency, will also avoid side-channel information leakage.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-913", "line_number": 913, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-914", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "General purpose processors such as CPUs, GPUs, and micro-controllers can not be modiﬁed", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-914", "line_number": 914, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-915", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "once fabricated. Thus protecting against micro-architectural attacks after fabrication by", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-915", "line_number": 915, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-916", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "means of software patches and updates is extremely difﬁcult and mostly at the cost of reduced", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-916", "line_number": 916, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-917", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "performance [6]. Micro-code updates are also a form of software, i.e. ﬁrmware update and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-917", "line_number": 917, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-918", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "not a hardware update. The main difference is that the translation from instructions to micro-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-918", "line_number": 918, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-919", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "code is a company secret, and thus for the user it looks like a hardware update. Providing", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-919", "line_number": 919, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-920", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "generic solutions to programmable hardware is a challenge as it is unknown beforehand which", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-920", "line_number": 920, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-921", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "application will run. Solutions to this problem will be a combined effort between hardware", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-921", "line_number": 921, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-922", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and software techniques.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-922", "line_number": 922, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-923", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Protection against fault attacks are made at the register transfer level, as well as at the circuit", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-923", "line_number": 923, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-924", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "level. At RTL, protection against fault attacks is mostly based on redundancy either in space or", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-924", "line_number": 924, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-925", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "in time and by adding checks based on coding, such as parity checks. The price is expensive", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-925", "line_number": 925, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-926", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "as calculations are performed multiple times. One problem with adding redundancy is that it", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-926", "line_number": 926, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-927", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "increases the attack surface of side-channels. Indeed, due to the redundant calculations, the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-927", "line_number": 927, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-928", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attacker has more traces available to perform time, power or electro-magnetic side-channel", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-928", "line_number": 928, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-929", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attacks [45]. At circuit level, monitors on the clock or power supply, might detect deviations", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-929", "line_number": 929, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-930", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "from normal operations and raise an alarm.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-930", "line_number": 930, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-931", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Many type of circuit level sensors are added to integrated circuits. Examples are light sensors", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-931", "line_number": 931, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-932", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that detect that a lid of a package has been opened. Mesh metal sensors which are laid-out", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-932", "line_number": 932, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-933", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "in top level metal layers can detect probing attacks. Temperature sensors detect heating or", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-933", "line_number": 933, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-934", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cooling of the integrated circuit. Antenna sensors to detect electro-magnetic probes close to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-934", "line_number": 934, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-935", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the surface have been developed: these sensors measure a change in electro-magnetic ﬁelds.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-935", "line_number": 935, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-936", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "And sensors that detect manipulation of the power supply or clock can be added to the device.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-936", "line_number": 936, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-937", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Note that adding sensors to detect active manipulation can again leak extra information to", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-937", "line_number": 937, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-938", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the side channel attacker.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-938", "line_number": 938, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-939", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Joint countermeasures against side-channel and fault attacks are challenging and an active", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-939", "line_number": 939, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-940", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "area of research.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-940", "line_number": 940, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-942", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ENTROPY GENERATING BUILDING BLOCKS: RANDOM", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-942", "line_number": 942, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-943", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "NUMBERS, PHYSICALLY UNCLONABLE FUNCTIONS", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-943", "line_number": 943, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-944", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Sources of entropy are essential for security and privacy protocols. In this section two", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-944", "line_number": 944, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-945", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "important sources of entropy related to silicon technology are discussed: random number", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-945", "line_number": 945, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-946", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "generators and physically unclonable functions.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-946", "line_number": 946, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-947", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 22", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-947", "line_number": 947, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-948", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 24", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-948", "line_number": 948, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-949", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "7.1", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-949", "line_number": 949, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-950", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Random number generation", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-950", "line_number": 950, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-951", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Security and privacy rely on strong cryptographic algorithms and protocols. A source of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-951", "line_number": 951, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-952", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "entropy is essential in these protocols: random numbers are used to generate session keys,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-952", "line_number": 952, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-953", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "nonces, initialization vectors, to introduce freshness, etc. Random numbers are also used", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-953", "line_number": 953, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-954", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to create masks in masking countermeasures, random shares in multi party computation,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-954", "line_number": 954, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-955", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "zero-knowledge proofs, etc. In this section the focus is on cryptographically secure ran-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-955", "line_number": 955, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-956", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "dom numbers as used in security applications. Random numbers are also used outside", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-956", "line_number": 956, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-957", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cryptography, e.g. in gaming, lottery applications, stochastic simulations, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-957", "line_number": 957, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-958", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In general, random numbers are subdivided in two major classes: the Pseudo Random Number", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-958", "line_number": 958, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-959", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Generator (PRNG) also called Deterministic Random Bit Generator (DRBG) and the True", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-959", "line_number": 959, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-960", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Random Number Generator (TRNG) or Non-Deterministic Random Bit Generator (NRBG).", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-960", "line_number": 960, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-961", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The design, properties and testing of random numbers is described in detail by important", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-961", "line_number": 961, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-962", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "standards, issued in the US by NIST. NIST has issued the NIST800-90A for deterministic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-962", "line_number": 962, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-963", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "random number generators, the NIST800-90B for entropy sources, and NIST800-90C for", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-963", "line_number": 963, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-964", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "random bit generation constructions [49], [50] [51] 1. In Germany and by extension in most", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-964", "line_number": 964, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-965", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of Europe, the German BSI has issued two important standards: the AIS-20 for functionality", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-965", "line_number": 965, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-966", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "classes and evaluation criteria for deterministic random number generators and the AIS-31", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-966", "line_number": 966, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-967", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "for physical random number generators [52, 53, 54].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-967", "line_number": 967, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-968", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "An ideal RNG should generate all numbers with equal probability. Secondly, these numbers", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-968", "line_number": 968, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-969", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "should be independent from previous or next numbers generated by the RNG, called forward", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-969", "line_number": 969, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-970", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and backward secrecy. The probabilities are veriﬁed with statistical tests. Each standard", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-970", "line_number": 970, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-971", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "includes a large set of statistical tests aimed at ﬁnding statistical weaknesses. Not being", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-971", "line_number": 971, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-972", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "able to predict future values or derive previous values is important not only in many security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-972", "line_number": 972, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-973", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "applications, e.g. when this is used for key generation, but also in many gaming and lottery", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-973", "line_number": 973, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-974", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "applications.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-974", "line_number": 974, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-975", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Pseudo-random number generators are deterministic algorithms that generate a sequence of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-975", "line_number": 975, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-976", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "bits or numbers that look random but are generated by a deterministic process. Since a PRNG", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-976", "line_number": 976, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-977", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is a deterministic process, when it starts with the same initial value, then the same sequence", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-977", "line_number": 977, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-978", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of numbers will be generated. Therefore it is essential that PRNG starts with a different", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-978", "line_number": 978, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-979", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "start-up value each time the PRNG is initiated. This initial seed can either be generated by a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-979", "line_number": 979, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-980", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "slow true random number generated or at minimum by a non-repeating value, e.g. as provided", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-980", "line_number": 980, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-981", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "by a monotonic increasing counter. A PRNG is called cryptographically secure if the attacker,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-981", "line_number": 981, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-982", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "who learns part of the sequence, is not able to compute any previous or future outputs.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-982", "line_number": 982, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-983", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Cryptographically secure PRNGs rely on cryptographic algorithms to guarantee this forward", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-983", "line_number": 983, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-984", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and backward secrecy. Forward secrecy requires on top a regular reseeding to introduce new", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-984", "line_number": 984, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-985", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "freshness into the generator. Hybrid RNG have an additional non-deterministic input to the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-985", "line_number": 985, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-986", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "PRNG.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-986", "line_number": 986, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-987", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "PRNGs provide conditional security based on the computational complexity of the underlying", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-987", "line_number": 987, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-988", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cryptographic algorithms. See the Cryptography CyBOK Knowledge Area [2] for more details.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-988", "line_number": 988, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-989", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In contrast, ideal true random number generators provide unconditional security as they are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-989", "line_number": 989, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-990", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "based on unpredictable physical phenomena. Thus their security is guaranteed independent", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-990", "line_number": 990, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-991", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of progress in mathematics and cryptanalysis.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-991", "line_number": 991, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-992", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The core of a true random number generator consists of an entropy source, which is a physical", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-992", "line_number": 992, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-993", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "phenomena with a random behavior. In electronic circuits, noise or entropy sources are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-993", "line_number": 993, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-994", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "1NIST800-90C does not exist as a standard yet.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-994", "line_number": 994, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-995", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 23", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-995", "line_number": 995, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-996", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 25", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-996", "line_number": 996, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-997", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "usually based on thermal noise, jitter and metastability. These noise sources are never perfect:", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-997", "line_number": 997, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-998", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the bits they generate might show bias or correlation or other variations. Hence they don’t", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-998", "line_number": 998, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-999", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "have full entropy. Therefore, they are typically followed by entropy extractors or conditioners.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-999", "line_number": 999, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1000", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "These building blocks improve the entropy per bit of output. But as the entropy extractor are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1000", "line_number": 1000, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1001", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "deterministic processes, they cannot increase the total entropy. So the output length will be", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1001", "line_number": 1001, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1002", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "shorter than the input length.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1002", "line_number": 1002, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1003", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Due to environmental conditions, e.g. due to temperature or voltage variations, the quality", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1003", "line_number": 1003, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1004", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of the generated numbers might vary over time. Therefore, the standards describe speciﬁc", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1004", "line_number": 1004, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1005", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tests that should be applied at the start and continuously during the process of generating", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1005", "line_number": 1005, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1006", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "numbers. One can distinguish three main categories of tests. The ﬁrst one is the total failure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1006", "line_number": 1006, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1007", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "test, applied at the source of entropy. The second ones are online health tests to monitor the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1007", "line_number": 1007, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1008", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "quality of the entropy extractors. The third ones are tests for the post-processed bits. The", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1008", "line_number": 1008, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1009", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "requirements for these tests are well described in the different standards and specialized text", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1009", "line_number": 1009, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1010", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "books [55].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1010", "line_number": 1010, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1011", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The challenge in designing TRNGs is ﬁrst to provide a clear and convincing proof of the entropy", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1011", "line_number": 1011, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1012", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "source, second the design of online tests which at the same are compact and can detect a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1012", "line_number": 1012, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1013", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "wide range of defects [56]. The topic of attacks, countermeasures and sensors for TRNGs,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1013", "line_number": 1013, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1014", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "especially in the context of IoT and embedded devices, is an active research topic.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1014", "line_number": 1014, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1015", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "7.2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1015", "line_number": 1015, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1016", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Physically Unclonable Functions", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1016", "line_number": 1016, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1017", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "From a hardware perspective, Physically Unclonable Functions (PUFs), are circuits and tech-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1017", "line_number": 1017, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1018", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "niques to derive unique features from silicon circuits, similar to human biometrics [57]. The", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1018", "line_number": 1018, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1019", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "manufacturing of silicon circuits results in unique process variations which cannot be physi-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1019", "line_number": 1019, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1020", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cally cloned. The basic idea of PUFs is that these unique manufacturing features are magniﬁed", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1020", "line_number": 1020, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1021", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and digitized so that they can be used in security applications similar to the use of ﬁngerprints", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1021", "line_number": 1021, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1022", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "or other biometrics. Process and physical variations such as doping ﬂuctuations, line or edge", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1022", "line_number": 1022, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1023", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "widths of interconnect wires, result in variations of threshold voltages, transistor dimensions,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1023", "line_number": 1023, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1024", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "capacitances, etc. Thus circuits are created that are sensitive to and amplify these variations.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1024", "line_number": 1024, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1025", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The major security application for PUFs is to derive unique device speciﬁc keys, e.g. for usage", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1025", "line_number": 1025, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1026", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "in an IoT device or smart card. Traditionally, this storage of device unique keys is done in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1026", "line_number": 1026, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1027", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "non-volatile memory, as the key has to remain in the chip even when the power is turned-off.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1027", "line_number": 1027, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1028", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Non-volatile memory requires however extra fabrication steps, which makes chips with non-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1028", "line_number": 1028, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1029", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "volatile memory more expense than regular standard CMOS chips. Thus PUFs are promised", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1029", "line_number": 1029, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1030", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "as cheap alternative for secure non-volatile memory, because the unique silicon ﬁngerprint", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1030", "line_number": 1030, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1031", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is available without the extra processing steps. Indeed, each time the key is needed, it can", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1031", "line_number": 1031, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1032", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "be read from the post-processed PUF and directly used in security protocols. They can also", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1032", "line_number": 1032, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1033", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "replace fuses, which are large and their state is relatively easy to detect under a microscope.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1033", "line_number": 1033, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1034", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The second security application is to use PUFs in identiﬁcation applications, e.g. for access", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1034", "line_number": 1034, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1035", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "control or tracking of goods. The input to a PUF is called a challenge, the output the response.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1035", "line_number": 1035, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1036", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The ideal PUF has an exponential number of unique challenge response pairs, exponential in", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1036", "line_number": 1036, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1037", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the number of circuit elements. The uniqueness of PUFs is measured by the inter-distance", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1037", "line_number": 1037, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1038", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "between different PUFs seeing the same challenge. The ideal PUF has stable responses:", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1038", "line_number": 1038, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1039", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "it replies with the same response, i.e. there is no noise in the responses. Moreover, PUF", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1039", "line_number": 1039, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1040", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "responses should be unpredictable and physically unclonable.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1040", "line_number": 1040, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1041", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The ideal PUF unfortunately does not exist. In literature, two main classes of PUFs are deﬁned,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1041", "line_number": 1041, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1042", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 24", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1042", "line_number": 1042, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1043", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 26", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1043", "line_number": 1043, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1044", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "characterized by the number of challenge-response pairs they can generate. So-called weak", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1044", "line_number": 1044, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1045", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "PUFs are circuits with a ﬁnite number of elements, with each element providing a high amount", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1045", "line_number": 1045, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1046", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "of entropy. The number of possible challenge-response pairs grows typically linear with the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1046", "line_number": 1046, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1047", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "area of the integrated circuit. Hence they are called weak PUFs. The most well known example", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1047", "line_number": 1047, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1048", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is the SRAM PUF [58]. These PUFs are typically used for key generation. The raw PUF output", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1048", "line_number": 1048, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1049", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "material is not directly usable for key generation as the PUF responses are affected by noise.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1049", "line_number": 1049, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1050", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Indeed, subsequent readings of the same PUF might result in slightly varying noisy responses,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1050", "line_number": 1050, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1051", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "typically up to 20%. Thus after the entropy extraction follows secure sketch (similar to error", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1051", "line_number": 1051, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1052", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "correction) circuits to eliminate the noise and compress the entropy to generate a full entropy", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1052", "line_number": 1052, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1053", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "key [59]. The challenge for the PUF designer is to come up with process variations and circuits", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1053", "line_number": 1053, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1054", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "that can be used as key material, but which are not sensitive to transient noise. A second", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1054", "line_number": 1054, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1055", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "challenge is to keep all the post-processing modules compact so that the key-generation PUF", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1055", "line_number": 1055, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1056", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "can be included in embedded IoT devices.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1056", "line_number": 1056, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1057", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The second class are the so-called strong PUFs. In this case, the number of challenge-response", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1057", "line_number": 1057, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1058", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "pairs grows large, ideally exponential, with the silicon area. The most well-known example is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1058", "line_number": 1058, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1059", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the arbiter PUF [60]. A small number of silicon elements are combined together, e.g. to create", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1059", "line_number": 1059, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1060", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "a chain of multiplexers or comparators, so that simple combinations of the elements create", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1060", "line_number": 1060, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1061", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the large challenge-response space. Also in this case, the effects of noise in the circuits", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1061", "line_number": 1061, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1062", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "needs to be taken into account. Strong PUFs are promised to be useful in authentication", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1062", "line_number": 1062, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1063", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "applications, e.g. for access control. Each time a challenge is applied to the PUF, a response", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1063", "line_number": 1063, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1064", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "unique to the chip will be sent. The veriﬁer will accept the response if it can be uniquely tied", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1064", "line_number": 1064, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1065", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "to the prover. This requires that the PUF responses are registered in a form of a database", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1065", "line_number": 1065, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1066", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "beforehand during an enrollment phase.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1066", "line_number": 1066, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1067", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The problem with strong PUFs is that there is a strong correlation between different challenge-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1067", "line_number": 1067, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1068", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "response pairs of most circuits proposed in literature. Hence all of these circuits are broken", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1068", "line_number": 1068, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1069", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "with machine learning techniques [61] and can not be used for authentication purposes. The", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1069", "line_number": 1069, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1070", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "fundamental problem is that very basic, mostly linear operations are used to combine PUF", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1070", "line_number": 1070, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1071", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "elements, which makes them easy targets for machine learning attacks. Ideally, these should", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1071", "line_number": 1071, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1072", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "be cryptographic or other computationally hard operations resistant to machine learning:", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1072", "line_number": 1072, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1073", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "unfortunately these cannot tolerate noise. Light-weight PUF based security protocols are an", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1073", "line_number": 1073, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1074", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "active area of research.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1074", "line_number": 1074, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1076", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "HARDWARE DESIGN PROCESS", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1076", "line_number": 1076, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1077", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "In this section, several hardware security topics are described which are directly related to the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1077", "line_number": 1077, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1078", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "lower design abstraction layers. One is the trust in the hardware design process itself. Directly", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1078", "line_number": 1078, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1079", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "related to this, is the problem of Trojan circuits. Also part of the hardware design process are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1079", "line_number": 1079, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1080", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "circuit level techniques for camouﬂaging, logic locking, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1080", "line_number": 1080, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1081", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 25", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1081", "line_number": 1081, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1082", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 27", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1082", "line_number": 1082, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1083", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "8.1", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1083", "line_number": 1083, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1084", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Design and fabrication of silicon integrated circuits", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1084", "line_number": 1084, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1085", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "It is important to note that the hardware design process itself also needs to be trusted.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1085", "line_number": 1085, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1086", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Because of its design complexity, design at each abstraction layer relies on Electronic Design", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1086", "line_number": 1086, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1087", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Automation (EDA) tools. The design, fabrication, packaging and test of silicon integrated", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1087", "line_number": 1087, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1088", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "circuits is an international engagement: silicon foundries are mostly located in Asia. Silicon", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1088", "line_number": 1088, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1089", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "design tools are most developed in the US, and silicon testing and packaging usually occur all", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1089", "line_number": 1089, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1090", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "over the world. For chips that end-up in critical infrastructure, such as telecommunication,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1090", "line_number": 1090, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1091", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "military, aviation, trust and veriﬁcation of the complete design cycle is essential.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1091", "line_number": 1091, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1092", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Since silicon foundries and mask making are extremely expensive, very few countries and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1092", "line_number": 1092, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1093", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "companies can still afford it and a huge consolidation has and is taking place in the industry.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1093", "line_number": 1093, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1094", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "For critical infrastructure, governments demand more tools and techniques to increase the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1094", "line_number": 1094, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1095", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "trustworthiness of this international design process. On this topic, large research projects", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1095", "line_number": 1095, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1096", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are deﬁned to come up with methods and tools to increase the trustworthiness of the design", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1096", "line_number": 1096, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1097", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "process and especially to assess the risk of Trojan insertions during the design process.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1097", "line_number": 1097, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1098", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "8.2", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1098", "line_number": 1098, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1099", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Trojan circuits", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1099", "line_number": 1099, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1100", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Trojan circuits are logic or gates added to large integrated circuits. As they are not part of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1100", "line_number": 1100, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1101", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the speciﬁed functionality, they are difﬁcult to detect. They rely on the fact that they are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1101", "line_number": 1101, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1102", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "extremely small in comparison with the large size of integrated circuits and SoCs. Trojan", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1102", "line_number": 1102, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1103", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "circuits are classiﬁed according to three main criteria [62, 63]. The ﬁrst one is the physical", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1103", "line_number": 1103, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1104", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "characteristics of the Trojan, i.e. how is the Trojan inserted into the circuit. E.g. does it", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1104", "line_number": 1104, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1105", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "requires logic modiﬁcations or only layout modiﬁcations. The second one is the activation", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1105", "line_number": 1105, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1106", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "characteristic: will the Trojan be turned on by an internal or external event, etc. The third", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1106", "line_number": 1106, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1107", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "characteristic classiﬁes the type of action taken by the Trojan, e.g. will it leak information or", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1107", "line_number": 1107, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1108", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "will it destroy functionality, etc. The knowledge area on this topic is summarized in [62, 63].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1108", "line_number": 1108, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1109", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "8.3", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1109", "line_number": 1109, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1110", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Circuit level techniques", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1110", "line_number": 1110, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1111", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "To avoid visual inspection, circuit level camouﬂaging techniques are introduced [64]. These", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1111", "line_number": 1111, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1112", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "are standard cells or other modules that visually look the same, or they look camouﬂaged by", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1112", "line_number": 1112, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1113", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "random extra material. This is done to avoid visual inspection and reverse engineering based", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1113", "line_number": 1113, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1114", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "on visual inspection.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1114", "line_number": 1114, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1115", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Another techniques to avoid loss of intellectual property is logic locking [65]. With this tech-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1115", "line_number": 1115, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1116", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "nique, extra gates are added to a circuit with a secret input. Only when the correct key is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1116", "line_number": 1116, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1117", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "applied to the secret gates, will the circuit perform the correct functionality. This is an active", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1117", "line_number": 1117, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1118", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "research topic with logic locking schemes being proposed and attacked, with SAT solvers", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1118", "line_number": 1118, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1119", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "being a very useful tool in attacking the circuits.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1119", "line_number": 1119, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1120", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 26", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1120", "line_number": 1120, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1121", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 28", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1121", "line_number": 1121, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1122", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "8.4", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1122", "line_number": 1122, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1123", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Board Level Security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1123", "line_number": 1123, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1124", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Integrated circuits are placed together on Printer Circuit Boards (PCBs). Many of the attacks", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1124", "line_number": 1124, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1125", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and countermeasures mentioned before for integrated circuits, can be repeated for PCBs", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1125", "line_number": 1125, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1126", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "albeit at a different scale. While integrated circuits provide some level of protection because", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1126", "line_number": 1126, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1127", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "they are encapsulated in packages and use much smaller CMOS technologies, PCB’s are", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1127", "line_number": 1127, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1128", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "less complex and somewhat easier to access. Therefore, for PCB’s special coatings, and", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1128", "line_number": 1128, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1129", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "mechanical tamper evident and tamper resistant protection mechanisms could be provided.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1129", "line_number": 1129, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1130", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "There have been some concerns that Trojan circuits could also be included at the board level.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1130", "line_number": 1130, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1131", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "8.5", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1131", "line_number": 1131, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1132", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Time", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1132", "line_number": 1132, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1133", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The concept of time and the concept of sequence of events are essential in security protocols.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1133", "line_number": 1133, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1134", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The TCG identiﬁes three types of sequencing: a monotonic counter, a tick counter and actual", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1134", "line_number": 1134, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1135", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "trusted time [5]. A monotonic counter always increases, but the wall clock time between two", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1135", "line_number": 1135, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1136", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "increments is unknown. The tick counter increases with a set frequency. It only increases", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1136", "line_number": 1136, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1137", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "when the power is on. At power-off the tick counter will reset. Therefore the tick counter is", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1137", "line_number": 1137, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1138", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "linked with a nonce and methods are foreseen to link this with a real wall clock time. Trusted", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1138", "line_number": 1138, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1139", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "time is the most secure. It makes sure that there is a link between the tick counter and the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1139", "line_number": 1139, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1140", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "real wall clock time. From a hardware viewpoint it will require non-volatile memory, counters,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1140", "line_number": 1140, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1141", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "crystals, continuous power, and an on chip clock generator. The connection to a real wall", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1141", "line_number": 1141, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1142", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "clock will require synchronization and an actual communication channel.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1142", "line_number": 1142, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1143", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "The importance of time is placed in a wider context in the Distributed Systems Security CyBOK", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1143", "line_number": 1143, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1144", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Knowledge Area [26].", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1144", "line_number": 1144, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1146", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "CONCLUSION", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1146", "line_number": 1146, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1147", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Hardware security is a very broad topic, covering many different topics. In this chapter, a", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1147", "line_number": 1147, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1148", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "classiﬁcation is made based on the different design abstraction layers. At each abstraction", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1148", "line_number": 1148, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1149", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "layer, the threat model, root of trust and security goals are identiﬁed.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1149", "line_number": 1149, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1150", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Because of the growth of IoT, edge and cloud computing, the importance of hardware security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1150", "line_number": 1150, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1151", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is growing. Yet, in many cases hardware security is in conﬂict with other performance optimi-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1151", "line_number": 1151, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1152", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "sations, such as low power or limited battery operated conditions. In these circumstances,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1152", "line_number": 1152, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1153", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "performance optimization is the most important design task. Yet it is also the most important", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1153", "line_number": 1153, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1154", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "cause of information leakage. This is the case at all abstraction layers: instruction level,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1154", "line_number": 1154, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1155", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "architecture level and logic and circuit level.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1155", "line_number": 1155, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1156", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Another trend is that hardware is becoming more ‘soft’. This is an important trend in processor", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1156", "line_number": 1156, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1157", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "architecture, where FPGA functionality is added to processor architectures. The fundamental", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1157", "line_number": 1157, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1158", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "assumption that hardware is immutable is lost here. This will create a whole new class of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1158", "line_number": 1158, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1159", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "attacks.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1159", "line_number": 1159, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1160", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "A last big challenge for hardware security is the lack of EDA tools to support hardware security.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1160", "line_number": 1160, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1161", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "EDA tools are made for performance optimization and security is usually an afterthought. An", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1161", "line_number": 1161, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1162", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "added challenge is that it is difﬁcult to measure security and thus difﬁcult to balance security", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1162", "line_number": 1162, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1163", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "versus area, throughput or power optimisations.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1163", "line_number": 1163, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1164", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 27", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1164", "line_number": 1164, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1165", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "## Page 36", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1165", "line_number": 1165, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1166", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "TXT Trusted Execution Technology.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1166", "line_number": 1166, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1167", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "VLSI Very Large Scale Integration.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1167", "line_number": 1167, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1168", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "VM Virtual Machine.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1168", "line_number": 1168, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1169", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "GLOSSARY", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1169", "line_number": 1169, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1170", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ASIC Application Speciﬁc Integrated Circuit is one class on integrated circuits, where the", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1170", "line_number": 1170, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1171", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "circuit is tuned to a speciﬁc application or set of applications. E.g. a TPM is a dedicated", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1171", "line_number": 1171, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1172", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ASIC for security applications .", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1172", "line_number": 1172, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1173", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "CMOS Complementary Metal Oxide Semiconductor technology is the most popular silicon", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1173", "line_number": 1173, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1174", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "technology to make integrated circuits. It consitst of complementary PMOS and NMOS", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1174", "line_number": 1174, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1175", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "transistors. Its main advantages are that it has a very low static power consumption", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1175", "line_number": 1175, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1176", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and relative robust operation. Hence it made it possible to integrate a large number of", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1176", "line_number": 1176, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1177", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "transistors (millions to billions) into one integrated circuit.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1177", "line_number": 1177, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1178", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "CPU Central Processing Unit is a general purpose integrated circuit made to execute a pro-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1178", "line_number": 1178, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1179", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "gram. It typically consists of an arithmetic unit, a program control unit, a bus structure", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1179", "line_number": 1179, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1180", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "and storage for code and data. Many types and variations exists. One SOC could contain", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1180", "line_number": 1180, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1181", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "one or more CPU cores with peripherals, extra memory, etc.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1181", "line_number": 1181, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1182", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "CyBOK Refers to the Cyber Security Body of Knowledge.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1182", "line_number": 1182, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1183", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "DRAM DRAM is Dynamic Random Access Memory. Very popular because of its high density.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1183", "line_number": 1183, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1184", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "It requires only one transistor and one small capacitance to store one bit of data. It", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1184", "line_number": 1184, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1185", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "requires regular refreshing. It looses its value when the power supply is turned off.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1185", "line_number": 1185, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1186", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "FPGA A Field Programmable Gate Array or FPGA is a specialized integrated circuit that con-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1186", "line_number": 1186, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1187", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "tains conﬁgurable logic, which can still be programmed after fabrication. Programming", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1187", "line_number": 1187, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1188", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "is done by loading a bitstream which conﬁgures each of the programmable logic gates", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1188", "line_number": 1188, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1189", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "individually.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1189", "line_number": 1189, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1190", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "GPU Graphics Processing Unit is a specialized programmable integrated circuit. Its com-", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1190", "line_number": 1190, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1191", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "ponents (arithmetic units, instruction set, memory conﬁguration, bus structure) are all", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1191", "line_number": 1191, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1192", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "optimized to accelerate graphics, video and image processing applications.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1192", "line_number": 1192, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1193", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "HDL A Hardware Description Language is a special language to describe digital hardware at", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1193", "line_number": 1193, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1194", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "the register transfer level. Most well known languages are VHDL and Verilog.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1194", "line_number": 1194, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1195", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "IC An Integrated Circuit is an electronic device that contains a large amount of electronic", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1195", "line_number": 1195, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1196", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "components, mostly transistors integrated into one piece of semiconductor material,", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1196", "line_number": 1196, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1197", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "usually CMOS silicon. A common name is a ‘chip’ or a ‘silicon chip’ .", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1197", "line_number": 1197, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1198", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "PCB A Printed Circuit Board is a specialized board which holds the different integrated circuits.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1198", "line_number": 1198, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1199", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "It is made of an insulated material with copper wiring to connect the pins of different", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1199", "line_number": 1199, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1200", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "integrated circuits with each other and the outside.", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1200", "line_number": 1200, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1201", "filename": "Hardware_Security_v1.0.1_processed.txt", "content": "Page 35", "metadata": {"filename": "Hardware_Security_v1.0.1_processed.txt", "chunk_id": "line-1201", "line_number": 1201, "source": "知识库\\output\\Hardware_Security_v1.0.1_processed.txt"}}
