archivebox的使用在那种简报或者每一期的总结的网页上会把其中的每个链接都进行处理，拿取到的数据有问题的，看怎么能将其过滤掉，这一类的网页就不需要分析了

https://securityaffairs.com/179018/security/security-affairs-malware-newsletter-round-49.html   



替换检索模型为BGE,以及Faiss为向量数据库或者chroma为向量数据库


BAAI/bge-base-en-v1.5  要比 sentence-transformers/all-MiniLM-L6-v2 好些吧




有的网站 ArchiveBoX 也拿不全，日期信息啥的

使用ArchiveBoX改进爬虫  --已完成

或者后续加入https://github.com/opendatalab/magic-html  html数据提取器作为备用  --未实现
 

BAAI/bge-base-en-v1.5  已使用


知识库的扩充，可以将OpenCTI的图数据，找些实时更新的数据源

----未实现 GraphRAG + OpenCTI，


优化pdf文档中内容的提取（对文档的分块有些小问题） ----进度70%,还未将RAG\First_doc_cope.py加载到向量数据库中，上传的pdf的处理还未使用这个文件



处理时转圈，可以切换界面，不影响分析界面的内容-------未完成


依据snapshot_id去进行自动化循环，保存好新的链接后，返回snapshot_id，以便后续自动分析使用   -----进度 80%


ArchiveBox爬取分析功能 ----单url分析展示有点问题，会截断内容


使用 auto_search_api_register.py 文件去半自动注册api-key ，最少都得3个，，改成从网页中自动获取，修改成全自动的   --未完成

