import os
import re
import sys
import time
import argparse
import json
import traceback

from dotenv import load_dotenv
import mysql.connector
from mysql.connector import Error

from RAG.utils import process_analysis_content, web_analyze

# 确保RAG模块可导入
sys.path.append(os.path.join(os.path.dirname(__file__), 'RAG'))

from rag_inference import SimpleRagInference



#  $env:PYTHONPATH = "K:\CTI\Data_Excation;" + $env:PYTHONPATH 

dotenv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'db.env')
load_dotenv(dotenv_path)
# MySQL 数据库配置
# 配置数据库连接
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}


class RAGAnalyzer:
    _instance = None
    _initialized = False
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            print("创建RAG分析器单例")
            cls._instance = super(RAGAnalyzer, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, db_config=None, rag_config="configs/rag_inference.yml"):
        # 只初始化一次核心组件
        self.db_config = db_config if db_config else DB_CONFIG
        self.api_call_count = 0
        self.start_time = time.time()

        # RAG系统初始化
        if not RAGAnalyzer._initialized:
            print("初始化RAG分析器...")
            try:
                self.rag = SimpleRagInference(rag_config)
                RAGAnalyzer._initialized = True
                print("RAG系统初始化完成")
            except Exception as e:
                print(f"RAG系统初始化失败: {e}")
                import traceback
                traceback.print_exc()
                raise RuntimeError("RAG系统初始化失败，无法继续")
        else:
            print("使用现有的RAG分析器实例")
            
        # 数据库连接 - 每个实例都创建自己的连接，避免共享
        if db_config:
            print("初始化数据库连接...")
            self.db = self._init_db()
            print("数据库连接初始化完成")
        else:
            self.db = None

    def _init_db(self):
        """初始化MySQL数据库连接"""
        class AnalysisDB:
            def __init__(self, db_config):
                self.db_config = db_config  # 保存配置以供reset使用
                self.conn = mysql.connector.connect(**db_config)
                # 使用带缓冲的游标
                self.cursor = self.conn.cursor(dictionary=True, buffered=True)
                
                # 执行测试查询确保连接状态清洁
                self.cursor.execute("SELECT 1")
                self.cursor.fetchall()  # 消费结果
                    
            def commit(self):
                self.conn.commit()
                    
            def close(self):
                if self.conn.is_connected():
                    self.cursor.close()
                    self.conn.close()
                    
            def reset(self):
                """重置连接状态"""
                try:
                    # 尝试关闭现有连接
                    self.close()
                except:
                    pass
                # 创建新连接
                self.conn = mysql.connector.connect(**self.db_config)
                self.cursor = self.conn.cursor(dictionary=True, buffered=True)
                # 清理状态
                self.cursor.execute("SELECT 1")
                self.cursor.fetchall()
        
        return AnalysisDB(self.db_config)
    
    def print_database_stats(self):
        """打印数据库状态统计"""
        try:
            # 爬取状态统计
            self.db.cursor.execute('''
            SELECT crawl_status, COUNT(*) as count
            FROM crawled_data 
            GROUP BY crawl_status
            ''')
            
            print("\n爬取状态统计:")
            status_names = {0: "未爬取", 1: "成功", 2: "失败", 3: "跳过", 4: "空白"}
            for row in self.db.cursor.fetchall():
                status = row['crawl_status']
                count = row['count']
                status_name = status_names.get(status, f"未知({status})")
                print(f"- {status_name}: {count}条")
            
            # 检查rag_analysis表是否有数据
            self.db.cursor.execute('SELECT COUNT(*) as count FROM rag_analysis')
            total_count = self.db.cursor.fetchone()['count']
            
            print("\nRAG分析状态统计:")
            if total_count == 0:
                print("- 尚未进行任何分析，rag_analysis表为空")
            else:
                # RAG分析状态统计
                self.db.cursor.execute('''
                SELECT analysis_status, COUNT(*) as count
                FROM rag_analysis 
                GROUP BY analysis_status
                ''')
                
                analysis_names = {0: "待分析", 1: "已分析", 2: "分析失败"}
                for row in self.db.cursor.fetchall():
                    status = row['analysis_status']
                    count = row['count']
                    status_name = analysis_names.get(status, f"未知({status})")
                    print(f"- {status_name}: {count}条")
                
        except Error as e:
            print(f"获取数据库统计信息失败: {e}")

    def _execute_query(self, query, params=None, fetch=True):
        """安全地执行SQL查询，确保正确处理结果"""
        try:
            # 执行查询
            self.db.cursor.execute(query, params or ())
            
            # 消费结果
            if fetch:
                result = self.db.cursor.fetchall()
                return result
            else:
                # 对于不需要返回结果的查询，也要尝试读取并丢弃结果
                try:
                    self.db.cursor.fetchall()
                except:
                    pass
                return None
                
        except Error as e:
            print(f"SQL查询执行失败: {e}, 查询: {query}")
            traceback.print_exc()
            raise


    def _get_analysis_candidates(self, batch_size=1, num_id=None):
        """获取待进行RAG分析的数据"""
        try:
            # 检查rag_analysis表是否存在
            self._ensure_rag_table_exists()
            
            # 只拿num_id的一条数据
            if num_id is not None and batch_size == 1:
                print(f"获取ID为{num_id}的分析候选数据")
                query = '''
                SELECT c.id, c.extracted_text, c.link, c.title 
                FROM crawled_data c
                LEFT JOIN rag_analysis r ON c.id = r.crawled_data_id AND r.analysis_type = 'all'
                WHERE c.crawl_status = 1 
                AND (r.crawled_data_id IS NULL OR r.analysis_status = 0)
                AND c.id = %s
                '''
                rows = self._execute_query(query, (num_id,))
                
            else:
                # 获取未分析过的记录，同时获取链接和标题
                print(f"获取前{batch_size}条未分析的记录")
                query = '''
                SELECT c.id, c.extracted_text, c.link, c.title 
                FROM crawled_data c
                LEFT JOIN rag_analysis r ON c.id = r.crawled_data_id AND r.analysis_type = 'all'
                WHERE c.crawl_status = 1 
                AND (r.crawled_data_id IS NULL OR r.analysis_status = 0)
                ORDER BY c.id ASC
                LIMIT %s
                '''
                rows = self._execute_query(query, (batch_size,))
                
            # 处理结果 - 返回包含标题的元组
            candidates = [(row['id'], row['extracted_text'], row['link'], row['title']) for row in rows]
            return candidates
                
        except Error as e:
            print(f"获取分析候选数据失败: {e}")
            traceback.print_exc()
            return []

    
    def analyze_from_crawled_data(self, record_data, analysis_type="all"):
        """
        分析爬取的数据条目（直接使用记录数据）
        
        Args:
            record_data: 包含爬取数据的字典（必须包含 'id', 'extracted_text', 'title', 'link'）
            analysis_type: 分析类型 (summary, threats, vulns, iocs, all)
            
        Returns:
            dict: 包含分析结果的字典
        """
        try:
            # 验证记录数据
            if not isinstance(record_data, dict):
                return {
                    "success": False,
                    "error": "记录数据必须是字典类型"
                }
                
            required_fields = ['id', 'extracted_text', 'title', 'link']
            for field in required_fields:
                if field not in record_data:
                    return {
                        "success": False,
                        "error": f"记录数据缺少必要字段: {field}"
                    }
            
            crawled_data_id = record_data['id']
            content = record_data['extracted_text']
            title = record_data['title']
            link = record_data['link']
            
            # 检查内容是否为空
            if not content or content.strip() == "":
                self._mark_analysis_failed(crawled_data_id, analysis_type)
                return {
                    "success": False,
                    "error": f"ID为{crawled_data_id}的记录内容为空"
                }
            
            # 尝试获取报告日期（从记录数据中获取，避免重复爬取）
            try:
                # 优先从记录数据中获取日期
                report_date = record_data.get('publication_date', '')
                if not report_date:
                    # 如果记录中没有日期，从HTML内容中尝试提取
                    html_content = record_data.get('html_content', '')
                    if html_content:
                        from htmldate import find_date
                        report_date = find_date(html_content) or ""
                    else:
                        report_date = ""
                print(f"获取报告日期: {report_date if report_date else '未知'}")
            except Exception as e:
                print(f"获取报告日期时出错: {str(e)}")
                report_date = ""
                
            print(f"分析ID={crawled_data_id}的记录，标题: '{title[:50]}...'，报告日期: {report_date if report_date else '未知'}")
            
            # 创建临时文件
            temp_file_path = self._create_temp_file(content, title, crawled_data_id)
            
            try:
                # 使用RAG系统分析文档
                start_time = time.time()
                result = self.rag.analyze_document(temp_file_path, analysis_type, save_to_db=False)
                process_time = time.time() - start_time
                
                # 检查分析是否成功
                if "error" in result:
                    self._mark_analysis_failed(crawled_data_id, analysis_type)
                    return {
                        "success": False,
                        "error": result["error"]
                    }
                
                # 提取分析结果
                analysis_result = result['answer']
                references = result.get('references', [])

                _, rationale = process_analysis_content(analysis_result)

                # 将RAG分析结果保存到数据库
                _ = self._save_rag_analysis_result(
                    record_id=crawled_data_id,
                    analysis_text=analysis_result,
                    references=references,
                    rationale=rationale,
                    analysis_type=analysis_type,
                    report_date=report_date,
                    input_source=link,
                    input_type="database"
                )
                
                self.api_call_count += 1
                print(f"成功分析并保存ID={crawled_data_id}的结果，耗时: {process_time:.2f}秒")
                
                # 打印进度
                self._print_progress()
                
                # 获取分析记录ID
                self.db.cursor.execute('''
                SELECT id FROM rag_analysis
                WHERE crawled_data_id = %s AND analysis_type = %s
                ''', (crawled_data_id, analysis_type))
                analysis_record = self.db.cursor.fetchone()
                analysis_id = analysis_record['id'] if analysis_record else None
                
                # 返回成功信息
                return {
                    "success": True, 
                    "analysis_id": analysis_id,
                    "crawled_data_id": crawled_data_id,
                    "process_time": f"{process_time:.2f}秒",
                    "analysis_type": analysis_type,
                    "report_date": report_date
                }
                
            except Exception as e:
                import traceback
                print(f"分析记录 {crawled_data_id} 时出错: {e}")
                traceback.print_exc()
                self._mark_analysis_failed(crawled_data_id, analysis_type)
                return {"success": False, "error": str(e)}
                
            finally:
                # 删除临时文件
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    
        except Error as e:
            print(f"数据库操作失败: {e}")
            return {"success": False, "error": f"数据库错误: {str(e)}"}
        except Exception as e:
            print(f"分析过程中出现未知错误: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
        
    def batch_analyze(self, batch_size=5, analysis_type="all", input_type="database"):
        """使用RAG系统批量分析文档"""
        candidates = self._get_analysis_candidates(batch_size=batch_size)
        if not candidates:
            print("没有待分析的文档")
            return
            
        print(f"找到 {len(candidates)} 条记录待分析")
        
        for record in candidates:
            record_id, content, link = record  # 从返回值中获取链接
            if not content or content.strip() == "":
                print(f"跳过ID={record_id}的空记录")
                self._mark_analysis_failed(record_id, analysis_type)
                continue
                
            print(f"\n正在使用RAG分析ID={record_id}的记录")

            try:
                _, report_date, _ = web_analyze(link)
            except:
                report_date = ""
            
            print(f"报告日期: {report_date if report_date else '未知'}")
            
            try:
                # 创建临时文件，因为RAG系统需要文件路径
                temp_file_path = self._create_temp_file(content, f"record_{record_id}", record_id)
                
                try:
                    # 使用RAG系统分析文档
                    start_time = time.time()
                    result = self.rag.analyze_document(temp_file_path, analysis_type, save_to_db=False)
                    process_time = time.time() - start_time
                    
                    # 检查分析是否成功
                    if "error" in result:
                        print(f"分析错误: {result['error']}")
                        self._mark_analysis_failed(record_id, analysis_type)
                        continue
                    
                    # 提取分析结果
                    analysis_result = result['answer']
                    references = result.get('references', [])
                    
                    # 将RAG分析结果保存到数据库
                    # 使用从记录中获取的链接作为input_source
                    # 保持input_type为"database"，因为这是数据的原始来源
                    _ = self._save_rag_analysis_result(
                        record_id=record_id, 
                        analysis_text=analysis_result, 
                        references=references, 
                        analysis_type=analysis_type,
                        report_date=report_date,
                        input_source=link,
                        input_type=input_type
                    )
                    
                    print(f"成功分析并保存ID={record_id}的结果，耗时: {process_time:.2f}秒")
                    self.api_call_count += 1
                    
                finally:
                    # 删除临时文件
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)
            
            except Exception as e:
                print(f"处理记录 {record_id} 时出错: {e}")
                self._mark_analysis_failed(record_id, analysis_type)
                import traceback
                traceback.print_exc()
            
            # 打印进度
            self._print_progress()
        
    def _create_temp_file(self, content, title, record_id):
        """创建临时文件存储内容"""
        temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        temp_file_path = os.path.join(temp_dir, f"record_{record_id}_{int(time.time())}.txt")
        
        with open(temp_file_path, "w", encoding="utf-8") as f:
            f.write(f"# {title}\n\n")
            f.write(content)
        
        return temp_file_path
    
    def _save_rag_analysis_result(self, record_id, analysis_text, references=None, rationale=None, analysis_type="all", input_source=None, input_type=None, report_date=""):
        """将RAG分析结果保存到数据库，并返回分析ID"""
        
        def normalize_date_format(date_string):
            """标准化日期格式为 YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD"""
            if not date_string or date_string in ["未知时间", ""]:
                return None
            
            try:
                from datetime import datetime
                import re
                
                # 移除常见的无关字符和前缀
                date_string = str(date_string).strip()
                
                # 处理 ISO 8601 格式 (带时区)
                if re.match(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z?$', date_string):
                    # 移除时区标识符
                    date_string = re.sub(r'Z$', '', date_string)
                    try:
                        dt = datetime.fromisoformat(date_string)
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        pass
                
                # 处理简单的日期格式
                if re.match(r'^\d{4}-\d{2}-\d{2}$', date_string):
                    return date_string
                
                # 处理其他常见格式
                date_patterns = [
                    ('%Y-%m-%dT%H:%M:%S', '%Y-%m-%d %H:%M:%S'),  # ISO format without Z
                    ('%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S'),  # Already correct format
                    ('%m/%d/%Y', '%Y-%m-%d'),                    # American format
                    ('%d/%m/%Y', '%Y-%m-%d'),                    # European format
                    ('%B %d, %Y', '%Y-%m-%d'),                  # December 25, 2024 (full month name)
                    ('%d %B %Y', '%Y-%m-%d'),                   # 25 December 2024 (full month name)
                    ('%b %d, %Y', '%Y-%m-%d'),                  # Jul 25, 2024 (abbreviated month name)
                    ('%d %b %Y', '%Y-%m-%d'),                   # 25 Jul 2024 (abbreviated month name)
                ]
                
                for input_fmt, output_fmt in date_patterns:
                    try:
                        dt = datetime.strptime(date_string, input_fmt)
                        return dt.strftime(output_fmt)
                    except ValueError:
                        continue
                
                print(f"警告: 无法解析日期格式: {date_string}")
                return None
                
            except Exception as e:
                print(f"日期格式标准化时出错: {str(e)}")
                return None
        
        try:
            # 标准化日期格式
            normalized_date = normalize_date_format(report_date)
            if report_date and not normalized_date:
                print(f"警告: 日期 '{report_date}' 无法标准化，将使用 NULL")
            
            # 处理分析结果，提取JSON内容
            cleaned_analysis, _ = process_analysis_content(analysis_text)

            # 将参考文献转换为字符串
            references_str = "\n---\n".join(references) if references else ""
            
            # 检查rag_analysis表是否存在，不存在则创建
            self._ensure_rag_table_exists()
            
            # 首先检查记录是否已存在
            self.db.cursor.execute('''
            SELECT id, crawled_data_id FROM rag_analysis 
            WHERE crawled_data_id = %s AND analysis_type = %s
            ''', (record_id, analysis_type))
            
            existing = self.db.cursor.fetchone()
            analysis_id = None
            
            if existing:
                # 获取现有记录ID
                analysis_id = existing['id']
                
                # 更新现有记录
                self.db.cursor.execute('''
                UPDATE rag_analysis SET
                    analysis_content = %s,
                    references_text = %s,
                    rationale = %s,
                    analysis_status = 1,
                    analysis_time = CURRENT_TIMESTAMP,
                    report_time = %s,
                    input_source = %s,
                    input_type = %s
                WHERE crawled_data_id = %s AND analysis_type = %s
                ''', (
                    cleaned_analysis,
                    references_str,
                    rationale,  
                    normalized_date,
                    input_source,
                    input_type,
                    record_id,
                    analysis_type
                ))
            else:
                # 创建新记录
                self.db.cursor.execute('''
                INSERT INTO rag_analysis (
                    crawled_data_id,
                    analysis_type,
                    analysis_content,
                    references_text,
                    rationale,
                    analysis_status,
                    report_time,
                    input_source,
                    input_type
                ) VALUES (%s, %s, %s, %s, %s, 1, %s, %s, %s)
                ''', (
                    record_id,
                    analysis_type,
                    cleaned_analysis,
                    references_str,
                    rationale,  
                    normalized_date,
                    input_source,
                    input_type
                ))
                
                # 获取新插入记录的ID
                analysis_id = self.db.cursor.lastrowid

            # 提交事务
            self.db.conn.commit()

            return analysis_id
                    
        except Error as e:
            print(f"保存RAG分析结果失败: {str(e)}")
            self.db.conn.rollback()
            return None

    def _ensure_rag_table_exists(self):
        """确保RAG分析表存在"""
        try:
            # 尝试创建表
            self.db.cursor.execute("""
            CREATE TABLE IF NOT EXISTS rag_analysis (
                id INT AUTO_INCREMENT PRIMARY KEY,
                crawled_data_id INT NOT NULL,
                analysis_type VARCHAR(50) NOT NULL,
                analysis_content TEXT,
                references_text TEXT,
                rationale TEXT,
                analysis_status TINYINT DEFAULT 0,
                analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                report_time TIMESTAMP NULL,
                input_source VARCHAR(1024) NULL,
                input_type ENUM('url', 'file', 'text', 'database') NULL,
                INDEX (crawled_data_id),
                INDEX (analysis_type),
                UNIQUE INDEX (crawled_data_id, analysis_type),
                INDEX (input_type),
                INDEX (analysis_time)
            ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            """)
            # 无论是否有结果，都尝试读取并丢弃
            try:
                self.db.cursor.fetchall()  # 消费所有结果
            except:
                pass  # 忽略没有结果的情况
                
            # 提交事务
            self.db.conn.commit()
            
        except Error as e:
            print(f"创建rag_analysis表失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 尝试重新创建数据库连接
            try:
                # 先尝试清理现有连接
                try:
                    if hasattr(self.db, 'cursor') and self.db.cursor:
                        self.db.cursor.close()
                    if hasattr(self.db, 'conn') and self.db.conn and self.db.conn.is_connected():
                        self.db.conn.close()
                except:
                    pass
                    
                # 重新初始化数据库连接
                print("尝试重新连接数据库...")
                self.db = self._init_db()
                print("数据库重新连接成功")
            except Exception as e2:
                print(f"重新连接数据库失败: {e2}")
    
    def _mark_analysis_failed(self, record_id, analysis_type="all"):
        """标记分析失败"""
        try:
            # 首先检查记录是否已存在
            self.db.cursor.execute('''
            SELECT crawled_data_id FROM rag_analysis 
            WHERE crawled_data_id = %s AND analysis_type = %s
            ''', (record_id, analysis_type))
            
            existing = self.db.cursor.fetchone()
            
            if existing:
                # 更新状态
                self.db.cursor.execute('''
                UPDATE rag_analysis 
                SET analysis_status = 2 
                WHERE crawled_data_id = %s AND analysis_type = %s
                ''', (record_id, analysis_type))
            else:
                # 插入新记录，标记为失败
                self.db.cursor.execute('''
                INSERT INTO rag_analysis 
                (crawled_data_id, analysis_type, analysis_status) 
                VALUES (%s, %s, 2)
                ''', (record_id, analysis_type))
                
            self.db.conn.commit()
            
        except Error as e:
            print(f"标记分析失败时出错: {e}")
            self.db.conn.rollback()

    def _print_progress(self):
        """打印处理统计"""
        elapsed = time.time() - self.start_time
        print(f"⏱️ 已处理 {self.api_call_count} 条 | 平均速度 {self.api_call_count/elapsed:.2f}条/秒")

    def close(self):
        """关闭数据库连接"""
        if hasattr(self, 'db'):
            self.db.close()

    def view_rag_analysis(self, record_id):
        """查看特定记录的RAG分析结果"""
        try:
            self.db.cursor.execute('''
            SELECT r.*, c.extracted_text 
            FROM rag_analysis r
            JOIN crawled_data c ON r.crawled_data_id = c.id
            WHERE r.crawled_data_id = %s
            ''', (record_id,))
            
            results = self.db.cursor.fetchall()
            if not results:
                print(f"未找到ID={record_id}的RAG分析结果")
                return
            
            for result in results:
                print(f"\n=== 分析类型: {result['analysis_type']} ===")
                print(f"分析时间: {result['analysis_time']}")
                print(f"分析状态: {'成功' if result['analysis_status'] == 1 else '失败'}")
                print("\n--- 分析结果 ---")
                print(result['analysis_content'][:1000] + ("..." if len(result['analysis_content']) > 1000 else ""))
                print("\n--- 参考资料 ---")
                print(result['references_text'][:500] + ("..." if len(result['references_text']) > 500 else ""))
            
        except Error as e:
            print(f"查看分析结果失败: {e}")


def run(db_config=None, analysis_type="all", batch_size=20, input_type="database"):

    if db_config is None:
        db_config = DB_CONFIG
    
    # 初始化分析器
    analyzer = RAGAnalyzer(db_config)
    
    # 在批量分析前打印统计信息
    analyzer.print_database_stats()
    
    try:
        # 执行RAG批量分析
        analyzer.batch_analyze(
            batch_size=batch_size,
            analysis_type=analysis_type,
            input_type=input_type
        )
        
        # 打印统计信息
        print(f"\nRAG分析完成! 总共处理了 {analyzer.api_call_count} 条记录")
        
        # 查看最新分析结果示例
        if analyzer.api_call_count > 0:
            print("\n示例分析结果:")
            # 获取最近分析的记录
            analyzer.db.cursor.execute('''
            SELECT crawled_data_id FROM rag_analysis 
            ORDER BY analysis_time DESC LIMIT 1
            ''')
            record = analyzer.db.cursor.fetchone()
            if record:
                analyzer.view_rag_analysis(record['crawled_data_id'])
        
    finally:
        # 确保关闭数据库连接
        analyzer.close()

def main():
    parser = argparse.ArgumentParser(description="基于RAG的威胁情报分析工具")
    parser.add_argument("--type", choices=["summary", "threats", "vulns", "iocs", "all"], 
                      default="all", help="RAG分析类型")
    parser.add_argument("--batch", type=int, default=20, help="批处理大小")
    parser.add_argument("--view", type=int, help="查看指定ID的分析结果")
    parser.add_argument("--input-type", choices=["url", "file", "text", "database"], default="database",
                      help="指定输入类型")
    
    args = parser.parse_args()
    
    if args.view:
        # 查看指定记录的分析结果
        analyzer = RAGAnalyzer()
        analyzer.view_rag_analysis(args.view)
        analyzer.close()
    else:
        # 执行批量分析
        run(
            analysis_type=args.type, 
            batch_size=args.batch,
            input_type=args.input_type
        )

    def check_existing_analysis(self, crawled_data_id, analysis_type):
        """检查是否已存在相同分析，代理到SimpleRagInference实例"""
        if hasattr(self, 'rag') and hasattr(self.rag, 'check_existing_analysis'):
            return self.rag.check_existing_analysis(crawled_data_id, analysis_type)
        else:
            print("⚠️ SimpleRagInference实例不可用或缺少check_existing_analysis方法")
            return None


if __name__ == "__main__":
    main()

