# U.S. CISA urges to immediately patch Microsoft SharePoint flaw adding it to its Known Exploited Vulnerabilities catalog

U.S. CISA urges to immediately patch Microsoft SharePoint¬†flaw adding it to its Known Exploited Vulnerabilities catalog
# U.S. CISA urges to immediately patch Microsoft SharePoint¬†flaw adding it to its Known Exploited Vulnerabilities catalog
# U.S. Cybersecurity and Infrastructure Security Agency (CISA) adds Microsoft SharePoint¬†flaw to its Known Exploited Vulnerabilities catalog.

The U.S. Cybersecurity and Infrastructure Security Agency (CISA)¬† added Microsoft SharePoint¬†flaw, tracked as CVE-2025-53770 (‚ÄúToolShell‚Äù) (CVSS score of 9.8), to its Known Exploited Vulnerabilities (KEV) catalog .

This week, Microsoft released emergency SharePoint updates for two zero-day flaws, tracked as¬† CVE-2025-53770 ¬†and CVE-2025-53771, exploited since July 18 in attacks dubbed ‚Äú ToolShell .‚Äù
Both vulnerabilities only impact on-premises SharePoint Servers, threat actors could chain them for unauthenticated, remote code execution.

Microsoft warned that the SharePoint zero-day vulnerability CVE-2025-53770 is under active exploitation.

The vulnerability is a deserialization of untrusted data in on-premises Microsoft SharePoint Server, an unauthorized attacker could exploit the vulnerability to execute code over a network. The flaw was discovered by Viettel Cyber Security via Trend Micro‚Äôs ZDI.

‚ÄúMicrosoft is aware that an exploit for CVE-2025-53770 exists in the wild.‚Äù reads the advisory . ‚ÄúMicrosoft is preparing and fully testing a comprehensive update to address this vulnerability. In the meantime, please make sure that the mitigation provided in this CVE documentation is in place so that you are protected from exploitation.‚Äù
Microsoft recommends that customers enable AMSI integration and deploy Microsoft Defender across all SharePoint Server farms. This configuration helps protect against the newly identified vulnerability.

Microsoft states that the vulnerability CVE-2025-53770 is a variant of a spoofing flaw tracked as CVE-2025-49706 (CVSS score: 6.3), which the IT giant addressed with the release of July 2025 Patch Tuesday updates . The company is developing a full patch and confirmed that the bug affects only on-premises SharePoint servers, not SharePoint Online in Microsoft 365.

‚ÄúMicrosoft is aware of active attacks targeting on-premises SharePoint Server customers. The attacks are exploiting a variant of CVE-2025-49706. This vulnerability has been assigned CVE-2025-53770.‚Äù reads the advisory published by Microsoft. ‚ÄúSharePoint Online in Microsoft 365 is not impacted. ‚ÄúA patch is currently not available for this vulnerability. Mitigations and detections are provided below.‚Äù
Attackers exploit the SharePoint flaw to run commands pre-authentication by abusing object deserialization. They use stolen machine keys to persist and move laterally, making detection difficult without deep endpoint visibility.

Security researchers from Eye Security and Palo Alto Networks warned of attacks combining two SharePoint flaws, CVE-2025-49706 and CVE-2025-49704, in a chain called ‚ÄúToolShell.‚Äù
However, given that CVE-2025-53770 is a variant of CVE-2025-49706, the attacks are likely related.

‚ÄúOn the evening of July 18, 2025, Eye Security identified active, large-scale exploitation of a new ¬†SharePoint remote code execution (RCE)¬† vulnerability chain, dubbed¬† ToolShell , demonstrated¬† just days ago on X , this exploit is being used in the wild to compromise on-premise SharePoint servers across the world. The new chain we elaborate in this blog, was later named¬† CVE-2025-53770 ¬†by Microsoft.‚Äù reads the¬† analysis ¬†published by Eye Security. ‚ÄúOur team scanned ¬†8000+ SharePoint servers¬† worldwide. We discovered dozens of systems actively compromised, probably on 18th of July around 18:00 UTC and 19th of July around 07:30 UTC. This blog will share our detailed findings and recommendations to patch & perform a compromise assessment if you think you are affected.‚Äù
According to Binding Operational Directive (BOD) 22-01: Reducing the Significant Risk of Known Exploited Vulnerabilities , FCEB agencies have to address the identified vulnerabilities by the due date to protect their networks against attacks exploiting the flaws in the catalog.

Experts also recommend that private organizations review the Catalog and address the vulnerabilities in their infrastructure.

CISA orders federal agencies to fix the vulnerabilities by¬†July 21, 2025.

Follow me on Twitter: @securityaffairs and Facebook and Mastodon