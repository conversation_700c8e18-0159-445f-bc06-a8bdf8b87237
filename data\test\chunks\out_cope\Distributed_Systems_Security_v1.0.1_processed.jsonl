{"chunk_id": "line-1", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 4", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-2", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "INTRODUCTION", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-3", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A distributed system is typically a composition of geo-dispersed resources (computing and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-4", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "communication) that collectively (a) provides services that link dispersed data producers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-5", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and consumers, (b) provides on-demand, highly reliable, highly available, and consistent", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-6", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resource access, often using replication schemas to handle resource failures, and (c) enables", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-7", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a collective aggregated capability (computational or services) from the distributed resources", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-8", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to provide (an illusion of) a logically centralised/coordinated resource or service.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-9", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Expanding on the above, the distributed resources are typically dispersed (for example, in", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-10", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "an Azure or Amazon Cloud, in Peer-to-Peer Systems such as Gnutella or BitTorrent, or in", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-11", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a Blockchain implementation such as Bitcoin or Ethereum) to provide various features to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-12", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the users. These include geo-proximate and low-latency access to computing elements,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-13", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "high-bandwidth and high-performance resource access, and especially highly-available unin-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-14", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "terrupted services in the case of resource failure or deliberate breaches. The overall technical", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-15", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "needs in a distributed system consequently relate to the orchestration of the distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-16", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resources such that the user can transparently access the enhanced services arising from", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-17", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the distribution of resources without having to deal with the technical mechanisms providing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-18", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the varied forms of distributed resource and service orchestrations.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-19", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "To support these functionalities, a distributed system commonly entails a progression of four", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-20", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "elements. These include (a) data ﬂows across the collection of authorised inputs (regulated via", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-21", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Access/Admission Control), (b) transportation of the data to/across the distributed resources", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-22", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(Data Transport functionality), (c) a resource coordination schema (Coordination Services),", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-23", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and (d) property based (e.g., time or event based ordering, consensus, virtualisation) data", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-24", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "management to support the desired applications such as transactions, databases, storage,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-25", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "control, and computing.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-26", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Consequently, distributed systems security addresses the threats arising from the exploitation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-27", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of vulnerabilities in the attack surfaces created across the resource structure and functionali-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-28", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ties of the distributed system. This covers the risks to the data ﬂows that can compromise", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-29", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the integrity of the distributed system’s resources/structure, access control mechanisms (for", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-30", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resource and data accesses), the data transport mechanisms, the middleware resource coor-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-31", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "dination services characterising the distributed system model (replication, failure handling,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-32", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "transactional processing, and data consistency), and ﬁnally the distributed applications based", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-33", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "on them (e.g., web services, storage, databases and ledgers).", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-34", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This Knowledge Area ﬁrst introduces the different classes of distributed systems categorising", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-35", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "them into two broad categories of decentralised distributed systems (without central coor-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-36", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "dination) and the coordinated resource/services type of distributed systems. Subsequently,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-37", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "each of these distributed system categories is expounded for the conceptual mechanisms", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-38", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "providing their characteristic functionalities prior to discussing the security issues pertinent", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-39", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to these systems. As security breaches in a distributed system typically arise from breaches", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-40", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "in the elements related to distribution (dispersion, access, communication, coordination, etc.),", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-41", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the KA emphasises the conceptual underpinnings of how distributed systems function. The", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-42", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "better one understands how functionality is distributed, the better one can understand how", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-43", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems can be compromised and how to mitigate the breaches. The KA also discusses", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-44", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "some technology aspects as appropriate along with providing references for following up the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-45", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 3", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-46", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 5", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-47", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "topics in greater depth.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-48", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "CONTENT", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-50", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "CLASSES OF DISTRIBUTED SYSTEMS AND", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-51", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "VULNERABILITIES", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-52", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[1, c2][2, c5][3, c18]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-53", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1.1", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-54", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Classes of Distributed Systems", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-55", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A diversity of viewpoints, models, and deployments exist for characterising distributed sys-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-56", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tems. These include deﬁning a distributed system at the level of the aggregation of physical", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-57", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resources (e.g., Peer to Peer or Cloud systems), deﬁning it at the Middleware level (e.g.,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-58", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Publish-Subscribe, distributed object platforms, or Web services), or deﬁning it in terms of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-59", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the services a distributed system provides (e.g., Databases or Ledgers). While a spectrum of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-60", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "deﬁnitions exists in literature, distributed systems can be broadly classiﬁed by the coordina-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-61", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tion schema linking the resources or by the speciﬁcation of the services utilising them. One", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-62", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "broad class is of decentralised control where the individual resources primarily interact with", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-63", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "their “neighbouring” resources. The other broad category links the distributed resources via", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-64", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "communication processes, such as message passing, to realise varied forms of virtual cen-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-65", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tralised/coordinated control. Thus, based on such communication and coordination models,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-66", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "distributed systems can be categorised into the following two broad classes.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-67", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1. Decentralised point-to-point interactions across distributed entities without a centralised", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-68", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordination service: Peer-to-Peer System (P2P) systems represent this class of dis-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-69", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tributed systems. Decentralised un-timed control is a prominent characteristic of such", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-70", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems. For example, systems such as Kademlia, Napster, Gnutella, and many other", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-71", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "distributed ﬁle and music sharing/storage systems, wireless sensor networks as well", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-72", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as online gaming systems fall in this category.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-73", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2. Coordinated clustering across distributed resources and services: This is a broad class", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-74", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "that is best understood when sub-divided into two coordination sub-classes, namely (a)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-75", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the coordination of resources and (b) the coordination of services. We will utilise these", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-76", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "two coordination abstractions throughout this chapter. The spectrum of distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-77", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems includes Client-Server models, n-Tier Multi-tenancy Models, elastic on-demand", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-78", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "geo-dispersed aggregation of resources (Clouds – public, private, hybrid, multi-Cloud,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-79", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Big Data services, High Performance Computing), and transactional services such as", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-80", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Databases, Ledgers, Storage Systems, or Key Value Store (KVS). The Google File System,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-81", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Amazon Web Services, Azure, and Apache Cassandra are simple examples of this class.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-82", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "While this class may appear to be both broad and diverse, the coordination abstraction", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-83", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(for either resources or services) directly characterises the type of distributed system", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-84", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "into these two sub-classes. In both cases, these systems are typically coordinated via", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-85", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "communication exchanges and coordination services with the intended outcome of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-86", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "providing a “virtually centralised system” where properties such as causality, ordering of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-87", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tasks, replication handling, and consistency are ensured. There are discrete deﬁnitions in", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-88", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the literature for Client-Server systems, Cloud Computing, Mobile Computing, Distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-89", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 4", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-90", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 6", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-91", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Databases, etc., though the provisioning of virtual “centralised/coordinated” behaviour", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-92", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is a common characteristic across them.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-93", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Notes:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-94", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "There are many nuances of security in distributed systems. One viewpoint focuses on", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-95", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the concepts and mechanisms to provide security in a distributed system where the resources", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-96", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and services are dispersed. The other viewpoint considers using distribution as a means", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-97", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of providing security, e.g., the dispersal of keys versus a centralised key store or the use of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-98", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Virtual Machines (VMs) to partition and isolate resources and applications. This KA focuses", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-99", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "on the former category of “security in a distributed system”. However, it also discusses the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-100", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "latter viewpoints given that the dispersed security mechanisms typically execute on dispersed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-101", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resources logically resulting in the need for the above mentioned classes of Decentralised or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-102", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Coordinated clustering.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-103", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "It is worth highlighting that a distributed system architecture is often an aggregation of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-104", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "multiple layers where each layer builds upon the services provided by the layer below and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-105", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordinated services offered across the distribution. At the lowest level, resources within a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-106", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "particular device (memory, computation, storage, communication) are accessed through the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-107", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Operating System primitives provided on that device. Distributed services e.g., naming, time", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-108", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "synchronisation, distributed ﬁle systems are assembled through the interaction of different", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-109", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "components and services running on individual devices. Higher layers build upon the lower", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-110", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "layers and services to provide additional functionalities and applications. Interactions across", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-111", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the different components of the distributed system at each level are provided by middleware", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-112", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "frameworks that support many different communication styles: message passing, Remote", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-113", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Procedure Calls (RPCs), distributed object platforms, publish-subscribe architectures, enter-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-114", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "prise service bus. Distributed applications are thus realised in a layered (or tiered) fashion", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-115", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "through the interactions and coordination of distributed components and services. Within", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-116", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "these architectures, decentralisation and coordination at each layer may differ resulting in", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-117", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "hybrid compositions of decentralisation and coordination patterns. We refer the reader to the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-118", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Operating Systems & Virtualisation CyBOK Knowledge Area [4] for issues concerning access", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-119", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to basic resources and the books [5, 6, 3, 7, 8] for further reading on distributed systems", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-120", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "architectures and middleware.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-121", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1.2", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-122", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Classes of Vulnerabilities & Threats", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-123", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Vulnerabilities refer to design or operational weaknesses that allow a system to be potentially", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-124", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "compromised by an attacker. Analogously, a threat reﬂects the potential or likelihood of an", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-125", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacker causing damage or compromising the system. Furthermore, security is an end-to-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-126", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "end systems property. Consequently, the vulnerabilities of a distributed system are broadly", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-127", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "grouped based on the functional blocks therein deﬁning the distributed system. Logically,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-128", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "these functional blocks and their operations also constitute the threat/attack surface for the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-129", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems where an attacker/adversary can exploit a vulnerability to compromise the system.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-130", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "At a high level, the attack surface relates to the compromises of the physical resources, the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-131", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "communication schema, the coordination mechanisms, the provided services themselves,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-132", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and the usage policies on the data underlying the services.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-133", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The following outlines the general functionalities that will be progressively detailed in the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-134", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "subsequent sections as relevant to the speciﬁc distributed system model.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-135", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 5", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-136", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 7", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-137", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1.2.1", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-138", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Access/Admission Control & ID Management", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-139", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Access or Admission control determines the authorised participation of a resource, a user, or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-140", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a service within a distributed system. This can include the sourcing of data and the access", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-141", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "rights to read/write and use data over the lifetime of a service. The potential threats and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-142", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "consequent attacks include masquerading or spooﬁng of identity to gain access rights to the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-143", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "data. They can also involve Denial of Service (DoS) attacks that detrimentally limit access", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-144", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(e.g., depletion of computing resources and communication channels) leading to the inacces-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-145", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sibility and unavailability of the distributed resources/services. It is worth emphasising that", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-146", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resource distribution often entails more points for access control, and also more information", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-147", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "transported in the system to support access control thus increasing the attack surface of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-148", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "system (see the Authentication, Authorisation & Accountability CyBOK Knowledge Area [9] for", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-149", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a discussion of authentication and authorisation in distributed systems).", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-150", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A distributed system entity (resource, service, user, or data element) participates in a dis-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-151", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tributed system with a physical or logical identity. The identity, statically or dynamically", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-152", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "allocated, can be a resource identiﬁer such as an ID name or a number1. Here, authorisation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-153", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "may be speciﬁed in terms of the user and/or resource identity including the use of login names", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-154", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and passwords. Thus, an activity that involves tampering with the identity constitutes a likely", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-155", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "threat.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-156", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1.2.2", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-157", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Data Transportation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-158", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The network level threats span routing, message passing, the publish-subscribe modalities", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-159", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of resource interaction, event based response triggering, and threats across the middleware", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-160", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "stack. Moreover, these can be passive (eavesdropping) or active attacks (data modiﬁcation).", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-161", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A typical example is the Man In the Middle (attack) (MITM) attack where the attacker inserts", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-162", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "itself between the victim’s browser and the web server to establish two separate connections", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-163", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "between them. This enables the attacker to actively record all messages and selectively", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-164", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "modify data without triggering a suspicious activity alarm if the system does not enforce", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-165", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "endpoint authentication. We refer the reader to [5, 6] for detailed coverage of these topics,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-166", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and to the Network Security CyBOK Knowledge Area [10].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-167", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1[7] provides an excellent discourse on naming issues in Chapter 6.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-168", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 6", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-169", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 9", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-170", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a multitude of P2P models have emerged. Regardless of their speciﬁc realisation, they usually", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-171", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "combine the following ﬁve principles: (1) symmetry of interfaces as peers can take inter-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-172", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "changeable duties as both servers and clients, (2) resilience to perturbations in the underlying", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-173", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "communication network substrate and to peer failures, (3) data and service survivability", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-174", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "through replication schemes, (4) usage of peer resources at the network’s edge, imposing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-175", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "potentially low infrastructure costs and fostering scalability as well as decentralisation, and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-176", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(5) address variance of resource provisioning among peers.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-177", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "These ﬁve principles make P2P a vital foundation for a diverse set of applications. Originally,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-178", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P2P systems were (in)famous for their support of ﬁle sharing applications such as eMule or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-179", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "KaZaA, though their usage is now common in applications such as social networks, multimedia", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-180", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "content distribution, online games, internet telephony services, instant messaging, the Internet", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-181", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of Things, Car-to-car communication, Supervisory Control and Data Acquisition (SCADA)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-182", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems, and wide area monitoring systems. As discussed in later sections, distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-183", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ledgers also utilise some aspects of P2P operations.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-184", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P2P Protocol Categories", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-185", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The two major P2P paradigms are unstructured and structured", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-186", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems. These system designs directly correlate with the application categories introduced", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-187", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "in the previous section, i.e., unstructured protocols are mostly suitable for (large scale and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-188", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "scalable) data dissemination, whereas structured ones are usually applied for efﬁciency", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-189", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of data discovery. The emergent hybrid P2P protocol designs combine aspects from both", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-190", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "unstructured and structured ones within an integrated P2P system.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-191", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Additionally, hierarchical P2P systems also exist. These partly contradict the conceptual", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-192", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P2P principle that considers all peers as equal in the sense of service provisioning. These", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-193", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "hierarchical systems can be considered as layered systems, e.g., composition of multiple", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-194", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "overlays consisting of front-end and back-end peers.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-195", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Regardless of the type of P2P system, it is important to note that the basic P2P operations", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-196", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are based on three elements, namely (a) identiﬁcation or naming of peer nodes, (b) routing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-197", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "schemas across peers, and (c) discovery of peers as a function of their identiﬁers and routing.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-198", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "In order to support the discussion of security in P2P systems, the next subsections provide", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-199", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "an introductory level technical overview on P2P protocols. We provide a brief overview of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-200", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P2P protocol categories in regard of the overlay topology, resources discovery, and message", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-201", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "passing. The reader is referred to [13] for a comprehensive discussion on P2P operations.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-202", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2.1", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-203", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Unstructured P2P Protocols", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-204", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Representatives of the unstructured P2P protocol class such as Freenet2 or Gnutella [14, 15]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-205", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are mainly used for data dissemination applications such as censorship-free3 communication", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-206", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or ﬁle sharing. While the set of peers do not have any characteristic topology linking them, their", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-207", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "implicit topology is usually embedded within the physical communication underlay network", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-208", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "topology and often unveils tree or mesh like sub-graphs, which allow for low latency message", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-209", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "exchange, e.g., to address timeliness requirements of data dissemination applications. Tree", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-210", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "topologies can be found, e.g., in single source streaming media data dissemination with vari-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-211", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ous consumers as leaf nodes. Meshes are the more generic case, for example, in applications", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-212", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2https://freenetproject.org/", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-213", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "3In the sense that data and information is stored and exchanged with integrity and privacy preserving tech-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-214", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "niques to address freedom of expression and speech concerns.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-215", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 8", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-216", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 10", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-217", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "with multiple sources and sinks such as in ﬁle sharing applications.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-218", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Unstructured P2P protocols typically search for resources (i.e., peers and data) by name", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-219", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or labels, and do not use a structured addressing scheme. This feature supports scalable", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-220", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "dissemination but scales poorly for resource discovery or reproducible routing paths. Peers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-221", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "nevertheless maintain an identiﬁer to allow independence of the underlay network address.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-222", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Resources are discovered using search algorithms on the overlay graph. Examples of search", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-223", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "algorithms include breadth-ﬁrst search, depth-ﬁrst search, random walks, or expanding ring", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-224", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "searches. These options are often combined according to the requirements of the application.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-225", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The communication across peers is via messages. Message passing may be direct, i.e., using", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-226", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "an underlay network connection between two peers, but this usually requires that the peers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-227", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "explicitly know the peer address and route. When the destination peer for the message to be", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-228", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sent is unknown, messages are piggybacked alongside a resource discovery operation.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-229", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "All peers maintain lists (direct routing tables with addresses or hashed addresses) with contact", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-230", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "information about other peers. Hence, messaging works efﬁciently and the network does", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-231", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "not suffocate from address-search messages. The efﬁciency of such lists depends on the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-232", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "liveness of the peers. Hence, the listed peers are periodically pinged for liveness and removed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-233", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "when no reply is received. The periodicity is dynamically adjusted based on the relevant churn,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-234", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "i.e., the rate of peer joins and departures.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-235", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2.2", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-236", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Structured P2P Protocols", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-237", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Structured P2P protocols such as Chord, Pastry, Tapestry, Kademlia, CAN etc. [16, 17, 18, 19]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-238", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are typically used for data discovery applications where the structure of the topology aids", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-239", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "efﬁcient searches. Their topology graphs usually show small-world properties, i.e., there exists", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-240", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a path between any two peers with a relatively small number of edges. Structured topologies", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-241", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "often appear as ring structures with shortcuts, which forms a basis for scalable and efﬁcient", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-242", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "operations such as resource discovery and message passing. Some protocols have more", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-243", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "exotic topologies, e.g., butterﬂy graphs, ﬁxed-degree graphs, or a multi-torus. The salient", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-244", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "characteristics are efﬁciency of node discovery and efﬁciency of routing that uses information", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-245", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "on the P2P structure and topology. As this aspect has security implications, we brieﬂy detail", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-246", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "these operations.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-247", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Unlike unstructured P2P’s open addressing schemas, in structured P2P protocols, pointers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-248", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to resources such as peers or data are stored in a distributed data structure which is called", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-249", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a Distributed Hash Table (DHT). The overlay’s address space is usually an integer scale in", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-250", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the range of [0, . . . , 2w −1] with w being 128 or 160 in general. Usually, a distance function", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-251", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "d(a, b) is deﬁned which allows distance computations between any two identiﬁers a and b", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-252", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "in the address space. Distance computations are crucial for the lookup mechanism and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-253", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "data storage responsibilities. The distance function and its properties differ among protocol", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-254", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "implementations. Data discovery is realised by computing the key of an easy-to-grasp resource", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-255", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "identiﬁer such as a distinctive name/key and subsequently requesting that key and its data", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-256", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "from one of the responsible peers.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-257", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Messages – for example to request the data for a given key – are exchanged in most structured", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-258", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "protocols directly, i.e., using an underlay network connection between two peers. If peers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-259", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "do not know each other, then no direct connection can be set up and the destination peer’s", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-260", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "location needs to be determined to conduct routing. To this end, an overlay lookup mechanism", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-261", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "aims to steadily decrease the address space distance towards the destination on each iteration", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-262", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 9", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-263", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 11", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-264", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of the lookup algorithm until the identiﬁer can be resolved. This design approach turns out", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-265", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to be very efﬁcient and promotes scalability. Once the lookup has successfully retrieved", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-266", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the destination’s underlay network address, messages can be exchanged. Lookup variants", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-267", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "include iterative or recursive algorithms as well as parallelised queries to a set of closest", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-268", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "neighbour peers.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-269", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Routing tables usually store k · w entries with k being a protocol speciﬁc constant. Moreover,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-270", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "for the ith portion of k entries with i ∈[0 . . . w], the peer stores contact information of peers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-271", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "that share i common preﬁx bits of the peer’s key. In other words, routing tables usually", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-272", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "provide more storage for closer peers than more distant ones. Moreover, routing tables keep", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-273", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "only information about live and reachable peers, therefore peers are periodically pinged. In", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-274", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "structured protocols, maintenance is more expensive as the topological structure needs to be", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-275", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "retained, e.g., newly joined peers have to be put into the appropriate peer’s routing tables or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-276", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "leaving/unresponsive peers have to be replaced by live ones in many peers’ routing tables.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-277", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2.3", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-278", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Hybrid P2P Protocols", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-279", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Hybrid variants of P2P protocols integrate elements from unstructured and structured schemas,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-280", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as their principal intent is data discovery and data dissemination. Prominent hybrid protocol", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-281", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "examples include ﬁle sharing services such as Napster and BitTorrent [20]. BitTorrent was", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-282", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "originally a classical unstructured protocol but now has been extended with structured P2P", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-283", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "features to provide a fully decentralised data discovery mechanism. Consequently, BitTorrent", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-284", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "could abandon the concept of so called “tracker servers” (that facilitated peer discovery) and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-285", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "improve its availability. On the other hand, architectural requirements often need to be consid-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-286", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ered to fully utilise the capacity of hybrid P2P protocols. An example would be establishing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-287", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "how the data discovery is transmitted among the servers and how it is reported back to the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-288", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "user [21]. Similar considerations apply to other streaming overlay approaches.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-289", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2.4", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-290", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Hierarchical P2P Protocols", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-291", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Typically, all the peers in a P2P system are considered to be equal in terms of the client-server", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-292", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "services they can provide. Yet, for some application scenarios it turns out that a hierarchical", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-293", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P2P design can be advantageous. These can include a layered design of structured and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-294", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "unstructured overlays. In hierarchical designs, peers are further categorised based on their", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-295", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "bandwidth, latency, storage, or computation cycles provisioning with some (super) peers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-296", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "taking a coordinating role. Usually, the category with fewer peers represented the back-end", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-297", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "part of the hierarchical system, whereas the multitude of peers act as front-end peers that", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-298", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "process service requests at the ﬁrst level and only forward requests to the back-end when they", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-299", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "cannot fulﬁll the service request in the ﬁrst place. This improves the look-up performance and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-300", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "also generates fewer messages in the network. Furthermore, popular content can be cached", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-301", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "locally to reduce download delays [22]. This design has proven successful, for example, in the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-302", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "eDonkey ﬁle sharing system or in Super P2P models such as KaZaA where a selected peer", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-303", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "acts as a server to a subset of clients.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-304", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 10", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-305", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 12", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-307", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "DISTRIBUTED SYSTEMS: ATTACKING P2P SYSTEMS", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-308", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[3, c16][23, c5]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-309", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "We present security attacks corresponding to the above mentioned classes of P2P systems.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-310", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "To facilitate this discussion, we outline the functional elements of a P2P system that help the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-311", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "reader relate the security implications for speciﬁc systems or application cases. Subsequently,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-312", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "we assess the risks stemming from attacks to plan the requisite mitigation. The P2P functional", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-313", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "elements that need protection broadly include:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-314", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1. P2P Operations (P-OP) such as discovery, query, routing, download, etc. that are acces-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-315", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sible through the service interface of the P2P protocol. This functionality relates to the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-316", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "network level.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-317", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2. P2P Data Structures (P-DS), e.g., data stored in a peer’s routing table or resources that", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-318", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are shared with other peers of the overlay network. This functional element may be", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-319", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "accessible at either the network level or locally on the peer’s host machine.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-320", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "We will refer to these two elements as P-OP and P-DS, in the following subsections where", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-321", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "we discuss the speciﬁc P2P attacks. We use the established security notions of [24] for", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-322", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Conﬁdentiality, Integrity and Availability. Whenever a deﬁnition refers to authentication, we", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-323", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "assume that peers are implicitly authenticated on joining the overlay network. P2P protocols", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-324", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "may may use admission control systems or may be open to arbitrary peers.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-325", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Note that we focus on attacks against P2P systems (e.g., denial of service or routing disrup-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-326", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tions) and do not consider attacks that are prepared or conducted using P2P systems in order", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-327", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to harm non-P2P systems (e.g., using a P2P system to coordinate distributed denial of service", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-328", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacks).", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-329", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "3.1", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-330", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Attack Types", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-331", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "We now present the different attacks that are speciﬁc to P2P systems. Broadly, the attacks", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-332", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "correspond to attacking the functional elements, P-OP and P-DS, either by (a) disrupting their", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-333", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "connectivity or access to other nodes for dissemination/discovery/routing or (b) corrupting", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-334", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "their data structures. Besides the well known (distributed) denial of service attacks which", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-335", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "apply to P2P as well as to other systems, most attacks exploit fundamental P2P features such", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-336", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as message exchange based decentralised coordination and especially that each peer has", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-337", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "only a partial (local) view of the entire system. Consequently, attackers aim to trick other peers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-338", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "by providing incorrect data or collude to create partitions that hide views of the system from", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-339", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "good nodes. This includes example scenarios such as (a) to mislead peers in terms of routing,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-340", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(b) to take advantage of access to resources, (c) to overcome limitations in voting systems or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-341", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "games, or (d) to hide information in the overlay among others. We refer the reader to the survey", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-342", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "articles [25, 26] for a fuller exposition of P2P security. We now enumerate some representative", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-343", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "security attacks and relate them to their corresponding impact on Conﬁdentiality, Integrity", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-344", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and Availability (CIA). Some examples of attacks are further discussed in Section 3.2 along", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-345", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "with corresponding mitigation approaches.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-346", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Denial of service attacks (DoS) [24], Distributed Denial of Service (DDoS), or disruption at-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-347", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tacks [27] manifest as resource exhaustion by limiting access to a node or a communication", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-348", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "route. In P2P architectures, the attacker aims to decrease the overlay network’s service", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-349", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "availability by excessively sending messages to a speciﬁc set of peers and thereby negatively", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-350", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 11", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-351", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 13", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-352", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "affecting the P-OP functionality. This could affect the peer join/leave mechanism, or other", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-353", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "arbitrary P2P service aspects, e.g., damaging the routing put/get operations in a DHT. For", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-354", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "example, benign peers may be impaired by an excessive maintenance workload. Moreover,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-355", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "DoS and DDoS attacks can have a negative impact on bandwidth usage and resource provi-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-356", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sioning which may result in degraded services. For instance, GitHub was hit with a sudden", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-357", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "onslaught of trafﬁc that reached 1.35 terabits per second4. The trafﬁc was traced back to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-358", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "“over a thousand different Autonmous Systems (ASNs) across tens of thousands of unique", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-359", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "endpoints” participating in the attack.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-360", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Collusion attacks [28] aim to compromise the availability, integrity, or conﬁdentiality of P2P", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-361", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "networks. Collusion refers to the fact that a sufﬁciently large subset of peers colludes to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-362", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "carry out a strategy which targets the P2P services and thereby negatively affects the P-OP", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-363", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "functionality. The typical attack aims to override control mechanisms such as those for", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-364", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "reputation or trust management, or bandwidth provisioning. The Sybil and Eclipse attacks,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-365", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "discussed later on, are based on attackers colluding to create network partitions to hide", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-366", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "system state information from good nodes.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-367", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Pollution attacks [29, 30] or index poisoning [31] aim to compromise the P2P system’s in-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-368", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tegrity and its P-DS functionality by adding incorrect information. Consequences of pollution", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-369", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacks are the proliferation of polluted content resulting in service impairments. An example", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-370", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is the typhoid ad-ware attack where the attacker partially alters the content, e.g., adding", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-371", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "advertisement at a single peer that subsequently spreads this polluted content to other peers.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-372", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- White washing [30] or censorship attacks aim to compromise the availability or integrity of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-373", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P2P systems. This includes either illicit changing of, deletion of or denying access to data.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-374", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Therefore, these attacks endanger the P-DS functionality. White washing attacks are especially", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-375", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "dangerous for P2P systems that use reputation based systems since they allow a peer with a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-376", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "bad reputation to leave the system, and subsequently re-join as a benign user.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-377", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Routing attacks [32, 27] aim to compromise the availability or integrity of P2P networks.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-378", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Routing attacks play an important role in composite attacks, such as the Eclipse attack", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-379", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "which obstructs a good node’s view of the rest of the system. In routing attacks, a malicious", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-380", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "peer undermines the message passing mechanism, e.g., by dropping or delaying messages.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-381", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Another routing attack variant is Routing Table Poisoning (RTP) [32]. In this attack, an attacker", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-382", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "deliberately modiﬁes its own or other peers’ routing tables, e.g., by returning bogus information", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-383", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to benign peer lookup requests. Attraction and repulsion [27] are speciﬁc variants of routing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-384", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacks which either increase (attraction) or decrease (repulsion) the attractiveness of peers,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-385", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "e.g., during path selection or routing table maintenance tasks. These attacks negatively affect", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-386", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the P-DS functionality. The compromise of the routing table in Pastry, often used in online", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-387", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "social networks, is a typical routing attack.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-388", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Buffer map cheating attacks [33] aim to decrease the availability of P2P networks, particularly", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-389", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "those used for media streaming applications. Through this attack, adversaries reduce the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-390", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "outgoing trafﬁc load of their peers by lying about their data provisioning. This is also an", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-391", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "infringement on integrity and affects the P-OP functionality. This attack is especially relevant", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-392", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "in streaming media P2P applications which rely on the collaboration of peers. Omission,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-393", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Fake Reporting, Fake Blocks, incorrect Neighbour Selection are related implications of such", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-394", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacks.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-395", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Sybil attacks [34] aim to compromise the availability or conﬁdentiality (via spooﬁng) of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-396", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P2P networks and can be regarded as a speciﬁc version of node/peer insertion attacks. They", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-397", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "4https://www.wired.com/story/github-ddos-memcached", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-398", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 12", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-399", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 14", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-400", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "consider the insertion into the overlay of peers that are controlled by one or several adversaries.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-401", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This could happen at speciﬁc or arbitrary locations of the overlay’s topology, depending on the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-402", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacker’s aim. Furthermore, P2P applications may consider system users as legal entities", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-403", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and consequently restrict the amount of peers per user to the amount of allowed votes for", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-404", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "that entity. Hence, an imbalance results in terms of the expected amount of peers per user.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-405", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Sybil attacks may be a precursor for many of the previously described attacks. Sybil attacks", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-406", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "affect the P-OP functionality of the system. Prominent Sybil attacks include the compromise", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-407", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of the BitTorrent DHT and the Sybil attack on the Tor anonymisation network.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-408", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Eclipse attacks [35] aim to decrease the availability, integrity and conﬁdentiality of P2P", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-409", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "networks. Essentially, a good peer is surrounded by a colluding group of malicious peers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-410", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "that either partially or fully block the peer’s view of the rest of the system. The consequence", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-411", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is that the malicious nodes can either mask or spoof the node’s external interactions. This", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-412", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is a composite attack that may involve routing table poisoning, DoS/DDoS, Sybil attacks,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-413", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "collusion, white washing, or censorship. Consequently, these attacks have an impact on both", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-414", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the P-OP and P-DS functionality. Variants of Eclipse attacks include Localised Eclipse Attacks", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-415", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(LEA), Topology Aware Localised Eclipse Attacks (taLEA) and Outgoing Eclipse Attacks (OEA)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-416", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacks among others. An example of an Eclipse attack on Bitcoin is discussed in Section 5.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-417", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Attack", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-418", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Availability", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-419", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Integrity", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-420", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Conﬁdentiality", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-421", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Functionality", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-422", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "DoS/DDoS", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-425", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P-OP", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-426", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Collusion", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-427", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P-OP", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-428", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Pollution", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-431", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P-DS", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-432", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "White washing & censorship", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-434", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P-DS", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-435", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Routing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-437", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P-DS", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-438", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Buffer map cheating", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-440", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P-OP", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-441", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Sybil", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-443", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P-OP", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-444", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Eclipse", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-445", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P-DS, P-OP", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-446", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Table 1: P2P Attacks, Security Goals and Affected Functionality", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-447", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "3.1.1", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-448", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Summary", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-449", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Table 1 summarises attacks on the P2P functional elements that entail modiﬁcations of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-450", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "P2P system to either degrade or compromise the P2P operations. The adversarial collusion", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-451", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of malicious peers is a key factor to launch these attacks resulting in signiﬁcant disruption.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-452", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "In many cases, the inherent design choices of P2P, which foster scalability and fault toler-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-453", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ance, are exploited. Attacks against P2P systems usually show an impact in terms of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-454", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "system’s conﬁdentiality, integrity, or availability. Several of the observed attacks are known", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-455", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "from other system architectures such as client-server models while others are new ones or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-456", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "compositions of various attacks. The difference from comparable attacks in client-server", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-457", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "system architectures is that P2P overlay networks may grow very large and adversaries have", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-458", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to correspondingly adapt their efforts, i.e., they need to scale up the fraction of malicious peers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-459", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "accordingly, thereby requiring a substantial amount of coordination to execute an effective", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-460", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "collusion strategy. These attacks vary depending upon whether the attacker has direct or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-461", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "indirect network access via a P2P overlay. The latter requires attackers to properly join the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-462", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "network prior to the attack. Thus, this may entail malicious peers making, e.g., a proper", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-463", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "announcement in the overlay network, before they can launch their adversarial behaviour.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-464", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Supplemental Observations:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-465", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 13", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-466", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 15", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-467", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Denial of service attacks degrade or prevent a system from correct service delivery [36, 37].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-468", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The more sophisticated Sybil attack [38, 37, 39] can be used as a potential precursor for an", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-469", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Eclipse attack [38, 37].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-470", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- If either secure storage, secure routing, or authentication mechanisms cannot be provided, a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-471", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "set of attacks including omission, content forgery, content pollution, censorship, or routing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-472", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "table poisoning may be the consequence [37, 39].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-473", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Churn relates to the effects of peers joining and leaving in an overlay. Churn attacks consider", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-474", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "artiﬁcially induced churn with potentially high peer join/leave rates to cause bandwidth con-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-475", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sumption due to the effort needed to maintain the overlay structure. This can lead to partial", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-476", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or complete denial of service [39].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-477", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Varied cheating attack strategies exist (for observing or corrupting player information and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-478", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "activities) in Massive Multiplayer Online Games (MMOG) built upon P2P architectures [39].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-479", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "3.2", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-480", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Attacks and their Mitigation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-481", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "We present some example attacks along with the approaches used to mitigate them. For a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-482", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "comprehensive coverage, we refer the reader to the surveys of [25, 26].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-483", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Basic PoS and P-DS Based Scenarios: The prominent P2P protocol security mechanisms", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-484", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are authentication mechanisms, secure storage, and secure routing. These three mecha-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-485", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "nisms allow the implementation of various downstream mechanisms. Authentication mecha-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-486", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "nisms [40, 37] help to maintain a benign peer population and provide the technical basis for", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-487", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "downstream mechanisms like secure admission, secure storage, or secure routing. Secure", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-488", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "storage is vital for data centric applications in order to prevent attackers from conducting", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-489", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "illicit data modiﬁcations [36, 38, 41, 40]. In a broader sense, illicit data modiﬁcation in online", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-490", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "games is considered as cheating [39]. The use of secure routing is typically advocated as", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-491", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "an approach to facilitate the identiﬁcation of peers conducting improper message forward-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-492", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ing [38, 41, 40]. Limiting the number of routing paths and/or protecting the paths using (high", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-493", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "overhead) cryptographic approaches are alternate approaches to mitigating routing attacks.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-494", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Sybil and Eclipse Scenarios: Sybil attacks occur where the attacker could launch an attack", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-495", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "with a small set of malicious peers and subsequently gather multiple addresses, which", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-496", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "allows malicious peers to fake being a larger set of peers. Using Sybil attacks, a LEA can be", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-497", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "launched via a chain of Sybil/malicious nodes. However, the attack relies on the assumption", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-498", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of the existence of a single path towards the victim that can be manipulated by the attacker.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-499", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Alternately, a LEA can be launched using Sybil peers.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-500", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "In such attacks, mitigation relies on using a centralised authority that handles peer enrolments", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-501", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or admission. Extending this concept, adding certiﬁcates (issued by a common Certiﬁcate", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-502", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Authority) to peers’ network IDs while joining the network is another possibility. Other miti-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-503", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "gation techniques to prevent malicious entities from selecting their own network IDs could", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-504", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "entail a signing entity using public key cryptography.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-505", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Buffer Map Cheating Scenarios: Other disruptions could be used to attack the KAD P2P net-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-506", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "work [19], which is a Kademlia based network, through ﬂooding peer index tables close to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-507", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the victim with false information as a simplistic taLEA variant. A KAD network crawler is", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-508", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "introduced to monitor the network status and detect malicious peers during a LEA. However,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-509", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a high overhead is incurred if each peer uses such a mechanism to detect malicious entities.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-510", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This becomes impractical as the overlay size increases.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-511", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 14", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-512", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 16", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-513", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Divergent lookups have been proposed as an alternate taLEA mitigation technique where the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-514", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "disjoint path lookups avoid searching the destination peer’s proximity to skip the wasteful", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-515", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "querying of malicious peers under taLEA assumptions.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-516", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Routing Scenarios: Mitigation mechanisms to handle routing attacks consider assigning mul-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-517", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tiple paths for each lookup using disjoint paths though at the cost of high message overhead.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-518", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Alternatives include the use of cryptographic schemes to protect the paths. However, P2P is a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-519", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "decentralised coordination environment where implementing a centralised service to support", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-520", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the coordination of system wide cryptographic signatures is hard to realise.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-521", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The aforementioned security mechanisms increase the resilience of P2P systems against the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-522", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "various attacks. Naturally, these mechanisms are resilient only until a critical mass of colluding", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-523", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "malicious peers is reached. In addition, some of these mechanisms require cryptographic", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-524", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "support or the identiﬁcation of peers. These requirements may interfere with application", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-525", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "requirements such as anonymity, heterogeneity, or resource frugality.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-527", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "DISTRIBUTED SYSTEMS: COORDINATED RESOURCE", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-528", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "CLUSTERING", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-529", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[12, c5,7,12,25][1, 3][2, c5,c14] [3, c16-17,c19]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-530", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Contrasting with the decentralised-control of P2P systems, a multitude of distributed systems", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-531", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "exist where the interactions across the distributed resources and services are orchestrated", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-532", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "using varied coordination mechanisms that provide the illusion of a logically centralised and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-533", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordinated system or service. The coordination can simply be a scheduler/resource manager,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-534", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a discrete coordinator or a coordination group, and include ordering in time (causality) or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-535", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "varied precedence orders across distributed transactions. While it is tempting to deﬁne each", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-536", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "type of distributed system discretely (i.e., differing from decentralised control in P2P), the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-537", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "large and diverse group of distributed systems/services share a common abstraction of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-538", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "“coordination” although its realisation and resultant properties for each system will vary.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-539", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Firstly, there is the case where a service is replicated on a distributed resources platform (or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-540", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "infrastructure) to enable geo-dispersed access to users while sustaining the required type", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-541", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of consistency speciﬁcations on the service. The Cloud and many distributed Client-Server", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-542", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems fall in this category.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-543", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The alternate approach addresses distributed services (versus platforms) where the dispersed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-544", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "service participants interact to yield the collective distributed service for given consistency", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-545", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "requirements. For example, transactional databases and distributed ledgers fall in such a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-546", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "category of strong consistency. Web crawlers, searches, or logistics applications may well", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-547", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "work with weak consistency speciﬁcations.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-548", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Overall, these constitute the two broad classes of distributed systems in the coordinated", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-549", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resource pooling mode, namely the classes of resource-coordination and service-coordination,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-550", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as based on their characteristic coordination schema although their functionality and deﬁni-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-551", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tions often overlap.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-552", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "In the subsequent subsections, in order to contextualise distributed systems security, we ﬁrst", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-553", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "detail the basic distributed concepts along with the coordination schema based on them.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-554", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This is followed by outlining the characteristic systems in each of the resource and service", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-555", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordination models. This forms the basis behind the general set of disruptions/vulnerabilities", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-556", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 15", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-557", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 17", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-558", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "relevant to both classes of coordinated distributed systems. We then outline the threats and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-559", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "security implications speciﬁc to each class of systems. We refer the reader to the excellent", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-560", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "texts of [2, 12, 1] for a comprehensive and rigorous treatise of these issues.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-561", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A Note on Technologies Underlying Distributed Platforms:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-562", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The introduction emphasised", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-563", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "that the focus of this KA is on security in distributed systems rather than the use of dis-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-564", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tribution towards providing security. Expanding on this topic, it is worth commenting on", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-565", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "alternate perspectives related to the “design and realisation” of distributed platforms and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-566", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "services. This design oriented perspective tends to emphasise the architecture of distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-567", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems, distributed services and their construction. This perspective typically focuses on (a)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-568", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "establishing security requirements, (b) realisation approaches on how to meet given security", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-569", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "requirements at each level of abstraction, and (c) considers a distributed system as a layered", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-570", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "architecture where each layer builds upon the primitives offered at the layer below and from", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-571", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "distributed services. In this perspective, centralised (coordinated) and decentralised patterns", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-572", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are often combined, differently and at different layers. Also from this perspective, the security", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-573", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "requirements of the applications must be met by complementing and building upon what is", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-574", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "offered at the lower layers and services.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-575", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This is a construction and compositional approach where the security properties (require-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-576", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ments) at the application level, or at a given layer, drive the selection of solutions and sub-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-577", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems that must be assembled (e.g., authentication, authorisation, accountability, non-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-578", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "repudiation etc.). The composition of such subsystems/solutions is often achieved through", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-579", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the use of trade-offs (and also threat) analysis that tend to cover some and not all of the re-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-580", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "quirements and thus determining relative strengths and weaknesses. For example, blockchain", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-581", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "applications, further discussed in Section 5.2, emphasise non-repudiation and decentralisation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-582", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as their main properties.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-583", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This layered and compositional approach can often be encountered in the literature such", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-584", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as [8, 42, 5, 6, 3, 2, 43] and many others. As the architectures and realisation fundamentally", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-585", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "underlie the KA premise of providing security in distributed systems, the reader is encouraged", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-586", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to refer to this literature. The following section returns the focus back on distributed system", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-587", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "concepts, and especially the fundamental concepts of the coordination class of distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-588", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-589", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Distributed Concepts, Classes of Coordination", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-590", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "As mentioned in the introduction, a distributed system is a collation of geo-dispersed com-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-591", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "puting resources that collectively interact to provide (a) services linking dispersed data pro-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-592", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ducers and consumers, (b) high-availability via fault tolerant replication to cover resource", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-593", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(computing and communication) failures, or (c) a collective aggregated capability (com-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-594", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "putational or services) from the distributed resources to provide (an illusion of) a logically", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-595", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "centralised/coordinated resource or service.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-596", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Distributed systems are often structured in terms of services to be delivered to clients. Each", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-597", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "service comprises and executes on one or more servers and exports operations that the clients", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-598", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "invoke by making requests. Although using a single, centralised server appears tempting, the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-599", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resulting service resident on a server can only be as fault tolerant as the server hosting it.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-600", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Typically, in order to accommodate server failures, the servers are replicated, either physically", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-601", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or logically, to ensure some degree of independence across server failures with such isolation.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-602", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Subsequently, replica management protocols are used to coordinate client interactions across", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-603", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 16", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-604", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 20", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-605", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "lead to inconsistent states that can be dealt with through conﬂict resolution mechanisms", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-606", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[51].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-607", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1. Sequential Consistency: Sequential consistency is met if the order in which the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-608", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "actions are performed by a certain process corresponds to their original order. In", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-609", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "order words, the sequential execution order of every process is preserved.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-610", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2. Causal Consistency: Causal consistency is achieved by categorising actions into", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-611", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "those causally related/dependent and those that are not. In this case only the order", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-612", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of causally related actions has to be preserved. Two events are causally related if", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-613", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "they both access the same data object and at least one of them is a write event.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-614", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "3. Eventual Consistency: In eventual consistency there are no special constraints that", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-615", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "have to be satisﬁed by the order of observer actions. The idea behind this concept", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-616", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is that the participants will eventually converge to a consistent state either by", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-617", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "observing equivalent orders of actions or by resorting to costly conﬂict resolution", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-618", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "mechanisms.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-619", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Systems with weaker consistency models became popular with the advent of the Internet", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-620", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "where wide scale web servers had to accommodate a large number of users. To achieve", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-621", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "this, such systems sacriﬁce strong consistency guarantees to achieve higher availability", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-622", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "for their user base. Systems like Amazon’s Dynamo [52], Facebook’s Cassandra [53] are", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-623", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "widely known examples of systems with weak consistency guarantees.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-624", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "4.4", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-625", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Replication Management and Coordination Schema: The Basis Be-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-626", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "hind Attack Mitigation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-627", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A fundamental challenge for developing reliable distributed systems is to support the cooper-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-628", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ation of the dispersed entities required to execute a common task, even when some of these", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-629", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "entities, or the communication across them, fails. There is a need to ensure ordering of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-630", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "service actions and to avoid partitions of the distributed resources in order to result in an", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-631", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "overall “coordinated” group of resources.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-632", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The state machine replication or state machine approach [54] is a general method for imple-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-633", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "menting a fault-tolerant service by replicating servers and coordinating client interactions", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-634", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "with server replicas. The approach also provides a framework for understanding and design-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-635", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ing replication management protocols. The essential system abstraction is that of a state", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-636", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "machine such that the outputs of the state machine are fully determined by the sequence of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-637", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "requests it processes independent of time or other activity in the system. Replication can be", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-638", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "active, semi-active, passive, or lazy [3].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-639", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "It should be noted that ideally one would like to collectively attain high availability, consistency", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-640", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and also full coordination to eliminate any partitioning of the set of distributed resources.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-641", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "However, the CAP assertion comes into play as:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-642", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 19", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-643", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 21", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-644", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "CAP", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-645", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Any network shared data system (e.g. Web) can provide only 2 of the 3 possible properties [55]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-646", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-647", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1. Consistency (C): equivalent to having a single up-to-date copy of the data, i.e., each", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-648", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "server returns the right response to each request.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-649", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2. Availability (A): of the data where each request eventually receives a response.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-650", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "3. Partition (P): Network partition tolerance such that servers cannot get partitioned into", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-651", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "non-communicating groups.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-652", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Naturally, security attacks attempt to compromise these elements of CAP.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-653", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Replication and Coordination", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-654", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "In order to provide coherent and consistent behaviour (in value and order), distributed re-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-655", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sources use various types of replica management, i.e., the coordination schema. This is a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-656", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "key coordination mechanism that characterises the functionality of any distributed system.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-657", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The factors determining the speciﬁc mechanism depend on the type of system synchronisa-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-658", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tion model, the type of group communication and especially the nature of the perturbations", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-659", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(faults or attacks) being considered. The mechanisms can be simple voting or leader election", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-660", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "processes (e.g., Ring Algorithms, Bully) or more complex consensus approaches to deal with", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-661", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "crashes or Byzantine5 behaviour. The commit protocols for database transactions are relevant", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-662", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "here as are the schemes for credential management and PKI infrastructures providing veriﬁed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-663", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "access control. We brieﬂy describe a set of widely used schema, and the reader is referred", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-664", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to [12, 2, 1] for complete coverage. Authorisation and Authentication in distributed systems", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-665", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are also discussed in the Authentication, Authorisation & Accountability CyBOK Knowledge", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-666", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Area [9].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-667", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Paxos", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-668", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "To avoid the situation of distributed entities conducting uncoordinated actions or failing to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-669", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "respond, Paxos [56], a group of implicit leader-election protocols for solving consensus in an", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-670", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "asynchronous setup, has been developed. Paxos solves the consensus problem by giving", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-671", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "all the participants the possibility to propose a value to agree upon in an initial phase. In the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-671", "line_number": 671, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-672", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "second phase, if a majority agrees on a certain value, the process that had proposed the value", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-673", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "implicitly becomes the leader, and agreement is achieved. The same process is repeated for", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-673", "line_number": 673, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-674", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the next value to achieve consensus on a sequence of values.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-675", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The protocol is known not to provide liveness only under very speciﬁc circumstances as", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-676", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "described in [56]. In this case, processes continue to propose values indeﬁnitely and remain", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-677", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "blocked in the initial phase as no majority can be formed and progress is never made. However,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-677", "line_number": 677, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-678", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "this situation rarely occurs in practice and Paxos remains one of most widely used coordination", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-679", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "protocols.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-680", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Since only a majority is necessary in the second phase to reach consensus, the protocol is", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-681", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "additionally tolerant to crashes even in the case of recovery. This is remarkable since, as long", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-681", "line_number": 681, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-682", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as the majority of the processes has not failed, consensus can be reached. The paper [57]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-683", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "5Byzantine behaviour happens when an entity/attacker sends different (albeit valid) information to different", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-684", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "recipients.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-685", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 20", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-686", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 23", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-687", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Commit Protocols", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-688", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A number of applications, e.g., databases, require ordering across replicated data or operations", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-689", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "where either all participants agree on conducting the same correct result (i.e., commit) or do", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-690", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "nothing – the atomicity property. Hence, as a specialised form of consensus, a distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-691", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordinator directed algorithm is required to coordinate all the processes that participate in a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-692", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "distributed atomic transaction on whether to commit or abort (roll back) the transaction.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-692", "line_number": 692, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-693", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The Two-Phase Commit (2PC) is a straightforward example of such atomic commitment", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-694", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "protocols. The protocol proceeds with a broadcast query from a leader to all the clients to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-695", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "commit. This is followed by an acknowledgment (commit or abort) from each client. On", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-696", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "receiving all responses, the leader notiﬁes all clients on an atomic decision to either commit", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-697", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or abort [2, 5, 6]. The protocol achieves its goal even in many cases of failure (involving either", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-698", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "process, network node, or communication failures among others), and is thus widely used.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-699", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "An approach based on logging protocol states is used to support recovery. The classical 2PC", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-700", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "protocol provides limited support for the coordinator failure that can lead to inconsistencies.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-701", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "To solve this problem the three-phase commit (3PC) protocol has been developed. The 3PC", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-702", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "protocol is essentially an extension of the BFT protocol and adds a third communication", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-703", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "phase to assist the leader with the decision for an abort. This entails a higher messaging and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-704", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "logging overhead to support recovery. While 3PC is a more robust protocol compared to BFT,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-705", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "it is not widely used due to the messaging overhead and its sensitivity to network partitioning", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-706", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(i.e., the P in CAP). In practice, systems use either BFT for its simplicity or the Paxos protocol", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-707", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "for its robustness.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-709", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "DISTRIBUTED SYSTEMS: COORDINATION CLASSES AND", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-709", "line_number": 709, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-710", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ATTACKABILITY", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-710", "line_number": 710, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-711", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[12, c3][1, c5,c6][2, c19] [3, c18][23, c3]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-711", "line_number": 711, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-712", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The General Class of Disruptions", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-712", "line_number": 712, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-713", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The attack surface [23, 66] in distributed systems involves the disruption of the resources,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-713", "line_number": 713, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-714", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "communication, interfaces, and/or data that either impairs the resource availability or disrupts", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-715", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the communication layer interconnecting the resources to impact Conﬁdentiality, Availability,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-716", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or Integrity of the overall system and its services. The disruptions can be from improper", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-717", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "design, arising from operational conditions or deliberate attacks. Resource compromises or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-717", "line_number": 717, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-718", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "disruptions form the basic attack targets. However, the functionality of a distributed system", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-718", "line_number": 718, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-719", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "emerges from the interactions across the distributed resources. As referenced in Section 1.2,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-719", "line_number": 719, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-720", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the resources and services (including replication management) in a distributed system are", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-720", "line_number": 720, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-721", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "primarily linked via communication infrastructures. These span the range of direct message", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-721", "line_number": 721, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-722", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "exchanges or via middleware architectures such as pub-sub or event based triggering among", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-722", "line_number": 722, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-723", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "others.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-723", "line_number": 723, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-724", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A number of varied terminologies exist to cover the range of operational and deliberate", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-725", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "perturbations from crashes, omissions, timing, value disruptions, spooﬁng, viruses, trapdoors,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-726", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and many others. We refer the reader to [24] for a comprehensive discussion on the topic.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-726", "line_number": 726, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-727", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "As the distributed systems primarily rely on message passing for both data transportation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-727", "line_number": 727, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-728", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 22", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-729", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 24", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-729", "line_number": 729, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-730", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and coordination, we group the perturbations at the level of message delivery6. The term", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-731", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "“perturbation or disruption” is deliberately used as the anomalous operation can result from", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-732", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "operational issues (dependability) or from a malicious intent (security). The manifestation of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-732", "line_number": 732, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-733", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "these perturbations on the system operations results in deviations from the speciﬁed behavior", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-733", "line_number": 733, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-734", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of the system. Complementing the vulnerabilities mentioned in Section 1.2 of access control,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-734", "line_number": 734, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-735", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "data distribution, interfaces, the communication level perturbations can be broadly grouped", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-735", "line_number": 735, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-736", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-736", "line_number": 736, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-737", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1. Timing Based: This spans the omission of messages, early, delayed, or out-of-order", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-738", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "messaging. Crashes and denial-of-service also fall in this group as they typically manifest", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-738", "line_number": 738, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-739", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as disruptions of the proper temporal delivery of messages by obstructing access to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-739", "line_number": 739, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-740", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the communication channels or resources.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-740", "line_number": 740, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-741", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2. Value/Information Based: Spooﬁng attacks, mimicking, duplication, information leak-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-741", "line_number": 741, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-742", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "age such as a Covert Channel Attack or Side Channel Attack, and content manipulation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-743", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacks broadly fall in this category. The manipulation of the content of messages", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-744", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "manifests as Byzantine behavior. This attack is only viable if a set of resources use", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-745", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the exchange messages to build their global view of the system. A malicious entity", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-746", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "can send deliberately modulated information (e.g., a mixture of correct and incorrect", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-747", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "values) to different groups of resources to result in partitions of system state views.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-748", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Thus, based on different values received by different nodes, the individual nodes are", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-749", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "unable to constitute a “consistent” and correct view of the system state. The degree", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-750", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of breach of consistency (strong – full agreement by all on value and order – weak,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-750", "line_number": 750, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-751", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "partial, eventual) constitutes the degree of disruption. The nature of the underlying", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-752", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "transactional service (e.g., distributed ledgers in Blockchains) determines the type of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-753", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "breach of the functionality. Relating to the groups of vulnerabilities, a Byzantine attack", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-754", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "can abuse access control, message delivery and coordination services, or the data itself", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-755", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(viruses, compromised mobile code, worms) to compromise the system.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-756", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "It should be noted that a perturbation also includes the property of persistence, i.e., the duration", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-756", "line_number": 756, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-757", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of a perturbation can be transient, episodic, intermittent, or permanent in nature. Furthermore,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-757", "line_number": 757, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-758", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacks often entail multiple simultaneous occurrences that involve a combination of timing,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-759", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "value, persistence, and dispersed locations, potentially due to collusion between multiple", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-760", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacking entities.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-760", "line_number": 760, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-761", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Attacks and Implications", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-761", "line_number": 761, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-762", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "On this general background, we now detail the two prominent classes of distributed systems", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-762", "line_number": 762, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-763", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as based on the coordination schema (resource- and service-coordination). This will also", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-763", "line_number": 763, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-764", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "form the system grouping for considering the security manifestations of attacks.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-765", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "We use the classical CIA (Conﬁdentiality, Integrity, and Availability) terminology though the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-766", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "implications of these terms often differ according to the type of system and services. For", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-767", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "each class, the speciﬁcation of its functionality determines the type of attack and the resultant", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-768", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "compromise that detrimentally affects the delivery of services.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-769", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "As mentioned in Section 1.2, the threat surfaces of a distributed system comprise attacks", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-770", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "on the resources, admission control, the communication architectures, the coordination", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-771", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "6The provisioning of message integrity by techniques such as coding, cryptographic primitives, message", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-772", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "acknowledgements, retries, secure group communication, etc. are discussed in [2, 3] and the Cryptography", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-773", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "CyBOK Knowledge Area [67].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-774", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 23", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-775", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 25", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-776", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "mechanisms, and the data. Similarly, attacks aim to subvert the assumptions behind the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-777", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "functionality of resources, the services, and the underlying coordination schema.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-778", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "In the following subsection, we enumerate some attack scenarios for the resources/infrastructure", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-779", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and services/application classes of coordination. Given the immense diversity of types of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-780", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resource and services based distributed systems, the purpose of these examples is only to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-781", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "illustrate some potential scenarios. It is also worth highlighting that often a resource attack", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-782", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "does not harm the resource per se but primarily affects the service executing on the resource.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-783", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "5.1", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-784", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The Resource Coordination Class – Infrastructure View", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-785", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This class of “virtualised resource access” primarily deals with the coordination of a group", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-786", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of computing and communication resources to provide an ensemble of highly-available,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-787", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "highly-reliable “platform” of diverse shared resources to the user. This is an infrastructure (vs", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-788", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "applications) view where the user speciﬁes the operational requirements for the desired service", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-789", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(e.g., computational capabilities, number of Virtual Machines (VMs), storage, bandwidth", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-790", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "constraints, etc.) but is agnostic to the actual mechanisms providing the on-demand access", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-791", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to the resources, scalability, physical characteristics, and geo-location/distribution of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-792", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "underlying resources.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-793", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Overall, the key characteristic of this coordination model is the provisioning of high-reliability,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-794", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "high-availability access to resources. The basic resource replication simply provides a pool", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-795", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of resources to support high-availability access. However, the resource replication schema", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-796", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "provides only the “capabilities” to support the services executing on it. Integrity is relevant", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-796", "line_number": 796, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-797", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "corresponding to the service speciﬁcations. For instance, VMs need to provide the speciﬁed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-798", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "level of isolation without information leakage. Similarly, a web server is typically replicated", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-799", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "across machines both for reliability and for low-latency localised geo-dispersed access. Each", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-800", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "replicated server has the same set of data, and any time the data is updated, a copy is updated", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-801", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "across the replicated servers to provide consistency on data. It is the nature of the service", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-802", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(as executing on the resources platform) that determines the type of desired coordination,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-803", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "perhaps as consistency (strong, weak, eventual, causal). This will be the basis of the Service", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-804", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Coordination class discussed later on.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-805", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "We brieﬂy present the Cloud and Client-Server models that constitute prominent examples of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-806", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the class of distributed resources.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-807", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The Cloud Model", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-808", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The Cloud, in all its manifestations, is representative of the resource coordination model", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-809", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as essentially a “resources platform” for services to execute on. There are multiple types", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-810", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of Clouds offering varied types of services ranging across emphasis on high-performance,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-811", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "low-latency access or high-availability amongst many other properties. It is the speciﬁc", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-812", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resource coordination schema dictated by the speciﬁcations of the desired services based on", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-813", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "which the Cloud “platform” provides structured access to the Cloud resources. The chosen", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-813", "line_number": 813, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-814", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordination schema correspondingly supports the type of desired capabilities, for example,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-814", "line_number": 814, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-815", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "access to specialised computing resources and/or resource containers such as physical", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-816", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or virtual machines each offering differing isolation guarantees across the containers. The", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-817", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "user speciﬁed services execute on the Cloud resources, which are managed by the Cloud", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-817", "line_number": 817, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-818", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "service provider. The coordination schema, as a centralised or distributed resource manager,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-818", "line_number": 818, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-819", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "handles the mapping and scheduling of tasks to resources, invoking VMs, health monitoring", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-820", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of resources, fault-handling of failed resources such that the user transparently obtains", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-821", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 24", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-822", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 26", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-823", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sustained access to the resources as per the contractual Service Level Agreements (SLAs)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-824", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "speciﬁed on the Cloud resources. The ENISA [68], NIST [69], and ISO [70] speciﬁcations", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-825", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of Infrastructure as a Service (IaaS) and Platform as a Service (PaaS) are representations", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-826", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of “resources/platforms/infrastructures supporting the services”. The multitude of Cloud", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-827", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "models, architectures, and services existing in practice makes it difﬁcult to project a single", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-828", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "notion of Cloud security. Each speciﬁc resource coordination model is characterized by the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-829", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "types of resource types in the Cloud model, the type of computing architecture as well as the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-830", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "desired functionalities within the Cloud. These include, as a non-exhaustive list, the desired", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-831", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "types of resource fault handling, the chosen approach for handling of service bursts, the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-832", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "type of schemas implemented for resource federation and migration, for task orchestration,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-833", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "scheduling, the desired degree of concurrent access, the supported levels of multi-tenancy", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-834", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "etc.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-835", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "However, from a security perspective, it is useful to de-construct the Cloud into its architectural", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-836", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and functional components that result in the Cloud’s attack surface to consider. Analogous to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-837", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the infrastructure view of a data center being an aggregation of computing and storage re-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-838", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sources, the Cloud is an aggregation of geo-dispersed resources that are available on-demand", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-839", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to the user. The user has resource-location and resource-composition agnostic transparent", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-840", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "access to highly-scalable, highly-available, highly-reliable resource and service virtualisation.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-841", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The user speciﬁes the operational attributes of interest (termed as Service Level Objectives)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-842", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as (a) performance speciﬁcations, (b) reliability, (c) replication and isolation characteristics as", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-843", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "types and number of VMs, (d) latency, (e) security as the level/degree of encryption and other", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-844", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "mechanisms at the computing or communication level and (f) cost parameters for delivery or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-845", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "non-delivery of services in the form of contracts known as Service Level Agreements. The", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-845", "line_number": 845, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-846", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "exact composition of the resources, their location or the mechanisms collating the aggregated", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-846", "line_number": 846, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-847", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resources is transparent to the user. The functional blocks of the Cloud include authentication,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-847", "line_number": 847, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-848", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "access control, admission control, resource brokering, VM invocation, schedulers, monitors,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-849", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "reconﬁguration mechanisms, load balancers, communication infrastructures, user interfaces,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-850", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "storage, and many other functions under the PaaS and IaaS paradigms [70, 68, 69]. These", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-851", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "functional blocks, the physical Cloud resources along with the interfaces across them directly", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-852", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "constitute the attack surface of the Cloud.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-853", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The Client-Server Model", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-854", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Resource groups where a set of dedicated entities (servers – service providers) provide a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-855", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "speciﬁed service (e.g., Web services – ﬁle system servers, name servers, databases, data min-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-856", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ers, web crawlers, etc.) to a set of data consumers (clients). A communication infrastructure,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-857", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "such as the public Internet, a local network, or a combination thereof, links the servers to the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-858", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "clients. This can be monolithic, layered, or hierarchical. Both servers and clients are replicated", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-859", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to either provide a characteristic collective distributed service or for fault tolerance. Note that", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-860", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "we are referring to Client-Server architecture as a resources platform or infrastructure and not", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-861", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the Client-Server services per se. The functionality of a Client-Server infrastructure is derived", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-862", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "from the speciﬁcations of the services using the Client-Server model and from the requisite", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-863", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordination schema underlying it.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-864", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 25", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-865", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 27", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-866", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Attackability Implications (and Mitigation Approaches) on Resource Coor-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-867", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "dination", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-868", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "We now outline some example scenarios for the Cloud though they analogously apply to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-869", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the Client-Server and other resource models as well. The reader is referred to [71, 72] for an", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-870", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "insightful discussion relating security and functionality issues in the Cloud.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-871", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Compromise of Resources: Such attacks impact the Availability of the basic resources.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-872", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Mitigation: Protection can be obtained by using access control schemes (including Firewalls)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-873", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to limit external access to services and network resources. Authorisation processes are set", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-874", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "up for granting of rights along with access control mechanisms that verify the actual rights", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-875", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of access [73]. Other approaches to resource protection include the sandboxing resources", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-876", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "or having a tamper-resistant Trusted Computing Base (TCB) that conducts coordination", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-877", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "handling [2, 3] and enforces resource accesses. While the resource class primarily considers", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-877", "line_number": 877, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-878", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "attacks on the infrastructure, data at-rest or in-motion (as in a data storage facility) can also", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-878", "line_number": 878, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-879", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "be considered as a resource. Consequently, it can be protected using techniques such as", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-879", "line_number": 879, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-880", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "encryption. As the speciﬁcation of a distributed service includes the speciﬁcation of both", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-880", "line_number": 880, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-881", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "normal and anomalous behavior on the use of the data providing the service, this protection", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-881", "line_number": 881, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-882", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is considered under the services class.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-882", "line_number": 882, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-883", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Other manifestation of resource attacks, including on communication channels, aim to parti-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-883", "line_number": 883, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-884", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tion resources (and overlying services). The implication here is on Availability for the resources", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-884", "line_number": 884, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-885", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and on Integrity for the services.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-885", "line_number": 885, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-886", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Compromise of Access/Admission Control: This comprises the broad categories of Mas-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-886", "line_number": 886, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-887", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "querading, Spooﬁng, and ID management attacks. The implication on the resources is on", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-887", "line_number": 887, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-888", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Availability, though both the Integrity and Conﬁdentiality of the data/service are affected. In", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-888", "line_number": 888, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-889", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "case of a DoS attack, the consequence is on resource Availability.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-889", "line_number": 889, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-890", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Mitigation: Intrusion Detection System (IDS) constitute typical mitigation approaches. These", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-890", "line_number": 890, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-891", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are complemented by periodic or random ID authentication queries. The periodic checking of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-891", "line_number": 891, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-892", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "system state is used to establish the sanity of IDs.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-892", "line_number": 892, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-893", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Compromise of VM: The typical manifestation is of information leakage from the VM via a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-893", "line_number": 893, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-894", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Covert Channel Attack or Side Channel Attack or similar attacks. The consequence is the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-894", "line_number": 894, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-895", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "violation of Integrity and Conﬁdentiality of the services provisioned by the VM.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-895", "line_number": 895, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-896", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Mitigation: A variety of schemes for VM’s protection are detailed in [43] (also see the Operating", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-896", "line_number": 896, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-897", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Systems & Virtualisation CyBOK Knowledge Area [4]). There are three aspects to be considered", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-897", "line_number": 897, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-898", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "here as the detection of leakage, the system level where the leakage transpires, and the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-898", "line_number": 898, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-899", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "handling of leakage. Taint analysis is a powerful technique for data level detection. As", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-899", "line_number": 899, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-900", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "covert/side-channel attacks often happen at the hardware level and are inﬂuenced by the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-900", "line_number": 900, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-901", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "schedulers, the use of detectors employing hardware performance counters is a generally", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-901", "line_number": 901, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-902", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "used technique as advocated in [74]. System level handling of VM compromises often starts", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-902", "line_number": 902, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-903", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "from the level of tightening the speciﬁcation of trust assumptions and validating them being", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-903", "line_number": 903, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-904", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "upheld using analytical, formal, or experimental stress techniques. Hypervisors are commonly", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-904", "line_number": 904, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-905", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "used for the enforcement of VM operations.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-905", "line_number": 905, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-906", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Compromise of Scheduler: There are two manifestations of such attacks. When the scheduler", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-906", "line_number": 906, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-907", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is affected and this results in an anomalous task or resource allocation, such a deviation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-907", "line_number": 907, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-908", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(on an incorrect resource allocation) can be detected through Access Control. In the case", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-908", "line_number": 908, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-909", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of a malicious takeover of the scheduler, the likely resultant inconsistencies across the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-909", "line_number": 909, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-910", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 26", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-910", "line_number": 910, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-911", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 28", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-911", "line_number": 911, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-912", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "system state or resource-task bindings can be ﬁltered by the coordination schema whose", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-912", "line_number": 912, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-913", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "job is to maintain a consistent state. Such attacks typically impact Availability and Integrity.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-913", "line_number": 913, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-914", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Conﬁdentiality is not breached.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-914", "line_number": 914, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-915", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Mitigation: As mentioned in the attack description, Access Control and coordination constructs", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-915", "line_number": 915, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-916", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "are used to check the consistency of the system state for any observed mis-match to the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-916", "line_number": 916, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-917", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "legitimate or allowed set of resource allocations. This can be used identify corruptions of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-917", "line_number": 917, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-918", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "scheduler.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-918", "line_number": 918, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-919", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Compromise of Broker: This occurrence, within a Cloud resource manager/broker or an", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-919", "line_number": 919, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-920", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "inter-Cloud broker, primarily impacts resource Availability.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-920", "line_number": 920, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-921", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Mitigation: Approaches similar to scheduler compromise mitigation are used here. If backup", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-921", "line_number": 921, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-922", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "brokers are part of the design, that is a typical fall back, otherwise, system stops are often the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-922", "line_number": 922, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-923", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "solution.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-923", "line_number": 923, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-924", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Compromise on Communication: As communication is a core functionality to achieve re-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-924", "line_number": 924, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-925", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "source coordination, this has strong implications on the resources to stay coordinated and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-925", "line_number": 925, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-926", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "directly impacts Availability. The consequent inability to support replication, resource to task", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-926", "line_number": 926, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-927", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "allocation, etc. fundamentally compromises the functionality of the system.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-927", "line_number": 927, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-928", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Mitigation: A variety of communication protection techniques are presented in the Network", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-928", "line_number": 928, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-929", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Security CyBOK Knowledge Area [10]. These include retries, ACK/NACK based schemes,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-929", "line_number": 929, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-930", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "cryptographically secured channels among others.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-930", "line_number": 930, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-931", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Compromise on Monitoring and Accounting: With incorrect information on the state of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-931", "line_number": 931, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-932", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "system and/or services, this can lead to compromise of Conﬁdentiality, Integrity, and Avail-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-932", "line_number": 932, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-933", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ability.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-933", "line_number": 933, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-934", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Mitigation: State consistency schemes are the typical mechanism utilised here. It is worth", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-934", "line_number": 934, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-935", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "mentioning that the replication and coordination concepts presented in Sections 4 and 4.4", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-935", "line_number": 935, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-936", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "form the basis of the mitigation approaches. The very purpose of the replication management", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-936", "line_number": 936, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-937", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is to obtain consistent system states to circumvent disruptions.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-937", "line_number": 937, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-938", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "5.2", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-938", "line_number": 938, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-939", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The Services Coordination Class – Applications View", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-939", "line_number": 939, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-940", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The service coordination model focuses on the speciﬁc characteristics of the services that", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-940", "line_number": 940, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-941", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "determine the degree/type of coordination relevant to supporting that service. For example,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-941", "line_number": 941, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-942", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a database hosted on a Cloud necessarily requires the provision of integrity in the form of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-942", "line_number": 942, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-943", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ACID7 properties along with liveness. Distributed storage, such as KVS (Key Value Store) or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-943", "line_number": 943, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-944", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "transactional database services, may require varied levels of consistency or linearisability", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-944", "line_number": 944, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-945", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "where the desired level of integrity may depend on the level of data-access latency feasible in", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-945", "line_number": 945, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-946", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the system. The broad class of Web services to include Web crawlers and search engines", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-946", "line_number": 946, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-947", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "may require weak or partial consistency as per CAP. On the other hand, Blockchains or", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-947", "line_number": 947, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-948", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ledger queries, that provide distributed crypto based consensus, have strong consistency", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-948", "line_number": 948, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-949", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "(and traceable auditing) as a key requirement with lesser demands on latency. Thus, it is the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-949", "line_number": 949, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-950", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "speciﬁcation of the service (KVS, Database, Blockchain) that determines the nature of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-950", "line_number": 950, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-951", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordination schema for the distributed resources platform.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-951", "line_number": 951, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-952", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "We present some characteristic examples of the services class as:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-952", "line_number": 952, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-953", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "7A stands for atomic, C for consistent, I for isolated and D for durable.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-953", "line_number": 953, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-954", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 27", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-954", "line_number": 954, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-955", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 29", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-955", "line_number": 955, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-956", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Web Services: These cover the spectrum of data mining, web crawlers, information servers,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-956", "line_number": 956, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-957", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "support for e-transactions, etc. This is a fairly broad, and generic, category, which encom-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-957", "line_number": 957, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-958", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "passes a wide variety of services. It is useful to note that many of these services utilise the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-958", "line_number": 958, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-959", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Client-Server paradigm though our interest here is at the services level.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-959", "line_number": 959, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-960", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Key Distribution: This is a broad class of (Authorisation & Authentication) services such", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-960", "line_number": 960, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-961", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as Kerberos, PKI, etc. Such services typically enable authentication (either proving server", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-961", "line_number": 961, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-962", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "authenticity to a client, or mutually authenticating both client and server) over insecure net-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-962", "line_number": 962, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-963", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "works, based on various cryptographic protocols. Authentication services commonly act as", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-963", "line_number": 963, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-964", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "trusted third party for interacting entities in a distributed system. For further details see the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-964", "line_number": 964, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-965", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Authentication, Authorisation & Accountability CyBOK Knowledge Area [9].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-965", "line_number": 965, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-966", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Storage/KVS", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-966", "line_number": 966, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-967", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This is a diverse set of services starting from register level distributed read-writes that entail", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-967", "line_number": 967, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-968", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "strong consistency with very low latency. Another general model is Key Value Store (KVS)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-968", "line_number": 968, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-969", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "where data is accessed via keys/pointers/maps with simple read, write, delete types of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-969", "line_number": 969, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-970", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "semantics. In KVS, the data is represented as a collection of key-value pairs, such that each", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-970", "line_number": 970, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-971", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "possible key appears at most once in the collection with a focus on fast access times (up to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-971", "line_number": 971, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-972", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "a constant access time). The key-value model is one of the simplest non-trivial data models,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-972", "line_number": 972, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-973", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and richer data models are often implemented as extensions with speciﬁed properties. For", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-973", "line_number": 973, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-974", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "example, an ordered model can be developed that maintains the keys in a lexicographic order", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-974", "line_number": 974, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-975", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to efﬁciently retrieve selective key ranges. Key-value stores can use consistency models", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-975", "line_number": 975, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-976", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ranging from eventual consistency to strict consistency. The security issues requires dealing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-976", "line_number": 976, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-977", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "with data-at-rest (static storage) and data-in-transit (dynamic R/W ops).", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-977", "line_number": 977, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-978", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Transactional Services, Databases", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-978", "line_number": 978, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-979", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This is a wide class of services covering databases and general transactional services (re-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-979", "line_number": 979, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-980", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "trieval, informational data mining, banking and stock transactions, etc.). The requirements are", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-980", "line_number": 980, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-981", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "consistency as in banking where all the debit and credit transactions are (strongly or weakly)", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-981", "line_number": 981, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-982", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "serializable for consistency. More generally, a database adheres to all of the stipulated ACID", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-982", "line_number": 982, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-983", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "properties.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-983", "line_number": 983, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-984", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "On the other hand, a number of data mining and information lookup transactions only require", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-984", "line_number": 984, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-985", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "weaker nuances of consistency. For example, an information lookup process can work with", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-985", "line_number": 985, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-986", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "physically partitioned data centers resulting in stale or inconsistent information as long as", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-986", "line_number": 986, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-987", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "they are eventually reconcilable within some speciﬁcation of the service requirements. The", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-987", "line_number": 987, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-988", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "speciﬁcation of the type and degree of perturbations and level of consistency the services are", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-988", "line_number": 988, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-989", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "designed to be resilient to determines the speciﬁc coordination schema to use. Additionally,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-989", "line_number": 989, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-990", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "in the case of weaker consistency models, the user is required to deal with any stale data that", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-990", "line_number": 990, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-991", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "might have been retrieved from the database.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-991", "line_number": 991, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-992", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Blockchains/Cryptocurrencies", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-992", "line_number": 992, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-993", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The concept of a ledger provides for consistent bookkeeping on transactions. This is problem-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-993", "line_number": 993, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-994", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "atic to achieve in a distributed system where the participating entities do not trust each other", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-994", "line_number": 994, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-995", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and are potentially untrustworthy. Blockchains provide a decentralised, distributed, and public", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-995", "line_number": 995, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-996", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ledger that is used to record transactions across many computers so that the record cannot", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-996", "line_number": 996, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-997", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "be altered retroactively without also altering all subsequent blocks. Such alterations require", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-997", "line_number": 997, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-998", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the consensus of the network and can therefore not be performed unilaterally by an attacker.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-998", "line_number": 998, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-999", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "This also allows the participants to verify and audit transactions inexpensively. Blockchains", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-999", "line_number": 999, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1000", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "form the foundation for numerous cryptocurrencies, most notable Bitcoin.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1000", "line_number": 1000, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1001", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 28", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1001", "line_number": 1001, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1002", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 30", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1002", "line_number": 1002, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1003", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "In technical terms, a Blockchain is a list of records or blocks. The aforementioned properties", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1003", "line_number": 1003, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1004", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "arise from the fact that each block incorporates a cryptographic hash of the previous block", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1004", "line_number": 1004, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1005", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and a timestamp. If a block in the chain is altered without also altering all subsequent blocks,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1005", "line_number": 1005, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1006", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the hash of the following block will no longer match, making the tampering on the Blockchain", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1006", "line_number": 1006, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1007", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "detectable.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1007", "line_number": 1007, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1008", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "When used as distributed ledgers, Blockchains are typically managed by peer-to-peer networks.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1008", "line_number": 1008, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1009", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Peers in such a network participate in a protocol for validating newly submitted blocks.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1009", "line_number": 1009, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1010", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Blockchains are also examples of widely deployed systems exhibiting high tolerance to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1010", "line_number": 1010, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1011", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Byzantine failures.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1011", "line_number": 1011, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1012", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The generic Blockchain concept allows participation by any entity (permission-less systems,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1012", "line_number": 1012, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1013", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "public blockhains) and does not include any access restrictions. This is the case for the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1013", "line_number": 1013, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1014", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "blockchains underlying many widely used cryptocurrencies such as Bitcoin. However, a more", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1014", "line_number": 1014, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1015", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "restrictive participation model (permissioned systems, private blockchains) is also possible,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1015", "line_number": 1015, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1016", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "where a “validating authority” grants permission for participation.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1016", "line_number": 1016, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1017", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "In order to deter denial of service attacks and other service abuses such as spam on a network,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1017", "line_number": 1017, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1018", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the concept of Proof-of-Work (PoW) (i.e., spending processing time to perform computationally", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1018", "line_number": 1018, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1019", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "expensive tasks) is speciﬁed as a requirement for participation. This is effective as a means", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1019", "line_number": 1019, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1020", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of preventing service abuses such as spam since the required work is typically hard to perform", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1020", "line_number": 1020, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1021", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "but easy to verify, leading to asymmetric requirements for service requester and provider.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1021", "line_number": 1021, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1022", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "However, PoW schemes also lead to high energy usage and, depending on the chosen work", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1022", "line_number": 1022, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1023", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "requirement, may lead to unreasonably high barriers of entry. This is the case, for instance, in", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1023", "line_number": 1023, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1024", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "certain cryptocurrencies, where meaningful participation requires custom hardware designed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1024", "line_number": 1024, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1025", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "for the speciﬁc type of work required. To avoid these shortcomings, alternative approaches", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1025", "line_number": 1025, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1026", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "relying on Proof-of-Stake (PoS) are in development but not as mature as PoW-based schemes", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1026", "line_number": 1026, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1027", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and not widely deployed.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1027", "line_number": 1027, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1028", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "A comprehensive discussion on Blockchain issues appears in [75, 76]. As a note, Blockchains", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1028", "line_number": 1028, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1029", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "represent an interesting combination of decentralised resources using the P2P model for the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1029", "line_number": 1029, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1030", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resource coordination and the coordination schema of consensus for its service functionality.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1030", "line_number": 1030, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1031", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Overall, service integrity, in terms of consensus as supported by requisite liveness, is the key", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1031", "line_number": 1031, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1032", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "characteristic of the service coordination model. This contrasts with the resource coordination", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1032", "line_number": 1032, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1033", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "class where resource accessibility and availability were the dominant drivers/considerations.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1033", "line_number": 1033, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1034", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Attackability Implications (and Mitigation Approaches) on Service Coordi-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1034", "line_number": 1034, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1035", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "nation", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1035", "line_number": 1035, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1036", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The services and applications constitute a very broad class to cover, both for the type of attacks", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1036", "line_number": 1036, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1037", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and the diversity of services where the functional speciﬁcation of the service determines the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1037", "line_number": 1037, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1038", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "type and degree of the impact on security. In most cases the breach on Integrity, along with on", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1038", "line_number": 1038, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1039", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Conﬁdentiality, is the ﬁrst class impact with impact on Availability following as a consequence.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1039", "line_number": 1039, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1040", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Some examples of breaches for the coordination schema and service types are mentioned", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1040", "line_number": 1040, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1041", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "below.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1041", "line_number": 1041, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1042", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Note: The mitigation schemes applicable here are the same as described in Section 5.1", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1042", "line_number": 1042, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1043", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "that essentially result from the basic replication management and coordination concepts", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1043", "line_number": 1043, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1044", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "presented in Sections 4 and 4.4. The very purpose of replication based coordination, at the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1044", "line_number": 1044, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1045", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resource or the service level, is to prevent compromises by discrete attacks up to the threshold", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1045", "line_number": 1045, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1046", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of severity type and the number of disruptions the replication schema is designed to handle.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1046", "line_number": 1046, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1047", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 29", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1047", "line_number": 1047, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1048", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 31", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1048", "line_number": 1048, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1049", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Compromise of Key distribution in PKI: The authentication processes supporting the distribu-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1049", "line_number": 1049, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1050", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tion of public keys is compromised affecting service Integrity and Conﬁdentiality.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1050", "line_number": 1050, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1051", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Compromise of Data at Rest: This is analogous to the breach of resources in the resource", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1051", "line_number": 1051, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1052", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "coordination model as applicable to storage systems.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1052", "line_number": 1052, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1053", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Compromise of Data in Motion: This has varied consistency and latency consequences that", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1053", "line_number": 1053, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1054", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "compromise the Integrity depending on the speciﬁcations of the services. We present a very", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1054", "line_number": 1054, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1055", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "simplistic enumeration using transactions classes as:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1055", "line_number": 1055, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1056", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Short transactions: (Storage/KVS, etc.) The major driver for this class is both consistency", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1056", "line_number": 1056, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1057", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and low latency (e.g., linearisability). As both liveness and safety are violated, the Integrity of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1057", "line_number": 1057, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1058", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the transaction is compromised. It is worth noting that a DoS attack may not affect consistency.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1058", "line_number": 1058, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1059", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "However, as latency is affected, the service Integrity is lost.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1059", "line_number": 1059, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1060", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Large transactions: Ledgers (Blockchain, etc.) lie in this category where, although latency", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1060", "line_number": 1060, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1061", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is important, it is the Integrity (as deﬁned by the consistency of the ledger) that is the primary", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1061", "line_number": 1061, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1062", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "property to preserve. As Ledgers constitute a popular service, we discuss it to illustrate", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1062", "line_number": 1062, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1063", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "aspects of both attack surfaces and assumptions.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1063", "line_number": 1063, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1064", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "To recapitulate from Section 5.2, Blockchains constitute a ledger of information that is dis-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1064", "line_number": 1064, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1065", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "persed across a distributed system. Blockchains ensure the security of data by not providing a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1065", "line_number": 1065, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1066", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "single point of attack. The ledger is stored in multiple copies on a network of computers. Each", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1066", "line_number": 1066, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1067", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "time an authorised participant (for example in a permissioned system) submits a transaction", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1067", "line_number": 1067, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1068", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "to the ledger, the other participants conduct checks to ensure that the transaction is valid,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1068", "line_number": 1068, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1069", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and such valid transactions (as blocks) are added to the ledger chain. Consensus ensures a", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1069", "line_number": 1069, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1070", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "consistent view of the sequence of transactions and the collated outcome. The cryptographic", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1070", "line_number": 1070, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1071", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "basis of the hash, on each block, is expected to avoid tampering, and the Proof of Work notion", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1071", "line_number": 1071, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1072", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "is designed to mitigate the effect of DoS attacks.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1072", "line_number": 1072, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1073", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "What makes this system theoretically tamper proof are two aspects: (a) an unforgeable cryp-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1073", "line_number": 1073, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1074", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "tographic hash linking the blocks, and (b) attack-resilient consensus by which the distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1074", "line_number": 1074, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1075", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "participants agree on a shared history of transactions.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1075", "line_number": 1075, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1076", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Compromising these involves the compromise of stored cryptographic keys and the hash.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1076", "line_number": 1076, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1077", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "While theoretically safe, such systems may turn out to be vulnerable to emergent technolo-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1077", "line_number": 1077, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1078", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "gies such as quantum computing. Moreover, while Proof of Work requirements (i.e., “to", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1078", "line_number": 1078, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1079", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "demonstrate” a greater than 50% participant agreement) can make collusion attacks pro-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1079", "line_number": 1079, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1080", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "hibitively expensive in sufﬁciently large systems, they can be feasible on systems with fewer", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1080", "line_number": 1080, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1081", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "participants.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1081", "line_number": 1081, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1082", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Similarly, the consensus property can be compromised via an Eclipse attack [77] for Bitcoin,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1082", "line_number": 1082, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1083", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and also in general cases where there exists the potential to trick nodes into wasting computing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1083", "line_number": 1083, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1084", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "power. Nodes on the Blockchain must remain in constant communication in order to compare", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1084", "line_number": 1084, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1085", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "data. An attacker that can take control of a node’s communication and spoof other nodes", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1085", "line_number": 1085, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1086", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "into accepting false data to result in wasted computing or conﬁrming fake transactions can", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1086", "line_number": 1086, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1087", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "potentially breach consensus. The work in [76] provides useful reading on such compromises.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1087", "line_number": 1087, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1088", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Mixed transactions: As implied in the label, this combines short and large transactions. The", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1088", "line_number": 1088, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1089", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "security implications depend on the type of services. As an example, we outline two service", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1089", "line_number": 1089, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1090", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "groups, namely:", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1090", "line_number": 1090, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1091", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- E-commerce supporting transactions: The core requirements here are ACID properties", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1091", "line_number": 1091, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1092", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "that entail strong consistency and no partitions. Any compromises affect the Integrity of the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1092", "line_number": 1092, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1093", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 30", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1093", "line_number": 1093, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1094", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 32", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1094", "line_number": 1094, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1095", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "service.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1095", "line_number": 1095, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1096", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "- Informational systems: Services such as Webcrawlers, Data Retrieval for applications such", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1096", "line_number": 1096, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1097", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "as Uber or informational queries for shopping can handle (both network and data) partitions", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1097", "line_number": 1097, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1098", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of data to operate on stale cached data. The attack may lead to redundant computations on", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1098", "line_number": 1098, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1099", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the searches or slightly stale information but Integrity is not violated as long as the semantics", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1099", "line_number": 1099, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1100", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of Weak, Relaxed, or Eventual consistency, as applicable for the service speciﬁcation, are", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1100", "line_number": 1100, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1101", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "sustained. Also informational queries have mixed latency requirements. For example, the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1101", "line_number": 1101, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1102", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "small latency within a local data center and higher-tolerable latency across geo-dispersed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1102", "line_number": 1102, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1103", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "data centers may deﬁne the degree of attack tolerance until both Availability and Integrity are", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1103", "line_number": 1103, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1104", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "compromised.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1104", "line_number": 1104, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1105", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "CONCLUSIONS", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1105", "line_number": 1105, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1106", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The intent of this chapter has been to outline how distributed systems work, and how the", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1106", "line_number": 1106, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1107", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "mechanisms supporting the operations of such systems open security issues in them. Very", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1107", "line_number": 1107, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1108", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "often the expectation is that classical security techniques will directly apply in a distributed", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1108", "line_number": 1108, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1109", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "systems context as well. However, this is often not the case and the better one understands", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1109", "line_number": 1109, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1110", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "the conceptual basis of a distributed system, the better one can understand and provide for", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1110", "line_number": 1110, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1111", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "its security. The KA discussed the functional categorisation of distributed systems into two", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1111", "line_number": 1111, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1112", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "major classes: decentralised and coordinated control. The operations for each class were", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1112", "line_number": 1112, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1113", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "elaborated leading to the security implications resulting from the different speciﬁcs underlying", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1113", "line_number": 1113, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1114", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "distributed systems.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1114", "line_number": 1114, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1115", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "CROSS-REFERENCE OF TOPICS VS REFERENCE MATERIAL", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1115", "line_number": 1115, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1116", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[12]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1116", "line_number": 1116, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1117", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[1]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1117", "line_number": 1117, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1118", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[2]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1118", "line_number": 1118, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1119", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[3]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1119", "line_number": 1119, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1120", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[23]", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1120", "line_number": 1120, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1121", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1 Classes of Distributed Systems and Vulnerabilities", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1121", "line_number": 1121, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1124", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c18", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1124", "line_number": 1124, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1125", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "2 Distributed Systems: Decentralised P2P Models", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1125", "line_number": 1125, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1126", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c11,c12", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1126", "line_number": 1126, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1127", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c25", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1127", "line_number": 1127, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1128", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "3 Distributed Systems: Attacking P2P Systems", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1128", "line_number": 1128, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1129", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c16", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1129", "line_number": 1129, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1131", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "4 Distributed Systems: Coordinated Resource Clustering", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1131", "line_number": 1131, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1132", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c5-7,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1132", "line_number": 1132, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1133", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c12,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1133", "line_number": 1133, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1134", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c25", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1134", "line_number": 1134, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1136", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c5,c14", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1136", "line_number": 1136, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1137", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c16,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1137", "line_number": 1137, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1138", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c17,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1138", "line_number": 1138, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1139", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c19", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1139", "line_number": 1139, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1140", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "5 Distributed Systems: Coordination Classes and Attackability", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1140", "line_number": 1140, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1142", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c5-6", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1142", "line_number": 1142, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1143", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c19", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1143", "line_number": 1143, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1144", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "c18", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1144", "line_number": 1144, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1146", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "FURTHER READING", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1146", "line_number": 1146, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1147", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "The following books are recommended for a deeper coverage of the distributed system and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1147", "line_number": 1147, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1148", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "security concepts.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1148", "line_number": 1148, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1149", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 31", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1149", "line_number": 1149, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1150", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "## Page 34", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1150", "line_number": 1150, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1151", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[12] N. Lynch, Distributed Algorithms.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1151", "line_number": 1151, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1152", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Morgan Kaufmann, 1996.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1152", "line_number": 1152, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1153", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[13] P. Eugster, P. Felber, R. Guerraoui, and A.-M. Kermarrec, “The many faces of pub-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1153", "line_number": 1153, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1154", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "lish/subscribe,” ACM Computing Surveys, vol. 35, no. 2, pp. 114–131, 2003.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1154", "line_number": 1154, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1155", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[14] M. Ripeanu, “Peer-to-peer architecture case study: Gnutella network,” in Peer-to-Peer", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1155", "line_number": 1155, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1156", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Computing, 2001, pp. 99–100.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1156", "line_number": 1156, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1157", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[15] Y. Chawathe, S. Ratnasamy, L. Breslau, N. Lanham, and S. Shenker, “Making Gnutella-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1157", "line_number": 1157, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1158", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "like P2P systems scalable,” in Proc. of Applications, Technologies, Architectures, and", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1158", "line_number": 1158, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1159", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Protocols for Computer Communications, ser. SIGCOMM.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1159", "line_number": 1159, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1160", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ACM, 2003, pp. 407–418.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1160", "line_number": 1160, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1161", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[16] I. Stoica et al., “Chord: A scalable peer-to-peer lookup service for internet applications,” In", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1161", "line_number": 1161, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1162", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Proc. SIGCOMM, pp. 149 – 160, 2001.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1162", "line_number": 1162, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1163", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[17] A. Rowstron and P. Druschel, “Pastry: Scalable, decentralized object location, and routing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1163", "line_number": 1163, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1164", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "for large-scale peer-to-peer systems,” Proc. Middleware, pp. 329–350, 2001.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1164", "line_number": 1164, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1165", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[18] B. Zhao, L. Huang, J. Stribling, S. Rhea, A. Joseph, and J. Kubiatowicz, “Tapestry: A", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1165", "line_number": 1165, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1166", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "resilient global-scale overlay for service deployment,” IEEE Journal on Selected Areas in", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1166", "line_number": 1166, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1167", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Communications, vol. 22, no. 1, pp. 41–53, Jan 2004.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1167", "line_number": 1167, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1168", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[19] P. Maymounkov and D. Mazi`eres, “Kademlia: A peer-to-peer information system based", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1168", "line_number": 1168, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1169", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "on the XOR metric,” In Proc. IPTPS, pp. 53 – 65, 2002.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1169", "line_number": 1169, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1170", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[20] B. Cohen, “BitTorrent protocol speciﬁcation,” BitTorrent, Tech. Rep., 2008. [Online].", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1170", "line_number": 1170, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1171", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Available: http://www.bittorrent.org/beps/bep 0003.htm", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1171", "line_number": 1171, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1172", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[21] B. Yang and H. Garcia-Molina, “Comparing hybrid peer-to-peer systems,” Proc. of VLDB,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1172", "line_number": 1172, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1173", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "pp. 561–570, 2001.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1173", "line_number": 1173, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1174", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[22] L. Garc´es-Erice, E. W. Biersack, K. W. Ross, P. Felber, and G. Urvoy-Keller, “Hierarchical", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1174", "line_number": 1174, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1175", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "peer-to-peer systems,” Parallel Processing Letters, vol. 13, no. 4, pp. 643–657, 2003.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1175", "line_number": 1175, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1176", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[23] F. Swiderski and W. Snyder, Threat Modeling.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1176", "line_number": 1176, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1177", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Springer, 2003.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1177", "line_number": 1177, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1178", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[24] A. Avizienis, J.-C. Laprie, B. Randell, and C. Landwehr, “Basic concepts and taxonomy of", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1178", "line_number": 1178, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1179", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "dependable and secure computing,” IEEE Transactions on Dependable Secure Computing,", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1179", "line_number": 1179, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1180", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "vol. 1, no. 1, pp. 11–33, Jan. 2004.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1180", "line_number": 1180, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1181", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[25] C. Esposito and M. Ciampi, “On security in publish/subscribe services: A survey,” IEEE", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1181", "line_number": 1181, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1182", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Communication Surveys and Tutorials, vol. 17, no. 2, 2015.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1182", "line_number": 1182, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1183", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[26] A. Uzunov, “A survey of security solutions for distributed publish/subscribe systems,”", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1183", "line_number": 1183, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1184", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Computers and Security, vol. 61, pp. 94–129, 2016.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1184", "line_number": 1184, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1185", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[27] A. Walters, D. Zage, and C. Rotaru, “A framework for mitigating attacks against", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1185", "line_number": 1185, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1186", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "measurement-based adaptation mechanisms in unstructured multicast overlay networks,”", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1186", "line_number": 1186, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1187", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "IEEE/ACM Trans on Networking, vol. 16, no. 6, pp. 1434–1446, Dec 2008.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1187", "line_number": 1187, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1188", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[28] T. Isdal, M. Piatek, and A. Krishnamurthy, “Privacy preserving P2P data sharing with", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1188", "line_number": 1188, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1189", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Oneswarm,” SIGCOMM, 2011.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1189", "line_number": 1189, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1190", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[29] J. Seibert, X. Sun, C. Nita-Rotaru, and S. Rao, “Towards securing data delivery in peer-to-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1190", "line_number": 1190, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1191", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "peer streaming,” in Proc. Communication Systems and Networks (COMSNETS), 2010, pp.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1191", "line_number": 1191, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1192", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "1–10.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1192", "line_number": 1192, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1193", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[30] R. Barra de Almeida, J. Miranda Natif, A. Couto da Silva, and A. Borges Vieira, “Pollution", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1193", "line_number": 1193, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1194", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "and whitewashing attacks in a P2P live streaming system: Analysis and counter-attack,”", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1194", "line_number": 1194, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1195", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "in IEEE Intl. Conf on Communications (ICC), June 2013, pp. 2006–2010.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1195", "line_number": 1195, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1196", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[31] J. Liang, N. Naoumov, and K. Ross, “The Index Poisoning Attack in P2P File Sharing", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1196", "line_number": 1196, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1197", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Systems,” in INFOCOM, 2006, pp. 1–12.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1197", "line_number": 1197, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1198", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[32] N. Naoumov and K. Ross, “Exploiting P2P systems for DDoS attacks,” in ACM Proceedings", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1198", "line_number": 1198, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1199", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "of Scalable Information Systems, 2006.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1199", "line_number": 1199, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1200", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[33] D. Li, J. Wu, and Y. Cui, “Defending against buffer map cheating in DONet-like P2P stream-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1200", "line_number": 1200, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1201", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "ing,” IEEE Trans. Multimedia, vol. 11, no. 3, pp. 535–542, April 2009.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1201", "line_number": 1201, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1202", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "[34] J. R. Douceur, “The sybil attack,” in Intl. Workshop on Peer-to-Peer Systems.", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1202", "line_number": 1202, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1203", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Springer-", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1203", "line_number": 1203, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
{"chunk_id": "line-1204", "filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "content": "Page 33", "metadata": {"filename": "Distributed_Systems_Security_v1.0.1_processed.txt", "chunk_id": "line-1204", "line_number": 1204, "source": "知识库\\output\\Distributed_Systems_Security_v1.0.1_processed.txt"}}
