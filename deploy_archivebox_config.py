#!/usr/bin/env python3
"""
部署ArchiveBox配置文件
"""

import os
import shutil
import subprocess

def deploy_config():
    """部署ArchiveBox配置"""
    print("=== 部署ArchiveBox配置 ===")
    
    # 源配置文件
    source_config = "archivebox_wechat_config.conf"
    
    # 目标路径
    archivebox_data_path = "/home/<USER>/docker_archivebox/Archive_box_data"
    target_config = os.path.join(archivebox_data_path, "ArchiveBox.conf")
    
    try:
        # 检查源文件是否存在
        if not os.path.exists(source_config):
            print(f"❌ 源配置文件不存在: {source_config}")
            return False
        
        # 检查目标目录是否存在
        if not os.path.exists(archivebox_data_path):
            print(f"❌ ArchiveBox数据目录不存在: {archivebox_data_path}")
            return False
        
        # 备份现有配置（如果存在）
        if os.path.exists(target_config):
            backup_config = target_config + ".backup"
            shutil.copy2(target_config, backup_config)
            print(f"✓ 备份现有配置到: {backup_config}")
        
        # 复制新配置
        shutil.copy2(source_config, target_config)
        print(f"✓ 部署配置文件到: {target_config}")
        
        # 设置权限
        os.chmod(target_config, 0o644)
        print("✓ 设置配置文件权限")
        
        return True
        
    except Exception as e:
        print(f"❌ 部署配置失败: {e}")
        return False

def check_chrome_binary():
    """检查Chrome二进制文件"""
    print("\n=== 检查Chrome二进制文件 ===")
    
    chrome_paths = [
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser", 
        "/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome"
    ]
    
    for chrome_path in chrome_paths:
        if os.path.exists(chrome_path):
            print(f"✓ 找到Chrome: {chrome_path}")
            try:
                # 测试Chrome版本
                result = subprocess.run([chrome_path, "--version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"  版本: {result.stdout.strip()}")
                    return chrome_path
            except Exception as e:
                print(f"  ⚠️ 无法获取版本: {e}")
        else:
            print(f"❌ 未找到: {chrome_path}")
    
    print("⚠️ 未找到可用的Chrome二进制文件")
    return None

def test_archivebox_config():
    """测试ArchiveBox配置"""
    print("\n=== 测试ArchiveBox配置 ===")
    
    archivebox_data_path = "/home/<USER>/docker_archivebox/Archive_box_data"
    
    try:
        # 切换到ArchiveBox目录并测试配置
        cmd = f"cd {archivebox_data_path} && archivebox config"
        result = subprocess.run(cmd, shell=True, capture_output=True, 
                              text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ ArchiveBox配置测试成功")
            print("配置摘要:")
            # 显示关键配置项
            lines = result.stdout.split('\n')
            key_configs = ['TIMEOUT', 'CHROME_BINARY', 'CHROME_USER_AGENT', 
                          'SAVE_SINGLEFILE', 'SAVE_READABILITY']
            for line in lines:
                for key in key_configs:
                    if key in line:
                        print(f"  {line.strip()}")
                        break
        else:
            print(f"❌ ArchiveBox配置测试失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 测试配置时出错: {e}")

def update_chrome_binary_in_config(chrome_path):
    """更新配置文件中的Chrome路径"""
    if not chrome_path:
        return
    
    print(f"\n=== 更新Chrome路径为: {chrome_path} ===")
    
    config_file = "/home/<USER>/docker_archivebox/Archive_box_data/ArchiveBox.conf"
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新Chrome路径
        lines = content.split('\n')
        updated_lines = []
        
        for line in lines:
            if line.startswith('CHROME_BINARY = '):
                updated_lines.append(f'CHROME_BINARY = {chrome_path}')
                print(f"✓ 更新Chrome路径: {chrome_path}")
            else:
                updated_lines.append(line)
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(updated_lines))
        
        print("✓ 配置文件更新完成")
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")

def main():
    """主函数"""
    print("ArchiveBox配置部署工具")
    print("=" * 50)

    # 1. 检查Chrome
    chrome_path = check_chrome_binary()

    # 2. 转换微信Cookie（如果存在）
    if os.path.exists("WX_Cookie.txt"):
        print("\n=== 转换微信Cookie ===")
        try:
            import subprocess
            result = subprocess.run(["python", "convert_wx_cookie.py"],
                                  capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print("✓ 微信Cookie转换成功")
            else:
                print(f"⚠️ 微信Cookie转换失败: {result.stderr}")
        except Exception as e:
            print(f"⚠️ 微信Cookie转换出错: {e}")

    # 3. 部署配置
    if deploy_config():
        # 4. 更新Chrome路径
        if chrome_path:
            update_chrome_binary_in_config(chrome_path)

        # 5. 测试配置
        test_archivebox_config()

        print("\n" + "=" * 50)
        print("✅ 配置部署完成！")
        print("\n建议:")
        print("1. 重启ArchiveBox服务（如果在运行）")
        print("2. 测试微信公众号链接归档")
        print("3. 检查归档日志确认配置生效")
        print("4. Cookie文件位置: /home/<USER>/docker_archivebox/Archive_box_data/WX_Cookie_Netscape.txt")

    else:
        print("\n❌ 配置部署失败")

if __name__ == "__main__":
    main()
