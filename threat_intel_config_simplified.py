"""
高质量威胁情报采集配置文件（精简版）
用于配置 ArchiveBox 集成的威胁情报采集系统
"""

# 高质量威胁情报域名列表（按语言分类）
# 这些都是经过验证的、持续更新的威胁情报来源

# 动态加载威胁情报域名配置
try:
    from simple_config_loader import load_international_domains, load_chinese_domains

    # 从数据库动态加载域名配置
    INTERNATIONAL_DOMAINS = load_international_domains()
    CHINESE_DOMAINS = load_chinese_domains()

    print(f"✓ 成功从数据库加载配置: 国际域名 {len(INTERNATIONAL_DOMAINS)} 个, 中文域名 {len(CHINESE_DOMAINS)} 个")

except Exception as e:
    print(f"⚠️ 从数据库加载配置失败: {e}")
    print("使用备用硬编码配置...")

    # 备用硬编码配置
    INTERNATIONAL_DOMAINS = [
        # === 国外知名安全厂商威胁情报博客（每周/每月更新） ===

        # 2. 微软威胁情报博客
        "www.microsoft.com/en-us/security/blog/topic/threat-intelligence",
        "www.microsoft.com/en-us/security/blog",

        # 4. Trellix 威胁研究博客
        "www.trellix.com/blogs/research",

        # 5. Symantec Enterprise 威胁情报
        "www.security.com/threat-intelligence",

        # 6. ASEC AhnLab 威胁分析
        "asec.ahnlab.com/en",

        # 7. Fortinet 威胁研究博客
        "www.fortinet.com/blog/threat-research",

        # 8. Infoblox 威胁情报博客
        "blogs.infoblox.com/category/threat-intelligence",

        # 9. 卡巴斯基 Securelist - 多个威胁分类
        "securelist.com",  # 主站点
        "securelist.com/threat-category/spam-and-phishing",
        "securelist.com/threat-category/vulnerabilities-and-exploits",
        "securelist.com/threat-category/apt-targeted-attacks",

        # 10. Proofpoint 威胁洞察
        "www.proofpoint.com/us/blog/threat-insight",

        # 11. ESET WeliveSecurity 威胁研究
        "www.welivesecurity.com/en/about-eset-research",

        # 12. 趋势科技威胁研究
        "www.trendmicro.com/en_us/research.html",

        # 14. Cofense 威胁分析博客
        "cofense.com/blog",

        # 15. SentinelOne 前线威胁分析
        "www.sentinelone.com/blog/category/from-the-front-lines",

        # 17. The Hacker News 威胁情报
        "thehackernews.com",

        # 18. PolySwarm 威胁分析
        "blog.polyswarm.io",

        # 19. Cyfirma 威胁情报研究
        "cyfirma.com/research/",

        # 20. Unit 42 Palo Alto Networks 威胁研究
        "unit42.paloaltonetworks.com/category/threat-research/",

        # 21. Trustwave SpiderLabs 博客
        "trustwave.com/en-us/resources/blogs/spiderlabs-blog",

        "darktrace.com/blog"
    ]

    # 国内中文威胁情报域名
    CHINESE_DOMAINS = [
        # === 国内威胁情报源 ===

        # 1. 360 网络实验室博客
        "ti.qianxin.com/blog",

        # 2. 奇安信威胁情报
        "ti.dbappsecurity.com.cn/info",

        # 3. SecRSS 威胁态势
        "www.secrss.com/articles",

        # 4. 绿盟科技威胁告警
        "blog.nsfocus.net",
    ]

# 合并所有域名列表（为了向后兼容）
HIGH_QUALITY_DOMAINS = INTERNATIONAL_DOMAINS + CHINESE_DOMAINS

# RSS源备用列表
RSS_FEEDS = {
    "Google威胁情报RSS": "feeds.feedburner.com/threatintelligence/pvexyqv7v0v",
    "CheckPoint RSS": "research.checkpoint.com/category/threat-research/feed",
    "HackerNews RSS": "hackernews.cc/feed"
}


# 英文威胁情报关键词
ENGLISH_KEYWORDS = [
    # === 核心威胁类型 ===
    "APT", "malware", "ransomware", "phishing", "vulnerability", "exploit",
    "threat intelligence", "cybersecurity", "zero-day", "IOC", "botnet", "backdoor",
    
    # === 事件响应和分析 ===
    "incident response", "threat hunting", "attack", "breach", "CVE",
    "indicators of compromise", "malware analysis", "reverse engineering",
    "forensics", "sandbox", "YARA",
    
    # === 高级威胁技术 ===
    "advanced persistent threat", "supply chain attack", "fileless malware",
    "living off the land", "lateral movement", "data exfiltration",
    "command and control", "C&C", "persistence",
    
    # === 特定威胁家族和工具 ===
    "Emotet", "Trickbot", "Cobalt Strike", "Mimikatz",
    "PowerShell Empire", "Metasploit", "BloodHound", "Lazarus",
    "DarkHalo", "SolarWinds", "Hafnium", "APT29", "APT28",
    
    # === 行业和地域威胁 ===
    "critical infrastructure", "industrial control", "healthcare security",
    "financial security", "government security", "military cyber",
    
    # === 新兴威胁 ===
    "AI security", "cloud security", "IoT security", "5G security",
    "quantum cryptography", "deepfake"
]

# 中文威胁情报关键词
CHINESE_KEYWORDS = [
    # === 核心威胁类型 ===
    "高级持续性威胁", "恶意软件", "勒索软件", "钓鱼攻击", "漏洞", "漏洞利用",
    "威胁情报", "网络安全", "零日漏洞", "失陷指标", "僵尸网络", "后门",
    
    # === 事件响应和分析 ===
    "事件响应", "威胁狩猎", "攻击", "数据泄露", "恶意软件分析", "逆向工程",
    "取证分析", "沙箱分析", "YARA规则", "威胁指标",
    
    # === 高级威胁技术 ===
    "供应链攻击", "无文件攻击", "白利用攻击", "横向移动", "数据窃取",
    "命令控制", "持久化",
    
    # === 行业和地域威胁 ===
    "关键基础设施", "工控安全", "医疗安全", "金融安全", "政府安全", "军事网络",
    
    # === 新兴威胁 ===
    "人工智能安全", "云安全", "物联网安全", "5G安全", "量子密码", "深度伪造",
    
    # === 中文特色关键词 ===
    "网络攻击", "数据安全", "信息安全", "安全事件", "病毒分析", "木马分析",
    "安全报告", "威胁分析", "攻击活动", "安全预警", "漏洞预警", "APT组织"
]

# 合并所有关键词列表（为了向后兼容）
THREAT_KEYWORDS = ENGLISH_KEYWORDS + CHINESE_KEYWORDS

# 搜索配置 (针对中英文混合环境优化)
SEARCH_CONFIG = {
    "max_domains_for_keyword_search": 8,   # 减少域名数，聚焦核心源
    "max_keywords_for_domain_search": 5,   # 减少关键词数，聚焦核心威胁
    "batch_size": 3,                       # 批处理大小
    "archivebox_timeout": 300,             # ArchiveBox 归档超时时间(秒)
    "min_content_length": 100,             # 最小内容长度
    "max_results_per_query": 10,           # 每个查询的最大结果数
    "chinese_keyword_ratio": 0.3,          # 中文关键词比例
    "enable_rss_fallback": False,          # 精简版禁用RSS
    "enable_cross_domain_search": True,    # 启用跨域名搜索
    "special_domain_handling": {           # 特殊域名处理
        "www.secrss.com": "tag=威胁态势",    # SecRSS需要特殊tag参数
        "research.checkpoint.com/category/threat-research": "archivebox_issues=true",  # CheckPoint威胁研究路径
        "cloud.google.com/blog/topics/threat-intelligence": "google_blog_format=true",  # Google威胁情报博客格式
        "www.microsoft.com/en-us/security/blog/topic/threat-intelligence": "microsoft_blog_format=true",  # 微软威胁情报博客格式
        "securelist.com": "multiple_categories=true",  # 卡巴斯基多分类处理
        "www.trendmicro.com/en_us/research.html": "research_page_format=true" # 趋势科技研究页面格式

    }
}

def generate_high_quality_search_syntax():
    """
    生成高质量搜索语法列表
    
    优化要求：
    1. 使用精准的威胁情报博客路径，提高检索精度
    2. 英文域名只与英文关键词组合，中文域名只与中文关键词组合
    3. 使用多种逻辑组合（OR、AND）和不同长度的关键词组合
    4. 增加每个域名的搜索组合数量，提高覆盖率
    5. 不添加时间范围
    
    Returns:
        list: 优化后的搜索语法列表
    """
    search_syntax = []
    
    # 核心关键词定义（分层级）
    core_english_keywords = ["APT", "malware", "ransomware", "vulnerability", "threat intelligence", "zero-day", "phishing", "botnet", "backdoor", "exploit"]
    core_chinese_keywords = ["APT组织", "恶意软件", "勒索软件", "漏洞", "威胁情报", "零日漏洞", "钓鱼攻击", "僵尸网络", "后门", "漏洞利用"]
    
    # 短关键词组合（高频核心词）
    short_english_keywords = ["APT", "malware", "ransomware", "zero-day"]
    short_chinese_keywords = ["APT组织", "恶意软件", "勒索软件", "零日漏洞"]
    
    # 中等长度关键词组合
    medium_english_keywords = ["APT", "malware", "vulnerability", "threat intelligence", "phishing", "exploit"]
    medium_chinese_keywords = ["APT组织", "恶意软件", "漏洞", "威胁情报", "钓鱼攻击", "漏洞利用"]
    
    # 特定威胁类型关键词
    specific_english_keywords = ["IOC", "CVE", "breach", "attack", "campaign", "analysis"]
    specific_chinese_keywords = ["失陷指标", "漏洞编号", "数据泄露", "攻击", "攻击活动", "分析"]
    
    # 高级威胁技术关键词
    advanced_english_keywords = ["advanced persistent threat", "supply chain attack", "fileless malware", "lateral movement"]
    advanced_chinese_keywords = ["高级持续性威胁", "供应链攻击", "无文件攻击", "横向移动"]
    
    # 1. 英文精准路径 + 多种关键词组合（每个域名生成3-4个搜索语法）
    for i, domain_path in enumerate(INTERNATIONAL_DOMAINS):
        # 第一组：基础核心关键词 OR 组合
        keywords_combination = " OR ".join([f'"{keyword}"' for keyword in core_english_keywords])
        search_syntax.append(f'site:{domain_path} ({keywords_combination})')
        
        # 第二组：短关键词 AND 组合
        keywords_combination = " AND ".join([f'"{keyword}"' for keyword in short_english_keywords[:2]])
        search_syntax.append(f'site:{domain_path} ({keywords_combination})')
        
        # 第三组：特定威胁类型
        if i % 3 == 0:  # 每3个域名使用不同的特定关键词
            keywords_combination = " OR ".join([f'"{keyword}"' for keyword in specific_english_keywords])
            search_syntax.append(f'site:{domain_path} ({keywords_combination})')
        elif i % 3 == 1:
            keywords_combination = " OR ".join([f'"{keyword}"' for keyword in advanced_english_keywords])
            search_syntax.append(f'site:{domain_path} ({keywords_combination})')
        else:
            # 混合组合：核心词 + 特定词
            mixed_combination = f'"{core_english_keywords[0]}" OR "{specific_english_keywords[0]}" OR "{medium_english_keywords[2]}"'
            search_syntax.append(f'site:{domain_path} ({mixed_combination})')
    
    # 2. 中文域名 + 多种关键词组合（每个域名生成3个搜索语法）
    for i, domain in enumerate(CHINESE_DOMAINS):
        # 第一组：完整核心关键词 OR 组合
        keywords_combination = " OR ".join([f'"{keyword}"' for keyword in core_chinese_keywords])
        search_syntax.append(f'site:{domain} ({keywords_combination})')
        
        # 第二组：短关键词 AND 组合
        keywords_combination = " AND ".join([f'"{keyword}"' for keyword in short_chinese_keywords[:2]])
        search_syntax.append(f'site:{domain} ({keywords_combination})')
        
        # 第三组：特定威胁类型
        if i % 2 == 0:
            keywords_combination = " OR ".join([f'"{keyword}"' for keyword in specific_chinese_keywords])
            search_syntax.append(f'site:{domain} ({keywords_combination})')
        else:
            keywords_combination = " OR ".join([f'"{keyword}"' for keyword in advanced_chinese_keywords])
            search_syntax.append(f'site:{domain} ({keywords_combination})')
    
    # 3. 多域名核心威胁检索（限制在威胁情报域名范围内）
    cross_domain_searches = [
        '"APT" AND "threat intelligence"',
        '"ransomware" AND "analysis"',
        '"zero-day" AND "vulnerability"',
        '"malware" OR "backdoor" OR "exploit"',
        '"APT组织" AND "威胁情报"',
        '"恶意软件" AND "分析"',
        '"phishing" AND "campaign"',
        '"IOC" AND "analysis"',
        '"CVE" AND "vulnerability"',
        '"breach" AND "incident"',
        '"attack" AND "campaign"',
        '"威胁狩猎" AND "分析"',
        '"数据泄露" AND "事件"',
        '"攻击活动" AND "分析"'
    ]
    
    # 生成多域名组合（限制在前5个高质量域名）
    top_domains = INTERNATIONAL_DOMAINS[:3] + CHINESE_DOMAINS[:2]  # 选择5个核心域名
    
    for search in cross_domain_searches:
        # 为每个跨域名搜索生成多域名组合
        domain_combination = " OR ".join([f"site:{domain}" for domain in top_domains])
        search_syntax.append(f"({domain_combination}) AND ({search})")
    
    # 4. 特定威胁家族和工具检索（限制在威胁情报域名）
    threat_families = [
        '"Emotet" OR "Trickbot" OR "Cobalt Strike"',
        '"Mimikatz" OR "PowerShell Empire"',
        '"Lazarus" OR "APT29" OR "APT28"',
        '"SolarWinds" OR "Hafnium"',
        '"DarkHalo" OR "supply chain"'
    ]
    
    for threat in threat_families:
        # 限制在核心威胁情报域名
        domain_combination = " OR ".join([f"site:{domain}" for domain in top_domains])
        search_syntax.append(f"({domain_combination}) AND ({threat})")
    
    # 5. 行业特定威胁检索（限制在威胁情报域名）
    industry_searches = [
        '"healthcare security" OR "medical" OR "hospital"',
        '"financial security" OR "banking" OR "fintech"',
        '"critical infrastructure" OR "industrial control"',
        '"government security" OR "military cyber"'
    ]
    
    for industry in industry_searches:
        # 限制在核心威胁情报域名
        domain_combination = " OR ".join([f"site:{domain}" for domain in top_domains])
        search_syntax.append(f"({domain_combination}) AND ({industry})")
    
    # 6. 新兴威胁检索（限制在威胁情报域名）
    emerging_threats = [
        '"AI security" OR "artificial intelligence threat"',
        '"cloud security" OR "AWS" OR "Azure"',
        '"IoT security" OR "5G security"',
        '"deepfake" OR "quantum cryptography"'
    ]
    
    for emerging in emerging_threats:
        # 限制在核心威胁情报域名
        domain_combination = " OR ".join([f"site:{domain}" for domain in top_domains])
        search_syntax.append(f"({domain_combination}) AND ({emerging})")
    
    print(f"生成完成，共 {len(search_syntax)} 条高质量搜索语法（扩展版）")
    return search_syntax

def get_search_statistics():
    """
    获取搜索配置统计信息（精简版）
    """
    syntax_list = generate_high_quality_search_syntax()
    
    # 精简版统计
    core_english_keywords = ["APT", "malware", "ransomware", "vulnerability", "threat intelligence", "zero-day", "phishing", "botnet", "backdoor", "exploit"]
    core_chinese_keywords = ["APT组织", "恶意软件", "勒索软件", "漏洞", "威胁情报", "零日漏洞", "钓鱼攻击", "僵尸网络", "后门", "漏洞利用"]
    
    # 计算各类搜索数量（使用多种逻辑组合）
    english_combinations = len(INTERNATIONAL_DOMAINS) * 3  # 每个英文域名3个组合
    chinese_combinations = len(CHINESE_DOMAINS) * 3  # 每个中文域名3个组合
    cross_domain_searches = 14  # 14个跨域名核心检索
    threat_family_searches = 5   # 5个威胁家族检索
    industry_searches = 4        # 4个行业特定检索
    emerging_searches = 4        # 4个新兴威胁检索
    
    stats = {
        "total_domains": len(HIGH_QUALITY_DOMAINS),
        "international_domains": len(INTERNATIONAL_DOMAINS),
        "chinese_domains": len(CHINESE_DOMAINS),
        "total_keywords": len(THREAT_KEYWORDS),
        "english_keywords": len(ENGLISH_KEYWORDS),
        "chinese_keywords": len(CHINESE_KEYWORDS),
        "core_english_keywords": len(core_english_keywords),
        "core_chinese_keywords": len(core_chinese_keywords),
        "total_search_syntax": len(syntax_list),
        "english_domain_combinations": english_combinations,
        "chinese_domain_combinations": chinese_combinations,
        "wechat_searches": 0,  # 已去掉微信公众号检索
        "cross_domain_searches": cross_domain_searches,
        "threat_family_searches": threat_family_searches,
        "industry_searches": industry_searches,
        "emerging_searches": emerging_searches,
        "rss_backup_searches": 0,  # 精简版不包含RSS
        "wechat_reference_sources": 0,  # 已去掉微信公众号参考
        "time_range_enabled": False,  # 精简版不包含时间范围
        "domain_only_searches": 0,   # 已移除单独域名搜索
        "domain_coverage": "100%",   # 覆盖所有域名
        "search_logic": "扩展版多样化组合",  # 新增：搜索逻辑说明
    }
    
    return stats

if __name__ == "__main__":

    stats = get_search_statistics()
    
    print(" 总体统计:")
    print(f"  威胁情报域名总数: {stats['total_domains']} (覆盖率: {stats['domain_coverage']})")
    print(f"  - 国际域名: {stats['international_domains']}")
    print(f"  - 中文域名: {stats['chinese_domains']}")
    print(f"  威胁关键词总数: {stats['total_keywords']}")
    print(f"  - 英文关键词: {stats['english_keywords']} (核心: {stats['core_english_keywords']})")
    print(f"  - 中文关键词: {stats['chinese_keywords']} (核心: {stats['core_chinese_keywords']})")
    print(f"  生成搜索语法总数: {stats['total_search_syntax']} (精简优化)")
    
    print(f"\n 搜索策略分布:")
    print(f"  英文域名+关键词组合: {stats['english_domain_combinations']}")
    print(f"  中文域名+关键词组合: {stats['chinese_domain_combinations']}")
    print(f"  跨域名核心威胁检索: {stats['cross_domain_searches']}")
    print(f"  威胁家族和工具检索: {stats['threat_family_searches']}")
    print(f"  行业特定威胁检索: {stats['industry_searches']}")
    print(f"  新兴威胁检索: {stats['emerging_searches']}")
    print(f"  单独域名检索: {stats['domain_only_searches']} (已移除)")
    
    print(f"\n 精简优化:")
    print(f"  时间范围: {'✓ 启用' if stats['time_range_enabled'] else '✗ 精简版已移除'}")
    print(f"  域名覆盖: ✓ 覆盖所有 {stats['total_domains']} 个域名")
    print(f"  关键词策略: ✓ 使用核心高频关键词")
    print(f"  语法质量: ✓ 高质量精简版")

    
    print(f"\n 重点威胁情报源:")
    for i, domain in enumerate(INTERNATIONAL_DOMAINS[:5], 1):
        print(f"  {i}. {domain}")
        
    for i, domain in enumerate(CHINESE_DOMAINS, 1):
        print(f"  {i}. {domain}")
    
    print("\n=== 搜索语法示例 ===")
    syntax_list = generate_high_quality_search_syntax()
    
    print("英文域名+关键词:")
    english_syntax = [s for s in syntax_list if 'site:' in s and any(domain in s for domain in INTERNATIONAL_DOMAINS[:3])]
    for i, syntax in enumerate(english_syntax[:3]):
        print(f"  {i+1}. {syntax}")
    
    print("\n中文域名+关键词:")
    chinese_syntax = [s for s in syntax_list if 'site:' in s and any(domain in s for domain in CHINESE_DOMAINS[:3])]
    for i, syntax in enumerate(chinese_syntax[:3]):
        print(f"  {i+1}. {syntax}")
        
    print("\n跨域名检索:")
    cross_domain_syntax = [s for s in syntax_list if 'site:' not in s]
    for i, syntax in enumerate(cross_domain_syntax[:3]):
        print(f"  {i+1}. {syntax}")
    
    print(f"\n... 总计 {len(syntax_list)} 条高质量搜索语法")
    