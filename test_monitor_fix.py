#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控线程修复
"""

import sys
import os

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'Page_view/backend'))

def test_method_access():
    """测试方法访问是否正确"""
    print("=== 测试方法访问 ===")
    
    try:
        from task_manager import TaskManager, WeeklyTaskScheduler
        
        # 数据库配置
        DB_CONFIG = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Qjq123456',
            'database': 'cti_database',
            'charset': 'utf8mb4'
        }
        
        # 创建任务管理器
        task_manager = TaskManager(DB_CONFIG)
        print("✓ TaskManager 创建成功")
        
        # 测试 _is_weekly_crawl_running 方法
        is_running = task_manager._is_weekly_crawl_running()
        print(f"✓ TaskManager._is_weekly_crawl_running() 调用成功: {is_running}")
        
        # 创建调度器
        scheduler = WeeklyTaskScheduler(task_manager)
        print("✓ WeeklyTaskScheduler 创建成功")
        
        # 测试调度器通过task_manager访问方法
        is_running_via_scheduler = scheduler.task_manager._is_weekly_crawl_running()
        print(f"✓ WeeklyTaskScheduler.task_manager._is_weekly_crawl_running() 调用成功: {is_running_via_scheduler}")
        
        print("\n所有方法访问测试通过！")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("监控线程修复测试")
    print("=" * 30)
    test_method_access()
