{"chunk_id": "line-1", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 4", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-2", "filename": "Forensics_v1.0.1_processed.txt", "content": "INTRODUCTION", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-3", "filename": "Forensics_v1.0.1_processed.txt", "content": "Digital forensic science, or digital forensics, is the application of scientiﬁc tools and methods", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-4", "filename": "Forensics_v1.0.1_processed.txt", "content": "to identify, collect and analyse digital (data) artifacts in support of legal proceedings. From a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-5", "filename": "Forensics_v1.0.1_processed.txt", "content": "technical perspective, it is the process of identifying and reconstructing the relevant sequence", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-6", "filename": "Forensics_v1.0.1_processed.txt", "content": "of events that has led to the currently observable state of a target IT system or (digital) artifacts.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-7", "filename": "Forensics_v1.0.1_processed.txt", "content": "The importance of digital evidence has grown in lockstep with the fast societal adoption of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-8", "filename": "Forensics_v1.0.1_processed.txt", "content": "information technology, which has resulted in the continuous accumulation of data at an", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-9", "filename": "Forensics_v1.0.1_processed.txt", "content": "exponential rate. Simultaneously, there has been rapid growth in network connectivity and the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-10", "filename": "Forensics_v1.0.1_processed.txt", "content": "complexity of IT systems, leading to more complex behaviour that may need investigation.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-11", "filename": "Forensics_v1.0.1_processed.txt", "content": "The primary purpose of this Knowledge Area is to provide a technical overview of digital", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-12", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic techniques and capabilities, and to put them into a broader perspective with regard", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-13", "filename": "Forensics_v1.0.1_processed.txt", "content": "to other related areas in the cybersecurity domain. The discussion on legal aspects of digital", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-14", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensics is limited only to general principles and best practices, as the speciﬁcs of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-15", "filename": "Forensics_v1.0.1_processed.txt", "content": "application of these principles tend to vary across jurisdictions. For example, the Knowledge", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-16", "filename": "Forensics_v1.0.1_processed.txt", "content": "Area discusses the availability of different types of evidence, but does not work through", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-17", "filename": "Forensics_v1.0.1_processed.txt", "content": "the legal processes that have to be followed to obtain them. The Law & Regulation CyBOK", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-18", "filename": "Forensics_v1.0.1_processed.txt", "content": "Knowledge Area [1] discusses speciﬁc concerns related to jurisdiction and the legal process", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-19", "filename": "Forensics_v1.0.1_processed.txt", "content": "to obtain, process, and present digital evidence.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-20", "filename": "Forensics_v1.0.1_processed.txt", "content": "CONTENT", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-22", "filename": "Forensics_v1.0.1_processed.txt", "content": "DEFINITIONS AND CONCEPTUAL MODELS", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-23", "filename": "Forensics_v1.0.1_processed.txt", "content": "[2, 3, 4, 5, 6]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-24", "filename": "Forensics_v1.0.1_processed.txt", "content": "Broadly, forensic science is the application of scientiﬁc methods to collect, preserve and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-25", "filename": "Forensics_v1.0.1_processed.txt", "content": "analyse evidence related to legal cases [2]. Historically, this involved the systematic analysis", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-26", "filename": "Forensics_v1.0.1_processed.txt", "content": "of (samples of) physical material in order to establish causal relationships between various", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-27", "filename": "Forensics_v1.0.1_processed.txt", "content": "events, as well as to address issues of provenance and authenticity. The rationale behind it,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-28", "filename": "Forensics_v1.0.1_processed.txt", "content": "Locard’s exchange principle, is that physical contact between objects inevitably results in the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-29", "filename": "Forensics_v1.0.1_processed.txt", "content": "exchange of matter, leaving traces that can be analysed to (partially) reconstruct the event.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-30", "filename": "Forensics_v1.0.1_processed.txt", "content": "With the introduction of digital computing and communication, which we refer to as the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-31", "filename": "Forensics_v1.0.1_processed.txt", "content": "cyber domain, the same general assumptions were extended largely unchallenged. Although a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-32", "filename": "Forensics_v1.0.1_processed.txt", "content": "detailed conceptual discussion is beyond the scope of this chapter, it is important to recognise", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-33", "filename": "Forensics_v1.0.1_processed.txt", "content": "that the presence of a persistent digital (forensic) trace is neither inevitable, nor is it a “natural”", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-34", "filename": "Forensics_v1.0.1_processed.txt", "content": "consequence of the processing and communication of digital information.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-35", "filename": "Forensics_v1.0.1_processed.txt", "content": "A digital (forensic) trace is an explicit, or implicit, record that testiﬁes to the execution of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-36", "filename": "Forensics_v1.0.1_processed.txt", "content": "speciﬁc computations, or the communication and/or storage of speciﬁc data. These events", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-37", "filename": "Forensics_v1.0.1_processed.txt", "content": "can be the result of human-computer interaction, such as a user launching an application, or", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-38", "filename": "Forensics_v1.0.1_processed.txt", "content": "they can be the result of the autonomous operation of the IT system (e.g., scheduled backup).", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-39", "filename": "Forensics_v1.0.1_processed.txt", "content": "Explicit traces directly record the occurrence of certain types of events as part of the normal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-40", "filename": "Forensics_v1.0.1_processed.txt", "content": "operation of the system; most prominently, these include a variety of timestamped system", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-41", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-42", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 5", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-43", "filename": "Forensics_v1.0.1_processed.txt", "content": "and application event logs. Implicit traces take many forms, and allow the occurrence of some", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-44", "filename": "Forensics_v1.0.1_processed.txt", "content": "events to be deduced from the observed state of the system, or artifact, and engineering", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-45", "filename": "Forensics_v1.0.1_processed.txt", "content": "knowledge of how the system operates. For example, the presence on a storage device of a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-46", "filename": "Forensics_v1.0.1_processed.txt", "content": "unique chunk of data that is part of a known ﬁle can demonstrate that the ﬁle was likely to have", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-47", "filename": "Forensics_v1.0.1_processed.txt", "content": "been present once, and was subsequently deleted and partially overwritten. The observed", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-48", "filename": "Forensics_v1.0.1_processed.txt", "content": "absence of normal log ﬁles can point to a security breach during which the perpetrators wiped", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-49", "filename": "Forensics_v1.0.1_processed.txt", "content": "the system logs as a means to cover their tracks.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-50", "filename": "Forensics_v1.0.1_processed.txt", "content": "Although they frequently exist, these traces of cyber interactions are the result of conscious", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-51", "filename": "Forensics_v1.0.1_processed.txt", "content": "engineering decisions that are not usually taken to speciﬁcally facilitate forensics. This has", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-52", "filename": "Forensics_v1.0.1_processed.txt", "content": "important implications with respect to the provenance and authenticity of digital evidence,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-53", "filename": "Forensics_v1.0.1_processed.txt", "content": "given the ease with which digital information can be modiﬁed.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-54", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.1", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-55", "filename": "Forensics_v1.0.1_processed.txt", "content": "Legal Concerns and the Daubert Standard", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-56", "filename": "Forensics_v1.0.1_processed.txt", "content": "The ﬁrst published accounts of misuse and manipulation of computer systems for illegal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-57", "filename": "Forensics_v1.0.1_processed.txt", "content": "purposes such as theft, espionage and other crimes date back to the 1960s. During the 1970s,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-58", "filename": "Forensics_v1.0.1_processed.txt", "content": "the ﬁrst empirical studies of computer crime were carried out using established criminological", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-59", "filename": "Forensics_v1.0.1_processed.txt", "content": "research methods. In the early-to-mid 1980s, targeted computer crime legislation emerged", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-60", "filename": "Forensics_v1.0.1_processed.txt", "content": "across Europe and North America [7, 8]; in recognition of the inherent cross-jurisdictional", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-61", "filename": "Forensics_v1.0.1_processed.txt", "content": "scope of many such crimes, international cooperation agreements were also put in place.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-62", "filename": "Forensics_v1.0.1_processed.txt", "content": "In the UK, the Computer Misuse Act 1990 [3] deﬁnes computer-speciﬁc crimes – S1 Unautho-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-63", "filename": "Forensics_v1.0.1_processed.txt", "content": "rised Access To Computer Material, S2 Unauthorised Access with Intent to Commit Other", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-64", "filename": "Forensics_v1.0.1_processed.txt", "content": "Offences, S3 Unauthorised Acts with Intent to Impair Operation, and S3A Making, Supplying", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-65", "filename": "Forensics_v1.0.1_processed.txt", "content": "or Obtaining. The Police & Criminal Evidence Act 1984 and Criminal Justice & Police Act 2001", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-66", "filename": "Forensics_v1.0.1_processed.txt", "content": "address computer-speciﬁc concerns with respect to warrants, search and seizure.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-67", "filename": "Forensics_v1.0.1_processed.txt", "content": "In many jurisdictions, legal statutes related to misuse of telecommunications are separate", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-68", "filename": "Forensics_v1.0.1_processed.txt", "content": "(and older than) those related to computer crimes. We use the umbrella term cybercrime to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-69", "filename": "Forensics_v1.0.1_processed.txt", "content": "collectively refer to all crimes related to computer and telecommunications misuse; broadly,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-70", "filename": "Forensics_v1.0.1_processed.txt", "content": "these include the use of cyber systems to commit any type of crime, as well as the criminal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-71", "filename": "Forensics_v1.0.1_processed.txt", "content": "targeting of cyber systems.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-72", "filename": "Forensics_v1.0.1_processed.txt", "content": "As is usually the case, legal systems require time to assimilate new laws and integrate them", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-73", "filename": "Forensics_v1.0.1_processed.txt", "content": "into routine law practice. Conversely, legislation usually requires corrections, clariﬁcation", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-74", "filename": "Forensics_v1.0.1_processed.txt", "content": "and uniﬁed interpretation in response to concerns encountered in the courtroom. One of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-75", "filename": "Forensics_v1.0.1_processed.txt", "content": "the earliest and most inﬂuential legal precedents was set by the US supreme court, which", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-76", "filename": "Forensics_v1.0.1_processed.txt", "content": "used three speciﬁc cases – Daubert v. Merrell Dow Pharmaceuticals, 509 U.S. 579 (1993);", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-77", "filename": "Forensics_v1.0.1_processed.txt", "content": "General Electric Co. v. Joiner, 522 U.S. 136 (1997); and Kumho Tire Co. v. Carmichael, 526 U.S.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-78", "filename": "Forensics_v1.0.1_processed.txt", "content": "137 (1999) – to establish a new standard for the presentation of scientiﬁc evidence in legal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-79", "filename": "Forensics_v1.0.1_processed.txt", "content": "proceedings, often referred to as the Daubert standard [9].", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-80", "filename": "Forensics_v1.0.1_processed.txt", "content": "As per Goodstein [4], “The presentation of scientiﬁc evidence in a court of law is a kind of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-81", "filename": "Forensics_v1.0.1_processed.txt", "content": "shotgun marriage between the two disciplines. ... The Daubert decision is an attempt (not", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-82", "filename": "Forensics_v1.0.1_processed.txt", "content": "the ﬁrst, of course) to regulate that encounter.” These cases set a new standard for expert", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-83", "filename": "Forensics_v1.0.1_processed.txt", "content": "testimony, overhauling the previous Frye standard of 1923 (Frye v. United States, 293 F. 1013,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-84", "filename": "Forensics_v1.0.1_processed.txt", "content": "D.C. Cir. 1923). In brief, the supreme court instructed trial judges to become gatekeepers", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-85", "filename": "Forensics_v1.0.1_processed.txt", "content": "of expert testimony, and gave four basic criteria to evaluate the admissibility of forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-85", "line_number": 85, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-86", "filename": "Forensics_v1.0.1_processed.txt", "content": "evidence:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-87", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 4", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-88", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 6", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-89", "filename": "Forensics_v1.0.1_processed.txt", "content": "1. The theoretical underpinnings of the methods must yield testable predictions by means", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-90", "filename": "Forensics_v1.0.1_processed.txt", "content": "of which the theory could be falsiﬁed.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-91", "filename": "Forensics_v1.0.1_processed.txt", "content": "2. The methods should preferably be published in a peer-reviewed journal.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-92", "filename": "Forensics_v1.0.1_processed.txt", "content": "3. There should be a known rate of error that can be used in evaluating the results.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-93", "filename": "Forensics_v1.0.1_processed.txt", "content": "4. The methods should be generally accepted within the relevant scientiﬁc community.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-94", "filename": "Forensics_v1.0.1_processed.txt", "content": "The court also emphasised that these standards are ﬂexible and that the trial judge has a lot", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-95", "filename": "Forensics_v1.0.1_processed.txt", "content": "of leeway in determining the admissibility of forensic evidence and expert witness testimony.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-96", "filename": "Forensics_v1.0.1_processed.txt", "content": "The Daubert criteria have been broadly accepted, in principle, by other jurisdictions subject to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-97", "filename": "Forensics_v1.0.1_processed.txt", "content": "interpretation in the context of local legislation. In the UK, the Law Commission for England", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-98", "filename": "Forensics_v1.0.1_processed.txt", "content": "and Wales proposed in consultation paper No. 190 [5] the adoption of criteria that build on", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-99", "filename": "Forensics_v1.0.1_processed.txt", "content": "Daubert.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-100", "filename": "Forensics_v1.0.1_processed.txt", "content": "The ACPO Good Practice Guide for Digital Evidence codiﬁes four basic principles for the ac-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-101", "filename": "Forensics_v1.0.1_processed.txt", "content": "quisition and handling of digital evidence:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-102", "filename": "Forensics_v1.0.1_processed.txt", "content": "1. No action taken by law enforcement agencies, persons employed within those agencies", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-103", "filename": "Forensics_v1.0.1_processed.txt", "content": "or their agents should change data which may subsequently be relied upon in court.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-104", "filename": "Forensics_v1.0.1_processed.txt", "content": "2. In circumstances where a person ﬁnds it necessary to access original data, that person", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-105", "filename": "Forensics_v1.0.1_processed.txt", "content": "must be competent to do so and be able to give evidence explaining the relevance and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-106", "filename": "Forensics_v1.0.1_processed.txt", "content": "the implications of their actions.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-107", "filename": "Forensics_v1.0.1_processed.txt", "content": "3. An audit trail or other record of all processes applied to digital evidence should be", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-108", "filename": "Forensics_v1.0.1_processed.txt", "content": "created and preserved. An independent third party should be able to examine those", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-109", "filename": "Forensics_v1.0.1_processed.txt", "content": "processes and achieve the same result.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-110", "filename": "Forensics_v1.0.1_processed.txt", "content": "4. The person in charge of the investigation has overall responsibility for ensuring that the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-111", "filename": "Forensics_v1.0.1_processed.txt", "content": "law and these principles are adhered to.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-112", "filename": "Forensics_v1.0.1_processed.txt", "content": "These principles seek to provide operational guidance to digital forensic investigators on how", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-113", "filename": "Forensics_v1.0.1_processed.txt", "content": "to maintain the integrity of the evidence and the investigative process, such that the evidence", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-114", "filename": "Forensics_v1.0.1_processed.txt", "content": "can be used in a court of law.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-115", "filename": "Forensics_v1.0.1_processed.txt", "content": "In the UK, the forensic science regulator mandates that any provider of digital forensic science", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-116", "filename": "Forensics_v1.0.1_processed.txt", "content": "must be “accredited to BS EN ISO/IEC 17020:2012 for any crime scene activity and BS EN", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-117", "filename": "Forensics_v1.0.1_processed.txt", "content": "ISO/IEC 17025:2005 for any laboratory function (such as the recovery or imaging of electronic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-118", "filename": "Forensics_v1.0.1_processed.txt", "content": "data)” [10]. ISO/IEC 17025 [11] is an international standard specifying general requirements for", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-119", "filename": "Forensics_v1.0.1_processed.txt", "content": "the competence of testing and calibration laboratories; in other words, the certiﬁcation attests", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-120", "filename": "Forensics_v1.0.1_processed.txt", "content": "to the quality and rigour of the processes followed in performing the forensic examination.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-121", "filename": "Forensics_v1.0.1_processed.txt", "content": "In the US, there is no strict legal requirement for digital forensic science providers to be", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-122", "filename": "Forensics_v1.0.1_processed.txt", "content": "certiﬁed to particular standards. Most large federal and state forensic labs do maintain ISO", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-123", "filename": "Forensics_v1.0.1_processed.txt", "content": "17025 certiﬁcations; as of 2019, eighty ﬁve of them have such credentials for the processing", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-124", "filename": "Forensics_v1.0.1_processed.txt", "content": "of digital evidence.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-125", "filename": "Forensics_v1.0.1_processed.txt", "content": "Digital forensic techniques are also applied in a much broader range of inquiries, such as", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-126", "filename": "Forensics_v1.0.1_processed.txt", "content": "internal corporate investigations, that often do not result in formal proceedings in public court.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-127", "filename": "Forensics_v1.0.1_processed.txt", "content": "Despite the fact that investigations may not require the same standard of proof, forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-128", "filename": "Forensics_v1.0.1_processed.txt", "content": "analysts should always follow sound forensic practices in collecting and analysing the artifacts.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-129", "filename": "Forensics_v1.0.1_processed.txt", "content": "This includes adherence to any judicial requirements when working with inherently personal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-130", "filename": "Forensics_v1.0.1_processed.txt", "content": "data, which can be a non-trivial concern when the investigation is multi-jurisdictional. In such", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-131", "filename": "Forensics_v1.0.1_processed.txt", "content": "cases, it is important to seek timely legal advice to preserve the integrity of the inquiry.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-132", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 5", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-133", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 7", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-134", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.2", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-135", "filename": "Forensics_v1.0.1_processed.txt", "content": "Deﬁnitions", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-136", "filename": "Forensics_v1.0.1_processed.txt", "content": "In 2001, the ﬁrst Digital Forensics Research Workshop (DFRWS) was organised in response", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-137", "filename": "Forensics_v1.0.1_processed.txt", "content": "to the need to replace the prevalent ad hoc approach to digital evidence with a systematic,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-138", "filename": "Forensics_v1.0.1_processed.txt", "content": "multi-disciplinary effort to ﬁrmly establish digital forensics as a rigorous scientiﬁc discipline.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-139", "filename": "Forensics_v1.0.1_processed.txt", "content": "The workshop produced an in-depth report outlining a research agenda and provided one of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-140", "filename": "Forensics_v1.0.1_processed.txt", "content": "the most frequently cited deﬁnitions of digital forensic science in the literature:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-141", "filename": "Forensics_v1.0.1_processed.txt", "content": "[DFRWS] Digital forensics is the use of scientiﬁcally derived and proven methods toward", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-142", "filename": "Forensics_v1.0.1_processed.txt", "content": "the preservation, collection, validation, identiﬁcation, analysis, interpretation, documenta-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-143", "filename": "Forensics_v1.0.1_processed.txt", "content": "tion and presentation of digital evidence derived from digital sources for the purpose of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-144", "filename": "Forensics_v1.0.1_processed.txt", "content": "facilitating or furthering the reconstruction of events found to be criminal, or helping to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-145", "filename": "Forensics_v1.0.1_processed.txt", "content": "anticipate unauthorised actions shown to be disruptive to planned operations. [12]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-146", "filename": "Forensics_v1.0.1_processed.txt", "content": "This deﬁnition, although primarily stressing the investigation of criminal actions, also includes", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-147", "filename": "Forensics_v1.0.1_processed.txt", "content": "an anticipatory element, which is typical of the notion of forensics in operational environments,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-148", "filename": "Forensics_v1.0.1_processed.txt", "content": "and brings it closer to incident response and cyber defence activities. In these situations,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-149", "filename": "Forensics_v1.0.1_processed.txt", "content": "the analysis is primarily to identify the vector of attack and the scope of a security incident;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-150", "filename": "Forensics_v1.0.1_processed.txt", "content": "the identiﬁcation of adversaries with any level of certainty is rare and prosecution is not the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-151", "filename": "Forensics_v1.0.1_processed.txt", "content": "typical outcome. In contrast, the reference deﬁnition provided by NIST a few years later [6] is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-152", "filename": "Forensics_v1.0.1_processed.txt", "content": "focused entirely on the legal aspects of forensics, and emphasises the importance of a strict", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-153", "filename": "Forensics_v1.0.1_processed.txt", "content": "chain of custody:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-154", "filename": "Forensics_v1.0.1_processed.txt", "content": "[NIST] Digital forensics (NIST) is considered the application of science to the identiﬁcation,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-155", "filename": "Forensics_v1.0.1_processed.txt", "content": "collection, examination, and analysis of data while preserving the integrity of the informa-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-156", "filename": "Forensics_v1.0.1_processed.txt", "content": "tion and maintaining a strict chain of custody for the data. Data refers to distinct pieces of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-157", "filename": "Forensics_v1.0.1_processed.txt", "content": "digital information that have been formatted in a speciﬁc way. [6]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-158", "filename": "Forensics_v1.0.1_processed.txt", "content": "The above law-centric deﬁnitions provide a litmus test for determining whether speciﬁc", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-159", "filename": "Forensics_v1.0.1_processed.txt", "content": "investigative tools and techniques qualify as being forensic. From a legal perspective, a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-160", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬂexible, open-ended deﬁnition is normal and necessary during legal proceedings to ﬁt the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-161", "filename": "Forensics_v1.0.1_processed.txt", "content": "case. However, from a technical perspective, they do not provide a meaningful starting point;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-162", "filename": "Forensics_v1.0.1_processed.txt", "content": "therefore, we can adapt a reﬁnement of the working deﬁnition ﬁrst introduced in [13]:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-163", "filename": "Forensics_v1.0.1_processed.txt", "content": "[Working] Digital forensics is the process of identifying and reconstructing the relevant", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-164", "filename": "Forensics_v1.0.1_processed.txt", "content": "sequence of events that have led to the currently observable state of a target IT system or", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-165", "filename": "Forensics_v1.0.1_processed.txt", "content": "(digital) artifacts.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-166", "filename": "Forensics_v1.0.1_processed.txt", "content": "The notion of relevance is inherently case-speciﬁc, and a large part of forensic analysts’", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-167", "filename": "Forensics_v1.0.1_processed.txt", "content": "expertise is the ability to identify evidence that concerns the case at hand. Frequently, a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-168", "filename": "Forensics_v1.0.1_processed.txt", "content": "critical component of forensic analysis is the causal attribution of event sequence to speciﬁc", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-169", "filename": "Forensics_v1.0.1_processed.txt", "content": "human actors of the system (such as users, administrators, attackers). The provenance,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-170", "filename": "Forensics_v1.0.1_processed.txt", "content": "reliability, and integrity of the data used as evidence data is of primary importance.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-171", "filename": "Forensics_v1.0.1_processed.txt", "content": "According to this deﬁnition, we can view every effort made to perform system or artifact", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-172", "filename": "Forensics_v1.0.1_processed.txt", "content": "analysis after the fact as a form of digital forensics. This includes common activities such as", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-173", "filename": "Forensics_v1.0.1_processed.txt", "content": "incident response and internal investigations, which almost never result in any legal action.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-174", "filename": "Forensics_v1.0.1_processed.txt", "content": "On balance, only a tiny fraction of forensic analyses make it to the courtroom as formal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-175", "filename": "Forensics_v1.0.1_processed.txt", "content": "evidence, although this should not constrain us from exploring the full spectrum of techniques", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-176", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 6", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-177", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 8", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-178", "filename": "Forensics_v1.0.1_processed.txt", "content": "for reconstructing the past of digital artifacts. The beneﬁt of employing a broader view of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-179", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic computing is that it helps us to identify closely related tools and methods that can", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-180", "filename": "Forensics_v1.0.1_processed.txt", "content": "be adapted and incorporated into forensics.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-181", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-182", "filename": "Forensics_v1.0.1_processed.txt", "content": "Conceptual Models", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-183", "filename": "Forensics_v1.0.1_processed.txt", "content": "In general, there are two possible approaches to rebuilding the relevant sequence of events in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-184", "filename": "Forensics_v1.0.1_processed.txt", "content": "the analysis of a cyber system from the available data sources – state-centric, and history-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-185", "filename": "Forensics_v1.0.1_processed.txt", "content": "centric/log-centric. The starting point for state-centric approaches is a snapshot of the state", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-186", "filename": "Forensics_v1.0.1_processed.txt", "content": "of the system of interest; for example, the current content of a hard drive or another storage", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-187", "filename": "Forensics_v1.0.1_processed.txt", "content": "medium. Using the knowledge of how a particular system/application operates, we can", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-188", "filename": "Forensics_v1.0.1_processed.txt", "content": "deduce a prior state of interest. For example, if unique pieces of a known ﬁle are on the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-189", "filename": "Forensics_v1.0.1_processed.txt", "content": "medium, but the ﬁle is not available via the normal ﬁle system interface, the most likely", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-189", "line_number": 189, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-190", "filename": "Forensics_v1.0.1_processed.txt", "content": "explanation is that the ﬁle was once stored in the ﬁle system but was subsequently deleted", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-191", "filename": "Forensics_v1.0.1_processed.txt", "content": "(the space was marked for reuse) and partially overwritten by newer ﬁles. The main constraint", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-192", "filename": "Forensics_v1.0.1_processed.txt", "content": "here is the dearth of historical data points, which limits our ability to deduce the state of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-193", "filename": "Forensics_v1.0.1_processed.txt", "content": "system at any given point in the past.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-194", "filename": "Forensics_v1.0.1_processed.txt", "content": "Log-centric approaches rely on an explicit, timestamped history of events (a log) that docu-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-195", "filename": "Forensics_v1.0.1_processed.txt", "content": "ments the updates to the system’s state. For example, a packet capture contains the complete", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-196", "filename": "Forensics_v1.0.1_processed.txt", "content": "history of network communications over a period of time. Operating Systems (OSs) maintain", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-197", "filename": "Forensics_v1.0.1_processed.txt", "content": "a variety of monitoring logs that detail various aspects of the operation of the OS kernel and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-198", "filename": "Forensics_v1.0.1_processed.txt", "content": "different applications; additional auditing and security monitoring tools can provide yet more", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-199", "filename": "Forensics_v1.0.1_processed.txt", "content": "potentially relevant events. Many applications, especially in the enterprise domain, provide", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-200", "filename": "Forensics_v1.0.1_processed.txt", "content": "application-level logs. Thus, a log-rich environment contains potentially all the relevant details", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-201", "filename": "Forensics_v1.0.1_processed.txt", "content": "to an investigation; the challenge is to sift through the log entries, which often number in the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-202", "filename": "Forensics_v1.0.1_processed.txt", "content": "millions, to ﬁnd and put together the relevant events.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-203", "filename": "Forensics_v1.0.1_processed.txt", "content": "Historically, storage has been a precious resource in computer systems, leading to software", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-204", "filename": "Forensics_v1.0.1_processed.txt", "content": "designs that emphasise space efﬁciency by updating the information in place, and keeping a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-205", "filename": "Forensics_v1.0.1_processed.txt", "content": "minimal amount of log information. Consequently, the principal approach to forensics has", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-206", "filename": "Forensics_v1.0.1_processed.txt", "content": "been predominantly state-centric.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-207", "filename": "Forensics_v1.0.1_processed.txt", "content": "Over the last ten to ﬁfteen years, technology advances have made storage and bandwidth", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-208", "filename": "Forensics_v1.0.1_processed.txt", "content": "plentiful and affordable, which has led to a massive increase in the amount of log data", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-209", "filename": "Forensics_v1.0.1_processed.txt", "content": "maintained by IT systems and applications. There is a clear trend towards increasing the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-210", "filename": "Forensics_v1.0.1_processed.txt", "content": "amount and granularity of telemetry data being sent over the network by operating systems and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-211", "filename": "Forensics_v1.0.1_processed.txt", "content": "individual applications as part of their normal operations. Consequently, there is a substantial", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-212", "filename": "Forensics_v1.0.1_processed.txt", "content": "need to evolve a forensic methodology such that log information takes on a correspondingly", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-213", "filename": "Forensics_v1.0.1_processed.txt", "content": "higher level of importance. In other words, the current period marks an important evolution", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-214", "filename": "Forensics_v1.0.1_processed.txt", "content": "in digital forensic methodology, one that requires substantial retooling and methodological", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-215", "filename": "Forensics_v1.0.1_processed.txt", "content": "updates.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-216", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 7", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-217", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 9", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-218", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.3.1", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-219", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cognitive Task Model", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-220", "filename": "Forensics_v1.0.1_processed.txt", "content": "Differential analysis [14] is a basic building block of the investigative process, one that is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-221", "filename": "Forensics_v1.0.1_processed.txt", "content": "applied at varying levels of abstraction and to a wide variety of artifacts. However, it does", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-222", "filename": "Forensics_v1.0.1_processed.txt", "content": "not provide an overall view of how forensic experts actually perform an investigation. This is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-223", "filename": "Forensics_v1.0.1_processed.txt", "content": "particularly important in order to build forensic tools that better support cognitive processes.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-224", "filename": "Forensics_v1.0.1_processed.txt", "content": "Unfortunately, digital forensics has not been the subject of any serious interest on the part of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-225", "filename": "Forensics_v1.0.1_processed.txt", "content": "cognitive scientists and there has been no coherent effort to document forensic investigations.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-226", "filename": "Forensics_v1.0.1_processed.txt", "content": "Therefore, we adopt the sense-making process originally developed by Pirolli & Card [15] to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-227", "filename": "Forensics_v1.0.1_processed.txt", "content": "describe intelligence analysis - a cognitive task that is very similar to forensic analysis. The", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-228", "filename": "Forensics_v1.0.1_processed.txt", "content": "Pirolli & Card cognitive model is derived from an in-depth Cognitive Task Analysis (CTA), and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-229", "filename": "Forensics_v1.0.1_processed.txt", "content": "provides a reasonably detailed view of the different aspects of an intelligence analyst’s work.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-230", "filename": "Forensics_v1.0.1_processed.txt", "content": "Although many of the tools are different, forensic and intelligence analysis are very similar in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-231", "filename": "Forensics_v1.0.1_processed.txt", "content": "nature - in both cases analysts have to go through a mountain of raw data to identify (relatively", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-232", "filename": "Forensics_v1.0.1_processed.txt", "content": "few) relevant facts and put them together into a coherent story. The beneﬁt of using this", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-233", "filename": "Forensics_v1.0.1_processed.txt", "content": "model is that: a) it provides a fairly accurate description of the investigative process in its", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-234", "filename": "Forensics_v1.0.1_processed.txt", "content": "own right, and allows us to map the various tools to the different phases of the investigation;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-235", "filename": "Forensics_v1.0.1_processed.txt", "content": "b) it provides a suitable framework for explaining the relationships of the various models", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-236", "filename": "Forensics_v1.0.1_processed.txt", "content": "developed within the area of digital forensics; and c) it can seamlessly incorporate information", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-237", "filename": "Forensics_v1.0.1_processed.txt", "content": "from other lines of the investigation.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-238", "filename": "Forensics_v1.0.1_processed.txt", "content": "The overall process is shown in Figure 1. The rectangular boxes represent different stages", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-239", "filename": "Forensics_v1.0.1_processed.txt", "content": "in the information processing pipeline, starting with raw data and ending with presentable", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-240", "filename": "Forensics_v1.0.1_processed.txt", "content": "results. The arrows indicate transformational processes that move information from one box", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-241", "filename": "Forensics_v1.0.1_processed.txt", "content": "to another. The x axis approximates the overall level of effort required to move information", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-242", "filename": "Forensics_v1.0.1_processed.txt", "content": "from the raw to the speciﬁc processing stage. The y axis shows the amount of structure", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-243", "filename": "Forensics_v1.0.1_processed.txt", "content": "(with respect to the investigative process) in the processed information for every stage. Thus,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-244", "filename": "Forensics_v1.0.1_processed.txt", "content": "the overall trend is to move the relevant information from the lower left-hand to the upper", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-245", "filename": "Forensics_v1.0.1_processed.txt", "content": "right-hand corner of the diagram. In reality, the processing can both meander through multiple", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-246", "filename": "Forensics_v1.0.1_processed.txt", "content": "iterations of local loops and jump over phases (for routine cases handled by an experienced", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-247", "filename": "Forensics_v1.0.1_processed.txt", "content": "investigator).", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-248", "filename": "Forensics_v1.0.1_processed.txt", "content": "External data sources include all potential evidence sources for a speciﬁc investigation such", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-249", "filename": "Forensics_v1.0.1_processed.txt", "content": "as disk images, memory snapshots, network captures and reference databases such as", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-250", "filename": "Forensics_v1.0.1_processed.txt", "content": "hashes of known ﬁles. The shoebox is a subset of all the data that have been identiﬁed as", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-251", "filename": "Forensics_v1.0.1_processed.txt", "content": "potentially relevant, such as all the email communications between two persons of interest.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-252", "filename": "Forensics_v1.0.1_processed.txt", "content": "At any given time, the contents of the shoebox can be viewed as the analyst’s approximation", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-253", "filename": "Forensics_v1.0.1_processed.txt", "content": "of the information content that is potentially relevant to the case. The evidence ﬁle contains", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-254", "filename": "Forensics_v1.0.1_processed.txt", "content": "only the parts that are directly relevant to the case such as speciﬁc email exchanges on a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-255", "filename": "Forensics_v1.0.1_processed.txt", "content": "topic of interest.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-256", "filename": "Forensics_v1.0.1_processed.txt", "content": "The schema contains a more organised version of the evidence such as a timeline of events or", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-257", "filename": "Forensics_v1.0.1_processed.txt", "content": "a graph of relationships, which allows higher-level reasoning over the evidence. A hypothesis", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-258", "filename": "Forensics_v1.0.1_processed.txt", "content": "is a tentative conclusion that explains the observed evidence in the schema and, by extension,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-259", "filename": "Forensics_v1.0.1_processed.txt", "content": "could form the ﬁnal conclusion. Once the analyst is satisﬁed that the hypothesis is supported", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-260", "filename": "Forensics_v1.0.1_processed.txt", "content": "by the evidence, the hypothesis turns into a presentation, which is the ﬁnal product of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-261", "filename": "Forensics_v1.0.1_processed.txt", "content": "process. The presentation usually takes on the form of an investigator’s report that both", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-262", "filename": "Forensics_v1.0.1_processed.txt", "content": "speaks to the high-level conclusions that are relevant to the legal case and also documents", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-263", "filename": "Forensics_v1.0.1_processed.txt", "content": "the low-level technical steps based on which the conclusion has been formed.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-264", "filename": "Forensics_v1.0.1_processed.txt", "content": "The overall analytical process is iterative in nature with two main activities loops: a foraging", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-265", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 8", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-266", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 11", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-267", "filename": "Forensics_v1.0.1_processed.txt", "content": "the trade; however, any method of organising and visualising the facts-graphs, charts,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-268", "filename": "Forensics_v1.0.1_processed.txt", "content": "etc.-can greatly speed up the analysis. This is not an easy process to formalise, and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-269", "filename": "Forensics_v1.0.1_processed.txt", "content": "most forensic tools do not directly support it. Therefore, the resulting schemas may", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-270", "filename": "Forensics_v1.0.1_processed.txt", "content": "exist on a piece of paper, on a whiteboard or only in the mind of the investigator. Since", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-271", "filename": "Forensics_v1.0.1_processed.txt", "content": "the overall case could be quite complicated, individual schemas may cover speciﬁc", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-272", "filename": "Forensics_v1.0.1_processed.txt", "content": "aspects of it such as the discovered sequence of events.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-273", "filename": "Forensics_v1.0.1_processed.txt", "content": "• Build case: From the analysis of the schemas, the analyst eventually comes up with", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-274", "filename": "Forensics_v1.0.1_processed.txt", "content": "testable theories, or working hypotheses, that can explain the evidence. A working", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-275", "filename": "Forensics_v1.0.1_processed.txt", "content": "hypothesis is a tentative conclusion and requires more supporting evidence, as well", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-276", "filename": "Forensics_v1.0.1_processed.txt", "content": "as rigorous testing against alternative explanations. It is a central component of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-277", "filename": "Forensics_v1.0.1_processed.txt", "content": "investigative process and is a common point of reference that brings together the legal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-278", "filename": "Forensics_v1.0.1_processed.txt", "content": "and technical sides in order to build a case.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-278", "line_number": 278, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-279", "filename": "Forensics_v1.0.1_processed.txt", "content": "• Tell story: The typical result of a forensic investigation is a ﬁnal report and, perhaps, an", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-280", "filename": "Forensics_v1.0.1_processed.txt", "content": "oral presentation in court. The actual presentation may only contain the part of the story", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-281", "filename": "Forensics_v1.0.1_processed.txt", "content": "that is strongly supported by the digital evidence; weaker points may be established by", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-282", "filename": "Forensics_v1.0.1_processed.txt", "content": "drawing on evidence from other sources.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-283", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.3.3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-284", "filename": "Forensics_v1.0.1_processed.txt", "content": "Top-Down Processes", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-285", "filename": "Forensics_v1.0.1_processed.txt", "content": "Top-down processes are analytical – they provide context and direction for the analysis of less", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-286", "filename": "Forensics_v1.0.1_processed.txt", "content": "structured data search and they help organise the evidence. Partial or tentative conclusions", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-287", "filename": "Forensics_v1.0.1_processed.txt", "content": "are used to drive the search for supporting and contradictory pieces of evidence.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-288", "filename": "Forensics_v1.0.1_processed.txt", "content": "• Re-evaluate: Feedback from clients may necessitate re-evaluations, such as collecting", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-289", "filename": "Forensics_v1.0.1_processed.txt", "content": "stronger evidence or pursuing alternative theories.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-290", "filename": "Forensics_v1.0.1_processed.txt", "content": "• Search for support: A hypothesis may need more facts to be of interest and, ideally,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-291", "filename": "Forensics_v1.0.1_processed.txt", "content": "would be tested against every (reasonably) possible alternative explanation.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-292", "filename": "Forensics_v1.0.1_processed.txt", "content": "• Search for evidence: Analysis of theories may require the re-evaluation of evidence", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-293", "filename": "Forensics_v1.0.1_processed.txt", "content": "to ascertain its signiﬁcance/provenance, or it may trigger the search for more/better", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-294", "filename": "Forensics_v1.0.1_processed.txt", "content": "evidence.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-295", "filename": "Forensics_v1.0.1_processed.txt", "content": "• Search for relations: Pieces of evidence in the ﬁle can suggest new searches for facts", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-296", "filename": "Forensics_v1.0.1_processed.txt", "content": "and relations on the data.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-297", "filename": "Forensics_v1.0.1_processed.txt", "content": "• Search for information: The feedback loop from any of the higher levels can ultimately", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-298", "filename": "Forensics_v1.0.1_processed.txt", "content": "cascade into a search for additional information; this may include new sources, or the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-299", "filename": "Forensics_v1.0.1_processed.txt", "content": "re-examination of information that was ﬁltered out during previous passes.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-300", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 10", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-301", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 12", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-302", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.3.4", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-303", "filename": "Forensics_v1.0.1_processed.txt", "content": "The Foraging Loop", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-304", "filename": "Forensics_v1.0.1_processed.txt", "content": "It has been observed [17] that analysts tend to start with a high-recall/low-selectivity query,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-305", "filename": "Forensics_v1.0.1_processed.txt", "content": "which encompasses a fairly large set of documents – many more than the analyst can read.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-306", "filename": "Forensics_v1.0.1_processed.txt", "content": "The original set is then successively modiﬁed and narrowed down before the documents are", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-307", "filename": "Forensics_v1.0.1_processed.txt", "content": "read and analysed.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-308", "filename": "Forensics_v1.0.1_processed.txt", "content": "The foraging loop is a balancing act between three kinds of processing that an analyst can", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-309", "filename": "Forensics_v1.0.1_processed.txt", "content": "perform- explore, enrich and exploit. Exploration effectively expands the shoebox by including", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-310", "filename": "Forensics_v1.0.1_processed.txt", "content": "larger amounts of data; enrichment shrinks it by providing more speciﬁc queries that include", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-311", "filename": "Forensics_v1.0.1_processed.txt", "content": "fewer objects for consideration; exploitation is the careful reading and analysis of an artifact", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-312", "filename": "Forensics_v1.0.1_processed.txt", "content": "to extract facts and inferences. Each of these options has varying costs and potential rewards", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-313", "filename": "Forensics_v1.0.1_processed.txt", "content": "and, according to information foraging theory [18], analysts seek to optimise their cost/beneﬁt", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-314", "filename": "Forensics_v1.0.1_processed.txt", "content": "trade-offs.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-315", "filename": "Forensics_v1.0.1_processed.txt", "content": "Information foraging in this context is a highly iterative process with a large number of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-316", "filename": "Forensics_v1.0.1_processed.txt", "content": "incremental adjustments in response to the emerging evidence. It is the responsibility of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-317", "filename": "Forensics_v1.0.1_processed.txt", "content": "investigator to keep the process on target and within the boundaries of any legal restrictions.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-318", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.3.5", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-319", "filename": "Forensics_v1.0.1_processed.txt", "content": "The Sense-Making Loop", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-320", "filename": "Forensics_v1.0.1_processed.txt", "content": "Sense-making is a cognitive term and, according to Klein’s [19] widely quoted deﬁnition, is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-321", "filename": "Forensics_v1.0.1_processed.txt", "content": "the ability to make sense of an ambiguous situation. It is the process of creating situational", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-322", "filename": "Forensics_v1.0.1_processed.txt", "content": "awareness and understanding to support decision making in the face of uncertainty – an", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-323", "filename": "Forensics_v1.0.1_processed.txt", "content": "effort to understand connections between people, places and events in order to anticipate", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-324", "filename": "Forensics_v1.0.1_processed.txt", "content": "their trajectories and act effectively.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-325", "filename": "Forensics_v1.0.1_processed.txt", "content": "There are three main processes involved in the sense-making loop: problem structuring-the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-326", "filename": "Forensics_v1.0.1_processed.txt", "content": "creation and exploration of hypotheses, evidentiary reasoning – the employment of evidence", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-327", "filename": "Forensics_v1.0.1_processed.txt", "content": "to support/disprove a hypothesis and decision making-selecting a course of action from a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-328", "filename": "Forensics_v1.0.1_processed.txt", "content": "set of available alternatives.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-329", "filename": "Forensics_v1.0.1_processed.txt", "content": "It is important to recognize that the described information processing loops are closely tied", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-330", "filename": "Forensics_v1.0.1_processed.txt", "content": "together and often trigger iterations in either directions. New evidence may require a new", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-331", "filename": "Forensics_v1.0.1_processed.txt", "content": "working theory, whereas a new hypothesis may drive the search for new evidence to support", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-332", "filename": "Forensics_v1.0.1_processed.txt", "content": "or disprove it.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-333", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.3.6", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-334", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data Extraction vs. Analysis vs. Legal Interpretation", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-335", "filename": "Forensics_v1.0.1_processed.txt", "content": "Considering the overall process from Figure 1, we gain a better understanding of the roles", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-336", "filename": "Forensics_v1.0.1_processed.txt", "content": "and relationships among the different actors. At present, digital forensic researchers and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-337", "filename": "Forensics_v1.0.1_processed.txt", "content": "tool developers primarily provide the means to acquire the digital evidence from the forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-338", "filename": "Forensics_v1.0.1_processed.txt", "content": "targets, extract (and logically reconstruct) data objects from it, and the essential tools to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-339", "filename": "Forensics_v1.0.1_processed.txt", "content": "search, ﬁlter, and organize it. In complex cases, such as a multi-national security incident,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-340", "filename": "Forensics_v1.0.1_processed.txt", "content": "identifying and acquiring the relevant forensic targets can be a difﬁcult and lengthy process.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-341", "filename": "Forensics_v1.0.1_processed.txt", "content": "It is often predicated in securing the necessary legal rulings in multiple jurisdictions, as well", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-342", "filename": "Forensics_v1.0.1_processed.txt", "content": "as the cooperation of multiple organizations.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-343", "filename": "Forensics_v1.0.1_processed.txt", "content": "Forensic investigators are the primary users of these technical capabilities, employing them", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-343", "line_number": 343, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-344", "filename": "Forensics_v1.0.1_processed.txt", "content": "to analyse speciﬁc cases and to present legally-relevant conclusions. It is the responsibility", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-345", "filename": "Forensics_v1.0.1_processed.txt", "content": "of the investigator to drive the process and to perform all the information foraging and sense-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-346", "filename": "Forensics_v1.0.1_processed.txt", "content": "making tasks. As the volume of the data being analysed continues to grow, it becomes ever", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-347", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 11", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-348", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 13", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-349", "filename": "Forensics_v1.0.1_processed.txt", "content": "more critical for the forensic software to offer higher levels of automation and abstraction.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-350", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data analytics and natural language processing methods are starting to appear in dedicated", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-351", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic software, and – going forward – an expanding range of statistical and machine", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-352", "filename": "Forensics_v1.0.1_processed.txt", "content": "learning tools will need to be incorporated into the process.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-353", "filename": "Forensics_v1.0.1_processed.txt", "content": "Legal experts operate in the upper right-hand corner of the depicted process in terms of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-354", "filename": "Forensics_v1.0.1_processed.txt", "content": "building/disproving legal theories. Thus, the investigator’s task can be described as the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-355", "filename": "Forensics_v1.0.1_processed.txt", "content": "translation of highly speciﬁc technical facts into a higher level representation and theory that", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-356", "filename": "Forensics_v1.0.1_processed.txt", "content": "explains them. The explanation is almost always connected to the sequence of the actions of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-357", "filename": "Forensics_v1.0.1_processed.txt", "content": "the people that are part of the case, such as suspects, victims, and witnesses.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-358", "filename": "Forensics_v1.0.1_processed.txt", "content": "In summary, investigators need not be forensic software engineers, but they must be techni-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-359", "filename": "Forensics_v1.0.1_processed.txt", "content": "cally proﬁcient enough to understand the signiﬁcance of the artifacts extracted from data", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-360", "filename": "Forensics_v1.0.1_processed.txt", "content": "sources, and they must be able to read the relevant technical literature (peer-reviewed articles)", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-361", "filename": "Forensics_v1.0.1_processed.txt", "content": "in full. As the sophistication of the tools grows, investigators will need to have a working", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-362", "filename": "Forensics_v1.0.1_processed.txt", "content": "understanding of a growing list of data science methods that are employed by the tools in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-363", "filename": "Forensics_v1.0.1_processed.txt", "content": "order to correctly interpret the results. Similarly, analysts must have a working understanding", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-364", "filename": "Forensics_v1.0.1_processed.txt", "content": "of the legal landscape, and they must be able to produce a competent report and present their", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-365", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁndings on the witness stand, if necessary.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-366", "filename": "Forensics_v1.0.1_processed.txt", "content": "1.3.7", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-367", "filename": "Forensics_v1.0.1_processed.txt", "content": "Forensic Process", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-368", "filename": "Forensics_v1.0.1_processed.txt", "content": "The deﬁning characteristic of forensic investigations is that their results must be admissible", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-369", "filename": "Forensics_v1.0.1_processed.txt", "content": "in court. This entails following established procedures for acquiring, storing, and processing", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-370", "filename": "Forensics_v1.0.1_processed.txt", "content": "of the evidence, employing scientiﬁcally established analytical tools and methods, and strict", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-371", "filename": "Forensics_v1.0.1_processed.txt", "content": "adherence to a professional code of practice and conduct.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-372", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data Provenance and Integrity. Starting with the data acquisition process, an investigator must", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-373", "filename": "Forensics_v1.0.1_processed.txt", "content": "follow accepted standards and procedures in order to certify the provenance and maintain the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-374", "filename": "Forensics_v1.0.1_processed.txt", "content": "integrity of the collected evidence. In brief, this entails acquiring a truthful copy of the evidence", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-375", "filename": "Forensics_v1.0.1_processed.txt", "content": "from the original source using validated tools, keeping custodial records and detailed case", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-376", "filename": "Forensics_v1.0.1_processed.txt", "content": "notes, using validated tools to perform the analysis of the evidence, cross-validating critical", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-377", "filename": "Forensics_v1.0.1_processed.txt", "content": "pieces of evidence, and correctly interpreting the results based on peer-reviewed scientiﬁc", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-378", "filename": "Forensics_v1.0.1_processed.txt", "content": "studies.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-379", "filename": "Forensics_v1.0.1_processed.txt", "content": "As discussed in the following section, data acquisition can be performed at different levels of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-380", "filename": "Forensics_v1.0.1_processed.txt", "content": "abstraction and completeness. The traditional gold standard is a bit-level copy of the forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-381", "filename": "Forensics_v1.0.1_processed.txt", "content": "target, which can then be analysed using knowledge of the structure and semantics of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-382", "filename": "Forensics_v1.0.1_processed.txt", "content": "data content. As storage devices increase in complexity and encryption becomes the default", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-383", "filename": "Forensics_v1.0.1_processed.txt", "content": "data encoding, it is increasingly infeasible to obtain a true physical copy of the media and a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-384", "filename": "Forensics_v1.0.1_processed.txt", "content": "(partial) logical acquisition may be the only possibility. For example, the only readily available", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-385", "filename": "Forensics_v1.0.1_processed.txt", "content": "source of data content for an up-to-date smartphone (with encrypted local storage) might", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-386", "filename": "Forensics_v1.0.1_processed.txt", "content": "be a cloud backup of the user’s data. Further, local data may be treated by the courts as", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-387", "filename": "Forensics_v1.0.1_processed.txt", "content": "having higher levels of privacy protection than data shared with a third party, such as a service", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-388", "filename": "Forensics_v1.0.1_processed.txt", "content": "provider.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-389", "filename": "Forensics_v1.0.1_processed.txt", "content": "Scientiﬁc Methodology. The notion of reproducibility is central to the scientiﬁc validity of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-390", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic analysis; starting with the same data and following the same process described in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-391", "filename": "Forensics_v1.0.1_processed.txt", "content": "the case notes should allow a third party to arrive at the same result. Processing methods", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-392", "filename": "Forensics_v1.0.1_processed.txt", "content": "should have scientiﬁcally established error rates and different forensic tools that implement", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-393", "filename": "Forensics_v1.0.1_processed.txt", "content": "the same type of data processing should yield results that are either identical, or within known", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-394", "filename": "Forensics_v1.0.1_processed.txt", "content": "statistical error boundaries.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-395", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 12", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-396", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 14", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-397", "filename": "Forensics_v1.0.1_processed.txt", "content": "The investigator must have a deep understanding of the results produced by various forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-398", "filename": "Forensics_v1.0.1_processed.txt", "content": "computations. Some of the central concerns include: inherent uncertainties in some of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-399", "filename": "Forensics_v1.0.1_processed.txt", "content": "source data, the possibility for multiple interpretations, as well as the recognition that some of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-400", "filename": "Forensics_v1.0.1_processed.txt", "content": "the data could be fake in that it was generated using anti-forensics tools in order to confuse", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-401", "filename": "Forensics_v1.0.1_processed.txt", "content": "the investigation. The latter is possible because most of the data item used in the forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-402", "filename": "Forensics_v1.0.1_processed.txt", "content": "analysis is produced during the normal operation of the system, and is not tamper-proof.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-403", "filename": "Forensics_v1.0.1_processed.txt", "content": "For example, an intruder with sufﬁcient access privileges can arbitrarily modify any of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-404", "filename": "Forensics_v1.0.1_processed.txt", "content": "millions of ﬁle timestamps potentially making timeline analysis – a core analytical technique –", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-405", "filename": "Forensics_v1.0.1_processed.txt", "content": "unreliable. Experienced forensic analysts are alert to such issues and seek, whenever possible,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-406", "filename": "Forensics_v1.0.1_processed.txt", "content": "to corroborate important pieces of information from multiple sources.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-407", "filename": "Forensics_v1.0.1_processed.txt", "content": "Tool Validation. Forensic tool validation is a scientiﬁc and engineering process that subjects", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-408", "filename": "Forensics_v1.0.1_processed.txt", "content": "speciﬁc tools to systematic testing in order to establish the validity of the results produced.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-409", "filename": "Forensics_v1.0.1_processed.txt", "content": "For example, data acquisition software must reliably produce an unmodiﬁed and complete", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-410", "filename": "Forensics_v1.0.1_processed.txt", "content": "copy of the class of forensic targets it is designed to handle.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-411", "filename": "Forensics_v1.0.1_processed.txt", "content": "Forensic Procedure. The organizational aspect of the forensic process, which dictates how", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-412", "filename": "Forensics_v1.0.1_processed.txt", "content": "evidence is acquired, stored, and processed is critical to the issue of admissibility. Strict", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-413", "filename": "Forensics_v1.0.1_processed.txt", "content": "adherence to established standards and court-imposed restriction is the most effective", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-414", "filename": "Forensics_v1.0.1_processed.txt", "content": "means of demonstrating to the court that the results of the forensic analysis are truthful and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-415", "filename": "Forensics_v1.0.1_processed.txt", "content": "trustworthy.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-416", "filename": "Forensics_v1.0.1_processed.txt", "content": "Triage. The volume of data contained by a forensic target typically far exceeds the amount", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-417", "filename": "Forensics_v1.0.1_processed.txt", "content": "of data relevant to an inquiry. Therefore, in the early stages of an investigation, the focus", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-418", "filename": "Forensics_v1.0.1_processed.txt", "content": "of the analysis is to (quickly) identify the relevant data and ﬁlter out the irrelevant. Such", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-419", "filename": "Forensics_v1.0.1_processed.txt", "content": "initial screening of the content, often referred to as triage, results in either follow up deep", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-420", "filename": "Forensics_v1.0.1_processed.txt", "content": "examination, or in deprioritisation, or removal of the target from further consideration.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-421", "filename": "Forensics_v1.0.1_processed.txt", "content": "Legally, there can be a number of constraints placed on the triage process based on the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-422", "filename": "Forensics_v1.0.1_processed.txt", "content": "the case and the inherent privacy rights in the jurisdiction. From a technical perspective", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-423", "filename": "Forensics_v1.0.1_processed.txt", "content": "[20], “triage is a partial forensic examination conducted under (signiﬁcant) time and resource", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-424", "filename": "Forensics_v1.0.1_processed.txt", "content": "constraints.” In other words, investigators employ fast examination methods, such as looking", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-425", "filename": "Forensics_v1.0.1_processed.txt", "content": "at ﬁle names, examining web search history, and similar, to estimate (based on experience)", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-426", "filename": "Forensics_v1.0.1_processed.txt", "content": "the value of the data. Such results are inherently less reliable than a deep examination as it is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-427", "filename": "Forensics_v1.0.1_processed.txt", "content": "easy to create a mismatch between data attribute and actual content. Therefore, courts may", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-428", "filename": "Forensics_v1.0.1_processed.txt", "content": "place constraints on the use of computers by convicted offenders to facilitate fast screening", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-429", "filename": "Forensics_v1.0.1_processed.txt", "content": "by ofﬁcers in the ﬁeld without impounding the device.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-431", "filename": "Forensics_v1.0.1_processed.txt", "content": "OPERATING SYSTEM ANALYSIS", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-432", "filename": "Forensics_v1.0.1_processed.txt", "content": "[6, 21, 22]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-433", "filename": "Forensics_v1.0.1_processed.txt", "content": "Modern computer systems generally still follow the original von Neumann architecture [23],", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-434", "filename": "Forensics_v1.0.1_processed.txt", "content": "which models a computer system as consisting of three main functional units – CPU, main", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-435", "filename": "Forensics_v1.0.1_processed.txt", "content": "memory, and secondary storage – connected via data buses. To be precise, the actual", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-436", "filename": "Forensics_v1.0.1_processed.txt", "content": "investigative targets are not individual pieces of hardware, but the different Operating System", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-437", "filename": "Forensics_v1.0.1_processed.txt", "content": "(OS) modules controlling the hardware subsystems and their respective data structures.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-438", "filename": "Forensics_v1.0.1_processed.txt", "content": "Our discussion takes a high level view of OS analysis – it is beyond the scope of the Knowledge", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-439", "filename": "Forensics_v1.0.1_processed.txt", "content": "Area to delve into the engineering details of how different classes of devices are analysed.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-440", "filename": "Forensics_v1.0.1_processed.txt", "content": "For example, smartphones present additional challenges with respect to data acquisition;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-441", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 13", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-442", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 15", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-443", "filename": "Forensics_v1.0.1_processed.txt", "content": "however, they are still commodity computers with the vast majority of them running on a Linux", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-444", "filename": "Forensics_v1.0.1_processed.txt", "content": "kernel. The same applies to other classes of embedded devices, such as UAVs and vehicle", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-445", "filename": "Forensics_v1.0.1_processed.txt", "content": "infotainment systems.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-446", "filename": "Forensics_v1.0.1_processed.txt", "content": "The OS functions at a higher level of privilege relative to user applications and directly manages", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-446", "line_number": 446, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-447", "filename": "Forensics_v1.0.1_processed.txt", "content": "all the computer system’s resources – CPU, main memory, and I/O devices. Applications", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-448", "filename": "Forensics_v1.0.1_processed.txt", "content": "request resources and services from the OS via the system call interface and employ them", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-449", "filename": "Forensics_v1.0.1_processed.txt", "content": "to utilize them to accomplish a speciﬁc task. The (operating) system maintains a variety of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-450", "filename": "Forensics_v1.0.1_processed.txt", "content": "accounting information that can bear witness to events relevant to an inquiry [21].", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-451", "filename": "Forensics_v1.0.1_processed.txt", "content": "System analysis employs knowledge of how operating systems function in order to reach", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-452", "filename": "Forensics_v1.0.1_processed.txt", "content": "conclusions about events and actions of interest to the case. Average users have very little", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-453", "filename": "Forensics_v1.0.1_processed.txt", "content": "understanding of the type of information operating systems maintain about their activities,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-454", "filename": "Forensics_v1.0.1_processed.txt", "content": "and usually do not have the knowledge and/or privilege level to tamper with system records", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-455", "filename": "Forensics_v1.0.1_processed.txt", "content": "thereby making them forensically useful, even if they do not ﬁt a formal deﬁnition for secure", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-456", "filename": "Forensics_v1.0.1_processed.txt", "content": "and trustworthy records.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-457", "filename": "Forensics_v1.0.1_processed.txt", "content": "2.1", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-458", "filename": "Forensics_v1.0.1_processed.txt", "content": "Storage Forensics", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-459", "filename": "Forensics_v1.0.1_processed.txt", "content": "Persistent storage in the form of Hard Disk Drives (HDDs), Solid State Drives (SSDs), optical", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-460", "filename": "Forensics_v1.0.1_processed.txt", "content": "disks, external (USB-connected) media etc. is the primary source of evidence for most digital", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-460", "line_number": 460, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-461", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic investigations. Although the importance of (volatile) memory forensics in solving", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-461", "line_number": 461, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-462", "filename": "Forensics_v1.0.1_processed.txt", "content": "cases has grown signiﬁcantly, a thorough examination of persistent data has remained a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-462", "line_number": 462, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-463", "filename": "Forensics_v1.0.1_processed.txt", "content": "cornerstone of most digital forensic investigations.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-464", "filename": "Forensics_v1.0.1_processed.txt", "content": "2.1.1", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-464", "line_number": 464, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-465", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data Abstraction Layers", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-465", "line_number": 465, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-466", "filename": "Forensics_v1.0.1_processed.txt", "content": "Computer systems organise raw storage in successive layers of abstraction – each software", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-466", "line_number": 466, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-467", "filename": "Forensics_v1.0.1_processed.txt", "content": "layer (some may be in ﬁrmware) builds an incrementally more abstract data representation", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-468", "filename": "Forensics_v1.0.1_processed.txt", "content": "that is only dependent on the interface provided by the layer immediately below it. Accordingly,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-468", "line_number": 468, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-469", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic analysis of storage devices can be performed at several levels of abstraction [22]:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-469", "line_number": 469, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-470", "filename": "Forensics_v1.0.1_processed.txt", "content": "PHYSICAL MEDIA. At the lowest level, every storage device encodes a sequence of bits and it is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-470", "line_number": 470, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-471", "filename": "Forensics_v1.0.1_processed.txt", "content": "possible, in principle, to use a custom mechanism to extract the data bit by bit. Depending on", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-471", "line_number": 471, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-472", "filename": "Forensics_v1.0.1_processed.txt", "content": "the underlying technology, this can be an expensive and time-consuming process, and often", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-473", "filename": "Forensics_v1.0.1_processed.txt", "content": "requires reverse engineering. One example of this process is the acquisition of mobile phone", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-474", "filename": "Forensics_v1.0.1_processed.txt", "content": "data, in some of which it is possible to physically remove (desolder) the memory chips and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-474", "line_number": 474, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-475", "filename": "Forensics_v1.0.1_processed.txt", "content": "perform a true hardware-level acquisition of the content [24]. A similar “chip-off” approach", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-475", "line_number": 475, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-476", "filename": "Forensics_v1.0.1_processed.txt", "content": "can be applied to a ﬂash memory devices, such as SSD, and to embedded and Internet of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-476", "line_number": 476, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-477", "filename": "Forensics_v1.0.1_processed.txt", "content": "Things (IoT) devices with limited capabilities and interfaces. Another practical approach is to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-478", "filename": "Forensics_v1.0.1_processed.txt", "content": "employ engineering tools that support the hardware development process and employ, for", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-479", "filename": "Forensics_v1.0.1_processed.txt", "content": "example, a standard JTAG interface [25] – designed for testing and debugging purposes – to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-479", "line_number": 479, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-480", "filename": "Forensics_v1.0.1_processed.txt", "content": "perform the necessary data acquisition.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-480", "line_number": 480, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-481", "filename": "Forensics_v1.0.1_processed.txt", "content": "In practice, the lowest level at which typical examinations are performed is the Host Bus", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-481", "line_number": 481, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-482", "filename": "Forensics_v1.0.1_processed.txt", "content": "Adapter (HBA) interface. Adapters implement a standard protocol (SATA, SCSI) through which", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-483", "filename": "Forensics_v1.0.1_processed.txt", "content": "they can be made to perform low-level operations, such as accessing the drive’s content.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-484", "filename": "Forensics_v1.0.1_processed.txt", "content": "Similarly, the NVMe protocol [26] is used to perform acquisition from PCI Express-based", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-485", "filename": "Forensics_v1.0.1_processed.txt", "content": "solid-state storage devices.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-486", "filename": "Forensics_v1.0.1_processed.txt", "content": "All physical media eventually fail and (part of) the stored data may become unavailable.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-487", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 14", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-488", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 16", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-489", "filename": "Forensics_v1.0.1_processed.txt", "content": "Depending on the nature of the failure, and the sophistication of the device, it may be possible", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-490", "filename": "Forensics_v1.0.1_processed.txt", "content": "to recover at least some of the data. For example, it may be possible to replace the failed", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-491", "filename": "Forensics_v1.0.1_processed.txt", "content": "controller of a HDD and recover the content. Such hardware recovery becomes more difﬁcult", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-492", "filename": "Forensics_v1.0.1_processed.txt", "content": "with more integrated and complex devices.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-493", "filename": "Forensics_v1.0.1_processed.txt", "content": "BLOCK DEVICE. The typical HBA presents a block device abstraction – the medium is presented", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-494", "filename": "Forensics_v1.0.1_processed.txt", "content": "as a sequence of ﬁxed-size blocks, commonly consisting of 512 or 4096 bytes, and the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-495", "filename": "Forensics_v1.0.1_processed.txt", "content": "contents of each block can be read or written using block read/write commands. The typical", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-495", "line_number": 495, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-496", "filename": "Forensics_v1.0.1_processed.txt", "content": "data acquisition process works at the block device level to obtain a working copy of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-496", "line_number": 496, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-497", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic target – a process known as imaging – on which all further processing is performed.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-497", "line_number": 497, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-498", "filename": "Forensics_v1.0.1_processed.txt", "content": "Historically, the term sector is used to refer to the data transfer units of magnetic hard disks;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-499", "filename": "Forensics_v1.0.1_processed.txt", "content": "a (logical) block is a more general term that is independent of the storage technology and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-500", "filename": "Forensics_v1.0.1_processed.txt", "content": "physical data layout.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-500", "line_number": 500, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-501", "filename": "Forensics_v1.0.1_processed.txt", "content": "FILE SYSTEM. The block device has no notion of ﬁles, directories or – in most cases – which", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-501", "line_number": 501, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-502", "filename": "Forensics_v1.0.1_processed.txt", "content": "blocks are considered allocated and which ones are free; it is the ﬁlesystem’s task to organise", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-502", "line_number": 502, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-503", "filename": "Forensics_v1.0.1_processed.txt", "content": "the block storage into a ﬁle-based store in which applications can create ﬁles and directo-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-504", "filename": "Forensics_v1.0.1_processed.txt", "content": "ries with all of their relevant metadata attributes – name, size, owner, timestamps, access", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-505", "filename": "Forensics_v1.0.1_processed.txt", "content": "permissions etc.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-505", "line_number": 505, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-506", "filename": "Forensics_v1.0.1_processed.txt", "content": "APPLICATION ARTIFACTS. User applications use the ﬁlesystem to store various artifacts that", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-506", "line_number": 506, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-507", "filename": "Forensics_v1.0.1_processed.txt", "content": "are of value to the end-user – documents, images, messages etc. The operating system itself", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-507", "line_number": 507, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-508", "filename": "Forensics_v1.0.1_processed.txt", "content": "also uses the ﬁle system to store its own image – executable binaries, libraries, conﬁguration", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-509", "filename": "Forensics_v1.0.1_processed.txt", "content": "and log ﬁles, registry entries – and to install applications. Some application artifacts such", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-510", "filename": "Forensics_v1.0.1_processed.txt", "content": "as compound documents have a complex internal structure integrating multiple artifacts of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-511", "filename": "Forensics_v1.0.1_processed.txt", "content": "different types. An analysis of application artifacts tends to yield the most immediately relevant", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-511", "line_number": 511, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-512", "filename": "Forensics_v1.0.1_processed.txt", "content": "results, as the recorded information most directly relates to actions and communications", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-512", "line_number": 512, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-513", "filename": "Forensics_v1.0.1_processed.txt", "content": "initiated by people. As the analysis goes deeper (to a lower level of abstraction), it requires", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-513", "line_number": 513, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-514", "filename": "Forensics_v1.0.1_processed.txt", "content": "greater effort and more expert knowledge to independently reconstruct the actions of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-515", "filename": "Forensics_v1.0.1_processed.txt", "content": "system. For example, by understanding the on-disk structures of a speciﬁc ﬁlesystem, a tool", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-516", "filename": "Forensics_v1.0.1_processed.txt", "content": "can reconstitute a ﬁle out of its constituent blocks. This knowledge is particularly costly to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-517", "filename": "Forensics_v1.0.1_processed.txt", "content": "obtain from a closed system such as Microsoft Windows, because of the substantial amount", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-518", "filename": "Forensics_v1.0.1_processed.txt", "content": "of blackbox reverse engineering involved.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-519", "filename": "Forensics_v1.0.1_processed.txt", "content": "Despite the cost, independent forensic reconstruction is of critical importance for several", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-520", "filename": "Forensics_v1.0.1_processed.txt", "content": "reasons:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-521", "filename": "Forensics_v1.0.1_processed.txt", "content": "• It enables the recovery of evidentiary data not available through the normal data access", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-522", "filename": "Forensics_v1.0.1_processed.txt", "content": "interface.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-523", "filename": "Forensics_v1.0.1_processed.txt", "content": "• It forms the basis for recovering partially overwritten data.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-524", "filename": "Forensics_v1.0.1_processed.txt", "content": "• It allows the discovery and analysis of malware agents that have subverted the normal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-525", "filename": "Forensics_v1.0.1_processed.txt", "content": "functioning of the system, thus making the data obtained via the regular interface", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-526", "filename": "Forensics_v1.0.1_processed.txt", "content": "untrustworthy.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-527", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 15", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-528", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 17", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-529", "filename": "Forensics_v1.0.1_processed.txt", "content": "2.2", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-530", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data Acquisition", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-531", "filename": "Forensics_v1.0.1_processed.txt", "content": "In line with best practices [6], analysing data at rest is not carried out on a live system. The", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-532", "filename": "Forensics_v1.0.1_processed.txt", "content": "target machine is powered down, an exact bit-wise copy of the storage media is created, the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-533", "filename": "Forensics_v1.0.1_processed.txt", "content": "original is stored in an evidence locker and all the forensic work is performed on the copy.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-534", "filename": "Forensics_v1.0.1_processed.txt", "content": "There are exceptions to this workﬂow in cases where it is not practical to shut down the target", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-535", "filename": "Forensics_v1.0.1_processed.txt", "content": "system and, therefore, a media image is obtained while the system is live. Evidently, such", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-536", "filename": "Forensics_v1.0.1_processed.txt", "content": "an approach does not provide the same level of consistency guarantees, but it can still yield", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-537", "filename": "Forensics_v1.0.1_processed.txt", "content": "valuable insights. The problem of consistency, also referred to as data smearing, does not", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-538", "filename": "Forensics_v1.0.1_processed.txt", "content": "exist in virtualised environments, where a consistent image of the virtual disk can be trivially", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-539", "filename": "Forensics_v1.0.1_processed.txt", "content": "obtained by using the built-in snapshot mechanism.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-540", "filename": "Forensics_v1.0.1_processed.txt", "content": "As already discussed, obtaining data from the lowest level system interface available and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-541", "filename": "Forensics_v1.0.1_processed.txt", "content": "independently reconstructing higher-level artifacts is considered the most reliable approach", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-542", "filename": "Forensics_v1.0.1_processed.txt", "content": "to forensic analysis. This results in a strong preference for acquiring data at lower levels of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-543", "filename": "Forensics_v1.0.1_processed.txt", "content": "abstraction and the concepts of physical and logical acquisition.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-544", "filename": "Forensics_v1.0.1_processed.txt", "content": "Physical data acquisition is the process of obtaining the data directly from hardware", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-545", "filename": "Forensics_v1.0.1_processed.txt", "content": "media, without the mediation of any (untrusted) third-party software.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-546", "filename": "Forensics_v1.0.1_processed.txt", "content": "An increasingly common example of this approach is mobile phone data acquisition that relies", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-547", "filename": "Forensics_v1.0.1_processed.txt", "content": "on removing the physical memory chip[24] and reading the data directly from it. More generally,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-548", "filename": "Forensics_v1.0.1_processed.txt", "content": "getting physical with the evidence source is usually the most practical and necessary method", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-549", "filename": "Forensics_v1.0.1_processed.txt", "content": "for low-end embedded systems with limited hardware capabilities. Physical acquisition also", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-550", "filename": "Forensics_v1.0.1_processed.txt", "content": "affords access to additional over-provisioned raw storage set aside by the storage device in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-551", "filename": "Forensics_v1.0.1_processed.txt", "content": "order to compensate for the expected hardware failures. As a general rule, devices offer no", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-552", "filename": "Forensics_v1.0.1_processed.txt", "content": "external means to interrogate this shadow storage area.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-553", "filename": "Forensics_v1.0.1_processed.txt", "content": "Chip-off techniques present their own challenges in that the process is inherently destructive", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-554", "filename": "Forensics_v1.0.1_processed.txt", "content": "to the device, the data extraction and reconstruction requires additional effort, and the overall", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-555", "filename": "Forensics_v1.0.1_processed.txt", "content": "cost can be substantial.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-556", "filename": "Forensics_v1.0.1_processed.txt", "content": "For general-purpose systems, tools use an HBA protocol such as SATA or SCSI to interrogate", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-557", "filename": "Forensics_v1.0.1_processed.txt", "content": "the storage device and obtain a copy of the data. The resulting image is a block-level copy of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-558", "filename": "Forensics_v1.0.1_processed.txt", "content": "the target that is generally referred to as physical acquisition by most investigators; Casey", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-559", "filename": "Forensics_v1.0.1_processed.txt", "content": "uses the more accurate term pseudo-physical to account for the fact that not every area of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-560", "filename": "Forensics_v1.0.1_processed.txt", "content": "the physical media is acquired and that the order of the acquired blocks does not necessarily", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-561", "filename": "Forensics_v1.0.1_processed.txt", "content": "reﬂect the physical layout of the device.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-562", "filename": "Forensics_v1.0.1_processed.txt", "content": "In some cases, it is necessary to perform additional recovery operations before a usable copy", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-563", "filename": "Forensics_v1.0.1_processed.txt", "content": "of the data is obtained. One common example is RAID storage devices, which contain multiple", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-564", "filename": "Forensics_v1.0.1_processed.txt", "content": "physical devices that function together as a single unit, providing built-in protection against", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-565", "filename": "Forensics_v1.0.1_processed.txt", "content": "certain classes of failures. In common conﬁgurations such as RAID 5 and 6 the content", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-566", "filename": "Forensics_v1.0.1_processed.txt", "content": "acquisition of individual drives is largely useless without the subsequent step of RAID data", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-567", "filename": "Forensics_v1.0.1_processed.txt", "content": "reconstruction.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-568", "filename": "Forensics_v1.0.1_processed.txt", "content": "Modern storage controllers are quickly evolving into autonomous storage devices, which", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-569", "filename": "Forensics_v1.0.1_processed.txt", "content": "implement complex (proprietary) wear-levelling and load-balancing algorithms. This has", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-570", "filename": "Forensics_v1.0.1_processed.txt", "content": "two major implications: a) the numbering of the data blocks is completely separated from", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-571", "filename": "Forensics_v1.0.1_processed.txt", "content": "the actual physical location; and b) it is possible for the storage controller itself to become", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-572", "filename": "Forensics_v1.0.1_processed.txt", "content": "compromised [27], thus rendering the acquisition process untrustworthy. These caveats", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-573", "filename": "Forensics_v1.0.1_processed.txt", "content": "notwithstanding, we will refer to block-level acquisition as being physical, in line with the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-574", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 16", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-575", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 18", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-576", "filename": "Forensics_v1.0.1_processed.txt", "content": "accepted terminology.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-577", "filename": "Forensics_v1.0.1_processed.txt", "content": "Logical data acquisition relies on one or more software layers as intermediaries to acquire", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-578", "filename": "Forensics_v1.0.1_processed.txt", "content": "the data from the storage device.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-579", "filename": "Forensics_v1.0.1_processed.txt", "content": "In other words, the tool uses an Application Programming Interface (API), or message protocol,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-580", "filename": "Forensics_v1.0.1_processed.txt", "content": "to perform the task. The integrity of this method hinges on the correctness and integrity of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-581", "filename": "Forensics_v1.0.1_processed.txt", "content": "implementation of the API, or protocol. In addition to the risk, however, there is also a reward –", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-582", "filename": "Forensics_v1.0.1_processed.txt", "content": "higher level interfaces present a data view that is closer in abstraction to that of user actions", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-583", "filename": "Forensics_v1.0.1_processed.txt", "content": "and application data structures. Experienced investigators, if equipped with the proper tools,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-584", "filename": "Forensics_v1.0.1_processed.txt", "content": "can make use of both physical and logical views to obtain and verify the evidence relevant to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-585", "filename": "Forensics_v1.0.1_processed.txt", "content": "the case.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-586", "filename": "Forensics_v1.0.1_processed.txt", "content": "Block-level acquisition can be accomplished in software, hardware or a combination of both.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-587", "filename": "Forensics_v1.0.1_processed.txt", "content": "The workhorse of forensic imaging is the dd Unix/Linux general purpose command-line utility,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-588", "filename": "Forensics_v1.0.1_processed.txt", "content": "which can produce a binary copy of any ﬁle, device partition or entire storage device. A", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-589", "filename": "Forensics_v1.0.1_processed.txt", "content": "hardware write blocker is often installed on the target device to eliminate the possibility of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-590", "filename": "Forensics_v1.0.1_processed.txt", "content": "operator error, which can lead to the accidental modiﬁcation of the target.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-591", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cryptographic hashes are computed for the entire image and (preferably) for every block; the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-592", "filename": "Forensics_v1.0.1_processed.txt", "content": "latter can be used to demonstrate the integrity of the remaining evidence if the original device", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-593", "filename": "Forensics_v1.0.1_processed.txt", "content": "suffers a partial failure, which makes it impossible to read its entire contents. The National", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-594", "filename": "Forensics_v1.0.1_processed.txt", "content": "Institute of Standards and Technology (NIST) maintains the Computer Forensic Tool Testing", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-595", "filename": "Forensics_v1.0.1_processed.txt", "content": "(CFTT) project [28], which independently tests various basic tools, such as write blockers and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-596", "filename": "Forensics_v1.0.1_processed.txt", "content": "image acquisition tools and regularly publishes reports on its ﬁndings.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-597", "filename": "Forensics_v1.0.1_processed.txt", "content": "Encryption Concerns", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-598", "filename": "Forensics_v1.0.1_processed.txt", "content": "Apart from having the technical capability to safely interrogate and acquire the content of a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-599", "filename": "Forensics_v1.0.1_processed.txt", "content": "storage device, one of the biggest concerns during data acquisition can be the presence of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-600", "filename": "Forensics_v1.0.1_processed.txt", "content": "encrypted data. Modern encryption is pervasive and is increasingly applied by default to both", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-601", "filename": "Forensics_v1.0.1_processed.txt", "content": "stored data and data in transit over the network. By deﬁnition, a properly implemented and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-601", "line_number": 601, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-602", "filename": "Forensics_v1.0.1_processed.txt", "content": "administered data security system, which inevitably employs encryption, will frustrate efforts", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-603", "filename": "Forensics_v1.0.1_processed.txt", "content": "to acquire the protected data and, by extension, to perform forensic analysis.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-604", "filename": "Forensics_v1.0.1_processed.txt", "content": "There are two possible paths to obtaining encrypted data – technical and legal. The technical", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-605", "filename": "Forensics_v1.0.1_processed.txt", "content": "approach relies on ﬁnding algorithmic, implementation, or administrative errors, which allow", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-606", "filename": "Forensics_v1.0.1_processed.txt", "content": "the data protection to be subverted. Although it is nearly impossible to create a complex IT", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-607", "filename": "Forensics_v1.0.1_processed.txt", "content": "system that has no bugs, the discovery and exploitation of such deﬁciencies is becoming", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-608", "filename": "Forensics_v1.0.1_processed.txt", "content": "increasingly more difﬁcult and resource intensive.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-609", "filename": "Forensics_v1.0.1_processed.txt", "content": "The legal approach relies on compelling the person with knowledge of the relevant encryption", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-610", "filename": "Forensics_v1.0.1_processed.txt", "content": "keys to surrender them. This is relatively new legal territory and its treatment varies across", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-611", "filename": "Forensics_v1.0.1_processed.txt", "content": "jurisdictions. In the UK, the Regulation of Investigatory Powers Act 2000 speciﬁes the circum-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-612", "filename": "Forensics_v1.0.1_processed.txt", "content": "stances under which individuals are legally required to disclose the keys. Disclosure may run", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-613", "filename": "Forensics_v1.0.1_processed.txt", "content": "counter the legal right against self-incrimination and in some jurisdictions, such as in the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-614", "filename": "Forensics_v1.0.1_processed.txt", "content": "United States, it is not yet deﬁnitively resolved.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-615", "filename": "Forensics_v1.0.1_processed.txt", "content": "The remainder of this discussion assumes that access to the raw data is ensured by either", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-616", "filename": "Forensics_v1.0.1_processed.txt", "content": "technical, or legal means, which are beyond the scope of this knowledge area.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-617", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 17", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-618", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 19", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-619", "filename": "Forensics_v1.0.1_processed.txt", "content": "2.3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-620", "filename": "Forensics_v1.0.1_processed.txt", "content": "Filesystem Analysis", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-621", "filename": "Forensics_v1.0.1_processed.txt", "content": "A typical storage device presents a block device interface with Bmax number of blocks of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-622", "filename": "Forensics_v1.0.1_processed.txt", "content": "size Bsize. All read and write I/O operations are executed at the granularity of a whole block;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-623", "filename": "Forensics_v1.0.1_processed.txt", "content": "historically, the standard block size adopted by HDD manufacturers has been 512 bytes. With", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-624", "filename": "Forensics_v1.0.1_processed.txt", "content": "the 2011 introduction of the advanced format standard [29], storage devices can support", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-625", "filename": "Forensics_v1.0.1_processed.txt", "content": "larger blocks, with 4,096 bytes being the new preferred size.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-626", "filename": "Forensics_v1.0.1_processed.txt", "content": "Regardless of the base block size, many operating systems manage storage in clusters; a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-627", "filename": "Forensics_v1.0.1_processed.txt", "content": "cluster is a contiguous sequence of blocks and is the smallest unit at which raw storage is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-628", "filename": "Forensics_v1.0.1_processed.txt", "content": "allocated/reclaimed. Thus, if the device block/sector size is 4KiB but the chosen cluster size", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-629", "filename": "Forensics_v1.0.1_processed.txt", "content": "is 16KiB, the OS will allocate blocks in groups of four.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-630", "filename": "Forensics_v1.0.1_processed.txt", "content": "For administration purposes, the raw drive may be split into one or more contiguous areas", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-631", "filename": "Forensics_v1.0.1_processed.txt", "content": "called partitions, each of which has a designated use and can be independently manipulated.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-632", "filename": "Forensics_v1.0.1_processed.txt", "content": "Partitions can further be organised into volumes – a physical volume maps onto a single", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-633", "filename": "Forensics_v1.0.1_processed.txt", "content": "partition, whereas a logical volume can integrate multiple partitions potentially from multiple", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-634", "filename": "Forensics_v1.0.1_processed.txt", "content": "devices. Volumes present a block device interface but allow for the decoupling of the physical", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-635", "filename": "Forensics_v1.0.1_processed.txt", "content": "media organisation from the logical view presented to the operating system.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-636", "filename": "Forensics_v1.0.1_processed.txt", "content": "With a few exceptions, volumes/partitions are formatted to accommodate a particular ﬁle", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-637", "filename": "Forensics_v1.0.1_processed.txt", "content": "system (ﬁlesystem), which organizes and manages the blocks to create the abstraction of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-638", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁles and directories together with their relevant metadata. The Operating System (OS), as part", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-639", "filename": "Forensics_v1.0.1_processed.txt", "content": "of its system call interface used by applications to request services, provides a ﬁlesystem API", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-640", "filename": "Forensics_v1.0.1_processed.txt", "content": "that allows applications to create, modify and delete ﬁles; it also allows ﬁles to be grouped", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-641", "filename": "Forensics_v1.0.1_processed.txt", "content": "into a hierarchical structure of directories (or folders).", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-642", "filename": "Forensics_v1.0.1_processed.txt", "content": "A ﬁle is a named (opaque) sequence of bytes that is stored persistently.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-643", "filename": "Forensics_v1.0.1_processed.txt", "content": "As a general rule, the format and interpretation of ﬁle content is almost always outside the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-644", "filename": "Forensics_v1.0.1_processed.txt", "content": "purview of the operating system; it is the concern of relevant applications acting on behalf of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-645", "filename": "Forensics_v1.0.1_processed.txt", "content": "users.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-646", "filename": "Forensics_v1.0.1_processed.txt", "content": "A ﬁle system (ﬁlesystem) is an OS subsystem that is responsible for the persistent storage", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-647", "filename": "Forensics_v1.0.1_processed.txt", "content": "and organisation of user and system ﬁles on a partition/volume.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-648", "filename": "Forensics_v1.0.1_processed.txt", "content": "It provides a high-level standard API such as POSIX, that is used by applications to store and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-649", "filename": "Forensics_v1.0.1_processed.txt", "content": "retrieve ﬁles by name without any concern for the physical storage method employed or the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-650", "filename": "Forensics_v1.0.1_processed.txt", "content": "layout of the data (and metadata) content.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-651", "filename": "Forensics_v1.0.1_processed.txt", "content": "Filesystem forensics uses knowledge of the ﬁlesystem’s data structures and the algorithms", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-652", "filename": "Forensics_v1.0.1_processed.txt", "content": "used to create, maintain, and delete them to: a) extract data content from devices indepen-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-653", "filename": "Forensics_v1.0.1_processed.txt", "content": "dently of the operating system instance which created it; and b) extract leftover artifacts to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-654", "filename": "Forensics_v1.0.1_processed.txt", "content": "which the regular ﬁlesystem API does not offer access.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-655", "filename": "Forensics_v1.0.1_processed.txt", "content": "The ﬁrst feature is important to ensure that the data are not being modiﬁed during acquisition", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-656", "filename": "Forensics_v1.0.1_processed.txt", "content": "and that any potential security compromises do not affect the validity of the data. The second", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-657", "filename": "Forensics_v1.0.1_processed.txt", "content": "provides access to (parts of) deallocated ﬁles that have not been overwritten, purposely", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-658", "filename": "Forensics_v1.0.1_processed.txt", "content": "hidden data, and an implied history of the ﬁlesystem operation – the creation/deletion of ﬁles", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-659", "filename": "Forensics_v1.0.1_processed.txt", "content": "– that is not explicitly maintained by the OS.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-660", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 18", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-661", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 20", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-662", "filename": "Forensics_v1.0.1_processed.txt", "content": "2.4", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-663", "filename": "Forensics_v1.0.1_processed.txt", "content": "Block Device Analysis", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-664", "filename": "Forensics_v1.0.1_processed.txt", "content": "Before the OS can organise a ﬁlesystem on a raw device, it typically splits it into a set of one", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-665", "filename": "Forensics_v1.0.1_processed.txt", "content": "or more disjoint partitions.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-666", "filename": "Forensics_v1.0.1_processed.txt", "content": "A block device partition, or physical volume, is a contiguous allocation of blocks for a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-667", "filename": "Forensics_v1.0.1_processed.txt", "content": "speciﬁc purpose, such as the organisation of a ﬁle system.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-668", "filename": "Forensics_v1.0.1_processed.txt", "content": "Partitions are the basic method used for coarse-grained storage management; they allow", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-669", "filename": "Forensics_v1.0.1_processed.txt", "content": "a single physical device to be dedicated to multiple purposes such as hosting different", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-670", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁlesystems or separating system from user ﬁles. If a subdivision is not needed, the entire", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-671", "filename": "Forensics_v1.0.1_processed.txt", "content": "device can be trivially allocated to a single partition.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-671", "line_number": 671, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-672", "filename": "Forensics_v1.0.1_processed.txt", "content": "A logical volume is a collection of physical volumes presented and managed as a single", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-673", "filename": "Forensics_v1.0.1_processed.txt", "content": "unit.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-673", "line_number": 673, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-674", "filename": "Forensics_v1.0.1_processed.txt", "content": "Logical volumes allow storage capacity from different devices to be pooled transparently", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-675", "filename": "Forensics_v1.0.1_processed.txt", "content": "(to the ﬁlesystem) to simplify the use of available capacity. They also enable automated", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-676", "filename": "Forensics_v1.0.1_processed.txt", "content": "block-level replication in the form of RAIDs [30] for enhanced performance and durability.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-677", "filename": "Forensics_v1.0.1_processed.txt", "content": "2.5", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-677", "line_number": 677, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-678", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data Recovery & File Content Carving", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-679", "filename": "Forensics_v1.0.1_processed.txt", "content": "One of the early staples of data recovery tools was the ‘undelete’ functionality, which can", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-680", "filename": "Forensics_v1.0.1_processed.txt", "content": "reverse the effects of users deleting data. The most common case is that of users deleting", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-681", "filename": "Forensics_v1.0.1_processed.txt", "content": "a ﬁle and needing to reverse the operation. On a HDD, this reversal is readily achievable", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-681", "line_number": 681, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-682", "filename": "Forensics_v1.0.1_processed.txt", "content": "immediately after the deletion - the storage taken up by the ﬁle’s content is merely deallocated", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-683", "filename": "Forensics_v1.0.1_processed.txt", "content": "(marked as available), but no actual destruction (sanitisation) of the data takes place.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-684", "filename": "Forensics_v1.0.1_processed.txt", "content": "A more difﬁcult case is a HDD that has been in use for some time and that has been subse-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-685", "filename": "Forensics_v1.0.1_processed.txt", "content": "quently formatted (e.g., by somebody attempting to destroy evidence). The often employed", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-686", "filename": "Forensics_v1.0.1_processed.txt", "content": "quick format command has the effect of overlaying a set of data structures that correspond", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-687", "filename": "Forensics_v1.0.1_processed.txt", "content": "to an empty ﬁlesystem (a full format sanitizes the content of the media but can take hours to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-688", "filename": "Forensics_v1.0.1_processed.txt", "content": "complete so it is used less frequently). Thus, the normal ﬁlesystem interface, after querying", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-689", "filename": "Forensics_v1.0.1_processed.txt", "content": "these structures, will report that there are no ﬁles. The reality is that – at that moment – only", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-690", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁlesystem metadata has been partially overwritten, and all the data blocks representing the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-691", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁle content are still present on the media in full.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-692", "filename": "Forensics_v1.0.1_processed.txt", "content": "Forensic computing, unlike most other types of computation, is very interested in all recover-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-692", "line_number": 692, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-693", "filename": "Forensics_v1.0.1_processed.txt", "content": "able (partial) artifacts, including (and sometimes especially) deallocated ones. Unless a user", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-694", "filename": "Forensics_v1.0.1_processed.txt", "content": "has taken special measures to securely wipe a hard disk, at any given time the media contains", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-695", "filename": "Forensics_v1.0.1_processed.txt", "content": "recoverable application artifacts (ﬁles) that have ostensibly been deleted. The process of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-696", "filename": "Forensics_v1.0.1_processed.txt", "content": "restoring the artifacts is commonly accomplished by carving.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-697", "filename": "Forensics_v1.0.1_processed.txt", "content": "File (content) carving is the process of recovering and reconstructing ﬁle content directly", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-698", "filename": "Forensics_v1.0.1_processed.txt", "content": "from block storage without using the ﬁlesystem metadata. More generally, data (struc-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-699", "filename": "Forensics_v1.0.1_processed.txt", "content": "ture) carving is the process of reconstructing logical objects (such as ﬁles and database", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-700", "filename": "Forensics_v1.0.1_processed.txt", "content": "records) from a bulk data capture (disk/RAM image) without using metadata that describes", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-701", "filename": "Forensics_v1.0.1_processed.txt", "content": "the location and layout of the artifacts.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-702", "filename": "Forensics_v1.0.1_processed.txt", "content": "File carving is the oldest and most commonly used, technique and its basic form is based on", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-703", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 19", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-704", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 21", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-705", "filename": "Forensics_v1.0.1_processed.txt", "content": "header", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-706", "filename": "Forensics_v1.0.1_processed.txt", "content": "footer", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-707", "filename": "Forensics_v1.0.1_processed.txt", "content": "A1 A2 A3 A4 A5", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-714", "filename": "Forensics_v1.0.1_processed.txt", "content": "a) Contiguous file content", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-715", "filename": "Forensics_v1.0.1_processed.txt", "content": "A1 A2 A3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-716", "filename": "Forensics_v1.0.1_processed.txt", "content": "A4 A5", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-722", "filename": "Forensics_v1.0.1_processed.txt", "content": "b) Nested file content", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-722", "line_number": 722, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-724", "filename": "Forensics_v1.0.1_processed.txt", "content": "A2 A3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-725", "filename": "Forensics_v1.0.1_processed.txt", "content": "A4 A5", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-728", "filename": "Forensics_v1.0.1_processed.txt", "content": "c) Bifragmented file", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-730", "filename": "Forensics_v1.0.1_processed.txt", "content": "A2 A3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-731", "filename": "Forensics_v1.0.1_processed.txt", "content": "A4 A5", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-737", "filename": "Forensics_v1.0.1_processed.txt", "content": "d) Interleaved file content", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-738", "filename": "Forensics_v1.0.1_processed.txt", "content": "Figure 2: Common ﬁle content layout encountered during carving.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-738", "line_number": 738, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-739", "filename": "Forensics_v1.0.1_processed.txt", "content": "two simple observations: a) most ﬁle formats have speciﬁc beginning and end tags (a.k.a.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-739", "line_number": 739, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-740", "filename": "Forensics_v1.0.1_processed.txt", "content": "header and footer); and b) ﬁle systems strongly favour a sequential ﬁle layout to maximise", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-740", "line_number": 740, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-741", "filename": "Forensics_v1.0.1_processed.txt", "content": "throughput.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-741", "line_number": 741, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-742", "filename": "Forensics_v1.0.1_processed.txt", "content": "Put together, these yield a basic recovery algorithm: 1) scan the capture sequentially until a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-743", "filename": "Forensics_v1.0.1_processed.txt", "content": "known header is found; for example, JPEG images always start with the (hexadecimal) FF", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-744", "filename": "Forensics_v1.0.1_processed.txt", "content": "D8 FF header; 2) scan sequentially until a corresponding footer is found; FF D9 for JPEG;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-745", "filename": "Forensics_v1.0.1_processed.txt", "content": "3) copy the data in between as the recovered artifact. Figure 2 illustrates some of the most", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-746", "filename": "Forensics_v1.0.1_processed.txt", "content": "common cases encountered during ﬁle carving:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-747", "filename": "Forensics_v1.0.1_processed.txt", "content": "1. No fragmentation is the most typical case, as modern ﬁlesystems require extra effort to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-748", "filename": "Forensics_v1.0.1_processed.txt", "content": "ensure sequential layout for optimal performance.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-749", "filename": "Forensics_v1.0.1_processed.txt", "content": "2. Nested content is often the result of deletion; in the example, after the initial sequential", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-750", "filename": "Forensics_v1.0.1_processed.txt", "content": "back-to-back layout of the ﬁles, the content ahead and behind ﬁle B was deleted and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-750", "line_number": 750, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-751", "filename": "Forensics_v1.0.1_processed.txt", "content": "replaced by A. In some cases, the ﬁle format allows nesting; e.g., JPEGs commonly", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-752", "filename": "Forensics_v1.0.1_processed.txt", "content": "have a thumbnail version of the image, which is also in JPEG format. This case can be", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-753", "filename": "Forensics_v1.0.1_processed.txt", "content": "solved by making multiple passes – once B is carved out (and its blocks removed from", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-754", "filename": "Forensics_v1.0.1_processed.txt", "content": "further consideration) the content of A becomes contiguous, so a subsequent pass will", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-755", "filename": "Forensics_v1.0.1_processed.txt", "content": "readily extract it.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-756", "filename": "Forensics_v1.0.1_processed.txt", "content": "3. Bi-fragmented ﬁles are split into two contiguous pieces with the other content in between,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-756", "line_number": 756, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-757", "filename": "Forensics_v1.0.1_processed.txt", "content": "which also determines how difﬁcult the reconstruction is; if the content in the middle is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-757", "line_number": 757, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-758", "filename": "Forensics_v1.0.1_processed.txt", "content": "easily distinguished from the content of the ﬁle (e.g., the pieces of text in the middle of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-759", "filename": "Forensics_v1.0.1_processed.txt", "content": "a compressed image) then the problem is relatively easy. Otherwise, it is ambiguous", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-760", "filename": "Forensics_v1.0.1_processed.txt", "content": "and it could be quite difﬁcult to identify the matching pieces.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-760", "line_number": 760, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-761", "filename": "Forensics_v1.0.1_processed.txt", "content": "4. Interleaved content is a more complicated version of nesting which happens when larger", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-761", "line_number": 761, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-762", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁles are used to ﬁll the gaps created by the deletion of smaller ones.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-762", "line_number": 762, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-763", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 20", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-763", "line_number": 763, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-764", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 22", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-765", "filename": "Forensics_v1.0.1_processed.txt", "content": "This simple carving approach usually yields a good number of usable artifacts; however, real", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-766", "filename": "Forensics_v1.0.1_processed.txt", "content": "data can contain a number of atypical patterns, which can lead to a large number of repetitive", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-767", "filename": "Forensics_v1.0.1_processed.txt", "content": "and/or false positive results. One major reason is that ﬁle formats are not designed with", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-768", "filename": "Forensics_v1.0.1_processed.txt", "content": "carving in mind and rarely have robust internal metadata that connect the constituent pieces", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-769", "filename": "Forensics_v1.0.1_processed.txt", "content": "together. Some do not even have a designated header and/or footer, and this can result in a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-770", "filename": "Forensics_v1.0.1_processed.txt", "content": "large number of false positives, potentially producing results substantially larger in volume", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-771", "filename": "Forensics_v1.0.1_processed.txt", "content": "than the source data.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-772", "filename": "Forensics_v1.0.1_processed.txt", "content": "Slack space recovery. Both RAM and persistent storage are almost always allocated in multi-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-773", "filename": "Forensics_v1.0.1_processed.txt", "content": "ples of a chosen minimum allocation units. Therefore, at the end of the allocated space, there", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-774", "filename": "Forensics_v1.0.1_processed.txt", "content": "is storage capacity – slack space – that is not used by the application, but is also not available", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-775", "filename": "Forensics_v1.0.1_processed.txt", "content": "for other uses. For example, if the minimum allocation is 4KiB, and a ﬁle needs 14KiB, the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-776", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁlesystem will allocate four 4KiB blocks. The application will fully use the ﬁrst three blocks,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-777", "filename": "Forensics_v1.0.1_processed.txt", "content": "but will only use 2KiB from the last block. This creates the potential to store data that would", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-778", "filename": "Forensics_v1.0.1_processed.txt", "content": "be inaccessible via the standard ﬁlesystem interface and can provide a simple means to hide", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-779", "filename": "Forensics_v1.0.1_processed.txt", "content": "data.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-780", "filename": "Forensics_v1.0.1_processed.txt", "content": "Slack space is the difference between the allocated storage for a data object, such as ﬁle,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-781", "filename": "Forensics_v1.0.1_processed.txt", "content": "or a volume, and the storage in actual use.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-782", "filename": "Forensics_v1.0.1_processed.txt", "content": "Once aware of the potential for storing hidden data in slack space, it is relatively easy to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-783", "filename": "Forensics_v1.0.1_processed.txt", "content": "identify and examine it, and this is a standard step in most investigations.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-784", "filename": "Forensics_v1.0.1_processed.txt", "content": "Upcoming challenges. As solid state drives continue to grow in capacity and displace hard", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-785", "filename": "Forensics_v1.0.1_processed.txt", "content": "disks from an increasing proportion of operational data storage, ﬁle carving’s utility is set to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-786", "filename": "Forensics_v1.0.1_processed.txt", "content": "diminish over time. The reason lies in the fact that SSD blocks need to be written twice in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-787", "filename": "Forensics_v1.0.1_processed.txt", "content": "order to be reused (the ﬁrst write resets the state of the block, thereby enabling its reuse). To", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-788", "filename": "Forensics_v1.0.1_processed.txt", "content": "improve performance, the TRIM and UNMAP commands were added to the ATA and SCSI", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-789", "filename": "Forensics_v1.0.1_processed.txt", "content": "command sets, respectively; they provide a mechanism for the ﬁlesystem to indicate to the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-790", "filename": "Forensics_v1.0.1_processed.txt", "content": "storage device which blocks need to be garbage collected and prepared for reuse.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-791", "filename": "Forensics_v1.0.1_processed.txt", "content": "King & Vidas [31] established experimentally that ﬁle carving would only work in a narrow", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-792", "filename": "Forensics_v1.0.1_processed.txt", "content": "set of circumstances on modern Solid State Drives (SSDs). Speciﬁcally, they show that for a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-793", "filename": "Forensics_v1.0.1_processed.txt", "content": "TRIM-aware operating system, such as Windows 7 and after, the data recovery rates in their", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-794", "filename": "Forensics_v1.0.1_processed.txt", "content": "tests were almost universally zero. In contrast, using a pre-TRIM OS (Windows XP) allows for", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-795", "filename": "Forensics_v1.0.1_processed.txt", "content": "near-perfect recovery rates under the same experimental conditions.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-797", "filename": "Forensics_v1.0.1_processed.txt", "content": "MAIN MEMORY FORENSICS", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-798", "filename": "Forensics_v1.0.1_processed.txt", "content": "[32]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-799", "filename": "Forensics_v1.0.1_processed.txt", "content": "The early view of best forensic practices was to literally pull the plug on a machine that", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-800", "filename": "Forensics_v1.0.1_processed.txt", "content": "was to be impounded. The rationale was that this would remove any possibility of alerting", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-801", "filename": "Forensics_v1.0.1_processed.txt", "content": "the processes running on the host and would preempt any attempts to hide information.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-802", "filename": "Forensics_v1.0.1_processed.txt", "content": "Over time, experience has shown that these concerns were largely exaggerated and that the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-803", "filename": "Forensics_v1.0.1_processed.txt", "content": "substantial and irreversible loss of important forensic information such as open connections", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-804", "filename": "Forensics_v1.0.1_processed.txt", "content": "and encryption keys was rarely justiﬁed. Studies have clearly demonstrated that data tend to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-805", "filename": "Forensics_v1.0.1_processed.txt", "content": "persist for a long time in volatile memory ([33], [34]). There is a wealth of information about a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-806", "filename": "Forensics_v1.0.1_processed.txt", "content": "system’s run-time state that can be readily extracted, even from a snapshot [32]:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-807", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 21", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-808", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 23", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-809", "filename": "Forensics_v1.0.1_processed.txt", "content": "Process information. It is practical to identify and enumerate all the running processes, threads", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-810", "filename": "Forensics_v1.0.1_processed.txt", "content": "and loaded systems modules; we can obtain a copy of the individual processes’ code, stack,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-811", "filename": "Forensics_v1.0.1_processed.txt", "content": "heap, code, and data segments. All this is particularly useful when analysing compromised", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-812", "filename": "Forensics_v1.0.1_processed.txt", "content": "machines, as it allows the identiﬁcation of suspicious services, abnormal parent/child rela-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-813", "filename": "Forensics_v1.0.1_processed.txt", "content": "tionships, and, more generally, to search for known symptoms of compromise, or patterns of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-813", "line_number": 813, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-814", "filename": "Forensics_v1.0.1_processed.txt", "content": "attack.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-814", "line_number": 814, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-815", "filename": "Forensics_v1.0.1_processed.txt", "content": "File information. It is practical for identifying any open ﬁles, shared libraries, shared memory,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-816", "filename": "Forensics_v1.0.1_processed.txt", "content": "and anonymously mapped memory. This is particularly useful for identifying correlated user", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-817", "filename": "Forensics_v1.0.1_processed.txt", "content": "actions and ﬁle system activities, potentially demonstrating user intent.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-817", "line_number": 817, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-818", "filename": "Forensics_v1.0.1_processed.txt", "content": "Network connections. It is practical for identifying open and recently closed network connec-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-818", "line_number": 818, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-819", "filename": "Forensics_v1.0.1_processed.txt", "content": "tions and protocol information, as well as sending and receiving queues of data not yet sent", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-820", "filename": "Forensics_v1.0.1_processed.txt", "content": "or delivered, respectively. This information could readily be used to identify related parties", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-821", "filename": "Forensics_v1.0.1_processed.txt", "content": "and communication patterns between them.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-822", "filename": "Forensics_v1.0.1_processed.txt", "content": "Artifacts and fragments. Just like the ﬁlesystem, the memory management system tends to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-823", "filename": "Forensics_v1.0.1_processed.txt", "content": "be reactive and leaves a lot of artifact traces behind. This is primarily an effort to avoid any", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-824", "filename": "Forensics_v1.0.1_processed.txt", "content": "processing that is not absolutely necessary for the functioning of the system; caching disk", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-825", "filename": "Forensics_v1.0.1_processed.txt", "content": "and network data tends to leave traces in memory for a long time.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-826", "filename": "Forensics_v1.0.1_processed.txt", "content": "Memory analysis can be performed either in real time on a live (running) system, or it could", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-827", "filename": "Forensics_v1.0.1_processed.txt", "content": "be performed on a snapshot (memory dump) of the state of the system. In addition to using", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-828", "filename": "Forensics_v1.0.1_processed.txt", "content": "specialized memory acquisitions tools, or a build-in snapshot mechanism (in virtualized", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-829", "filename": "Forensics_v1.0.1_processed.txt", "content": "environments) memory content can also be obtained from a system hibernation ﬁle, page", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-830", "filename": "Forensics_v1.0.1_processed.txt", "content": "swap, or a crash dump.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-831", "filename": "Forensics_v1.0.1_processed.txt", "content": "In live forensics, a trusted agent (process) designed to allow remote access over a secure", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-832", "filename": "Forensics_v1.0.1_processed.txt", "content": "channel is pre-installed on the system. The remote operator has full control over the monitored", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-833", "filename": "Forensics_v1.0.1_processed.txt", "content": "system and can take snapshots of speciﬁc processes, or the entire system. Live investigations", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-834", "filename": "Forensics_v1.0.1_processed.txt", "content": "are an extension of regular security preventive mechanisms, which allow for maximum control", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-835", "filename": "Forensics_v1.0.1_processed.txt", "content": "and data acquisition; they are primarily used in large enterprise deployments.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-836", "filename": "Forensics_v1.0.1_processed.txt", "content": "The main conceptual problem of working on a live system is that, if it is compromised, the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-837", "filename": "Forensics_v1.0.1_processed.txt", "content": "data acquisition and analysis results are not trustworthy; therefore, forensic analysis is most", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-838", "filename": "Forensics_v1.0.1_processed.txt", "content": "frequently performed on a snapshot of the target system’s RAM. Analysing a snapshot is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-839", "filename": "Forensics_v1.0.1_processed.txt", "content": "considerably more difﬁcult than working with a live system, which provides access to the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-840", "filename": "Forensics_v1.0.1_processed.txt", "content": "state of the running system via a variety of APIs and data structures. In contrast, a raw", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-841", "filename": "Forensics_v1.0.1_processed.txt", "content": "memory capture offers no such facilities and forensic tools need to rebuild the ability to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-842", "filename": "Forensics_v1.0.1_processed.txt", "content": "extract semantic information from the ground up. This is a semantic gap problem, and the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-843", "filename": "Forensics_v1.0.1_processed.txt", "content": "purpose of memory forensics is to bridge it.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-844", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 22", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-845", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 24", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-845", "line_number": 845, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-847", "filename": "Forensics_v1.0.1_processed.txt", "content": "APPLICATION FORENSICS", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-847", "line_number": 847, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-848", "filename": "Forensics_v1.0.1_processed.txt", "content": "Application forensics is the process of establishing a data-centric theory of operation for a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-849", "filename": "Forensics_v1.0.1_processed.txt", "content": "speciﬁc application. The goal of the analysis is to objectively establish causal dependencies", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-850", "filename": "Forensics_v1.0.1_processed.txt", "content": "between data input and output, as a function of the user interactions with the application.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-851", "filename": "Forensics_v1.0.1_processed.txt", "content": "Depending on whether an application is an open or closed source and on the level of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-852", "filename": "Forensics_v1.0.1_processed.txt", "content": "accompanying documentation, the analytical effort required can vary from reading detailed", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-853", "filename": "Forensics_v1.0.1_processed.txt", "content": "speciﬁcations to reverse engineering code, data structures and communication protocols, to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-854", "filename": "Forensics_v1.0.1_processed.txt", "content": "performing time-consuming black box differential analysis experiments. Alternatively, forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-855", "filename": "Forensics_v1.0.1_processed.txt", "content": "tool vendors may license code from the application vendor to gain access to the proprietary", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-856", "filename": "Forensics_v1.0.1_processed.txt", "content": "data structures.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-857", "filename": "Forensics_v1.0.1_processed.txt", "content": "The big advantage of analysing applications is that we have a better chance of observing", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-858", "filename": "Forensics_v1.0.1_processed.txt", "content": "and documenting direct evidence of user actions, which is of primary importance to the legal", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-859", "filename": "Forensics_v1.0.1_processed.txt", "content": "process. Also, the level of abstraction of the relevant forensic traces tend to have a level of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-860", "filename": "Forensics_v1.0.1_processed.txt", "content": "abstraction corresponding to a particular domain.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-861", "filename": "Forensics_v1.0.1_processed.txt", "content": "4.1", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-862", "filename": "Forensics_v1.0.1_processed.txt", "content": "Case Study: the Web Browser", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-863", "filename": "Forensics_v1.0.1_processed.txt", "content": "Although there are at least four major web browsers in common use, after more than 20 years", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-864", "filename": "Forensics_v1.0.1_processed.txt", "content": "of development, their capabilities have converged, thus allowing us to talk about them in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-865", "filename": "Forensics_v1.0.1_processed.txt", "content": "common terms. There are six main sources of forensically interesting information:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-866", "filename": "Forensics_v1.0.1_processed.txt", "content": "URL/search history. At present, there are no practical barriers to maintaining a complete", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-867", "filename": "Forensics_v1.0.1_processed.txt", "content": "browsing history (a log of visited websites), and making it available to users is a major", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-868", "filename": "Forensics_v1.0.1_processed.txt", "content": "usability feature; most users rarely delete this information. Separately, service providers such", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-869", "filename": "Forensics_v1.0.1_processed.txt", "content": "as Google and Facebook, are interested in this information for commercial reasons, and make", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-870", "filename": "Forensics_v1.0.1_processed.txt", "content": "it easy to share a browsing log with multiple devices. Combined with the content of the local", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-871", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁle cache, the browsing history allows an investigator to almost look over the shoulder of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-872", "filename": "Forensics_v1.0.1_processed.txt", "content": "the user of interest as they were navigating the Web. In particular, analysing user queries", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-873", "filename": "Forensics_v1.0.1_processed.txt", "content": "to search engines is among the most commonly employed techniques. The search query is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-874", "filename": "Forensics_v1.0.1_processed.txt", "content": "encoded as part of the URL, and can often provide very clear and targeted clues as to what", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-875", "filename": "Forensics_v1.0.1_processed.txt", "content": "the user was trying to accomplish.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-876", "filename": "Forensics_v1.0.1_processed.txt", "content": "Form data. Browsers offer the convenience of remembering auto-completing passwords and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-877", "filename": "Forensics_v1.0.1_processed.txt", "content": "other form data (such as address information). This can be very helpful to an investigator,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-877", "line_number": 877, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-878", "filename": "Forensics_v1.0.1_processed.txt", "content": "especially if the user is less security conscious and does not use a master password to encrypt", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-878", "line_number": 878, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-879", "filename": "Forensics_v1.0.1_processed.txt", "content": "all of this information.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-879", "line_number": 879, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-880", "filename": "Forensics_v1.0.1_processed.txt", "content": "Temporary ﬁles. The local ﬁle cache provides its own chronology of web activities, including a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-880", "line_number": 880, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-881", "filename": "Forensics_v1.0.1_processed.txt", "content": "stored version of the actual web objects that were downloaded and shown to the user (these", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-881", "line_number": 881, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-882", "filename": "Forensics_v1.0.1_processed.txt", "content": "may no longer be available online). Although caching has become considerably less effective", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-882", "line_number": 882, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-883", "filename": "Forensics_v1.0.1_processed.txt", "content": "owing to the increased use of dynamic content, this is tempered by the large increase in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-883", "line_number": 883, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-884", "filename": "Forensics_v1.0.1_processed.txt", "content": "available storage capacity, which places very few, if any, practical constraints on the amount", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-884", "line_number": 884, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-885", "filename": "Forensics_v1.0.1_processed.txt", "content": "of data cached.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-885", "line_number": 885, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-886", "filename": "Forensics_v1.0.1_processed.txt", "content": "Downloaded ﬁles are, by default, never deleted providing another valuable source of activity", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-886", "line_number": 886, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-887", "filename": "Forensics_v1.0.1_processed.txt", "content": "information.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-887", "line_number": 887, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-888", "filename": "Forensics_v1.0.1_processed.txt", "content": "HTML5 local storage provides a standard means for web applications to store information", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-888", "line_number": 888, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-889", "filename": "Forensics_v1.0.1_processed.txt", "content": "locally; for example, this could be used to support disconnected operations, or to provide a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-889", "line_number": 889, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-890", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 23", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-890", "line_number": 890, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-891", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 25", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-891", "line_number": 891, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-892", "filename": "Forensics_v1.0.1_processed.txt", "content": "measure of persistence for user input. Accordingly, the same interface can be interrogated to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-892", "line_number": 892, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-893", "filename": "Forensics_v1.0.1_processed.txt", "content": "reconstruct web activities.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-893", "line_number": 893, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-894", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cookies are opaque pieces of data used by servers to keep a variety of information on the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-894", "line_number": 894, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-895", "filename": "Forensics_v1.0.1_processed.txt", "content": "web client in order to support transactions such as web mail sessions. In practice, most", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-895", "line_number": 895, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-896", "filename": "Forensics_v1.0.1_processed.txt", "content": "cookies are used by websites to track user behaviour, and it is well-documented that some", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-896", "line_number": 896, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-897", "filename": "Forensics_v1.0.1_processed.txt", "content": "providers go to great lengths to make sure that this information is resilient. Some cookies", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-897", "line_number": 897, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-898", "filename": "Forensics_v1.0.1_processed.txt", "content": "are time-limited access tokens that can provide access to online accounts (until they expire);", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-898", "line_number": 898, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-899", "filename": "Forensics_v1.0.1_processed.txt", "content": "others have a parsable structure and may provide additional information.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-899", "line_number": 899, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-900", "filename": "Forensics_v1.0.1_processed.txt", "content": "Most local information is stored in SQLite databases, which provide a secondary target for", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-900", "line_number": 900, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-901", "filename": "Forensics_v1.0.1_processed.txt", "content": "data recovery. In particular, ostensibly deleted records may persist until the database is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-901", "line_number": 901, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-902", "filename": "Forensics_v1.0.1_processed.txt", "content": "explicitly ‘vacuumed’; otherwise, they remain recoverable at a later time ([35, 36]).", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-902", "line_number": 902, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-904", "filename": "Forensics_v1.0.1_processed.txt", "content": "CLOUD FORENSICS", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-904", "line_number": 904, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-905", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cloud computing is fast emerging as the primary model for delivering information technology", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-905", "line_number": 905, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-906", "filename": "Forensics_v1.0.1_processed.txt", "content": "(IT) services to Internet-connected devices. It brings both disruptive challenges for current", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-906", "line_number": 906, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-907", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic tools, methods and processes, as well as qualitatively new forensic opportunities. It", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-907", "line_number": 907, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-908", "filename": "Forensics_v1.0.1_processed.txt", "content": "is not difﬁcult to foresee that, after an intermediate period of adjustment, digital forensics will", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-908", "line_number": 908, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-909", "filename": "Forensics_v1.0.1_processed.txt", "content": "enter a new period marked by substantially higher levels of automation and will employ much", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-909", "line_number": 909, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-910", "filename": "Forensics_v1.0.1_processed.txt", "content": "more sophisticated data analytics. Cloud computing environments will greatly facilitate this", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-910", "line_number": 910, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-911", "filename": "Forensics_v1.0.1_processed.txt", "content": "process, but not before bringing about substantial changes to currently established tools and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-911", "line_number": 911, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-912", "filename": "Forensics_v1.0.1_processed.txt", "content": "practices.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-912", "line_number": 912, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-913", "filename": "Forensics_v1.0.1_processed.txt", "content": "5.1", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-913", "line_number": 913, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-914", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cloud Basics", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-914", "line_number": 914, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-915", "filename": "Forensics_v1.0.1_processed.txt", "content": "Conceptually, cloud-based IT abstracts away the physical compute and communication infras-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-915", "line_number": 915, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-916", "filename": "Forensics_v1.0.1_processed.txt", "content": "tructure, and allows customers to rent as much compute capacity as needed. Cloud systems", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-916", "line_number": 916, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-917", "filename": "Forensics_v1.0.1_processed.txt", "content": "have ﬁve essential characteristics: on-demand self service, broad network access, resource", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-917", "line_number": 917, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-918", "filename": "Forensics_v1.0.1_processed.txt", "content": "pooling, rapid elasticity, and measured service. [37]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-918", "line_number": 918, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-919", "filename": "Forensics_v1.0.1_processed.txt", "content": "The cloud is enabled by a number of technological developments, but its adoption is driven", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-919", "line_number": 919, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-920", "filename": "Forensics_v1.0.1_processed.txt", "content": "primarily by business considerations, which drive changes to how organisations and individu-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-920", "line_number": 920, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-921", "filename": "Forensics_v1.0.1_processed.txt", "content": "als use IT services. Accordingly, it also changes how software is developed, maintained and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-921", "line_number": 921, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-922", "filename": "Forensics_v1.0.1_processed.txt", "content": "delivered to its customers. Cloud computing services are commonly classiﬁed into one of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-922", "line_number": 922, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-923", "filename": "Forensics_v1.0.1_processed.txt", "content": "three canonical models – Software as a Service (SaaS), Platform as a Service (PaaS) and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-923", "line_number": 923, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-924", "filename": "Forensics_v1.0.1_processed.txt", "content": "Infrastructure as a Service (IaaS). In actual deployments, the distinctions can be blurred and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-924", "line_number": 924, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-925", "filename": "Forensics_v1.0.1_processed.txt", "content": "many cloud deployments (and potential investigative targets) incorporate elements of all of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-925", "line_number": 925, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-926", "filename": "Forensics_v1.0.1_processed.txt", "content": "these.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-926", "line_number": 926, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-927", "filename": "Forensics_v1.0.1_processed.txt", "content": "The differences between the models are best understood when we consider the virtualised", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-927", "line_number": 927, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-928", "filename": "Forensics_v1.0.1_processed.txt", "content": "computing environments as a stack of layers: hardware such as storage, and networking;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-928", "line_number": 928, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-929", "filename": "Forensics_v1.0.1_processed.txt", "content": "virtualisation, consisting of a hypervisor allowing the installation and lifecycle management", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-929", "line_number": 929, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-930", "filename": "Forensics_v1.0.1_processed.txt", "content": "of virtual machines; operating system, installed on each virtual machine; middleware and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-930", "line_number": 930, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-931", "filename": "Forensics_v1.0.1_processed.txt", "content": "runtime environment; and application and data.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-931", "line_number": 931, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-932", "filename": "Forensics_v1.0.1_processed.txt", "content": "Each of the cloud models splits the responsibility between the client and the Cloud Service", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-932", "line_number": 932, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-933", "filename": "Forensics_v1.0.1_processed.txt", "content": "Provider (CSP) at different levels in the stack (Figure 3). In a private (cloud) deployment,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-933", "line_number": 933, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-934", "filename": "Forensics_v1.0.1_processed.txt", "content": "the entire stack is hosted by the owner and the overall forensic picture is very similar to the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-934", "line_number": 934, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-935", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 24", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-935", "line_number": 935, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-936", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 26", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-936", "line_number": 936, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-937", "filename": "Forensics_v1.0.1_processed.txt", "content": "Hardware", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-937", "line_number": 937, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-938", "filename": "Forensics_v1.0.1_processed.txt", "content": "Virtualization", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-938", "line_number": 938, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-939", "filename": "Forensics_v1.0.1_processed.txt", "content": "Operating System", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-939", "line_number": 939, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-940", "filename": "Forensics_v1.0.1_processed.txt", "content": "Middleware", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-940", "line_number": 940, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-941", "filename": "Forensics_v1.0.1_processed.txt", "content": "Runtime", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-941", "line_number": 941, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-942", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-942", "line_number": 942, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-943", "filename": "Forensics_v1.0.1_processed.txt", "content": "Application", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-943", "line_number": 943, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-944", "filename": "Forensics_v1.0.1_processed.txt", "content": "Hardware", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-944", "line_number": 944, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-945", "filename": "Forensics_v1.0.1_processed.txt", "content": "Virtualization", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-945", "line_number": 945, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-946", "filename": "Forensics_v1.0.1_processed.txt", "content": "Operating System", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-946", "line_number": 946, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-947", "filename": "Forensics_v1.0.1_processed.txt", "content": "Middleware", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-947", "line_number": 947, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-948", "filename": "Forensics_v1.0.1_processed.txt", "content": "Runtime", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-948", "line_number": 948, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-949", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-949", "line_number": 949, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-950", "filename": "Forensics_v1.0.1_processed.txt", "content": "Application", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-950", "line_number": 950, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-951", "filename": "Forensics_v1.0.1_processed.txt", "content": "Hardware", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-951", "line_number": 951, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-952", "filename": "Forensics_v1.0.1_processed.txt", "content": "Virtualization", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-952", "line_number": 952, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-953", "filename": "Forensics_v1.0.1_processed.txt", "content": "Operating System", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-953", "line_number": 953, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-954", "filename": "Forensics_v1.0.1_processed.txt", "content": "Middleware", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-954", "line_number": 954, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-955", "filename": "Forensics_v1.0.1_processed.txt", "content": "Runtime", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-955", "line_number": 955, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-956", "filename": "Forensics_v1.0.1_processed.txt", "content": "Data", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-956", "line_number": 956, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-957", "filename": "Forensics_v1.0.1_processed.txt", "content": "Application", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-957", "line_number": 957, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-958", "filename": "Forensics_v1.0.1_processed.txt", "content": "Infrastructure", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-958", "line_number": 958, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-959", "filename": "Forensics_v1.0.1_processed.txt", "content": "as a Service (IaaS)", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-959", "line_number": 959, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-960", "filename": "Forensics_v1.0.1_processed.txt", "content": "Platform", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-960", "line_number": 960, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-961", "filename": "Forensics_v1.0.1_processed.txt", "content": "as a Service (PaaS)", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-961", "line_number": 961, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-962", "filename": "Forensics_v1.0.1_processed.txt", "content": "Software", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-962", "line_number": 962, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-963", "filename": "Forensics_v1.0.1_processed.txt", "content": "as a Service (SaaS)", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-963", "line_number": 963, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-964", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cloud Service", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-964", "line_number": 964, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-965", "filename": "Forensics_v1.0.1_processed.txt", "content": "Providers", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-965", "line_number": 965, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-966", "filename": "Forensics_v1.0.1_processed.txt", "content": "Customer", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-966", "line_number": 966, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-967", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cloud Service", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-967", "line_number": 967, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-968", "filename": "Forensics_v1.0.1_processed.txt", "content": "Providers", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-968", "line_number": 968, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-969", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cloud Service", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-969", "line_number": 969, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-970", "filename": "Forensics_v1.0.1_processed.txt", "content": "Providers", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-970", "line_number": 970, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-971", "filename": "Forensics_v1.0.1_processed.txt", "content": "Customer", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-971", "line_number": 971, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-972", "filename": "Forensics_v1.0.1_processed.txt", "content": "Figure 3: Layers of cloud computing environment owned by customer and cloud service", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-972", "line_number": 972, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-973", "filename": "Forensics_v1.0.1_processed.txt", "content": "provider on three service models: IaaS, PaaS, and SaaS (public cloud).", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-973", "line_number": 973, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-974", "filename": "Forensics_v1.0.1_processed.txt", "content": "problem of investigating a non-cloud IT target. Data ownership is clear, as is the legal and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-974", "line_number": 974, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-975", "filename": "Forensics_v1.0.1_processed.txt", "content": "procedural path to obtain it; indeed, the very use of the term ‘cloud’ in this situation is not", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-975", "line_number": 975, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-976", "filename": "Forensics_v1.0.1_processed.txt", "content": "particularly signiﬁcant to a forensic inquiry.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-976", "line_number": 976, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-977", "filename": "Forensics_v1.0.1_processed.txt", "content": "In a public deployment, the SaaS/PaaS/IaaS classiﬁcation becomes important, as it indicates", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-977", "line_number": 977, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-978", "filename": "Forensics_v1.0.1_processed.txt", "content": "the ownership of data and service responsibilities. Figure 3 shows the typical ownership of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-978", "line_number": 978, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-979", "filename": "Forensics_v1.0.1_processed.txt", "content": "layers by customer and service providers under different service models. In hybrid deploy-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-979", "line_number": 979, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-980", "filename": "Forensics_v1.0.1_processed.txt", "content": "ments, layer ownership can be split between the customer and the provider, and/or across", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-980", "line_number": 980, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-981", "filename": "Forensics_v1.0.1_processed.txt", "content": "multiple providers. Further, it can change over time, as, for example, the customer may handle", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-981", "line_number": 981, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-982", "filename": "Forensics_v1.0.1_processed.txt", "content": "the base load on private infrastructure, but burst into the public cloud to handle peak demand,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-982", "line_number": 982, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-983", "filename": "Forensics_v1.0.1_processed.txt", "content": "or system failures.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-983", "line_number": 983, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-984", "filename": "Forensics_v1.0.1_processed.txt", "content": "5.2", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-984", "line_number": 984, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-985", "filename": "Forensics_v1.0.1_processed.txt", "content": "Forensic Challenges", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-985", "line_number": 985, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-986", "filename": "Forensics_v1.0.1_processed.txt", "content": "The main technical challenges to established forensic practices can be summarised as", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-986", "line_number": 986, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-987", "filename": "Forensics_v1.0.1_processed.txt", "content": "follows.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-987", "line_number": 987, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-988", "filename": "Forensics_v1.0.1_processed.txt", "content": "Logical acquisition is the norm. The existing forensic toolset is almost exclusively built to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-988", "line_number": 988, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-989", "filename": "Forensics_v1.0.1_processed.txt", "content": "work with the leftover artifacts of prior computations. It relies on algorithmic knowledge of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-989", "line_number": 989, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-990", "filename": "Forensics_v1.0.1_processed.txt", "content": "different OS subsystems such as the ﬁlesystem in order to interpret the physical layout of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-990", "line_number": 990, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-991", "filename": "Forensics_v1.0.1_processed.txt", "content": "data as acquired from the device.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-991", "line_number": 991, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-992", "filename": "Forensics_v1.0.1_processed.txt", "content": "Physical acquisition is almost completely inapplicable to the cloud, where data moves, re-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-992", "line_number": 992, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-993", "filename": "Forensics_v1.0.1_processed.txt", "content": "sources are shared and ownership and jurisdictional issues can be complicated. Cloud", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-993", "line_number": 993, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-994", "filename": "Forensics_v1.0.1_processed.txt", "content": "service APIs are emerging as the primary new interface through which data acquisition is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-994", "line_number": 994, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-995", "filename": "Forensics_v1.0.1_processed.txt", "content": "being performed.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-995", "line_number": 995, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-996", "filename": "Forensics_v1.0.1_processed.txt", "content": "The cloud is the authoritative data source. Another important reason to query cloud services", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-996", "line_number": 996, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-997", "filename": "Forensics_v1.0.1_processed.txt", "content": "for relevant information is that they store the primary historical record of computations and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-997", "line_number": 997, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-998", "filename": "Forensics_v1.0.1_processed.txt", "content": "interactions with users. Most residual information on the client, such as a cloud drive is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-998", "line_number": 998, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-999", "filename": "Forensics_v1.0.1_processed.txt", "content": "transient and often of uncertain provenance.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-999", "line_number": 999, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1000", "filename": "Forensics_v1.0.1_processed.txt", "content": "Logging is pervasive. Cloud-based software is developed and organised differently. Instead", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1000", "line_number": 1000, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1001", "filename": "Forensics_v1.0.1_processed.txt", "content": "of one monolithic piece of code, the application logic is decomposed into several layers and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1001", "line_number": 1001, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1002", "filename": "Forensics_v1.0.1_processed.txt", "content": "modules that interact with each other over well-deﬁned service interfaces. Once the software", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1002", "line_number": 1002, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1003", "filename": "Forensics_v1.0.1_processed.txt", "content": "components and their communication are formalised, it becomes easy to organise extensive", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1003", "line_number": 1003, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1004", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 25", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1004", "line_number": 1004, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1005", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 27", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1005", "line_number": 1005, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1006", "filename": "Forensics_v1.0.1_processed.txt", "content": "logging of every aspect of the system. Indeed, it becomes critical to have this information", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1006", "line_number": 1006, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1007", "filename": "Forensics_v1.0.1_processed.txt", "content": "just to be able to debug, test and monitor cloud applications and services.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1007", "line_number": 1007, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1008", "filename": "Forensics_v1.0.1_processed.txt", "content": "These developments point to logs (of user and system activities) becoming the primary source", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1008", "line_number": 1008, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1009", "filename": "Forensics_v1.0.1_processed.txt", "content": "of forensic information. The immediate implication is that much more will be explicitly known", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1009", "line_number": 1009, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1010", "filename": "Forensics_v1.0.1_processed.txt", "content": "– as opposed to deduced – about the historical state of applications and artifacts. This", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1010", "line_number": 1010, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1011", "filename": "Forensics_v1.0.1_processed.txt", "content": "will require a new set of data analytics tools and will completely transform the way forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1011", "line_number": 1011, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1012", "filename": "Forensics_v1.0.1_processed.txt", "content": "investigations are performed. It will also bring new challenges in terms of long-term case", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1012", "line_number": 1012, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1013", "filename": "Forensics_v1.0.1_processed.txt", "content": "data preservation.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1013", "line_number": 1013, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1014", "filename": "Forensics_v1.0.1_processed.txt", "content": "Distributed computations are the norm. The key attribute of the client/standalone model is that", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1014", "line_number": 1014, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1015", "filename": "Forensics_v1.0.1_processed.txt", "content": "practically all computations take place on the device itself. Applications are monolithic, self-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1015", "line_number": 1015, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1016", "filename": "Forensics_v1.0.1_processed.txt", "content": "contained pieces of code that have immediate access to user input and consume it instantly", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1016", "line_number": 1016, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1017", "filename": "Forensics_v1.0.1_processed.txt", "content": "with (almost) no traces left behind. Since a large part of forensics comprises attributing the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1017", "line_number": 1017, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1018", "filename": "Forensics_v1.0.1_processed.txt", "content": "observed state of a system to user-triggered events, forensic research and development has", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1018", "line_number": 1018, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1019", "filename": "Forensics_v1.0.1_processed.txt", "content": "relentlessly focused on two driving problems – discovering every last piece of log/timestamp", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1019", "line_number": 1019, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1020", "filename": "Forensics_v1.0.1_processed.txt", "content": "information, and extracting every last bit of discarded data left behind by applications or the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1020", "line_number": 1020, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1021", "filename": "Forensics_v1.0.1_processed.txt", "content": "operating system.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1021", "line_number": 1021, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1022", "filename": "Forensics_v1.0.1_processed.txt", "content": "The cloud model, particularly SaaS, completely breaks with this approach – the computation", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1022", "line_number": 1022, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1023", "filename": "Forensics_v1.0.1_processed.txt", "content": "is split between the client and the server, with the latter performing the heavy computational", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1023", "line_number": 1023, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1024", "filename": "Forensics_v1.0.1_processed.txt", "content": "lifting and the former performing predominantly user interaction functions. Code and data", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1024", "line_number": 1024, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1025", "filename": "Forensics_v1.0.1_processed.txt", "content": "are downloaded on demand and have no persistent place with regard to the client. The", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1025", "line_number": 1025, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1026", "filename": "Forensics_v1.0.1_processed.txt", "content": "direct consequence is that the vast majority of the established forensic tool chain becomes", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1026", "line_number": 1026, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1027", "filename": "Forensics_v1.0.1_processed.txt", "content": "irrelevant, which points to a clear need for a different approach.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1027", "line_number": 1027, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1028", "filename": "Forensics_v1.0.1_processed.txt", "content": "5.3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1028", "line_number": 1028, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1029", "filename": "Forensics_v1.0.1_processed.txt", "content": "SaaS Forensics", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1029", "line_number": 1029, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1030", "filename": "Forensics_v1.0.1_processed.txt", "content": "The software industry’s traditional delivery model is Software as a Product (SaaP); that is,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1030", "line_number": 1030, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1031", "filename": "Forensics_v1.0.1_processed.txt", "content": "software acquired like any physical product and is installed by the owner on a speciﬁc machine,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1031", "line_number": 1031, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1032", "filename": "Forensics_v1.0.1_processed.txt", "content": "where all the computations are performed. As a result, the traditional analytical model of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1032", "line_number": 1032, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1033", "filename": "Forensics_v1.0.1_processed.txt", "content": "digital forensics is physical device-centric – the investigator works with physical evidence", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1033", "line_number": 1033, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1034", "filename": "Forensics_v1.0.1_processed.txt", "content": "carriers such as storage media or integrated compute devices (e.g., smartphones). On the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1034", "line_number": 1034, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1035", "filename": "Forensics_v1.0.1_processed.txt", "content": "client (or standalone) device, it is easy to identify where the computations are performed and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1035", "line_number": 1035, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1036", "filename": "Forensics_v1.0.1_processed.txt", "content": "where the results/traces are stored. The new software delivery model – Software as a Service", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1036", "line_number": 1036, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1037", "filename": "Forensics_v1.0.1_processed.txt", "content": "(SaaS) – is subscription-based and did not start becoming practical until the widespread", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1037", "line_number": 1037, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1038", "filename": "Forensics_v1.0.1_processed.txt", "content": "adoption of fast broadband access some ten to ﬁfteen years ago.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1038", "line_number": 1038, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1039", "filename": "Forensics_v1.0.1_processed.txt", "content": "The cloud renders many device-centric methods — especially those focused on low-level", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1039", "line_number": 1039, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1040", "filename": "Forensics_v1.0.1_processed.txt", "content": "physical acquisition and analysis — irrelevent. It also requires the development of new tools", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1040", "line_number": 1040, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1041", "filename": "Forensics_v1.0.1_processed.txt", "content": "that can work in the new deployment environment, where the code execution is split between", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1041", "line_number": 1041, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1042", "filename": "Forensics_v1.0.1_processed.txt", "content": "the server and the client devices, the primary storage interface is a service API and the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1042", "line_number": 1042, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1043", "filename": "Forensics_v1.0.1_processed.txt", "content": "application artifacts are not persistently stored on the device (although local storage may be", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1043", "line_number": 1043, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1044", "filename": "Forensics_v1.0.1_processed.txt", "content": "used as a cache).", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1044", "line_number": 1044, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1045", "filename": "Forensics_v1.0.1_processed.txt", "content": "Case Study: Cloud Drive Acquisition. Cloud drive services, such as Dropbox, Google Drive and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1045", "line_number": 1045, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1046", "filename": "Forensics_v1.0.1_processed.txt", "content": "Microsoft OneDrive are the SaaS version of the local storage device, which is central to modern", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1046", "line_number": 1046, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1047", "filename": "Forensics_v1.0.1_processed.txt", "content": "digital forensics. The problem of cloud drive acquisition, a clear ﬁrst investigative step, is a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1047", "line_number": 1047, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1048", "filename": "Forensics_v1.0.1_processed.txt", "content": "good illustration of the challenges and opportunities offered by SaaS with respect to forensics.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1048", "line_number": 1048, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1049", "filename": "Forensics_v1.0.1_processed.txt", "content": "At ﬁrst, it may appear that simply copying a local replica of the drive’s content is a simple and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1049", "line_number": 1049, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1050", "filename": "Forensics_v1.0.1_processed.txt", "content": "effective solution. However, this approach offers no guarantees with respect to the accuracy", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1050", "line_number": 1050, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1051", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 26", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1051", "line_number": 1051, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1052", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 28", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1052", "line_number": 1052, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1053", "filename": "Forensics_v1.0.1_processed.txt", "content": "and completeness of the acquisition. Speciﬁcally, there are three major concerns:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1053", "line_number": 1053, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1054", "filename": "Forensics_v1.0.1_processed.txt", "content": "Partial replication. The most obvious problem is that there is no guarantee that any of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1054", "line_number": 1054, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1055", "filename": "Forensics_v1.0.1_processed.txt", "content": "clients attached to an account will have a complete copy of the (cloud) drive’s content. As data", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1055", "line_number": 1055, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1056", "filename": "Forensics_v1.0.1_processed.txt", "content": "accumulates online, it quickly becomes impractical to keep full replicas on every device; indeed,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1056", "line_number": 1056, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1057", "filename": "Forensics_v1.0.1_processed.txt", "content": "it is likely that most users will have no device with a complete copy of the data. Furthermore,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1057", "line_number": 1057, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1058", "filename": "Forensics_v1.0.1_processed.txt", "content": "the acquisition tool needs direct access to the cloud drive’s metadata to ascertain its contents;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1058", "line_number": 1058, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1059", "filename": "Forensics_v1.0.1_processed.txt", "content": "without this information, the acquisition is of an unknown quality, subject to potentially stale", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1059", "line_number": 1059, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1060", "filename": "Forensics_v1.0.1_processed.txt", "content": "and omitted data.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1060", "line_number": 1060, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1061", "filename": "Forensics_v1.0.1_processed.txt", "content": "Revision acquisition. Most drive services provide some form of revision history; the look-back", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1061", "line_number": 1061, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1062", "filename": "Forensics_v1.0.1_processed.txt", "content": "period varies, but this is a standard feature that users expect, especially in paid services.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1062", "line_number": 1062, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1063", "filename": "Forensics_v1.0.1_processed.txt", "content": "Although there are some analogous data sources in traditional forensics, such as archival", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1063", "line_number": 1063, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1064", "filename": "Forensics_v1.0.1_processed.txt", "content": "versions of important OS data structures, the volume and granularity of the revision information", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1064", "line_number": 1064, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1065", "filename": "Forensics_v1.0.1_processed.txt", "content": "in cloud application are qualitatively and quantitatively different. Revisions reside in the cloud", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1065", "line_number": 1065, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1066", "filename": "Forensics_v1.0.1_processed.txt", "content": "and clients rarely have anything but the most recent version in their cache; a client-side", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1066", "line_number": 1066, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1067", "filename": "Forensics_v1.0.1_processed.txt", "content": "acquisition will clearly miss prior revisions, and does not even have the means to identify", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1067", "line_number": 1067, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1068", "filename": "Forensics_v1.0.1_processed.txt", "content": "these omissions.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1068", "line_number": 1068, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1069", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cloud-native artifacts. The mass movement towards web-based applications means that", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1069", "line_number": 1069, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1070", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensics needs to learn how to deal with a new problem – digital artifacts that have no", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1070", "line_number": 1070, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1071", "filename": "Forensics_v1.0.1_processed.txt", "content": "serialised representation in the local ﬁlesystem. For example, Google Docs documents are", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1071", "line_number": 1071, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1072", "filename": "Forensics_v1.0.1_processed.txt", "content": "stored locally as a link to the document which can only be edited via a web app. Acquiring an", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1072", "line_number": 1072, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1073", "filename": "Forensics_v1.0.1_processed.txt", "content": "opaque link without the actual content of the document has minimal forensic utility. Most", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1073", "line_number": 1073, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1074", "filename": "Forensics_v1.0.1_processed.txt", "content": "services provide the means to export the web app artifact in a standard format such as PDF;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1074", "line_number": 1074, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1075", "filename": "Forensics_v1.0.1_processed.txt", "content": "however, this can only be accomplished by requesting directly from the service (manually or", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1075", "line_number": 1075, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1076", "filename": "Forensics_v1.0.1_processed.txt", "content": "via an API).", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1076", "line_number": 1076, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1077", "filename": "Forensics_v1.0.1_processed.txt", "content": "In summary, bringing the traditional client-side approach to drive acquisition to bear on SaaS", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1077", "line_number": 1077, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1078", "filename": "Forensics_v1.0.1_processed.txt", "content": "acquisition has major conceptual ﬂaws that are beyond remediation; a new approach is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1078", "line_number": 1078, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1079", "filename": "Forensics_v1.0.1_processed.txt", "content": "needed, one that obtains the data directly from the cloud service.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1079", "line_number": 1079, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1081", "filename": "Forensics_v1.0.1_processed.txt", "content": "ARTIFACT ANALYSIS", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1081", "line_number": 1081, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1082", "filename": "Forensics_v1.0.1_processed.txt", "content": "[38, 39, 40, 41, 42]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1082", "line_number": 1082, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1083", "filename": "Forensics_v1.0.1_processed.txt", "content": "Once the external (serialised) representation of a digital artifact such as a text document", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1083", "line_number": 1083, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1084", "filename": "Forensics_v1.0.1_processed.txt", "content": "or an image is standardised, it provides a convenient level of abstraction, thus allowing the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1084", "line_number": 1084, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1085", "filename": "Forensics_v1.0.1_processed.txt", "content": "development of artifact-centric forensic techniques.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1085", "line_number": 1085, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1086", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 27", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1086", "line_number": 1086, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1087", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 29", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1087", "line_number": 1087, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1088", "filename": "Forensics_v1.0.1_processed.txt", "content": "6.1", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1088", "line_number": 1088, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1089", "filename": "Forensics_v1.0.1_processed.txt", "content": "Finding a Known Data Object: Cryptographic Hashing", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1089", "line_number": 1089, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1090", "filename": "Forensics_v1.0.1_processed.txt", "content": "The lowest common denominator for all digital artifacts is to consider them as a sequence of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1090", "line_number": 1090, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1091", "filename": "Forensics_v1.0.1_processed.txt", "content": "bits/bytes without trying to parse, or assign any semantics to them. Despite this low level", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1091", "line_number": 1091, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1092", "filename": "Forensics_v1.0.1_processed.txt", "content": "of abstraction, some crucial problems can be addressed, the most important one being to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1092", "line_number": 1092, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1093", "filename": "Forensics_v1.0.1_processed.txt", "content": "identify known content, usually ﬁles.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1093", "line_number": 1093, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1094", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cryptographic hashing is the ﬁrst tool of choice when investigating any case; it provides a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1094", "line_number": 1094, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1095", "filename": "Forensics_v1.0.1_processed.txt", "content": "basic means of validating data integrity and identifying known artifacts. Recall that a hash", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1095", "line_number": 1095, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1096", "filename": "Forensics_v1.0.1_processed.txt", "content": "function takes an arbitrary string of binary data and produces a number, often referred to as a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1096", "line_number": 1096, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1097", "filename": "Forensics_v1.0.1_processed.txt", "content": "digest, within a predeﬁned range. Ideally, given a set of different inputs, the hash function will", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1097", "line_number": 1097, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1098", "filename": "Forensics_v1.0.1_processed.txt", "content": "map them onto different outputs.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1098", "line_number": 1098, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1099", "filename": "Forensics_v1.0.1_processed.txt", "content": "Hash functions are collision-resistant if it is computationally infeasible to ﬁnd two different", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1099", "line_number": 1099, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1100", "filename": "Forensics_v1.0.1_processed.txt", "content": "inputs for which the output is the same. Cryptographic hash functions such as MD5, RIPEMD-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1100", "line_number": 1100, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1101", "filename": "Forensics_v1.0.1_processed.txt", "content": "160, SHA-1, SHA-2 and the current NIST standard SHA-3[38], are designed to be collision-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1101", "line_number": 1101, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1102", "filename": "Forensics_v1.0.1_processed.txt", "content": "resistant and produce large 128- to 512-bit results.1 Since the probability that hashing two", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1102", "line_number": 1102, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1103", "filename": "Forensics_v1.0.1_processed.txt", "content": "different data objects will produce the same digest by chance is astronomically small, we can", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1103", "line_number": 1103, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1104", "filename": "Forensics_v1.0.1_processed.txt", "content": "safely assume that, if two objects have the same crypto digest, then the objects themselves", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1104", "line_number": 1104, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1105", "filename": "Forensics_v1.0.1_processed.txt", "content": "are identical.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1105", "line_number": 1105, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1106", "filename": "Forensics_v1.0.1_processed.txt", "content": "Current practice is to apply a crypto hash function either to an entire target (drive, partition", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1106", "line_number": 1106, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1107", "filename": "Forensics_v1.0.1_processed.txt", "content": "etc.) or to individual ﬁles. The former is used to validate the integrity of the forensic tar-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1107", "line_number": 1107, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1108", "filename": "Forensics_v1.0.1_processed.txt", "content": "get by comparing before-and-after results at important points in the investigation (e.g., to", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1108", "line_number": 1108, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1109", "filename": "Forensics_v1.0.1_processed.txt", "content": "demonstrate that the integrity of the evidence throughout the chain of custody) whereas the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1109", "line_number": 1109, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1110", "filename": "Forensics_v1.0.1_processed.txt", "content": "latter are used to work with known ﬁles. This involves either removing from consideration", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1110", "line_number": 1110, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1111", "filename": "Forensics_v1.0.1_processed.txt", "content": "common ﬁles such as OS and application installations or pinpointing known ﬁles of interest", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1111", "line_number": 1111, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1112", "filename": "Forensics_v1.0.1_processed.txt", "content": "such as malware and contraband. The US National Institute of Standards and Technology", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1112", "line_number": 1112, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1113", "filename": "Forensics_v1.0.1_processed.txt", "content": "(NIST) maintains the National Software Reference Library (NSRL) [39], which covers the most", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1113", "line_number": 1113, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1114", "filename": "Forensics_v1.0.1_processed.txt", "content": "common operating system installation and application packages. Other organisations and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1114", "line_number": 1114, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1115", "filename": "Forensics_v1.0.1_processed.txt", "content": "commercial vendors of digital forensic tools provide additional hash sets of other known data.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1115", "line_number": 1115, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1116", "filename": "Forensics_v1.0.1_processed.txt", "content": "From a performance and efﬁciency perspective, hash-based ﬁle ﬁltering is very attractive –", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1116", "line_number": 1116, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1117", "filename": "Forensics_v1.0.1_processed.txt", "content": "using a 20-byte SHA-1 hash, the representation of 50 million ﬁles takes only 1 GB. This makes", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1117", "line_number": 1117, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1118", "filename": "Forensics_v1.0.1_processed.txt", "content": "it possible to load a reference set of that size in the main memory and ﬁlter out, on the ﬂy, any", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1118", "line_number": 1118, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1119", "filename": "Forensics_v1.0.1_processed.txt", "content": "known ﬁles in the set as data is read from a forensic target.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1119", "line_number": 1119, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1120", "filename": "Forensics_v1.0.1_processed.txt", "content": "6.2", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1120", "line_number": 1120, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1121", "filename": "Forensics_v1.0.1_processed.txt", "content": "Block-Level Analysis", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1121", "line_number": 1121, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1122", "filename": "Forensics_v1.0.1_processed.txt", "content": "In addition to whole ﬁles, investigators are often interested in discovering known ﬁle remnants,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1122", "line_number": 1122, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1123", "filename": "Forensics_v1.0.1_processed.txt", "content": "such as those produced when a ﬁle is marked as deleted and subsequently partially overwritten.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1123", "line_number": 1123, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1124", "filename": "Forensics_v1.0.1_processed.txt", "content": "One routinely used method to address this problem is to increase the granularity of the hashes", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1124", "line_number": 1124, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1125", "filename": "Forensics_v1.0.1_processed.txt", "content": "by splitting the ﬁles into ﬁxed-size blocks and storing the hash for each individual block. The", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1125", "line_number": 1125, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1126", "filename": "Forensics_v1.0.1_processed.txt", "content": "block size is commonly set to 4 KiB to match the minimum allocation unit used by most", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1126", "line_number": 1126, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1127", "filename": "Forensics_v1.0.1_processed.txt", "content": "operating systems’ installations. Given a block-based reference set, a forensic target (RAM", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1127", "line_number": 1127, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1128", "filename": "Forensics_v1.0.1_processed.txt", "content": "capture or disk image) can be treated as a sequence of blocks that can be read block by block,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1128", "line_number": 1128, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1129", "filename": "Forensics_v1.0.1_processed.txt", "content": "hashed and compared to the reference set.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1129", "line_number": 1129, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1130", "filename": "Forensics_v1.0.1_processed.txt", "content": "In this context, we say that a block is distinct, if the probability that its exact content arises", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1130", "line_number": 1130, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1131", "filename": "Forensics_v1.0.1_processed.txt", "content": "by chance more than once is vanishingly small. If we knew for a fact that a speciﬁc block", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1131", "line_number": 1131, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1132", "filename": "Forensics_v1.0.1_processed.txt", "content": "1A discussion on the known vulnerabilities of cryptographic hash functions is outside the scope of this text.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1132", "line_number": 1132, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1133", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 28", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1133", "line_number": 1133, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1134", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 30", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1134", "line_number": 1134, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1135", "filename": "Forensics_v1.0.1_processed.txt", "content": "was unique and speciﬁc to a particular ﬁle, then (in terms of evidentiary value) ﬁnding it on a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1135", "line_number": 1135, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1136", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensic target would be almost the same as ﬁnding the entire ﬁle from which it was derived. In", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1136", "line_number": 1136, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1137", "filename": "Forensics_v1.0.1_processed.txt", "content": "practice, we cannot deﬁnitely know the distinctiveness of every possible data block; therefore,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1137", "line_number": 1137, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1138", "filename": "Forensics_v1.0.1_processed.txt", "content": "we use an approximating assumption based on empirical data:", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1138", "line_number": 1138, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1139", "filename": "Forensics_v1.0.1_processed.txt", "content": "“If a ﬁle is known to have been manufactured using some high-entropy process, and if the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1139", "line_number": 1139, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1140", "filename": "Forensics_v1.0.1_processed.txt", "content": "blocks of that ﬁle are shown to be distinct throughout a large and representative corpus,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1140", "line_number": 1140, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1141", "filename": "Forensics_v1.0.1_processed.txt", "content": "then those blocks can be treated as if they are distinct.” [40] Perhaps the most common", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1141", "line_number": 1141, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1142", "filename": "Forensics_v1.0.1_processed.txt", "content": "transformation that yields high-entropy data is data compression, which is routinely employed", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1142", "line_number": 1142, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1143", "filename": "Forensics_v1.0.1_processed.txt", "content": "in many common ﬁle formats, such as audio/video and ofﬁce documents.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1143", "line_number": 1143, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1144", "filename": "Forensics_v1.0.1_processed.txt", "content": "Apart from the direct use of blocks as trace evidence for the (past or current) presence of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1144", "line_number": 1144, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1145", "filename": "Forensics_v1.0.1_processed.txt", "content": "known ﬁles, block hashes can be used to improve ﬁle carving results by excluding every known", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1145", "line_number": 1145, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1146", "filename": "Forensics_v1.0.1_processed.txt", "content": "blocks before performing the carving process. This can improve results by reducing gaps and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1146", "line_number": 1146, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1147", "filename": "Forensics_v1.0.1_processed.txt", "content": "eliminating certain classes of false positive results.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1147", "line_number": 1147, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1148", "filename": "Forensics_v1.0.1_processed.txt", "content": "6.3", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1148", "line_number": 1148, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1149", "filename": "Forensics_v1.0.1_processed.txt", "content": "approximate matching", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1149", "line_number": 1149, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1150", "filename": "Forensics_v1.0.1_processed.txt", "content": "A natural generalisation of the problem of ﬁnding identical data objects is to ﬁnd similar ones.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1150", "line_number": 1150, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1151", "filename": "Forensics_v1.0.1_processed.txt", "content": "In the context of digital forensics, the accepted umbrella term for similarity-based techniques", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1151", "line_number": 1151, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1152", "filename": "Forensics_v1.0.1_processed.txt", "content": "is Approximate Matching (AM). As per NIST’s deﬁnition, ‘approximate matching is a generic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1152", "line_number": 1152, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1153", "filename": "Forensics_v1.0.1_processed.txt", "content": "term describing any technique designed to identify similarities between two digital artifacts’.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1153", "line_number": 1153, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1154", "filename": "Forensics_v1.0.1_processed.txt", "content": "[41]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1154", "line_number": 1154, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1155", "filename": "Forensics_v1.0.1_processed.txt", "content": "This broad term encompasses methods that can work at different levels of abstraction.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1155", "line_number": 1155, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1156", "filename": "Forensics_v1.0.1_processed.txt", "content": "At the lowest level, artifacts can be treated as bit strings; at the highest levels, similarity", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1156", "line_number": 1156, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1157", "filename": "Forensics_v1.0.1_processed.txt", "content": "techniques could employ, for example, natural language processing and image recognition", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1157", "line_number": 1157, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1158", "filename": "Forensics_v1.0.1_processed.txt", "content": "methods to provide a level of reasoning that is much closer to that of a human analyst.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1158", "line_number": 1158, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1159", "filename": "Forensics_v1.0.1_processed.txt", "content": "With regard to the whole spectrum of similarity methods, lower-level ones are more generic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1159", "line_number": 1159, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1160", "filename": "Forensics_v1.0.1_processed.txt", "content": "and computationally affordable, whereas higher-level ones tend to be more specialised and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1160", "line_number": 1160, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1161", "filename": "Forensics_v1.0.1_processed.txt", "content": "require considerably more computational resources. Therefore, we would expect a forensic", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1161", "line_number": 1161, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1162", "filename": "Forensics_v1.0.1_processed.txt", "content": "investigation to customise its use of AM techniques based on the goals of the analysis and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1162", "line_number": 1162, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1163", "filename": "Forensics_v1.0.1_processed.txt", "content": "the target data.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1163", "line_number": 1163, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1164", "filename": "Forensics_v1.0.1_processed.txt", "content": "Use Cases. Using a common information retrieval terminology, it is useful to consider two vari-", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1164", "line_number": 1164, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1165", "filename": "Forensics_v1.0.1_processed.txt", "content": "ations of the similarity detection problem: resemblance and containment [43]. Resemblance", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1165", "line_number": 1165, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1166", "filename": "Forensics_v1.0.1_processed.txt", "content": "queries compare two comparably-sized data objects (peers) and seek to infer how closely", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1166", "line_number": 1166, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1167", "filename": "Forensics_v1.0.1_processed.txt", "content": "related they are. Two common forensic applications include: (a) object similarity detection", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1167", "line_number": 1167, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1168", "filename": "Forensics_v1.0.1_processed.txt", "content": "– correlating artifacts that a person would classify as versions of each other; and (b) cross", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1168", "line_number": 1168, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1169", "filename": "Forensics_v1.0.1_processed.txt", "content": "correlation – correlating objects that share the same components, such as an embedded", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1169", "line_number": 1169, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1170", "filename": "Forensics_v1.0.1_processed.txt", "content": "image.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1170", "line_number": 1170, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1171", "filename": "Forensics_v1.0.1_processed.txt", "content": "In the case of containment, we compare artifacts that have a large disparity in terms of size", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1171", "line_number": 1171, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1172", "filename": "Forensics_v1.0.1_processed.txt", "content": "and seek to establish whether a larger one contains (pieces of) a smaller one. Two common", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1172", "line_number": 1172, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1173", "filename": "Forensics_v1.0.1_processed.txt", "content": "variations are embedded object detection – establishing whether a smaller object (such as an", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1173", "line_number": 1173, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1174", "filename": "Forensics_v1.0.1_processed.txt", "content": "image) is part of a larger one (such as a PDF document), and fragment detection - establishing", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1174", "line_number": 1174, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1175", "filename": "Forensics_v1.0.1_processed.txt", "content": "whether a smaller object is a fragment (such as a network packet or disk block) of a bigger", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1175", "line_number": 1175, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1176", "filename": "Forensics_v1.0.1_processed.txt", "content": "one, such as a ﬁle.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1176", "line_number": 1176, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1177", "filename": "Forensics_v1.0.1_processed.txt", "content": "The difference between resemblance and containment is case-speciﬁc and the same tool", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1177", "line_number": 1177, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1178", "filename": "Forensics_v1.0.1_processed.txt", "content": "may work in both cases. However, it is important for analysts to put the tool results into the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1178", "line_number": 1178, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1179", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 29", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1179", "line_number": 1179, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1180", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 31", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1180", "line_number": 1180, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1181", "filename": "Forensics_v1.0.1_processed.txt", "content": "correct context and to understand the performance envelope of the tools they are using in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1181", "line_number": 1181, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1182", "filename": "Forensics_v1.0.1_processed.txt", "content": "order to correctly interpret the results.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1182", "line_number": 1182, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1183", "filename": "Forensics_v1.0.1_processed.txt", "content": "Deﬁnitions. The notion of similarity is speciﬁc to the particular context in which it is used. An", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1183", "line_number": 1183, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1184", "filename": "Forensics_v1.0.1_processed.txt", "content": "approximate matching algorithm works by deﬁning two essential elements – features and a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1184", "line_number": 1184, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1185", "filename": "Forensics_v1.0.1_processed.txt", "content": "similarity function. Features are the atomic components derived from the artifacts through", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1185", "line_number": 1185, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1186", "filename": "Forensics_v1.0.1_processed.txt", "content": "which the artifacts are compared. Comparing two features yields a binary outcome – zero or", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1186", "line_number": 1186, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1187", "filename": "Forensics_v1.0.1_processed.txt", "content": "one – indicating whether the feature match was successful or not. The set of all the features", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1187", "line_number": 1187, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1188", "filename": "Forensics_v1.0.1_processed.txt", "content": "computed by an algorithm for a given artifact constitutes a feature set. It can be viewed as an", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1188", "line_number": 1188, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1189", "filename": "Forensics_v1.0.1_processed.txt", "content": "approximate representation of the original object for the purposes of matching it with other", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1189", "line_number": 1189, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1190", "filename": "Forensics_v1.0.1_processed.txt", "content": "objects.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1190", "line_number": 1190, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1191", "filename": "Forensics_v1.0.1_processed.txt", "content": "The similarity function maps a pair of feature sets to a similarity range; it is increasingly", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1191", "line_number": 1191, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1192", "filename": "Forensics_v1.0.1_processed.txt", "content": "monotonic with respect to the number of matching features. That is, all else being equal,more", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1192", "line_number": 1192, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1193", "filename": "Forensics_v1.0.1_processed.txt", "content": "feature matches yield a higher similarity score.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1193", "line_number": 1193, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1194", "filename": "Forensics_v1.0.1_processed.txt", "content": "Classes. It is useful to consider three general classes of approximate matching algorithms.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1194", "line_number": 1194, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1195", "filename": "Forensics_v1.0.1_processed.txt", "content": "Bytewise matching considers the objects it compares to a sequence of bytes, and makes no", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1195", "line_number": 1195, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1196", "filename": "Forensics_v1.0.1_processed.txt", "content": "effort to parse or interpret them. Consequently, the features extracted from the artifact are", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1196", "line_number": 1196, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1197", "filename": "Forensics_v1.0.1_processed.txt", "content": "also byte sequences, and these methods can be applied to any data blob. The utility of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1197", "line_number": 1197, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1198", "filename": "Forensics_v1.0.1_processed.txt", "content": "result depends heavily on the encoding of the data. If small changes to the content of the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1198", "line_number": 1198, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1199", "filename": "Forensics_v1.0.1_processed.txt", "content": "artifact result in small changes to the serialised format (e.g., plain text), then the bytewise", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1199", "line_number": 1199, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1200", "filename": "Forensics_v1.0.1_processed.txt", "content": "similarity tends to correlate well with a person’s perception of similarity. Conversely, if a small", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1200", "line_number": 1200, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1201", "filename": "Forensics_v1.0.1_processed.txt", "content": "change can trigger large changes in the output (e.g., compressed data), then the correlation", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1201", "line_number": 1201, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1202", "filename": "Forensics_v1.0.1_processed.txt", "content": "would be substantially weaker.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1202", "line_number": 1202, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1203", "filename": "Forensics_v1.0.1_processed.txt", "content": "Syntactic matching relies on parsing the format of an object, potentially using this knowledge", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1203", "line_number": 1203, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1204", "filename": "Forensics_v1.0.1_processed.txt", "content": "to split it into a logical set of features. For example, a zip archive or a PDF document could", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1204", "line_number": 1204, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1205", "filename": "Forensics_v1.0.1_processed.txt", "content": "easily be split into constituent parts without understanding the underlying semantics. The", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1205", "line_number": 1205, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1206", "filename": "Forensics_v1.0.1_processed.txt", "content": "beneﬁt is that this results in a more accurate solution with more precisely interpretable results;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1206", "line_number": 1206, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1207", "filename": "Forensics_v1.0.1_processed.txt", "content": "the downside is that it is a more specialised solution, requiring additional information to parse", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1207", "line_number": 1207, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1208", "filename": "Forensics_v1.0.1_processed.txt", "content": "different data formats.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1208", "line_number": 1208, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1209", "filename": "Forensics_v1.0.1_processed.txt", "content": "Semantic matching (partially) interprets the data content in order to derive semantic features", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1209", "line_number": 1209, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1210", "filename": "Forensics_v1.0.1_processed.txt", "content": "for comparison. Examples include perceptual hashes that can detect visually similar images,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1210", "line_number": 1210, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1211", "filename": "Forensics_v1.0.1_processed.txt", "content": "and methods of information retrieval and natural language processing that can ﬁnd similarities", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1211", "line_number": 1211, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1212", "filename": "Forensics_v1.0.1_processed.txt", "content": "in the subject and content of text documents.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1212", "line_number": 1212, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1213", "filename": "Forensics_v1.0.1_processed.txt", "content": "Researchers use a variety of terms to name the different approximate matching methods they", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1213", "line_number": 1213, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1214", "filename": "Forensics_v1.0.1_processed.txt", "content": "have developed: fuzzy hashing and similarity hashing refer to bytewise approximate matching;", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1214", "line_number": 1214, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1215", "filename": "Forensics_v1.0.1_processed.txt", "content": "perceptual hashing and robust hashing refer to semantic approximate matching techniques.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1215", "line_number": 1215, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1216", "filename": "Forensics_v1.0.1_processed.txt", "content": "Bytewise approximate matching algorithms are the most frequently used AM algorithms in", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1216", "line_number": 1216, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1217", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensics; they follow an overall pattern of extracting a feature set and generating a similarity", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1217", "line_number": 1217, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1218", "filename": "Forensics_v1.0.1_processed.txt", "content": "digest, followed by a comparison of the digests. A similarity digest (a.k.a., ﬁngerprint or", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1218", "line_number": 1218, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1219", "filename": "Forensics_v1.0.1_processed.txt", "content": "signature) is a (compressed) representation of the feature set of the target artifact. It often", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1219", "line_number": 1219, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1220", "filename": "Forensics_v1.0.1_processed.txt", "content": "employs hashing and other techniques to minimise the footprint of the set and to facilitate", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1220", "line_number": 1220, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1221", "filename": "Forensics_v1.0.1_processed.txt", "content": "fast comparison.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1221", "line_number": 1221, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1222", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 30", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1222", "line_number": 1222, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1223", "filename": "Forensics_v1.0.1_processed.txt", "content": "## Page 32", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1223", "line_number": 1223, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1224", "filename": "Forensics_v1.0.1_processed.txt", "content": "6.4", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1224", "line_number": 1224, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1225", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cloud-Native Artifacts", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1225", "line_number": 1225, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1226", "filename": "Forensics_v1.0.1_processed.txt", "content": "Forensic analysis of cloud systems is still in its early stages of development, but it will", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1226", "line_number": 1226, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1227", "filename": "Forensics_v1.0.1_processed.txt", "content": "quickly grow in importance. One new and promising area is the analysis of cloud(-native)", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1227", "line_number": 1227, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1228", "filename": "Forensics_v1.0.1_processed.txt", "content": "artifacts—data objects that maintain the persistent state of web/SaaS applications. [42]", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1228", "line_number": 1228, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1229", "filename": "Forensics_v1.0.1_processed.txt", "content": "Unlike traditional applications, in which the persistent state takes the form of ﬁles in a local", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1229", "line_number": 1229, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1230", "filename": "Forensics_v1.0.1_processed.txt", "content": "ﬁle system, web apps download the necessary state on the ﬂy and do not rely on local storage.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1230", "line_number": 1230, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1231", "filename": "Forensics_v1.0.1_processed.txt", "content": "Recall that a web app’s functionality is split between server and client components, and the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1231", "line_number": 1231, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1232", "filename": "Forensics_v1.0.1_processed.txt", "content": "two communicate via web APIs. From a forensic perspective, the most interesting API calls", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1232", "line_number": 1232, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1233", "filename": "Forensics_v1.0.1_processed.txt", "content": "involve (complete) state transfer; for example, opening a document or loading a prior version,", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1233", "line_number": 1233, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1234", "filename": "Forensics_v1.0.1_processed.txt", "content": "triggers the transfer of its full content. Conceptually, this is analogous to the process of", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1234", "line_number": 1234, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1235", "filename": "Forensics_v1.0.1_processed.txt", "content": "opening and reading the content of a local ﬁle by an application installed on a device. The", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1235", "line_number": 1235, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1236", "filename": "Forensics_v1.0.1_processed.txt", "content": "main difference is that cloud artifacts are internal data structures that, unlike a ﬁle, are not", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1236", "line_number": 1236, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1237", "filename": "Forensics_v1.0.1_processed.txt", "content": "readily available for analysis.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1237", "line_number": 1237, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1238", "filename": "Forensics_v1.0.1_processed.txt", "content": "Cloud artifacts often have a completely different structure from traditional snapshot-centric", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1238", "line_number": 1238, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1239", "filename": "Forensics_v1.0.1_processed.txt", "content": "encoding. For example, internally, Google Docs’ documents are represented as the complete", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1239", "line_number": 1239, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1240", "filename": "Forensics_v1.0.1_processed.txt", "content": "history (log) of every editing action performed on it; given valid credentials, this history is", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1240", "line_number": 1240, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1241", "filename": "Forensics_v1.0.1_processed.txt", "content": "available via Google Docs’ internal API. It is also possible to obtain a snapshot of the artifact", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1241", "line_number": 1241, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1242", "filename": "Forensics_v1.0.1_processed.txt", "content": "of interest in a standard format such as a PDF, via the public API. However, this is inherently", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1242", "line_number": 1242, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1243", "filename": "Forensics_v1.0.1_processed.txt", "content": "forensically deﬁcient in that it ignores potentially critical information on the evolution of a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1243", "line_number": 1243, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1244", "filename": "Forensics_v1.0.1_processed.txt", "content": "document over time.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1244", "line_number": 1244, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1246", "filename": "Forensics_v1.0.1_processed.txt", "content": "CONCLUSION", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1246", "line_number": 1246, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1247", "filename": "Forensics_v1.0.1_processed.txt", "content": "Digital forensics identiﬁes and reconstructs the relevant sequence of events that has led", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1247", "line_number": 1247, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1248", "filename": "Forensics_v1.0.1_processed.txt", "content": "to a currently observable state of a target IT system or (digital) artifacts. The provenance", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1248", "line_number": 1248, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1249", "filename": "Forensics_v1.0.1_processed.txt", "content": "and integrity of the data source and the scientiﬁc grounding of the investigative tools and", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1249", "line_number": 1249, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1250", "filename": "Forensics_v1.0.1_processed.txt", "content": "methods employed are of primary importance in determining their admissibility to a court", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1250", "line_number": 1250, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1251", "filename": "Forensics_v1.0.1_processed.txt", "content": "of law’s proceedings. Digital forensic analysis is applied to both individual digital artifacts", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1251", "line_number": 1251, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1252", "filename": "Forensics_v1.0.1_processed.txt", "content": "such as ﬁles and to complex IT systems comprising multiple components and networked", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1252", "line_number": 1252, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1253", "filename": "Forensics_v1.0.1_processed.txt", "content": "processes.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1253", "line_number": 1253, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1254", "filename": "Forensics_v1.0.1_processed.txt", "content": "Following the rapid cloud-based transition from Software as a Product (SaaP) to Software as a", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1254", "line_number": 1254, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1255", "filename": "Forensics_v1.0.1_processed.txt", "content": "Service (SaaS), forensic methods and tools are also in a respective process of transition. One", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1255", "line_number": 1255, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1256", "filename": "Forensics_v1.0.1_processed.txt", "content": "aspect is a change of emphasis from state-centric analysis, which seeks to deduce events", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1256", "line_number": 1256, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1257", "filename": "Forensics_v1.0.1_processed.txt", "content": "and actions by looking at different snapshots and applying knowledge about the system’s", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1257", "line_number": 1257, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1258", "filename": "Forensics_v1.0.1_processed.txt", "content": "operations, to log-centric analysis, which employs explicitly collected log entries to infer", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1258", "line_number": 1258, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1259", "filename": "Forensics_v1.0.1_processed.txt", "content": "the sequence of relevant (to the inquiry) events. Another aspect is the transition from the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1259", "line_number": 1259, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1260", "filename": "Forensics_v1.0.1_processed.txt", "content": "low-level physical acquisition of storage device images to the high-level logical acquisition", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1260", "line_number": 1260, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1261", "filename": "Forensics_v1.0.1_processed.txt", "content": "of (primarily) application artifacts via well-deﬁned cloud service APIs. Some of the most", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1261", "line_number": 1261, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1262", "filename": "Forensics_v1.0.1_processed.txt", "content": "important emerging questions in digital forensics are the analysis of the large variety of IoT", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1262", "line_number": 1262, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1263", "filename": "Forensics_v1.0.1_processed.txt", "content": "devices, which are forecast to increase in number to as many as 125 billion by 2030, and the", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1263", "line_number": 1263, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1264", "filename": "Forensics_v1.0.1_processed.txt", "content": "employment of machine learning/AI in order to automate and scale up forensic processing.", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1264", "line_number": 1264, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
{"chunk_id": "line-1265", "filename": "Forensics_v1.0.1_processed.txt", "content": "Page 31", "metadata": {"filename": "Forensics_v1.0.1_processed.txt", "chunk_id": "line-1265", "line_number": 1265, "source": "知识库\\output\\Forensics_v1.0.1_processed.txt"}}
