import sqlite3
import pandas as pd
import os





def excel_to_sqlite_legacy(excel_path: str, db_name: str = 'Grammar.db') -> None:

    # 创建数据库连接
    conn = sqlite3.connect(db_name)
    
    try:
        # crawled_data 表结构保持不变（用于存储爬取结果）
        conn.execute('''
        CREATE TABLE IF NOT EXISTS crawled_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            query TEXT NOT NULL CHECK(length(query) > 0),
            title TEXT,
            link TEXT UNIQUE NOT NULL CHECK(link LIKE 'http%'),
            snippet TEXT,
            html_content TEXT,
            extracted_text TEXT,
            crawl_status INTEGER DEFAULT 0 CHECK(crawl_status IN (0,1,2,3,4)),
            crawl_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ''')

        # NewRetrieval 用于存储语法检索的信息,,0表示未检索，1表示已检索且有数据，2表示已检索但无数据
        conn.execute('''
        CREATE TABLE IF NOT EXISTS NewRetrieval (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            search_syntax TEXT,
            use_status INTEGER DEFAULT 0 CHECK(use_status IN (0,1,2))
        );
        ''')

        # 新增 analysis_results 表存储分析结果 0表示未分析，1表示分析成功，2表示分析失败
        conn.execute('''
        CREATE TABLE IF NOT EXISTS analysis_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            crawled_data_id INTEGER NOT NULL,
            analysis_status INTEGER DEFAULT 0 CHECK(analysis_status IN (0,1,2)),
            report_summary TEXT,
            keywords TEXT,
            aliases TEXT,
            organization_type TEXT,

            -- 攻击模式信息
            attack_title TEXT,
            attack_date TEXT,
            attack_source TEXT,
            
            -- 时间线
            first_seen TEXT,
            last_seen TEXT,
            attack_pattern TEXT,
            
            -- TTPs (存储为JSON数组)
            ttps TEXT,
            
            -- 基础设施
            domains TEXT,
            urls TEXT,
            hashes TEXT,
            c2_protocols TEXT,
            fast_flux INTEGER,
            
            -- 攻击者信息
            actors TEXT,
            
            -- 检测指标
            detection_files TEXT,
            detection_registry TEXT,
            detection_network TEXT,
            
            analysis_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (crawled_data_id) REFERENCES crawled_data(id)
        );
        ''')



        # 合并artificial和auto表
        combined_df = pd.DataFrame()
        for sheet in ['artificial', 'auto']:
            df = pd.read_excel(excel_path, sheet_name=sheet)
            combined_df = pd.concat([combined_df, df], ignore_index=True)

        # 按类型分组存储
        for keywords_type, group in combined_df.groupby('Keywords_type'):
            # 原始表名映射规则
            if keywords_type == "group":
                table_name = "gp"
            elif keywords_type == "Malicious Software":
                table_name = "ms"
            else:
                table_name = keywords_type.replace(' ', '_').replace('-', '_')
            
            group.to_sql(table_name, conn, if_exists='replace', index=False)

        # 处理campaign表
        campaign_df = pd.read_excel(excel_path, sheet_name='campaign')
        campaign_df.to_sql('ca', conn, if_exists='replace', index=False)

        # 处理link表
        link_df = pd.read_excel(excel_path, sheet_name='link')
        link_df.to_sql('li', conn, if_exists='replace', index=False)

        print("数据成功导入 SQLite 数据库！")

    except Exception as e:
        print(f"发生错误: {str(e)}")
    finally:
        conn.close()



if __name__ == '__main__':
    current_dir = os.path.dirname(os.path.abspath(__file__))
    excel_path = os.path.join(current_dir, '判断标准.xlsx')
    excel_to_sqlite_legacy(excel_path=excel_path)



