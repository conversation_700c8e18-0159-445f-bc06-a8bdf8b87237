CVE-2003-0001(PUBLISHED):Multiple ethernet Network Interface Card (NIC) device drivers do not pad frames with null bytes, which allows remote attackers to obtain information from previous packets or kernel memory by using malformed packets, as demonstrated by Etherleak.
CVE-2003-0002(PUBLISHED):Cross-site scripting vulnerability (XSS) in ManualLogin.asp script for Microsoft Content Management Server (MCMS) 2001 allows remote attackers to execute arbitrary script via the REASONTXT parameter.
CVE-2003-0003(PUBLISHED):Buffer overflow in the RPC Locator service for Microsoft Windows NT 4.0, Windows NT 4.0 Terminal Server Edition, Windows 2000, and Windows XP allows local users to execute arbitrary code via an RPC call to the service containing certain parameter information.
CVE-2003-0004(PUBLISHED):Buffer overflow in the Windows Redirector function in Microsoft Windows XP allows local users to execute arbitrary code via a long parameter.
CVE-2003-0007(PUBLISHED):Microsoft Outlook 2002 does not properly handle requests to encrypt email messages with V1 Exchange Server Security certificates, which causes Outlook to send the email in plaintext, aka "Flaw in how Outlook 2002 handles V1 Exchange Server Security Certificates could lead to Information Disclosure."
CVE-2003-0009(PUBLISHED):Cross-site scripting (XSS) vulnerability in Help and Support Center for Microsoft Windows Me allows remote attackers to execute arbitrary script in the Local Computer security context via an hcp:// URL with the malicious script in the topic parameter.
CVE-2003-0010(PUBLISHED):Integer overflow in JsArrayFunctionHeapSort function used by Windows Script Engine for JScript (JScript.dll) on various Windows operating system allows remote attackers to execute arbitrary code via a malicious web page or HTML e-mail that uses a large array index value that enables a heap-based buffer overflow attack.
CVE-2003-0011(PUBLISHED):Unknown vulnerability in the DNS intrusion detection application filter for Microsoft Internet Security and Acceleration (ISA) Server 2000 allows remote attackers to cause a denial of service (blocked traffic to DNS servers) via a certain type of incoming DNS request that is not properly handled.
CVE-2003-0012(PUBLISHED):The data collection script for Bugzilla 2.14.x before 2.14.5, 2.16.x before 2.16.2, and 2.17.x before 2.17.3 sets world-writable permissions for the data/mining directory when it runs, which allows local users to modify or delete the data.
CVE-2003-0013(PUBLISHED):The default .htaccess scripts for Bugzilla 2.14.x before 2.14.5, 2.16.x before 2.16.2, and 2.17.x before 2.17.3 do not include filenames for backup copies of the localconfig file that are made from editors such as vi and Emacs, which could allow remote attackers to obtain a database password by directly accessing the backup file.
CVE-2003-0014(PUBLISHED):gsinterf.c in bmv 1.2 and earlier allows local users to overwrite arbitrary files via a symlink attack on temporary files.
CVE-2003-0015(PUBLISHED):Double-free vulnerability in CVS 1.11.4 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via a malformed Directory request, as demonstrated by bypassing write checks to execute Update-prog and Checkin-prog commands.
CVE-2003-0016(PUBLISHED):Apache before 2.0.44, when running on unpatched Windows 9x and Me operating systems, allows remote attackers to cause a denial of service or execute arbitrary code via an HTTP request containing MS-DOS device names.
CVE-2003-0017(PUBLISHED):Apache 2.0 before 2.0.44 on Windows platforms allows remote attackers to obtain certain files via an HTTP request that ends in certain illegal characters such as ">", which causes a different filename to be processed and served.
CVE-2003-0018(PUBLISHED):Linux kernel 2.4.10 through 2.4.21-pre4 does not properly handle the O_DIRECT feature, which allows local attackers with write privileges to read portions of previously deleted files, or cause file system corruption.
CVE-2003-0019(PUBLISHED):uml_net in the kernel-utils package for Red Hat Linux 8.0 has incorrect setuid root privileges, which allows local users to modify network interfaces, e.g. by modifying ARP entries or placing interfaces into promiscuous mode.
CVE-2003-0020(PUBLISHED):Apache does not filter terminal escape sequences from its error logs, which could make it easier for attackers to insert those sequences into terminal emulators containing vulnerabilities related to escape sequences.
CVE-2003-0021(PUBLISHED):The "screen dump" feature in Eterm 0.9.1 and earlier allows attackers to overwrite arbitrary files via a certain character escape sequence when it is echoed to a user's terminal, e.g. when the user views a file containing the malicious sequence.
CVE-2003-0022(PUBLISHED):The "screen dump" feature in rxvt 2.7.8 allows attackers to overwrite arbitrary files via a certain character escape sequence when it is echoed to a user's terminal, e.g. when the user views a file containing the malicious sequence.
CVE-2003-0023(PUBLISHED):The menuBar feature in rxvt 2.7.8 allows attackers to modify menu options and execute arbitrary commands via a certain character escape sequence that inserts the commands into the menu.
CVE-2003-0024(PUBLISHED):The menuBar feature in aterm 0.42 allows attackers to modify menu options and execute arbitrary commands via a certain character escape sequence that inserts the commands into the menu.
CVE-2003-0025(PUBLISHED):Multiple SQL injection vulnerabilities in IMP 2.2.8 and earlier allow remote attackers to perform unauthorized database activities and possibly gain privileges via certain database functions such as check_prefs() in db.pgsql, as demonstrated using mailbox.php3.
CVE-2003-0026(PUBLISHED):Multiple stack-based buffer overflows in the error handling routines of the minires library, as used in the NSUPDATE capability for ISC DHCPD 3.0 through 3.0.1RC10, allow remote attackers to execute arbitrary code via a DHCP message containing a long hostname.
CVE-2003-0027(PUBLISHED):Directory traversal vulnerability in Sun Kodak Color Management System (KCMS) library service daemon (kcms_server) allows remote attackers to read arbitrary files via the KCS_OPEN_PROFILE procedure.
CVE-2003-0028(PUBLISHED):Integer overflow in the xdrmem_getbytes() function, and possibly other functions, of XDR (external data representation) libraries derived from SunRPC, including libnsl, libc, glibc, and dietlibc, allows remote attackers to execute arbitrary code via certain integer values in length fields, a different vulnerability than CVE-2002-0391.
CVE-2003-0030(PUBLISHED):Buffer overflows in protegrity.dll of Protegrity Secure.Data Extension Feature (SEF) before ******* allow attackers with SQL access to execute arbitrary code via the extended stored procedures (1) xp_pty_checkusers, (2) xp_pty_insert, or (3) xp_pty_select.
CVE-2003-0031(PUBLISHED):Multiple buffer overflows in libmcrypt before 2.5.5 allow attackers to cause a denial of service (crash).
CVE-2003-0032(PUBLISHED):Memory leak in libmcrypt before 2.5.5 allows attackers to cause a denial of service (memory exhaustion) via a large number of requests to the application, which causes libmcrypt to dynamically load algorithms via libtool.
CVE-2003-0033(PUBLISHED):Buffer overflow in the RPC preprocessor for Snort 1.8 and 1.9.x before 1.9.1 allows remote attackers to execute arbitrary code via fragmented RPC packets.
CVE-2003-0034(PUBLISHED):Buffer overflow in the mtink status monitor, as included in the printer-drivers package in Mandrake Linux, allows local users to execute arbitrary code via a long HOME environment variable.
CVE-2003-0035(PUBLISHED):Buffer overflow in escputil, as included in the printer-drivers package in Mandrake Linux, allows local users to execute arbitrary code via a long printer-name command line argument.
CVE-2003-0036(PUBLISHED):ml85p, as included in the printer-drivers package for Mandrake Linux, allows local users to overwrite arbitrary files via a symlink attack on temporary files with predictable filenames of the form "mlg85p%d".
CVE-2003-0037(PUBLISHED):Buffer overflows in noffle news server 1.0.1 and earlier allow remote attackers to cause a denial of service (segmentation fault) and possibly execute arbitrary code.
CVE-2003-0038(PUBLISHED):Cross-site scripting (XSS) vulnerability in options.py for Mailman 2.1 allows remote attackers to inject script or HTML into web pages via the (1) email or (2) language parameters.
CVE-2003-0039(PUBLISHED):ISC dhcrelay (dhcp-relay) 3.0rc9 and earlier, and possibly other versions, allows remote attackers to cause a denial of service (packet storm) via a certain BOOTP packet that is forwarded to a broadcast MAC address, causing an infinite loop that is not restricted by a hop count.
CVE-2003-0040(PUBLISHED):SQL injection vulnerability in the PostgreSQL auth module for courier 0.40 and earlier allows remote attackers to execute SQL code via the user name.
CVE-2003-0041(PUBLISHED):Kerberos FTP client allows remote FTP sites to execute arbitrary code via a pipe (|) character in a filename that is retrieved by the client.
CVE-2003-0042(PUBLISHED):Jakarta Tomcat before 3.3.1a, when used with JDK 1.3.1 or earlier, allows remote attackers to list directories even with an index.html or other file present, or obtain unprocessed source code for a JSP file, via a URL containing a null character.
CVE-2003-0043(PUBLISHED):Jakarta Tomcat before 3.3.1a, when used with JDK 1.3.1 or earlier, uses trusted privileges when processing the web.xml file, which could allow remote attackers to read portions of some files through the web.xml file.
CVE-2003-0044(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in the (1) examples and (2) ROOT web applications for Jakarta Tomcat 3.x through 3.3.1a allow remote attackers to insert arbitrary web script or HTML.
CVE-2003-0045(PUBLISHED):Jakarta Tomcat before 3.3.1a on certain Windows systems may allow remote attackers to cause a denial of service (thread hang and resource consumption) via a request for a JSP page containing an MS-DOS device name, such as aux.jsp.
CVE-2003-0046(PUBLISHED):AbsoluteTelnet SSH2 client does not clear logon credentials from memory, including plaintext passwords, which could allow attackers with access to memory to steal the SSH credentials.
CVE-2003-0047(PUBLISHED):SSH2 clients for VanDyke (1) SecureCRT 4.0.2 and 3.4.7, (2) SecureFX 2.1.2 and 2.0.4, and (3) Entunnel 1.0.2 and earlier, do not clear logon credentials from memory, including plaintext passwords, which could allow attackers with access to memory to steal the SSH credentials.
CVE-2003-0048(PUBLISHED):PuTTY 0.53b and earlier does not clear logon credentials from memory, including plaintext passwords, which could allow attackers with access to memory to steal the SSH credentials.
CVE-2003-0049(PUBLISHED):Apple File Protocol (AFP) in Mac OS X before 10.2.4 allows administrators to log in as other users by using the administrator password.
CVE-2003-0050(PUBLISHED):parse_xml.cgi in Apple Darwin Streaming Administration Server 4.1.2 and QuickTime Streaming Server 4.1.1 allows remote attackers to execute arbitrary code via shell metacharacters.
CVE-2003-0051(PUBLISHED):parse_xml.cgi in Apple Darwin Streaming Administration Server 4.1.2 and QuickTime Streaming Server 4.1.1 allows remote attackers to obtain the physical path of the server's installation path via a NULL file parameter.
CVE-2003-0052(PUBLISHED):parse_xml.cgi in Apple Darwin Streaming Administration Server 4.1.2 and QuickTime Streaming Server 4.1.1 allows remote attackers to list arbitrary directories.
CVE-2003-0053(PUBLISHED):Cross-site scripting (XSS) vulnerability in parse_xml.cgi in Apple Darwin Streaming Administration Server 4.1.2 and QuickTime Streaming Server 4.1.1 allows remote attackers to insert arbitrary script via the filename parameter, which is inserted into an error message.
CVE-2003-0054(PUBLISHED):Apple Darwin Streaming Administration Server 4.1.2 and QuickTime Streaming Server 4.1.1 allows remote attackers to execute certain code via a request to port 7070 with the script in an argument to the rtsp DESCRIBE method, which is inserted into a log file and executed when the log is viewed using a browser.
CVE-2003-0055(PUBLISHED):Buffer overflow in the MP3 broadcasting module of Apple Darwin Streaming Administration Server 4.1.2 and QuickTime Streaming Server 4.1.1 allows remote attackers to execute arbitrary code via a long filename.
CVE-2003-0056(PUBLISHED):Buffer overflow in secure locate (slocate) before 2.7 allows local users to execute arbitrary code via a long (1) -c or (2) -r command line argument.
CVE-2003-0057(PUBLISHED):Multiple buffer overflows in Hypermail 2 before 2.1.6 allows remote attackers to cause a denial of service and possibly execute arbitrary code (1) via a long attachment filename that is not properly handled by the hypermail executable, or (2) by connecting to the mail CGI program from an IP address that reverse-resolves to a long hostname.
CVE-2003-0058(PUBLISHED):MIT Kerberos V5 Key Distribution Center (KDC) before 1.2.5 allows remote authenticated attackers to cause a denial of service (crash) on KDCs within the same realm via a certain protocol request that causes a null dereference.
CVE-2003-0059(PUBLISHED):Unknown vulnerability in the chk_trans.c of the libkrb5 library for MIT Kerberos V5 before 1.2.5 allows users from one realm to impersonate users in other realms that have the same inter-realm keys.
CVE-2003-0060(PUBLISHED):Format string vulnerabilities in the logging routines for MIT Kerberos V5 Key Distribution Center (KDC) before 1.2.5 allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via format string specifiers in Kerberos principal names.
CVE-2003-0061(PUBLISHED):Buffer overflow in passwd for HP UX B.10.20 allows local users to execute arbitrary commands with root privileges via a long LANG environment variable.
CVE-2003-0062(PUBLISHED):Buffer overflow in Eset Software NOD32 for UNIX before 1.013 allows local users to execute arbitrary code via a long path name.
CVE-2003-0063(PUBLISHED):The xterm terminal emulator in XFree86 4.2.0 and earlier allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0064(PUBLISHED):The dtterm terminal emulator allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0065(PUBLISHED):The uxterm terminal emulator allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0066(PUBLISHED):The rxvt terminal emulator 2.7.8 and earlier allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0067(PUBLISHED):The aterm terminal emulator 0.42 allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0068(PUBLISHED):The Eterm terminal emulator 0.9.1 and earlier allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0069(PUBLISHED):The PuTTY terminal emulator 0.53 allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0070(PUBLISHED):VTE, as used by default in gnome-terminal terminal emulator 2.2 and as an option in gnome-terminal 2.0, allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0071(PUBLISHED):The DEC UDK processing feature in the xterm terminal emulator in XFree86 4.2.99.4 and earlier allows attackers to cause a denial of service via a certain character escape sequence that causes the terminal to enter a tight loop.
CVE-2003-0072(PUBLISHED):The Key Distribution Center (KDC) in Kerberos 5 (krb5) 1.2.7 and earlier allows remote, authenticated attackers to cause a denial of service (crash) on KDCs within the same realm using a certain protocol request that causes an out-of-bounds read of an array (aka "array overrun").
CVE-2003-0073(PUBLISHED):Double-free vulnerability in mysqld for MySQL before 3.23.55 allows attackers with MySQL access to cause a denial of service (crash) via mysql_change_user.
CVE-2003-0074(PUBLISHED):Format string vulnerability in mpmain.c for plpnfsd of the plptools package allows remote attackers to execute arbitrary code via the functions (1) debuglog, (2) errorlog, and (3) infolog.
CVE-2003-0075(PUBLISHED):Integer signedness error in the myFseek function of samplein.c for Blade encoder (BladeEnc) 0.94.2 and earlier allows remote attackers to execute arbitrary code via a negative offset value following a "fmt" wave chunk.
CVE-2003-0076(PUBLISHED):Unknown vulnerability in the directory parser for Direct Connect 4 Linux (dcgui) before 0.2.2 allows remote attackers to read files outside the sharelist.
CVE-2003-0077(PUBLISHED):The hanterm (hanterm-xf) terminal emulator 2.0.5 and earlier, and possibly later versions, allows attackers to modify the window title via a certain character escape sequence and then insert it back to the command line in the user's terminal, e.g. when the user views a file containing the malicious sequence, which could allow the attacker to execute arbitrary commands.
CVE-2003-0078(PUBLISHED):ssl3_get_record in s3_pkt.c for OpenSSL before 0.9.7a and 0.9.6 before 0.9.6i does not perform a MAC computation if an incorrect block cipher padding is used, which causes an information leak (timing discrepancy) that may make it easier to launch cryptographic attacks that rely on distinguishing between padding and MAC verification errors, possibly leading to extraction of the original plaintext, aka the "Vaudenay timing attack."
CVE-2003-0079(PUBLISHED):The DEC UDK processing feature in the hanterm (hanterm-xf) terminal emulator before 2.0.5 allows attackers to cause a denial of service via a certain character escape sequence that causes the terminal to enter a tight loop.
CVE-2003-0080(PUBLISHED):The iptables ruleset in Gnome-lokkit in Red Hat Linux 8.0 does not include any rules in the FORWARD chain, which could allow attackers to bypass intended access restrictions if packet forwarding is enabled.
CVE-2003-0081(PUBLISHED):Format string vulnerability in packet-socks.c of the SOCKS dissector for Ethereal 0.8.7 through 0.9.9 allows remote attackers to execute arbitrary code via SOCKS packets containing format string specifiers.
CVE-2003-0082(PUBLISHED):The Key Distribution Center (KDC) in Kerberos 5 (krb5) 1.2.7 and earlier allows remote, authenticated attackers to cause a denial of service (crash) on KDCs within the same realm using a certain protocol request that causes the KDC to corrupt its heap (aka "buffer underrun").
CVE-2003-0083(PUBLISHED):Apache 1.3 before 1.3.25 and Apache 2.0 before version 2.0.46 does not filter terminal escape sequences from its access logs, which could make it easier for attackers to insert those sequences into terminal emulators containing vulnerabilities related to escape sequences, a different vulnerability than CVE-2003-0020.
CVE-2003-0084(PUBLISHED):mod_auth_any package in Red Hat Enterprise Linux 2.1 and other operating systems does not properly escape arguments when calling other programs, which allows attackers to execute arbitrary commands via shell metacharacters.
CVE-2003-0085(PUBLISHED):Buffer overflow in the SMB/CIFS packet fragment re-assembly code for SMB daemon (smbd) in Samba before 2.2.8, and Samba-TNG before 0.3.1, allows remote attackers to execute arbitrary code.
CVE-2003-0086(PUBLISHED):The code for writing reg files in Samba before 2.2.8 allows local users to overwrite arbitrary files via a race condition involving chown.
CVE-2003-0087(PUBLISHED):Buffer overflow in libIM library (libIM.a) for National Language Support (NLS) on AIX 4.3 through 5.2 allows local users to gain privileges via several possible attack vectors, including a long -im argument to aixterm.
CVE-2003-0088(PUBLISHED):TruBlueEnvironment for MacOS 10.2.3 and earlier allows local users to overwrite or create arbitrary files and gain root privileges by setting a certain environment variable that is used to write debugging information.
CVE-2003-0089(PUBLISHED):Buffer overflow in the Software Distributor utilities for HP-UX B.11.00 and B.11.11 allows local users to execute arbitrary code via a long LANG environment variable to setuid programs such as (1) swinstall and (2) swmodify.
CVE-2003-0091(PUBLISHED):Stack-based buffer overflow in the bsd_queue() function for lpq on Solaris 2.6 and 7 allows local users to gain root privilege.
CVE-2003-0092(PUBLISHED):Heap-based buffer overflow in dtsession for Solaris 2.5.1 through Solaris 9 allows local users to gain root privileges via a long HOME environment variable.
CVE-2003-0093(PUBLISHED):The RADIUS decoder in tcpdump 3.6.2 and earlier allows remote attackers to cause a denial of service (crash) via an invalid RADIUS packet with a header length field of 0, which causes tcpdump to generate data within an infinite loop.
CVE-2003-0094(PUBLISHED):A patch for mcookie in the util-linux package for Mandrake Linux 8.2 and 9.0 uses /dev/urandom instead of /dev/random, which causes mcookie to use an entropy source that is more predictable than expected, which may make it easier for certain types of attacks to succeed.
CVE-2003-0095(PUBLISHED):Buffer overflow in ORACLE.EXE for Oracle Database Server 9i, 8i, 8.1.7, and 8.0.6 allows remote attackers to execute arbitrary code via a long username that is provided during login, as exploitable through client applications that perform their own authentication, as demonstrated using LOADPSP.
CVE-2003-0096(PUBLISHED):Multiple buffer overflows in Oracle 9i Database release 2, Release 1, 8i, 8.1.7, and 8.0.6 allow remote attackers to execute arbitrary code via (1) a long conversion string argument to the TO_TIMESTAMP_TZ function, (2) a long time zone argument to the TZ_OFFSET function, or (3) a long DIRECTORY parameter to the BFILENAME function.
CVE-2003-0097(PUBLISHED):Unknown vulnerability in CGI module for PHP 4.3.0 allows attackers to access arbitrary files as the PHP user, and possibly execute PHP code, by bypassing the CGI force redirect settings (cgi.force_redirect or --enable-force-cgi-redirect).
CVE-2003-0098(PUBLISHED):Unknown vulnerability in apcupsd before 3.8.6, and 3.10.x before 3.10.5, allows remote attackers to gain root privileges, possibly via format strings in a request to a slave server.
CVE-2003-0099(PUBLISHED):Multiple buffer overflows in apcupsd before 3.8.6, and 3.10.x before 3.10.5, may allow attackers to cause a denial of service or execute arbitrary code, related to usage of the vsprintf function.
CVE-2003-0100(PUBLISHED):Buffer overflow in Cisco IOS 11.2.x to 12.0.x allows remote attackers to cause a denial of service and possibly execute commands via a large number of OSPF neighbor announcements.
CVE-2003-0101(PUBLISHED):miniserv.pl in (1) Webmin before 1.070 and (2) Usermin before 1.000 does not properly handle metacharacters such as line feeds and carriage returns (CRLF) in Base-64 encoded strings during Basic authentication, which allows remote attackers to spoof a session ID and gain root privileges.
CVE-2003-0102(PUBLISHED):Buffer overflow in tryelf() in readelf.c of the file command allows attackers to execute arbitrary code as the user running file, possibly via a large entity size value in an ELF header (elfhdr.e_shentsize).
CVE-2003-0103(PUBLISHED):Format string vulnerability in Nokia 6210 handset allows remote attackers to cause a denial of service (crash, lockup, or restart) via a Multi-Part vCard with fields containing a large number of format string specifiers.
CVE-2003-0104(PUBLISHED):Directory traversal vulnerability in PeopleTools 8.10 through 8.18, 8.40, and 8.41 allows remote attackers to overwrite arbitrary files via the SchedulerTransfer servlet.
CVE-2003-0105(PUBLISHED):ServerMask 2.2 and earlier does not obfuscate (1) ETag, (2) HTTP Status Message, or (3) Allow HTTP responses, which could tell remote attackers that the web server is an IIS server.
CVE-2003-0106(PUBLISHED):The HTTP proxy for Symantec Enterprise Firewall (SEF) 7.0 allows proxy users to bypass pattern matching for blocked URLs via requests that are URL-encoded with escapes, Unicode, or UTF-8.
CVE-2003-0107(PUBLISHED):Buffer overflow in the gzprintf function in zlib 1.1.4, when zlib is compiled without vsnprintf or when long inputs are truncated using vsnprintf, allows attackers to cause a denial of service or possibly execute arbitrary code.
CVE-2003-0108(PUBLISHED):isakmp_sub_print in tcpdump 3.6 through 3.7.1 allows remote attackers to cause a denial of service (CPU consumption) via a certain malformed ISAKMP packet to UDP port 500, which causes tcpdump to enter an infinite loop.
CVE-2003-0109(PUBLISHED):Buffer overflow in ntdll.dll on Microsoft Windows NT 4.0, Windows NT 4.0 Terminal Server Edition, Windows 2000, and Windows XP allows remote attackers to execute arbitrary code, as demonstrated via a WebDAV request to IIS 5.0.
CVE-2003-0110(PUBLISHED):The Winsock Proxy service in Microsoft Proxy Server 2.0 and the Microsoft Firewall service in Internet Security and Acceleration (ISA) Server 2000 allow remote attackers to cause a denial of service (CPU consumption or packet storm) via a spoofed, malformed packet to UDP port 1745.
CVE-2003-0111(PUBLISHED):The ByteCode Verifier component of Microsoft Virtual Machine (VM) build 5.0.3809 and earlier, as used in Windows and Internet Explorer, allows remote attackers to bypass security checks and execute arbitrary code via a malicious Java applet, aka "Flaw in Microsoft VM Could Enable System Compromise."
CVE-2003-0112(PUBLISHED):Buffer overflow in Windows Kernel allows local users to gain privileges by causing certain error messages to be passed to a debugger.
CVE-2003-0113(PUBLISHED):Buffer overflow in URLMON.DLL in Microsoft Internet Explorer 5.01, 5.5 and 6.0 allows remote attackers to execute arbitrary code via an HTTP response containing long values in (1) Content-type and (2) Content-encoding fields.
CVE-2003-0114(PUBLISHED):The file upload control in Microsoft Internet Explorer 5.01, 5.5, and 6.0 allows remote attackers to automatically upload files from the local system via a web page containing a script to upload the files.
CVE-2003-0115(PUBLISHED):Microsoft Internet Explorer 5.01, 5.5 and 6.0 does not properly check parameters that are passed during third party rendering, which could allow remote attackers to execute arbitrary web script, aka the "Third Party Plugin Rendering" vulnerability, a different vulnerability than CVE-2003-0233.
CVE-2003-0116(PUBLISHED):Microsoft Internet Explorer 5.01, 5.5 and 6.0 does not properly check the Cascading Style Sheet input parameter for Modal dialogs, which allows remote attackers to read files on the local system via a web page containing script that creates a dialog and then accesses the target files, aka "Modal Dialog script execution."
CVE-2003-0117(PUBLISHED):Buffer overflow in the HTTP receiver function (BizTalkHTTPReceive.dll ISAPI) of Microsoft BizTalk Server 2002 allows attackers to execute arbitrary code via a certain request to the HTTP receiver.
CVE-2003-0118(PUBLISHED):SQL injection vulnerability in the Document Tracking and Administration (DTA) website of Microsoft BizTalk Server 2000 and 2002 allows remote attackers to execute operating system commands via a request to (1) rawdocdata.asp or (2) RawCustomSearchField.asp containing an embedded SQL statement.
CVE-2003-0119(PUBLISHED):The secldapclntd daemon in AIX 4.3, 5.1 and 5.2 uses an Internet socket when communicating with the loadmodule, which allows remote attackers to directly connect to the daemon and conduct unauthorized activities.
CVE-2003-0120(PUBLISHED):adb2mhc in the mhc-utils package before 0.25+20010625-7.1 allows local users to overwrite arbitrary files via a symlink attack on a default temporary directory with a predictable name.
CVE-2003-0121(PUBLISHED):Clearswift MAILsweeper 4.x allows remote attackers to bypass attachment detection via an attachment that does not specify a MIME-Version header field, which is processed by some mail clients.
CVE-2003-0122(PUBLISHED):Buffer overflow in Notes server before Lotus Notes R4, R5 before 5.0.11, and early R6 allows remote attackers to execute arbitrary code via a long distinguished name (DN) during NotesRPC authentication and an outer field length that is less than that of the DN field.
CVE-2003-0123(PUBLISHED):Buffer overflow in Web Retriever client for Lotus Notes/Domino R4.5 through R6 allows remote malicious web servers to cause a denial of service (crash) via a long HTTP status line.
CVE-2003-0124(PUBLISHED):man before 1.5l allows attackers to execute arbitrary code via a malformed man file with improper quotes, which causes the my_xsprintf function to return a string with the value "unsafe," which is then executed as a program via a system call if it is in the search path of the user who runs man.
CVE-2003-0125(PUBLISHED):Buffer overflow in the web interface for SOHO Routefinder 550 before firmware 4.63 allows remote attackers to cause a denial of service (reboot) and execute arbitrary code via a long GET /OPTIONS value.
CVE-2003-0126(PUBLISHED):The web interface for SOHO Routefinder 550 firmware 4.63 and earlier, and possibly later versions, has a default "admin" account with a blank password, which could allow attackers on the LAN side to conduct unauthorized activities.
CVE-2003-0127(PUBLISHED):The kernel module loader in Linux kernel 2.2.x before 2.2.25, and 2.4.x before 2.4.21, allows local users to gain root privileges by using ptrace to attach to a child process that is spawned by the kernel.
CVE-2003-0128(PUBLISHED):The try_uudecoding function in mail-format.c for Ximian Evolution Mail User Agent 1.2.2 and earlier allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a malicious uuencoded (UUE) header, possibly triggering a heap-based buffer overflow.
CVE-2003-0129(PUBLISHED):Ximian Evolution Mail User Agent 1.2.2 and earlier allows remote attackers to cause a denial of service (memory consumption) via a mail message that is uuencoded multiple times.
CVE-2003-0130(PUBLISHED):The handle_image function in mail-format.c for Ximian Evolution Mail User Agent 1.2.2 and earlier does not properly escape HTML characters, which allows remote attackers to inject arbitrary data and HTML via a MIME Content-ID header in a MIME-encoded image.
CVE-2003-0131(PUBLISHED):The SSL and TLS components for OpenSSL 0.9.6i and earlier, 0.9.7, and 0.9.7a allow remote attackers to perform an unauthorized RSA private key operation via a modified Bleichenbacher attack that uses a large number of SSL or TLS connections using PKCS #1 v1.5 padding that cause OpenSSL to leak information regarding the relationship between ciphertext and the associated plaintext, aka the "Klima-Pokorny-Rosa attack."
CVE-2003-0132(PUBLISHED):A memory leak in Apache 2.0 through 2.0.44 allows remote attackers to cause a denial of service (memory consumption) via large chunks of linefeed characters, which causes Apache to allocate 80 bytes for each linefeed.
CVE-2003-0133(PUBLISHED):GtkHTML, as included in Evolution before 1.2.4, allows remote attackers to cause a denial of service (crash) via certain malformed messages.
CVE-2003-0134(PUBLISHED):Unknown vulnerability in filestat.c for Apache running on OS2, versions 2.0 through 2.0.45, allows unknown attackers to cause a denial of service via requests related to device names.
CVE-2003-0135(PUBLISHED):vsftpd FTP daemon in Red Hat Linux 9 is not compiled against TCP wrappers (tcp_wrappers) but is installed as a standalone service, which inadvertently prevents vsftpd from restricting access as intended.
CVE-2003-0136(PUBLISHED):psbanner in the LPRng package allows local users to overwrite arbitrary files via a symbolic link attack on the /tmp/before file.
CVE-2003-0137(PUBLISHED):SNMP daemon in the DX200 based network element for Nokia Serving GPRS support node (SGSN) allows remote attackers to read SNMP options via arbitrary community strings.
CVE-2003-0138(PUBLISHED):Version 4 of the Kerberos protocol (krb4), as used in Heimdal and other packages, allows an attacker to impersonate any principal in a realm via a chosen-plaintext attack.
CVE-2003-0139(PUBLISHED):Certain weaknesses in the implementation of version 4 of the Kerberos protocol (krb4) in the krb5 distribution, when triple-DES keys are used to key krb4 services, allow an attacker to create krb4 tickets for unauthorized principals using a cut-and-paste attack and "ticket splicing."
CVE-2003-0140(PUBLISHED):Buffer overflow in Mutt 1.4.0 and possibly earlier versions, 1.5.x up to 1.5.3, and other programs that use Mutt code such as Balsa before 2.0.10, allows a remote malicious IMAP server to cause a denial of service (crash) and possibly execute arbitrary code via a crafted folder.
CVE-2003-0141(PUBLISHED):The PNG deflate algorithm in RealOne Player 6.0.11.x and earlier, RealPlayer 8/RealPlayer Plus 8 6.0.9.584, and other versions allows remote attackers to corrupt the heap and overwrite arbitrary memory via a PNG graphic file format containing compressed data using fixed trees that contain the length values 286-287, which are treated as a very large length.
CVE-2003-0142(PUBLISHED):Adobe Acrobat Reader (acroread) 6, under certain circumstances when running with the "Certified plug-ins only" option disabled, loads plug-ins with signatures used for older versions of Acrobat, which can allow attackers to cause Acrobat to enter Certified mode and run untrusted plugins by modifying the CTIsCertifiedMode function.
CVE-2003-0143(PUBLISHED):The pop_msg function in qpopper 4.0.x before 4.0.5fc2 does not null terminate a message buffer after a call to Qvsnprintf, which could allow authenticated users to execute arbitrary code via a buffer overflow in a mdef command with a long macro name.
CVE-2003-0144(PUBLISHED):Buffer overflow in the lprm command in the lprold lpr package on SuSE 7.1 through 7.3, OpenBSD 3.2 and earlier, and possibly other operating systems, allows local users to gain root privileges via long command line arguments such as (1) request ID or (2) user name.
CVE-2003-0145(PUBLISHED):Unknown vulnerability in tcpdump before 3.7.2 related to an inability to "Handle unknown RADIUS attributes properly," allows remote attackers to cause a denial of service (infinite loop), a different vulnerability than CAN-2003-0093.
CVE-2003-0146(PUBLISHED):Multiple vulnerabilities in NetPBM 9.20 and earlier, and possibly other versions, may allow remote attackers to cause a denial of service or execute arbitrary code via "maths overflow errors" such as (1) integer signedness errors or (2) integer overflows, which lead to buffer overflows.
CVE-2003-0147(PUBLISHED):OpenSSL does not use RSA blinding by default, which allows local and remote attackers to obtain the server's private key by determining factors using timing differences on (1) the number of extra reductions during Montgomery reduction, and (2) the use of different integer multiplication algorithms ("Karatsuba" and normal).
CVE-2003-0148(PUBLISHED):The default installation of MSDE via McAfee ePolicy Orchestrator 2.0 through 3.0 allows attackers to execute arbitrary code via a series of steps that (1) obtain the database administrator username and encrypted password in a configuration file from the ePO server using a certain request, (2) crack the password due to weak cryptography, and (3) use the password to pass commands through xp_cmdshell.
CVE-2003-0149(PUBLISHED):Heap-based buffer overflow in ePO agent for McAfee ePolicy Orchestrator 2.0, 2.5, and 2.5.1 allows remote attackers to execute arbitrary code via a POST request containing long parameters.
CVE-2003-0150(PUBLISHED):MySQL 3.23.55 and earlier creates world-writeable files and allows mysql users to gain root privileges by using the "SELECT * INFO OUTFILE" operator to overwrite a configuration file and cause mysql to run as root upon restart, as demonstrated by modifying my.cnf.
CVE-2003-0151(PUBLISHED):BEA WebLogic Server and Express 6.0 through 7.0 does not properly restrict access to certain internal servlets that perform administrative functions, which allows remote attackers to read arbitrary files or execute arbitrary code.
CVE-2003-0152(PUBLISHED):Unknown vulnerability in bonsai Mozilla CVS query tool allows remote attackers to execute arbitrary commands as the www-data user.
CVE-2003-0153(PUBLISHED):bonsai Mozilla CVS query tool leaks the absolute pathname of the tool in certain error messages generated by (1) cvslog.cgi, (2) cvsview2.cgi, or (3) multidiff.cgi.
CVE-2003-0154(PUBLISHED):Cross-site scripting vulnerabilities (XSS) in bonsai Mozilla CVS query tool allow remote attackers to execute arbitrary web script via (1) the file, root, or rev parameters to cvslog.cgi, (2) the file or root parameters to cvsblame.cgi, (3) various parameters to cvsquery.cgi, (4) the person parameter to showcheckins.cgi, (5) the module parameter to cvsqueryform.cgi, and (6) possibly other attack vectors as identified by Mozilla bug #146244.
CVE-2003-0155(PUBLISHED):bonsai Mozilla CVS query tool allows remote attackers to gain access to the parameters page without authentication.
CVE-2003-0156(PUBLISHED):Directory traversal vulnerability in Cross-Referencing Linux (LXR) allows remote attackers to read arbitrary files via .. (dot dot) sequences in the v parameter.
CVE-2003-0159(PUBLISHED):Heap-based buffer overflow in the NTLMSSP code for Ethereal 0.9.9 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code.
CVE-2003-0160(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in SquirrelMail before 1.2.11 allow remote attackers to inject arbitrary HTML code and steal information from a client's web browser.
CVE-2003-0161(PUBLISHED):The prescan() function in the address parser (parseaddr.c) in Sendmail before 8.12.9 does not properly handle certain conversions from char and int types, which can cause a length check to be disabled when Sendmail misinterprets an input value as a special "NOCHAR" control value, allowing attackers to cause a denial of service and possibly execute arbitrary code via a buffer overflow attack using messages, a different vulnerability than CVE-2002-1337.
CVE-2003-0162(PUBLISHED):Ecartis 1.0.0 (formerly listar) before snapshot 20030227 allows remote attackers to reset passwords of other users and gain privileges by modifying hidden form fields in the HTML page.
CVE-2003-0163(PUBLISHED):decrypt_msg for the Gaim-Encryption GAIM plugin 1.15 and earlier does not properly validate a message length parameter, which allows remote attackers to cause a denial of service (crash) via a negative length, which overwrites arbitrary heap memory with a zero byte.
CVE-2003-0165(PUBLISHED):Format string vulnerability in Eye Of Gnome (EOG) allows attackers to execute arbitrary code via format string specifiers in a command line argument for the file to display.
CVE-2003-0166(PUBLISHED):Integer signedness error in emalloc() function for PHP before 4.3.2 allow remote attackers to cause a denial of service (memory consumption) and possibly execute arbitrary code via negative arguments to functions such as (1) socket_recv, (2) socket_recvfrom, and possibly other functions.
CVE-2003-0167(PUBLISHED):Multiple off-by-one buffer overflows in the IMAP capability for Mutt 1.3.28 and earlier, and Balsa 1.2.4 and earlier, allow a remote malicious IMAP server to cause a denial of service (crash) and possibly execute arbitrary code via a specially crafted mail folder, a different vulnerability than CVE-2003-0140.
CVE-2003-0168(PUBLISHED):Buffer overflow in Apple QuickTime Player 5.x and 6.0 for Windows allows remote attackers to execute arbitrary code via a long QuickTime URL.
CVE-2003-0169(PUBLISHED):hpnst.exe in the GoAhead-Webs webserver for HP Instant TopTools before 5.55 allows remote attackers to cause a denial of service (CPU consumption) via a request to hpnst.exe that calls itself, which causes an infinite loop.
CVE-2003-0170(PUBLISHED):Unknown vulnerability in ftpd in IBM AIX 5.2, when configured to use Kerberos 5 for authentication, allows remote attackers to gain privileges via unknown attack vectors.
CVE-2003-0171(PUBLISHED):DirectoryServices in MacOS X trusts the PATH environment variable to locate and execute the touch command, which allows local users to execute arbitrary commands by modifying the PATH to point to a directory containing a malicious touch program.
CVE-2003-0172(PUBLISHED):Buffer overflow in openlog function for PHP 4.3.1 on Windows operating system, and possibly other OSes, allows remote attackers to cause a crash and possibly execute arbitrary code via a long filename argument.
CVE-2003-0173(PUBLISHED):xfsdq in xfsdump does not create quota information files securely, which allows local users to gain root privileges.
CVE-2003-0174(PUBLISHED):The LDAP name service (nsd) in IRIX 6.5.19 and earlier does not properly verify if the USERPASSWORD attribute has been provided by an LDAP server, which could allow attackers to log in without a password.
CVE-2003-0175(PUBLISHED):SGI IRIX before 6.5.21 allows local users to cause a denial of service (kernel panic) via a certain call to the PIOCSWATCH ioctl.
CVE-2003-0176(PUBLISHED):The Name Service Daemon (nsd), when running on an NIS master on SGI IRIX 6.5.x through 6.5.20f, and possibly earlier versions, allows remote attackers to cause a denial of service (crash) via a UDP port scan.
CVE-2003-0177(PUBLISHED):SGI IRIX 6.5.x through 6.5.20f, and possibly earlier versions, does not follow "-" entries in the /etc/group file, which may cause subsequent group membership entries to be processed inadvertently.
CVE-2003-0178(PUBLISHED):Multiple buffer overflows in Lotus Domino Web Server before 6.0.1 allow remote attackers to cause a denial of service or execute arbitrary code via (1) the s_ViewName option in the PresetFields parameter for iNotes, (2) the Foldername option in the PresetFields parameter for iNotes, or (3) a long Host header, which is inserted into a long Location header and used during a redirect operation.
CVE-2003-0179(PUBLISHED):Buffer overflow in the COM Object Control Handler for Lotus Domino 6.0.1 and earlier allows remote attackers to execute arbitrary code via multiple attack vectors, as demonstrated using the InitializeUsingNotesUserName method in the iNotes ActiveX control.
CVE-2003-0180(PUBLISHED):Lotus Domino Web Server (nhttp.exe) before 6.0.1 allows remote attackers to cause a denial of service via an incomplete POST request, as demonstrated using the h_PageUI form.
CVE-2003-0181(PUBLISHED):Lotus Domino Web Server (nhttp.exe) before 6.0.1 allows remote attackers to cause a denial of service via a "Fictionary Value Field POST request" as demonstrated using the s_Validation form with a long, unknown parameter name.
CVE-2003-0187(PUBLISHED):The connection tracking core of Netfilter for Linux 2.4.20, with CONFIG_IP_NF_CONNTRACK enabled or the ip_conntrack module loaded, allows remote attackers to cause a denial of service (resource consumption) due to an inconsistency with Linux 2.4.20's support of linked lists, which causes Netfilter to fail to identify connections with an UNCONFIRMED status and use large timeouts.
CVE-2003-0188(PUBLISHED):lv reads a .lv file from the current working directory, which allows local users to execute arbitrary commands as other lv users by placing malicious .lv files into other directories.
CVE-2003-0189(PUBLISHED):The authentication module for Apache 2.0.40 through 2.0.45 on Unix does not properly handle threads safely when using the crypt_r or crypt functions, which allows remote attackers to cause a denial of service (failed Basic authentication with valid usernames and passwords) when a threaded MPM is used.
CVE-2003-0190(PUBLISHED):OpenSSH-portable (OpenSSH) 3.6.1p1 and earlier with PAM support enabled immediately sends an error message when a user does not exist, which allows remote attackers to determine valid usernames via a timing attack.
CVE-2003-0192(PUBLISHED):Apache 2 before 2.0.47, and certain versions of mod_ssl for Apache 1.3, do not properly handle "certain sequences of per-directory renegotiations and the SSLCipherSuite directive being used to upgrade from a weak ciphersuite to a strong one," which could cause Apache to use the weak ciphersuite.
CVE-2003-0193(PUBLISHED):msxlsview.sh in xlsview for catdoc 0.91 and earlier allows local users to overwrite arbitrary files via a symlink attack on predictable temporary file names ("word$$.html").
CVE-2003-0194(PUBLISHED):tcpdump does not properly drop privileges to the pcap user when starting up.
CVE-2003-0195(PUBLISHED):CUPS before 1.1.19 allows remote attackers to cause a denial of service via a partial printing request to the IPP port (631), which does not time out.
CVE-2003-0196(PUBLISHED):Multiple buffer overflows in Samba before 2.2.8a may allow remote attackers to execute arbitrary code or cause a denial of service, as discovered by the Samba team and a different vulnerability than CVE-2003-0201.
CVE-2003-0197(PUBLISHED):Buffer overflow gds_lock_mgr of Interbase Database 6.x allows local users to gain privileges via a long ISC_LOCK_ENV environment variable (INTERBASE_LOCK).
CVE-2003-0198(PUBLISHED):Mac OS X before 10.2.5 allows guest users to modify the permissions of the DropBox folder and read unauthorized files.
CVE-2003-0201(PUBLISHED):Buffer overflow in the call_trans2open function in trans2.c for Samba 2.2.x before 2.2.8a, 2.0.10 and earlier 2.0.x versions, and Samba-TNG before 0.3.2, allows remote attackers to execute arbitrary code.
CVE-2003-0202(PUBLISHED):The (1) halstead and (2) gather_stats scripts in metrics 1.0 allow local users to overwrite arbitrary files via a symlink attack on temporary files.
CVE-2003-0203(PUBLISHED):Buffer overflow in moxftp 2.2 and earlier allows remote malicious FTP servers to execute arbitrary code via a long FTP banner.
CVE-2003-0204(PUBLISHED):KDE 2 and KDE 3.1.1 and earlier 3.x versions allows attackers to execute arbitrary commands via (1) PostScript (PS) or (2) PDF files, related to missing -dPARANOIDSAFER and -dSAFER arguments when using the kghostview Ghostscript viewer.
CVE-2003-0205(PUBLISHED):gkrellm-newsticker gkrellm plugin before 0.3-3.1 allows remote attackers to execute arbitrary commands via shell metacharacters in the ticker title of a URI.
CVE-2003-0206(PUBLISHED):gkrellm-newsticker gkrellm plugin before 0.3-3.1 allows remote attackers to cause a denial of service (crash) via (1) link or (2) title elements that contain multiple lines.
CVE-2003-0207(PUBLISHED):ps2epsi creates insecure temporary files when calling ghostscript, which allows local attackers to overwrite arbitrary files.
CVE-2003-0208(PUBLISHED):Cross-site scripting (XSS) vulnerability in Macromedia Flash ad user tracking capability allows remote attackers to insert arbitrary Javascript via the clickTAG field.
CVE-2003-0209(PUBLISHED):Integer overflow in the TCP stream reassembly module (stream4) for Snort 2.0 and earlier allows remote attackers to execute arbitrary code via large sequence numbers in packets, which enable a heap-based buffer overflow.
CVE-2003-0210(PUBLISHED):Buffer overflow in the administration service (CSAdmin) for Cisco Secure ACS before 3.1.2 allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long user parameter to port 2002.
CVE-2003-0211(PUBLISHED):Memory leak in xinetd 2.3.10 allows remote attackers to cause a denial of service (memory consumption) via a large number of rejected connections.
CVE-2003-0212(PUBLISHED):handleAccept in rinetd before 0.62 does not properly resize the connection list when it becomes full and sets an array index incorrectly, which allows remote attackers to cause a denial of service and possibly execute arbitrary code via a large number of connections.
CVE-2003-0213(PUBLISHED):ctrlpacket.c in PoPToP PPTP server before 1.1.4-b3 allows remote attackers to cause a denial of service via a length field of 0 or 1, which causes a negative value to be fed into a read operation, leading to a buffer overflow.
CVE-2003-0214(PUBLISHED):run-mailcap in mime-support 3.22 and earlier allows local users to overwrite arbitrary files via a symlink attack on temporary files.
CVE-2003-0215(PUBLISHED):SQL injection vulnerability in bttlxeForum 2.0 beta 3 and earlier allows remote attackers to bypass authentication via the (1) username and (2) password fields, and possibly other fields.
CVE-2003-0216(PUBLISHED):Unknown vulnerability in Cisco Catalyst 7.5(1) allows local users to bypass authentication and gain access to the enable mode without a password.
CVE-2003-0217(PUBLISHED):Cross-site scripting (XSS) vulnerability in Neoteris Instant Virtual Extranet (IVE) 3.01 and earlier allows remote attackers to insert arbitrary web script and bypass authentication via a certain CGI script.
CVE-2003-0218(PUBLISHED):Buffer overflow in PostMethod() function for Monkey HTTP Daemon (monkeyd) 0.6.1 and earlier allows remote attackers to execute arbitrary code via a POST request with a large body.
CVE-2003-0219(PUBLISHED):Kerio Personal Firewall (KPF) 2.1.4 and earlier allows remote attackers to execute administrator commands by sniffing packets from a valid session and replaying them against the remote administration server.
CVE-2003-0220(PUBLISHED):Buffer overflow in the administrator authentication process for Kerio Personal Firewall (KPF) 2.1.4 and earlier allows remote attackers to execute arbitrary code via a handshake packet.
CVE-2003-0221(PUBLISHED):The (1) dupatch and (2) setld utilities in HP Tru64 UNIX 5.1B PK1 and earlier allows local users to overwrite files and possibly gain root privileges via a symlink attack.
CVE-2003-0222(PUBLISHED):Stack-based buffer overflow in Oracle Net Services for Oracle Database Server 9i release 2 and earlier allows attackers to execute arbitrary code via a "CREATE DATABASE LINK" query containing a connect string with a long USING parameter.
CVE-2003-0223(PUBLISHED):Cross-site scripting vulnerability (XSS) in the ASP function responsible for redirection in Microsoft Internet Information Server (IIS) 4.0, 5.0, and 5.1 allows remote attackers to embed a URL containing script in a redirection message.
CVE-2003-0224(PUBLISHED):Buffer overflow in ssinc.dll for Microsoft Internet Information Services (IIS) 5.0 allows local users to execute arbitrary code via a web page with a Server Side Include (SSI) directive with a long filename, aka "Server Side Include Web Pages Buffer Overrun."
CVE-2003-0225(PUBLISHED):The ASP function Response.AddHeader in Microsoft Internet Information Server (IIS) 4.0 and 5.0 does not limit memory requests when constructing headers, which allow remote attackers to generate a large header to cause a denial of service (memory consumption) with an ASP page.
CVE-2003-0226(PUBLISHED):Microsoft Internet Information Services (IIS) 5.0 and 5.1 allows remote attackers to cause a denial of service via a long WebDAV request with a (1) PROPFIND or (2) SEARCH method, which generates an error condition that is not properly handled.
CVE-2003-0227(PUBLISHED):The logging capability for unicast and multicast transmissions in the ISAPI extension for Microsoft Windows Media Services in Microsoft Windows NT 4.0 and 2000, nsiislog.dll, allows remote attackers to cause a denial of service in Internet Information Server (IIS) and execute arbitrary code via a certain network request.
CVE-2003-0228(PUBLISHED):Directory traversal vulnerability in Microsoft Windows Media Player 7.1 and Windows Media Player for Windows XP allows remote attackers to execute arbitrary code via a skins file with a URL containing hex-encoded backslash characters (%5C) that causes an executable to be placed in an arbitrary location.
CVE-2003-0230(PUBLISHED):Microsoft SQL Server 7, 2000, and MSDE allows local users to gain privileges by hijacking a named pipe during the authentication of another user, aka the "Named Pipe Hijacking" vulnerability.
CVE-2003-0231(PUBLISHED):Microsoft SQL Server 7, 2000, and MSDE allows local or remote authenticated users to cause a denial of service (crash or hang) via a long request to a named pipe.
CVE-2003-0232(PUBLISHED):Microsoft SQL Server 7, 2000, and MSDE allows local users to execute arbitrary code via a certain request to the Local Procedure Calls (LPC) port that leads to a buffer overflow.
CVE-2003-0233(PUBLISHED):Heap-based buffer overflow in plugin.ocx for Internet Explorer 5.01, 5.5 and 6.0 allows remote attackers to execute arbitrary code via the Load() method, a different vulnerability than CVE-2003-0115.
CVE-2003-0235(PUBLISHED):Format string vulnerability in POP3 client for Mirabilis ICQ Pro 2003a allows remote malicious servers to execute arbitrary code via format strings in the response to a UIDL command.
CVE-2003-0236(PUBLISHED):Integer signedness errors in the POP3 client for Mirabilis ICQ Pro 2003a allow remote attackers to execute arbitrary code via the (1) Subject or (2) Date headers.
CVE-2003-0237(PUBLISHED):The "ICQ Features on Demand" functionality for Mirabilis ICQ Pro 2003a does not properly verify the authenticity of software upgrades, which allows remote attackers to install arbitrary software via a spoofing attack.
CVE-2003-0238(PUBLISHED):The Message Session window in Mirabilis ICQ Pro 2003a allows remote attackers to cause a denial of service (CPU consumption) by spoofing the address of an ADS server and sending HTML with a -1 width in a table tag.
CVE-2003-0239(PUBLISHED):icqateimg32.dll parsing/rendering library in Mirabilis ICQ Pro 2003a allows remote attackers to cause a denial of service via malformed GIF89a headers that do not contain a GCT (Global Color Table) or an LCT (Local Color Table) after an Image Descriptor.
CVE-2003-0240(PUBLISHED):The web-based administration capability for various Axis Network Camera products allows remote attackers to bypass access restrictions and modify configuration via an HTTP request to the admin/admin.shtml containing a leading // (double slash).
CVE-2003-0241(PUBLISHED):FrontRange GoldMine mail agent 5.70 and 6.00 before 30503 directly sends HTML to the default browser without setting its security zone or otherwise labeling it untrusted, which allows remote attackers to execute arbitrary code via a message that is rendered in IE using a less secure zone.
CVE-2003-0242(PUBLISHED):IPSec in Mac OS X before 10.2.6 does not properly handle certain incoming security policies that match by port, which could allow traffic that is not explicitly allowed by the policies.
CVE-2003-0243(PUBLISHED):Happycgi.com Happymall 4.3 and 4.4 allows remote attackers to execute arbitrary commands via shell metacharacters in the file parameter for the (1) normal_html.cgi or (2) member_html.cgi scripts.
CVE-2003-0244(PUBLISHED):The route cache implementation in Linux 2.4, and the Netfilter IP conntrack module, allows remote attackers to cause a denial of service (CPU consumption) via packets with forged source addresses that cause a large number of hash table collisions.
CVE-2003-0245(PUBLISHED):Vulnerability in the apr_psprintf function in the Apache Portable Runtime (APR) library for Apache 2.0.37 through 2.0.45 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via long strings, as demonstrated using XML objects to mod_dav, and possibly other vectors.
CVE-2003-0246(PUBLISHED):The ioperm system call in Linux kernel 2.4.20 and earlier does not properly restrict privileges, which allows local users to gain read or write access to certain I/O ports.
CVE-2003-0247(PUBLISHED):Unknown vulnerability in the TTY layer of the Linux kernel 2.4 allows attackers to cause a denial of service ("kernel oops").
CVE-2003-0248(PUBLISHED):The mxcsr code in Linux kernel 2.4 allows attackers to modify CPU state registers via a malformed address.
CVE-2003-0249(PUBLISHED):PHP treats unknown methods such as "PoSt" as a GET request, which could allow attackers to intended access restrictions if PHP is running on a server that passes on all methods, such as Apache httpd 2.0, as demonstrated using a Limit directive.  NOTE: this issue has been disputed by the Apache security team, saying "It is by design that PHP allows scripts to process any request method.  A script which does not explicitly verify the request method will hence be processed as normal for arbitrary methods.  It is therefore expected behaviour that one cannot implement per-method access control using the Apache configuration alone, which is the assumption made in this report.
CVE-2003-0251(PUBLISHED):ypserv NIS server before 2.7 allows remote attackers to cause a denial of service via a TCP client request that does not respond to the server, which causes ypserv to block.
CVE-2003-0252(PUBLISHED):Off-by-one error in the xlog function of mountd in the Linux NFS utils package (nfs-utils) before 1.0.4 allows remote attackers to cause a denial of service and possibly execute arbitrary code via certain RPC requests to mountd that do not contain newlines.
CVE-2003-0253(PUBLISHED):The prefork MPM in Apache 2 before 2.0.47 does not properly handle certain errors from accept, which could lead to a denial of service.
CVE-2003-0254(PUBLISHED):Apache 2 before 2.0.47, when running on an IPv6 host, allows attackers to cause a denial of service (CPU consumption by infinite loop) when the FTP proxy server fails to create an IPv6 socket.
CVE-2003-0255(PUBLISHED):The key validation code in GnuPG before 1.2.2 does not properly determine the validity of keys with multiple user IDs and assigns the greatest validity of the most valid user ID, which prevents GnuPG from warning the encrypting user when a user ID does not have a trusted path.
CVE-2003-0256(PUBLISHED):The GnuPG plugin in kopete before 0.6.2 does not properly cleanse the command line when executing gpg, which allows remote attackers to execute arbitrary commands.
CVE-2003-0257(PUBLISHED):Format string vulnerability in the printer capability for IBM AIX .3, 5.1, and 5.2 allows local users to gain printq or root privileges.
CVE-2003-0258(PUBLISHED):Cisco VPN 3000 series concentrators and Cisco VPN 3002 Hardware Client 3.5.x through 4.0.REL, when enabling IPSec over TCP for a port on the concentrator, allow remote attackers to reach the private network without authentication.
CVE-2003-0259(PUBLISHED):Cisco VPN 3000 series concentrators and Cisco VPN 3002 Hardware Client 2.x.x through 3.6.7 allows remote attackers to cause a denial of service (reload) via a malformed SSH initialization packet.
CVE-2003-0260(PUBLISHED):Cisco VPN 3000 series concentrators and Cisco VPN 3002 Hardware Client 2.x.x through 3.6.7A allow remote attackers to cause a denial of service (slowdown and possibly reload) via a flood of malformed ICMP packets.
CVE-2003-0261(PUBLISHED):fuzz 0.6 and earlier creates temporary files insecurely, which could allow local users to gain root privileges.
CVE-2003-0262(PUBLISHED):leksbot 1.2.3 in Debian GNU/Linux installs the KATAXWR as setuid root, which allows local users to gain root privileges by exploiting unknown vulnerabilities related to the escalated privileges, which KATAXWR is not designed to have.
CVE-2003-0263(PUBLISHED):Multiple buffer overflows in Floosietek FTGate Pro Mail Server (FTGatePro) 1.22 allow remote attackers to execute arbitrary code via long (1) MAIL FROM or (2) RCPT TO commands.
CVE-2003-0264(PUBLISHED):Multiple buffer overflows in SLMail 5.1.0.4420 allows remote attackers to execute arbitrary code via (1) a long EHLO argument to slmail.exe, (2) a long XTRN argument to slmail.exe, (3) a long string to POPPASSWD, or (4) a long password to the POP3 server.
CVE-2003-0265(PUBLISHED):Race condition in SDBINST for SAP database ******** creates critical files with world-writable permissions before initializing the setuid bits, which allows local attackers to gain root privileges by modifying the files before the permissions are changed.
CVE-2003-0266(PUBLISHED):Multiple buffer overflows in SLWebMail 3 on Windows systems allows remote attackers to cause a denial of service and possibly execute arbitrary code via (1) a long Language parameter to showlogin.dll, (2) a long CompanyID parameter to recman.dll, (3) a long CompanyID parameter to admin.dll, or (4) a long CompanyID parameter to globallogin.dll.
CVE-2003-0267(PUBLISHED):ShowGodLog.dll in SLWebMail 3 on Windows systems allows remote attackers to read arbitrary files by directly calling ShowGodLog.dll with an argument specifying the full path of the target file.
CVE-2003-0268(PUBLISHED):SLWebMail 3 on Windows systems allows remote attackers to identify the full path of the server via invalid requests to DLLs such as WebMailReq.dll, which reveals the path in an error message.
CVE-2003-0269(PUBLISHED):Buffer overflow in youbin allows local users to gain privileges via a long HOME environment variable.
CVE-2003-0270(PUBLISHED):The administration capability for Apple AirPort 802.11 wireless access point devices uses weak encryption (XOR with a fixed key) for protecting authentication credentials, which could allow remote attackers to obtain administrative access via sniffing when the capability is available via Ethernet or non-WEP connections.
CVE-2003-0271(PUBLISHED):Buffer overflow in Personal FTP Server allows remote attackers to execute arbitrary code via a long USER argument.
CVE-2003-0272(PUBLISHED):admin.php in miniPortail allows remote attackers to gain administrative privileges by setting the miniPortailAdmin cookie to an "adminok" value.
CVE-2003-0273(PUBLISHED):Cross-site scripting (XSS) vulnerability in the web interface for Request Tracker (RT) 1.0 through 1.0.7 allows remote attackers to execute script via message bodies.
CVE-2003-0274(PUBLISHED):Buffer overflow in catmail for ListProc 8.2.09 and earlier allows remote attackers to execute arbitrary code via a long ULISTPROC_UMASK value.
CVE-2003-0275(PUBLISHED):SSI.php in YaBB SE 1.5.2 allows remote attackers to execute arbitrary PHP code by modifying the sourcedir parameter to reference a URL on a remote web server that contains the code.
CVE-2003-0276(PUBLISHED):Buffer overflow in Pi3Web 2.0.1 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a GET request with a large number of / characters.
CVE-2003-0277(PUBLISHED):Directory traversal vulnerability in normal_html.cgi in Happycgi.com Happymall 4.3 and 4.4 allows remote attackers to read arbitrary files via .. (dot dot) sequences in the file parameter.
CVE-2003-0278(PUBLISHED):Cross-site scripting (XSS) vulnerability in normal_html.cgi in Happycgi.com Happymall 4.3 and 4.4 allows remote attackers to insert arbitrary web script via the file parameter.
CVE-2003-0279(PUBLISHED):Multiple SQL injection vulnerabilities in the Web_Links module for PHP-Nuke 5.x through 6.5 allows remote attackers to steal sensitive information via numeric fields, as demonstrated using (1) the viewlink function and cid parameter, or (2) index.php.
CVE-2003-0280(PUBLISHED):Multiple buffer overflows in the SMTP Service for ESMTP CMailServer 4.0.2003.03.27 allow remote attackers to execute arbitrary code via long (1) MAIL FROM or (2) RCPT TO commands.
CVE-2003-0281(PUBLISHED):Buffer overflow in Firebird 1.0.2 and other versions before 1.5, and possibly other products that use the InterBase codebase, allows local users to execute arbitrary code via a long INTERBASE environment variable when calling (1) gds_inet_server, (2) gds_lock_mgr, or (3) gds_drop.
CVE-2003-0282(PUBLISHED):Directory traversal vulnerability in UnZip 5.50 allows attackers to overwrite arbitrary files via invalid characters between two . (dot) characters, which are filtered and result in a ".." sequence.
CVE-2003-0283(PUBLISHED):Cross-site scripting (XSS) vulnerability in Phorum before 3.4.3 allows remote attackers to inject arbitrary web script and HTML tags via a message with a "<<" before a tag name in the (1) subject, (2) author's name, or (3) author's e-mail.
CVE-2003-0284(PUBLISHED):Adobe Acrobat 5 does not properly validate JavaScript in PDF files, which allows remote attackers to write arbitrary files into the Plug-ins folder that spread to other PDF documents, as demonstrated by the W32.Yourde virus.
CVE-2003-0285(PUBLISHED):IBM AIX 5.2 and earlier distributes Sendmail with a configuration file (sendmail.cf) with the (1) promiscuous_relay, (2) accept_unresolvable_domains, and (3) accept_unqualified_senders features enabled, which allows Sendmail to be used as an open mail relay for sending spam e-mail.
CVE-2003-0286(PUBLISHED):SQL injection vulnerability in register.asp in Snitz Forums 2000 before 3.4.03, and possibly 3.4.07 and earlier, allows remote attackers to execute arbitrary stored procedures via the Email variable.
CVE-2003-0287(PUBLISHED):Cross-site scripting (XSS) vulnerability in Movable Type before 2.6, and possibly other versions including 2.63, allows remote attackers to insert arbitrary web script or HTML via the Name textbox, possibly when the "Allow HTML in comments?" option is enabled.
CVE-2003-0288(PUBLISHED):Buffer overflow in the file & folder transfer mechanism for IP Messenger for Win 2.00 through 2.02 allows remote attackers to execute arbitrary code via file with a long filename, which triggers the overflow when the user saves the file.
CVE-2003-0289(PUBLISHED):Format string vulnerability in scsiopen.c of the cdrecord program in cdrtools 2.0 allows local users to gain privileges via format string specifiers in the dev parameter.
CVE-2003-0290(PUBLISHED):Memory leak in eServ 2.9x allows remote attackers to cause a denial of service (memory exhaustion) via a large number of connections, whose memory is not freed when the connection is terminated.
CVE-2003-0291(PUBLISHED):3com OfficeConnect Remote 812 ADSL Router 1.1.7 does not properly clear memory from DHCP responses, which allows remote attackers to identify the contents of previous HTTP requests by sniffing DHCP packets.
CVE-2003-0292(PUBLISHED):Cross-site scripting (XSS) vulnerability in Inktomi Traffic-Server 5.5.1 allows remote attackers to insert arbitrary web script or HTML into an error page that appears to come from the domain that the client is visiting, aka "Man-in-the-Middle" XSS.
CVE-2003-0293(PUBLISHED):PalmOS allows remote attackers to cause a denial of service (CPU consumption) via a flood of ICMP echo request (ping) packets.
CVE-2003-0294(PUBLISHED):autohtml.php in php-proxima 6.0 and earlier allows remote attackers to read arbitrary files via the name parameter in a modload operation.
CVE-2003-0295(PUBLISHED):Cross-site scripting (XSS) vulnerability in private.php for vBulletin 3.0.0 Beta 2 allows remote attackers to inject arbitrary web script and HTML via the "Preview Message" capability.
CVE-2003-0296(PUBLISHED):The IMAP Client for Evolution 1.2.4 allows remote malicious IMAP servers to cause a denial of service and possibly execute arbitrary code via certain large literal size values that cause either integer signedness errors or integer overflow errors.
CVE-2003-0297(PUBLISHED):c-client IMAP Client, as used in imap-2002b and Pine 4.53, allows remote malicious IMAP servers to cause a denial of service (crash) and possibly execute arbitrary code via certain large (1) literal and (2) mailbox size values that cause either integer signedness errors or integer overflow errors.
CVE-2003-0298(PUBLISHED):The IMAP Client for Mozilla 1.3 and 1.4a allows remote malicious IMAP servers to cause a denial of service and possibly execute arbitrary code via certain large (1) literal and possibly (2) mailbox size values that cause either integer signedness errors or integer overflow errors.
CVE-2003-0299(PUBLISHED):The IMAP Client, as used in mutt 1.4.1 and Balsa 2.0.10, allows remote malicious IMAP servers to cause a denial of service and possibly execute arbitrary code via certain large mailbox size values that cause either integer signedness errors or integer overflow errors.
CVE-2003-0300(PUBLISHED):The IMAP Client for Sylpheed 0.8.11 allows remote malicious IMAP servers to cause a denial of service (crash) via certain large literal size values that cause either integer signedness errors or integer overflow errors.
CVE-2003-0301(PUBLISHED):The IMAP Client for Outlook Express 6.00.2800.1106 allows remote malicious IMAP servers to cause a denial of service (crash) via certain large literal size values that cause either integer signedness errors or integer overflow errors.
CVE-2003-0302(PUBLISHED):The IMAP Client for Eudora 5.2.1 allows remote malicious IMAP servers to cause a denial of service and possibly execute arbitrary code via certain large literal size values that cause either integer signedness errors or integer overflow errors.
CVE-2003-0303(PUBLISHED):SQL injection vulnerability in one||zero (aka One or Zero) Helpdesk 1.4 rc4 allows remote attackers to modify arbitrary ticket number descriptions via the sg parameter.
CVE-2003-0304(PUBLISHED):one||zero (aka One or Zero) Helpdesk 1.4 rc4 allows remote attackers to create administrator accounts by directly calling the install.php Helpdesk Installation script.
CVE-2003-0305(PUBLISHED):The Service Assurance Agent (SAA) in Cisco IOS 12.0 through 12.2, aka Response Time Reporter (RTR), allows remote attackers to cause a denial of service (crash) via malformed RTR packets to port 1967.
CVE-2003-0306(PUBLISHED):Buffer overflow in EXPLORER.EXE on Windows XP allows attackers to execute arbitrary code as the XP user via a desktop.ini file with a long .ShellClassInfo parameter.
CVE-2003-0307(PUBLISHED):Poster version.two allows remote authenticated users to gain administrative privileges by appending the "|" field separator and an "admin" value into the email address field.
CVE-2003-0308(PUBLISHED):The Sendmail 8.12.3 package in Debian GNU/Linux 3.0 does not securely create temporary files, which could allow local users to gain additional privileges via (1) expn, (2) checksendmail, or (3) doublebounce.pl.
CVE-2003-0309(PUBLISHED):Internet Explorer 5.01, 5.5, and 6.0 allows remote attackers to bypass security zone restrictions and execute arbitrary programs via a web document with a large number of duplicate file:// or other requests that point to the program and open multiple file download dialogs, which eventually cause Internet Explorer to execute the program, as demonstrated using a large number of FRAME or IFRAME tags, aka the "File Download Dialog Vulnerability."
CVE-2003-0310(PUBLISHED):Cross-site scripting (XSS) vulnerability in articleview.php for eZ publish 2.2 allows remote attackers to insert arbitrary web script.
CVE-2003-0312(PUBLISHED):Directory traversal vulnerability in Snowblind Web Server 1.0 allows remote attackers to read arbitrary files via a .. (dot dot) in an HTTP request.
CVE-2003-0313(PUBLISHED):Directory traversal vulnerability in Snowblind Web Server 1.0 allows remote attackers to list arbitrary directory contents via a ... (triple dot) in an HTTP request.
CVE-2003-0314(PUBLISHED):Snowblind Web Server 1.0 allows remote attackers to cause a denial of service (crash) via a URL that ends in a "</" sequence.
CVE-2003-0315(PUBLISHED):Snowblind Web Server 1.0 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a long HTTP request, which may trigger a buffer overflow.
CVE-2003-0316(PUBLISHED):Venturi Client before 2.2, as used in certain Fourelle and Venturi Wireless products, can be used as an open proxy for various protocols, including an open relay for SMTP, which allows it to be abused by spammers.
CVE-2003-0317(PUBLISHED):iisPROTECT 2.1 and 2.2 allows remote attackers to bypass authentication via an HTTP request containing URL-encoded characters.
CVE-2003-0318(PUBLISHED):Cross-site scripting (XSS) vulnerability in the Statistics module for PHP-Nuke 6.0 and earlier allows remote attackers to insert arbitrary web script via the year parameter.
CVE-2003-0319(PUBLISHED):Buffer overflow in the IMAP server (IMAPMax) for SmartMax MailMax ******** and earlier allows remote authenticated users to execute arbitrary code via a long SELECT command.
CVE-2003-0320(PUBLISHED):header.php in ttCMS 2.3 and earlier allows remote attackers to inject arbitrary PHP code by setting the ttcms_user_admin parameter to "1" and modifying the admin_root parameter to point to a URL that contains a Trojan horse header.inc.php script.
CVE-2003-0321(PUBLISHED):Multiple buffer overflows in BitchX IRC client 1.0-0c19 and earlier allow remote malicious IRC servers to cause a denial of service (crash) and possibly execute arbitrary code via long hostnames, nicknames, or channel names, which are not properly handled by the functions (1) send_ctcp, (2) cannot_join_channel, (3) cluster, (4) BX_compress_modes, (5) handle_oper_vision, and (6) ban_it.
CVE-2003-0322(PUBLISHED):Integer overflow in BitchX IRC client 1.0-0c19 and earlier allows remote malicious IRC servers to cause a denial of service (crash).
CVE-2003-0323(PUBLISHED):Multiple buffer overflows in ircII 20020912 allows remote malicious IRC servers to cause a denial of service (crash) and possibly execute arbitrary code via responses that are not properly fed to the my_strcat function by (1) ctcp_buffer, (2) cannot_join_channel, (3) status_make_printable for Statusbar drawing, (4) create_server_list, and possibly other functions.
CVE-2003-0324(PUBLISHED):Buffer overflows in EPIC IRC Client (EPIC4) 1.0.1 allows remote malicious IRC servers to cause a denial of service (crash) and possibly execute arbitrary code via long replies that are not properly handled by the (1) userhost_cmd_returned function, or (2) Statusbar capability.
CVE-2003-0325(PUBLISHED):Buffer overflow in Maelstrom 3.0.6, 3.0.5, and earlier allows local users to execute arbitrary code via a long -server command line argument.
CVE-2003-0326(PUBLISHED):Integer overflow in parse_decode_path() of slocate may allow attackers to execute arbitrary code via a LOCATE_PATH with a large number of ":" (colon) characters, whose count is used in a call to malloc.
CVE-2003-0327(PUBLISHED):Sybase Adaptive Server Enterprise (ASE) 12.5 allows remote attackers to cause a denial of service (hang) via a remote password array with an invalid length, which triggers a heap-based buffer overflow.
CVE-2003-0328(PUBLISHED):EPIC IRC Client (EPIC4) pre2.002, pre2.003, and possibly later versions, allows remote malicious IRC servers to cause a denial of service (crash) and possibly execute arbitrary code via a CTCP request from a large nickname, which causes an incorrect length calculation.
CVE-2003-0329(PUBLISHED):CesarFTP 0.99g stores user names and passwords in plaintext in the settings.ini file, which could allow local users to gain privileges.
CVE-2003-0330(PUBLISHED):Buffer overflow in unknown versions of Maelstrom allows local users to execute arbitrary code via a long -player command line argument.
CVE-2003-0331(PUBLISHED):SQL injection vulnerability in ttForum allows remote attackers to execute arbitrary SQL and gain ttForum Administrator privileges via the Ignorelist-Textfield argument in the Preferences page.
CVE-2003-0332(PUBLISHED):The ISAPI extension in BadBlue 1.7 through 2.2, and possibly earlier versions, modifies the first two letters of a filename extension after performing a security check, which allows remote attackers to bypass authentication via a filename with a .ats extension instead of a .hts extension.
CVE-2003-0333(PUBLISHED):Multiple buffer overflows in kermit in HP-UX 10.20 and 11.00 (C-Kermit 6.0.192 and possibly other versions before 8.0) allow local users to gain privileges via long arguments to (1) ask, (2) askq, (3) define, (4) assign, and (5) getc, some of which may share the same underlying function "doask," a different vulnerability than CVE-2001-0085.
CVE-2003-0334(PUBLISHED):BitchX IRC client 1.0c20cvs and earlier allows attackers to cause a denial of service (core dump) via certain channel mode changes that are not properly handled in names.c.
CVE-2003-0335(PUBLISHED):rc.M in Slackware 9.0 calls quotacheck with the -M option, which causes the filesystem to be remounted and possibly reset security-relevant mount flags such as nosuid, nodev, and noexec.
CVE-2003-0336(PUBLISHED):Qualcomm Eudora 5.2.1 allows remote attackers to read arbitrary files via an email message with a carriage return (CR) character in a spoofed "Attachment Converted:" string, which is not properly handled by Eudora.
CVE-2003-0337(PUBLISHED):The ckconfig command in lsadmin for Load Sharing Facility (LSF) 5.1 allows local users to execute arbitrary programs by modifying the LSF_ENVDIR environment variable to reference an alternate lsf.conf file, then modifying LSF_SERVERDIR to point to a malicious lim program, which lsadmin then executes.
CVE-2003-0338(PUBLISHED):Directory traversal vulnerability in WsMp3 daemon (WsMp3d) 0.0.10 and earlier allows remote attackers to read and execute arbitrary files via .. (dot dot) sequences in HTTP GET or POST requests.
CVE-2003-0339(PUBLISHED):Multiple heap-based buffer overflows in WsMp3 daemon (WsMp3d) 0.0.10 and earlier allow remote attackers to execute arbitrary code via long HTTP requests.
CVE-2003-0340(PUBLISHED):Demarc Puresecure 1.6 stores authentication information for the logging server in plaintext, which allows attackers to steal login names and passwords to gain privileges.
CVE-2003-0341(PUBLISHED):Cross-site scripting (XSS) vulnerability in Owl Intranet Engine 0.71 and earlier allows remote attackers to insert arbitrary script via the Search field.
CVE-2003-0342(PUBLISHED):BlackMoon FTP Server 2.6 Free Edition, and possibly other distributions and versions, stores user names and passwords in plaintext in the blackmoon.mdb file, which can allow local users to gain privileges.
CVE-2003-0343(PUBLISHED):BlackMoon FTP Server 2.6 Free Edition, and possibly other distributions and versions, generates an "Account does not exist" error message when an invalid username is entered, which makes it easier for remote attackers to conduct brute force attacks.
CVE-2003-0344(PUBLISHED):Buffer overflow in Microsoft Internet Explorer 5.01, 5.5, and 6.0 allows remote attackers to execute arbitrary code via / (slash) characters in the Type property of an Object tag in a web page.
CVE-2003-0345(PUBLISHED):Buffer overflow in the SMB capability for Microsoft Windows XP, 2000, and NT allows remote attackers to cause a denial of service and possibly execute arbitrary code via an SMB packet that specifies a smaller buffer length than is required.
CVE-2003-0346(PUBLISHED):Multiple integer overflows in a Microsoft Windows DirectX MIDI library (QUARTZ.DLL) allow remote attackers to execute arbitrary code via a MIDI (.mid) file with (1) large length for a Text or Copyright string, or (2) a large number of tracks, which leads to a heap-based buffer overflow.
CVE-2003-0347(PUBLISHED):Heap-based buffer overflow in VBE.DLL and VBE6.DLL of Microsoft Visual Basic for Applications (VBA) SDK 5.0 through 6.3 allows remote attackers to execute arbitrary code via a document with a long ID parameter.
CVE-2003-0348(PUBLISHED):A certain Microsoft Windows Media Player 9 Series ActiveX control allows remote attackers to view and manipulate the Media Library on the local system via HTML script.
CVE-2003-0349(PUBLISHED):Buffer overflow in the streaming media component for logging multicast requests in the ISAPI for the logging capability of Microsoft Windows Media Services (nsiislog.dll), as installed in IIS 5.0, allows remote attackers to execute arbitrary code via a large POST request to nsiislog.dll.
CVE-2003-0350(PUBLISHED):The control for listing accessibility options in the Accessibility Utility Manager on Windows 2000 (ListView) does not properly handle Windows messages, which allows local users to execute arbitrary code via a "Shatter" style message to the Utility Manager that references a user-controlled callback function.
CVE-2003-0352(PUBLISHED):Buffer overflow in a certain DCOM interface for RPC in Microsoft Windows NT 4.0, 2000, XP, and Server 2003 allows remote attackers to execute arbitrary code via a malformed message, as exploited by the Blaster/MSblast/LovSAN and Nachi/Welchia worms.
CVE-2003-0353(PUBLISHED):Buffer overflow in a component of SQL-DMO for Microsoft Data Access Components (MDAC) 2.5 through 2.7 allows remote attackers to execute arbitrary code via a long response to a broadcast request to UDP port 1434.
CVE-2003-0354(PUBLISHED):Unknown vulnerability in GNU Ghostscript before 7.07 allows attackers to execute arbitrary commands, even when -dSAFER is enabled, via a PostScript file that causes the commands to be executed from a malicious print job.
CVE-2003-0355(PUBLISHED):Safari 1.0 Beta 2 (v73) and earlier does not validate the Common Name (CN) field for X.509 Certificates, which could allow remote attackers to spoof certificates.
CVE-2003-0356(PUBLISHED):Multiple off-by-one vulnerabilities in Ethereal 0.9.11 and earlier allow remote attackers to cause a denial of service and possibly execute arbitrary code via the (1) AIM, (2) GIOP Gryphon, (3) OSPF, (4) PPTP, (5) Quake, (6) Quake2, (7) Quake3, (8) Rsync, (9) SMB, (10) SMPP, and (11) TSP dissectors, which do not properly use the tvb_get_nstringz and tvb_get_nstringz0 functions.
CVE-2003-0357(PUBLISHED):Multiple integer overflow vulnerabilities in Ethereal 0.9.11 and earlier allow remote attackers to cause a denial of service and possibly execute arbitrary code via the (1) Mount and (2) PPP dissectors.
CVE-2003-0358(PUBLISHED):Buffer overflow in (1) nethack 3.4.0 and earlier, and (2) falconseye 1.9.3 and earlier, which is based on nethack, allows local users to gain privileges via a long -s command line option.
CVE-2003-0359(PUBLISHED):nethack 3.4.0 and earlier installs certain setgid binaries with insecure permissions, which allows local users to gain privileges by replacing the original binaries with malicious code.
CVE-2003-0360(PUBLISHED):Multiple buffer overflows in gPS before 1.0.0 allow attackers to cause a denial of service and possibly execute arbitrary code.
CVE-2003-0361(PUBLISHED):gPS before 1.1.0 does not properly follow the rgpsp connection source acceptation policy as specified in the rgpsp.conf file, which could allow unauthorized remote attackers to connect to rgpsp.
CVE-2003-0362(PUBLISHED):Buffer overflow in gPS before 0.10.2 may allow local users to cause a denial of service (SIGSEGV) in rgpsp via long command lines.
CVE-2003-0363(PUBLISHED):Format string vulnerability in LICQ 1.2.6, 1.0.3 and possibly other versions allows remote attackers to perform unknown actions via format string specifiers.
CVE-2003-0364(PUBLISHED):The TCP/IP fragment reassembly handling in the Linux kernel 2.4 allows remote attackers to cause a denial of service (CPU consumption) via certain packets that cause a large number of hash table collisions.
CVE-2003-0365(PUBLISHED):ICQLite 2003a creates the ICQ Lite directory with an ACE for "Full Control" privileges for Interactive Users, which allows local users to gain privileges as other users by replacing the executables with malicious programs.
CVE-2003-0366(PUBLISHED):lyskom-server 2.0.7 and earlier allows unauthenticated users to cause a denial of service (CPU consumption) via a large query.
CVE-2003-0367(PUBLISHED):znew in the gzip package allows local users to overwrite arbitrary files via a symlink attack on temporary files.
CVE-2003-0368(PUBLISHED):Nokia Gateway GPRS support node (GGSN) allows remote attackers to cause a denial of service (kernel panic) via a malformed IP packet with a 0xFF TCP option.
CVE-2003-0370(PUBLISHED):Konqueror Embedded and KDE 2.2.2 and earlier does not validate the Common Name (CN) field for X.509 Certificates, which could allow remote attackers to spoof certificates via a man-in-the-middle attack.
CVE-2003-0371(PUBLISHED):Buffer overflow in Prishtina FTP client 1.x allows remote FTP servers to cause a denial of service (crash) and possibly execute arbitrary code via a long FTP banner.
CVE-2003-0372(PUBLISHED):Signed integer vulnerability in libnasl in Nessus before 2.0.6 allows local users with plugin upload privileges to cause a denial of service (core dump) and possibly execute arbitrary code by causing a negative argument to be provided to the insstr function as used in a NASL script.
CVE-2003-0373(PUBLISHED):Multiple buffer overflows in libnasl in Nessus before 2.0.6 allow local users with plugin upload privileges to cause a denial of service (core dump) and possibly execute arbitrary code via (1) a long proto argument to the scanner_add_port function, (2) a long user argument to the ftp_log_in function, (3) a long pass argument to the ftp_log_in function.
CVE-2003-0374(PUBLISHED):Multiple unknown vulnerabilities in Nessus before 2.0.6, in libnessus and possibly libnasl, a different set of vulnerabilities than those identified by CVE-2003-0372 and CVE-2003-0373, aka "similar issues in other nasl functions as well as in libnessus."
CVE-2003-0375(PUBLISHED):Cross-site scripting (XSS) vulnerability in member.php of XMBforum XMB 1.8.x (aka Partagium) allows remote attackers to insert arbitrary HTML and web script via the "member" parameter.
CVE-2003-0376(PUBLISHED):Buffer overflow in Eudora 5.2.1 allows remote attackers to cause a denial of service (crash and failed restart) and possibly execute arbitrary code via an Attachment Converted argument with a large number of . (dot) characters.
CVE-2003-0377(PUBLISHED):SQL injection vulnerability in the web-based administration interface for iisPROTECT 2.2-r4, and possibly earlier versions, allows remote attackers to insert arbitrary SQL and execute code via certain variables, as demonstrated using the GroupName variable in SiteAdmin.ASP.
CVE-2003-0378(PUBLISHED):The Kerberos login authentication feature in Mac OS X, when used with an LDAPv3 server and LDAP bind authentication, may send cleartext passwords to the LDAP server when the AuthenticationAuthority attribute is not set.
CVE-2003-0379(PUBLISHED):Unknown vulnerability in Apple File Service (AFP Server) for Mac OS X Server, when sharing files on a UFS or re-shared NFS volume, allows remote attackers to overwrite arbitrary files.
CVE-2003-0380(PUBLISHED):Buffer overflow in atftp daemon (atftpd) 0.6.1 and earlier, and possibly later versions, allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a long filename.
CVE-2003-0381(PUBLISHED):Multiple vulnerabilities in noweb 2.9 and earlier creates temporary files insecurely, which allows local users to overwrite arbitrary files via multiple vectors including the noroff script.
CVE-2003-0382(PUBLISHED):Buffer overflow in Eterm 0.9.2 allows local users to gain privileges via a long ETERMPATH environment variable.
CVE-2003-0385(PUBLISHED):Buffer overflow in xaos 3.0-23 and earlier, when running setuid, allows local users to gain root privileges via a long -language option.
CVE-2003-0386(PUBLISHED):OpenSSH 3.6.1 and earlier, when restricting host access by numeric IP addresses and with VerifyReverseMapping disabled, allows remote attackers to bypass "from=" and "user@host" address restrictions by connecting to a host from a system whose reverse DNS hostname contains the numeric IP address.
CVE-2003-0388(PUBLISHED):pam_wheel in Linux-PAM 0.78, with the trust option enabled and the use_uid option disabled, allows local users to spoof log entries and gain privileges by causing getlogin() to return a spoofed user name.
CVE-2003-0389(PUBLISHED):Cross-site scripting (XSS) vulnerability in the secure redirect function of RSA ACE/Agent 5.0 for Windows, and 5.x for Web, allows remote attackers to insert arbitrary web script and possibly cause users to enter a passphrase via a GET request containing the script.
CVE-2003-0390(PUBLISHED):Multiple buffer overflows in Options Parsing Tool (OPT) shared library 3.18 and earlier, when used in setuid programs, may allow local users to execute arbitrary code via long command line options that are fed into macros such as opt_warn_2, as used in functions such as opt_atoi.
CVE-2003-0391(PUBLISHED):Format string vulnerability in Magic WinMail Server 2.3, and possibly other 2.x versions, allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via format string specifiers in the PASS command.
CVE-2003-0392(PUBLISHED):Directory traversal vulnerability in ST FTP Service 3.0 allows remote attackers to list arbitrary directories via a CD command with a DoS drive letter argument (e.g. E:).
CVE-2003-0393(PUBLISHED):Privacyware Privatefirewall 3.0 does not block certain incoming packets when in "Filter Internet Traffic" or Deny Internet Traffic" modes, which allows remote attackers to identify running services via FIN scans or Xmas scans.
CVE-2003-0394(PUBLISHED):objects.inc.php4 in BLNews 2.1.3 allows remote attackers to execute arbitrary PHP code via a Server[path] parameter that points to malicious code on an attacker-controlled web site.
CVE-2003-0395(PUBLISHED):Ultimate PHP Board (UPB) 1.9 allows remote attackers to execute arbitrary PHP code with UPB administrator privileges via an HTTP request containing the code in the User-Agent header, which is executed when the administrator executes admin_iplog.php.
CVE-2003-0396(PUBLISHED):Buffer overflow in les for ATM on Linux (linux-atm) before 2.4.1, if used setuid, allows local users to gain privileges via a long -f command line argument.
CVE-2003-0397(PUBLISHED):Buffer overflow in FastTrack (FT) network code, as used in Kazaa 2.0.2 and possibly other versions and products, allows remote attackers to execute arbitrary code via a packet containing a large list of supernodes, aka "Packet 0' death."
CVE-2003-0398(PUBLISHED):Vignette StoryServer 4 and 5, and Vignette V/5 and V/6, with the SSI EXEC feature enabled, allows remote attackers to execute arbitrary code via a text variable to a Vignette Application that is later displayed.
CVE-2003-0399(PUBLISHED):Vignette StoryServer 4 and 5, Vignette V/5, and possibly other versions allows remote attackers to perform unauthorized SELECT queries by setting the vgn_creds cookie to an arbitrary value and directly accessing the save template.
CVE-2003-0400(PUBLISHED):Vignette StoryServer and Vignette V/5 does not properly calculate the size of text variables, which causes Vignette to return unauthorized portions of memory, as demonstrated using the "-->" string in a CookieName argument to the login template, referred to as a "memory leak" in some reports.
CVE-2003-0401(PUBLISHED):Vignette StoryServer and Vignette V/5 allows remote attackers to obtain sensitive information via a request for the /vgn/style template.
CVE-2003-0402(PUBLISHED):The default login template (/vgn/login) in Vignette StoryServer 5 and Vignette V/5 generates different responses whether a user exists or not, which allows remote attackers to identify valid usernames via brute force attacks.
CVE-2003-0403(PUBLISHED):Vignette StoryServer 5 and Vignette V/5 allows remote attackers to read and modify license information, and cause a denial of service (service halt) by directly accessing the /vgn/license template.
CVE-2003-0404(PUBLISHED):Multiple Cross Site Scripting (XSS) vulnerabilities in Vignette StoryServer 4 and 5, and Vignette V/5 and V/6, allow remote attackers to insert arbitrary HTML and script via text variables, as demonstrated using the errInfo parameter of the default login template.
CVE-2003-0405(PUBLISHED):Vignette StoryServer 5 and Vignette V/6 allows remote attackers to execute arbitrary TCL code via (1) an HTTP query or cookie which is processed in the NEEDS command, or (2) an HTTP Referrer that is processed in the VALID_PATHS command.
CVE-2003-0406(PUBLISHED):PalmVNC 1.40 and earlier stores passwords in plaintext in the PalmVNCDB, which is backed up to PCs that the Palm is synchronized with, which could allow attackers to gain privileges.
CVE-2003-0407(PUBLISHED):Buffer overflow in gbnserver for Gnome Batalla Naval 1.0.4 allows remote attackers to execute arbitrary code via a long connection string.
CVE-2003-0408(PUBLISHED):Buffer overflow in Uptime Client (UpClient) 5.0b7, and possibly other versions, allows local users to gain privileges via a long -p argument.
CVE-2003-0409(PUBLISHED):Buffer overflow in BRS WebWeaver 1.04 and earlier allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a long HTTP (1) POST or (2) HEAD request.
CVE-2003-0410(PUBLISHED):Buffer overflow in AnalogX Proxy 4.13 allows remote attackers to execute arbitrary code via a long URL to port 6588.
CVE-2003-0411(PUBLISHED):Sun ONE Application Server 7.0 for Windows 2000/XP allows remote attackers to obtain JSP source code via a request that uses the uppercase ".JSP" extension instead of the lowercase .jsp extension.
CVE-2003-0412(PUBLISHED):Sun ONE Application Server 7.0 for Windows 2000/XP does not log the complete URI of a long HTTP request, which could allow remote attackers to hide malicious activities.
CVE-2003-0413(PUBLISHED):Cross-site scripting (XSS) vulnerability in the webapps-simple sample application for (1) Sun ONE Application Server 7.0 for Windows 2000/XP or (2) Sun Java System Web Server 6.1 allows remote attackers to insert arbitrary web script or HTML via an HTTP request that generates an "Invalid JSP file" error, which inserts the text in the resulting error message.
CVE-2003-0414(PUBLISHED):The installation of Sun ONE Application Server 7.0 for Windows 2000/XP creates a statefile with world-readable permissions, which allows local users to gain privileges by reading a plaintext password in the statefile.
CVE-2003-0415(PUBLISHED):Remote PC Access Server 2.2 allows remote attackers to cause a denial of service (crash) by receiving packets from the server and sending them back to the server.
CVE-2003-0416(PUBLISHED):Cross-site scripting (XSS) vulnerability in index.cgi for Bandmin 1.4 allows remote attackers to insert arbitrary HTML or script via (1) the year parameter in a showmonth action, (2) the month parameter in a showmonth action, or (3) the host parameter in a showhost action.
CVE-2003-0417(PUBLISHED):Directory traversal vulnerability in Son hServer 0.2 allows remote attackers to read arbitrary files via ".|." (modified dot-dot) sequences.
CVE-2003-0418(PUBLISHED):The Linux 2.0 kernel IP stack does not properly calculate the size of an ICMP citation, which causes it to include portions of unauthorized memory in ICMP error responses.
CVE-2003-0419(PUBLISHED):SMC Networks Barricade Wireless Cable/DSL Broadband Router SMC7004VWBR allows remote attackers to cause a denial of service via certain packets to PPTP port 1723 on the internal interface.
CVE-2003-0420(PUBLISHED):Information leak in dsimportexport for Apple Macintosh OS X Server 10.2.6 allows local users to obtain the username and password of the account running the tool.
CVE-2003-0421(PUBLISHED):Apple QuickTime / Darwin Streaming Server before 4.1.3f allows remote attackers to cause a denial of service (crash) via an MS-DOS device name (e.g. AUX) in a request to HTTP port 1220, a different vulnerability than CVE-2003-0502.
CVE-2003-0422(PUBLISHED):Apple QuickTime / Darwin Streaming Server before 4.1.3f allows remote attackers to cause a denial of service (crash) via a request to view_broadcast.cgi that does not contain the required parameters.
CVE-2003-0423(PUBLISHED):parse_xml.cgi in Apple QuickTime / Darwin Streaming Server before 4.1.3g allows remote attackers to obtain the source code for parseable files via the filename parameter.
CVE-2003-0424(PUBLISHED):Apple QuickTime / Darwin Streaming Server before 4.1.3f allows remote attackers to obtain the source code for scripts by appending encoded space (%20) or . (%2e) characters to an HTTP request for the script, e.g. view_broadcast.cgi.
CVE-2003-0425(PUBLISHED):Directory traversal vulnerability in Apple QuickTime / Darwin Streaming Server before 4.1.3f allows remote attackers to read arbitrary files via a ... (triple dot) in an HTTP request.
CVE-2003-0426(PUBLISHED):The installation of Apple QuickTime / Darwin Streaming Server before 4.1.3f starts the administration server with a "Setup Assistant" page that allows remote attackers to set the administrator password and gain privileges before the real administrator.
CVE-2003-0427(PUBLISHED):Buffer overflow in mikmod 3.1.6 and earlier allows remote attackers to execute arbitrary code via an archive file that contains a file with a long filename.
CVE-2003-0428(PUBLISHED):Unknown vulnerability in the DCERPC (DCE/RPC) dissector in Ethereal 0.9.12 and earlier allows remote attackers to cause a denial of service (memory consumption) via a certain NDR string.
CVE-2003-0429(PUBLISHED):The OSI dissector in Ethereal 0.9.12 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via invalid IPv4 or IPv6 prefix lengths, possibly triggering a buffer overflow.
CVE-2003-0430(PUBLISHED):The SPNEGO dissector in Ethereal 0.9.12 and earlier allows remote attackers to cause a denial of service (crash) via an invalid ASN.1 value.
CVE-2003-0431(PUBLISHED):The tvb_get_nstringz0 function in Ethereal 0.9.12 and earlier does not properly handle a zero-length buffer size, with unknown consequences.
CVE-2003-0432(PUBLISHED):Ethereal 0.9.12 and earlier does not handle certain strings properly, with unknown consequences, in the (1) BGP, (2) WTP, (3) DNS, (4) 802.11, (5) ISAKMP, (6) WSP, (7) CLNP, (8) ISIS, and (9) RMI dissectors.
CVE-2003-0433(PUBLISHED):Multiple buffer overflows in gnocatan 0.6.1 and earlier allow attackers to execute arbitrary code.
CVE-2003-0434(PUBLISHED):Various PDF viewers including (1) Adobe Acrobat 5.06 and (2) Xpdf 1.01 allow remote attackers to execute arbitrary commands via shell metacharacters in an embedded hyperlink.
CVE-2003-0435(PUBLISHED):Buffer overflow in net_swapscore for typespeed 0.4.1 and earlier allows remote attackers to execute arbitrary code.
CVE-2003-0436(PUBLISHED):Buffer overflow in search.cgi for mnoGoSearch 3.1.20 allows remote attackers to execute arbitrary code via a long ul parameter.
CVE-2003-0437(PUBLISHED):Buffer overflow in search.cgi for mnoGoSearch 3.2.10 allows remote attackers to execute arbitrary code via a long tmplt parameter.
CVE-2003-0438(PUBLISHED):eldav WebDAV client for Emacs, version 0.7.2 and earlier, allows local users to create or overwrite arbitrary files via a symlink attack on temporary files.
CVE-2003-0440(PUBLISHED):The (1) semi MIME library 1.14.5 and earlier, and (2) wemi 1.14.0 and possibly other versions, allows local users to overwrite arbitrary files via a symlink attack on temporary files.
CVE-2003-0441(PUBLISHED):Multiple buffer overflows in Orville Write (orville-write) 2.53 and earlier allow local users to gain privileges.
CVE-2003-0442(PUBLISHED):Cross-site scripting (XSS) vulnerability in the transparent SID support capability for PHP before 4.3.2 (session.use_trans_sid) allows remote attackers to insert arbitrary script via the PHPSESSID parameter.
CVE-2003-0444(PUBLISHED):Heap-based buffer overflow in GTKSee 0.5 and 0.5.1 allows remote attackers to execute arbitrary code via a PNG image of certain color depths.
CVE-2003-0445(PUBLISHED):Buffer overflow in webfs before 1.17.1 allows remote attackers to execute arbitrary code via an HTTP request with a long Request-URI.
CVE-2003-0446(PUBLISHED):Cross-site scripting (XSS) in Internet Explorer 5.5 and 6.0, possibly in a component that is also used by other Microsoft products, allows remote attackers to insert arbitrary web script via an XML file that contains a parse error, which inserts the script in the resulting error message.
CVE-2003-0447(PUBLISHED):The Custom HTTP Errors capability in Internet Explorer 5.01, 5.5 and 6.0 allows remote attackers to execute script in the Local Zone via an argument to shdocvw.dll that causes a "javascript:" link to be generated.
CVE-2003-0448(PUBLISHED):Portmon 1.7 and possibly earlier versions allows local users to read and write arbitrary files via the (1) -c (host file) or (2) -l (log file) command line options.
CVE-2003-0449(PUBLISHED):Progress Database 9.1 to 9.1D06 trusts user input to find and load libraries using dlopen, which allows local users to gain privileges via (1) a PATH environment variable that points to malicious libraries, as demonstrated using libjutil.so in_proapsv, or (2) the -installdir command line parameter, as demonstrated using librocket_r.so in _dbagent.
CVE-2003-0450(PUBLISHED):Cistron RADIUS daemon (radiusd-cistron) 1.6.6 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via a large value in an NAS-Port attribute, which is interpreted as a negative number and causes a buffer overflow.
CVE-2003-0451(PUBLISHED):Multiple buffer overflows in xbl before 1.0k allow local users to gain privileges via certain long command line arguments.
CVE-2003-0452(PUBLISHED):Buffer overflows in osh before 1.7-11 allow local users to execute arbitrary code and bypass shell restrictions via (1) long environment variables or (2) long "file redirections."
CVE-2003-0453(PUBLISHED):traceroute-nanog 6.1.1 allows local users to overwrite unauthorized memory and possibly execute arbitrary code via certain "nprobes" and "max_ttl" arguments that cause an integer overflow that is used when allocating memory, which leads to a buffer overflow.
CVE-2003-0454(PUBLISHED):Multiple buffer overflows in xgalaga 2.0.34 and earlier allow local users to gain privileges via a long HOME environment variable.
CVE-2003-0455(PUBLISHED):The imagemagick libmagick library 5.5 and earlier creates temporary files insecurely, which allows local users to create or overwrite arbitrary files.
CVE-2003-0456(PUBLISHED):VisNetic WebSite 3.5 allows remote attackers to obtain the full pathname of the server via a request containing a folder that does not exist, which leaks the pathname in an error message, as demonstrated using _vti_bin/fpcount.exe.
CVE-2003-0458(PUBLISHED):Unknown vulnerability in HP NonStop Server D40.00 through D48.03, and G01.00 through G06.20, allows local users to gain additional privileges.
CVE-2003-0459(PUBLISHED):KDE Konqueror for KDE 3.1.2 and earlier does not remove authentication credentials from URLs of the "user:password@host" form in the HTTP-Referer header, which could allow remote web sites to steal the credentials for pages that link to the sites.
CVE-2003-0460(PUBLISHED):The rotatelogs program on Apache before 1.3.28, for Windows and OS/2 systems, does not properly ignore certain control characters that are received over the pipe, which could allow remote attackers to cause a denial of service.
CVE-2003-0461(PUBLISHED):/proc/tty/driver/serial in Linux 2.4.x reveals the exact number of characters used in serial links, which could allow local users to obtain potentially sensitive information such as the length of passwords.
CVE-2003-0462(PUBLISHED):A race condition in the way env_start and env_end pointers are initialized in the execve system call and used in fs/proc/base.c on Linux 2.4 allows local users to cause a denial of service (crash).
CVE-2003-0464(PUBLISHED):The RPC code in Linux kernel 2.4 sets the reuse flag when sockets are created, which could allow local users to bind to UDP ports that are used by privileged services such as nfsd.
CVE-2003-0465(PUBLISHED):The kernel strncpy function in Linux 2.4 and 2.5 does not %NUL pad the buffer on architectures other than x86, as opposed to the expected behavior of strncpy as implemented in libc, which could lead to information leaks.
CVE-2003-0466(PUBLISHED):Off-by-one error in the fb_realpath() function, as derived from the realpath function in BSD, may allow attackers to execute arbitrary code, as demonstrated in wu-ftpd 2.5.0 through 2.6.2 via commands that cause pathnames of length MAXPATHLEN+1 to trigger a buffer overflow, including (1) STOR, (2) RETR, (3) APPE, (4) DELE, (5) MKD, (6) RMD, (7) STOU, or (8) RNTO.
CVE-2003-0467(PUBLISHED):Unknown vulnerability in ip_nat_sack_adjust of Netfilter in Linux kernels 2.4.20, and some 2.5.x, when CONFIG_IP_NF_NAT_FTP or CONFIG_IP_NF_NAT_IRC is enabled, or the ip_nat_ftp or ip_nat_irc modules are loaded, allows remote attackers to cause a denial of service (crash) in systems using NAT, possibly due to an integer signedness error.
CVE-2003-0468(PUBLISHED):Postfix 1.1.11 and earlier allows remote attackers to use Postfix to conduct "bounce scans" or DDos attacks of other hosts via an email address to the local host containing the target IP address and service name followed by a "!" string, which causes Postfix to attempt to use SMTP to communicate with the target on the associated port.
CVE-2003-0469(PUBLISHED):Buffer overflow in the HTML Converter (HTML32.cnv) on various Windows operating systems allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via cut-and-paste operation, as demonstrated in Internet Explorer 5.0 using a long "align" argument in an HR tag.
CVE-2003-0470(PUBLISHED):Buffer overflow in the "RuFSI Utility Class" ActiveX control (aka "RuFSI Registry Information Class"), as used for the Symantec Security Check service, allows remote attackers to execute arbitrary code via a long argument to CompareVersionStrings.
CVE-2003-0471(PUBLISHED):Buffer overflow in WebAdmin.exe for WebAdmin allows remote attackers to execute arbitrary code via an HTTP request to WebAdmin.dll with a long USER argument.
CVE-2003-0472(PUBLISHED):The IPv6 capability in IRIX 6.5.19 allows remote attackers to cause a denial of service (hang) in inetd via port scanning.
CVE-2003-0473(PUBLISHED):Unknown vulnerability in the IPv6 capability in IRIX 6.5.19 causes snoop to process packets as the root user, with unknown implications.
CVE-2003-0474(PUBLISHED):Directory traversal vulnerability in iWeb Server allows remote attackers to read arbitrary files via an HTTP request containing .. sequences, a different vulnerability than CVE-2003-0475.
CVE-2003-0475(PUBLISHED):Directory traversal vulnerability in iWeb Server 2 allows remote attackers to read arbitrary files via an HTTP request containing URL-encoded .. sequences ("%5c%2e%2e"), a different vulnerability than CVE-2003-0474.
CVE-2003-0476(PUBLISHED):The execve system call in Linux 2.4.x records the file descriptor of the executable process in the file table of the calling process, which allows local users to gain read access to restricted file descriptors.
CVE-2003-0477(PUBLISHED):wzdftpd 0.1rc4 and earlier allows remote attackers to cause a denial of service (crash) via a PORT command without an argument.
CVE-2003-0478(PUBLISHED):Format string vulnerability in (1) Bahamut IRCd 1.4.35 and earlier, and other IRC daemons based on Bahamut including (2) digatech 1.2.1, (3) methane 0.1.1, (4) AndromedeIRCd 1.2.3-Release, and (5) ircd-RU, when running in debug mode, allows remote attackers to cause a denial of service and possibly execute arbitrary code via a request containing format strings.
CVE-2003-0479(PUBLISHED):Cross-site scripting (XSS) vulnerability in the guestbook for WebBBS allows remote attackers to insert arbitrary web script via the (1) Name, (2) Email, or (3) Message fields.
CVE-2003-0480(PUBLISHED):VMware Workstation 4.0 for Linux allows local users to overwrite arbitrary files and gain privileges via "symlink manipulation."
CVE-2003-0481(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in TUTOS 1.1 allow remote attackers to insert arbitrary web script, as demonstrated using the msg parameter to file_select.php.
CVE-2003-0482(PUBLISHED):TUTOS 1.1 allows remote attackers to execute arbitrary code by uploading the code using file_new.php, then directly accessing the uploaded code via a request to the repository containing the code.
CVE-2003-0483(PUBLISHED):Cross-site scripting (XSS) vulnerabilities in XMB Forum 1.8 Partagium allow remote attackers to insert arbitrary script via (1) the member parameter to member.php or (2) the action parameter to buddy.php.
CVE-2003-0484(PUBLISHED):Cross-site scripting (XSS) vulnerability in viewtopic.php for phpBB allows remote attackers to insert arbitrary web script via the topic_id parameter.
CVE-2003-0485(PUBLISHED):Buffer overflow in Progress 4GL Compiler 9.1D06 and earlier allows attackers to execute arbitrary code via source code containing a long, invalid data type.
CVE-2003-0486(PUBLISHED):SQL injection vulnerability in viewtopic.php for phpBB 2.0.5 and earlier allows remote attackers to steal password hashes via the topic_id parameter.
CVE-2003-0487(PUBLISHED):Multiple buffer overflows in Kerio MailServer 5.6.3 allow remote authenticated users to cause a denial of service and possibly execute arbitrary code via (1) a long showuser parameter in the do_subscribe module, (2) a long folder parameter in the add_acl module, (3) a long folder parameter in the list module, and (4) a long user parameter in the do_map module.
CVE-2003-0488(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Kerio MailServer 5.6.3 allow remote attackers to insert arbitrary web script via (1) the add_name parameter in the add_acl module, or (2) the alias parameter in the do_map module.
CVE-2003-0489(PUBLISHED):tcptraceroute 1.4 and earlier does not fully drop privileges after obtaining a file descriptor for capturing packets, which may allow local users to gain access to the descriptor via a separate vulnerability in tcptraceroute.
CVE-2003-0490(PUBLISHED):The installation of Dantz Retrospect Client 5.0.540 on MacOS X 10.2.6, and possibly other versions, creates critical directories and files with world-writable permissions, which allows local users to gain privileges as other users by replacing programs with malicious code.
CVE-2003-0491(PUBLISHED):The Tutorials 2.0 module in XOOPS and E-XOOPS allows remote attackers to execute arbitrary code by uploading a PHP file without a MIME image type, then directly accessing the uploaded file.
CVE-2003-0492(PUBLISHED):Cross-site scripting (XSS) vulnerability in search.asp for Snitz Forums 3.4.03 and earlier allows remote attackers to execute arbitrary web script via the Search parameter.
CVE-2003-0493(PUBLISHED):Snitz Forums 3.4.03 and earlier allows attackers to gain privileges as other users by stealing and replaying the encrypted password after obtaining a valid session ID.
CVE-2003-0494(PUBLISHED):password.asp in Snitz Forums 3.4.03 and earlier allows remote attackers to reset passwords and gain privileges as other users by via a direct request to password.asp with a modified member id.
CVE-2003-0495(PUBLISHED):Cross-site scripting (XSS) vulnerability in LedNews 0.7 allows remote attackers to insert arbitrary web script via a news item.
CVE-2003-0496(PUBLISHED):Microsoft SQL Server before Windows 2000 SP4 allows local users to gain privileges as the SQL Server user by calling the xp_fileexist extended stored procedure with a named pipe as an argument instead of a normal file.
CVE-2003-0497(PUBLISHED):Caché Database 5.x installs /cachesys/bin/cache with world-writable permissions, which allows local users to gain privileges by modifying cache and executing it via cuxs.
CVE-2003-0498(PUBLISHED):Caché Database 5.x installs the /cachesys/csp directory with insecure permissions, which allows local users to execute arbitrary code by adding server-side scripts that are executed with root privileges.
CVE-2003-0499(PUBLISHED):Mantis 0.17.5 and earlier stores its database password in cleartext in a world-readable configuration file, which allows local users to perform unauthorized database operations.
CVE-2003-0500(PUBLISHED):SQL injection vulnerability in the PostgreSQL authentication module (mod_sql_postgres) for ProFTPD before 1.2.9rc1 allows remote attackers to execute arbitrary SQL and gain privileges by bypassing authentication or stealing passwords via the USER name.
CVE-2003-0501(PUBLISHED):The /proc filesystem in Linux allows local users to obtain sensitive information by opening various entries in /proc/self before executing a setuid program, which causes the program to fail to change the ownership and permissions of those entries.
CVE-2003-0502(PUBLISHED):Apple QuickTime / Darwin Streaming Server before 4.1.3g allows remote attackers to cause a denial of service (crash) via a .. (dot dot) sequence followed by an MS-DOS device name (e.g. AUX) in a request to HTTP port 1220, a different vulnerability than CVE-2003-0421.
CVE-2003-0503(PUBLISHED):Buffer overflow in the ShellExecute API function of SHELL32.DLL in Windows 2000 before SP4 may allow attackers to cause a denial of service or execute arbitrary code via a long third argument.
CVE-2003-0504(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Phpgroupware ********** (aka webdistro) allow remote attackers to insert arbitrary HTML or web script, as demonstrated with a request to index.php in the addressbook module.
CVE-2003-0505(PUBLISHED):Directory traversal vulnerability in Microsoft NetMeeting 3.01 2000 before SP4 allows remote attackers to read arbitrary files via "..\.." (dot dot) sequences in a file transfer request.
CVE-2003-0506(PUBLISHED):Microsoft NetMeeting 3.01 2000 before SP4 allows remote attackers to cause a denial of service (shutdown of NetMeeting conference) via malformed packets, as demonstrated via the chat conversation.
CVE-2003-0507(PUBLISHED):Stack-based buffer overflow in Active Directory in Windows 2000 before SP4 allows remote attackers to cause a denial of service (reboot) and possibly execute arbitrary code via an LDAP version 3 search request with a large number of (1) "AND," (2) "OR," and possibly other statements, which causes LSASS.EXE to crash.
CVE-2003-0508(PUBLISHED):Buffer overflow in the WWWLaunchNetscape function of Adobe Acrobat Reader (acroread) 5.0.7 and earlier allows remote attackers to execute arbitrary code via a .pdf file with a long mailto link.
CVE-2003-0509(PUBLISHED):SQL injection vulnerability in Cyberstrong eShop 4.2 and earlier allows remote attackers to steal authentication information and gain privileges via the ProductCode parameter in (1) 10expand.asp, (2) 10browse.asp, and (3) 20review.asp.
CVE-2003-0510(PUBLISHED):Format string vulnerability in ezbounce 1.0 through 1.50 allows remote attackers to execute arbitrary code via the "sessions" command.
CVE-2003-0511(PUBLISHED):The web server for Cisco Aironet AP1x00 Series Wireless devices running certain versions of IOS 12.2 allow remote attackers to cause a denial of service (reload) via a malformed URL.
CVE-2003-0512(PUBLISHED):Cisco IOS 12.2 and earlier generates a "% Login invalid" message instead of prompting for a password when an invalid username is provided, which allows remote attackers to identify valid usernames on the system and conduct brute force password guessing, as reported for the Aironet Bridge.
CVE-2003-0513(PUBLISHED):Microsoft Internet Explorer allows remote attackers to bypass intended cookie access restrictions on a web application via "%2e%2e" (encoded dot dot) directory traversal sequences in a URL, which causes Internet Explorer to send the cookie outside the specified URL subsets, e.g. to a vulnerable application that runs on the same server as the target application.
CVE-2003-0514(PUBLISHED):Apple Safari allows remote attackers to bypass intended cookie access restrictions on a web application via "%2e%2e" (encoded dot dot) directory traversal sequences in a URL, which causes Safari to send the cookie outside the specified URL subsets, e.g. to a vulnerable application that runs on the same server as the target application.
CVE-2003-0515(PUBLISHED):SQL injection vulnerabilities in the (1) PostgreSQL or (2) MySQL authentication modules for teapop 0.3.5 and earlier allow attackers to execute arbitrary SQL and possibly gain privileges.
CVE-2003-0516(PUBLISHED):cnd.c in mgetty 1.1.28 and earlier does not properly filter non-printable characters and quotes, which may allow remote attackers to execute arbitrary commands via shell metacharacters in (1) caller ID or (2) caller name strings.
CVE-2003-0517(PUBLISHED):faxrunqd.in in mgetty 1.1.28 and earlier allows local users to overwrite files via a symlink attack on JOB files.
CVE-2003-0518(PUBLISHED):The screen saver in MacOS X allows users with physical access to cause the screen saver to crash and gain access to the underlying session via a large number of characters in the password field, possibly triggering a buffer overflow.
CVE-2003-0519(PUBLISHED):Certain versions of Internet Explorer 5 and 6, in certain Windows environments, allow remote attackers to cause a denial of service (freeze) via a URL to C:\aux (MS-DOS device name) and possibly other devices.
CVE-2003-0520(PUBLISHED):Trillian 1.0 Pro and 0.74 Freeware allows remote attackers to cause a denial of service (crash) via a TypingUser message in which the "TypingUser" string has been modified.
CVE-2003-0521(PUBLISHED):Cross-site scripting (XSS) vulnerability in cPanel 6.4.2 allows remote attackers to insert arbitrary HTML and possibly gain cPanel administrator privileges via script in a URL that is logged but not properly quoted when displayed via the (1) Error Log or (2) Latest Visitors screens.
CVE-2003-0522(PUBLISHED):Multiple SQL injection vulnerabilities in ProductCart 1.5 through 2 allow remote attackers to (1) gain access to the admin control panel via the idadmin parameter to login.asp or (2) gain other privileges via the Email parameter to Custva.asp.
CVE-2003-0523(PUBLISHED):Cross-site scripting (XSS) vulnerability in msg.asp for certain versions of ProductCart allow remote attackers to execute arbitrary web script via the message parameter.
CVE-2003-0524(PUBLISHED):Qt in Knoppix 3.1 Live CD allows local users to overwrite arbitrary files via a symlink attack on the qt_plugins_3.0rc temporary file in the .qt directory.
CVE-2003-0525(PUBLISHED):The getCanonicalPath function in Windows NT 4.0 may free memory that it does not own and cause heap corruption, which allows attackers to cause a denial of service (crash) via requests that cause a long file name to be passed to getCanonicalPath, as demonstrated on the IBM JVM using a long string to the java.io.getCanonicalPath Java method.
CVE-2003-0526(PUBLISHED):Cross-site scripting (XSS) vulnerability in Microsoft Internet Security and Acceleration (ISA) Server 2000 allows remote attackers to inject arbitrary web script via a URL containing the script in the domain name portion, which is not properly cleansed in the default error pages (1) 500.htm for "500 Internal Server error" or (2) 404.htm for "404 Not Found."
CVE-2003-0528(PUBLISHED):Heap-based buffer overflow in the Distributed Component Object Model (DCOM) interface in the RPCSS Service allows remote attackers to execute arbitrary code via a malformed RPC request with a long filename parameter, a different vulnerability than CVE-2003-0352 (Blaster/Nachi) and CVE-2003-0715.
CVE-2003-0530(PUBLISHED):Buffer overflow in the BR549.DLL ActiveX control for Internet Explorer 5.01 SP3 through 6.0 SP1 allows remote attackers to execute arbitrary code.
CVE-2003-0531(PUBLISHED):Internet Explorer 5.01 SP3 through 6.0 SP1 allows remote attackers to access and execute script in the My Computer domain using the browser cache via crafted Content-Type and Content-Disposition headers, aka the "Browser Cache Script Execution in My Computer Zone" vulnerability.
CVE-2003-0532(PUBLISHED):Internet Explorer 5.01 SP3 through 6.0 SP1 does not properly determine object types that are returned by web servers, which could allow remote attackers to execute arbitrary code via an object tag with a data parameter to a malicious file hosted on a server that returns an unsafe Content-Type, aka the "Object Type" vulnerability.
CVE-2003-0533(PUBLISHED):Stack-based buffer overflow in certain Active Directory service functions in LSASRV.DLL of the Local Security Authority Subsystem Service (LSASS) in Microsoft Windows NT 4.0 SP6a, 2000 SP2 through SP4, XP SP1, Server 2003, NetMeeting, Windows 98, and Windows ME, allows remote attackers to execute arbitrary code via a packet that causes the DsRolerUpgradeDownlevelServer function to create long debug entries for the DCPROMO.LOG log file, as exploited by the Sasser worm.
CVE-2003-0535(PUBLISHED):Buffer overflow in xbl 1.0k and earlier allows local users to gain privileges via a long -display command line option.
CVE-2003-0536(PUBLISHED):Directory traversal vulnerability in phpSysInfo 2.1 and earlier allows attackers with write access to a local directory to read arbitrary files as the PHP user or cause a denial of service via .. (dot dot) sequences in the (1) template or (2) lng parameters.
CVE-2003-0537(PUBLISHED):The liece Emacs IRC client 2.0+0.20030527 and earlier creates temporary files insecurely, which could allow local users to overwrite arbitrary files as other users.
CVE-2003-0538(PUBLISHED):The mailcap file for mozart 1.2.5 and earlier causes Oz applications to be passed to the Oz interpreter, which allows remote attackers to execute arbitrary Oz programs in a MIME-aware client program.
CVE-2003-0539(PUBLISHED):skk (Simple Kana to Kanji conversion program) 12.1 and earlier, and the ddskk package which is based on skk, creates temporary files insecurely, which allows local users to overwrite arbitrary files.
CVE-2003-0540(PUBLISHED):The address parser code in Postfix 1.1.12 and earlier allows remote attackers to cause a denial of service (lock) via (1) a malformed envelope address to a local host that would generate a bounce and contains the ".!" string in the MAIL FROM or Errors-To headers, which causes nqmgr to lock up, or (2) via a valid MAIL FROM with a RCPT TO containing a ".!" string, which causes an instance of the SMTP listener to lock up.
CVE-2003-0541(PUBLISHED):gtkhtml before 1.1.10, as used in Evolution, allows remote attackers to cause a denial of service (crash) via a malformed message that causes a null pointer dereference.
CVE-2003-0542(PUBLISHED):Multiple stack-based buffer overflows in (1) mod_alias and (2) mod_rewrite for Apache before 1.3.29 allow attackers to create configuration files to cause a denial of service (crash) or execute arbitrary code via a regular expression with more than 9 captures.
CVE-2003-0543(PUBLISHED):Integer overflow in OpenSSL 0.9.6 and 0.9.7 allows remote attackers to cause a denial of service (crash) via an SSL client certificate with certain ASN.1 tag values.
CVE-2003-0544(PUBLISHED):OpenSSL 0.9.6 and 0.9.7 does not properly track the number of characters in certain ASN.1 inputs, which allows remote attackers to cause a denial of service (crash) via an SSL client certificate that causes OpenSSL to read past the end of a buffer when the long form is used.
CVE-2003-0545(PUBLISHED):Double free vulnerability in OpenSSL 0.9.7 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via an SSL client certificate with a certain invalid ASN.1 encoding.
CVE-2003-0546(PUBLISHED):up2date 3.0.7 and 3.1.23 does not properly verify RPM GPG signatures, which could allow remote attackers to cause unsigned packages to be installed from the Red Hat Network, if that network is compromised.
CVE-2003-0547(PUBLISHED):GDM before *******, when using the "examine session errors" feature, allows local users to read arbitrary files via a symlink attack on the ~/.xsession-errors file.
CVE-2003-0548(PUBLISHED):The X Display Manager Control Protocol (XDMCP) support for GDM before ******* allows attackers to cause a denial of service (daemon crash) when a chosen host expires, a different issue than CVE-2003-0549.
CVE-2003-0549(PUBLISHED):The X Display Manager Control Protocol (XDMCP) support for GDM before ******* allows attackers to cause a denial of service (daemon crash) via a short authorization key name.
CVE-2003-0550(PUBLISHED):The STP protocol, as enabled in Linux 2.4.x, does not provide sufficient security by design, which allows attackers to modify the bridge topology.
CVE-2003-0551(PUBLISHED):The STP protocol implementation in Linux 2.4.x does not properly verify certain lengths, which could allow attackers to cause a denial of service.
CVE-2003-0552(PUBLISHED):Linux 2.4.x allows remote attackers to spoof the bridge Forwarding table via forged packets whose source addresses are the same as the target.
CVE-2003-0553(PUBLISHED):Buffer overflow in the Client Detection Tool (CDT) plugin (npcdt.dll) for Netscape 7.02 allows remote attackers to execute arbitrary code via an attachment with a long filename.
CVE-2003-0554(PUBLISHED):NeoModus Direct Connect 1.0 build 9, and possibly other versions, allows remote attackers to cause a denial of service (connection and possibly memory exhaustion) via a flood of ConnectToMe requests containing arbitrary IP addresses and ports.
CVE-2003-0555(PUBLISHED):ImageMagick 5.4.3.x and earlier allows attackers to cause a denial of service (crash) and possibly execute arbitrary code via a "%x" filename, possibly triggering a format string vulnerability.
CVE-2003-0556(PUBLISHED):Polycom MGC 25 allows remote attackers to cause a denial of service (crash) via a large number of "user" requests to the control port 5003, as demonstrated using the blast TCP stress tester.
CVE-2003-0557(PUBLISHED):SQL injection vulnerability in login.asp for StoreFront 6.0, and possibly earlier versions, allows remote attackers to obtain sensitive user information via SQL statements in the password field.
CVE-2003-0558(PUBLISHED):Buffer overflow in LeapFTP 2.7.3.600 allows remote FTP servers to execute arbitrary code via a long IP address response to a PASV request.
CVE-2003-0559(PUBLISHED):mainfile.php in phpforum 2 RC-1, and possibly earlier versions, allows remote attackers to execute arbitrary PHP code by modifying the MAIN_PATH parameter to reference a URL on a remote web server that contains the code.
CVE-2003-0560(PUBLISHED):SQL injection vulnerability in shopexd.asp for VP-ASP allows remote attackers to gain administrator privileges via the id parameter.
CVE-2003-0561(PUBLISHED):Multiple buffer overflows in IglooFTP PRO 3.8 allow remote FTP servers to execute arbitrary code via (1) a long FTP banner, or long responses to the client commands (2) USER, (3) PASS, (4) ACCT, and possibly other commands.
CVE-2003-0562(PUBLISHED):Buffer overflow in the CGI2PERL.NLM PERL handler in Novell Netware 5.1 and 6.0 allows remote attackers to cause a denial of service (ABEND) via a long input string.
CVE-2003-0564(PUBLISHED):Multiple vulnerabilities in multiple vendor implementations of the Secure/Multipurpose Internet Mail Extensions (S/MIME) protocol allow remote attackers to cause a denial of service and possibly execute arbitrary code via an S/MIME email message containing certain unexpected ASN.1 constructs, as demonstrated using the NISSC test suite.
CVE-2003-0565(PUBLISHED):Multiple vulnerabilities in multiple vendor implementations of the X.400 protocol allow remote attackers to cause a denial of service and possibly execute arbitrary code via an X.400 message containing certain unexpected ASN.1 constructs, as demonstrated using the NISSC test suite.
CVE-2003-0567(PUBLISHED):Cisco IOS 11.x and 12.0 through 12.2 allows remote attackers to cause a denial of service (traffic block) by sending a particular sequence of IPv4 packets to an interface on the device, causing the input queue on that interface to be marked as full.
CVE-2003-0572(PUBLISHED):Unknown vulnerability in nsd in SGI IRIX 6.5.x through 6.5.20f, and possibly earlier versions, allows attackers to cause a denial of service (memory consumption).
CVE-2003-0573(PUBLISHED):The DNS callbacks in nsd in SGI IRIX 6.5.x through 6.5.20f, and possibly earlier versions, do not perform sufficient sanity checking, with unknown impact.
CVE-2003-0574(PUBLISHED):Unknown vulnerability in SGI IRIX 6.5.x through 6.5.20, and possibly earlier versions, allows local users to cause a core dump in scheme and possibly gain privileges via certain environment variables, a different vulnerability than CVE-2001-0797 and CVE-1999-0028.
CVE-2003-0575(PUBLISHED):Heap-based buffer overflow in the name services daemon (nsd) in SGI IRIX 6.5.x through 6.5.21f, and possibly earlier versions, allows attackers to gain root privileges via the AUTH_UNIX gid list.
CVE-2003-0576(PUBLISHED):Unknown vulnerability in the NFS daemon (nfsd) in SGI IRIX 6.5.19f and earlier allows remote attackers to cause a denial of service (kernel panic) via certain packets that cause XDR decoding errors, a different vulnerability than CVE-2003-0619.
CVE-2003-0577(PUBLISHED):mpg123 0.59r allows remote attackers to cause a denial of service and possibly execute arbitrary code via an MP3 file with a zero bitrate, which creates a negative frame size.
CVE-2003-0578(PUBLISHED):cci_dir in IBM U2 UniVerse 10.0.0.9 and earlier creates hard links and unlinks files as root, which allows local users to gain privileges by deleting and overwriting arbitrary files.
CVE-2003-0579(PUBLISHED):uvadmsh in IBM U2 UniVerse 10.0.0.9 and earlier trusts the user-supplied -uv.install command line option to find and execute the uv.install program, which allows local users to gain privileges by providing a pathname that is under control of the user.
CVE-2003-0580(PUBLISHED):Buffer overflow in uvadmsh in IBM U2 UniVerse 10.0.0.9 and earlier allows the uvadm user to execute arbitrary code via a long -uv.install command line argument.
CVE-2003-0581(PUBLISHED):X Fontserver for Truetype fonts (xfstt) 1.4 allows remote attackers to cause a denial of service and possibly execute arbitrary code via a (1) FS_QueryXExtents8 or (2) FS_QueryXBitmaps8 packet, and possibly other types of packets, with a large num_ranges value, which causes an out-of-bounds array access.
CVE-2003-0583(PUBLISHED):Buffer overflow in Backup and Restore Utility for Unix (BRU) 17.0 and earlier, when running setuid, allows local users to execute arbitrary code via a long command line argument.
CVE-2003-0584(PUBLISHED):Format string vulnerability in Backup and Restore Utility for Unix (BRU) 17.0 and earlier, when running setuid, allows local users to execute arbitrary code via format string specifiers in a command line argument.
CVE-2003-0585(PUBLISHED):SQL injection vulnerability in login.asp of Brooky eStore 1.0.1 through 1.0.2b allows remote attackers to bypass authentication and execute arbitrary SQL code via the (1) user or (2) pass parameters.
CVE-2003-0586(PUBLISHED):Brooky eStore 1.0.1 through 1.0.2b allows remote attackers to obtain sensitive path information via a direct HTTP request to settings.inc.php.
CVE-2003-0587(PUBLISHED):Cross-site scripting (XSS) vulnerability in Infopop Ultimate Bulletin Board (UBB) 6.x allows remote authenticated users to execute arbitrary web script and gain administrative access via the "displayed name" attribute of the "ubber" cookie.
CVE-2003-0588(PUBLISHED):admin.php in Digi-news 1.1 allows remote attackers to bypass authentication via a cookie with the username set to the name of the administrator, which satisfies an improper condition in admin.php that does not require a correct password.
CVE-2003-0589(PUBLISHED):admin.php in Digi-ads 1.1 allows remote attackers to bypass authentication via a cookie with the username set to the name of the administrator, which satisfies an improper condition in admin.php that does not require a correct password.
CVE-2003-0590(PUBLISHED):Cross-site scripting (XSS) vulnerability in Splatt Forum allows remote attackers to insert arbitrary HTML and web script via the post icon (image_subject) field.
CVE-2003-0592(PUBLISHED):Konqueror in KDE 3.1.3 and earlier (kdelibs) allows remote attackers to bypass intended cookie access restrictions on a web application via "%2e%2e" (encoded dot dot) directory traversal sequences in a URL, which causes Konqueror to send the cookie outside the specified URL subsets, e.g. to a vulnerable application that runs on the same server as the target application.
CVE-2003-0593(PUBLISHED):Opera allows remote attackers to bypass intended cookie access restrictions on a web application via "%2e%2e" (encoded dot dot) directory traversal sequences in a URL, which causes Opera to send the cookie outside the specified URL subsets, e.g. to a vulnerable application that runs on the same server as the target application.
CVE-2003-0594(PUBLISHED):Mozilla allows remote attackers to bypass intended cookie access restrictions on a web application via "%2e%2e" (encoded dot dot) directory traversal sequences in a URL, which causes Mozilla to send the cookie outside the specified URL subsets, e.g. to a vulnerable application that runs on the same server as the target application.
CVE-2003-0595(PUBLISHED):Buffer overflow in WiTango Application Server and Tango 2000 allows remote attackers to execute arbitrary code via a long cookie to Witango_UserReference.
CVE-2003-0596(PUBLISHED):FDclone 2.00a, and other versions before 2.02a, creates temporary directories with predictable names and uses them if they already exist, which allows local users to read or modify files of other fdclone users by creating the directory ahead of time.
CVE-2003-0597(PUBLISHED):Unknown vulnerability in display of Merge before 5.3.23a in UnixWare 7.1.x allows local users to gain root privileges.
CVE-2003-0599(PUBLISHED):Unknown vulnerability in the Virtual File System (VFS) capability for phpGroupWare 0.9.16preRC and versions before ********** with unknown implications, related to the VFS path being under the web document root.
CVE-2003-0601(PUBLISHED):Workgroup Manager in Apple Mac OS X Server 10.2 through 10.2.6 does not disable a password for a new account before it is saved for the first time, which allows remote attackers to gain unauthorized access via the new account before it is saved.
CVE-2003-0602(PUBLISHED):Multiple cross-site scripting vulnerabilities (XSS) in Bugzilla 2.16.x before 2.16.3 and 2.17.x before 2.17.4 allow remote attackers to insert arbitrary HTML or web script via (1) multiple default German and Russian HTML templates or (2) ALT and NAME attributes in AREA tags as used by the GraphViz graph generation feature for local dependency graphs.
CVE-2003-0603(PUBLISHED):Bugzilla 2.16.x before 2.16.3, 2.17.x before 2.17.4, and earlier versions allows local users to overwrite arbitrary files via a symlink attack on temporary files that are created in directories with group-writable or world-writable permissions.
CVE-2003-0604(PUBLISHED):Windows Media Player (WMP) 7 and 8, as running on Internet Explorer and possibly other Microsoft products that process HTML, allows remote attackers to bypass zone restrictions and access or execute arbitrary files via an IFRAME tag pointing to an ASF file whose Content-location contains a File:// URL.
CVE-2003-0605(PUBLISHED):The RPC DCOM interface in Windows 2000 SP3 and SP4 allows remote attackers to cause a denial of service (crash), and local attackers to use the DoS to hijack the epmapper pipe to gain privileges, via certain messages to the __RemoteGetClassObject interface that cause a NULL pointer to be passed to the PerformScmStage function.
CVE-2003-0606(PUBLISHED):sup 1.8 and earlier does not properly create temporary files, which allows local users to overwrite arbitrary files.
CVE-2003-0607(PUBLISHED):Buffer overflow in xconq 7.4.1 allows local users to become part of the "games" group via the (1) USER or (2) DISPLAY environment variables.
CVE-2003-0609(PUBLISHED):Stack-based buffer overflow in the runtime linker, ld.so.1, on Solaris 2.6 through 9 allows local users to gain root privileges via a long LD_PRELOAD environment variable.
CVE-2003-0610(PUBLISHED):Directory traversal vulnerability in ePO agent for McAfee ePolicy Orchestrator 3.0 allows remote attackers to read arbitrary files via a certain HTTP request.
CVE-2003-0611(PUBLISHED):Multiple buffer overflows in xtokkaetama 1.0 allow local users to gain privileges via a long (1) -display command line argument or (2) XTOKKAETAMADIR environment variable.
CVE-2003-0612(PUBLISHED):Multiple buffer overflows in main.c for Crafty 19.3 allow local users to gain group "games" privileges via long command line arguments to crafty.bin.
CVE-2003-0613(PUBLISHED):Buffer overflow in zblast-svgalib of zblast 1.2.1 and earlier allows local users to execute arbitrary code via the high score file.
CVE-2003-0614(PUBLISHED):Cross-site scripting (XSS) vulnerability in search.php of Gallery 1.1 through 1.3.4 allows remote attackers to insert arbitrary web script via the searchstring parameter.
CVE-2003-0615(PUBLISHED):Cross-site scripting (XSS) vulnerability in start_form() of CGI.pm allows remote attackers to insert web script via a URL that is fed into the form's action parameter.
CVE-2003-0616(PUBLISHED):Format string vulnerability in ePO service for McAfee ePolicy Orchestrator 2.0, 2.5, and 2.5.1 allows remote attackers to execute arbitrary code via a POST request with format strings in the computerlist parameter, which are used when logging a failed name resolution.
CVE-2003-0617(PUBLISHED):mindi 0.58 and earlier does not properly create temporary files, which allows local users to overwrite arbitrary files.
CVE-2003-0618(PUBLISHED):Multiple vulnerabilities in suidperl 5.6.1 and earlier allow a local user to obtain sensitive information about files for which the user does not have appropriate permissions.
CVE-2003-0619(PUBLISHED):Integer signedness error in the decode_fh function of nfs3xdr.c in Linux kernel before 2.4.21 allows remote attackers to cause a denial of service (kernel panic) via a negative size value within XDR data of an NFSv3 procedure call.
CVE-2003-0620(PUBLISHED):Multiple buffer overflows in man-db 2.4.1 and earlier, when installed setuid, allow local users to gain privileges via (1) MANDATORY_MANPATH, MANPATH_MAP, and MANDB_MAP arguments to add_to_dirlist in manp.c, (2) a long pathname to ult_src in ult_src.c, (3) a long .so argument to test_for_include in ult_src.c, (4) a long MANPATH environment variable, or (5) a long PATH environment variable.
CVE-2003-0621(PUBLISHED):The Administration Console for BEA Tuxedo 8.1 and earlier allows remote attackers to determine the existence of files outside the web root via modified paths in the INIFILE argument.
CVE-2003-0622(PUBLISHED):The Administration Console for BEA Tuxedo 8.1 and earlier allows remote attackers to cause a denial of service (hang) via pathname arguments that contain MS-DOS device names such as CON and AUX.
CVE-2003-0623(PUBLISHED):Cross-site scripting (XSS) vulnerability in the Administration Console for BEA Tuxedo 8.1 and earlier allows remote attackers to inject arbitrary web script via the INIFILE argument.
CVE-2003-0624(PUBLISHED):Cross-site scripting (XSS) vulnerability in InteractiveQuery.jsp for BEA WebLogic 8.1 and earlier allows remote attackers to inject malicious web script via the person parameter.
CVE-2003-0625(PUBLISHED):Off-by-one error in certain versions of xfstt allows remote attackers to read potentially sensitive memory via a malformed client request in the connection handshake, which leaks the memory in the server's response.
CVE-2003-0626(PUBLISHED):psdoccgi.exe in PeopleSoft PeopleTools 8.4 through 8.43 allows remote attackers to read arbitrary files via the (1) headername or (2) footername arguments.
CVE-2003-0627(PUBLISHED):psdoccgi.exe in PeopleSoft PeopleTools 8.4 through 8.43 allows remote attackers to cause a denial of service (application crash), possibly via the headername and footername arguments.
CVE-2003-0628(PUBLISHED):PeopleSoft Gateway Administration servlet (gateway.administration) in PeopleTools 8.43 and earlier allows remote attackers to obtain the full pathnames for server-side include (SSI) files via an HTTP request with an invalid value.
CVE-2003-0629(PUBLISHED):Cross-site scripting (XSS) vulnerability in PeopleSoft IScript environment for PeopleTools 8.43 and earlier allows remote attackers to insert arbitrary web script via a certain HTTP request to IScript.
CVE-2003-0630(PUBLISHED):Multiple buffer overflows in the atari800.svgalib setuid program of the Atari 800 emulator (atari800) before 1.2.2 allow local users to gain privileges via long command line arguments, as demonstrated with the -osa_rom argument.
CVE-2003-0631(PUBLISHED):VMware GSX Server 2.5.1 build 4968 and earlier, and Workstation 4.0 and earlier, allows local users to gain root privileges via certain enivronment variables that are used when launching a virtual machine session.
CVE-2003-0632(PUBLISHED):Buffer overflow in the Oracle Applications Web Report Review (FNDWRR) CGI program (FNDWRR.exe) of Oracle E-Business Suite 11.0 and 11.5.1 through 11.5.8 may allow remote attackers to execute arbitrary code via a long URL.
CVE-2003-0633(PUBLISHED):Multiple vulnerabilities in aoljtest.jsp of Oracle Applications AOL/J Setup Test Suite in Oracle E-Business Suite 11.5.1 through 11.5.8 allow a remote attacker to obtain sensitive information without authentication, such as the GUEST user password and the application server security key.
CVE-2003-0634(PUBLISHED):Stack-based buffer overflow in the PL/SQL EXTPROC functionality for Oracle9i Database Release 2 and 1, and Oracle 8i, allows authenticated database users, and arbitrary database users in some cases, to execute arbitrary code via a long library name.
CVE-2003-0635(PUBLISHED):Unknown vulnerability or vulnerabilities in Novell iChain 2.2 before Support Pack 1, with unknown impact, possibly related to unauthorized access to (1) NCPIP.NLM and (2) JSTCP.NLM.
CVE-2003-0636(PUBLISHED):Novell iChain 2.2 before Support Pack 1 does not properly verify that URL redirects match the DNS name of an accelerator, which allows attackers to redirect URLs to malicious web sites.
CVE-2003-0637(PUBLISHED):Novell iChain 2.2 before Support Pack 1 uses a shorter timeout for a non-existent user than a valid user, which makes it easier for remote attackers to guess usernames and conduct brute force password guessing.
CVE-2003-0638(PUBLISHED):Multiple buffer overflows in Novell iChain 2.1 before Field Patch 3, and iChain 2.2 before Field Patch 1a, allow attackers to cause a denial of service (ABEND) and possibly execute arbitrary code via (1) a long user name or (2) an unknown attack related to a "special script against login."
CVE-2003-0639(PUBLISHED):Unknown vulnerability in Novell iChain 2.2 before Support Pack 1 allows users to access restricted or secure pages without authentication.
CVE-2003-0640(PUBLISHED):BEA WebLogic Server and Express, when using NodeManager to start servers, provides Operator users with privileges to overwrite usernames and passwords, which may allow Operators to gain Admin privileges.
CVE-2003-0641(PUBLISHED):WatchGuard ServerLock for Windows 2000 before SL 2.0.3 allows local users to load arbitrary modules via the OpenProcess() function, as demonstrated using (1) a DLL injection attack, (2) ZwSetSystemInformation, and (3) API hooking in OpenProcess.
CVE-2003-0642(PUBLISHED):WatchGuard ServerLock for Windows 2000 before SL 2.0.4 allows local users to access kernel memory via a symlink attack on \Device\PhysicalMemory.
CVE-2003-0643(PUBLISHED):Integer signedness error in the Linux Socket Filter implementation (filter.c) in Linux 2.4.3-pre3 to 2.4.22-pre10 allows attackers to cause a denial of service (crash).
CVE-2003-0644(PUBLISHED):Kdbg 1.1.0 through 1.2.8 does not check permissions of the .kdbgrc file, which allows local users to execute arbitrary commands.
CVE-2003-0645(PUBLISHED):man-db 2.3.12 and 2.3.18 to 2.4.1 uses certain user-controlled DEFINE directives from the ~/.manpath file, even when running setuid, which could allow local users to gain privileges.
CVE-2003-0646(PUBLISHED):Multiple buffer overflows in ActiveX controls used by Trend Micro HouseCall 5.5 and 5.7, and Damage Cleanup Server 1.0, allow remote attackers to execute arbitrary code via long parameter strings.
CVE-2003-0647(PUBLISHED):Buffer overflow in the HTTP server for Cisco IOS 12.2 and earlier allows remote attackers to execute arbitrary code via an extremely long (2GB) HTTP GET request.
CVE-2003-0648(PUBLISHED):Multiple buffer overflows in vfte, based on FTE, before 0.50, allow local users to execute arbitrary code.
CVE-2003-0649(PUBLISHED):Buffer overflow in xpcd-svga for xpcd 2.08 and earlier allows local users to execute arbitrary code via a long HOME environment variable.
CVE-2003-0650(PUBLISHED):Directory traversal vulnerability in GSAPAK.EXE for GameSpy Arcade, possibly versions before 1.3e, allows remote attackers to overwrite arbitrary files and execute arbitrary code via .. (dot dot) sequences in filenames in a .APK (Zip) file.
CVE-2003-0651(PUBLISHED):Buffer overflow in the mylo_log logging function for mod_mylo 0.2.1 and earlier allows remote attackers to execute arbitrary code via a long HTTP GET request.
CVE-2003-0652(PUBLISHED):Buffer overflow in xtokkaetama allows local users to gain privileges via a long -nickname command line argument, a different vulnerability than CVE-2003-0611.
CVE-2003-0653(PUBLISHED):The OSI networking kernel (sys/netiso) in NetBSD 1.6.1 and earlier does not use a BSD-required "PKTHDR" mbuf when sending certain error responses to the sender of an OSI packet, which allows remote attackers to cause a denial of service (kernel panic or crash) via certain OSI packets.
CVE-2003-0654(PUBLISHED):Buffer overflow in autorespond may allow remote attackers to execute arbitrary code as the autorespond user via qmail.
CVE-2003-0655(PUBLISHED):rscsi in cdrtools 2.01 and earlier allows local users to overwrite arbitrary files and gain root privileges by specifying the target file as a command line argument, which is modified while rscsi is running with privileges.
CVE-2003-0656(PUBLISHED):eroaster before 2.2.0 allows local users to overwrite arbitrary files via a symlink attack on a temporary file that is used as a lockfile.
CVE-2003-0657(PUBLISHED):Multiple SQL injection vulnerabilities in the infolog module for phpgroupware 0.9.14 and earlier could allow remote attackers to conduct unauthorized database actions.
CVE-2003-0658(PUBLISHED):Docview before 1.1-18 in Caldera OpenLinux 3.1.1, SCO Linux 4.0, OpenServer 5.0.7, configures the Apache web server in a way that allows remote attackers to read arbitrary publicly readable files via a certain URL, possibly related to rewrite rules.
CVE-2003-0659(PUBLISHED):Buffer overflow in a function in User32.dll on Windows NT through Server 2003 allows local users to execute arbitrary code via long (1) LB_DIR messages to ListBox or (2) CB_DIR messages to ComboBox controls in a privileged application.
CVE-2003-0660(PUBLISHED):The Authenticode capability in Microsoft Windows NT through Server 2003 does not prompt the user to download and install ActiveX controls when the system is low on memory, which could allow remote attackers to execute arbitrary code without user approval.
CVE-2003-0661(PUBLISHED):The NetBT Name Service (NBNS) for NetBIOS in Windows NT 4.0, 2000, XP, and Server 2003 may include random memory in a response to a NBNS query, which could allow remote attackers to obtain sensitive information.
CVE-2003-0662(PUBLISHED):Buffer overflow in Troubleshooter ActiveX Control (Tshoot.ocx) in Microsoft Windows 2000 SP4 and earlier allows remote attackers to execute arbitrary code via an HTML document with a long argument to the RunQuery2 method.
CVE-2003-0663(PUBLISHED):Unknown vulnerability in the Local Security Authority Subsystem Service (LSASS) in Windows 2000 domain controllers allows remote attackers to cause a denial of service via a crafted LDAP message.
CVE-2003-0664(PUBLISHED):Microsoft Word 2002, 2000, 97, and 98(J) does not properly check certain properties of a document, which allows attackers to bypass the macro security model and automatically execute arbitrary macros via a malicious document.
CVE-2003-0665(PUBLISHED):Buffer overflow in the ActiveX control for Microsoft Access Snapshot Viewer for Access 97, 2000, and 2002 allows remote attackers to execute arbitrary code via long parameters to the control.
CVE-2003-0666(PUBLISHED):Buffer overflow in Microsoft Wordperfect Converter allows remote attackers to execute arbitrary code via modified data offset and data size parameters in a Corel WordPerfect file.
CVE-2003-0669(PUBLISHED):Unknown vulnerability in Solaris 2.6 through 9 causes a denial of service (system panic) via "a rare race condition" or an attack by local users.
CVE-2003-0670(PUBLISHED):Sustworks IPNetSentryX and IPNetMonitorX allow local users to sniff network packets via the setuid helper applications (1) RunTCPDump, which calls tcpdump, and (2) RunTCPFlow, which calls tcpflow.
CVE-2003-0671(PUBLISHED):Format string vulnerability in tcpflow, when used in a setuid context, allows local users to execute arbitrary code via the device name argument, as demonstrated in Sustworks IPNetSentryX and IPNetMonitorX the setuid program RunTCPFlow.
CVE-2003-0672(PUBLISHED):Format string vulnerability in pam-pgsql 0.5.2 and earlier allows remote attackers to execute arbitrary code via the username that isp rovided during authentication, which is not properly handled when recording a log message.
CVE-2003-0676(PUBLISHED):Directory traversal vulnerability in ViewLog for iPlanet Administration Server 5.1 (aka Sun ONE) allows remote attackers to read arbitrary files via "..%2f" (partially encoded dot dot) sequences.
CVE-2003-0677(PUBLISHED):Cisco CSS 11000 routers on the CS800 chassis allow remote attackers to cause a denial of service (CPU consumption or reboot) via a large number of TCP SYN packets to the circuit IP address, aka "ONDM Ping failure."
CVE-2003-0679(PUBLISHED):Unknown vulnerability in the libcpr library for the Checkpoint/Restart (cpr) system on SGI IRIX 6.5.21f and earlier allows local users to truncate or overwrite certain files.
CVE-2003-0680(PUBLISHED):Unknown vulnerability in NFS for SGI IRIX 6.5.21 and earlier may allow an NFS client to bypass read-only restrictions.
CVE-2003-0681(PUBLISHED):A "potential buffer overflow in ruleset parsing" for Sendmail 8.12.9, when using the nonstandard rulesets (1) recipient (2), final, or (3) mailer-specific envelope recipients, has unknown consequences.
CVE-2003-0682(PUBLISHED):"Memory bugs" in OpenSSH 3.7.1 and earlier, with unknown impact, a different set of vulnerabilities than CVE-2003-0693 and CVE-2003-0695.
CVE-2003-0683(PUBLISHED):NFS in SGI 6.5.21m and 6.5.21f does not perform access checks in certain configurations when an /etc/exports entry uses wildcards without any hostnames or groups, which could allow attackers to bypass intended restrictions.
CVE-2003-0685(PUBLISHED):Buffer overflow in Netris 0.52 and earlier, and possibly other versions, allows remote malicious Netris servers to execute arbitrary code on netris clients via a long server response.
CVE-2003-0686(PUBLISHED):Buffer overflow in PAM SMB module (pam_smb) 1.1.6 and earlier, when authenticating to a remote service, allows remote attackers to execute arbitrary code.
CVE-2003-0688(PUBLISHED):The DNS map code in Sendmail 8.12.8 and earlier, when using the "enhdnsbl" feature, does not properly initialize certain data structures, which allows remote attackers to cause a denial of service (process crash) via an invalid DNS response that causes Sendmail to free incorrect data.
CVE-2003-0689(PUBLISHED):The getgrouplist function in GNU libc (glibc) 2.2.4 and earlier allows attackers to cause a denial of service (segmentation fault) and execute arbitrary code when a user is a member of a large number of groups, which can cause a buffer overflow.
CVE-2003-0690(PUBLISHED):KDM in KDE 3.1.3 and earlier does not verify whether the pam_setcred function call succeeds, which may allow attackers to gain root privileges by triggering error conditions within PAM modules, as demonstrated in certain configurations of the MIT pam_krb5 module.
CVE-2003-0692(PUBLISHED):KDM in KDE 3.1.3 and earlier uses a weak session cookie generation algorithm that does not provide 128 bits of entropy, which allows attackers to guess session cookies via brute force methods and gain access to the user session.
CVE-2003-0693(PUBLISHED):A "buffer management error" in buffer_append_space of buffer.c for OpenSSH before 3.7 may allow remote attackers to execute arbitrary code by causing an incorrect amount of memory to be freed and corrupting the heap, a different vulnerability than CVE-2003-0695.
CVE-2003-0694(PUBLISHED):The prescan function in Sendmail 8.12.9 allows remote attackers to execute arbitrary code via buffer overflow attacks, as demonstrated using the parseaddr function in parseaddr.c.
CVE-2003-0695(PUBLISHED):Multiple "buffer management errors" in OpenSSH before 3.7.1 may allow attackers to cause a denial of service or execute arbitrary code using (1) buffer_init in buffer.c, (2) buffer_free in buffer.c, or (3) a separate function in channels.c, a different vulnerability than CVE-2003-0693.
CVE-2003-0696(PUBLISHED):The getipnodebyname() API in AIX 5.1 and 5.2 does not properly close sockets, which allows attackers to cause a denial of service (resource exhaustion).
CVE-2003-0697(PUBLISHED):Format string vulnerability in lpd in the bos.rte.printers fileset for AIX 4.3 through 5.2, with debug enabled, allows local users to cause a denial of service (crash) or gain root privileges.
CVE-2003-0699(PUBLISHED):The C-Media PCI sound driver in Linux before 2.4.21 does not use the get_user function to access userspace, which crosses security boundaries and may facilitate the exploitation of vulnerabilities, a different vulnerability than CVE-2003-0700.
CVE-2003-0700(PUBLISHED):The C-Media PCI sound driver in Linux before 2.4.22 does not use the get_user function to access userspace in certain conditions, which crosses security boundaries and may facilitate the exploitation of vulnerabilities, a different vulnerability than CVE-2003-0699.
CVE-2003-0701(PUBLISHED):Buffer overflow in Internet Explorer 6 SP1 for certain languages that support double-byte encodings (e.g., Japanese) allows remote attackers to execute arbitrary code via the Type property of an Object tag, a variant of CVE-2003-0344.
CVE-2003-0702(PUBLISHED):Unknown vulnerability in an ISAPI plugin for ISS Server Sensor 7.0 XPU 20.16, 20.18, and possibly other versions before 20.19, allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code in Internet Information Server (IIS) via a certain URL through SSL.
CVE-2003-0703(PUBLISHED):KisMAC before 0.05d trusts user-supplied variables to load arbitrary kernels or kernel modules, which allows local users to gain privileges via the $DRIVER_KEXT environment variable as used in (1) viha_driver.sh, (2) macjack_load.sh, or (3) airojack_load.sh, or (4) via "similar techniques" using exchangeKernel.sh.
CVE-2003-0704(PUBLISHED):KisMAC before 0.05d trusts user-supplied variables when chown'ing files or directories, which allows local users to gain privileges via the $DRIVER_KEXT environment variable in (1) viha_driver.sh, (2) macjack_load.sh, (3) airojack_load.sh, (4) setuid_enable.sh, (5) setuid_disable.sh, and using a "similar technique" for (6) viha_prep.sh and (7) viha_unprep.sh.
CVE-2003-0705(PUBLISHED):Buffer overflow in mah-jong 1.5.6 and earlier allows remote attackers to execute arbitrary code.
CVE-2003-0706(PUBLISHED):Unknown vulnerability in mah-jong 1.5.6 and earlier allows remote attackers to cause a denial of service (tight loop).
CVE-2003-0707(PUBLISHED):Buffer overflow in LinuxNode (node) before 0.3.2 allows remote attackers to execute arbitrary code.
CVE-2003-0708(PUBLISHED):Format string vulnerability in LinuxNode (node) before 0.3.2 may allow attackers to cause a denial of service or execute arbitrary code.
CVE-2003-0709(PUBLISHED):Buffer overflow in the whois client, which is not setuid but is sometimes called from within CGI programs, may allow remote attackers to execute arbitrary code via a long command line option.
CVE-2003-0711(PUBLISHED):Stack-based buffer overflow in the PCHealth system in the Help and Support Center function in Windows XP and Windows Server 2003 allows remote attackers to execute arbitrary code via a long query in an HCP URL.
CVE-2003-0712(PUBLISHED):Cross-site scripting (XSS) vulnerability in the HTML encoding for the Compose New Message form in Microsoft Exchange Server 5.5 Outlook Web Access (OWA) allows remote attackers to execute arbitrary web script.
CVE-2003-0714(PUBLISHED):The Internet Mail Service in Exchange Server 5.5 and Exchange 2000 allows remote attackers to cause a denial of service (memory exhaustion) by directly connecting to the SMTP service and sending a certain extended verb request, possibly triggering a buffer overflow in Exchange 2000.
CVE-2003-0715(PUBLISHED):Heap-based buffer overflow in the Distributed Component Object Model (DCOM) interface in the RPCSS Service allows remote attackers to execute arbitrary code via a malformed DCERPC DCOM object activation request packet with modified length fields, a different vulnerability than CVE-2003-0352 (Blaster/Nachi) and CVE-2003-0528.
CVE-2003-0717(PUBLISHED):The Messenger Service for Windows NT through Server 2003 does not properly verify the length of the message, which allows remote attackers to execute arbitrary code via a buffer overflow attack.
CVE-2003-0718(PUBLISHED):The WebDAV Message Handler for Internet Information Services (IIS) 5.0, 5.1, and 6.0 allows remote attackers to cause a denial of service (memory and CPU exhaustion, application crash) via a PROPFIND request with an XML message containing XML elements with a large number of attributes.
CVE-2003-0719(PUBLISHED):Buffer overflow in the Private Communications Transport (PCT) protocol implementation in the Microsoft SSL library, as used in Microsoft Windows NT 4.0 SP6a, 2000 SP2 through SP4, XP SP1, Server 2003, NetMeeting, Windows 98, and Windows ME, allows remote attackers to execute arbitrary code via PCT 1.0 handshake packets.
CVE-2003-0720(PUBLISHED):Buffer overflow in PINE before 4.58 allows remote attackers to execute arbitrary code via a malformed message/external-body MIME type.
CVE-2003-0721(PUBLISHED):Integer signedness error in rfc2231_get_param from strings.c in PINE before 4.58 allows remote attackers to execute arbitrary code via an email that causes an out-of-bounds array access using a negative number.
CVE-2003-0722(PUBLISHED):The default installation of sadmind on Solaris uses weak authentication (AUTH_SYS), which allows local and remote attackers to spoof Solstice AdminSuite clients and gain root privileges via a certain sequence of RPC packets.
CVE-2003-0723(PUBLISHED):Buffer overflow in gkrellmd for gkrellm 2.1.x before 2.1.14 may allow remote attackers to execute arbitrary code.
CVE-2003-0724(PUBLISHED):ssh on HP Tru64 UNIX 5.1B and 5.1A does not properly handle RSA signatures when digital certificates and RSA keys are used, which could allow local and remote attackers to gain privileges.
CVE-2003-0725(PUBLISHED):Buffer overflow in the RTSP protocol parser for the View Source plug-in (vsrcplin.so or vsrcplin3260.dll) for RealNetworks Helix Universal Server 9 and RealSystem Server 8, 7 and RealServer G2 allows remote attackers to execute arbitrary code.
CVE-2003-0726(PUBLISHED):RealOne player allows remote attackers to execute arbitrary script in the "My Computer" zone via a SMIL presentation with a URL that references a scripting protocol, which is executed in the security context of the previously loaded URL, as demonstrated using a "javascript:" URL in the area tag.
CVE-2003-0727(PUBLISHED):Multiple buffer overflows in the XML Database (XDB) functionality for Oracle 9i Database Release 2 allow local users to cause a denial of service or hijack user sessions.
CVE-2003-0728(PUBLISHED):Horde before 2.2.4 allows remote malicious web sites to steal session IDs and read or create arbitrary email by stealing the ID from a referrer URL.
CVE-2003-0729(PUBLISHED):Buffer overflow in Tellurian TftpdNT 1.8 allows remote attackers to execute arbitrary code via a TFTP request with a long filename.
CVE-2003-0730(PUBLISHED):Multiple integer overflows in the font libraries for XFree86 4.3.0 allow local or remote attackers to cause a denial of service or execute arbitrary code via heap-based and stack-based buffer overflow attacks.
CVE-2003-0731(PUBLISHED):CiscoWorks Common Management Foundation (CMF) 2.1 and earlier allows the guest user to gain administrative privileges via a certain POST request to com.cisco.nm.cmf.servlet.CsAuthServlet, possibly involving the "cmd" parameter with a modifyUser value and a modified "priviledges" parameter.
CVE-2003-0732(PUBLISHED):CiscoWorks Common Management Foundation (CMF) 2.1 and earlier allows the guest user to obtain restricted information and possibly gain administrative privileges by changing the "guest" user to the Admin user on the Modify or delete users pages.
CVE-2003-0733(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in WebLogic Integration 7.0 and 2.0, Liquid Data 1.1, and WebLogic Server and Express 5.1 through 7.0, allow remote attackers to execute arbitrary web script and steal authentication credentials via (1) a forward instruction to the Servlet container or (2) other vulnerabilities in the WebLogic Server console application.
CVE-2003-0734(PUBLISHED):Unknown vulnerability in the pam_filter mechanism in pam_ldap before version 162, when LDAP based authentication is being used, allows users to bypass host-based access restrictions and log onto the system.
CVE-2003-0735(PUBLISHED):SQL injection vulnerability in the Calendar module of phpWebSite 0.9.x and earlier allows remote attackers to execute arbitrary SQL queries, as demonstrated using the year parameter.
CVE-2003-0736(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in phpWebSite 0.9.x and earlier allow remote attackers to execute arbitrary web script via (1) the day parameter in the calendar module, (2) the fatcat_id parameter in the fatcat module, (3) the PAGE_id parameter in the pagemaster module, (4) the PDA_limit parameter in the search, and (5) possibly other parameters in the calendar, fatcat, and pagemaster modules.
CVE-2003-0737(PUBLISHED):The calendar module in phpWebSite 0.9.x and earlier allows remote attackers to obtain the full pathname of phpWebSite via an invalid year, which generates an error from localtime() in TimeZone.php of the Pear library.
CVE-2003-0738(PUBLISHED):The calendar module in phpWebSite 0.9.x and earlier allows remote attackers to cause a denial of service (crash) via a long year parameter.
CVE-2003-0739(PUBLISHED):VMware Workstation 4.0.1 for Linux, build 5289 and earlier, allows local users to delete arbitrary files via a symlink attack.
CVE-2003-0740(PUBLISHED):Stunnel 4.00, and 3.24 and earlier, leaks a privileged file descriptor returned by listen(), which allows local users to hijack the Stunnel server.
CVE-2003-0742(PUBLISHED):SCO Internet Manager (mana) allows local users to execute arbitrary programs by setting the REMOTE_ADDR environment variable to cause menu.mana to run as if it were called from ncsa_httpd, then modifying the PATH environment variable to point to a malicious "hostname" program.
CVE-2003-0743(PUBLISHED):Heap-based buffer overflow in smtp_in.c for Exim 3 (exim3) before 3.36 and Exim 4 (exim4) before 4.21 may allow remote attackers to execute arbitrary code via an invalid (1) HELO or (2) EHLO argument with a large number of spaces followed by a NULL character and a newline, which is not properly trimmed before the "(no argument given)" string is appended to the buffer.
CVE-2003-0744(PUBLISHED):The fetchnews NNTP client in leafnode 1.9.3 to 1.9.41 allows remote attackers to cause a denial of service (process hang and termination) via certain malformed Usenet news articles that cause fetchnews to hang while waiting for input.
CVE-2003-0745(PUBLISHED):SNMPc 6.0.8 and earlier performs authentication to the server on the client side, which allows remote attackers to gain privileges by decrypting the password that is returned by the server.
CVE-2003-0746(PUBLISHED):Various Distributed Computing Environment (DCE) implementations, including HP OpenView, allow remote attackers to cause a denial of service (process hang or termination) via certain malformed inputs, as triggered by attempted exploits against the vulnerabilities CVE-2003-0352 or CVE-2003-0605, such as the Blaster/MSblast/LovSAN worm.
CVE-2003-0747(PUBLISHED):wgate.dll in SAP Internet Transaction Server (ITS) 4620.2.0.323011 allows remote attackers to obtain potentially sensitive information such as directory structure and operating system via incorrect parameters (1) ~service, (2) ~templatelanguage, (3) ~language, (4) ~theme, or (5) ~template, which leaks the information in the resulting error message.
CVE-2003-0748(PUBLISHED):Directory traversal vulnerability in wgate.dll for SAP Internet Transaction Server (ITS) 4620.2.0.323011 allows remote attackers to read arbitrary files via ..\ (dot-dot backslash) sequences in the ~theme parameter and a ~template parameter with a filename followed by space characters, which can prevent SAP from effectively adding a .html extension to the filename.
CVE-2003-0749(PUBLISHED):Cross-site scripting (XSS) vulnerability in wgate.dll for SAP Internet Transaction Server (ITS) 4620.2.0.323011 allows remote attackers to insert arbitrary web script and steal cookies via the ~service parameter.
CVE-2003-0750(PUBLISHED):secure.php in PY-Membres 4.2 and earlier allows remote attackers to bypass authentication by setting the adminpy parameter.
CVE-2003-0751(PUBLISHED):SQL injection vulnerability in pass_done.php for PY-Membres 4.2 and earlier allows remote attackers to execute arbitrary SQL queries via the email parameter.
CVE-2003-0752(PUBLISHED):SQL injection vulnerability in global.php3 of AttilaPHP 3.0, and possibly earlier versions, allows remote attackers to bypass authentication via a modified cook_id parameter.
CVE-2003-0753(PUBLISHED):nphpd.php in newsPHP 216 and earlier allows remote attackers to read arbitrary files via a full pathname to the target file in the nphp_config[LangFile] parameter.
CVE-2003-0754(PUBLISHED):nphpd.php in newsPHP 216 and earlier allows remote attackers to bypass authentication via an HTTP request with a modified nphp_users array, which is used for authentication.
CVE-2003-0755(PUBLISHED):Buffer overflow in sys_cmd.c for gtkftpd 1.0.4 and earlier allows remote attackers to execute arbitrary code by creating long directory names and listing them with a LIST command.
CVE-2003-0756(PUBLISHED):Directory traversal vulnerability in sitebuilder.cgi in SiteBuilder 1.4 allows remote attackers to read arbitrary files via .. (dot dot) sequences in the selectedpage parameter.
CVE-2003-0757(PUBLISHED):Check Point FireWall-1 4.0 and 4.1 before SP5 allows remote attackers to obtain the IP addresses of internal interfaces via certain SecuRemote requests to TCP ports 256 or 264, which leaks the IP addresses in a reply packet.
CVE-2003-0758(PUBLISHED):Buffer overflow in db2dart in IBM DB2 Universal Data Base 7.2 before Fixpak 10 allows local users to gain root privileges via a long command line argument.
CVE-2003-0759(PUBLISHED):Buffer overflow in db2licm in IBM DB2 Universal Data Base 7.2 before Fixpak 10a allows local users to gain root privileges via a long command line argument.
CVE-2003-0760(PUBLISHED):Blubster 2.5 allows remote attackers to cause a denial of service (crash) via a flood of connections to UDP port 701.
CVE-2003-0761(PUBLISHED):Buffer overflow in the get_msg_text of chan_sip.c in the Session Initiation Protocol (SIP) protocol implementation for Asterisk releases before August 15, 2003, allows remote attackers to execute arbitrary code via certain (1) MESSAGE or (2) INFO requests.
CVE-2003-0762(PUBLISHED):Buffer overflow in (1) foxweb.dll and (2) foxweb.exe of Foxweb 2.5 allows remote attackers to execute arbitrary code via a long URL (PATH_INFO value).
CVE-2003-0763(PUBLISHED):Cross-site scripting (XSS) vulnerability in Escapade Scripting Engine (ESP) allows remote attackers to inject arbitrary script via the method parameter, as demonstrated using the PAGE parameter.
CVE-2003-0764(PUBLISHED):Escapade Scripting Engine (ESP) allows remote attackers to obtain sensitive path information via a malformed request, which leaks the information in an error message, as demonstrated using the PAGE parameter.
CVE-2003-0765(PUBLISHED):The IN_MIDI.DLL plugin 3.01 and earlier, as used in Winamp 2.91, allows remote attackers to execute arbitrary code via a MIDI file with a large "Track data size" value.
CVE-2003-0766(PUBLISHED):Multiple heap-based buffer overflows in FTP Desktop client 3.5, and possibly earlier versions, allow remote malicious servers to execute arbitrary code via (1) a long FTP banner, (2) a long response to a USER command, or (3) a long response to a PASS command.
CVE-2003-0767(PUBLISHED):Buffer overflow in RogerWilco graphical server ******* and earlier, dedicated server 0.32a and earlier for Windows, and 0.27 and earlier for Linux and BSD, allows remote attackers to cause a denial of service and execute arbitrary code via a client request with a large length value.
CVE-2003-0768(PUBLISHED):Microsoft ASP.Net 1.1 allows remote attackers to bypass the Cross-Site Scripting (XSS) and Script Injection protection feature via a null character in the beginning of a tag name.
CVE-2003-0769(PUBLISHED):Cross-site scripting (XSS) vulnerability in the ICQ Web Front guestbook (guestbook.html) allows remote attackers to insert arbitrary web script and HTML via the message field.
CVE-2003-0770(PUBLISHED):FUNC.pm in IkonBoard 3.1.2a and earlier, including 3.1.1, does not properly cleanse the "lang" cookie when it contains illegal characters, which allows remote attackers to execute arbitrary code when the cookie is inserted into a Perl "eval" statement.
CVE-2003-0771(PUBLISHED):Gallery.pm in Apache::Gallery (aka A::G) uses predictable temporary filenames when running Inline::C, which allows local users to execute arbitrary code by creating and modifying the files before Apache::Gallery does.
CVE-2003-0772(PUBLISHED):Multiple buffer overflows in WS_FTP 3 and 4 allow remote authenticated users to cause a denial of service and possibly execute arbitrary code via long (1) APPE (append) or (2) STAT (status) arguments.
CVE-2003-0773(PUBLISHED):saned in sane-backends 1.0.7 and earlier does not check the IP address of the connecting host during the SANE_NET_INIT RPC call, which allows remote attackers to use that call even if they are restricted in saned.conf.
CVE-2003-0774(PUBLISHED):saned in sane-backends 1.0.7 and earlier does not quickly handle connection drops, which allows remote attackers to cause a denial of service (segmentation fault) when invalid memory is accessed.
CVE-2003-0775(PUBLISHED):saned in sane-backends 1.0.7 and earlier calls malloc with an arbitrary size value if a connection is dropped before the size value has been sent, which allows remote attackers to cause a denial of service (memory consumption or crash).
CVE-2003-0776(PUBLISHED):saned in sane-backends 1.0.7 and earlier does not properly "check the validity of the RPC numbers it gets before getting the parameters," with unknown consequences.
CVE-2003-0777(PUBLISHED):saned in sane-backends 1.0.7 and earlier, when debug messages are enabled, does not properly handle dropped connections, which can prevent strings from being null terminated and cause a denial of service (segmentation fault).
CVE-2003-0778(PUBLISHED):saned in sane-backends 1.0.7 and earlier, and possibly later versions, does not properly allocate memory in certain cases, which could allow attackers to cause a denial of service (memory consumption).
CVE-2003-0779(PUBLISHED):SQL injection vulnerability in the Call Detail Record (CDR) logging functionality for Asterisk allows remote attackers to execute arbitrary SQL via a CallerID string.
CVE-2003-0780(PUBLISHED):Buffer overflow in get_salt_from_password from sql_acl.cc for MySQL 4.0.14 and earlier, and 3.23.x, allows attackers with ALTER TABLE privileges to execute arbitrary code via a long Password field.
CVE-2003-0781(PUBLISHED):Unknown vulnerability in ecartis before 1.0.0 does not properly validate user input, which allows attackers to obtain mailing list passwords.
CVE-2003-0782(PUBLISHED):Multiple buffer overflows in ecartis before 1.0.0 allow attackers to cause a denial of service and possibly execute arbitrary code.
CVE-2003-0783(PUBLISHED):Multiple buffer overflows in hztty 2.0 allow local users to gain root privileges.
CVE-2003-0784(PUBLISHED):Format string vulnerability in tsm for the bos.rte.security fileset on AIX 5.2 allows remote attackers to gain root privileges via login, and local users to gain privileges via login, su, or passwd, with a username that contains format string specifiers.
CVE-2003-0785(PUBLISHED):ipmasq before 3.5.12, in certain configurations, may forward packets to the external interface even if the packets are not associated with an established connection, which could allow remote attackers to bypass intended filtering.
CVE-2003-0786(PUBLISHED):The SSH1 PAM challenge response authentication in OpenSSH 3.7.1 and 3.7.1p1, when Privilege Separation is disabled, does not check the result of the authentication attempt, which can allow remote attackers to gain privileges.
CVE-2003-0787(PUBLISHED):The PAM conversation function in OpenSSH 3.7.1 and 3.7.1p1 interprets an array of structures as an array of pointers, which allows attackers to modify the stack and possibly gain privileges.
CVE-2003-0788(PUBLISHED):Unknown vulnerability in the Internet Printing Protocol (IPP) implementation in CUPS before 1.1.19 allows remote attackers to cause a denial of service (CPU consumption from a "busy loop") via certain inputs to the IPP port (TCP 631).
CVE-2003-0789(PUBLISHED):mod_cgid in Apache before 2.0.48, when using a threaded MPM, does not properly handle CGI redirect paths, which could cause Apache to send the output of a CGI program to the wrong client.
CVE-2003-0791(PUBLISHED):The Script.prototype.freeze/thaw functionality in Mozilla 1.4 and earlier allows attackers to execute native methods by modifying the string used as input to the script.thaw JavaScript function, which is then deserialized and executed.
CVE-2003-0792(PUBLISHED):Fetchmail 6.2.4 and earlier does not properly allocate memory for long lines, which allows remote attackers to cause a denial of service (crash) via a certain email.
CVE-2003-0793(PUBLISHED):GDM 2.4.4.x before *******, and 2.4.1.x before *******, does not restrict the size of input, which allows attackers to cause a denial of service (memory consumption).
CVE-2003-0794(PUBLISHED):GDM 2.4.4.x before *******, and 2.4.1.x before *******, does not limit the number or duration of commands and uses a blocking socket connection, which allows attackers to cause a denial of service (resource exhaustion) by sending commands and not reading the results.
CVE-2003-0795(PUBLISHED):The vty layer in Quagga before 0.96.4, and Zebra 0.93b and earlier, does not verify that sub-negotiation is taking place when processing the SE marker, which allows remote attackers to cause a denial of service (crash) via a malformed telnet command to the telnet CLI port, which may trigger a null dereference.
CVE-2003-0796(PUBLISHED):Unknown vulnerability in rpc.mountd SGI IRIX 6.5.18 through 6.5.22 allows remote attackers to mount from unprivileged ports even with the -n option disabled.
CVE-2003-0797(PUBLISHED):Unknown vulnerability in rpc.mountd in SGI IRIX 6.5 through 6.5.22 allows remote attackers to cause a denial of service (process death) via unknown attack vectors.
CVE-2003-0801(PUBLISHED):Cross-site scripting (XSS) vulnerability in Nokia Electronic Documentation (NED) 5.0 allows remote attackers to execute arbitrary web script and steal cookies via a URL to the docs/ directory that contains the script.
CVE-2003-0802(PUBLISHED):Nokia Electronic Documentation (NED) 5.0 allows remote attackers to obtain a directory listing of the WebLogic web root, and the physical path of the NED server, via a "retrieve" action with a location parameter of . (dot).
CVE-2003-0803(PUBLISHED):Nokia Electronic Documentation (NED) 5.0 allows remote attackers to use NED as an open HTTP proxy via a URL in the location parameter, which NED accesses and returns to the user.
CVE-2003-0804(PUBLISHED):The arplookup function in FreeBSD 5.1 and earlier, Mac OS X before 10.2.8, and possibly other BSD-based systems, allows remote attackers on a local subnet to cause a denial of service (resource starvation and panic) via a flood of spoofed ARP requests.
CVE-2003-0805(PUBLISHED):Multiple buffer overflows in UMN gopher daemon (gopherd) 2.x and 3.x before 3.0.6 allows attackers to execute arbitrary code via (1) a long filename as a result of a LIST command, and (2) the GSisText function, which calculates the view-type.
CVE-2003-0806(PUBLISHED):Buffer overflow in the Windows logon process (winlogon) in Microsoft Windows NT 4.0 SP6a, 2000 SP2 through SP4, and XP SP1, when a member of a domain, allows remote attackers to execute arbitrary code.
CVE-2003-0807(PUBLISHED):Buffer overflow in the COM Internet Services and in the RPC over HTTP Proxy components for Microsoft Windows NT Server 4.0, NT 4.0 Terminal Server Edition, 2000, XP, and Server 2003 allows remote attackers to cause a denial of service via a crafted request.
CVE-2003-0809(PUBLISHED):Internet Explorer 5.01 through 6.0 does not properly handle object tags returned from a Web server during XML data binding, which allows remote attackers to execute arbitrary code via an HTML e-mail message or web page.
CVE-2003-0812(PUBLISHED):Stack-based buffer overflow in a logging function for Windows Workstation Service (WKSSVC.DLL) allows remote attackers to execute arbitrary code via RPC calls that cause long entries to be written to a debug log file ("NetSetup.LOG"), as demonstrated using the NetAddAlternateComputerName API.
CVE-2003-0813(PUBLISHED):A multi-threaded race condition in the Windows RPC DCOM functionality with the MS03-039 patch installed allows remote attackers to cause a denial of service (crash or reboot) by causing two threads to process the same RPC request, which causes one thread to use memory after it has been freed, a different vulnerability than CVE-2003-0352 (Blaster/Nachi), CVE-2003-0715, and CVE-2003-0528, and as demonstrated by certain exploits against those vulnerabilities.
CVE-2003-0814(PUBLISHED):Internet Explorer 6 SP1 and earlier allows remote attackers to bypass zone restrictions and execute Javascript by setting the window's "href" to the malicious Javascript, then calling execCommand("Refresh") to refresh the page, aka BodyRefreshLoadsJPU or the "ExecCommand Cross Domain" vulnerability.
CVE-2003-0815(PUBLISHED):Internet Explorer 6 SP1 and earlier allows remote attackers to bypass zone restrictions and read arbitrary files by (1) modifying the createTextRange method and using CreateLink, as demonstrated using LinkillerSaveRef, LinkillerJPU, and Linkiller, or (2) modifying the createRange method and using the FIND dialog to select text, as demonstrated using Findeath, aka the "Function Pointer Override Cross Domain" vulnerability.
CVE-2003-0816(PUBLISHED):Internet Explorer 6 SP1 and earlier allows remote attackers to bypass zone restrictions by (1) using the NavigateAndFind method to load a file: URL containing Javascript, as demonstrated by NAFfileJPU, (2) using the window.open method to load a file: URL containing Javascript, as demonstrated using WsOpenFileJPU, (3) setting the href property in the base tag for the _search window, as demonstrated using WsBASEjpu, (4) loading the search window into an Iframe, as demonstrated using WsFakeSrc, (5) caching a javascript: URL in the browser history, then accessing that URL in the same frame as the target domain, as demonstrated using WsOpenJpuInHistory, NAFjpuInHistory, BackMyParent, BackMyParent2, and RefBack, aka the "Script URLs Cross Domain" vulnerability.
CVE-2003-0817(PUBLISHED):Internet Explorer 5.01 through 6 SP1 allows remote attackers to bypass zone restrictions and read arbitrary files via an XML object.
CVE-2003-0818(PUBLISHED):Multiple integer overflows in Microsoft ASN.1 library (MSASN1.DLL), as used in LSASS.EXE, CRYPT32.DLL, and other Microsoft executables and libraries on Windows NT 4.0, 2000, and XP, allow remote attackers to execute arbitrary code via ASN.1 BER encodings with (1) very large length fields that cause arbitrary heap data to be overwritten, or (2) modified bit strings.
CVE-2003-0819(PUBLISHED):Buffer overflow in the H.323 filter of Microsoft Internet Security and Acceleration Server 2000 allows remote attackers to execute arbitrary code in the Microsoft Firewall Service via certain H.323 traffic, as demonstrated by the NISCC/OUSPG PROTOS test suite for the H.225 protocol.
CVE-2003-0820(PUBLISHED):Microsoft Word 97, 98(J), 2000, and 2002, and Microsoft Works Suites 2001 through 2004, do not properly check the length of the "Macro names" data value, which could allow remote attackers to execute arbitrary code via a buffer overflow attack.
CVE-2003-0821(PUBLISHED):Microsoft Excel 97, 2000, and 2002 allows remote attackers to execute arbitrary code via a spreadsheet with a malicious XLM (Excel 4) macro that bypasses the macro security model.
CVE-2003-0822(PUBLISHED):Buffer overflow in the debug functionality in fp30reg.dll of Microsoft FrontPage Server Extensions (FPSE) 2000 and 2002 allows remote attackers to execute arbitrary code via a crafted chunked encoded request.
CVE-2003-0823(PUBLISHED):Internet Explorer 6 SP1 and earlier allows remote attackers to direct drag and drop behaviors and other mouse click actions to other windows by calling the window.moveBy method, aka HijackClick, a different vulnerability than CVE-2003-1027.
CVE-2003-0824(PUBLISHED):Unknown vulnerability in the SmartHTML interpreter (shtml.dll) in Microsoft FrontPage Server Extensions 2000 and 2002, and Microsoft SharePoint Team Services 2002, allows remote attackers to cause a denial of service (response failure) via a certain request.
CVE-2003-0825(PUBLISHED):The Windows Internet Naming Service (WINS) for Microsoft Windows Server 2003, and possibly Windows NT and Server 2000, does not properly validate the length of certain packets, which allows attackers to cause a denial of service and possibly execute arbitrary code.
CVE-2003-0826(PUBLISHED):lsh daemon (lshd) does not properly return from certain functions in (1) read_line.c, (2) channel_commands.c, or (3) client_keyexchange.c when long input is provided, which could allow remote attackers to execute arbitrary code via a heap-based buffer overflow attack.
CVE-2003-0827(PUBLISHED):The DB2 Discovery Service for IBM DB2 before FixPak 10a allows remote attackers to cause a denial of service (crash) via a long packet to UDP port 523.
CVE-2003-0828(PUBLISHED):Buffer overflow in freesweep in Debian GNU/Linux 3.0 allows local users to gain "games" group privileges when processing environment variables.
CVE-2003-0830(PUBLISHED):Buffer overflow in marbles 1.0.2 and earlier allows local users to gain privileges via a long HOME environment variable.
CVE-2003-0831(PUBLISHED):ProFTPD 1.2.7 through 1.2.9rc2 does not properly translate newline characters when transferring files in ASCII mode, which allows remote attackers to execute arbitrary code via a buffer overflow using certain files.
CVE-2003-0832(PUBLISHED):Directory traversal vulnerability in webfs before 1.20 allows remote attackers to read arbitrary files via .. (dot dot) sequences in a Hostname header.
CVE-2003-0833(PUBLISHED):Stack-based buffer overflow in webfs before 1.20 allows attackers to execute arbitrary code by creating directories that result in a long pathname.
CVE-2003-0834(PUBLISHED):Buffer overflow in CDE libDtHelp library allows local users to execute arbitrary code via (1) a modified DTHELPUSERSEARCHPATH environment variable and the Help feature, (2) DTSEARCHPATH, or (3) LOGNAME.
CVE-2003-0835(PUBLISHED):Multiple buffer overflows in asf_http_request of MPlayer before 0.92 allows remote attackers to execute arbitrary code via an ASX header with a long hostname.
CVE-2003-0836(PUBLISHED):Stack-based buffer overflow in IBM DB2 Universal Data Base 7.2 before Fixpak 10 and 10a, and 8.1 before Fixpak 2, allows attackers with "Connect" privileges to execute arbitrary code via a LOAD command.
CVE-2003-0837(PUBLISHED):Stack-based buffer overflow in IBM DB2 Universal Data Base 7.2 for Windows, before Fixpak 10a, allows attackers with "Connect" privileges to execute arbitrary code via the INVOKE command.
CVE-2003-0838(PUBLISHED):Internet Explorer allows remote attackers to bypass zone restrictions to inject and execute arbitrary programs by creating a popup window and inserting ActiveX object code with a "data" tag pointing to the malicious code, which Internet Explorer treats as HTML or Javascript, but later executes as an HTA application, a different vulnerability than CVE-2003-0532, and as exploited using the QHosts Trojan horse (aka Trojan.Qhosts, QHosts-1, VBS.QHOSTS, or aolfix.exe).
CVE-2003-0839(PUBLISHED):Directory traversal vulnerability in the "Shell Folders" capability in Microsoft Windows Server 2003 allows remote attackers to read arbitrary files via .. (dot dot) sequences in a "shell:" link.
CVE-2003-0840(PUBLISHED):Buffer overflow in dtprintinfo on HP-UX 11.00, and possibly other operating systems, allows local users to gain root privileges via a long DISPLAY environment variable.
CVE-2003-0841(PUBLISHED):The grid option in PeopleSoft 8.42 stores temporary .xls files in guessable directories under the web document root, which allows remote attackers to steal search results by directly accessing the files via a URL request.
CVE-2003-0842(PUBLISHED):Stack-based buffer overflow in mod_gzip_printf for mod_gzip 1.3.26.1a and earlier, and possibly later official versions, when running in debug mode, allows remote attackers to execute arbitrary code via a long filename in a GET request with an "Accept-Encoding: gzip" header.
CVE-2003-0843(PUBLISHED):Format string vulnerability in mod_gzip_printf for mod_gzip 1.3.26.1a and earlier, and possibly later official versions, when running in debug mode and using the Apache log, allows remote attackers to execute arbitrary code via format string characters in an HTTP GET request with an "Accept-Encoding: gzip" header.
CVE-2003-0844(PUBLISHED):mod_gzip 1.3.26.1a and earlier, and possibly later official versions, when running in debug mode without the Apache log, allows local users to overwrite arbitrary files via (1) a symlink attack on predictable temporary filenames on Unix systems, or (2) an NTFS hard link on Windows systems when the "Strengthen default permissions of internal system objects" policy is not enabled.
CVE-2003-0845(PUBLISHED):Unknown vulnerability in the HSQLDB component in JBoss 3.2.1 and 3.0.8 on Java 1.4.x platforms, when running in the default configuration, allows remote attackers to conduct unauthorized activities and possibly execute arbitrary code via certain SQL statements to (1) TCP port 1701 in JBoss 3.2.1, and (2) port 1476 in JBoss 3.0.8.
CVE-2003-0846(PUBLISHED):SuSEconfig.javarunt in the javarunt package on SuSE Linux 7.3Pro allows local users to overwrite arbitrary files via a symlink attack on the .java_wrapper temporary file.
CVE-2003-0847(PUBLISHED):SuSEconfig.susewm in the susewm package on SuSE Linux 8.2Pro allows local users to overwrite arbitrary files via a symlink attack on the susewm.$$ temporary file.
CVE-2003-0848(PUBLISHED):Heap-based buffer overflow in main.c of slocate 2.6, and possibly other versions, may allow local users to gain privileges via a modified slocate database that causes a negative "pathlen" value to be used.
CVE-2003-0849(PUBLISHED):Buffer overflow in net.c for cfengine 2.x before 2.0.8 allows remote attackers to execute arbitrary code via certain packets with modified length values, which is trusted by the ReceiveTransaction function when using a buffer provided by the BusyWithConnection function.
CVE-2003-0850(PUBLISHED):The TCP reassembly functionality in libnids before 1.18 allows remote attackers to cause "memory corruption" and possibly execute arbitrary code via "overlarge TCP packets."
CVE-2003-0851(PUBLISHED):OpenSSL 0.9.6k allows remote attackers to cause a denial of service (crash via large recursion) via malformed ASN.1 sequences.
CVE-2003-0852(PUBLISHED):Format string vulnerability in send_message.c for Sylpheed-claws 0.9.4 through 0.9.6 allows remote SMTP servers to cause a denial of service (crash) in sylpheed via format strings in an error message.
CVE-2003-0853(PUBLISHED):An integer overflow in ls in the fileutils or coreutils packages may allow local users to cause a denial of service or execute arbitrary code via a large -w value, which could be remotely exploited via applications that use ls, such as wu-ftpd.
CVE-2003-0854(PUBLISHED):ls in the fileutils or coreutils packages allows local users to consume a large amount of memory via a large -w value, which can be remotely exploited via applications that use ls, such as wu-ftpd.
CVE-2003-0855(PUBLISHED):Pan 0.13.3 and earlier allows remote attackers to cause a denial of service (crash) via a news post with a long author email address.
CVE-2003-0856(PUBLISHED):iproute 2.4.7 and earlier allows local users to cause a denial of service via spoofed messages as other users to the kernel netlink interface.
CVE-2003-0857(PUBLISHED):The (1) ipq_read and (2) ipulog_read functions in iptables allow local users to cause a denial of service by sending spoofed messages as other users to the kernel netlink interface.
CVE-2003-0858(PUBLISHED):Zebra 0.93b and earlier, and quagga before 0.95, allows local users to cause a denial of service by sending spoofed messages as other users to the kernel netlink interface.
CVE-2003-0859(PUBLISHED):The getifaddrs function in GNU libc (glibc) 2.2.4 and earlier allows local users to cause a denial of service by sending spoofed messages as other users to the kernel netlink interface.
CVE-2003-0860(PUBLISHED):Buffer overflows in PHP before 4.3.3 have unknown impact and unknown attack vectors.
CVE-2003-0861(PUBLISHED):Integer overflows in (1) base64_encode and (2) the GD library for PHP before 4.3.3 have unknown impact and unknown attack vectors.
CVE-2003-0863(PUBLISHED):The php_check_safe_mode_include_dir function in fopen_wrappers.c of PHP 4.3.x returns a success value (0) when the safe_mode_include_dir variable is not specified in configuration, which differs from the previous failure value and may allow remote attackers to exploit file include vulnerabilities in PHP applications.
CVE-2003-0864(PUBLISHED):Buffer overflow in m_join in channel.c for IRCnet IRCD 2.10.x to 2.10.3p3 allows remote attackers to cause a denial of service.
CVE-2003-0865(PUBLISHED):Heap-based buffer overflow in readstring of httpget.c for mpg123 0.59r and 0.59s allows remote attackers to execute arbitrary code via a long request.
CVE-2003-0866(PUBLISHED):The Catalina org.apache.catalina.connector.http package in Tomcat 4.0.x up to 4.0.3 allows remote attackers to cause a denial of service via several requests that do not follow the HTTP protocol, which causes Tomcat to reject later requests.
CVE-2003-0870(PUBLISHED):Heap-based buffer overflow in Opera 7.11 and 7.20 allows remote attackers to execute arbitrary code via an HREF with a large number of escaped characters in the server name.
CVE-2003-0871(PUBLISHED):Unknown vulnerability in QuickTime Java in Mac OS X v10.3 and Mac OS X Server 10.3 allows attackers to gain "unauthorized access to a system."
CVE-2003-0872(PUBLISHED):Certain scripts in OpenServer before 5.0.6 allow local users to overwrite files and conduct other unauthorized activities via a symlink attack on temporary files.
CVE-2003-0874(PUBLISHED):Multiple SQL injection vulnerabilities in DeskPRO 1.1.0 and earlier allow remote attackers to insert arbitrary SQL and conduct unauthorized activities via (1) the cat parameter in faq.php, (2) the article parameter in faq.php, (3) the tickedid parameter in view.php, and (4) the Password entry on the logon screen.
CVE-2003-0875(PUBLISHED):Symbolic link vulnerability in the slpd script slpd.all_init for OpenSLP before 1.0.11 allows local users to overwrite arbitrary files via the route.check temporary file.
CVE-2003-0876(PUBLISHED):Finder in Mac OS X 10.2.8 and earlier sets global read/write/execute permissions on directories when they are dragged (copied) from a mounted volume such as a disk image (DMG), which could cause the directories to have less restrictive permissions than intended.
CVE-2003-0877(PUBLISHED):Mac OS X before 10.3 with core files enabled allows local users to overwrite arbitrary files and read core files via a symlink attack on core files that are created with predictable names in the /cores directory.
CVE-2003-0878(PUBLISHED):slpd daemon in Mac OS X before 10.3 allows local users to overwrite arbitrary files via a symlink attack on a temporary file, a different vulnerability than CVE-2003-0875.
CVE-2003-0880(PUBLISHED):Unknown vulnerability in Mac OS X before 10.3 allows local users to access Dock functions from behind Screen Effects when Full Keyboard Access is enabled using the Keyboard pane in System Preferences.
CVE-2003-0881(PUBLISHED):Mail in Mac OS X before 10.3, when configured to use MD5 Challenge Response, uses plaintext authentication if the CRAM-MD5 hashed login fails, which could allow remote attackers to gain privileges by sniffing the password.
CVE-2003-0882(PUBLISHED):Mac OS X before 10.3 initializes the TCP timestamp with a constant number, which allows remote attackers to determine the system's uptime via the ID field in a TCP packet.
CVE-2003-0883(PUBLISHED):The System Preferences capability in Mac OS X before 10.3 allows local users to access secure Preference Panes for a short period after an administrator has authenticated to the system.
CVE-2003-0885(PUBLISHED):Xscreensaver 4.14 contains certain debugging code that should have been omitted, which causes Xscreensaver to create temporary files insecurely in the (1) apple2, (2) xanalogtv, and (3) pong screensavers, and allows local users to overwrite arbitrary files via a symlink attack.
CVE-2003-0886(PUBLISHED):Format string vulnerability in hfaxd for Hylafax 4.1.7 and earlier allows remote attackers to execute arbitrary code.
CVE-2003-0887(PUBLISHED):ez-ipupdate 3.0.11b7 and earlier creates insecure temporary cache files, which allows local users to conduct unauthorized operations via a symlink attack on the ez-ipupdate.cache file.
CVE-2003-0894(PUBLISHED):Buffer overflow in the (1) oracle and (2) oracleO programs in Oracle 9i Database 9.0.x and 9.2.x before ******* allows local users to execute arbitrary code via a long command line argument.
CVE-2003-0895(PUBLISHED):Buffer overflow in the Mac OS X kernel 10.2.8 and earlier allows local users, and possibly remote attackers, to cause a denial of service (crash), access portions of memory, and possibly execute arbitrary code via a long command line argument (argv[]).
CVE-2003-0896(PUBLISHED):The loadClass method of the sun.applet.AppletClassLoader class in the Java Virtual Machine (JVM) in Sun SDK and JRE 1.4.1_03 and earlier allows remote attackers to bypass sandbox restrictions and execute arbitrary code via a loaded class name that contains "/" (slash) instead of "." (dot) characters, which bypasses a call to the Security Manager's checkPackageAccess method.
CVE-2003-0897(PUBLISHED):"Shatter" vulnerability in CommCtl32.dll in Windows XP may allow local users to execute arbitrary code by sending (1) BCM_GETTEXTMARGIN or (2) BCM_SETTEXTMARGIN button control messages to privileged applications.
CVE-2003-0898(PUBLISHED):IBM DB2 7.2 before FixPak 10a, and earlier versions including 7.1, allows local users to overwrite arbitrary files and gain privileges via a symlink attack on (1) db2job and (2) db2job2.
CVE-2003-0899(PUBLISHED):Buffer overflow in defang in libhttpd.c for thttpd 2.21 to 2.23b1 allows remote attackers to execute arbitrary code via requests that contain '<' or '>' characters, which trigger the overflow when the characters are expanded to "&lt;" and "&gt;" sequences.
CVE-2003-0900(PUBLISHED):Perl 5.8.1 on Fedora Core does not properly initialize the random number generator when forking, which makes it easier for attackers to predict random numbers.
CVE-2003-0901(PUBLISHED):Buffer overflow in to_ascii for PostgreSQL 7.2.x, and 7.3.x before 7.3.4, allows remote attackers to execute arbitrary code.
CVE-2003-0902(PUBLISHED):Unknown vulnerability in minimalist mailing list manager 2.4, 2.2, and possibly other versions, allows remote attackers to execute arbitrary commands.
CVE-2003-0903(PUBLISHED):Buffer overflow in a component of Microsoft Data Access Components (MDAC) 2.5 through 2.8 allows remote attackers to execute arbitrary code via a malformed UDP response to a broadcast request.
CVE-2003-0904(PUBLISHED):Microsoft Exchange 2003 and Outlook Web Access (OWA), when configured to use NTLM authentication, does not properly reuse HTTP connections, which can cause OWA users to view mailboxes of other users when Kerberos has been disabled as an authentication method for IIS 6.0, e.g. when SharePoint Services 2.0 is installed.
CVE-2003-0905(PUBLISHED):Unknown vulnerability in Windows Media Station Service and Windows Media Monitor Service components of Windows Media Services 4.1 allows remote attackers to cause a denial of service (disallowing new connections) via a certain sequence of TCP/IP packets.
CVE-2003-0906(PUBLISHED):Buffer overflow in the rendering for (1) Windows Metafile (WMF) or (2) Enhanced Metafile (EMF) image formats in Microsoft Windows NT 4.0 SP6a, 2000 SP2 through SP4, and XP SP1 allows remote attackers to execute arbitrary code via a malformed WMF or EMF image.
CVE-2003-0907(PUBLISHED):Help and Support Center in Microsoft Windows XP SP1 does not properly validate HCP URLs, which allows remote attackers to execute arbitrary code via quotation marks in an hcp:// URL, which are not quoted when constructing the argument list to HelpCtr.exe.
CVE-2003-0908(PUBLISHED):The Utility Manager in Microsoft Windows 2000 executes winhlp32.exe with system privileges, which allows local users to execute arbitrary code via a "Shatter" style attack using a Windows message that accesses the context sensitive help button in the GUI, as demonstrated using the File Open dialog in the Help window, a different vulnerability than CVE-2004-0213.
CVE-2003-0909(PUBLISHED):Windows XP allows local users to execute arbitrary programs by creating a task at an elevated privilege level through the eventtriggers.exe command-line tool or the Task Scheduler service, aka "Windows Management Vulnerability."
CVE-2003-0910(PUBLISHED):The NtSetLdtEntries function in the programming interface for the Local Descriptor Table (LDT) in Windows NT 4.0 and Windows 2000 allows local attackers to gain access to kernel memory and execute arbitrary code via an expand-down data segment descriptor descriptor that points to protected memory.
CVE-2003-0913(PUBLISHED):Unknown vulnerability in the Terminal application for Mac OS X 10.3 (Client and Server) may allow "unauthorized access."
CVE-2003-0914(PUBLISHED):ISC BIND 8.3.x before 8.3.7, and 8.4.x before 8.4.3, allows remote attackers to poison the cache via a malicious name server that returns negative responses with a large TTL (time-to-live) value.
CVE-2003-0924(PUBLISHED):netpbm 9.25 and earlier does not properly create temporary files, which allows local users to overwrite arbitrary files.
CVE-2003-0925(PUBLISHED):Buffer overflow in Ethereal 0.9.15 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via a malformed GTP MSISDN string.
CVE-2003-0926(PUBLISHED):Ethereal 0.9.15 and earlier, and Tethereal, allows remote attackers to cause a denial of service (crash) via certain malformed (1) ISAKMP or (2) MEGACO packets.
CVE-2003-0927(PUBLISHED):Heap-based buffer overflow in Ethereal 0.9.15 and earlier allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via the SOCKS dissector.
CVE-2003-0928(PUBLISHED):Clearswift MAILsweeper before 4.3.15 does not properly detect and filter RAR 3.20 encoded files, which allows remote attackers to bypass intended policy.
CVE-2003-0929(PUBLISHED):Clearswift MAILsweeper before 4.3.15 does not properly detect and filter ZIP 6.0 encoded files, which allows remote attackers to bypass intended policy.
CVE-2003-0930(PUBLISHED):Clearswift MAILsweeper before 4.3.15 does not properly detect filenames in BinHex (HQX) encoded files, which allows remote attackers to bypass intended policy.
CVE-2003-0931(PUBLISHED):Sygate Enforcer 4.0 earlier allows remote attackers to cause a denial of service (service hang) by replaying a malformed discovery packet to UDP port 39999.
CVE-2003-0932(PUBLISHED):Buffer overflow in omega-rpg 0.90 allows local users to execute arbitrary code via a long (1) command line or (2) environment variable.
CVE-2003-0933(PUBLISHED):Buffer overflow in conquest 7.2 and earlier may allow a local user to execute arbitrary code via a long environment variable.
CVE-2003-0934(PUBLISHED):Symbol Access Portable Data Terminal (PDT) 8100 does not hide the default WEP keys if they are not changed, which could allow attackers to retrieve the keys and gain access to the wireless network.
CVE-2003-0935(PUBLISHED):Net-SNMP before 5.0.9 allows a user or community to access data in MIB objects, even if that data is not allowed to be viewed.
CVE-2003-0936(PUBLISHED):Symantec PCAnywhere 10.x and 11, when started as a service, allows attackers to gain SYSTEM privileges via the help interface using AWHOST32.exe.
CVE-2003-0937(PUBLISHED):SCO UnixWare 7.1.1, 7.1.3, and Open UNIX 8.0.0 allows local users to bypass protections for the "as" address space file for a process ID (PID) by obtaining a procfs file descriptor for the file and calling execve() on a setuid or setgid program, which leaves the descriptor open to the user.
CVE-2003-0938(PUBLISHED):vos24u.c in SAP database server (SAP DB) ********* and earlier allows local users to gain SYSTEM privileges via a malicious "NETAPI32.DLL" in the current working directory, which is found and loaded by SAP DB before the real DLL, as demonstrated using the SQLAT stored procedure.
CVE-2003-0939(PUBLISHED):eo420_GetStringFromVarPart in veo420.c for SAP database server (SAP DB) ********* and earlier may allow remote attackers to execute arbitrary code via a connect packet with a 256 byte segment to the niserver (aka serv.exe) process on TCP port 7269, which prevents the server from NULL terminating the string and leads to a buffer overflow.
CVE-2003-0940(PUBLISHED):Directory traversal vulnerability in sqlfopenc for web-tools in SAP DB before ********* allows remote attackers to read arbitrary files via .. (dot dot) sequences in a URL.
CVE-2003-0941(PUBLISHED):web-tools in SAP DB before ********* allows remote attackers to access the Web Agent Administration pages and modify configuration via a direct request to waadmin.wa.
CVE-2003-0942(PUBLISHED):Buffer overflow in Web Agent Administration service in web-tools for SAP DB before ********* allows remote attackers to execute arbitrary code via a long Name parameter to waadmin.wa.
CVE-2003-0943(PUBLISHED):web-tools in SAP DB before ********* installs several services that are enabled by default, which could allow remote attackers to obtain potentially sensitive information or redirect attacks against internal databases via (1) waecho, (2) Web SQL Interface (websql), or (3) Web Database Manager (webdbm).
CVE-2003-0944(PUBLISHED):Buffer overflow in the WAECHO default service in web-tools in SAP DB before ********* allows remote attackers to execute arbitrary code via a URL with a long requestURI.
CVE-2003-0945(PUBLISHED):The Web Database Manager in web-tools for SAP DB before ********* generates predictable session IDs, which allows remote attackers to conduct unauthorized activities.
CVE-2003-0946(PUBLISHED):Format string vulnerability in clamav-milter for Clam AntiVirus 0.60 through 0.60p, and other versions before 0.65, allows remote attackers to cause a denial of service and possibly execute arbitrary code via format string specifiers in the email address argument of a "MAIL FROM" command.
CVE-2003-0947(PUBLISHED):Buffer overflow in iwconfig, when installed setuid, allows local users to execute arbitrary code via a long OUT environment variable.
CVE-2003-0948(PUBLISHED):Buffer overflow in iwconfig allows local users to execute arbitrary code via a long HOME environment variable.
CVE-2003-0949(PUBLISHED):xsok 1.02 does not properly drop privileges before finding and executing the "gunzip" program, which allows local users to execute arbitrary commands.
CVE-2003-0950(PUBLISHED):PeopleSoft PeopleTools 8.1x, 8.2x, and 8.4x allows remote attackers to execute arbitrary commands by uploading a file to the IClient Servlet, guessing the insufficiently random (system time) name of the directory used to store the file, and directly requesting that file.
CVE-2003-0951(PUBLISHED):Partition Manager (parmgr) in HP-UX B.11.23 does not properly validate certificates that are provided by the cimserver, which allows attackers to obtain sensitive data or gain privileges.
CVE-2003-0954(PUBLISHED):Buffer overflow in rcp for AIX 4.3.3, 5.1 and 5.2 allows local users to gain privileges.
CVE-2003-0955(PUBLISHED):OpenBSD kernel 3.3 and 3.4 allows local users to cause a denial of service (kernel panic) and possibly execute arbitrary code in 3.4 via a program with an invalid header that is not properly handled by (1) ibcs2_exec.c in the iBCS2 emulation (compat_ibcs2) or (2) exec_elf.c, which leads to a stack-based buffer overflow.
CVE-2003-0956(PUBLISHED):Multiple race conditions in the handling of O_DIRECT in Linux kernel prior to version 2.4.22 could cause stale data to be returned from the disk when handling sparse files, or cause incorrect data to be returned when a file is truncated as it is being read, which might allow local users to obtain sensitive data that was originally owned by other users, a different vulnerability than CVE-2003-0018.
CVE-2003-0959(PUBLISHED):Multiple integer overflows in the 32bit emulation for AMD64 architectures in Linux 2.4 kernel before 2.4.21 allows attackers to cause a denial of service or gain root privileges via unspecified vectors that trigger copy_from_user function calls with improper length arguments.
CVE-2003-0960(PUBLISHED):OpenCA before ******* does not use the correct certificate in a chain to check the serial, which could cause OpenCA to accept revoked or expired certificates.
CVE-2003-0961(PUBLISHED):Integer overflow in the do_brk function for the brk system call in Linux kernel 2.4.22 and earlier allows local users to gain root privileges.
CVE-2003-0962(PUBLISHED):Heap-based buffer overflow in rsync before 2.5.7, when running in server mode, allows remote attackers to execute arbitrary code and possibly escape the chroot jail.
CVE-2003-0963(PUBLISHED):Buffer overflows in (1) try_netscape_proxy and (2) try_squid_eplf for lftp 2.6.9 and earlier allow remote HTTP servers to execute arbitrary code via long directory names that are processed by the ls or rels commands.
CVE-2003-0965(PUBLISHED):Cross-site scripting (XSS) vulnerability in the admin CGI script for Mailman before 2.1.4 allows remote attackers to steal session cookies and conduct unauthorized activities.
CVE-2003-0966(PUBLISHED):Buffer overflow in the frm command in elm 2.5.6 and earlier, and possibly later versions, allows remote attackers to execute arbitrary code via a long Subject line.
CVE-2003-0967(PUBLISHED):rad_decode in FreeRADIUS 0.9.2 and earlier allows remote attackers to cause a denial of service (crash) via a short RADIUS string attribute with a tag, which causes memcpy to be called with a -1 length argument, as demonstrated using the Tunnel-Password attribute.
CVE-2003-0968(PUBLISHED):Stack-based buffer overflow in SMB_Logon_Server of the rlm_smb experimental module for FreeRADIUS 0.9.3 and earlier allows remote attackers to execute arbitrary code via a long User-Password attribute.
CVE-2003-0969(PUBLISHED):mpg321 0.2.10 allows remote attackers to overwrite memory and possibly execute arbitrary code via an mp3 file that passes certain strings to the printf function, possibly triggering a format string vulnerability.
CVE-2003-0970(PUBLISHED):The Network Management Port on Sun Fire B1600 systems allows remote attackers to cause a denial of service (packet loss) via ARP packets, which cause all ports to become temporarily disabled.
CVE-2003-0971(PUBLISHED):GnuPG (GPG) 1.0.2, and other versions up to 1.2.3, creates ElGamal type 20 (sign+encrypt) keys using the same key component for encryption as for signing, which allows attackers to determine the private key from a signature.
CVE-2003-0972(PUBLISHED):Integer signedness error in ansi.c for GNU screen 4.0.1 and earlier, and 3.9.15 and earlier, allows local users to execute arbitrary code via a large number of ";" (semicolon) characters in escape sequences, which leads to a buffer overflow.
CVE-2003-0973(PUBLISHED):Unknown vulnerability in mod_python 3.0.x before 3.0.4, and 2.7.x before 2.7.9, allows remote attackers to cause a denial of service (httpd crash) via a certain query string.
CVE-2003-0974(PUBLISHED):Applied Watch Command Center allows remote attackers to conduct unauthorized activities without authentication, such as (1) add new users to a console, as demonstrated using appliedsnatch.c, or (2) add spurious IDS rules to sensors, as demonstrated using addrule.c.
CVE-2003-0975(PUBLISHED):Apple Safari 1.0 through 1.1 on Mac OS X 10.3.1 and Mac OS X 10.2.8 allows remote attackers to steal user cookies from another domain via a link with a hex-encoded null character (%00) followed by the target domain.
CVE-2003-0976(PUBLISHED):NFS Server (XNFS.NLM) for Novell NetWare 6.5 does not properly enforce sys:\etc\exports when hostname aliases from sys:etc\hosts file are used, which could allow users to mount file systems when XNFS should deny the host.
CVE-2003-0977(PUBLISHED):CVS server before 1.11.10 may allow attackers to cause the CVS server to create directories and files in the file system root directory via malformed module requests.
CVE-2003-0978(PUBLISHED):Format string vulnerability in gpgkeys_hkp (experimental HKP interface) for the GnuPG (gpg) client 1.2.3 and earlier, and 1.3.3 and earlier, allows remote attackers or a malicious keyserver to cause a denial of service (crash) and possibly execute arbitrary code during key retrieval.
CVE-2003-0979(PUBLISHED):FreeScripts VisitorBook LE (visitorbook.pl) does not properly escape line breaks in input, which allows remote attackers to (1) use VisitorBook as an open mail relay, when $mailuser is 1, via extra headers in the email field, or (2) cause the guestbook database to be deleted via a large number of line breaks that exceeds the $max_posts variable.
CVE-2003-0980(PUBLISHED):Cross-site scripting (XSS) vulnerability in FreeScripts VisitorBook LE (visitorbook.pl) allows remote attackers to inject arbitrary HTML or web script via (1) the "do" parameter, (2) via the "user" parameter from a host with a malicious reverse DNS name, (3) via quote marks or ampersands in other parameters.
CVE-2003-0981(PUBLISHED):FreeScripts VisitorBook LE (visitorbook.pl) logs the reverse DNS name of a visiting host, which allows remote attackers to spoof the origin of their incoming requests and facilitate cross-site scripting (XSS) attacks.
CVE-2003-0982(PUBLISHED):Buffer overflow in the authentication module for Cisco ACNS 4.x before 4.2.11, and 5.x before 5.0.5, allows remote attackers to execute arbitrary code via a long password.
CVE-2003-0983(PUBLISHED):Cisco Unity on IBM servers is shipped with default settings that should have been disabled by the manufacturer, which allows local or remote attackers to conduct unauthorized activities via (1) a "bubba" local user account, (2) an open TCP port 34571, or (3) when a local DHCP server is unavailable, a DHCP server on the manufacturer's test network.
CVE-2003-0984(PUBLISHED):Real time clock (RTC) routines in Linux kernel 2.4.23 and earlier do not properly initialize their structures, which could leak kernel data to user space.
CVE-2003-0985(PUBLISHED):The mremap system call (do_mremap) in Linux kernel 2.4.x before 2.4.21, and possibly other versions before 2.4.24, does not properly perform bounds checks, which allows local users to cause a denial of service and possibly gain privileges by causing a remapping of a virtual memory area (VMA) to create a zero length VMA, a different vulnerability than CAN-2004-0077.
CVE-2003-0986(PUBLISHED):Various routines for the ppc64 architecture on Linux kernel 2.6 prior to 2.6.2 and 2.4 prior to 2.4.24 do not use the copy_from_user function when copying data from userspace to kernelspace, which crosses security boundaries and allows local users to cause a denial of service.
CVE-2003-0987(PUBLISHED):mod_digest for Apache before 1.3.31 does not properly verify the nonce of a client response by using a AuthNonce secret.
CVE-2003-0988(PUBLISHED):Buffer overflow in the VCF file information reader for KDE Personal Information Management (kdepim) suite in KDE 3.1.0 through 3.1.4 allows attackers to execute arbitrary code via a VCF file.
CVE-2003-0989(PUBLISHED):tcpdump before 3.8.1 allows remote attackers to cause a denial of service (infinite loop) via certain ISAKMP packets, a different vulnerability than CVE-2004-0057.
CVE-2003-0990(PUBLISHED):The parseAddress code in (1) SquirrelMail 1.4.0 and (2) GPG Plugin 1.1 allows remote attackers to execute commands via shell metacharacters in the "To:" field.
CVE-2003-0991(PUBLISHED):Unknown vulnerability in the mail command handler in Mailman before 2.0.14 allows remote attackers to cause a denial of service (crash) via malformed e-mail commands.
CVE-2003-0992(PUBLISHED):Cross-site scripting (XSS) vulnerability in the create CGI script for Mailman before 2.1.3 allows remote attackers to steal cookies of other users.
CVE-2003-0993(PUBLISHED):mod_access in Apache 1.3 before 1.3.30, when running big-endian 64-bit platforms, does not properly parse Allow/Deny rules using IP addresses without a netmask, which could allow remote attackers to bypass intended access restrictions.
CVE-2003-0994(PUBLISHED):The GUI functionality for an interactive session in Symantec LiveUpdate 1.70.x through 1.90.x, as used in Norton Internet Security 2001 through 2004, SystemWorks 2001 through 2004, and AntiVirus and Norton AntiVirus Pro 2001 through 2004, AntiVirus for Handhelds v3.0, allows local users to gain SYSTEM privileges.
CVE-2003-0995(PUBLISHED):Buffer overflow in the Microsoft Message Queue Manager (MSQM) allows remote attackers to cause a denial of service (RPC service crash) via a queue registration request.
CVE-2003-0996(PUBLISHED):Unknown "System Security Vulnerability" in Computer Associates (CA) Unicenter Remote Control (URC) 6.0 allows attackers to gain privileges via the help interface.
CVE-2003-0997(PUBLISHED):Unknown "Denial of Service Attack" vulnerability in Computer Associates (CA) Unicenter Remote Control (URC) 6.0 allows attackers to cause a denial of service (CPU consumption in URC host service).
CVE-2003-0998(PUBLISHED):Unknown "potential system security vulnerability" in Computer Associates (CA) Unicenter Remote Control 5.0 through 5.2, and ControlIT 5.0 and 5.1, may allow attackers to gain privileges to the local system account.
CVE-2003-0999(PUBLISHED):Unknown multiple vulnerabilities in (1) lpstat and (2) the libprint library in Solaris 2.6 through 9 may allow attackers to execute arbitrary code or read or write arbitrary files.
CVE-2003-1000(PUBLISHED):xchat 2.0.6 allows remote attackers to cause a denial of service (crash) via a passive DCC request with an invalid ID number, which causes a null dereference.
CVE-2003-1001(PUBLISHED):Buffer overflow in the Cisco Firewall Services Module (FWSM) in Cisco Catalyst 6500 and 7600 series devices allows remote attackers to cause a denial of service (crash and reload) via HTTP auth requests for (1) TACACS+ or (2) RADIUS authentication.
CVE-2003-1002(PUBLISHED):Cisco Firewall Services Module (FWSM) in Cisco Catalyst 6500 and 7600 series devices allows remote attackers to cause a denial of service (crash and reload) via an SNMPv3 message when snmp-server is set.
CVE-2003-1003(PUBLISHED):Cisco PIX firewall 5.x.x, and 6.3.1 and earlier, allows remote attackers to cause a denial of service (crash and reload) via an SNMPv3 message when snmp-server is set.
CVE-2003-1004(PUBLISHED):Cisco PIX firewall 6.2.x through 6.2.3, when configured as a VPN Client, allows remote attackers to cause a denial of service (dropped IPSec tunnel connection) via an IKE Phase I negotiation request to the outside interface of the firewall.
CVE-2003-1005(PUBLISHED):The PKI functionality in Mac OS X 10.2.8 and 10.3.2 allows remote attackers to cause a denial of service (service crash) via malformed ASN.1 sequences.
CVE-2003-1006(PUBLISHED):Buffer overflow in cd9660.util in Apple Mac OS X 10.0 through 10.3.2 and Apple Mac OS X Server 10.0 through 10.3.2 may allow local users to execute arbitrary code via a long command line parameter.
CVE-2003-1007(PUBLISHED):AppleFileServer (AFS) in Apple Mac OS X 10.2.8 and 10.3.2 does not properly handle certain malformed requests, with unknown impact.
CVE-2003-1008(PUBLISHED):Unknown vulnerability in Mac OS X 10.2.8 and 10.3.2 allows local users to bypass the screen saver login window and write a text clipping to the desktop or another application.
CVE-2003-1009(PUBLISHED):Directory Services in Apple Mac OS X 10.0.2, 10.0.3, 10.2.8, 10.3.2 and Apple Mac OS X Server 10.2 through 10.3.2 accepts authentication server information from unknown LDAP or NetInfo sources as provided by a malicious DHCP server, which allows remote attackers to gain privileges.
CVE-2003-1010(PUBLISHED):Unknown vulnerability in fs_usage in Mac OS X 10.2.8 and 10.3.2 and Mac OS X Server 10.2.8 and 10.3.2 allows local users to gain privileges via unknown attack vectors.
CVE-2003-1011(PUBLISHED):Apple Mac OS X 10.0 through 10.2.8 allows local users with a USB keyboard to gain unauthorized access by holding down the CTRL and C keys when the system is booting, which crashes the init process and leaves the user in a root shell.
CVE-2003-1012(PUBLISHED):The SMB dissector in Ethereal before 0.10.0 allows remote attackers to cause a denial of service via a malformed SMB packet that triggers a segmentation fault during processing of Selected packets.
CVE-2003-1013(PUBLISHED):The Q.931 dissector in Ethereal before 0.10.0, and Tethereal, allows remote attackers to cause a denial of service (crash) via a malformed Q.931, which triggers a null dereference.
CVE-2003-1014(PUBLISHED):Multiple content security gateway and antivirus products allow remote attackers to bypass content restrictions via MIME messages that use multiple MIME fields with the same name, which may be interpreted differently by mail clients.
CVE-2003-1015(PUBLISHED):Multiple content security gateway and antivirus products allow remote attackers to bypass content restrictions via MIME messages that use whitespace in an unusual fashion, which may be interpreted differently by mail clients.
CVE-2003-1016(PUBLISHED):Multiple content security gateway and antivirus products allow remote attackers to bypass content restrictions via MIME messages that use malformed quoting in MIME headers, parameters, and values, including (1) fields that should not be quoted, (2) duplicate quotes, or (3) missing leading or trailing quote characters, which may be interpreted differently by mail clients.
CVE-2003-1017(PUBLISHED):Macromedia Flash Player before 7,0,19,0 stores a Flash data file in a predictable location that is accessible to web browsers such as Internet Explorer and Opera, which allows remote attackers to read restricted files via vulnerabilities in web browsers whose exploits rely on predictable names.
CVE-2003-1018(PUBLISHED):Format string vulnerability in enq command in AIX 4.3, 5.1, and 5.2 allows local users with rintq group privileges to gain privileges via unknown attack vectors.
CVE-2003-1020(PUBLISHED):The format_send_to_gui function in formats.c for irssi before 0.8.9 allows remote IRC users to cause a denial of service (crash).
CVE-2003-1021(PUBLISHED):The scosession program in OpenServer 5.0.6 and 5.0.7 allows local users to gain privileges via crafted strings on the commandline.
CVE-2003-1022(PUBLISHED):Directory traversal vulnerability in fsp before 2.81.b18 allows remote users to access files outside the FSP root directory.
CVE-2003-1023(PUBLISHED):Stack-based buffer overflow in vfs_s_resolve_symlink of vfs/direntry.c for Midnight Commander (mc) 4.6.0 and earlier, and possibly later versions, allows remote attackers to execute arbitrary code during symlink conversion.
CVE-2003-1024(PUBLISHED):Unknown vulnerability in the ls-F builtin function in tcsh on Solaris 8 allows local users to create or delete files as other users, and gain privileges.
CVE-2003-1025(PUBLISHED):Internet Explorer 5.01 through 6 SP1 allows remote attackers to spoof the domain of a URL via a "%01" character before an @ sign in the user@domain portion of the URL, which hides the rest of the URL, including the real site, in the address bar, aka the "Improper URL Canonicalization Vulnerability."
CVE-2003-1026(PUBLISHED):Internet Explorer 5.01 through 6 SP1 allows remote attackers to bypass zone restrictions via a javascript protocol URL in a sub-frame, which is added to the history list and executed in the top window's zone when the history.back (back) function is called, as demonstrated by BackToFramedJpu, aka the "Travel Log Cross Domain Vulnerability."
CVE-2003-1027(PUBLISHED):Internet Explorer 5.01 through 6 SP1 allows remote attackers to direct drag and drop behaviors and other mouse click actions to other windows by using method caching (SaveRef) to access the window.moveBy method, which is otherwise inaccessible, as demonstrated by HijackClickV2, a different vulnerability than CVE-2003-0823, aka the "Function Pointer Drag and Drop Vulnerability."
CVE-2003-1028(PUBLISHED):The download function of Internet Explorer 6 SP1 allows remote attackers to obtain the cache directory name via an HTTP response with an invalid ContentType and a .htm file, which could allow remote attackers to bypass security mechanisms that rely on random names, as demonstrated by threadid10008.
CVE-2003-1029(PUBLISHED):The L2TP protocol parser in tcpdump 3.8.1 and earlier allows remote attackers to cause a denial of service (infinite loop and memory consumption) via a packet with invalid data to UDP port 1701, which causes l2tp_avp_print to use a bad length value when calling print_octets.
CVE-2003-1030(PUBLISHED):Buffer overflow in DameWare Mini Remote Control before 3.73 allows remote attackers to execute arbitrary code via a long pre-authentication request to TCP port 6129.
CVE-2003-1031(PUBLISHED):Cross-site scripting (XSS) vulnerability in register.php for vBulletin 3.0 Beta 2 allows remote attackers to inject arbitrary HTML or web script via optional fields such as (1) "Interests-Hobbies", (2) "Biography", or (3) "Occupation."
CVE-2003-1032(PUBLISHED):Pi3Web web server 2.0.2 Beta 1, when the Directory Index is configured to use the "Name" column and sort using the column title as a hyperlink, allows remote attackers to cause a denial of service (crash) via a malformed URL to the web server, possibly involving a buffer overflow.
CVE-2003-1033(PUBLISHED):The (1) instdbmsrv and (2) instlserver programs in SAP DB Development Tools 7.x trust the user-provided INSTROOT environment variable as a path when assigning setuid permissions to the lserver program, which allows local users to gain root privileges via a modified INSTROOT that points to a malicious dbmsrv or lserver program.
CVE-2003-1034(PUBLISHED):The RPM installation of SAP DB 7.x creates the (1) dbmsrv or (2) lserver programs with world-writable permissions, which allows local users to gain privileges by modifying those programs.
CVE-2003-1035(PUBLISHED):The default installation of SAP R/3 46C/D allows remote attackers to bypass account locking by using the RFC API instead of the SAPGUI to conduct a brute force password guessing attack, which does not lock out the account like the SAPGUI does.
CVE-2003-1036(PUBLISHED):Multiple buffer overflows in the AGate component for SAP Internet Transaction Server (ITS) allow remote attackers to execute arbitrary code via long (1) ~command, (2) ~runtimemode, or (3) ~session parameters, or (4) a long HTTP Content-Type header.
CVE-2003-1037(PUBLISHED):Format string vulnerability in the WGate component for SAP Internet Transaction Server (ITS) allows remote attackers to execute arbitrary code via a high "trace level."
CVE-2003-1038(PUBLISHED):The AGate component for SAP Internet Transaction Server (ITS) allows remote attackers to obtain sensitive information via a ~command parameter with an AgateInstallCheck value, which provides a list of installed DLLs and full pathnames.
CVE-2003-1039(PUBLISHED):Multiple buffer overflows in the mySAP.com architecture for SAP allow remote attackers to execute arbitrary code via a long HTTP Host header to (1) Message Server, (2) Web Dispatcher, or (3) Application Server.
CVE-2003-1040(PUBLISHED):kmod in the Linux kernel does not set its uid, suid, gid, or sgid to 0, which allows local users to cause a denial of service (crash) by sending certain signals to kmod.
CVE-2003-1041(PUBLISHED):Internet Explorer 5.x and 6.0 allows remote attackers to execute arbitrary programs via a modified directory traversal attack using a URL containing ".." (dot dot) sequences and a filename that ends in "::" which is treated as a .chm file even if it does not have a .chm extension.  NOTE: this bug may overlap CVE-2004-0475.
CVE-2003-1042(PUBLISHED):SQL injection vulnerability in collectstats.pl for Bugzilla 2.16.3 and earlier allows remote authenticated users with editproducts privileges to execute arbitrary SQL via the product name.
CVE-2003-1043(PUBLISHED):SQL injection vulnerability in Bugzilla 2.16.3 and earlier, and 2.17.1 through 2.17.4, allows remote authenticated users with editkeywords privileges to execute arbitrary SQL via the id parameter to editkeywords.cgi.
CVE-2003-1044(PUBLISHED):editproducts.cgi in Bugzilla 2.16.3 and earlier, when usebuggroups is enabled, does not properly remove group add privileges from a group that is being deleted, which allows users with those privileges to perform unauthorized additions to the next group that is assigned with the original group ID.
CVE-2003-1045(PUBLISHED):votes.cgi in Bugzilla 2.16.3 and earlier, and 2.17.1 through 2.17.4, allows remote attackers to read a user's voting page when that user has voted on a restricted bug, which allows remote attackers to read potentially sensitive voting information by modifying the who parameter.
CVE-2003-1046(PUBLISHED):describecomponents.cgi in Bugzilla 2.17.3 and 2.17.4 does not properly verify group membership when bug entry groups are used, which allows remote attackers to list component descriptions for otherwise restricted products.
CVE-2003-1048(PUBLISHED):Double free vulnerability in mshtml.dll for certain versions of Internet Explorer 6.x allows remote attackers to cause a denial of service (application crash) via a malformed GIF image.
CVE-2003-1049(PUBLISHED):IBM DB2 Universal Database 7 before FixPak 12 creates certain DMS directories with insecure permissions (777), which allows local users to modify or delete certain DB2 files.
CVE-2003-1050(PUBLISHED):Multiple buffer overflows in IBM DB2 Universal Database 8.1 may allow local users to execute arbitrary code via long command line arguments to (1) db2start, (2) db2stop, or (3) db2govd.
CVE-2003-1051(PUBLISHED):Multiple format string vulnerabilities in IBM DB2 Universal Database 8.1 may allow local users to execute arbitrary code via certain command line arguments to (1) db2start, (2) db2stop, or (3) db2govd.
CVE-2003-1052(PUBLISHED):IBM DB2 7.1 and 8.1 allow the bin user to gain root privileges by modifying the shared libraries that are used in setuid root programs.
CVE-2003-1053(PUBLISHED):Multiple buffer overflows in XShisen allow attackers to execute arbitrary code via a long (1) -KCONV command line option or (2) XSHISENLIB environment variable.
CVE-2003-1054(PUBLISHED):mod_access_referer 1.0.2 allows remote attackers to cause a denial of service (crash) via a malformed Referer header that is missing a hostname, as parsed by the ap_parse_uri_components function in Apache, which triggers a null dereference.
CVE-2003-1055(PUBLISHED):Buffer overflow in the nss_ldap.so.1 library for Sun Solaris 8 and 9 may allow local users to gain root access via a long hostname in an LDAP lookup.
CVE-2003-1056(PUBLISHED):The ed editor for Sun Solaris 2.6, 7, and 8 allows local users to create or overwrite arbitrary files via a symlink attack on temporary files.
CVE-2003-1057(PUBLISHED):Unknown vulnerability in CDE Print Viewer (dtprintinfo) for Sun Solaris 2.6 through 9 may allow local users to execute arbitrary code.
CVE-2003-1058(PUBLISHED):The Xsun server for Sun Solaris 2.6 through 9, when running in Direct Graphics Access (DGA) mode, allows local users to cause a denial of service (Xsun crash) or to create or overwrite arbitrary files on the system, probably via a symlink attack on temporary server files.
CVE-2003-1059(PUBLISHED):Unknown vulnerability in the libraries for the PGX32 frame buffer in Solaris 2.5.1 and 2.6 through 9 allows local users to gain root access.
CVE-2003-1060(PUBLISHED):The NFS Server for Solaris 7, 8, and 9 allows remote attackers to cause a denial of service (UFS panic) via certain invalid UFS requests, which triggers a null dereference.
CVE-2003-1061(PUBLISHED):Race condition in Solaris 2.6 through 9 allows local users to cause a denial of service (kernel panic), as demonstrated via the namefs function, pipe, and certain STREAMS routines.
CVE-2003-1062(PUBLISHED):Unknown vulnerability in the sysinfo system call for Solaris for SPARC 2.6 through 9, and Solaris for x86 2.6, 7, and 8, allows local users to read kernel memory.
CVE-2003-1063(PUBLISHED):The patches (1) 105693-13, (2) 108800-02, (3) 105694-13, and (4) 108801-02 for cachefs on Solaris 2.6 and 7 overwrite the inetd.conf file, which may silently reenable services and allow remote attackers to bypass the intended security policy.
CVE-2003-1064(PUBLISHED):Solaris 8 with IPv6 enabled allows remote attackers to cause a denial of service (kernel panic) via a crafted IPv6 packet.
CVE-2003-1065(PUBLISHED):Unknown vulnerability in patches 108993-14 through 108993-19 and 108994-14 through 108994-19 for Solaris 8 may allow local users to cause a denial of service (automountd crash).
CVE-2003-1066(PUBLISHED):Buffer overflow in the syslog daemon for Solaris 2.6 through 9 allows remote attackers to cause a denial of service (syslogd crash) and possibly execute arbitrary code via long syslog UDP packets.
CVE-2003-1067(PUBLISHED):Multiple buffer overflows in the (1) dbm_open function, as used in ndbm and dbm, and the (2) dbminit function in Solaris 2.6 through 9 allow local users to gain root privileges via long arguments to Xsun or other programs that use these functions.
CVE-2003-1068(PUBLISHED):Buffer overflow in utmp_update for Solaris 2.6 through 9 allows local users to gain root privileges, as identified by Sun BugID 4659277, a different vulnerability than CVE-2003-1082.
CVE-2003-1069(PUBLISHED):The Telnet daemon (in.telnetd) for Solaris 2.6 through 9 allows remote attackers to cause a denial of service (CPU consumption by infinite loop).
CVE-2003-1070(PUBLISHED):Unknown vulnerability in rpcbind for Solaris 2.6 through 9 allows remote attackers to cause a denial of service (rpcbind crash).
CVE-2003-1071(PUBLISHED):rpc.walld (wall daemon) for Solaris 2.6 through 9 allows local users to send messages to logged on users that appear to come from arbitrary user IDs by closing stderr before executing wall, then supplying a spoofed from header.
CVE-2003-1072(PUBLISHED):Memory leak in lofiadm in Solaris 8 allows local users to cause a denial of service (kernel memory consumption).
CVE-2003-1073(PUBLISHED):A race condition in the at command for Solaris 2.6 through 9 allows local users to delete arbitrary files via the -r argument with .. (dot dot) sequences in the job name, then modifying the directory structure after at checks permissions to delete the file and before the deletion actually takes place.
CVE-2003-1074(PUBLISHED):Unknown vulnerability in newtask for Solaris 9 allows local users to gain root privileges.
CVE-2003-1075(PUBLISHED):Unknown vulnerability in the FTP server (in.ftpd) for Solaris 2.6 through 9 allows remote attackers to cause a denial of service (temporary FTP server hang), which affects other active mode FTP clients.
CVE-2003-1076(PUBLISHED):Unknown vulnerability in sendmail for Solaris 7, 8, and 9 allows local users to cause a denial of service (unknown impact) and possibly gain privileges via certain constructs in a .forward file.
CVE-2003-1077(PUBLISHED):Unknown vulnerability in UFS for Solaris 9 for SPARC, with logging enabled, allows local users to cause a denial of service (UFS file system hang).
CVE-2003-1078(PUBLISHED):The FTP client for Solaris 2.6, 7, and 8 with the debug (-d) flag enabled displays the user password on the screen during login.
CVE-2003-1079(PUBLISHED):Unknown vulnerability in UDP RPC for Solaris 2.5.1 through 9 for SPARC, and 2.5.1 through 8 for x86, allows remote attackers to cause a denial of service (memory consumption) via certain arguments in RPC calls that cause large amounts of memory to be allocated.
CVE-2003-1080(PUBLISHED):Unknown vulnerability in mail for Solaris 2.6 through 9 allows local users to read the email of other users.
CVE-2003-1081(PUBLISHED):Aspppls for Solaris 8 allows local users to overwrite arbitrary files via a symlink attack on the .asppp.fifo temporary file.
CVE-2003-1082(PUBLISHED):Buffer overflow in utmp_update for Solaris 2.6 through 9 allows local users to gain root privileges, as identified by Sun BugID 4705891, a different vulnerability than CVE-2003-1068.
CVE-2003-1083(PUBLISHED):Stack-based buffer overflow in Monit 1.4 to 4.1 allows remote attackers to execute arbitrary code via a long HTTP request.
CVE-2003-1084(PUBLISHED):Monit 1.4 to 4.1 allows remote attackers to cause a denial of service (daemon crash) via an HTTP POST request with a negative Content-Length field.
CVE-2003-1085(PUBLISHED):The HTTP server in the Thomson TWC305, TWC315, and TCW690 cable modem ST42.03.0a allows remote attackers to cause a denial of service (unstable service) via a long GET request, possibly caused by a buffer overflow.
CVE-2003-1086(PUBLISHED):PHP remote file inclusion vulnerability in pm/lib.inc.php in pMachine Free and pMachine Pro 2.2 and 2.2.1 allows remote attackers to execute arbitrary PHP code by modifying the pm_path parameter to reference a URL on a remote web server that contains the code.
CVE-2003-1087(PUBLISHED):Unknown vulnerability in diagmond and possibly other applications in HP9000 Series 700/800 running HP-UX B.11.00, B.11.04, B.11.11, and B.11.22 allows remote attackers to cause a denial of service (program failure) via certain network traffic.
CVE-2003-1088(PUBLISHED):Cross-site scripting (XSS) vulnerability in index.php for Zorum 3.4 and 3.5 allows remote attackers to inject arbitrary web script or HTML via the method parameter.
CVE-2003-1089(PUBLISHED):index.php for Zorum 3.4 allows remote attackers to determine the full path of the web root via invalid parameter names, which reveals the path in a PHP error message.
CVE-2003-1090(PUBLISHED):Buffer overflow in AbsoluteTelnet before 2.12 RC10 allows remote attackers to execute arbitrary code via a long window title.
CVE-2003-1091(PUBLISHED):Integer overflow in MP3Broadcaster for Apple QuickTime/Darwin Streaming Server 4.1.3 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via malformed ID3 tags in MP3 files.
CVE-2003-1092(PUBLISHED):Unknown vulnerability in the "Automatic File Content Type Recognition (AFCTR) Tool version of the file package before 3.41, related to "a memory allocation problem," has unknown impact.
CVE-2003-1093(PUBLISHED):BEA WebLogic Server 6.1, 7.0 and *******, when routing messages to a JMS target domain that is inaccessible, may leak the user's password when it throws a ResourceAllocationException.
CVE-2003-1094(PUBLISHED):BEA WebLogic Server and Express version 7.0 SP3 may follow certain code execution paths that result in an incorrect current user, such as in the frequent use of JNDI initial contexts, which could allow remote authenticated users to gain privileges.
CVE-2003-1095(PUBLISHED):BEA WebLogic Server and Express 7.0 and *******, when using "memory" session persistence for web applications, does not clear authentication information when a web application is redeployed, which could allow users of that application to gain access without having to re-authenticate.
CVE-2003-1096(PUBLISHED):The Cisco LEAP challenge/response authentication mechanism uses passwords in a way that is susceptible to dictionary attacks, which makes it easier for remote attackers to gain privileges via brute force password guessing attacks.
CVE-2003-1097(PUBLISHED):Buffer overflow in rexec on HP-UX B.10.20, B.11.00, and B.11.04, when setuid root, may allow local users to gain privileges via a long -l option.
CVE-2003-1098(PUBLISHED):The Xserver for HP-UX 11.22 was not properly built, which introduced a vulnerability that allows local users to gain privileges.
CVE-2003-1099(PUBLISHED):shar on HP-UX B.11.00, B.11.04, and B.11.11 creates temporary files with predictable names in /tmp, which allows local users to cause a denial of service and possibly execute arbitrary code via a symlink attack.
CVE-2003-1100(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Hummingbird CyberDOCS 3.5.1, 3.9, and 4.0 allow remote attackers to inject arbitrary web script or HTML via certain vectors.
CVE-2003-1101(PUBLISHED):Hummingbird CyberDOCS 3.5.1, 3.9, and 4.0 allows remote attackers to obtain the full path of the DM Web Server via invalid login credentials, which reveals the path in an error message.
CVE-2003-1102(PUBLISHED):Hummingbird CyberDOCS 3.5, 3.9, and 4.0, when running on IIS, uses insecure permissions for script source code files, which allows remote attackers to read the source code.
CVE-2003-1103(PUBLISHED):SQL injection vulnerability in loginact.asp for Hummingbird CyberDOCS before 3.9 allows remote attackers to execute arbitrary SQL commands.
CVE-2003-1104(PUBLISHED):Buffer overflow in IBM Tivoli Firewall Toolbox (TFST) 1.2 allows remote attackers to execute arbitrary code via unknown vectors.
CVE-2003-1105(PUBLISHED):Unknown vulnerability in Internet Explorer 5.01 SP3 through 6.0 SP1 allows remote attackers to cause a denial of service (browser or Outlook Express crash) via HTML with certain input tags that are not properly rendered.
CVE-2003-1106(PUBLISHED):The SMTP service in Microsoft Windows 2000 before SP4 allows remote attackers to cause a denial of service (crash or hang) via an e-mail message with a malformed time stamp in the FILETIME attribute.
CVE-2003-1107(PUBLISHED):The DHTML capability in Microsoft Windows Media Player (WMP) 6.4, 7.0, 7.1, and 9 may run certain URL commands from a security zone that is less trusted than the current zone, which allows attackers to bypass intended access restrictions.
CVE-2003-1108(PUBLISHED):The Session Initiation Protocol (SIP) implementation in Alcatel OmniPCX Enterprise 5.0 Lx allows remote attackers to cause a denial of service and possibly execute arbitrary code via crafted INVITE messages, as demonstrated by the OUSPG PROTOS c07-sip test suite.
CVE-2003-1109(PUBLISHED):The Session Initiation Protocol (SIP) implementation in multiple Cisco products including IP Phone models 7940 and 7960, IOS versions in the 12.2 train, and Secure PIX 5.2.9 to 6.2.2 allows remote attackers to cause a denial of service and possibly execute arbitrary code via crafted INVITE messages, as demonstrated by the OUSPG PROTOS c07-sip test suite.
CVE-2003-1110(PUBLISHED):The Session Initiation Protocol (SIP) implementation in Columbia SIP User Agent (sipc) 1.74 and other versions before sipc 2.0 build 2003-02-21 allows remote attackers to cause a denial of service or execute arbitrary code via crafted INVITE messages, as demonstrated by the OUSPG PROTOS c07-sip test suite.
CVE-2003-1111(PUBLISHED):The Session Initiation Protocol (SIP) implementation in multiple dynamicsoft products including y and certain demo products for AppEngine allows remote attackers to cause a denial of service or execute arbitrary code via crafted INVITE messages, as demonstrated by the OUSPG PROTOS c07-sip test suite.
CVE-2003-1112(PUBLISHED):The Session Initiation Protocol (SIP) implementation in Ingate Firewall and Ingate SIParator before 3.1.3 allows remote attackers to cause a denial of service and possibly execute arbitrary code via crafted INVITE messages, as demonstrated by the OUSPG PROTOS c07-sip test suite.
CVE-2003-1113(PUBLISHED):The Session Initiation Protocol (SIP) implementation in IPTel SIP Express Router 0.8.9 and earlier allows remote attackers to cause a denial of service and possibly execute arbitrary code via crafted INVITE messages, as demonstrated by the OUSPG PROTOS c07-sip test suite.
CVE-2003-1114(PUBLISHED):The Session Initiation Protocol (SIP) implementation in Mediatrix Telecom VoIP Access Devices and Gateways running SIPv2.4 and SIPv4.3 firmware allows remote attackers to cause a denial of service or execute arbitrary code via crafted INVITE messages, as demonstrated by the OUSPG PROTOS c07-sip test suite.
CVE-2003-1115(PUBLISHED):The Session Initiation Protocol (SIP) implementation in Nortel Networks Succession Communication Server 2000, when using SIP-T, allows remote attackers to cause a denial of service and possibly execute arbitrary code via crafted INVITE messages, as demonstrated by the OUSPG PROTOS c07-sip test suite.
CVE-2003-1116(PUBLISHED):The communications protocol for the Report Review Agent (RRA), aka FND File Server (FNDFS) program, in Oracle E-Business Suite 10.7, 11.0, and 11.5.1 to 11.5.8 allows remote attackers to bypass authentication and obtain sensitive information from the Oracle Applications Concurrent Manager by spoofing requests to the TNS Listener.
CVE-2003-1117(PUBLISHED):Buffer overflow in RealSystem Server 6.x, 7.x and 8.x, and RealSystem Proxy 8.x, related to URL error handling, allows remote attackers to cause a denial of service and possibly execute arbitrary code.
CVE-2003-1118(PUBLISHED):Buffer overflow in the SETI@home client 3.03 and other versions allows remote attackers to cause a denial of service (client crash) and execute arbitrary code via a spoofed server response containing a long string followed by a \n (newline) character.
CVE-2003-1119(PUBLISHED):SSH Secure Shell before 3.2.9 allows remote attackers to cause a denial of service via malformed BER/DER packets.
CVE-2003-1120(PUBLISHED):Race condition in SSH Tectia Server 4.0.3 and 4.0.4 for Unix, when the password change plugin (ssh-passwd-plugin) is enabled, allows local users to obtain the server's private key.
CVE-2003-1121(PUBLISHED):Services in ScriptLogic 4.01, and possibly other versions before 4.14, process client requests at raised privileges, which allows remote attackers to (1) modify arbitrary registry entries via the ScriptLogic RPC service (SLRPC) or (2) modify arbitrary configuration via the RunAdmin services (SLRAserver.exe and SLRAclient.exe).
CVE-2003-1122(PUBLISHED):ScriptLogic 4.01, and possibly other versions before 4.14, uses insecure permissions for the LOGS$ share, which allows users to modify log records and possibly execute arbitrary code.
CVE-2003-1123(PUBLISHED):Sun Java Runtime Environment (JRE) and SDK 1.4.0_01 and earlier allows untrusted applets to access certain information within trusted applets, which allows attackers to bypass the restrictions of the Java security model.
CVE-2003-1124(PUBLISHED):Unknown vulnerability in Sun Management Center (SunMC) 2.1.1, 3.0, and 3.0 Revenue Release (RR), when installed and run by root, allows local users to create or modify arbitrary files.
CVE-2003-1125(PUBLISHED):Unknown vulnerability in ns-ldapd for Sun ONE Directory Server 4.16, 5.0, and 5.1 allows LDAP clients to cause a denial of service (service halt).
CVE-2003-1126(PUBLISHED):Unknown vulnerability in SunOne/iPlanet Web Server SP3 through SP5 on Windows platforms allows remote attackers to cause a denial of service.
CVE-2003-1127(PUBLISHED):Whale Communications e-Gap 2.5 on Windows 2000 allows remote attackers to obtain the source code for the login page via the HTTP TRACE method, which bypasses the preprocessor.
CVE-2003-1128(PUBLISHED):XMMS.pm in X2 XMMS Remote, as obtained from the vendor server between 4 AM 11 AM PST on May 7, 2003, allows remote attackers to execute arbitrary commands via shell metacharacters in a request to TCP port 8086.
CVE-2003-1129(PUBLISHED):Buffer overflow in the Yahoo! Audio Conferencing (aka Voice Chat) ActiveX control before 1,0,0,45 allows remote attackers to cause a denial of service and possibly execute arbitrary code via a URL with a long hostname to Yahoo! Messenger or Yahoo! Chat.
CVE-2003-1131(PUBLISHED):PHP remote file inclusion vulnerability in index.php in KnowledgeBuilder, referred to as KnowledgeBase, allows remote attackers to execute arbitrary PHP code by modifying the page parameter to reference a URL on a remote web server that contains the code.
CVE-2003-1132(PUBLISHED):The DNS server for Cisco Content Service Switch (CSS) 11000 and 11500, when prompted for a nonexistent AAAA record, responds with response code 3 (NXDOMAIN or "Name Error") instead of response code 0 ("No Error"), which allows remote attackers to cause a denial of service (inaccessible domain) by forcing other DNS servers to send and cache a request for a AAAA record to the vulnerable server.
CVE-2003-1133(PUBLISHED):Rit Research Labs The Bat! 1.0.11 through 2.0 creates new accounts with insecure ACLs, which allows local users to read other users' email messages.
CVE-2003-1134(PUBLISHED):Sun Java 1.3.1, 1.4.1, and 1.4.2 allows local users to cause a denial of service (JVM crash), possibly by calling the ClassDepth function with a null parameter, which causes a crash instead of generating a null pointer exception.
CVE-2003-1135(PUBLISHED):Buffer overflow in Yahoo! Messenger 5.6 allows remote attackers to cause a denial of service (crash) via a file send request (sendfile) with a large number of "%" (percent) characters after the Yahoo ID.
CVE-2003-1136(PUBLISHED):Cross-site scripting (XSS) vulnerability in Chi Kien Uong Guestbook 1.51 allows remote attackers to inject arbitrary web script or HTML via (1) HTML in a posted message or (2) Javascript in an onmouseover attribute in an e-mail address or URL.
CVE-2003-1137(PUBLISHED):Charles Steinkuehler sh-httpd 0.3 and 0.4 allows remote attackers to read files or execute arbitrary CGI scripts via a GET request that contains an asterisk (*) wildcard character.
CVE-2003-1138(PUBLISHED):The default configuration of Apache 2.0.40, as shipped with Red Hat Linux 9.0, allows remote attackers to list directory contents, even if auto indexing is turned off and there is a default web page configured, via a GET request containing a double slash (//).
CVE-2003-1139(PUBLISHED):Musicqueue 1.2.0 allows local users to overwrite arbitrary files by triggering a segmentation fault and using a symlink attack on the resulting musicqueue.crash file.
CVE-2003-1140(PUBLISHED):Buffer overflow in Musicqueue 1.2.0 allows local users to execute arbitrary code via a long language variable in the configuration file.
CVE-2003-1141(PUBLISHED):Buffer overflow in NIPrint 4.10 allows remote attackers to execute arbitrary code via a long string to TCP port 515.
CVE-2003-1142(PUBLISHED):Help in NIPrint LPD-LPR Print Server 4.10 and earlier executes Windows Explorer with SYSTEM privileges, which allows local users to gain privileges.
CVE-2003-1143(PUBLISHED):Croteam Serious Sam demo test 2 2.1a, Serious Sam: the First Encounter 1.05, and Serious Sam: the Second Encounter 1.05 allow remote attackers to cause a denial of service (crash or freeze) via a TCP packet with an invalid first parameter.
CVE-2003-1144(PUBLISHED):Buffer overflow in the log viewing interface in Perception LiteServe 1.25 through 2.2 allows remote attackers to execute arbitrary code via a GET request with a long file name.
CVE-2003-1145(PUBLISHED):Cross-site scripting (XSS) vulnerability in friendmail.php in OpenAutoClassifieds 1.0 allows remote attackers to inject arbitrary web script or HTML via the listing parameter.
CVE-2003-1146(PUBLISHED):Cross-site scripting (XSS) vulnerability in John Beatty Easy PHP Photo Album 1.0 allows remote attackers to inject arbitrary web script or HTML via the dir parameter.
CVE-2003-1148(PUBLISHED):Multiple PHP remote file inclusion vulnerabilities in J-Pierre DEZELUS Les Visiteurs 2.0.1, as used in phpMyConferences (phpMyConference) 8.0.2 and possibly other products, allow remote attackers to execute arbitrary PHP code via a URL in the lvc_include_dir parameter to (1) config.inc.php or (2) new-visitor.inc.php in common/visiteurs/include/.
CVE-2003-1149(PUBLISHED):Cross-site scripting (XSS) vulnerability in Symantec Norton Internet Security 2003 ******** allows remote attackers to inject arbitrary web script or HTML via a URL to a blocked site, which is displayed on the blocked sites error page.
CVE-2003-1150(PUBLISHED):Buffer overflow in the portmapper service (PMAP.NLM) in Novell NetWare 6 SP3 and ZenWorks for Desktops 3.2 SP2 through 4.0.1 allows remote attackers to cause a denial of service and possibly execute arbitrary code via unknown attack vectors.
CVE-2003-1151(PUBLISHED):Cross-site scripting (XSS) vulnerability in Fastream NETFile Server 6.0.3.588 allows remote attackers to inject arbitrary web script or HTML via the URL, which is displayed on a "404 Not Found" error page.
CVE-2003-1152(PUBLISHED):WebTide 7.04 allows remote attackers to list arbitrary directories via an HTTP request for %3f.jsp (encoded "?").
CVE-2003-1153(PUBLISHED):byteHoard 0.7 and 0.71 allows remote attackers to list arbitrary files and directories via a direct request to files.inc.php.
CVE-2003-1154(PUBLISHED):MAILsweeper for SMTP 4.3 allows remote attackers to bypass virus protection via a mail message with a malformed zip attachment, as exploited by certain MIMAIL virus variants.
CVE-2003-1155(PUBLISHED):X-CD-Roast 0.98 alpha10 through alpha14 allows local users to overwrite arbitrary files via a symlink attack on an unknown file.
CVE-2003-1156(PUBLISHED):Java Runtime Environment (JRE) and Software Development Kit (SDK) 1.4.2 through 1.4.2_02 allows local users to overwrite arbitrary files via a symlink attack on (1) unpack.log, as created by the unpack program, or (2) .mailcap1 and .mime.types1, as created by the RPM program.
CVE-2003-1157(PUBLISHED):Cross-site scripting (XSS) vulnerability in login.asp in Citrix MetaFrame XP Server 1.0 allows remote attackers to inject arbitrary web script or HTML via the NFuse_Message parameter.
CVE-2003-1158(PUBLISHED):Multiple buffer overflows in the FTP service in Plug and Play Web Server 1.0002c allow remote attackers to cause a denial of service (crash) via long (1) dir, (2) ls, (3) delete, (4) mkdir, (5) DELE, (6) RMD, or (7) MKD commands.
CVE-2003-1159(PUBLISHED):Plug and Play Web Server Proxy 1.0002c allows remote attackers to cause a denial of service (server crash) via an invalid URI in an HTTP GET request to TCP port 8080.
CVE-2003-1160(PUBLISHED):FlexWATCH Network video server 132 allows remote attackers to bypass authentication and gain administrative privileges via an HTTP request to aindex.htm that contains double leading slashes (//).
CVE-2003-1161(PUBLISHED):exit.c in Linux kernel 2.6-test9-CVS, as stored on kernel.bkbits.net, was modified to contain a backdoor, which could allow local users to elevate their privileges by passing __WCLONE|__WALL to the sys_wait4 function.
CVE-2003-1162(PUBLISHED):index.php in Tritanium Bulletin Board 1.2.3 allows remote attackers to read and reply to arbitrary messages by modifying the thread_id, forum_id, and sid parameters.
CVE-2003-1163(PUBLISHED):hash.c in Ganglia gmond 2.5.3 allows remote attackers to cause a denial of service (segmentation fault) via a UDP packet that contains a single-byte name string, which is used as an out-of-bounds array index.
CVE-2003-1164(PUBLISHED):Cross-site scripting (XSS) vulnerability in Mldonkey 2.5-4 allows remote attackers to inject arbitrary web script or HTML via the URI, which is injected into the HTML error page.
CVE-2003-1165(PUBLISHED):Buffer overflow in BRS WebWeaver 1.06 and earlier allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via an HTTP request with a long User-Agent header.
CVE-2003-1166(PUBLISHED):Directory traversal vulnerability in (1) Openfile.aspx and (2) Html.aspx in HTTP Commander 4.0 allows remote attackers to view arbitrary files via a .. (dot dot) in the file parameter.
CVE-2003-1167(PUBLISHED):misc.cpp in KPopup 0.9.1 trusts the PATH variable when executing killall, which allows local users to elevate their privileges by modifying the PATH variable to reference a malicious killall program.
CVE-2003-1168(PUBLISHED):HTTP Commander 4.0 allows remote attackers to obtain sensitive information via an HTTP request that contains a . (dot) in the file parameter, which reveals the installation path in an error message.
CVE-2003-1169(PUBLISHED):DATEV Nutzungskontrolle 2.1 and 2.2 has insecure write permissions for critical registry keys, which allows local users to bypass access restrictions by importing NukoInfo values in certain DATEV keys, which disables Nutzungskontrolle.
CVE-2003-1170(PUBLISHED):Format string vulnerability in main.cpp in kpopup 0.9.1 and 0.9.5pre2 allows local users to cause a denial of service (segmentation fault) and possibly execute arbitrary code via format string specifiers in command line arguments.
CVE-2003-1171(PUBLISHED):Heap-based buffer overflow in the sec_filter_out function in mod_security 1.7RC1 through 1.7.1 in Apache 2 allows remote attackers to execute arbitrary code via a server side script that sends a large amount of data.
CVE-2003-1172(PUBLISHED):Directory traversal vulnerability in the view-source sample file in Apache Software Foundation Cocoon 2.1 and 2.2 allows remote attackers to access arbitrary files via a .. (dot dot) in the filename parameter.
CVE-2003-1173(PUBLISHED):Centrinity FirstClass 7.1 allows remote attackers to access sensitive information by appending search to the end of the URL and checking all of the search option checkboxes and leaving the text field blank, which will return all files in the searched directory.
CVE-2003-1174(PUBLISHED):Buffer overflow in NullSoft Shoutcast Server 1.9.2 allows local users to cause a denial of service via (1) icy-name followed by a long server name or (2) icy-url followed by a long URL.
CVE-2003-1175(PUBLISHED):Cross-site scripting (XSS) vulnerability in index.php in Sympoll 1.5 allows remote attackers to inject arbitrary web script or HTML via the vo parameter.
CVE-2003-1176(PUBLISHED):post_message_form.asp in Web Wiz Forums 6.34 through 7.5, when quote mode is used, allows remote attackers to read or write to private forums by modifying the FID (forum ID) parameter.
CVE-2003-1177(PUBLISHED):Buffer overflow in the base64 decoder in MERCUR Mailserver 4.2 before SP3a allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long (1) AUTH command to the POP3 server or (2) AUTHENTICATE command to the IMAP server.
CVE-2003-1178(PUBLISHED):Eval injection vulnerability in comments.php in Advanced Poll 2.0.2 allows remote attackers to execute arbitrary PHP code via the (1) id, (2) template_set, or (3) action parameter.
CVE-2003-1179(PUBLISHED):Multiple PHP remote file inclusion vulnerabilities in Advanced Poll 2.0.2 allow remote attackers to execute arbitrary PHP code via the include_path parameter in (1) booth.php, (2) png.php, (3) poll_ssi.php, or (4) popup.php, the (5) base_path parameter to common.inc.php.
CVE-2003-1180(PUBLISHED):Directory traversal vulnerability in Advanced Poll 2.0.2 allows remote attackers to read arbitrary files or inject arbitrary local PHP files via .. sequences in the base_path or pollvars[lang] parameters to the admin files (1) index.php, (2) admin_tpl_new.php, (3) admin_tpl_misc_new.php, (4) admin_templates_misc.php, (5) admin_templates.php, (6) admin_stats.php, (7) admin_settings.php, (8) admin_preview.php, (9) admin_password.php, (10) admin_logout.php, (11) admin_license.php, (12) admin_help.php, (13) admin_embed.php, (14) admin_edit.php, or (15) admin_comment.php.
CVE-2003-1181(PUBLISHED):Advanced Poll 2.0.2 allows remote attackers to obtain sensitive information via an HTTP request to info.php, which invokes the phpinfo() function.
CVE-2003-1182(PUBLISHED):Cross-site scripting (XSS) vulnerability in MPM Guestbook 1.2 allows remote attackers to inject arbitrary web script or HTML via the lng parameter.
CVE-2003-1183(PUBLISHED):The WebCache component in Oracle Files *******.0, *******.0, and *******.0 of Oracle Collaboration Suite Release 1 caches files despite the cacheability rules imposed by Oracle Files, which allows local users to gain access.
CVE-2003-1184(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in ThWboard Beta 2.8 and 2.81 allow remote attackers to inject arbitrary web script or HTML via (1) time in board.php, (2) the profile Homepage-Feld, (3) pictures, and (4) other "Diverse XSS Bugs."
CVE-2003-1185(PUBLISHED):Multiple SQL injection vulnerabilities in ThWboard before Beta 2.8.2 allow remote attackers to inject arbitrary SQL commands via various vectors including (1) Admin-Center, (2) Announcements, (3) admin/calendar.php, and (4) showevent.php.
CVE-2003-1186(PUBLISHED):Buffer overflow in TelCondex SimpleWebServer 2.12.30210 Build3285 allows remote attackers to execute arbitrary code via a long HTTP Referer header.
CVE-2003-1187(PUBLISHED):Cross-site scripting (XSS) vulnerability in include.php in PHPKIT 1.6.02 and 1.6.03 allows remote attackers to inject arbitrary web script or HTML via the contact_email parameter.
CVE-2003-1188(PUBLISHED):Unichat allows remote attackers to cause a denial of service (crash) by adding extra chat characters (avatars) and logging in to a chat room, as demonstrated using duplicate ACTOR entries in u2res000.rit.
CVE-2003-1189(PUBLISHED):Unknown vulnerability in Nokia IPSO 3.7, configured as IP Clusters, allows remote attackers to cause a denial of service via unknown attack vectors.
CVE-2003-1190(PUBLISHED):Cross-site scripting (XSS) vulnerability in PHPRecipeBook 1.24 through 2.17 allows remote attackers to inject arbitrary web script or HTML via a recipe.
CVE-2003-1191(PUBLISHED):chatbox.php in e107 0.554 and 0.603 allows remote attackers to cause a denial of service (pages fail to load) via HTML in the Name field, which prevents the main.php form from being loaded.
CVE-2003-1192(PUBLISHED):Stack-based buffer overflow in IA WebMail Server 3.1.0 allows remote attackers to execute arbitrary code via a long GET request.
CVE-2003-1193(PUBLISHED):Multiple SQL injection vulnerabilities in the Portal DB (1) List of Values (LOVs), (2) Forms, (3) Hierarchy, and (4) XML components packages in Oracle Oracle9i Application Server ******** through *******.5 allow remote attackers to execute arbitrary SQL commands via the URL.
CVE-2003-1194(PUBLISHED):Cross-site scripting (XSS) vulnerability in Booby .1 through 0.2.3 allows remote attackers to inject arbitrary web script or HTML via the error message.
CVE-2003-1195(PUBLISHED):SQL injection vulnerability in getmember.asp in VieBoard 2.6 Beta 1 allows remote attackers to execute arbitrary SQL commands via the msn variable.
CVE-2003-1196(PUBLISHED):SQL injection vulnerability in viewtopic.asp in VieBoard 2.6 allows remote attackers to execute arbitrary SQL commands via the forumid parameter.
CVE-2003-1197(PUBLISHED):Cross-site scripting (XSS) vulnerability in index.php for Ledscripts.com LedForums Beta 1 allows remote attackers to inject arbitrary web script or HTML via the (1) top_message parameter or (2) topic field of a new thread.
CVE-2003-1198(PUBLISHED):connection.c in Cherokee web server before 0.4.6 allows remote attackers to cause a denial of service via an HTTP POST request without a Content-Length header field.
CVE-2003-1199(PUBLISHED):Cross-site scripting (XSS) vulnerability in MyProxy 20030629 allows remote attackers to inject arbitrary web script or HTML via the URL.
CVE-2003-1200(PUBLISHED):Stack-based buffer overflow in FORM2RAW.exe in Alt-N MDaemon 6.5.2 through 6.8.5 allows remote attackers to execute arbitrary code via a long From parameter to Form2Raw.cgi.
CVE-2003-1201(PUBLISHED):ldbm_back_exop_passwd in the back-ldbm backend in passwd.c for OpenLDAP 2.1.12 and earlier, when the slap_passwd_parse function does not return LDAP_SUCCESS, attempts to free an uninitialized pointer, which allows remote attackers to cause a denial of service (segmentation fault).
CVE-2003-1202(PUBLISHED):The checklogin function in omail.pl for omail webmail 0.98.4 and earlier allows remote attackers to execute arbitrary commands via shell metacharacters in a (1) password, (2) domainname, or (3) username.
CVE-2003-1203(PUBLISHED):Cross-site scripting (XSS) vulnerability in index.php for Mambo Site Server 4.0.10 allows remote attackers to execute script on other clients via the ?option parameter.
CVE-2003-1204(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Mambo Site Server 4.0.12 BETA and earlier allow remote attackers to execute script on other clients via (1) the link parameter in sectionswindow.php, the directory parameter in (2) gallery.php, (3) navigation.php, or (4) uploadimage.php, the path parameter in (5) view.php, (6) the choice parameter in upload.php, (7) the sitename parameter in mambosimple.php, (8) the type parameter in upload.php, or the id parameter in (9) emailarticle.php, (10) emailfaq.php, or (11) emailnews.php.
CVE-2003-1205(PUBLISHED):Crob FTP Server 2.60.1 allows remote authenticated users to cause a denial of service (crash) by renaming a file to the "con" MS-DOS device name.
CVE-2003-1206(PUBLISHED):Format string vulnerability in Crob FTP Server 2.60.1 allows remote attackers to cause a denial of service (crash) via "%s" or "%n" sequences in (1) the username during login, or other FTP commands such as (2) dir.
CVE-2003-1207(PUBLISHED):Crob FTP Server 3.5.1 allows remote authenticated users to cause a denial of service (crash) via a dir command with a large number of "." characters followed by a "/*" string.
CVE-2003-1208(PUBLISHED):Multiple buffer overflows in Oracle 9i 9 before ******* allow local users to execute arbitrary code by (1) setting the TIME_ZONE session parameter to a long value, or providing long parameters to the (2) NUMTOYMINTERVAL, (3) NUMTODSINTERVAL or (4) FROM_TZ functions.
CVE-2003-1209(PUBLISHED):The Post_Method function in Monkey HTTP Daemon before 0.6.2 allows remote attackers to cause a denial of service (crash) via a POST request without a Content-Type header.
CVE-2003-1210(PUBLISHED):Multiple SQL injection vulnerabilities in the Downloads module for PHP-Nuke 5.x through 6.5 allow remote attackers to execute arbitrary SQL commands via the (1) lid parameter to the getit function or the (2) min parameter to the search function.
CVE-2003-1211(PUBLISHED):Cross-site scripting (XSS) vulnerability in search.asp for MaxWebPortal 1.30 and possibly earlier versions allows remote attackers to inject arbitrary web script or HTML via the Search parameter.
CVE-2003-1212(PUBLISHED):MaxWebPortal 1.30 allows remote attackers to perform unauthorized actions by modifying hidden form fields, such as the (1) news, (2) lock, or (3) allmem fields in the 'start new topic' HTML page.
CVE-2003-1213(PUBLISHED):The default installation of MaxWebPortal 1.30 stores the portal database under the web document root with insecure access control, which allows remote attackers to obtain sensitive information via a direct request to database/db2000.mdb.
CVE-2003-1214(PUBLISHED):Unknown vulnerability in the server login for VisualShapers ezContents 2.02 and earlier allows remote attackers to bypass access restrictions and gain access to restricted functions.
CVE-2003-1215(PUBLISHED):SQL injection vulnerability in groupcp.php for phpBB 2.0.6 and earlier allows group moderators to perform unauthorized activities via the sql_in parameter.
CVE-2003-1216(PUBLISHED):SQL injection vulnerability in search.php for phpBB 2.0.6 and earlier allows remote attackers to execute arbitrary SQL and gain privileges via the search_id parameter.
CVE-2003-1219(PUBLISHED):Cross-site scripting (XSS) vulnerability in the tep_href_link function in html_output.php for osCommerce before 2.2-MS3 allows remote attackers to inject arbitrary web script or HTML via the osCsid parameter.
CVE-2003-1220(PUBLISHED):BEA WebLogic Server proxy plugin for BEA Weblogic Express and Server 6.1 through 8.1 SP 1 allows remote attackers to cause a denial of service (proxy plugin crash) via a malformed URL.
CVE-2003-1221(PUBLISHED):BEA WebLogic Express and Server 7.0 through 8.1 SP 1, under certain circumstances when a request to use T3 over SSL (t3s) is made to the insecure T3 port, may use a non-SSL connection for the communication, which could allow attackers to sniff sessions.
CVE-2003-1222(PUBLISHED):BEA Weblogic Express and Server 8.0 through 8.1 SP 1, when using a foreign Java Message Service (JMS) provider, echoes the password for the foreign provider to the console and stores it in cleartext in config.xml, which could allow attackers to obtain the password.
CVE-2003-1223(PUBLISHED):The Node Manager for BEA WebLogic Express and Server 6.1 through 8.1 SP 1 allows remote attackers to cause a denial of service (Node Manager crash) via malformed data to the Node Manager's port, as demonstrated by nmap.
CVE-2003-1224(PUBLISHED):Weblogic.admin for BEA WebLogic Server and Express 7.0 and ******* displays the JDBCConnectionPoolRuntimeMBean password to the screen in cleartext, which allows attackers to read a user's password by physically observing ("shoulder surfing") the screen.
CVE-2003-1225(PUBLISHED):The default CredentialMapper for BEA WebLogic Server and Express 7.0 and ******* stores passwords in cleartext on disk, which allows local users to extract passwords.
CVE-2003-1226(PUBLISHED):BEA WebLogic Server and Express 7.0 and ******* stores certain secrets concerning password encryption insecurely in config.xml, filerealm.properties, and weblogic-rar.xml, which allows local users to learn those secrets and decrypt passwords.
CVE-2003-1227(PUBLISHED):PHP remote file include vulnerability in index.php for Gallery 1.4 and 1.4-pl1, when running on Windows or in Configuration mode on Unix, allows remote attackers to inject arbitrary PHP code via a URL in the GALLERY_BASEDIR parameter, a different vulnerability than CVE-2002-1412.  NOTE: this issue might be exploitable only during installation, or if the administrator has not run a security script after installation.
CVE-2003-1228(PUBLISHED):Buffer overflow in the prepare_reply function in request.c for Mathopd 1.2 through 1.5b13, and possibly earlier versions, allows remote attackers to cause a denial of service (server crash) and possibly execute arbitrary code via an HTTP request with a long path.
CVE-2003-1229(PUBLISHED):X509TrustManager in (1) Java Secure Socket Extension (JSSE) in SDK and JRE 1.4.0 through 1.4.0_01, (2) JSSE before 1.0.3, (3) Java Plug-in SDK and JRE 1.3.0 through 1.4.1, and (4) Java Web Start 1.0 through 1.2 incorrectly calls the isClientTrusted method when determining server trust, which results in improper validation of digital certificate and allows remote attackers to (1) falsely authenticate peers for SSL or (2) incorrectly validate signed JAR files.
CVE-2003-1230(PUBLISHED):The implementation of SYN cookies (syncookies) in FreeBSD 4.5 through 5.0-RELEASE-p3 uses only 32-bit internal keys when generating syncookies, which makes it easier for remote attackers to conduct brute force ISN guessing attacks and spoof legitimate traffic.
CVE-2003-1231(PUBLISHED):Cross-site scripting (XSS) vulnerability in index.php in ECW-Shop 5.5 allows remote attackers to inject arbitrary web script or HTML via the cat parameter.
CVE-2003-1232(PUBLISHED):Emacs 21.2.1 does not prompt or warn the user before executing Lisp code in the local variables section of a text file, which allows user-assisted attackers to execute arbitrary commands, as demonstrated using the mode-name variable.
CVE-2003-1233(PUBLISHED):Pedestal Software Integrity Protection Driver (IPD) 1.3 and earlier allows privileged attackers, such as rootkits, to bypass file access restrictions to the Windows kernel by using the NtCreateSymbolicLinkObject function to create a symbolic link to (1) \Device\PhysicalMemory or (2) to a drive letter using the subst command.
CVE-2003-1234(PUBLISHED):Integer overflow in the f_count counter in FreeBSD before 4.2 through 5.0 allows local users to cause a denial of service (crash) and possibly execute arbitrary code via multiple calls to (1) fpathconf and (2) lseek, which do not properly decrement f_count through a call to fdrop.
CVE-2003-1235(PUBLISHED):BRW WebWeaver 1.03 allows remote attackers to obtain sensitive server environment information via a URL request for testcgi.exe, which lists the values of environment variables and the current working directory.
CVE-2003-1236(PUBLISHED):Multiple format string vulnerabilities in the logger function in netzio.c for Tanne 0.6.17 allows remote attackers to execute arbitrary code via format string specifiers in syslog.
CVE-2003-1237(PUBLISHED):Cross-site scripting vulnerability (XSS) in WWWBoard 2.0A2.1 and earlier allows remote attackers to inject arbitrary HTML or web script via a message post.
CVE-2003-1238(PUBLISHED):Cross-site scripting vulnerability (XSS) in Nuked-Klan 1.3 beta and earlier allows remote attackers to steal authentication information via cookies by injecting arbitrary HTML or script into op of the (1) Team, (2) News, and (3) Liens modules.
CVE-2003-1239(PUBLISHED):Directory traversal vulnerability in sendphoto.php in WihPhoto 0.86 allows remote attackers to read arbitrary files via .. specifiers in the album parameter, and the target filename in the pic parameter.
CVE-2003-1240(PUBLISHED):PHP remote file inclusion vulnerability in CuteNews 0.88 allows remote attackers to execute arbitrary PHP code via a URL in the cutepath parameter in (1) shownews.php, (2) search.php, or (3) comments.php.
CVE-2003-1241(PUBLISHED):Cross-site scripting vulnerability (XSS) in (1) admin_index.php, (2) admin_pass.php, (3) admin_modif.php, and (4) admin_suppr.php in MyGuestbook 3.0 allows remote attackers to execute arbitrary PHP code by modifying the location parameter to reference a URL on a remote web server that contains file.php via script injected into the pseudo, email, and message parameters.
CVE-2003-1242(PUBLISHED):Sage 1.0 b3 allows remote attackers to obtain the root web server path via a URL request for a non-existent module, which returns the path in an error message.
CVE-2003-1243(PUBLISHED):Cross-site scripting vulnerability (XSS) in Sage 1.0 b3 allows remote attackers to insert arbitrary HTML or web script via the mod parameter.
CVE-2003-1244(PUBLISHED):SQL injection vulnerability in page_header.php in phpBB 2.0, 2.0.1 and 2.0.2 allows remote attackers to brute force user passwords and possibly gain unauthorized access to forums via the forum_id parameter to index.php.
CVE-2003-1245(PUBLISHED):index2.php in Mambo 4.0.12 allows remote attackers to gain administrator access via a URL request where session_id is set to the MD5 hash of a session cookie.
CVE-2003-1246(PUBLISHED):NtCreateSymbolicLinkObject in ntdll.dll in Integrity Protection Driver (IPD) 1.2 and 1.3 allows local users to create and overwrite arbitrary files via a symlink attack on \winnt\system32\drivers using the subst command.
CVE-2003-1247(PUBLISHED):Multiple buffer overflows in H-Sphere WebShell 2.3 allow remote attackers to execute arbitrary code via (1) a long URL content type in CGI::readFile, (2) a long path in diskusage, and (3) a long fname in flist.
CVE-2003-1248(PUBLISHED):H-Sphere WebShell 2.3 allows remote attackers to execute arbitrary commands via shell metacharacters in the (1) mode and (2) zipfile parameters in a URL request.
CVE-2003-1249(PUBLISHED):WebIntelligence 2.7.1 uses guessable user session cookies, which allows remote attackers to hijack sessions.
CVE-2003-1250(PUBLISHED):Efficient Networks 5861 DSL router, when running firmware 5.3.80 configured to block incoming TCP SYN, packets allows remote attackers to cause a denial of service (crash) via a flood of TCP SYN packets to the WAN interface using a port scanner such as nmap.
CVE-2003-1251(PUBLISHED):The (1) menu.inc.php, (2) datasets.php and (3) mass_operations.inc.php (mistakenly referred to as mass_opeations.inc.php) scripts in N/X 2002 allow remote attackers to execute arbitrary PHP code via a c_path that references a URL on a remote web server that contains the code.
CVE-2003-1252(PUBLISHED):register.php in S8Forum 3.0 allows remote attackers to execute arbitrary PHP commands by creating a user whose name ends in a .php extension and entering the desired commands into the E-mail field, which creates a web-accessible .php file that can be called by the attacker, as demonstrated using a "system($cmd)" E-mail address with a "any_name.php" username.
CVE-2003-1253(PUBLISHED):PHP remote file inclusion vulnerability in Bookmark4U 1.8.3 allows remote attackers to execute arbitrary PHP code viaa URL in the prefix parameter to (1) dbase.php, (2) config.php, or (3) common.load.php.
CVE-2003-1254(PUBLISHED):Active PHP Bookmarks (APB) 1.1.01 allows remote attackers to execute arbitrary PHP code via (1) head.php, (2) apb_common.php, or (3) apb_view_class.php by modifying the APB_SETTINGS parameter to reference a URL on a remote web server that contains the code.
CVE-2003-1255(PUBLISHED):add_bookmark.php in Active PHP Bookmarks (APB) 1.1.01 allows remote attackers to add arbitrary bookmarks as other users using a modified auth_user_id parameter.
CVE-2003-1256(PUBLISHED):aff_liste_langue.php in E-theni allows remote attackers to execute arbitrary PHP code by modifying the rep_include parameter to reference a URL on a remote web server that contains para_langue.php.
CVE-2003-1257(PUBLISHED):find_theni_home.php in E-theni allows remote attackers to obtain sensitive system information via a URL request which executes phpinfo.
CVE-2003-1258(PUBLISHED):activate.php in versatileBulletinBoard (vBB) 0.9.5 and 0.9.6 allows remote attackers to gain unauthorized administrative access via a URL request with the uid parameter set to the webmaster uid.
CVE-2003-1259(PUBLISHED):Buffer overflow in CuteFTP 4.2 and 5.0 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a long FTP server banner.
CVE-2003-1260(PUBLISHED):Buffer overflow in CuteFTP 5.0 allows remote attackers to execute arbitrary code via a long response to a LIST command.
CVE-2003-1261(PUBLISHED):Buffer overflow in CuteFTP 5.0 and 5.0.1 allows local users to cause a denial of service (crash) by copying a long URL into a clipboard.
CVE-2003-1262(PUBLISHED):Buffer overflow in the http_fetch function of HTTP Fetcher 1.0.0 and 1.0.1 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a URL request via a long (1) host, (2) referer, or (3) userAgent value.
CVE-2003-1263(PUBLISHED):ICAL.EXE in iCal 3.7 allows remote attackers to cause a denial of service (crash) via a malformed HTTP request, possibly due to an invalid method name.
CVE-2003-1264(PUBLISHED):TFTP server in Longshine Wireless Access Point (WAP) LCS-883R-AC-B, and in D-Link DI-614+ 2.0 which is based on it, allows remote attackers to obtain the WEP secret and gain administrator privileges by downloading the configuration file (config.img) and other files without authentication.
CVE-2003-1265(PUBLISHED):Netscape 7.0 and Mozilla 5.0 do not immediately delete messages in the trash folder when users select the 'Empty Trash' option, which could allow local users to access deleted messages.
CVE-2003-1266(PUBLISHED):The (1) FTP, (2) POP3, (3) SMTP, and (4) NNTP servers in EServer 2.92 through 2.97, and possibly 2.98, allow remote attackers to cause a denial of service (crash) via a large amount of data.
CVE-2003-1267(PUBLISHED):GuildFTPd 0.999 allows remote attackers to cause a denial of service (crash) via a GET request for MS-DOS device names such as lpt1.
CVE-2003-1268(PUBLISHED):Multiple SQL injection vulnerabilities in (1) addcustomer.asp, (2) addprod.asp, and (3) process.asp in a.shopKart 2.0.3 allow remote attackers to execute arbitrary SQL and obtain sensitive information via the zip, state, country, phone, and fax parameters.
CVE-2003-1269(PUBLISHED):AN HTTP 1.41e allows remote attackers to obtain the root web server path via an HTTP request with a long argument to a script, which leaks the path in an error message.
CVE-2003-1270(PUBLISHED):AN HTTP 1.41e allows remote attackers to cause a denial of service (borken pipe) via an HTTP request to aux.cgi with a long argument, possibly triggering a buffer overflow or MS-DOS device vulnerability.
CVE-2003-1271(PUBLISHED):Cross-site scripting vulnerability (XSS) in AN HTTP 1.41e allows remote attackers to execute arbitrary web script or HTML as other users via a URL containing the script.
CVE-2003-1272(PUBLISHED):Multiple buffer overflows in Winamp 3.0 allow remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a .b4s file containing (1) a long playlist name or (2) a long path in a file: argument to the Playstring parameter.
CVE-2003-1273(PUBLISHED):Winamp 3.0 allows remote attackers to cause a denial of service (crash) via a .b4s file with a playlist name that contains some non-English characters, e.g. Cyrillic characters.
CVE-2003-1274(PUBLISHED):Winamp 3.0 allows remote attackers to cause a denial of service (crash) via .b4s file with a file: argument to the Playstring parameter that contains MS-DOS device names such as aux.
CVE-2003-1275(PUBLISHED):Pocket Internet Explorer (PIE) 3.0 allows remote attackers to cause a denial of service (crash) via a Javascript function that uses the object.innerHTML function to recursively call that function.
CVE-2003-1276(PUBLISHED):Netfone.exe of NetTelephone 3.5.6 uses weak encryption for user PIN's and stores user account numbers in plaintext in the HKEY_CURRENT_USER\Software\MediaRing.com\SDK\NetTelephone\settings registry key, which could allow local users to gain unauthorized access to NetTelephone accounts.
CVE-2003-1277(PUBLISHED):Cross-site scripting (XSS) vulnerabilities in Yet Another Bulletin Board (YaBB) 1.5.0 allow remote attackers to execute arbitrary script as other users and possibly steal authentication information via cookies by injecting arbitrary HTML or script into (1) news_icon of news_template.php, and (2) threadid and subject of index.html
CVE-2003-1278(PUBLISHED):Cross-site scripting vulnerability (XSS) in OpenTopic 2.3.1 allows remote attackers to execute arbitrary script as other users and possibly steal authentication information via cookies by injecting arbitrary HTML or script into IMG tags.
CVE-2003-1279(PUBLISHED):S-PLUS 6.0 allows local users to overwrite arbitrary files and possibly elevate privileges via a symlink attack on (1) /tmp/__F8499 by Sqpe, (2) /tmp/PRINT.$$.out by PRINT, (3) /tmp/SUBST$PID.TXT and /tmp/ed.cmds$PID by mustfix.hlinks, (4) /tmp/file.1 and /tmp/file.2 by sas_get, (5) /tmp/file.1 by sas_vars, and (6) /tmp/sgml2html$$tmp /tmp/sgml2html$$tmp1 /tmp/sgml2html$$tmp2 by sglm2html.
CVE-2003-1280(PUBLISHED):Directory traversal vulnerability in cgihtml 1.69 allows remote attackers to overwrite and create arbitrary files via a .. (dot dot) in multipart/form-data uploads.
CVE-2003-1281(PUBLISHED):cgihtml 1.69 allows local users to overwrite arbitrary files via a symlink attack on certain temporary files.
CVE-2003-1282(PUBLISHED):IBM Net.Data allows remote attackers to obtain sensitive information such as path names, server names and possibly user names and passwords by causing the (1) $(DTW_CURRENT_FILENAME), (2) $(DATABASE), (3) $(LOGIN), (4) $(PASSWORD), and possibly other predefined variables that can be echoed back to the user via a web form.
CVE-2003-1283(PUBLISHED):KaZaA Media Desktop (KMD) 2.0 launches advertisements in the Internet Explorer (IE) local security zone, which could allow remote attackers to view local files and possibly execute arbitrary code.
CVE-2003-1284(PUBLISHED):Sambar Server before 6.0 beta 6 allows remote attackers to obtain sensitive information via direct requests to the default scripts (1) environ.pl and (2) testcgi.exe.
CVE-2003-1285(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Sambar Server before 6.0 beta 6 allow remote attackers to inject arbitrary web script or HTML via the query string to (1) isapi/testisa.dll, (2) testcgi.exe, (3) environ.pl, (4) the query parameter to samples/search.dll, (5) the price parameter to mortgage.pl, (6) the query string in dumpenv.pl, (7) the query string to dumpenv.pl, and (8) the E-Mail field of the guestbook script (book.pl).
CVE-2003-1286(PUBLISHED):HTTP Proxy in Sambar Server before 6.0 beta 6, when security.ini lacks a 12******* proxydeny entry, allows remote attackers to send proxy HTTP requests to the Sambar Server's administrative interface and external web servers, by making a "Connection: keep-alive" request before the proxy requests.
CVE-2003-1287(PUBLISHED):Sambar Server before 6.0 beta 3 allows attackers with physical access to execute arbitrary code via a request with an MS-DOS device name such as com1.pl, con.pl, or aux.pl, which causes Perl to read the code from the associated device.
CVE-2003-1288(PUBLISHED):Multiple race conditions in Linux-VServer 1.22 with Linux kernel 2.4.23 and SMP allow local users to cause a denial of service (kernel oops) via unknown attack vectors related to the (1) s_info and (2) ip_info data structures and the (a) forget_original_parent, (b) goodness, (c) schedule, (d) update_process_times, and (e) vc_new_s_context functions.
CVE-2003-1289(PUBLISHED):The iBCS2 system call translator for statfs in NetBSD 1.5 through 1.5.3 and FreeBSD 4 up to 4.8-RELEASE-p2 and 5 up to 5.1-RELEASE-p1 allows local users to read portions of kernel memory (memory disclosure) via a large length parameter, which copies additional kernel memory into userland memory.
CVE-2003-1290(PUBLISHED):BEA WebLogic Server and WebLogic Express 6.1, 7.0, and 8.1, with RMI and anonymous admin lookup enabled, allows remote attackers to obtain configuration information by accessing MBeanHome via the Java Naming and Directory Interface (JNDI).
CVE-2003-1291(PUBLISHED):VMware ESX Server 1.5.2 before Patch 4 allows local users to execute arbitrary programs as root via certain modified VMware ESX Server environment variables.
CVE-2003-1292(PUBLISHED):PHP remote file include vulnerability in Derek Ashauer ashNews 0.83 allows remote attackers to include and execute arbitrary remote files via a URL in the pathtoashnews parameter to (1) ashnews.php and (2) ashheadlines.php.
CVE-2003-1293(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in NukedWeb GuestBookHost allow remote attackers to inject arbitrary web script or HTML via the (1) Name, (2) Email and (3) Message fields when signing the guestbook.
CVE-2003-1294(PUBLISHED):Xscreensaver before 4.15 creates temporary files insecurely in (1) driver/passwd-kerberos.c, (2) driver/xscreensaver-getimage-video, (3) driver/xscreensaver.kss.in, and the (4) vidwhacker and (5) webcollage screensavers, which allows local users to overwrite arbitrary files via a symlink attack.
CVE-2003-1295(PUBLISHED):Unspecified vulnerability in xscreensaver 4.12, and possibly other versions, allows attackers to cause xscreensaver to crash via unspecified vectors "while verifying the user-password."
CVE-2003-1296(PUBLISHED):Easy File Sharing (EFS) Web Server 1.2 allows remote authenticated users to cause a denial of service via (1) an "empty symbol" in the Title field or (2) certain data in the Your Message field, possibly a long argument.
CVE-2003-1297(PUBLISHED):Easy File Sharing (EFS) Web Server 1.2 stores the (1) option.ini (aka options.ini) file and (2) log directory under the web root with insufficient access control, which allows remote attackers to obtain sensitive information including an SMTP account username and password hash, the server configuration, and server log files.
CVE-2003-1298(PUBLISHED):Multiple directory traversal vulnerabilities in siteman.php3 in AnyPortal(php) 12 MAY 00 allow remote attackers to (1) create, (2) delete, (3) save, and (4) upload files by navigating to the root directory and entering a filename beginning with "./.." (dot slash dot dot).
CVE-2003-1299(PUBLISHED):Directory traversal vulnerability in Baby FTP Server 1.2, and possibly other versions before May 31, 2003 allows remote authenticated users to list arbitrary directories and possibly read files via "..." (triple dot) manipulations to the CWD command.
CVE-2003-1300(PUBLISHED):Baby FTP Server (BabyFTP) 1.2, and possibly other versions before May 31, 2003, allows remote attackers to cause a denial of service via a large number of connections from the same IP address, which triggers an access violation.
CVE-2003-1301(PUBLISHED):Sun Java Runtime Environment (JRE) 1.x before 1.4.2_11 and 1.5.x before 1.5.0_06, and as used in multiple web browsers, allows remote attackers to cause a denial of service (application crash) via deeply nested object arrays, which are not properly handled by the garbage collector and trigger invalid memory accesses.
CVE-2003-1302(PUBLISHED):The IMAP functionality in PHP before 4.3.1 allows remote attackers to cause a denial of service via an e-mail message with a (1) To or (2) From header with an address that contains a large number of "\" (backslash) characters.
CVE-2003-1303(PUBLISHED):Buffer overflow in the imap_fetch_overview function in the IMAP functionality (php_imap.c) in PHP before 4.3.3 allows remote attackers to cause a denial of service (segmentation fault) and possibly execute arbitrary code via a long e-mail address in a (1) To or (2) From header.
CVE-2003-1304(PUBLISHED):EarlyImpact ProductCart 1.0 through 2.0 stores database/EIPC.mdb under the web root with insufficient access control, which allows remote attackers to obtain sensitive database information via a direct request.
CVE-2003-1305(PUBLISHED):Microsoft Internet Explorer allows remote attackers to cause a denial of service (resource consumption) via a Javascript src attribute that recursively loads the current web page.
CVE-2003-1306(PUBLISHED):Microsoft URLScan 2.5, with the RemoveServerHeader option enabled, allows remote attackers to obtain sensitive information (server name and version) via an HTTP request that generates certain errors such as 400 "Bad Request," which leak the Server header in the response.
CVE-2003-1307(PUBLISHED):The mod_php module for the Apache HTTP Server allows local users with write access to PHP scripts to send signals to the server's process group and use the server's file descriptors, as demonstrated by sending a STOP signal, then intercepting incoming connections on the server's TCP port.  NOTE: the PHP developer has disputed this vulnerability, saying "The opened file descriptors are opened by Apache. It is the job of Apache to protect them ... Not a bug in PHP.
CVE-2003-1308(PUBLISHED):CRLF injection vulnerability in fvwm-menu-directory for fvwm 2.5.x before 2.5.10 and 2.4.x before 2.4.18 allows local users to execute arbitrary commands via carriage returns in a filename.
CVE-2003-1309(PUBLISHED):The DeviceIoControl function in the TrueVector Device Driver (VSDATANT) in ZoneAlarm before 3.7.211, Pro before ***********, and Plus before *********** allows local users to gain privileges via certain signals (aka "Device Driver Attack").
CVE-2003-1310(PUBLISHED):The DeviceIoControl function in the Norton Device Driver (NAVAP.sys) in Symantec Norton AntiVirus 2002 allows local users to gain privileges by overwriting memory locations via certain control codes (aka "Device Driver Attack").
CVE-2003-1311(PUBLISHED):siteminderagent/SmMakeCookie.ccc in Netegrity SiteMinder does not ensure that the TARGET parameter names a valid redirection resource, which allows remote attackers to construct a URL that might trick users into visiting an arbitrary web site referenced by this parameter.
CVE-2003-1312(PUBLISHED):siteminderagent/SmMakeCookie.ccc in Netegrity SiteMinder places a session ID string in the value of the SMSESSION parameter in a URL, which might allow remote attackers to obtain the ID by sniffing, reading Referer logs, or other methods.
CVE-2003-1313(PUBLISHED):Multiple PHP remote file inclusion vulnerabilities in EternalMart Mailing List Manager (EMLM) 1.32 allow remote attackers to execute arbitrary PHP code via a URL in (1) the emml_admin_path parameter to admin/auth.php or (2) the emml_path parameter to emml_email_func.php.
CVE-2003-1314(PUBLISHED):PHP remote file inclusion vulnerability in admin/auth.php in EternalMart Guestbook (EMGB) 1.1 allows remote attackers to execute arbitrary PHP code via a URL in the emgb_admin_path parameter.
CVE-2003-1315(PUBLISHED):SQL injection vulnerability in auth.php in Land Down Under (LDU) v601 and earlier allows remote attackers to execute arbitrary SQL commands.
CVE-2003-1316(PUBLISHED):mod.php in eNdonesia 8.2 allows remote attackers to obtain sensitive information via a ' (quote) value in the lng parameter, which reveals the path in an error message.  NOTE: The provenance of this information is unknown; the details are obtained solely from third party information.
CVE-2003-1317(PUBLISHED):Cross-site scripting (XSS) vulnerability in mod.php in eNdonesia 8.2 allows remote attackers to inject arbitrary web script or HTML via the mod parameter.  NOTE: The provenance of this information is unknown; the details are obtained solely from third party information.
CVE-2003-1318(PUBLISHED):Twilight Webserver ******* allows remote attackers to cause a denial of service (application crash) via a GET request for a long URI, a different vulnerability than CVE-2004-2376.
CVE-2003-1319(PUBLISHED):Multiple buffer overflows in SmartFTP 1.0.973, and other versions before 1.0.976, allow remote attackers to execute arbitrary code via (1) a long response to a PWD command, which triggers a stack-based overflow, and (2) a long line in a response to a file LIST command, which triggers a heap-based overflow.
CVE-2003-1320(PUBLISHED):SonicWALL firmware before ******* allows remote attackers to cause a denial of service and possibly execute arbitrary code via crafted Internet Key Exchange (IKE) response packets, possibly including (1) a large Security Parameter Index (SPI) field, (2) a large number of payloads, or (3) a long payload.
CVE-2003-1321(PUBLISHED):Buffer overflow in Avant Browser 8.02 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a long URL in an HTTP request.
CVE-2003-1322(PUBLISHED):Multiple stack-based buffer overflows in Atrium MERCUR IMAPD in MERCUR Mailserver before ******** allow remote attackers to execute arbitrary code via a long (1) EXAMINE, (2) DELETE, (3) SUBSCRIBE, (4) RENAME, (5) UNSUBSCRIBE, (6) LIST, (7) LSUB, (8) STATUS, (9) LOGIN, (10) CREATE, or (11) SELECT command.
CVE-2003-1323(PUBLISHED):Elm ME+ 2.4 before PL109S, when installed setgid mail and the operating system lacks POSIX saved ID support, allows local users to read and modify certain files with the privileges of the mail group via unspecified vectors.
CVE-2003-1324(PUBLISHED):Race condition in the can_open function in Elm ME+ 2.4, when installed setgid mail and the operating system lacks POSIX saved ID support, allows local users to read and modify certain files with the privileges of the mail group.
CVE-2003-1325(PUBLISHED):The SV_CheckForDuplicateNames function in Valve Software Half-Life CSTRIKE Dedicated Server ******* and earlier allows remote authenticated users to cause a denial of service (infinite loop and daemon hang) via a certain connection string to UDP port 27015 that represents "absence of player informations," a related issue to CVE-2006-0734.
CVE-2003-1326(PUBLISHED):Microsoft Internet Explorer 5.5 and 6.0 allows remote attackers to bypass the cross-domain security model to run malicious script or arbitrary programs via dialog boxes, aka "Improper Cross Domain Security Validation with dialog box."
CVE-2003-1327(PUBLISHED):Buffer overflow in the SockPrintf function in wu-ftpd 2.6.2 and earlier, when compiled with MAIL_ADMIN option enabled on a system that supports very long pathnames, might allow remote anonymous users to execute arbitrary code by uploading a file with a long pathname, which triggers the overflow when wu-ftpd constructs a notification message to the administrator.
CVE-2003-1328(PUBLISHED):The showHelp() function in Microsoft Internet Explorer 5.01, 5.5, and 6.0 supports certain types of pluggable protocols that allow remote attackers to bypass the cross-domain security model and execute arbitrary code, aka "Improper Cross Domain Security Validation with ShowHelp functionality."
CVE-2003-1329(PUBLISHED):ftpd.c in wu-ftpd 2.6.2, when running on "operating systems that only allow one non-connected socket bound to the same local address," does not close failed connections, which allows remote attackers to cause a denial of service.
CVE-2003-1330(PUBLISHED):Clearswift MAILsweeper for SMTP 4.3.6 SP1 does not execute custom "on strip unsuccessful" hooks, which allows remote attackers to bypass e-mail attachment filtering policies via an attachment that MAILsweeper can detect but not remove.
CVE-2003-1331(PUBLISHED):Stack-based buffer overflow in the mysql_real_connect function in the MySql client library (libmysqlclient) 4.0.13 and earlier allows local users to execute arbitrary code via a long socket name, a different vulnerability than CVE-2001-1453.
CVE-2003-1332(PUBLISHED):Stack-based buffer overflow in the reply_nttrans function in Samba 2.2.7a and earlier allows remote attackers to execute arbitrary code via a crafted request, a different vulnerability than CVE-2003-0201.
CVE-2003-1333(PUBLISHED):Unspecified vulnerability in the Cache' Server Page (CSP) implementation in InterSystems Cache' 4.0.3 through 5.0.5 allows remote attackers to "gain complete control" of a server.
CVE-2003-1334(PUBLISHED):Cross-site scripting (XSS) vulnerability in Kai Blankenhorn Bitfolge simple and nice index file (aka snif) before 1.2.7 allows remote attackers to inject arbitrary web script or HTML via unspecified vectors.
CVE-2003-1335(PUBLISHED):Directory traversal vulnerability in Kai Blankenhorn Bitfolge simple and nice index file (aka snif) before 1.2.5 allows remote attackers to download files from locations above the snif directory.
CVE-2003-1336(PUBLISHED):Buffer overflow in mIRC before 6.11 allows remote attackers to execute arbitrary code via a long irc:// URL.
CVE-2003-1337(PUBLISHED):Heap-based buffer overflow in Aprelium Abyss Web Server 1.1.2 and earlier allows remote attackers to execute arbitrary code via a long HTTP GET request.
CVE-2003-1338(PUBLISHED):CRLF injection vulnerability in Aprelium Abyss Web Server 1.1.2 and earlier allows remote attackers to inject arbitrary HTTP headers and possibly conduct HTTP Response Splitting attacks via CRLF sequences in the Location header.
CVE-2003-1339(PUBLISHED):Stack-based buffer overflow in eZnet.exe, as used in eZ (a) eZphotoshare, (b) eZmeeting, (c) eZnetwork, and (d) eZshare allows remote attackers to cause a denial of service (crash) or execute arbitrary code, as demonstrated via (1) a long GET request and (2) a long operation or autologin parameter to SwEzModule.dll.
CVE-2003-1340(PUBLISHED):Multiple SQL injection vulnerabilities in Francisco Burzi PHP-Nuke 5.6 and 6.5 allow remote authenticated users to execute arbitrary SQL commands via (1) a uid (user) cookie to modules.php; and allow remote attackers to execute arbitrary SQL commands via an aid (admin) cookie to the Web_Links module in a (2) viewlink, (3) MostPopular, or (4) NewLinksDate action, different vectors than CVE-2003-0279.
CVE-2003-1341(PUBLISHED):The default installation of Trend Micro OfficeScan 3.0 through 3.54 and 5.x allows remote attackers to bypass authentication from cgiChkMasterPasswd.exe and gain access to the web management console via a direct request to cgiMasterPwd.exe.
CVE-2003-1342(PUBLISHED):Trend Micro Virus Control System (TVCS) 1.8 running with IIS allows remote attackers to cause a denial of service (memory consumption) in IIS via multiple URL requests for ActiveSupport.exe.
CVE-2003-1343(PUBLISHED):Trend Micro ScanMail for Exchange (SMEX) before 3.81 and before 6.1 might install a back door account in smg_Smxcfg30.exe, which allows remote attackers to gain access to the web management interface via the vcc parameter, possibly "3560121183d3".
CVE-2003-1344(PUBLISHED):Trend Micro Virus Control System (TVCS) Log Collector allows remote attackers to obtain usernames, encrypted passwords, and other sensitive information via a URL request for getservers.exe with the action parameter set to "selects1", which returns log files.
CVE-2003-1345(PUBLISHED):Directory traversal vulnerability in s.dll in WebCollection Plus 5.00 allows remote attackers to view arbitrary files in c:\ via a full pathname in the d parameter.
CVE-2003-1346(PUBLISHED):D-Link wireless access point DWL-900AP+ 2.2, 2.3 and possibly 2.5 allows remote attackers to set factory default settings by upgrading the firmware using AirPlus Access Point Manager.
CVE-2003-1347(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Geeklog 1.3.7 allow remote attackers to inject arbitrary web script or HTML via the (1) cid parameter to comment.php, (2) uid parameter to profiles.php, (3) uid to users.php, and (4) homepage field.
CVE-2003-1348(PUBLISHED):Cross-site scripting (XSS) vulnerability in guestbook.cgi in ftls.org Guestbook 1.1 allows remote attackers to inject arbitrary web script or HTML via the (1) comment, (2) name, or (3) title field.
CVE-2003-1349(PUBLISHED):Directory traversal vulnerability in NITE ftp-server (NiteServer) 1.83 allows remote attackers to list arbitrary directories via a "\.." (backslash dot dot) in the CD (CWD) command.
CVE-2003-1350(PUBLISHED):List Site Pro 2.0 allows remote attackers to hijack user accounts by inserting a "|" (pipe), which is used as a field delimiter, into the bannerurl field.
CVE-2003-1351(PUBLISHED):Directory traversal vulnerability in edittag.cgi in EditTag 1.1 allows remote attackers to read arbitrary files via a "%2F.." (encoded slash dot dot) in the file parameter.
CVE-2003-1352(PUBLISHED):Gabber 0.8.7 sends an email to a specific address during user login and logout, which allows remote attackers to obtain user session activity and Gabber version number by sniffing.
CVE-2003-1353(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Outreach Project Tool (OPT) 0.946b allow remote attackers to inject arbitrary web script or HTML, as demonstrated using the news field.
CVE-2003-1354(PUBLISHED):Multiple GameSpy 3D 2.62 compatible gaming servers generate very large UDP responses to small requests, which allows remote attackers to use the servers as an amplifier in DDoS attacks with spoofed UDP query packets, as demonstrated using Battlefield 1942.
CVE-2003-1355(PUBLISHED):Buffer overflow in the remote console (rcon) in Battlefield 1942 1.2 and 1.3 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a long user name and password.
CVE-2003-1356(PUBLISHED):The "file handling" in sort in HP-UX 10.01 through 10.20, and 11.00 through 11.11 is "incorrect," which allows attackers to gain access or cause a denial of service via unknown vectors.
CVE-2003-1357(PUBLISHED):ProxyView has a default administrator password of Administrator for Embedded Windows NT, which allows remote attackers to gain access.
CVE-2003-1358(PUBLISHED):rs.F300 for HP-UX 10.0 through 11.22 uses the PATH environment variable to find and execute programs such as rm while operating at raised privileges, which allows local users to gain privileges by modifying the path to point to a malicious rm program.
CVE-2003-1359(PUBLISHED):Buffer overflow in stmkfont utility of HP-UX 10.0 through 11.22 allows local users to gain privileges via a long command line argument.
CVE-2003-1360(PUBLISHED):Buffer overflow in the setupterm function of (1) lanadmin and (2) landiag programs of HP-UX 10.0 through 10.34 allows local users to execute arbitrary code via a long TERM environment variable.
CVE-2003-1361(PUBLISHED):Unknown vulnerability in VERITAS Bare Metal Restore (BMR) of Tivoli Storage Manager (TSM) 3.1.0 through 3.2.1 allows remote attackers to gain root privileges on the BMR Main Server.
CVE-2003-1362(PUBLISHED):Bastille B.02.00.00 of HP-UX 11.00 and 11.11 does not properly configure the (1) NOVRFY and (2) NOEXPN options in the sendmail.cf file, which could allow remote attackers to verify the existence of system users and expand defined sendmail aliases.
CVE-2003-1363(PUBLISHED):The remote web management interface of Aprelium Technologies Abyss Web Server 1.1.2 and earlier does not log connection attempts to the web management port (9999), which allows remote attackers to mount brute force attacks on the administration console without detection.
CVE-2003-1364(PUBLISHED):Aprelium Technologies Abyss Web Server 1.1.2, and possibly other versions before 1.1.4, allows remote attackers to cause a denial of service (crash) via an HTTP GET message with empty (1) Connection or (2) Range fields.
CVE-2003-1365(PUBLISHED):The escape_dangerous_chars function in CGI::Lite 2.0 and earlier does not correctly remove special characters including (1) "\" (backslash), (2) "?", (3) "~" (tilde), (4) "^" (carat), (5) newline, or (6) carriage return, which could allow remote attackers to read or write arbitrary files, or execute arbitrary commands, in shell scripts that rely on CGI::Lite to filter such dangerous inputs.
CVE-2003-1366(PUBLISHED):chpass in OpenBSD 2.0 through 3.2 allows local users to read portions of arbitrary files via a hard link attack on a temporary file used to store user database information.
CVE-2003-1367(PUBLISHED):The which_access variable for Majordomo 2.0 through 1.94.4, and possibly earlier versions, is set to "open" by default, which allows remote attackers to identify the email addresses of members of mailing lists via a "which" command.
CVE-2003-1368(PUBLISHED):Buffer overflow in the 32bit FTP client 9.49.1 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a long FTP server banner.
CVE-2003-1369(PUBLISHED):Buffer overflow in ByteCatcher FTP client 1.04b allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a long FTP server banner.
CVE-2003-1370(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Nuked-Klan 1.2b allow remote attackers to inject arbitrary HTML or web script via (1) the Author field in the Guestbook module, (2) the Titre or Pseudo fields in the Forum module, or (3) "La Tribune Libre" in the Shoutbox module.
CVE-2003-1371(PUBLISHED):Nuked-Klan 1.3b, and possibly earlier versions, allows remote attackers to obtain sensitive server information via an op parameter set to phpinfo for the (1) Team, (2) News, or (3) Liens modules.
CVE-2003-1372(PUBLISHED):Cross-site scripting (XSS) vulnerability in links.php script in myPHPNuke 1.8.8, and possibly earlier versions, allows remote attackers to inject arbitrary HTML and web script via the (1) ratenum or (2) query parameters.
CVE-2003-1373(PUBLISHED):Directory traversal vulnerability in auth.php for PhpBB 1.4.0 through 1.4.4 allows remote attackers to read and include arbitrary files via .. (dot dot) sequences followed by NULL (%00) characters in CGI parameters, as demonstrated using the lang parameter in prefs.php.
CVE-2003-1374(PUBLISHED):Buffer overflow in disable of HP-UX 11.0 may allow local users to execute arbitrary code via a long argument to the (1) -r or (2)-c options.
CVE-2003-1375(PUBLISHED):Buffer overflow in wall for HP-UX 10.20 through 11.11 may allow local users to execute arbitrary code by calling wall with a large file as an argument.
CVE-2003-1376(PUBLISHED):WinZip 8.0 uses weak random number generation for password protected ZIP files, which allows local users to brute force the encryption keys and extract the data from the zip file by guessing the state of the stream coder.
CVE-2003-1377(PUBLISHED):Buffer overflow in the reverse DNS lookup of Smart IRC Daemon (SIRCD) 0.4.0 and 0.4.4 allows remote attackers to execute arbitrary code via a client with a long hostname.
CVE-2003-1378(PUBLISHED):Microsoft Outlook Express 6.0 and Outlook 2000, with the security zone set to Internet Zone, allows remote attackers to execute arbitrary programs via an HTML email with the CODEBASE parameter set to the program, a vulnerability similar to CAN-2002-0077.
CVE-2003-1379(PUBLISHED):clarkconnectd in ClarkConnect Linux 1.2 allows remote attackers to obtain sensitive information about the server via the characters (1) A, which reveals the date and time, (2) F, (3) M, which reveals 'ifconfig' information, (4) P, which lists the processes, (5) Y, which reveals the snort log files, or (6) b, which reveals /var/log/messages.
CVE-2003-1380(PUBLISHED):Directory traversal vulnerability in BisonFTP Server 4 release 2 allows remote attackers to (1) list directories above the root via an 'ls @../' command, or (2) list files above the root via a "mget @../FILE" command.
CVE-2003-1381(PUBLISHED):Format string vulnerability in AMX 0.9.2 and earlier, a plugin for Valve Software's Half-Life Server, allows remote attackers to execute arbitrary commands via format string specifiers in the amx_say command.
CVE-2003-1382(PUBLISHED):Buffer overflow in ISMail 1.4.3 and earlier allow remote attackers to execute arbitrary code via long domain names in (1) MAIL FROM or (2) RCPT TO fields.
CVE-2003-1383(PUBLISHED):WEB-ERP 0.1.4 and earlier allows remote attackers to obtain sensitive information via an HTTP request for the logicworks.ini file, which contains the MySQL database username and password.
CVE-2003-1384(PUBLISHED):Cross-site scripting (XSS) vulnerability in index.php in PY-Livredor 1.0 allows remote attackers to insert arbitrary web script or HTML via the (1) titre, (2) Votre pseudo, (3) Votre e-mail, or (4) Votre message fields.
CVE-2003-1385(PUBLISHED):ipchat.php in Invision Power Board 1.1.1 allows remote attackers to execute arbitrary PHP code, if register_globals is enabled, by modifying the root_path parameter to reference a URL on a remote web server that contains the code.
CVE-2003-1386(PUBLISHED):AXIS 2400 Video Server 2.00 through 2.33 allows remote attackers to obtain sensitive information via an HTTP request to /support/messages, which displays the server's /var/log/messages file.
CVE-2003-1387(PUBLISHED):Buffer overflow in Opera 6.05 and 6.06, and possibly other versions, allows remote attackers to execute arbitrary code via a URL with a long username.
CVE-2003-1388(PUBLISHED):Buffer overflow in Opera 7.02 Build 2668 allows remote attackers to crash Opera via a long HTTP request ending in a .ZIP extension.
CVE-2003-1389(PUBLISHED):RTS CryptoBuddy 1.2 and earlier truncates long passphrases without warning the user, which may make it easier to conduct certain brute force guessing attacks.
CVE-2003-1390(PUBLISHED):RTS CryptoBuddy 1.2 and earlier stores bytes 53 through 55 of a 55-byte passphrase in plaintext, which makes it easier for local users to guess the passphrase.
CVE-2003-1391(PUBLISHED):RTS CryptoBuddy 1.0 and 1.2 uses a weak encryption algorithm for the passphrase and generates predictable keys, which makes it easier for attackers to guess the passphrase.
CVE-2003-1392(PUBLISHED):CryptoBuddy 1.0 and 1.2 does not use the user-supplied passphrase to encrypt data, which could allow local users to use their own passphrase to decrypt the data.
CVE-2003-1393(PUBLISHED):Buffer overflow in Gupta SQLBase 8.1.0 allows remote attackers to cause a denial of service and possibly execute arbitrary code via a long EXECUTE command.
CVE-2003-1394(PUBLISHED):CoffeeCup Software Password Wizard 4.0 stores sensitive information such as usernames and passwords in a .apw file under the web document root with insufficient access control, which allows remote attackers to obtain that information via a direct request for the file.
CVE-2003-1395(PUBLISHED):Buffer overflow in KaZaA Media Desktop 2.0 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a response to the ad server.
CVE-2003-1396(PUBLISHED):Heap-based buffer overflow in Opera 6.05 through 7.10 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a filename with a long extension.
CVE-2003-1397(PUBLISHED):The PluginContext object of Opera 6.05 and 7.0 allows remote attackers to cause a denial of service (crash) via an HTTP request containing a long string that gets passed to the ShowDocument method.
CVE-2003-1398(PUBLISHED):Cisco IOS 12.0 through 12.2, when IP routing is disabled, accepts false ICMP redirect messages, which allows remote attackers to cause a denial of service (network routing modification).
CVE-2003-1399(PUBLISHED):eject 2.0.10, when installed setuid on systems such as SuSE Linux 7.3, generates different error messages depending on whether a specified file exists or not, which allows local users to obtain sensitive information.
CVE-2003-1400(PUBLISHED):Cross-site scripting (XSS) vulnerability in the Your_Account module for PHP-Nuke 5.0 through 6.0 allows remote attackers to inject arbitrary web script or HTML via the user_avatar parameter.
CVE-2003-1401(PUBLISHED):login.php in php-Board 1.0 stores plaintext passwords in $username.txt with insufficient access control under the web document root, which allows remote attackers to obtain sensitive information via a direct request.
CVE-2003-1402(PUBLISHED):PHP remote file inclusion vulnerability in hit.php for Kietu 2.0 and 2.3 allows remote attackers to execute arbitrary PHP code via the url_hit parameter, a different vulnerability than CVE-2006-5015.
CVE-2003-1403(PUBLISHED):foo.php3 in DotBr 0.1 allows remote attackers to obtain sensitive information via a direct request, which calls the phpinfo function.
CVE-2003-1404(PUBLISHED):DotBr 0.1 stores config.inc with insufficient access control under the web document root, which allows remote attackers to obtain sensitive information such as SQL usernames and passwords.
CVE-2003-1405(PUBLISHED):DotBr 0.1 allows remote attackers to execute arbitrary shell commands via the cmd parameter to (1) exec.php3 or (2) system.php3.
CVE-2003-1406(PUBLISHED):PHP remote file inclusion vulnerability in D-Forum 1.00 through 1.11 allows remote attackers to execute arbitrary PHP code via a URL in the (1) my_header parameter to header.php3 or (2) my_footer parameter to footer.php3.
CVE-2003-1407(PUBLISHED):Buffer overflow in cmd.exe in Windows NT 4.0 may allow local users to execute arbitrary code via a long pathname argument to the cd command.
CVE-2003-1408(PUBLISHED):Lotus Domino Server 5.0 and 6.0 allows remote attackers to read the source code for files via an HTTP request with a filename with a trailing dot.
CVE-2003-1409(PUBLISHED):TOPo 1.43 allows remote attackers to obtain sensitive information by sending an HTTP request with an invalid parameter to (1) in.php or (2) out.php, which reveals the path to the TOPo directory in the error message.
CVE-2003-1410(PUBLISHED):PHP remote file inclusion vulnerability in email.php (aka email.php3) in Cedric Email Reader 0.2 and 0.3 allows remote attackers to execute arbitrary PHP code via the cer_skin parameter.
CVE-2003-1411(PUBLISHED):PHP remote file inclusion vulnerability in emailreader_execute_on_each_page.inc.php in Cedric Email Reader 0.4 allows remote attackers to execute arbitrary PHP code via the emailreader_ini parameter.
CVE-2003-1412(PUBLISHED):PHP remote file inclusion vulnerability in index.php for GONiCUS System Administrator (GOsa) 1.0 allows remote attackers to execute arbitrary PHP code via the plugin parameter to (1) 3fax/1blocklists/index.php; (2) 6departamentadmin/index.php, (3) 5terminals/index.php, (4) 4mailinglists/index.php, (5) 3departaments/index.php, and (6) 2groupd/index.php in 2administration/; or (7) the base parameter to include/help.php.
CVE-2003-1413(PUBLISHED):parse_xml.cgi in Apple Darwin Streaming Server 4.1.1 allows remote attackers to determine the existence of arbitrary files by using ".." sequences in the filename parameter and comparing the resulting error messages.
CVE-2003-1414(PUBLISHED):Directory traversal vulnerability in parse_xml.cg Apple Darwin Streaming Server 4.1.2 and Apple Quicktime Streaming Server 4.1.1 allows remote attackers to read arbitrary files via a ... (triple dot) in the filename parameter.
CVE-2003-1415(PUBLISHED):NetCharts XBRL Server 4.0.0 allows remote attackers to obtain sensitive information via an HTTP request with an invalid chunked transfer encoding specification.
CVE-2003-1416(PUBLISHED):BisonFTP Server 4 release 2 allows remote attackers to cause a denial of service (CPU consumption) via a long (1) ls or (2) cwd command.
CVE-2003-1417(PUBLISHED):nCipher Support Software 6.00, when using generatekey KeySafe to import keys, does not delete the temporary copies of the key, which may allow local users to gain access to the key by reading the (1) key.pem or (2) key.der files.
CVE-2003-1418(PUBLISHED):Apache HTTP Server 1.3.22 through 1.3.27 on OpenBSD allows remote attackers to obtain sensitive information via (1) the ETag header, which reveals the inode number, or (2) multipart MIME boundary, which reveals child process IDs (PID).
CVE-2003-1419(PUBLISHED):Netscape 7.0 allows remote attackers to cause a denial of service (crash) via a web page with an invalid regular expression argument to the JavaScript reformatDate function.
CVE-2003-1420(PUBLISHED):Cross-site scripting (XSS) vulnerability in Opera 6.0 through 7.0 with automatic redirection disabled allows remote attackers to inject arbitrary web script or HTML via the HTTP Location header.
CVE-2003-1421(PUBLISHED):Unspecified vulnerability in mod_mysql_logger shared object in SuckBot 0.006 allows remote attackers to cause a denial of service (seg fault) via unknown attack vectors.
CVE-2003-1422(PUBLISHED):Multiple unspecified vulnerabilities in the installer for SYSLINUX 2.01, when running setuid root, allow local users to gain privileges via unknown vectors.
CVE-2003-1423(PUBLISHED):Petitforum stores the liste.txt data file under the web document root with insufficient access control, which allows remote attackers to obtain sensitive information such as e-mail addresses and encrypted passwords.
CVE-2003-1424(PUBLISHED):message.php in Petitforum does not properly authenticate users, which allows remote attackers to impersonate forum users via a modified connect cookie.
CVE-2003-1425(PUBLISHED):guestbook.cgi in cPanel 5.0 allows remote attackers to execute arbitrary commands via the template parameter.
CVE-2003-1426(PUBLISHED):Openwebmail in cPanel 5.0, when run using suid Perl, adds the directory in the SCRIPT_FILENAME environment variable to Perl's @INC include array, which allows local users to execute arbitrary code by modifying SCRIPT_FILENAME to reference a directory containing a malicious openwebmail-shared.pl executable.
CVE-2003-1427(PUBLISHED):Directory traversal vulnerability in the web configuration interface in Netgear FM114P 1.4 allows remote attackers to read arbitrary files, such as the netgear.cfg configuration file, via a hex-encoded (%2e%2e%2f) ../ (dot dot slash) in the port parameter.
CVE-2003-1428(PUBLISHED):Gallery 1.3.3 creates directories with insecure permissions, which allows local users to read, modify, or delete photos.
CVE-2003-1429(PUBLISHED):Buffer overflow in Proxomitron Naoko 4.4 allows remote attackers to execute arbitrary code via a long request.
CVE-2003-1430(PUBLISHED):Directory traversal vulnerability in Unreal Tournament Server 436 and earlier allows remote attackers to access known files via a ".." (dot dot) in an unreal:// URL.
CVE-2003-1431(PUBLISHED):Buffer overflow in Epic Games Unreal Engine 226f through 436 allows remote attackers to cause a denial of service (crash) via a long host string in the Unreal URL.
CVE-2003-1432(PUBLISHED):Epic Games Unreal Engine 226f through 436 allows remote attackers to cause a denial of service (CPU consumption or crash) and possibly execute arbitrary code via (1) a packet with a negative size value, which is treated as a large positive number during memory allocation, or (2) a negative size value in a package file.
CVE-2003-1433(PUBLISHED):Epic Games Unreal Engine 226f through 436 does not validate the challenge key, which allows remote attackers to exhaust the player limit by joining the game multiple times.
CVE-2003-1434(PUBLISHED):login_ldap 3.1 and 3.2 allows remote attackers to initiate unauthenticated bind requests if (1) bind_anon_dn is on, which allows a bind with no password provided, (2) bind_anon_cred is on, which allows a bind with no DN, or (3) bind_anon is on, which allows a bind with no DN or password.
CVE-2003-1435(PUBLISHED):SQL injection vulnerability in PHP-Nuke 5.6 and 6.0 allows remote attackers to execute arbitrary SQL commands via the days parameter to the search module.
CVE-2003-1436(PUBLISHED):PHP remote file inclusion vulnerability in nukebrowser.php in Nukebrowser 2.1 to 2.5 allows remote attackers to execute arbitrary PHP code via the filhead parameter.
CVE-2003-1437(PUBLISHED):BEA WebLogic Express and WebLogic Server 7.0 and *******, stores passwords in plaintext when a keystore is used to store a private key or trust certificate authorities, which allows local users to gain access.
CVE-2003-1438(PUBLISHED):Race condition in BEA WebLogic Server and Express 5.1 through *******, when using in-memory session replication or replicated stateful session beans, causes the same buffer to be provided to two users, which could allow one user to see session data that was intended for another user.
CVE-2003-1439(PUBLISHED):Secure Internet Live Conferencing (SILC) 0.9.11 and 0.9.12 stores passwords and sessions in plaintext in memory, which could allow local users to obtain sensitive information.
CVE-2003-1440(PUBLISHED):SpamProbe 0.8a allows remote attackers to cause a denial of service (crash) via HTML e-mail with newline characters within an href tag, which is not properly handled by certain regular expressions.
CVE-2003-1441(PUBLISHED):Posadis 0.50.4 through 0.50.8 allows remote attackers to cause a denial of service (crash) via a DNS message without a question section, which triggers null dereference.
CVE-2003-1442(PUBLISHED):The web administration page for the Ericsson HM220dp ADSL modem does not require authentication, which could allow remote attackers to gain access from the LAN side.
CVE-2003-1443(PUBLISHED):Kaspersky Antivirus (KAV) 4.0.9.0 does not detect viruses in files with MS-DOS device names in their filenames, which allows local users to bypass virus protection, as demonstrated using aux.vbs and aux.com.
CVE-2003-1444(PUBLISHED):Kaspersky Antivirus (KAV) 4.0.9.0 allows local users to cause a denial of service (CPU consumption or crash) and prevent malicious code from being detected via a file with a long pathname.
CVE-2003-1445(PUBLISHED):Stack-based buffer overflow in Far Manager 1.70beta1 and earlier allows local users to cause a denial of service (crash) and possibly execute arbitrary code via a long pathname.
CVE-2003-1446(PUBLISHED):Buffer overflow in the save_into_file function in save.c for Rogue 5.2-2 allows local users to execute arbitrary code with games group privileges by setting a long HOME environment variable and invoking the save game function with a ~ (tilde).
CVE-2003-1447(PUBLISHED):IBM WebSphere Advanced Server Edition 4.0.4 uses a weak encryption algorithm (XOR and base64 encoding), which allows local users to decrypt passwords when the configuration file is exported to XML.
CVE-2003-1448(PUBLISHED):Memory leak in the Windows 2000 kernel allows remote attackers to cause a denial of service (SMB request hang) via a NetBIOS continuation packet.
CVE-2003-1449(PUBLISHED):Aladdin Knowlege Systems eSafe Gateway ********* does not check the entire stream of Content Vectoring Protocol (CVP) data, which allows remote attackers to bypass virus protection.
CVE-2003-1450(PUBLISHED):BitchX 75p3 and 1.0c16 through 1.0c20cvs allows remote attackers to cause a denial of service (segmentation fault) via a malformed RPL_NAMREPLY numeric 353 message.
CVE-2003-1451(PUBLISHED):Buffer overflow in Symantec Norton AntiVirus 2002 allows remote attackers to execute arbitrary code via an e-mail attachment with a compressed ZIP file that contains a file with a long filename.
CVE-2003-1452(PUBLISHED):Untrusted search path vulnerability in Qualcomm qpopper 4.0 through 4.05 allows local users to execute arbitrary code by modifying the PATH environment variable to reference a malicious smbpasswd program.
CVE-2003-1453(PUBLISHED):Cross-site scripting (XSS) vulnerability in the MytextSanitizer function in XOOPS 1.3.5 through 1.3.9 and XOOPS 2.0 through 2.0.1 allows remote attackers to inject arbitrary web script or HTML via a javascript: URL in an IMG tag.
CVE-2003-1454(PUBLISHED):Invision Power Services Invision Board 1.0 through 1.1.1, when a forum is password protected, stores the administrator password in a cookie in plaintext, which could allow remote attackers to gain access.
CVE-2003-1455(PUBLISHED):Multiple buffer overflows in the launch_bcrelay function in pptpctrl.c in PoPToP 1.1.4-b1 through PoPToP 1.1.4-b3 allow local users to execute arbitrary code.
CVE-2003-1456(PUBLISHED):Album.pl 6.1 allows remote attackers to execute arbitrary commands, when an alternative configuration file is used, via unknown attack vectors.
CVE-2003-1457(PUBLISHED):Auerswald COMsuite CTI ControlCenter 3.1 creates a default "runasositron" user account with an easily guessable password, which allows local users or remote attackers to gain access.
CVE-2003-1458(PUBLISHED):SQL injection vulnerability in Profile.php in ttCMS 2.2 and ttForum allows remote attackers to execute arbitrary SQL commands via the member name.
CVE-2003-1459(PUBLISHED):Multiple PHP remote file inclusion vulnerabilities in ttCMS 2.2 and ttForum allow remote attackers to execute arbitrary PHP code via the (1) template parameter in News.php or (2) installdir parameter in install.php.
CVE-2003-1460(PUBLISHED):Worker Filemanager 1.0 through 2.7 sets the permissions on the destination directory to world-readable and executable while copying data, which could allow local users to obtain sensitive information.
CVE-2003-1461(PUBLISHED):Buffer overflow in rwrite for HP-UX 11.0 could allow local users to execute arbitrary code via a long argument.  NOTE: the vendor was unable to reproduce the problem on a system that had been patched for an lp vulnerability (CVE-2002-1473).
CVE-2003-1462(PUBLISHED):mod_survey 3.0.0 through 3.0.15-pre6 does not check whether a survey exists before creating a subdirectory for it, which allows remote attackers to cause a denial of service (disk consumption and possible crash).
CVE-2003-1463(PUBLISHED):Absolute path traversal vulnerability in Alt-N Technologies WebAdmin 2.0.0 through 2.0.2 allows remote attackers with administrator privileges to (1) determine the installation path by reading the contents of the Name parameter in a link, and (2) read arbitrary files via an absolute path in the Name parameter.
CVE-2003-1464(PUBLISHED):Buffer overflow in Siemens 45 series mobile phones allows remote attackers to cause a denial of service (disconnect and unavailable inbox) via a Short Message Service (SMS) message with a long image name.
CVE-2003-1465(PUBLISHED):Directory traversal vulnerability in download.php in Phorum 3.4 through 3.4.2 allows remote attackers to read arbitrary files.
CVE-2003-1466(PUBLISHED):Unspecified vulnerability in Phorum 3.4 through 3.4.2 allows remote attackers to use Phorum as a connection proxy to other sites via (1) register.php or (2) login.php.
CVE-2003-1467(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in (1) login.php, (2) register.php, (3) post.php, and (4) common.php in Phorum before 3.4.3 allow remote attackers to inject arbitrary web script or HTML via unknown attack vectors.
CVE-2003-1468(PUBLISHED):The Web_Links module in PHP-Nuke 6.0 through 6.5 final allows remote attackers to obtain the full web server path via an invalid cid parameter that is non-numeric or null, which leaks the pathname in an error message.
CVE-2003-1469(PUBLISHED):The default configuration of ColdFusion MX has the "Enable Robust Exception Information" option selected, which allows remote attackers to obtain the full path of the web server via a direct request to CFIDE/probe.cfm, which leaks the path in an error message.
CVE-2003-1470(PUBLISHED):Buffer overflow in IMAP service in MDaemon 6.7.5 and earlier allows remote authenticated users to cause a denial of service (crash) and execute arbitrary code via a CREATE command with a long mailbox name.
CVE-2003-1471(PUBLISHED):MDaemon POP server 6.0.7 and earlier allows remote authenticated users to cause a denial of service (crash) via a (1) DELE or (2) UIDL with a negative number.
CVE-2003-1472(PUBLISHED):Buffer overflow in 3D-FTP client 4.0 allows remote FTP servers to cause a denial of service (crash) and possibly execute arbitrary code via a long banner.
CVE-2003-1473(PUBLISHED):Buffer overflow in LTris 1.0.1 of FreeBSD Ports Collection 2003-02-25 and earlier allows local users to execute arbitrary code with gid "games" permission via a long HOME environment variable.
CVE-2003-1474(PUBLISHED):slashem-tty in the FreeBSD Ports Collection is installed with write permissions for the games group, which allows local users with group games privileges to modify slashem-tty and execute arbitrary code as other users, as demonstrated using a separate vulnerability in LTris.
CVE-2003-1475(PUBLISHED):Netbus 1.5 through 1.7 allows more than one client to be connected at the same time, but only prompts the first connection for authentication, which allows remote attackers to gain access.
CVE-2003-1476(PUBLISHED):Cerberus FTP Server 2.1 stores usernames and passwords in plaintext, which could allow local users to gain access.
CVE-2003-1477(PUBLISHED):MAILsweeper for SMTP 4.3.6 and 4.3.7 allows remote attackers to cause a denial of service (CPU consumption) via a PowerPoint attachment that either (1) is corrupt or (2) contains "embedded objects."
CVE-2003-1478(PUBLISHED):Konqueror in KDE 3.0.3 allows remote attackers to cause a denial of service (core dump) via a web page that begins with a "xFFxFE" byte sequence and a large number of CRLF sequences, as demonstrated using freeze.htm.
CVE-2003-1479(PUBLISHED):Cross-site scripting (XSS) vulnerability in webcamXP 1.02.432 and 1.02.535 allows remote attackers to inject arbitrary web script or HTML via the message field.
CVE-2003-1480(PUBLISHED):MySQL 3.20 through 4.1.0 uses a weak algorithm for hashed passwords, which makes it easier for attackers to decrypt the password via brute force methods.
CVE-2003-1481(PUBLISHED):CommuniGate Pro 3.1 through 4.0.6 sends the session ID in the referer field for an HTTP request for an image, which allows remote attackers to hijack mail sessions via an e-mail with an IMG tag that references a malicious URL that captures the referer.
CVE-2003-1482(PUBLISHED):The backup configuration file for Microsoft MN-500 wireless base station stores administrative passwords in plaintext, which allows local users to gain access.
CVE-2003-1483(PUBLISHED):FlashFXP 1.4 uses a weak encryption algorithm for user passwords, which allows attackers to decrypt the passwords and gain access.
CVE-2003-1484(PUBLISHED):Microsoft Internet Explorer 6.0 SP1 allows remote attackers to cause a denial of service (crash) by creating a DHTML link that uses the AnchorClick "A" object with a blank href attribute.
CVE-2003-1485(PUBLISHED):Clearswift MAILsweeper 4.0 through 4.3.7 allows remote attackers to bypass filtering via a file attachment that contains "multiple extensions combined with large blocks of white space."
CVE-2003-1486(PUBLISHED):Phorum 3.4 through 3.4.2 allows remote attackers to obtain the full path of the web server via an incorrect HTTP request to (1) smileys.php, (2) quick_listrss.php, (3) purge.php, (4) news.php, (5) memberlist.php, (6) forum_listrss.php, (7) forum_list_rdf.php, (8) forum_list.php, or (9) move.php, which leaks the information in an error message.
CVE-2003-1487(PUBLISHED):Multiple "command injection" vulnerabilities in Phorum 3.4 through 3.4.2 allow remote attackers to execute arbitrary commands and modify the Phorum configuration files via the (1) UserAdmin program, (2) Edit user profile, or (3) stats program.
CVE-2003-1488(PUBLISHED):The (1) verif_admin.php and (2) check_admin.php scripts in Truegalerie 1.0 allow remote attackers to gain administrator access via a request to admin.php without the connect parameter and with the loggedin parameter set to any value, such as 1.
CVE-2003-1489(PUBLISHED):upload.php in Truegalerie 1.0 allows remote attackers to read arbitrary files by specifying the target filename in the file cookie in form.php, then downloading the file from the image gallery.
CVE-2003-1490(PUBLISHED):SonicWall Pro running firmware ******* allows remote attackers to cause a denial of service (device reset) via a long HTTP POST to the internal interface, possibly due to a buffer overflow.
CVE-2003-1491(PUBLISHED):Kerio Personal Firewall (KPF) 2.1.4 has a default rule to accept incoming packets from DNS (UDP port 53), which allows remote attackers to bypass the firewall filters via packets with a source port of 53.
CVE-2003-1492(PUBLISHED):Netscape Navigator 7.0.2 and Mozilla allows remote attackers to access cookie information in a different domain via an HTTP request for a domain with an extra . (dot) at the end.
CVE-2003-1493(PUBLISHED):Memory leak in HP OpenView Network Node Manager (NNM) 6.2 and 6.4 allows remote attackers to cause a denial of service (memory exhaustion) via crafted TCP packets.
CVE-2003-1494(PUBLISHED):Unspecified vulnerability in HP OpenView Network Node Manager (NNM) 6.2 and 6.4 allows remote attackers to cause a denial of service (CPU consumption) via a crafted TCP packet.
CVE-2003-1495(PUBLISHED):Unspecified vulnerability in the non-SSL web agent in various HP Management Agent products allows local users or remote attackers to gain privileges or cause a denial of service via unknown attack vectors.
CVE-2003-1496(PUBLISHED):Unspecified vulnerability in CDE dtmailpr of HP Tru64 4.0F through 5.1B allows local users to gain privileges via unknown attack vectors. NOTE: due to lack of details in the vendor advisory, it is not clear whether this is the same issue as CVE-1999-0840.
CVE-2003-1497(PUBLISHED):Buffer overflow in the system log viewer of Linksys BEFSX41 1.44.3 allows remote attackers to cause a denial of service via an HTTP request with a long Log_Page_Num variable.
CVE-2003-1498(PUBLISHED):Cross-site scripting (XSS) vulnerability in search.php for WRENSOFT Zoom Search Engine 2.0 Build 1018 and earlier allows remote attackers to inject arbitrary web script or HTML via the zoom_query parameter.
CVE-2003-1499(PUBLISHED):Directory traversal vulnerability in index.php in Bytehoard 0.7 allows remote attackers to read arbitrary files via a .. (dot dot) in the infolder parameter.
CVE-2003-1500(PUBLISHED):PHP remote file inclusion vulnerability in _functions.php in cpCommerce 0.5f allows remote attackers to execute arbitrary code via the prefix parameter.
CVE-2003-1501(PUBLISHED):Directory traversal vulnerability in the file upload CGI of Gast Arbeiter 1.3 allows remote attackers to write arbitrary files via a .. (dot dot) in the req_file parameter.
CVE-2003-1502(PUBLISHED):mod_throttle 3.0 allows local users with Apache privileges to access shared memory that points to a file that is writable by the apache user, which could allow local users to gain privileges.
CVE-2003-1503(PUBLISHED):Buffer overflow in AOL Instant Messenger (AIM) 5.2.3292 allows remote attackers to execute arbitrary code via an aim:getfile URL with a long screen name.
CVE-2003-1504(PUBLISHED):SQL injection vulnerability in variables.php in Goldlink 3.0 allows remote attackers to execute arbitrary SQL commands via the (1) vadmin_login or (2) vadmin_pass cookie in a request to goldlink.php.
CVE-2003-1505(PUBLISHED):Microsoft Internet Explorer 6.0 allows remote attackers to cause a denial of service (crash) by creating a web page or HTML e-mail with a textarea in a div element whose scrollbar-base-color is modified by a CSS style, which is then moved.
CVE-2003-1506(PUBLISHED):Cross-site scripting (XSS) vulnerability in dansguardian.pl in Adelix CensorNet 3.0 through 3.2 allows remote attackers to execute arbitrary script as other users by injecting arbitrary HTML or script into the DENIEDURL parameter.
CVE-2003-1507(PUBLISHED):Planet Technology WGSD-1020 and WSW-2401 Ethernet switches use a default "superuser" account with the "planet" password, which allows remote attackers to gain administrative access.
CVE-2003-1508(PUBLISHED):Buffer overflow in mIRC 6.12, when the DCC get dialog window has been minimized and the user opens the minimized window, allows remote attackers to cause a denial of service (crash) via a long filename.
CVE-2003-1509(PUBLISHED):Real Networks RealOne Enterprise Desktop 6.0.11.774, RealOne Player 2.0, and RealOne Player 6.0.11.818 through RealOne Player 6.0.11.853 allows remote attackers to execute arbitrary script in the local security zone by embedding script in a temp file before the temp file is executed by the default web browser.
CVE-2003-1510(PUBLISHED):TinyWeb 1.9 allows remote attackers to cause a denial of service (CPU consumption) via a ".%00." in an HTTP GET request to the cgi-bin directory.
CVE-2003-1511(PUBLISHED):Cross-site scripting (XSS) vulnerability in Bajie Java HTTP Server 0.95 through 0.95zxv4 allows remote attackers to inject arbitrary web script or HTML via (1) the query string to test.txt, (2) the guestName parameter to the custMsg servlet, or (3) the cookiename parameter to the CookieExample servlet.
CVE-2003-1512(PUBLISHED):Buffer overflow in mIRC 6.1 and 6.11 allows remote attackers to cause a denial of service (crash) via a long DCC SEND request.
CVE-2003-1513(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in example scripts in Caucho Technology Resin 2.0 through 2.1.2 allow remote attackers to inject arbitrary web script or HTML via (1) env.jsp, (2) form.jsp, (3) session.jsp, (4) the move parameter to tictactoe.jsp, or the (5) name or (6) comment fields to guestbook.jsp.
CVE-2003-1514(PUBLISHED):eMule 0.29c allows remote attackers to cause a denial of service (crash) via a long password, possibly due to a buffer overflow.
CVE-2003-1515(PUBLISHED):Origo ASR-8100 ADSL Router 3.21 has an administration service running on port 254 that does not require a password, which allows remote attackers to cause a denial of service by restoring the factory defaults.
CVE-2003-1516(PUBLISHED):The org.apache.xalan.processor.XSLProcessorVersion class in Java Plug-in 1.4.2_01 allows signed and unsigned applets to share variables, which violates the Java security model and could allow remote attackers to read or write data belonging to a signed applet.
CVE-2003-1517(PUBLISHED):cart.pl in Dansie shopping cart allows remote attackers to obtain the installation path via an invalid db parameter, which leaks the path in an error message.
CVE-2003-1518(PUBLISHED):Adiscon WinSyslog 4.21 SP1 allows remote attackers to cause a denial of service (CPU consumption) via a long syslog message.
CVE-2003-1519(PUBLISHED):Cross-site scripting (XSS) vulnerability in Vivisimo clustering engine allows remote attackers to inject arbitrary web script or HTML via the query parameter to the search program.
CVE-2003-1520(PUBLISHED):SQL injection vulnerability in FuzzyMonkey My Classifieds 2.11 allows remote attackers to execute arbitrary SQL commands via the email parameter.
CVE-2003-1521(PUBLISHED):Sun Java Plug-In 1.4 through 1.4.2_02 allows remote attackers to repeatedly access the floppy drive via the createXmlDocument method in the org.apache.crimson.tree.XmlDocument class, which violates the Java security model.
CVE-2003-1522(PUBLISHED):Cross-site scripting (XSS) vulnerability in PSCS VPOP3 Web Mail server 2.0e and 2.0f allows remote attackers to inject arbitrary web script or HTML via the redirect parameter to the admin/index.html page.
CVE-2003-1523(PUBLISHED):SQL injection vulnerability in the IMAP daemon in dbmail 1.1 allows remote attackers to execute arbitrary SQL commands via the (1) login username, (2) mailbox name, and possibly other attack vectors.
CVE-2003-1524(PUBLISHED):PGPi PGPDisk 6.0.2i does not unmount a PGP partition when the switch user function in Windows XP is used, which could allow local users to access data on another user's PGP partition.
CVE-2003-1525(PUBLISHED):Unspecified vulnerability in My Photo Gallery 3.5, and possibly earlier versions, has unknown impact and attack vectors.
CVE-2003-1526(PUBLISHED):PHP-Nuke 7.0 allows remote attackers to obtain the installation path via certain characters such as (1) ", (2) ', or (3) > in the search field, which reveals the path in an error message.
CVE-2003-1527(PUBLISHED):BlackICE Defender 2.9.cap and Server Protection 3.5.cdf, when configured to automatically block attacks, allows remote attackers to block IP addresses and cause a denial of service via spoofed packets.
CVE-2003-1528(PUBLISHED):nsr_shutdown in Fujitsu Siemens NetWorker 6.0 allows local users to overwrite arbitrary files via a symlink attack on the nsrsh[PID] temporary file.
CVE-2003-1529(PUBLISHED):Directory traversal vulnerability in Seagull Software Systems J Walk application server 3.2C9, and other versions before 3.3c4, allows remote attackers to read arbitrary files via a ".%252e" (encoded dot dot) in the URL.
CVE-2003-1530(PUBLISHED):SQL injection vulnerability in privmsg.php in phpBB 2.0.3 and earlier allows remote attackers to execute arbitrary SQL commands via the mark[] parameter.
CVE-2003-1531(PUBLISHED):Cross-site scripting (XSS) vulnerability in testcgi.exe in Lilikoi Software Ceilidh 2.70 and earlier allows remote attackers to inject arbitrary web script or HTML via the query string.
CVE-2003-1532(PUBLISHED):SQL injection vulnerability in compte.php in PhpMyShop 1.00 allows remote attackers to execute arbitrary SQL commands via the (1) identifiant and (2) password parameters.
CVE-2003-1533(PUBLISHED):SQL injection vulnerability in accesscontrol.php in PhpPass 2 allows remote attackers to execute arbitrary SQL commands via the (1) uid and (2) pwd parameters.
CVE-2003-1534(PUBLISHED):Cross-site scripting (XSS) vulnerability in jgb.php3 in Justice Guestbook 1.3 allows remote attackers to inject arbitrary web script or HTML via the (1) name, (2) homepage, (3) aim, (4) yim, (5) location, and (6) comment variables.
CVE-2003-1535(PUBLISHED):Justice Guestbook 1.3 allows remote attackers to obtain the full installation path via a direct request to cfooter.php3, which leaks the path in an error message.
CVE-2003-1536(PUBLISHED):Multiple cross-site scripting (XSS) vulnerabilities in Codeworx Technologies DCP-Portal 5.3.1 allow remote attackers to inject arbitrary web script or HTML via (1) the q parameter to search.php and (2) the year parameter to calendar.php.
CVE-2003-1537(PUBLISHED):Directory traversal vulnerability in PostNuke 0.723 and earlier allows remote attackers to include arbitrary files named theme.php via the theme parameter to index.php.
CVE-2003-1538(PUBLISHED):susehelp in SuSE Linux 8.1, Enterprise Server 8, Office Server, and Openexchange Server 4 does not properly filter shell metacharacters, which allows remote attackers to execute arbitrary commands via CGI queries.
CVE-2003-1539(PUBLISHED):Cross-site scripting (XSS) vulnerability in ONEdotOH Simple File Manager (SFM) before 0.21 allows remote attackers to inject arbitrary web script or HTML via (1) file names and (2) directory names.
CVE-2003-1540(PUBLISHED):WF-Chat 1.0 Beta stores sensitive information under the web root with insufficient access control, which allows remote attackers to obtain authentication information via a direct request to (1) !pwds.txt and (2) !nicks.txt.
CVE-2003-1541(PUBLISHED):PlanetMoon Guestbook tr3.a stores sensitive information under the web root with insufficient access control, which allows remote attackers to obtain the admin script password, and other passwords, via a direct request to files/passwd.txt.
CVE-2003-1542(PUBLISHED):Directory traversal vulnerability in plugins/file.php in phpWebFileManager before 0.4.4 allows remote attackers to read arbitrary files via a .. (dot dot) in the fm_path parameter.
CVE-2003-1543(PUBLISHED):Cross-site scripting (XSS) vulnerability in Bajie Http Web Server 0.95zxe, 0.95zxc, and possibly others, allows remote attackers to inject arbitrary web script or HTML via the query string, which is reflected in an error message.
CVE-2003-1544(PUBLISHED):Unrestricted critical resource lock in Terminal Services for Windows 2000 before SP4 and Windows XP allows remote authenticated users to cause a denial of service (reboot) by obtaining a read lock on msgina.dll, which prevents msgina.dll from being loaded.
CVE-2003-1545(PUBLISHED):Absolute path traversal vulnerability in nukestyles.com viewpage.php addon for PHP-Nuke allows remote attackers to read arbitrary files via a full pathname in the file parameter.  NOTE: This was originally reported as an issue in PHP-Nuke 6.5, but this is an independent addon.
CVE-2003-1546(PUBLISHED):Cross-site scripting (XSS) vulnerability in gbook.php in Filebased guestbook 1.1.3 allows remote attackers to inject arbitrary web script or HTML via the comment section.
CVE-2003-1547(PUBLISHED):Cross-site scripting (XSS) vulnerability in block-Forums.php in the Splatt Forum module for PHP-Nuke 6.x allows remote attackers to inject arbitrary web script or HTML via the subject parameter.
CVE-2003-1548(PUBLISHED):MyABraCaDaWeb 1.0.2 and earlier allows remote attackers to obtain sensitive information via an invalid IDAdmin or other parameter, which reveals the installation path in an error message.
CVE-2003-1549(PUBLISHED):Cross-site scripting (XSS) vulnerability in header.php in MyABraCaDaWeb 1.0.2 and earlier allows remote attackers to inject arbitrary web script or HTML via the ma_kw parameter.
CVE-2003-1550(PUBLISHED):XOOPS 2.0, and possibly earlier versions, allows remote attackers to obtain sensitive information via an invalid xoopsOption parameter, which reveals the installation path in an error message.
CVE-2003-1551(PUBLISHED):Unspecified vulnerability in Novell GroupWise 6 SP3 WebAccess before Revision F has unknown impact and attack vectors related to "malicious script."
CVE-2003-1552(PUBLISHED):Unrestricted file upload vulnerability in uploader.php in Uploader 1.1 allows remote attackers to execute arbitrary code by uploading a file with an executable extension, then accessing it via a direct request to the file in uploads/.
CVE-2003-1553(PUBLISHED):Haakon Nilsen Simple Internet Publishing System (SIPS) 0.2.2 stores sensitive information under the web root with insufficient access control, which allows remote attackers to obtain password and other user information via a direct request to a user-specific configuration directory.
CVE-2003-1554(PUBLISHED):Cross-site scripting (XSS) vulnerability in scozbook/add.php in ScozNet ScozBook 1.1 BETA allows remote attackers to inject arbitrary web script or HTML via the (1) username, (2) useremail, (3) aim, (4) msn, (5) sitename and (6) siteaddy variables.
CVE-2003-1555(PUBLISHED):ScozNet ScozBook 1.1 BETA allows remote attackers to obtain sensitive information via an invalid PG parameter in view.php, which reveals the installation path in an error message.
CVE-2003-1556(PUBLISHED):Cross-site scripting (XSS) vulnerability in cc_guestbook.pl in CGI City CC GuestBook allows remote attackers to inject arbitrary web script or HTML via the (1) name and (2) homepage_title (webpage title) parameters.
CVE-2003-1557(PUBLISHED):Off-by-one buffer overflow in spamc of SpamAssassin 2.40 through 2.43, when using BSMTP mode ("-B"), allows remote attackers to execute arbitrary code via email containing headers with leading "." characters.
CVE-2003-1558(PUBLISHED):Buffer overflow in httpd.c of fnord 1.6 allows remote attackers to create a denial of service (crash) and possibly execute arbitrary code via a long CGI request passed to the do_cgi function.
CVE-2003-1559(PUBLISHED):Microsoft Internet Explorer 5.22, and other 5 through 6 SP1 versions, sends Referer headers containing https:// URLs in requests for http:// URLs, which allows remote attackers to obtain potentially sensitive information by reading Referer log data.
CVE-2003-1560(PUBLISHED):Netscape 4 sends Referer headers containing https:// URLs in requests for http:// URLs, which allows remote attackers to obtain potentially sensitive information by reading Referer log data.
CVE-2003-1561(PUBLISHED):Opera, probably before 7.50, sends Referer headers containing https:// URLs in requests for http:// URLs, which allows remote attackers to obtain potentially sensitive information by reading Referer log data.
CVE-2003-1562(PUBLISHED):sshd in OpenSSH 3.6.1p2 and earlier, when PermitRootLogin is disabled and using PAM keyboard-interactive authentication, does not insert a delay after a root login attempt with the correct password, which makes it easier for remote attackers to use timing differences to determine if the password step of a multi-step authentication is successful, a different vulnerability than CVE-2003-0190.
CVE-2003-1563(PUBLISHED):Sun Cluster 2.2 through 3.2 for Oracle Parallel Server / Real Application Clusters (OPS/RAC) allows local users to cause a denial of service (cluster node panic or abort) by launching a daemon listening on a TCP port that would otherwise be used by the Distributed Lock Manager (DLM), possibly involving this daemon responding in a manner that spoofs a cluster reconfiguration.
CVE-2003-1564(PUBLISHED):libxml2, possibly before 2.5.0, does not properly detect recursion during entity expansion, which allows context-dependent attackers to cause a denial of service (memory and CPU consumption) via a crafted XML document containing a large number of nested entity references, aka the "billion laughs attack."
CVE-2003-1566(PUBLISHED):Microsoft Internet Information Services (IIS) 5.0 does not log requests that use the TRACK method, which allows remote attackers to obtain sensitive information without detection.
CVE-2003-1567(PUBLISHED):The undocumented TRACK method in Microsoft Internet Information Services (IIS) 5.0 returns the content of the original request in the body of the response, which makes it easier for remote attackers to steal cookies and authentication credentials, or bypass the HttpOnly protection mechanism, by using TRACK to read the contents of the HTTP headers that are returned in the response, a technique that is similar to cross-site tracing (XST) using HTTP TRACE.
CVE-2003-1568(PUBLISHED):GoAhead WebServer before 2.1.6 allows remote attackers to cause a denial of service (NULL pointer dereference and daemon crash) via an invalid URL, related to the websSafeUrl function.
CVE-2003-1569(PUBLISHED):GoAhead WebServer before 2.1.5 on Windows 95, 98, and ME allows remote attackers to cause a denial of service (daemon crash) via an HTTP request with a (1) con, (2) nul, (3) clock$, or (4) config$ device name in a path component, different vectors than CVE-2001-0385.
CVE-2003-1570(PUBLISHED):The server in IBM Tivoli Storage Manager (TSM) 5.1.x, 5.2.x before 5.2.1.2, and 6.x before 6.1 does not require credentials to observe the server console in some circumstances, which allows remote authenticated administrators to monitor server operations by establishing a console mode session, related to "session exposure."
CVE-2003-1571(PUBLISHED):Web Wiz Guestbook 6.0 stores sensitive information under the web root with insufficient access control, which allows remote attackers to download the database and obtain sensitive information via a direct request for database/WWGguestbook.mdb.  NOTE: it was later reported that 8.21 is also affected.
CVE-2003-1572(PUBLISHED):Sun Java Media Framework (JMF) 2.1.1 through 2.1.1c allows unsigned applets to cause a denial of service (JVM crash) and read or write unauthorized memory locations via the ReadEnv class, as demonstrated by reading environment variables using modified .data and .size fields.
CVE-2003-1573(PUBLISHED):The PointBase 4.6 database component in the J2EE 1.4 reference implementation (J2EE/RI) allows remote attackers to execute arbitrary programs, conduct a denial of service, and obtain sensitive information via a crafted SQL statement, related to "inadequate security settings and library bugs in sun.* and org.apache.* packages."
CVE-2003-1574(PUBLISHED):TikiWiki 1.6.1 allows remote attackers to bypass authentication by entering a valid username with an arbitrary password, possibly related to the Internet Explorer "Remember Me" feature.  NOTE: some of these details are obtained from third party information.
CVE-2003-1575(PUBLISHED):VERITAS File System (VxFS) 3.3.3, 3.4, and 3.5 before MP1 Rolling Patch 02 for Sun Solaris 2.5.1 through 9 does not properly implement inheritance of default ACLs in certain circumstances related to the characteristics of a directory inode, which allows local users to bypass intended file permissions by accessing a file on a VxFS filesystem.
CVE-2003-1576(PUBLISHED):Buffer overflow in pamverifier in Change Manager (CM) 1.0 for Sun Management Center (SunMC) 3.0 on Solaris 8 and 9 on the sparc platform allows remote attackers to execute arbitrary code via unspecified vectors.
CVE-2003-1577(PUBLISHED):Sun ONE (aka iPlanet) Web Server 4.1 through SP12 and 6.0 through SP5, when DNS resolution is enabled for client IP addresses, allows remote attackers to inject arbitrary text into log files, and conduct cross-site scripting (XSS) attacks involving the iPlanet Log Analyzer, via an HTTP request in conjunction with a crafted DNS response, related to an "Inverse Lookup Log Corruption (ILLC)" issue, a different vulnerability than CVE-2002-1315 and CVE-2002-1316.
CVE-2003-1578(PUBLISHED):Sun ONE (aka iPlanet) Web Server 4.1 through SP12 and 6.0 through SP5, when DNS resolution is enabled for client IP addresses, allows remote attackers to hide HTTP requests from the log-preview functionality by accompanying the requests with crafted DNS responses specifying a domain name beginning with a "format=" substring, related to an "Inverse Lookup Log Corruption (ILLC)" issue.
CVE-2003-1579(PUBLISHED):Sun ONE (aka iPlanet) Web Server 6 on Windows, when DNS resolution is enabled for client IP addresses, uses a logging format that does not identify whether a dotted quad represents an unresolved IP address, which allows remote attackers to spoof IP addresses via crafted DNS responses containing numerical top-level domains, as demonstrated by a forged *************** domain name, related to an "Inverse Lookup Log Corruption (ILLC)" issue.
CVE-2003-1580(PUBLISHED):The Apache HTTP Server 2.0.44, when DNS resolution is enabled for client IP addresses, uses a logging format that does not identify whether a dotted quad represents an unresolved IP address, which allows remote attackers to spoof IP addresses via crafted DNS responses containing numerical top-level domains, as demonstrated by a forged *************** domain name, related to an "Inverse Lookup Log Corruption (ILLC)" issue.
CVE-2003-1581(PUBLISHED):The Apache HTTP Server 2.0.44, when DNS resolution is enabled for client IP addresses, allows remote attackers to inject arbitrary text into log files via an HTTP request in conjunction with a crafted DNS response, as demonstrated by injecting XSS sequences, related to an "Inverse Lookup Log Corruption (ILLC)" issue.
CVE-2003-1582(PUBLISHED):Microsoft Internet Information Services (IIS) 6.0, when DNS resolution is enabled for client IP addresses, allows remote attackers to inject arbitrary text into log files via an HTTP request in conjunction with a crafted DNS response, as demonstrated by injecting XSS sequences, related to an "Inverse Lookup Log Corruption (ILLC)" issue.
CVE-2003-1583(PUBLISHED):Cross-site scripting (XSS) vulnerability in WebTrends allows remote attackers to inject arbitrary web script or HTML via a crafted client domain name, related to an "Inverse Lookup Log Corruption (ILLC)" issue.
CVE-2003-1584(PUBLISHED):Cross-site scripting (XSS) vulnerability in SurfStats allows remote attackers to inject arbitrary web script or HTML via a crafted client domain name, related to an "Inverse Lookup Log Corruption (ILLC)" issue.
CVE-2003-1585(PUBLISHED):Cross-site scripting (XSS) vulnerability in WebLogExpert allows remote attackers to inject arbitrary web script or HTML via a crafted client domain name, related to an "Inverse Lookup Log Corruption (ILLC)" issue.
CVE-2003-1586(PUBLISHED):Cross-site scripting (XSS) vulnerability in WebExpert allows remote attackers to inject arbitrary web script or HTML via a crafted User-Agent HTTP header.
CVE-2003-1587(PUBLISHED):Cross-site scripting (XSS) vulnerability in LoganPro allows remote attackers to inject arbitrary web script or HTML via a crafted User-Agent HTTP header.
CVE-2003-1588(PUBLISHED):Sun Cluster 2.2, when HA-Oracle or HA-Sybase DBMS services are used, stores database credentials in cleartext in a cluster configuration file, which allows local users to obtain sensitive information by reading this file.
CVE-2003-1589(PUBLISHED):Unspecified vulnerability in Sun ONE (aka iPlanet) Web Server 4.1 before SP13 and 6.0 before SP6 on Windows allows attackers to cause a denial of service (daemon crash) via unknown vectors.
CVE-2003-1590(PUBLISHED):Unspecified vulnerability in Sun ONE (aka iPlanet) Web Server 6.0 SP3 through SP5 on Windows allows remote attackers to cause a denial of service (daemon crash) via unknown vectors.
CVE-2003-1591(PUBLISHED):NWFTPD.nlm in the FTP server in Novell NetWare 6.0 before SP4 and 6.5 before SP1 allows user-assisted remote attackers to cause a denial of service (console hang) via a large number of FTP sessions, which are not properly handled during an NLM unload.
CVE-2003-1592(PUBLISHED):Multiple buffer overflows in NWFTPD.nlm in the FTP server in Novell NetWare 6.0 before SP4 and 6.5 before SP1 allow remote attackers to cause a denial of service (abend) via a long (1) username or (2) password.
CVE-2003-1593(PUBLISHED):NWFTPD.nlm in the FTP server in Novell NetWare 6.0 before SP4 and 6.5 before SP1 does not enforce domain-name login restrictions, which allows remote attackers to bypass intended access control via an FTP connection.
CVE-2003-1594(PUBLISHED):NWFTPD.nlm before 5.04.05 in the FTP server in Novell NetWare 6.5 does not properly enforce FTPREST.TXT settings, which allows remote attackers to bypass intended access restrictions via an FTP session.
CVE-2003-1595(PUBLISHED):NWFTPD.nlm before 5.04.05 in the FTP server in Novell NetWare 6.5 does not properly perform "intruder detection," which has unspecified impact and attack vectors.
CVE-2003-1596(PUBLISHED):NWFTPD.nlm before 5.03.12 in the FTP server in Novell NetWare does not properly restrict filesystem use by anonymous users with NFS Gateway home directories, which allows remote attackers to bypass intended access restrictions via an FTP session.
CVE-2003-1598(PUBLISHED):SQL injection vulnerability in log.header.php in WordPress 0.7 and earlier allows remote attackers to execute arbitrary SQL commands via the posts variable.
CVE-2003-1599(PUBLISHED):PHP remote file inclusion vulnerability in wp-links/links.all.php in WordPress 0.70 allows remote attackers to execute arbitrary PHP code via a URL in the $abspath variable.
CVE-2003-1603(PUBLISHED):GE Healthcare Discovery VH has a default password of (1) interfile for the ftpclient user of the Interfile server or (2) "2" for the LOCAL user of the FTP server for the Codonics printer, which has unspecified impact and attack vectors.
CVE-2003-1604(PUBLISHED):The redirect_target function in net/ipv4/netfilter/ipt_REDIRECT.c in the Linux kernel before 2.6.0 allows remote attackers to cause a denial of service (NULL pointer dereference and OOPS) by sending packets to an interface that has a 0.0.0.0 IP address, a related issue to CVE-2015-8787.
CVE-2003-1605(PUBLISHED):curl 7.x before 7.10.7 sends CONNECT proxy credentials to the remote server.
CVE-2003-20001(PUBLISHED):An issue was discovered on Mitel ICP VoIP 3100 devices. When a remote user attempts to log in via TELNET during the login wait time and an external call comes in, the system incorrectly divulges information about the call and any SMDR records generated by the system. The information provided includes the service type, extension number and other parameters, related to the call activity.
CVE-2003-5001(PUBLISHED):A vulnerability was found in ISS BlackICE PC Protection and classified as critical. Affected by this issue is the component Cross Site Scripting Detection. The manipulation as part of POST/PUT/DELETE/OPTIONS Request leads to privilege escalation. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. It is recommended to upgrade the affected component. NOTE: This vulnerability only affects products that are no longer supported by the maintainer
CVE-2003-5002(PUBLISHED):A vulnerability was found in ISS BlackICE PC Protection. It has been declared as problematic. Affected by this vulnerability is the component Update Handler which allows cleartext transmission of data. NOTE: This vulnerability only affects products that are no longer supported by the maintainer
CVE-2003-5003(PUBLISHED):A vulnerability was found in ISS BlackICE PC Protection. It has been rated as problematic. Affected by this issue is the Update Handler. The manipulation with an unknown input leads to cross site scripting. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. NOTE: This vulnerability only affects products that are no longer supported by the maintainer
