{"chunk_id": "line-1", "filename": "mitre_tactics_mobile_v17.txt", "content": "ID\tName\tDescription", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-2", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0027:Initial Access,The adversary is trying to get into your device.The initial access tactic represents the vectors adversaries use to gain an initial foothold onto a mobile device.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-3", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0041:Execution,The adversary is trying to run malicious code.Execution consists of techniques that result in adversary-controlled code running on a mobile device. Techniques that run malicious code are often paired with techniques from all other tactics to achieve broader goals, like exploring a network or stealing data.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-4", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0028:Persistence,The adversary is trying to maintain their foothold.Persistence is any access, action, or configuration change to a mobile device that gives an attacker a persistent presence on the device. Attackers often will need to maintain access to mobile devices through interruptions such as device reboots and potentially even factory data resets.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-5", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0029:Privilege Escalation,The adversary is trying to gain higher-level permissions.Privilege escalation includes techniques that allow an attacker to obtain a higher level of permissions on the mobile device. Attackers may enter the mobile device with very limited privileges and may be required to take advantage of a device weakness to obtain higher privileges necessary to successfully carry out their mission objectives.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-6", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0030:Defense Evasion,The adversary is trying to avoid being detected.Defense evasion consists of techniques an adversary may use to evade detection or avoid other defenses. Sometimes these actions are the same as or variations of techniques in other categories that have the added benefit of subverting a particular defense or mitigation. Defense evasion may be considered a set of attributes the adversary applies to all other phases of the operation.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-7", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0031:Credential Access,The adversary is trying to steal account names, passwords, or other secrets that enable access to resources.Credential access represents techniques that can be used by adversaries to obtain access to or control over passwords, tokens, cryptographic keys, or other values that could be used by an adversary to gain unauthorized access to resources. Credential access allows the adversary to assume the identity of an account, with all of that account's permissions on the system and network, and makes it harder for defenders to detect the adversary. With sufficient access within a network, an adversary can create accounts for later use within the environment.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-8", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0032:Discovery,The adversary is trying to figure out your environment.Discovery consists of techniques that allow the adversary to gain knowledge about the characteristics of the mobile device and potentially other networked systems. When adversaries gain access to a new system, they must orient themselves to what they now have control of and what benefits operating from that system give to their current objective or overall goals during the intrusion. The operating system may provide capabilities that aid in this post-compromise information-gathering phase.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-9", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0033:Lateral Movement,The adversary is trying to move through your environment.Lateral movement consists of techniques that enable an adversary to access and control remote systems on a network and could, but does not necessarily, include execution of tools on remote systems. The lateral movement techniques could allow an adversary to gather information from a system without needing additional tools, such as a remote access tool.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-10", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0035:Collection,The adversary is trying to gather data of interest to their goal.Collection consists of techniques used to identify and gather information, such as sensitive files, from a target network prior to exfiltration. This category also covers locations on a system or network where the adversary may look for information to exfiltrate.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-11", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0037:Command and Control,The adversary is trying to communicate with compromised devices to control them.The command and control tactic represents how adversaries communicate with systems under their control within a target network. There are many ways an adversary can establish command and control with various levels of covertness, depending on system configuration and network topology. Due to the wide degree of variation available to the adversary at the network level, only the most common factors were used to describe the differences in command and control. There are still a great many specific techniques within the documented methods, largely due to how easy it is to define new protocols and use existing, legitimate protocols and network services for communication. The resulting breakdown should help convey the concept that detecting intrusion through command and control protocols without prior knowledge is a difficult proposition over the long term. Adversaries' main constraints in network-level defense avoidance are testing and deployment of tools to rapidly change their protocols, awareness of existing defensive technologies, and access to legitimate Web services that, when used appropriately, make their tools difficult to distinguish from benign traffic.Additionally, in the mobile environment, mobile devices are frequently connected to networks outside enterprise control such as cellular networks or public Wi-Fi networks. Adversaries could attempt to evade detection by communicating on these networks, and potentially even by using non-Internet Protocol mechanisms such as Short Message Service (SMS). However, cellular networks often have data caps and/or extra data charges that could increase the potential for adversarial communication to be detected.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-12", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0036:Exfiltration,The adversary is trying to steal data.Exfiltration refers to techniques and attributes that result or aid in the adversary removing files and information from the targeted mobile device.In the mobile environment, mobile devices are frequently connected to networks outside enterprise control such as cellular networks or public Wi-Fi networks. Adversaries could attempt to evade detection by communicating on these networks, and potentially even by using non-Internet Protocol mechanisms such as Short Message Service (SMS). However, cellular networks often have data caps and/or extra data charges that could increase the potential for adversarial communication to be detected.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-13", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0034:Impact,The adversary is trying to manipulate, interrupt, or destroy your devices and data.The impact tactic consists of techniques used by the adversary to execute his or her mission objectives but that do not cleanly fit into another category such as Collection. Mission objectives vary based on each adversary's goals, but examples include toll fraud, destruction of device data, or locking the user out of his or her device until a ransom is paid.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-14", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0038:Network Effects,The adversary is trying to intercept or manipulate network traffic to or from a device.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
{"chunk_id": "line-15", "filename": "mitre_tactics_mobile_v17.txt", "content": "TA0039:Remote Service Effects,The adversary is trying to control or monitor the device using remote services.", "metadata": {"filename": "mitre_tactics_mobile_v17.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\ATTCK\\mitre_tactics_mobile_v17.txt"}}
