# 从文件中读取json数据
# 格式是K:\cvelistV5-main\cves\2007\0xxx\CVE-2007-0001.json
# 直接拿取文件夹下所有json文件
# K:\cvelistV5-main\cves\是通用的， 2007是年份， 0xxx是编号开头的文件夹

# 只拿取containers中的descriptions的value的值和cveMetadata中的cveId、state；
# 最拼接成 cveId(state):descriptions格式，保存到txt中，一个年份是一个txt, 一个json文件是一行

import os
import json
import re
import time
from tqdm import tqdm
import pandas as pd
from typing import List, Dict, Any

# 读取json文件
def get_json_files_in_directory(directory: str) -> List[str]:
    json_files = []
    for root, dirs, files in os.walk(directory):
        for filename in files:
            if filename.endswith(".json"):
                json_files.append(os.path.join(root, filename))
    return json_files

#进行提取
def extract_cve_data(json_file: str) -> List[str]:
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    cve_id = data.get('cveMetadata', {}).get('cveId', '')
    state = data.get('cveMetadata', {}).get('state', '')
    descriptions = data.get('containers', {}).get('cna', {}).get('descriptions', {})
    descriptions_value= [desc.get('value', '') for desc in descriptions if isinstance(desc, dict)]

    # 拼接成 cveId(state):descriptions格式
    result = []
    for desc in descriptions_value:
        result.append(f"{cve_id}({state}):{desc}")

    return result



def main():
    # 设置路径
    base_directory = r"K:\cvelistV5-main\cves"
    output_directory = r"K:\cvelistV5-main\cves_output"
    os.makedirs(output_directory, exist_ok=True)

    # 获取所有年份的文件夹
    year_folders = [d for d in os.listdir(base_directory) if os.path.isdir(os.path.join(base_directory, d))]

    print(f"Found {len(year_folders)} year folders.")

    # 遍历每个年份的文件夹，在每个年份的文件中获取到的还是文件夹，还要进行遍历拿取json文件
    for year in year_folders:
        year_directory = os.path.join(base_directory, year)
        output_file = os.path.join(output_directory, f"{year}.txt")

        # 获取所有json文件
        json_files = get_json_files_in_directory(year_directory)


        # 提取数据并写入文件
        all_cve_data = []
        for json_file in tqdm(json_files, desc=f"Processing {year}"):
            cve_data = extract_cve_data(json_file)

            all_cve_data.extend(cve_data)

        # 保存到txt文件
        with open(output_file, 'w', encoding='utf-8') as out_f:
            for line in all_cve_data:
                out_f.write(line + '\n')


if __name__ == "__main__":
    start_time = time.time()
    main()
    end_time = time.time()
    print(f"Execution time: {end_time - start_time:.2f} seconds")