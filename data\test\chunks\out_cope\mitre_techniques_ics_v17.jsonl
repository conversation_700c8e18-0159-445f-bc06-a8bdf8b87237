{"chunk_id": "line-1", "filename": "mitre_techniques_ics_v17.txt", "content": "T0800:Activate Firmware Update Mode,Adversaries may activate firmware update mode on devices to prevent expected response functions from engaging in reaction to an emergency or process malfunction. For example, devices such as protection relays may have an operation mode designed for firmware installation. This mode may halt process monitoring and related functions to allow new firmware to be loaded. A device left in update mode may be placed in an inactive holding state if no firmware is provided to it. By entering and leaving a device in this mode, the adversary may deny its usual functionalities.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-2", "filename": "mitre_techniques_ics_v17.txt", "content": "T0830:Adversary-in-the-Middle,Adversaries with privileged network access may seek to modify network traffic in real time using adversary-in-the-middle (AiTM) attacks.  This type of attack allows the adversary to intercept traffic to and/or from a particular device on the network. If a AiTM attack is established, then the adversary has the ability to block, log, modify, or inject traffic into the communication stream. There are several ways to accomplish this attack, but some of the most-common are Address Resolution Protocol (ARP) poisoning and the use of a proxy.  An AiTM attack may allow an adversary to perform the following attacks:Block Reporting Message, Spoof Reporting Message, Modify Parameter, Unauthorized Command Message", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-3", "filename": "mitre_techniques_ics_v17.txt", "content": "T0878:Alarm Suppression,Adversaries may target protection function alarms to prevent them from notifying operators of critical conditions. Alarm messages may be a part of an overall reporting system and of particular interest for adversaries. Disruption of the alarm system does not imply the disruption of the reporting system as a whole.A Secura presentation on targeting OT notes a dual fold goal for adversaries attempting alarm suppression: prevent outgoing alarms from being raised and prevent incoming alarms from being responded to.  The method of suppression may greatly depend on the type of alarm in question:  An alarm raised by a protocol message An alarm signaled with I/O An alarm bit set in a flag (and read) In ICS environments, the adversary may have to suppress or contend with multiple alarms and/or alarm propagation to achieve a specific goal to evade detection or prevent intended responses from occurring.   Methods of suppression may involve tampering or altering device displays and logs, modifying in memory code to fixed values, or even tampering with assembly level instruction code.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-4", "filename": "mitre_techniques_ics_v17.txt", "content": "T0802:Automated Collection,Adversaries may automate collection of industrial environment information using tools or scripts. This automated collection may leverage native control protocols and tools available in the control systems environment. For example, the OPC protocol may be used to enumerate and gather information. Access to a system or interface with these native protocols may allow collection and enumeration of other attached, communicating servers and devices.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-5", "filename": "mitre_techniques_ics_v17.txt", "content": "T0806:Brute Force I/O,Adversaries may repetitively or successively change I/O point values to perform an action. Brute Force I/O may be achieved by changing either a range of I/O point values or a single point value repeatedly to manipulate a process function. The adversary's goal and the information they have about the target environment will influence which of the options they choose. In the case of brute forcing a range of point values, the adversary may be able to achieve an impact without targeting a specific point. In the case where a single point is targeted, the adversary may be able to generate instability on the process function associated with that particular point. Adversaries may use Brute Force I/O to cause failures within various industrial processes. These failures could be the result of wear on equipment or damage to downstream equipment.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-6", "filename": "mitre_techniques_ics_v17.txt", "content": "T0892:Change Credential,Adversaries may modify software and device credentials to prevent operator and responder access. Depending on the device, the modification or addition of this password could prevent any device configuration actions from being accomplished and may require a factory reset or replacement of hardware. These credentials are often built-in features provided by the device vendors as a means to restrict access to management interfaces.An adversary with access to valid or hardcoded credentials could change the credential to prevent future authorized device access. Change Credential may be especially damaging when paired with other techniques such as Modify Program, Data Destruction, or Modify Controller Tasking. In these cases, a device’s configuration may be destroyed or include malicious actions for the process environment, which cannot not be removed through normal device configuration actions. Additionally, recovery of the device and original configuration may be difficult depending on the features provided by the device. In some cases, these passwords cannot be removed onsite and may require that the device be sent back to the vendor for additional recovery steps.A chain of incidents occurred in Germany, where adversaries locked operators out of their building automation system (BAS) controllers by enabling a previously unset BCU key.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-7", "filename": "mitre_techniques_ics_v17.txt", "content": "T0858:Change Operating Mode,Adversaries may change the operating mode of a controller to gain additional access to engineering functions such as Program Download.   Programmable controllers typically have several modes of operation that control the state of the user program and control access to the controllers API. Operating modes can be physically selected using a key switch on the face of the controller but may also be selected with calls to the controllers API. Operating modes and the mechanisms by which they are selected often vary by vendor and product line. Some commonly implemented operating modes are described below:  Program - This mode must be enabled before changes can be made to a devices program. This allows program uploads and downloads between the device and an engineering workstation. Often the PLCs logic Is halted, and all outputs may be forced off.  Run - Execution of the devices program occurs in this mode. Input and output (values, points, tags, elements, etc.) are monitored and used according to the programs logic. Program Upload and Program Download are disabled while in this mode.     Remote - Allows for remote changes to a PLCs operation mode.  Stop - The PLC and program is stopped, while in this mode, outputs are forced off.  Reset - Conditions on the PLC are reset to their original states. Warm resets may retain some memory while cold resets will reset all I/O and data registers.  Test / Monitor mode - Similar to run mode, I/O is processed, although this mode allows for monitoring, force set, resets, and more generally tuning or debugging of the system. Often monitor mode may be used as a trial for initialization.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-8", "filename": "mitre_techniques_ics_v17.txt", "content": "T0807:Command-Line Interface,Adversaries may utilize command-line interfaces (CLIs) to interact with systems and execute commands. CLIs provide a means of interacting with computer systems and are a common feature across many types of platforms and devices within control systems environments.  Adversaries may also use CLIs to install and run new software, including malicious tools that may be installed over the course of an operation.CLIs are typically accessed locally, but can also be exposed via services, such as SSH, Telnet, and RDP.  Commands that are executed in the CLI execute with the current permissions level of the process running the terminal emulator, unless the command specifies a change in permissions context. Many controllers have CLI interfaces for management purposes.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-9", "filename": "mitre_techniques_ics_v17.txt", "content": "T0885:Commonly Used Port,Adversaries may communicate over a commonly used port to bypass firewalls or network detection systems and to blend in with normal network activity, to avoid more detailed inspection. They may use the protocol associated with the port, or a completely different protocol. They may use commonly open ports, such as the examples provided below. TCP:80 (HTTP) TCP:443 (HTTPS) TCP/UDP:53 (DNS) TCP:1024-4999 (OPC on XP/Win2k3) TCP:49152-65535 (OPC on Vista and later) TCP:23 (TELNET) UDP:161 (SNMP) TCP:502 (MODBUS) TCP:102 (S7comm/ISO-TSAP) TCP:20000 (DNP3) TCP:44818 (Ethernet/IP)", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-10", "filename": "mitre_techniques_ics_v17.txt", "content": "T0884:Connection Proxy,Adversaries may use a connection proxy to direct network traffic between systems or act as an intermediary for network communications.The definition of a proxy can also be expanded to encompass trust relationships between networks in peer-to-peer, mesh, or trusted connections between networks consisting of hosts or systems that regularly communicate with each other.The network may be within a single organization or across multiple organizations with trust relationships. Adversaries could use these types of relationships to manage command and control communications, to reduce the number of simultaneous outbound network connections, to provide resiliency in the face of connection loss, or to ride over existing trusted communications paths between victims to avoid suspicion.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-11", "filename": "mitre_techniques_ics_v17.txt", "content": "T0817:Drive-by Compromise,Adversaries may gain access to a system during a drive-by compromise, when a user visits a website as part of a regular browsing session. With this technique, the user's web browser is targeted and exploited simply by visiting the compromised website. The adversary may target a specific community, such as trusted third party suppliers or other industry specific groups, which often visit the target website. This kind of targeted attack relies on a common interest, and is known as a strategic web compromise or watering hole attack. The National Cyber Awareness System (NCAS) has issued a Technical Alert (TA) regarding Russian government cyber activity targeting critical infrastructure sectors.  Analysis by DHS and FBI has noted two distinct categories of victims in the Dragonfly campaign on the Western energy sector: staging and intended targets. The adversary targeted the less secure networks of staging targets, including trusted third-party suppliers and related peripheral organizations. Initial access to the intended targets used watering hole attacks to target process control, ICS, and critical infrastructure related trade publications and informational websites.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-12", "filename": "mitre_techniques_ics_v17.txt", "content": "T0871:Execution through API,Adversaries may attempt to leverage Application Program Interfaces (APIs) used for communication between control software and the hardware. Specific functionality is often coded into APIs which can be called by software to engage specific functions on a device or other software.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-13", "filename": "mitre_techniques_ics_v17.txt", "content": "T0819:Exploit Public-Facing Application,Adversaries may leverage weaknesses to exploit internet-facing software for initial access into an industrial network. Internet-facing software may be user applications, underlying networking implementations, an assets operating system, weak defenses, etc. Targets of this technique may be intentionally exposed for the purpose of remote management and visibility.An adversary may seek to target public-facing applications as they may provide direct access into an ICS environment or the ability to move into the ICS network. Publicly exposed applications may be found through online tools that scan the internet for open ports and services. Version numbers for the exposed application may provide adversaries an ability to target specific known vulnerabilities. Exposed control protocol or remote access ports found in Commonly Used Port may be of interest by adversaries.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-14", "filename": "mitre_techniques_ics_v17.txt", "content": "T0820:Exploitation for Evasion,Adversaries may exploit a software vulnerability to take advantage of a programming error in a program, service, or within the operating system software or kernel itself to evade detection. Vulnerabilities may exist in software that can be used to disable or circumvent security features.  Adversaries may have prior knowledge through Remote System Information Discovery about security features implemented on control devices. These device security features will likely be targeted directly for exploitation. There are examples of firmware RAM/ROM consistency checks on control devices being targeted by adversaries to enable the installation of malicious System Firmware.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-15", "filename": "mitre_techniques_ics_v17.txt", "content": "T0890:Exploitation for Privilege Escalation,Adversaries may exploit software vulnerabilities in an attempt to elevate privileges. Exploitation of a software vulnerability occurs when an adversary takes advantage of a programming error in a program, service, or within the operating system software or kernel itself to execute adversary-controlled code. Security constructs such as permission levels will often hinder access to information and use of certain techniques, so adversaries will likely need to perform privilege escalation to include use of software exploitation to circumvent those restrictions.  When initially gaining access to a system, an adversary may be operating within a lower privileged process which will prevent them from accessing certain resources on the system. Vulnerabilities may exist, usually in operating system components and software commonly running at higher permissions, that can be exploited to gain higher levels of access on the system. This could enable someone to move from unprivileged or user level permissions to SYSTEM or root permissions depending on the component that is vulnerable. This may be a necessary step for an adversary compromising an endpoint system that has been properly configured and limits other privilege escalation methods.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-16", "filename": "mitre_techniques_ics_v17.txt", "content": "T0866:Exploitation of Remote Services,Adversaries may exploit a software vulnerability to take advantage of a programming error in a program, service, or within the operating system software or kernel itself to enable remote service abuse. A common goal for post-compromise exploitation of remote services is for initial access into and lateral movement throughout the ICS environment to enable access to targeted systems. ICS asset owners and operators have been affected by ransomware (or disruptive malware masquerading as ransomware) migrating from enterprise IT to ICS environments: WannaCry, NotPetya, and BadRabbit. In each of these cases, self-propagating (wormable) malware initially infected IT networks, but through exploit (particularly the SMBv1-targeting MS17-010 vulnerability) spread to industrial networks, producing significant impacts.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-17", "filename": "mitre_techniques_ics_v17.txt", "content": "T0822:External Remote Services,Adversaries may leverage external remote services as a point of initial access into your network. These services allow users to connect to internal network resources from external locations. Examples are VPNs, Citrix, and other access mechanisms. Remote service gateways often manage connections and credential authentication for these services. External remote services allow administration of a control system from outside the system. Often, vendors and internal engineering groups have access to external remote services to control system networks via the corporate network. In some cases, this access is enabled directly from the internet. While remote access enables ease of maintenance when a control system is in a remote area, compromise of remote access solutions is a liability. The adversary may use these services to gain access to and execute attacks against a control system network. Access to valid accounts is often a requirement. As they look for an entry point into the control system network, adversaries may begin searching for existing point-to-point VPN implementations at trusted third party networks or through remote support employee connections where split tunneling is enabled.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-18", "filename": "mitre_techniques_ics_v17.txt", "content": "T0823:Graphical User Interface,Adversaries may attempt to gain access to a machine via a Graphical User Interface (GUI) to enhance execution capabilities. Access to a GUI allows a user to interact with a computer in a more visual manner than a CLI. A GUI allows users to move a cursor and click on interface objects, with a mouse and keyboard as the main input devices, as opposed to just using the keyboard.If physical access is not an option, then access might be possible via protocols such as VNC on Linux-based and Unix-based operating systems, and RDP on Windows operating systems. An adversary can use this access to execute programs and applications on the target machine.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-19", "filename": "mitre_techniques_ics_v17.txt", "content": "T0891:Hardcoded Credentials,Adversaries may leverage credentials that are hardcoded in software or firmware to gain an unauthorized interactive user session to an asset. Examples credentials that may be hardcoded in an asset include:Username/PasswordsCryptographic keys/CertificatesAPI tokensUnlike Default Credentials, these credentials are built into the system in a way that they either cannot be changed by the asset owner, or may be infeasible to change because of the impact it would cause to the control system operation. These credentials may be reused across whole product lines or device models and are often not published or known to the owner and operators of the asset. Adversaries may utilize these hardcoded credentials to move throughout the control system environment or provide reliable access for their tools to interact with industrial assets.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-20", "filename": "mitre_techniques_ics_v17.txt", "content": "T0874:Hooking,Adversaries may hook into application programming interface (API) functions used by processes to redirect calls for execution and privilege escalation means. Windows processes often leverage these API functions to perform tasks that require reusable system resources. Windows API functions are typically stored in dynamic-link libraries (DLLs) as exported functions. One type of hooking seen in ICS involves redirecting calls to these functions via import address table (IAT) hooking. IAT hooking uses modifications to a process IAT, where pointers to imported API functions are stored.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-21", "filename": "mitre_techniques_ics_v17.txt", "content": "T0877:I/O Image,Adversaries may seek to capture process values related to the inputs and outputs of a PLC. During the scan cycle, a PLC reads the status of all inputs and stores them in an image table.  The image table is the PLCs internal storage location where values of inputs/outputs for one scan are stored while it executes the user program. After the PLC has solved the entire logic program, it updates the output image table. The contents of this output image table are written to the corresponding output points in I/O Modules.The Input and Output Image tables described above make up the I/O Image on a PLC. This image is used by the user program instead of directly interacting with physical I/O.  Adversaries may collect the I/O Image state of a PLC by utilizing a devices Native API to access the memory regions directly. The collection of the PLCs I/O state could be used to replace values or inform future stages of an attack.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-22", "filename": "mitre_techniques_ics_v17.txt", "content": "T0872:Indicator Removal on Host,Adversaries may attempt to remove indicators of their presence on a system in an effort to cover their tracks. In cases where an adversary may feel detection is imminent, they may try to overwrite, delete, or cover up changes they have made to the device.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-23", "filename": "mitre_techniques_ics_v17.txt", "content": "T0883:Internet Accessible Device,Adversaries may gain access into industrial environments through systems exposed directly to the internet for remote access rather than through External Remote Services. Internet Accessible Devices are exposed to the internet unintentionally or intentionally without adequate protections. This may allow for adversaries to move directly into the control system network. Access onto these devices is accomplished without the use of exploits, these would be represented within the Exploit Public-Facing Application technique.Adversaries may leverage built in functions for remote access which may not be protected or utilize minimal legacy protections that may be targeted.  These services may be discoverable through the use of online scanning tools. In the case of the Bowman dam incident, adversaries leveraged access to the dam control network through a cellular modem. Access to the device was protected by password authentication, although the application was vulnerable to brute forcing.   In Trend Micros manufacturing deception operations adversaries were detected leveraging direct internet access to an ICS environment through the exposure of operational protocols such as Siemens S7, Omron FINS, and EtherNet/IP, in addition to misconfigured VNC access.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-24", "filename": "mitre_techniques_ics_v17.txt", "content": "T0867:Lateral Tool Transfer,Adversaries may transfer tools or other files from one system to another to stage adversary tools or other files over the course of an operation.  Copying of files may also be performed laterally between internal victim systems to support Lateral Movement with remote Execution using inherent file sharing protocols such as file sharing over SMB to connected network shares. In control systems environments, malware may use SMB and other file sharing protocols to move laterally through industrial networks.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-25", "filename": "mitre_techniques_ics_v17.txt", "content": "T0826:Loss of Availability,Adversaries may attempt to disrupt essential components or systems to prevent owner and operator from delivering products or services.    Adversaries may leverage malware to delete or encrypt critical data on HMIs, workstations, or databases.In the 2021 Colonial Pipeline ransomware incident, pipeline operations were temporally halted on May 7th and were not fully restarted until May 12th.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-26", "filename": "mitre_techniques_ics_v17.txt", "content": "T0827:Loss of Control,Adversaries may seek to achieve a sustained loss of control or a runaway condition in which operators cannot issue any commands even if the malicious interference has subsided.   The German Federal Office for Information Security (BSI) reported a targeted attack on a steel mill in its 2014 IT Security Report. These targeted attacks affected industrial operations and resulted in breakdowns of control system components and even entire installations. As a result of these breakdowns, massive impact resulted in damage and unsafe conditions from the uncontrolled shutdown of a blast furnace.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-27", "filename": "mitre_techniques_ics_v17.txt", "content": "T0828:Loss of Productivity and Revenue,Adversaries may cause loss of productivity and revenue through disruption and even damage to the availability and integrity of control system operations, devices, and related processes. This technique may manifest as a direct effect of an ICS-targeting attack or tangentially, due to an IT-targeting attack against non-segregated environments. In cases where these operations or services are brought to a halt, the loss of productivity may eventually present an impact for the end-users or consumers of products and services. The disrupted supply-chain may result in supply shortages and increased prices, among other consequences. A ransomware attack on an Australian beverage company resulted in the shutdown of some manufacturing sites, including precautionary halts to protect key systems.  The company announced the potential for temporary shortages of their products following the attack.   In the 2021 Colonial Pipeline ransomware incident, the pipeline was unable to transport approximately 2.5 million barrels of fuel per day to the East Coast.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-28", "filename": "mitre_techniques_ics_v17.txt", "content": "T0837:Loss of Protection,Adversaries may compromise protective system functions designed to prevent the effects of faults and abnormal conditions. This can result in equipment damage, prolonged process disruptions and hazards to personnel. Many faults and abnormal conditions in process control happen too quickly for a human operator to react to. Speed is critical in correcting these conditions to limit serious impacts such as Loss of Control and Property Damage. Adversaries may target and disable protective system functions as a prerequisite to subsequent attack execution or to allow for future faults and abnormal conditions to go unchecked. Detection of a Loss of Protection by operators can result in the shutdown of a process due to strict policies regarding protection systems. This can cause a Loss of Productivity and Revenue and may meet the technical goals of adversaries seeking to cause process disruptions.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-29", "filename": "mitre_techniques_ics_v17.txt", "content": "T0880:Loss of Safety,Adversaries may compromise safety system functions designed to maintain safe operation of a process when unacceptable or dangerous conditions occur. Safety systems are often composed of the same elements as control systems but have the sole purpose of ensuring the process fails in a predetermined safe manner. Many unsafe conditions in process control happen too quickly for a human operator to react to. Speed is critical in correcting these conditions to limit serious impacts such as Loss of Control and Property Damage. Adversaries may target and disable safety system functions as a prerequisite to subsequent attack execution or to allow for future unsafe conditionals to go unchecked. Detection of a Loss of Safety by operators can result in the shutdown of a process due to strict policies regarding safety systems. This can cause a Loss of Productivity and Revenue and may meet the technical goals of adversaries seeking to cause process disruptions.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-30", "filename": "mitre_techniques_ics_v17.txt", "content": "T0829:Loss of View,Adversaries may cause a sustained or permanent loss of view where the ICS equipment will require local, hands-on operator intervention; for instance, a restart or manual operation. By causing a sustained reporting or visibility loss, the adversary can effectively hide the present state of operations. This loss of view can occur without affecting the physical processes themselves.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-31", "filename": "mitre_techniques_ics_v17.txt", "content": "T0835:Manipulate I/O Image,Adversaries may manipulate the I/O image of PLCs through various means to prevent them from functioning as expected. Methods of I/O image manipulation may include overriding the I/O table via direct memory manipulation or using the override function used for testing PLC programs.  During the scan cycle, a PLC reads the status of all inputs and stores them in an image table.  The image table is the PLCs internal storage location where values of inputs/outputs for one scan are stored while it executes the user program. After the PLC has solved the entire logic program, it updates the output image table. The contents of this output image table are written to the corresponding output points in I/O Modules. One of the unique characteristics of PLCs is their ability to override the status of a physical discrete input or to override the logic driving a physical output coil and force the output to a desired status.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-32", "filename": "mitre_techniques_ics_v17.txt", "content": "T0831:Manipulation of Control,Adversaries may manipulate physical process control within the industrial environment. Methods of manipulating control can include changes to set point values, tags, or other parameters. Adversaries may manipulate control systems devices or possibly leverage their own, to communicate with and command physical control processes. The duration of manipulation may be temporary or longer sustained, depending on operator detection.   Methods of Manipulation of Control include: Man-in-the-middle  Spoof command message Changing setpoints  A Polish student used a remote controller device to interface with the Lodz city tram system in Poland.    Using this remote, the student was able to capture and replay legitimate tram signals. As a consequence, four trams were derailed and twelve people injured due to resulting emergency stops.  The track controlling commands issued may have also resulted in tram collisions, a further risk to those on board and nearby the areas of impact.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-33", "filename": "mitre_techniques_ics_v17.txt", "content": "T0832:Manipulation of View,Adversaries may attempt to manipulate the information reported back to operators or controllers. This manipulation may be short term or sustained. During this time the process itself could be in a much different state than what is reported.    Operators may be fooled into doing something that is harmful to the system in a loss of view situation. With a manipulated view into the systems, operators may issue inappropriate control sequences that introduce faults or catastrophic failures into the system. Business analysis systems can also be provided with inaccurate data leading to bad management decisions.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-34", "filename": "mitre_techniques_ics_v17.txt", "content": "T0849:Masquerading,Adversaries may use masquerading to disguise a malicious application or executable as another file, to avoid operator and engineer suspicion. Possible disguises of these masquerading files can include commonly found programs, expected vendor executables and configuration files, and other commonplace application and naming conventions. By impersonating expected and vendor-relevant files and applications, operators and engineers may not notice the presence of the underlying malicious content and possibly end up running those masquerading as legitimate functions. Applications and other files commonly found on Windows systems or in engineering workstations have been impersonated before. This can be as simple as renaming a file to effectively disguise it in the ICS environment.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-35", "filename": "mitre_techniques_ics_v17.txt", "content": "T0838:Modify Alarm Settings,Adversaries may modify alarm settings to prevent alerts that may inform operators of their presence or to prevent responses to dangerous and unintended scenarios. Reporting messages are a standard part of data acquisition in control systems. Reporting messages are used as a way to transmit system state information and acknowledgements that specific actions have occurred. These messages provide vital information for the management of a physical process, and keep operators, engineers, and administrators aware of the state of system devices and physical processes. If an adversary is able to change the reporting settings, certain events could be prevented from being reported. This type of modification can also prevent operators or devices from performing actions to keep the system in a safe state. If critical reporting messages cannot trigger these actions then a Impact could occur. In ICS environments, the adversary may have to use Alarm Suppression or contend with multiple alarms and/or alarm propagation to achieve a specific goal to evade detection or prevent intended responses from occurring.   Methods of suppression often rely on modification of alarm settings, such as modifying in memory code to fixed values or tampering with assembly level instruction code.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-36", "filename": "mitre_techniques_ics_v17.txt", "content": "T0821:Modify Controller Tasking,Adversaries may modify the tasking of a controller to allow for the execution of their own programs. This can allow an adversary to manipulate the execution flow and behavior of a controller. According to 61131-3, the association of a Task with a Program Organization Unit (POU) defines a task association.  An adversary may modify these associations or create new ones to manipulate the execution flow of a controller. Modification of controller tasking can be accomplished using a Program Download in addition to other types of program modification such as online edit and program append.Tasks have properties, such as interval, frequency and priority to meet the requirements of program execution. Some controller vendors implement tasks with implicit, pre-defined properties whereas others allow for these properties to be formulated explicitly. An adversary may associate their program with tasks that have a higher priority or execute associated programs more frequently. For instance, to ensure cyclic execution of their program on a Siemens controller, an adversary may add their program to the task, Organization Block 1 (OB1).", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-37", "filename": "mitre_techniques_ics_v17.txt", "content": "T0836:Modify Parameter,Adversaries may modify parameters used to instruct industrial control system devices. These devices operate via programs that dictate how and when to perform actions based on such parameters. Such parameters can determine the extent to which an action is performed and may specify additional options. For example, a program on a control system device dictating motor processes may take a parameter defining the total number of seconds to run that motor.      An adversary can potentially modify these parameters to produce an outcome outside of what was intended by the operators. By modifying system and process critical parameters, the adversary may cause Impact to equipment and/or control processes. Modified parameters may be turned into dangerous, out-of-bounds, or unexpected values from typical operations. For example, specifying that a process run for more or less time than it should, or dictating an unusually high, low, or invalid value as a parameter.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-38", "filename": "mitre_techniques_ics_v17.txt", "content": "T0889:Modify Program,Adversaries may modify or add a program on a controller to affect how it interacts with the physical process, peripheral devices and other hosts on the network. Modification to controller programs can be accomplished using a Program Download in addition to other types of program modification such as online edit and program append. Program modification encompasses the addition and modification of instructions and logic contained in Program Organization Units (POU)   and similar programming elements found on controllers. This can include, for example, adding new functions to a controller, modifying the logic in existing functions and making new calls from one function to another. Some programs may allow an adversary to interact directly with the native API of the controller to take advantage of obscure features or vulnerabilities.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-39", "filename": "mitre_techniques_ics_v17.txt", "content": "T0839:Module Firmware,Adversaries may install malicious or vulnerable firmware onto modular hardware devices. Control system devices often contain modular hardware devices. These devices may have their own set of firmware that is separate from the firmware of the main control system equipment.   This technique is similar to System Firmware, but is conducted on other system components that may not have the same capabilities or level of integrity checking. Although it results in a device re-image, malicious device firmware may provide persistent access to remaining devices.  An easy point of access for an adversary is the Ethernet card, which may have its own CPU, RAM, and operating system. The adversary may attack and likely exploit the computer on an Ethernet card. Exploitation of the Ethernet card computer may enable the adversary to accomplish additional attacks, such as the following:  Delayed Attack - The adversary may stage an attack in advance and choose when to launch it, such as at a particularly damaging time.  Brick the Ethernet Card - Malicious firmware may be programmed to result in an Ethernet card failure, requiring a factory return.  Random Attack or Failure - The adversary may load malicious firmware onto multiple field devices. Execution of an attack and the time it occurs is generated by a pseudo-random number generator.   A Field Device Worm - The adversary may choose to identify all field devices of the same model, with the end goal of performing a device-wide compromise.  Attack Other Cards on the Field Device - Although it is not the most important module in a field device, the Ethernet card is most accessible to the adversary and malware. Compromise of the Ethernet card may provide a more direct route to compromising other modules, such as the CPU module.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-40", "filename": "mitre_techniques_ics_v17.txt", "content": "T0801:Monitor Process State,Adversaries may gather information about the physical process state. This information may be used to gain more information about the process itself or used as a trigger for malicious actions. The sources of process state information may vary such as, OPC tags, historian data, specific PLC block information, or network traffic.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-41", "filename": "mitre_techniques_ics_v17.txt", "content": "T0834:Native API,Adversaries may directly interact with the native OS application programming interface (API) to access system functions. Native APIs provide a controlled means of calling low-level OS services within the kernel, such as those involving hardware/devices, memory, and processes.  These native APIs are leveraged by the OS during system boot (when other system components are not yet initialized) as well as carrying out tasks and requests during routine operations. Functionality provided by native APIs are often also exposed to user-mode applications via interfaces and libraries. For example, functions such as memcpy and direct operations on memory registers can be used to modify user and system memory space.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-42", "filename": "mitre_techniques_ics_v17.txt", "content": "T0840:Network Connection Enumeration,Adversaries may perform network connection enumeration to discover information about device communication patterns. If an adversary can inspect the state of a network connection with tools, such as Netstat, in conjunction with System Firmware, then they can determine the role of certain devices on the network  . The adversary can also use Network Sniffing to watch network traffic for details about the source, destination, protocol, and content.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-43", "filename": "mitre_techniques_ics_v17.txt", "content": "T0842:Network Sniffing,Network sniffing is the practice of using a network interface on a computer system to monitor or capture information  regardless of whether it is the specified destination for the information. An adversary may attempt to sniff the traffic to gain information about the target. This information can vary in the level of importance. Relatively unimportant information is general communications to and from machines.  Relatively important information would be login information. User credentials may be sent over an unencrypted protocol, such as Telnet, that can be captured and obtained through network packet analysis. In addition, ARP and Domain Name Service (DNS) poisoning can be used to capture credentials to websites, proxies, and internal systems by redirecting traffic to an adversary.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-44", "filename": "mitre_techniques_ics_v17.txt", "content": "T0861:Point & Tag Identification,Adversaries may collect point and tag values to gain a more comprehensive understanding of the process environment. Points may be values such as inputs, memory locations, outputs or other process specific variables.  Tags are the identifiers given to points for operator convenience. Collecting such tags provides valuable context to environmental points and enables an adversary to map inputs, outputs, and other values to their control processes. Understanding the points being collected may inform an adversary on which processes and values to keep track of over the course of an operation.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-45", "filename": "mitre_techniques_ics_v17.txt", "content": "T0843:Program Download,Adversaries may perform a program download to transfer a user program to a controller. Variations of program download, such as online edit and program append, allow a controller to continue running during the transfer and reconfiguration process without interruption to process control. However, before starting a full program download (i.e., download all) a controller may need to go into a stop state. This can have negative consequences on the physical process, especially if the controller is not able to fulfill a time-sensitive action. Adversaries may choose to avoid a download all in favor of an online edit or program append to avoid disrupting the physical process. An adversary may need to use the technique Detect Operating Mode or Change Operating Mode to make sure the controller is in the proper mode to accept a program download.The granularity of control to transfer a user program in whole or parts is dictated by the management protocol (e.g., S7CommPlus, TriStation) and underlying controller API. Thus, program download is a high-level term for the suite of vendor-specific API calls used to configure a controllers user program memory space.  Modify Controller Tasking and Modify Program represent the configuration changes that are transferred to a controller via a program download.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-46", "filename": "mitre_techniques_ics_v17.txt", "content": "T0845:Program Upload,Adversaries may attempt to upload a program from a PLC to gather information about an industrial process. Uploading a program may allow them to acquire and study the underlying logic. Methods of program upload include vendor software, which enables the user to upload and read a program running on a PLC. This software can be used to upload the target program to a workstation, jump box, or an interfacing device.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-47", "filename": "mitre_techniques_ics_v17.txt", "content": "T0873:Project File Infection,Adversaries may attempt to infect project files with malicious code. These project files may consist of objects, program organization units, variables such as tags, documentation, and other configurations needed for PLC programs to function.  Using built in functions of the engineering software, adversaries may be able to download an infected program to a PLC in the operating environment enabling further Execution and Persistence techniques.  Adversaries may export their own code into project files with conditions to execute at specific intervals.  Malicious programs allow adversaries control of all aspects of the process enabled by the PLC. Once the project file is downloaded to a PLC the workstation device may be disconnected with the infected project file still executing.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-47", "line_number": 47, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-48", "filename": "mitre_techniques_ics_v17.txt", "content": "T0886:Remote Services,Adversaries may leverage remote services to move between assets and network segments. These services are often used to allow operators to interact with systems remotely within the network, some examples are RDP, SMB, SSH, and other similar mechanisms.    Remote services could be used to support remote access, data transmission, authentication, name resolution, and other remote functions. Further, remote services may be necessary to allow operators and administrators to configure systems within the network from their engineering or management workstations. An adversary may use this technique to access devices which may be dual-homed  to multiple network segments, and can be used for Program Download or to execute attacks on control devices directly through Valid Accounts.Specific remote services (RDP & VNC) may be a precursor to enable Graphical User Interface execution on devices such as HMIs or engineering workstation software.Based on incident data, CISA and FBI assessed that Chinese state-sponsored actors also compromised various authorized remote access channels, including systems designed to transfer data and/or allow access between corporate and ICS networks.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-49", "filename": "mitre_techniques_ics_v17.txt", "content": "T0846:Remote System Discovery,Adversaries may attempt to get a listing of other systems by IP address, hostname, or other logical identifier on a network that may be used for subsequent Lateral Movement or Discovery techniques. Functionality could exist within adversary tools to enable this, but utilities available on the operating system or vendor software could also be used.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-50", "filename": "mitre_techniques_ics_v17.txt", "content": "T0888:Remote System Information Discovery,An adversary may attempt to get detailed information about remote systems and their peripherals, such as make/model, role, and configuration. Adversaries may use information from Remote System Information Discovery to aid in targeting and shaping follow-on behaviors. For example, the system's operational role and model information can dictate whether it is a relevant target for the adversary's operational objectives. In addition, the system's configuration may be used to scope subsequent technique usage. Requests for system information are typically implemented using automation and management protocols and are often automatically requested by vendor software during normal operation. This information may be used to tailor management actions, such as program download and system or module firmware. An adversary may leverage this same information by issuing calls directly to the system's API.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-51", "filename": "mitre_techniques_ics_v17.txt", "content": "T0847:Replication Through Removable Media,Adversaries may move onto systems, such as those separated from the enterprise network, by copying malware to removable media which is inserted into the control systems environment. The adversary may rely on unknowing trusted third parties, such as suppliers or contractors with access privileges, to introduce the removable media. This technique enables initial access to target devices that never connect to untrusted networks, but are physically accessible.     Operators of the German nuclear power plant, Gundremmingen, discovered malware on a facility computer not connected to the internet.   The malware included Conficker and W32.Ramnit, which were also found on eighteen removable disk drives in the facility.       The plant has since checked for infection and cleaned up more than 1,000 computers.  An ESET researcher commented that internet disconnection does not guarantee system safety from infection or payload execution.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-52", "filename": "mitre_techniques_ics_v17.txt", "content": "T0848:Rogue Master,Adversaries may setup a rogue master to leverage control server functions to communicate with outstations. A rogue master can be used to send legitimate control messages to other control system devices, affecting processes in unintended ways. It may also be used to disrupt network communications by capturing and receiving the network traffic meant for the actual master. Impersonating a master may also allow an adversary to avoid detection. In the case of the 2017 Dallas Siren incident, adversaries used a rogue master to send command messages to the 156 distributed sirens across the city, either through a single rogue transmitter with a strong signal, or using many distributed repeaters.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-53", "filename": "mitre_techniques_ics_v17.txt", "content": "T0851:Rootkit,Adversaries may deploy rootkits to hide the presence of programs, files, network connections, services, drivers, and other system components. Rootkits are programs that hide the existence of malware by intercepting and modifying operating-system API calls that supply system information. Rootkits or rootkit-enabling functionality may reside at the user or kernel level in the operating system, or lower.  Firmware rootkits that affect the operating system yield nearly full control of the system. While firmware rootkits are normally developed for the main processing board, they can also be developed for the I/O that is attached to an asset. Compromise of this firmware allows the modification of all of the process variables and functions the module engages in. This may result in commands being disregarded and false information being fed to the main device. By tampering with device processes, an adversary may inhibit its expected response functions and possibly enable Impact.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-54", "filename": "mitre_techniques_ics_v17.txt", "content": "T0852:Screen Capture,Adversaries may attempt to perform screen capture of devices in the control system environment. Screenshots may be taken of workstations, HMIs, or other devices that display environment-relevant process, device, reporting, alarm, or related data. These device displays may reveal information regarding the ICS process, layout, control, and related schematics. In particular, an HMI can provide a lot of important industrial process information.  Analysis of screen captures may provide the adversary with an understanding of intended operations and interactions between critical devices.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-55", "filename": "mitre_techniques_ics_v17.txt", "content": "T0853:Scripting,Adversaries may use scripting languages to execute arbitrary code in the form of a pre-written script or in the form of user-supplied code to an interpreter. Scripting languages are programming languages that differ from compiled languages, in that scripting languages use an interpreter, instead of a compiler. These interpreters read and compile part of the source code just before it is executed, as opposed to compilers, which compile each and every line of code to an executable file. Scripting allows software developers to run their code on any system where the interpreter exists. This way, they can distribute one package, instead of precompiling executables for many different systems. Scripting languages, such as Python, have their interpreters shipped as a default with many Linux distributions. In addition to being a useful tool for developers and administrators, scripting language interpreters may be abused by the adversary to execute code in the target environment. Due to the nature of scripting languages, this allows for weaponized code to be deployed to a target easily, and leaves open the possibility of on-the-fly scripting to perform a task.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-56", "filename": "mitre_techniques_ics_v17.txt", "content": "T0881:Service Stop,Adversaries may stop or disable services on a system to render those services unavailable to legitimate users. Stopping critical services can inhibit or stop response to an incident or aid in the adversary's overall objectives to cause damage to the environment.   Services may not allow for modification of their data stores while running. Adversaries may stop services in order to conduct Data Destruction.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-57", "filename": "mitre_techniques_ics_v17.txt", "content": "T0865:Spearphishing Attachment,Adversaries may use a spearphishing attachment, a variant of spearphishing, as a form of a social engineering attack against specific targets. Spearphishing attachments are different from other forms of spearphishing in that they employ malware attached to an email. All forms of spearphishing are electronically delivered and target a specific individual, company, or industry. In this scenario, adversaries attach a file to the spearphishing email and usually rely upon User Execution to gain execution and access.  A Chinese spearphishing campaign running from December 9, 2011 through February 29, 2012, targeted ONG organizations and their employees. The emails were constructed with a high level of sophistication to convince employees to open the malicious file attachments.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-58", "filename": "mitre_techniques_ics_v17.txt", "content": "T0856:Spoof Reporting Message,Adversaries may spoof reporting messages in control system environments for evasion and to impair process control. In control systems, reporting messages contain telemetry data (e.g., I/O values) pertaining to the current state of equipment and the industrial process. Reporting messages are important for monitoring the normal operation of a system or identifying important events such as deviations from expected values. If an adversary has the ability to Spoof Reporting Messages, they can impact the control system in many ways. The adversary can Spoof Reporting Messages that state that the process is operating normally, as a form of evasion. The adversary could also Spoof Reporting Messages to make the defenders and operators think that other errors are occurring in order to distract them from the actual source of a problem.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-59", "filename": "mitre_techniques_ics_v17.txt", "content": "T0869:Standard Application Layer Protocol,Adversaries may establish command and control capabilities over commonly used application layer protocols such as HTTP(S), OPC, RDP, telnet, DNP3, and modbus. These protocols may be used to disguise adversary actions as benign network traffic. Standard protocols may be seen on their associated port or in some cases over a non-standard port.  Adversaries may use these protocols to reach out of the network for command and control, or in some cases to other infected devices within the network.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-60", "filename": "mitre_techniques_ics_v17.txt", "content": "T0862:Supply Chain Compromise,Adversaries may perform supply chain compromise to gain control systems environment access by means of infected products, software, and workflows. Supply chain compromise is the manipulation of products, such as devices or software, or their delivery mechanisms before receipt by the end consumer. Adversary compromise of these products and mechanisms is done for the goal of data or system compromise, once infected products are introduced to the target environment. Supply chain compromise can occur at all stages of the supply chain, from manipulation of development tools and environments to manipulation of developed products and tools distribution mechanisms. This may involve the compromise and replacement of legitimate software and patches, such as on third party or vendor websites. Targeting of supply chain compromise can be done in attempts to infiltrate the environments of a specific audience. In control systems environments with assets in both the IT and OT networks, it is possible a supply chain compromise affecting the IT environment could enable further access to the OT environment.   Counterfeit devices may be introduced to the global supply chain posing safety and cyber risks to asset owners and operators. These devices may not meet the safety, engineering and manufacturing requirements of regulatory bodies but may feature tagging indicating conformance with industry standards. Due to the lack of adherence to standards and overall lesser quality, the counterfeit products may pose a serious safety and operational risk.  Yokogawa identified instances in which their customers received counterfeit differential pressure transmitters using the Yokogawa logo. The counterfeit transmitters were nearly indistinguishable with a semblance of functionality and interface that mimics the genuine product.  F-Secure Labs analyzed the approach the adversary used to compromise victim systems with Havex.  The adversary planted trojanized software installers available on legitimate ICS/SCADA vendor websites. After being downloaded, this software infected the host computer with a Remote Access Trojan (RAT).", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-61", "filename": "mitre_techniques_ics_v17.txt", "content": "T0894:System Binary Proxy Execution,Adversaries may bypass process and/or signature-based defenses by proxying execution of malicious content with signed, or otherwise trusted, binaries. Binaries used in this technique are often Microsoft-signed files, indicating that they have been either downloaded from Microsoft or are already native in the operating system.  Binaries signed with trusted digital certificates can typically execute on Windows systems protected by digital signature validation. Several Microsoft signed binaries that are default on Windows installations can be used to proxy execution of other files or commands. Similarly, on Linux systems adversaries may abuse trusted binaries such as split to proxy execution of malicious commands. Adversaries may abuse application binaries installed on a system for proxy execution of malicious code or domain-specific commands. These commands could be used to target local resources on the device or networked devices within the environment through defined APIs (Execution through API) or application-specific programming languages (e.g., MicroSCADA SCIL). Application binaries may be signed by the developer or generally trusted by the operators, analysts, and monitoring tools accustomed to the environment. These applications may be developed and/or directly provided by the device vendor to enable configuration, management, and operation of their devices without many alternatives. Adversaries may seek to target these trusted application binaries to execute or send commands without the development of custom malware. For example, adversaries may target a SCADA server binary which has the existing ability to send commands to substation devices, such as through IEC 104 command messages. Proxy execution may still require the development of custom tools to hook into the application binary’s execution.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-62", "filename": "mitre_techniques_ics_v17.txt", "content": "T0857:System Firmware,System firmware on modern assets is often designed with an update feature. Older device firmware may be factory installed and require special reprograming equipment. When available, the firmware update feature enables vendors to remotely patch bugs and perform upgrades. Device firmware updates are often delegated to the user and may be done using a software update package. It may also be possible to perform this task over the network. An adversary may exploit the firmware update feature on accessible devices to upload malicious or out-of-date firmware. Malicious modification of device firmware may provide an adversary with root access to a device, given firmware is one of the lowest programming abstraction layers.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-63", "filename": "mitre_techniques_ics_v17.txt", "content": "T0882:Theft of Operational Information,Adversaries may steal operational information on a production environment as a direct mission outcome for personal gain or to inform future operations. This information may include design documents, schedules, rotational data, or similar artifacts that provide insight on operations.    In the Bowman Dam incident, adversaries probed systems for operational data.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-64", "filename": "mitre_techniques_ics_v17.txt", "content": "T0864:Transient Cyber Asset,Adversaries may target devices that are transient across ICS networks and external networks. Normally, transient assets are brought into an environment by authorized personnel and do not remain in that environment on a permanent basis.  Transient assets are commonly needed to support management functions and may be more common in systems where a remotely managed asset is not feasible, external connections for remote access do not exist, or 3rd party contractor/vendor access is required. Adversaries may take advantage of transient assets in different ways. For instance, adversaries may target a transient asset when it is connected to an external network and then leverage its trusted access in another environment to launch an attack. They may also take advantage of installed applications and libraries that are used by legitimate end-users to interact with control system devices. Transient assets, in some cases, may not be deployed with a secure configuration leading to weaknesses that could allow an adversary to propagate malicious executable code, e.g., the transient asset may be infected by malware and when connected to an ICS environment the malware propagates onto other systems.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-65", "filename": "mitre_techniques_ics_v17.txt", "content": "T0855:Unauthorized Command Message,Adversaries may send unauthorized command messages to instruct control system assets to perform actions outside of their intended functionality, or without the logical preconditions to trigger their expected function. Command messages are used in ICS networks to give direct instructions to control systems devices. If an adversary can send an unauthorized command message to a control system, then it can instruct the control systems device to perform an action outside the normal bounds of the device's actions. An adversary could potentially instruct a control systems device to perform an action that will cause an Impact. In the Dallas Siren incident, adversaries were able to send command messages to activate tornado alarm systems across the city without an impending tornado or other disaster.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-66", "filename": "mitre_techniques_ics_v17.txt", "content": "T0863:User Execution,Adversaries may rely on a targeted organizations user interaction for the execution of malicious code. User interaction may consist of installing applications, opening email attachments, or granting higher permissions to documents. Adversaries may embed malicious code or visual basic code into files such as Microsoft Word and Excel documents or software installers.  Execution of this code requires that the user enable scripting or write access within the document. Embedded code may not always be noticeable to the user especially in cases of trojanized software.  A Chinese spearphishing campaign running from December 9, 2011 through February 29, 2012 delivered malware through spearphishing attachments which required user action to achieve execution.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-67", "filename": "mitre_techniques_ics_v17.txt", "content": "T0859:Valid Accounts,Adversaries may steal the credentials of a specific user or service account using credential access techniques. In some cases, default credentials for control system devices may be publicly available. Compromised credentials may be used to bypass access controls placed on various resources on hosts and within the network, and may even be used for persistent access to remote systems. Compromised and default credentials may also grant an adversary increased privilege to specific systems and devices or access to restricted areas of the network. Adversaries may choose not to use malware or tools, in conjunction with the legitimate access those credentials provide, to make it harder to detect their presence or to control devices and send legitimate commands in an unintended way. Adversaries may also create accounts, sometimes using predefined account names and passwords, to provide a means of backup access for persistence.  The overlap of credentials and permissions across a network of systems is of concern because the adversary may be able to pivot across accounts and systems to reach a high level of access (i.e., domain or enterprise administrator)  and possibly between the enterprise and operational technology environments. Adversaries may be able to leverage valid credentials from one system to gain access to another system.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-68", "filename": "mitre_techniques_ics_v17.txt", "content": "T0860:Wireless Compromise,Adversaries may perform wireless compromise as a method of gaining communications and unauthorized access to a wireless network. Access to a wireless network may be gained through the compromise of a wireless device.   Adversaries may also utilize radios and other wireless communication devices on the same frequency as the wireless network. Wireless compromise can be done as an initial access vector from a remote distance. A Polish student used a modified TV remote controller to gain access to and control over the Lodz city tram system in Poland.   The remote controller device allowed the student to interface with the trams network to modify track settings and override operator control. The adversary may have accomplished this by aligning the controller to the frequency and amplitude of IR control protocol signals.  The controller then enabled initial access to the network, allowing the capture and replay of tram signals.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
{"chunk_id": "line-69", "filename": "mitre_techniques_ics_v17.txt", "content": "T0887:Wireless Sniffing,Adversaries may seek to capture radio frequency (RF) communication used for remote control and reporting in distributed environments. RF communication frequencies vary between 3 kHz to 300 GHz, although are commonly between 300 MHz to 6 GHz.   The wavelength and frequency of the signal affect how the signal propagates through open air, obstacles (e.g. walls and trees) and the type of radio required to capture them. These characteristics are often standardized in the protocol and hardware and may have an effect on how the signal is captured. Some examples of wireless protocols that may be found in cyber-physical environments are: WirelessHART, Zigbee, WIA-FA, and 700 MHz Public Safety Spectrum. Adversaries may capture RF communications by using specialized hardware, such as software defined radio (SDR), handheld radio, or a computer with radio demodulator tuned to the communication frequency.  Information transmitted over a wireless medium may be captured in-transit whether the sniffing device is the intended destination or not. This technique may be particularly useful to an adversary when the communications are not encrypted.  In the 2017 Dallas Siren incident, it is suspected that adversaries likely captured wireless command message broadcasts on a 700 MHz frequency during a regular test of the system. These messages were later replayed to trigger the alarm systems.", "metadata": {"filename": "mitre_techniques_ics_v17.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\ATTCK\\mitre_techniques_ics_v17.txt"}}
