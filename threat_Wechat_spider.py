#!/usr/bin/env python
# --*-- coding:UTF-8 --*--

import os
import re
import json
import time
import random
import shutil
import csv
import requests
import datetime
from threading import Thread
from bs4 import BeautifulSoup
from requests.adapters import HTTPAdapter

sess = requests.Session()
# max_retries=10 重试10次
sess.mount('http://', HTTPAdapter(max_retries=10))
sess.mount('https://', HTTPAdapter(max_retries=10))

file_path = os.path.dirname(__file__)
with open(os.path.join(file_path, 'config.json'), encoding='UTF-8') as fp:
    CONFIG = json.load(fp)

weixin_url_before_login = "https://mp.weixin.qq.com/"

weixin_url_after_login = 'https://mp.weixin.qq.com/cgi-bin/searchbiz'

weixin_search_url = 'https://mp.weixin.qq.com/cgi-bin/appmsg?'

headers = {
    'HOST': 'mp.weixin.qq.com',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_3) AppleWebKit/537.36 (KHTML, like Gecko) '
                  'Chrome/72.0.3626.109 Safari/537.36 '
}

REQUEST_PARAM_1 = {
    'action': 'search_biz',
    'token': '',
    'random': random.random(),
    'query': '',
    'lang': 'zh_CN',
    'f': 'json',
    'ajax': '1',
    'begin': '0',
    'count': '5'
}

REQUEST_PARAM_2 = {
    'action': 'list_ex',
    'token': None,
    'random': random.random(),
    'fakeid': None,
    'lang': 'zh_CN',
    'f': 'json',
    'ajax': '1',
    'begin': 0,
    'count': 5,
    'query': '',
    'type': '9'
}

file_save_path = file_path + r'/spider/'

# CSV文件路径
csv_file_path = os.path.join(file_save_path, 'articles_info.csv')

# 全局参数
cur_spider_pattern = 0
cur_cookie_number = 0
cur_official_account_name = None
cur_serial_number = 0
latest_url = None

# 时间过滤参数：可配置的天数（默认7天）
FILTER_DAYS = CONFIG.get('filter_days', 7)  # 从配置文件读取，默认7天
TIME_FILTER_SECONDS = FILTER_DAYS * 24 * 60 * 60


def start():
    # 初始化CSV文件
    init_csv_file()
    
    # 只执行一轮，不要无限循环
    for official_account_name in CONFIG.get('official_accounts_name'):
        global cur_spider_pattern, cur_cookie_number, cur_official_account_name, cur_serial_number, latest_url

        cur_spider_pattern = CONFIG['spider_pattern']
        cur_official_account_name = official_account_name
        cur_serial_number = CONFIG['cur_serial_number'][cur_official_account_name]

        print('----- 当前爬取模式：%d -----' % CONFIG['spider_pattern'])

        # 获取上次爬取最新的文章，如果配置为：仅抓取未抓取的文章，则会在爬取过程中使用该参数进行判断
        # 若采用模式1爬取，默认开始爬取序号都为0
        if cur_spider_pattern == 1:
            cur_serial_number = 0
            latest_url = get_latest_article_from_csv()

        while True:
            print('----- 当前cookie序号: %d -----' % cur_cookie_number)

            # 初始化请求参数
            init_param()

            print('----- 开始查询公众号[%s]相关文章 -----' % official_account_name)
            response = requests.get(weixin_search_url,
                                    cookies=CONFIG['cookies'][cur_cookie_number],
                                    headers=headers,
                                    params=REQUEST_PARAM_2, timeout=10)
            time.sleep(10)

            # 获取文章总数
            total_num = response.json().get('app_msg_cnt')

            if total_num is None:  # cookie失效或者被封
                print('----- 查询失败！请确认公众号名或该查询账号被禁止使用 -----')
                print('----- 开始切换公众号 -----')
                break
            elif total_num == 0:  # 查询公众号未发表文章
                print('----- 查询成功! 文章总数：%s' % str(total_num))
                print('----- 开始切换公众号 -----')
                break
            else:
                print('----- 查询成功! 文章总数：%s' % str(total_num))
                saved_count = get_article(total_num)
                
                # 显示汇总信息
                display_summary(official_account_name, total_num, saved_count)

                # 公众号处理完成，直接跳出内层循环
                print('----- 开始切换公众号 -----')
                break

            cur_cookie_number += 1
            cur_cookie_number %= len(CONFIG['cookies'])
        
        print('----- 公众号[%s]文章爬取结束！ -----' % official_account_name)
    
    print('----- 所有公众号处理完成，程序结束 -----')


def get_latest_article_from_csv():
    """从CSV文件获取指定公众号的最新文章链接"""
    try:
        if not os.path.exists(csv_file_path):
            return None
            
        with open(csv_file_path, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.DictReader(csvfile)
            latest_link = None
            for row in reader:
                if row['来源公众号名称'] == cur_official_account_name:
                    latest_link = row['文章链接']
            return latest_link
    except Exception as e:
        print(f'----- 从CSV获取最新文章失败: {str(e)} -----')
        return None


# 为防止程序意外中止导致爬取不全，将爬取文章的序号保存到config.json中，每次请求都从配置文件中初始化
def save_cur_serial_number_to_config():
    CONFIG['cur_serial_number'][cur_official_account_name] = cur_serial_number
    save_file = file_path + '/config.json'
    with open(save_file, 'w', encoding='UTF-8') as file:
        file.truncate()
        file.write(json.dumps(CONFIG, ensure_ascii=False))
        file.close()


def init_param():
    response = requests.get(url=weixin_url_before_login, cookies=CONFIG['cookies'][cur_cookie_number], timeout=10)
    token = re.findall(r'token=(\d+)', str(response.url))[0]
    time.sleep(2)
    REQUEST_PARAM_1['token'] = token
    REQUEST_PARAM_1['query'] = cur_official_account_name
    print('----- 正在查询[%s]相关公众号 -----' % cur_official_account_name)
    response = requests.get(weixin_url_after_login, cookies=CONFIG['cookies'][cur_cookie_number], headers=headers,
                            params=REQUEST_PARAM_1, timeout=10)
    # 解决: 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败
    if response is None:
        time.sleep(10 * 60)
        return
    time.sleep(2)
    print('----- 查询成功，默认选择返回结果的第一条数据 -----')
    lists = response.json().get('list')[0]
    REQUEST_PARAM_2['token'] = token
    REQUEST_PARAM_2['fakeid'] = lists.get('fakeid')
    REQUEST_PARAM_2['nickname'] = lists.get('nickname')


def get_article(total_num):
    current_num = cur_serial_number
    saved_count = 0  # 记录保存的文章数量
    skipped_old_count = 0  # 记录跳过的过期文章数量
    consecutive_old_count = 0  # 记录连续跳过的过期文章数量
    
    while current_num < total_num:
        REQUEST_PARAM_2['begin'] = current_num
        REQUEST_PARAM_2['random'] = random.random()
        response = requests.get(weixin_search_url, cookies=CONFIG['cookies'][cur_cookie_number], headers=headers,
                                params=REQUEST_PARAM_2, timeout=10)
        print('----- 开始爬取第%d条到%d条文章 -----' % (
            current_num + 1, current_num + len(response.json().get('app_msg_list', []))))
        time.sleep(5)
        count = 1
        
        for per in response.json().get('app_msg_list', []):
            create_time = per.get('create_time')
            
            # 检查文章是否在指定天数内
            if not is_within_one_week(create_time):
                print(f'----- 第{current_num + count}篇文章超过{FILTER_DAYS}天，跳过: {per.get("title", "未知标题")} -----')
                skipped_old_count += 1
                consecutive_old_count += 1
                count += 1
                
                # 如果连续跳过3篇文章，则结束当前公众号的爬取
                if consecutive_old_count >= 3:
                    print(f'----- 连续跳过{consecutive_old_count}篇过期文章，结束当前公众号爬取 -----')
                    print(f'----- 本轮爬取完成，共保存 {saved_count} 篇文章，跳过 {skipped_old_count} 篇过期文章 -----')
                    return saved_count
                continue
            else:
                # 重置连续跳过计数器，因为找到了新文章
                consecutive_old_count = 0
            
            # 已经获取上次爬取最新的文章，且配置为：仅抓取未抓取的文章，则直接退出
            if CONFIG['spider_pattern'] == 1 and per.get('link') == latest_url:
                print(f'----- 本轮爬取完成，共保存 {saved_count} 篇文章，跳过 {skipped_old_count} 篇过期文章 -----')
                return saved_count

            print('----- 开始处理第%d篇文章信息 -----' % (current_num + count))
            
            # 显示文章信息概览（包含时间信息）
            display_article_info_with_time(per, current_num, count)

            # 初始化要保存的参数
            aid = per.get('aid')
            title = re.sub(r'[\|\/\<\>\:\*\?\\\"]', "_", per.get('title'))
            digest = per.get('digest')
            link = per.get('link')
            update_time = per.get('update_time')

            # 保存文章信息到CSV文件
            if save_article_info_to_csv(cur_official_account_name, title, link, create_time):
                print('----- CSV: 第%d篇文章信息保存成功 -----' % (current_num + count))
                saved_count += 1

            count += 1
            time.sleep(random.randint(1, 2))  # 减少睡眠时间，因为只获取文章信息

        current_num += len(response.json().get('app_msg_list', []))

        # 开启一个新的线程，用来执行config.json文件修改保存的操作
        if cur_spider_pattern == 0:
            save_config_thread = Thread(target=save_cur_serial_number_to_config)
            save_config_thread.start()

        # 爬取文章数超过30篇就更换cookie
        if current_num - cur_serial_number >= 30:
            print(f'----- 本轮爬取完成，共保存 {saved_count} 篇文章，跳过 {skipped_old_count} 篇过期文章 -----')
            return saved_count

        # 睡眠10秒到30秒，防止被封（减少睡眠时间因为只获取文章信息）
        time.sleep(random.randint(10, 30))
    
    print(f'----- 所有文章爬取完成，共保存 {saved_count} 篇文章，跳过 {skipped_old_count} 篇过期文章 -----')
    return saved_count


def save_article_info_to_csv(account_name, title, link, create_time):
    """保存文章信息到CSV文件中"""
    try:
        # 检查文章是否已存在
        if is_article_exists(link):
            print(f'----- 文章已存在，跳过保存: {title} -----')
            return False  # 返回False表示没有新增保存
            
        # 格式化时间
        create_time_str = datetime.datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')
        
        # 保存文章信息到CSV
        with open(csv_file_path, 'a', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow([account_name, title, link, create_time_str])
        
        return True
    except Exception as e:
        print(f'----- 保存文章信息到CSV失败: {str(e)} -----')
        return False


def is_article_exists(link):
    """检查文章链接是否已存在于CSV文件中"""
    try:
        if not os.path.exists(csv_file_path):
            return False
            
        with open(csv_file_path, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if row['文章链接'] == link:
                    return True
        return False
    except Exception as e:
        print(f'----- 检查文章是否存在失败: {str(e)} -----')
        return False


def save_article_to_file(filepath, title, link):
    if os.path.exists(filepath):
        shutil.rmtree(filepath)
    os.makedirs(filepath)
    os.chdir(filepath)
    html = sess.get(link, headers=headers, timeout=10)
    soup_html = BeautifulSoup(html.text, 'lxml')
    article = soup_html.find('div', id='js_content')

    # Case 1: 文章内容涉嫌违反法律法规和政策导致发送失败
    if article is None:
        print('----- 保存失败：文章内容涉嫌违反法律法规和政策导致发送失败 -----')
        return False

    # 保存文章到本地
    file_name = title + '.txt'
    with open(file_name, 'a+', encoding='UTF-8') as file:
        file.truncate()
        file.write(article.text)
        file.close()

    # 保存图片到本地
    img_urls = soup_html.find('div', id='js_content').find_all("img")
    for i in range(len(img_urls)):
        try:
            if img_urls[i]['data-src']:
                img_url = img_urls[i]['data-src']
            else:
                img_url = img_urls[i]["src"]
            pic_down = requests.get(img_url)
            with open(str(i) + r'.jpeg', 'ab+') as file:
                file.write(pic_down.content)
                file.close()
        except KeyError:
            log_file_path = file_save_path + 'log/' + cur_official_account_name + '/'
            if not os.path.exists(log_file_path):
                os.makedirs(log_file_path)
            os.chdir(log_file_path)
            with open(file_name, 'a+', encoding='UTF-8') as file:
                file.write(link)
                file.write('\n')
                file.close()
    return True


def display_article_info(per, current_num, count):
    """显示文章信息概览"""
    title = per.get('title', '未知标题')
    link = per.get('link', '')
    create_time = per.get('create_time', 0)
    
    # 格式化时间
    create_time_str = datetime.datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')
    
    print(f'----- 第{current_num + count}篇文章信息 -----')
    print(f'标题: {title}')
    print(f'链接: {link}')
    print(f'发布时间: {create_time_str}')
    print('-' * 50)


def display_article_info_with_time(per, current_num, count):
    """显示带时间相对信息的文章概览"""
    title = per.get('title', '未知标题')
    link = per.get('link', '')
    create_time = per.get('create_time', 0)
    
    # 格式化时间，包含相对时间信息
    time_info = format_time_info(create_time)
    
    print(f'----- 第{current_num + count}篇文章信息 -----')
    print(f'标题: {title}')
    print(f'链接: {link}')
    print(f'发布时间: {time_info}')
    print('-' * 50)


def display_summary(account_name, total_articles, saved_articles):
    """显示爬取汇总信息"""
    print('=' * 60)
    print(f'公众号: {account_name}')
    print(f'文章总数: {total_articles}')
    print(f'已保存文章信息: {saved_articles} (仅保存{FILTER_DAYS}天内的文章)')
    print(f'CSV文件位置: {csv_file_path}')
    print(f'爬取时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print(f'时间过滤: 只保存过去{FILTER_DAYS}天内发布的文章')
    print('=' * 60)


def init_csv_file():
    """初始化CSV文件，如果不存在则创建并写入表头"""
    if not os.path.exists(file_save_path):
        os.makedirs(file_save_path)
    
    if not os.path.exists(csv_file_path):
        with open(csv_file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['来源公众号名称', '文章标题', '文章链接', '文章日期'])
        print(f'----- CSV文件初始化完成: {csv_file_path} -----')


def is_within_one_week(create_time):
    """检查文章是否在指定天数内发布"""
    try:
        current_time = time.time()
        article_time = create_time
        
        # 计算时间差
        time_diff = current_time - article_time
        
        # 如果时间差小于等于指定天数，返回True
        return time_diff <= TIME_FILTER_SECONDS
    except Exception as e:
        print(f'----- 检查文章时间失败: {str(e)} -----')
        return False


def format_time_info(create_time):
    """格式化时间信息用于显示"""
    try:
        current_time = time.time()
        time_diff = current_time - create_time
        days_ago = int(time_diff / (24 * 60 * 60))
        
        create_time_str = datetime.datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')
        
        if days_ago == 0:
            return f"{create_time_str} (今天)"
        elif days_ago == 1:
            return f"{create_time_str} (昨天)"
        else:
            return f"{create_time_str} ({days_ago}天前)"
    except Exception as e:
        return datetime.datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')


if __name__ == '__main__':
    print('----- Spider_GongZhongHao -----')
    print(f'----- 时间过滤: 只爬取过去{FILTER_DAYS}天内发布的文章 -----')
    print(f'----- 跳过条件: 连续跳过3篇过期文章则切换到下个公众号 -----')
    print(f'----- 当前时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")} -----')
    init_csv_file()
    start()
