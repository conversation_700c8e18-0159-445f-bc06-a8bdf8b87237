import json
import os
import time
import threading
import traceback
import mysql.connector
from mysql.connector import Error, pooling
from datetime import datetime, timedelta
from dotenv import load_dotenv


# 修正db.env文件路径 - 指向Data_Excation目录下的db.env
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'db.env')
load_dotenv(dotenv_path)

DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

# 添加任务状态管理类
class TaskManager:
    """分析任务状态管理器"""

    def __init__(self, db_config, connection_pool=None):
        self.db_config = db_config
        self.connection_pool = connection_pool
        self._ensure_task_table_exists()

    def get_connection(self):
        """获取数据库连接"""
        try:
            if self.connection_pool:
                return self.connection_pool.get_connection()
            else:
                return mysql.connector.connect(**self.db_config)
        except Error as e:
            print(f"获取数据库连接失败: {e}")
            return None
    
    def _ensure_task_table_exists(self):
        """确保任务状态表存在"""
        try:
            
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 首先检查表是否已存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = %s AND table_name = 'analysis_tasks'
            """, (self.db_config['database'],))
            
            table_exists = cursor.fetchone()[0] > 0
            
            if table_exists:
                print("任务表已存在，检查表结构...")
            else:
                print("任务表不存在，正在创建...")
            
            # 创建分析任务状态表（移除外键约束以避免循环引用问题）
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS analysis_tasks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                task_type ENUM('batch_analysis', 'single_analysis', 'archivebox_analysis', 'archivebox_archive_only', 'batch_unanalyzed', 'test_task', 'follow_up_analysis', 'weekly_crawl') NOT NULL,
                task_status ENUM('pending', 'running', 'completed', 'failed', 'cancelled', 'partially_completed') DEFAULT 'pending',
                task_params JSON,
                total_count INT DEFAULT 0,
                completed_count INT DEFAULT 0,
                success_count INT DEFAULT 0,
                error_count INT DEFAULT 0,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP NULL,
                created_by VARCHAR(100) DEFAULT 'system',
                results JSON,
                error_message TEXT,
                parent_task_id INT NULL,
                child_task_ids JSON NULL,
                is_follow_up_analysis BOOLEAN DEFAULT FALSE,
                related_snapshot_ids JSON NULL,
                INDEX idx_task_type (task_type),
                INDEX idx_task_status (task_status),
                INDEX idx_start_time (start_time),
                INDEX idx_parent_task_id (parent_task_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            
            conn.commit()
            print("任务状态表创建/检查完成")
            
            # 更新表结构（如果需要）
            self._update_table_structure(conn, cursor)
            
            # 清理服务重启前的运行中任务
            self._cleanup_running_tasks(conn, cursor)
            
        except Error as e:
            print(f"创建任务状态表失败: {e}")
            print(f"错误码: {e.errno}")
            print(f"SQL状态: {e.sqlstate}")
            import traceback
            traceback.print_exc()
            # 不要在这里抛出异常，而是让应用继续运行
        finally:
            if 'conn' in locals() and conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def create_task(self, task_type, task_params, total_count=0, created_by='system', parent_task_id=None, is_follow_up_analysis=False, related_snapshot_ids=None):
        """创建新的分析任务"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor()
            
            cursor.execute("""
            INSERT INTO analysis_tasks (task_type, task_params, total_count, created_by, parent_task_id, is_follow_up_analysis, related_snapshot_ids)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (task_type, json.dumps(task_params), total_count, created_by, parent_task_id, is_follow_up_analysis, json.dumps(related_snapshot_ids) if related_snapshot_ids else None))
            
            task_id = cursor.lastrowid
            
            # 如果有父任务，更新父任务的子任务列表
            if parent_task_id:
                self._add_child_task_to_parent(conn, cursor, parent_task_id, task_id)
            
            conn.commit()
            
            return task_id
            
        except Error as e:
            print(f"创建任务失败: {e}")
            return None
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def update_task_status(self, task_id, status, **kwargs):
        """更新任务状态"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 构建更新语句
            update_fields = ["task_status = %s"]
            update_values = [status]
            
            if 'completed_count' in kwargs:
                update_fields.append("completed_count = %s")
                update_values.append(kwargs['completed_count'])
            
            if 'success_count' in kwargs:
                update_fields.append("success_count = %s")
                update_values.append(kwargs['success_count'])
            
            if 'error_count' in kwargs:
                update_fields.append("error_count = %s")
                update_values.append(kwargs['error_count'])
                
            if 'results' in kwargs:
                update_fields.append("results = %s")
                update_values.append(json.dumps(kwargs['results']))
            
            if 'error_message' in kwargs:
                update_fields.append("error_message = %s")
                update_values.append(kwargs['error_message'])
            
            if status in ['completed', 'failed', 'cancelled']:
                update_fields.append("end_time = CURRENT_TIMESTAMP")
            
            update_values.append(task_id)
            
            sql = f"UPDATE analysis_tasks SET {', '.join(update_fields)} WHERE id = %s"
            cursor.execute(sql, update_values)
            conn.commit()
            
            # 如果任务完成或失败，检查是否需要更新父任务状态
            if status in ['completed', 'failed', 'cancelled']:
                # 在另一个事务中更新父任务，避免递归锁定
                try:
                    self.update_parent_task_on_child_completion(task_id)
                except Exception as e:
                    print(f"更新父任务状态时出错: {e}")
            
            return True
            
        except Error as e:
            print(f"更新任务状态失败: {e}")
            return False
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            cursor.execute("""
            SELECT * FROM analysis_tasks WHERE id = %s
            """, (task_id,))
            
            task = cursor.fetchone()
            
            if task:
                # 解析JSON字段
                if task['task_params']:
                    task['task_params'] = json.loads(task['task_params'])
                if task['results']:
                    task['results'] = json.loads(task['results'])
                if task['child_task_ids']:
                    task['child_task_ids'] = json.loads(task['child_task_ids'])
                if task['related_snapshot_ids']:
                    task['related_snapshot_ids'] = json.loads(task['related_snapshot_ids'])
                    
                # 计算进度百分比
                if task['total_count'] > 0:
                    task['progress'] = (task['completed_count'] / task['total_count']) * 100
                else:
                    task['progress'] = 0
            
            return task
            
        except Error as e:
            print(f"获取任务状态失败: {e}")
            return None
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def get_recent_tasks(self, limit=10, task_type=None):
        """获取最近的任务列表"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            where_clause = ""
            params = []
            
            if task_type:
                where_clause = "WHERE task_type = %s"
                params.append(task_type)
            
            cursor.execute(f"""
            SELECT id, task_type, task_status, total_count, completed_count,
                   success_count, error_count, start_time, end_time, created_by,
                   parent_task_id, is_follow_up_analysis
            FROM analysis_tasks
            {where_clause}
            ORDER BY id DESC
            LIMIT %s
            """, params + [limit])
            
            tasks = cursor.fetchall()
            
            # 计算进度
            for task in tasks:
                if task['total_count'] > 0:
                    task['progress'] = (task['completed_count'] / task['total_count']) * 100
                else:
                    task['progress'] = 0
            
            return tasks
            
        except Error as e:
            print(f"获取任务列表失败: {e}")
            return []
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()

    def _add_child_task_to_parent(self, conn, cursor, parent_task_id, child_task_id):
        """将子任务ID添加到父任务的子任务列表中"""
        try:
            # 获取父任务的当前子任务列表
            cursor.execute("SELECT child_task_ids FROM analysis_tasks WHERE id = %s", (parent_task_id,))
            result = cursor.fetchone()
            
            if result:
                current_child_ids = json.loads(result[0]) if result[0] else []
                current_child_ids.append(child_task_id)
                
                # 更新父任务的子任务列表
                cursor.execute(
                    "UPDATE analysis_tasks SET child_task_ids = %s WHERE id = %s",
                    (json.dumps(current_child_ids), parent_task_id)
                )
        except Exception as e:
            print(f"更新父任务子任务列表失败: {e}")
    
    def update_parent_task_on_child_completion(self, child_task_id):
        """当子任务完成时，更新父任务状态"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            # 获取子任务信息
            cursor.execute("SELECT parent_task_id, task_status FROM analysis_tasks WHERE id = %s", (child_task_id,))
            child_task = cursor.fetchone()
            
            if not child_task or not child_task['parent_task_id']:
                return False
            
            parent_task_id = child_task['parent_task_id']
            
            # 获取父任务的所有子任务
            cursor.execute("SELECT child_task_ids FROM analysis_tasks WHERE id = %s", (parent_task_id,))
            parent_result = cursor.fetchone()
            
            if not parent_result or not parent_result['child_task_ids']:
                return False
            
            child_task_ids = json.loads(parent_result['child_task_ids'])
            
            # 检查所有子任务的状态
            placeholders = ','.join(['%s'] * len(child_task_ids))
            cursor.execute(f"SELECT task_status FROM analysis_tasks WHERE id IN ({placeholders})", child_task_ids)
            child_statuses = [row['task_status'] for row in cursor.fetchall()]
            
            # 计算父任务的新状态
            new_parent_status = self._calculate_parent_status(child_statuses)
            
            # 更新父任务状态
            if new_parent_status:
                cursor.execute(
                    "UPDATE analysis_tasks SET task_status = %s WHERE id = %s",
                    (new_parent_status, parent_task_id)
                )
                conn.commit()
                
                print(f"已更新父任务 {parent_task_id} 状态为: {new_parent_status}")
                return True
            
            return False
            
        except Error as e:
            print(f"更新父任务状态失败: {e}")
            return False
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def _calculate_parent_status(self, child_statuses):
        """根据子任务状态计算父任务状态"""
        if not child_statuses:
            return None
        
        # 如果所有子任务都完成了，父任务也完成
        if all(status == 'completed' for status in child_statuses):
            return 'completed'
        
        # 如果有子任务失败，但有子任务成功，标记为部分完成
        if any(status == 'completed' for status in child_statuses) and any(status == 'failed' for status in child_statuses):
            return 'partially_completed'
        
        # 如果所有子任务都失败了，父任务也失败
        if all(status == 'failed' for status in child_statuses):
            return 'failed'
        
        # 如果还有运行中的任务，保持运行状态
        if any(status == 'running' for status in child_statuses):
            return 'running'
        
        return None
    
    def get_child_tasks(self, parent_task_id):
        """获取指定父任务的所有子任务"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            # 查询所有子任务
            cursor.execute("""
                SELECT id, task_type, task_status, task_params, created_time, 
                       completed_time, total_count, success_count, error_count, 
                       results, error_message
                FROM analysis_tasks 
                WHERE parent_task_id = %s 
                ORDER BY created_time ASC
            """, (parent_task_id,))
            
            child_tasks = cursor.fetchall()
            
            # 处理结果
            for task in child_tasks:
                # 解析JSON字段
                if task['task_params']:
                    try:
                        task['task_params'] = json.loads(task['task_params'])
                    except:
                        task['task_params'] = {}
                
                if task['results']:
                    try:
                        task['results'] = json.loads(task['results'])
                    except:
                        task['results'] = []
                
                # 格式化时间
                if task['created_time']:
                    task['created_time'] = task['created_time'].isoformat()
                if task['completed_time']:
                    task['completed_time'] = task['completed_time'].isoformat()
            
            return child_tasks
            
        except Error as e:
            print(f"获取子任务失败: {e}")
            return []
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def link_analysis_to_archive_task(self, snapshot_ids, analysis_task_id):
        """将分析任务关联到对应的存档任务"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            if not isinstance(snapshot_ids, list):
                snapshot_ids = [snapshot_ids]
            
            # 查找包含这些snapshot_ids的存档任务
            for snapshot_id in snapshot_ids:
                cursor.execute("""
                SELECT id FROM analysis_tasks 
                WHERE task_type = 'archivebox_analysis' 
                AND JSON_CONTAINS(results, JSON_OBJECT('snapshot_id', %s))
                ORDER BY start_time DESC 
                LIMIT 1
                """, (snapshot_id,))
                
                archive_task = cursor.fetchone()
                if archive_task:
                    archive_task_id = archive_task['id']
                    
                    # 更新分析任务，设置父任务
                    cursor.execute(
                        "UPDATE analysis_tasks SET parent_task_id = %s, is_follow_up_analysis = TRUE WHERE id = %s",
                        (archive_task_id, analysis_task_id)
                    )
                    
                    # 更新存档任务，添加子任务
                    self._add_child_task_to_parent(conn, cursor, archive_task_id, analysis_task_id)
                    
                    print(f"已将分析任务 {analysis_task_id} 关联到存档任务 {archive_task_id}")
            
            conn.commit()
            return True
            
        except Error as e:
            print(f"关联分析任务到存档任务失败: {e}")
            return False
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()

    # 修正TaskManager.find_related_archive_task方法，使用JSON_SEARCH查找snapshot_id，提升存档分析任务的父子任务自动关联准确性。
    def find_related_archive_task(self, snapshot_ids):
        """查找与给定快照ID相关的存档任务（修正版，支持MySQL JSON_SEARCH）"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor(dictionary=True)
            
            if not snapshot_ids:
                return None
            
            # 查找包含这些snapshot_ids的最近的存档任务
            for snapshot_id in snapshot_ids:
                cursor.execute("""
                SELECT id, start_time FROM analysis_tasks 
                WHERE task_type = 'archivebox_analysis' 
                AND task_status != 'failed'
                AND results IS NOT NULL
                AND JSON_SEARCH(results, 'one', %s, NULL, '$[*].snapshot_id') IS NOT NULL
                ORDER BY start_time DESC 
                LIMIT 1
                """, (snapshot_id,))
                result = cursor.fetchone()
                if result:
                    print(f"找到相关存档任务: {result['id']} for snapshot {snapshot_id}")
                    return result['id']
            # 如果没找到，尝试最近的存档任务
            cursor.execute("""
            SELECT id FROM analysis_tasks 
            WHERE task_type = 'archivebox_analysis' 
            AND task_status != 'failed'
            AND start_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY start_time DESC 
            LIMIT 1
            """)
            result = cursor.fetchone()
            if result:
                print(f"找到最近的存档任务: {result['id']}")
                return result['id']
            return None
        except Error as e:
            print(f"查找相关存档任务失败: {e}")
            return None
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def _cleanup_running_tasks(self, conn, cursor):
        """清理服务重启前的运行中任务"""
        try:
            # 获取所有运行中的任务
            cursor.execute("""
                SELECT id, task_type, start_time
                FROM analysis_tasks
                WHERE task_status IN ('running', 'pending')
            """)

            running_tasks = cursor.fetchall()
            cleanup_count = 0

            for task in running_tasks:
                task_id, task_type, start_time = task
                should_cleanup = False

                # 检查任务是否应该被清理
                if task_type == 'weekly_crawl':
                    # 对于每周爬取任务，检查进程是否还在运行
                    should_cleanup = not self._is_weekly_crawl_running()
                else:
                    # 对于其他任务，如果超过5分钟就清理
                    from datetime import datetime, timedelta
                    if start_time < datetime.now() - timedelta(minutes=5):
                        should_cleanup = True

                if should_cleanup:
                    cursor.execute("""
                        UPDATE analysis_tasks
                        SET task_status = 'failed',
                            error_message = '服务重启，任务中断',
                            end_time = CURRENT_TIMESTAMP
                        WHERE id = %s
                    """, (task_id,))
                    cleanup_count += 1

            if cleanup_count > 0:
                print(f"清理了 {cleanup_count} 个因服务重启而中断的任务")
                conn.commit()

        except Error as e:
            print(f"清理运行中任务失败: {e}")

    def _is_weekly_crawl_running(self):
        """检查每周爬取进程是否在运行"""
        try:
            import subprocess
            import os

            if os.name == 'nt':  # Windows
                result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'],
                                      capture_output=True, text=True)
                return 'pa_week_ar_test.py' in result.stdout
            else:  # Linux/Unix
                result = subprocess.run(['pgrep', '-f', 'pa_week_ar_test.py'],
                                      capture_output=True, text=True)
                return result.returncode == 0
        except Exception as e:
            print(f"检查进程状态失败: {e}")
            return True  # 如果检查失败，假设进程还在运行，避免误杀
    
    def cleanup_stale_tasks(self, hours=24):
        """清理超时的任务"""
        try:
            conn = mysql.connector.connect(**self.db_config)
            cursor = conn.cursor()
            
            # 清理超过指定时间的运行中任务
            cursor.execute("""
                UPDATE analysis_tasks 
                SET task_status = 'failed', 
                    error_message = '任务超时',
                    end_time = CURRENT_TIMESTAMP
                WHERE task_status IN ('running', 'pending')
                AND start_time < DATE_SUB(NOW(), INTERVAL %s HOUR)
            """, (hours,))
            
            affected_rows = cursor.rowcount
            conn.commit()
            
            print(f"清理了 {affected_rows} 个超时任务")
            return affected_rows
            
        except Error as e:
            print(f"清理超时任务失败: {e}")
            return 0
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def _update_table_structure(self, conn, cursor):
        """更新表结构以支持新的枚举值"""
        try:
            # 检查当前的task_type枚举值
            cursor.execute("""
                SELECT COLUMN_TYPE 
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'analysis_tasks' 
                AND COLUMN_NAME = 'task_type'
            """, (self.db_config['database'],))
            
            result = cursor.fetchone()
            if result:
                column_type = result[0]
                
                # 检查是否需要添加新的枚举值
                missing_types = []
                required_types = ['test_task', 'archivebox_archive_only', 'follow_up_analysis', 'weekly_crawl']
                
                for task_type in required_types:
                    if task_type not in column_type:
                        missing_types.append(task_type)
                
                if missing_types:
                    print(f"需要添加的任务类型: {missing_types}")
                    cursor.execute("""
                        ALTER TABLE analysis_tasks
                        MODIFY COLUMN task_type ENUM('batch_analysis', 'single_analysis', 'archivebox_analysis', 'archivebox_archive_only', 'batch_unanalyzed', 'test_task', 'follow_up_analysis', 'weekly_crawl') NOT NULL
                    """)
                    conn.commit()
                    print("task_type 枚举更新完成")
            
        except Error as e:
            print(f"更新表结构失败: {e}")
            # 不抛出异常，让应用继续运行
    
    @staticmethod
    def get_task_type_display_name(task_type):
        """获取任务类型的友好显示名称"""
        task_type_names = {
            'batch_analysis': '批量RAG分析',
            'single_analysis': '单个RAG分析',
            'archivebox_analysis': 'ArchiveBox存档+分析',
            'archivebox_archive_only': 'ArchiveBox仅存档',
            'batch_unanalyzed': '批量分析未分析快照',
            'test_task': '测试任务',
            'follow_up_analysis': '后续分析任务',
            'weekly_crawl': '每周爬取任务'
        }
        return task_type_names.get(task_type, task_type)
    
    @staticmethod
    def get_task_status_display_name(task_status):
        """获取任务状态的友好显示名称"""
        status_names = {
            'pending': '等待中',
            'running': '运行中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消',
            'partially_completed': '部分完成'
        }
        return status_names.get(task_status, task_status)





# 定时任务管理器
class WeeklyTaskScheduler:
    """每周威胁情报爬取任务调度器 - 与TaskManager集成"""

    def __init__(self, task_manager_instance):
        print(f"正在初始化WeeklyTaskScheduler，task_manager类型: {type(task_manager_instance)}")

        if task_manager_instance is None:
            raise ValueError("task_manager_instance不能为None")

        self.timer = None
        self.is_active = False
        self.next_run_time = None
        self.last_run_time = None
        self.lock = threading.Lock()
        self.task_manager = task_manager_instance
        self.current_task_id = None

        print("WeeklyTaskScheduler初始化完成")

    def start_weekly_schedule(self, create_initial_task=True):
        """启动每周定时任务"""
        with self.lock:
            if self.is_active:
                print("定时任务已经在运行中")
                return True, "定时任务已经在运行中"

            self.is_active = True

            # 只有在明确要求时才创建初始任务
            if create_initial_task:
                try:
                    self.current_task_id = self.task_manager.create_task(
                        task_type='weekly_crawl',
                        task_params={
                            'script_path': 'pa_week_ar_test.py',
                            'log_file': 'crawl_week.log',
                            'description': '每周威胁情报网站爬取任务（定时调度）',
                            'scheduled': True,
                            'scheduler_started': True
                        },
                        total_count=1,
                        created_by='scheduler'
                    )
                    print(f"创建定时任务记录，任务ID: {self.current_task_id}")
                except Exception as e:
                    print(f"创建定时任务记录失败: {e}")
                    self.current_task_id = None

            self._schedule_next_run()
            print("每周威胁情报爬取定时任务已启动")
            return True, "每周威胁情报爬取定时任务已启动"

    def stop_weekly_schedule(self):
        """停止每周定时任务"""
        with self.lock:
            if self.timer:
                self.timer.cancel()
                self.timer = None
            self.is_active = False
            self.next_run_time = None

            # 停止正在运行的爬取脚本
            self._stop_crawl_script()

            # 如果有正在运行的任务，标记为取消
            if self.current_task_id:
                try:
                    self.task_manager.update_task_status(
                        self.current_task_id,
                        'cancelled',
                        error_message='用户停止了定时任务'
                    )
                    self.current_task_id = None
                except Exception as e:
                    print(f"取消当前任务时出错: {e}")

            print("每周威胁情报爬取定时任务已停止")
            return True, "每周威胁情报爬取定时任务已停止"

    def _schedule_next_run(self):
        """安排下次执行"""
        if not self.is_active:
            return

        # 计算下次执行时间（一周后）
        # 测试1小时后
        next_time = datetime.now() + timedelta(hours=1)
        # next_time = datetime.now() + timedelta(weeks=1)
        self.next_run_time = next_time

        # 计算延迟秒数
        delay_seconds = (next_time - datetime.now()).total_seconds()

        # 创建定时器
        self.timer = threading.Timer(delay_seconds, self._execute_weekly_task)
        self.timer.start()

        print(f"下次威胁情报爬取任务计划于: {next_time.strftime('%Y-%m-%d %H:%M:%S')}")

    def _execute_weekly_task(self):
        """执行每周任务"""
        print(f"开始执行每周威胁情报爬取任务: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.last_run_time = datetime.now()

        try:
            # 如果有pending状态的任务，更新为运行中；否则创建新任务
            if self.current_task_id:
                # 更新现有任务状态为运行中
                self.task_manager.update_task_status(self.current_task_id, 'running')
                print(f"更新任务 {self.current_task_id} 状态为运行中")
            else:
                # 创建新任务记录
                self.current_task_id = self.task_manager.create_task(
                    task_type='weekly_crawl',
                    task_params={
                        'script_path': 'pa_week_ar_test.py',
                        'log_file': 'crawl_week.log',
                        'description': '定时每周威胁情报网站爬取任务',
                        'scheduled': True
                    },
                    total_count=1,
                    created_by='scheduler'
                )
                # 更新任务状态为运行中
                self.task_manager.update_task_status(self.current_task_id, 'running')
                print(f"创建新任务 {self.current_task_id} 并设置为运行中")

            # 执行爬取脚本（后台运行）
            success = self._run_crawl_script()

            if success:
                print("每周威胁情报爬取任务已启动（后台运行）")
                # 启动监控线程来检查脚本执行状态
                self._start_scheduled_task_monitor(self.current_task_id)
            else:
                print("每周威胁情报爬取任务启动失败")
                self.task_manager.update_task_status(
                    self.current_task_id,
                    'failed',
                    error_message='脚本启动失败'
                )

        except Exception as e:
            print(f"执行每周任务时发生错误: {e}")
            import traceback
            traceback.print_exc()

            # 更新任务状态为失败
            if self.current_task_id:
                self.task_manager.update_task_status(
                    self.current_task_id,
                    'failed',
                    error_message=str(e)
                )
        finally:
            # 注意：不在这里清空current_task_id，因为监控线程还需要使用它
            # 监控线程会在脚本完成后清空current_task_id并创建下次任务
            pass

    def _run_crawl_script(self):
        """运行爬取脚本 - 使用nohup后台运行"""
        try:
            # 导入必要的模块
            import subprocess
            import os

            # 获取脚本路径
            script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'pa_week_ar_test.py')
            log_path = os.path.join(os.path.dirname(script_path), 'crawl_week.log')

            if not os.path.exists(script_path):
                print(f"爬取脚本不存在: {script_path}")
                return False

            print(f"执行脚本: {script_path}")
            print(f"日志文件: {log_path}")

            # 使用nohup命令后台运行脚本

            if os.name == 'nt':  # Windows
                # Windows下使用start命令后台运行
                cmd = f'start /B python -u "{script_path}" > "{log_path}" 2>&1'
                process = subprocess.Popen(cmd, shell=True, cwd=os.path.dirname(script_path))
                print(f"Windows后台启动命令: {cmd}")
            else:  # Linux/Unix
                # Linux下使用nohup命令
                cmd = f'nohup python -u "{script_path}" > "{log_path}" 2>&1 &'
                process = subprocess.Popen(cmd, shell=True, cwd=os.path.dirname(script_path))
                print(f"Linux后台启动命令: {cmd}")

            print(f"后台进程已启动，PID: {process.pid}")
            print(f"脚本将在后台运行，日志输出到: {log_path}")

            # 不等待进程完成，直接返回成功（因为是后台运行）
            return True

        except Exception as e:
            print(f"运行爬取脚本时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _stop_crawl_script(self):
        """停止正在运行的爬取脚本"""
        try:
            import subprocess
            import os

            if os.name == 'nt':  # Windows
                # Windows下杀掉python进程
                cmd = 'taskkill /f /im python.exe /fi "WINDOWTITLE eq pa_week_ar_test*"'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                print(f"Windows停止命令: {cmd}")
                print(f"结果: {result.stdout}")
            else:  # Linux/Unix
                # Linux下使用pkill命令
                cmd = 'pkill -f "pa_week_ar_test.py"'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                print(f"Linux停止命令: {cmd}")
                print(f"结果: {result.stdout}")

            print("已尝试停止pa_week_ar_test.py进程")
            return True

        except Exception as e:
            print(f"停止爬取脚本时发生错误: {e}")
            return False

    def _start_task_monitor(self, task_id):
        """启动任务监控线程，检查脚本执行状态"""
        def monitor_task():
            import time
            print(f"开始监控任务 {task_id} 的执行状态")

            # 监控脚本执行状态，使用动态检查间隔
            max_wait_time = 48*3600  # 最大等待2天
            elapsed_time = 0

            def get_check_interval(elapsed_seconds):
                """根据已运行时间动态调整检查间隔"""
                if elapsed_seconds < 300:  # 前5分钟
                    return 30  # 每30秒检查一次
                elif elapsed_seconds < 1800:  # 前30分钟
                    return 60  # 每1分钟检查一次
                elif elapsed_seconds < 3600:  # 前1小时
                    return 300  # 每5分钟检查一次
                elif elapsed_seconds < 7200:  # 前2小时
                    return 600  # 每10分钟检查一次
                else:  # 2小时后
                    return 1800  # 每30分钟检查一次

            while elapsed_time < max_wait_time:
                check_interval = get_check_interval(elapsed_time)
                time.sleep(check_interval)
                elapsed_time += check_interval

                # 检查脚本是否还在运行
                if not self.task_manager._is_weekly_crawl_running():
                    print(f"检测到脚本已完成，更新任务 {task_id} 状态")

                    # 脚本已完成，更新任务状态为completed
                    self.task_manager.update_task_status(task_id, 'completed')

                    # 如果定时调度器是活跃的，现在创建下一个pending任务
                    if self.is_active:
                        try:
                            self.current_task_id = self.task_manager.create_task(
                                task_type='weekly_crawl',
                                task_params={
                                    'script_path': 'pa_week_ar_test.py',
                                    'log_file': 'crawl_week.log',
                                    'description': '每周威胁情报网站爬取任务（等待下次执行）',
                                    'scheduled': True,
                                    'next_scheduled': True
                                },
                                total_count=1,
                                created_by='scheduler'
                            )
                            print(f"脚本执行完成后，创建下次执行的任务记录，任务ID: {self.current_task_id}")
                        except Exception as e:
                            print(f"创建下次任务记录失败: {e}")
                            self.current_task_id = None

                    break
                else:
                    # 根据运行时间显示不同的信息
                    if elapsed_time < 3600:
                        print(f"任务 {task_id} 仍在运行中，已等待 {elapsed_time} 秒")
                    else:
                        hours = elapsed_time // 3600
                        minutes = (elapsed_time % 3600) // 60
                        print(f"任务 {task_id} 仍在运行中，已等待 {hours}小时{minutes}分钟")

            # 如果超时仍未完成，标记为超时
            if elapsed_time >= max_wait_time:
                print(f"任务 {task_id} 执行超时，标记为失败")
                self.task_manager.update_task_status(
                    task_id,
                    'failed',
                    error_message='任务执行超时'
                )

        # 在新线程中启动监控
        monitor_thread = threading.Thread(target=monitor_task, daemon=True)
        monitor_thread.start()

    def _start_scheduled_task_monitor(self, task_id):
        """启动定时任务监控线程，检查脚本执行状态"""
        def monitor_scheduled_task():
            import time
            print(f"开始监控定时任务 {task_id} 的执行状态")

            # 监控脚本执行状态，使用动态检查间隔
            max_wait_time = 48*3600  # 最大等待2天
            elapsed_time = 0

            def get_check_interval(elapsed_seconds):
                """根据已运行时间动态调整检查间隔"""
                if elapsed_seconds < 300:  # 前5分钟
                    return 30  # 每30秒检查一次
                elif elapsed_seconds < 1800:  # 前30分钟
                    return 60  # 每1分钟检查一次
                elif elapsed_seconds < 3600:  # 前1小时
                    return 300  # 每5分钟检查一次
                elif elapsed_seconds < 7200:  # 前2小时
                    return 600  # 每10分钟检查一次
                else:  # 2小时后
                    return 1800  # 每30分钟检查一次

            while elapsed_time < max_wait_time:
                check_interval = get_check_interval(elapsed_time)
                time.sleep(check_interval)
                elapsed_time += check_interval

                # 检查脚本是否还在运行
                if not self.task_manager._is_weekly_crawl_running():
                    print(f"检测到定时脚本已完成，更新任务 {task_id} 状态")

                    # 脚本已完成，更新任务状态为completed
                    self.task_manager.update_task_status(task_id, 'completed')

                    # 清空当前任务ID
                    self.current_task_id = None

                    # 安排下次执行
                    if self.is_active:
                        # 创建下一个pending状态的任务
                        try:
                            self.current_task_id = self.task_manager.create_task(
                                task_type='weekly_crawl',
                                task_params={
                                    'script_path': 'pa_week_ar_test.py',
                                    'log_file': 'crawl_week.log',
                                    'description': '每周威胁情报网站爬取任务（等待下次执行）',
                                    'scheduled': True,
                                    'next_scheduled': True
                                },
                                total_count=1,
                                created_by='scheduler'
                            )
                            print(f"定时任务执行完成后，创建下次执行的任务记录，任务ID: {self.current_task_id}")
                        except Exception as e:
                            print(f"创建下次任务记录失败: {e}")
                            self.current_task_id = None

                        # 安排下次执行
                        self._schedule_next_run()

                    break
                else:
                    # 根据运行时间显示不同的信息
                    if elapsed_time < 3600:
                        print(f"定时任务 {task_id} 仍在运行中，已等待 {elapsed_time} 秒")
                    else:
                        hours = elapsed_time // 3600
                        minutes = (elapsed_time % 3600) // 60
                        print(f"定时任务 {task_id} 仍在运行中，已等待 {hours}小时{minutes}分钟")

            # 如果超时仍未完成，标记为超时
            if elapsed_time >= max_wait_time:
                print(f"定时任务 {task_id} 执行超时，标记为失败")
                self.task_manager.update_task_status(
                    task_id,
                    'failed',
                    error_message='任务执行超时'
                )
                # 清空当前任务ID
                self.current_task_id = None

        # 在新线程中启动监控
        monitor_thread = threading.Thread(target=monitor_scheduled_task, daemon=True)
        monitor_thread.start()

    def run_now(self):
        """立即执行一次任务"""
        print("立即执行威胁情报爬取任务")

        def run_task():
            try:
                # 创建任务记录
                task_id = self.task_manager.create_task(
                    task_type='weekly_crawl',
                    task_params={
                        'script_path': 'pa_week_ar_test.py',
                        'log_file': 'crawl_week.log',
                        'description': '手动触发的威胁情报网站爬取任务',
                        'manual': True
                    },
                    total_count=1,
                    created_by='user'
                )

                # 更新任务状态为运行中
                self.task_manager.update_task_status(task_id, 'running')

                # 执行任务（后台运行）
                success = self._run_crawl_script()
                self.last_run_time = datetime.now()

                # 更新任务状态
                if success:
                    # 后台运行成功启动，保持running状态，因为脚本还在执行中
                    print(f"手动任务 {task_id} 已启动（后台运行中）")

                    # 启动一个监控线程来检查脚本执行状态
                    self._start_task_monitor(task_id)
                else:
                    self.task_manager.update_task_status(
                        task_id,
                        'failed',
                        error_message='脚本启动失败'
                    )

                return success
            except Exception as e:
                print(f"立即执行任务时发生错误: {e}")
                if 'task_id' in locals():
                    self.task_manager.update_task_status(
                        task_id,
                        'failed',
                        error_message=str(e)
                    )
                return False

        # 在新线程中执行，避免阻塞主线程
        task_thread = threading.Thread(target=run_task)
        task_thread.daemon = True
        task_thread.start()

        return True

    def get_status(self):
        """获取任务状态"""
        return {
            'is_active': self.is_active,
            'next_run': self.next_run_time.isoformat() if self.next_run_time else None,
            'last_run': self.last_run_time.isoformat() if self.last_run_time else None
        }
