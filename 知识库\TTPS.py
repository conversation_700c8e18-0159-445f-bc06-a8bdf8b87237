import requests
from bs4 import BeautifulSoup
import csv
import time
import os
import re

'''设置所需版本'''
version = 17  # 设置ATT&CK版本号

# URL配置 - 注意：ICS领域的URL格式可能有所不同
base_url = f"https://attack.mitre.org/versions/v{version}/"

# 列表页面URL
tactics_urls = {
    "enterprise": f"{base_url}tactics/enterprise/",
    "mobile": f"{base_url}tactics/mobile/",
    "ics": f"{base_url}tactics/ics/"  # 这是列表页面
}
techniques_urls = {
    # "enterprise": f"{base_url}techniques/enterprise/",
    "mobile": f"{base_url}techniques/mobile/",
    "ics": f"{base_url}techniques/ics/"
}
mitigations_urls = {
    "enterprise": f"{base_url}mitigations/enterprise/",
    "mobile": f"{base_url}mitigations/mobile/",
    "ics": f"{base_url}mitigations/ics/"
}

# 结果文件
tactics_txt_files = {
    "enterprise": f'mitre_tactics_enterprise_v{version}.txt',
    "mobile": f'mitre_tactics_mobile_v{version}.txt',
    "ics": f'mitre_tactics_ics_v{version}.txt'
}
techniques_txt_files = {
    # "enterprise": f'mitre_techniques_enterprise_v{version}.txt',
    "mobile": f'mitre_techniques_mobile_v{version}.txt',
    "ics": f'mitre_techniques_ics_v{version}.txt'
}
mitigations_txt_files = {
    "enterprise": f'mitre_mitigations_enterprise_v{version}.txt',
    "mobile": f'mitre_mitigations_mobile_v{version}.txt',
    "ics": f'mitre_mitigations_ics_v{version}.txt'
}

# CSV结果文件
tactics_csv_files = {
    "enterprise": f'result_tactics_enterprise_v{version}.csv',
    "mobile": f'result_tactics_mobile_v{version}.csv',
    "ics": f'result_tactics_ics_v{version}.csv'
}
techniques_csv_files = {
    # "enterprise": f'result_techniques_enterprise_v{version}.csv',
    "mobile": f'result_techniques_mobile_v{version}.csv',
    "ics": f'result_techniques_ics_v{version}.csv'
}
mitigations_csv_files = {
    "enterprise": f'result_mitigations_enterprise_v{version}.csv',
    "mobile": f'result_mitigations_mobile_v{version}.csv',
    "ics": f'result_mitigations_ics_v{version}.csv'
}

# 全局变量
start_time = 0
end_time = 0


def fetch_html(url):
    """获取URL的HTML内容"""
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            print(f"{url} fetch html failed (attempt {retry_count+1}/{max_retries}): {e}")
            retry_count += 1
            time.sleep(2)  # 添加延迟，避免过快请求
    
    return None


def get_soup(html):
    """将HTML转换为BeautifulSoup对象"""
    if html is None:
        return None
    soup = BeautifulSoup(html, "html.parser")
    return soup


def find_tbody(soup):
    """在页面中查找表格主体"""
    if soup is None:
        return None
    tbody = soup.find("tbody")
    return tbody


def find_tr(tbody):
    """查找表格行"""
    if tbody is None:
        return []
    tr = tbody.find_all("tr")
    return tr


def create_txt_file(filename):
    """创建TXT文件并写入标题行"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("ID\tName\tDescription\n")


def append_to_txt(id, name, description, filename):
    """将信息添加到TXT文件"""
    with open(filename, 'a', encoding='utf-8') as f:
        # 清理文本
        id = id.replace('\t', ' ').replace('\n', ' ').strip()
        name = name.replace('\t', ' ').replace('\n', ' ').strip()
        
        # 清理描述中的引用标记 [1], [2], [3] 等
        description = re.sub(r'\[\d+\]', '', description)
        description = description.replace('\t', ' ').replace('\n', ' ').strip()
        
        # 修改: 使用标准格式输出技术ID和子技术ID
        if '.' in id:
            # 对于子技术 (如 T1548.001)，使用标准点号格式
            f.write(f"{id}:{name}{description}\n")
        else:
            # 对于主技术
            f.write(f"{id}:{name},{description}\n")

def create_csv(filename):
    """创建CSV文件并写入标题行"""
    with open(filename, 'w', newline='', encoding='utf-8') as rst_csv:
        writer = csv.writer(rst_csv)
        header = ['Index', 'ID', 'Name', 'Description', 'URL']
        writer.writerow(header)


def write_result(data_list, filename):
    """将数据写入CSV文件"""
    with open(filename, 'a', newline='', encoding='utf-8') as rst_csv:
        writer = csv.writer(rst_csv)
        data_list[4] = f'=HYPERLINK("{data_list[4]}", "{data_list[4]}")'
        writer.writerow(data_list)


def process_tactics(category):
    """处理特定类别的战术数据"""
    print(f'Processing {category} tactics...')
    
    url = tactics_urls[category]
    txt_file = tactics_txt_files[category]
    csv_file = tactics_csv_files[category]
    
    html_content = fetch_html(url)
    if html_content:
        soup_content = get_soup(html_content)
        tbody_content = find_tbody(soup_content)
        tr_content = find_tr(tbody_content)
        print(f"Found {len(tr_content)} {category} tactics in the table")
        
        # 创建结果文件
        create_txt_file(txt_file)
        create_csv(csv_file)
        
        total_index = 0
        for row_index, r in enumerate(tr_content):
            td = r.find_all("td")
            id = ''
            name = ''
            description = ''
            
            # 提取ID和Name
            for j, d in enumerate(td):
                str_val = d.text.strip()
                if j == 0:
                    id = str_val
                elif j == 1:
                    name = str_val
                elif j == 2:
                    description = str_val
                else:
                    break
                    
            # 构建详细页面的URL - ICS战术使用不同的URL格式
            if category == "ics" or category == "enterprise" or category == "mobile":
                # 对于ICS战术，直接使用战术ID访问
                tactic_url = f"{base_url}tactics/{id}/"
            else:
                # 对于其他战术，使用常规URL格式
                tactic_url = f"{url}{id}/"

            
            # 获取详细描述（如果需要更详细的描述）
            detailed_html = fetch_html(tactic_url)
            if detailed_html:
                detailed_soup = get_soup(detailed_html)
                
                # 查找详细描述
                desc_div = detailed_soup.find('div', class_='description-body')
                if desc_div:
                    detailed_description = desc_div.text.strip()
                    description = detailed_description  # 使用详细描述覆盖表格中的简短描述
                
                print(f"{tactic_url} - fetch success")
            else:
                print(f"{tactic_url} - failed to fetch HTML, using table description")
            
            # 写入TXT和CSV文件
            append_to_txt(id, name, description, txt_file)
            
            list_data = [total_index, id, name, description, tactic_url]
            write_result(list_data, csv_file)
            total_index += 1
            
            # 添加延迟
            time.sleep(1)
            
        print('---------------------------------')
        print(f"Processed {len(tr_content)} {category} tactics, wrote {total_index} rows of data to {txt_file}")
    else:
        print(f"Failed to fetch {category} tactics page!")


def get_subtechniques(parent_id, detailed_soup, base_url, category, txt_file, csv_file, total_index):
    """解析子技术信息并获取详细内容"""
    found_subtechniques = 0
    
    print(f"寻找 {parent_id} 的子技术...")
    
    # 1. 在主技术页面查找子技术表格
    sub_table = detailed_soup.find('table', {'id': 'subtechniques-table'})
    if not sub_table:
        print(f"未在 {parent_id} 页面找到子技术表格")
        
        # 2. 查找可能的子技术链接区域
        sub_card = detailed_soup.find('div', class_='card-data')
        if sub_card:
            sub_links = sub_card.find_all('a')
            if sub_links:
                print(f"在卡片数据中找到了 {len(sub_links)} 个可能的子技术链接")
                
                for link in sub_links:
                    href = link.get('href', '')
                    # 检查是否是子技术链接
                    if parent_id.lower() in href.lower() and '/' in href:
                        sub_id_part = href.rstrip('/').split('/')[-1]
                        # 构建完整子技术ID
                        sub_full_id = f"{parent_id}.{sub_id_part}"
                        sub_url = f"{base_url}techniques/{parent_id}/{sub_id_part}/"
                        
                        print(f"处理子技术链接: {sub_url}")
                        
                        # 获取子技术详情
                        sub_html = fetch_html(sub_url)
                        if sub_html:
                            process_subtechnique(sub_html, sub_full_id, sub_url, txt_file, csv_file, total_index)
                            total_index += 1
                            found_subtechniques += 1
                            time.sleep(1)  # 避免请求过快
    else:
        # 3. 从表格中提取子技术信息
        sub_rows = sub_table.find('tbody').find_all('tr') if sub_table.find('tbody') else []
        if sub_rows:
            print(f"在表格中找到 {len(sub_rows)} 个子技术")
            
            for row in sub_rows:
                cells = row.find_all('td')
                if len(cells) >= 2:
                    sub_id_raw = cells[0].text.strip()
                    
                    # 尝试从链接直接获取子技术ID部分
                    id_link = cells[0].find('a')
                    if id_link and 'href' in id_link.attrs:
                        href = id_link['href']
                        sub_id_part = href.rstrip('/').split('/')[-1]
                    else:
                        # 从文本解析子技术ID
                        if '.' in sub_id_raw:
                            # 格式如 "T1548.001"
                            sub_id_part = sub_id_raw.split('.')[-1]
                        else:
                            # 格式可能是 "001" 或其他
                            sub_id_part = sub_id_raw.lstrip(':.').strip()
                            # 确保是3位数格式
                            if sub_id_part.isdigit() and len(sub_id_part) < 3:
                                sub_id_part = sub_id_part.zfill(3)
                    
                    # 构建完整的子技术ID和URL
                    sub_full_id = f"{parent_id}.{sub_id_part}"
                    sub_url = f"{base_url}techniques/{parent_id}/{sub_id_part}/"
                    
                    print(f"处理子技术: {sub_full_id}, URL: {sub_url}")
                    
                    # 获取子技术详细信息
                    sub_html = fetch_html(sub_url)
                    if sub_html:
                        process_subtechnique(sub_html, sub_full_id, sub_url, txt_file, csv_file, total_index)
                        total_index += 1
                        found_subtechniques += 1
                    
                    time.sleep(1)  # 避免请求过快
    
    # 4. 如果以上方法都没找到子技术，尝试直接从已知子技术ID构建URL
    if found_subtechniques == 0:
        print(f"未在页面中找到子技术表格或链接，尝试从已知子技术模式构建")
        
        # 常见的子技术编号模式
        common_subtechnique_patterns = ['001', '002', '003', '004', '005', '006', '007', '008', '009', '010']
        
        consecutive_failures = 0  # 跟踪连续失败次数
        
        for sub_pattern in common_subtechnique_patterns:
            sub_url = f"{base_url}techniques/{parent_id}/{sub_pattern}/"
            print(f"尝试访问可能的子技术URL: {sub_url}")
            
            sub_html = fetch_html(sub_url)
            if sub_html:
                sub_full_id = f"{parent_id}.{sub_pattern}"
                process_subtechnique(sub_html, sub_full_id, sub_url, txt_file, csv_file, total_index)
                total_index += 1
                found_subtechniques += 1
                consecutive_failures = 0  # 重置连续失败计数
                time.sleep(1)  # 避免请求过快
            else:
                consecutive_failures += 1
                print(f"子技术 {parent_id}.{sub_pattern} 不存在")
                
                # 如果连续两次失败，则退出循环
                if consecutive_failures >= 2:
                    print(f"已连续尝试 {consecutive_failures} 次无效的子技术ID，停止尝试")
                    break
    
    return total_index, found_subtechniques


def process_subtechnique(html, sub_id, sub_url, txt_file, csv_file, index):
    """处理单个子技术页面"""
    sub_soup = get_soup(html)
    sub_name = ""
    sub_desc = ""
    
    # 获取子技术名称
    title_elem = sub_soup.find('h1', class_='page-title')
    if title_elem:
        title_text = title_elem.text.strip()
        # 提取名称部分，通常格式是 "Sub-technique: XXX - Name" 或 "Technique: XXX - Name"
        if ' - ' in title_text:
            sub_name = title_text.split(' - ', 1)[1].strip()
        else:
            sub_name = title_text
    
    # 获取子技术描述
    desc_div = sub_soup.find('div', class_='description-body')
    if desc_div:
        sub_desc = desc_div.text.strip()
    
    print(f"成功获取子技术: {sub_id} - {sub_name}")
    
    # 写入文件
    append_to_txt(sub_id, sub_name, sub_desc, txt_file)
    
    list_data = [index, sub_id, sub_name, sub_desc, sub_url]
    write_result(list_data, csv_file)
    
    return sub_name, sub_desc


def process_techniques(category):
    """处理特定类别的技术数据"""
    print(f'Processing {category} techniques...')
    
    url = techniques_urls[category]
    txt_file = techniques_txt_files[category]
    csv_file = techniques_csv_files[category]
    
    html_content = fetch_html(url)
    if html_content:
        soup_content = get_soup(html_content)
        tbody_content = find_tbody(soup_content)
        tr_content = find_tr(tbody_content)
        print(f"Found {len(tr_content)} {category} techniques in the table")
        
        # 创建结果文件
        create_txt_file(txt_file)
        create_csv(csv_file)
        
        total_index = 0
        for row_index, r in enumerate(tr_content):
            td = r.find_all("td")
            id = ''
            name = ''
            description = ''
            
            # 增强的错误检查和调试输出
            if len(td) < 2:
                print(f"警告: 行 {row_index+1} 的表格单元格数量不足 ({len(td)})")
                continue
            
            # 提取ID和Name
            try:
                id = td[0].text.strip()
                name = td[1].text.strip()
                
                # 验证ID不为空
                if not id:
                    print(f"警告: 行 {row_index+1} 的技术ID为空")
                    continue
                    
                # 调试输出当前处理的技术ID
                print(f"正在处理技术: ID={id}, Name={name}")
                
            except Exception as e:
                print(f"错误: 处理行 {row_index+1} 时出现异常: {e}")
                continue
            
            # 构建详细页面的URL - 对于技术，ID可能包含点号
            technique_id = id.replace(".", "/")
            
            # 针对不同类别使用适当的URL格式
            if category == "ics" or category == "enterprise" or category == "mobile":
                technique_url = f"{base_url}techniques/{technique_id}/"
            else:
                technique_url = f"{url}{technique_id}/"
            
            print(f"访问技术详情页: {technique_url}")
            
            # 尝试获取详细描述
            detailed_html = fetch_html(technique_url)
            if detailed_html:
                detailed_soup = get_soup(detailed_html)
                
                # 查找详细描述
                desc_div = detailed_soup.find('div', class_='description-body')
                if desc_div:
                    description = desc_div.text.strip()
                
                # 写入主技术信息
                append_to_txt(id, name, description, txt_file)
                
                list_data = [total_index, id, name, description, technique_url]
                write_result(list_data, csv_file)
                total_index += 1
                
                print(f"{id} - fetch success")
                
                # 查找并处理子技术
                print(f"Looking for sub-techniques of {id}...")
                total_index, found_subtechniques = get_subtechniques(
                    parent_id=id, 
                    detailed_soup=detailed_soup,
                    base_url=base_url,
                    category=category,
                    txt_file=txt_file,
                    csv_file=csv_file,
                    total_index=total_index
                )

                if found_subtechniques > 0:
                    print(f"Successfully processed {found_subtechniques} sub-techniques for {id}")
                else:
                    print(f"No sub-techniques found for {id}")
            else:
                print(f"无法获取技术 {id} 的详细信息")

        print('---------------------------------')
        print(f"Processed {category} techniques, wrote {total_index} rows of data to {txt_file}")
    else:
        print(f"Failed to fetch {category} techniques page!")



def process_mitigations(category):
    """处理特定类别的技术数据"""
    print(f'Processing {category} mitigations_urls...')
    
    url = mitigations_urls[category]
    txt_file = mitigations_txt_files[category]
    csv_file = mitigations_csv_files[category]
    
    html_content = fetch_html(url)
    if html_content:
        soup_content = get_soup(html_content)
        tbody_content = find_tbody(soup_content)
        tr_content = find_tr(tbody_content)
        print(f"Found {len(tr_content)} {category} mitigations_urls in the table")
        
        # 创建结果文件
        create_txt_file(txt_file)
        create_csv(csv_file)
        
        total_index = 0
        for row_index, r in enumerate(tr_content):
            td = r.find_all("td")
            id = ''
            name = ''
            description = ''
            
            # 增强的错误检查和调试输出
            if len(td) < 2:
                print(f"警告: 行 {row_index+1} 的表格单元格数量不足 ({len(td)})")
                continue
            
            # 提取ID和Name
            try:
                id = td[0].text.strip()
                name = td[1].text.strip()
                
                # 验证ID不为空
                if not id:
                    print(f"警告: 行 {row_index+1} 的技术ID为空")
                    continue
                    
                # 调试输出当前处理的技术ID
                print(f"正在处理技术: ID={id}, Name={name}")
                
            except Exception as e:
                print(f"错误: 处理行 {row_index+1} 时出现异常: {e}")
                continue
            
            # 构建详细页面的URL - 对于技术，ID可能包含点号
            technique_id = id.replace(".", "/")
            
            # 针对不同类别使用适当的URL格式
            if category == "ics" or category == "enterprise" or category == "mobile":
                technique_url = f"{base_url}mitigations/{technique_id}/"
            else:
                technique_url = f"{url}{technique_id}/"
            
            print(f"访问技术详情页: {technique_url}")
            
            # 尝试获取详细描述
            detailed_html = fetch_html(technique_url)
            if detailed_html:
                detailed_soup = get_soup(detailed_html)
                
                # 查找详细描述
                desc_div = detailed_soup.find('div', class_='description-body')
                if desc_div:
                    description = desc_div.text.strip()
                
                # 写入主技术信息
                append_to_txt(id, name, description, txt_file)
                
                list_data = [total_index, id, name, description, technique_url]
                write_result(list_data, csv_file)
                total_index += 1
                
                print(f"{id} - fetch success")
                
            #     # 查找并处理子技术
            #     print(f"Looking for sub-techniques of {id}...")
            #     total_index, found_subtechniques = get_subtechniques(
            #         parent_id=id, 
            #         detailed_soup=detailed_soup,
            #         base_url=base_url,
            #         category=category,
            #         txt_file=txt_file,
            #         csv_file=csv_file,
            #         total_index=total_index
            #     )

            #     if found_subtechniques > 0:
            #         print(f"Successfully processed {found_subtechniques} sub-techniques for {id}")
            #     else:
            #         print(f"No sub-techniques found for {id}")
            # else:
            #     print(f"无法获取技术 {id} 的详细信息")

        print('---------------------------------')
        print(f"Processed {category} techniques, wrote {total_index} rows of data to {txt_file}")
    else:
        print(f"Failed to fetch {category} techniques page!")




def main():
    global start_time, end_time
    start_time = time.time()
    
    print(f'Start crawler, target: MITRE ATT&CK framework version {version}')
    print('-------------------------------------------')
    
    # 处理所有战术、技术和缓解措施
    categories = [
        # "enterprise", 
        # "mobile", 
        "ics"
                  ]
    
    # # 处理战术
    # for category in categories:
    #     process_tactics(category)
    
    # 处理技术
    # for category in categories:
    #     process_techniques(category)
        
    # 处理缓解措施
    for category in categories:
        process_mitigations(category)
    
    end_time = time.time()
    print('-------------------------------------------')
    print(f'All Done! Total time: {end_time-start_time:.2f} seconds')
    
    # 打印所有结果文件路径
    for category in categories:
        print(f'{category.capitalize()} Tactics data saved to: {os.path.abspath(tactics_txt_files[category])}')
        print(f'{category.capitalize()} Techniques data saved to: {os.path.abspath(techniques_txt_files[category])}')
        print(f'{category.capitalize()} Mitigations data saved to: {os.path.abspath(mitigations_txt_files[category])}')


if __name__ == "__main__":
    main()