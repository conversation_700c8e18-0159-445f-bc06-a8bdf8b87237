import atexit
import json
import os
import subprocess
import threading
import logging
from htmldate import find_date
import markdown
import re
from flask import Flask, Response, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import mysql.connector
from mysql.connector import Error, pooling
from dotenv import load_dotenv
import time as time_module


import uuid
from werkzeug.utils import secure_filename

from flask_httpauth import HTTPBasicAuth

import sys
import os
import json
import time
import uuid
import threading
import traceback
import markdown
import mysql.connector
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from task_manager import TaskManager, WeeklyTaskScheduler
from b_utils import identify_input_type, parse_analysis_for_intel
from RAG.utils import process_analysis_content
from config_manager import ThreatIntelConfigManager


# 修正db.env文件路径 - 指向Data_Excation目录下的db.env
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'db.env')
load_dotenv(dotenv_path)

DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'test'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

# 全局连接池
connection_pool = None

def init_connection_pool():
    """初始化全局数据库连接池"""
    global connection_pool
    try:
        pool_config = DB_CONFIG.copy()
        pool_config.update({
            'pool_name': 'main_pool',
            'pool_size': 10,  # 连接池大小
            'pool_reset_session': True
        })

        connection_pool = pooling.MySQLConnectionPool(**pool_config)
        print("全局数据库连接池初始化成功")
    except Error as e:
        print(f"连接池初始化失败: {e}")
        connection_pool = None

def get_db_connection():
    """从连接池获取数据库连接"""
    try:
        if connection_pool:
            return connection_pool.get_connection()
        else:
            # 回退到直接连接
            return mysql.connector.connect(**DB_CONFIG)
    except Error as e:
        print(f"获取数据库连接失败: {e}")
        return None


# 安全初始化任务管理器
def init_task_manager():
    """安全初始化任务管理器"""
    try:
        print("正在初始化任务管理器...")
        task_manager = TaskManager(DB_CONFIG, connection_pool)
        print("任务管理器初始化成功")
        
        # 启动后清理超时任务（可选）
        try:
            cleaned_count = task_manager.cleanup_stale_tasks(hours=24)
            if cleaned_count > 0:
                print(f"清理了 {cleaned_count} 个超时任务")
        except Exception as cleanup_error:
            print(f"清理超时任务时出错: {cleanup_error}")
        
        return task_manager
    except Exception as e:
        print(f"任务管理器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

# 安全初始化配置管理器
def init_config_manager():
    """安全初始化配置管理器"""
    try:
        print("正在初始化威胁情报配置管理器...")
        config_manager = ThreatIntelConfigManager(DB_CONFIG, connection_pool)
        print("威胁情报配置管理器初始化成功")

        # 测试基本功能
        try:
            sites_count = len(config_manager.get_all_sites())
            print(f"✓ 配置管理器工作正常，共有 {sites_count} 个网站配置")
        except Exception as test_e:
            print(f"配置管理器基本功能测试失败: {test_e}")

        return config_manager
    except Exception as e:
        print(f"威胁情报配置管理器初始化失败: {e}")
        print("系统将继续运行，但配置管理功能可能不可用")
        import traceback
        traceback.print_exc()
        return None

# 初始化任务管理器
task_manager = init_task_manager()
print(f"task_manager初始化结果: {task_manager}")

# 初始化配置管理器
config_manager = init_config_manager()

# 初始化定时任务调度器
try:
    weekly_scheduler = WeeklyTaskScheduler(task_manager)
    print(f"WeeklyTaskScheduler初始化成功")
except Exception as e:
    print(f"WeeklyTaskScheduler初始化失败: {e}")
    import traceback
    traceback.print_exc()
    weekly_scheduler = None

# 配置日志
logger = logging.getLogger(__name__)

def check_config_manager():
    """检查配置管理器是否可用"""
    if not config_manager:
        return jsonify({
            'success': False,
            'error': '配置管理器未初始化，请检查数据库连接'
        }), 503
    return None

# ArchiveBox快照缓存
_archivebox_cache = {}
_cache_last_updated = None
_cache_lock = threading.Lock()
CACHE_REFRESH_INTERVAL = 43200  # 缓存刷新间隔：12小时（进一步减少刷新频率）

def refresh_snapshot_cache():
    """异步刷新快照缓存 - 不阻塞主线程"""
    def _refresh_in_background():
        global _archivebox_cache, _cache_last_updated

        try:
            print("[缓存] 后台开始刷新ArchiveBox快照缓存...")

            # 导入ArchiveBox处理函数
            sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
            from Archivebox_cti import get_all_archivebox_snapshots

            # 获取所有快照信息，使用数据库查询（更快）
            snapshot_map = get_all_archivebox_snapshots(timeout=60)  # 数据库查询应该很快

            if snapshot_map:
                with _cache_lock:
                    _archivebox_cache = snapshot_map
                    _cache_last_updated = time.time()
                print(f"[缓存] 后台成功刷新缓存，包含 {len(snapshot_map)} 个快照")
            else:
                print("[缓存] 后台获取快照信息失败，保持原有缓存")
                # 如果获取失败，延长下次刷新时间，避免频繁失败
                with _cache_lock:
                    _cache_last_updated = time.time() - CACHE_REFRESH_INTERVAL + 7200  # 2小时后再试

        except Exception as e:
            print(f"[缓存] 后台刷新快照缓存时出错: {e}")
            # 出错时也延长下次刷新时间
            with _cache_lock:
                _cache_last_updated = time.time() - CACHE_REFRESH_INTERVAL + 7200  # 2小时后再试

    # 在后台线程中执行刷新，不阻塞当前请求
    threading.Thread(target=_refresh_in_background, daemon=True).start()

# 删除重复的函数定义，使用下面统一的版本
_cache_refreshing = False  # 防止重复刷新

def init_archivebox_cache():
    """初始化ArchiveBox快照缓存 - 优化版本"""
    global _archivebox_cache, _cache_last_updated, _cache_refreshing

    # 防止重复刷新
    with _cache_lock:
        if _cache_refreshing:
            print("ArchiveBox缓存正在刷新中，跳过重复请求")
            return False
        _cache_refreshing = True

    try:
        print("正在初始化ArchiveBox快照缓存...")

        # 导入ArchiveBox处理函数
        sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
        from Archivebox_cti import get_all_archivebox_snapshots

        # 使用数据库查询获取所有快照（更快）
        print("使用数据库查询获取ArchiveBox快照...")
        snapshot_map = get_all_archivebox_snapshots(timeout=60)

        if snapshot_map:
            with _cache_lock:
                _archivebox_cache = snapshot_map
                _cache_last_updated = time.time()

            print(f"ArchiveBox快照缓存初始化完成，共缓存 {len(snapshot_map)} 个快照")
            return True
        else:
            print("获取ArchiveBox快照失败")
            return False

    except Exception as e:
        print(f"初始化ArchiveBox缓存失败: {e}")
        return False
    finally:
        # 重置刷新标志
        with _cache_lock:
            _cache_refreshing = False

def get_snapshot_id_from_cache(url):
    """从缓存中获取快照ID"""
    global _archivebox_cache, _cache_last_updated

    with _cache_lock:
        # 检查缓存是否过期（6小时），但不在查询时刷新
        if _cache_last_updated and (time.time() - _cache_last_updated) > CACHE_REFRESH_INTERVAL:
            print("ArchiveBox缓存已过期，将在后台异步刷新...")
            # 在后台线程中异步刷新缓存，不阻塞当前查询
            refresh_snapshot_cache()

        snapshot_info = _archivebox_cache.get(url)
        if snapshot_info:
            # 兼容两种数据格式：字典格式和字符串格式
            if isinstance(snapshot_info, dict):
                return snapshot_info.get('snapshot_id')
            else:
                # 旧格式：直接是snapshot_id字符串
                return snapshot_info
        return None

def get_snapshot_url_from_cache(url):
    """从缓存中获取快照URL"""
    global _archivebox_cache, _cache_last_updated

    with _cache_lock:
        snapshot_info = _archivebox_cache.get(url)
        if snapshot_info:
            # 兼容两种数据格式：字典格式和字符串格式
            if isinstance(snapshot_info, dict):
                return snapshot_info.get('snapshot_url')
            else:
                # 旧格式：只有snapshot_id，需要构建URL
                snapshot_id = snapshot_info
                archivebox_base_url = os.getenv('ARCHIVEBOX_BASE_URL', 'http://localhost:8000')
                return f"{archivebox_base_url}/archive/{snapshot_id}/index.html"
        return None

def refresh_archivebox_cache():
    """手动刷新ArchiveBox缓存"""
    print("手动刷新ArchiveBox缓存...")
    return init_archivebox_cache()

# 在后台线程中初始化ArchiveBox缓存
def init_archivebox_cache_async():
    """异步初始化ArchiveBox缓存"""
    try:
        init_archivebox_cache()
    except Exception as e:
        print(f"异步初始化ArchiveBox缓存失败: {e}")

# 启动后台线程初始化缓存
cache_init_thread = threading.Thread(target=init_archivebox_cache_async, daemon=True)
cache_init_thread.start()

# 导入RAGAnalyzer类
sys.path.append('../../RAG')
# 在全局范围内初始化RAG分析器实例
# RAGAnalyzer单例

# 设置标志防止其他模块重复初始化
os.environ['RAG_INITIALIZING'] = '1'

# 初始化操作
print("正在初始化RAG分析器...")

try:
    from AI_copy import RAGAnalyzer
    global _analyzer_instance
    
    # 如果已有实例则直接使用
    if '_analyzer_instance' in globals() and _analyzer_instance is not None:
        print("使用已有的RAG分析器实例")
    else:
        # 创建新实例(会利用单例模式)
        _analyzer_instance = RAGAnalyzer(db_config=DB_CONFIG)
        print("RAG分析器初始化完成")
        
    # 之后再导入依赖此实例的其他模块
    from RAG.utils import web_analyze
    import pa_week_ar_test
    
except ImportError as e:
    _analyzer_instance = None
    print(f"无法导入 RAGAnalyzer 类，错误信息: {e}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path}")
    traceback.print_exc()
except Exception as e:
    _analyzer_instance = None
    print(f"初始化RAG分析器失败: {e}")
    traceback.print_exc()
finally:
    # 移除初始化标志
    if 'RAG_INITIALIZING' in os.environ:
        del os.environ['RAG_INITIALIZING']

def get_analyzer_instance():
    """获取RAGAnalyzer实例"""
    global _analyzer_instance
    if _analyzer_instance is None:
        # 如果初始化失败，尝试重新初始化
        try:
            print("尝试重新初始化RAG分析器...")
            _analyzer_instance = RAGAnalyzer(db_config=DB_CONFIG)
            print(" RAG分析器重新初始化成功")
        except Exception as e:
            print(f" 初始化RAG分析器失败: {e}")
            traceback.print_exc()
    return _analyzer_instance



app = Flask(__name__, static_folder='../frontend/static', template_folder='../frontend')
CORS(app)  # 允许跨域请求

# 添加静态文件路由，禁用JS和CSS文件的缓存
@app.route('/static/js/<path:filename>')
def static_js(filename):
    """静态JS文件服务，禁用缓存"""
    response = send_from_directory(app.static_folder + '/js', filename)
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

@app.route('/static/css/<path:filename>')
def static_css(filename):
    """静态CSS文件服务，禁用缓存"""
    response = send_from_directory(app.static_folder + '/css', filename)
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

def connect_to_db():
    """连接到MySQL数据库（使用连接池）"""
    conn = get_db_connection()
    if conn:
        print("数据库连接成功")
    else:
        print("数据库连接失败")
    return conn

def markdown_to_html(markdown_text):
    """将Markdown转换为HTML"""
    if not markdown_text:
        return "<p>无分析内容</p>"
    
    # 基本转换
    html = markdown.markdown(markdown_text, extensions=['extra'])
    return html




auth = HTTPBasicAuth()
app.config['BASIC_AUTH_USERNAME'] = 'admin'
app.config['BASIC_AUTH_PASSWORD'] = 'bi90@?xs@4ttWC:12'
app.config['GUEST_USERNAME'] = 'guest' 
app.config['GUEST_PASSWORD'] = 'xB8]fr$5vP;'

# 基本认证
@auth.verify_password
def verify_password(username, password):
    if username == app.config['BASIC_AUTH_USERNAME'] and password == app.config['BASIC_AUTH_PASSWORD']:
        return 'admin'  # 返回用户角色
    elif username == app.config['GUEST_USERNAME'] and password == app.config['GUEST_PASSWORD']:
        return 'guest'  # 返回游客角色
    return None


@app.route('/')
@auth.login_required
def index():
    """主页路由"""
    return render_template('index.html')

@app.route('/analysis')
@auth.login_required
def analysis():
    """分析页面路由"""
    return render_template('analysis.html')

@app.route('/weekly_intel')
@auth.login_required
def weekly_intel():
    """每周威胁情报页面路由"""
    return render_template('weekly_intel.html')

@app.route('/task_test')
@auth.login_required
def task_test():
    """任务测试页面路由"""
    return render_template('task_test.html')

@app.route('/scheduler_test')
@auth.login_required
def scheduler_test():
    """定时任务测试页面路由"""
    return render_template('task_scheduler_test.html')

@app.route('/config_management')
@auth.login_required
def config_management():
    """威胁情报配置管理页面路由"""
    return render_template('config_management.html')

@app.route('/analysis/<int:analysis_id>')
@auth.login_required
def analysis_detail(analysis_id):
    """分析详情页面路由"""
    return render_template('analysis_content.html')


@app.route('/api/analysis_records', methods=['GET'])
@auth.login_required
def get_analysis_records():
    """获取分析记录列表，包括已分析和未分析的数据"""
    conn = connect_to_db()
    if not conn:
        return jsonify({"error": "数据库连接失败"}), 500
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        # 获取搜索参数
        search_text = request.args.get('search', '').strip()
        search_type = request.args.get('search_type', 'all')  # 新增搜索类型参数
        analysis_type = request.args.get('analysis_type', 'all')
        status = request.args.get('status', 'all')

        # 获取分页参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 12))
        offset = (page - 1) * per_page

        # 构建查询条件
        conditions = []
        params = []

        if search_text:
            # 根据搜索类型构建不同的搜索条件
            if search_type == 'title':
                conditions.append("c.title LIKE %s")
                params.append(f"%{search_text}%")
            elif search_type == 'content':
                conditions.append("c.extracted_text LIKE %s")
                params.append(f"%{search_text}%")
            elif search_type == 'domain':
                conditions.append("c.link LIKE %s")
                params.append(f"%{search_text}%")
            else:  # search_type == 'all' 或其他值
                # 同时在标题、内容和链接中搜索
                conditions.append("(c.title LIKE %s OR c.extracted_text LIKE %s OR c.link LIKE %s)")
                params.extend([f"%{search_text}%", f"%{search_text}%", f"%{search_text}%"])
        
        # 增加过滤条件：只显示爬取成功的数据
        conditions.append("c.crawl_status = 1")
        
        # 根据分析状态过滤
        if status != "all":
            try:
                status_code = int(status)
                # 这里需要考虑未分析的情况（在rag_analysis表中不存在记录）
                if status_code == 0:  # 待分析
                    conditions.append("(a.id IS NULL OR a.analysis_status = 0)")
                elif status_code in [1, 2]:  # 已分析或分析失败
                    conditions.append("a.analysis_status = %s")
                    params.append(status_code)
            except ValueError:
                pass
        
        # 性能优化：根据是否有搜索条件选择不同的查询策略
        if search_text or analysis_type != "all":
            # 有搜索条件时使用传统JOIN查询
            query = """
            SELECT c.id as crawled_data_id, c.title, c.link as source_url,
                   c.crawl_time, c.crawl_status,
                   a.id as analysis_id, a.analysis_type, a.analysis_status,
                   a.analysis_time, a.report_time
            FROM crawled_data c
            LEFT JOIN rag_analysis a ON c.id = a.crawled_data_id
            """
        else:
            # 无搜索条件时使用UNION查询，分别处理已分析和未分析的记录
            analyzed_limit = min(per_page, per_page)  # 已分析记录的限制
            unanalyzed_limit = max(0, per_page - analyzed_limit)  # 未分析记录的限制

            query = f"""
            (SELECT c.id as crawled_data_id, c.title, c.link as source_url,
                    c.crawl_time, c.crawl_status,
                    a.id as analysis_id, a.analysis_type, a.analysis_status,
                    a.analysis_time, a.report_time
             FROM rag_analysis a
             JOIN crawled_data c ON a.crawled_data_id = c.id
             WHERE a.analysis_status = 1
             ORDER BY a.report_time DESC
             LIMIT {analyzed_limit} OFFSET {offset})
            UNION ALL
            (SELECT c.id as crawled_data_id, c.title, c.link as source_url,
                    c.crawl_time, c.crawl_status,
                    NULL as analysis_id, NULL as analysis_type, NULL as analysis_status,
                    NULL as analysis_time, NULL as report_time
             FROM crawled_data c
             WHERE c.id NOT IN (SELECT crawled_data_id FROM rag_analysis WHERE analysis_status = 1)
             LIMIT {unanalyzed_limit})
            """
            # 对于UNION查询，不需要额外的条件和排序
            conditions = []
            params = []
        
        # 只有在使用传统JOIN查询时才添加条件和排序
        if search_text or analysis_type != "all":
            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            # 根据分析类型过滤（如果选择了特定分析类型且不是未分析状态）
            if analysis_type != "all":
                if "WHERE" in query:
                    query += " AND (a.analysis_type = %s OR a.id IS NULL)"
                else:
                    query += " WHERE (a.analysis_type = %s OR a.id IS NULL)"
                params.append(analysis_type)

            # 性能优化：只对已分析记录排序，未分析记录按自然顺序
            query += " ORDER BY a.analysis_status DESC, a.report_time DESC"

            # 添加分页
            query += " LIMIT %s OFFSET %s"
            params.extend([per_page, offset])
        # UNION查询已经包含了分页和排序，不需要额外处理

        cursor.execute(query, params)
        records = cursor.fetchall()

        # 优化计数查询
        if not search_text and analysis_type == "all" and status == "all":
            # 无搜索条件时，使用快速计数
            cursor.execute("SELECT COUNT(*) as total FROM crawled_data")
            total_count = cursor.fetchone()['total']
        else:
            # 有搜索条件时，使用精确计数
            count_query = """
            SELECT COUNT(DISTINCT c.id)
            FROM crawled_data c
            LEFT JOIN rag_analysis a ON c.id = a.crawled_data_id
            """

            count_params = []
            if conditions:
                # 对于UNION查询，需要重新构建参数
                if search_text or status != 'all':
                    count_params = [p for p in params if p not in [per_page, offset]]
                    count_query += " WHERE " + " AND ".join(conditions)

            if analysis_type != "all":
                if "WHERE" in count_query:
                    count_query += " AND (a.analysis_type = %s OR a.id IS NULL)"
                else:
                    count_query += " WHERE (a.analysis_type = %s OR a.id IS NULL)"
                count_params.append(analysis_type)

            cursor.execute(count_query, count_params)
            total_count = cursor.fetchone()['COUNT(DISTINCT c.id)']
        
        # 处理结果
        result = []
        for record in records:
            # 确定分析状态
            if record['analysis_id'] is None:
                # 未分析的记录
                analysis_status = 0
                status_text = "待分析"
                analysis_type_text = "未分析"
                analysis_time = "未分析"
            else:
                # 已有分析记录
                analysis_status = record['analysis_status']
                analysis_type_text = record['analysis_type']
                
                if analysis_status == 0:
                    status_text = "待分析"
                elif analysis_status == 1:
                    status_text = "已分析"
                elif analysis_status == 2:
                    status_text = "分析失败" 
                else:
                    status_text = "未知"
                    
                # 格式化分析时间
                analysis_time = record['analysis_time'].strftime("%Y-%m-%d %H:%M:%S") if record['analysis_time'] else "未知"
            
            # 格式化其他时间
            report_time = record['report_time'].strftime("%Y-%m-%d %H:%M:%S") if record.get('report_time') else "未知"
            
            # 获取标题，如果没有则使用占位符
            article_title = record['title'] if record['title'] else f"未命名文档 #{record['crawled_data_id']}"
            
            # 组装结果
            result.append({
                "id": record['analysis_id'],  # 可能为None表示未分析
                "crawled_data_id": record['crawled_data_id'],
                "analysis_type": analysis_type_text,
                "analysis_status": analysis_status,
                "analysis_status_text": status_text,
                "analysis_time": analysis_time,
                "report_time": report_time,
                "article_title": article_title,
                "source_url": record['source_url'] or "file analysis",
                "crawl_status": record['crawl_status']
            })
        
        # 返回分页结果
        return jsonify({
            'data': result,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': (total_count + per_page - 1) // per_page
            }
        })
        
    except Error as e:
        print(f"查询数据错误: {e}")
        return jsonify({"error": f"查询失败: {e}"}), 500
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()


@app.route('/api/analysis/<int:analysis_id>', methods=['GET'])
@auth.login_required
def get_analysis_content(analysis_id):
    """获取指定分析记录的详细内容"""
    conn = connect_to_db()
    if not conn:
        return jsonify({"error": "数据库连接失败"}), 500
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        # 使用LEFT JOIN避免因crawled_data问题丢失分析记录
        query = """
        SELECT 
            a.id AS analysis_id,
            a.analysis_content, 
            a.references_text,
            a.rationale,
            a.analysis_type, 
            a.analysis_time, 
            a.report_time,
            c.id AS crawled_data_id,
            c.title AS article_title,
            c.link AS source_url,
            c.crawl_time,
            c.extracted_text AS article_content
        FROM rag_analysis a
        LEFT JOIN crawled_data c ON a.crawled_data_id = c.id
        WHERE a.id = %s
        """
        
        cursor.execute(query, (analysis_id,))
        record = cursor.fetchone()
        
        # 添加调试信息
        if not record:
            # 单独查询rag_analysis表验证记录是否存在
            cursor.execute("SELECT * FROM rag_analysis WHERE id = %s", (analysis_id,))
            rag_record = cursor.fetchone()
            
            if rag_record:
                return jsonify({
                    "error": f"分析记录存在(crawled_data_id={rag_record['crawled_data_id']})但关联数据缺失",
                    "debug": "尝试直接查询rag_analysis成功，但JOIN失败"
                }), 404
            else:
                return jsonify({"error": "分析记录不存在"}), 404
        
        # 处理结果（确保处理可能的NULL值）
        result = {
            "analysis_id": record['analysis_id'],
            "crawled_data_id": record.get('crawled_data_id'),
            "article_title": record.get('article_title', "无标题"),
            "source_url": record.get('source_url', ""),
            "crawl_time": record['crawl_time'].strftime("%Y-%m-%d %H:%M:%S") if record.get('crawl_time') else "未知",
            "article_content": record.get('article_content', ""),
            "analysis_content": record.get('analysis_content', ""),
            "references_text": record.get('references_text', ""),
            "rationale": record.get('rationale', ""),
            "analysis_type": record.get('analysis_type', "未知"),
            "analysis_time": record['analysis_time'].strftime("%Y-%m-%d %H:%M:%S") if record.get('analysis_time') else "未知",
            "report_time": record['report_time'].strftime("%Y-%m-%d %H:%M:%S") if record.get('report_time') else "未知"
        }
        
        return jsonify(result)
        
    except Error as e:
        # 添加详细错误日志
        print(f"查询分析详情出错: {str(e)}")
        return jsonify({"error": f"数据库错误: {str(e)}"}), 500
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()



# 定义允许的文件类型
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'docx', 'doc', 'html', 'json'}
def allowed_file(filename):
    """检查文件扩展名是否被允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# 创建上传目录
UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), '..', '..', 'uploads')
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 配置Flask应用的上传文件夹
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
# 设置最大上传文件大小为5MB
app.config['MAX_CONTENT_LENGTH'] = 5 * 1024 * 1024

# -------------- RAG分析相关API-----------------#
@app.route('/api/crawled_data/<int:crawled_data_id>', methods=['GET'])
@auth.login_required
def get_crawled_data(crawled_data_id):
    """获取原始爬取数据 - 优化版本，避免 find_date 阻塞，支持后台更新"""
    start_time = time.time()
    print(f"开始处理爬取数据请求 ID: {crawled_data_id}")
    
    # 添加缓存机制
    _date_cache = getattr(app, '_date_cache', {})
    if not hasattr(app, '_date_cache'):
        app._date_cache = _date_cache
    
    # 确保更新队列存在
    _date_updates = getattr(app, '_date_updates', {})
    if not hasattr(app, '_date_updates'):
        app._date_updates = _date_updates
    
    try:
        with mysql.connector.connect(**DB_CONFIG) as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute("""
                SELECT id as crawled_data_id, title as article_title, link, crawl_status,
                       crawl_time, extracted_text
                FROM crawled_data
                WHERE id = %s
            """, (crawled_data_id,))
            result = cursor.fetchone()

            if not result:
                return jsonify({"error": "爬取数据不存在"}), 404
            
            # 从缓存中获取日期
            link = result.get('link', '')
            cache_key = f"date_{link}"
            if cache_key in _date_cache:
                report_date = _date_cache[cache_key]
                print(f"从缓存获取日期: {link} -> {report_date}")
            else:
                # 使用线程获取日期，设置超时
                import threading
                date_result = ['待获取']
                date_thread = None
                
                if link:
                    def get_date():
                        try:
                            actual_date = find_date(link)
                            date_result[0] = actual_date
                            
                            # 存入缓存
                            _date_cache[cache_key] = actual_date
                            
                            # 添加到更新队列，供SSE使用
                            app._date_updates[crawled_data_id] = {
                                'report_date': actual_date,
                                'timestamp': time.time()
                            }
                            print(f"后台成功获取日期: {link} -> {actual_date}")
                        except Exception as e:
                            print(f"获取日期失败: {e}")
                    
                    date_thread = threading.Thread(target=get_date)
                    date_thread.daemon = True
                    date_thread.start()
                    date_thread.join(timeout=15)  # 最多等待15秒

                report_date = date_result[0]
                
                # 如果线程还在运行，让它在后台继续，不阻塞响应
                if date_thread and date_thread.is_alive():
                    print(f"获取日期超时，在后台继续处理: {link}")
            
            result['report_date'] = report_date
            
            process_time = time.time() - start_time
            print(f"处理爬取数据完成，ID: {crawled_data_id}, 耗时: {process_time:.2f}秒")
            return jsonify(result)
                
    except mysql.connector.Error as e:
        process_time = time.time() - start_time
        print(f"数据库错误，耗时: {process_time:.2f}秒, 错误: {str(e)}")
        return jsonify({"error": f"数据库错误: {str(e)}"}), 500
    except Exception as e:
        process_time = time.time() - start_time
        print(f"处理请求时出现意外错误，耗时: {process_time:.2f}秒, 错误: {str(e)}")
        return jsonify({"error": f"获取数据失败: {str(e)}"}), 500



@app.route('/api/analysis_stats', methods=['GET'])
@auth.login_required
def get_analysis_stats():
    """获取分析统计信息"""
    try:
        analyzer = get_analyzer_instance()
        if not analyzer:
            return jsonify({"error": "无法初始化RAG分析器"}), 500
            
        # 获取数据库状态统计
        conn = connect_to_db()
        cursor = conn.cursor(dictionary=True)
        
        # 爬取状态统计
        cursor.execute('''
        SELECT crawl_status, COUNT(*) as count
        FROM crawled_data 
        GROUP BY crawl_status
        ''')
        
        crawl_stats = {}
        status_names = {0: "未爬取", 1: "成功", 2: "失败", 3: "跳过", 4: "空白"}
        for row in cursor.fetchall():
            status = row['crawl_status']
            count = row['count']
            status_name = status_names.get(status, f"未知({status})")
            crawl_stats[status_name] = count
        
        # 检查rag_analysis表是否有数据
        cursor.execute('SELECT COUNT(*) as count FROM rag_analysis')
        total_count = cursor.fetchone()['count']
        
        analysis_stats = {}
        if total_count > 0:
            # RAG分析状态统计
            cursor.execute('''
            SELECT analysis_status, COUNT(*) as count
            FROM rag_analysis 
            GROUP BY analysis_status
            ''')
            
            analysis_names = {0: "待分析", 1: "已分析", 2: "分析失败", 3: "处理中"}
            for row in cursor.fetchall():
                status = row['analysis_status']
                count = row['count']
                status_name = analysis_names.get(status, f"未知({status})")
                analysis_stats[status_name] = count
        
        cursor.close()
        conn.close()
        
        return jsonify({
            "crawl_stats": crawl_stats,
            "analysis_stats": analysis_stats,
            "total_analysis": total_count
        })
            
    except Exception as e:
        print(f"获取分析统计信息失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"获取分析统计信息失败: {str(e)}"}), 500


@app.route('/api/analysis_batch', methods=['POST'])
@auth.login_required
def start_batch_analysis():
    """启动批量分析任务，从数据库中拿取的信息进行分析"""
    # 解析请求中的JSON数据
    data = request.json
    if not data:
        return jsonify({"error": "请求中未包含JSON数据"}), 400
    
    # 获取参数
    batch_size = data.get('batch_size', 5)
    analysis_type = data.get('analysis_type', 'all')
    input_type = data.get('input_type', 'database')
    
    # 验证参数
    if analysis_type not in ["summary", "threats", "vulns", "iocs", "all"]:
        return jsonify({"error": "无效的分析类型"}), 400
    
    if input_type not in ["url", "file", "text", "database"]:
        return jsonify({"error": "无效的输入类型"}), 400
    
    try:
        # 声明全局变量
        global task_manager
        
        # 检查任务管理器是否可用
        if task_manager is None:
            print("任务管理器未初始化，尝试重新初始化...")
            task_manager = init_task_manager()
            if task_manager is None:
                return jsonify({"error": "任务管理器初始化失败，请检查数据库连接"}), 500
        
        analyzer = get_analyzer_instance()
        if not analyzer:
            return jsonify({"error": "无法初始化RAG分析器"}), 500
        
        # 获取待分析的记录
        candidates = analyzer._get_analysis_candidates(batch_size=batch_size)
        if not candidates:
            return jsonify({
                "success": True,
                "message": "没有待分析的记录",
                "task_id": None
            })
        
        # 创建任务记录
        task_params = {
            'batch_size': batch_size,
            'analysis_type': analysis_type,
            'input_type': input_type
        }
        task_id = task_manager.create_task(
            task_type='batch_analysis',
            task_params=task_params,
            total_count=len(candidates),
            created_by=auth.current_user()
        )
        
        if not task_id:
            return jsonify({"error": "创建任务记录失败"}), 500
        
        # 更新任务状态为运行中
        task_manager.update_task_status(task_id, 'running')
        
        # 启动分析任务
        analyzed_records = []
        success_count = 0
        error_count = 0
        
        for i, record in enumerate(candidates):
            record_id, content, link, title = record  # 更新解包以包含标题
            
            try:
                if not content or content.strip() == "":
                    analyzer._mark_analysis_failed(record_id, analysis_type)
                    analyzed_records.append({
                        "id": record_id, 
                        "status": "skipped", 
                        "reason": "空内容",
                        "url": link,
                        "source_url": link,
                        "title": title,
                        "crawled_data_id": record_id
                    })
                    error_count += 1
                else:
                    # 创建临时文件
                    temp_file_path = analyzer._create_temp_file(content, f"record_{record_id}", record_id)
                    
                    try:
                        # 使用RAG系统分析文档
                        result = analyzer.rag.analyze_document(temp_file_path, analysis_type, save_to_db=False)
                        
                        if "error" in result:
                            analyzer._mark_analysis_failed(record_id, analysis_type)
                            analyzed_records.append({
                                "id": record_id, 
                                "status": "failed", 
                                "reason": result["error"],
                                "url": link,
                                "source_url": link,
                                "title": title,
                                "crawled_data_id": record_id
                            })
                            error_count += 1
                        else:
                            # 提取分析结果
                            analysis_result = result['answer']
                            references = result.get('references', [])
                            
                            # 尝试获取报告日期
                            try:
                                _, report_date, _ = web_analyze(link)
                            except:
                                report_date = None
                            
                            # 保存分析结果
                            rationale = result.get('rationale', "")
                            analysis_id = analyzer._save_rag_analysis_result(
                                record_id=record_id, 
                                analysis_text=analysis_result, 
                                references=references, 
                                rationale=rationale,
                                analysis_type=analysis_type,
                                report_date=report_date,
                                input_source=link,
                                input_type=input_type
                            )
                            
                            analyzed_records.append({
                                "id": record_id, 
                                "status": "success",
                                "analysis_id": analysis_id,
                                "url": link,
                                "source_url": link,
                                "title": title,
                                "crawled_data_id": record_id
                            })
                            success_count += 1
                        
                    finally:
                        # 删除临时文件
                        if os.path.exists(temp_file_path):
                            os.remove(temp_file_path)
                
                # 更新进度
                completed_count = i + 1
                task_manager.update_task_status(
                    task_id, 
                    'running',
                    completed_count=completed_count,
                    success_count=success_count,
                    error_count=error_count
                )
                    
            except Exception as e:
                analyzer._mark_analysis_failed(record_id, analysis_type)
                analyzed_records.append({
                    "id": record_id, 
                    "status": "failed", 
                    "reason": str(e),
                    "url": link,
                    "source_url": link,
                    "title": title,
                    "crawled_data_id": record_id
                })
                error_count += 1
                print(f"处理记录 {record_id} 时出错: {e}")
                traceback.print_exc()
        
        # 完成任务
        task_manager.update_task_status(
            task_id, 
            'completed',
            completed_count=len(candidates),
            success_count=success_count,
            error_count=error_count,
            results=analyzed_records
        )
        
        return jsonify({
            "success": True,
            "message": f"批量分析任务已启动，正在处理 {len(candidates)} 条记录",
            "task_id": task_id,
            "records": analyzed_records,
            "stats": {
                "total": len(candidates),
                "success": success_count,
                "error": error_count
            }
        })
        
    except Exception as e:
        # 如果有任务ID，标记为失败
        if 'task_id' in locals() and task_id:
            task_manager.update_task_status(task_id, 'failed', error_message=str(e))
        
        print(f"批量分析失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"批量分析失败: {str(e)}"}), 500


# 单个ID进行分析
@app.route('/api/analysis_content/<int:crawled_data_id>', methods=['POST'])
@auth.login_required
def analyze_single_record(crawled_data_id):
    """分析单个爬取记录"""
    try:
        # 创建任务记录
        task_params = {
            'crawled_data_id': crawled_data_id,
            'analysis_type': 'all'
        }
        task_id = task_manager.create_task(
            task_type='single_analysis',
            task_params=task_params,
            total_count=1,
            created_by=auth.current_user()
        )
        
        if not task_id:
            return jsonify({"error": "创建任务记录失败"}), 500
        
        # 更新任务状态为运行中
        task_manager.update_task_status(task_id, 'running')
        
        # 获取RAG分析器实例
        analyzer = get_analyzer_instance()
        if not analyzer:
            task_manager.update_task_status(task_id, 'failed', error_message="无法初始化RAG分析器")
            return jsonify({"error": "无法初始化RAG分析器"}), 500
        
        # 获取待分析的数据，全部的
        records = analyzer._get_analysis_candidates(num_id=crawled_data_id)
        if not records:
            task_manager.update_task_status(task_id, 'failed', error_message="未找到待分析的记录")
            return jsonify({"error": "未找到待分析的记录"}), 404

        # 从records拿取crawled_data_id对应的数据的内容
        record = next((r for r in records if r[0] == crawled_data_id), None)
        if not record:
            task_manager.update_task_status(task_id, 'failed', error_message="未找到对应的记录")
            return jsonify({"error": "未找到对应的记录"}), 404

        record_id, content, link, title = record  # 更新解包以包含标题

        if not content or content.strip() == "":
            analyzer._mark_analysis_failed(record_id, "all")
            task_manager.update_task_status(task_id, 'failed', error_message="内容为空，无法进行分析")
            return jsonify({"error": "内容为空，无法进行分析"}), 400
        
        # 创建临时文件
        temp_file_path = analyzer._create_temp_file(content, f"record_{record_id}", record_id)
        
        try:
            # 使用RAG系统分析文档
            result = analyzer.rag.analyze_document(temp_file_path, "all", save_to_db=False)
            
            if "error" in result:
                analyzer._mark_analysis_failed(record_id, "all")
                task_manager.update_task_status(task_id, 'failed', error_message=result["error"])
                return jsonify({"error": result["error"]}), 500
            
            # 提取分析结果
            analysis_result = result['answer']
            references = result.get('references', [])
            rationale = result.get('rationale', "")

            # 尝试获取报告日期
            try:
                report_date = find_date(link)
            except:
                report_date = None
            
            # 保存分析结果
            analysis_id = analyzer._save_rag_analysis_result(
                record_id=record_id, 
                analysis_text=analysis_result, 
                references=references, 
                rationale=rationale,
                analysis_type="all",
                report_date=report_date,
                input_source=link,
                input_type="database"  # 标记为数据库输入类型
            )
            
            # 完成任务
            task_manager.update_task_status(
                task_id, 
                'completed',
                completed_count=1,
                success_count=1,
                error_count=0,
                results=[{"analysis_id": analysis_id, "status": "success"}]
            )
            
            return jsonify({
                "success": True,
                "message": "分析完成",
                "task_id": task_id,
                "analysis_id": analysis_id,  # 添加analysis_id字段
                "analysis_content": analysis_result,
                "references": references,
                "report_date": report_date
            })
            
        finally:
            # 删除临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                
    except Exception as e:
        # 如果有任务ID，标记为失败
        if 'task_id' in locals() and task_id:
            task_manager.update_task_status(task_id, 'failed', error_message=str(e))
        
        print(f"分析单个记录失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"分析失败: {str(e)}"}), 500

@app.route('/api/analysis', methods=['POST'])
@auth.login_required
def analyze_content():
    """统一的分析接口 - 支持文件上传、URL或文本内容分析"""
    input_type = None
    content_to_analyze = None

    try:
        # 1. 检查是否有文件上传
        file_path = None
        if 'file' in request.files:
            file = request.files['file']
            if file and file.filename != '':
                # 检查文件类型
                if not allowed_file(file.filename):
                    return jsonify({
                        "success": False,
                        "error": f"不允许的文件类型，仅支持: {', '.join(ALLOWED_EXTENSIONS)}"
                    }), 400
                
                # 保存上传文件
                filename = secure_filename(file.filename)
                unique_filename = f"{int(time.time())}_{uuid.uuid4().hex}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                file.save(file_path)
                print(f"文件上传成功: {file_path}")
        
         # 2. 获取其他参数（表单数据或JSON数据）
        data = {}
        if request.is_json:
            data = request.json
        elif request.form:
            data = request.form
        else:
            # 尝试获取直接的URL参数
            url = request.args.get('url')
            if url:
                data = {'content': url}
            # 如果是普通POST但不是json或form，尝试从body获取
            elif request.data:
                try:
                    data = {'content': request.data.decode('utf-8')}
                except:
                    pass
        
        # 3. 获取分析相关参数
        analysis_type = data.get('analysis_type', 'all')
        # 修复 save_to_db 处理
        save_to_db_value = data.get('save_to_db', True)  # 默认为 True
        if isinstance(save_to_db_value, bool):
            save_to_db = save_to_db_value
        elif isinstance(save_to_db_value, str):
            save_to_db = save_to_db_value.lower() == 'true'
        else:
            save_to_db = True  # 默认值
        
        # 4. 确定分析内容来源
        content_to_analyze = None
        input_type = None
        
        # 如果有文件上传，优先使用文件
        if file_path:
            content_to_analyze = file_path
            input_type = "file"
            print(f"使用上传文件进行分析: {file_path}")
        else:
            # 否则检查URL或文本内容
            content = data.get('content', '')
            input_info = identify_input_type(content)

            url = data.get('url', '').strip() or input_info.get('processed_input', '')
            
            if url:
                content_to_analyze = url
                input_info = input_info
                if input_info['input_type'] == 'url' and input_info['is_valid']:
                    input_type = "url"
                    print(f"使用URL进行分析: {url}")
                else:
                    return jsonify({
                        "success": False,
                        "error": "提供的URL格式无效"
                    }), 400
            # 当接收到内容但不是有效URL时，检查它是否为上传的文件路径
            elif content:
                if os.path.exists(content) and os.path.isfile(content):
                    # 这是一个有效的文件路径
                    content_to_analyze = content
                    input_type = "file"
                    print(f"使用已上传的文件进行分析: {content}")
                else:
                    # 不是文件路径，作为文本内容处理
                    temp_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'temp')
                    os.makedirs(temp_dir, exist_ok=True)
                    
                    temp_file_path = os.path.join(temp_dir, f"text_analysis_{int(time.time())}.txt")
                    with open(temp_file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    content_to_analyze = temp_file_path
                    input_type = "text"
                    print(f"使用文本内容进行分析，已创建临时文件: {temp_file_path}")
        
        # 5. 检查是否有有效的分析内容
        if not content_to_analyze:
            return jsonify({
                "success": False,
                "error": "未提供分析内容，请上传文件、输入URL或提供文本内容"
            }), 400
        
        # 6. 获取RAG分析器实例
        analyzer = get_analyzer_instance()
        if not analyzer:
            return jsonify({
                "success": False,
                "error": "无法初始化RAG分析器"
            }), 500
        
        # 7. 执行分析
        print(f"开始分析，类型: {analysis_type}，输入类型: {input_type}")
        result = analyzer.rag.analyze_document(content_to_analyze, analysis_type, save_to_db=save_to_db)
        
        # 8. 处理结果
        if "error" in result:
            error_msg = result["error"]
            
            # 特殊处理URL访问失败的情况
            if input_type == "url" and any(keyword in error_msg.lower() for keyword in 
                ["http错误", "403", "400", "401", "404", "500", "502", "503", "504", "超时", "timeout", "爬取url超时", "没有提取任何信息", "无法加载文档", "connection", "status", "failed to fetch", "network error", "dns", "ssl", "tls", "certificate"]):
                return jsonify({
                    "success": False,
                    "error": "URL访问失败，建议使用ArchiveBox进行分析爬取",
                    "error_type": "url_access_failed",
                    "original_error": error_msg,
                    "suggestion": "请尝试使用ArchiveBox功能来分析该URL"
                }), 400
            
            # 其他错误情况
            return jsonify({
                "success": False,
                "error": error_msg
            }), 500
        
        analysis_result = result['answer']
        references = result.get('references', [])

        analysis_content, rationale = process_analysis_content(analysis_result)
        
        # 9. 返回分析结果
        return jsonify({
            "success": True,
            "analysis_content": analysis_content,
            "references": references,
            "rationale": rationale,
            "process_time": result.get("process_time", "未知"),
            "input_type": input_type,
            "crawled_data_id": result.get("crawled_data_id"),
            "analysis_id": result.get("analysis_id")
        })
    
    except Exception as e:
        error_msg = str(e)
        print(f"分析过程中出错: {error_msg}")
        traceback.print_exc()
        
        # 特殊处理URL访问失败的情况
        if input_type == "url" and any(keyword in error_msg.lower() for keyword in 
            ["http错误", "403", "400", "401", "404", "500", "502", "503", "504", "超时", "timeout", "爬取url超时", "没有提取任何信息", "无法加载文档", "connection", "status", "failed to fetch", "network error", "dns", "ssl", "tls", "certificate"]):
            return jsonify({
                "success": False,
                "error": "URL访问失败，建议使用ArchiveBox进行分析爬取",
                "error_type": "url_access_failed",
                "original_error": error_msg,
                "suggestion": "请尝试使用ArchiveBox功能来分析该URL"
            }), 400
        
        # 其他异常情况
        return jsonify({
            "success": False,
            "error": f"分析过程中出错: {error_msg}"
        }), 500
    
    finally:
        # 清理临时文件
        if input_type == "text" and content_to_analyze and os.path.exists(content_to_analyze):
            try:
                os.remove(content_to_analyze)
                print(f"已删除临时文件: {content_to_analyze}")
            except Exception as e:
                print(f"删除临时文件失败: {e}")



# 新增 Archive box 相关接口
@app.route('/api/analysis_archivebox', methods=['POST', 'GET'])
@auth.login_required
def analysis_archivebox():
    """ArchiveBox 分析接口 - 支持单个URL或批量URL处理，以及查看快照"""
    
    # 处理GET请求 - 查看快照
    if request.method == 'GET':
        snapshot_id = request.args.get('snapshot_id')
        if not snapshot_id:
            return jsonify({"error": "未提供快照ID"}), 400
        
        try:
            # 导入ArchiveBox处理函数
            sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
            from Archivebox_cti import get_archivebox_snapshot_url
            
            # 获取快照URL并重定向到ArchiveBox界面
            snapshot_url = get_archivebox_snapshot_url(snapshot_id)
            if snapshot_url:
                # 返回重定向URL，让前端处理跳转
                return jsonify({
                    "success": True,
                    "snapshot_url": snapshot_url,
                    "snapshot_id": snapshot_id
                })
            else:
                return jsonify({
                    "success": False,
                    "error": "无法获取快照URL"
                }), 404
                
        except Exception as e:
            print(f"获取快照URL时出错: {str(e)}")
            return jsonify({
                "success": False,
                "error": f"获取快照时出错: {str(e)}"
            }), 500
    
    # 处理POST请求 
    try:
        # 解析请求中的JSON数据
        data = request.json
        if not data:
            return jsonify({"error": "请求中未包含JSON数据"}), 400
        
        # 提取参数
        urls = data.get('urls', [])  # 批量URL列表
        url = data.get('url')        # 单个URL
        snapshot_id = data.get('snapshot_id')  # 直接通过snapshot_id进行分析
        parent_task_id = data.get('parent_task_id')  # 父任务ID，用于建立任务关系
        no_analyze = data.get('no_analyze', False)  # 是否只存档不分析
        timeout = data.get('timeout', 1000)  # 超时时间
        analysis_type = data.get('analysis_type', 'all')  # 分析类型，与现有接口保持一致
        
        # 处理通过snapshot_id直接分析的情况 (点击分析按钮)
        if snapshot_id and not urls and not url:
            # 导入ArchiveBox处理函数
            sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
            from Archivebox_cti import extract_single_url_by_id, analyze_archived_content, SnapshotManager
            
            # 获取快照信息
            snapshot_manager = SnapshotManager()
            snapshot_info = snapshot_manager.get_snapshot_by_id(snapshot_id)
            if not snapshot_info:
                return jsonify({
                    "success": False,
                    "error": "无法获取快照信息"
                }), 404
            
            target_url = snapshot_info.get('url', '')
            
            start_time = time.time()
            
            try:
                # 提取快照内容
                print(f"开始分析快照 {snapshot_id}，URL: {target_url}")
                content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id, target_url)
                
                if content or html_content:
                    # 分析内容
                    analysis_result = analyze_archived_content(
                        content, html_content, target_url, title, publication_date,
                        analyze_after_crawl=True, snapshot_id=snapshot_id
                    )
                    analysis_id = analysis_result.get("analysis_id")
                    analysis_success = analysis_result.get("success", False)
                    
                    if analysis_success and analysis_id:
                        # 分析成功，如果有父任务，直接更新父任务状态和结果
                        if parent_task_id:
                            # 获取父任务当前结果
                            parent_task = task_manager.get_task_status(parent_task_id)
                            if parent_task and parent_task.get('results'):
                                current_results = parent_task['results']
                                # 更新对应快照的结果状态
                                for result in current_results:
                                    if result.get('snapshot_id') == snapshot_id:
                                        result['status'] = 'analyzed'
                                        result['analysis_id'] = analysis_id
                                        result['message'] = '分析完成'
                                        break
                                
                                # 更新父任务状态和结果
                                task_manager.update_task_status(
                                    parent_task_id,
                                    'analyzed',  # 标记为已分析状态
                                    results=current_results
                                )
                        
                        # 获取分析结果并返回
                        try:
                            analysis_content_response = get_analysis_content(analysis_id)
                            if isinstance(analysis_content_response, Response):
                                analysis_content_data = json.loads(analysis_content_response.data.decode('utf-8'))
                            else:
                                analysis_content_data = analysis_content_response
                            
                            return jsonify({
                                "success": True,
                                "analysis_content": analysis_content_data.get('analysis_content', ''),
                                "references": analysis_content_data.get('references_text', ''),
                                "rationale": analysis_content_data.get('rationale', ''),
                                "process_time": f"{time.time() - start_time:.2f}秒",
                                "input_type": "snapshot",
                                "crawled_data_id": analysis_content_data.get('crawled_data_id'),
                                "analysis_id": analysis_id,
                                "title": title or analysis_content_data.get('article_title', '无标题'),
                                "publication_date": publication_date,
                                "snapshot_id": snapshot_id,
                                "source_url": target_url,
                                "parent_task_id": parent_task_id
                            })
                        except Exception as e:
                            print(f"获取分析结果时出错: {e}")
                            # 仍然标记为成功，但返回基本信息
                            return jsonify({
                                "success": True,
                                "analysis_content": "分析已完成但获取详细结果时出错",
                                "references": "",
                                "rationale": "",
                                "process_time": f"{time.time() - start_time:.2f}秒",
                                "input_type": "snapshot",
                                "analysis_id": analysis_id,
                                "title": title,
                                "publication_date": publication_date,
                                "snapshot_id": snapshot_id,
                                "source_url": target_url,
                                "parent_task_id": parent_task_id
                            })
                    else:
                        # 分析失败，更新父任务对应快照的状态
                        if parent_task_id:
                            parent_task = task_manager.get_task_status(parent_task_id)
                            if parent_task and parent_task.get('results'):
                                current_results = parent_task['results']
                                # 更新对应快照的结果状态
                                for result in current_results:
                                    if result.get('snapshot_id') == snapshot_id:
                                        result['status'] = 'analysis_failed'
                                        result['message'] = '分析失败'
                                        break
                                
                                # 更新父任务结果
                                task_manager.update_task_status(
                                    parent_task_id,
                                    parent_task['status'],  # 保持原状态
                                    results=current_results
                                )
                        
                        return jsonify({
                            "success": False,
                            "error": "分析快照内容失败",
                            "snapshot_id": snapshot_id,
                            "parent_task_id": parent_task_id
                        }), 500
                else:
                    # 无法提取内容，更新父任务对应快照的状态
                    if parent_task_id:
                        parent_task = task_manager.get_task_status(parent_task_id)
                        if parent_task and parent_task.get('results'):
                            current_results = parent_task['results']
                            # 更新对应快照的结果状态
                            for result in current_results:
                                if result.get('snapshot_id') == snapshot_id:
                                    result['status'] = 'content_extraction_failed'
                                    result['message'] = '无法提取内容'
                                    break
                            
                            # 更新父任务结果
                            task_manager.update_task_status(
                                parent_task_id,
                                parent_task['status'],  # 保持原状态
                                results=current_results
                            )
                    
                    return jsonify({
                        "success": False,
                        "error": "无法从快照提取内容",
                        "snapshot_id": snapshot_id,
                        "parent_task_id": parent_task_id
                    }), 500
            except Exception as e:
                # 处理异常，更新父任务对应快照的状态
                print(f"分析快照时出错: {str(e)}")
                if parent_task_id:
                    parent_task = task_manager.get_task_status(parent_task_id)
                    if parent_task and parent_task.get('results'):
                        current_results = parent_task['results']
                        # 更新对应快照的结果状态
                        for result in current_results:
                            if result.get('snapshot_id') == snapshot_id:
                                result['status'] = 'analysis_error'
                                result['message'] = f'分析出错: {str(e)}'
                                break
                        
                        # 更新父任务结果
                        task_manager.update_task_status(
                            parent_task_id,
                            parent_task['status'],  # 保持原状态
                            results=current_results
                        )
                
                return jsonify({
                    "success": False,
                    "error": f"分析快照时出错: {str(e)}",
                    "snapshot_id": snapshot_id,
                    "parent_task_id": parent_task_id
                }), 500
        
        # 确保至少有一个URL（原有逻辑）
        if not urls and not url:
            return jsonify({"error": "请提供至少一个URL或快照ID"}), 400
        
        # 判断是单URL模式还是批量模式
        is_single_url = not urls and url
        
        # 如果提供了单个URL，添加到列表
        if url and url not in urls:
            urls.insert(0, url)  # 把单个URL放在最前面
        
        # 创建任务记录 - 根据是否分析设置不同的任务类型
        task_params = {
            'urls': urls,
            'no_analyze': no_analyze,
            'timeout': timeout,
            'analysis_type': analysis_type,
            'is_single_url': is_single_url
        }
        
        # 根据是否分析确定任务类型
        if no_analyze:
            task_type = 'archivebox_archive_only'
        else:
            task_type = 'archivebox_analysis'
            
        task_id = task_manager.create_task(
            task_type=task_type,
            task_params=task_params,
            total_count=len(urls),
            created_by=auth.current_user()
        )
        
        if not task_id:
            return jsonify({"error": "创建任务记录失败"}), 500
        
        # 更新任务状态为运行中
        task_manager.update_task_status(task_id, 'running')
        
        # 导入ArchiveBox处理函数
        sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
        from Archivebox_cti import add_url_to_archivebox, extract_single_url_by_id, analyze_archived_content
        from Archivebox_cti import SnapshotManager
        
        # 初始化快照管理器
        snapshot_manager = SnapshotManager()
        
        # 处理结果
        results = []
        successful_snapshots = []
        
        # 单个URL结果(特殊格式)
        single_result = {
            "success": False,
            "error": "处理失败"
        }
        start_time = time.time()
        
        success_count = 0
        error_count = 0
        
        # 处理每个URL
        for i, target_url in enumerate(urls):
            # 添加到ArchiveBox
            success, snapshot_id = add_url_to_archivebox(target_url, timeout=timeout)
            
            if success:
                if snapshot_id:
                    # 保存到快照管理器
                    snapshot_manager.add_snapshot(snapshot_id, target_url, "pending")
                    successful_snapshots.append((target_url, snapshot_id))
                    
                    # 初始化URL结果对象
                    url_result = {
                        "url": target_url,
                        "success": True,
                        "snapshot_id": snapshot_id
                    }
                    
                    # 判断是否需要分析：单个URL始终分析，多个URL根据no_analyze参数决定
                    should_analyze = is_single_url or not no_analyze
                    
                    # 如果不需要立即分析（仅限多个URL的情况），则只记录结果
                    if not should_analyze:
                        url_result.update({
                            "status": "archived",
                            "message": "已成功存档，未进行分析"
                        })
                    else:
                        # 提取和分析内容 - 添加详细日志
                        print(f"开始提取和分析URL: {target_url}, 快照ID: {snapshot_id}")
                        try:
                            content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id,target_url)
                            print(f"提取成功: {target_url}, 内容长度: {len(content) if content else 0}, HTML长度: {len(html_content) if html_content else 0}")
                            
                            if content or html_content:
                                # 分析内容
                                print(f"开始分析内容: {target_url}")

                                analysis_result = analyze_archived_content(
                                    content, html_content, target_url, title, publication_date,
                                    analyze_after_crawl=True, snapshot_id=snapshot_id
                                )
                                analysis_id = analysis_result.get("analysis_id")

                                print(f"分析结果: {analysis_result}")
                                
                                # 构建批量模式的结果
                                analysis_success = analysis_result.get("success", False)
                                print(f"分析结果详情: {analysis_result}")
                                print(f"分析是否成功: {analysis_success}")
                                
                                url_result.update({
                                    "success": analysis_success,
                                    "status": "analyzed" if analysis_success else "archive_success_analysis_failed",
                                    "title": title,
                                    "publication_date": publication_date,
                                    "content_length": len(content) if content else 0,
                                    "html_length": len(html_content) if html_content else 0,
                                    "analysis_id": analysis_id,
                                    "message": "分析完成" if analysis_success else "分析失败"
                                })
                                
                                print(f"URL结果状态设置为: {url_result.get('status')}")
                                
                                # 如果是单URL模式，按照analyze_content的格式返回
                                if is_single_url and analysis_success:
                                    # 直接按照 analysis_id 从rag_analysis表中获取分析结果
                                    if analysis_id:
                                        try:
                                            # 调用现有的 get_analysis_content 函数获取分析结果
                                            analysis_content_response = get_analysis_content(analysis_id)
                                            
                                            # 将 Flask 响应转换为 Python 字典
                                            if isinstance(analysis_content_response, Response):
                                                # 从响应中提取 JSON 数据
                                                analysis_content_data = json.loads(analysis_content_response.data.decode('utf-8'))
                                            else:
                                                # 如果已经是字典，直接使用
                                                analysis_content_data = analysis_content_response
                                            

                                            final_content = analysis_content_data.get('analysis_content', '')
                                            final_rationale = analysis_content_data.get('rationale', '')
                                            references = analysis_content_data.get('references_text', '')

                                            # 构建返回结果
                                            single_result = {
                                                "success": True,
                                                "analysis_content": final_content,
                                                "references": references,
                                                "rationale": final_rationale,
                                                "process_time": f"{time.time() - start_time:.2f}秒",
                                                "input_type": "url",
                                                "crawled_data_id": analysis_content_data.get('crawled_data_id'),
                                                "analysis_id": analysis_id,
                                                "title": title or analysis_content_data.get('article_title', '无标题'),
                                                "publication_date": publication_date,
                                                "snapshot_id": snapshot_id,
                                                "source_url": target_url
                                            }
                                        except Exception as db_error:
                                            print(f"获取分析结果时出错: {db_error}")
                                            traceback.print_exc()
                                            # 返回基本结果
                                            single_result = {
                                                "success": True,
                                                "analysis_content": "分析已完成但获取详细结果时出错",
                                                "references": [],
                                                "rationale": "",
                                                "process_time": f"{time.time() - start_time:.2f}秒",
                                                "input_type": "url",
                                                "analysis_id": analysis_id,
                                                "title": title,
                                                "publication_date": publication_date,
                                                "snapshot_id": snapshot_id,
                                                "source_url": target_url
                                            }
                                    else:
                                        # 无法获取分析ID，检查是否是重复链接且已有分析结果
                                        print(f"未获取到analysis_id，检查是否为重复链接: {target_url}")
                                        try:
                                            # 查询数据库中是否已有该链接的分析结果
                                            conn = connect_to_db()
                                            if conn:
                                                cursor = conn.cursor(dictionary=True)
                                                cursor.execute("""
                                                    SELECT ra.id as analysis_id, ra.analysis_content, ra.rationale, ra.references_text,
                                                           cd.id as crawled_data_id, cd.title as article_title
                                                    FROM crawled_data cd
                                                    LEFT JOIN rag_analysis ra ON cd.id = ra.crawled_data_id
                                                    WHERE cd.link = %s AND ra.analysis_status = 1
                                                    ORDER BY ra.analysis_time DESC
                                                    LIMIT 1
                                                """, (target_url,))
                                                
                                                existing_analysis = cursor.fetchone()
                                                cursor.close()
                                                conn.close()
                                                
                                                if existing_analysis and existing_analysis['analysis_id']:
                                                    # 找到已有的分析结果
                                                    analysis_id = existing_analysis['analysis_id']
                                                    print(f"找到已有分析结果，analysis_id: {analysis_id}")
                                                    
                                                    single_result = {
                                                        "success": True,
                                                        "analysis_content": existing_analysis['analysis_content'] or '',
                                                        "references": existing_analysis['references_text'] or '',
                                                        "rationale": existing_analysis['rationale'] or '',
                                                        "process_time": f"{time.time() - start_time:.2f}秒",
                                                        "input_type": "url",
                                                        "crawled_data_id": existing_analysis['crawled_data_id'],
                                                        "analysis_id": analysis_id,
                                                        "title": title or existing_analysis['article_title'] or '无标题',
                                                        "publication_date": publication_date,
                                                        "snapshot_id": snapshot_id,
                                                        "source_url": target_url,
                                                        "message": "使用已有分析结果（重复链接）"
                                                    }
                                                else:
                                                    # 确实没有有效的分析结果
                                                    single_result = {
                                                        "success": False,
                                                        "error": "分析过程中未生成有效的分析ID",
                                                        "process_time": f"{time.time() - start_time:.2f}秒",
                                                        "input_type": "url",
                                                        "snapshot_id": snapshot_id,
                                                        "source_url": target_url,
                                                        "title": title
                                                    }
                                            else:
                                                # 数据库连接失败
                                                single_result = {
                                                    "success": False,
                                                    "error": "无法连接数据库检查分析结果",
                                                    "process_time": f"{time.time() - start_time:.2f}秒",
                                                    "input_type": "url",
                                                    "snapshot_id": snapshot_id,
                                                    "source_url": target_url,
                                                    "title": title
                                                }
                                        except Exception as db_check_error:
                                            print(f"检查重复链接时出错: {db_check_error}")
                                            single_result = {
                                                "success": False,
                                                "error": "分析过程中未生成有效的分析ID",
                                                "process_time": f"{time.time() - start_time:.2f}秒",
                                                "input_type": "url",
                                                "snapshot_id": snapshot_id,
                                                "source_url": target_url,
                                                "title": title
                                            }
                                
                                # 更新快照状态
                                if analysis_success:
                                    snapshot_manager.mark_processed(snapshot_id, success=True)
                                    success_count += 1
                                else:
                                    snapshot_manager.mark_processed(snapshot_id, success=False)
                                    error_count += 1
                            else:
                                print(f"未能提取内容: {target_url}")
                                snapshot_manager.mark_processed(snapshot_id, success=False)
                                error_count += 1
                                url_result.update({
                                    "success": False,
                                    "status": "archive_success_content_extraction_failed",
                                    "message": "存档成功但无法提取内容"
                                })
                        except Exception as e:
                            print(f"处理URL时出错: {target_url}, 错误: {str(e)}")
                            traceback.print_exc()
                            snapshot_manager.mark_processed(snapshot_id, success=False)
                            error_count += 1
                            url_result.update({
                                "success": False,
                                "status": "archive_success_processing_error",
                                "message": f"存档成功但处理时出错: {str(e)}"
                            })
                    
                    results.append(url_result)
                else:
                    # 添加成功但无法获取snapshot_id
                    success_count += 1
                    results.append({
                        "url": target_url,
                        "success": True,
                        "status": "archive_success_no_id",
                        "message": "已存档但无法获取快照ID"
                    })
            else:
                # 添加失败
                error_count += 1
                results.append({
                    "url": target_url,
                    "success": False,
                    "status": "archive_failed",
                    "message": "存档失败"
                })
            
            # 更新任务进度
            completed_count = i + 1
            task_manager.update_task_status(
                task_id, 
                'running',
                completed_count=completed_count,
                success_count=success_count,
                error_count=error_count
            )
        
        # 确保保存快照管理器状态
        snapshot_manager.save_snapshots()
        
        # 统计结果
        stats = {
            "total": len(urls),
            "success": success_count,
            "archived": sum(1 for r in results if "snapshot_id" in r),
            "analyzed": sum(1 for r in results if r.get("status") == "analyzed"),
            "failed": error_count
        }
        
        # 完成任务
        final_status = 'completed'
        if error_count > 0 and success_count > 0:
            final_status = 'partially_completed'
        elif error_count == len(urls):
            final_status = 'failed'
            
        task_manager.update_task_status(
            task_id, 
            final_status,
            completed_count=len(urls),
            success_count=success_count,
            error_count=error_count,
            results=results
        )
        
        # 根据模式返回不同格式
        if is_single_url:
            # 单URL模式 - 与analyze_content格式一致
            single_result["task_id"] = task_id
            return jsonify(single_result)
        else:
            # 批量模式 - 返回批量处理结果
            return jsonify({
                "success": True,
                "message": f"处理了 {stats['total']} 个URL，成功: {stats['success']}，分析: {stats['analyzed']}，失败: {stats['failed']}",
                "task_id": task_id,
                "stats": stats,
                "results": results
            })
        
    except Exception as e:
        # 如果有任务ID，标记为失败
        if 'task_id' in locals() and task_id:
            task_manager.update_task_status(task_id, 'failed', error_message=str(e))
        
        print(f"批量分析快照时出错: {str(e)}")
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"批量分析失败: {str(e)}"
        }), 500
    

# -------------- 问题回答和威胁情报API -----------------#

@app.route('/api/question', methods=['POST'])
@auth.login_required
def answer_question():
    """回答问题"""
    
    # 解析请求中的JSON数据
    data = request.json
    if not data:
        return jsonify({"error": "请求中未包含JSON数据"}), 400
    
    # 获取问题
    question = data.get('question')
    if not question:
        return jsonify({"error": "请求中未指定问题"}), 400
    
    # 获取RAG分析器实例
    analyzer = get_analyzer_instance()
    if not analyzer:
        return jsonify({"error": "无法初始化RAG分析器"}), 500
    
    try:
        # 使用RAG分析器回答问题
        result = analyzer.rag.answer_question(question)
        
        return jsonify({
            "success": True,
            "answer": result.get("answer", ""),
            "references": result.get("references", [])
        })
        
    except Exception as e:
        print(f"回答问题失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"回答问题失败: {str(e)}"}), 500


# 只获取当前日期的前一周的数据
# 威胁情报缓存
_weekly_intel_cache = None
_weekly_intel_cache_timestamp = 0
_weekly_intel_cache_ttl = 300  # 缓存5分钟

# 任务状态缓存
_task_status_cache = None
_task_status_cache_timestamp = 0
_task_status_cache_ttl = 5  # 缓存5秒，减少缓存时间以提高实时性

# 任务历史缓存
_task_history_cache = {}
_task_history_cache_timestamp = {}
_task_history_cache_ttl = 30  # 缓存30秒

def clear_task_status_cache():
    """清理任务状态缓存"""
    global _task_status_cache, _task_status_cache_timestamp
    _task_status_cache = None
    _task_status_cache_timestamp = 0

def clear_task_history_cache():
    """清理任务历史缓存"""
    global _task_history_cache, _task_history_cache_timestamp
    _task_history_cache.clear()
    _task_history_cache_timestamp.clear()

@app.route('/api/weekly_intel', methods=['GET'])
@auth.login_required
def get_weekly_intel():
    """获取最近一周的威胁情报数据"""
    global _weekly_intel_cache, _weekly_intel_cache_timestamp

    # 检查缓存
    current_time = time.time()
    if (_weekly_intel_cache is not None and
        current_time - _weekly_intel_cache_timestamp < _weekly_intel_cache_ttl):
        print("使用缓存的威胁情报数据")
        return jsonify(_weekly_intel_cache)

    try:
        # 获取当前日期并计算一周前的日期
        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "数据库连接失败"}), 500

        cursor = conn.cursor(dictionary=True)

        # 计算一周前的日期 
        current_date = datetime.now().date()  # 只取日期部分，忽略时间
        one_week_ago_date = current_date - timedelta(days=7)

        # 添加调试信息
        print(f"当前日期: {current_date}")
        print(f"一周前日期: {one_week_ago_date}")

        # 查询分析过的数据（过去一周）- 只基于report_time进行过滤
        query = """
            SELECT
                a.id as analysis_id,
                a.crawled_data_id,
                a.analysis_type,
                a.analysis_content,
                a.analysis_time,
                a.report_time,
                a.references_text,
                c.title,
                c.link as source_url,
                c.extracted_text
            FROM rag_analysis a
            JOIN crawled_data c ON a.crawled_data_id = c.id
            WHERE a.analysis_status = 1
            AND a.report_time IS NOT NULL
            AND DATE(a.report_time) >= %s
            ORDER BY a.report_time DESC
        """

        cursor.execute(query, (one_week_ago_date,))
        results = cursor.fetchall()

        # 添加调试信息 - 显示查询到的数据时间范围（只显示report_time）
        # print(f"查询到 {len(results)} 条记录（只基于report_time过滤）")
        if results:
            for i, row in enumerate(results[:10]):  # 显示前10条的时间信息
                report_time = row.get('report_time')
                report_date = report_time.date() if report_time else None

                print(f"记录 {i+1}: 报告日期={report_date}, 标题={row.get('title', '无标题')[:50]}")
        else:
            print("没有找到符合条件的记录，可能原因：")
            print("1. 数据库中没有report_time不为空的记录")
            print("2. 所有记录的report_time都早于一周前")
            print(f"3. 过滤条件：report_time >= {one_week_ago_date}")

        # 处理结果，提取威胁情报
        intel_data = []
        for row in results:
            # 将分析结果解析为威胁情报
            intel_item = parse_analysis_for_intel(row)
            if intel_item:
                intel_data.append(intel_item)

        print(f"最终返回 {len(intel_data)} 条威胁情报数据")

        # 更新缓存
        _weekly_intel_cache = intel_data
        _weekly_intel_cache_timestamp = time.time()
        print(f"更新威胁情报缓存，共 {len(intel_data)} 条记录")

        return jsonify(intel_data)

    except Exception as e:
        print(f"获取威胁情报数据失败: {str(e)}")
        return jsonify({"error": "获取数据失败"}), 500
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()


# 添加SSE路由
@app.route('/api/date_updates/<int:crawled_data_id>', methods=['GET'])
def date_updates(crawled_data_id):
    """提供日期更新的SSE接口"""
    def generate():
        # 设置较长的超时时间
        timeout_seconds = 30
        start_time = time.time()
        
        # 首先检查是否已经有更新
        if hasattr(app, '_date_updates') and crawled_data_id in app._date_updates:
            update = app._date_updates[crawled_data_id]
            yield f"data: {json.dumps(update)}\n\n"
            return
            
        # 如果没有，则等待更新
        while time.time() - start_time < timeout_seconds:
            if hasattr(app, '_date_updates') and crawled_data_id in app._date_updates:
                update = app._date_updates[crawled_data_id]
                # 移除旧的更新以节省内存
                if time.time() - update['timestamp'] > 300:  # 5分钟后清理
                    app._date_updates.pop(crawled_data_id, None)
                yield f"data: {json.dumps(update)}\n\n"
                return
            
            # 等待一下再检查
            time.sleep(1)
            
        # 超时，返回无更新
        yield "data: {\"status\": \"timeout\"}\n\n"
        
    return Response(generate(), mimetype='text/event-stream')


@app.route('/api/batch_analyze_unanalyzed', methods=['POST'])
@auth.login_required
def batch_analyze_unanalyzed():
    """批量分析所有已存档但未分析的快照"""
    try:
        # 解析请求中的JSON数据
        data = request.json or {}
        analysis_type = data.get('analysis_type', 'all')  # 分析类型
        limit = data.get('limit', 50)  # 限制批量处理数量，避免过载
        
        print(f"开始批量分析未分析快照，分析类型: {analysis_type}, 限制数量: {limit}")
        
        # 导入ArchiveBox处理函数
        sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
        from Archivebox_cti import SnapshotManager, extract_single_url_by_id, analyze_archived_content
        
        # 初始化快照管理器
        snapshot_manager = SnapshotManager()
        
        # 获取所有待处理的快照
        pending_snapshots = snapshot_manager.get_pending_snapshots(limit=limit)
        
        if not pending_snapshots:
            return jsonify({
                "success": True,
                "message": "没有找到待分析的快照，所有已存档的快照都已完成分析",
                "task_id": None,
                "pending_count": 0,
                "started_snapshots": []
            })
        
        # 创建任务记录
        task_params = {
            'analysis_type': analysis_type,
            'limit': limit
        }
        task_id = task_manager.create_task(
            task_type='batch_unanalyzed',
            task_params=task_params,
            total_count=len(pending_snapshots),
            created_by=auth.current_user()
        )
        
        if not task_id:
            return jsonify({"error": "创建任务记录失败"}), 500
        
        # 更新任务状态为运行中
        task_manager.update_task_status(task_id, 'running')
        
        print(f"找到 {len(pending_snapshots)} 个待分析快照")
        
        started_snapshots = []
        success_count = 0
        error_count = 0
        
        # 批量处理快照
        for i, snapshot in enumerate(pending_snapshots):
            try:
                snapshot_id = snapshot['id']
                snapshot_url = snapshot['url']
                
                print(f"开始分析快照: {snapshot_id} - {snapshot_url}")
                
                # 提取快照内容
                content, html_content, publication_date, title = extract_single_url_by_id(snapshot_id, snapshot_url)
                
                if content or html_content:
                    print(f"成功提取快照内容: {snapshot_id}")
                    
                    # 分析内容
                    analysis_result = analyze_archived_content(
                        content=content,
                        html_content=html_content,
                        url=snapshot_url,
                        title=title,
                        publication_date=publication_date,
                        analyze_after_crawl=True,  # 强制分析
                        snapshot_id=snapshot_id  # 传入快照ID，用于删除无效快照
                    )
                    
                    if analysis_result and analysis_result.get('success', False):
                        # 标记为已处理
                        snapshot_manager.mark_processed(snapshot_id, success=True)
                        success_count += 1
                        
                        started_snapshots.append({
                            'id': snapshot_id,
                            'snapshot_id': snapshot_id,  # 明确添加snapshot_id字段
                            'url': snapshot_url,
                            'title': title if title else snapshot_url,  # 添加标题信息
                            'status': 'analyzed',
                            'analysis_id': analysis_result.get('analysis_id')
                        })
                        
                        print(f"✓ 快照分析完成: {snapshot_id}")
                    else:
                        # 标记为失败
                        snapshot_manager.mark_processed(snapshot_id, success=False)
                        error_count += 1
                        print(f"✗ 快照分析失败: {snapshot_id}")
                        
                        started_snapshots.append({
                            'id': snapshot_id,
                            'snapshot_id': snapshot_id,  # 明确添加snapshot_id字段
                            'url': snapshot_url,
                            'title': title if title else snapshot_url,  # 添加标题信息
                            'status': 'failed',
                            'error': '分析过程失败'
                        })
                else:
                    # 标记为失败
                    snapshot_manager.mark_processed(snapshot_id, success=False)
                    error_count += 1
                    print(f"✗ 快照内容提取失败: {snapshot_id}")
                    
                    started_snapshots.append({
                        'id': snapshot_id,
                        'snapshot_id': snapshot_id,  # 明确添加snapshot_id字段
                        'url': snapshot_url,
                        'title': title if title else snapshot_url,  # 添加标题信息
                        'status': 'failed',
                        'error': '内容提取失败'
                    })
                
                # 更新任务进度
                completed_count = i + 1
                task_manager.update_task_status(
                    task_id, 
                    'running',
                    completed_count=completed_count,
                    success_count=success_count,
                    error_count=error_count
                )
                    
            except Exception as e:
                error_count += 1
                error_msg = str(e)
                print(f"处理快照时出错 {snapshot.get('id', 'unknown')}: {error_msg}")
                
                # 尝试标记为失败
                try:
                    snapshot_manager.mark_processed(snapshot.get('id'), success=False)
                except:
                    pass  # 忽略标记失败的错误
                
                started_snapshots.append({
                    'id': snapshot.get('id', 'unknown'),
                    'snapshot_id': snapshot.get('id', 'unknown'),  # 明确添加snapshot_id字段
                    'url': snapshot.get('url', 'unknown'),
                    'title': snapshot.get('url', 'unknown'),  # 添加标题信息（这里用URL作为备用）
                    'status': 'failed',
                    'error': error_msg
                })
        
        # 完成父任务
        task_manager.update_task_status(
            task_id, 
            'completed',
            completed_count=len(pending_snapshots),
            success_count=success_count,
            error_count=error_count,
            results=started_snapshots
        )
        
        # 为成功分析的快照创建子任务
        analyzed_snapshots = [s for s in started_snapshots if s['status'] == 'analyzed']
        if analyzed_snapshots:
            print(f"为 {len(analyzed_snapshots)} 个已分析快照创建子任务")
            
            follow_up_task_id = task_manager.create_task(
                task_type='follow_up_analysis',
                task_params={
                    'batch_type': 'unanalyzed_snapshots',
                    'analyzed_snapshots': [r.get("snapshot_id") for r in analyzed_snapshots],
                    'analysis_type': analysis_type,
                    'analysis_ids': [r.get("analysis_id") for r in analyzed_snapshots if r.get("analysis_id")]
                },
                total_count=len(analyzed_snapshots),
                created_by=auth.current_user(),
                parent_task_id=task_id,
                is_follow_up_analysis=True,
                related_snapshot_ids=[r.get("snapshot_id") for r in analyzed_snapshots]
            )
            
            if follow_up_task_id:
                # 立即标记后续任务为完成，因为分析已经完成
                task_manager.update_task_status(
                    follow_up_task_id,
                    'completed',
                    completed_count=len(analyzed_snapshots),
                    success_count=len(analyzed_snapshots),
                    error_count=0,
                    results=[{
                        'snapshot_id': r.get("snapshot_id"),
                        'analysis_id': r.get("analysis_id"),
                        'url': r.get("url"),
                        'title': r.get("title"),
                        'status': 'analyzed'
                    } for r in analyzed_snapshots]
                )
                print(f"✓ 子任务创建完成: {follow_up_task_id}")
        
        # 返回结果
        return jsonify({
            "success": True,
            "message": f"批量分析快照任务已启动: 找到 {len(pending_snapshots)} 个待分析快照",
            "task_id": task_id,
            "pending_count": len(pending_snapshots),
            "success_count": success_count,
            "error_count": error_count,
            "started_snapshots": started_snapshots
        })
        
    except Exception as e:
        # 如果有任务ID，标记为失败
        if 'task_id' in locals() and task_id:
            task_manager.update_task_status(task_id, 'failed', error_message=str(e))
        
        print(f"批量分析快照时出错: {str(e)}")
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"批量分析失败: {str(e)}"
        }), 500


# -------------- ArchiveBox快照查询API -----------------#

@app.route('/api/snapshot_by_url', methods=['GET'])
@auth.login_required
def get_snapshot_by_url():
    """根据URL查询ArchiveBox快照信息 - 使用缓存优化"""
    url = request.args.get('url')
    print(f"[快照查询] 收到请求，URL: {url}")

    if not url:
        print("[快照查询] 错误：缺少URL参数")
        return jsonify({
            "success": False,
            "error": "URL参数为必需"
        }), 400

    try:
        # 首先从缓存中查找
        print(f"[快照查询] 从缓存中查找URL: {url}")
        snapshot_id = get_snapshot_id_from_cache(url)
        print(f"[快照查询] 缓存查询结果: {snapshot_id}")

        if snapshot_id:
            # 从缓存获取快照URL
            snapshot_url = get_snapshot_url_from_cache(url)
            
            result = {
                "success": True,
                "has_snapshot": True,
                "snapshot_id": snapshot_id,
                "snapshot_url": snapshot_url,
                "message": f"找到快照ID: {snapshot_id}",
                "from_cache": True
            }

            print(f"[快照查询] 返回成功结果: {result}")
            return jsonify(result)
        else:
            # 如果缓存中没有，直接返回未找到，不进行同步刷新
            print(f"[快照查询] 缓存中未找到URL: {url}")

            # 在后台异步刷新缓存（不阻塞响应）
            print("[快照查询] 启动后台缓存刷新...")
            threading.Thread(target=refresh_snapshot_cache, daemon=True).start()

            result = {
                "success": True,
                "has_snapshot": False,
                "snapshot_id": None,
                "snapshot_url": None,
                "message": "该链接没有使用ArchiveBox存档"
            }
            print(f"[快照查询] 返回未找到结果: {result}")
            return jsonify(result)

    except Exception as e:
        error_msg = f"查询快照时出错: {str(e)}"
        print(f"[快照查询] 异常: {error_msg}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "error": error_msg
        }), 500


@app.route('/api/archivebox_cache/refresh', methods=['POST'])
@auth.login_required
def refresh_archivebox_cache_api():
    """手动刷新ArchiveBox快照缓存"""
    try:
        print("收到手动刷新ArchiveBox缓存请求")

        # 在后台线程中刷新，立即返回响应
        def async_refresh():
            success = refresh_archivebox_cache()
            print(f"后台缓存刷新完成: {success}")

        threading.Thread(target=async_refresh, daemon=True).start()

        with _cache_lock:
            cache_size = len(_archivebox_cache)
            last_updated = _cache_last_updated

        return jsonify({
            "success": True,
            "message": f"缓存刷新已启动，当前缓存 {cache_size} 个快照",
            "cache_size": cache_size,
            "last_updated": last_updated,
            "refresh_started": True
        })

    except Exception as e:
        print(f"刷新缓存API出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"刷新缓存时出错: {str(e)}"
        }), 500


@app.route('/api/archivebox_cache/status', methods=['GET'])
@auth.login_required
def get_archivebox_cache_status():
    """获取ArchiveBox缓存状态"""
    try:
        with _cache_lock:
            cache_size = len(_archivebox_cache)
            last_updated = _cache_last_updated

        # 计算缓存年龄
        cache_age = None
        if last_updated:
            cache_age = time.time() - last_updated

        return jsonify({
            "success": True,
            "cache_size": cache_size,
            "last_updated": last_updated,
            "cache_age_seconds": cache_age,
            "cache_age_minutes": cache_age / 60 if cache_age else None,
            "is_expired": cache_age > 3600 if cache_age else True
        })

    except Exception as e:
        print(f"获取缓存状态API出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"获取缓存状态时出错: {str(e)}"
        }), 500


@app.route('/api/archivebox_cache/initialize', methods=['POST'])
@auth.login_required
def initialize_archivebox_cache():
    """手动初始化ArchiveBox缓存"""
    try:
        # 在后台线程中初始化缓存
        def init_cache():
            try:
                print("开始手动初始化ArchiveBox缓存...")
                refresh_snapshot_cache()
                print("ArchiveBox缓存初始化完成")
            except Exception as e:
                print(f"手动初始化缓存出错: {str(e)}")

        # 启动后台初始化
        thread = threading.Thread(target=init_cache, daemon=True)
        thread.start()

        return jsonify({
            "success": True,
            "message": "缓存初始化已启动",
            "initialization_started": True
        })

    except Exception as e:
        print(f"初始化缓存API出错: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"初始化缓存时出错: {str(e)}"
        }), 500


# -------------- 任务状态管理相关API -----------------#

@app.route('/api/tasks', methods=['GET'])
@auth.login_required
def get_tasks():
    """获取任务列表"""
    try:
        task_type = request.args.get('task_type', None)
        limit = int(request.args.get('limit', 20))
        
        tasks = task_manager.get_recent_tasks(limit=limit, task_type=task_type)
        
        # 为每个任务添加友好的显示名称
        for task in tasks:
            task['task_type_display'] = TaskManager.get_task_type_display_name(task['task_type'])
            task['task_status_display'] = TaskManager.get_task_status_display_name(task['task_status'])
        
        return jsonify({
            "success": True,
            "tasks": tasks
        })
        
    except Exception as e:
        print(f"获取任务列表失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"获取任务列表失败: {str(e)}"}), 500

@app.route('/api/tasks/<int:task_id>', methods=['GET'])
@auth.login_required
def get_task_status(task_id):
    """获取任务状态"""
    try:
        task = task_manager.get_task_status(task_id)
        
        if not task:
            return jsonify({"error": "任务不存在"}), 404
        
        # 添加友好的显示名称
        task['task_type_display'] = task_manager.get_task_type_display_name(task['task_type'])
        task['task_status_display'] = task_manager.get_task_status_display_name(task['task_status'])
        
        return jsonify({
            "success": True,
            "task": task
        })
        
    except Exception as e:
        print(f"获取任务状态失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"获取任务状态失败: {str(e)}"}), 500

@app.route('/api/tasks/<int:task_id>/cancel', methods=['POST'])
@auth.login_required
def cancel_task(task_id):
    """取消任务（目前只是标记为已取消，实际任务可能已经在执行）"""
    try:
        success = task_manager.update_task_status(task_id, 'cancelled')
        
        if success:
            return jsonify({
                "success": True,
                "message": "任务已标记为取消"
            })
        else:
            return jsonify({"error": "取消任务失败"}), 500
        
    except Exception as e:
        print(f"取消任务失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"取消任务失败: {str(e)}"}), 500

@app.route('/api/tasks/stats', methods=['GET'])
@auth.login_required
def get_task_stats():
    """获取任务统计信息"""
    try:
        conn = connect_to_db()
        if not conn:
            return jsonify({"error": "数据库连接失败"}), 500
            
        cursor = conn.cursor(dictionary=True)
        
        # 按任务类型统计
        cursor.execute("""
        SELECT task_type, task_status, COUNT(*) as count
        FROM analysis_tasks 
        GROUP BY task_type, task_status
        ORDER BY task_type, task_status
        """)
        
        raw_stats = cursor.fetchall()
        
        # 整理统计数据
        stats = {}
        for row in raw_stats:
            task_type = row['task_type']
            status = row['task_status']
            count = row['count']
            
            if task_type not in stats:
                stats[task_type] = {}
            stats[task_type][status] = count
        
        # 获取最近24小时的任务数量
        cursor.execute("""
        SELECT COUNT(*) as count
        FROM analysis_tasks 
        WHERE start_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        """)
        recent_count = cursor.fetchone()['count']
        
        # 获取正在运行的任务数量
        cursor.execute("""
        SELECT COUNT(*) as count
        FROM analysis_tasks 
        WHERE task_status = 'running'
        """)
        running_count = cursor.fetchone()['count']
        
        return jsonify({
            "success": True,
            "stats": stats,
            "recent_24h": recent_count,
            "running": running_count
        })
        
    except Exception as e:
        print(f"获取任务统计失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"获取任务统计失败: {str(e)}"}), 500
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()

# ===== 定时任务相关API =====

@app.route('/api/weekly_task_status', methods=['GET'])
@auth.login_required
def get_weekly_task_status():
    """获取每周定时任务状态"""
    try:
        # 检查weekly_scheduler是否存在
        if 'weekly_scheduler' not in globals():
            logger.error("weekly_scheduler对象不存在")
            return jsonify({"error": "定时任务调度器未初始化"}), 500

        if weekly_scheduler is None:
            logger.error("weekly_scheduler对象为None")
            return jsonify({"error": "定时任务调度器为空"}), 500

        logger.info(f"weekly_scheduler类型: {type(weekly_scheduler)}")
        status = weekly_scheduler.get_status()
        logger.info(f"获取到状态: {status}")

        # 添加最近任务信息
        recent_tasks = task_manager.get_recent_tasks(
            limit=5,
            task_type='weekly_crawl'
        )

        status['recent_tasks'] = recent_tasks
        return jsonify(status)
    except Exception as e:
        logger.error(f"获取定时任务状态失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return jsonify({"error": f"获取任务状态失败: {str(e)}"}), 500

@app.route('/api/weekly_task_toggle', methods=['POST'])
@auth.login_required
def toggle_weekly_task():
    """切换每周定时任务状态"""
    try:
        if weekly_scheduler.is_active:
            success, message = weekly_scheduler.stop_weekly_schedule()
            is_active = False
        else:
            success, message = weekly_scheduler.start_weekly_schedule()
            is_active = True

        if success:
            return jsonify({
                "success": True,
                "is_active": is_active,
                "message": message
            })
        else:
            return jsonify({
                "success": False,
                "message": message
            }), 500

    except Exception as e:
        logger.error(f"切换定时任务状态失败: {e}")
        return jsonify({
            "success": False,
            "message": f"操作失败: {str(e)}"
        }), 500

@app.route('/api/weekly_task_run_now', methods=['POST'])
@auth.login_required
def run_weekly_task_now():
    """立即执行一次每周任务"""
    try:
        success = weekly_scheduler.run_now()

        if success:
            return jsonify({
                "success": True,
                "message": "任务已开始执行，请查看任务历史"
            })
        else:
            return jsonify({
                "success": False,
                "message": "任务启动失败"
            }), 500

    except Exception as e:
        logger.error(f"立即执行任务失败: {e}")
        return jsonify({
            "success": False,
            "message": f"执行失败: {str(e)}"
        }), 500

@app.route('/api/update_task_status', methods=['POST'])
@auth.login_required
def update_task_status_by_snapshot():
    """根据快照ID更新任务状态为已分析 - 仅更新前端显示状态，不保存到数据库"""
    conn = None
    cursor = None
    try:
        data = request.json or {}
        snapshot_id = data.get('snapshot_id')
        status = data.get('status', 'analyzed')
        analysis_id = data.get('analysis_id')  # 可选的分析ID
        
        if not snapshot_id:
            return jsonify({"error": "缺少快照ID"}), 400
        
        # 连接数据库，仅更新任务结果中的JSON状态
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查找包含该快照ID的所有任务结果并更新状态
        cursor.execute("""
            SELECT id, results FROM analysis_tasks 
            WHERE results IS NOT NULL 
            AND JSON_CONTAINS_PATH(results, 'one', '$[*].snapshot_id') 
            AND JSON_EXTRACT(results, '$[*].snapshot_id') LIKE %s
            ORDER BY start_time DESC
        """, (f'%{snapshot_id}%',))
        
        task_results = cursor.fetchall()
        updated_count = 0
        
        for task_result in task_results:
            analysis_task_id, results_json = task_result
            if results_json:
                try:
                    results = json.loads(results_json)
                    # 更新对应快照的状态
                    task_updated = False
                    for item in results:
                        if item.get('snapshot_id') == snapshot_id:
                            item['status'] = status
                            # 如果提供了analysis_id，也要更新
                            if analysis_id:
                                item['analysis_id'] = analysis_id
                            task_updated = True
                    
                    if task_updated:
                        # 更新数据库中的results字段
                        cursor.execute("""
                            UPDATE analysis_tasks 
                            SET results = %s 
                            WHERE id = %s
                        """, (json.dumps(results), analysis_task_id))
                        
                        updated_count += 1
                        print(f"已更新任务结果中的状态: 任务ID {analysis_task_id}, 快照ID {snapshot_id}, 状态 {status}")
                        if analysis_id:
                            print(f"同时更新了分析ID: {analysis_id}")
                    
                except json.JSONDecodeError:
                    print(f"解析任务结果JSON失败: {results_json}")
        
        conn.commit()
        
        if updated_count == 0:
            print(f"未找到快照ID为 {snapshot_id} 的任务结果，跳过状态更新")
        else:
            print(f"总共更新了 {updated_count} 个任务中的快照状态")
        
        # 无论是否找到都返回成功，因为这只是前端状态更新
        return jsonify({
            "success": True,
            "message": f"状态已更新为 {status}",
            "snapshot_id": snapshot_id,
            "status": status,
            "updated": updated_count > 0,
            "updated_tasks": updated_count
        })
        
    except Exception as e:
        print(f"更新任务状态失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"更新失败: {str(e)}"}), 500
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()

@app.route('/api/analysis_records', methods=['GET'])
@auth.login_required
def cleanup_stale_tasks():
    """手动清理超时任务"""
    try:
        data = request.json or {}
        hours = data.get('hours', 24)  # 默认24小时
        
        if not isinstance(hours, (int, float)) or hours <= 0:
            return jsonify({"error": "小时数必须是正数"}), 400
        
        cleaned_count = task_manager.cleanup_stale_tasks(hours=hours)
        
        return jsonify({
            "success": True,
            "message": f"已清理 {cleaned_count} 个超过 {hours} 小时的超时任务",
            "cleaned_count": cleaned_count
        })
        
    except Exception as e:
        print(f"清理超时任务失败: {e}")
        traceback.print_exc()
        return jsonify({"error": f"清理失败: {str(e)}"}), 500


# ==================== 威胁情报网站配置管理API ====================

@app.route('/api/config/sites', methods=['GET'])
@auth.login_required
def get_sites():
    """获取所有网站配置"""
    try:
        error_response = check_config_manager()
        if error_response:
            return error_response

        category = request.args.get('category')
        status = request.args.get('status')

        sites = config_manager.get_all_sites(category=category, status=status)

        return jsonify({
            'success': True,
            'data': sites,
            'total': len(sites)
        })

    except Exception as e:
        logger.error(f"获取网站配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/<int:site_id>', methods=['GET'])
@auth.login_required
def get_site(site_id):
    """获取单个网站配置"""
    try:
        if not config_manager:
            return jsonify({
                'success': False,
                'error': '配置管理器未初始化'
            }), 503

        site = config_manager.get_site_by_id(site_id)

        if not site:
            return jsonify({
                'success': False,
                'error': '网站配置不存在'
            }), 404

        return jsonify({
            'success': True,
            'data': site
        })

    except Exception as e:
        logger.error(f"获取网站配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites', methods=['POST'])
@auth.login_required
def add_site():
    """添加新网站配置"""
    try:
        if not config_manager:
            return jsonify({
                'success': False,
                'error': '配置管理器未初始化'
            }), 503

        data = request.get_json()

        # 验证必需字段
        required_fields = ['name', 'domain', 'url']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'缺少必需字段: {field}'
                }), 400

        site_id = config_manager.add_site(data)

        return jsonify({
            'success': True,
            'data': {'id': site_id},
            'message': '网站配置添加成功'
        })

    except ValueError as e:
        # 处理重复检查错误
        logger.warning(f"添加网站配置失败 - 重复检查: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        logger.error(f"添加网站配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/<int:site_id>', methods=['PUT'])
@auth.login_required
def update_site(site_id):
    """更新网站配置"""
    try:
        data = request.get_json()

        success = config_manager.update_site(site_id, data)

        if not success:
            return jsonify({
                'success': False,
                'error': '网站配置不存在'
            }), 404

        return jsonify({
            'success': True,
            'message': '网站配置更新成功'
        })

    except ValueError as e:
        # 处理重复检查错误
        logger.warning(f"更新网站配置失败 - 重复检查: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        logger.error(f"更新网站配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/<int:site_id>', methods=['DELETE'])
@auth.login_required
def delete_site(site_id):
    """删除网站配置"""
    try:
        success = config_manager.delete_site(site_id)

        if not success:
            return jsonify({
                'success': False,
                'error': '网站配置不存在'
            }), 404

        return jsonify({
            'success': True,
            'message': '网站配置删除成功'
        })

    except Exception as e:
        logger.error(f"删除网站配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/<int:site_id>/toggle', methods=['POST'])
@auth.login_required
def toggle_site_status(site_id):
    """切换网站状态"""
    try:
        success = config_manager.toggle_site_status(site_id)

        if not success:
            return jsonify({
                'success': False,
                'error': '网站配置不存在'
            }), 404

        return jsonify({
            'success': True,
            'message': '网站状态切换成功'
        })

    except Exception as e:
        logger.error(f"切换网站状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/summary', methods=['GET'])
@auth.login_required
def get_config_summary():
    """获取配置摘要信息"""
    try:
        summary = config_manager.get_config_summary()

        return jsonify({
            'success': True,
            'data': summary
        })

    except Exception as e:
        logger.error(f"获取配置摘要失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/export', methods=['GET'])
@auth.login_required
def export_sites():
    """导出网站配置为CSV"""
    try:
        import csv
        import io
        from flask import make_response

        sites = config_manager.get_all_sites()

        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        writer.writerow(['name', 'domain', 'url', 'description'])

        # 写入数据
        for site in sites:
            writer.writerow([
                site.get('name', ''),
                site.get('domain', ''),
                site.get('url', ''),
                site.get('description', '')
            ])

        # 创建响应
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = 'attachment; filename=threat_intel_sites.csv'

        return response

    except Exception as e:
        logger.error(f"导出网站配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/import', methods=['POST'])
@auth.login_required
def import_sites():
    """批量导入网站配置"""
    try:
        import csv
        import io

        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '未找到上传文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '未选择文件'
            }), 400

        if not file.filename.endswith('.csv'):
            return jsonify({
                'success': False,
                'error': '只支持CSV格式文件'
            }), 400

        # 读取CSV内容
        content = file.read().decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(content))

        success_count = 0
        error_count = 0
        errors = []

        for row_num, row in enumerate(csv_reader, start=2):  # 从第2行开始（第1行是表头）
            try:
                # 验证必需字段
                if not row.get('name') or not row.get('domain') or not row.get('url'):
                    error_count += 1
                    errors.append(f"第{row_num}行：缺少必需字段（name、domain、url）")
                    continue

                # 准备数据
                site_data = {
                    'name': row['name'].strip(),
                    'domain': row['domain'].strip(),
                    'url': row['url'].strip(),
                    'description': row.get('description', '').strip(),
                    'category': 'international',  # 默认分类
                    'language': 'en',  # 默认语言
                    'status': 'active',  # 默认状态
                    'priority': 5  # 默认优先级
                }

                # 根据域名自动判断分类和语言
                domain = site_data['domain'].lower()
                if any(chinese_domain in domain for chinese_domain in ['.cn', '.com.cn', '.net.cn', 'baidu', 'tencent', 'alibaba']):
                    site_data['category'] = 'chinese'
                    site_data['language'] = 'zh'

                # 添加网站
                config_manager.add_site(site_data)
                success_count += 1

            except ValueError as e:
                # 处理重复检查错误
                error_count += 1
                errors.append(f"第{row_num}行：{str(e)}")
            except Exception as e:
                error_count += 1
                errors.append(f"第{row_num}行：导入失败 - {str(e)}")

        return jsonify({
            'success': True,
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'data': {
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors[:10]  # 只返回前10个错误
            }
        })

    except Exception as e:
        logger.error(f"批量导入失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/weekly-crawl', methods=['POST'])
@auth.login_required
def start_weekly_crawl():
    """启动每周爬取任务"""
    try:
        import subprocess
        import os
        from task_manager import TaskManager

        # 获取请求参数
        try:
            data = request.get_json(force=True, silent=True) or {}
        except Exception:
            data = {}
        enable_schedule = data.get('enable_schedule', True)  # 默认启用定时调度

        logger.info(f"启动每周爬取任务，参数: {data}, enable_schedule: {enable_schedule}")

        # 创建任务管理器实例
        task_manager_instance = TaskManager(DB_CONFIG)

        # 检查是否已经有定时任务在运行
        if weekly_scheduler.is_active:
            return jsonify({
                'success': True,
                'message': '定时任务已在运行中',
                'scheduled': True,
                'already_running': True
            })

        if enable_schedule:
            # 启用定时调度并立即执行第一次任务
            success, message = weekly_scheduler.start_weekly_schedule(create_initial_task=False)
            if success:
                # 立即执行第一次任务
                weekly_scheduler.run_now()

                return jsonify({
                    'success': True,
                    'message': '定时任务已启动，首次执行已开始',
                    'scheduled': True
                })
            else:
                return jsonify({
                    'success': False,
                    'error': message
                }), 500
        else:
            # 仅立即执行一次任务（不启用定时调度）
            # 直接使用WeeklyTaskScheduler的run_now方法
            success = weekly_scheduler.run_now()

            if success:
                return jsonify({
                    'success': True,
                    'message': '每周爬取任务已启动（单次执行）'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '任务启动失败'
                }), 500



    except Exception as e:
        logger.error(f"启动每周爬取任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/weekly-crawl/status', methods=['GET'])
@auth.login_required
def get_weekly_crawl_status():
    """获取每周爬取任务状态"""
    global _task_status_cache, _task_status_cache_timestamp

    # 检查缓存
    current_time = time.time()
    if (_task_status_cache is not None and
        current_time - _task_status_cache_timestamp < _task_status_cache_ttl):
        return jsonify(_task_status_cache)

    try:
        # 使用全局的task_manager实例，避免重复创建
        global task_manager
        if task_manager is None:
            task_manager = init_task_manager()
            if task_manager is None:
                return jsonify({
                    'success': False,
                    'error': '任务管理器不可用'
                }), 500

        # 获取最近的每周爬取任务（获取更多任务以检查是否有运行中的）
        recent_tasks = task_manager.get_recent_tasks(
            limit=10,  # 获取最近10个任务
            task_type='weekly_crawl'
        )

        # 查找是否有运行中或等待中的任务
        running_task = None
        has_running_task = False

        for task in recent_tasks:
            logger.info(f"检查任务: ID={task['id']}, 状态={task['task_status']}")
            if task['task_status'] in ['pending', 'running']:
                running_task = task
                has_running_task = True
                logger.info(f"找到运行中/等待中的任务: ID={task['id']}, 状态={task['task_status']}")
                break

        # 检查定时调度器状态
        scheduler_active = weekly_scheduler.is_active

        # 如果定时调度器活跃，或者有运行中的任务，都应该显示"查看任务"按钮
        if has_running_task and running_task:
            # 检查任务字段
            if 'task_status' not in running_task:
                logger.error(f"任务缺少task_status字段: {list(running_task.keys())}")
                return jsonify({
                    'success': False,
                    'error': 'task_status字段缺失'
                }), 500

            result = {
                'success': True,
                'data': {
                    'has_running_task': True,
                    'task_id': running_task['id'],
                    'status': running_task['task_status'],
                    'created_at': running_task['start_time'],
                    'updated_at': running_task['end_time'] if running_task['end_time'] else running_task['start_time'],
                    'scheduler_active': scheduler_active,
                    'next_run': weekly_scheduler.next_run_time.isoformat() if weekly_scheduler.next_run_time else None
                }
            }

            logger.info(f"返回运行中任务状态: {result}")
        elif scheduler_active:
            # 定时调度器活跃，但当前没有运行中的任务
            latest_task = recent_tasks[0] if recent_tasks else None

            result = {
                'success': True,
                'data': {
                    'has_running_task': True,  # 虽然当前没有任务运行，但定时调度器活跃，应该显示"查看任务"
                    'task_id': latest_task['id'] if latest_task else None,
                    'status': 'scheduled',  # 特殊状态表示定时调度中
                    'created_at': latest_task['start_time'] if latest_task else None,
                    'updated_at': latest_task['end_time'] if latest_task and latest_task['end_time'] else (latest_task['start_time'] if latest_task else None),
                    'scheduler_active': True,
                    'next_run': weekly_scheduler.next_run_time.isoformat() if weekly_scheduler.next_run_time else None,
                    'last_run': weekly_scheduler.last_run_time.isoformat() if weekly_scheduler.last_run_time else None
                }
            }

            logger.info(f"定时调度器活跃，返回调度状态: {result}")
        else:
            # 没有运行中的任务，也没有定时调度
            latest_task = recent_tasks[0] if recent_tasks else None

            result = {
                'success': True,
                'data': {
                    'has_running_task': False,
                    'task_id': latest_task['id'] if latest_task else None,
                    'status': latest_task['task_status'] if latest_task else None,
                    'created_at': latest_task['start_time'] if latest_task else None,
                    'updated_at': latest_task['end_time'] if latest_task and latest_task['end_time'] else (latest_task['start_time'] if latest_task else None),
                    'scheduler_active': False
                }
            }

            logger.info(f"没有运行中任务，也没有定时调度，返回最新任务状态: {result}")

        # 更新缓存
        _task_status_cache = result
        _task_status_cache_timestamp = time.time()

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取每周爬取任务状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/tasks', methods=['GET'])
@auth.login_required
def get_analysis_tasks():
    """获取分析任务历史"""
    global _task_history_cache, _task_history_cache_timestamp

    try:
        # 获取查询参数
        limit = request.args.get('limit', 20, type=int)  # 减少默认数量
        task_type = request.args.get('task_type', None)

        # 生成缓存键
        cache_key = f"{limit}_{task_type or 'all'}"
        current_time = time.time()

        # 检查缓存
        if (cache_key in _task_history_cache and
            cache_key in _task_history_cache_timestamp and
            current_time - _task_history_cache_timestamp[cache_key] < _task_history_cache_ttl):
            return jsonify(_task_history_cache[cache_key])

        # 使用全局task_manager实例
        global task_manager
        if task_manager is None:
            task_manager = init_task_manager()
            if task_manager is None:
                return jsonify({
                    'success': False,
                    'error': '任务管理器不可用'
                }), 500

        # 获取任务列表
        tasks = task_manager.get_recent_tasks(
            limit=limit,
            task_type=task_type
        )

        result = {
            'success': True,
            'data': tasks
        }

        # 更新缓存
        _task_history_cache[cache_key] = result
        _task_history_cache_timestamp[cache_key] = current_time

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取任务历史失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/tasks/<int:task_id>', methods=['GET'])
@auth.login_required
def get_task_details(task_id):
    """获取单个任务详情"""
    try:
        # 使用全局task_manager实例
        global task_manager
        if task_manager is None:
            task_manager = init_task_manager()
            if task_manager is None:
                return jsonify({
                    'success': False,
                    'error': '任务管理器不可用'
                }), 500

        # 获取任务详情
        tasks = task_manager.get_recent_tasks(limit=1000)  # 获取所有任务
        task = next((t for t in tasks if t['id'] == task_id), None)

        if not task:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404

        return jsonify({
            'success': True,
            'data': task
        })

    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/tasks/<int:task_id>/stop', methods=['POST'])
@auth.login_required
def stop_single_task(task_id):
    """停止单个任务"""
    try:
        from task_manager import TaskManager

        task_manager = TaskManager(DB_CONFIG)

        # 更新任务状态为取消
        task_manager.update_task_status(task_id, 'cancelled')

        # 清理任务状态缓存，确保状态更新及时反映
        clear_task_status_cache()

        # 如果是每周爬取任务，还需要停止进程
        tasks = task_manager.get_recent_tasks(limit=1000)
        task = next((t for t in tasks if t['id'] == task_id), None)

        if task and task['task_type'] == 'weekly_crawl':
            try:
                if os.name == 'nt':  # Windows
                    subprocess.run(['taskkill', '/f', '/im', 'python.exe', '/fi', 'WINDOWTITLE eq pa_week_ar_test*'],
                                 capture_output=True, text=True)
                else:  # Linux/Unix
                    subprocess.run(['pkill', '-f', 'pa_week_ar_test.py'],
                                 capture_output=True, text=True)

                logger.info(f"已尝试停止任务 {task_id} 的相关进程")
            except Exception as e:
                logger.warning(f"停止进程时出错: {e}")

        return jsonify({
            'success': True,
            'message': f'任务 {task_id} 已停止'
        })

    except Exception as e:
        logger.error(f"停止任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config/sites/weekly-crawl/stop', methods=['POST'])
@auth.login_required
def stop_weekly_crawl():
    """停止每周爬取任务"""
    try:
        import subprocess
        import os
        from task_manager import TaskManager

        # 获取请求参数
        data = request.get_json() or {}
        stop_schedule = data.get('stop_schedule', True)  # 是否停止定时调度

        # 创建任务管理器实例
        task_manager_instance = TaskManager(DB_CONFIG)

        # 停止定时调度器
        if stop_schedule and weekly_scheduler.is_active:
            success, message = weekly_scheduler.stop_weekly_schedule()
            if not success:
                logger.warning(f"停止定时调度失败: {message}")

        # 停止pa_week_ar_test.py进程
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['taskkill', '/f', '/im', 'python.exe', '/fi', 'WINDOWTITLE eq pa_week_ar_test*'],
                             capture_output=True, text=True)
            else:  # Linux/Unix
                subprocess.run(['pkill', '-f', 'pa_week_ar_test.py'],
                             capture_output=True, text=True)

            logger.info("已尝试停止pa_week_ar_test.py进程")
        except Exception as e:
            logger.warning(f"停止进程时出错: {e}")

        # 更新数据库中运行中的任务状态
        try:
            recent_tasks = task_manager.get_recent_tasks(
                limit=5,
                task_type='weekly_crawl'
            )

            for task in recent_tasks:
                if task['task_status'] in ['pending', 'running']:
                    task_manager.update_task_status(task['id'], 'cancelled')
                    logger.info(f"已取消任务 {task['id']}")

            # 清理任务状态缓存，确保状态更新及时反映
            clear_task_status_cache()
        except Exception as e:
            logger.warning(f"更新任务状态时出错: {e}")

        return jsonify({
            'success': True,
            'message': '已停止每周爬取任务'
        })

    except Exception as e:
        logger.error(f"停止每周爬取任务失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 注册应用关闭时的清理函数
import atexit

def cleanup_processes():
    """应用关闭时清理进程（仅在正常退出时执行）"""
    try:
        import subprocess

        print("应用正常退出，清理相关进程...")

        # Cross-platform process cleanup
        import platform
        system = platform.system()

        if system == 'Windows':
            subprocess.run(['taskkill', '/f', '/im', 'python.exe', '/fi', 'WINDOWTITLE eq pa_week_ar_test*'],
                         capture_output=True, text=True)
        elif system in ['Linux', 'Darwin']:  # Linux or macOS
            subprocess.run(['pkill', '-f', 'pa_week_ar_test.py'],
                         capture_output=True, text=True)
        else:
            print(f"Unsupported platform: {system}")  # Fallback for other platforms

        print("进程清理完成")
    except Exception as e:
        print(f"清理进程时出错: {e}")

# 只在正常退出时清理进程，服务重启时不自动清理
atexit.register(cleanup_processes)


# 启动时初始化ArchiveBox缓存
def startup_cache_initialization():
    """应用启动时初始化ArchiveBox缓存"""
    def init_cache():
        try:
            print("应用启动 - 开始初始化ArchiveBox缓存...")
            refresh_snapshot_cache()
            print("应用启动 - ArchiveBox缓存初始化完成")
        except Exception as e:
            print(f"应用启动 - 初始化缓存出错: {str(e)}")

    # 在后台线程中初始化缓存，避免阻塞应用启动
    thread = threading.Thread(target=init_cache, daemon=True)
    thread.start()
    print("ArchiveBox缓存初始化线程已启动")


if __name__ == '__main__':
    # 初始化连接池
    init_connection_pool()

    # 启动缓存初始化
    startup_cache_initialization()

    app.run(debug=False, host='0.0.0.0', port=5011)




# 使用   source ~/archivebox_venv/bin/activate   环境

# nohup python -u Page_view/backend/app.py  > App_web.log 2>&1 &