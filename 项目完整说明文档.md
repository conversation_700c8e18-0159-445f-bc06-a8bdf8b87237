# 完整项目说明

## 项目概述

这是一个基于ArchiveBox和RAG技术的威胁情报自动收集、存储、分析系统，旨在自动化收集国内外高质量威胁情报文章，并通过LLM进行智能分析和知识提取。

收集到的内容会经过智能处理流程：首先进行正文提取和清洗，去除广告和无关内容；然后使用专门为网络安全领域训练的ATTACK-BERT模型进行语义理解；最后通过检索增强生成（RAG）技术，结合MITRE ATT&CK框架和CVE漏洞库等专业知识库，对内容进行深度分析和知识提取。

每周定时任务会自动运行，收集最新的威胁情报文章，需要配置API_key,cookie等，会自动存档博客，文章等，过滤无用的网页。

## 重点注意，运行前检查

运行前先检查Data_Excation/db.env中的配置是否完善， Search api KEY是否可用？

Data_Excation/config.json 中的微信公众号的 Cookie是否更新？

## 系统架构

### 核心技术栈

- **Python 3.10** + 虚拟环境隔离
- **Docker + ArchiveBox** - 网页存档和内容提取
- **MySQL数据库** - 数据持久化存储
- **RAG系统** - 基于向量检索的智能分析
- **Flask Web框架** - 前端展示和交互
- **Sentence Transformers** - 专业嵌入模型（ATTACK-BERT）

### 系统组件

1. **数据收集层**: 自动爬虫 + 手动收集 + Search API
2. **存储层**: ArchiveBox存档 + MySQL数据库
3. **分析层**: RAG智能分析 + 向量检索
4. **展示层**: Web界面 + 配置管理系统

## 环境配置

### Linux环境配置（使用虚拟环境更好）

```bash
# 1. 安装Python虚拟环境
sudo apt install python3-venv python3-full

# 2. 创建并激活虚拟环境
python3 -m venv ~/archivebox_venv
source ~/archivebox_venv/bin/activate

# 3. 安装核心依赖
pip install --upgrade archivebox yt-dlp playwright -i https://pypi.tuna.tsinghua.edu.cn/simple

# 4. 设置项目环境变量（必须在Data_Excation路径下）
export PYTHONPATH="/home/<USER>/docker_archivebox/Data_Excation:$PYTHONPATH"

# 5. pip安装整个项目所需的包
pip install -r Data_Excation/requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple


```

### ArchiveBox配置（参考官方文档 https://github.com/ArchiveBox/ArchiveBox）

Linux 参考 `Archivebox安装.txt` 文件内容

```bash
# Docker方式部署(可以在Windows中使用)
docker compose run --rm archivebox init --setup
docker compose up -d

# 查看容器状态
docker compose ps

# 添加URL到存档
docker cp ./data/CTI_urls.txt archivebox-dev-archivebox-1:/tmp/
docker compose run archivebox add --parser=url_list --depth=0 --overwrite --timeout=120 /tmp/CTI_urls.txt
```

## 核心功能模块

### 1. 数据收集系统

#### 手动收集

- **文件**: `国内外威胁情报博客收集.xlsx`
- **功能**: 高质量威胁情报文章URL收集
- **数据源**:

  - 国内: 奇安信、360、绿盟、SecRSS等公众号和博客
  - 国外: Microsoft、Trellix、Kaspersky、Unit42等厂商博客
- **批量上传数据库中**：在系统中的（/config_management）界面使用导入，仅支持CSV格式
- **必需字段**: name（网站名称）、domain（域名）、url（URL地址）
- **可选字段**: description（描述）

#### 自动爬取

- **文件**: `pa_week_ar_test.py`
- **功能**: 每周定时自动爬取威胁情报文章
- **特点**:

  - 基于配置的域名和URL列表（在前端/config_management中可以配置），在 `simple_config_loader.py` 中进行读取数据库中的域名进行使用
  - 自动去重和状态管理
  - 按时间筛选（前一周文章）
  - 支持列表页和详情页爬取
- **界面**: Data_Excation/Page_view/frontend/config_management.html 进行配置并爬取，Search API以及微信公众号的Cookie需要手动在后端进行配置（config.json，公众号）、（.env中Search API key）， 爬取
- Data_Excation/Page_view/frontend/weekly_intel.html   展示每周的威胁情报
- config.json，Cookie 参考 https://blog.csdn.net/Mr_zhubao/article/details/142362563 文章
- .env中Search API key 使用 auto_search_api_register.py 获取

#### Search API集成

- **文件**: `auto_search_api_register.py`
- **功能**: 半自动注册Search API获取搜索结果，用到临时邮箱 https://www.linshiguge.com/
- **语法**: `site:域名 ("APT" OR "malware" OR "threat intelligence")`

### 2. 内容分析系统

#### ArchiveBox集成

- **文件**: `Archivebox_cti.py`
- **功能**:
  - 批量网页存档
  - HTML/PDF内容提取
  - 快照管理和状态跟踪

#### RAG智能分析

- **目录**: `RAG/`
- **核心文件**:
  - `rag_inference.py` - RAG推理引擎
  - `data_process.py` - 数据预处理
  - `utils.py` - 工具函数和HTTP管理
- **特点**:
  - 使用ATTACK-BERT专业网络安全模型
  - BM25 + 语义向量混合检索
  - 智能威胁分析和知识提取

#### 文本处理

- **文件**: `data_cope.py`
- **功能**:
  - 网页正文提取
  - 日期自动识别
  - 内容清洗和格式化

### 3. 知识库管理

#### 向量知识库

- **目录**: `data/test/`
- **模型**: `sentence-transformer-models/ATTACK-BERT/`
- **内容**:
  - MITRE ATT&CK框架
  - CVE漏洞数据库
  - 威胁情报专业知识

#### 知识库构建

- **目录**: `知识库/`
- **文件**:
  - `cve` - CVE数据
  - `mitregroup` - MITRE组织数据

### 4. Web展示系统

#### 主要功能

- **文件**: `Page_view/backend/app.py`
- **功能**:
  - 威胁情报数据展示
  - 分析结果查看
  - 文件上传和URL分析
  - 任务管理和爬取网站配置
  - 网站配置的Web界面管理
  - 动态配置加载
  - 数据库自动初始化

## 数据库设计

### 核心数据表

1. **crawled_data** - 爬取的原始数据
2. **threat_intel_sites** - 网站配置和检索语法生成
3. **rag_analysis** - RAG分析结果
4. **analysis_tasks** - 批量分析任务管理

## 系统启动

### 主要服务启动

```bash
# 进入虚拟环境source ~/archivebox_venv/bin/activate
# 进入项目目录
# 设置项目环境变量（必须在Data_Excation路径下）
export PYTHONPATH="/home/<USER>/docker_archivebox/Data_Excation:$PYTHONPATH"

# 1. 启动威胁情报分析Web系统
python ./Page_view/backend/app.py

# 2. 启动每周自动爬取脚本（可选，最好在前端网站配置界面可以进行启停 每周爬取的启动，启动一次后，后续会每隔7天进行爬取）
# 但要注意 KEY的补充,
python pa_week_ar_test.py

# 3. 生成检索语法配置（可单独，或者python pa_week_ar_test.py 会自动调用）
python threat_intel_config_simplified.py

# 4. 查看运行状态
pgrep -f "Page_view/backend/app.py"

# 5. 终止程序
pkill -f "Page_view/backend/app.py"

```

### 后台运行

```bash
# Web系统后台运行
nohup python -u Page_view/backend/app.py > App_web.log 2>&1 &

# 爬取脚本后台运行，测试或单独需要时进行
nohup python -u pa_week_ar_test.py > crawl_week.log 2>&1 &

# 查看运行状态
pgrep -f "Page_view/backend/app.py"

# 终止程序
pkill -f "Page_view/backend/app.py"
```

## 文件结构说明

### 核心脚本

- `mian_data_pa.py` - 语法检索爬虫（需要SearchAPI KEY）
- `data_cope.py` - 网页内容提取和处理
- `AI_copy.py` - 数据库数据的LLM+RAG分析
- `pa_week_ar_test.py` - 每周定时更新脚本
- `Archivebox_cti.py` - ArchiveBox集成模块

### 系统模块

- `Page_view/` - Web展示界面
- `RAG/` - 检索增强生成系统
- `SQL_cope/` - 数据库操作模块
- `configs/` - 系统配置文件
- `知识库/` - 威胁情报知识库

### 数据目录

- `data/test/` - 向量知识库存储
- `spider/` - 微信公众号爬取文章网址
- `logs/` - 系统运行日志
- `temp/` - 临时文件存储
- `uploads/` - 用户上传文件

### 配置文件

- `db.env` - 数据库连接配置
- `configs/rag_inference.yml` - RAG推理配置
- `configs/chunking.yml` - 文本分块配置
- `threat_intel_config_simplified.py` - 检索语法配置
- `Data_Excation/config.json` - 微信公众号爬取的cookie配置

## 运行日志

### 主要日志文件

- `App_web.log` - Web系统运行日志
- `crawl_week.log` - 每周爬取脚本日志
  主要以上两个日志

### 查看所有日志

tail -f crawl_week.log

#### 只看主线程日志

tail -f crawl_week.log | grep "\[主线程\]"   -----并行的

#### 只看分析线程日志

tail -f crawl_week.log | grep "\[分析线程\]"    -----分析

#### 只看搜索API日志

tail -f crawl_week.log | grep "\[搜索API\]"     ----语法检索

#### 看详细分析过程（无标识的）

tail -f crawl_week.log | grep -v "\[.*线程\]"

- `archive_box_analyzer.log` - ArchiveBox分析日志

### 进度文件

- `process_progress_*.txt` - 处理进度记录
- `logs/processed_*_pages_*.txt` - 已处理页面记录

## 已实现功能

1. **威胁情报URL自动收集和存档**
2. **ArchiveBox批量内容提取**
3. **RAG智能分析系统**
4. **自动去重和状态管理**
5. **Search API半自动注册工具**
6. **Web界面展示和交互**
7. **配置管理系统**
8. **任务调度和监控**

## 未完成的功能

Data_Excation/MCP_cope 使用MCP功能让LLM自动去全网搜索威胁情报信息，筛选，进行分析保存等功能；需要支持MCP功能的LLM客户端或者网页。
