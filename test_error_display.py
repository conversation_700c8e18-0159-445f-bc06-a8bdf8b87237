#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试错误信息显示功能
"""

import sys
import os
import requests
import json

def test_error_display():
    """测试错误信息显示"""
    print("=== 测试错误信息显示功能 ===")
    
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    try:
        # 1. 获取最近的任务列表
        print("\n1. 获取最近的任务列表...")
        response = session.get(f"{base_url}/api/analysis/tasks?limit=10")
        if response.status_code == 200:
            tasks_data = response.json()
            print(f"获取到 {len(tasks_data.get('tasks', []))} 个任务")
            
            # 查找有错误信息的任务
            error_tasks = []
            for task in tasks_data.get('tasks', []):
                if task.get('task_status') == 'failed' and task.get('error_message'):
                    error_tasks.append(task)
                    print(f"  任务ID: {task['id']}, 状态: {task['task_status']}, 错误: {task['error_message']}")
            
            if not error_tasks:
                print("  没有找到有错误信息的失败任务")
            else:
                print(f"  找到 {len(error_tasks)} 个有错误信息的任务")
                
                # 测试获取任务详情
                for task in error_tasks[:3]:  # 只测试前3个
                    print(f"\n2. 获取任务 {task['id']} 的详情...")
                    detail_response = session.get(f"{base_url}/api/analysis/tasks/{task['id']}")
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        if detail_data.get('success'):
                            task_detail = detail_data.get('data', {})
                            print(f"  任务类型: {task_detail.get('task_type')}")
                            print(f"  任务状态: {task_detail.get('task_status')}")
                            print(f"  错误信息: {task_detail.get('error_message', '无')}")
                            print(f"  开始时间: {task_detail.get('start_time')}")
                            print(f"  结束时间: {task_detail.get('end_time', '无')}")
                        else:
                            print(f"  获取详情失败: {detail_data.get('error')}")
                    else:
                        print(f"  获取详情失败: HTTP {detail_response.status_code}")
        else:
            print(f"获取任务列表失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_database_error_messages():
    """直接查询数据库中的错误信息"""
    print("\n=== 直接查询数据库中的错误信息 ===")
    
    try:
        import mysql.connector
        
        # 数据库配置
        DB_CONFIG = {
            'host': 'localhost',
            'user': 'root',
            'password': 'Qjq123456',
            'database': 'cti_database',
            'charset': 'utf8mb4'
        }
        
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        # 查询有错误信息的任务
        cursor.execute("""
            SELECT id, task_type, task_status, error_message, start_time, end_time
            FROM analysis_tasks 
            WHERE error_message IS NOT NULL AND error_message != ''
            ORDER BY start_time DESC 
            LIMIT 10
        """)
        
        error_tasks = cursor.fetchall()
        
        if error_tasks:
            print(f"数据库中找到 {len(error_tasks)} 个有错误信息的任务:")
            for task in error_tasks:
                print(f"  ID: {task['id']}")
                print(f"  类型: {task['task_type']}")
                print(f"  状态: {task['task_status']}")
                print(f"  错误: {task['error_message']}")
                print(f"  时间: {task['start_time']} - {task['end_time']}")
                print("  " + "-" * 50)
        else:
            print("数据库中没有找到有错误信息的任务")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"数据库查询失败: {e}")

if __name__ == "__main__":
    print("错误信息显示测试脚本")
    print("=" * 50)
    
    # 首先测试数据库查询
    test_database_error_messages()
    
    # 询问是否要测试API接口
    response = input("\n是否要测试API接口？(需要后端服务运行) (y/n): ")
    if response.lower() == 'y':
        test_error_display()
    else:
        print("跳过API测试")
