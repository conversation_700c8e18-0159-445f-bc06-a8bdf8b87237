# record_30

This blog post was originally sent to our clients on 19 June, 2023. Since the threat is still very active in July 2023 and continues to distribute a variety of malware families, Sekoia.io TDR analysts decided to publish a blog post.

# Introduction

During our daily threat hunting routine, we identified an undocumented .NET loader aimed at downloading, decrypting and executing next-stage payloads . In early June 2023, this new loader was actively distributed by multiple threat actors using malicious phishing emails, YouTube videos, and web pages impersonating legitimate websites.
We named this new malware “CustomerLoader” because of the presence of the string “customer” in its Command and Control (C2) communications and loading capabilities.
The malwrhunterteam and g0njxa researchers also observed campaigns distributing CustomerLoader in early June 2023.
Sekoia.io analysts’ investigation led us to discover that all payloads downloaded by CustomerLoader are dotRunpeX samples that deliver a variety of malware families , including infostealers, Remote Access Trojans (RAT) and commodity ransomware. dotRunpeX is an .NET injector implementing several anti-analysis techniques, first publicly documented by Checkpoint in March 2023.
We assess that CustomerLoader is almost certainly associated with a Loader-as-a-Service , which remains unknown at the time of writing. It is possible that CustomerLoader is a new stage added before the execution of the dotRunpeX injector by its developer.
This blog post aims at presenting a technical analysis of CustomerLoader focusing on the decryption of the next-stage payloads, an overview of more than 30 known and distributed malware families, and details on three infection chains observed distributing the loader .

# Technical analysis

Here is an overview of the infection chains’ stages observed distributing multiple commodity malware via CustomerLoader:

# Loader capabilities

Samples of CustomerLoader used several techniques to obfuscate their code or to hide their execution by masquerading as a legitimate application. This usage makes the analysis of CustomerLoader slower and longer, this is likely a result of the democratisation of tools to obfuscate .NET code. As indicated in the list hosted on NotPrab/.NET-Obfuscator GitHub repository, many tools are available without requiring an advanced knowledge on code obfuscation to use them.

# Data encryption

CustomerLoader obfuscates its strings using AES in Electronic CodeBook (ECB) mode, the decryption key is stored in cleartext in the PE. The obfuscated strings are:
As shown in Figure 3, the loader decodes base64-encoded strings and calls the AES decryption function. Here is a straightforward cyberchef recipe to decrypt strings for d40af29bbc4ff1ea1827871711e5bfa3470d59723dd8ea29d2b19f5239e509e9 sample. The same recipe can be used to decrypt the downloaded next-stage payload.

# Impair Defenses

To avoid possible detection of the malware, CustomerLoader patches the AmsiScanBuffer function from amsi.dll . This method aims at scanning buffer content for potential malware. The patch returns the AMSI_RESULT_CLEAN constant for the AmsiScanBuffer method when a malicious payload is written in memory to mark the buffer as clean and bypass the antivirus. When the patch is successfully applied, this value indicates to the caller that the buffer is clean and can be safely executed.
The article Memory Patching AMSI Bypass of RastaMouse details how this patch work to execute malicious payload in memory.

# Next-stage execution

N.B.: The extracted data and the obfuscated strings in the PE are encrypted with the same routine (base64, AES encryption).
To execute the next-stage in memory, CustomerLoader uses reflecting code loading; this technique consists of injecting then executing the downloaded payload in the same process. Here, the method of reflecting code is shuffled to load the .NET function from their string value using the NewLateBinding.LateGet function.

# C2 infrastructure

CustomerLoader samples download their next-stage encrypted payload from their C2 server. Each payload is associated with a customer identifier and is hosted at hxxp://$C2/customer/$ID .

# Rounds

We observed that the CustomerLoader’s operator re-indexed the payload identifiers twice, on 19 June 2023, and on 25 June 2023. This means that each time, all encrypted payloads were removed from the C2 server, and the identifiers were reassigned from 0.

# Change of C2

Between 31 May and 20 June 2023, CustomerLoader samples communicated directly with the IP address with the C2 server 5.42.94[.]169 in HTTP.
On 20 June 2023, CustomerLoader switched its C2 server and communications to the domain name kyliansuperm92139124[.]sbs and HTTPS. The domain kyliansuperm92139124[.]sbs is protected by Cloudflare, which prevents payloads from being scanned and collected by security researchers.
However, this domain is a proxy for C2 communications and the backend server is always 5.42.94[.]169 . Sekoia.io analysts assess that this change of C2 server is likely intended to avoid network detections, and possibly to avoid security researchers’ analysis.

# Loader update

The code was updated at round 3, the developer added some obfuscation to hide the strings such as C2 URL and AMSI constants. Furthermore it attempted to hide code execution using IL (Intermediate Language) code in asynchronous tasks definition.
The malware implements a method that inherits the method MoveNext from IAsyncStateMachine , which executes CustomerLoader malicious code. The loader calls these asynchronous methods by awaiting tasks created for this purpose.

# Malware families distribution

Once the CustomerLoader’s decryption method of the next-stage payload is achieved, we collected all the payloads distributed by the malware from the C2 server.
By extracting the first bytes of the collected files, we identified clusters of payloads encrypted with the same AES key. Pivoting on CustomerLoader samples and downloading the encrypted payloads, we were able to retrieve the AES key for each cluster, allowing us to decrypt almost every next-stage payload. Here is a table listing the clusters of payloads and associated AES keys.
As a reminder, the download URL for the CustomerLoader next-stage payload is: hxxp://5.42.94[.]169/customer/$ID .
Based on static malware detection, Sekoia.io analysts noticed that every decrypted next-stage payload matches our internal YARA rule for the dotRunpeX injector.
To classify the distributed payload by CustomerLoader and injected by dotRunpeX, we executed them in a sandbox environment. We identified more than 40 known malware families , as shown in the following figure.
We also identified botnets associated with some malware families. Here are the number of unique botnets for the following malware families distributed by CustomerLoader:
Although one threat actor/group can operate several botnets, malware families and use several servers, domain names – the number of deployed malware, the extent of related infrastructure as well as the diversity of alleged objectives lead Sekoia.io analysts to assess it is highly unlikely that all these final payloads are leveraged by a unique threat actor/group.
This in-depth investigation allows us to assess with high confidence that CustomerLoader is a new malware associated with a Loader-as-a-Service – which are very common in the cybercrime ecosystem, to offer cybercriminals a solution to ensure that their payloads are less likely to be detected. The likely high number of customers for this service is probably due to its stealthy code.

# Infection chains

Sekoia.io observed three infection chains delivering CustomerLoader in the wild, which we briefly detail in the following sections. These attackers leveraged CustomerLoader for their distribution campaign and are almost certainly customers of the Loader-as-a-Service.

# Phishing emails (customer 735)

Early June 2023, we observed a phishing campaign delivering CustomerLoader. The email content purports to be a follow-up email to trick victims into thinking they had a previous exchange with the sender. The body of the mail contains an image mimicking a PDF file, which, in fact, is a hyperlink to hxxp://smartmaster.com[.]my/48E003A01/48E003A01.7z . This link redirects to a compromised website hosting a ZIP file. The archive contains an executable which is the loader.

# YouTube compromised channels (customer 770)

Known on the Russian-speaking cybercrime forums as “911”, this infection chain that consists in delivering malware using stolen YouTube accounts to distribute a download link was leveraged to deliver CustomerLoader.
Here is an analysis from the Hatching Triage sandbox of the CustomerLoader sample for this infection chain: https://tria.ge/230608-y3pgnsag5s .

# Page impersonating Slack website (customer 798)
# CustomerLoader’s infection chain

A webpage impersonating the website of the video conferencing software Slack distributed CustomerLoader as a fake installer. The technique used to spread this fake web site remains unknown at the time of writing, it could be SEO-poisoning, phishing emails or redirections from legitimate forums.
Here is an analysis from the Hatching Triage sandbox of the CustomerLoader sample for this infection chain: https://tria.ge/230611-xmzr2aad3z .

# Unveiling the infrastructure associated with “customer 798”

In this section, we focus on the C2 infrastructure associated with the third CustomerLoader’s infection chain (customer 798). As described above, this attacker leveraged CustomerLoader for its distribution campaign and is almost certainly a customer of the Loader-as-a-Service.
While we did not dig deeper into the analysis of the crypter downloaded by CustomerLoader in the above infection chain, we observed additional requests to the following domains:
All domains are likely to be malicious and related to an infrastructure of a single attacker . Common characteristics of this infrastructure are:
Based on previously discussed technical artefacts, we were able to unveil an infrastructure of over 50 domains used for:

# Conclusion

The new malware CustomerLoader does not implement advanced techniques, but when used with the dotRunpeX injector, it reduces the detection rate of the final payload , allowing attackers to improve their compromise rate.
Sekoia.io analysts’ investigation led us to discover only one CustomerLoader C2 server. However, the number and the variety of malware families loaded by CustomerLoader in the first half of June show that the threat is widespread . By pivoting on the infrastructure of one of the attackers using CustomerLoader, we identified over 50 domains used to distribute commodity malware widely. Sekoia.io analysts assess that CustomerLoader is highly likely associated with a Loader-as-a-Service and used by multiple threat actors , including some previously observed running long-term campaigns with large and resilient infrastructure.
To provide our customers with actionable intelligence, we will continue to monitor the evolution of CustomerLoader and proactively search for new emerging malware and adversary infrastructure.

# IoCs & Technical Details
# IoCs

Indicators of Compromise shared in this report are only associated with the above described infection chains. More CustomerLoader’s and dotRunpeX’s IoCs are available in the Sekoia.io Intelligence Center.

# C2 servers
# Infection chain 1
# Infection chain 2
# Infection chain 3
# MITRE ATT&CK TTPs
# Chat with our team!

Would you like to know more about our solutions? Do you want to discover our XDR and CTI products? Do you have a cybersecurity project in your organization? Make an appointment and meet us!
Thank you for reading this blogpost. We welcome any reaction, feedback or critics about this analysis. Please contact us on tdr[at]sekoia.io Feel free to read other TDR analysis here :