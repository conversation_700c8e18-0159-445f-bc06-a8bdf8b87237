#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SearchAPI.io 自动注册工具
使用临时邮箱自动注册 https://www.searchapi.io/ 获取API密钥
支持临时邮箱：https://www.linshiguge.com/ 等

更新说明：
- 使用Selenium 4.33+的现代方式，自动管理WebDriver
- 移除了过时的undetected_chromedriver依赖
- 支持Selenium Manager自动下载ChromeDriver
"""

import requests
import time
import random
import string
import re
import json
import logging
import traceback
import re
import json
import time
import random
import string
import traceback
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 检查Selenium版本
def check_selenium_version():
    """检查Selenium版本并提供建议"""
    try:
        import selenium
        version = selenium.__version__
        print(f"当前Selenium版本: {version}")
        
        # 检查版本
        major_version = int(version.split('.')[0])
        minor_version = int(version.split('.')[1])
        
        if major_version < 4:
            print("⚠️ 警告: Selenium版本过低，建议升级到4.33+")
            print("运行: pip install --upgrade selenium")
            return False
        elif major_version == 4 and minor_version < 6:
            print("⚠️ 警告: Selenium版本较低，建议升级到4.33+以获得最佳体验")
            print("运行: pip install --upgrade selenium")
        else:
            print("✓ Selenium版本符合要求")
        
        return True
    except Exception as e:
        print(f"检查Selenium版本失败: {e}")
        return False

# 可选的webdriver-manager支持
def try_webdriver_manager():
    """尝试使用webdriver-manager作为备用方案"""
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        print("使用webdriver-manager管理ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        return service
    except ImportError:
        print("webdriver-manager未安装，如需使用请运行: pip install webdriver-manager")
        return None
    except Exception as e:
        print(f"webdriver-manager失败: {e}")
        return None


class TempEmailProvider:
    """临时邮箱提供商 - 专注于 linshiguge.com 等服务"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
    
    def get_temp_email_linshiguge(self):
        """使用linshiguge.com获取临时邮箱"""
        try:
            # 访问临时邮箱首页
            response = self.session.get('https://www.linshiguge.com/')
            if response.status_code == 200:
                # 解析邮箱地址 - 根据网站实际结构调整
                email_patterns = [
                    r'<input[^>]*id="email"[^>]*value="([^"]+)"',
                    r'<span[^>]*class="[^"]*email[^"]*"[^>]*>([^<]+@[^<]+)</span>',
                    r'邮箱地址[：:]\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                    r'([a-zA-Z0-9._%+-]+@tmpmail\.[a-zA-Z]{2,})',
                ]
                
                for pattern in email_patterns:
                    email_match = re.search(pattern, response.text, re.IGNORECASE)
                    if email_match:
                        email = email_match.group(1).strip()
                        if '@' in email and '.' in email:
                            print(f"✓ 获取到临时邮箱: {email}")
                            return {
                                'email': email,
                                'provider': 'linshiguge',
                                'session': self.session
                            }
                
                print("未能从页面解析到邮箱地址，尝试API接口...")
                # 如果页面解析失败，尝试可能的API接口
                api_response = self.session.get('https://www.linshiguge.com/api/getmail')
                if api_response.status_code == 200:
                    data = api_response.json()
                    email = data.get('email') or data.get('mail')
                    if email:
                        print(f"✓ 通过API获取到临时邮箱: {email}")
                        return {
                            'email': email,
                            'provider': 'linshiguge',
                            'session': self.session
                        }
        except Exception as e:
            print(f"linshiguge获取失败: {str(e)}")
        return None

    # 使用API 文档
    # 基本网址： https://22.do/api/v2 # 开放API： https://22.do/api
    
    
    def get_available_temp_email(self):
        """尝试获取可用的临时邮箱 - 专注于linshiguge，因为其他被SearchAPI.io拒绝"""
        print("=" * 50)
        print("⚠️ 重要提示: SearchAPI.io拒绝一次性邮箱域名")
        print("- 1secmail.com: 被拒绝")
        print("- guerrillamail.com: 被拒绝") 
        print("- linshiguge.com: 可用但需要手动获取")
        print("=" * 50)
        
        # 首先尝试自动获取linshiguge
        print("\n1. 尝试自动获取linshiguge邮箱...")
        try:
            result = self.get_temp_email_linshiguge()
            if result:
                return result
        except Exception as e:
            print(f"自动获取linshiguge失败: {str(e)}")
        
        # 如果自动获取失败，提供手动输入选项
        print("\n2. 自动获取失败，切换到手动模式...")
        return self.get_temp_email_manual_linshiguge()
    
    def get_temp_email_manual_linshiguge(self):
        """手动从linshiguge.com获取邮箱"""
        try:
            print("\n🔄 手动获取linshiguge邮箱")
            print("=" * 40)
            print("请按以下步骤操作:")
            print("1. 打开浏览器访问: https://www.linshiguge.com/")
            print("2. 复制显示的临时邮箱地址")
            print("3. 粘贴到下面的输入框中")
            print("=" * 40)
            
            while True:
                try:
                    raw_email = input("\n请输入从linshiguge.com获取的邮箱地址: ").strip()
                    
                    if not raw_email:
                        print("邮箱地址不能为空，请重新输入")
                        continue
                    
                    # 清理邮箱地址 - 移除可能的重复内容  
                    email = raw_email
                    
                    # 手动清理邮箱地址
                    if email.count('@') > 1:
                        # 尝试提取第一个有效的邮箱地址
                        parts = email.split('@')
                        if len(parts) >= 3:
                            # 寻找第一个包含有效域名的部分
                            for i in range(1, len(parts)):
                                potential_domain = parts[i]
                                # 检查是否包含点号且不是用户名的一部分
                                if '.' in potential_domain and not potential_domain.startswith(parts[0]):
                                    email = parts[0] + '@' + potential_domain
                                    print(f"修复重复的邮箱地址: {raw_email} -> {email}")
                                    break
                    
                    # 验证清理后的邮箱格式
                    if '@' not in email or '.' not in email:
                        print("邮箱格式不正确，请重新输入")
                        continue
                    
                    # 确保邮箱地址只有一个@符号
                    if email.count('@') != 1:
                        print("邮箱地址格式异常，请检查并重新输入")
                        continue
                    
                    # 提取域名进行验证
                    try:
                        domain = email.split('@')[1].lower()
                        
                        # 检查域名是否合理（不包含用户名部分）
                        if '+' in domain or len(domain.split('.')) < 2:
                            print("域名格式异常，请检查邮箱地址")
                            continue
                            
                    except IndexError:
                        print("无法解析邮箱域名，请检查格式")
                        continue
                    
                    # 验证是否为支持的域名（与clean_email_address保持一致）
                    valid_domains = [
                        'linshiguge.com', 'tmpmail.org', 'guerrillamail.com', 
                        '1secmail.com', '1secmail.org', '1secmail.net',
                        'tmpmail.net', 'tmpmail.us', 'tempmail.plus',
                        'temp-mail.org', 'emailfake.com',
                        'gmail.com', 'googlemail.com'  # linshiguge提供的Gmail系列
                    ]
                    
                    if any(valid_domain in domain for valid_domain in valid_domains):
                        print(f"✓ 邮箱地址验证通过: {email}")
                        return {
                            'email': email,
                            'provider': 'linshiguge_manual',
                            'session': self.session
                        }
                    else:
                        print(f"⚠️ 警告: {domain} 可能不被SearchAPI.io接受")
                        print(f"支持的域名: {', '.join(valid_domains)}")
                        use_anyway = input("是否仍要使用此邮箱? (y/N): ").strip().lower()
                        if use_anyway == 'y':
                            return {
                                'email': email,
                                'provider': 'manual',
                                'session': self.session
                            }
                        else:
                            print("请重新输入邮箱地址")
                            continue
                            
                except KeyboardInterrupt:
                    print("\n用户取消邮箱输入")
                    return None
                except Exception as e:
                    print(f"输入处理出错: {e}")
                    print("请重新输入邮箱地址")
                    continue
                    
        except Exception as e:
            print(f"手动获取邮箱失败: {str(e)}")
            return None
    
    def check_emails_linshiguge(self, email_info):
        """检查linshiguge邮箱中的邮件"""
        try:
            # 尝试多种可能的API接口
            email = email_info['email']
            username = email.split('@')[0] if '@' in email else email
            
            # 尝试不同的API端点
            api_endpoints = [
                f'https://www.linshiguge.com/api/getmails?email={email}',
                f'https://www.linshiguge.com/getmails?mail={email}',
                f'https://www.linshiguge.com/api/checkmails?user={username}',
            ]
            
            for endpoint in api_endpoints:
                try:
                    response = self.session.get(endpoint)
                    if response.status_code == 200:
                        data = response.json()
                        if isinstance(data, list):
                            return data
                        elif isinstance(data, dict) and 'mails' in data:
                            return data['mails']
                        elif isinstance(data, dict) and 'messages' in data:
                            return data['messages']
                except:
                    continue
            
            return []
        except Exception as e:
            print(f"检查linshiguge邮件失败: {str(e)}")
        return []
    
    def check_emails_1secmail(self, email_info):
        """检查1secmail邮箱中的邮件"""
        try:
            username = email_info['username']
            domain = email_info['domain']
            
            response = self.session.get(f'https://www.1secmail.com/api/v1/?action=getMessages&login={username}&domain={domain}')
            if response.status_code == 200:
                messages = response.json()
                return messages
        except Exception as e:
            print(f"检查1secmail邮件失败: {str(e)}")
        return []
    
    def check_emails_guerrillamail(self, email_info):
        """检查guerrillamail邮箱中的邮件"""
        try:
            sid_token = email_info.get('sid_token', '')
            response = self.session.get(f'https://www.guerrillamail.com/ajax.php?f=get_email_list&offset=0&sid_token={sid_token}')
            if response.status_code == 200:
                data = response.json()
                return data.get('list', [])
        except Exception as e:
            print(f"检查guerrillamail邮件失败: {str(e)}")
        return []
    
    def get_email_content_1secmail(self, email_info, message_id):
        """获取1secmail邮件内容"""
        try:
            username = email_info['username']
            domain = email_info['domain']
            
            response = self.session.get(f'https://www.1secmail.com/api/v1/?action=readMessage&login={username}&domain={domain}&id={message_id}')
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            print(f"获取1secmail邮件内容失败: {str(e)}")
        return None


class SearchAPIRegister:
    """SearchAPI.io 自动注册器"""
    
    def __init__(self):
        self.email_provider = TempEmailProvider()
        self.driver = None
        self.registered_apis = []
    
    def clean_email_address(self, email):
        """清理邮箱地址，处理重复粘贴等问题"""
        if not email:
            return None
            
        # 清理前后空格
        email = email.strip()
        
        # 处理重复粘贴的情况
        if email.count('@') > 1:
            print(f"调试 - 检测到重复邮箱: {email}")
            # 找到第一个@符号的位置
            first_at = email.find('@')
            if first_at != -1:
                # 查找域名部分
                domain_part = email[first_at + 1:]
                # 查找第二个@符号（如果存在）
                second_at = domain_part.find('@')
                if second_at != -1:
                    # 截取到第二个@符号之前
                    email = email[:first_at + 1 + second_at]
                    print(f"调试 - 修复后的邮箱: {email}")
        
        # 验证邮箱格式
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(email_pattern, email):
            # 检查是否是支持的临时邮箱域名（包括linshiguge提供的gmail系列）
            supported_domains = [
                'linshiguge.com', 'tmpmail.org', 'guerrillamail.com', 
                '1secmail.com', '1secmail.org', '1secmail.net',
                'tmpmail.net', 'tmpmail.us', 'tempmail.plus',
                'temp-mail.org', 'emailfake.com',
                # linshiguge.com 提供的 Gmail 系列域名
                'gmail.com', 'googlemail.com'
            ]
            
            domain = email.split('@')[1].lower()
            if any(domain.endswith(supported) for supported in supported_domains):
                print(f"调试 - 邮箱域名验证通过: {domain}")
                return email
            else:
                print(f"调试 - 不支持的邮箱域名: {domain}")
                print(f"调试 - 支持的域名列表: {', '.join(supported_domains)}")
                return None
        else:
            print(f"调试 - 邮箱格式无效: {email}")
            return None
    
    def setup_chrome_driver(self, use_proxy=True, proxy_host="127.0.0.1", proxy_port="7890"):
        """设置Chrome驱动 - 使用Selenium 4.33+的现代方式"""
        # 首先检查Selenium版本
        if not check_selenium_version():
            print("请升级Selenium版本后重试")
            return False
            
        try:
            # 创建Chrome选项
            options = Options()
            
            # 基本设置
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            # 移除禁用图片的设置，因为验证码需要显示图片
            # options.add_argument('--disable-images')  # 注释掉，验证码需要图片
            options.add_argument('--disable-web-security')
            options.add_argument('--allow-running-insecure-content')
            
            # 代理设置
            if use_proxy:
                proxy_server = f"{proxy_host}:{proxy_port}"
                options.add_argument(f'--proxy-server=http://{proxy_server}')
                print(f"✓ 配置代理: {proxy_server}")
            
            # 反检测设置
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 配置选项 - 允许图片显示（验证码需要）
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,  # 禁用通知
                    # "images": 2,  # 注释掉，允许图片显示
                },
                # "profile.managed_default_content_settings": {
                #     "images": 2  # 注释掉，允许图片显示
                # }
            }
            options.add_experimental_option("prefs", prefs)
            
            # 随机化User-Agent
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            options.add_argument(f'--user-agent={random.choice(user_agents)}')
            
            # 可选：无头模式（调试时可以注释掉）
            # options.add_argument('--headless')
            
            print("正在启动Chrome驱动...")
            
            # 方法1: 使用Selenium 4.33+的自动WebDriver管理（推荐）
            try:
                self.driver = webdriver.Chrome(options=options)
                print("✓ Chrome驱动启动成功 (使用Selenium Manager)")
                
                # 设置反检测脚本
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
                self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
                
                return True
                
            except Exception as e1:
                print(f"Selenium Manager失败: {e1}")
                print("尝试使用webdriver-manager...")
                
                # 方法2: 使用webdriver-manager作为备用
                service = try_webdriver_manager()
                if service:
                    try:
                        self.driver = webdriver.Chrome(service=service, options=options)
                        print("✓ Chrome驱动启动成功 (使用webdriver-manager)")
                        
                        # 设置反检测脚本
                        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                        self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
                        self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
                        
                        return True
                    except Exception as e2:
                        print(f"webdriver-manager也失败: {e2}")
                
                # 方法3: 最小化配置
                print("尝试使用最小化配置...")
                try:
                    minimal_options = Options()
                    minimal_options.add_argument('--no-sandbox')
                    minimal_options.add_argument('--disable-dev-shm-usage')
                    
                    self.driver = webdriver.Chrome(options=minimal_options)
                    print("✓ 使用最小化配置启动Chrome驱动成功")
                    return True
                except Exception as e3:
                    print(f"最小化配置也失败: {e3}")
                    raise e3
            
        except Exception as e:
            print(f"Chrome驱动设置完全失败: {str(e)}")
            print("\n故障排除建议:")
            print("1. 确保已安装Chrome浏览器并更新到最新版本")
            print("2. 更新Selenium: pip install --upgrade selenium")
            print("3. 安装webdriver-manager: pip install webdriver-manager")
            print("4. 检查防火墙设置是否阻止了WebDriver下载")
            print("5. 如果在公司网络环境，可能需要配置代理")
            return False
    
    def debug_page_elements(self):
        """调试页面元素，显示页面信息"""
        try:
            print(f"\n=== 页面调试信息 ===")
            print(f"当前URL: {self.driver.current_url}")
            print(f"页面标题: {self.driver.title}")
            
            # 查找所有输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            print(f"\n找到 {len(inputs)} 个输入框:")
            for i, inp in enumerate(inputs[:10]):  # 只显示前10个
                try:
                    input_type = inp.get_attribute("type") or "text"
                    input_name = inp.get_attribute("name") or "N/A"
                    input_id = inp.get_attribute("id") or "N/A"
                    input_placeholder = inp.get_attribute("placeholder") or "N/A"
                    print(f"  {i+1}. Type: {input_type}, Name: {input_name}, ID: {input_id}, Placeholder: {input_placeholder}")
                except:
                    print(f"  {i+1}. [获取属性失败]")
            
            # 查找所有按钮
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            submit_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
            all_buttons = buttons + submit_inputs
            print(f"\n找到 {len(all_buttons)} 个按钮:")
            for i, btn in enumerate(all_buttons[:10]):  # 只显示前10个
                try:
                    btn_text = btn.text.strip() or btn.get_attribute("value") or "N/A"
                    btn_type = btn.get_attribute("type") or "N/A"
                    btn_class = btn.get_attribute("class") or "N/A"
                    print(f"  {i+1}. Text: {btn_text}, Type: {btn_type}, Class: {btn_class}")
                except:
                    print(f"  {i+1}. [获取属性失败]")
            
            # 查找所有选择框
            selects = self.driver.find_elements(By.TAG_NAME, "select")
            print(f"\n找到 {len(selects)} 个下拉选择框:")
            for i, sel in enumerate(selects[:5]):  # 只显示前5个
                try:
                    sel_name = sel.get_attribute("name") or "N/A"
                    sel_id = sel.get_attribute("id") or "N/A"
                    options = sel.find_elements(By.TAG_NAME, "option")
                    print(f"  {i+1}. Name: {sel_name}, ID: {sel_id}, Options: {len(options)}")
                except:
                    print(f"  {i+1}. [获取属性失败]")
            
            # 查找表单
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            print(f"\n找到 {len(forms)} 个表单:")
            for i, form in enumerate(forms):
                try:
                    form_action = form.get_attribute("action") or "N/A"
                    form_method = form.get_attribute("method") or "N/A"
                    print(f"  {i+1}. Action: {form_action}, Method: {form_method}")
                except:
                    print(f"  {i+1}. [获取属性失败]")
            
            print("=== 调试信息结束 ===\n")
            
        except Exception as e:
            print(f"页面调试失败: {e}")
    
    def handle_captcha_manually(self, timeout=120):
        """处理验证码 - 提示用户手动完成"""
        try:
            print("\n=== 验证码检测 ===")
            
            # 检测常见的验证码元素
            captcha_selectors = [
                "iframe[src*='recaptcha']",
                "iframe[src*='hcaptcha']", 
                "div[class*='captcha']",
                "div[id*='captcha']",
                "canvas",
                ".cf-turnstile",
                "#cf-turnstile",
                ".g-recaptcha",
                "[data-sitekey]"
            ]
            
            captcha_found = False
            captcha_type = ""
            for selector in captcha_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"✓ 检测到验证码元素: {selector}")
                    captcha_found = True
                    if 'recaptcha' in selector:
                        captcha_type = "reCAPTCHA"
                    elif 'hcaptcha' in selector:
                        captcha_type = "hCaptcha"
                    elif 'turnstile' in selector:
                        captcha_type = "Cloudflare Turnstile"
                    else:
                        captcha_type = "验证码"
                    break
            
            if not captcha_found:
                print("未检测到明显的验证码元素")
                return True
            
            print(f"\n🤖 检测到{captcha_type}！")
            print("=" * 50)
            print("🔥 重要提示:")
            print("1. 请确保你的代理(7890端口)已正确配置")
            print("2. 在浏览器中手动完成验证码")
            print("3. 验证完成后，请勿提交表单，只需完成验证码")
            print("4. 验证成功后按Enter键，程序将自动继续")
            print("=" * 50)
            print("\n请在浏览器中完成验证码验证，然后:")
            print("- 验证完成后，按 Enter 键继续")
            print("- 如果验证失败或想跳过，输入 'skip' 后按 Enter")
            print("- 如果需要重新加载页面，输入 'reload' 后按 Enter")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    user_input = input(f"验证码完成后按Enter继续 (或输入'skip'/'reload'): ").strip().lower()
                    if user_input == 'skip':
                        print("用户选择跳过验证码")
                        return False
                    elif user_input == 'reload':
                        print("重新加载页面...")
                        self.driver.refresh()
                        time.sleep(5)
                        print("页面已重新加载，请重新处理验证码")
                        continue
                    elif user_input == '':
                        print("用户确认验证码已完成，继续流程...")
                        
                        # 验证码完成后，可能需要重新输入某些字段
                        print("⚠️ 注意：验证码完成后某些字段可能需要重新输入")
                        time.sleep(2)
                        return True
                    else:
                        print("无效输入，请按Enter确认或输入skip/reload")
                        continue
                        
                except KeyboardInterrupt:
                    print("\n用户中断验证码处理")
                    return False
                except:
                    # 非交互环境，自动等待
                    print("非交互环境，等待验证码自动完成...")
                    time.sleep(10)
                    return True
            
            print(f"验证码处理超时 ({timeout}秒)")
            return False
            
        except Exception as e:
            print(f"验证码处理出错: {e}")
            return True  # 出错时假设没有验证码，继续流程
    
    def register_searchapi_io(self, email_info):
        """注册SearchAPI.io - 新流程：填写信息 → 点击注册 → 验证码 → 重新填写密码"""
        try:
            print("开始注册SearchAPI.io...")
            
            # 访问正确的注册页面
            self.driver.get('https://www.searchapi.io/users/sign_up')
            time.sleep(5)
            
            # 调试：输出页面信息
            print("页面加载完成，开始调试...")
            self.debug_page_elements()
            
            # 生成随机用户信息
            full_name = f"{random.choice(['John', 'Jane', 'Mike', 'Sarah', 'David', 'Emma'])} {random.choice(['Smith', 'Johnson', 'Brown', 'Davis', 'Wilson', 'Miller'])}"
            password = ''.join(random.choices(string.ascii_letters + string.digits + '!@#$%', k=12))
            
            print(f"\n步骤1: 填写基本注册信息...")
            
            # 填写姓名 (根据调试信息，字段名是user[name]，ID是user_name)
            try:
                name_input = WebDriverWait(self.driver, 15).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "input[name='user[name]'], #user_name"))
                )
                name_input.clear()
                name_input.send_keys(full_name)
                print(f"✓ 填写姓名: {full_name}")
            except Exception as e:
                print(f"填写姓名失败: {str(e)}")
                # 尝试备用方法
                try:
                    name_input = self.driver.find_element(By.ID, "user_name")
                    name_input.clear()
                    name_input.send_keys(full_name)
                    print(f"✓ 填写姓名 (备用方法): {full_name}")
                except:
                    print("无法填写姓名字段")
            
            # 填写邮箱
            try:
                email_input = self.driver.find_element(By.CSS_SELECTOR, "input[name='user[email]'], input[name='email'], #user_email, #email, input[type='email']")
                email_input.clear()
                email_input.send_keys(email_info['email'])
                print(f"✓ 填写邮箱: {email_info['email']}")
            except Exception as e:
                print(f"填写邮箱失败: {str(e)}")
                return None
            
            # 填写密码
            try:
                password_input = self.driver.find_element(By.CSS_SELECTOR, "input[name='user[password]'], input[name='password'], #user_password, #password, input[type='password']")
                password_input.clear()
                password_input.send_keys(password)
                print("✓ 填写密码")
            except Exception as e:
                print(f"填写密码失败: {str(e)}")
                return None
            
            # 选择时区 (GMT+08:00) Beijing
            try:
                timezone_select = self.driver.find_element(By.CSS_SELECTOR, "select[name='user[time_zone]'], select[name='time_zone'], #user_time_zone, #time_zone")
                from selenium.webdriver.support.ui import Select
                select = Select(timezone_select)
                
                # 尝试选择北京时区
                timezone_options = [
                    "(GMT+08:00) Beijing",
                    "Beijing",
                    "Asia/Shanghai", 
                    "GMT+8",
                    "+08:00"
                ]
                
                timezone_selected = False
                for option_text in timezone_options:
                    try:
                        select.select_by_visible_text(option_text)
                        print(f"✓ 选择时区: {option_text}")
                        timezone_selected = True
                        break
                    except:
                        try:
                            select.select_by_value(option_text)
                            print(f"✓ 选择时区 (按值): {option_text}")
                            timezone_selected = True
                            break
                        except:
                            continue
                
                if not timezone_selected:
                    # 如果都失败了，选择第一个可用选项
                    try:
                        options = select.options
                        if len(options) > 1:  # 跳过默认选项
                            select.select_by_index(1)
                            print("✓ 选择默认时区")
                    except:
                        print("⚠️ 无法选择时区，继续...")
                        
            except Exception as e:
                print(f"时区选择失败: {str(e)}")
                # 时区不是必需的，继续执行
            
            # 同意条款和隐私政策
            try:
                # 查找复选框 (根据调试信息是#tos)
                checkbox_selectors = [
                    "#tos",  # 根据调试信息
                    "input[name='user[terms_of_service]']",
                    "input[name='terms_of_service']", 
                    "input[type='checkbox']",
                    "#user_terms_of_service",
                    ".terms-checkbox",
                    "[data-testid='terms-checkbox']"
                ]
                
                checkbox_found = False
                for selector in checkbox_selectors:
                    try:
                        checkboxes = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for checkbox in checkboxes:
                            if not checkbox.is_selected() and checkbox.is_displayed():
                                # 使用JavaScript点击避免被其他元素遮挡
                                self.driver.execute_script("arguments[0].click();", checkbox)
                                print("✓ 同意服务条款和隐私政策")
                                checkbox_found = True
                                break
                        if checkbox_found:
                            break
                    except:
                        continue
                
                if not checkbox_found:
                    print("⚠️ 未找到条款复选框，继续...")
                    
            except Exception as e:
                print(f"同意条款失败: {str(e)}")
            
            time.sleep(2)
            
            print(f"\n步骤2: 点击注册按钮触发验证码...")
            
            # 点击注册按钮触发验证码
            try:
                submit_selectors = [
                    "button[type='submit']:contains('Sign up')",  # 根据调试信息
                    ".btn.btn-primary.btn-expanded",  # 根据调试信息
                    "button[type='submit']",
                    "input[type='submit']",
                    "button:contains('Sign up')",
                    "button:contains('Create Account')",
                    "button:contains('Register')",
                    ".btn-primary",
                    ".signup-btn",
                    ".submit-btn",
                    "[data-testid='signup-button']",
                    "form button"
                ]
                
                submit_button = None
                for selector in submit_selectors:
                    try:
                        # 先尝试精确的选择器
                        if selector == "button[type='submit']:contains('Sign up')":
                            buttons = self.driver.find_elements(By.CSS_SELECTOR, "button[type='submit']")
                            for btn in buttons:
                                if 'sign up' in btn.text.lower():
                                    submit_button = btn
                                    break
                        elif selector == ".btn.btn-primary.btn-expanded":
                            submit_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                        else:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                if element.is_displayed() and element.is_enabled():
                                    submit_button = element
                                    break
                        if submit_button:
                            break
                    except:
                        continue
                
                if submit_button:
                    # 滚动到按钮位置并点击
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                    time.sleep(1)
                    
                    button_text = submit_button.text.strip() or submit_button.get_attribute("value")
                    print(f"找到注册按钮: {button_text}")
                    
                    # 点击按钮触发验证码
                    self.driver.execute_script("arguments[0].click();", submit_button)
                    print("✓ 点击注册按钮，等待验证码出现...")
                    time.sleep(3)
                else:
                    print("✗ 未找到注册按钮")
                    return None
                    
            except Exception as e:
                print(f"点击注册按钮失败: {str(e)}")
                return None
            
            print(f"\n步骤3: 处理验证码...")
            
            # 现在应该出现验证码，处理验证码
            if not self.handle_captcha_manually():
                print("验证码处理失败或被跳过")
                return None
            
            print(f"\n步骤4: 验证码完成后，可能需要重新填写密码...")
            
            # 验证码完成后，可能需要重新填写密码
            try:
                # 检查密码字段是否为空，如果为空则重新填写
                password_input = self.driver.find_element(By.CSS_SELECTOR, "input[name='user[password]'], input[name='password'], #user_password, #password, input[type='password']")
                if not password_input.get_attribute('value'):
                    password_input.clear()
                    password_input.send_keys(password)
                    print("✓ 重新填写密码")
                else:
                    print("✓ 密码仍然有效")
            except Exception as e:
                print(f"检查密码字段失败: {str(e)}")
            
            # 再次检查条款复选框
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, "#tos")
                if not checkbox.is_selected():
                    self.driver.execute_script("arguments[0].click();", checkbox)
                    print("✓ 重新勾选服务条款")
            except:
                pass
            
            print(f"\n步骤5: 最终提交注册...")
            
            # 再次点击提交按钮
            try:
                submit_button = self.driver.find_element(By.CSS_SELECTOR, ".btn.btn-primary.btn-expanded")
                self.driver.execute_script("arguments[0].click();", submit_button)
                print("✓ 最终提交注册表单")
            except Exception as e:
                print(f"最终提交失败: {str(e)}")
                return None
            
            time.sleep(8)
            
            # 检查注册结果
            current_url = self.driver.current_url
            page_source = self.driver.page_source.lower()
            
            print(f"注册后页面URL: {current_url}")
            
            # 检查是否需要邮件验证
            if any(keyword in page_source for keyword in ["verify", "confirmation", "check your email", "activate", "验证"]):
                print("需要邮件验证，等待验证邮件...")
                
                # 等待验证邮件
                verification_link = self.wait_for_verification_email(email_info, "searchapi")
                if verification_link:
                    print(f"✓ 获取到验证链接: {verification_link}")
                    self.driver.get(verification_link)
                    time.sleep(5)
                    
                    # 验证完成后，尝试登录并获取API密钥
                    api_key = self.login_and_get_api_key(email_info['email'], password)
                    if api_key:
                        return {
                            'provider': 'searchapi.io',
                            'api_key': api_key,
                            'email': email_info['email'],
                            'password': password,
                            'full_name': full_name
                        }
                else:
                    print("✗ 未收到验证邮件")
                    return None
            else:
                # 可能直接注册成功，尝试获取API密钥
                print("注册可能已完成，尝试获取API密钥...")
                api_key = self.get_searchapi_key_from_current_page()
                if api_key:
                    return {
                        'provider': 'searchapi.io',
                        'api_key': api_key,
                        'email': email_info['email'],
                        'password': password,
                        'full_name': full_name
                    }
                else:
                    # 尝试登录获取
                    api_key = self.login_and_get_api_key(email_info['email'], password)
                    if api_key:
                        return {
                            'provider': 'searchapi.io',
                            'api_key': api_key,
                            'email': email_info['email'],
                            'password': password,
                            'full_name': full_name
                        }
            
            return None
            
        except Exception as e:
            print(f"SearchAPI.io注册失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def login_and_get_api_key(self, email, password):
        """登录SearchAPI.io并获取API密钥"""
        try:
            print("尝试登录获取API密钥...")
            
            # 访问正确的登录页面
            self.driver.get('https://www.searchapi.io/users/sign_in')
            time.sleep(5)
            
            print("=== 登录页面调试信息 ===")
            print(f"当前URL: {self.driver.current_url}")
            print(f"页面标题: {self.driver.title}")
            
            # 填写登录信息
            print("填写邮箱...")
            try:
                email_input = WebDriverWait(self.driver, 15).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "input[name='user[email]'], input[name='email'], #user_email, #email, input[type='email']"))
                )
                email_input.clear()
                email_input.send_keys(email)
                print(f"✓ 填写邮箱: {email}")
            except Exception as e:
                print(f"填写邮箱失败: {str(e)}")
                return None
            
            print("填写密码...")
            try:
                password_input = self.driver.find_element(By.CSS_SELECTOR, "input[name='user[password]'], input[name='password'], #user_password, #password, input[type='password']")
                password_input.clear()
                password_input.send_keys(password)
                print("✓ 填写密码")
            except Exception as e:
                print(f"填写密码失败: {str(e)}")
                return None
            
            # 查找并点击正确的登录按钮（避免GitHub登录）
            print("查找登录按钮...")
            
            # 输出页面上所有的按钮和提交元素进行调试
            try:
                all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                all_submits = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
                all_clickable = all_buttons + all_submits
                
                print(f"找到 {len(all_clickable)} 个可点击元素:")
                for i, elem in enumerate(all_clickable):
                    try:
                        text = elem.text.strip() or elem.get_attribute("value") or "N/A"
                        elem_type = elem.get_attribute("type") or elem.tag_name
                        elem_class = elem.get_attribute("class") or "N/A"
                        elem_name = elem.get_attribute("name") or "N/A"
                        print(f"  {i+1}. Text: '{text}', Type: {elem_type}, Class: '{elem_class}', Name: '{elem_name}'")
                    except:
                        print(f"  {i+1}. [获取信息失败]")
            except Exception as e:
                print(f"调试按钮信息时出错: {e}")
            
            # 更精确的登录按钮选择策略
            login_button = None
            
            # 策略1: 查找包含"Log in"文本的按钮，排除第三方登录
            try:
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                submit_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit']")
                all_buttons = buttons + submit_inputs
                
                for button in all_buttons:
                    text = button.text.strip().lower()
                    button_class = button.get_attribute("class") or ""
                    
                    # 排除第三方登录按钮（GitHub、Google等）
                    if any(third_party in text for third_party in ['github', 'google', 'facebook', 'twitter', 'microsoft']):
                        print(f"跳过第三方登录按钮: {button.text}")
                        continue
                    
                    if any(third_party in button_class.lower() for third_party in ['github', 'google', 'facebook', 'twitter', 'microsoft']):
                        print(f"跳过第三方登录按钮 (基于class): {button.text}")
                        continue
                    
                    # 只查找普通的"Log in"按钮，排除"Sign in with"格式
                    if text == 'log in' or (text.startswith('log in') and 'with' not in text):
                        login_button = button
                        print(f"找到登录按钮 (策略1): '{button.text}'")
                        break
                    
                    # 如果没找到"Log in"，尝试查找其他可能的登录按钮
                    if not login_button and text in ['login', 'submit'] and 'with' not in text:
                        login_button = button
                        print(f"找到登录按钮 (策略1-备用): '{button.text}'")
                        
            except Exception as e:
                print(f"策略1失败: {e}")
            
            # 策略2: 如果策略1失败，查找表单中的提交按钮
            if not login_button:
                try:
                    # 查找表单中的提交按钮
                    form_submits = self.driver.find_elements(By.CSS_SELECTOR, "form button[type='submit'], form input[type='submit']")
                    for submit_btn in form_submits:
                        text = submit_btn.text.strip() or submit_btn.get_attribute("value") or ""
                        button_class = submit_btn.get_attribute("class") or ""
                        
                        # 排除第三方登录按钮
                        if any(third_party in text.lower() for third_party in ['github', 'google', 'facebook', 'twitter', 'microsoft']):
                            print(f"跳过第三方提交按钮: {text}")
                            continue
                            
                        if any(third_party in button_class.lower() for third_party in ['github', 'google', 'facebook', 'twitter', 'microsoft']):
                            print(f"跳过第三方提交按钮 (基于class): {text}")
                            continue
                        
                        # 优先选择普通的登录按钮
                        if 'with' not in text.lower():
                            login_button = submit_btn
                            print(f"找到表单提交按钮 (策略2): '{text}'")
                            break
                except Exception as e:
                    print(f"策略2失败: {e}")
            
            # 策略3: 查找特定的CSS类或ID，排除第三方登录
            if not login_button:
                try:
                    specific_selectors = [
                        "button.btn-primary:not([class*='github']):not([class*='google'])",
                        "input[type='submit']:not([class*='github']):not([class*='google'])",
                        "button[type='submit']:not([class*='github']):not([class*='google'])",
                        ".btn-primary:not([class*='github']):not([class*='google'])",
                        "#sign-in-button",
                        "#login-button"
                    ]
                    
                    for selector in specific_selectors:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            if elements:
                                # 进一步检查，确保不是第三方登录按钮
                                for elem in elements:
                                    text = elem.text.strip().lower()
                                    button_class = elem.get_attribute("class") or ""
                                    
                                    # 排除第三方登录
                                    if any(third_party in text for third_party in ['github', 'google', 'facebook', 'twitter', 'microsoft']):
                                        continue
                                    if any(third_party in button_class.lower() for third_party in ['github', 'google', 'facebook', 'twitter', 'microsoft']):
                                        continue
                                    if 'with' in text:  # 排除 "Sign in with..." 格式
                                        continue
                                        
                                    login_button = elem
                                    print(f"找到登录按钮 (策略3): '{elem.text}' (选择器: {selector})")
                                    break
                                if login_button:
                                    break
                        except:
                            continue
                except Exception as e:
                    print(f"策略3失败: {e}")
            
            # 如果所有策略都失败，提供手动选择
            if not login_button:
                print("⚠️ 自动识别登录按钮失败")
                print("请手动点击正确的登录按钮（不是GitHub登录）")
                
                # 等待用户手动点击
                print("等待30秒供用户手动点击登录按钮...")
                time.sleep(30)
            else:
                # 点击找到的登录按钮
                try:
                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", login_button)
                    time.sleep(1)
                    
                    # 点击按钮
                    self.driver.execute_script("arguments[0].click();", login_button)
                    print(f"✓ 点击登录按钮: '{login_button.text}'")
                except Exception as e:
                    print(f"点击登录按钮失败: {e}")
                    return None
            
            # 等待登录完成
            print("等待登录完成...")
            time.sleep(8)
            
            # 检查登录是否成功
            current_url = self.driver.current_url
            print(f"登录后URL: {current_url}")
            
            # 如果没有跳转到dashboard，尝试手动导航
            if 'dashboard' not in current_url and 'sign_in' in current_url:
                print("登录可能失败，当前仍在登录页面")
                return None
            
            # 获取API密钥
            print("登录成功，开始获取API密钥...")
            api_key = self.get_searchapi_key_from_current_page()
            return api_key
            
        except Exception as e:
            print(f"登录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def search_api_key_in_current_page(self):
        """在当前页面搜索API密钥"""
        try:
            # 输出页面调试信息
            print("=== API密钥页面调试信息 ===")
            page_source = self.driver.page_source
            print(f"页面标题: {self.driver.title}")
            print(f"页面URL: {self.driver.current_url}")
            
            # 根据您提供的信息，API密钥在特定的input元素中
            specific_selectors = [
                # 根据您提供的HTML结构
                'input[name="api-key"]',
                'input#api-key',
                'input[id="api-key"]',
                'input[readonly][value]',  # readonly的input通常包含API密钥
                'input[autocomplete="off"][readonly]',
            ]
            
            print("尝试特定选择器...")
            for selector in specific_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    print(f"选择器 '{selector}' 找到 {len(elements)} 个元素")
                    
                    for i, element in enumerate(elements):
                        try:
                            value = element.get_attribute('value')
                            name = element.get_attribute('name')
                            element_id = element.get_attribute('id')
                            
                            print(f"  元素{i+1}: name={name}, id={element_id}, value={value[:20] if value else 'None'}...")
                            
                            # 检查是否是API密钥
                            if value and len(value) >= 15:  # API密钥通常较长
                                # 检查是否只包含字母数字字符
                                if value.replace('-', '').replace('_', '').isalnum():
                                    print(f"✓ 找到API密钥: {value}")
                                    return value
                        except Exception as inner_e:
                            print(f"  处理元素{i+1}时出错: {inner_e}")
                except Exception as e:
                    print(f"选择器 '{selector}' 出错: {e}")
            
            # 备用方法：查找所有readonly的input元素
            print("尝试查找所有readonly input元素...")
            try:
                readonly_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[readonly]")
                print(f"找到 {len(readonly_inputs)} 个readonly input元素")
                
                for i, inp in enumerate(readonly_inputs):
                    try:
                        value = inp.get_attribute('value')
                        name = inp.get_attribute('name') or 'N/A'
                        element_id = inp.get_attribute('id') or 'N/A'
                        
                        print(f"  readonly元素{i+1}: name={name}, id={element_id}, value={value[:20] if value else 'None'}...")
                        
                        if value and len(value) >= 15 and value.replace('-', '').replace('_', '').isalnum():
                            print(f"✓ 在readonly元素中找到API密钥: {value}")
                            return value
                    except Exception as inner_e:
                        print(f"  处理readonly元素{i+1}时出错: {inner_e}")
            except Exception as e:
                print(f"查找readonly元素时出错: {e}")
            
            # 最后尝试：通过正则表达式在页面源码中查找
            print("在页面源码中搜索API密钥模式...")
            api_key_patterns = [
                # 根据您提供的信息，API密钥类似 "x9A6tsRjcbmYscy7mb4HSHrt"
                r'value="([a-zA-Z0-9]{20,})"[^>]*name="api-key"',
                r'name="api-key"[^>]*value="([a-zA-Z0-9]{20,})"',
                r'id="api-key"[^>]*value="([a-zA-Z0-9]{20,})"',
                r'value="([a-zA-Z0-9]{20,})"[^>]*id="api-key"',
                r'Your API Key[^<]*<[^>]*>([a-zA-Z0-9]{15,})',
                r'API Key[^<]*<[^>]*>([a-zA-Z0-9]{15,})',
                # 通用模式
                r'"api[_-]?key"[^>]*value="([a-zA-Z0-9]{15,})"',
                r'value="([a-zA-Z0-9]{15,})"[^>]*"api[_-]?key"',
            ]
            
            for i, pattern in enumerate(api_key_patterns):
                try:
                    matches = re.findall(pattern, page_source, re.IGNORECASE | re.DOTALL)
                    if matches:
                        for match in matches:
                            api_key = match.strip()
                            if len(api_key) >= 15:
                                print(f"✓ 通过正则表达式找到API密钥 (模式{i+1}): {api_key}")
                                return api_key
                except Exception as e:
                    print(f"正则表达式模式{i+1}出错: {e}")
            
            # 如果还是没找到，输出更多调试信息
            print("\n=== 详细调试信息 ===")
            # 查找页面中包含 "API" 的文本
            try:
                elements_with_api = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'API') or contains(text(), 'api')]")
                print(f"包含'API'的元素数量: {len(elements_with_api)}")
                for i, elem in enumerate(elements_with_api[:5]):  # 只显示前5个
                    try:
                        text = elem.text.strip()[:100]  # 限制长度
                        tag = elem.tag_name
                        print(f"  元素{i+1} ({tag}): {text}")
                    except:
                        pass
            except Exception as e:
                print(f"查找包含API的元素时出错: {e}")
            
            # 查找所有input元素的详细信息
            try:
                all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                print(f"\n所有input元素数量: {len(all_inputs)}")
                for i, inp in enumerate(all_inputs[:10]):  # 只显示前10个
                    try:
                        input_type = inp.get_attribute("type") or "text"
                        name = inp.get_attribute("name") or "N/A"
                        element_id = inp.get_attribute("id") or "N/A"
                        value = inp.get_attribute("value") or "N/A"
                        readonly = inp.get_attribute("readonly")
                        
                        value_display = value[:20] + "..." if len(value) > 20 else value
                        print(f"  input{i+1}: type={input_type}, name={name}, id={element_id}, value={value_display}, readonly={readonly}")
                    except:
                        print(f"  input{i+1}: [获取信息失败]")
            except Exception as e:
                print(f"获取所有input元素时出错: {e}")
            
            print("=== 调试信息结束 ===")
            print("在当前页面未能找到API密钥")
            return None
            
        except Exception as e:
            print(f"在当前页面搜索API密钥失败: {str(e)}")
            return None

    def get_searchapi_key_from_current_page(self):
        """从当前页面获取SearchAPI.io的API密钥"""
        try:
            print("开始查找API密钥...")
            
            # 首先检查当前页面是否已经在Dashboard
            current_url = self.driver.current_url
            print(f"当前页面URL: {current_url}")
            
            # 先在当前页面查找API密钥，不立即跳转
            print("首先在当前页面查找API密钥...")
            api_key = self.search_api_key_in_current_page()
            if api_key:
                print(f"✓ 在当前页面找到API密钥: {api_key}")
                return api_key
            
            # 如果当前页面没找到且不在dashboard，尝试导航到dashboard
            if 'dashboard' not in current_url.lower():
                print("当前页面未找到API密钥，导航到Dashboard页面...")
                self.driver.get('https://www.searchapi.io/dashboard')
                time.sleep(5)
                
                # 在Dashboard页面再次查找
                api_key = self.search_api_key_in_current_page()
                if api_key:
                    print(f"✓ 在Dashboard页面找到API密钥: {api_key}")
                    return api_key
            
            
            # 尝试其他可能的页面，如账户设置或API页面
            other_possible_pages = [
                'https://www.searchapi.io/account',
                'https://www.searchapi.io/api',
                'https://www.searchapi.io/settings',
                'https://www.searchapi.io/profile'
            ]
            
            for page_url in other_possible_pages:
                try:
                    print(f"尝试访问页面: {page_url}")
                    self.driver.get(page_url)
                    time.sleep(3)
                    
                    api_key = self.search_api_key_in_current_page()
                    if api_key:
                        print(f"✓ 在页面 {page_url} 找到API密钥: {api_key}")
                        return api_key
                except Exception as e:
                    print(f"访问页面 {page_url} 失败: {e}")
                    continue
            
            print("所有页面都未找到API密钥")
            return None
                    
        except Exception as e:
            print(f"获取SearchAPI.io密钥失败: {str(e)}")
            traceback.print_exc()
            return None
                    
        except Exception as e:
            print(f"获取SearchAPI.io密钥失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    def wait_for_verification_email(self, email_info, service_name, timeout=300):
        """等待验证邮件 - 支持手动邮箱"""
        print(f"等待{service_name}验证邮件...")
        
        provider = email_info.get('provider', '')
        
        # 如果是手动输入的邮箱，提供手动检查选项
        if provider in ['linshiguge_manual', 'manual']:
            return self.wait_for_verification_email_manual(email_info, service_name, timeout)
        
        # 自动检查邮件
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                if email_info['provider'] == 'linshiguge':
                    messages = self.email_provider.check_emails_linshiguge(email_info)
                elif email_info['provider'] == '1secmail':
                    messages = self.email_provider.check_emails_1secmail(email_info)
                elif email_info['provider'] == 'guerrillamail':
                    messages = self.email_provider.check_emails_guerrillamail(email_info)
                else:
                    print("不支持的邮箱提供商，切换到手动模式")
                    return self.wait_for_verification_email_manual(email_info, service_name, timeout)
                
                for message in messages:
                    subject = message.get('subject', '').lower()
                    if service_name.lower() in subject or 'verify' in subject or 'confirm' in subject or 'activate' in subject:
                        print(f"收到验证邮件: {message.get('subject', '')}")
                        
                        # 获取邮件内容
                        if email_info['provider'] == '1secmail':
                            content = self.email_provider.get_email_content_1secmail(email_info, message['id'])
                            if content:
                                # 从邮件内容中提取验证链接
                                verification_link = self.extract_verification_link(content.get('body', ''))
                                if verification_link:
                                    return verification_link
                        elif email_info['provider'] == 'linshiguge':
                            # linshiguge的邮件内容可能直接在message中
                            content = message.get('content', '') or message.get('body', '') or str(message)
                            verification_link = self.extract_verification_link(content)
                            if verification_link:
                                return verification_link
                
                time.sleep(15)  # 每15秒检查一次
                print(f"继续等待验证邮件... ({int(time.time() - start_time)}s)")
                
            except Exception as e:
                print(f"检查邮件时出错: {str(e)}")
                time.sleep(15)
        
        print("验证邮件等待超时，切换到手动模式")
        return self.wait_for_verification_email_manual(email_info, service_name, 60)
    
    def wait_for_verification_email_manual(self, email_info, service_name, timeout=300):
        """手动等待验证邮件"""
        email = email_info['email']
        print(f"\n📧 手动验证邮件模式")
        print("=" * 50)
        print(f"邮箱地址: {email}")
        print(f"等待来自: {service_name}")
        print("=" * 50)
        print("请按以下步骤操作:")
        print("1. 打开浏览器访问邮箱网站 (如 linshiguge.com)")
        print("2. 查看新邮件，找到来自SearchAPI.io的验证邮件")
        print("3. 复制邮件中的验证链接")
        print("4. 粘贴到下面的输入框中")
        print("=" * 50)
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                user_input = input(f"\n请输入验证链接 (或输入'skip'跳过, 'refresh'刷新邮箱): ").strip()
                
                if user_input.lower() == 'skip':
                    print("用户选择跳过邮件验证")
                    return None
                elif user_input.lower() == 'refresh':
                    print("请手动刷新邮箱页面，继续等待...")
                    continue
                elif user_input.startswith('http'):
                    # 验证链接格式
                    if 'searchapi.io' in user_input and ('confirm' in user_input or 'verify' in user_input or 'activate' in user_input):
                        print(f"✓ 验证链接格式正确: {user_input}")
                        return user_input
                    else:
                        print("⚠️ 链接格式可能不正确，但仍会尝试使用")
                        return user_input
                else:
                    print("请输入有效的HTTP链接或'skip'跳过")
                    continue
                    
            except KeyboardInterrupt:
                print("\n用户中断邮件验证")
                return None
            except Exception as e:
                print(f"输入出错: {e}")
                continue
        
        print("手动验证邮件超时")
        return None
    
    def extract_verification_link(self, email_body):
        """从邮件内容中提取验证链接"""
        # SearchAPI.io的验证链接模式
        patterns = [
            # SearchAPI.io特定模式
            r'https?://(?:www\.)?searchapi\.io/users/confirmation\?[^\s<>"]*',
            r'https?://(?:www\.)?searchapi\.io/[^\s<>"]*confirm[^\s<>"]*',
            r'https?://(?:www\.)?searchapi\.io/[^\s<>"]*verification[^\s<>"]*',
            r'https?://(?:www\.)?searchapi\.io/[^\s<>"]*verify[^\s<>"]*',
            r'https?://(?:www\.)?searchapi\.io/[^\s<>"]*activate[^\s<>"]*',
            
            # 通用验证链接模式
            r'https?://[^\s<>"]+(?:verify|confirm|activate)[^\s<>"]*',
            r'https?://[^\s<>"]+confirmation[^\s<>"]*',
            r'https?://[^\s<>"]+verification[^\s<>"]*',
            r'https?://[^\s<>"]+token[^\s<>"]*',
            
            # HTML链接格式
            r'href=["\']?(https?://(?:www\.)?searchapi\.io/[^"\']*(?:confirm|verify|activate)[^"\']*)["\']?',
            r'href=["\']?(https?://[^"\']+(?:verify|confirm|activate)[^"\']*)["\']?',
            r'href=["\']?(https?://[^"\']+token[^"\']*)["\']?',
        ]
        
        print("正在搜索验证链接...")
        print(f"邮件内容长度: {len(email_body)} 字符")
        
        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, email_body, re.IGNORECASE)
            if matches:
                for match in matches:
                    link = match if isinstance(match, str) else match[0] if isinstance(match, tuple) else str(match)
                    if link.startswith('http'):
                        print(f"找到验证链接 (模式 {i+1}): {link}")
                        return link
        
        # 如果没有找到，输出邮件内容的一部分进行调试
        print("未找到验证链接，邮件内容摘要:")
        print("=" * 50)
        print(email_body[:500] + "..." if len(email_body) > 500 else email_body)
        print("=" * 50)
        
        return None
    
    def auto_register_multiple_apis(self, target_count=3, use_proxy=True):
        """自动注册多个SearchAPI.io账号"""
        print(f"开始自动注册{target_count}个SearchAPI.io账号...")
        
        if not self.setup_chrome_driver(use_proxy=use_proxy):
            print("Chrome驱动设置失败")
            return []
        
        registered_count = 0
        
        try:
            while registered_count < target_count:
                # 获取临时邮箱
                email_info = self.email_provider.get_available_temp_email()
                if not email_info:
                    print("无法获取临时邮箱，停止注册")
                    break
                
                # 清理邮箱地址
                clean_email = self.clean_email_address(email_info['email'])
                if not clean_email:
                    print("邮箱地址无效，尝试获取新的邮箱")
                    continue
                    
                # 更新邮箱信息
                email_info['email'] = clean_email
                
                print(f"\n=== 尝试注册第{registered_count + 1}个账号 ===")
                print(f"使用邮箱: {email_info['email']}")
                
                # 添加邮箱地址验证
                email_address = email_info['email']
                print(f"调试 - 邮箱地址长度: {len(email_address)}")
                print(f"调试 - 邮箱地址@符号数量: {email_address.count('@')}")
                
                # 验证邮箱地址格式
                if email_address.count('@') != 1:
                    print(f"⚠️ 警告：邮箱地址格式异常: {email_address}")
                    print("跳过此邮箱，尝试获取新的邮箱地址")
                    continue
                
                # 注册SearchAPI.io
                api_info = self.register_searchapi_io(email_info)
                
                if api_info:
                    self.registered_apis.append(api_info)
                    registered_count += 1
                    print(f"✓ 成功注册第{registered_count}个API: {api_info['api_key'][:10]}...")
                    
                    # 保存API信息
                    self.save_api_info(api_info)
                else:
                    print(f"✗ 注册失败")
                
                # 随机等待避免过于频繁的请求
                if registered_count < target_count:
                    wait_time = random.uniform(60, 120)  # 1-2分钟随机等待
                    print(f"等待{wait_time:.0f}秒后继续...")
                    time.sleep(wait_time)
                
        except KeyboardInterrupt:
            print("用户中断注册过程")
        except Exception as e:
            print(f"注册过程出错: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            if self.driver:
                self.driver.quit()
        
        print(f"\n注册完成，共获得{len(self.registered_apis)}个API密钥")
        return self.registered_apis
    
    def save_api_info(self, api_info):
        """保存API信息到文件"""
        try:
            filename = f"searchapi_keys_{datetime.now().strftime('%Y%m%d')}.json"
            
            # 读取现有数据
            existing_data = []
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except FileNotFoundError:
                pass
            
            # 添加新的API信息
            api_record = {
                'provider': api_info['provider'],
                'api_key': api_info['api_key'],
                'email': api_info['email'],
                'created_at': datetime.now().isoformat(),
                'status': 'active'
            }
            
            existing_data.append(api_record)
            
            # 保存到文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
            
            print(f"✓ API信息已保存到: {filename}")
            
        except Exception as e:
            print(f"保存API信息失败: {str(e)}")
    
    def load_existing_apis(self):
        """加载现有的API密钥"""
        try:
            filename = f"searchapi_keys_{datetime.now().strftime('%Y%m%d')}.json"
            
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            active_apis = []
            for item in data:
                if item.get('status') == 'active':
                    active_apis.append(item['api_key'])
            
            print(f"✓ 加载了{len(active_apis)}个现有API密钥")
            return active_apis
            
        except FileNotFoundError:
            print("未找到现有API密钥文件")
            return []
        except Exception as e:
            print(f"加载API密钥失败: {str(e)}")
            return []


def get_fresh_search_api_keys(target_count=2):
    """获取新鲜的SearchAPI.io密钥"""
    print("=" * 50)
    print("自动获取SearchAPI.io密钥")
    print("=" * 50)
    
    register = SearchAPIRegister()
    
    # 先尝试加载现有的API
    existing_apis = register.load_existing_apis()
    if len(existing_apis) >= target_count:
        print(f"已有足够的API密钥({len(existing_apis)}个)，无需重新注册")
        return existing_apis[:target_count]
    
    # 如果现有API不足，进行注册
    need_count = target_count - len(existing_apis)
    print(f"需要注册{need_count}个新的SearchAPI.io账号")
    
    # 尝试自动注册
    new_apis = register.auto_register_multiple_apis(need_count)
    
    # 如果自动注册失败，提供手动输入选项
    if not new_apis:
        print("\n自动注册失败，提供手动输入选项...")
        manual_apis = manual_api_input()
        if manual_apis:
            new_apis = [{'api_key': key} for key in manual_apis]
    
    # 合并现有和新注册的API
    if new_apis:
        all_apis = existing_apis + [api['api_key'] for api in new_apis]
    else:
        all_apis = existing_apis
    
    return all_apis[:target_count] if all_apis else []


def test_searchapi_keys(api_keys):
    """测试SearchAPI.io密钥是否有效"""
    print("\n测试SearchAPI.io密钥有效性...")
    
    valid_keys = []
    
    for i, api_key in enumerate(api_keys):
        try:
            print(f"测试API密钥 {i+1}: {api_key[:10]}...")
            
            # 发送测试请求到SearchAPI.io
            test_url = "https://www.searchapi.io/api/v1/search"
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            # 简单的测试搜索
            payload = {
                'q': 'test',
                'engine': 'google',
                'num': 1
            }
            
            response = requests.post(test_url, headers=headers, json=payload, timeout=10)
            
            if response.status_code == 200:
                valid_keys.append(api_key)
                print(f"✓ API密钥 {i+1} 有效")
            else:
                print(f"✗ API密钥 {i+1} 无效 (状态码: {response.status_code})")
            
        except Exception as e:
            print(f"✗ API密钥 {i+1} 测试失败: {str(e)}")
    
    print(f"测试完成，{len(valid_keys)}/{len(api_keys)} 个密钥有效")
    return valid_keys


def manual_api_input():
    """手动输入API密钥的备用方案"""
    print("\n" + "="*60)
    print("Chrome驱动问题，切换到手动输入模式")
    print("="*60)
    print("\n请手动注册SearchAPI.io并输入API密钥:")
    print("1. 访问: https://www.searchapi.io/signup")
    print("2. 使用临时邮箱注册: https://www.linshiguge.com/")
    print("3. 验证邮箱后登录")
    print("4. 访问dashboard获取API密钥")
    print("5. 输入获取到的API密钥\n")
    
    api_keys = []
    count = 0
    target = 2
    
    while count < target:
        try:
            api_key = input(f"请输入第{count+1}个API密钥 (按Enter跳过): ").strip()
            if api_key and len(api_key) > 10:
                # 简单验证API密钥格式
                if api_key.replace('-', '').replace('_', '').isalnum():
                    api_keys.append(api_key)
                    count += 1
                    print(f"✓ 已保存第{count}个API密钥")
                    
                    # 保存到文件
                    save_manual_api_key(api_key)
                else:
                    print("API密钥格式不正确，请重新输入")
            else:
                break
        except KeyboardInterrupt:
            print("\n用户取消输入")
            break
    
    return api_keys

def save_manual_api_key(api_key):
    """保存手动输入的API密钥"""
    try:
        import json
        from datetime import datetime
        
        filename = f"searchapi_keys_{datetime.now().strftime('%Y%m%d')}.json"
        
        # 读取现有数据
        existing_data = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        except FileNotFoundError:
            pass
        
        # 添加新的API信息
        api_record = {
            'provider': 'searchapi.io',
            'api_key': api_key,
            'email': 'manual_input',
            'created_at': datetime.now().isoformat(),
            'status': 'active'
        }
        
        existing_data.append(api_record)
        
        # 保存到文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ API密钥已保存到: {filename}")
        
    except Exception as e:
        print(f"保存API密钥失败: {str(e)}")


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("=" * 60)
    print("SearchAPI.io 自动注册工具 - 现代版本")
    print("使用Selenium 4.33+ 和 Selenium Manager")
    print("=" * 60)
    
    try:
        # 检查Selenium版本
        print("\n检查运行环境...")
        if not check_selenium_version():
            print("❌ Selenium版本不符合要求，请升级后重试")
            print("升级命令: pip install --upgrade selenium")
            exit(1)
        
        print("✓ 环境检查通过")
        
        # 获取2个SearchAPI.io密钥
        api_keys = get_fresh_search_api_keys(target_count=2)
        
        if api_keys:
            print(f"\n✓ 成功获取{len(api_keys)}个API密钥:")
            for i, key in enumerate(api_keys):
                print(f"  {i+1}. {key}")
            
            # 测试API密钥
            valid_keys = test_searchapi_keys(api_keys)
            
            if valid_keys:
                print(f"\n✓ 最终可用API密钥数量: {len(valid_keys)}")
                print("\n建议:")
                print("1. 将API密钥保存到安全的地方")
                print("2. 定期检查API密钥的使用限制")
                print("3. 如需更多密钥，可以重新运行此脚本")
            else:
                print("\n✗ 没有可用的API密钥")
        else:
            print("\n✗ 未能获取任何API密钥")
            print("可能的原因:")
            print("1. 网络连接问题")
            print("2. 临时邮箱服务不可用")
            print("3. SearchAPI.io注册流程发生变化")
            print("4. WebDriver配置问题")
            
    except KeyboardInterrupt:
        print("\n用户中断程序执行")
    except Exception as e:
        print(f"执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\n如果问题持续，请检查:")
        print("1. Chrome浏览器是否正确安装")
        print("2. 网络连接是否正常")
        print("3. 是否需要配置代理")
        print("4. Selenium版本是否为最新")
