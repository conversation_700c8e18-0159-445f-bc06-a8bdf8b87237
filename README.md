
语义检索和匹配有些问题，通用领域的嵌入模型还是不太行，还有向量检索的逻辑需要进行优化

检索使用自带的BM25


运行前在终端配置环境，必须

 在Data_Excation路径下进行运行

 export PYTHONPATH="/home/<USER>/docker_archivebox/Data_Excation:$PYTHONPATH"           ---linux下设置环境变量，运行任何模块前先进行对项目环境变量的设置

 使用的是  虚拟环境中的python包  ，隔离环境

 # 激活虚拟环境
 
source ~/archivebox_venv/bin/activate


在Data_Excation路径下进行运行
    python .\Page_view\backend\app.py   ------启动网络威胁情报分析系统



    threat_intel_config_simplified.py   ---------生成检索语法

    python pa_week_ar_test.py        ---------启动每周爬取分析脚本

    文件需要Data_Excation/config.json中的cookie，微信公众号的。。。还需search的api_key

Data_Excation\data 目录下是archivebox爬虫的docker存储的数据

# 已实现

1. 每天手动收集  质量高高的文章  然后记录下 URL    Data_Excation/国内外威胁情报博客收集.csv 文件中
2. URL 存档在Archive box中 
3. 自动读取 Archive Box中的存档分析，批量存，批量分析
4. 手动收集  质量高高的文章  域名记下来  加到自动爬虫的脚本中
5. 不用爬虫爬数据  search api 拿到文章的URL  提供给Archive box  存档 循环读取
----------------------------------------------------------------------在 Data_Excation/pa_week_ar_test.py 文件中

单独的一套运行流程了

search api 搞一个用临时邮箱自动注册search api 的工具，执行前调用拿到api，半自动已实现，   
---------------------------------------------------------------------------------在 Data_Excation/auto_search_api_register.py 中

后面改成利用临时邮箱全自动获取 search api




使用nohup 运行起来后的日志s


Data_Excation/App_web.log   是系统运行的日志

Data_Excation/crawl_week.log   是每周爬取脚本的日志

# 存在的问题

有的网页的日期提取不到

问题：先存档，再分析的任务，分析完成后，之前的存档任务中的状态需要改变

search api 拿到文章的URL 不全是威胁情报领域的文章，这个需要过滤一下


<!-- 进度 -->
改进（未完成）：

1、中文文章就不需要加入知识库检索，再对引用（参考信息）做后处理，去掉不可读的内容（已完成）

2、文章日期提取，一些网站的标题提取也不是很好

-----------------------------------使用了htmldate库，
爬取不到日期的网站： 

微信公众号  ---------已解决，从response的js文件中获取var ct = "时间戳" ，因为微信公众号的内容是动态加载的


https://thehackernews.com/    ------日期提取不到，单独逻辑（已完成）

https://research.checkpoint.com/ 、 https://cybersecuritynews.com/    -----直接拒绝访问，去掉了

https://www.varonis.com/blog/   

https://www.darkreading.com/threat-intelligence/       

https://cloud.google.com/blog/topics/threat-intelligence/

-------------------------------------------可能是有反爬机制，或者是代理问题，Archive Box的内容都不全，都直接去掉了，这几个大多是新闻类的

3、任务历史的调度和展示逻辑改进

4、持续收集高质量且稳定更新的威胁报告。。。。。。。。。。

5、search api自动脚本批量注册 

6、search api 拿文章高质量URL的改进  ---已完成
---------------------1、对每个检索语法得到的链接进行过滤，必须和检索语法中的域名一致
---------------------2、加入了对列表页、内容长度的限制过滤
---------------------3、在存档分析之前对其内容进行简单的过滤，去掉一些简短的新闻（仅存档，不分析入库）
                        对其进行标题、内容的过滤，去掉那些超时，拒绝访问的存档。

7、替换RAG检索模型为网络安全领域对应模型 -ATTACK-BERT 效果好一些 主要CPU加载有点慢了(已完成)

8、IOC关联知识库




data\test  目录下是向量知识库的保存

文档类的知识库需要先分段，然后将按照一行一段的格式生成txt文件即可完成知识库的初步预处理

代理设置在  RAG\utils.py  的HttpManager中，裸连可能比较慢

**mian_data_pa.py**   ----最初的语法检索爬虫版本，可一键爬取，需要searchapi的KEY；

data_cope.py  ----从网页链接中进行爬取正文内容；

**AI_copy.py** ---是直接读取数据库中的数据进行LLM+RAG分析；

pa_week.py   ------每周更新的数据，是从数据库中获取列表页URL，然后从中获取文章数据，按照年份进行筛选，目前是只爬取当前日期前一周的文章URL，先保存到csv文件中，然后拿取URL进行分析，会自动去重，已在库中的不再进行保存分析，最后再将内容存到crawled_data表中，分析的结果存到rag_analysis表中

SQL_cope 文件夹中是数据库的一些操作，可能需要更新字段了

**RAG、知识库、configs、data、logs、outputs等文件夹都是LLM+RAG的实现；**

1、知识库的存储、检索后续可以使用LLM进行智能分块，对每个块给出简短的摘要，提高后续RAG向量检索的准确率；

2、对语义匹配检索模型进行网络安全领域的微调，提高文本的向量化的准确率；

**Page_view**是威胁情报分析的简单的展示网页，实现了获取数据，查看具体数据的分析内容，上传文本、URL,文件等内容进行分析

python .\Page_view\backend\app.py   ------启动网络威胁情报分析系统



首页快照的展示，

前端配置 ：网站域名以及爬取链接的添加