URL: https://mp.weixin.qq.com/s?__biz=Mzg2Nzg0NDkwMw==&mid=2247493239&idx=1&sn=e9ad2d81d3a1936a45c5f0e1f86e064a&chksm=ceb7f03ff9c0792902fd1e686a6fda23a0071c8aa2309b8809b75333acae8c02a37239f01dbe&cur_album_id=2557125051435712513&scene=189#wechat_redirect
标题: Unknown Title
来源: readability/content.txt (清洗后)
提取时间: 2025-06-13 17:33:50

一概述2025年4月份，绿盟科技伏影实验室依托全球威胁狩猎系统监测到一个基于Go语言开发的新型僵尸网络木马活跃度激增，大肆扩张。鉴于其内置的多种DDoS攻击方式均为http类型，伏影实验室将其命名为HTTPBot。HTTPBot僵尸网络家族最早于2024年8月份出现在我们的监测视野，数月间，该家族大肆扩张，持续操控所感染设备对外发起攻击活动。监测数据显示，其攻击目标主要集中在国内的游戏行业，此外，部分科技公司和教育行业也有受到波及。该家族攻击目标针对性极强，攻击者采用分时多阶段攻击策略，对选定目标实施持续性饱和攻击。HTTPBot僵尸网络木马在技术实现上通过“攻击ID”实现对攻击进程的精准启停，同时内置了多种创新性较高的DDoS攻击方式，通过高度仿真的HTTP Flood攻击及动态特征混淆技术，规避传统规则引擎检测，包括但不限于如下绕过检测机制：Cookie回填机制随机化http请求的 UA 与头部真实浏览器调用随机化URL路径动态速率控制状态码重试机制近年来，大部分新兴僵尸网络家族将主要精力集中在传播方式和网络控制方面的发展，比如开发专门的传播工具，采取将漏洞与木马分离的策略来保护关键信息；或者通过DGA、DOH、OpenNIC等技术手段来增强通信隐匿性；并且它们大都更关注流量型攻击（带宽消耗性），而HTTPBot则开发了一系列http类型的攻击方式，发起事务（业务）消耗性DDos攻击，攻击者可利用此类攻击方式精准打击高价值业务接口，针对游戏登录、支付等关键接口发起定向饱和攻击。其“手术刀式”攻击对依赖实时交互的行业构成系统性威胁，标志着DDoS攻击从“无差别流量压制”向“高精度业务绞杀”的范式跃迁，倒逼防御体系从“规则拦截”升级为“行为分析+资源弹性化”的动态对抗。二攻击活动监测绿盟科技伏影实验室全球威胁狩猎系统监测数据显示，HTTPBot在2025年4月初至今下发过200余条攻击指令。攻击时间段在全天24小时内均有分布，近半个月攻击活动态势如下：图 2.1 攻击活动HTTPBot家族内置了7种DDoS攻击方式，均为http类型，其攻击行动中所使用的攻击方式主要包括http_fp、http_auto以及http（上述三种攻击方式均为攻击者自命名）。图 2.2 攻击方式上述攻击活动涵盖80多个独立攻击目标，这些目标以国内的游戏行业为主，同时科技公司、教育机构及旅游景区也在其攻击范围之内。攻击者通常会分时间段对同一目标发起多轮攻击，目标性明确。近半个月以来HTTPBot攻击频率较高的部分单位如下：目标归属download.ztecq.com熵基科技（计算机视觉与生物识别）www.ltrxjh.com留恋江湖游戏网站（游戏）cn6h5.gxqykq.com广西奇遇科技有限公司（互联网）m.doyo.cn逗游网（游戏）m.youxidui.com游戏堆手游网（游戏）28jh.com28JH开区信息站（游戏）m.99680pg.com9k9k手游网（游戏）h2025411560.同济教育.com北京中经同济教育科技有限公司（教育）www.bizcn.com商中在线（互联网）www.chinagtfs.com官亭林海景区官网（景区）三木马文件分析3.1 隐藏窗口恶意软件执行后通过隐藏图形化界面（GUI），规避用户及安全工具的进程监控，显著提升攻击隐蔽性。图  3.1 隐藏窗口3.2 自启动木马通过将自身路径写入HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run启动项键值实现开机自动运行。图 3.2 自启动3.3 控制指令HTTPBot上线认证过程中向服务端发送字符串“ok”，随即等待服务端下发攻击指令，验证过程较为精简。在完成上线认证后，等待接收服务端下发的控制指令。服务端所下发的每条指令都会分配一个“攻击ID”。攻击者通过控制“攻击ID”的方式实现对攻击进程的精准启停。比如攻击者可以通过“stop”字符串结合“攻击ID”来停止一个正在进行的攻击进程。图 3.3 通过“攻击ID”实现对攻击进程的精准启停木马内置了7种DDoS攻击方式，其控制协议的内容包含：攻击方式、攻击ID、攻击目标、线程数、攻击时长等参数，其中部分参数采用Base64编码，比如攻击者下发通常包含如下关键字段：攻击方式（如HTTP_FP 表示http flood）。攻击ID，每条指令都会有一个“攻击ID”，如：“d5713a6c-7da2-4a94-9b0f-bec05d2a6b0f”。攻击目标URL，其中可能包含动态路径，意图通过随机字符串绕过静态URL检测机制。请求方法（比如GET）。Base64解码的http请求头部信息，如UserAgent等）攻击时长。攻击效果如下：图 3.4 攻击效果示例3.4 攻击能力HTTPBot内置的7种DDoS攻击方式均为http类型，部分攻击方式还存在环境限制。比如BrowserAttack和CookieAttack攻击方式在Windows版本大于8的环境下才会触发。 图 3.5 环境判断HTTPBot内置的DDoS攻击方法具备较高的原创性，通过特征随机化和高度模拟真人操作等方式尝试绕过抗DDoS设备的防护，表现出攻击者较高的技术积累。3.4.1 前置--防御http flood常见方式http flood攻击通过大量僵尸主机向目标服务器发起大量的 HTTP报文请求，请求涉及数据库操作的URI（Universal Resource Identifier）或其他消耗系统资源的URI，并且攻击在HTTP层发起，极力模仿正常用户的网页请求行为，与网站业务紧密相关。最常见的防御思路如下：基于固定特征：抗D设备依据请求流量的URI、UserAgent等固定特征来识别异常流量。比如：当指定时间内，某个URI的访问流量超过一定阈值时，AntiDDoS设备启动针对源IP的URI行为监测。当这个源IP地址对某个URI的访问数与总访问数的比例超过阈值时，则将该源IP地址作为攻击源并加入动态黑名单。基于重定向机制：僵尸工具没有实现完整的HTTP协议栈，不支持自动重定向，无法通过认证。而浏览器支持自动重定向，可以通过认证。基于cookie机制：防御HTTP POST Flood攻击时，抗D设备代替服务器向客户端响应。状态码（针对POST请求方法的重定向），同时向客户端的浏览器注入Cookie，客户端再次发起请求时会在HTTP报头上附加Cookie信息，抗D设备通过验证Cookie信息的真实性来验证客户端。基于验证码：有些僵尸工具实现了重定向功能，或者攻击过程中使用的免费代理支持重定向功能，导致基本模式的防御失效，通过推送验证码的方式可以避免此类防御失效。此时通过让用户输入验证码，可以判断HTTP访问是否由真实的用户发起，而不是僵尸工具发起的访问。因为僵尸网络攻击依靠自动植入PC的僵尸工具发起，无法自动响应随机变化的验证码，故可以有效地防御攻击。3.4.2 HttpAttackHTTPBot其内置的HttpAttack攻击方式实现TCP/TLS的动态选择。即根据目标配置动态选择明文TCP或加密TLS连接。图 3.6 检查是否为TLS此外，该攻击方式还具备如下特点：重试机制。若连接失败，休眠 100ms后重试，避免频繁触发防火墙规则。随机化UA与头部。UserAgent、Referer及诸多头部信息均采用随机化策略，比如构建UserAgent时从预定义列表随机选择 UA，并采取随机化策略。动态速率控制。每次攻击后休眠一定时间，动态调节攻击频率。3.4.3  BrowserAttackBrowserAttack在攻击过程中会启动浏览器进程极力模仿用户真实操作，但值得注意的是其预置的浏览器进程路径为“`C:\Windows\Temp\chrome-win\pp.exe`”，这并非chrome默认配置路径，再结合近半个月监测数据中攻击者并未下发过该攻击方式，推测正处于测试阶段。图 3.7 启动浏览器进程其具备如下特点：真实浏览器调用：通过启动合法浏览器进程（`C:\Windows\Temp\chrome-win\pp.exe`），利用浏览器引擎发起请求，流量特征与真实用户完全一致。隐藏窗口模式：隐藏浏览器窗口，避免触发桌面环境告警。自动化控制：通过与浏览器子进程通信，实现远程控制。动态调整：通过动态构造各参数，规避静态特征检测。3.4.4 HttpAutoAttackHttpAutoAttack攻击方式相较于HttpAttack 新增了Cookie自动化处理流程，可更好模拟合法会话。解析响应中的 `SetCookie`，提取 `guardret` 参数并构造新的Cookie，在后续请求中携带，模拟合法会话。guardret是服务器通过Set-Cookie头部返回的会话标识参数，通常用于验证客户端请求的合法性，HTTPBot通过解析响应中的guardret值，并在后续请求中回填该参数，模拟合法用户的会话行为，避免因缺失或固定Cookie值触发防护规则。图 3.8 Cookie自动化处理流程此外还具备如下特点：状态码重试机制。若响应状态码为 `429`（Too Many Requests）或 `405`（Method Not Allowed），触发休眠（`time_Sleep` 705ms），绕过服务端限速策略。HTTP/HTTPS动态支持。通过目标URL的协议类型自动选择明文HTTP或加密HTTPS。随机化 UA 与头部。从预定义列表随机选择UA，并实现随机化，动态拼接伪造的Referer。3.4.5 HttpFpDlAttackHttpFpDlAttack攻击方式采取资源消耗最大化策略。将TCP Keep-Alive时间设置为30分钟，确保即使无数据传输也不会被操作系统断开，强制使用HTTP/2，HTTP/2的多路复用特性允许在单个连接上并行传输多个大文件，强制完整接收响应， 启用 `isSaveResponse` 保存响应体，消耗目标服务器带宽，即使不需要数据内容，也要求服务器完整传输响应体。当isSaveResponse设置为1时，HTTPBot会完整接收并保存服务器返回的响应体（即使攻击无需利用响应内容）。此操作迫使服务器必须生成并传输完整响应数据（如大文件、动态页面），显著增加其带宽与CPU负载。图 3.9 isSaveResponse置为1其还具备如下特点：自动重定向与请求重试机制：设置最多5次重定向。对 `429`（Too Many Requests）和 `405`（Method Not Allowed）状态码触发重试，并添加705ms延迟，模拟人工操作。多协议支持：动态处理HTTP/HTTPS，自动管理TLS握手。路径与数据随机化。动态替换URL中的占位符，并生成随机请求体。随机化 UA。动态选择浏览器指纹，或从预定义列表随机选择UA。自动Cookie管理。解析响应中的 `Set-Cookie`（如 `guardret`），构造合法Cookie并回注到后续请求。使用非标准密码套件， 跳过证书验证。随机化请求间隔，避免固定频率触发速率限制。动态生成 `guardret` Cookie参数，绕过基于静态Session ID的拦截。3.4.6 WebSocketAttackWebSocketAttack攻击方式支持动态切换ws/wss 。解析目标URL，支持 `ws://` 和 `wss://` 协议。建立WebSocket连接后，循环发送消息，通过控制单连接的消息数量，减少握手开销。图 3.10 WebSocket攻击此外还具备如下特点：UA随机化与头部混淆：随机选择UA。动态生成头部键值对，打乱头部顺序。随机载荷：随机内容作为消息内容，支持动态占位符替换，生成随机或攻击特定载荷。握手请求模拟。WebSocket握手阶段伪装成正常HTTP请求，包含合法的 `Upgrade: websocket` 和 `Connection: Upgrade` 头部。消息间隔控制。通过动态控制消息发送间隔，模拟人类操作节奏（如每秒1-5条），规避频次检测。3.4.7 PostAttackPostAttack强制使用HTTP POST方法。图 3.11 使用HTTP POST方法其具备如下特点：Content-Type动态管理：动态设置Content-Type，（如 `application/json` 或 `multipart/form-data`），匹配目标接口规范。Cookie回填机制：解析响应中的 `Set-Cookie`，自动管理会话。UA深度伪装与头部顺序随机化：从预定义UA池中随机选择UA，并通过随机轮换来模拟多浏览器版本。遍历头部映射，打乱注入顺序（如 `Header-Order: Host,User-Agent,Accept`），绕过基于固定顺序的规则。3.4.8 CookieAttackCookieAttack攻击方式在BrowserAttack攻击方式的基础上增加了对Cookie的处理流程，实现了Cookie自动化管理，解析响应中的`Set-Cookie`并存入变量，后续请求自动携带合法Cookie。图 3.12 Cookie处理流程该攻击方式具备如下特点：调用浏览器：调用隐藏窗口的Chrome进程（`C:\Windows\Temp\chrome-win\pp.exe`），通过浏览器引擎发起请求，流量特征与真实用户完全一致。动态下发攻击指令（如URL、速率参数)。智能占位符替换：动态替换URL中的`%%RAND%%`等标记，生成随机路径（如`/api/%%RAND%%/submit`）。自适应重试机制：对`429`（Too Many Requests）和`405`（Method Not Allowed）触发705ms休眠，模拟人工操作。四总结通常情况下，DDoS型僵尸网络家族多聚集于Linux/IoT平台，HTTPBot僵尸网络家族则瞄准了Windows平台。短短数月，该家族已成长为Windows平台不容忽视的一大威胁源，并通过协议层深度仿真，模拟合法浏览器行为，绕过基于协议完整性的防御；通过随机化URL路径和Cookie回填机制，持续占用服务器会话资源，而非依赖流量压制；并且能够根据目标响应自动调整攻击节奏，规避基于请求速率的阈值检测，实现“低流量、高杀伤”。使得现有基于规则匹配的防御系统面临大规模失效风险。附录 IOC463f7db6bf1116954fceaa3141065592726eaa2e9185ed5f36d92cfbad7adbe8jjj.jjycc.cc104.233.144.17:55888 关于绿盟科技伏影实验室  专注于安全威胁监测与对抗技术的研究，涵盖APT高级威胁、Botnet、DDoS对抗、流行服务漏洞利用、黑灰产业链威胁及数字资产等新兴领域。通过掌握现有网络威胁，识别并追踪新型威胁，精准溯源与反制威胁，降低风险影响，为威胁对抗提供有力决策支持 绿盟威胁情报中心绿盟威胁情报中心（NSFOCUS Threat Intelligence center, NTI）是绿盟科技为落实智慧安全3.0战略，促进网络空间安全生态建设和威胁情报应用，增强客户攻防对抗能力而组建的专业性安全研究组织。其依托公司专业的安全团队和强大的安全研究能力，对全球网络安全威胁和态势进行持续观察和分析，以威胁情报的生产、运营、应用等能力及关键技术作为核心研究内容，推出了绿盟威胁情报平台以及一系列集成威胁情报的新一代安全产品，为用户提供可操作的情报数据、专业的情报服务和高效的威胁防护能力，帮助用户更好地了解和应对各类网络威胁。（绿盟威胁情报中心官网：https://nti.nsfocus.com/）