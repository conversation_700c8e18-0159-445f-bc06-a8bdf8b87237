#!/usr/bin/env python3
"""
将微信Cookie从JSON格式转换为Netscape格式供ArchiveBox使用
"""

import json
import os
import re

def parse_json_cookie_file(cookie_file):
    """解析JSON格式的cookie文件"""
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 如果内容不是完整的JSON，尝试修复
        if not content.startswith('{'):
            # 添加大括号包装
            content = '{' + content + '}'
        
        # 移除末尾可能的逗号
        content = re.sub(r',\s*}', '}', content)
        
        # 解析JSON
        cookies = json.loads(content)
        return cookies
        
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        # 尝试逐行解析
        return parse_line_by_line(cookie_file)
    except Exception as e:
        print(f"读取cookie文件失败: {e}")
        return {}

def parse_line_by_line(cookie_file):
    """逐行解析cookie文件"""
    cookies = {}
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                # 匹配格式: "key": "value",
                match = re.match(r'"([^"]+)":\s*"([^"]*)"', line)
                if match:
                    key, value = match.groups()
                    cookies[key] = value
                    
    except Exception as e:
        print(f"逐行解析失败: {e}")
    
    return cookies

def convert_to_netscape_format(cookies, domain="mp.weixin.qq.com"):
    """转换为Netscape cookie格式"""
    netscape_lines = [
        "# Netscape HTTP Cookie File",
        "# This is a generated file! Do not edit.",
        ""
    ]
    
    for name, value in cookies.items():
        if not name or not value:
            continue
            
        # Netscape格式: domain, domain_specified, path, secure, expires, name, value
        # domain_specified: TRUE表示精确匹配域名
        # path: cookie的路径
        # secure: FALSE表示HTTP和HTTPS都可以
        # expires: 0表示会话cookie
        line = f"{domain}\tTRUE\t/\tFALSE\t0\t{name}\t{value}"
        netscape_lines.append(line)
    
    return "\n".join(netscape_lines)

def convert_wx_cookie():
    """转换微信cookie"""
    print("=== 微信Cookie格式转换 ===")
    
    # 输入和输出文件
    input_file = "WX_Cookie.txt"
    output_file = "/home/<USER>/docker_archivebox/Archive_box_data/WX_Cookie_Netscape.txt"
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    try:
        # 解析JSON格式的cookie
        print(f"📖 读取cookie文件: {input_file}")
        cookies = parse_json_cookie_file(input_file)
        
        if not cookies:
            print("❌ 未能解析到任何cookie")
            return False
        
        print(f"✓ 解析到 {len(cookies)} 个cookie")
        
        # 显示关键cookie
        key_cookies = ['wxuin', 'data_ticket', 'slave_sid', 'slave_user']
        for key in key_cookies:
            if key in cookies:
                value = cookies[key]
                if len(value) > 20:
                    print(f"  {key}: {value[:10]}...{value[-10:]}")
                else:
                    print(f"  {key}: {value}")
        
        # 转换为Netscape格式
        print("🔄 转换为Netscape格式...")
        netscape_content = convert_to_netscape_format(cookies)
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        os.makedirs(output_dir, exist_ok=True)
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(netscape_content)
        
        print(f"✓ 转换完成，输出文件: {output_file}")
        print(f"📊 文件大小: {len(netscape_content)} 字符")
        
        # 验证输出文件
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"✓ 验证成功，共 {len(lines)} 行")
            
            # 显示前几行作为示例
            print("📝 文件内容示例:")
            for i, line in enumerate(lines[:8]):
                print(f"  {i+1}: {line.rstrip()}")
            
            return True
        else:
            print("❌ 输出文件创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False

def update_archivebox_config(cookie_file):
    """更新ArchiveBox配置文件中的cookie路径"""
    config_file = "/home/<USER>/docker_archivebox/Archive_box_data/ArchiveBox.conf"
    
    if not os.path.exists(config_file):
        print(f"⚠️ ArchiveBox配置文件不存在: {config_file}")
        return False
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新COOKIES_FILE路径
        lines = content.split('\n')
        updated_lines = []
        updated = False
        
        for line in lines:
            if line.startswith('COOKIES_FILE = '):
                updated_lines.append(f'COOKIES_FILE = {cookie_file}')
                updated = True
                print(f"✓ 更新cookie文件路径: {cookie_file}")
            else:
                updated_lines.append(line)
        
        if not updated:
            # 如果没找到COOKIES_FILE配置，添加一个
            updated_lines.append(f'COOKIES_FILE = {cookie_file}')
            print(f"✓ 添加cookie文件配置: {cookie_file}")
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(updated_lines))
        
        print("✓ ArchiveBox配置文件更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("微信Cookie转换工具")
    print("=" * 50)
    
    # 转换cookie格式
    if convert_wx_cookie():
        # 更新ArchiveBox配置
        netscape_cookie_file = "/home/<USER>/docker_archivebox/Archive_box_data/WX_Cookie_Netscape.txt"
        update_archivebox_config(netscape_cookie_file)
        
        print("\n" + "=" * 50)
        print("✅ Cookie转换完成！")
        print("\n下一步:")
        print("1. 重新部署ArchiveBox配置")
        print("2. 测试微信公众号链接归档")
        print("3. 检查归档日志确认cookie生效")
    else:
        print("\n❌ Cookie转换失败")

if __name__ == "__main__":
    main()
