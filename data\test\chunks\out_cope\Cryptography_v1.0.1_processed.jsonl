{"chunk_id": "line-1", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 4", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1", "line_number": 1, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-2", "filename": "Cryptography_v1.0.1_processed.txt", "content": "INTRODUCTION", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-2", "line_number": 2, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-3", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The purpose of this chapter is to explain the various aspects of cryptography which we feel", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-3", "line_number": 3, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-4", "filename": "Cryptography_v1.0.1_processed.txt", "content": "should be known to an expert in cyber-security. The presentation is at a level needed for an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-4", "line_number": 4, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-5", "filename": "Cryptography_v1.0.1_processed.txt", "content": "instructor in a module in cryptography; so they can select the depth needed in each topic.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-5", "line_number": 5, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-6", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Whilst not all experts in cyber-security need be aware of all the technical aspects mentioned", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-6", "line_number": 6, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-7", "filename": "Cryptography_v1.0.1_processed.txt", "content": "below, we feel they should be aware of all the overall topics and have an intuitive grasp as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-7", "line_number": 7, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-8", "filename": "Cryptography_v1.0.1_processed.txt", "content": "to what they mean, and what services they can provide. Our focus is mainly on primitives,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-8", "line_number": 8, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-9", "filename": "Cryptography_v1.0.1_processed.txt", "content": "schemes and protocols which are widely used, or which are suitably well studied that they", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-9", "line_number": 9, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-10", "filename": "Cryptography_v1.0.1_processed.txt", "content": "could be used (or are currently being used) in speciﬁc application domains.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-10", "line_number": 10, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-11", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Cryptography by its very nature is one of the more mathematical aspects of cyber-security;", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-11", "line_number": 11, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-12", "filename": "Cryptography_v1.0.1_processed.txt", "content": "thus this chapter contains a lot more mathematics than one has in some of the other chap-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-12", "line_number": 12, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-13", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ters. The overall presentation assumes a basic knowledge of either ﬁrst-year undergraduate", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-13", "line_number": 13, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-14", "filename": "Cryptography_v1.0.1_processed.txt", "content": "mathematics, or that found in a discrete mathematics course of an undergraduate Computer", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-14", "line_number": 14, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-15", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Science degree.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-15", "line_number": 15, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-16", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The chapter is structured as follows: After a quick recap on some basic mathematical notation", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-16", "line_number": 16, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-17", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(Section 1), we then give an introduction to how security is deﬁned in modern cryptography.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-17", "line_number": 17, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-18", "filename": "Cryptography_v1.0.1_processed.txt", "content": "This section (Section 2) forms the basis of our discussions in the other sections. Section", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-18", "line_number": 18, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-19", "filename": "Cryptography_v1.0.1_processed.txt", "content": "3 discusses information theoretic constructions, in particular the one-time pad, and secret", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-19", "line_number": 19, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-20", "filename": "Cryptography_v1.0.1_processed.txt", "content": "sharing. Sections 4 and 5 then detail modern symmetric cryptography; by discussing primitives", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-20", "line_number": 20, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-21", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(such as block cipher constructions) and then speciﬁc schemes (such as modes-of-operation).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-21", "line_number": 21, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-22", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Then in Sections 6 and 7 we discuss the standard methodologies for performing public", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-22", "line_number": 22, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-23", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key encryption and public key signatures, respectively. Then in Section 8 we discuss how", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-23", "line_number": 23, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-24", "filename": "Cryptography_v1.0.1_processed.txt", "content": "these basic schemes are used in various standard protocols; such as for authentication", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-24", "line_number": 24, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-25", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and key agreement. All of the sections, up to and including Section 8, focus exclusively on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-25", "line_number": 25, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-26", "filename": "Cryptography_v1.0.1_processed.txt", "content": "constructions which have widespread deployment.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-26", "line_number": 26, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-27", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Section 9 begins our treatment of constructions and protocols which are less widely used;", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-27", "line_number": 27, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-28", "filename": "Cryptography_v1.0.1_processed.txt", "content": "but which do have a number of niche applications. These sections are included to enable the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-28", "line_number": 28, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-29", "filename": "Cryptography_v1.0.1_processed.txt", "content": "instructor to prepare students for the wider applications of the cryptography that they may", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-29", "line_number": 29, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-30", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encounter as niche applications become more mainstream. In particular, Section 9 covers", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-30", "line_number": 30, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-31", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Oblivious Transfer, Zero-Knowledge, and Multi-Party Computation. Section 10 covers public", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-31", "line_number": 31, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-32", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key schemes with special properties, such as group signatures, identity-based encryption and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-32", "line_number": 32, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-33", "filename": "Cryptography_v1.0.1_processed.txt", "content": "homomorphic encryption.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-33", "line_number": 33, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-34", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The chapter assumes the reader wants to use cryptographic constructs in order to build", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-34", "line_number": 34, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-35", "filename": "Cryptography_v1.0.1_processed.txt", "content": "secure systems, it is not meant to introduce the reader to attack techniques on cryptographic", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-35", "line_number": 35, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-36", "filename": "Cryptography_v1.0.1_processed.txt", "content": "primitives. Indeed, all primitives here can be assumed to have been selected to avoid speciﬁc", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-36", "line_number": 36, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-37", "filename": "Cryptography_v1.0.1_processed.txt", "content": "attack vectors, or key lengths chosen to avoid them. Further details on this can be found in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-37", "line_number": 37, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-38", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the regular European Key Size and Algorithms report, of which the most up to date version is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-38", "line_number": 38, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-39", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[1].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-39", "line_number": 39, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-40", "filename": "Cryptography_v1.0.1_processed.txt", "content": "For a similar reason we do not include a discussion of historical aspects of cryptography, or", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-40", "line_number": 40, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-41", "filename": "Cryptography_v1.0.1_processed.txt", "content": "historical ciphers such as Caesar, Vigen`ere or Enigma. These are at best toy examples, and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-41", "line_number": 41, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-42", "filename": "Cryptography_v1.0.1_processed.txt", "content": "so have no place in a such a body of knowledge. They are best left to puzzle books. However", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-42", "line_number": 42, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-43", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the interested reader is referred to [2].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-43", "line_number": 43, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-44", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-44", "line_number": 44, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-45", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 5", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-45", "line_number": 45, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-46", "filename": "Cryptography_v1.0.1_processed.txt", "content": "CONTENT", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-46", "line_number": 46, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-48", "filename": "Cryptography_v1.0.1_processed.txt", "content": "MATHEMATICS", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-48", "line_number": 48, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-49", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[3, c8–c9,App B][4, c1–c5]", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-49", "line_number": 49, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-50", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Cryptography is inherently mathematical in nature, the reader is therefore going to be assumed", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-50", "line_number": 50, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-51", "filename": "Cryptography_v1.0.1_processed.txt", "content": "to be familiar with a number of concepts. A good textbook to cover the basics needed, and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-51", "line_number": 51, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-52", "filename": "Cryptography_v1.0.1_processed.txt", "content": "more, is that of Galbraith [5].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-52", "line_number": 52, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-53", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Before proceeding we will set up some notation: The ring of integers is denoted by Z, whilst", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-53", "line_number": 53, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-54", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the ﬁelds of rational, real and complex numbers are denoted by Q, R and C. The ring of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-54", "line_number": 54, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-55", "filename": "Cryptography_v1.0.1_processed.txt", "content": "integers modulo N will be denoted by Z/NZ, when N is a prime p this is a ﬁnite ﬁeld often", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-55", "line_number": 55, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-56", "filename": "Cryptography_v1.0.1_processed.txt", "content": "denoted by Fp. The set of invertible elements will be written (Z/NZ)∗or F∗", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-56", "line_number": 56, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-57", "filename": "Cryptography_v1.0.1_processed.txt", "content": "p. An RSA modulus", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-57", "line_number": 57, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-58", "filename": "Cryptography_v1.0.1_processed.txt", "content": "N will denote an integer N, which is the product of two (large) prime factors N = p · q.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-58", "line_number": 58, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-59", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Finite abelian groups of prime order q are also a basic construct. These are either written", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-59", "line_number": 59, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-60", "filename": "Cryptography_v1.0.1_processed.txt", "content": "multiplicatively, in which case an element is written as gx for some x ∈Z/qZ; when written", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-60", "line_number": 60, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-61", "filename": "Cryptography_v1.0.1_processed.txt", "content": "additively an element can be written as [x] · P. The element g (in the multiplicative case) and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-61", "line_number": 61, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-62", "filename": "Cryptography_v1.0.1_processed.txt", "content": "P (in the additive case) is called the generator.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-62", "line_number": 62, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-63", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The standard example of ﬁnite abelian groups of prime order used in cryptography are elliptic", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-63", "line_number": 63, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-64", "filename": "Cryptography_v1.0.1_processed.txt", "content": "curves. An elliptic curve over a ﬁnite ﬁeld Fp is the set of solutions (X, Y ) to an equation of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-64", "line_number": 64, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-65", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the form", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-65", "line_number": 65, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-66", "filename": "Cryptography_v1.0.1_processed.txt", "content": "E : Y 2 = X3 + A · X + B", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-66", "line_number": 66, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-67", "filename": "Cryptography_v1.0.1_processed.txt", "content": "where A and B are ﬁxed constants. Such a set of solutions, plus a special point at inﬁnity", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-67", "line_number": 67, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-68", "filename": "Cryptography_v1.0.1_processed.txt", "content": "denoted by O, form a ﬁnite abelian group denoted by E(Fp). The group law is a classic law", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-68", "line_number": 68, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-69", "filename": "Cryptography_v1.0.1_processed.txt", "content": "dating back to Newton and Fermat called the chord-tangent process. When A and B are", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-69", "line_number": 69, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-70", "filename": "Cryptography_v1.0.1_processed.txt", "content": "selected carefully one can ensure that the size of E(Fp) is a prime q. This will be important", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-70", "line_number": 70, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-71", "filename": "Cryptography_v1.0.1_processed.txt", "content": "later in Section 2.3 to ensure the discrete logarithm problem in the elliptic curve is hard.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-71", "line_number": 71, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-72", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Some cryptographic schemes make use of lattices which are discrete subgroups of the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-72", "line_number": 72, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-73", "filename": "Cryptography_v1.0.1_processed.txt", "content": "subgroups of Rn. A lattice can be deﬁned by a generating matrix B ∈Rn·m, where each", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-73", "line_number": 73, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-74", "filename": "Cryptography_v1.0.1_processed.txt", "content": "column of B forms a basis element. The lattice is then the set of elements of the form", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-74", "line_number": 74, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-75", "filename": "Cryptography_v1.0.1_processed.txt", "content": "y = B · x where x ranges over all elements in Zm. Since a lattice is discrete it has a well-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-75", "line_number": 75, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-76", "filename": "Cryptography_v1.0.1_processed.txt", "content": "deﬁned length of the shortest non-zero vector. In Section 2.3 we note that ﬁnding this shortest", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-76", "line_number": 76, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-77", "filename": "Cryptography_v1.0.1_processed.txt", "content": "non-zero vector is a hard computational problem.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-77", "line_number": 77, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-78", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Sampling a uniformly random element from a set A will be denoted by x ←A. If the set A", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-78", "line_number": 78, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-79", "filename": "Cryptography_v1.0.1_processed.txt", "content": "consists of a single element a we will write this as the assignment x ←a; with the equality", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-79", "line_number": 79, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-80", "filename": "Cryptography_v1.0.1_processed.txt", "content": "symbol = being reserved for equalities as opposed to assignments. If A is a randomized", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-80", "line_number": 80, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-81", "filename": "Cryptography_v1.0.1_processed.txt", "content": "algorithm, then we write x ←A(y; r) for the assignment to x of the output of running A on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-81", "line_number": 81, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-82", "filename": "Cryptography_v1.0.1_processed.txt", "content": "input y with random coins r.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-82", "line_number": 82, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-83", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 4", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-83", "line_number": 83, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-84", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 6", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-84", "line_number": 84, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-86", "filename": "Cryptography_v1.0.1_processed.txt", "content": "CRYPTOGRAPHIC SECURITY MODELS", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-86", "line_number": 86, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-87", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[3, c1–c4][4, c11]", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-87", "line_number": 87, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-88", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Modern cryptography has adopted a methodology of ‘Provable Security’ to deﬁne and under-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-88", "line_number": 88, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-89", "filename": "Cryptography_v1.0.1_processed.txt", "content": "stand the security of cryptographic constructions. The basic design procedure is to deﬁne the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-89", "line_number": 89, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-90", "filename": "Cryptography_v1.0.1_processed.txt", "content": "syntax for a cryptographic scheme. This gives the input and output behaviours of the algo-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-90", "line_number": 90, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-91", "filename": "Cryptography_v1.0.1_processed.txt", "content": "rithms making up the scheme and deﬁnes correctness. Then a security model is presented", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-91", "line_number": 91, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-92", "filename": "Cryptography_v1.0.1_processed.txt", "content": "which deﬁnes what security goals are expected of the given scheme. Then, given a speciﬁc", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-92", "line_number": 92, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-93", "filename": "Cryptography_v1.0.1_processed.txt", "content": "instantiation which meets the given syntax, a formal security proof for the instantiation is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-93", "line_number": 93, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-94", "filename": "Cryptography_v1.0.1_processed.txt", "content": "given relative to some known hard problems.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-94", "line_number": 94, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-95", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The security proof is not an absolute guarantee of security. It is a proof that the given", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-95", "line_number": 95, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-96", "filename": "Cryptography_v1.0.1_processed.txt", "content": "instantiation, when implemented correctly, satisﬁes the given security model assuming some", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-96", "line_number": 96, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-97", "filename": "Cryptography_v1.0.1_processed.txt", "content": "hard problems are indeed hard. Thus, if an attacker can perform operations which are outside", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-97", "line_number": 97, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-98", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the model, or manages to break the underlying hard problem, then the proof is worthless.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-98", "line_number": 98, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-99", "filename": "Cryptography_v1.0.1_processed.txt", "content": "However, a security proof, with respect to well studied models and hard problems, can give", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-99", "line_number": 99, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-100", "filename": "Cryptography_v1.0.1_processed.txt", "content": "strong guarantees that the given construction has no fundamental weaknesses.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-100", "line_number": 100, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-101", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In the next subsections we shall go into these ideas in more detail, and then give some", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-101", "line_number": 101, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-102", "filename": "Cryptography_v1.0.1_processed.txt", "content": "examples of security statements; further details of the syntax and security deﬁnitions can be", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-102", "line_number": 102, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-103", "filename": "Cryptography_v1.0.1_processed.txt", "content": "found in [6, 7]. At a high level the reason for these deﬁnitions is that the intuitive notion of a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-103", "line_number": 103, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-104", "filename": "Cryptography_v1.0.1_processed.txt", "content": "cryptographic construction being secure is not sufﬁcient enough. For example the natural", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-104", "line_number": 104, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-105", "filename": "Cryptography_v1.0.1_processed.txt", "content": "deﬁnition for encryption security is that an attacker should be unable to recover the decryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-105", "line_number": 105, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-106", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key, or the attacker should be unable to recover a message encrypted under one ciphertext.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-106", "line_number": 106, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-107", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Whilst these ideas are necessary for any secure scheme they are not sufﬁcient. We need to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-107", "line_number": 107, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-108", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protect against an attacker aims for ﬁnd some information about an encrypted message, when", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-108", "line_number": 108, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-109", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the attacker is able to mount chosen plaintext and chosen ciphertext attacks on a legitimate", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-109", "line_number": 109, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-110", "filename": "Cryptography_v1.0.1_processed.txt", "content": "user.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-110", "line_number": 110, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-111", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-111", "line_number": 111, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-112", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Syntax of Basic Schemes", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-112", "line_number": 112, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-113", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The syntax of a cryptographic scheme is deﬁned by the algorithms which make up the scheme,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-113", "line_number": 113, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-114", "filename": "Cryptography_v1.0.1_processed.txt", "content": "as well as a correctness deﬁnition. The correctness deﬁnition gives what behaviour one can", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-114", "line_number": 114, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-115", "filename": "Cryptography_v1.0.1_processed.txt", "content": "expect when there is no adversarial behaviour. For example, a symmetric encryption scheme", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-115", "line_number": 115, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-116", "filename": "Cryptography_v1.0.1_processed.txt", "content": "is deﬁned by three algorithms (KeyGen, Enc, Dec). The KeyGen algorithm is a probabilistic", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-116", "line_number": 116, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-117", "filename": "Cryptography_v1.0.1_processed.txt", "content": "algorithm which outputs a symmetric key k ←KeyGen(); Enc is a probabilistic algorithm which", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-117", "line_number": 117, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-118", "filename": "Cryptography_v1.0.1_processed.txt", "content": "takes a message m ∈M, some randomness r ∈R and a key and returns a c ←Enc(m, k; r) ∈", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-118", "line_number": 118, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-119", "filename": "Cryptography_v1.0.1_processed.txt", "content": "C; whilst Dec is (usually) a deterministic algorithm which takes a ciphertext and a key and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-119", "line_number": 119, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-120", "filename": "Cryptography_v1.0.1_processed.txt", "content": "returns the underlying plaintext. The correctness deﬁnition is:", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-120", "line_number": 120, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-121", "filename": "Cryptography_v1.0.1_processed.txt", "content": "∀k ←KeyGen(), r ←R, m ←M, Dec(Enc(m, k; r), k) = m.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-121", "line_number": 121, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-122", "filename": "Cryptography_v1.0.1_processed.txt", "content": "For public key encryption schemes the deﬁnitions are similar, but now KeyGen() outputs key", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-122", "line_number": 122, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-123", "filename": "Cryptography_v1.0.1_processed.txt", "content": "pairs and the correctness deﬁnition becomes:", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-123", "line_number": 123, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-124", "filename": "Cryptography_v1.0.1_processed.txt", "content": "∀(pk, sk) ←KeyGen(), r ←R, m ←M, Dec(Enc(m, pk; r), sk) = m.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-124", "line_number": 124, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-125", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The equivalent constructions for authentication mechanisms are Message Authentication", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-125", "line_number": 125, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-126", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Codes (or MACs) in the symmetric key setting, and digital signatures schemes in the public key", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-126", "line_number": 126, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-127", "filename": "Cryptography_v1.0.1_processed.txt", "content": "setting. A MAC scheme is given by a triple of algorithms (KeyGen, MAC, Verify), where the MAC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-127", "line_number": 127, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-128", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 5", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-128", "line_number": 128, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-129", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 7", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-129", "line_number": 129, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-130", "filename": "Cryptography_v1.0.1_processed.txt", "content": "function outputs a tag given a message and a key (and possibly some random coins), and the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-130", "line_number": 130, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-131", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Verify function checks the message, tag and key are consistent. A signature scheme is given", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-131", "line_number": 131, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-132", "filename": "Cryptography_v1.0.1_processed.txt", "content": "by a similar triple (KeyGen, Sign, Verify), where now the tag produced is called a ‘signature’.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-132", "line_number": 132, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-133", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Thus the correctness deﬁnitions for these constructions are as follows", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-133", "line_number": 133, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-134", "filename": "Cryptography_v1.0.1_processed.txt", "content": "k ←KeyGen(), r ←R, m ←M, Verify(m, MAC(m, k; r), k) = true.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-134", "line_number": 134, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-135", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-135", "line_number": 135, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-136", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(pk, sk) ←KeyGen(), r ←R, m ←M, Verify(m, Sign(m, sk; r), pk) = true.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-136", "line_number": 136, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-137", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Note, that for deterministic MACs the veriﬁcation algorithm is usually just to recompute the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-137", "line_number": 137, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-138", "filename": "Cryptography_v1.0.1_processed.txt", "content": "MAC tag MAC(m, k), and then check it was what was received.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-138", "line_number": 138, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-139", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2.2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-139", "line_number": 139, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-140", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Basic Security Deﬁnitions", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-140", "line_number": 140, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-141", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A security deﬁnition is usually given in the context of an attacker’s security goal, followed by", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-141", "line_number": 141, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-142", "filename": "Cryptography_v1.0.1_processed.txt", "content": "their capabilities. So, for example, a naive security goal for encryption could be to recover the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-142", "line_number": 142, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-143", "filename": "Cryptography_v1.0.1_processed.txt", "content": "underlying plaintext, so-called One-Way (or OW) security. This process of an attacker trying to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-143", "line_number": 143, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-144", "filename": "Cryptography_v1.0.1_processed.txt", "content": "obtain a speciﬁc goal is called a security game, with the attacker winning the game, if they", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-144", "line_number": 144, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-145", "filename": "Cryptography_v1.0.1_processed.txt", "content": "can break this security goal with greater probability than random guessing. This advantage in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-145", "line_number": 145, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-146", "filename": "Cryptography_v1.0.1_processed.txt", "content": "probability over random guessing is called the adversary’s advantage. The capabilities are", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-146", "line_number": 146, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-147", "filename": "Cryptography_v1.0.1_processed.txt", "content": "expressed in terms of what oracles, or functions, we give the adversary access to. So, for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-147", "line_number": 147, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-148", "filename": "Cryptography_v1.0.1_processed.txt", "content": "example, in a naive security game for encryption we may give the adversary no oracles at all,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-148", "line_number": 148, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-149", "filename": "Cryptography_v1.0.1_processed.txt", "content": "producing a so-called Passive Attack (or PASS) capability.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-149", "line_number": 149, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-150", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The attacker is modelled as an arbitrary algorithm, or Turing machine, A, and if we give the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-150", "line_number": 150, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-151", "filename": "Cryptography_v1.0.1_processed.txt", "content": "adversary access to oracles then we write these as subscripts AO. In our naive security game", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-151", "line_number": 151, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-152", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(called OW-PASS) the adversary has no oracles and its goal is simply to recover the message", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-152", "line_number": 152, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-153", "filename": "Cryptography_v1.0.1_processed.txt", "content": "underlying a given ciphertext. The precise deﬁnition is given in Figure 1, where AdvOW−PASS(A, t)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-153", "line_number": 153, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-154", "filename": "Cryptography_v1.0.1_processed.txt", "content": "denote the advantage over a random guess that a given adversary has after running for time", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-154", "line_number": 154, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-155", "filename": "Cryptography_v1.0.1_processed.txt", "content": "t. We say that a given construction is secure in the given model (which our naive example", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-155", "line_number": 155, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-156", "filename": "Cryptography_v1.0.1_processed.txt", "content": "would be named OW-PASS), if the above advantage is negligible for all probabilistic polynomial", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-156", "line_number": 156, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-157", "filename": "Cryptography_v1.0.1_processed.txt", "content": "time adversaries A. Here, negligible and polynomial time are measured in terms of a security", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-157", "line_number": 157, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-158", "filename": "Cryptography_v1.0.1_processed.txt", "content": "parameter (which one can think of as the key size). Note, for OW-PASS this assumes that the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-158", "line_number": 158, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-159", "filename": "Cryptography_v1.0.1_processed.txt", "content": "message space is not bigger than the space of all possible keys. Also note, that this is an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-159", "line_number": 159, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-160", "filename": "Cryptography_v1.0.1_processed.txt", "content": "asymptotic deﬁnition, which in the context of schemes with ﬁxed key size, makes no sense.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-160", "line_number": 160, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-161", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In such situations we require that (t/Adv) is greater than some given concrete bound such as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-161", "line_number": 161, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-162", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2128, since it is believed that performing an algorithm requiring 2128 steps is infeasible even for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-162", "line_number": 162, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-163", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a nation-state adversary.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-163", "line_number": 163, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-164", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In the context of encryption (both symmetric and public key) the above naive security goal is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-164", "line_number": 164, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-165", "filename": "Cryptography_v1.0.1_processed.txt", "content": "not seen as being suitable for real applications. Instead, the security goal of Indistinguishable", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-165", "line_number": 165, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-166", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encryptions (or IND) is usually used. This asks the adversary to ﬁrst come up with two", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-166", "line_number": 166, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-167", "filename": "Cryptography_v1.0.1_processed.txt", "content": "plaintexts, of equal length, and then the challenger (or environment) encrypts one of them", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-167", "line_number": 167, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-168", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and gives the resulting challenge to the adversary. The adversary’s goal is then to determine", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-168", "line_number": 168, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-169", "filename": "Cryptography_v1.0.1_processed.txt", "content": "which plaintext was encrypted. In the context of a passive attack this gives an advantage", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-169", "line_number": 169, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-170", "filename": "Cryptography_v1.0.1_processed.txt", "content": "statement as given in the second part of Figure 1, where the two stages of the adversary are", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-170", "line_number": 170, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-171", "filename": "Cryptography_v1.0.1_processed.txt", "content": "given by A1 and A2.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-171", "line_number": 171, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-172", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In terms of encryption, the above passive attack is almost always not sufﬁcient in terms", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-172", "line_number": 172, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-173", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of capturing real-world adversarial capabilities, since real systems almost always give the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-173", "line_number": 173, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-174", "filename": "Cryptography_v1.0.1_processed.txt", "content": "attacker additional attack vectors. Thus two other (increasingly strong) attack capabilities", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-174", "line_number": 174, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-175", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 6", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-175", "line_number": 175, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-176", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 9", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-176", "line_number": 176, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-177", "filename": "Cryptography_v1.0.1_processed.txt", "content": "message/tag (resp. message/signature) pair which passes the veriﬁcation algorithm, a so-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-177", "line_number": 177, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-178", "filename": "Cryptography_v1.0.1_processed.txt", "content": "called Universal Forgery (or UF) attack. We make no assumption about whether the message", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-178", "line_number": 178, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-179", "filename": "Cryptography_v1.0.1_processed.txt", "content": "has any meaning, indeed, the attacker wins if he is able to create a signature on any bit-string.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-179", "line_number": 179, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-180", "filename": "Cryptography_v1.0.1_processed.txt", "content": "If the adversary is given no oracles then he is said to be mounting a passive attack, whilst", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-180", "line_number": 180, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-181", "filename": "Cryptography_v1.0.1_processed.txt", "content": "if the adversary is given a tag generation (resp. signing oracle) he is said to be executing a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-181", "line_number": 181, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-182", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Chosen Message Attack (CMA). In the latter case the ﬁnal forgery must not be one of the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-182", "line_number": 182, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-183", "filename": "Cryptography_v1.0.1_processed.txt", "content": "outputs of the given oracle. In the case of MAC security, one may also give the adversary", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-183", "line_number": 183, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-184", "filename": "Cryptography_v1.0.1_processed.txt", "content": "access to a tag veriﬁcation oracle. However, for deterministic MACs this is implied by the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-184", "line_number": 184, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-185", "filename": "Cryptography_v1.0.1_processed.txt", "content": "CMA capability and is hence usually dropped, since veriﬁcation only involves re-computing", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-185", "line_number": 185, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-186", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the MAC.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-186", "line_number": 186, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-187", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Again we deﬁne an advantage and require this to be negligible in the security parameter. For", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-187", "line_number": 187, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-188", "filename": "Cryptography_v1.0.1_processed.txt", "content": "digital signatures the advantage for the UF-CMA game is given by the fourth equation in Figure", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-188", "line_number": 188, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-190", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-190", "line_number": 190, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-191", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Hard Problems", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-191", "line_number": 191, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-192", "filename": "Cryptography_v1.0.1_processed.txt", "content": "As explained above, security proofs are always relative to some hard problems. These hard", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-192", "line_number": 192, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-193", "filename": "Cryptography_v1.0.1_processed.txt", "content": "problems are often called cryptographic primitives, since they are the smallest atomic object", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-193", "line_number": 193, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-194", "filename": "Cryptography_v1.0.1_processed.txt", "content": "from which cryptographic schemes and protocols can be built. Such cryptographic primitives", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-194", "line_number": 194, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-195", "filename": "Cryptography_v1.0.1_processed.txt", "content": "come in two ﬂavours: Either they are keyed complexity theoretic deﬁnitions of functions, or", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-195", "line_number": 195, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-196", "filename": "Cryptography_v1.0.1_processed.txt", "content": "they are mathematical hard problems.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-196", "line_number": 196, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-197", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In the former case one could consider a function Fk(·) : D −→C selected from a function", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-197", "line_number": 197, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-198", "filename": "Cryptography_v1.0.1_processed.txt", "content": "family {Fk} and indexed by some index k (thought of as a key of varying length). One can", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-198", "line_number": 198, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-199", "filename": "Cryptography_v1.0.1_processed.txt", "content": "then ask whether the function selected is indistinguishable (by a probabilistic polynomial", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-199", "line_number": 199, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-200", "filename": "Cryptography_v1.0.1_processed.txt", "content": "time algorithm A which has oracle access to the function) from a uniform random function", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-200", "line_number": 200, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-201", "filename": "Cryptography_v1.0.1_processed.txt", "content": "from D to C. If such an assumption holds, then we say the function family deﬁnes a (keyed)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-201", "line_number": 201, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-202", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Pseudo-Random Function (PRF). In the case when the domain D is equal to the co-domain C", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-202", "line_number": 202, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-203", "filename": "Cryptography_v1.0.1_processed.txt", "content": "we can ask whether the function is indistinguishable from a randomly chosen permutation, in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-203", "line_number": 203, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-204", "filename": "Cryptography_v1.0.1_processed.txt", "content": "which case we say the family deﬁnes a (keyed) Pseudo-Random Permutation (PRP).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-204", "line_number": 204, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-205", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In the case of a block cipher, such as AES (see later), where one has C = D = {0, 1}128, it is a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-205", "line_number": 205, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-206", "filename": "Cryptography_v1.0.1_processed.txt", "content": "basic assumption that the AES function family (indexed by the key k) is a Pseudo-Random", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-206", "line_number": 206, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-207", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Permutation.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-207", "line_number": 207, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-208", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In the case of mathematical hard problems we have a similar formulation, but the deﬁnitions", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-208", "line_number": 208, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-209", "filename": "Cryptography_v1.0.1_processed.txt", "content": "are often more intuitive. For example, one can ask the question whether a given RSA modulus", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-209", "line_number": 209, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-210", "filename": "Cryptography_v1.0.1_processed.txt", "content": "N = p · q can be factored into its prime components p and q, the so-called factoring problem.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-210", "line_number": 210, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-211", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The RSA group Z/NZ deﬁnes a ﬁnite abelian group of unknown order (the order is known", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-211", "line_number": 211, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-212", "filename": "Cryptography_v1.0.1_processed.txt", "content": "to the person who created N), ﬁnding the order of this group is equivalent to factoring N.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-212", "line_number": 212, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-213", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The RSA function x −→xe (mod N) is believed to be hard to invert, leading to the so-called", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-213", "line_number": 213, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-214", "filename": "Cryptography_v1.0.1_processed.txt", "content": "RSA-inversion problem of, given y ∈(Z/NZ)∗, ﬁnding x such that xe = y (mod N). It is known", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-214", "line_number": 214, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-215", "filename": "Cryptography_v1.0.1_processed.txt", "content": "that the function can easily be inverted if the modulus N can be factored, but it is unknown if", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-215", "line_number": 215, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-216", "filename": "Cryptography_v1.0.1_processed.txt", "content": "inverting the function implies N can be factored. Thus we have a situation where one problem", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-216", "line_number": 216, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-217", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(factoring) seems to be harder to solve than another problem (the RSA problem). However,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-217", "line_number": 217, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-218", "filename": "Cryptography_v1.0.1_processed.txt", "content": "in practice, we assume that both problems are hard, given appropriately chosen parameters.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-218", "line_number": 218, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-219", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Details on the best method to factor large numbers, the so-called Number Field Sieve, can be", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-219", "line_number": 219, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-220", "filename": "Cryptography_v1.0.1_processed.txt", "content": "found in [8].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-220", "line_number": 220, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-221", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In ﬁnite abelian groups of known order (usually assumed to be prime), one can deﬁne other", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-221", "line_number": 221, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-222", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 8", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-222", "line_number": 222, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-223", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 10", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-223", "line_number": 223, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-224", "filename": "Cryptography_v1.0.1_processed.txt", "content": "problems. The problem of inverting the function x −→gx, is known as the Discrete Logarithm", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-224", "line_number": 224, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-225", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Problem (DLP). The problem of, given gx and gy, determining gx·y is known as the Difﬁe–", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-225", "line_number": 225, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-226", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Hellman Problem (DHP). The problem of distinguishing between triples of the form (gx, gy, gz)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-226", "line_number": 226, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-227", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and (gx, gy, gx·y) for random x, y, z is known as the Decision Difﬁe–Hellman (DDH) problem.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-227", "line_number": 227, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-228", "filename": "Cryptography_v1.0.1_processed.txt", "content": "When written additively in an elliptic curve group, a DDH triple has the form ([x]·P, [y]·P, [z]·P).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-228", "line_number": 228, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-229", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Generally speaking, the mathematical hard problems are used to establish the security of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-229", "line_number": 229, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-230", "filename": "Cryptography_v1.0.1_processed.txt", "content": "public key primitives. A major issue is that the above problems (Factoring, RSA-problem,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-230", "line_number": 230, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-231", "filename": "Cryptography_v1.0.1_processed.txt", "content": "DLP, DHP, DDH), on which we base all of our main existing public key algorithms, are easily", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-231", "line_number": 231, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-232", "filename": "Cryptography_v1.0.1_processed.txt", "content": "solved by large-scale quantum computers. This has led designers to try to build cryptographic", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-232", "line_number": 232, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-233", "filename": "Cryptography_v1.0.1_processed.txt", "content": "schemes on top of mathematical primitives which do not appear to be able to be broken", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-233", "line_number": 233, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-234", "filename": "Cryptography_v1.0.1_processed.txt", "content": "by a quantum computer. Examples of such problems are the problem of determining the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-234", "line_number": 234, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-235", "filename": "Cryptography_v1.0.1_processed.txt", "content": "shortest vector in a high dimensional lattice, the so-called Shortest Vector Problem (SVP),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-235", "line_number": 235, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-236", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and the problem of determining the closest lattice vector to a non-lattice vector, the so-called", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-236", "line_number": 236, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-237", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Closest Vector Problem (CVP). The best algorithms to solve these hard problems are lattice", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-237", "line_number": 237, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-238", "filename": "Cryptography_v1.0.1_processed.txt", "content": "reduction algorithms, a nice survey of these algorithms and applications can be found in [9].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-238", "line_number": 238, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-239", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The SVP and CVP problems, and others, give rise to a whole new area called Post-Quantum", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-239", "line_number": 239, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-240", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Cryptography (PQC).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-240", "line_number": 240, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-241", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Example:", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-241", "line_number": 241, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-242", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Putting the above ideas together, one may encounter statements such as: The", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-242", "line_number": 242, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-243", "filename": "Cryptography_v1.0.1_processed.txt", "content": "public key encryption XYZ is IND-CCA secure assuming the RSA-problem is hard and AES is a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-243", "line_number": 243, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-244", "filename": "Cryptography_v1.0.1_processed.txt", "content": "PRP. This statement tells us that any attack against the XYZ scheme must either be against", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-244", "line_number": 244, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-245", "filename": "Cryptography_v1.0.1_processed.txt", "content": "some weakness in the implementation, or must come from some attack not captured in the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-245", "line_number": 245, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-246", "filename": "Cryptography_v1.0.1_processed.txt", "content": "IND-CCA model, or must come from solving the RSA-problem, or must come from showing", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-246", "line_number": 246, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-247", "filename": "Cryptography_v1.0.1_processed.txt", "content": "that AES is not a PRP.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-247", "line_number": 247, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-248", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2.4", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-248", "line_number": 248, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-249", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Setup Assumptions", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-249", "line_number": 249, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-250", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Some cryptographic protocols require some setup assumptions. These are assumptions", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-250", "line_number": 250, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-251", "filename": "Cryptography_v1.0.1_processed.txt", "content": "about the environment, or some data, which need to be satisﬁed before the protocol can", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-251", "line_number": 251, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-252", "filename": "Cryptography_v1.0.1_processed.txt", "content": "be considered secure. These assumptions come in a variety of ﬂavours. For example, one", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-252", "line_number": 252, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-253", "filename": "Cryptography_v1.0.1_processed.txt", "content": "common setup assumption is that there exists a so-called Public-Key Infrastructure (PKI),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-253", "line_number": 253, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-254", "filename": "Cryptography_v1.0.1_processed.txt", "content": "meaning that we have a trusted binding between entities’ public keys and their identities.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-254", "line_number": 254, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-255", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Another setup assumption is the existence of a string (called the Common Reference String", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-255", "line_number": 255, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-256", "filename": "Cryptography_v1.0.1_processed.txt", "content": "or CRS) available to all parties, and which has been set up in a trusted manner, i.e. such that", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-256", "line_number": 256, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-257", "filename": "Cryptography_v1.0.1_processed.txt", "content": "no party has control of this string.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-257", "line_number": 257, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-258", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Other setup assumptions could be physical, for example, that the algorithms have access to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-258", "line_number": 258, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-259", "filename": "Cryptography_v1.0.1_processed.txt", "content": "good sources of random numbers, or that their internal workings are not susceptible to an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-259", "line_number": 259, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-260", "filename": "Cryptography_v1.0.1_processed.txt", "content": "invasive attacker, i.e. they are immune to side-channel attacks.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-260", "line_number": 260, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-261", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 9", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-261", "line_number": 261, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-262", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 11", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-262", "line_number": 262, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-263", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2.5", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-263", "line_number": 263, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-264", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Simulation and UC Security", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-264", "line_number": 264, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-265", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The above deﬁnitions of security make extensive use of the notion of indistinguishability", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-265", "line_number": 265, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-266", "filename": "Cryptography_v1.0.1_processed.txt", "content": "between two executions. Indeed, many of the proof techniques used in the security proofs", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-266", "line_number": 266, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-267", "filename": "Cryptography_v1.0.1_processed.txt", "content": "construct simulations of cryptographic operations. A simulation is an execution which is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-267", "line_number": 267, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-268", "filename": "Cryptography_v1.0.1_processed.txt", "content": "indistinguishable from the real execution, but does not involve (typically) the use of any key", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-268", "line_number": 268, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-269", "filename": "Cryptography_v1.0.1_processed.txt", "content": "material. Another method to produce security models is the so-called simulation paradigm,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-269", "line_number": 269, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-270", "filename": "Cryptography_v1.0.1_processed.txt", "content": "where we ask that an adversary cannot tell the simulation from a real execution (unless they", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-270", "line_number": 270, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-271", "filename": "Cryptography_v1.0.1_processed.txt", "content": "can solve some hard problem). This paradigm is often used to establish security results for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-271", "line_number": 271, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-272", "filename": "Cryptography_v1.0.1_processed.txt", "content": "more complex cryptographic protocols.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-272", "line_number": 272, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-273", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A problem with both the game/advantage-based deﬁnitions deﬁned earlier and the simulation", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-273", "line_number": 273, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-274", "filename": "Cryptography_v1.0.1_processed.txt", "content": "deﬁnitions is that they only apply to stand-alone executions, i.e. executions of one instance of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-274", "line_number": 274, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-275", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the protocol in one environment. To cope with arbitrarily complex executions and composition", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-275", "line_number": 275, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-276", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of cryptographic protocols an extension to the simulation paradigm exists called the Universal", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-276", "line_number": 276, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-277", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Composability (UC) framework.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-277", "line_number": 277, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-279", "filename": "Cryptography_v1.0.1_processed.txt", "content": "INFORMATION-THEORETICALLY SECURE", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-279", "line_number": 279, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-280", "filename": "Cryptography_v1.0.1_processed.txt", "content": "CONSTRUCTIONS", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-280", "line_number": 280, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-281", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[3, c2][4, c19]", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-281", "line_number": 281, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-282", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Whilst much of cryptography is focused on securing against adversaries that are modelled", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-282", "line_number": 282, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-283", "filename": "Cryptography_v1.0.1_processed.txt", "content": "as probabilistic polynomial time Turing machines, some constructions are known to provide", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-283", "line_number": 283, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-284", "filename": "Cryptography_v1.0.1_processed.txt", "content": "security against unbounded adversaries. These are called information-theoretically secure", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-284", "line_number": 284, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-285", "filename": "Cryptography_v1.0.1_processed.txt", "content": "constructions. A nice introduction to the information theoretic side of cryptography can be", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-285", "line_number": 285, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-286", "filename": "Cryptography_v1.0.1_processed.txt", "content": "found in [10].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-286", "line_number": 286, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-287", "filename": "Cryptography_v1.0.1_processed.txt", "content": "3.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-287", "line_number": 287, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-288", "filename": "Cryptography_v1.0.1_processed.txt", "content": "One-Time Pad", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-288", "line_number": 288, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-289", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The most famous primitive which provides information-theoretic security is the one-time", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-289", "line_number": 289, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-290", "filename": "Cryptography_v1.0.1_processed.txt", "content": "pad. Here, a binary message m ∈{0, 1}t is encrypted by taking a key k ∈{0, 1}t uniformly at", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-290", "line_number": 290, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-291", "filename": "Cryptography_v1.0.1_processed.txt", "content": "random, and then producing the ciphertext c = m ⊕k. In terms of our earlier security models,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-291", "line_number": 291, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-292", "filename": "Cryptography_v1.0.1_processed.txt", "content": "this is an IND-PASS scheme even in the presence of a computationally unbounded adversary.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-292", "line_number": 292, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-293", "filename": "Cryptography_v1.0.1_processed.txt", "content": "However, the fact that it does not provide IND-CPA security is obvious, as the encryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-293", "line_number": 293, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-294", "filename": "Cryptography_v1.0.1_processed.txt", "content": "scheme is determinisitic. The scheme is unsuitable in almost all modern environments as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-294", "line_number": 294, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-295", "filename": "Cryptography_v1.0.1_processed.txt", "content": "one requires a key as long as the message and the key may only be used once; hence the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-295", "line_number": 295, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-296", "filename": "Cryptography_v1.0.1_processed.txt", "content": "name one-time pad.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-296", "line_number": 296, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-297", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 10", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-297", "line_number": 297, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-298", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 12", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-298", "line_number": 298, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-299", "filename": "Cryptography_v1.0.1_processed.txt", "content": "3.2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-299", "line_number": 299, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-300", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Secret Sharing", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-300", "line_number": 300, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-301", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Secret sharing schemes allow a secret to be shared among a set of parties so that only a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-301", "line_number": 301, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-302", "filename": "Cryptography_v1.0.1_processed.txt", "content": "given subset can reconstruct the secret by bringing their shares together. The person who", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-302", "line_number": 302, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-303", "filename": "Cryptography_v1.0.1_processed.txt", "content": "constructs the sharing of the secret is called the dealer. The set of parties who can reconstruct", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-303", "line_number": 303, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-304", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the secret are called qualiﬁed sets, with the set of all qualiﬁed sets being called an access", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-304", "line_number": 304, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-305", "filename": "Cryptography_v1.0.1_processed.txt", "content": "structure.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-305", "line_number": 305, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-306", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Any set which is not qualiﬁed is said to be an unqualiﬁed set, and the set of all unqualiﬁed sets", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-306", "line_number": 306, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-307", "filename": "Cryptography_v1.0.1_processed.txt", "content": "is called an adversary structure. The access structure is usually assumed to be monotone, in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-307", "line_number": 307, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-308", "filename": "Cryptography_v1.0.1_processed.txt", "content": "that if the parties in A can reconstruct the secret, then so can any super-set of A.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-308", "line_number": 308, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-309", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Many secret sharing schemes provided information-theoretic security, in that any set of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-309", "line_number": 309, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-310", "filename": "Cryptography_v1.0.1_processed.txt", "content": "parties which is unqualiﬁed can obtain no information about the shared secret even if they", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-310", "line_number": 310, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-311", "filename": "Cryptography_v1.0.1_processed.txt", "content": "have unbounded computing power.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-311", "line_number": 311, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-312", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A special form of access structure is a so-called threshold structure. Here we allow any subset", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-312", "line_number": 312, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-313", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of t + 1 parties to reconstruct the secret, whereas any subset of t parties is unable to learn", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-313", "line_number": 313, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-314", "filename": "Cryptography_v1.0.1_processed.txt", "content": "anything. The value t is being called the threshold. One example construction of a threshold", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-314", "line_number": 314, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-315", "filename": "Cryptography_v1.0.1_processed.txt", "content": "secret sharing scheme for a secret s in a ﬁeld Fp, with n > p is via Shamir’s secret sharing", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-315", "line_number": 315, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-316", "filename": "Cryptography_v1.0.1_processed.txt", "content": "scheme.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-316", "line_number": 316, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-317", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In Shamir secret sharing, one selects a polynomial f(X) ∈Fp[X] of degree t with constant", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-317", "line_number": 317, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-318", "filename": "Cryptography_v1.0.1_processed.txt", "content": "coefﬁcients s, the value one wishes to share. The share values are then given by si = f(i)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-318", "line_number": 318, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-319", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(mod p), for i = 1, . . . , n, with party i being given si. Reconstruction of the value s from a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-319", "line_number": 319, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-320", "filename": "Cryptography_v1.0.1_processed.txt", "content": "subset of more than t values si can be done using Lagrange interpolation.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-320", "line_number": 320, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-321", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Due to an equivalence with Reed-Solomon error correcting codes, if t < n/2, then on receipt", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-321", "line_number": 321, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-322", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of n share values si, a reconstructing party can detect if any party has given it an invalid share.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-322", "line_number": 322, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-323", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Additionally, if t < n/3 then the reconstructing party can correct for any invalid shares.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-323", "line_number": 323, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-324", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Replicated secret sharing is a second popular scheme which supports any monotone access", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-324", "line_number": 324, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-325", "filename": "Cryptography_v1.0.1_processed.txt", "content": "structure. Given a boolean formula deﬁning who should have access to the secret, one can", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-325", "line_number": 325, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-326", "filename": "Cryptography_v1.0.1_processed.txt", "content": "deﬁne a secret sharing scheme from this formula by replacing all occurrences of AND with +", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-326", "line_number": 326, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-327", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and all occurrences of OR with a new secret. For example, given the formulae", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-327", "line_number": 327, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-328", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(P1 AND P2) OR (P2 AND P3),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-328", "line_number": 328, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-329", "filename": "Cryptography_v1.0.1_processed.txt", "content": "one can share a secret s by writing it as s = s1 +s2 = s′", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-329", "line_number": 329, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-330", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2 +s3 and then, giving party P1 the value", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-330", "line_number": 330, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-331", "filename": "Cryptography_v1.0.1_processed.txt", "content": "s1, party P2 the pair of values s2 and s′", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-331", "line_number": 331, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-332", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2, and party P3 the value s3. Replicated secret sharing is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-332", "line_number": 332, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-333", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the scheme obtained in this way when putting the boolean formulae into Conjunctive Normal", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-333", "line_number": 333, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-334", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Form.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-334", "line_number": 334, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-335", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Of importance in applications of secret sharing, especially to Secure Multi-Party Computation", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-335", "line_number": 335, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-336", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(see Section 9.4) is whether the adversary structure is Q2 or Q3. An adversary structure is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-336", "line_number": 336, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-337", "filename": "Cryptography_v1.0.1_processed.txt", "content": "said to be Qi if no set of i unqualiﬁed sets have union the full set of players. Shamir’s secret", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-337", "line_number": 337, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-338", "filename": "Cryptography_v1.0.1_processed.txt", "content": "sharing scheme is Q2 if t < n/2 and Q3 when t < n/3. The error detection (resp. correction)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-338", "line_number": 338, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-339", "filename": "Cryptography_v1.0.1_processed.txt", "content": "properties of Shamir’s secret sharing scheme mentioned above follow through to any Q2 (resp.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-339", "line_number": 339, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-340", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Q3) adversary structure.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-340", "line_number": 340, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-341", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 11", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-341", "line_number": 341, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-342", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 13", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-342", "line_number": 342, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-344", "filename": "Cryptography_v1.0.1_processed.txt", "content": "SYMMETRIC PRIMITIVES", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-344", "line_number": 344, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-345", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[3, c3–c6][4, c11–c14]", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-345", "line_number": 345, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-346", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Symmetric primitives are a key component of many cryptographic constructions. There are", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-346", "line_number": 346, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-347", "filename": "Cryptography_v1.0.1_processed.txt", "content": "three such basic primitives: block ciphers, stream ciphers, and hash functions. Theoretically,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-347", "line_number": 347, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-348", "filename": "Cryptography_v1.0.1_processed.txt", "content": "all are keyed functions, i.e. they take as input a secret key, whilst in practice one often considers", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-348", "line_number": 348, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-349", "filename": "Cryptography_v1.0.1_processed.txt", "content": "hash functions which are unkeyed. At a basic level, all are functions f : K × D −→C where K", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-349", "line_number": 349, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-350", "filename": "Cryptography_v1.0.1_processed.txt", "content": "is the key space, D is the domain (which is of a ﬁxed ﬁnite size for block ciphers and stream", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-350", "line_number": 350, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-351", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ciphers).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-351", "line_number": 351, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-352", "filename": "Cryptography_v1.0.1_processed.txt", "content": "As explained in the introduction we will not be discussing in this report cryptanalysis of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-352", "line_number": 352, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-353", "filename": "Cryptography_v1.0.1_processed.txt", "content": "symmetric primitives, we will only be examining secure constructions. However, the main two", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-353", "line_number": 353, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-354", "filename": "Cryptography_v1.0.1_processed.txt", "content": "techniques for attacks in this space are so-called differential and linear cryptanalysis. The", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-354", "line_number": 354, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-355", "filename": "Cryptography_v1.0.1_processed.txt", "content": "interested reader is referred to the excellent tutorial by Howard Heys [11] on these topics, or", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-355", "line_number": 355, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-356", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the book [12].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-356", "line_number": 356, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-357", "filename": "Cryptography_v1.0.1_processed.txt", "content": "4.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-357", "line_number": 357, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-358", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Block Ciphers", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-358", "line_number": 358, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-359", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A block cipher is a function f : K × {0, 1}b −→{0, 1}b, where b is the block size. Despite their", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-359", "line_number": 359, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-360", "filename": "Cryptography_v1.0.1_processed.txt", "content": "names such functions should not be thought of as an encryption algorithm. It is, however,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-360", "line_number": 360, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-361", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a building block in many encryption algorithms. The design of block ciphers is a deep area", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-361", "line_number": 361, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-362", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of subject in cryptography, analogous to the design of number theoretic one-way functions.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-362", "line_number": 362, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-363", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Much like number-theoretic one-way functions, cryptographic constructions are proved secure", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-363", "line_number": 363, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-364", "filename": "Cryptography_v1.0.1_processed.txt", "content": "relative to an associated hard problem which a given block cipher is assumed to satisfy.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-364", "line_number": 364, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-365", "filename": "Cryptography_v1.0.1_processed.txt", "content": "For a ﬁxed key, a block cipher is assumed to act as a permutation on the set {0, 1}b, i.e. for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-365", "line_number": 365, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-366", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a ﬁxed key k, the map fk : {0, 1}b −→{0, 1}b is a bijection. It is also assumed that inverting", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-366", "line_number": 366, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-367", "filename": "Cryptography_v1.0.1_processed.txt", "content": "this permutation is also easy (if the key is known). A block cipher is considered secure if", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-367", "line_number": 367, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-368", "filename": "Cryptography_v1.0.1_processed.txt", "content": "no polynomial time adversary, given oracle access to a permutation on {0, 1}b, can tell the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-368", "line_number": 368, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-369", "filename": "Cryptography_v1.0.1_processed.txt", "content": "difference between being given a uniformly random permutation or the function fk for some", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-369", "line_number": 369, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-370", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ﬁxed hidden key k, i.e. the block cipher is a PRP. In some applications, we only require that", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-370", "line_number": 370, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-371", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the block cipher is a function, i.e. not a bijection. In which case we require the block cipher is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-371", "line_number": 371, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-372", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a PRF.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-372", "line_number": 372, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-373", "filename": "Cryptography_v1.0.1_processed.txt", "content": "One can never prove that a block cipher is a PRP, so the design criteria is usually a task", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-373", "line_number": 373, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-374", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of building a mathematical construction which resists all known attacks. The main such", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-374", "line_number": 374, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-375", "filename": "Cryptography_v1.0.1_processed.txt", "content": "attacks which one resists are so-called linear cryptanalysis, where one approximates non-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-375", "line_number": 375, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-376", "filename": "Cryptography_v1.0.1_processed.txt", "content": "linear components within the block cipher by linear functions, and differential cryptanalysis,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-376", "line_number": 376, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-377", "filename": "Cryptography_v1.0.1_processed.txt", "content": "where one looks at how two outputs vary on related input messages, e.g. one applies fk to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-377", "line_number": 377, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-378", "filename": "Cryptography_v1.0.1_processed.txt", "content": "various inputs m0 and m1 where m0 ⊕m1 = ∆a ﬁxed value.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-378", "line_number": 378, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-379", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The design of a block cipher is made up of a number of simpler components. There are usually", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-379", "line_number": 379, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-380", "filename": "Cryptography_v1.0.1_processed.txt", "content": "layers of simple ﬁxed permutations, and layers of table lookups. These table lookups are", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-380", "line_number": 380, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-381", "filename": "Cryptography_v1.0.1_processed.txt", "content": "called S-boxes, where the S stands for substitutions. There are two main techniques to design", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-381", "line_number": 381, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-382", "filename": "Cryptography_v1.0.1_processed.txt", "content": "block ciphers. Both repeat a simple operation (called a round) a number of times. Each round", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-382", "line_number": 382, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-383", "filename": "Cryptography_v1.0.1_processed.txt", "content": "consists of a combination of permutations and substitutions, and a key addition. The main", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-383", "line_number": 383, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-384", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key is ﬁrst expanded into round-keys, with each round having a different round-key.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-384", "line_number": 384, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-385", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In the ﬁrst methodology, called a Feistel Network, the S-Boxes allowed in each round can", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-385", "line_number": 385, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-386", "filename": "Cryptography_v1.0.1_processed.txt", "content": "be non-injective, i.e. non-invertible. Despite this, the Feistel constructions still maintain the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-386", "line_number": 386, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-387", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 12", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-387", "line_number": 387, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-388", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 14", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-388", "line_number": 388, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-389", "filename": "Cryptography_v1.0.1_processed.txt", "content": "overall invertibility of the block cipher construction. The second method is a Substitution-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-389", "line_number": 389, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-390", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Permutation Network design in which each round consists of a round-key addition, followed", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-390", "line_number": 390, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-391", "filename": "Cryptography_v1.0.1_processed.txt", "content": "by a ﬁxed permutation, followed by the application of bijective S-boxes. In general, the Feistel", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-391", "line_number": 391, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-392", "filename": "Cryptography_v1.0.1_processed.txt", "content": "construction requires more rounds than the Substitution-Permutation network construction.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-392", "line_number": 392, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-393", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The DES (Data Encryption Standard) block cipher (with an original key of 56-bits and block", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-393", "line_number": 393, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-394", "filename": "Cryptography_v1.0.1_processed.txt", "content": "size of b = 64) is a Feistel construction. The DES algorithm dates from the 1970s, and the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-394", "line_number": 394, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-395", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key size is now considered far too small for any application. However, one can extend DES", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-395", "line_number": 395, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-396", "filename": "Cryptography_v1.0.1_processed.txt", "content": "into a 112- or 168-bit key block cipher to construct an algorithm called 2DES or 3DES. The use", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-396", "line_number": 396, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-397", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of 2DES or 3DES is still considered secure, although in some applications, the block size of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-397", "line_number": 397, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-398", "filename": "Cryptography_v1.0.1_processed.txt", "content": "64-bits is considered insecure for real-world use.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-398", "line_number": 398, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-399", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The AES (Advanced Encryption Standard) block cipher is the modern replacement for DES,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-399", "line_number": 399, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-400", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and it is a block cipher with a 128-, 192- or 256-bit key, and with a block size of b = 128 bits.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-400", "line_number": 400, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-401", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The AES algorithm has hardware support on many microprocessors, making operations using", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-401", "line_number": 401, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-402", "filename": "Cryptography_v1.0.1_processed.txt", "content": "AES much faster than using other cryptographic primitives. Readers who wish to understand", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-402", "line_number": 402, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-403", "filename": "Cryptography_v1.0.1_processed.txt", "content": "more about the design of the AES block cipher referred to [13].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-403", "line_number": 403, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-404", "filename": "Cryptography_v1.0.1_processed.txt", "content": "4.2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-404", "line_number": 404, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-405", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Stream Ciphers", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-405", "line_number": 405, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-406", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A stream cipher is one which produces an arbitrary length string of output bits, i.e. the co-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-406", "line_number": 406, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-407", "filename": "Cryptography_v1.0.1_processed.txt", "content": "domain of the function is essentially unbounded. Stream ciphers can be constructed from", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-407", "line_number": 407, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-408", "filename": "Cryptography_v1.0.1_processed.txt", "content": "block ciphers, by using a block cipher in Counter Mode (see Section 5.1). However, the stream", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-408", "line_number": 408, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-409", "filename": "Cryptography_v1.0.1_processed.txt", "content": "cipher is usually reserved for constructions which are special-purpose and for which the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-409", "line_number": 409, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-410", "filename": "Cryptography_v1.0.1_processed.txt", "content": "hardware complexity is much reduced.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-410", "line_number": 410, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-411", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Clearly, a stream cipher cannot be a permutation, but we require that no polynomial time", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-411", "line_number": 411, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-412", "filename": "Cryptography_v1.0.1_processed.txt", "content": "adversary can distinguish oracle access to the stream cipher from oracle access to a uniformly", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-412", "line_number": 412, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-413", "filename": "Cryptography_v1.0.1_processed.txt", "content": "random function with inﬁnite co-domain. The design of stream ciphers is more ad-hoc than", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-413", "line_number": 413, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-414", "filename": "Cryptography_v1.0.1_processed.txt", "content": "that of the design of block ciphers. In addition, there is less widespread adoption outside", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-414", "line_number": 414, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-415", "filename": "Cryptography_v1.0.1_processed.txt", "content": "speciﬁc application areas. The interested reader is referred to the outcome of the eStream", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-415", "line_number": 415, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-416", "filename": "Cryptography_v1.0.1_processed.txt", "content": "competition for details of speciﬁc ad-hoc stream cipher designs [14].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-416", "line_number": 416, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-417", "filename": "Cryptography_v1.0.1_processed.txt", "content": "4.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-417", "line_number": 417, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-418", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Hash Functions", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-418", "line_number": 418, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-419", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Hash functions are much like block ciphers in that they should act as PRFs. However, the input", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-419", "line_number": 419, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-420", "filename": "Cryptography_v1.0.1_processed.txt", "content": "domain can be unbounded. Since a PRF needs to be keyed to make any sense in theoretical", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-420", "line_number": 420, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-421", "filename": "Cryptography_v1.0.1_processed.txt", "content": "tracts, a hash function is usually a keyed object. In practice, we often require an unkeyed", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-421", "line_number": 421, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-422", "filename": "Cryptography_v1.0.1_processed.txt", "content": "object, in which case one considers the actual hash function used to have an implicit inbuilt", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-422", "line_number": 422, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-423", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ﬁxed key, and have been chosen from a function family already.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-423", "line_number": 423, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-424", "filename": "Cryptography_v1.0.1_processed.txt", "content": "When considering a ﬁxed hash function, one is usually interested in the intractability of inverting", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-424", "line_number": 424, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-425", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the hash function (the one-way property), the intractability of ﬁnding two inputs with the same", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-425", "line_number": 425, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-426", "filename": "Cryptography_v1.0.1_processed.txt", "content": "output (the collision resistance property), or the intractability of ﬁnding, given an input/output", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-426", "line_number": 426, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-427", "filename": "Cryptography_v1.0.1_processed.txt", "content": "pair, a new input which gives the same output (the second-preimage resistance property).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-427", "line_number": 427, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-428", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 13", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-428", "line_number": 428, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-429", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 16", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-429", "line_number": 429, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-430", "filename": "Cryptography_v1.0.1_processed.txt", "content": "4.3.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-430", "line_number": 430, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-431", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Random Oracle Model", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-431", "line_number": 431, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-432", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Many cryptographic constructions are only secure if one assumes that the hash function", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-432", "line_number": 432, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-433", "filename": "Cryptography_v1.0.1_processed.txt", "content": "used in the construction behaves ‘like a random oracle’. Such constructions are believed to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-433", "line_number": 433, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-434", "filename": "Cryptography_v1.0.1_processed.txt", "content": "be secure in the real world, but theoretically, they are less pleasing. One can think of a proof", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-434", "line_number": 434, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-435", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of security in the random oracle model as a proof in which we allow the attacker to have", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-435", "line_number": 435, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-436", "filename": "Cryptography_v1.0.1_processed.txt", "content": "their usual powers; however, when they (or any of the partners they are attacking) call the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-436", "line_number": 436, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-437", "filename": "Cryptography_v1.0.1_processed.txt", "content": "underlying hash function the call is made to an external party via an oracle call. This external", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-437", "line_number": 437, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-438", "filename": "Cryptography_v1.0.1_processed.txt", "content": "party then simply plays back a random value, i.e. it does not use any algorithm to generate", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-438", "line_number": 438, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-439", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the random values. All that is required is that if the input is given to the oracle twice, then the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-439", "line_number": 439, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-440", "filename": "Cryptography_v1.0.1_processed.txt", "content": "same output is always returned.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-440", "line_number": 440, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-441", "filename": "Cryptography_v1.0.1_processed.txt", "content": "This clearly does not capture attacks in which the adversary makes clever use of exactly", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-441", "line_number": 441, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-442", "filename": "Cryptography_v1.0.1_processed.txt", "content": "how the hash function is deﬁned etc, and how this deﬁnition interacts with other aspects", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-442", "line_number": 442, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-443", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of the scheme/protocol under analysis. However, this modelling methodology has proved", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-443", "line_number": 443, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-444", "filename": "Cryptography_v1.0.1_processed.txt", "content": "remarkably good in enabling cryptographers to design schemes which are secure in the real", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-444", "line_number": 444, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-445", "filename": "Cryptography_v1.0.1_processed.txt", "content": "world.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-445", "line_number": 445, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-447", "filename": "Cryptography_v1.0.1_processed.txt", "content": "SYMMETRIC ENCRYPTION AND AUTHENTICATION", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-447", "line_number": 447, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-448", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[3, c3–c4][4, c13–c14]", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-448", "line_number": 448, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-449", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A block cipher, such as AES or DES, does not provide an effective form of data encryption or", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-449", "line_number": 449, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-450", "filename": "Cryptography_v1.0.1_processed.txt", "content": "data/entity authentication on its own. To provide such symmetric cryptographic constructions,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-450", "line_number": 450, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-451", "filename": "Cryptography_v1.0.1_processed.txt", "content": "one needs a scheme, which takes the primitive and then utilizes this in a more complex", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-451", "line_number": 451, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-452", "filename": "Cryptography_v1.0.1_processed.txt", "content": "construction to provide the required cryptographic service. In the context of symmetric", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-452", "line_number": 452, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-453", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encryption, these are provided by modes of operation. In the case of authentication, it is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-453", "line_number": 453, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-454", "filename": "Cryptography_v1.0.1_processed.txt", "content": "provided by a MAC construction. Additionally, block ciphers are often used to take some", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-454", "line_number": 454, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-455", "filename": "Cryptography_v1.0.1_processed.txt", "content": "entropy and then expand, or collapse, this into a pseudo-random stream or key; a so-called XOF", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-455", "line_number": 455, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-456", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(or Extendable Output Function) or KDF (or Key Derivation Function). Further details on block", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-456", "line_number": 456, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-457", "filename": "Cryptography_v1.0.1_processed.txt", "content": "cipher based constructions can be found at [16], whereas further details on Sponger/Keccak", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-457", "line_number": 457, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-458", "filename": "Cryptography_v1.0.1_processed.txt", "content": "based constructions can be found at [15].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-458", "line_number": 458, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-459", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-459", "line_number": 459, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-463", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-463", "line_number": 463, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-467", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-467", "line_number": 467, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-472", "filename": "Cryptography_v1.0.1_processed.txt", "content": "· · · · · ·", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-472", "line_number": 472, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-473", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-473", "line_number": 473, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-477", "filename": "Cryptography_v1.0.1_processed.txt", "content": "· · · · · ·", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-477", "line_number": 477, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-478", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-478", "line_number": 478, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-482", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Figure 2: CBC Mode Encryption (All Figures are produced using TikZ for Cryptographers", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-482", "line_number": 482, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-483", "filename": "Cryptography_v1.0.1_processed.txt", "content": "https://www.iacr.org/authors/tikz/).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-483", "line_number": 483, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-484", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 15", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-484", "line_number": 484, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-485", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 17", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-485", "line_number": 485, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-486", "filename": "Cryptography_v1.0.1_processed.txt", "content": "5.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-486", "line_number": 486, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-487", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Modes of Operation", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-487", "line_number": 487, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-488", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Historically, there have been four traditional modes of operation to turn a block cipher into an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-488", "line_number": 488, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-489", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encryption algorithm. These were ECB, CBC, OFB and CFB modes. In recent years, the CTR", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-489", "line_number": 489, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-490", "filename": "Cryptography_v1.0.1_processed.txt", "content": "mode has also been added to this list. Among these, only CBC mode (given in Figure 2) and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-490", "line_number": 490, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-491", "filename": "Cryptography_v1.0.1_processed.txt", "content": "CTR mode (given in Figure 3) are used widely within current systems. In these Figures, the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-491", "line_number": 491, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-492", "filename": "Cryptography_v1.0.1_processed.txt", "content": "block cipher is represented by the function Enc", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-492", "line_number": 492, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-493", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-493", "line_number": 493, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-494", "filename": "Cryptography_v1.0.1_processed.txt", "content": "IV, Ctr+0", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-494", "line_number": 494, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-498", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-498", "line_number": 498, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-499", "filename": "Cryptography_v1.0.1_processed.txt", "content": "IV, Ctr+1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-499", "line_number": 499, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-503", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-503", "line_number": 503, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-504", "filename": "Cryptography_v1.0.1_processed.txt", "content": "IV, Ctr+2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-504", "line_number": 504, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-508", "filename": "Cryptography_v1.0.1_processed.txt", "content": "· · · · · ·", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-508", "line_number": 508, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-509", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ENC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-509", "line_number": 509, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-510", "filename": "Cryptography_v1.0.1_processed.txt", "content": "IV, Ctr+n", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-510", "line_number": 510, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-514", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Figure 3: CTR Mode Encryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-514", "line_number": 514, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-515", "filename": "Cryptography_v1.0.1_processed.txt", "content": "On their own, however, CBC and CTR modes only provide IND-CPA security. This is far weaker", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-515", "line_number": 515, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-516", "filename": "Cryptography_v1.0.1_processed.txt", "content": "than the ‘gold standard’ of security, namely IND-CCA (discussed earlier). Thus, modern", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-516", "line_number": 516, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-517", "filename": "Cryptography_v1.0.1_processed.txt", "content": "systems use modes which provide this level of security, also enabling additional data (such", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-517", "line_number": 517, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-518", "filename": "Cryptography_v1.0.1_processed.txt", "content": "as session identiﬁers) to be tagged into the encryption algorithm. Such algorithms are called", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-518", "line_number": 518, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-519", "filename": "Cryptography_v1.0.1_processed.txt", "content": "AEAD methods (or Authenticated Encryption with Associated Data). In such algorithms, the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-519", "line_number": 519, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-520", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encryption primitive takes as input a message to be encrypted, plus some associated data.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-520", "line_number": 520, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-521", "filename": "Cryptography_v1.0.1_processed.txt", "content": "To decrypt, the ciphertext is given, along with the associated data. Decryption will only work", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-521", "line_number": 521, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-522", "filename": "Cryptography_v1.0.1_processed.txt", "content": "if both the key is correct and the associated data is what was input during the encryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-522", "line_number": 522, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-523", "filename": "Cryptography_v1.0.1_processed.txt", "content": "process.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-523", "line_number": 523, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-524", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The simplest method to obtain an AEAD algorithm is to take an IND-CPA mode of operation", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-524", "line_number": 524, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-525", "filename": "Cryptography_v1.0.1_processed.txt", "content": "such as CBC or CTR, and then to apply a MAC to the ciphertext and the data to be authenticated,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-525", "line_number": 525, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-526", "filename": "Cryptography_v1.0.1_processed.txt", "content": "giving us the so-called Encrypt-then-MAC paradigm. Thus, to encrypt m with authenticated", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-526", "line_number": 526, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-527", "filename": "Cryptography_v1.0.1_processed.txt", "content": "data a, one applies the transform", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-527", "line_number": 527, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-528", "filename": "Cryptography_v1.0.1_processed.txt", "content": "c1 ←Enc(m, k1; r),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-528", "line_number": 528, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-529", "filename": "Cryptography_v1.0.1_processed.txt", "content": "c2 ←MAC(c1∥a, k2; r),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-529", "line_number": 529, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-530", "filename": "Cryptography_v1.0.1_processed.txt", "content": "with the ciphertext being (c1, c2). In such a construction, it is important that the MAC is applied", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-530", "line_number": 530, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-531", "filename": "Cryptography_v1.0.1_processed.txt", "content": "to the ciphertext as opposed to the message.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-531", "line_number": 531, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-532", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A major issue with the Encrypt-then-MAC construction is that one needs to pass the data to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-532", "line_number": 532, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-533", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the underlying block cipher twice, with two different keys. Thus, new constructions of AEAD", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-533", "line_number": 533, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-534", "filename": "Cryptography_v1.0.1_processed.txt", "content": "schemes have been given which are more efﬁcient. The most widely deployed of these is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-534", "line_number": 534, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-535", "filename": "Cryptography_v1.0.1_processed.txt", "content": "GCM (or Galois Counter Mode), see Figure 4, which is widely deployed due to the support for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-535", "line_number": 535, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-536", "filename": "Cryptography_v1.0.1_processed.txt", "content": "this in modern processors.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-536", "line_number": 536, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-537", "filename": "Cryptography_v1.0.1_processed.txt", "content": "One time AEAD constructions, otherwise known as DEMs, can be obtained by simply making", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-537", "line_number": 537, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-538", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the randomized AEAD deterministic by ﬁxing the IV to zero.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-538", "line_number": 538, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-539", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 16", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-539", "line_number": 539, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-540", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 19", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-540", "line_number": 540, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-541", "filename": "Cryptography_v1.0.1_processed.txt", "content": "As HMAC is designed speciﬁcally for use with Merkle–Damg˚ard-based hash functions, it", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-541", "line_number": 541, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-542", "filename": "Cryptography_v1.0.1_processed.txt", "content": "makes no-sense to use this construction when using a sponge based hash function such as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-542", "line_number": 542, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-543", "filename": "Cryptography_v1.0.1_processed.txt", "content": "SHA-3. The standardized MAC function derived from SHA-3 is called KMAC (or Keccak MAC).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-543", "line_number": 543, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-544", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In this function, the sponge construction is used to input a suitably padded message, then the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-544", "line_number": 544, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-545", "filename": "Cryptography_v1.0.1_processed.txt", "content": "required MAC output is taken as the squeezed output of the sponge; whereas as many bits as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-545", "line_number": 545, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-546", "filename": "Cryptography_v1.0.1_processed.txt", "content": "squeezed are as needed for the MAC output.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-546", "line_number": 546, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-547", "filename": "Cryptography_v1.0.1_processed.txt", "content": "5.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-547", "line_number": 547, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-548", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Key Derivation and Extendable Output Functions", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-548", "line_number": 548, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-549", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The security deﬁnition of a deterministic MAC is essentially equivalent to the deﬁnition that the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-549", "line_number": 549, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-550", "filename": "Cryptography_v1.0.1_processed.txt", "content": "output of the MAC function is indistinguishable from a random string, if one does not know the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-550", "line_number": 550, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-551", "filename": "Cryptography_v1.0.1_processed.txt", "content": "underlying secret key. As such, MAC functions can be used for other cryptographic operations.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-551", "line_number": 551, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-552", "filename": "Cryptography_v1.0.1_processed.txt", "content": "For example, in many situations, one must derive a long (or short) string of random bits, given", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-552", "line_number": 552, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-553", "filename": "Cryptography_v1.0.1_processed.txt", "content": "some random input bits. Such functions are called KDFs or XOFs (for Key Derivation Function", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-553", "line_number": 553, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-554", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and Extendable Output Function). Usually, one uses the term KDF when the output is of a ﬁxed", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-554", "line_number": 554, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-555", "filename": "Cryptography_v1.0.1_processed.txt", "content": "length, and XOF when the output could be of an arbitrary length. But the constructions are,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-555", "line_number": 555, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-556", "filename": "Cryptography_v1.0.1_processed.txt", "content": "usually, essentially the same in both cases.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-556", "line_number": 556, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-557", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Such functions can take an arbitrary length input string, and produce another arbitrary length", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-557", "line_number": 557, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-558", "filename": "Cryptography_v1.0.1_processed.txt", "content": "output string which is pseudo-random. There are three main constructions for such functions;", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-558", "line_number": 558, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-559", "filename": "Cryptography_v1.0.1_processed.txt", "content": "one based on block ciphers, one on the Merkle–Damg˚ard hash functions, and one based on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-559", "line_number": 559, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-560", "filename": "Cryptography_v1.0.1_processed.txt", "content": "sponge-based hash functions.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-560", "line_number": 560, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-561", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The constructions based on a block cipher are, at their heart, using CBC-MAC, with a zero key", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-561", "line_number": 561, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-562", "filename": "Cryptography_v1.0.1_processed.txt", "content": "to compress the input string into a cryptographic key and then use the CTR mode of operation", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-562", "line_number": 562, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-563", "filename": "Cryptography_v1.0.1_processed.txt", "content": "under this key to produce the output string. Hence, the construction is essentially given by", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-563", "line_number": 563, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-564", "filename": "Cryptography_v1.0.1_processed.txt", "content": "k ←CBC-MAC(m, 0),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-564", "line_number": 564, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-565", "filename": "Cryptography_v1.0.1_processed.txt", "content": "o1 ←Enc(1, k),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-565", "line_number": 565, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-566", "filename": "Cryptography_v1.0.1_processed.txt", "content": "o2 ←Enc(2, k),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-566", "line_number": 566, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-567", "filename": "Cryptography_v1.0.1_processed.txt", "content": ". . .", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-567", "line_number": 567, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-568", "filename": "Cryptography_v1.0.1_processed.txt", "content": "where Enc is the underlying block cipher.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-568", "line_number": 568, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-569", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The constructions based on the Merkle–Damg˚ard hash function use a similar structure, but", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-569", "line_number": 569, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-570", "filename": "Cryptography_v1.0.1_processed.txt", "content": "using one hash function application per output block, in a method similar to the following", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-570", "line_number": 570, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-571", "filename": "Cryptography_v1.0.1_processed.txt", "content": "o1 ←H(m∥1),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-571", "line_number": 571, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-572", "filename": "Cryptography_v1.0.1_processed.txt", "content": "o2 ←H(m∥2),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-572", "line_number": 572, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-573", "filename": "Cryptography_v1.0.1_processed.txt", "content": ". . .", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-573", "line_number": 573, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-574", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Due to the way Merkle–Damg˚ard hash functions are constructed, the above construction (for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-574", "line_number": 574, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-575", "filename": "Cryptography_v1.0.1_processed.txt", "content": "large enough m) can be done more efﬁciently than simply applying H as many times as the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-575", "line_number": 575, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-576", "filename": "Cryptography_v1.0.1_processed.txt", "content": "number of output blocks will dictate.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-576", "line_number": 576, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-577", "filename": "Cryptography_v1.0.1_processed.txt", "content": "As one can imagine, the functions based on Keccak are simpler—one simply inputs the suitably", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-577", "line_number": 577, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-578", "filename": "Cryptography_v1.0.1_processed.txt", "content": "padded message into the sponge and then squeezes as many output bits out as required.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-578", "line_number": 578, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-579", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Special KDFs can also be deﬁned which take as input a low entropy input, such as a password", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-579", "line_number": 579, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-580", "filename": "Cryptography_v1.0.1_processed.txt", "content": "or PIN, and produce a key for use in a symmetric algorithm. These password based key", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-580", "line_number": 580, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-581", "filename": "Cryptography_v1.0.1_processed.txt", "content": "derivation functions are designed to be computationally expensive, so as to mitigate problems", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-581", "line_number": 581, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-582", "filename": "Cryptography_v1.0.1_processed.txt", "content": "associated to brute force attacking of the underlying low entropy input.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-582", "line_number": 582, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-583", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 18", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-583", "line_number": 583, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-584", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 20", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-584", "line_number": 584, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-585", "filename": "Cryptography_v1.0.1_processed.txt", "content": "5.4", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-585", "line_number": 585, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-586", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Merkle-Trees and Blockchains", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-586", "line_number": 586, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-587", "filename": "Cryptography_v1.0.1_processed.txt", "content": "An application of cryptographic hash functions which has recently come to prominance is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-587", "line_number": 587, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-588", "filename": "Cryptography_v1.0.1_processed.txt", "content": "that of using Merkle-Trees and by extension blockchains. A Merkle-Tree, or hash-tree, is a tree", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-588", "line_number": 588, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-589", "filename": "Cryptography_v1.0.1_processed.txt", "content": "in which each leaf node contains data, and each internal node is the hash of its child nodes.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-589", "line_number": 589, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-590", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The root node is then publicly published. Merkle-Trees enable efﬁcient demonstration that a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-590", "line_number": 590, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-591", "filename": "Cryptography_v1.0.1_processed.txt", "content": "leaf node is contained in the tree, in that one simply presents the path of hashes from the leaf", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-591", "line_number": 591, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-592", "filename": "Cryptography_v1.0.1_processed.txt", "content": "up to the root node. Thus veriﬁcation is logarithmic in the number of leaf nodes. Merkle-Trees", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-592", "line_number": 592, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-593", "filename": "Cryptography_v1.0.1_processed.txt", "content": "can verify any form of stored data and have been used in various protocols such as version", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-593", "line_number": 593, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-594", "filename": "Cryptography_v1.0.1_processed.txt", "content": "control systems, such as Git, and backup systems.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-594", "line_number": 594, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-595", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A blockchain is a similar structure, but now the data items are aligned in a chain, and each", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-595", "line_number": 595, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-596", "filename": "Cryptography_v1.0.1_processed.txt", "content": "node hashes both the data item and a link to the previous item in the chain. Blockchains", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-596", "line_number": 596, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-597", "filename": "Cryptography_v1.0.1_processed.txt", "content": "are used in cryptocurrencies such as Bitcoin, but they have wider application. The key prop-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-597", "line_number": 597, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-598", "filename": "Cryptography_v1.0.1_processed.txt", "content": "erty a blockchain provides is that (assuming the current head of the chain is authenticated", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-598", "line_number": 598, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-599", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and trusted) the data provides an open distributed ledger in which previous data items are", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-599", "line_number": 599, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-600", "filename": "Cryptography_v1.0.1_processed.txt", "content": "immutable, and the ordering of data items is preserved.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-600", "line_number": 600, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-602", "filename": "Cryptography_v1.0.1_processed.txt", "content": "PUBLIC KEY ENCRYPTION", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-602", "line_number": 602, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-603", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[3, c11][4, c15–c17]", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-603", "line_number": 603, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-604", "filename": "Cryptography_v1.0.1_processed.txt", "content": "As explained above, public key encryption involves two keys, a public one pk and a private one", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-604", "line_number": 604, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-605", "filename": "Cryptography_v1.0.1_processed.txt", "content": "sk. The encryption algorithm uses the public key, whilst the decryption algorithm uses the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-605", "line_number": 605, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-606", "filename": "Cryptography_v1.0.1_processed.txt", "content": "secret key. Much of public key cryptography is based on number theoretic constructions, thus", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-606", "line_number": 606, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-607", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[5] provides a good coverage of much in this section. The standard security requirement for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-607", "line_number": 607, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-608", "filename": "Cryptography_v1.0.1_processed.txt", "content": "public key encryption is that the scheme should be IND-CCA. Note that since the encryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-608", "line_number": 608, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-609", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key is public we have that IND-PASS is the same as IND-CPA for a public key encryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-609", "line_number": 609, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-610", "filename": "Cryptography_v1.0.1_processed.txt", "content": "scheme.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-610", "line_number": 610, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-611", "filename": "Cryptography_v1.0.1_processed.txt", "content": "6.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-611", "line_number": 611, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-612", "filename": "Cryptography_v1.0.1_processed.txt", "content": "KEM-DEM Philosophy", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-612", "line_number": 612, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-613", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In general, public key encryption schemes are orders of magnitude less efﬁcient than symmet-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-613", "line_number": 613, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-614", "filename": "Cryptography_v1.0.1_processed.txt", "content": "ric key encryption schemes. Thus, the usual method in utilizing a public key scheme, when", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-614", "line_number": 614, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-615", "filename": "Cryptography_v1.0.1_processed.txt", "content": "large messages need to be encrypted, is via a hybrid method. This hybrid methodology is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-615", "line_number": 615, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-616", "filename": "Cryptography_v1.0.1_processed.txt", "content": "called the KEM-DEM philosophy A KEM, which stands for Key Encapsulation Mechanism, a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-616", "line_number": 616, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-617", "filename": "Cryptography_v1.0.1_processed.txt", "content": "public key method to transmit a short key, selected at random from a set K, to a designated", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-617", "line_number": 617, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-618", "filename": "Cryptography_v1.0.1_processed.txt", "content": "recipient. Whereas, a DEM, or Data Encryption Mechanism, is essentially the same as an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-618", "line_number": 618, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-619", "filename": "Cryptography_v1.0.1_processed.txt", "content": "IND-CCA symmetric encryption scheme, which has key space K. Since a DEM is only ever", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-619", "line_number": 619, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-620", "filename": "Cryptography_v1.0.1_processed.txt", "content": "used once with the same key, we can actually use a weaker notion of IND-CCA encryption for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-620", "line_number": 620, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-621", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the DEM, in which the adversary is not given access to an encryption oracle; which means the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-621", "line_number": 621, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-622", "filename": "Cryptography_v1.0.1_processed.txt", "content": "DEM can be deterministic.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-622", "line_number": 622, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-623", "filename": "Cryptography_v1.0.1_processed.txt", "content": "For a KEM, we call the encryption and decryption mechanisms encapsulation and decapsula-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-623", "line_number": 623, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-624", "filename": "Cryptography_v1.0.1_processed.txt", "content": "tion, respectively. It is usual for the syntax of the encapsulation algorithm to not take any input,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-624", "line_number": 624, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-625", "filename": "Cryptography_v1.0.1_processed.txt", "content": "bar the randomness, and then to return both the ciphertext and the key which it encapsulates.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-625", "line_number": 625, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-626", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Thus, the syntax, and correctness, of a KEM becomes", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-626", "line_number": 626, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-627", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(pk, sk) ←KEMKeyGen(), r ←R, (k, c) ←KEMEnc(pk; r), KEMDec(c, sk) = k.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-627", "line_number": 627, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-628", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 19", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-628", "line_number": 628, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-629", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 22", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-629", "line_number": 629, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-630", "filename": "Cryptography_v1.0.1_processed.txt", "content": "RSA-KEM, on the other hand, is a KEM which is much simpler to execute. To produce the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-630", "line_number": 630, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-631", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encapsulated key and the ciphertext, one takes the random input r (which one thinks of as a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-631", "line_number": 631, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-632", "filename": "Cryptography_v1.0.1_processed.txt", "content": "uniformly random element in (Z/NZ)∗). Then the KEM is deﬁned by", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-632", "line_number": 632, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-633", "filename": "Cryptography_v1.0.1_processed.txt", "content": "c ←re", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-633", "line_number": 633, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-634", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(mod N), k ←H(r),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-634", "line_number": 634, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-635", "filename": "Cryptography_v1.0.1_processed.txt", "content": "where H : (Z/NZ) −→K is a hash function, which we model as a random oracle.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-635", "line_number": 635, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-636", "filename": "Cryptography_v1.0.1_processed.txt", "content": "6.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-636", "line_number": 636, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-637", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Constructions based on Elliptic Curves", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-637", "line_number": 637, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-638", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Elliptic Curve Cryptography, or ECC, uses the fact that elliptic curves form a ﬁnite abelian", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-638", "line_number": 638, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-639", "filename": "Cryptography_v1.0.1_processed.txt", "content": "group. In terms of encryption schemes, the standard method is to use ECIES (Elliptic Curve", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-639", "line_number": 639, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-640", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Integrated Encryption Scheme) to deﬁne a public key, KEM which is IND-CCA in the random", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-640", "line_number": 640, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-641", "filename": "Cryptography_v1.0.1_processed.txt", "content": "oracle model, assuming the DDH problem in the subgroup of the elliptic curve being used.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-641", "line_number": 641, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-642", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In practice, this means that one selects a curve E(Fp) for which there is a point P ∈E(Fp)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-642", "line_number": 642, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-643", "filename": "Cryptography_v1.0.1_processed.txt", "content": "whose order is a prime q > 2256.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-643", "line_number": 643, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-644", "filename": "Cryptography_v1.0.1_processed.txt", "content": "For ECIES, the KeyGen algorithm is deﬁned as follows. A secret key sk ←F∗", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-644", "line_number": 644, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-645", "filename": "Cryptography_v1.0.1_processed.txt", "content": "q is selected", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-645", "line_number": 645, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-646", "filename": "Cryptography_v1.0.1_processed.txt", "content": "uniformly at random, and then the public key is set to be Q ←[sk]P. Key encapsulation is very", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-646", "line_number": 646, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-647", "filename": "Cryptography_v1.0.1_processed.txt", "content": "similar to RSA-KEM in that it is deﬁned by", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-647", "line_number": 647, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-648", "filename": "Cryptography_v1.0.1_processed.txt", "content": "r ←F∗", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-648", "line_number": 648, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-649", "filename": "Cryptography_v1.0.1_processed.txt", "content": "q, C ←[r] · P, k ←H([r] · Q),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-649", "line_number": 649, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-650", "filename": "Cryptography_v1.0.1_processed.txt", "content": "where H : E(Fp) −→K is a hash function (modelled as a random oracle). To decapsulate the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-650", "line_number": 650, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-651", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key is recovered via", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-651", "line_number": 651, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-652", "filename": "Cryptography_v1.0.1_processed.txt", "content": "k ←H([sk]C).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-652", "line_number": 652, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-653", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Compared to RSA-based primitives, ECC-based primitives are relatively fast and use less", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-653", "line_number": 653, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-654", "filename": "Cryptography_v1.0.1_processed.txt", "content": "bandwidth. This is because, at the time of writing, one can select elliptic curve parameters", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-654", "line_number": 654, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-655", "filename": "Cryptography_v1.0.1_processed.txt", "content": "with p ≈q ≈2256 to obtain security equivalent to a work-factor of 2128 operations. Hence, in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-655", "line_number": 655, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-656", "filename": "Cryptography_v1.0.1_processed.txt", "content": "current systems elliptic curve-based systems are preferred over RSA-based ones.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-656", "line_number": 656, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-657", "filename": "Cryptography_v1.0.1_processed.txt", "content": "6.4", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-657", "line_number": 657, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-658", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Lattice-based Constructions", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-658", "line_number": 658, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-659", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A major problem with both RSA and ECC primitives is that they are not secure against quantum", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-659", "line_number": 659, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-660", "filename": "Cryptography_v1.0.1_processed.txt", "content": "computers; namely, Shor’s algorithm will break both the RSA and ECC hard problems in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-660", "line_number": 660, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-661", "filename": "Cryptography_v1.0.1_processed.txt", "content": "polynomial time. Hence, the search is on for public key schemes which would resist the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-661", "line_number": 661, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-662", "filename": "Cryptography_v1.0.1_processed.txt", "content": "advent of a quantum computer. The National Institute of Standards and Technology (NIST)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-662", "line_number": 662, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-663", "filename": "Cryptography_v1.0.1_processed.txt", "content": "is currently engaged in a process to determine potential schemes which are post-quantum", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-663", "line_number": 663, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-664", "filename": "Cryptography_v1.0.1_processed.txt", "content": "secure, see [17] for more details on this.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-664", "line_number": 664, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-665", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The most prominent of these so-called post-quantum schemes are those based on hard", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-665", "line_number": 665, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-666", "filename": "Cryptography_v1.0.1_processed.txt", "content": "problems on lattices. In particular, the NTRU schemes and a variety of schemes based on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-666", "line_number": 666, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-667", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the Learning With Errors (LWE) problem, and its generalisation to polynomial rings, known as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-667", "line_number": 667, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-668", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the Ring-LWE problem. There are other proposals based on hard probles in coding theory, on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-668", "line_number": 668, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-669", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the difﬁculty of computing isogenies between elliptic curves and other constructs. NIST is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-669", "line_number": 669, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-670", "filename": "Cryptography_v1.0.1_processed.txt", "content": "currently conducting a program to select potential post-quantum replacements.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-670", "line_number": 670, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-671", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 21", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-671", "line_number": 671, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-672", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 25", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-672", "line_number": 672, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-673", "filename": "Cryptography_v1.0.1_processed.txt", "content": "for the Schnorr algorithm, one computes it via e ←H(m∥r). Then the signature equation is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-673", "line_number": 673, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-674", "filename": "Cryptography_v1.0.1_processed.txt", "content": "applied which, in the case of EC-DSA, is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-674", "line_number": 674, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-675", "filename": "Cryptography_v1.0.1_processed.txt", "content": "s ←(e + x · r)/k", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-675", "line_number": 675, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-676", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(mod q)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-676", "line_number": 676, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-677", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and, in the case of Schnorr, is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-677", "line_number": 677, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-678", "filename": "Cryptography_v1.0.1_processed.txt", "content": "s ←(k + e · x)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-678", "line_number": 678, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-679", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(mod q).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-679", "line_number": 679, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-680", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Finally, the output signature is given by (r, s) for EC-DSA and (e, s) for Schnorr.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-680", "line_number": 680, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-681", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Veriﬁcation is done by checking the equation", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-681", "line_number": 681, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-682", "filename": "Cryptography_v1.0.1_processed.txt", "content": "r = x −coord([e/s] · P + [r/s] · Q)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-682", "line_number": 682, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-683", "filename": "Cryptography_v1.0.1_processed.txt", "content": "in the case of EC-DSA, and by checking", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-683", "line_number": 683, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-684", "filename": "Cryptography_v1.0.1_processed.txt", "content": "e = H (m∥x −coord([s] · P −e · Q))", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-684", "line_number": 684, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-685", "filename": "Cryptography_v1.0.1_processed.txt", "content": "in the case of Schnorr. The key difference in the two algorithms is not the signing and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-685", "line_number": 685, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-686", "filename": "Cryptography_v1.0.1_processed.txt", "content": "veriﬁcation equations (although these do affect performance), but the fact that, with the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-686", "line_number": 686, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-687", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Schnorr scheme, the r value is also entered into the hash function to produce e. This small", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-687", "line_number": 687, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-688", "filename": "Cryptography_v1.0.1_processed.txt", "content": "distinction results in the different provable security properties of the two algorithms.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-688", "line_number": 688, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-689", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A key aspect of both EC-DSA and Schnorr signatures is that they are very brittle to exposure", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-689", "line_number": 689, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-690", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of the per-message random nonce k. If only a small number of bits of k leak to the attacker", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-690", "line_number": 690, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-691", "filename": "Cryptography_v1.0.1_processed.txt", "content": "with every signing operation, then the attacker can easily recover the secret key.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-691", "line_number": 691, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-693", "filename": "Cryptography_v1.0.1_processed.txt", "content": "STANDARD PROTOCOLS", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-693", "line_number": 693, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-694", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[4, c18]", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-694", "line_number": 694, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-695", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Cryptographic protocols are interactive operations conducted between two or more parties in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-695", "line_number": 695, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-696", "filename": "Cryptography_v1.0.1_processed.txt", "content": "order to realize some cryptographic goal. Almost all cryptographic protocols make use of the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-696", "line_number": 696, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-697", "filename": "Cryptography_v1.0.1_processed.txt", "content": "primitives we have already discussed (encryption, message authentication, secret sharing).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-697", "line_number": 697, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-698", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In this section, we discuss the two most basic forms of protocol, namely authentication and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-698", "line_number": 698, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-699", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key agreement.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-699", "line_number": 699, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-700", "filename": "Cryptography_v1.0.1_processed.txt", "content": "8.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-700", "line_number": 700, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-701", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Authentication Protocols", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-701", "line_number": 701, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-702", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In an authentication protocol, one entity (the Prover) convinces the other entity (the Veriﬁer)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-702", "line_number": 702, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-703", "filename": "Cryptography_v1.0.1_processed.txt", "content": "that they are who they claim to be, and that they are ‘online’; where ‘online’ means that the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-703", "line_number": 703, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-704", "filename": "Cryptography_v1.0.1_processed.txt", "content": "verifying party is assured that the proving party is actually responding and it is not a replay.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-704", "line_number": 704, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-705", "filename": "Cryptography_v1.0.1_processed.txt", "content": "There are three basic types of protocol: Encryption based, Message Authentication based", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-705", "line_number": 705, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-706", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and Zero-Knowledge based.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-706", "line_number": 706, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-707", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 24", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-707", "line_number": 707, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-708", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 26", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-708", "line_number": 708, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-709", "filename": "Cryptography_v1.0.1_processed.txt", "content": "8.1.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-709", "line_number": 709, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-710", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Encryption-Based Protocols", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-710", "line_number": 710, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-711", "filename": "Cryptography_v1.0.1_processed.txt", "content": "These can operate in the symmetric or public key setting. In the symmetric key setting, both", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-711", "line_number": 711, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-712", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the prover and the veriﬁer hold the same secret key, whilst in the public key setting, the prover", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-712", "line_number": 712, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-713", "filename": "Cryptography_v1.0.1_processed.txt", "content": "holds the private key and the veriﬁer holds the public key. In both settings, the veriﬁer ﬁrst", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-713", "line_number": 713, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-714", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encrypts a random nonce to the prover, the prover then decrypts this and returns it to the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-714", "line_number": 714, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-715", "filename": "Cryptography_v1.0.1_processed.txt", "content": "veriﬁer, the veriﬁer checks that the random nonce and the returned value are equivalent.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-715", "line_number": 715, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-716", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Veriﬁer", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-716", "line_number": 716, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-717", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Prover", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-717", "line_number": 717, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-718", "filename": "Cryptography_v1.0.1_processed.txt", "content": "N ←M", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-718", "line_number": 718, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-719", "filename": "Cryptography_v1.0.1_processed.txt", "content": "c ←Enc(N, pk; r)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-719", "line_number": 719, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-724", "filename": "Cryptography_v1.0.1_processed.txt", "content": "m ←Dec(c, sk)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-724", "line_number": 724, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-725", "filename": "Cryptography_v1.0.1_processed.txt", "content": "N ?= m", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-725", "line_number": 725, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-726", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The encryption scheme needs to be IND-CCA secure for the above protocol to be secure", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-726", "line_number": 726, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-727", "filename": "Cryptography_v1.0.1_processed.txt", "content": "against active attacks. The nonce N is used to prevent replay attacks.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-727", "line_number": 727, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-728", "filename": "Cryptography_v1.0.1_processed.txt", "content": "8.1.2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-728", "line_number": 728, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-729", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Message Authentication-Based Protocols", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-729", "line_number": 729, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-730", "filename": "Cryptography_v1.0.1_processed.txt", "content": "These also operate in the public key or the symmetric setting. In these protocols, the veriﬁer", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-730", "line_number": 730, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-731", "filename": "Cryptography_v1.0.1_processed.txt", "content": "sends a nonce in the clear to the prover, the prover then produces a digital signature (or a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-731", "line_number": 731, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-732", "filename": "Cryptography_v1.0.1_processed.txt", "content": "MAC in the symmetric key setting) on this nonce and passes it back to the veriﬁer. The veriﬁer", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-732", "line_number": 732, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-733", "filename": "Cryptography_v1.0.1_processed.txt", "content": "then veriﬁes the digital signature (or veriﬁes the MAC). In the following diagram we give the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-733", "line_number": 733, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-734", "filename": "Cryptography_v1.0.1_processed.txt", "content": "public key/digital signature based variant.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-734", "line_number": 734, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-735", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Veriﬁer", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-735", "line_number": 735, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-736", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Prover", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-736", "line_number": 736, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-737", "filename": "Cryptography_v1.0.1_processed.txt", "content": "N ←M", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-737", "line_number": 737, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-742", "filename": "Cryptography_v1.0.1_processed.txt", "content": "σ ←Sign(N, sk)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-742", "line_number": 742, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-743", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Verify(N, σ, pk) ?= true", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-743", "line_number": 743, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-744", "filename": "Cryptography_v1.0.1_processed.txt", "content": "8.1.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-744", "line_number": 744, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-745", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Zero-Knowledge-Based", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-745", "line_number": 745, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-746", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Zero-knowledge-based authentication protocols are the simplest examples of zero-knowledge", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-746", "line_number": 746, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-747", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protocols (see Section 9.3) available. The basic protocol is a so-called Σ- (or Sigma-) protocol", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-747", "line_number": 747, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-748", "filename": "Cryptography_v1.0.1_processed.txt", "content": "consisting of three message ﬂows; a commitment, a challenge and a response. The simplest", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-748", "line_number": 748, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-749", "filename": "Cryptography_v1.0.1_processed.txt", "content": "example is the Schnorr identiﬁcation protocol, based on the hardness of computing discrete", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-749", "line_number": 749, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-750", "filename": "Cryptography_v1.0.1_processed.txt", "content": "logarithms. In this protocol, the Prover is assumed to have a long-term secret x and an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-750", "line_number": 750, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-751", "filename": "Cryptography_v1.0.1_processed.txt", "content": "associated public key Q = [x]·P. One should note the similarity of this protocol to the Schnorr", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-751", "line_number": 751, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-752", "filename": "Cryptography_v1.0.1_processed.txt", "content": "signature scheme above.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-752", "line_number": 752, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-753", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Veriﬁer", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-753", "line_number": 753, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-754", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Prover", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-754", "line_number": 754, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-755", "filename": "Cryptography_v1.0.1_processed.txt", "content": "k ←Z/qZ", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-755", "line_number": 755, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-758", "filename": "Cryptography_v1.0.1_processed.txt", "content": "R ←[k] · P", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-758", "line_number": 758, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-759", "filename": "Cryptography_v1.0.1_processed.txt", "content": "e ←Z/qZ", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-759", "line_number": 759, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-764", "filename": "Cryptography_v1.0.1_processed.txt", "content": "s ←(k + e · x)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-764", "line_number": 764, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-765", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(mod q)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-765", "line_number": 765, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-766", "filename": "Cryptography_v1.0.1_processed.txt", "content": "R ?= [s] · P −e · Q", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-766", "line_number": 766, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-767", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Indeed, the conversion of the Schnorr authentication protocol into the Schnorr signature", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-767", "line_number": 767, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-768", "filename": "Cryptography_v1.0.1_processed.txt", "content": "scheme is an example of the Fiat–Shamir transform, which transforms any Σ-protocol into a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-768", "line_number": 768, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-769", "filename": "Cryptography_v1.0.1_processed.txt", "content": "signature scheme. If the underlying Σ-protocol is secure, in the sense of a zero-knowledge", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-769", "line_number": 769, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-770", "filename": "Cryptography_v1.0.1_processed.txt", "content": "proofs of knowledge (see Section 9.3), then the resulting signature scheme is UF-CMA.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-770", "line_number": 770, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-771", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 25", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-771", "line_number": 771, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-772", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 27", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-772", "line_number": 772, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-773", "filename": "Cryptography_v1.0.1_processed.txt", "content": "8.2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-773", "line_number": 773, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-774", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Key Agreement Protocols", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-774", "line_number": 774, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-775", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A key agreement protocol allows two parties to agree on a secret key for use in subsequent", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-775", "line_number": 775, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-776", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protocols. The security requirements of key agreement protocols are very subtle, leading to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-776", "line_number": 776, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-777", "filename": "Cryptography_v1.0.1_processed.txt", "content": "various subtle security properties that many deployed protocols may or may not have. We", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-777", "line_number": 777, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-778", "filename": "Cryptography_v1.0.1_processed.txt", "content": "recap on basic properties of key agreement protocols here, but a more complete discussion", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-778", "line_number": 778, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-779", "filename": "Cryptography_v1.0.1_processed.txt", "content": "can be found in [18]. The basic security requirements are", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-779", "line_number": 779, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-780", "filename": "Cryptography_v1.0.1_processed.txt", "content": "• The underlying key should be indistinguishable from random to the adversary, or that", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-780", "line_number": 780, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-781", "filename": "Cryptography_v1.0.1_processed.txt", "content": "at least it should be able to be used in the subsequent protocol without the adversary", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-781", "line_number": 781, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-782", "filename": "Cryptography_v1.0.1_processed.txt", "content": "breaking the subsequent protocol.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-782", "line_number": 782, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-783", "filename": "Cryptography_v1.0.1_processed.txt", "content": "• Each party is assured that only the other party has access to the secret key. This is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-783", "line_number": 783, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-784", "filename": "Cryptography_v1.0.1_processed.txt", "content": "so-called mutual authentication. In many application scenarios (e.g. in the standard", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-784", "line_number": 784, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-785", "filename": "Cryptography_v1.0.1_processed.txt", "content": "application of Transport Layer Security (TLS) to web browsing protocol), one only requires", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-785", "line_number": 785, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-786", "filename": "Cryptography_v1.0.1_processed.txt", "content": "this property of one-party, in which case we are said to only have one-way authentication.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-786", "line_number": 786, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-787", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Kerberos is an example of a (usually) symmetric key-based key agreement system. This is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-787", "line_number": 787, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-788", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a protocol that requires trusted parties to relay and generate secret keys from one party to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-788", "line_number": 788, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-789", "filename": "Cryptography_v1.0.1_processed.txt", "content": "another. It is most suited to closed corporate networks. On the public internet, protocols", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-789", "line_number": 789, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-790", "filename": "Cryptography_v1.0.1_processed.txt", "content": "like Kerberos are less useful. Thus, here one uses public key-based protocols such as TLS", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-790", "line_number": 790, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-791", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and IPSec. More advanced properties required of modern public key-based protocols are as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-791", "line_number": 791, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-792", "filename": "Cryptography_v1.0.1_processed.txt", "content": "follows.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-792", "line_number": 792, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-793", "filename": "Cryptography_v1.0.1_processed.txt", "content": "• Key Conﬁrmation: The parties know that the other party has received the same secret", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-793", "line_number": 793, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-794", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key. Sometimes this can be eliminated as the correct execution of the subsequent", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-794", "line_number": 794, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-795", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protocol using the secret key provides this conﬁrmation. This later process is called", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-795", "line_number": 795, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-796", "filename": "Cryptography_v1.0.1_processed.txt", "content": "implicit key conﬁrmation.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-796", "line_number": 796, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-797", "filename": "Cryptography_v1.0.1_processed.txt", "content": "• Forward Secrecy: The compromise of a participant’s long-term secret in the future does", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-797", "line_number": 797, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-798", "filename": "Cryptography_v1.0.1_processed.txt", "content": "not compromise the security of the secret key derived now, i.e. current conversations", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-798", "line_number": 798, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-799", "filename": "Cryptography_v1.0.1_processed.txt", "content": "are still secure in the future.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-799", "line_number": 799, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-800", "filename": "Cryptography_v1.0.1_processed.txt", "content": "• Unknown Key Share Security: This prevents one party (Alice) sharing a key with Bob,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-800", "line_number": 800, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-801", "filename": "Cryptography_v1.0.1_processed.txt", "content": "whereas Bob thinks he shares a key with Charlie, despite sharing it with Alice.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-801", "line_number": 801, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-802", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Variations on the theme of key agreement protocols include group key agreement, which", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-802", "line_number": 802, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-803", "filename": "Cryptography_v1.0.1_processed.txt", "content": "enables a group of users to agree on a key, or password based key agreement, in which two", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-803", "line_number": 803, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-804", "filename": "Cryptography_v1.0.1_processed.txt", "content": "parties only agree on a (high entropy) key if they also agree on a shared password.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-804", "line_number": 804, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-805", "filename": "Cryptography_v1.0.1_processed.txt", "content": "8.2.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-805", "line_number": 805, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-806", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Key Transport", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-806", "line_number": 806, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-807", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The most basic form of key agreement protocol is a form of key transport in which the parties", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-807", "line_number": 807, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-808", "filename": "Cryptography_v1.0.1_processed.txt", "content": "use public key encryption to exchange a random key. In the case of a one-way authenticated", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-808", "line_number": 808, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-809", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protocol, this was the traditional method of TLS operation (up until TLS version 1.2) between", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-809", "line_number": 809, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-810", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a server and a client", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-810", "line_number": 810, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-811", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Client", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-811", "line_number": 811, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-812", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Server", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-812", "line_number": 812, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-815", "filename": "Cryptography_v1.0.1_processed.txt", "content": "k ←K", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-815", "line_number": 815, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-816", "filename": "Cryptography_v1.0.1_processed.txt", "content": "c ←Enc(k, pk; r)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-816", "line_number": 816, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-819", "filename": "Cryptography_v1.0.1_processed.txt", "content": "k ←Dec(c, sk)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-819", "line_number": 819, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-820", "filename": "Cryptography_v1.0.1_processed.txt", "content": "This protocol produced the pre-master secret in older versions of TLS (pre-TLS 1.2). To derive", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-820", "line_number": 820, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-821", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the ﬁnal secret in TLS, further nonces were exchanged between the parties (to ensure that", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-821", "line_number": 821, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-822", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 26", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-822", "line_number": 822, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-823", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 28", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-823", "line_number": 823, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-824", "filename": "Cryptography_v1.0.1_processed.txt", "content": "both parties were alive and the key was fresh). Then, a master secret was derived from the pre-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-824", "line_number": 824, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-825", "filename": "Cryptography_v1.0.1_processed.txt", "content": "master secret and the nonces. Finally, key conﬁrmation was provided by the entire protocol", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-825", "line_number": 825, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-826", "filename": "Cryptography_v1.0.1_processed.txt", "content": "transcript being hashed and encrypted under the master secret (the so-called FINISHED", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-826", "line_number": 826, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-827", "filename": "Cryptography_v1.0.1_processed.txt", "content": "message). In TLS, the resulting key is not indistinguishable from random as the encrypted", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-827", "line_number": 827, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-828", "filename": "Cryptography_v1.0.1_processed.txt", "content": "FINISHED message provides the adversary with a trivial check to determine whether a key is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-828", "line_number": 828, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-829", "filename": "Cryptography_v1.0.1_processed.txt", "content": "real or not. However, the protocol can be shown to be secure for the purposes of using the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-829", "line_number": 829, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-830", "filename": "Cryptography_v1.0.1_processed.txt", "content": "master secret to produce a secure bi-directional channel between the server and the client.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-830", "line_number": 830, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-831", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A more basic issue with the above protocol is that it is not forward-secure. Any adversary who", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-831", "line_number": 831, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-832", "filename": "Cryptography_v1.0.1_processed.txt", "content": "records a session now, and in the future manages to obtain the server’s long-term secret sk,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-832", "line_number": 832, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-833", "filename": "Cryptography_v1.0.1_processed.txt", "content": "can obtain the pre-master secret, and hence decrypt the entire session.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-833", "line_number": 833, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-834", "filename": "Cryptography_v1.0.1_processed.txt", "content": "8.2.2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-834", "line_number": 834, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-835", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Difﬁe–Hellman Key Agreement", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-835", "line_number": 835, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-836", "filename": "Cryptography_v1.0.1_processed.txt", "content": "To avoid the issues with forward secrecy of RSA-based key transport, modern protocols make", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-836", "line_number": 836, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-837", "filename": "Cryptography_v1.0.1_processed.txt", "content": "use of Difﬁe–Hellman key exchange. This allows two parties to agree on a uniformly random", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-837", "line_number": 837, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-838", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key, which is indistinguishable from random assuming the Decision Difﬁe–Hellman problem", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-838", "line_number": 838, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-839", "filename": "Cryptography_v1.0.1_processed.txt", "content": "is hard", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-839", "line_number": 839, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-840", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Alice", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-840", "line_number": 840, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-841", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Bob", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-841", "line_number": 841, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-842", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a ←Z/qZ", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-842", "line_number": 842, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-843", "filename": "Cryptography_v1.0.1_processed.txt", "content": "b ←Z/qZ", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-843", "line_number": 843, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-844", "filename": "Cryptography_v1.0.1_processed.txt", "content": "QA ←[a] · P", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-844", "line_number": 844, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-846", "filename": "Cryptography_v1.0.1_processed.txt", "content": "−−→", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-846", "line_number": 846, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-848", "filename": "Cryptography_v1.0.1_processed.txt", "content": "←−−", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-848", "line_number": 848, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-849", "filename": "Cryptography_v1.0.1_processed.txt", "content": "QB ←[b] · P", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-849", "line_number": 849, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-850", "filename": "Cryptography_v1.0.1_processed.txt", "content": "K ←[a] · QB", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-850", "line_number": 850, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-851", "filename": "Cryptography_v1.0.1_processed.txt", "content": "K ←[b] · QA", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-851", "line_number": 851, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-852", "filename": "Cryptography_v1.0.1_processed.txt", "content": "This protocol provides forward secrecy, but provides no form of authentication. Due to this,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-852", "line_number": 852, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-853", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the protocol suffers from a man-in-the-middle attack. To obtain mutual authentication, the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-853", "line_number": 853, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-854", "filename": "Cryptography_v1.0.1_processed.txt", "content": "message ﬂow of QA is signed by Alice’s public key and the message ﬂow of QB is signed by", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-854", "line_number": 854, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-855", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Bob’s public key. This prevents the man-in-the-middle attack. However, since the signatures", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-855", "line_number": 855, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-856", "filename": "Cryptography_v1.0.1_processed.txt", "content": "are not bound into the message, the signed-Difﬁe–Hellman protocol suffers from an unknown-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-856", "line_number": 856, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-857", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key-share attack; an adversary (Charlie) can strip Alice’s signature from QA and replace it with", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-857", "line_number": 857, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-858", "filename": "Cryptography_v1.0.1_processed.txt", "content": "their signature. The adversary does not learn the secret, but does convince Bob he is talking", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-858", "line_number": 858, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-859", "filename": "Cryptography_v1.0.1_processed.txt", "content": "to another entity.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-859", "line_number": 859, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-860", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The one-way authenticated version of Difﬁe–Hellman key agreement is the preferred method", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-860", "line_number": 860, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-861", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of key agreement in modern TLS deployments, and is the only method of key agreement", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-861", "line_number": 861, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-862", "filename": "Cryptography_v1.0.1_processed.txt", "content": "supported by TLS 1.3. In TLS, the FINISHED message, which hashes the entire transcript,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-862", "line_number": 862, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-863", "filename": "Cryptography_v1.0.1_processed.txt", "content": "prevents the above unknown-key-share attack. However, it also prevents the protocol from", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-863", "line_number": 863, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-864", "filename": "Cryptography_v1.0.1_processed.txt", "content": "producing keys which are indistinguishable from random, as mentioned above.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-864", "line_number": 864, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-865", "filename": "Cryptography_v1.0.1_processed.txt", "content": "8.2.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-865", "line_number": 865, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-866", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Station-to-Station Protocol", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-866", "line_number": 866, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-867", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The Station-to-Station (STS) protocol can be used to prevent unknown-key-share attacks", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-867", "line_number": 867, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-868", "filename": "Cryptography_v1.0.1_processed.txt", "content": "on signed Difﬁe–Hellman and maintain key indistinguishability. In this protocol, the Difﬁe–", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-868", "line_number": 868, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-869", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Hellman derived key is used to encrypt the signatures, thus ensuring the signatures cannot be", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-869", "line_number": 869, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-870", "filename": "Cryptography_v1.0.1_processed.txt", "content": "stripped off the messages. In addition, the signatures are applied to the transcript so as to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-870", "line_number": 870, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-871", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 27", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-871", "line_number": 871, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-872", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 30", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-872", "line_number": 872, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-873", "filename": "Cryptography_v1.0.1_processed.txt", "content": "messages are two elements M0 and M1 in an elliptic curve group E(Fp) of prime order q.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-873", "line_number": 873, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-874", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Sender", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-874", "line_number": 874, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-875", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Receiver", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-875", "line_number": 875, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-876", "filename": "Cryptography_v1.0.1_processed.txt", "content": "C ←E(Fp)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-876", "line_number": 876, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-879", "filename": "Cryptography_v1.0.1_processed.txt", "content": "x ←(Z/qZ)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-879", "line_number": 879, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-880", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Qb ←[x] · P", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-880", "line_number": 880, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-881", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Q1−b ←C −Qb", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-881", "line_number": 881, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-884", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Q1 ←C −Q0", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-884", "line_number": 884, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-885", "filename": "Cryptography_v1.0.1_processed.txt", "content": "k ←(Z/qZ)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-885", "line_number": 885, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-886", "filename": "Cryptography_v1.0.1_processed.txt", "content": "C1 ←[k] · P", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-886", "line_number": 886, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-887", "filename": "Cryptography_v1.0.1_processed.txt", "content": "E0 ←M0 + [k] · Q0", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-887", "line_number": 887, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-888", "filename": "Cryptography_v1.0.1_processed.txt", "content": "E1 ←M1 + [k] · Q1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-888", "line_number": 888, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-889", "filename": "Cryptography_v1.0.1_processed.txt", "content": "C1,E0,E1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-889", "line_number": 889, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-891", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Mb ←Eb −[x] · C1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-891", "line_number": 891, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-892", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The extension to an actively secure protocol is only a little more complex, but beyond the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-892", "line_number": 892, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-893", "filename": "Cryptography_v1.0.1_processed.txt", "content": "scope of this article.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-893", "line_number": 893, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-894", "filename": "Cryptography_v1.0.1_processed.txt", "content": "9.2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-894", "line_number": 894, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-895", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Private Information Retrieval and ORAM", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-895", "line_number": 895, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-896", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A Private Information Retrieval (PIR) protocol is one which enables a computer to retrieve", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-896", "line_number": 896, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-897", "filename": "Cryptography_v1.0.1_processed.txt", "content": "data from a server held database, without revealing the exact item which is retrieved. If the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-897", "line_number": 897, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-898", "filename": "Cryptography_v1.0.1_processed.txt", "content": "server has n data items then this is related to a 1-out-of-n OT protocol. However, in PIR we", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-898", "line_number": 898, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-899", "filename": "Cryptography_v1.0.1_processed.txt", "content": "do not insist that the user does not learn anything else about the servers data, we only care", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-899", "line_number": 899, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-900", "filename": "Cryptography_v1.0.1_processed.txt", "content": "about privacy of the user query. In addition protocols for PIR are meant to be run many times,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-900", "line_number": 900, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-901", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and we are interested in hiding the total set of access patterns, i.e. even whether a data item", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-901", "line_number": 901, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-902", "filename": "Cryptography_v1.0.1_processed.txt", "content": "is retrieved multiple times. The goal of PIR protocols is to obtain greater efﬁciency than the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-902", "line_number": 902, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-903", "filename": "Cryptography_v1.0.1_processed.txt", "content": "trivial solution of the server sending the user the entire database.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-903", "line_number": 903, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-904", "filename": "Cryptography_v1.0.1_processed.txt", "content": "An Oblivious Random Access Memory (ORAM) protocol is similar but now we not only allow", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-904", "line_number": 904, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-905", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the user to obliviously read from the server’s database, we also allow the user to write to the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-905", "line_number": 905, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-906", "filename": "Cryptography_v1.0.1_processed.txt", "content": "database. So as to protect the write queries the server held database must now be held in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-906", "line_number": 906, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-907", "filename": "Cryptography_v1.0.1_processed.txt", "content": "an encrypted form (so what is written cannot be determined by the server). In addition the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-907", "line_number": 907, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-908", "filename": "Cryptography_v1.0.1_processed.txt", "content": "access patterns, i.e. where data is written to and read from, needs to be hidden from the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-908", "line_number": 908, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-909", "filename": "Cryptography_v1.0.1_processed.txt", "content": "server.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-909", "line_number": 909, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-910", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 29", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-910", "line_number": 910, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-911", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 31", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-911", "line_number": 911, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-912", "filename": "Cryptography_v1.0.1_processed.txt", "content": "9.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-912", "line_number": 912, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-913", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Zero-Knowledge", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-913", "line_number": 913, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-914", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A Zero-Knowledge protocol is a protocol executed between a Prover and a Veriﬁer in which", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-914", "line_number": 914, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-915", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the Prover demonstrates that a statement is true, without revealing why the statement is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-915", "line_number": 915, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-916", "filename": "Cryptography_v1.0.1_processed.txt", "content": "true. The concept is used in many places in cryptography, to construct signature schemes, to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-916", "line_number": 916, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-917", "filename": "Cryptography_v1.0.1_processed.txt", "content": "attest ones identity, and to construct more advanced protocols. An introduction to the more", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-917", "line_number": 917, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-918", "filename": "Cryptography_v1.0.1_processed.txt", "content": "theoretical aspects of zero-knowledge can be found in [6]. More formally, consider an NP", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-918", "line_number": 918, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-919", "filename": "Cryptography_v1.0.1_processed.txt", "content": "language L (i.e. a set of statements x which can be veriﬁed to be true in polynomial time given", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-919", "line_number": 919, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-920", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a witness or proof w). An interactive proof system for L is a sequence of protocol executions", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-920", "line_number": 920, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-921", "filename": "Cryptography_v1.0.1_processed.txt", "content": "by an (inﬁnitely powerful) Prover P and a (probabilistic polynomial time) Veriﬁer V , which on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-921", "line_number": 921, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-922", "filename": "Cryptography_v1.0.1_processed.txt", "content": "joint input x proceeds as follows:", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-922", "line_number": 922, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-923", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Veriﬁer", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-923", "line_number": 923, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-924", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Prover", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-924", "line_number": 924, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-927", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(p1, s′", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-927", "line_number": 927, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-928", "filename": "Cryptography_v1.0.1_processed.txt", "content": "1) ←P1(x)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-928", "line_number": 928, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-929", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(v1, s1) ←V1(x, p1)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-929", "line_number": 929, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-934", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(p2, s′", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-934", "line_number": 934, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-935", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2) ←P2(s′", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-935", "line_number": 935, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-936", "filename": "Cryptography_v1.0.1_processed.txt", "content": "1, v1)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-936", "line_number": 936, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-937", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(v2, s2) ←V2(s1, p2)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-937", "line_number": 937, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-942", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(p3, s′", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-942", "line_number": 942, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-943", "filename": "Cryptography_v1.0.1_processed.txt", "content": "3) ←P3(s′", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-943", "line_number": 943, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-944", "filename": "Cryptography_v1.0.1_processed.txt", "content": "2, v2)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-944", "line_number": 944, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-945", "filename": "Cryptography_v1.0.1_processed.txt", "content": "...", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-945", "line_number": 945, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-946", "filename": "Cryptography_v1.0.1_processed.txt", "content": "...", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-946", "line_number": 946, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-949", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(pr, s′", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-949", "line_number": 949, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-950", "filename": "Cryptography_v1.0.1_processed.txt", "content": "r) ←Pr(sr1, vr1)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-950", "line_number": 950, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-951", "filename": "Cryptography_v1.0.1_processed.txt", "content": "By the end of the protocol, the Veriﬁer will output either true or false. An interactive proof", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-951", "line_number": 951, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-952", "filename": "Cryptography_v1.0.1_processed.txt", "content": "system is one which is both complete and sound", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-952", "line_number": 952, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-953", "filename": "Cryptography_v1.0.1_processed.txt", "content": "• Completeness: If the statement x is true, i.e. x ∈L, then if the Prover is honest then the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-953", "line_number": 953, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-954", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Veriﬁer will output true.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-954", "line_number": 954, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-955", "filename": "Cryptography_v1.0.1_processed.txt", "content": "• Soundness: If the statement is false, i.e. x ̸∈L, then no cheating Prover can convince", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-955", "line_number": 955, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-956", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the Veriﬁer with probability greater than p.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-956", "line_number": 956, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-957", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Note that even if p is large (say p = 0.5) then repeating the proof multiple times can reduce", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-957", "line_number": 957, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-958", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the soundness probability to anything desired. Of course, protocols with small p to start with", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-958", "line_number": 958, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-959", "filename": "Cryptography_v1.0.1_processed.txt", "content": "are going to be more efﬁcient.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-959", "line_number": 959, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-960", "filename": "Cryptography_v1.0.1_processed.txt", "content": "For any NP statement, there is a trivial proof system. Namely, the Prover simply sends over", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-960", "line_number": 960, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-961", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the witness w which the Veriﬁer then veriﬁes. However, this reveals the witness. In a zero-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-961", "line_number": 961, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-962", "filename": "Cryptography_v1.0.1_processed.txt", "content": "knowledge proof, we obtain the same goal, but the Veriﬁer learns nothing bar the fact that", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-962", "line_number": 962, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-963", "filename": "Cryptography_v1.0.1_processed.txt", "content": "x ∈L. To formally deﬁne zero-knowledge, we insist that there is a (probabilistic polynomial", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-963", "line_number": 963, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-964", "filename": "Cryptography_v1.0.1_processed.txt", "content": "time) simulator S which can produce protocol transcripts identical to the transcripts produced", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-964", "line_number": 964, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-965", "filename": "Cryptography_v1.0.1_processed.txt", "content": "between a Veriﬁer and an honest Prover; except the simulator has no access to the Prover.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-965", "line_number": 965, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-966", "filename": "Cryptography_v1.0.1_processed.txt", "content": "This implies that the Veriﬁer cannot use the transcript to perform any other task, since what it", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-966", "line_number": 966, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-967", "filename": "Cryptography_v1.0.1_processed.txt", "content": "learned from the transcript it could have produced without the Prover by simply running the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-967", "line_number": 967, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-968", "filename": "Cryptography_v1.0.1_processed.txt", "content": "simulator.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-968", "line_number": 968, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-969", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A zero-knowledge proof is said to be perfect zero-knowledge if the distribution of transcripts", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-969", "line_number": 969, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-970", "filename": "Cryptography_v1.0.1_processed.txt", "content": "produced by the simulator is identical to those produced between a valid prover and veriﬁer. If", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-970", "line_number": 970, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-971", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the two distributions only cannot be distinguished by an efﬁcient algorithm we say we have", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-971", "line_number": 971, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-972", "filename": "Cryptography_v1.0.1_processed.txt", "content": "computational zero-knowledge.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-972", "line_number": 972, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-973", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A zero-knowledge proof is said to be a proof of knowledge if a Veriﬁer given rewinding access", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-973", "line_number": 973, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-974", "filename": "Cryptography_v1.0.1_processed.txt", "content": "to the prover (i.e. the Veriﬁer can keep resetting the Prover to a previous protocol state and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-974", "line_number": 974, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-975", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 30", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-975", "line_number": 975, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-976", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 32", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-976", "line_number": 976, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-977", "filename": "Cryptography_v1.0.1_processed.txt", "content": "continue executing) can extract the underlying witness w. This implies that the Prover must", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-977", "line_number": 977, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-978", "filename": "Cryptography_v1.0.1_processed.txt", "content": "‘know’ w since we can extract w from it.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-978", "line_number": 978, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-979", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A non-interactive zero-knowledge proof is one in which there is no message ﬂowing from the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-979", "line_number": 979, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-980", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Veriﬁer to the Prover, and only one message ﬂowing from the Prover to the Veriﬁer. Such non-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-980", "line_number": 980, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-981", "filename": "Cryptography_v1.0.1_processed.txt", "content": "interactive proofs require additional setup assumptions, such as a Common Reference String", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-981", "line_number": 981, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-982", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(CRS), or they require one to assume the Random Oracle Model. Traditionally these are applied", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-982", "line_number": 982, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-983", "filename": "Cryptography_v1.0.1_processed.txt", "content": "to speciﬁc number theoretic statements, such to show knowledge of a discrete logarithm", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-983", "line_number": 983, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-984", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(see the next section on Σ-protocols), however recently so called Succinct Non-Interactive", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-984", "line_number": 984, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-985", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Arguments of Knowledge (SNARKs) have been developed which enable such non-interactive", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-985", "line_number": 985, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-986", "filename": "Cryptography_v1.0.1_processed.txt", "content": "arguments for more complex statements. Such SNARKs are ﬁnding applications in some", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-986", "line_number": 986, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-987", "filename": "Cryptography_v1.0.1_processed.txt", "content": "blockchain systems.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-987", "line_number": 987, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-988", "filename": "Cryptography_v1.0.1_processed.txt", "content": "9.3.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-988", "line_number": 988, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-989", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Σ-Protocols", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-989", "line_number": 989, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-990", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The earlier Σ-protocol for identiﬁcation is a zero-knowledge proof of knowledge.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-990", "line_number": 990, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-991", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Veriﬁer", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-991", "line_number": 991, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-992", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Prover", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-992", "line_number": 992, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-993", "filename": "Cryptography_v1.0.1_processed.txt", "content": "k ←Z/qZ", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-993", "line_number": 993, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-996", "filename": "Cryptography_v1.0.1_processed.txt", "content": "R ←[k] · P", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-996", "line_number": 996, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-997", "filename": "Cryptography_v1.0.1_processed.txt", "content": "e ←Z/qZ", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-997", "line_number": 997, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1002", "filename": "Cryptography_v1.0.1_processed.txt", "content": "s ←(k + e · x)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1002", "line_number": 1002, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1003", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(mod q)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1003", "line_number": 1003, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1004", "filename": "Cryptography_v1.0.1_processed.txt", "content": "R ?= [s] · P −e · Q", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1004", "line_number": 1004, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1005", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The protocol is obviously complete since Q = [x] · P, and the soundness error is 1/q. That it is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1005", "line_number": 1005, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1006", "filename": "Cryptography_v1.0.1_processed.txt", "content": "zero-knowledge follows from the following simulation, which ﬁrst samples e, s ←Z/qZ and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1006", "line_number": 1006, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1007", "filename": "Cryptography_v1.0.1_processed.txt", "content": "then computes R = [s]P −e · Q; the resulting simulated transcript being (R, e, s). Namely, the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1007", "line_number": 1007, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1008", "filename": "Cryptography_v1.0.1_processed.txt", "content": "simulator computes things in the wrong order.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1008", "line_number": 1008, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1009", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The protocol is also a proof of knowledge since if we execute two protocol runs with the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1009", "line_number": 1009, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1010", "filename": "Cryptography_v1.0.1_processed.txt", "content": "same R value but different e-values (e1 and e2) then we obtain two s-values (s1 and s2). This is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1010", "line_number": 1010, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1011", "filename": "Cryptography_v1.0.1_processed.txt", "content": "done by rewinding the prover to just after it has sent its ﬁrst message. If the two obtained", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1011", "line_number": 1011, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1012", "filename": "Cryptography_v1.0.1_processed.txt", "content": "transcripts (R, e1, s1) and (R, e2, s2) are both valid then we have", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1012", "line_number": 1012, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1013", "filename": "Cryptography_v1.0.1_processed.txt", "content": "R = [s1] · P −e1 · Q = [s2] · P −e2 · Q", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1013", "line_number": 1013, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1014", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and so", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1014", "line_number": 1014, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1015", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[s1 −s2] · P = [e1 −e2] · Q", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1015", "line_number": 1015, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1016", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and hence", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1016", "line_number": 1016, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1017", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Q =", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1017", "line_number": 1017, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1018", "filename": "Cryptography_v1.0.1_processed.txt", "content": "\u0014s1 −s2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1018", "line_number": 1018, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1019", "filename": "Cryptography_v1.0.1_processed.txt", "content": "e1 −e2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1019", "line_number": 1019, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1021", "filename": "Cryptography_v1.0.1_processed.txt", "content": "· P", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1021", "line_number": 1021, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1022", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and hence we ‘extract’ the secret x from x = (s1 −s2)/(e1 −e2) (mod q).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1022", "line_number": 1022, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1023", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 31", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1023", "line_number": 1023, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1024", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 33", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1024", "line_number": 1024, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1025", "filename": "Cryptography_v1.0.1_processed.txt", "content": "9.4", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1025", "line_number": 1025, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1026", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Secure Multi-Party Computation", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1026", "line_number": 1026, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1027", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Multi-Party Computation (MPC) is a technique to enable a set of parties to compute on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1027", "line_number": 1027, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1028", "filename": "Cryptography_v1.0.1_processed.txt", "content": "data, without learning anything about the data. Consider n parties P1, . . . , Pn each with input", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1028", "line_number": 1028, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1029", "filename": "Cryptography_v1.0.1_processed.txt", "content": "x1, . . . , xn. MPC allows these parties to compute any function f(x1, . . . , xn) of these inputs", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1029", "line_number": 1029, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1030", "filename": "Cryptography_v1.0.1_processed.txt", "content": "without revealing any information about the xi to each other, bar what can be deduced from", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1030", "line_number": 1030, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1031", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the output of the function f. A general introduction to the theory of such protocols can be", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1031", "line_number": 1031, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1032", "filename": "Cryptography_v1.0.1_processed.txt", "content": "found in [7].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1032", "line_number": 1032, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1033", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In an MPC protocol, we assume that a subset of the parties A is corrupt. In statically secure", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1033", "line_number": 1033, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1034", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protocols, this set is deﬁned at the start of the protocol, but remains unknown to the honest", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1034", "line_number": 1034, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1035", "filename": "Cryptography_v1.0.1_processed.txt", "content": "parties. In an adaptively secure protocol, the set can be chosen by the adversary as the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1035", "line_number": 1035, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1036", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protocol progresses. An MPC protocol is said to be passively secure if the parties in A follow", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1036", "line_number": 1036, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1037", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the protocol, but try to learn data about the honest parties’ inputs from their joint view. In an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1037", "line_number": 1037, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1038", "filename": "Cryptography_v1.0.1_processed.txt", "content": "actively secure protocol, the parties in A can arbitrarily deviate from the protocol.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1038", "line_number": 1038, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1039", "filename": "Cryptography_v1.0.1_processed.txt", "content": "An MPC protocol should be correct, i.e. it outputs the correct answer if all parties follow the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1039", "line_number": 1039, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1040", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protocol. It should also be secure, i.e. the dishonest parties should learn nothing about the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1040", "line_number": 1040, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1041", "filename": "Cryptography_v1.0.1_processed.txt", "content": "inputs of the honest parties. In the case of active adversaries, a protocol is said to be robust if", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1041", "line_number": 1041, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1042", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the honest parties will obtain the correct output, even when the dishonest parties deviate from", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1042", "line_number": 1042, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1043", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the protocol. A protocol which is not robust, but which aborts with overwhelming probability", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1043", "line_number": 1043, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1044", "filename": "Cryptography_v1.0.1_processed.txt", "content": "when a dishonest party deviates, is said to be an actively secure MPC protocol with abort.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1044", "line_number": 1044, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1045", "filename": "Cryptography_v1.0.1_processed.txt", "content": "MPC protocols are catagorized by whether they utilize information-theoretic primitives (namely", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1045", "line_number": 1045, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1046", "filename": "Cryptography_v1.0.1_processed.txt", "content": "secret sharing), or they utilize computationally secure primitives (such as symmetric-key and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1046", "line_number": 1046, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1047", "filename": "Cryptography_v1.0.1_processed.txt", "content": "public-key encryption). They are also further characterized by the properties of the set A. Of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1047", "line_number": 1047, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1048", "filename": "Cryptography_v1.0.1_processed.txt", "content": "particular interest is when the size t of A is bounded by a function of n (so-called threshold", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1048", "line_number": 1048, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1049", "filename": "Cryptography_v1.0.1_processed.txt", "content": "schemes). The cases of particular interest are t < n, t < n/2, and t < n/3; the threshold cases", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1049", "line_number": 1049, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1050", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of t < n/2 and t < n/3 can be generalized to Q2 and Q3 access structures, as discussed in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1050", "line_number": 1050, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1051", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Section 3.2.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1051", "line_number": 1051, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1052", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In the information-theoretic setting, one can achieve passively secure MPC in the case of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1052", "line_number": 1052, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1053", "filename": "Cryptography_v1.0.1_processed.txt", "content": "t < n/2 (or Q2 access structures). Actively secure robust MPC is possible in the information-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1053", "line_number": 1053, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1054", "filename": "Cryptography_v1.0.1_processed.txt", "content": "theoretic setting when we have t < n/3 (or Q3 access structures). All of these protocols", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1054", "line_number": 1054, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1055", "filename": "Cryptography_v1.0.1_processed.txt", "content": "are achieved using secret sharing schemes. A detailed study of secret sharing based MPC", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1055", "line_number": 1055, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1056", "filename": "Cryptography_v1.0.1_processed.txt", "content": "protocols is given in [19].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1056", "line_number": 1056, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1057", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In the computational setting, one can achieve actively secure robust computation when", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1057", "line_number": 1057, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1058", "filename": "Cryptography_v1.0.1_processed.txt", "content": "t < n/2, using Oblivious Transfer as the basic computational foundation. The interesting case", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1058", "line_number": 1058, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1059", "filename": "Cryptography_v1.0.1_processed.txt", "content": "of two party computation is done using the Yao protocol. This protocol has one party (the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1059", "line_number": 1059, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1060", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Circuit Creator, also called the Garbler) ‘encrypting’ a boolean function gate by gate using a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1060", "line_number": 1060, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1061", "filename": "Cryptography_v1.0.1_processed.txt", "content": "cipher such as AES, the circuit is then sent to the other party (called the Circuit Evaluator).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1061", "line_number": 1061, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1062", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The Evaluator then obtains the ‘keys’ for their input values from the Creator using Oblivious", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1062", "line_number": 1062, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1063", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Transfer, and can then evaluate the circuit. A detailed study of two party Yao based protocols", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1063", "line_number": 1063, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1064", "filename": "Cryptography_v1.0.1_processed.txt", "content": "is given in [20].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1064", "line_number": 1064, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1065", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Modern MPC protocols have looked at active security with abort in the case of t < n. The", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1065", "line_number": 1065, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1066", "filename": "Cryptography_v1.0.1_processed.txt", "content": "modern protocols are divided into a function-dependent ofﬂine phase, which requires public", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1066", "line_number": 1066, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1067", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key functionality but which is function independent, then a function-dependent online phase", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1067", "line_number": 1067, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1068", "filename": "Cryptography_v1.0.1_processed.txt", "content": "which mainly uses information-theoretic primitives. Since information theoretic primitives", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1068", "line_number": 1068, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1069", "filename": "Cryptography_v1.0.1_processed.txt", "content": "are usually very fast, this means the time-critical online phase can be executed as fast as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1069", "line_number": 1069, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1070", "filename": "Cryptography_v1.0.1_processed.txt", "content": "possible.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1070", "line_number": 1070, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1071", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 32", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1071", "line_number": 1071, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1072", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 34", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1072", "line_number": 1072, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1074", "filename": "Cryptography_v1.0.1_processed.txt", "content": "PUBLIC KEY ENCRYPTION/SIGNATURES WITH SPECIAL", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1074", "line_number": 1074, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1075", "filename": "Cryptography_v1.0.1_processed.txt", "content": "PROPERTIES", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1075", "line_number": 1075, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1076", "filename": "Cryptography_v1.0.1_processed.txt", "content": "[3, c13]", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1076", "line_number": 1076, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1077", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A major part of modern cryptography over the last twenty years has been the construction of", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1077", "line_number": 1077, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1078", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encryption and signature algorithms with special properties or advanced functionalities. A", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1078", "line_number": 1078, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1079", "filename": "Cryptography_v1.0.1_processed.txt", "content": "number of the following have been deployed in specialized systems (for example, U-PROVE,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1079", "line_number": 1079, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1080", "filename": "Cryptography_v1.0.1_processed.txt", "content": "IDEMIX, attestation protocols and some cryptocurrencies). We recap the main variants below,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1080", "line_number": 1080, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1081", "filename": "Cryptography_v1.0.1_processed.txt", "content": "giving for each one the basic idea behind their construction.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1081", "line_number": 1081, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1082", "filename": "Cryptography_v1.0.1_processed.txt", "content": "10.1", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1082", "line_number": 1082, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1083", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Group Signatures", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1083", "line_number": 1083, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1084", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A group signature scheme deﬁned a group public key pk, associated to a number of secret", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1084", "line_number": 1084, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1085", "filename": "Cryptography_v1.0.1_processed.txt", "content": "keys sk1, . . . , skn. The public key is usually determined by an entity called a Group Manager,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1085", "line_number": 1085, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1086", "filename": "Cryptography_v1.0.1_processed.txt", "content": "during an interaction with the group members. Given a group signature s, one cannot tell which", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1086", "line_number": 1086, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1087", "filename": "Cryptography_v1.0.1_processed.txt", "content": "secret key signed it, although one is guaranteed that one did. Thus group signatures provide", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1087", "line_number": 1087, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1088", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the anonymity of a Signer. Most group signature algorithms have a special entity called an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1088", "line_number": 1088, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1089", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Opener who has some secret information which enables them to revoke the anonymity of a", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1089", "line_number": 1089, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1090", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Signer. This last property ensures one can identify group members who act dishonestly in", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1090", "line_number": 1090, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1091", "filename": "Cryptography_v1.0.1_processed.txt", "content": "some way.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1091", "line_number": 1091, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1092", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A group signature scheme can either support static or dynamic groups. In a static group", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1092", "line_number": 1092, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1093", "filename": "Cryptography_v1.0.1_processed.txt", "content": "signature scheme, the group members are ﬁxed at the start of the protocol, when the public", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1093", "line_number": 1093, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1094", "filename": "Cryptography_v1.0.1_processed.txt", "content": "key is ﬁxed. In a dynamic group signature scheme the group manager can add members into", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1094", "line_number": 1094, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1095", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the group as the protocol proceeds, and (often) revoke members as well.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1095", "line_number": 1095, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1096", "filename": "Cryptography_v1.0.1_processed.txt", "content": "An example of this type of signature scheme which is currently deployed is the Direct Anony-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1096", "line_number": 1096, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1097", "filename": "Cryptography_v1.0.1_processed.txt", "content": "mous Attestation (DAA) protocol; which is essentially a group signature scheme in which the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1097", "line_number": 1097, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1098", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Opener is replaced with a form of user controlled linkability; i.e. a signer can decide whether", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1098", "line_number": 1098, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1099", "filename": "Cryptography_v1.0.1_processed.txt", "content": "two signatures output by the speciﬁc signer can be linked or not.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1099", "line_number": 1099, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1100", "filename": "Cryptography_v1.0.1_processed.txt", "content": "10.2", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1100", "line_number": 1100, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1101", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Ring Signatures", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1101", "line_number": 1101, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1102", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A ring signature scheme is much like a group signature scheme, but in a ring signature there is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1102", "line_number": 1102, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1103", "filename": "Cryptography_v1.0.1_processed.txt", "content": "no group manager. Each user in a ring signature scheme has a public/private key pair (pki, ski).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1103", "line_number": 1103, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1104", "filename": "Cryptography_v1.0.1_processed.txt", "content": "At the point of signing, the Signer selects a subset of the public keys (containing this own),", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1104", "line_number": 1104, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1105", "filename": "Cryptography_v1.0.1_processed.txt", "content": "which is called a ring of public keys, and then produces a signature. The Receiver knows the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1105", "line_number": 1105, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1106", "filename": "Cryptography_v1.0.1_processed.txt", "content": "signature was produced by someone in the ring, but not which member of the ring.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1106", "line_number": 1106, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1107", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 33", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1107", "line_number": 1107, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1108", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 35", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1108", "line_number": 1108, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1109", "filename": "Cryptography_v1.0.1_processed.txt", "content": "10.3", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1109", "line_number": 1109, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1110", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Blind Signatures", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1110", "line_number": 1110, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1111", "filename": "Cryptography_v1.0.1_processed.txt", "content": "A blind signature scheme is a two party protocol in which a one party (the User) wants to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1111", "line_number": 1111, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1112", "filename": "Cryptography_v1.0.1_processed.txt", "content": "obtain the signature on a message by a second party (the Signer). However, the Signer is", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1112", "line_number": 1112, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1113", "filename": "Cryptography_v1.0.1_processed.txt", "content": "not allowed to know which message is being signed. For example, the Signer may be simply", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1113", "line_number": 1113, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1114", "filename": "Cryptography_v1.0.1_processed.txt", "content": "notarising that something happened, but does not need to know precisely what. Security", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1114", "line_number": 1114, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1115", "filename": "Cryptography_v1.0.1_processed.txt", "content": "requires that the Signer should not learn anything about any message passed to it for signing,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1115", "line_number": 1115, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1116", "filename": "Cryptography_v1.0.1_processed.txt", "content": "and the user should not obtain the signature on any message other than those they submitted", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1116", "line_number": 1116, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1117", "filename": "Cryptography_v1.0.1_processed.txt", "content": "for signing.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1117", "line_number": 1117, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1118", "filename": "Cryptography_v1.0.1_processed.txt", "content": "10.4", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1118", "line_number": 1118, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1119", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Identity-Based Encryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1119", "line_number": 1119, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1120", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In normal public key encryption, a user obtains a public key pk, along with a certiﬁcate C. The", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1120", "line_number": 1120, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1121", "filename": "Cryptography_v1.0.1_processed.txt", "content": "certiﬁcate is produced by a trusted third party, and binds the public key to the identity. Usually,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1121", "line_number": 1121, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1122", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a certiﬁcate is a digitally signed statement containing the public key and the associated user", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1122", "line_number": 1122, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1123", "filename": "Cryptography_v1.0.1_processed.txt", "content": "identity. So, when sending a message to Alice the Sender is sure that Alice is the legitimate", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1123", "line_number": 1123, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1124", "filename": "Cryptography_v1.0.1_processed.txt", "content": "holder of public key pk.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1124", "line_number": 1124, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1125", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Identity Based Encryption (IBE) is an encryption scheme which dispenses with the need for", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1125", "line_number": 1125, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1126", "filename": "Cryptography_v1.0.1_processed.txt", "content": "certiﬁcate authorities, and certiﬁcates. To encrypt to a user, say Alice, we simply use her", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1126", "line_number": 1126, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1127", "filename": "Cryptography_v1.0.1_processed.txt", "content": "identity Alice as the public key, plus a global ‘system’ public key. However, to enable Alice to", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1127", "line_number": 1127, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1128", "filename": "Cryptography_v1.0.1_processed.txt", "content": "decrypt, we must have a trusted third party, called a Key Generation Centre, which can provide", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1128", "line_number": 1128, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1129", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Alice with her secret key. This third party uses its knowledge of the ‘system’ secret key to be", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1129", "line_number": 1129, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1130", "filename": "Cryptography_v1.0.1_processed.txt", "content": "able to derive Alice’s secret key. Whilst dispensing with certiﬁcates, an IBE system inherently", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1130", "line_number": 1130, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1131", "filename": "Cryptography_v1.0.1_processed.txt", "content": "has a notion of key escrow; the Key Generation Centre can decrypt all messages.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1131", "line_number": 1131, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1132", "filename": "Cryptography_v1.0.1_processed.txt", "content": "10.5", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1132", "line_number": 1132, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1133", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Linearly Homomorphic Encryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1133", "line_number": 1133, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1134", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In a linearly homomorphic encryption scheme one can perform a number of linear operations", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1134", "line_number": 1134, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1135", "filename": "Cryptography_v1.0.1_processed.txt", "content": "on ciphertexts, which result in a ciphertext encrypting a message having had the same", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1135", "line_number": 1135, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1136", "filename": "Cryptography_v1.0.1_processed.txt", "content": "operations performed on the plaintext. Thus, given two encryptions c1 ←Enc(m1, pk; r1) and", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1136", "line_number": 1136, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1137", "filename": "Cryptography_v1.0.1_processed.txt", "content": "c2 ←Enc(m2, pk; r2) one can form a ‘sum’ operation c ←c1 ⊕c2 such that c decrypts to m1 +m2.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1137", "line_number": 1137, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1138", "filename": "Cryptography_v1.0.1_processed.txt", "content": "The standard example of such encryption schemes is the Paillier encryption scheme, which", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1138", "line_number": 1138, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1139", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encrypts elements m ∈(Z/NZ), for an RSA-modulus N by computing c ←(1 + N)m · rN", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1139", "line_number": 1139, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1140", "filename": "Cryptography_v1.0.1_processed.txt", "content": "(mod N 2) where r is selected in Z/NZ.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1140", "line_number": 1140, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1141", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Such encryption algorithms can never be IND-CCA secure, as the homomorphic property", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1141", "line_number": 1141, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1142", "filename": "Cryptography_v1.0.1_processed.txt", "content": "produces a trivial malleability which can be exploited by a CCA attacker. However, they can", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1142", "line_number": 1142, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1143", "filename": "Cryptography_v1.0.1_processed.txt", "content": "have applications in many interesting areas. For example, one can use a linearly homomorphic", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1143", "line_number": 1143, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1144", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encryption scheme to add up votes in a digitally balloted election for two candidates, where", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1144", "line_number": 1144, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1145", "filename": "Cryptography_v1.0.1_processed.txt", "content": "each vote is an encryption of either the message zero or one.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1145", "line_number": 1145, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1146", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 34", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1146", "line_number": 1146, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1147", "filename": "Cryptography_v1.0.1_processed.txt", "content": "## Page 36", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1147", "line_number": 1147, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1148", "filename": "Cryptography_v1.0.1_processed.txt", "content": "10.6", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1148", "line_number": 1148, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1149", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Fully Homomorphic Encryption", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1149", "line_number": 1149, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1150", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Fully Homomorphic Encryption (or FHE) is an extension to linearly homomorphic encryption,", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1150", "line_number": 1150, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1151", "filename": "Cryptography_v1.0.1_processed.txt", "content": "in that one can not only homomorphically evaluate linear functions, but also non-linear ones.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1151", "line_number": 1151, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1152", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In particular, the ability to homomorphically evaluate both addition and multiplication on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1152", "line_number": 1152, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1153", "filename": "Cryptography_v1.0.1_processed.txt", "content": "encrypted data enables one to (theoretically) evaluate any function. Applications of FHE which", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1153", "line_number": 1153, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1154", "filename": "Cryptography_v1.0.1_processed.txt", "content": "have been envisioned are things such as performing complex search queries on encrypted", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1154", "line_number": 1154, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1155", "filename": "Cryptography_v1.0.1_processed.txt", "content": "medical data etc. Thus, FHE is very interesting in a cloud environment.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1155", "line_number": 1155, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1156", "filename": "Cryptography_v1.0.1_processed.txt", "content": "All existing FHE schemes are highly inefﬁcient. Thus only very simple functions can be", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1156", "line_number": 1156, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1157", "filename": "Cryptography_v1.0.1_processed.txt", "content": "evaluated in suitable time limits. A scheme which can perform homomorphic operations from", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1157", "line_number": 1157, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1158", "filename": "Cryptography_v1.0.1_processed.txt", "content": "a restricted class of functions (for example, to homomorphically evaluate all multi-variate", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1158", "line_number": 1158, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1159", "filename": "Cryptography_v1.0.1_processed.txt", "content": "polynomials of total degree ﬁve) is called a Somewhat Homomorphic Encryption (or SHE)", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1159", "line_number": 1159, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1160", "filename": "Cryptography_v1.0.1_processed.txt", "content": "scheme. Obviously, if the set of functions are all multi-variate polynomials of degree one, then", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1160", "line_number": 1160, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1161", "filename": "Cryptography_v1.0.1_processed.txt", "content": "the SHE scheme is a linear homomorphic encryption scheme.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1161", "line_number": 1161, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1163", "filename": "Cryptography_v1.0.1_processed.txt", "content": "IMPLEMENTATION ASPECTS", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1163", "line_number": 1163, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1164", "filename": "Cryptography_v1.0.1_processed.txt", "content": "There are two aspects one needs to bear in mind with respect to cryptographic implementation.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1164", "line_number": 1164, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1165", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Firstly security and secondly performance.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1165", "line_number": 1165, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1166", "filename": "Cryptography_v1.0.1_processed.txt", "content": "In terms of security the main concern is one of side-channel attacks. These can be mounted", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1166", "line_number": 1166, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1167", "filename": "Cryptography_v1.0.1_processed.txt", "content": "against both hardware implementations, for example cryptographic circuits implemented on", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1167", "line_number": 1167, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1168", "filename": "Cryptography_v1.0.1_processed.txt", "content": "smart-cards, or against software implementations running on commodity processors. Any", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1168", "line_number": 1168, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1169", "filename": "Cryptography_v1.0.1_processed.txt", "content": "measurable difference which occurs when running an algorithm on one set of inputs versus", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1169", "line_number": 1169, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1170", "filename": "Cryptography_v1.0.1_processed.txt", "content": "another can lead to an attack. Such measurements may involve timing differences, power", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1170", "line_number": 1170, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1171", "filename": "Cryptography_v1.0.1_processed.txt", "content": "comsumption differences, differences in electromagnetic radiation, or even differences in the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1171", "line_number": 1171, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1172", "filename": "Cryptography_v1.0.1_processed.txt", "content": "sound produced by the fan on the processor. It is even possible to mount remote side-channel", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1172", "line_number": 1172, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1173", "filename": "Cryptography_v1.0.1_processed.txt", "content": "attacks where one measures differences in response times from a remote server. A good", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1173", "line_number": 1173, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1174", "filename": "Cryptography_v1.0.1_processed.txt", "content": "survey of such attacks, focused on power analysis applied to symmetric algorithms such as", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1174", "line_number": 1174, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1175", "filename": "Cryptography_v1.0.1_processed.txt", "content": "AES, can be found in [21].", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1175", "line_number": 1175, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1176", "filename": "Cryptography_v1.0.1_processed.txt", "content": "To protect against such side-channel attacks at the hardware level various techniques have", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1176", "line_number": 1176, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1177", "filename": "Cryptography_v1.0.1_processed.txt", "content": "been proposed including utilizing techniques based on secret-sharing (called masking in the", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1177", "line_number": 1177, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1178", "filename": "Cryptography_v1.0.1_processed.txt", "content": "side-channel community). In the area of software one needs to ensure code is constant-time", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1178", "line_number": 1178, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1179", "filename": "Cryptography_v1.0.1_processed.txt", "content": "at the least (i.e. every execution path takes the same amount of time), indeed having multiple", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1179", "line_number": 1179, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1180", "filename": "Cryptography_v1.0.1_processed.txt", "content": "execution paths can itself lead to attacks via power-analysis.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1180", "line_number": 1180, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1181", "filename": "Cryptography_v1.0.1_processed.txt", "content": "To enable increased performance it is becoming increasingly common for processor manu-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1181", "line_number": 1181, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1182", "filename": "Cryptography_v1.0.1_processed.txt", "content": "facturers to supply special instructions to enable improvements to cryptographic algorithms.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1182", "line_number": 1182, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1183", "filename": "Cryptography_v1.0.1_processed.txt", "content": "This is similar to the multi-media extensions which have been common place for other appli-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1183", "line_number": 1183, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1184", "filename": "Cryptography_v1.0.1_processed.txt", "content": "cations for some decades. An example of this is special instructions on x86 chips to perform", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1184", "line_number": 1184, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1185", "filename": "Cryptography_v1.0.1_processed.txt", "content": "operations related to AES, to perform GCM-mode and to perform some ECC operations.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1185", "line_number": 1185, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1186", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Public key, i.e. number theoretic constructions, are particularly expensive in terms of compu-", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1186", "line_number": 1186, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1187", "filename": "Cryptography_v1.0.1_processed.txt", "content": "tational resources. Thus it is common for these speciﬁc algorithms to be implemented in low", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1187", "line_number": 1187, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1188", "filename": "Cryptography_v1.0.1_processed.txt", "content": "level machine code, which is tuned to a speciﬁc architecture. However, this needs to be done", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1188", "line_number": 1188, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1189", "filename": "Cryptography_v1.0.1_processed.txt", "content": "with care so as to take into account the earlier mentioned side-channel attacks.", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1189", "line_number": 1189, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1190", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Finally an implementation can also be prone to fault attacks. These are attacks in which an", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1190", "line_number": 1190, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1191", "filename": "Cryptography_v1.0.1_processed.txt", "content": "attacker injects faults (either physical faults on hardware, or datagram faults into a protocol).", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1191", "line_number": 1191, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
{"chunk_id": "line-1192", "filename": "Cryptography_v1.0.1_processed.txt", "content": "Page 35", "metadata": {"filename": "Cryptography_v1.0.1_processed.txt", "chunk_id": "line-1192", "line_number": 1192, "source": "知识库\\output\\Cryptography_v1.0.1_processed.txt"}}
